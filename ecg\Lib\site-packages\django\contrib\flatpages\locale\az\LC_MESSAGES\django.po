# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012
# <AUTHOR> <EMAIL>, 2018,2020
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-01-12 07:27+0000\n"
"Last-Translator: Emin Mast<PERSON>da <<EMAIL>>\n"
"Language-Team: Azerbaijani (http://www.transifex.com/django/django/language/"
"az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Əlavə imkanlar"

msgid "Flat Pages"
msgstr "Flat Səhifələr"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Məsələn, “/about/contact/”. Əvvəldə və sondakı kəsr xəttinin olmasına diqqət "
"edin."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Burada yalnız hərf, rəqəm, nöqtə, altdan xətt, defis, kəsr xətti və ya "
"tildadan istifadə etmək olar."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Məsələn, “/about/contact”. Əvvəldəki kəsr xəttinin olmasına diqqət edin."

msgid "URL is missing a leading slash."
msgstr "Ünvan başlanğıcında çəp xətt əksikdir."

msgid "URL is missing a trailing slash."
msgstr "Ünvan sonunda çəp xətt əksikdir."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "%(site)s saytı üçün artıq %(url)s ünvanlı Flatpage mövcuddur"

msgid "title"
msgstr "başlıq"

msgid "content"
msgstr "məzmun"

msgid "enable comments"
msgstr "şərhlər olsun"

msgid "template name"
msgstr "şablonun adı"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Məsələn, “flatpages/contact_page.html”. Əgər təchiz edilməsə, sistem "
"“flatpages/default.html” işlədəcək."

msgid "registration required"
msgstr "ancaq qeydiyyatlılar üçün"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Əgər bura quş qoysanız, ancaq qeydiyyatdan keçib sayta daxil olmuş "
"istifadəçilər bu səhifəni görə biləcəklər."

msgid "sites"
msgstr "saytlar"

msgid "flat page"
msgstr "adi səhifə"

msgid "flat pages"
msgstr "adi səhifələr"
