/* Copyright 2020 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/
#ifndef TENSORFLOW_CORE_DATA_SERVICE_COMPRESSION_UTILS_H_
#define TENSORFLOW_CORE_DATA_SERVICE_COMPRESSION_UTILS_H_

#include "tensorflow/core/common_runtime/dma_helper.h"
#include "tensorflow/core/data/dataset.pb.h"
#include "tensorflow/core/platform/status.h"

namespace tensorflow {
namespace data {

// Compresses the components of `element` into the `CompressedElement` proto.
//
// In addition to writing the actual compressed bytes, `Compress` fills
// out the per-component metadata for the `CompressedElement`.
//
// Returns an error if the uncompressed size of the element exceeds 4GB.
Status CompressElement(const std::vector<Tensor>& element,
                       CompressedElement* out);

// Uncompresses a `CompressedElement` into a vector of tensor components.
Status UncompressElement(const CompressedElement& compressed,
                         std::vector<Tensor>* out);

}  // namespace data
}  // namespace tensorflow

#endif  // TENSORFLOW_CORE_DATA_SERVICE_COMPRESSION_UTILS_H_
