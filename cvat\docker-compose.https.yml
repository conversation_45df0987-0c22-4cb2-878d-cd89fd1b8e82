# Copyright (C) 2018-2022 Intel Corporation
#
# SPDX-License-Identifier: MIT

services:
  cvat_server:
    labels:
      traefik.http.routers.cvat.entrypoints: websecure
      traefik.http.routers.cvat.tls.certresolver: lets-encrypt

  cvat_ui:
    labels:
      traefik.http.routers.cvat-ui.entrypoints: websecure
      traefik.http.routers.cvat-ui.tls.certresolver: lets-encrypt

  traefik:
    environment:
      TRAEFIK_ENTRYPOINTS_web_ADDRESS: :80
      TRAEFIK_ENTRYPOINTS_web_HTTP_REDIRECTIONS_ENTRYPOINT_TO: websecure
      TRAEFIK_ENTRYPOINTS_web_HTTP_REDIRECTIONS_ENTRYPOINT_SCHEME: https
      TRAEFIK_ENTRYPOINTS_websecure_ADDRESS: :443
      TRAEFIK_CERTIFICATESRESOLVERS_lets-encrypt_ACME_EMAIL: "${ACME_EMAIL:?Please set the ACME_EMAIL env variable}"
      TRAEFIK_CERTIFICATESRESOLVERS_lets-encrypt_ACME_TLSCHALLENGE: "true"
      TRAEFIK_CERTIFICATESRESOLVERS_lets-encrypt_ACME_STORAGE: /letsencrypt/acme.json
    ports:
      - 80:80
      - 443:443
    volumes:
      - cvat_letsencrypt:/letsencrypt

volumes:
  cvat_letsencrypt:
