import random
from typing import Union, Any
from .diagnosis import process as model_predict
from .config_reader import PersonalizedConfig
import os

# 初始化配置读取器
current_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(current_dir, 'personalized_age_config.csv')
config = PersonalizedConfig(config_path)


def get_range_by_gender(gender: Union[str, int]) -> tuple:
    """
    根据性别获取波动范围
    :param gender: 性别 (1为男性，2为女性)
    :return: (下限, 上限)
    """
    if isinstance(gender, str):
        is_male = gender in ['1', 'M', 'MALE']
    else:
        is_male = gender == 1

    if is_male:
        return -5, 3  # 男性：-5到+3的范围内随机
    else:
        return -7, 5  # 女性：-7到+5的范围内随机


def process(ecg_data: Any, union_id: str) -> tuple:
    """
    个性化心脏年龄预测处理
    :param ecg_data: 心电数据
    :param union_id: 用户ID
    :return: (预测年龄, 是否个性化预测)
    """
    try:
        # 获取模型预测结果
        model_prediction = model_predict(ecg_data)

        # 获取用户配置
        user_config = config.get_user_config(str(union_id))

        # 如果没有找到用户配置，返回模型预测结果
        if not user_config:
            print(f"未找到用户配置信息 (union_id: {union_id})")
            return model_prediction, False

        # 获取用户配置
        real_age = user_config['real_age']
        gender = user_config['gender']
        alpha = user_config['alpha']
        beta = user_config['beta']

        weighted_age = alpha * real_age + beta * model_prediction

        # 根据性别获取波动范围并添加随机波动
        range_min, range_max = get_range_by_gender(gender)
        random_deviation = random.uniform(range_min, range_max)
        final_result = weighted_age + random_deviation

        return int(round(final_result)), True

    except Exception as e:
        print(f"个性化预测出错 (union_id: {union_id}): {str(e)}")
        return model_prediction, False