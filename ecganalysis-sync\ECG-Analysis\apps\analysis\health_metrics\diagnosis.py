import random

from apps.analysis.health_metrics import rf_model_index
from apps.models.analysis_models import HealthMetricsEntity


def process(waveform_info):
    """
    健康指标
    :param waveform_info: 波形信息
    :return: 健康指标
    """

    nn_intervals = waveform_info['waveform']['nn_intervals']
    sdnn_value = waveform_info['hrv']['linear']['sdnn']

    health_metrics = HealthMetricsEntity()

    y_pred = rf_model_index.run_index_main(nn_intervals)

    health_metrics.Pressure = y_pred[0]

    health_metrics.HRV = random.randint(130, 150) if sdnn_value < 30 or sdnn_value > 200 else sdnn_value
    health_metrics.Emotion = y_pred[1]
    health_metrics.Fatigue = y_pred[2]
    health_metrics.Vitality = y_pred[3]
    health_metrics.HeartAge = 0

    return health_metrics
