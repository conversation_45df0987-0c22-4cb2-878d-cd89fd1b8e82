// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/debug/debug_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/profiler/tfprof_log.pb.h"
#include "tensorflow/core/protobuf/debug.pb.h"
#include "tensorflow/core/util/event.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto;
namespace tensorflow {
class CallTraceback;
class CallTracebackDefaultTypeInternal;
extern CallTracebackDefaultTypeInternal _CallTraceback_default_instance_;
class CallTraceback_OriginIdToStringEntry_DoNotUse;
class CallTraceback_OriginIdToStringEntry_DoNotUseDefaultTypeInternal;
extern CallTraceback_OriginIdToStringEntry_DoNotUseDefaultTypeInternal _CallTraceback_OriginIdToStringEntry_DoNotUse_default_instance_;
class EventReply;
class EventReplyDefaultTypeInternal;
extern EventReplyDefaultTypeInternal _EventReply_default_instance_;
class EventReply_DebugOpStateChange;
class EventReply_DebugOpStateChangeDefaultTypeInternal;
extern EventReply_DebugOpStateChangeDefaultTypeInternal _EventReply_DebugOpStateChange_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CallTraceback* Arena::CreateMaybeMessage<::tensorflow::CallTraceback>(Arena*);
template<> ::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse>(Arena*);
template<> ::tensorflow::EventReply* Arena::CreateMaybeMessage<::tensorflow::EventReply>(Arena*);
template<> ::tensorflow::EventReply_DebugOpStateChange* Arena::CreateMaybeMessage<::tensorflow::EventReply_DebugOpStateChange>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum EventReply_DebugOpStateChange_State : int {
  EventReply_DebugOpStateChange_State_STATE_UNSPECIFIED = 0,
  EventReply_DebugOpStateChange_State_DISABLED = 1,
  EventReply_DebugOpStateChange_State_READ_ONLY = 2,
  EventReply_DebugOpStateChange_State_READ_WRITE = 3,
  EventReply_DebugOpStateChange_State_EventReply_DebugOpStateChange_State_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  EventReply_DebugOpStateChange_State_EventReply_DebugOpStateChange_State_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool EventReply_DebugOpStateChange_State_IsValid(int value);
constexpr EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange_State_State_MIN = EventReply_DebugOpStateChange_State_STATE_UNSPECIFIED;
constexpr EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange_State_State_MAX = EventReply_DebugOpStateChange_State_READ_WRITE;
constexpr int EventReply_DebugOpStateChange_State_State_ARRAYSIZE = EventReply_DebugOpStateChange_State_State_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* EventReply_DebugOpStateChange_State_descriptor();
template<typename T>
inline const std::string& EventReply_DebugOpStateChange_State_Name(T enum_t_value) {
  static_assert(::std::is_same<T, EventReply_DebugOpStateChange_State>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function EventReply_DebugOpStateChange_State_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    EventReply_DebugOpStateChange_State_descriptor(), enum_t_value);
}
inline bool EventReply_DebugOpStateChange_State_Parse(
    const std::string& name, EventReply_DebugOpStateChange_State* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<EventReply_DebugOpStateChange_State>(
    EventReply_DebugOpStateChange_State_descriptor(), name, value);
}
enum CallTraceback_CallType : int {
  CallTraceback_CallType_UNSPECIFIED = 0,
  CallTraceback_CallType_GRAPH_EXECUTION = 1,
  CallTraceback_CallType_EAGER_EXECUTION = 2,
  CallTraceback_CallType_CallTraceback_CallType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  CallTraceback_CallType_CallTraceback_CallType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool CallTraceback_CallType_IsValid(int value);
constexpr CallTraceback_CallType CallTraceback_CallType_CallType_MIN = CallTraceback_CallType_UNSPECIFIED;
constexpr CallTraceback_CallType CallTraceback_CallType_CallType_MAX = CallTraceback_CallType_EAGER_EXECUTION;
constexpr int CallTraceback_CallType_CallType_ARRAYSIZE = CallTraceback_CallType_CallType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CallTraceback_CallType_descriptor();
template<typename T>
inline const std::string& CallTraceback_CallType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CallTraceback_CallType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CallTraceback_CallType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CallTraceback_CallType_descriptor(), enum_t_value);
}
inline bool CallTraceback_CallType_Parse(
    const std::string& name, CallTraceback_CallType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CallTraceback_CallType>(
    CallTraceback_CallType_descriptor(), name, value);
}
// ===================================================================

class EventReply_DebugOpStateChange :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.EventReply.DebugOpStateChange) */ {
 public:
  EventReply_DebugOpStateChange();
  virtual ~EventReply_DebugOpStateChange();

  EventReply_DebugOpStateChange(const EventReply_DebugOpStateChange& from);
  EventReply_DebugOpStateChange(EventReply_DebugOpStateChange&& from) noexcept
    : EventReply_DebugOpStateChange() {
    *this = ::std::move(from);
  }

  inline EventReply_DebugOpStateChange& operator=(const EventReply_DebugOpStateChange& from) {
    CopyFrom(from);
    return *this;
  }
  inline EventReply_DebugOpStateChange& operator=(EventReply_DebugOpStateChange&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const EventReply_DebugOpStateChange& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EventReply_DebugOpStateChange* internal_default_instance() {
    return reinterpret_cast<const EventReply_DebugOpStateChange*>(
               &_EventReply_DebugOpStateChange_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(EventReply_DebugOpStateChange& a, EventReply_DebugOpStateChange& b) {
    a.Swap(&b);
  }
  inline void Swap(EventReply_DebugOpStateChange* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EventReply_DebugOpStateChange* New() const final {
    return CreateMaybeMessage<EventReply_DebugOpStateChange>(nullptr);
  }

  EventReply_DebugOpStateChange* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EventReply_DebugOpStateChange>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const EventReply_DebugOpStateChange& from);
  void MergeFrom(const EventReply_DebugOpStateChange& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EventReply_DebugOpStateChange* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.EventReply.DebugOpStateChange";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef EventReply_DebugOpStateChange_State State;
  static constexpr State STATE_UNSPECIFIED =
    EventReply_DebugOpStateChange_State_STATE_UNSPECIFIED;
  static constexpr State DISABLED =
    EventReply_DebugOpStateChange_State_DISABLED;
  static constexpr State READ_ONLY =
    EventReply_DebugOpStateChange_State_READ_ONLY;
  static constexpr State READ_WRITE =
    EventReply_DebugOpStateChange_State_READ_WRITE;
  static inline bool State_IsValid(int value) {
    return EventReply_DebugOpStateChange_State_IsValid(value);
  }
  static constexpr State State_MIN =
    EventReply_DebugOpStateChange_State_State_MIN;
  static constexpr State State_MAX =
    EventReply_DebugOpStateChange_State_State_MAX;
  static constexpr int State_ARRAYSIZE =
    EventReply_DebugOpStateChange_State_State_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  State_descriptor() {
    return EventReply_DebugOpStateChange_State_descriptor();
  }
  template<typename T>
  static inline const std::string& State_Name(T enum_t_value) {
    static_assert(::std::is_same<T, State>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function State_Name.");
    return EventReply_DebugOpStateChange_State_Name(enum_t_value);
  }
  static inline bool State_Parse(const std::string& name,
      State* value) {
    return EventReply_DebugOpStateChange_State_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kNodeNameFieldNumber = 2,
    kDebugOpFieldNumber = 4,
    kStateFieldNumber = 1,
    kOutputSlotFieldNumber = 3,
  };
  // string node_name = 2;
  void clear_node_name();
  const std::string& node_name() const;
  void set_node_name(const std::string& value);
  void set_node_name(std::string&& value);
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  std::string* mutable_node_name();
  std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);

  // string debug_op = 4;
  void clear_debug_op();
  const std::string& debug_op() const;
  void set_debug_op(const std::string& value);
  void set_debug_op(std::string&& value);
  void set_debug_op(const char* value);
  void set_debug_op(const char* value, size_t size);
  std::string* mutable_debug_op();
  std::string* release_debug_op();
  void set_allocated_debug_op(std::string* debug_op);

  // .tensorflow.EventReply.DebugOpStateChange.State state = 1;
  void clear_state();
  ::tensorflow::EventReply_DebugOpStateChange_State state() const;
  void set_state(::tensorflow::EventReply_DebugOpStateChange_State value);

  // int32 output_slot = 3;
  void clear_output_slot();
  ::PROTOBUF_NAMESPACE_ID::int32 output_slot() const;
  void set_output_slot(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.EventReply.DebugOpStateChange)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr debug_op_;
  int state_;
  ::PROTOBUF_NAMESPACE_ID::int32 output_slot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto;
};
// -------------------------------------------------------------------

class EventReply :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.EventReply) */ {
 public:
  EventReply();
  virtual ~EventReply();

  EventReply(const EventReply& from);
  EventReply(EventReply&& from) noexcept
    : EventReply() {
    *this = ::std::move(from);
  }

  inline EventReply& operator=(const EventReply& from) {
    CopyFrom(from);
    return *this;
  }
  inline EventReply& operator=(EventReply&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const EventReply& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EventReply* internal_default_instance() {
    return reinterpret_cast<const EventReply*>(
               &_EventReply_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(EventReply& a, EventReply& b) {
    a.Swap(&b);
  }
  inline void Swap(EventReply* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EventReply* New() const final {
    return CreateMaybeMessage<EventReply>(nullptr);
  }

  EventReply* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EventReply>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const EventReply& from);
  void MergeFrom(const EventReply& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EventReply* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.EventReply";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef EventReply_DebugOpStateChange DebugOpStateChange;

  // accessors -------------------------------------------------------

  enum : int {
    kDebugOpStateChangesFieldNumber = 1,
    kTensorFieldNumber = 2,
  };
  // repeated .tensorflow.EventReply.DebugOpStateChange debug_op_state_changes = 1;
  int debug_op_state_changes_size() const;
  void clear_debug_op_state_changes();
  ::tensorflow::EventReply_DebugOpStateChange* mutable_debug_op_state_changes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >*
      mutable_debug_op_state_changes();
  const ::tensorflow::EventReply_DebugOpStateChange& debug_op_state_changes(int index) const;
  ::tensorflow::EventReply_DebugOpStateChange* add_debug_op_state_changes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >&
      debug_op_state_changes() const;

  // .tensorflow.TensorProto tensor = 2;
  bool has_tensor() const;
  void clear_tensor();
  const ::tensorflow::TensorProto& tensor() const;
  ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);

  // @@protoc_insertion_point(class_scope:tensorflow.EventReply)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange > debug_op_state_changes_;
  ::tensorflow::TensorProto* tensor_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CallTraceback_OriginIdToStringEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallTraceback_OriginIdToStringEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallTraceback_OriginIdToStringEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  CallTraceback_OriginIdToStringEntry_DoNotUse();
  CallTraceback_OriginIdToStringEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const CallTraceback_OriginIdToStringEntry_DoNotUse& other);
  static const CallTraceback_OriginIdToStringEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CallTraceback_OriginIdToStringEntry_DoNotUse*>(&_CallTraceback_OriginIdToStringEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CallTraceback.OriginIdToStringEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto.file_level_metadata[2];
  }

  public:
};

// -------------------------------------------------------------------

class CallTraceback :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CallTraceback) */ {
 public:
  CallTraceback();
  virtual ~CallTraceback();

  CallTraceback(const CallTraceback& from);
  CallTraceback(CallTraceback&& from) noexcept
    : CallTraceback() {
    *this = ::std::move(from);
  }

  inline CallTraceback& operator=(const CallTraceback& from) {
    CopyFrom(from);
    return *this;
  }
  inline CallTraceback& operator=(CallTraceback&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CallTraceback& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CallTraceback* internal_default_instance() {
    return reinterpret_cast<const CallTraceback*>(
               &_CallTraceback_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CallTraceback& a, CallTraceback& b) {
    a.Swap(&b);
  }
  inline void Swap(CallTraceback* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CallTraceback* New() const final {
    return CreateMaybeMessage<CallTraceback>(nullptr);
  }

  CallTraceback* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CallTraceback>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CallTraceback& from);
  void MergeFrom(const CallTraceback& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CallTraceback* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CallTraceback";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  typedef CallTraceback_CallType CallType;
  static constexpr CallType UNSPECIFIED =
    CallTraceback_CallType_UNSPECIFIED;
  static constexpr CallType GRAPH_EXECUTION =
    CallTraceback_CallType_GRAPH_EXECUTION;
  static constexpr CallType EAGER_EXECUTION =
    CallTraceback_CallType_EAGER_EXECUTION;
  static inline bool CallType_IsValid(int value) {
    return CallTraceback_CallType_IsValid(value);
  }
  static constexpr CallType CallType_MIN =
    CallTraceback_CallType_CallType_MIN;
  static constexpr CallType CallType_MAX =
    CallTraceback_CallType_CallType_MAX;
  static constexpr int CallType_ARRAYSIZE =
    CallTraceback_CallType_CallType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  CallType_descriptor() {
    return CallTraceback_CallType_descriptor();
  }
  template<typename T>
  static inline const std::string& CallType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, CallType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function CallType_Name.");
    return CallTraceback_CallType_Name(enum_t_value);
  }
  static inline bool CallType_Parse(const std::string& name,
      CallType* value) {
    return CallTraceback_CallType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kOriginIdToStringFieldNumber = 4,
    kCallKeyFieldNumber = 2,
    kOriginStackFieldNumber = 3,
    kGraphTracebackFieldNumber = 5,
    kGraphVersionFieldNumber = 6,
    kCallTypeFieldNumber = 1,
  };
  // map<int64, string> origin_id_to_string = 4;
  int origin_id_to_string_size() const;
  void clear_origin_id_to_string();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >&
      origin_id_to_string() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >*
      mutable_origin_id_to_string();

  // string call_key = 2;
  void clear_call_key();
  const std::string& call_key() const;
  void set_call_key(const std::string& value);
  void set_call_key(std::string&& value);
  void set_call_key(const char* value);
  void set_call_key(const char* value, size_t size);
  std::string* mutable_call_key();
  std::string* release_call_key();
  void set_allocated_call_key(std::string* call_key);

  // .tensorflow.tfprof.CodeDef origin_stack = 3;
  bool has_origin_stack() const;
  void clear_origin_stack();
  const ::tensorflow::tfprof::CodeDef& origin_stack() const;
  ::tensorflow::tfprof::CodeDef* release_origin_stack();
  ::tensorflow::tfprof::CodeDef* mutable_origin_stack();
  void set_allocated_origin_stack(::tensorflow::tfprof::CodeDef* origin_stack);

  // .tensorflow.tfprof.OpLogProto graph_traceback = 5;
  bool has_graph_traceback() const;
  void clear_graph_traceback();
  const ::tensorflow::tfprof::OpLogProto& graph_traceback() const;
  ::tensorflow::tfprof::OpLogProto* release_graph_traceback();
  ::tensorflow::tfprof::OpLogProto* mutable_graph_traceback();
  void set_allocated_graph_traceback(::tensorflow::tfprof::OpLogProto* graph_traceback);

  // int64 graph_version = 6;
  void clear_graph_version();
  ::PROTOBUF_NAMESPACE_ID::int64 graph_version() const;
  void set_graph_version(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .tensorflow.CallTraceback.CallType call_type = 1;
  void clear_call_type();
  ::tensorflow::CallTraceback_CallType call_type() const;
  void set_call_type(::tensorflow::CallTraceback_CallType value);

  // @@protoc_insertion_point(class_scope:tensorflow.CallTraceback)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      CallTraceback_OriginIdToStringEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int64, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > origin_id_to_string_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr call_key_;
  ::tensorflow::tfprof::CodeDef* origin_stack_;
  ::tensorflow::tfprof::OpLogProto* graph_traceback_;
  ::PROTOBUF_NAMESPACE_ID::int64 graph_version_;
  int call_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// EventReply_DebugOpStateChange

// .tensorflow.EventReply.DebugOpStateChange.State state = 1;
inline void EventReply_DebugOpStateChange::clear_state() {
  state_ = 0;
}
inline ::tensorflow::EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange::state() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.state)
  return static_cast< ::tensorflow::EventReply_DebugOpStateChange_State >(state_);
}
inline void EventReply_DebugOpStateChange::set_state(::tensorflow::EventReply_DebugOpStateChange_State value) {
  
  state_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.state)
}

// string node_name = 2;
inline void EventReply_DebugOpStateChange::clear_node_name() {
  node_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& EventReply_DebugOpStateChange::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.node_name)
  return node_name_.GetNoArena();
}
inline void EventReply_DebugOpStateChange::set_node_name(const std::string& value) {
  
  node_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.node_name)
}
inline void EventReply_DebugOpStateChange::set_node_name(std::string&& value) {
  
  node_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.EventReply.DebugOpStateChange.node_name)
}
inline void EventReply_DebugOpStateChange::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  node_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.EventReply.DebugOpStateChange.node_name)
}
inline void EventReply_DebugOpStateChange::set_node_name(const char* value, size_t size) {
  
  node_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.EventReply.DebugOpStateChange.node_name)
}
inline std::string* EventReply_DebugOpStateChange::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.DebugOpStateChange.node_name)
  return node_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* EventReply_DebugOpStateChange::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.EventReply.DebugOpStateChange.node_name)
  
  return node_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void EventReply_DebugOpStateChange::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  node_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), node_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EventReply.DebugOpStateChange.node_name)
}

// int32 output_slot = 3;
inline void EventReply_DebugOpStateChange::clear_output_slot() {
  output_slot_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 EventReply_DebugOpStateChange::output_slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.output_slot)
  return output_slot_;
}
inline void EventReply_DebugOpStateChange::set_output_slot(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  output_slot_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.output_slot)
}

// string debug_op = 4;
inline void EventReply_DebugOpStateChange::clear_debug_op() {
  debug_op_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& EventReply_DebugOpStateChange::debug_op() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.debug_op)
  return debug_op_.GetNoArena();
}
inline void EventReply_DebugOpStateChange::set_debug_op(const std::string& value) {
  
  debug_op_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.debug_op)
}
inline void EventReply_DebugOpStateChange::set_debug_op(std::string&& value) {
  
  debug_op_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.EventReply.DebugOpStateChange.debug_op)
}
inline void EventReply_DebugOpStateChange::set_debug_op(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  debug_op_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.EventReply.DebugOpStateChange.debug_op)
}
inline void EventReply_DebugOpStateChange::set_debug_op(const char* value, size_t size) {
  
  debug_op_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.EventReply.DebugOpStateChange.debug_op)
}
inline std::string* EventReply_DebugOpStateChange::mutable_debug_op() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.DebugOpStateChange.debug_op)
  return debug_op_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* EventReply_DebugOpStateChange::release_debug_op() {
  // @@protoc_insertion_point(field_release:tensorflow.EventReply.DebugOpStateChange.debug_op)
  
  return debug_op_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void EventReply_DebugOpStateChange::set_allocated_debug_op(std::string* debug_op) {
  if (debug_op != nullptr) {
    
  } else {
    
  }
  debug_op_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), debug_op);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EventReply.DebugOpStateChange.debug_op)
}

// -------------------------------------------------------------------

// EventReply

// repeated .tensorflow.EventReply.DebugOpStateChange debug_op_state_changes = 1;
inline int EventReply::debug_op_state_changes_size() const {
  return debug_op_state_changes_.size();
}
inline void EventReply::clear_debug_op_state_changes() {
  debug_op_state_changes_.Clear();
}
inline ::tensorflow::EventReply_DebugOpStateChange* EventReply::mutable_debug_op_state_changes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.debug_op_state_changes)
  return debug_op_state_changes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >*
EventReply::mutable_debug_op_state_changes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.EventReply.debug_op_state_changes)
  return &debug_op_state_changes_;
}
inline const ::tensorflow::EventReply_DebugOpStateChange& EventReply::debug_op_state_changes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.debug_op_state_changes)
  return debug_op_state_changes_.Get(index);
}
inline ::tensorflow::EventReply_DebugOpStateChange* EventReply::add_debug_op_state_changes() {
  // @@protoc_insertion_point(field_add:tensorflow.EventReply.debug_op_state_changes)
  return debug_op_state_changes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >&
EventReply::debug_op_state_changes() const {
  // @@protoc_insertion_point(field_list:tensorflow.EventReply.debug_op_state_changes)
  return debug_op_state_changes_;
}

// .tensorflow.TensorProto tensor = 2;
inline bool EventReply::has_tensor() const {
  return this != internal_default_instance() && tensor_ != nullptr;
}
inline const ::tensorflow::TensorProto& EventReply::tensor() const {
  const ::tensorflow::TensorProto* p = tensor_;
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.tensor)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* EventReply::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.EventReply.tensor)
  
  ::tensorflow::TensorProto* temp = tensor_;
  tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* EventReply::mutable_tensor() {
  
  if (tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    tensor_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.tensor)
  return tensor_;
}
inline void EventReply::set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_);
  }
  if (tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor)->GetArena();
    if (message_arena != submessage_arena) {
      tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EventReply.tensor)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// CallTraceback

// .tensorflow.CallTraceback.CallType call_type = 1;
inline void CallTraceback::clear_call_type() {
  call_type_ = 0;
}
inline ::tensorflow::CallTraceback_CallType CallTraceback::call_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.call_type)
  return static_cast< ::tensorflow::CallTraceback_CallType >(call_type_);
}
inline void CallTraceback::set_call_type(::tensorflow::CallTraceback_CallType value) {
  
  call_type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CallTraceback.call_type)
}

// string call_key = 2;
inline void CallTraceback::clear_call_key() {
  call_key_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& CallTraceback::call_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.call_key)
  return call_key_.GetNoArena();
}
inline void CallTraceback::set_call_key(const std::string& value) {
  
  call_key_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.CallTraceback.call_key)
}
inline void CallTraceback::set_call_key(std::string&& value) {
  
  call_key_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CallTraceback.call_key)
}
inline void CallTraceback::set_call_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  call_key_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.CallTraceback.call_key)
}
inline void CallTraceback::set_call_key(const char* value, size_t size) {
  
  call_key_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CallTraceback.call_key)
}
inline std::string* CallTraceback::mutable_call_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CallTraceback.call_key)
  return call_key_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* CallTraceback::release_call_key() {
  // @@protoc_insertion_point(field_release:tensorflow.CallTraceback.call_key)
  
  return call_key_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void CallTraceback::set_allocated_call_key(std::string* call_key) {
  if (call_key != nullptr) {
    
  } else {
    
  }
  call_key_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), call_key);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallTraceback.call_key)
}

// .tensorflow.tfprof.CodeDef origin_stack = 3;
inline bool CallTraceback::has_origin_stack() const {
  return this != internal_default_instance() && origin_stack_ != nullptr;
}
inline const ::tensorflow::tfprof::CodeDef& CallTraceback::origin_stack() const {
  const ::tensorflow::tfprof::CodeDef* p = origin_stack_;
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.origin_stack)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tfprof::CodeDef*>(
      &::tensorflow::tfprof::_CodeDef_default_instance_);
}
inline ::tensorflow::tfprof::CodeDef* CallTraceback::release_origin_stack() {
  // @@protoc_insertion_point(field_release:tensorflow.CallTraceback.origin_stack)
  
  ::tensorflow::tfprof::CodeDef* temp = origin_stack_;
  origin_stack_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* CallTraceback::mutable_origin_stack() {
  
  if (origin_stack_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(GetArenaNoVirtual());
    origin_stack_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CallTraceback.origin_stack)
  return origin_stack_;
}
inline void CallTraceback::set_allocated_origin_stack(::tensorflow::tfprof::CodeDef* origin_stack) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(origin_stack_);
  }
  if (origin_stack) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      origin_stack = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, origin_stack, submessage_arena);
    }
    
  } else {
    
  }
  origin_stack_ = origin_stack;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallTraceback.origin_stack)
}

// map<int64, string> origin_id_to_string = 4;
inline int CallTraceback::origin_id_to_string_size() const {
  return origin_id_to_string_.size();
}
inline void CallTraceback::clear_origin_id_to_string() {
  origin_id_to_string_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >&
CallTraceback::origin_id_to_string() const {
  // @@protoc_insertion_point(field_map:tensorflow.CallTraceback.origin_id_to_string)
  return origin_id_to_string_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >*
CallTraceback::mutable_origin_id_to_string() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.CallTraceback.origin_id_to_string)
  return origin_id_to_string_.MutableMap();
}

// .tensorflow.tfprof.OpLogProto graph_traceback = 5;
inline bool CallTraceback::has_graph_traceback() const {
  return this != internal_default_instance() && graph_traceback_ != nullptr;
}
inline const ::tensorflow::tfprof::OpLogProto& CallTraceback::graph_traceback() const {
  const ::tensorflow::tfprof::OpLogProto* p = graph_traceback_;
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.graph_traceback)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tfprof::OpLogProto*>(
      &::tensorflow::tfprof::_OpLogProto_default_instance_);
}
inline ::tensorflow::tfprof::OpLogProto* CallTraceback::release_graph_traceback() {
  // @@protoc_insertion_point(field_release:tensorflow.CallTraceback.graph_traceback)
  
  ::tensorflow::tfprof::OpLogProto* temp = graph_traceback_;
  graph_traceback_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::OpLogProto* CallTraceback::mutable_graph_traceback() {
  
  if (graph_traceback_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::OpLogProto>(GetArenaNoVirtual());
    graph_traceback_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CallTraceback.graph_traceback)
  return graph_traceback_;
}
inline void CallTraceback::set_allocated_graph_traceback(::tensorflow::tfprof::OpLogProto* graph_traceback) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_traceback_);
  }
  if (graph_traceback) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      graph_traceback = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_traceback, submessage_arena);
    }
    
  } else {
    
  }
  graph_traceback_ = graph_traceback;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallTraceback.graph_traceback)
}

// int64 graph_version = 6;
inline void CallTraceback::clear_graph_version() {
  graph_version_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CallTraceback::graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.graph_version)
  return graph_version_;
}
inline void CallTraceback::set_graph_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  graph_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CallTraceback.graph_version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::EventReply_DebugOpStateChange_State> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::EventReply_DebugOpStateChange_State>() {
  return ::tensorflow::EventReply_DebugOpStateChange_State_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::CallTraceback_CallType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::CallTraceback_CallType>() {
  return ::tensorflow::CallTraceback_CallType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto
