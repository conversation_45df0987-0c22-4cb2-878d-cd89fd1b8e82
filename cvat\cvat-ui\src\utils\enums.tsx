// Copyright (C) 2021-2022 Intel Corporation
//
// SPDX-License-Identifier: MIT

export enum ProviderType {
    AWS_S3_BUCKET = 'AWS_S3_BUCKET',
    AZURE_CONTAINER = 'AZURE_CONTAINER',
    GOOGLE_CLOUD_STORAGE = 'GOOGLE_CLOUD_STORAGE',
}

export enum CredentialsType {
    KEY_SECRET_KEY_PAIR = 'KEY_SECRET_KEY_PAIR',
    ACCOUNT_NAME_TOKEN_PAIR = 'ACCOUNT_NAME_TOKEN_PAIR',
    ANONYMOUS_ACCESS = 'ANONYMOUS_ACCESS',
    CONNECTION_STRING = 'CONNECTION_STRING',
    KEY_FILE_PATH = 'KEY_FILE_PATH',
}

export enum StorageStatuses {
    AVAILABLE = 'AVAILABLE',
    FORBIDDEN = 'FORBIDDEN',
    NOT_FOUND = 'NOT_FOUND',
}

// if this enum is changed, kindly update the conflictDetector and it's helpers as well
export enum ShortcutScope {
    GENERAL = 'GENERAL',
    ANNOTATION_PAGE = 'ANNOTATION_PAGE',
    OBJECTS_SIDEBAR = 'OBJECTS_SIDEBAR',
    STANDARD_WORKSPACE = 'STANDARD_WORKSPACE',
    STANDARD_WORKSPACE_CONTROLS = 'STANDARD_WORKSPACE_CONTROLS',
    ATTRIBUTE_ANNOTATION_WORKSPACE = 'ATTRIBUTE_ANNOTATION_WORKSPACE',
    SINGLE_SHAPE_ANNOTATION_WORKSPACE = 'SINGLE_SHAPE_ANNOTATION_WORKSPACE',
    TAG_ANNOTATION_WORKSPACE = 'TAG_ANNOTATION_WORKSPACE',
    REVIEW_WORKSPACE_CONTROLS = 'REVIEW_WORKSPACE_CONTROLS',
    '3D_ANNOTATION_WORKSPACE' = '3D_ANNOTATION_WORKSPACE',
    '3D_ANNOTATION_WORKSPACE_CONTROLS' = '3D_ANNOTATION_WORKSPACE_CONTROLS',
    LABELS_EDITOR = 'LABELS_EDITOR',
}
