# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/trackable_object_graph.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5tensorboard/compat/proto/trackable_object_graph.proto\x12\x0btensorboard\x1a\x1egoogle/protobuf/wrappers.proto\"\xf8\x05\n\x14TrackableObjectGraph\x12@\n\x05nodes\x18\x01 \x03(\x0b\x32\x31.tensorboard.TrackableObjectGraph.TrackableObject\x1a\x9d\x05\n\x0fTrackableObject\x12S\n\x08\x63hildren\x18\x01 \x03(\x0b\x32\x41.tensorboard.TrackableObjectGraph.TrackableObject.ObjectReference\x12V\n\nattributes\x18\x02 \x03(\x0b\x32\x42.tensorboard.TrackableObjectGraph.TrackableObject.SerializedTensor\x12_\n\x0eslot_variables\x18\x03 \x03(\x0b\x32G.tensorboard.TrackableObjectGraph.TrackableObject.SlotVariableReference\x12\x36\n\x10registered_saver\x18\x04 \x01(\x0b\x32\x1c.tensorboard.RegisteredSaver\x12\x39\n\x15has_checkpoint_values\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x1a\x36\n\x0fObjectReference\x12\x0f\n\x07node_id\x18\x01 \x01(\x05\x12\x12\n\nlocal_name\x18\x02 \x01(\t\x1a\x63\n\x10SerializedTensor\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tfull_name\x18\x02 \x01(\t\x12\x16\n\x0e\x63heckpoint_key\x18\x03 \x01(\tJ\x04\x08\x04\x10\x05R\x10optional_restore\x1al\n\x15SlotVariableReference\x12!\n\x19original_variable_node_id\x18\x01 \x01(\x05\x12\x11\n\tslot_name\x18\x02 \x01(\t\x12\x1d\n\x15slot_variable_node_id\x18\x03 \x01(\x05\"4\n\x0fRegisteredSaver\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bobject_name\x18\x02 \x01(\tBZZUgithub.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto\xf8\x01\x01\x62\x06proto3')



_TRACKABLEOBJECTGRAPH = DESCRIPTOR.message_types_by_name['TrackableObjectGraph']
_TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT = _TRACKABLEOBJECTGRAPH.nested_types_by_name['TrackableObject']
_TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_OBJECTREFERENCE = _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT.nested_types_by_name['ObjectReference']
_TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_SERIALIZEDTENSOR = _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT.nested_types_by_name['SerializedTensor']
_TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_SLOTVARIABLEREFERENCE = _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT.nested_types_by_name['SlotVariableReference']
_REGISTEREDSAVER = DESCRIPTOR.message_types_by_name['RegisteredSaver']
TrackableObjectGraph = _reflection.GeneratedProtocolMessageType('TrackableObjectGraph', (_message.Message,), {

  'TrackableObject' : _reflection.GeneratedProtocolMessageType('TrackableObject', (_message.Message,), {

    'ObjectReference' : _reflection.GeneratedProtocolMessageType('ObjectReference', (_message.Message,), {
      'DESCRIPTOR' : _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_OBJECTREFERENCE,
      '__module__' : 'tensorboard.compat.proto.trackable_object_graph_pb2'
      # @@protoc_insertion_point(class_scope:tensorboard.TrackableObjectGraph.TrackableObject.ObjectReference)
      })
    ,

    'SerializedTensor' : _reflection.GeneratedProtocolMessageType('SerializedTensor', (_message.Message,), {
      'DESCRIPTOR' : _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_SERIALIZEDTENSOR,
      '__module__' : 'tensorboard.compat.proto.trackable_object_graph_pb2'
      # @@protoc_insertion_point(class_scope:tensorboard.TrackableObjectGraph.TrackableObject.SerializedTensor)
      })
    ,

    'SlotVariableReference' : _reflection.GeneratedProtocolMessageType('SlotVariableReference', (_message.Message,), {
      'DESCRIPTOR' : _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_SLOTVARIABLEREFERENCE,
      '__module__' : 'tensorboard.compat.proto.trackable_object_graph_pb2'
      # @@protoc_insertion_point(class_scope:tensorboard.TrackableObjectGraph.TrackableObject.SlotVariableReference)
      })
    ,
    'DESCRIPTOR' : _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT,
    '__module__' : 'tensorboard.compat.proto.trackable_object_graph_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.TrackableObjectGraph.TrackableObject)
    })
  ,
  'DESCRIPTOR' : _TRACKABLEOBJECTGRAPH,
  '__module__' : 'tensorboard.compat.proto.trackable_object_graph_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.TrackableObjectGraph)
  })
_sym_db.RegisterMessage(TrackableObjectGraph)
_sym_db.RegisterMessage(TrackableObjectGraph.TrackableObject)
_sym_db.RegisterMessage(TrackableObjectGraph.TrackableObject.ObjectReference)
_sym_db.RegisterMessage(TrackableObjectGraph.TrackableObject.SerializedTensor)
_sym_db.RegisterMessage(TrackableObjectGraph.TrackableObject.SlotVariableReference)

RegisteredSaver = _reflection.GeneratedProtocolMessageType('RegisteredSaver', (_message.Message,), {
  'DESCRIPTOR' : _REGISTEREDSAVER,
  '__module__' : 'tensorboard.compat.proto.trackable_object_graph_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.RegisteredSaver)
  })
_sym_db.RegisterMessage(RegisteredSaver)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'ZUgithub.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto\370\001\001'
  _TRACKABLEOBJECTGRAPH._serialized_start=103
  _TRACKABLEOBJECTGRAPH._serialized_end=863
  _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT._serialized_start=194
  _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT._serialized_end=863
  _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_OBJECTREFERENCE._serialized_start=598
  _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_OBJECTREFERENCE._serialized_end=652
  _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_SERIALIZEDTENSOR._serialized_start=654
  _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_SERIALIZEDTENSOR._serialized_end=753
  _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_SLOTVARIABLEREFERENCE._serialized_start=755
  _TRACKABLEOBJECTGRAPH_TRACKABLEOBJECT_SLOTVARIABLEREFERENCE._serialized_end=863
  _REGISTEREDSAVER._serialized_start=865
  _REGISTEREDSAVER._serialized_end=917
# @@protoc_insertion_point(module_scope)
