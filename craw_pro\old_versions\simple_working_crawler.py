#!/usr/bin/env python3
"""
简化版羽毛球装备爬虫 - 基于测试程序的成功经验
直接访问，如果遇到验证就跳过，专注于能获取到的数据
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os
from urllib.parse import urljoin

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleWorkingCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        })
        
        self.base_url = "https://www.badmintoncn.com"
        self.equipment_types = {
            1: "羽毛球拍",
            2: "羽毛球鞋", 
            3: "运动包",
            4: "羽毛球线",
            5: "羽毛球",
            6: "运动服饰",
            7: "手胶"
        }
        
        os.makedirs('output', exist_ok=True)
        logger.info("🏸 简化版爬虫初始化完成")

    def simple_get(self, url, max_retries=2):
        """简单的GET请求 - 不处理复杂验证"""
        for attempt in range(max_retries):
            try:
                logger.debug(f"访问: {url}")
                response = self.session.get(url, timeout=20)
                
                if response.status_code == 200:
                    return response.text
                else:
                    logger.warning(f"状态码 {response.status_code}: {url}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                        continue
                    return None
                    
            except Exception as e:
                logger.error(f"访问失败 {attempt+1}/{max_retries}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(3)
                    continue
                return None
        
        return None

    def get_equipment_links_from_main_page(self):
        """从主装备页面获取所有装备链接 - 基于测试程序的成功经验"""
        logger.info("🔍 从主装备页面获取装备链接...")
        
        # 尝试主装备列表页
        list_url = f"{self.base_url}/cbo_eq/list.php"
        html_content = self.simple_get(list_url)
        
        if not html_content:
            logger.warning("无法访问主装备页面")
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        page_text = soup.get_text()
        
        # 检查是否有验证
        if any(keyword in page_text for keyword in ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛']):
            logger.warning("⚠️ 主页面需要验证，跳过")
            return []
        
        # 查找装备链接
        equipment_links = []
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            if href and 'view.php?eid=' in href:
                if not href.startswith('http'):
                    href = urljoin(self.base_url, href)
                equipment_links.append(href)
        
        equipment_links = list(set(equipment_links))
        logger.info(f"✅ 从主页面找到 {len(equipment_links)} 个装备链接")
        
        if len(equipment_links) > 0:
            logger.info(f"示例链接: {equipment_links[0]}")
        
        return equipment_links

    def get_equipment_links_by_type(self, equipment_type_id):
        """按类型获取装备链接"""
        type_name = self.equipment_types.get(equipment_type_id, f"类型{equipment_type_id}")
        logger.info(f"🔍 获取{type_name}装备链接...")
        
        # 尝试类型页面
        type_url = f"{self.base_url}/cbo_eq/list.php?tid={equipment_type_id}"
        html_content = self.simple_get(type_url)
        
        if not html_content:
            logger.warning(f"无法访问{type_name}页面")
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        page_text = soup.get_text()
        
        # 检查是否有验证
        if any(keyword in page_text for keyword in ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛']):
            logger.warning(f"⚠️ {type_name}页面需要验证，跳过")
            return []
        
        # 查找装备链接
        equipment_links = []
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            if href and 'view.php?eid=' in href:
                if not href.startswith('http'):
                    href = urljoin(self.base_url, href)
                equipment_links.append(href)
        
        equipment_links = list(set(equipment_links))
        logger.info(f"  ✅ 找到 {len(equipment_links)} 个{type_name}链接")
        
        return equipment_links

    def parse_equipment_detail(self, url):
        """解析装备详情"""
        try:
            eid_match = re.search(r'eid=(\d+)', url)
            equipment_id = eid_match.group(1) if eid_match else 'unknown'
            
            logger.info(f"  📋 解析装备 (ID: {equipment_id})")
            
            html_content = self.simple_get(url)
            if not html_content:
                logger.warning(f"    无法获取详情页: {url}")
                return None
            
            soup = BeautifulSoup(html_content, 'html.parser')
            page_text = soup.get_text()
            
            # 检查是否有验证
            if any(keyword in page_text for keyword in ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛']):
                logger.warning(f"    详情页需要验证，跳过")
                return None
            
            # 初始化数据
            equipment_data = {
                'equipment_id': equipment_id,
                'equipment_name': '',
                'equipment_type': '',
                'equipment_brand': '',
                'equipment_series': '',
                'equipment_description': '',
                'release_date': '',
                'equipment_introduction': '',
                'specifications': '',
                'frame_material': '',
                'shaft_material': '',
                'weight': '',
                'length': '',
                'grip_size': '',
                'shaft_stiffness': '',
                'string_tension': '',
                'balance_point': '',
                'new_avg_price': '',
                'used_avg_price': '',
                'total_registered_users': '',
                'detail_url': url,
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 提取标题
            if soup.title:
                title = soup.title.string.strip()
                equipment_name = title.replace('中羽在线 badmintoncn.com', '').strip()
                if equipment_name:
                    equipment_data['equipment_name'] = equipment_name[:100]
                    logger.info(f"    📝 装备名称: {equipment_name}")
            
            # 提取表格信息
            self.extract_table_info(soup, equipment_data)
            
            # 尝试获取价格信息
            self.get_price_info(equipment_id, equipment_data)
            
            # 显示数据完整性
            filled = sum(1 for v in equipment_data.values() if v and v != '')
            total = len(equipment_data)
            logger.info(f"    📊 数据完整性: {filled/total*100:.1f}% ({filled}/{total})")
            
            return equipment_data
            
        except Exception as e:
            logger.error(f"解析装备详情失败: {e}")
            return None

    def extract_table_info(self, soup, equipment_data):
        """提取表格信息"""
        try:
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        # 字段映射
                        mappings = {
                            '装备类型': 'equipment_type', '类型': 'equipment_type',
                            '装备品牌': 'equipment_brand', '品牌': 'equipment_brand',
                            '装备系列': 'equipment_series', '系列': 'equipment_series',
                            '上市日期': 'release_date', '发布日期': 'release_date',
                            '拍框材质': 'frame_material', '框架材质': 'frame_material',
                            '拍杆材质': 'shaft_material', '中管材质': 'shaft_material',
                            '重量': 'weight', '拍重': 'weight', '拍身重量': 'weight',
                            '长度': 'length', '拍身长度': 'length',
                            '手柄尺寸': 'grip_size', '拍柄粗细': 'grip_size',
                            '中管韧度': 'shaft_stiffness', '硬度': 'shaft_stiffness',
                            '拉线磅数': 'string_tension', '穿线磅数': 'string_tension',
                            '平衡点': 'balance_point', '重心': 'balance_point',
                        }
                        
                        for keyword, field in mappings.items():
                            if keyword in key and not equipment_data[field]:
                                equipment_data[field] = value
                                logger.debug(f"    ✅ 映射: {keyword} -> {value}")
                                break
                        
                        # 规格参数
                        if key and value:
                            if equipment_data['specifications']:
                                equipment_data['specifications'] += f"; {key}: {value}"
                            else:
                                equipment_data['specifications'] = f"{key}: {value}"
                                
        except Exception as e:
            logger.error(f"提取表格信息失败: {e}")

    def get_price_info(self, equipment_id, equipment_data):
        """获取价格信息"""
        try:
            price_url = f"{self.base_url}/cbo_eq/view_buy.php?eid={equipment_id}"
            price_html = self.simple_get(price_url)
            
            if not price_html:
                logger.debug("    ⚠️ 未获取到价格页面")
                return
            
            price_soup = BeautifulSoup(price_html, 'html.parser')
            price_text = price_soup.get_text()
            
            # 检查是否有验证
            if any(keyword in price_text for keyword in ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛']):
                logger.debug("    ⚠️ 价格页面需要验证，跳过")
                return
            
            # 价格模式
            patterns = {
                'new_avg_price': [r'最近全新均价[：:]\s*(\d+)', r'全新均价[：:]\s*(\d+)'],
                'used_avg_price': [r'最近二手均价[：:]\s*(\d+)', r'二手均价[：:]\s*(\d+)'],
                'total_registered_users': [r'总登记球友[：:]\s*(\d+)', r'登记球友[：:]\s*(\d+)'],
            }
            
            for field, field_patterns in patterns.items():
                for pattern in field_patterns:
                    match = re.search(pattern, price_text)
                    if match:
                        equipment_data[field] = match.group(1)
                        logger.debug(f"    💰 {field}: {match.group(1)}")
                        break
                        
        except Exception as e:
            logger.error(f"获取价格信息失败: {e}")

    def crawl_equipment(self, max_total_items=15):
        """爬取装备数据"""
        logger.info("🚀 开始爬取装备数据...")
        
        all_equipment_links = []
        
        # 方法1：从主页面获取
        main_links = self.get_equipment_links_from_main_page()
        all_equipment_links.extend(main_links)
        
        # 方法2：从各类型页面获取
        for type_id in self.equipment_types.keys():
            if len(all_equipment_links) >= max_total_items * 2:  # 预留一些链接
                break
            
            type_links = self.get_equipment_links_by_type(type_id)
            all_equipment_links.extend(type_links)
            
            time.sleep(1)  # 礼貌延时
        
        # 去重
        all_equipment_links = list(set(all_equipment_links))
        logger.info(f"🔗 总共收集到 {len(all_equipment_links)} 个装备链接")
        
        if not all_equipment_links:
            logger.error("❌ 未找到任何装备链接")
            return []
        
        # 解析装备详情
        all_data = []
        for i, link in enumerate(all_equipment_links[:max_total_items]):
            logger.info(f"🔍 处理装备 ({i+1}/{min(max_total_items, len(all_equipment_links))})")
            
            equipment_data = self.parse_equipment_detail(link)
            if equipment_data:
                all_data.append(equipment_data)
                logger.info(f"    ✅ 成功提取装备: {equipment_data['equipment_name']}")
            else:
                logger.warning(f"    ❌ 提取失败")
            
            time.sleep(2)  # 礼貌延时
        
        logger.info(f"\n🎉 爬取完成！成功获取 {len(all_data)} 条装备数据")
        return all_data

    def save_data(self, data):
        """保存数据"""
        if not data:
            logger.warning("没有数据需要保存")
            return
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # CSV
        csv_file = f"output/equipment_{timestamp}.csv"
        fieldnames = list(data[0].keys())
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        # JSON
        json_file = f"output/equipment_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📁 数据已保存: {csv_file}, {json_file}")
        return csv_file, json_file

    def analyze_data(self, data):
        """分析数据"""
        if not data:
            return
            
        logger.info("\n📊 数据分析:")
        logger.info(f"总数量: {len(data)}")
        
        # 类型分布
        types = {}
        brands = {}
        
        for item in data:
            eq_type = item.get('equipment_type', '未知')
            types[eq_type] = types.get(eq_type, 0) + 1
            
            brand = item.get('equipment_brand', '未知')
            brands[brand] = brands.get(brand, 0) + 1
        
        logger.info("\n类型分布:")
        for eq_type, count in sorted(types.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {eq_type}: {count}")
        
        logger.info("\n品牌分布:")
        for brand, count in sorted(brands.items(), key=lambda x: x[1], reverse=True)[:5]:
            logger.info(f"  {brand}: {count}")

def main():
    """主函数"""
    crawler = SimpleWorkingCrawler()
    
    # 爬取数据
    data = crawler.crawl_equipment(max_total_items=10)
    
    if data:
        # 保存和分析
        crawler.save_data(data)
        crawler.analyze_data(data)
    else:
        logger.error("未获取到数据")

if __name__ == "__main__":
    main() 