import json
import re
import traceback

from django.views import View

from apps.api.sync.business import SyncBusiness
from apps.utils.get_response import GetResponse
from apps.utils.logger_helper import Logger
from apps.utils.qiniu_helper import get_qiniu_data


class SyncDataView(View):

    def post(self, request):
        sync_data = json.loads(request.body)

        study_uid = sync_data.get('studyUid')
        es_Key = sync_data.get('esKey')
        try:

            ecg_info = get_qiniu_data(es_Key)

            if ecg_info:

                check_time = sync_data.get('checkTime')
                diagnosis_details = sync_data.get('DiagnosisDetails')
                diagnosis_details_ii = sync_data.get('DiagnosisDetailsII')

                data_date = re.sub(r"[-:\s]", "", check_time[:10])

                # 创建数据存储表
                SyncBusiness().create_partition_table(data_date)

                SyncBusiness.save_data(study_uid, check_time, ecg_info, data_date, diagnosis_details,
                                       diagnosis_details_ii)
            else:
                Logger().info(f'七牛云未能获取数据，参数 study_uid：{study_uid}，es_Key：{es_Key}')
            return GetResponse().get_response(code=0)
        except Exception as e:
            # 错误处理
            Logger().error(f'es_Key:{es_Key}, {traceback.format_exc()}')
            return GetResponse.get_response(code=2)
