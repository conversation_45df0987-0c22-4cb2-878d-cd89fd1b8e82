import json
import requests
import os
import csv
from qiniu import Auth

# 要导出的特定es_key
TARGET_ES_KEY = "CUSTOMER19054257726245560321078/20250401152805"

# 七牛云配置
QINIU = {
    'prod': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://upload.weiheyixue.com'
    }
}

# 本地保存路径
SAVE_DIR = r'D:\ECG\0331标注平台数据\特定查询'

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")

def sanitize_filename(filename):
    """将 es_key 转换为有效的文件名"""
    return filename.replace('/', '_').replace('\\', '_')

def parse_ecg_data(content):
    """从内容中解析ECG数据"""
    try:
        # 解码内容
        decoded_content = content.decode('utf-8')
        
        # 打印前200个字符，帮助调试
        print(f"文件内容前200字符: {decoded_content[:200]}...")
        
        # 解析JSON
        data = json.loads(decoded_content)
        
        # 打印JSON的顶层键
        if isinstance(data, dict):
            print(f"JSON 顶层键: {list(data.keys())}")
            
            # 直接检查"ecg"字段
            if "ecg" in data:
                ecg_value = data["ecg"]
                
                # 如果ecg字段是字符串，尝试将其解析为JSON数组
                if isinstance(ecg_value, str):
                    try:
                        ecg_data = json.loads(ecg_value)
                        if isinstance(ecg_data, list):
                            print(f"成功从ecg字段解析出{len(ecg_data)}个数据点")
                            return ecg_data
                    except:
                        # 尝试手动解析
                        if ecg_value.startswith('[') and ecg_value.endswith(']'):
                            try:
                                values_str = ecg_value[1:-1].split(',')
                                values = [float(x.strip()) for x in values_str]
                                print(f"手动解析出{len(values)}个数据点")
                                return values
                            except Exception as e:
                                print(f"手动解析失败: {e}")
                
                # 如果ecg字段直接是列表，直接返回
                elif isinstance(ecg_value, list):
                    print(f"ecg字段直接是列表，包含{len(ecg_value)}个数据点")
                    return ecg_value
        
        print("无法从内容中提取ECG数据")
        return None
    except Exception as e:
        print(f"解析过程中出错: {e}")
        return None

def download_and_save_ecg(es_key, environment='prod'):
    """下载并保存特定es_key的ECG数据"""
    # 确保保存目录存在
    ensure_dir_exists(SAVE_DIR)
    
    try:
        # 从配置获取七牛云参数
        access_key = QINIU[environment]['access_key']
        secret_key = QINIU[environment]['secret_key']
        domain_prefix = QINIU[environment]['domain_prefix']
        
        # 构建鉴权对象
        q = Auth(access_key, secret_key)
        
        # 构建完整的文件路径
        file_path = f'ecg/{es_key}'
        
        # 生成私有下载链接
        private_url = q.private_download_url(domain_prefix + '/' + file_path, expires=3600)
        
        # 下载文件
        print(f"正在下载: {es_key}")
        response = requests.get(private_url)
        
        if response.status_code == 200:
            # 准备保存的文件名
            safe_filename = sanitize_filename(es_key)
            
            # 1. 保存原始文件
            original_path = os.path.join(SAVE_DIR, safe_filename + '.dat')
            with open(original_path, 'wb') as f:
                f.write(response.content)
            print(f"已保存原始文件: {original_path}")
            
            # 2. 解析ECG数据并保存为CSV
            ecg_data = parse_ecg_data(response.content)
            
            if ecg_data:
                # 保存为CSV
                csv_path = os.path.join(SAVE_DIR, safe_filename + '.csv')
                with open(csv_path, 'w', newline='') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(ecg_data)  # 一行包含所有数据点
                
                print(f"已成功保存ECG数据CSV: {csv_path}")
                return True
            else:
                print(f"无法解析ECG数据，仅保存了原始文件")
                return False
        else:
            print(f"下载失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"处理过程中出错: {e}")
        return False

def query_db_for_es_key(es_key):
    """从数据库查询特定es_key的详细信息"""
    try:
        import mysql.connector
        
        # 数据库连接配置
        db_config = {
            'host': '**************',
            'port': 3308,
            'user': 'ai',
            'password': 'z8^#g4r4mz',
            'database': 'ecg_marking'
        }
        
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        
        # 执行查询
        query = "SELECT * FROM t_patient_ecg WHERE es_key = %s"
        cursor.execute(query, (es_key,))
        
        # 获取结果
        result = cursor.fetchone()
        
        # 关闭连接
        cursor.close()
        connection.close()
        
        if result:
            print("\n---- 数据库中的记录信息 ----")
            info_path = os.path.join(SAVE_DIR, sanitize_filename(es_key) + '_info.json')
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"数据库记录已保存到: {info_path}")
            
            # 打印一些关键信息
            print(f"诊断结论: {result.get('new_report_conclusion', '无')}")
            print(f"心率: {result.get('heart_rate', '无')}")
            print(f"采样率: {result.get('sample_rate', '无')}")
            
            return result
        else:
            print(f"数据库中未找到 es_key: {es_key} 的记录")
            return None
    except Exception as e:
        print(f"查询数据库时出错: {e}")
        return None

if __name__ == "__main__":
    print(f"开始导出 es_key: {TARGET_ES_KEY} 的ECG数据")
    
    # 下载并保存ECG数据
    download_success = download_and_save_ecg(TARGET_ES_KEY)
    
    # 从数据库查询额外信息
    if download_success:
        print("\n尝试从数据库查询更多信息...")
        query_db_for_es_key(TARGET_ES_KEY)
    
    print("\n任务完成") 