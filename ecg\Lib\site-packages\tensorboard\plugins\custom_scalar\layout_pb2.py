# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/custom_scalar/layout.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.tensorboard/plugins/custom_scalar/layout.proto\x12\x0btensorboard\"\x8d\x01\n\x05\x43hart\x12\r\n\x05title\x18\x01 \x01(\t\x12\x37\n\tmultiline\x18\x02 \x01(\x0b\x32\".tensorboard.MultilineChartContentH\x00\x12\x31\n\x06margin\x18\x03 \x01(\x0b\x32\x1f.tensorboard.MarginChartContentH\x00\x42\t\n\x07\x63ontent\"$\n\x15MultilineChartContent\x12\x0b\n\x03tag\x18\x01 \x03(\t\"\x83\x01\n\x12MarginChartContent\x12\x36\n\x06series\x18\x01 \x03(\x0b\x32&.tensorboard.MarginChartContent.Series\x1a\x35\n\x06Series\x12\r\n\x05value\x18\x01 \x01(\t\x12\r\n\x05lower\x18\x02 \x01(\t\x12\r\n\x05upper\x18\x03 \x01(\t\"L\n\x08\x43\x61tegory\x12\r\n\x05title\x18\x01 \x01(\t\x12!\n\x05\x63hart\x18\x02 \x03(\x0b\x32\x12.tensorboard.Chart\x12\x0e\n\x06\x63losed\x18\x03 \x01(\x08\"B\n\x06Layout\x12\x0f\n\x07version\x18\x01 \x01(\x05\x12\'\n\x08\x63\x61tegory\x18\x02 \x03(\x0b\x32\x15.tensorboard.Categoryb\x06proto3')



_CHART = DESCRIPTOR.message_types_by_name['Chart']
_MULTILINECHARTCONTENT = DESCRIPTOR.message_types_by_name['MultilineChartContent']
_MARGINCHARTCONTENT = DESCRIPTOR.message_types_by_name['MarginChartContent']
_MARGINCHARTCONTENT_SERIES = _MARGINCHARTCONTENT.nested_types_by_name['Series']
_CATEGORY = DESCRIPTOR.message_types_by_name['Category']
_LAYOUT = DESCRIPTOR.message_types_by_name['Layout']
Chart = _reflection.GeneratedProtocolMessageType('Chart', (_message.Message,), {
  'DESCRIPTOR' : _CHART,
  '__module__' : 'tensorboard.plugins.custom_scalar.layout_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.Chart)
  })
_sym_db.RegisterMessage(Chart)

MultilineChartContent = _reflection.GeneratedProtocolMessageType('MultilineChartContent', (_message.Message,), {
  'DESCRIPTOR' : _MULTILINECHARTCONTENT,
  '__module__' : 'tensorboard.plugins.custom_scalar.layout_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.MultilineChartContent)
  })
_sym_db.RegisterMessage(MultilineChartContent)

MarginChartContent = _reflection.GeneratedProtocolMessageType('MarginChartContent', (_message.Message,), {

  'Series' : _reflection.GeneratedProtocolMessageType('Series', (_message.Message,), {
    'DESCRIPTOR' : _MARGINCHARTCONTENT_SERIES,
    '__module__' : 'tensorboard.plugins.custom_scalar.layout_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.MarginChartContent.Series)
    })
  ,
  'DESCRIPTOR' : _MARGINCHARTCONTENT,
  '__module__' : 'tensorboard.plugins.custom_scalar.layout_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.MarginChartContent)
  })
_sym_db.RegisterMessage(MarginChartContent)
_sym_db.RegisterMessage(MarginChartContent.Series)

Category = _reflection.GeneratedProtocolMessageType('Category', (_message.Message,), {
  'DESCRIPTOR' : _CATEGORY,
  '__module__' : 'tensorboard.plugins.custom_scalar.layout_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.Category)
  })
_sym_db.RegisterMessage(Category)

Layout = _reflection.GeneratedProtocolMessageType('Layout', (_message.Message,), {
  'DESCRIPTOR' : _LAYOUT,
  '__module__' : 'tensorboard.plugins.custom_scalar.layout_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.Layout)
  })
_sym_db.RegisterMessage(Layout)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _CHART._serialized_start=64
  _CHART._serialized_end=205
  _MULTILINECHARTCONTENT._serialized_start=207
  _MULTILINECHARTCONTENT._serialized_end=243
  _MARGINCHARTCONTENT._serialized_start=246
  _MARGINCHARTCONTENT._serialized_end=377
  _MARGINCHARTCONTENT_SERIES._serialized_start=324
  _MARGINCHARTCONTENT_SERIES._serialized_end=377
  _CATEGORY._serialized_start=379
  _CATEGORY._serialized_end=455
  _LAYOUT._serialized_start=457
  _LAYOUT._serialized_end=523
# @@protoc_insertion_point(module_scope)
