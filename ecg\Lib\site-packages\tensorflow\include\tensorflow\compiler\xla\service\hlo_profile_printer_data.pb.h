// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/xla/service/hlo_profile_printer_data.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto;
namespace xla {
class HloProfilePrinterData;
class HloProfilePrinterDataDefaultTypeInternal;
extern HloProfilePrinterDataDefaultTypeInternal _HloProfilePrinterData_default_instance_;
class HloProfilePrinterData_ExtraMetricsEntry_DoNotUse;
class HloProfilePrinterData_ExtraMetricsEntry_DoNotUseDefaultTypeInternal;
extern HloProfilePrinterData_ExtraMetricsEntry_DoNotUseDefaultTypeInternal _HloProfilePrinterData_ExtraMetricsEntry_DoNotUse_default_instance_;
class HloProfilePrinterData_HloComputationInfo;
class HloProfilePrinterData_HloComputationInfoDefaultTypeInternal;
extern HloProfilePrinterData_HloComputationInfoDefaultTypeInternal _HloProfilePrinterData_HloComputationInfo_default_instance_;
class HloProfilePrinterData_HloInstructionInfo;
class HloProfilePrinterData_HloInstructionInfoDefaultTypeInternal;
extern HloProfilePrinterData_HloInstructionInfoDefaultTypeInternal _HloProfilePrinterData_HloInstructionInfo_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::HloProfilePrinterData* Arena::CreateMaybeMessage<::xla::HloProfilePrinterData>(Arena*);
template<> ::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse* Arena::CreateMaybeMessage<::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse>(Arena*);
template<> ::xla::HloProfilePrinterData_HloComputationInfo* Arena::CreateMaybeMessage<::xla::HloProfilePrinterData_HloComputationInfo>(Arena*);
template<> ::xla::HloProfilePrinterData_HloInstructionInfo* Arena::CreateMaybeMessage<::xla::HloProfilePrinterData_HloInstructionInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

// ===================================================================

class HloProfilePrinterData_HloInstructionInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloProfilePrinterData.HloInstructionInfo) */ {
 public:
  HloProfilePrinterData_HloInstructionInfo();
  virtual ~HloProfilePrinterData_HloInstructionInfo();

  HloProfilePrinterData_HloInstructionInfo(const HloProfilePrinterData_HloInstructionInfo& from);
  HloProfilePrinterData_HloInstructionInfo(HloProfilePrinterData_HloInstructionInfo&& from) noexcept
    : HloProfilePrinterData_HloInstructionInfo() {
    *this = ::std::move(from);
  }

  inline HloProfilePrinterData_HloInstructionInfo& operator=(const HloProfilePrinterData_HloInstructionInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloProfilePrinterData_HloInstructionInfo& operator=(HloProfilePrinterData_HloInstructionInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloProfilePrinterData_HloInstructionInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloProfilePrinterData_HloInstructionInfo* internal_default_instance() {
    return reinterpret_cast<const HloProfilePrinterData_HloInstructionInfo*>(
               &_HloProfilePrinterData_HloInstructionInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(HloProfilePrinterData_HloInstructionInfo& a, HloProfilePrinterData_HloInstructionInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(HloProfilePrinterData_HloInstructionInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloProfilePrinterData_HloInstructionInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloProfilePrinterData_HloInstructionInfo* New() const final {
    return CreateMaybeMessage<HloProfilePrinterData_HloInstructionInfo>(nullptr);
  }

  HloProfilePrinterData_HloInstructionInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloProfilePrinterData_HloInstructionInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloProfilePrinterData_HloInstructionInfo& from);
  void MergeFrom(const HloProfilePrinterData_HloInstructionInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloProfilePrinterData_HloInstructionInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloProfilePrinterData.HloInstructionInfo";
  }
  protected:
  explicit HloProfilePrinterData_HloInstructionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLongNameFieldNumber = 1,
    kShortNameFieldNumber = 2,
    kCategoryFieldNumber = 3,
    kFlopCountFieldNumber = 4,
    kTranscendentalCountFieldNumber = 5,
    kProfileIndexFieldNumber = 8,
    kBytesAccessedFieldNumber = 9,
    kOptimalSecondsFieldNumber = 7,
  };
  // string long_name = 1;
  void clear_long_name();
  const std::string& long_name() const;
  void set_long_name(const std::string& value);
  void set_long_name(std::string&& value);
  void set_long_name(const char* value);
  void set_long_name(const char* value, size_t size);
  std::string* mutable_long_name();
  std::string* release_long_name();
  void set_allocated_long_name(std::string* long_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_long_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_long_name(
      std::string* long_name);

  // string short_name = 2;
  void clear_short_name();
  const std::string& short_name() const;
  void set_short_name(const std::string& value);
  void set_short_name(std::string&& value);
  void set_short_name(const char* value);
  void set_short_name(const char* value, size_t size);
  std::string* mutable_short_name();
  std::string* release_short_name();
  void set_allocated_short_name(std::string* short_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_short_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_short_name(
      std::string* short_name);

  // string category = 3;
  void clear_category();
  const std::string& category() const;
  void set_category(const std::string& value);
  void set_category(std::string&& value);
  void set_category(const char* value);
  void set_category(const char* value, size_t size);
  std::string* mutable_category();
  std::string* release_category();
  void set_allocated_category(std::string* category);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_category();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_category(
      std::string* category);

  // float flop_count = 4;
  void clear_flop_count();
  float flop_count() const;
  void set_flop_count(float value);

  // float transcendental_count = 5;
  void clear_transcendental_count();
  float transcendental_count() const;
  void set_transcendental_count(float value);

  // int64 profile_index = 8;
  void clear_profile_index();
  ::PROTOBUF_NAMESPACE_ID::int64 profile_index() const;
  void set_profile_index(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 bytes_accessed = 9;
  void clear_bytes_accessed();
  ::PROTOBUF_NAMESPACE_ID::int64 bytes_accessed() const;
  void set_bytes_accessed(::PROTOBUF_NAMESPACE_ID::int64 value);

  // float optimal_seconds = 7;
  void clear_optimal_seconds();
  float optimal_seconds() const;
  void set_optimal_seconds(float value);

  // @@protoc_insertion_point(class_scope:xla.HloProfilePrinterData.HloInstructionInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr long_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr short_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr category_;
  float flop_count_;
  float transcendental_count_;
  ::PROTOBUF_NAMESPACE_ID::int64 profile_index_;
  ::PROTOBUF_NAMESPACE_ID::int64 bytes_accessed_;
  float optimal_seconds_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto;
};
// -------------------------------------------------------------------

class HloProfilePrinterData_HloComputationInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloProfilePrinterData.HloComputationInfo) */ {
 public:
  HloProfilePrinterData_HloComputationInfo();
  virtual ~HloProfilePrinterData_HloComputationInfo();

  HloProfilePrinterData_HloComputationInfo(const HloProfilePrinterData_HloComputationInfo& from);
  HloProfilePrinterData_HloComputationInfo(HloProfilePrinterData_HloComputationInfo&& from) noexcept
    : HloProfilePrinterData_HloComputationInfo() {
    *this = ::std::move(from);
  }

  inline HloProfilePrinterData_HloComputationInfo& operator=(const HloProfilePrinterData_HloComputationInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloProfilePrinterData_HloComputationInfo& operator=(HloProfilePrinterData_HloComputationInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloProfilePrinterData_HloComputationInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloProfilePrinterData_HloComputationInfo* internal_default_instance() {
    return reinterpret_cast<const HloProfilePrinterData_HloComputationInfo*>(
               &_HloProfilePrinterData_HloComputationInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(HloProfilePrinterData_HloComputationInfo& a, HloProfilePrinterData_HloComputationInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(HloProfilePrinterData_HloComputationInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloProfilePrinterData_HloComputationInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloProfilePrinterData_HloComputationInfo* New() const final {
    return CreateMaybeMessage<HloProfilePrinterData_HloComputationInfo>(nullptr);
  }

  HloProfilePrinterData_HloComputationInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloProfilePrinterData_HloComputationInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloProfilePrinterData_HloComputationInfo& from);
  void MergeFrom(const HloProfilePrinterData_HloComputationInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloProfilePrinterData_HloComputationInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloProfilePrinterData.HloComputationInfo";
  }
  protected:
  explicit HloProfilePrinterData_HloComputationInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstructionInfosFieldNumber = 3,
    kNameFieldNumber = 1,
    kProfileIndexFieldNumber = 2,
  };
  // repeated .xla.HloProfilePrinterData.HloInstructionInfo instruction_infos = 3;
  int instruction_infos_size() const;
  void clear_instruction_infos();
  ::xla::HloProfilePrinterData_HloInstructionInfo* mutable_instruction_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloInstructionInfo >*
      mutable_instruction_infos();
  const ::xla::HloProfilePrinterData_HloInstructionInfo& instruction_infos(int index) const;
  ::xla::HloProfilePrinterData_HloInstructionInfo* add_instruction_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloInstructionInfo >&
      instruction_infos() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // int64 profile_index = 2;
  void clear_profile_index();
  ::PROTOBUF_NAMESPACE_ID::int64 profile_index() const;
  void set_profile_index(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.HloProfilePrinterData.HloComputationInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloInstructionInfo > instruction_infos_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::int64 profile_index_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto;
};
// -------------------------------------------------------------------

class HloProfilePrinterData_ExtraMetricsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HloProfilePrinterData_ExtraMetricsEntry_DoNotUse, 
    std::string, ::PROTOBUF_NAMESPACE_ID::int64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HloProfilePrinterData_ExtraMetricsEntry_DoNotUse, 
    std::string, ::PROTOBUF_NAMESPACE_ID::int64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    0 > SuperType;
  HloProfilePrinterData_ExtraMetricsEntry_DoNotUse();
  HloProfilePrinterData_ExtraMetricsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const HloProfilePrinterData_ExtraMetricsEntry_DoNotUse& other);
  static const HloProfilePrinterData_ExtraMetricsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const HloProfilePrinterData_ExtraMetricsEntry_DoNotUse*>(&_HloProfilePrinterData_ExtraMetricsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "xla.HloProfilePrinterData.ExtraMetricsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto.file_level_metadata[2];
  }

  public:
};

// -------------------------------------------------------------------

class HloProfilePrinterData :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloProfilePrinterData) */ {
 public:
  HloProfilePrinterData();
  virtual ~HloProfilePrinterData();

  HloProfilePrinterData(const HloProfilePrinterData& from);
  HloProfilePrinterData(HloProfilePrinterData&& from) noexcept
    : HloProfilePrinterData() {
    *this = ::std::move(from);
  }

  inline HloProfilePrinterData& operator=(const HloProfilePrinterData& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloProfilePrinterData& operator=(HloProfilePrinterData&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloProfilePrinterData& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloProfilePrinterData* internal_default_instance() {
    return reinterpret_cast<const HloProfilePrinterData*>(
               &_HloProfilePrinterData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(HloProfilePrinterData& a, HloProfilePrinterData& b) {
    a.Swap(&b);
  }
  inline void Swap(HloProfilePrinterData* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloProfilePrinterData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloProfilePrinterData* New() const final {
    return CreateMaybeMessage<HloProfilePrinterData>(nullptr);
  }

  HloProfilePrinterData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloProfilePrinterData>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloProfilePrinterData& from);
  void MergeFrom(const HloProfilePrinterData& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloProfilePrinterData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloProfilePrinterData";
  }
  protected:
  explicit HloProfilePrinterData(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef HloProfilePrinterData_HloInstructionInfo HloInstructionInfo;
  typedef HloProfilePrinterData_HloComputationInfo HloComputationInfo;

  // accessors -------------------------------------------------------

  enum : int {
    kComputationInfosFieldNumber = 1,
    kExtraMetricsFieldNumber = 3,
    kEntryComputationFieldNumber = 4,
    kProfileCountersSizeFieldNumber = 2,
  };
  // repeated .xla.HloProfilePrinterData.HloComputationInfo computation_infos = 1;
  int computation_infos_size() const;
  void clear_computation_infos();
  ::xla::HloProfilePrinterData_HloComputationInfo* mutable_computation_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloComputationInfo >*
      mutable_computation_infos();
  const ::xla::HloProfilePrinterData_HloComputationInfo& computation_infos(int index) const;
  ::xla::HloProfilePrinterData_HloComputationInfo* add_computation_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloComputationInfo >&
      computation_infos() const;

  // map<string, int64> extra_metrics = 3;
  int extra_metrics_size() const;
  void clear_extra_metrics();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int64 >&
      extra_metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_extra_metrics();

  // string entry_computation = 4;
  void clear_entry_computation();
  const std::string& entry_computation() const;
  void set_entry_computation(const std::string& value);
  void set_entry_computation(std::string&& value);
  void set_entry_computation(const char* value);
  void set_entry_computation(const char* value, size_t size);
  std::string* mutable_entry_computation();
  std::string* release_entry_computation();
  void set_allocated_entry_computation(std::string* entry_computation);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_entry_computation();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_entry_computation(
      std::string* entry_computation);

  // int64 profile_counters_size = 2;
  void clear_profile_counters_size();
  ::PROTOBUF_NAMESPACE_ID::int64 profile_counters_size() const;
  void set_profile_counters_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.HloProfilePrinterData)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloComputationInfo > computation_infos_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      HloProfilePrinterData_ExtraMetricsEntry_DoNotUse,
      std::string, ::PROTOBUF_NAMESPACE_ID::int64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      0 > extra_metrics_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr entry_computation_;
  ::PROTOBUF_NAMESPACE_ID::int64 profile_counters_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// HloProfilePrinterData_HloInstructionInfo

// string long_name = 1;
inline void HloProfilePrinterData_HloInstructionInfo::clear_long_name() {
  long_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloProfilePrinterData_HloInstructionInfo::long_name() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
  return long_name_.Get();
}
inline void HloProfilePrinterData_HloInstructionInfo::set_long_name(const std::string& value) {
  
  long_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
}
inline void HloProfilePrinterData_HloInstructionInfo::set_long_name(std::string&& value) {
  
  long_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
}
inline void HloProfilePrinterData_HloInstructionInfo::set_long_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  long_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
}
inline void HloProfilePrinterData_HloInstructionInfo::set_long_name(const char* value,
    size_t size) {
  
  long_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::mutable_long_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
  return long_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::release_long_name() {
  // @@protoc_insertion_point(field_release:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
  
  return long_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloProfilePrinterData_HloInstructionInfo::set_allocated_long_name(std::string* long_name) {
  if (long_name != nullptr) {
    
  } else {
    
  }
  long_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), long_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::unsafe_arena_release_long_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return long_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloProfilePrinterData_HloInstructionInfo::unsafe_arena_set_allocated_long_name(
    std::string* long_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (long_name != nullptr) {
    
  } else {
    
  }
  long_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      long_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
}

// string short_name = 2;
inline void HloProfilePrinterData_HloInstructionInfo::clear_short_name() {
  short_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloProfilePrinterData_HloInstructionInfo::short_name() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
  return short_name_.Get();
}
inline void HloProfilePrinterData_HloInstructionInfo::set_short_name(const std::string& value) {
  
  short_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
}
inline void HloProfilePrinterData_HloInstructionInfo::set_short_name(std::string&& value) {
  
  short_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
}
inline void HloProfilePrinterData_HloInstructionInfo::set_short_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  short_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
}
inline void HloProfilePrinterData_HloInstructionInfo::set_short_name(const char* value,
    size_t size) {
  
  short_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::mutable_short_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
  return short_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::release_short_name() {
  // @@protoc_insertion_point(field_release:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
  
  return short_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloProfilePrinterData_HloInstructionInfo::set_allocated_short_name(std::string* short_name) {
  if (short_name != nullptr) {
    
  } else {
    
  }
  short_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), short_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::unsafe_arena_release_short_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return short_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloProfilePrinterData_HloInstructionInfo::unsafe_arena_set_allocated_short_name(
    std::string* short_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (short_name != nullptr) {
    
  } else {
    
  }
  short_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      short_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
}

// string category = 3;
inline void HloProfilePrinterData_HloInstructionInfo::clear_category() {
  category_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloProfilePrinterData_HloInstructionInfo::category() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.category)
  return category_.Get();
}
inline void HloProfilePrinterData_HloInstructionInfo::set_category(const std::string& value) {
  
  category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.category)
}
inline void HloProfilePrinterData_HloInstructionInfo::set_category(std::string&& value) {
  
  category_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloProfilePrinterData.HloInstructionInfo.category)
}
inline void HloProfilePrinterData_HloInstructionInfo::set_category(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloProfilePrinterData.HloInstructionInfo.category)
}
inline void HloProfilePrinterData_HloInstructionInfo::set_category(const char* value,
    size_t size) {
  
  category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloProfilePrinterData.HloInstructionInfo.category)
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::mutable_category() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.HloInstructionInfo.category)
  return category_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::release_category() {
  // @@protoc_insertion_point(field_release:xla.HloProfilePrinterData.HloInstructionInfo.category)
  
  return category_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloProfilePrinterData_HloInstructionInfo::set_allocated_category(std::string* category) {
  if (category != nullptr) {
    
  } else {
    
  }
  category_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), category,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloProfilePrinterData.HloInstructionInfo.category)
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::unsafe_arena_release_category() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloProfilePrinterData.HloInstructionInfo.category)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return category_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloProfilePrinterData_HloInstructionInfo::unsafe_arena_set_allocated_category(
    std::string* category) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (category != nullptr) {
    
  } else {
    
  }
  category_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      category, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloProfilePrinterData.HloInstructionInfo.category)
}

// float flop_count = 4;
inline void HloProfilePrinterData_HloInstructionInfo::clear_flop_count() {
  flop_count_ = 0;
}
inline float HloProfilePrinterData_HloInstructionInfo::flop_count() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.flop_count)
  return flop_count_;
}
inline void HloProfilePrinterData_HloInstructionInfo::set_flop_count(float value) {
  
  flop_count_ = value;
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.flop_count)
}

// float transcendental_count = 5;
inline void HloProfilePrinterData_HloInstructionInfo::clear_transcendental_count() {
  transcendental_count_ = 0;
}
inline float HloProfilePrinterData_HloInstructionInfo::transcendental_count() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.transcendental_count)
  return transcendental_count_;
}
inline void HloProfilePrinterData_HloInstructionInfo::set_transcendental_count(float value) {
  
  transcendental_count_ = value;
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.transcendental_count)
}

// int64 bytes_accessed = 9;
inline void HloProfilePrinterData_HloInstructionInfo::clear_bytes_accessed() {
  bytes_accessed_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloProfilePrinterData_HloInstructionInfo::bytes_accessed() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.bytes_accessed)
  return bytes_accessed_;
}
inline void HloProfilePrinterData_HloInstructionInfo::set_bytes_accessed(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  bytes_accessed_ = value;
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.bytes_accessed)
}

// float optimal_seconds = 7;
inline void HloProfilePrinterData_HloInstructionInfo::clear_optimal_seconds() {
  optimal_seconds_ = 0;
}
inline float HloProfilePrinterData_HloInstructionInfo::optimal_seconds() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.optimal_seconds)
  return optimal_seconds_;
}
inline void HloProfilePrinterData_HloInstructionInfo::set_optimal_seconds(float value) {
  
  optimal_seconds_ = value;
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.optimal_seconds)
}

// int64 profile_index = 8;
inline void HloProfilePrinterData_HloInstructionInfo::clear_profile_index() {
  profile_index_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloProfilePrinterData_HloInstructionInfo::profile_index() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.profile_index)
  return profile_index_;
}
inline void HloProfilePrinterData_HloInstructionInfo::set_profile_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  profile_index_ = value;
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.profile_index)
}

// -------------------------------------------------------------------

// HloProfilePrinterData_HloComputationInfo

// string name = 1;
inline void HloProfilePrinterData_HloComputationInfo::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloProfilePrinterData_HloComputationInfo::name() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloComputationInfo.name)
  return name_.Get();
}
inline void HloProfilePrinterData_HloComputationInfo::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloComputationInfo.name)
}
inline void HloProfilePrinterData_HloComputationInfo::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloProfilePrinterData.HloComputationInfo.name)
}
inline void HloProfilePrinterData_HloComputationInfo::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloProfilePrinterData.HloComputationInfo.name)
}
inline void HloProfilePrinterData_HloComputationInfo::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloProfilePrinterData.HloComputationInfo.name)
}
inline std::string* HloProfilePrinterData_HloComputationInfo::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.HloComputationInfo.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloProfilePrinterData_HloComputationInfo::release_name() {
  // @@protoc_insertion_point(field_release:xla.HloProfilePrinterData.HloComputationInfo.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloProfilePrinterData_HloComputationInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloProfilePrinterData.HloComputationInfo.name)
}
inline std::string* HloProfilePrinterData_HloComputationInfo::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloProfilePrinterData.HloComputationInfo.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloProfilePrinterData_HloComputationInfo::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloProfilePrinterData.HloComputationInfo.name)
}

// int64 profile_index = 2;
inline void HloProfilePrinterData_HloComputationInfo::clear_profile_index() {
  profile_index_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloProfilePrinterData_HloComputationInfo::profile_index() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloComputationInfo.profile_index)
  return profile_index_;
}
inline void HloProfilePrinterData_HloComputationInfo::set_profile_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  profile_index_ = value;
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloComputationInfo.profile_index)
}

// repeated .xla.HloProfilePrinterData.HloInstructionInfo instruction_infos = 3;
inline int HloProfilePrinterData_HloComputationInfo::instruction_infos_size() const {
  return instruction_infos_.size();
}
inline void HloProfilePrinterData_HloComputationInfo::clear_instruction_infos() {
  instruction_infos_.Clear();
}
inline ::xla::HloProfilePrinterData_HloInstructionInfo* HloProfilePrinterData_HloComputationInfo::mutable_instruction_infos(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.HloComputationInfo.instruction_infos)
  return instruction_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloInstructionInfo >*
HloProfilePrinterData_HloComputationInfo::mutable_instruction_infos() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloProfilePrinterData.HloComputationInfo.instruction_infos)
  return &instruction_infos_;
}
inline const ::xla::HloProfilePrinterData_HloInstructionInfo& HloProfilePrinterData_HloComputationInfo::instruction_infos(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloComputationInfo.instruction_infos)
  return instruction_infos_.Get(index);
}
inline ::xla::HloProfilePrinterData_HloInstructionInfo* HloProfilePrinterData_HloComputationInfo::add_instruction_infos() {
  // @@protoc_insertion_point(field_add:xla.HloProfilePrinterData.HloComputationInfo.instruction_infos)
  return instruction_infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloInstructionInfo >&
HloProfilePrinterData_HloComputationInfo::instruction_infos() const {
  // @@protoc_insertion_point(field_list:xla.HloProfilePrinterData.HloComputationInfo.instruction_infos)
  return instruction_infos_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// HloProfilePrinterData

// repeated .xla.HloProfilePrinterData.HloComputationInfo computation_infos = 1;
inline int HloProfilePrinterData::computation_infos_size() const {
  return computation_infos_.size();
}
inline void HloProfilePrinterData::clear_computation_infos() {
  computation_infos_.Clear();
}
inline ::xla::HloProfilePrinterData_HloComputationInfo* HloProfilePrinterData::mutable_computation_infos(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.computation_infos)
  return computation_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloComputationInfo >*
HloProfilePrinterData::mutable_computation_infos() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloProfilePrinterData.computation_infos)
  return &computation_infos_;
}
inline const ::xla::HloProfilePrinterData_HloComputationInfo& HloProfilePrinterData::computation_infos(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.computation_infos)
  return computation_infos_.Get(index);
}
inline ::xla::HloProfilePrinterData_HloComputationInfo* HloProfilePrinterData::add_computation_infos() {
  // @@protoc_insertion_point(field_add:xla.HloProfilePrinterData.computation_infos)
  return computation_infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloComputationInfo >&
HloProfilePrinterData::computation_infos() const {
  // @@protoc_insertion_point(field_list:xla.HloProfilePrinterData.computation_infos)
  return computation_infos_;
}

// int64 profile_counters_size = 2;
inline void HloProfilePrinterData::clear_profile_counters_size() {
  profile_counters_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloProfilePrinterData::profile_counters_size() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.profile_counters_size)
  return profile_counters_size_;
}
inline void HloProfilePrinterData::set_profile_counters_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  profile_counters_size_ = value;
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.profile_counters_size)
}

// map<string, int64> extra_metrics = 3;
inline int HloProfilePrinterData::extra_metrics_size() const {
  return extra_metrics_.size();
}
inline void HloProfilePrinterData::clear_extra_metrics() {
  extra_metrics_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int64 >&
HloProfilePrinterData::extra_metrics() const {
  // @@protoc_insertion_point(field_map:xla.HloProfilePrinterData.extra_metrics)
  return extra_metrics_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int64 >*
HloProfilePrinterData::mutable_extra_metrics() {
  // @@protoc_insertion_point(field_mutable_map:xla.HloProfilePrinterData.extra_metrics)
  return extra_metrics_.MutableMap();
}

// string entry_computation = 4;
inline void HloProfilePrinterData::clear_entry_computation() {
  entry_computation_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloProfilePrinterData::entry_computation() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.entry_computation)
  return entry_computation_.Get();
}
inline void HloProfilePrinterData::set_entry_computation(const std::string& value) {
  
  entry_computation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.entry_computation)
}
inline void HloProfilePrinterData::set_entry_computation(std::string&& value) {
  
  entry_computation_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloProfilePrinterData.entry_computation)
}
inline void HloProfilePrinterData::set_entry_computation(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  entry_computation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloProfilePrinterData.entry_computation)
}
inline void HloProfilePrinterData::set_entry_computation(const char* value,
    size_t size) {
  
  entry_computation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloProfilePrinterData.entry_computation)
}
inline std::string* HloProfilePrinterData::mutable_entry_computation() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.entry_computation)
  return entry_computation_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloProfilePrinterData::release_entry_computation() {
  // @@protoc_insertion_point(field_release:xla.HloProfilePrinterData.entry_computation)
  
  return entry_computation_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloProfilePrinterData::set_allocated_entry_computation(std::string* entry_computation) {
  if (entry_computation != nullptr) {
    
  } else {
    
  }
  entry_computation_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), entry_computation,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloProfilePrinterData.entry_computation)
}
inline std::string* HloProfilePrinterData::unsafe_arena_release_entry_computation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloProfilePrinterData.entry_computation)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return entry_computation_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloProfilePrinterData::unsafe_arena_set_allocated_entry_computation(
    std::string* entry_computation) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (entry_computation != nullptr) {
    
  } else {
    
  }
  entry_computation_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      entry_computation, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloProfilePrinterData.entry_computation)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto
