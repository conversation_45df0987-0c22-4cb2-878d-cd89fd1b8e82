{"arrowParens": "always", "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxBracketSameLine": false, "jsxSingleQuote": true, "printWidth": 120, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "tabWidth": 4, "trailingComma": "all", "useTabs": false, "vueIndentScriptAndStyle": false, "overrides": [{"files": ["*.json", "*.yml", "*.yaml", "*.md"], "options": {"tabWidth": 2}}]}