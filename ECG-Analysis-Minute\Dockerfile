# 建立 python 环境
FROM python:3.9

# 镜像作者
MAINTAINER zhouj

# 设置 python 环境变量
ENV PYTHONUNBUFFERED 1

# 在容器内/var/www/html/下创建 mysite1文件夹
# 注意：这里就是在容器里面具体操作，run起来之后会新建下面的目录
#RUN mkdir -p /home/<USER>

# 设置容器内工作目录
WORKDIR /app
ADD . .
# 将当前目录文件加入到容器工作目录中（. 表示当前宿主机目录）
# 就是将/home/<USER>/data/project下面所有的文件都会加入到容器里面，这里我们只放了我们的项目文件
#ADD . /home/<USER>

# 利用 pip 安装依赖
# 安装依赖
RUN cp ./Shanghai /etc/localtime
RUN python3 -m pip config set global.index-url https://pypi.mirrors.ustc.edu.cn/simple
RUN python3 -m pip config set install.trusted-host pypi.mirrors.ustc.edu.cn
# Windows环境下编写的start.sh每行命令结尾有多余的\r字符，需移除。
# 启动项目
RUN python3 -m pip install -r requirements.txt
#RUN sed -i 's/\r//' /start.sh
 # 设置start.sh文件可执行权限
#RUN chmod +x /home/<USER>/person_website/start.sh
RUN chmod +x ./manage.py
#ENTRYPOINT ["python3"]
