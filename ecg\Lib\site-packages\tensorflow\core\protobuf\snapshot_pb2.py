# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/core/protobuf/snapshot.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.core.framework import tensor_pb2 as tensorflow_dot_core_dot_framework_dot_tensor__pb2
from tensorflow.core.framework import tensor_shape_pb2 as tensorflow_dot_core_dot_framework_dot_tensor__shape__pb2
from tensorflow.core.framework import types_pb2 as tensorflow_dot_core_dot_framework_dot_types__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/core/protobuf/snapshot.proto',
  package='tensorflow.data.experimental',
  syntax='proto3',
  serialized_options=_b('ZUgithub.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto'),
  serialized_pb=_b('\n\'tensorflow/core/protobuf/snapshot.proto\x12\x1ctensorflow.data.experimental\x1a&tensorflow/core/framework/tensor.proto\x1a,tensorflow/core/framework/tensor_shape.proto\x1a%tensorflow/core/framework/types.proto\"9\n\x0eSnapshotRecord\x12\'\n\x06tensor\x18\x01 \x03(\x0b\x32\x17.tensorflow.TensorProto\"\xb8\x01\n\x16SnapshotMetadataRecord\x12\x12\n\ngraph_hash\x18\x01 \x01(\t\x12\x0e\n\x06run_id\x18\x02 \x01(\t\x12\x1a\n\x12\x63reation_timestamp\x18\x03 \x01(\x03\x12\x0f\n\x07version\x18\x04 \x01(\x03\x12#\n\x05\x64type\x18\x05 \x03(\x0e\x32\x14.tensorflow.DataType\x12\x14\n\x0cnum_elements\x18\x06 \x01(\x03\x12\x12\n\tfinalized\x18\xe8\x07 \x01(\x08\"_\n\x0eTensorMetadata\x12\x32\n\x0ctensor_shape\x18\x02 \x01(\x0b\x32\x1c.tensorflow.TensorShapeProto\x12\x19\n\x11tensor_size_bytes\x18\x03 \x01(\x03\"_\n\x16SnapshotTensorMetadata\x12\x45\n\x0ftensor_metadata\x18\x01 \x03(\x0b\x32,.tensorflow.data.experimental.TensorMetadataBWZUgithub.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_protob\x06proto3')
  ,
  dependencies=[tensorflow_dot_core_dot_framework_dot_tensor__pb2.DESCRIPTOR,tensorflow_dot_core_dot_framework_dot_tensor__shape__pb2.DESCRIPTOR,tensorflow_dot_core_dot_framework_dot_types__pb2.DESCRIPTOR,])




_SNAPSHOTRECORD = _descriptor.Descriptor(
  name='SnapshotRecord',
  full_name='tensorflow.data.experimental.SnapshotRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tensor', full_name='tensorflow.data.experimental.SnapshotRecord.tensor', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=198,
  serialized_end=255,
)


_SNAPSHOTMETADATARECORD = _descriptor.Descriptor(
  name='SnapshotMetadataRecord',
  full_name='tensorflow.data.experimental.SnapshotMetadataRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='graph_hash', full_name='tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='run_id', full_name='tensorflow.data.experimental.SnapshotMetadataRecord.run_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='creation_timestamp', full_name='tensorflow.data.experimental.SnapshotMetadataRecord.creation_timestamp', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='version', full_name='tensorflow.data.experimental.SnapshotMetadataRecord.version', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dtype', full_name='tensorflow.data.experimental.SnapshotMetadataRecord.dtype', index=4,
      number=5, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_elements', full_name='tensorflow.data.experimental.SnapshotMetadataRecord.num_elements', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='finalized', full_name='tensorflow.data.experimental.SnapshotMetadataRecord.finalized', index=6,
      number=1000, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=258,
  serialized_end=442,
)


_TENSORMETADATA = _descriptor.Descriptor(
  name='TensorMetadata',
  full_name='tensorflow.data.experimental.TensorMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tensor_shape', full_name='tensorflow.data.experimental.TensorMetadata.tensor_shape', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tensor_size_bytes', full_name='tensorflow.data.experimental.TensorMetadata.tensor_size_bytes', index=1,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=444,
  serialized_end=539,
)


_SNAPSHOTTENSORMETADATA = _descriptor.Descriptor(
  name='SnapshotTensorMetadata',
  full_name='tensorflow.data.experimental.SnapshotTensorMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tensor_metadata', full_name='tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=541,
  serialized_end=636,
)

_SNAPSHOTRECORD.fields_by_name['tensor'].message_type = tensorflow_dot_core_dot_framework_dot_tensor__pb2._TENSORPROTO
_SNAPSHOTMETADATARECORD.fields_by_name['dtype'].enum_type = tensorflow_dot_core_dot_framework_dot_types__pb2._DATATYPE
_TENSORMETADATA.fields_by_name['tensor_shape'].message_type = tensorflow_dot_core_dot_framework_dot_tensor__shape__pb2._TENSORSHAPEPROTO
_SNAPSHOTTENSORMETADATA.fields_by_name['tensor_metadata'].message_type = _TENSORMETADATA
DESCRIPTOR.message_types_by_name['SnapshotRecord'] = _SNAPSHOTRECORD
DESCRIPTOR.message_types_by_name['SnapshotMetadataRecord'] = _SNAPSHOTMETADATARECORD
DESCRIPTOR.message_types_by_name['TensorMetadata'] = _TENSORMETADATA
DESCRIPTOR.message_types_by_name['SnapshotTensorMetadata'] = _SNAPSHOTTENSORMETADATA
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SnapshotRecord = _reflection.GeneratedProtocolMessageType('SnapshotRecord', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTRECORD,
  '__module__' : 'tensorflow.core.protobuf.snapshot_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.data.experimental.SnapshotRecord)
  })
_sym_db.RegisterMessage(SnapshotRecord)

SnapshotMetadataRecord = _reflection.GeneratedProtocolMessageType('SnapshotMetadataRecord', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTMETADATARECORD,
  '__module__' : 'tensorflow.core.protobuf.snapshot_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.data.experimental.SnapshotMetadataRecord)
  })
_sym_db.RegisterMessage(SnapshotMetadataRecord)

TensorMetadata = _reflection.GeneratedProtocolMessageType('TensorMetadata', (_message.Message,), {
  'DESCRIPTOR' : _TENSORMETADATA,
  '__module__' : 'tensorflow.core.protobuf.snapshot_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.data.experimental.TensorMetadata)
  })
_sym_db.RegisterMessage(TensorMetadata)

SnapshotTensorMetadata = _reflection.GeneratedProtocolMessageType('SnapshotTensorMetadata', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTTENSORMETADATA,
  '__module__' : 'tensorflow.core.protobuf.snapshot_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.data.experimental.SnapshotTensorMetadata)
  })
_sym_db.RegisterMessage(SnapshotTensorMetadata)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
