// © 2016 and later: Unicode, Inc. and others.
// License & terms of use: http://www.unicode.org/copyright.html
/**
 *******************************************************************************
 * Copyright (C) 2002-2005, International Business Machines Corporation and    *
 * others. All Rights Reserved.                                                *
 *******************************************************************************
 *
 *******************************************************************************
 */
#ifndef LOCUTIL_H
#define LOCUTIL_H

#include "unicode/utypes.h"
#include "hash.h"

#if !UCONFIG_NO_SERVICE || !UCONFIG_NO_TRANSLITERATION


U_NAMESPACE_BEGIN

// temporary utility functions, till I know where to find them
// in header so tests can also access them

class U_COMMON_API LocaleUtility {
public:
  static UnicodeString& canonicalLocaleString(const UnicodeString* id, UnicodeString& result);
  static Locale& initLocaleFromName(const UnicodeString& id, Locale& result);
  static UnicodeString& initNameFromLocale(const Locale& locale, UnicodeString& result);
  static const Hashtable* getAvailableLocaleNames(const UnicodeString& bundleID);
  static UBool isFallbackOf(const UnicodeString& root, const UnicodeString& child);
};

U_NAMESPACE_END


#endif

#endif
