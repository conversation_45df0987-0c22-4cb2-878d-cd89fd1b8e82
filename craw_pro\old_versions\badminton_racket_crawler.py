#!/usr/bin/env python3
"""
羽毛球拍详细数据爬虫 - 专门用于爬取羽毛球拍的详细参数和价格信息
增强反反爬能力版本
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os
from urllib.parse import urljoin, urlparse
import random

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BadmintonRacketCrawler:
    def __init__(self, use_proxy=False):
        self.session = requests.Session()
        self.use_proxy = use_proxy
        self.current_proxy = None
        
        # 用户代理池 - 更多样化的浏览器标识
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        # 代理池 (可以添加免费代理或付费代理)
        self.proxy_list = [
            # 示例格式，需要添加真实可用的代理
            # {'http': 'http://proxy1:port', 'https': 'https://proxy1:port'},
            # {'http': 'http://proxy2:port', 'https': 'https://proxy2:port'},
        ]
        
        self.setup_session()
        
        self.base_url = "https://www.badmintoncn.com"
        self.verified = False
        self.verification_count = 0  # 验证次数计数
        self.max_verification_attempts = 3  # 最大验证尝试次数
        
        # 访问频率控制
        self.min_delay = 3
        self.max_delay = 8
        self.last_request_time = 0
        
        # 创建输出目录
        os.makedirs('data', exist_ok=True)
        os.makedirs('output', exist_ok=True)
        
        logger.info("🏸 增强版羽毛球拍爬虫初始化完成")

    def setup_session(self):
        """设置会话配置"""
        # 随机选择用户代理
        user_agent = random.choice(self.user_agents)
        
        self.session.headers.update({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'DNT': '1'
        })
        
        # 设置代理
        if self.use_proxy and self.proxy_list:
            self.rotate_proxy()
        
        logger.info(f"🔧 使用用户代理: {user_agent[:50]}...")

    def rotate_proxy(self):
        """轮换代理"""
        if self.proxy_list:
            self.current_proxy = random.choice(self.proxy_list)
            self.session.proxies.update(self.current_proxy)
            logger.info(f"🔄 切换代理: {list(self.current_proxy.values())[0]}")

    def rotate_user_agent(self):
        """轮换用户代理"""
        user_agent = random.choice(self.user_agents)
        self.session.headers.update({'User-Agent': user_agent})
        logger.info(f"🔄 切换用户代理: {user_agent[:50]}...")

    def smart_delay(self, base_delay=None):
        """智能延迟 - 根据访问频率动态调整"""
        if base_delay is None:
            delay = random.uniform(self.min_delay, self.max_delay)
        else:
            delay = random.uniform(base_delay * 0.8, base_delay * 1.2)
        
        # 确保两次请求之间有足够间隔
        current_time = time.time()
        elapsed = current_time - self.last_request_time
        
        if elapsed < delay:
            additional_delay = delay - elapsed
            if additional_delay > 0:
                logger.info(f"⏱️ 智能延迟 {additional_delay:.1f} 秒...")
                time.sleep(additional_delay)
        
        self.last_request_time = time.time()

    def reset_session(self):
        """重置会话 - 在遇到问题时使用"""
        logger.info("🔄 重置会话...")
        self.session.close()
        self.session = requests.Session()
        self.setup_session()
        self.verified = False
        self.verification_count = 0

    def initialize_session(self):
        """初始化Session - 建立稳定连接"""
        try:
            logger.info("🌐 初始化增强版Session...")
            
            # 1. 首先访问主页建立基础连接
            self.smart_delay(2)
            response = self.session.get(self.base_url, timeout=30)
            logger.info(f"主页访问状态码: {response.status_code}")
            
            if response.status_code == 200:
                logger.info("✅ 主页访问成功")
                
                # 2. 检查Cookie设置
                cookies = self.session.cookies
                logger.info(f"当前Cookie数量: {len(cookies)}")
                
                # 3. 尝试访问装备页面测试连接状态
                self.smart_delay(3)
                test_url = f"{self.base_url}/cbo_eq/list.php"
                logger.info("🧪 测试装备页面连接...")
                
                test_response = self.session.get(test_url, timeout=30)
                logger.info(f"测试页面状态码: {test_response.status_code}")
                
                # 4. 解析测试页面
                if test_response.status_code in [200, 400]:  # 400也可能有有效内容
                    test_soup = BeautifulSoup(test_response.text, 'html.parser')
                    test_text = test_soup.get_text()
                    
                    # 检查是否需要验证
                    verification_keywords = ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛']
                    needs_verification = any(keyword in test_text for keyword in verification_keywords)
                    
                    if needs_verification:
                        logger.info("🔐 检测到需要验证，Session将在首次访问时处理")
                    else:
                        # 检查是否有装备链接
                        equipment_links = [link.get('href') for link in test_soup.find_all('a', href=True) 
                                         if link.get('href') and 'view.php?eid=' in link.get('href')]
                        
                        if equipment_links:
                            logger.info(f"✅ 测试成功，找到 {len(equipment_links)} 个装备链接")
                            self.verified = True  # 标记为已验证状态
                        else:
                            logger.info("⚠️ 测试页面无装备链接，可能需要验证")
                
                self.smart_delay(3)  # 给服务器时间处理
                return True
            else:
                logger.warning(f"⚠️ 主页访问状态码异常: {response.status_code}")
                # 尝试重置会话
                if response.status_code in [403, 429]:
                    logger.info("🔄 尝试重置会话...")
                    self.reset_session()
                return True
                
        except Exception as e:
            logger.error(f"❌ Session初始化失败: {e}")
            # 尝试重置会话再试一次
            if self.verification_count < self.max_verification_attempts:
                logger.info("🔄 尝试重置会话重新初始化...")
                self.reset_session()
                return self.initialize_session()
            return False

    def ask_ai_for_answer(self, question):
        """获取AI验证答案"""
        logger.info(f"🤖 AI验证问题: {question}")
        
        # 羽毛球有几根毛？
        if '羽毛球' in question and ('几根毛' in question or '多少毛' in question or '根毛' in question):
            return "16"
        
        # ZYZX小写
        if 'ZYZX' in question and ('小写' in question or '怎么写' in question or '英文' in question):
            return "zyzx"
        
        # 中羽在线缩写
        if ('中羽' in question and '缩写' in question) or ('中羽在线' in question and ('缩写' in question or '英文' in question)):
            return "zyzx"
        
        # 数学计算
        try:
            # 清理问题文本，移除问号等
            math_text = re.sub(r'[=？?].*', '', question).strip()
            
            # 处理乘法符号
            math_text = math_text.replace('×', '*').replace('x', '*').replace('X', '*')
            
            # 验证是否为简单数学表达式
            if re.match(r'^\d+\s*[+\-*/]\s*\d+$', math_text):
                result = eval(math_text)
                logger.info(f"  🧮 计算结果: {math_text} = {result}")
                return str(result)
        except Exception as e:
            logger.debug(f"数学计算失败: {e}")
        
        logger.warning(f"⚠️ 未识别的验证问题: {question}")
        return None

    def solve_verification(self, soup, page_text, current_url):
        """解决验证问题 - 增强版"""
        try:
            # 查找验证表单
            form = soup.find('form')
            if not form:
                logger.warning("未找到验证表单")
                return False
            
            # 增强的问题模式匹配
            question_patterns = [
                r'(\d+[×*x]\d+)=？',
                r'(\d+[+]\d+)=？', 
                r'(\d+[-]\d+)=？',
                r'(\d+[/÷]\d+)=？',
                r'羽毛球有几根毛',
                r'ZYZX.*?怎么写',
                r'中羽.*?缩写',
                r'(\d+)\s*[×*x]\s*(\d+)\s*=\s*？',
                r'(\d+)\s*[+]\s*(\d+)\s*=\s*？'
            ]
            
            question = None
            for pattern in question_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    if isinstance(matches[0], tuple):
                        # 处理多组捕获的情况
                        question = matches[0][0] if len(matches[0]) > 0 else page_text
                    else:
                        question = matches[0] if isinstance(matches[0], str) else page_text
                    break
            
            if not question:
                # 更精确地寻找验证问题
                lines = page_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if re.search(r'\d+\s*[×*x+\-/÷]\s*\d+\s*=\s*？', line):
                        question = line
                        break
                    elif any(keyword in line for keyword in ['羽毛球有几根毛', 'ZYZX', '中羽']):
                        question = line
                        break
            
            if not question:
                logger.warning("未找到验证问题")
                return False
            
            answer = self.ask_ai_for_answer(question)
            if not answer:
                logger.warning("无法获取验证答案")
                return False
            
            # 查找输入字段 - 更全面的搜索
            input_field = (form.find('input', {'type': 'text'}) or 
                          form.find('input', attrs={'name': True}) or
                          form.find('input', attrs={'id': True}))
            
            if not input_field:
                # 尝试查找所有input字段
                all_inputs = form.find_all('input')
                for inp in all_inputs:
                    if inp.get('type') not in ['hidden', 'submit', 'button']:
                        input_field = inp
                        break
            
            if not input_field:
                logger.warning("未找到输入字段")
                return False
            
            # 准备提交数据
            form_data = {}
            
            # 获取所有隐藏字段
            for hidden_input in form.find_all('input', {'type': 'hidden'}):
                name = hidden_input.get('name')
                value = hidden_input.get('value', '')
                if name:
                    form_data[name] = value
            
            # 添加答案
            input_name = input_field.get('name') or input_field.get('id') or 'answer'
            form_data[input_name] = answer
            
            # 获取表单提交URL
            form_action = form.get('action', '')
            if form_action:
                if form_action.startswith('http'):
                    submit_url = form_action
                else:
                    submit_url = urljoin(current_url, form_action)
            else:
                submit_url = current_url
            
            logger.info(f"🔐 提交验证: {question} = {answer}")
            logger.info(f"提交到: {submit_url}")
            logger.info(f"表单数据: {form_data}")
            
            # 提交验证
            response = self.session.post(submit_url, data=form_data, timeout=30)
            logger.info(f"验证提交状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 检查是否验证成功
                response_soup = BeautifulSoup(response.text, 'html.parser')
                response_text = response_soup.get_text()
                
                # 检查是否还有验证问题
                has_verification = any(re.search(pattern, response_text, re.IGNORECASE) 
                                     for pattern in question_patterns)
                
                if not has_verification:
                    logger.info("✅ 验证成功")
                    self.verified = True
                    return True
                else:
                    logger.warning("⚠️ 验证后仍有验证问题")
                    # 保存验证失败的页面内容以供调试
                    logger.debug(f"验证后页面内容: {response_text[:500]}...")
            
            return False
            
        except Exception as e:
            logger.error(f"验证过程出错: {e}")
            return False

    def get_page_with_verification(self, url, max_retries=5):
        """获取页面内容，处理验证 - 增强版"""
        for retry in range(max_retries):
            try:
                logger.info(f"🌐 访问 {url} (尝试 {retry+1}/{max_retries})")
                
                # 在每次重试时可能轮换用户代理和代理
                if retry > 0:
                    if retry % 2 == 0:  # 每隔一次轮换用户代理
                        self.rotate_user_agent()
                    if self.use_proxy and retry > 2:  # 重试多次后轮换代理
                        self.rotate_proxy()
                
                # 智能延迟
                if retry > 0:
                    self.smart_delay(retry * 2)
                else:
                    self.smart_delay()
                
                response = self.session.get(url, timeout=30)
                
                if response.status_code in [200, 400]:  # 400也可能有内容
                    soup = BeautifulSoup(response.text, 'html.parser')
                    page_text = soup.get_text()
                    
                    # 检查是否需要验证
                    verification_patterns = [
                        r'\d+[×*x]\d+=？', r'\d+[+]\d+=？', r'\d+[-]\d+=？',
                        r'羽毛球有几根毛', r'ZYZX.*?怎么写', r'中羽.*?缩写',
                    ]
                    
                    has_verification = any(re.search(pattern, page_text, re.IGNORECASE) 
                                         for pattern in verification_patterns)
                    
                    if has_verification and self.verification_count < self.max_verification_attempts:
                        logger.info(f"🔐 检测到验证页面 (第{self.verification_count + 1}次)")
                        if self.solve_verification(soup, page_text, url):
                            self.verification_count += 1
                            # 验证成功后重新访问
                            self.smart_delay(5)
                            logger.info("🔄 验证后重新访问")
                            response = self.session.get(url, timeout=30)
                            if response.status_code in [200, 400]:
                                return response.text
                        else:
                            # 验证失败，重置会话
                            logger.warning("🔄 验证失败，重置会话")
                            self.reset_session()
                            continue
                    else:
                        if has_verification:
                            logger.warning(f"⚠️ 已达到最大验证次数 ({self.max_verification_attempts})")
                            # 重置会话重新开始
                            self.reset_session()
                            continue
                        else:
                            logger.info(f"✅ 获取页面内容 (状态码: {response.status_code})")
                            return response.text
                
                elif response.status_code == 403:
                    logger.warning(f"访问被拒绝 (403)，尝试 {retry+1}")
                    if retry < max_retries - 1:
                        # 403错误时重置会话
                        self.reset_session()
                        self.smart_delay(10)
                        continue
                
                elif response.status_code == 429:
                    logger.warning(f"请求过于频繁 (429)，尝试 {retry+1}")
                    if retry < max_retries - 1:
                        # 增加延迟时间
                        self.smart_delay(15)
                        continue
                
                else:
                    logger.warning(f"状态码: {response.status_code}")
                    if retry < max_retries - 1:
                        self.smart_delay(random.uniform(5, 10))
                        continue
                
            except requests.exceptions.Timeout:
                logger.warning(f"请求超时 (尝试 {retry+1})")
                if retry < max_retries - 1:
                    self.smart_delay(random.uniform(8, 15))
                    continue
            except requests.exceptions.ConnectionError:
                logger.warning(f"连接错误 (尝试 {retry+1})")
                if retry < max_retries - 1:
                    # 连接错误时重置会话
                    self.reset_session()
                    self.smart_delay(random.uniform(10, 20))
                    continue
            except Exception as e:
                logger.error(f"访问失败 (尝试 {retry+1}): {e}")
                if retry < max_retries - 1:
                    self.smart_delay(random.uniform(5, 10))
                    continue
        
        logger.error(f"❌ 无法访问页面: {url}")
        return None

    def get_racket_list(self, list_url="https://www.badmintoncn.com/cbo_eq/list.php"):
        """获取羽毛球拍列表 - 增强版"""
        logger.info("📋 获取羽毛球拍列表")
        
        # 尝试多种页面URL
        urls_to_try = [
            list_url,
            f"{self.base_url}/cbo_eq/",
            f"{self.base_url}/cbo_eq/index.php",
            f"{self.base_url}/cbo_eq/list.php?page=1",
            f"{self.base_url}/cbo_eq/list.php?brand=all"
        ]
        
        racket_links = []
        
        for url in urls_to_try:
            logger.info(f"🔍 尝试访问: {url}")
            page_content = self.get_page_with_verification(url)
            
            if not page_content:
                continue
            
            soup = BeautifulSoup(page_content, 'html.parser')
            page_text = soup.get_text()
            
            # 检查页面是否包含有效内容
            if '装备' in page_text or '球拍' in page_text or '羽毛球' in page_text:
                logger.info("✅ 找到相关页面内容")
                
                # 查找装备链接 - 使用多种模式
                link_patterns = [
                    'view.php?eid=',
                    'cbo_eq/view.php',
                    '/view.php?eid=',
                    'eid='
                ]
                
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    if href and any(pattern in href for pattern in link_patterns):
                        # 构建完整URL
                        if href.startswith('http'):
                            full_url = href
                        else:
                            full_url = urljoin(self.base_url, href)
                        
                        # 提取设备ID
                        eid_match = re.search(r'eid=(\d+)', href)
                        if eid_match:
                            equipment_id = eid_match.group(1)
                            link_text = link.get_text(strip=True)
                            
                            # 过滤掉明显不是装备的链接
                            if link_text and len(link_text) > 2 and len(link_text) < 200:
                                # 尝试从链接文本中分离品牌和名称
                                brand = ""
                                name = link_text
                                
                                # 常见品牌识别
                                brands = ['尤尼克斯', 'YONEX', '李宁', 'Li-Ning', '胜利', 'VICTOR', '波力', 'Bonny', 
                                         '川崎', 'Kawasaki', '高纤', 'Gosen', '亚狮龙', 'RSL', '凯胜', 'Kason',
                                         '威克多', 'Victor', '李宁', 'Lining', 'LINING']
                                
                                for brand_name in brands:
                                    if brand_name in link_text:
                                        brand = brand_name
                                        break
                                
                                racket_links.append({
                                    'id': equipment_id,
                                    'url': full_url,
                                    'name': name,
                                    'brand': brand,
                                    'title': link_text  # 保留原始标题作为备用
                                })
                
                # 如果找到链接，就停止尝试其他URL
                if racket_links:
                    break
                
                # 检查是否有分页或其他装备链接
                pagination_links = soup.find_all('a', href=True)
                for page_link in pagination_links:
                    page_href = page_link.get('href', '')
                    if 'page=' in page_href or 'list.php' in page_href:
                        logger.info(f"发现分页链接: {page_href}")
            else:
                logger.warning(f"⚠️ 页面内容不包含装备信息: {url}")
        
        # 如果直接访问失败，尝试通过主页导航
        if not racket_links:
            logger.info("🔍 尝试通过主页导航寻找装备链接")
            main_page_content = self.get_page_with_verification(self.base_url)
            if main_page_content:
                main_soup = BeautifulSoup(main_page_content, 'html.parser')
                
                # 查找所有可能的装备相关链接
                for link in main_soup.find_all('a', href=True):
                    href = link.get('href')
                    link_text = link.get_text(strip=True).lower()
                    
                    if href and ('装备' in link_text or '球拍' in link_text or 'cbo_eq' in href):
                        equipment_page_url = urljoin(self.base_url, href)
                        logger.info(f"🔍 尝试装备导航页面: {equipment_page_url}")
                        
                        page_content = self.get_page_with_verification(equipment_page_url)
                        if page_content:
                            soup = BeautifulSoup(page_content, 'html.parser')
                            
                            # 在这个页面中查找装备链接
                            for equipment_link in soup.find_all('a', href=True):
                                eq_href = equipment_link.get('href')
                                if eq_href and 'view.php?eid=' in eq_href:
                                    eid_match = re.search(r'eid=(\d+)', eq_href)
                                    if eid_match:
                                        equipment_id = eid_match.group(1)
                                        link_text = equipment_link.get_text(strip=True)
                                        
                                        if link_text and len(link_text) > 2:
                                            full_url = urljoin(self.base_url, eq_href)
                                            racket_links.append({
                                                'id': equipment_id,
                                                'url': full_url,
                                                'name': link_text,
                                                'brand': "",
                                                'title': link_text
                                            })
                            
                            if racket_links:
                                break
        
        # 去重
        seen_ids = set()
        unique_racket_links = []
        for racket in racket_links:
            if racket['id'] not in seen_ids:
                seen_ids.add(racket['id'])
                unique_racket_links.append(racket)
        
        logger.info(f"找到 {len(unique_racket_links)} 个羽毛球拍链接")
        
        # 如果还是没找到，使用预设的一些装备ID进行测试
        if not unique_racket_links:
            logger.info("🎯 使用预设装备ID进行测试")
            test_equipment_ids = ['22974', '6853', '4147', '15738', '18562']
            for eq_id in test_equipment_ids:
                test_url = f"{self.base_url}/cbo_eq/view.php?eid={eq_id}"
                unique_racket_links.append({
                    'id': eq_id,
                    'url': test_url,
                    'name': f"测试装备_{eq_id}",
                    'brand': "未知",
                    'title': f"测试装备_{eq_id}"
                })
            logger.info(f"添加 {len(test_equipment_ids)} 个测试装备ID")
        
        return unique_racket_links

    def extract_navigation_path(self, soup):
        """提取导航路径"""
        try:
            # 多种导航路径提取策略
            navigation_patterns = [
                # 面包屑导航
                {'selector': '.breadcrumb', 'join': ' > '},
                {'selector': '.nav-path', 'join': ' > '},
                {'selector': '.crumb', 'join': ' > '},
                # 通用路径模式
                {'selector': 'div:contains("首页")', 'join': ' > '},
                {'selector': 'span:contains("首页")', 'join': ' > '},
                {'selector': 'a:contains("首页")', 'join': ' > '}
            ]
            
            for pattern in navigation_patterns:
                elements = soup.select(pattern['selector'])
                if elements:
                    nav_parts = []
                    for elem in elements:
                        # 提取所有链接文本
                        links = elem.find_all('a')
                        if links:
                            nav_parts.extend([link.get_text().strip() for link in links if link.get_text().strip()])
                        else:
                            # 如果没有链接，直接取文本
                            text = elem.get_text().strip()
                            if text and '>' in text:
                                nav_parts.extend([part.strip() for part in text.split('>') if part.strip()])
                    
                    if nav_parts:
                        # 清理和过滤导航部分
                        cleaned_parts = []
                        for part in nav_parts:
                            part = re.sub(r'\s+', ' ', part).strip()
                            if part and part not in ['', '>', '＞']:
                                cleaned_parts.append(part)
                        
                        if cleaned_parts and len(cleaned_parts) >= 2:
                            return pattern['join'].join(cleaned_parts)
            
            # 如果没有找到标准导航，尝试从页面文本中提取
            page_text = soup.get_text()
            
            # 匹配类似 "首页 > 羽毛球拍 > 品牌 > 系列 > 产品名" 的模式
            nav_patterns = [
                r'首页[>\s＞]+([^>\n]+>[^>\n]+>[^>\n]+>[^>\n]+)',
                r'首页[>\s＞]+([^>\n]+>[^>\n]+>[^>\n]+)',
                r'首页[>\s＞]+([^>\n]+>[^>\n]+)',
                r'([^>\n]*羽毛球拍[^>\n]*>[^>\n]+>[^>\n]+)',
                r'([^>\n]*羽毛球[^>\n]*>[^>\n]+>[^>\n]+)'
            ]
            
            for pattern in nav_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    nav_path = matches[0].strip()
                    # 清理路径
                    nav_path = re.sub(r'\s+', ' ', nav_path)
                    nav_path = re.sub(r'[>\s＞]+', ' > ', nav_path)
                    if len(nav_path) > 10:  # 确保路径有意义
                        return f"首页 > {nav_path}"
                        
            return ""
        except Exception as e:
            logger.warning(f"提取导航路径失败: {e}")
            return ""

    def extract_racket_name(self, soup, page_url=""):
        """提取球拍名称"""
        try:
            # 策略1: 从标题标签提取
            title_tag = soup.find('title')
            if title_tag:
                title_text = title_tag.get_text().strip()
                # 清理标题，去掉网站名称
                title_text = re.sub(r'\s*-\s*中羽在线.*', '', title_text)
                title_text = re.sub(r'\s*\|\s*.*', '', title_text)
                if title_text and '验证' not in title_text and len(title_text) > 2:
                    return title_text.strip()
            
            # 策略2: 从h1标签提取
            h1_tags = soup.find_all('h1')
            for h1 in h1_tags:
                h1_text = h1.get_text().strip()
                if h1_text and len(h1_text) > 2 and '验证' not in h1_text:
                    return h1_text
            
            # 策略3: 从导航路径最后一部分提取
            nav_path = self.extract_navigation_path(soup)
            if nav_path and '>' in nav_path:
                parts = nav_path.split('>')
                if parts:
                    last_part = parts[-1].strip()
                    if last_part and len(last_part) > 2:
                        return last_part
            
            # 策略4: 从特定class或id提取
            name_selectors = [
                '.product-name', '.equipment-name', '.title',
                '#product-name', '#equipment-name',
                'h2', 'h3'
            ]
            
            for selector in name_selectors:
                elements = soup.select(selector)
                for elem in elements:
                    text = elem.get_text().strip()
                    if text and len(text) > 2 and '验证' not in text and len(text) < 100:
                        return text
            
            # 策略5: 从页面文本中用正则表达式提取
            page_text = soup.get_text()
            
            # 匹配常见的产品名称模式
            name_patterns = [
                r'([A-Z0-9\s]+(?:PRO|TOUR|PLAY|BP|LCW|Z|X|S|D|ASTROX|ARCSABER|NANORAY|VOLTRIC)[A-Z0-9\s]*[^\n\r]*?(?:色|款|版)?)',
                r'(ASTROX\s+\d+[^\n\r]*)',
                r'(ARCSABER\s+\d+[^\n\r]*)',
                r'(NANORAY\s+[^\n\r]*)',
                r'(VOLTRIC\s+[^\n\r]*)',
                r'([A-Z]{2,}\s+\d+[^\n\r]*)',
            ]
            
            for pattern in name_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    name = matches[0].strip()
                    # 清理名称
                    name = re.sub(r'\s+', ' ', name)
                    if len(name) > 2 and len(name) < 50:
                        return name
                        
            return ""
        except Exception as e:
            logger.warning(f"提取球拍名称失败: {e}")
            return ""

    def extract_rating_score(self, soup):
        """提取评分"""
        try:
            page_text = soup.get_text()
            
            # 精确的评分模式
            rating_patterns = [
                r'(\d+\.?\d*)\s*中羽评分',
                r'评分[：:]?\s*(\d+\.?\d*)',
                r'评分\s+(\d+\.?\d*)',
                r'(\d+\.?\d*)\s*分',
                r'评价\s*\(\d+\)\s*(\d+\.?\d*)'
            ]
            
            for pattern in rating_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    try:
                        score = float(matches[0])
                        if 0 <= score <= 10:  # 确保评分在合理范围内
                            return str(score)
                    except ValueError:
                        continue
            
            return ""
        except Exception as e:
            logger.warning(f"提取评分失败: {e}")
            return ""

    def extract_equipment_parameters(self, soup):
        """提取装备参数"""
        try:
            params = {}
            page_text = soup.get_text()
            
            # 基本信息模式
            basic_patterns = {
                'equipment_type': [
                    r'装备类型[：:\s]*([^\n\r]+)',
                    r'产品类型[：:\s]*([^\n\r]+)',
                    r'类型[：:\s]*([^\n\r]*羽毛球拍[^\n\r]*)'
                ],
                'equipment_brand': [
                    r'装备品牌[：:\s]*([^\n\r]+)',
                    r'产品品牌[：:\s]*([^\n\r]+)',
                    r'品牌[：:\s]*([^\n\r]+)'
                ],
                'equipment_series': [
                    r'装备系列[：:\s]*([^\n\r]+)',
                    r'产品系列[：:\s]*([^\n\r]+)',
                    r'系列[：:\s]*([^\n\r]+)'
                ],
                'equipment_description': [
                    r'装备介绍[：:\s]*([^\n\r]+)',
                    r'产品介绍[：:\s]*([^\n\r]+)',
                    r'介绍[：:\s]*([^\n\r]+)',
                    r'装备描述[：:\s]*([^\n\r]+)'
                ],
                'release_date': [
                    r'上市时间[：:\s]*([^\n\r]+)',
                    r'发布时间[：:\s]*([^\n\r]+)',
                    r'发售时间[：:\s]*([^\n\r]+)'
                ],
                'equipment_introduction': [
                    r'装备简介[：:\s]*([^\n\r]+)',
                    r'产品简介[：:\s]*([^\n\r]+)',
                    r'简介[：:\s]*([^\n\r]+)'
                ]
            }
            
            # 规格参数模式
            spec_patterns = {
                'frame_material': [
                    r'拍框材质[：:\s]*([^\n\r]+)',
                    r'框架材质[：:\s]*([^\n\r]+)',
                    r'拍框[：:\s]*([^\n\r]+)',
                    r'框体材料[：:\s]*([^\n\r]+)'
                ],
                'shaft_material': [
                    r'拍杆材质[：:\s]*([^\n\r]+)',
                    r'杆身材质[：:\s]*([^\n\r]+)',
                    r'拍杆[：:\s]*([^\n\r]+)',
                    r'杆体材料[：:\s]*([^\n\r]+)'
                ],
                'weight': [
                    r'球拍重量[：:\s]*([^\n\r]+)',
                    r'重量[：:\s]*([^\n\r]*g[^\n\r]*)',
                    r'拍重[：:\s]*([^\n\r]+)',
                    r'净重[：:\s]*([^\n\r]+)'
                ],
                'length': [
                    r'球拍长度[：:\s]*([^\n\r]+)',
                    r'长度[：:\s]*([^\n\r]*mm[^\n\r]*)',
                    r'拍长[：:\s]*([^\n\r]+)',
                    r'总长[：:\s]*([^\n\r]+)'
                ],
                'grip_size': [
                    r'握把尺寸[：:\s]*([^\n\r]+)',
                    r'手柄尺寸[：:\s]*([^\n\r]+)',
                    r'握把[：:\s]*([G]\d[^\n\r]*)',
                    r'手柄[：:\s]*([G]\d[^\n\r]*)',
                    r'握柄尺寸[：:\s]*([^\n\r]+)'
                ],
                'shaft_stiffness': [
                    r'拍杆硬度[：:\s]*([^\n\r]+)',
                    r'杆身硬度[：:\s]*([^\n\r]+)',
                    r'硬度[：:\s]*([^\n\r]+)',
                    r'中杆硬度[：:\s]*([^\n\r]+)'
                ],
                'string_tension': [
                    r'穿线磅数[：:\s]*([^\n\r]+)',
                    r'拉线磅数[：:\s]*([^\n\r]+)',
                    r'磅数[：:\s]*([^\n\r]*磅[^\n\r]*)',
                    r'张力[：:\s]*([^\n\r]+)',
                    r'拉力[：:\s]*([^\n\r]+)'
                ],
                'balance_point': [
                    r'平衡点[：:\s]*([^\n\r]+)',
                    r'重心[：:\s]*([^\n\r]*mm[^\n\r]*)',
                    r'重心位置[：:\s]*([^\n\r]+)',
                    r'平衡[：:\s]*([^\n\r]+)'
                ]
            }
            
            # 合并所有模式
            all_patterns = {**basic_patterns, **spec_patterns}
            
            # 提取所有参数
            for key, patterns in all_patterns.items():
                for pattern in patterns:
                    matches = re.findall(pattern, page_text)
                    if matches and matches[0].strip():
                        params[key] = matches[0].strip()
                        break
                if key not in params:
                    params[key] = ""
            
            return params
        except Exception as e:
            logger.warning(f"提取装备参数失败: {e}")
            return {
                'equipment_type': '', 'equipment_brand': '', 'equipment_series': '',
                'equipment_description': '', 'release_date': '', 'equipment_introduction': '',
                'frame_material': '', 'shaft_material': '', 'weight': '', 'length': '',
                'grip_size': '', 'shaft_stiffness': '', 'string_tension': '', 'balance_point': ''
            }

    def extract_price_info(self, soup):
        """提取价格信息"""
        try:
            price_info = {}
            page_text = soup.get_text()
            
            # 价格模式 - 更精确的匹配
            price_patterns = {
                'new_avg_price': [
                    r'新品均价[：:\s]*[¥￥]?(\d+[-~]\d+|\d+)',
                    r'最近全新均价[：:\s]*[¥￥]?(\d+[-~]\d+|\d+)',
                    r'全新均价[：:\s]*[¥￥]?(\d+[-~]\d+|\d+)',
                    r'新品价格[：:\s]*[¥￥]?(\d+[-~]\d+|\d+)'
                ],
                'used_avg_price': [
                    r'二手均价[：:\s]*[¥￥]?(\d+[-~]\d+|\d+)',
                    r'最近二手均价[：:\s]*[¥￥]?(\d+[-~]\d+|\d+)',
                    r'二手价格[：:\s]*[¥￥]?(\d+[-~]\d+|\d+)',
                    r'二手价[：:\s]*[¥￥]?(\d+[-~]\d+|\d+)'
                ],
                'total_registered_users': [
                    r'总注册人数[：:\s]*(\d+)人?',
                    r'总登记球友[：:\s]*(\d+)人?',
                    r'登记球友[：:\s]*(\d+)人?',
                    r'注册用户[：:\s]*(\d+)人?',
                    r'入手价[\s\S]*?(\d+)人',
                    r'球友数[：:\s]*(\d+)人?',
                    r'用户数[：:\s]*(\d+)人?'
                ]
            }
            
            for key, patterns in price_patterns.items():
                for pattern in patterns:
                    matches = re.findall(pattern, page_text)
                    if matches:
                        price_info[key] = matches[0].strip()
                        break
                if key not in price_info:
                    price_info[key] = ""
            
            return price_info
        except Exception as e:
            logger.warning(f"提取价格信息失败: {e}")
            return {'new_avg_price': '', 'used_avg_price': '', 'total_registered_users': ''}

    def parse_racket_detail(self, racket_info):
        """解析羽毛球拍详情页"""
        try:
            logger.info(f"🏸 解析球拍: {racket_info['id']} - {racket_info['brand']} \n{racket_info['name']}")
            
            page_url = f"https://www.badmintoncn.com/cbo_eq/view.php?eid={racket_info['id']}"
            page_content = self.get_page_with_verification(page_url)
            
            if not page_content:
                return None
            
            soup = BeautifulSoup(page_content, 'html.parser')
            
            # 检查是否为验证页面
            page_title = soup.find('title')
            if page_title and '验证' in page_title.get_text():
                logger.warning(f"⚠️ 获取到验证页面，跳过解析")
                return None
            
            # 提取核心信息
            navigation_path = self.extract_navigation_path(soup)
            racket_name = self.extract_racket_name(soup, page_url)
            rating_score = self.extract_rating_score(soup)
            equipment_params = self.extract_equipment_parameters(soup)
            price_info = self.extract_price_info(soup)
            
            # 构建完整的数据结构 - 按用户要求的字段顺序
            data = {
                # 基本信息
                'equipment_id': racket_info['id'],
                'racket_name': racket_name or racket_info['name'],
                'navigation_path': navigation_path,
                'rating_score': rating_score,
                
                # 装备信息 
                'equipment_type': equipment_params.get('equipment_type', ''),
                'equipment_brand': equipment_params.get('equipment_brand', ''),
                'equipment_series': equipment_params.get('equipment_series', ''),
                'equipment_description': equipment_params.get('equipment_description', ''),
                'release_date': equipment_params.get('release_date', ''),
                'equipment_introduction': equipment_params.get('equipment_introduction', ''),
                
                # 规格参数
                'frame_material': equipment_params.get('frame_material', ''),
                'shaft_material': equipment_params.get('shaft_material', ''),
                'weight': equipment_params.get('weight', ''),
                'length': equipment_params.get('length', ''),
                'grip_size': equipment_params.get('grip_size', ''),
                'shaft_stiffness': equipment_params.get('shaft_stiffness', ''),
                'string_tension': equipment_params.get('string_tension', ''),
                'balance_point': equipment_params.get('balance_point', ''),
                
                # 价格和用户信息
                'new_avg_price': price_info.get('new_avg_price', ''),
                'used_avg_price': price_info.get('used_avg_price', ''),
                'total_registered_users': price_info.get('total_registered_users', ''),
                
                # 元数据
                'detail_url': page_url,
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            logger.info(f"✅ 成功解析球拍: {data['racket_name']}")
            return data
            
        except Exception as e:
            logger.error(f"❌ 解析失败: {e}")
            return None

    def save_data(self, data, filename_prefix="racket_data"):
        """保存数据"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存JSON格式
            json_filename = f"data/{filename_prefix}_{timestamp}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"💾 数据已保存到: {json_filename}")
            
            # 保存CSV格式
            if data:
                csv_filename = f"data/{filename_prefix}_{timestamp}.csv"
                with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as f:
                    if isinstance(data, list) and data:
                        writer = csv.DictWriter(f, fieldnames=data[0].keys())
                        writer.writeheader()
                        writer.writerows(data)
                    else:
                        # 单个记录
                        writer = csv.DictWriter(f, fieldnames=data.keys())
                        writer.writeheader()
                        writer.writerow(data)
                logger.info(f"💾 数据已保存到: {csv_filename}")
            
            return json_filename, csv_filename
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            return None, None

    def crawl_rackets(self, max_rackets=10, start_url=None):
        """爬取羽毛球拍数据 - 增强版"""
        logger.info(f"🚀 开始增强版爬取羽毛球拍数据 (最多 {max_rackets} 个)")
        
        # 首先初始化Session
        if not self.initialize_session():
            logger.error("❌ Session初始化失败")
            return []
        
        # 获取球拍列表
        if start_url:
            racket_list = self.get_racket_list(start_url)
        else:
            racket_list = self.get_racket_list()
        
        if not racket_list:
            logger.error("❌ 无法获取球拍列表")
            return []
        
        # 限制爬取数量
        racket_list = racket_list[:max_rackets]
        
        successful_data = []
        failed_count = 0
        
        for i, racket_info in enumerate(racket_list, 1):
            try:
                logger.info(f"🔄 处理第 {i}/{len(racket_list)} 个球拍")
                
                racket_data = self.parse_racket_detail(racket_info)
                
                if racket_data:
                    successful_data.append(racket_data)
                    logger.info(f"✅ 成功处理: {racket_data['racket_name']}")
                else:
                    failed_count += 1
                    logger.warning(f"❌ 处理失败: {racket_info['name']}")
                    
                    # 失败次数过多时重置会话
                    if failed_count >= 3 and i < len(racket_list):
                        logger.info("🔄 失败次数过多，重置会话")
                        self.reset_session()
                        self.initialize_session()
                        failed_count = 0  # 重置失败计数
                
                # 动态调整延迟
                if i < len(racket_list):
                    if failed_count > 0:
                        # 有失败时增加延迟
                        self.smart_delay(random.uniform(5, 10))
                    else:
                        # 正常延迟
                        self.smart_delay()
                
            except Exception as e:
                failed_count += 1
                logger.error(f"处理球拍时出错: {e}")
                continue
        
        logger.info(f"🎯 增强版爬取完成: 成功 {len(successful_data)} 个，失败 {failed_count} 个")
        
        # 保存数据
        if successful_data:
            self.save_data(successful_data)
        
        return successful_data

def main():
    """主函数"""
    try:
        # 可以选择是否使用代理
        use_proxy = False  # 设置为 True 来启用代理（需要先配置代理列表）
        
        crawler = BadmintonRacketCrawler(use_proxy=use_proxy)
        
        # 爬取数据
        data = crawler.crawl_rackets(max_rackets=5)  # 先测试5个
        
        if data:
            print(f"\n🎉 成功爬取 {len(data)} 个羽毛球拍的数据")
            print("\n📊 样本数据:")
            for i, racket in enumerate(data[:2], 1):  # 显示前2个
                print(f"\n第 {i} 个球拍:")
                print(f"  名称: {racket['racket_name']}")
                print(f"  路径: {racket['navigation_path']}")
                print(f"  评分: {racket['rating_score']}")
                print(f"  品牌: {racket.get('equipment_brand', '未知')}")
                print(f"  系列: {racket.get('equipment_series', '未知')}")
                print(f"  最近全新均价: {racket.get('new_avg_price', '未知')}")
                print(f"  最近二手均价: {racket.get('used_avg_price', '未知')}")
                print(f"  总登记球友: {racket.get('total_registered_users', '未知')}")
        else:
            print("❌ 没有成功爬取到数据")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断爬取")
    except Exception as e:
        print(f"❌ 程序出错: {e}")

if __name__ == "__main__":
    main() 