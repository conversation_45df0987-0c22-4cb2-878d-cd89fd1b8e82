// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/debug_event.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/protobuf/graph_debug_info.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[10]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
namespace tensorflow {
class CodeLocation;
class CodeLocationDefaultTypeInternal;
extern CodeLocationDefaultTypeInternal _CodeLocation_default_instance_;
class DebugEvent;
class DebugEventDefaultTypeInternal;
extern DebugEventDefaultTypeInternal _DebugEvent_default_instance_;
class DebugMetadata;
class DebugMetadataDefaultTypeInternal;
extern DebugMetadataDefaultTypeInternal _DebugMetadata_default_instance_;
class DebuggedDevice;
class DebuggedDeviceDefaultTypeInternal;
extern DebuggedDeviceDefaultTypeInternal _DebuggedDevice_default_instance_;
class DebuggedGraph;
class DebuggedGraphDefaultTypeInternal;
extern DebuggedGraphDefaultTypeInternal _DebuggedGraph_default_instance_;
class Execution;
class ExecutionDefaultTypeInternal;
extern ExecutionDefaultTypeInternal _Execution_default_instance_;
class GraphExecutionTrace;
class GraphExecutionTraceDefaultTypeInternal;
extern GraphExecutionTraceDefaultTypeInternal _GraphExecutionTrace_default_instance_;
class GraphOpCreation;
class GraphOpCreationDefaultTypeInternal;
extern GraphOpCreationDefaultTypeInternal _GraphOpCreation_default_instance_;
class SourceFile;
class SourceFileDefaultTypeInternal;
extern SourceFileDefaultTypeInternal _SourceFile_default_instance_;
class StackFrameWithId;
class StackFrameWithIdDefaultTypeInternal;
extern StackFrameWithIdDefaultTypeInternal _StackFrameWithId_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CodeLocation* Arena::CreateMaybeMessage<::tensorflow::CodeLocation>(Arena*);
template<> ::tensorflow::DebugEvent* Arena::CreateMaybeMessage<::tensorflow::DebugEvent>(Arena*);
template<> ::tensorflow::DebugMetadata* Arena::CreateMaybeMessage<::tensorflow::DebugMetadata>(Arena*);
template<> ::tensorflow::DebuggedDevice* Arena::CreateMaybeMessage<::tensorflow::DebuggedDevice>(Arena*);
template<> ::tensorflow::DebuggedGraph* Arena::CreateMaybeMessage<::tensorflow::DebuggedGraph>(Arena*);
template<> ::tensorflow::Execution* Arena::CreateMaybeMessage<::tensorflow::Execution>(Arena*);
template<> ::tensorflow::GraphExecutionTrace* Arena::CreateMaybeMessage<::tensorflow::GraphExecutionTrace>(Arena*);
template<> ::tensorflow::GraphOpCreation* Arena::CreateMaybeMessage<::tensorflow::GraphOpCreation>(Arena*);
template<> ::tensorflow::SourceFile* Arena::CreateMaybeMessage<::tensorflow::SourceFile>(Arena*);
template<> ::tensorflow::StackFrameWithId* Arena::CreateMaybeMessage<::tensorflow::StackFrameWithId>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum TensorDebugMode : int {
  UNSPECIFIED = 0,
  NO_TENSOR = 1,
  CURT_HEALTH = 2,
  CONCISE_HEALTH = 3,
  FULL_HEALTH = 4,
  SHAPE = 5,
  FULL_NUMERICS = 6,
  FULL_TENSOR = 7,
  REDUCE_INF_NAN_THREE_SLOTS = 8,
  TensorDebugMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  TensorDebugMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool TensorDebugMode_IsValid(int value);
constexpr TensorDebugMode TensorDebugMode_MIN = UNSPECIFIED;
constexpr TensorDebugMode TensorDebugMode_MAX = REDUCE_INF_NAN_THREE_SLOTS;
constexpr int TensorDebugMode_ARRAYSIZE = TensorDebugMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TensorDebugMode_descriptor();
template<typename T>
inline const std::string& TensorDebugMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TensorDebugMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TensorDebugMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TensorDebugMode_descriptor(), enum_t_value);
}
inline bool TensorDebugMode_Parse(
    const std::string& name, TensorDebugMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TensorDebugMode>(
    TensorDebugMode_descriptor(), name, value);
}
// ===================================================================

class DebugEvent :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebugEvent) */ {
 public:
  DebugEvent();
  virtual ~DebugEvent();

  DebugEvent(const DebugEvent& from);
  DebugEvent(DebugEvent&& from) noexcept
    : DebugEvent() {
    *this = ::std::move(from);
  }

  inline DebugEvent& operator=(const DebugEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebugEvent& operator=(DebugEvent&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DebugEvent& default_instance();

  enum WhatCase {
    kDebugMetadata = 3,
    kSourceFile = 4,
    kStackFrameWithId = 6,
    kGraphOpCreation = 7,
    kDebuggedGraph = 8,
    kExecution = 9,
    kGraphExecutionTrace = 10,
    kGraphId = 11,
    kDebuggedDevice = 12,
    WHAT_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebugEvent* internal_default_instance() {
    return reinterpret_cast<const DebugEvent*>(
               &_DebugEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DebugEvent& a, DebugEvent& b) {
    a.Swap(&b);
  }
  inline void Swap(DebugEvent* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebugEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DebugEvent* New() const final {
    return CreateMaybeMessage<DebugEvent>(nullptr);
  }

  DebugEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DebugEvent>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DebugEvent& from);
  void MergeFrom(const DebugEvent& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugEvent* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebugEvent";
  }
  protected:
  explicit DebugEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWallTimeFieldNumber = 1,
    kStepFieldNumber = 2,
    kDebugMetadataFieldNumber = 3,
    kSourceFileFieldNumber = 4,
    kStackFrameWithIdFieldNumber = 6,
    kGraphOpCreationFieldNumber = 7,
    kDebuggedGraphFieldNumber = 8,
    kExecutionFieldNumber = 9,
    kGraphExecutionTraceFieldNumber = 10,
    kGraphIdFieldNumber = 11,
    kDebuggedDeviceFieldNumber = 12,
  };
  // double wall_time = 1;
  void clear_wall_time();
  double wall_time() const;
  void set_wall_time(double value);

  // int64 step = 2;
  void clear_step();
  ::PROTOBUF_NAMESPACE_ID::int64 step() const;
  void set_step(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .tensorflow.DebugMetadata debug_metadata = 3;
  bool has_debug_metadata() const;
  void clear_debug_metadata();
  const ::tensorflow::DebugMetadata& debug_metadata() const;
  ::tensorflow::DebugMetadata* release_debug_metadata();
  ::tensorflow::DebugMetadata* mutable_debug_metadata();
  void set_allocated_debug_metadata(::tensorflow::DebugMetadata* debug_metadata);
  void unsafe_arena_set_allocated_debug_metadata(
      ::tensorflow::DebugMetadata* debug_metadata);
  ::tensorflow::DebugMetadata* unsafe_arena_release_debug_metadata();

  // .tensorflow.SourceFile source_file = 4;
  bool has_source_file() const;
  void clear_source_file();
  const ::tensorflow::SourceFile& source_file() const;
  ::tensorflow::SourceFile* release_source_file();
  ::tensorflow::SourceFile* mutable_source_file();
  void set_allocated_source_file(::tensorflow::SourceFile* source_file);
  void unsafe_arena_set_allocated_source_file(
      ::tensorflow::SourceFile* source_file);
  ::tensorflow::SourceFile* unsafe_arena_release_source_file();

  // .tensorflow.StackFrameWithId stack_frame_with_id = 6;
  bool has_stack_frame_with_id() const;
  void clear_stack_frame_with_id();
  const ::tensorflow::StackFrameWithId& stack_frame_with_id() const;
  ::tensorflow::StackFrameWithId* release_stack_frame_with_id();
  ::tensorflow::StackFrameWithId* mutable_stack_frame_with_id();
  void set_allocated_stack_frame_with_id(::tensorflow::StackFrameWithId* stack_frame_with_id);
  void unsafe_arena_set_allocated_stack_frame_with_id(
      ::tensorflow::StackFrameWithId* stack_frame_with_id);
  ::tensorflow::StackFrameWithId* unsafe_arena_release_stack_frame_with_id();

  // .tensorflow.GraphOpCreation graph_op_creation = 7;
  bool has_graph_op_creation() const;
  void clear_graph_op_creation();
  const ::tensorflow::GraphOpCreation& graph_op_creation() const;
  ::tensorflow::GraphOpCreation* release_graph_op_creation();
  ::tensorflow::GraphOpCreation* mutable_graph_op_creation();
  void set_allocated_graph_op_creation(::tensorflow::GraphOpCreation* graph_op_creation);
  void unsafe_arena_set_allocated_graph_op_creation(
      ::tensorflow::GraphOpCreation* graph_op_creation);
  ::tensorflow::GraphOpCreation* unsafe_arena_release_graph_op_creation();

  // .tensorflow.DebuggedGraph debugged_graph = 8;
  bool has_debugged_graph() const;
  void clear_debugged_graph();
  const ::tensorflow::DebuggedGraph& debugged_graph() const;
  ::tensorflow::DebuggedGraph* release_debugged_graph();
  ::tensorflow::DebuggedGraph* mutable_debugged_graph();
  void set_allocated_debugged_graph(::tensorflow::DebuggedGraph* debugged_graph);
  void unsafe_arena_set_allocated_debugged_graph(
      ::tensorflow::DebuggedGraph* debugged_graph);
  ::tensorflow::DebuggedGraph* unsafe_arena_release_debugged_graph();

  // .tensorflow.Execution execution = 9;
  bool has_execution() const;
  void clear_execution();
  const ::tensorflow::Execution& execution() const;
  ::tensorflow::Execution* release_execution();
  ::tensorflow::Execution* mutable_execution();
  void set_allocated_execution(::tensorflow::Execution* execution);
  void unsafe_arena_set_allocated_execution(
      ::tensorflow::Execution* execution);
  ::tensorflow::Execution* unsafe_arena_release_execution();

  // .tensorflow.GraphExecutionTrace graph_execution_trace = 10;
  bool has_graph_execution_trace() const;
  void clear_graph_execution_trace();
  const ::tensorflow::GraphExecutionTrace& graph_execution_trace() const;
  ::tensorflow::GraphExecutionTrace* release_graph_execution_trace();
  ::tensorflow::GraphExecutionTrace* mutable_graph_execution_trace();
  void set_allocated_graph_execution_trace(::tensorflow::GraphExecutionTrace* graph_execution_trace);
  void unsafe_arena_set_allocated_graph_execution_trace(
      ::tensorflow::GraphExecutionTrace* graph_execution_trace);
  ::tensorflow::GraphExecutionTrace* unsafe_arena_release_graph_execution_trace();

  // string graph_id = 11;
  private:
  bool has_graph_id() const;
  public:
  void clear_graph_id();
  const std::string& graph_id() const;
  void set_graph_id(const std::string& value);
  void set_graph_id(std::string&& value);
  void set_graph_id(const char* value);
  void set_graph_id(const char* value, size_t size);
  std::string* mutable_graph_id();
  std::string* release_graph_id();
  void set_allocated_graph_id(std::string* graph_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_graph_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_id(
      std::string* graph_id);

  // .tensorflow.DebuggedDevice debugged_device = 12;
  bool has_debugged_device() const;
  void clear_debugged_device();
  const ::tensorflow::DebuggedDevice& debugged_device() const;
  ::tensorflow::DebuggedDevice* release_debugged_device();
  ::tensorflow::DebuggedDevice* mutable_debugged_device();
  void set_allocated_debugged_device(::tensorflow::DebuggedDevice* debugged_device);
  void unsafe_arena_set_allocated_debugged_device(
      ::tensorflow::DebuggedDevice* debugged_device);
  ::tensorflow::DebuggedDevice* unsafe_arena_release_debugged_device();

  void clear_what();
  WhatCase what_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.DebugEvent)
 private:
  class _Internal;
  void set_has_debug_metadata();
  void set_has_source_file();
  void set_has_stack_frame_with_id();
  void set_has_graph_op_creation();
  void set_has_debugged_graph();
  void set_has_execution();
  void set_has_graph_execution_trace();
  void set_has_graph_id();
  void set_has_debugged_device();

  inline bool has_what() const;
  inline void clear_has_what();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double wall_time_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_;
  union WhatUnion {
    WhatUnion() {}
    ::tensorflow::DebugMetadata* debug_metadata_;
    ::tensorflow::SourceFile* source_file_;
    ::tensorflow::StackFrameWithId* stack_frame_with_id_;
    ::tensorflow::GraphOpCreation* graph_op_creation_;
    ::tensorflow::DebuggedGraph* debugged_graph_;
    ::tensorflow::Execution* execution_;
    ::tensorflow::GraphExecutionTrace* graph_execution_trace_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_id_;
    ::tensorflow::DebuggedDevice* debugged_device_;
  } what_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class DebugMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebugMetadata) */ {
 public:
  DebugMetadata();
  virtual ~DebugMetadata();

  DebugMetadata(const DebugMetadata& from);
  DebugMetadata(DebugMetadata&& from) noexcept
    : DebugMetadata() {
    *this = ::std::move(from);
  }

  inline DebugMetadata& operator=(const DebugMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebugMetadata& operator=(DebugMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DebugMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebugMetadata* internal_default_instance() {
    return reinterpret_cast<const DebugMetadata*>(
               &_DebugMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DebugMetadata& a, DebugMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(DebugMetadata* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebugMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DebugMetadata* New() const final {
    return CreateMaybeMessage<DebugMetadata>(nullptr);
  }

  DebugMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DebugMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DebugMetadata& from);
  void MergeFrom(const DebugMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebugMetadata";
  }
  protected:
  explicit DebugMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorflowVersionFieldNumber = 1,
    kFileVersionFieldNumber = 2,
    kTfdbgRunIdFieldNumber = 3,
  };
  // string tensorflow_version = 1;
  void clear_tensorflow_version();
  const std::string& tensorflow_version() const;
  void set_tensorflow_version(const std::string& value);
  void set_tensorflow_version(std::string&& value);
  void set_tensorflow_version(const char* value);
  void set_tensorflow_version(const char* value, size_t size);
  std::string* mutable_tensorflow_version();
  std::string* release_tensorflow_version();
  void set_allocated_tensorflow_version(std::string* tensorflow_version);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_tensorflow_version();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_tensorflow_version(
      std::string* tensorflow_version);

  // string file_version = 2;
  void clear_file_version();
  const std::string& file_version() const;
  void set_file_version(const std::string& value);
  void set_file_version(std::string&& value);
  void set_file_version(const char* value);
  void set_file_version(const char* value, size_t size);
  std::string* mutable_file_version();
  std::string* release_file_version();
  void set_allocated_file_version(std::string* file_version);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_file_version();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_file_version(
      std::string* file_version);

  // string tfdbg_run_id = 3;
  void clear_tfdbg_run_id();
  const std::string& tfdbg_run_id() const;
  void set_tfdbg_run_id(const std::string& value);
  void set_tfdbg_run_id(std::string&& value);
  void set_tfdbg_run_id(const char* value);
  void set_tfdbg_run_id(const char* value, size_t size);
  std::string* mutable_tfdbg_run_id();
  std::string* release_tfdbg_run_id();
  void set_allocated_tfdbg_run_id(std::string* tfdbg_run_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_tfdbg_run_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_tfdbg_run_id(
      std::string* tfdbg_run_id);

  // @@protoc_insertion_point(class_scope:tensorflow.DebugMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tensorflow_version_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_version_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tfdbg_run_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class SourceFile :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SourceFile) */ {
 public:
  SourceFile();
  virtual ~SourceFile();

  SourceFile(const SourceFile& from);
  SourceFile(SourceFile&& from) noexcept
    : SourceFile() {
    *this = ::std::move(from);
  }

  inline SourceFile& operator=(const SourceFile& from) {
    CopyFrom(from);
    return *this;
  }
  inline SourceFile& operator=(SourceFile&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SourceFile& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SourceFile* internal_default_instance() {
    return reinterpret_cast<const SourceFile*>(
               &_SourceFile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SourceFile& a, SourceFile& b) {
    a.Swap(&b);
  }
  inline void Swap(SourceFile* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SourceFile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SourceFile* New() const final {
    return CreateMaybeMessage<SourceFile>(nullptr);
  }

  SourceFile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SourceFile>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SourceFile& from);
  void MergeFrom(const SourceFile& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SourceFile* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SourceFile";
  }
  protected:
  explicit SourceFile(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLinesFieldNumber = 3,
    kFilePathFieldNumber = 1,
    kHostNameFieldNumber = 2,
  };
  // repeated string lines = 3;
  int lines_size() const;
  void clear_lines();
  const std::string& lines(int index) const;
  std::string* mutable_lines(int index);
  void set_lines(int index, const std::string& value);
  void set_lines(int index, std::string&& value);
  void set_lines(int index, const char* value);
  void set_lines(int index, const char* value, size_t size);
  std::string* add_lines();
  void add_lines(const std::string& value);
  void add_lines(std::string&& value);
  void add_lines(const char* value);
  void add_lines(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& lines() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_lines();

  // string file_path = 1;
  void clear_file_path();
  const std::string& file_path() const;
  void set_file_path(const std::string& value);
  void set_file_path(std::string&& value);
  void set_file_path(const char* value);
  void set_file_path(const char* value, size_t size);
  std::string* mutable_file_path();
  std::string* release_file_path();
  void set_allocated_file_path(std::string* file_path);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_file_path();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_file_path(
      std::string* file_path);

  // string host_name = 2;
  void clear_host_name();
  const std::string& host_name() const;
  void set_host_name(const std::string& value);
  void set_host_name(std::string&& value);
  void set_host_name(const char* value);
  void set_host_name(const char* value, size_t size);
  std::string* mutable_host_name();
  std::string* release_host_name();
  void set_allocated_host_name(std::string* host_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_host_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_host_name(
      std::string* host_name);

  // @@protoc_insertion_point(class_scope:tensorflow.SourceFile)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> lines_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_path_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class StackFrameWithId :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.StackFrameWithId) */ {
 public:
  StackFrameWithId();
  virtual ~StackFrameWithId();

  StackFrameWithId(const StackFrameWithId& from);
  StackFrameWithId(StackFrameWithId&& from) noexcept
    : StackFrameWithId() {
    *this = ::std::move(from);
  }

  inline StackFrameWithId& operator=(const StackFrameWithId& from) {
    CopyFrom(from);
    return *this;
  }
  inline StackFrameWithId& operator=(StackFrameWithId&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StackFrameWithId& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StackFrameWithId* internal_default_instance() {
    return reinterpret_cast<const StackFrameWithId*>(
               &_StackFrameWithId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(StackFrameWithId& a, StackFrameWithId& b) {
    a.Swap(&b);
  }
  inline void Swap(StackFrameWithId* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StackFrameWithId* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StackFrameWithId* New() const final {
    return CreateMaybeMessage<StackFrameWithId>(nullptr);
  }

  StackFrameWithId* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StackFrameWithId>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StackFrameWithId& from);
  void MergeFrom(const StackFrameWithId& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StackFrameWithId* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.StackFrameWithId";
  }
  protected:
  explicit StackFrameWithId(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kFileLineColFieldNumber = 2,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  void set_id(const std::string& value);
  void set_id(std::string&& value);
  void set_id(const char* value);
  void set_id(const char* value, size_t size);
  std::string* mutable_id();
  std::string* release_id();
  void set_allocated_id(std::string* id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_id(
      std::string* id);

  // .tensorflow.GraphDebugInfo.FileLineCol file_line_col = 2;
  bool has_file_line_col() const;
  void clear_file_line_col();
  const ::tensorflow::GraphDebugInfo_FileLineCol& file_line_col() const;
  ::tensorflow::GraphDebugInfo_FileLineCol* release_file_line_col();
  ::tensorflow::GraphDebugInfo_FileLineCol* mutable_file_line_col();
  void set_allocated_file_line_col(::tensorflow::GraphDebugInfo_FileLineCol* file_line_col);
  void unsafe_arena_set_allocated_file_line_col(
      ::tensorflow::GraphDebugInfo_FileLineCol* file_line_col);
  ::tensorflow::GraphDebugInfo_FileLineCol* unsafe_arena_release_file_line_col();

  // @@protoc_insertion_point(class_scope:tensorflow.StackFrameWithId)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::tensorflow::GraphDebugInfo_FileLineCol* file_line_col_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class CodeLocation :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CodeLocation) */ {
 public:
  CodeLocation();
  virtual ~CodeLocation();

  CodeLocation(const CodeLocation& from);
  CodeLocation(CodeLocation&& from) noexcept
    : CodeLocation() {
    *this = ::std::move(from);
  }

  inline CodeLocation& operator=(const CodeLocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline CodeLocation& operator=(CodeLocation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CodeLocation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CodeLocation* internal_default_instance() {
    return reinterpret_cast<const CodeLocation*>(
               &_CodeLocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(CodeLocation& a, CodeLocation& b) {
    a.Swap(&b);
  }
  inline void Swap(CodeLocation* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CodeLocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CodeLocation* New() const final {
    return CreateMaybeMessage<CodeLocation>(nullptr);
  }

  CodeLocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CodeLocation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CodeLocation& from);
  void MergeFrom(const CodeLocation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CodeLocation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CodeLocation";
  }
  protected:
  explicit CodeLocation(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStackFrameIdsFieldNumber = 2,
    kHostNameFieldNumber = 1,
  };
  // repeated string stack_frame_ids = 2;
  int stack_frame_ids_size() const;
  void clear_stack_frame_ids();
  const std::string& stack_frame_ids(int index) const;
  std::string* mutable_stack_frame_ids(int index);
  void set_stack_frame_ids(int index, const std::string& value);
  void set_stack_frame_ids(int index, std::string&& value);
  void set_stack_frame_ids(int index, const char* value);
  void set_stack_frame_ids(int index, const char* value, size_t size);
  std::string* add_stack_frame_ids();
  void add_stack_frame_ids(const std::string& value);
  void add_stack_frame_ids(std::string&& value);
  void add_stack_frame_ids(const char* value);
  void add_stack_frame_ids(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& stack_frame_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_stack_frame_ids();

  // string host_name = 1;
  void clear_host_name();
  const std::string& host_name() const;
  void set_host_name(const std::string& value);
  void set_host_name(std::string&& value);
  void set_host_name(const char* value);
  void set_host_name(const char* value, size_t size);
  std::string* mutable_host_name();
  std::string* release_host_name();
  void set_allocated_host_name(std::string* host_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_host_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_host_name(
      std::string* host_name);

  // @@protoc_insertion_point(class_scope:tensorflow.CodeLocation)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> stack_frame_ids_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class GraphOpCreation :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphOpCreation) */ {
 public:
  GraphOpCreation();
  virtual ~GraphOpCreation();

  GraphOpCreation(const GraphOpCreation& from);
  GraphOpCreation(GraphOpCreation&& from) noexcept
    : GraphOpCreation() {
    *this = ::std::move(from);
  }

  inline GraphOpCreation& operator=(const GraphOpCreation& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphOpCreation& operator=(GraphOpCreation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphOpCreation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphOpCreation* internal_default_instance() {
    return reinterpret_cast<const GraphOpCreation*>(
               &_GraphOpCreation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GraphOpCreation& a, GraphOpCreation& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphOpCreation* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphOpCreation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphOpCreation* New() const final {
    return CreateMaybeMessage<GraphOpCreation>(nullptr);
  }

  GraphOpCreation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphOpCreation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphOpCreation& from);
  void MergeFrom(const GraphOpCreation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphOpCreation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphOpCreation";
  }
  protected:
  explicit GraphOpCreation(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputNamesFieldNumber = 6,
    kOutputTensorIdsFieldNumber = 9,
    kOpTypeFieldNumber = 1,
    kOpNameFieldNumber = 2,
    kGraphNameFieldNumber = 3,
    kGraphIdFieldNumber = 4,
    kDeviceNameFieldNumber = 5,
    kCodeLocationFieldNumber = 8,
    kNumOutputsFieldNumber = 7,
  };
  // repeated string input_names = 6;
  int input_names_size() const;
  void clear_input_names();
  const std::string& input_names(int index) const;
  std::string* mutable_input_names(int index);
  void set_input_names(int index, const std::string& value);
  void set_input_names(int index, std::string&& value);
  void set_input_names(int index, const char* value);
  void set_input_names(int index, const char* value, size_t size);
  std::string* add_input_names();
  void add_input_names(const std::string& value);
  void add_input_names(std::string&& value);
  void add_input_names(const char* value);
  void add_input_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& input_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_input_names();

  // repeated int32 output_tensor_ids = 9;
  int output_tensor_ids_size() const;
  void clear_output_tensor_ids();
  ::PROTOBUF_NAMESPACE_ID::int32 output_tensor_ids(int index) const;
  void set_output_tensor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_output_tensor_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      output_tensor_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_output_tensor_ids();

  // string op_type = 1;
  void clear_op_type();
  const std::string& op_type() const;
  void set_op_type(const std::string& value);
  void set_op_type(std::string&& value);
  void set_op_type(const char* value);
  void set_op_type(const char* value, size_t size);
  std::string* mutable_op_type();
  std::string* release_op_type();
  void set_allocated_op_type(std::string* op_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_op_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op_type(
      std::string* op_type);

  // string op_name = 2;
  void clear_op_name();
  const std::string& op_name() const;
  void set_op_name(const std::string& value);
  void set_op_name(std::string&& value);
  void set_op_name(const char* value);
  void set_op_name(const char* value, size_t size);
  std::string* mutable_op_name();
  std::string* release_op_name();
  void set_allocated_op_name(std::string* op_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_op_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op_name(
      std::string* op_name);

  // string graph_name = 3;
  void clear_graph_name();
  const std::string& graph_name() const;
  void set_graph_name(const std::string& value);
  void set_graph_name(std::string&& value);
  void set_graph_name(const char* value);
  void set_graph_name(const char* value, size_t size);
  std::string* mutable_graph_name();
  std::string* release_graph_name();
  void set_allocated_graph_name(std::string* graph_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_graph_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_name(
      std::string* graph_name);

  // string graph_id = 4;
  void clear_graph_id();
  const std::string& graph_id() const;
  void set_graph_id(const std::string& value);
  void set_graph_id(std::string&& value);
  void set_graph_id(const char* value);
  void set_graph_id(const char* value, size_t size);
  std::string* mutable_graph_id();
  std::string* release_graph_id();
  void set_allocated_graph_id(std::string* graph_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_graph_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_id(
      std::string* graph_id);

  // string device_name = 5;
  void clear_device_name();
  const std::string& device_name() const;
  void set_device_name(const std::string& value);
  void set_device_name(std::string&& value);
  void set_device_name(const char* value);
  void set_device_name(const char* value, size_t size);
  std::string* mutable_device_name();
  std::string* release_device_name();
  void set_allocated_device_name(std::string* device_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_name(
      std::string* device_name);

  // .tensorflow.CodeLocation code_location = 8;
  bool has_code_location() const;
  void clear_code_location();
  const ::tensorflow::CodeLocation& code_location() const;
  ::tensorflow::CodeLocation* release_code_location();
  ::tensorflow::CodeLocation* mutable_code_location();
  void set_allocated_code_location(::tensorflow::CodeLocation* code_location);
  void unsafe_arena_set_allocated_code_location(
      ::tensorflow::CodeLocation* code_location);
  ::tensorflow::CodeLocation* unsafe_arena_release_code_location();

  // int32 num_outputs = 7;
  void clear_num_outputs();
  ::PROTOBUF_NAMESPACE_ID::int32 num_outputs() const;
  void set_num_outputs(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphOpCreation)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> input_names_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > output_tensor_ids_;
  mutable std::atomic<int> _output_tensor_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_name_;
  ::tensorflow::CodeLocation* code_location_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_outputs_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class DebuggedGraph :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebuggedGraph) */ {
 public:
  DebuggedGraph();
  virtual ~DebuggedGraph();

  DebuggedGraph(const DebuggedGraph& from);
  DebuggedGraph(DebuggedGraph&& from) noexcept
    : DebuggedGraph() {
    *this = ::std::move(from);
  }

  inline DebuggedGraph& operator=(const DebuggedGraph& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggedGraph& operator=(DebuggedGraph&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DebuggedGraph& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebuggedGraph* internal_default_instance() {
    return reinterpret_cast<const DebuggedGraph*>(
               &_DebuggedGraph_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DebuggedGraph& a, DebuggedGraph& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggedGraph* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebuggedGraph* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DebuggedGraph* New() const final {
    return CreateMaybeMessage<DebuggedGraph>(nullptr);
  }

  DebuggedGraph* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DebuggedGraph>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DebuggedGraph& from);
  void MergeFrom(const DebuggedGraph& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggedGraph* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebuggedGraph";
  }
  protected:
  explicit DebuggedGraph(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstrumentedOpsFieldNumber = 3,
    kGraphIdFieldNumber = 1,
    kGraphNameFieldNumber = 2,
    kOriginalGraphDefFieldNumber = 4,
    kInstrumentedGraphDefFieldNumber = 5,
    kOuterContextIdFieldNumber = 6,
  };
  // repeated string instrumented_ops = 3;
  int instrumented_ops_size() const;
  void clear_instrumented_ops();
  const std::string& instrumented_ops(int index) const;
  std::string* mutable_instrumented_ops(int index);
  void set_instrumented_ops(int index, const std::string& value);
  void set_instrumented_ops(int index, std::string&& value);
  void set_instrumented_ops(int index, const char* value);
  void set_instrumented_ops(int index, const char* value, size_t size);
  std::string* add_instrumented_ops();
  void add_instrumented_ops(const std::string& value);
  void add_instrumented_ops(std::string&& value);
  void add_instrumented_ops(const char* value);
  void add_instrumented_ops(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& instrumented_ops() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_instrumented_ops();

  // string graph_id = 1;
  void clear_graph_id();
  const std::string& graph_id() const;
  void set_graph_id(const std::string& value);
  void set_graph_id(std::string&& value);
  void set_graph_id(const char* value);
  void set_graph_id(const char* value, size_t size);
  std::string* mutable_graph_id();
  std::string* release_graph_id();
  void set_allocated_graph_id(std::string* graph_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_graph_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_id(
      std::string* graph_id);

  // string graph_name = 2;
  void clear_graph_name();
  const std::string& graph_name() const;
  void set_graph_name(const std::string& value);
  void set_graph_name(std::string&& value);
  void set_graph_name(const char* value);
  void set_graph_name(const char* value, size_t size);
  std::string* mutable_graph_name();
  std::string* release_graph_name();
  void set_allocated_graph_name(std::string* graph_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_graph_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_name(
      std::string* graph_name);

  // bytes original_graph_def = 4;
  void clear_original_graph_def();
  const std::string& original_graph_def() const;
  void set_original_graph_def(const std::string& value);
  void set_original_graph_def(std::string&& value);
  void set_original_graph_def(const char* value);
  void set_original_graph_def(const void* value, size_t size);
  std::string* mutable_original_graph_def();
  std::string* release_original_graph_def();
  void set_allocated_original_graph_def(std::string* original_graph_def);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_original_graph_def();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_original_graph_def(
      std::string* original_graph_def);

  // bytes instrumented_graph_def = 5;
  void clear_instrumented_graph_def();
  const std::string& instrumented_graph_def() const;
  void set_instrumented_graph_def(const std::string& value);
  void set_instrumented_graph_def(std::string&& value);
  void set_instrumented_graph_def(const char* value);
  void set_instrumented_graph_def(const void* value, size_t size);
  std::string* mutable_instrumented_graph_def();
  std::string* release_instrumented_graph_def();
  void set_allocated_instrumented_graph_def(std::string* instrumented_graph_def);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_instrumented_graph_def();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_instrumented_graph_def(
      std::string* instrumented_graph_def);

  // string outer_context_id = 6;
  void clear_outer_context_id();
  const std::string& outer_context_id() const;
  void set_outer_context_id(const std::string& value);
  void set_outer_context_id(std::string&& value);
  void set_outer_context_id(const char* value);
  void set_outer_context_id(const char* value, size_t size);
  std::string* mutable_outer_context_id();
  std::string* release_outer_context_id();
  void set_allocated_outer_context_id(std::string* outer_context_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_outer_context_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_outer_context_id(
      std::string* outer_context_id);

  // @@protoc_insertion_point(class_scope:tensorflow.DebuggedGraph)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> instrumented_ops_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr original_graph_def_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr instrumented_graph_def_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr outer_context_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class DebuggedDevice :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebuggedDevice) */ {
 public:
  DebuggedDevice();
  virtual ~DebuggedDevice();

  DebuggedDevice(const DebuggedDevice& from);
  DebuggedDevice(DebuggedDevice&& from) noexcept
    : DebuggedDevice() {
    *this = ::std::move(from);
  }

  inline DebuggedDevice& operator=(const DebuggedDevice& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggedDevice& operator=(DebuggedDevice&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DebuggedDevice& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebuggedDevice* internal_default_instance() {
    return reinterpret_cast<const DebuggedDevice*>(
               &_DebuggedDevice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(DebuggedDevice& a, DebuggedDevice& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggedDevice* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebuggedDevice* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DebuggedDevice* New() const final {
    return CreateMaybeMessage<DebuggedDevice>(nullptr);
  }

  DebuggedDevice* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DebuggedDevice>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DebuggedDevice& from);
  void MergeFrom(const DebuggedDevice& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggedDevice* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebuggedDevice";
  }
  protected:
  explicit DebuggedDevice(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceNameFieldNumber = 1,
    kDeviceIdFieldNumber = 2,
  };
  // string device_name = 1;
  void clear_device_name();
  const std::string& device_name() const;
  void set_device_name(const std::string& value);
  void set_device_name(std::string&& value);
  void set_device_name(const char* value);
  void set_device_name(const char* value, size_t size);
  std::string* mutable_device_name();
  std::string* release_device_name();
  void set_allocated_device_name(std::string* device_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_name(
      std::string* device_name);

  // int32 device_id = 2;
  void clear_device_id();
  ::PROTOBUF_NAMESPACE_ID::int32 device_id() const;
  void set_device_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DebuggedDevice)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_name_;
  ::PROTOBUF_NAMESPACE_ID::int32 device_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class Execution :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Execution) */ {
 public:
  Execution();
  virtual ~Execution();

  Execution(const Execution& from);
  Execution(Execution&& from) noexcept
    : Execution() {
    *this = ::std::move(from);
  }

  inline Execution& operator=(const Execution& from) {
    CopyFrom(from);
    return *this;
  }
  inline Execution& operator=(Execution&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Execution& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Execution* internal_default_instance() {
    return reinterpret_cast<const Execution*>(
               &_Execution_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(Execution& a, Execution& b) {
    a.Swap(&b);
  }
  inline void Swap(Execution* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Execution* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Execution* New() const final {
    return CreateMaybeMessage<Execution>(nullptr);
  }

  Execution* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Execution>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Execution& from);
  void MergeFrom(const Execution& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Execution* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Execution";
  }
  protected:
  explicit Execution(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputTensorIdsFieldNumber = 4,
    kOutputTensorIdsFieldNumber = 5,
    kTensorProtosFieldNumber = 7,
    kOutputTensorDeviceIdsFieldNumber = 9,
    kOpTypeFieldNumber = 1,
    kGraphIdFieldNumber = 3,
    kCodeLocationFieldNumber = 8,
    kNumOutputsFieldNumber = 2,
    kTensorDebugModeFieldNumber = 6,
  };
  // repeated int64 input_tensor_ids = 4;
  int input_tensor_ids_size() const;
  void clear_input_tensor_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 input_tensor_ids(int index) const;
  void set_input_tensor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_input_tensor_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      input_tensor_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_input_tensor_ids();

  // repeated int64 output_tensor_ids = 5;
  int output_tensor_ids_size() const;
  void clear_output_tensor_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 output_tensor_ids(int index) const;
  void set_output_tensor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_output_tensor_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      output_tensor_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_output_tensor_ids();

  // repeated .tensorflow.TensorProto tensor_protos = 7;
  int tensor_protos_size() const;
  void clear_tensor_protos();
  ::tensorflow::TensorProto* mutable_tensor_protos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensor_protos();
  const ::tensorflow::TensorProto& tensor_protos(int index) const;
  ::tensorflow::TensorProto* add_tensor_protos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensor_protos() const;

  // repeated int32 output_tensor_device_ids = 9;
  int output_tensor_device_ids_size() const;
  void clear_output_tensor_device_ids();
  ::PROTOBUF_NAMESPACE_ID::int32 output_tensor_device_ids(int index) const;
  void set_output_tensor_device_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_output_tensor_device_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      output_tensor_device_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_output_tensor_device_ids();

  // string op_type = 1;
  void clear_op_type();
  const std::string& op_type() const;
  void set_op_type(const std::string& value);
  void set_op_type(std::string&& value);
  void set_op_type(const char* value);
  void set_op_type(const char* value, size_t size);
  std::string* mutable_op_type();
  std::string* release_op_type();
  void set_allocated_op_type(std::string* op_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_op_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op_type(
      std::string* op_type);

  // string graph_id = 3;
  void clear_graph_id();
  const std::string& graph_id() const;
  void set_graph_id(const std::string& value);
  void set_graph_id(std::string&& value);
  void set_graph_id(const char* value);
  void set_graph_id(const char* value, size_t size);
  std::string* mutable_graph_id();
  std::string* release_graph_id();
  void set_allocated_graph_id(std::string* graph_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_graph_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_id(
      std::string* graph_id);

  // .tensorflow.CodeLocation code_location = 8;
  bool has_code_location() const;
  void clear_code_location();
  const ::tensorflow::CodeLocation& code_location() const;
  ::tensorflow::CodeLocation* release_code_location();
  ::tensorflow::CodeLocation* mutable_code_location();
  void set_allocated_code_location(::tensorflow::CodeLocation* code_location);
  void unsafe_arena_set_allocated_code_location(
      ::tensorflow::CodeLocation* code_location);
  ::tensorflow::CodeLocation* unsafe_arena_release_code_location();

  // int32 num_outputs = 2;
  void clear_num_outputs();
  ::PROTOBUF_NAMESPACE_ID::int32 num_outputs() const;
  void set_num_outputs(::PROTOBUF_NAMESPACE_ID::int32 value);

  // .tensorflow.TensorDebugMode tensor_debug_mode = 6;
  void clear_tensor_debug_mode();
  ::tensorflow::TensorDebugMode tensor_debug_mode() const;
  void set_tensor_debug_mode(::tensorflow::TensorDebugMode value);

  // @@protoc_insertion_point(class_scope:tensorflow.Execution)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > input_tensor_ids_;
  mutable std::atomic<int> _input_tensor_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > output_tensor_ids_;
  mutable std::atomic<int> _output_tensor_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensor_protos_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > output_tensor_device_ids_;
  mutable std::atomic<int> _output_tensor_device_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_id_;
  ::tensorflow::CodeLocation* code_location_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_outputs_;
  int tensor_debug_mode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class GraphExecutionTrace :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphExecutionTrace) */ {
 public:
  GraphExecutionTrace();
  virtual ~GraphExecutionTrace();

  GraphExecutionTrace(const GraphExecutionTrace& from);
  GraphExecutionTrace(GraphExecutionTrace&& from) noexcept
    : GraphExecutionTrace() {
    *this = ::std::move(from);
  }

  inline GraphExecutionTrace& operator=(const GraphExecutionTrace& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphExecutionTrace& operator=(GraphExecutionTrace&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphExecutionTrace& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphExecutionTrace* internal_default_instance() {
    return reinterpret_cast<const GraphExecutionTrace*>(
               &_GraphExecutionTrace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GraphExecutionTrace& a, GraphExecutionTrace& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphExecutionTrace* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphExecutionTrace* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphExecutionTrace* New() const final {
    return CreateMaybeMessage<GraphExecutionTrace>(nullptr);
  }

  GraphExecutionTrace* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphExecutionTrace>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphExecutionTrace& from);
  void MergeFrom(const GraphExecutionTrace& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphExecutionTrace* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphExecutionTrace";
  }
  protected:
  explicit GraphExecutionTrace(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTfdbgContextIdFieldNumber = 1,
    kOpNameFieldNumber = 2,
    kDeviceNameFieldNumber = 6,
    kTensorProtoFieldNumber = 5,
    kOutputSlotFieldNumber = 3,
    kTensorDebugModeFieldNumber = 4,
  };
  // string tfdbg_context_id = 1;
  void clear_tfdbg_context_id();
  const std::string& tfdbg_context_id() const;
  void set_tfdbg_context_id(const std::string& value);
  void set_tfdbg_context_id(std::string&& value);
  void set_tfdbg_context_id(const char* value);
  void set_tfdbg_context_id(const char* value, size_t size);
  std::string* mutable_tfdbg_context_id();
  std::string* release_tfdbg_context_id();
  void set_allocated_tfdbg_context_id(std::string* tfdbg_context_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_tfdbg_context_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_tfdbg_context_id(
      std::string* tfdbg_context_id);

  // string op_name = 2;
  void clear_op_name();
  const std::string& op_name() const;
  void set_op_name(const std::string& value);
  void set_op_name(std::string&& value);
  void set_op_name(const char* value);
  void set_op_name(const char* value, size_t size);
  std::string* mutable_op_name();
  std::string* release_op_name();
  void set_allocated_op_name(std::string* op_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_op_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op_name(
      std::string* op_name);

  // string device_name = 6;
  void clear_device_name();
  const std::string& device_name() const;
  void set_device_name(const std::string& value);
  void set_device_name(std::string&& value);
  void set_device_name(const char* value);
  void set_device_name(const char* value, size_t size);
  std::string* mutable_device_name();
  std::string* release_device_name();
  void set_allocated_device_name(std::string* device_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_name(
      std::string* device_name);

  // .tensorflow.TensorProto tensor_proto = 5;
  bool has_tensor_proto() const;
  void clear_tensor_proto();
  const ::tensorflow::TensorProto& tensor_proto() const;
  ::tensorflow::TensorProto* release_tensor_proto();
  ::tensorflow::TensorProto* mutable_tensor_proto();
  void set_allocated_tensor_proto(::tensorflow::TensorProto* tensor_proto);
  void unsafe_arena_set_allocated_tensor_proto(
      ::tensorflow::TensorProto* tensor_proto);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor_proto();

  // int32 output_slot = 3;
  void clear_output_slot();
  ::PROTOBUF_NAMESPACE_ID::int32 output_slot() const;
  void set_output_slot(::PROTOBUF_NAMESPACE_ID::int32 value);

  // .tensorflow.TensorDebugMode tensor_debug_mode = 4;
  void clear_tensor_debug_mode();
  ::tensorflow::TensorDebugMode tensor_debug_mode() const;
  void set_tensor_debug_mode(::tensorflow::TensorDebugMode value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphExecutionTrace)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tfdbg_context_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_name_;
  ::tensorflow::TensorProto* tensor_proto_;
  ::PROTOBUF_NAMESPACE_ID::int32 output_slot_;
  int tensor_debug_mode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DebugEvent

// double wall_time = 1;
inline void DebugEvent::clear_wall_time() {
  wall_time_ = 0;
}
inline double DebugEvent::wall_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.wall_time)
  return wall_time_;
}
inline void DebugEvent::set_wall_time(double value) {
  
  wall_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebugEvent.wall_time)
}

// int64 step = 2;
inline void DebugEvent::clear_step() {
  step_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DebugEvent::step() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.step)
  return step_;
}
inline void DebugEvent::set_step(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebugEvent.step)
}

// .tensorflow.DebugMetadata debug_metadata = 3;
inline bool DebugEvent::has_debug_metadata() const {
  return what_case() == kDebugMetadata;
}
inline void DebugEvent::set_has_debug_metadata() {
  _oneof_case_[0] = kDebugMetadata;
}
inline void DebugEvent::clear_debug_metadata() {
  if (has_debug_metadata()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete what_.debug_metadata_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::DebugMetadata* DebugEvent::release_debug_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.debug_metadata)
  if (has_debug_metadata()) {
    clear_has_what();
      ::tensorflow::DebugMetadata* temp = what_.debug_metadata_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    what_.debug_metadata_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::DebugMetadata& DebugEvent::debug_metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.debug_metadata)
  return has_debug_metadata()
      ? *what_.debug_metadata_
      : *reinterpret_cast< ::tensorflow::DebugMetadata*>(&::tensorflow::_DebugMetadata_default_instance_);
}
inline ::tensorflow::DebugMetadata* DebugEvent::unsafe_arena_release_debug_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.debug_metadata)
  if (has_debug_metadata()) {
    clear_has_what();
    ::tensorflow::DebugMetadata* temp = what_.debug_metadata_;
    what_.debug_metadata_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_debug_metadata(::tensorflow::DebugMetadata* debug_metadata) {
  clear_what();
  if (debug_metadata) {
    set_has_debug_metadata();
    what_.debug_metadata_ = debug_metadata;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.debug_metadata)
}
inline ::tensorflow::DebugMetadata* DebugEvent::mutable_debug_metadata() {
  if (!has_debug_metadata()) {
    clear_what();
    set_has_debug_metadata();
    what_.debug_metadata_ = CreateMaybeMessage< ::tensorflow::DebugMetadata >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.debug_metadata)
  return what_.debug_metadata_;
}

// .tensorflow.SourceFile source_file = 4;
inline bool DebugEvent::has_source_file() const {
  return what_case() == kSourceFile;
}
inline void DebugEvent::set_has_source_file() {
  _oneof_case_[0] = kSourceFile;
}
inline void DebugEvent::clear_source_file() {
  if (has_source_file()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete what_.source_file_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::SourceFile* DebugEvent::release_source_file() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.source_file)
  if (has_source_file()) {
    clear_has_what();
      ::tensorflow::SourceFile* temp = what_.source_file_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    what_.source_file_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SourceFile& DebugEvent::source_file() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.source_file)
  return has_source_file()
      ? *what_.source_file_
      : *reinterpret_cast< ::tensorflow::SourceFile*>(&::tensorflow::_SourceFile_default_instance_);
}
inline ::tensorflow::SourceFile* DebugEvent::unsafe_arena_release_source_file() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.source_file)
  if (has_source_file()) {
    clear_has_what();
    ::tensorflow::SourceFile* temp = what_.source_file_;
    what_.source_file_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_source_file(::tensorflow::SourceFile* source_file) {
  clear_what();
  if (source_file) {
    set_has_source_file();
    what_.source_file_ = source_file;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.source_file)
}
inline ::tensorflow::SourceFile* DebugEvent::mutable_source_file() {
  if (!has_source_file()) {
    clear_what();
    set_has_source_file();
    what_.source_file_ = CreateMaybeMessage< ::tensorflow::SourceFile >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.source_file)
  return what_.source_file_;
}

// .tensorflow.StackFrameWithId stack_frame_with_id = 6;
inline bool DebugEvent::has_stack_frame_with_id() const {
  return what_case() == kStackFrameWithId;
}
inline void DebugEvent::set_has_stack_frame_with_id() {
  _oneof_case_[0] = kStackFrameWithId;
}
inline void DebugEvent::clear_stack_frame_with_id() {
  if (has_stack_frame_with_id()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete what_.stack_frame_with_id_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::StackFrameWithId* DebugEvent::release_stack_frame_with_id() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.stack_frame_with_id)
  if (has_stack_frame_with_id()) {
    clear_has_what();
      ::tensorflow::StackFrameWithId* temp = what_.stack_frame_with_id_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    what_.stack_frame_with_id_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::StackFrameWithId& DebugEvent::stack_frame_with_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.stack_frame_with_id)
  return has_stack_frame_with_id()
      ? *what_.stack_frame_with_id_
      : *reinterpret_cast< ::tensorflow::StackFrameWithId*>(&::tensorflow::_StackFrameWithId_default_instance_);
}
inline ::tensorflow::StackFrameWithId* DebugEvent::unsafe_arena_release_stack_frame_with_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.stack_frame_with_id)
  if (has_stack_frame_with_id()) {
    clear_has_what();
    ::tensorflow::StackFrameWithId* temp = what_.stack_frame_with_id_;
    what_.stack_frame_with_id_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_stack_frame_with_id(::tensorflow::StackFrameWithId* stack_frame_with_id) {
  clear_what();
  if (stack_frame_with_id) {
    set_has_stack_frame_with_id();
    what_.stack_frame_with_id_ = stack_frame_with_id;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.stack_frame_with_id)
}
inline ::tensorflow::StackFrameWithId* DebugEvent::mutable_stack_frame_with_id() {
  if (!has_stack_frame_with_id()) {
    clear_what();
    set_has_stack_frame_with_id();
    what_.stack_frame_with_id_ = CreateMaybeMessage< ::tensorflow::StackFrameWithId >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.stack_frame_with_id)
  return what_.stack_frame_with_id_;
}

// .tensorflow.GraphOpCreation graph_op_creation = 7;
inline bool DebugEvent::has_graph_op_creation() const {
  return what_case() == kGraphOpCreation;
}
inline void DebugEvent::set_has_graph_op_creation() {
  _oneof_case_[0] = kGraphOpCreation;
}
inline void DebugEvent::clear_graph_op_creation() {
  if (has_graph_op_creation()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete what_.graph_op_creation_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::GraphOpCreation* DebugEvent::release_graph_op_creation() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.graph_op_creation)
  if (has_graph_op_creation()) {
    clear_has_what();
      ::tensorflow::GraphOpCreation* temp = what_.graph_op_creation_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    what_.graph_op_creation_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::GraphOpCreation& DebugEvent::graph_op_creation() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.graph_op_creation)
  return has_graph_op_creation()
      ? *what_.graph_op_creation_
      : *reinterpret_cast< ::tensorflow::GraphOpCreation*>(&::tensorflow::_GraphOpCreation_default_instance_);
}
inline ::tensorflow::GraphOpCreation* DebugEvent::unsafe_arena_release_graph_op_creation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.graph_op_creation)
  if (has_graph_op_creation()) {
    clear_has_what();
    ::tensorflow::GraphOpCreation* temp = what_.graph_op_creation_;
    what_.graph_op_creation_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_graph_op_creation(::tensorflow::GraphOpCreation* graph_op_creation) {
  clear_what();
  if (graph_op_creation) {
    set_has_graph_op_creation();
    what_.graph_op_creation_ = graph_op_creation;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.graph_op_creation)
}
inline ::tensorflow::GraphOpCreation* DebugEvent::mutable_graph_op_creation() {
  if (!has_graph_op_creation()) {
    clear_what();
    set_has_graph_op_creation();
    what_.graph_op_creation_ = CreateMaybeMessage< ::tensorflow::GraphOpCreation >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.graph_op_creation)
  return what_.graph_op_creation_;
}

// .tensorflow.DebuggedGraph debugged_graph = 8;
inline bool DebugEvent::has_debugged_graph() const {
  return what_case() == kDebuggedGraph;
}
inline void DebugEvent::set_has_debugged_graph() {
  _oneof_case_[0] = kDebuggedGraph;
}
inline void DebugEvent::clear_debugged_graph() {
  if (has_debugged_graph()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete what_.debugged_graph_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::DebuggedGraph* DebugEvent::release_debugged_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.debugged_graph)
  if (has_debugged_graph()) {
    clear_has_what();
      ::tensorflow::DebuggedGraph* temp = what_.debugged_graph_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    what_.debugged_graph_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::DebuggedGraph& DebugEvent::debugged_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.debugged_graph)
  return has_debugged_graph()
      ? *what_.debugged_graph_
      : *reinterpret_cast< ::tensorflow::DebuggedGraph*>(&::tensorflow::_DebuggedGraph_default_instance_);
}
inline ::tensorflow::DebuggedGraph* DebugEvent::unsafe_arena_release_debugged_graph() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.debugged_graph)
  if (has_debugged_graph()) {
    clear_has_what();
    ::tensorflow::DebuggedGraph* temp = what_.debugged_graph_;
    what_.debugged_graph_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_debugged_graph(::tensorflow::DebuggedGraph* debugged_graph) {
  clear_what();
  if (debugged_graph) {
    set_has_debugged_graph();
    what_.debugged_graph_ = debugged_graph;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.debugged_graph)
}
inline ::tensorflow::DebuggedGraph* DebugEvent::mutable_debugged_graph() {
  if (!has_debugged_graph()) {
    clear_what();
    set_has_debugged_graph();
    what_.debugged_graph_ = CreateMaybeMessage< ::tensorflow::DebuggedGraph >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.debugged_graph)
  return what_.debugged_graph_;
}

// .tensorflow.Execution execution = 9;
inline bool DebugEvent::has_execution() const {
  return what_case() == kExecution;
}
inline void DebugEvent::set_has_execution() {
  _oneof_case_[0] = kExecution;
}
inline void DebugEvent::clear_execution() {
  if (has_execution()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete what_.execution_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::Execution* DebugEvent::release_execution() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.execution)
  if (has_execution()) {
    clear_has_what();
      ::tensorflow::Execution* temp = what_.execution_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    what_.execution_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::Execution& DebugEvent::execution() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.execution)
  return has_execution()
      ? *what_.execution_
      : *reinterpret_cast< ::tensorflow::Execution*>(&::tensorflow::_Execution_default_instance_);
}
inline ::tensorflow::Execution* DebugEvent::unsafe_arena_release_execution() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.execution)
  if (has_execution()) {
    clear_has_what();
    ::tensorflow::Execution* temp = what_.execution_;
    what_.execution_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_execution(::tensorflow::Execution* execution) {
  clear_what();
  if (execution) {
    set_has_execution();
    what_.execution_ = execution;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.execution)
}
inline ::tensorflow::Execution* DebugEvent::mutable_execution() {
  if (!has_execution()) {
    clear_what();
    set_has_execution();
    what_.execution_ = CreateMaybeMessage< ::tensorflow::Execution >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.execution)
  return what_.execution_;
}

// .tensorflow.GraphExecutionTrace graph_execution_trace = 10;
inline bool DebugEvent::has_graph_execution_trace() const {
  return what_case() == kGraphExecutionTrace;
}
inline void DebugEvent::set_has_graph_execution_trace() {
  _oneof_case_[0] = kGraphExecutionTrace;
}
inline void DebugEvent::clear_graph_execution_trace() {
  if (has_graph_execution_trace()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete what_.graph_execution_trace_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::GraphExecutionTrace* DebugEvent::release_graph_execution_trace() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.graph_execution_trace)
  if (has_graph_execution_trace()) {
    clear_has_what();
      ::tensorflow::GraphExecutionTrace* temp = what_.graph_execution_trace_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    what_.graph_execution_trace_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::GraphExecutionTrace& DebugEvent::graph_execution_trace() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.graph_execution_trace)
  return has_graph_execution_trace()
      ? *what_.graph_execution_trace_
      : *reinterpret_cast< ::tensorflow::GraphExecutionTrace*>(&::tensorflow::_GraphExecutionTrace_default_instance_);
}
inline ::tensorflow::GraphExecutionTrace* DebugEvent::unsafe_arena_release_graph_execution_trace() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.graph_execution_trace)
  if (has_graph_execution_trace()) {
    clear_has_what();
    ::tensorflow::GraphExecutionTrace* temp = what_.graph_execution_trace_;
    what_.graph_execution_trace_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_graph_execution_trace(::tensorflow::GraphExecutionTrace* graph_execution_trace) {
  clear_what();
  if (graph_execution_trace) {
    set_has_graph_execution_trace();
    what_.graph_execution_trace_ = graph_execution_trace;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.graph_execution_trace)
}
inline ::tensorflow::GraphExecutionTrace* DebugEvent::mutable_graph_execution_trace() {
  if (!has_graph_execution_trace()) {
    clear_what();
    set_has_graph_execution_trace();
    what_.graph_execution_trace_ = CreateMaybeMessage< ::tensorflow::GraphExecutionTrace >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.graph_execution_trace)
  return what_.graph_execution_trace_;
}

// string graph_id = 11;
inline bool DebugEvent::has_graph_id() const {
  return what_case() == kGraphId;
}
inline void DebugEvent::set_has_graph_id() {
  _oneof_case_[0] = kGraphId;
}
inline void DebugEvent::clear_graph_id() {
  if (has_graph_id()) {
    what_.graph_id_.Destroy(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_what();
  }
}
inline const std::string& DebugEvent::graph_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.graph_id)
  if (has_graph_id()) {
    return what_.graph_id_.Get();
  }
  return *&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void DebugEvent::set_graph_id(const std::string& value) {
  if (!has_graph_id()) {
    clear_what();
    set_has_graph_id();
    what_.graph_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  what_.graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebugEvent.graph_id)
}
inline void DebugEvent::set_graph_id(std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebugEvent.graph_id)
  if (!has_graph_id()) {
    clear_what();
    set_has_graph_id();
    what_.graph_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  what_.graph_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebugEvent.graph_id)
}
inline void DebugEvent::set_graph_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  if (!has_graph_id()) {
    clear_what();
    set_has_graph_id();
    what_.graph_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  what_.graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugEvent.graph_id)
}
inline void DebugEvent::set_graph_id(const char* value,
                             size_t size) {
  if (!has_graph_id()) {
    clear_what();
    set_has_graph_id();
    what_.graph_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  what_.graph_id_.Set(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugEvent.graph_id)
}
inline std::string* DebugEvent::mutable_graph_id() {
  if (!has_graph_id()) {
    clear_what();
    set_has_graph_id();
    what_.graph_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return what_.graph_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.graph_id)
}
inline std::string* DebugEvent::release_graph_id() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.graph_id)
  if (has_graph_id()) {
    clear_has_what();
    return what_.graph_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void DebugEvent::set_allocated_graph_id(std::string* graph_id) {
  if (has_what()) {
    clear_what();
  }
  if (graph_id != nullptr) {
    set_has_graph_id();
    what_.graph_id_.UnsafeSetDefault(graph_id);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugEvent.graph_id)
}
inline std::string* DebugEvent::unsafe_arena_release_graph_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.graph_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (has_graph_id()) {
    clear_has_what();
    return what_.graph_id_.UnsafeArenaRelease(
        &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_graph_id(std::string* graph_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (!has_graph_id()) {
    what_.graph_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  clear_what();
  if (graph_id) {
    set_has_graph_id();
    what_.graph_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), graph_id, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.graph_id)
}

// .tensorflow.DebuggedDevice debugged_device = 12;
inline bool DebugEvent::has_debugged_device() const {
  return what_case() == kDebuggedDevice;
}
inline void DebugEvent::set_has_debugged_device() {
  _oneof_case_[0] = kDebuggedDevice;
}
inline void DebugEvent::clear_debugged_device() {
  if (has_debugged_device()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete what_.debugged_device_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::DebuggedDevice* DebugEvent::release_debugged_device() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.debugged_device)
  if (has_debugged_device()) {
    clear_has_what();
      ::tensorflow::DebuggedDevice* temp = what_.debugged_device_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    what_.debugged_device_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::DebuggedDevice& DebugEvent::debugged_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.debugged_device)
  return has_debugged_device()
      ? *what_.debugged_device_
      : *reinterpret_cast< ::tensorflow::DebuggedDevice*>(&::tensorflow::_DebuggedDevice_default_instance_);
}
inline ::tensorflow::DebuggedDevice* DebugEvent::unsafe_arena_release_debugged_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.debugged_device)
  if (has_debugged_device()) {
    clear_has_what();
    ::tensorflow::DebuggedDevice* temp = what_.debugged_device_;
    what_.debugged_device_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_debugged_device(::tensorflow::DebuggedDevice* debugged_device) {
  clear_what();
  if (debugged_device) {
    set_has_debugged_device();
    what_.debugged_device_ = debugged_device;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.debugged_device)
}
inline ::tensorflow::DebuggedDevice* DebugEvent::mutable_debugged_device() {
  if (!has_debugged_device()) {
    clear_what();
    set_has_debugged_device();
    what_.debugged_device_ = CreateMaybeMessage< ::tensorflow::DebuggedDevice >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.debugged_device)
  return what_.debugged_device_;
}

inline bool DebugEvent::has_what() const {
  return what_case() != WHAT_NOT_SET;
}
inline void DebugEvent::clear_has_what() {
  _oneof_case_[0] = WHAT_NOT_SET;
}
inline DebugEvent::WhatCase DebugEvent::what_case() const {
  return DebugEvent::WhatCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// DebugMetadata

// string tensorflow_version = 1;
inline void DebugMetadata::clear_tensorflow_version() {
  tensorflow_version_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebugMetadata::tensorflow_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugMetadata.tensorflow_version)
  return tensorflow_version_.Get();
}
inline void DebugMetadata::set_tensorflow_version(const std::string& value) {
  
  tensorflow_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebugMetadata.tensorflow_version)
}
inline void DebugMetadata::set_tensorflow_version(std::string&& value) {
  
  tensorflow_version_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebugMetadata.tensorflow_version)
}
inline void DebugMetadata::set_tensorflow_version(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  tensorflow_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugMetadata.tensorflow_version)
}
inline void DebugMetadata::set_tensorflow_version(const char* value,
    size_t size) {
  
  tensorflow_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugMetadata.tensorflow_version)
}
inline std::string* DebugMetadata::mutable_tensorflow_version() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugMetadata.tensorflow_version)
  return tensorflow_version_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebugMetadata::release_tensorflow_version() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugMetadata.tensorflow_version)
  
  return tensorflow_version_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebugMetadata::set_allocated_tensorflow_version(std::string* tensorflow_version) {
  if (tensorflow_version != nullptr) {
    
  } else {
    
  }
  tensorflow_version_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tensorflow_version,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugMetadata.tensorflow_version)
}
inline std::string* DebugMetadata::unsafe_arena_release_tensorflow_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugMetadata.tensorflow_version)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return tensorflow_version_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebugMetadata::unsafe_arena_set_allocated_tensorflow_version(
    std::string* tensorflow_version) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (tensorflow_version != nullptr) {
    
  } else {
    
  }
  tensorflow_version_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      tensorflow_version, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugMetadata.tensorflow_version)
}

// string file_version = 2;
inline void DebugMetadata::clear_file_version() {
  file_version_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebugMetadata::file_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugMetadata.file_version)
  return file_version_.Get();
}
inline void DebugMetadata::set_file_version(const std::string& value) {
  
  file_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebugMetadata.file_version)
}
inline void DebugMetadata::set_file_version(std::string&& value) {
  
  file_version_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebugMetadata.file_version)
}
inline void DebugMetadata::set_file_version(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  file_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugMetadata.file_version)
}
inline void DebugMetadata::set_file_version(const char* value,
    size_t size) {
  
  file_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugMetadata.file_version)
}
inline std::string* DebugMetadata::mutable_file_version() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugMetadata.file_version)
  return file_version_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebugMetadata::release_file_version() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugMetadata.file_version)
  
  return file_version_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebugMetadata::set_allocated_file_version(std::string* file_version) {
  if (file_version != nullptr) {
    
  } else {
    
  }
  file_version_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), file_version,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugMetadata.file_version)
}
inline std::string* DebugMetadata::unsafe_arena_release_file_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugMetadata.file_version)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return file_version_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebugMetadata::unsafe_arena_set_allocated_file_version(
    std::string* file_version) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (file_version != nullptr) {
    
  } else {
    
  }
  file_version_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      file_version, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugMetadata.file_version)
}

// string tfdbg_run_id = 3;
inline void DebugMetadata::clear_tfdbg_run_id() {
  tfdbg_run_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebugMetadata::tfdbg_run_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugMetadata.tfdbg_run_id)
  return tfdbg_run_id_.Get();
}
inline void DebugMetadata::set_tfdbg_run_id(const std::string& value) {
  
  tfdbg_run_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebugMetadata.tfdbg_run_id)
}
inline void DebugMetadata::set_tfdbg_run_id(std::string&& value) {
  
  tfdbg_run_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebugMetadata.tfdbg_run_id)
}
inline void DebugMetadata::set_tfdbg_run_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  tfdbg_run_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugMetadata.tfdbg_run_id)
}
inline void DebugMetadata::set_tfdbg_run_id(const char* value,
    size_t size) {
  
  tfdbg_run_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugMetadata.tfdbg_run_id)
}
inline std::string* DebugMetadata::mutable_tfdbg_run_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugMetadata.tfdbg_run_id)
  return tfdbg_run_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebugMetadata::release_tfdbg_run_id() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugMetadata.tfdbg_run_id)
  
  return tfdbg_run_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebugMetadata::set_allocated_tfdbg_run_id(std::string* tfdbg_run_id) {
  if (tfdbg_run_id != nullptr) {
    
  } else {
    
  }
  tfdbg_run_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tfdbg_run_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugMetadata.tfdbg_run_id)
}
inline std::string* DebugMetadata::unsafe_arena_release_tfdbg_run_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugMetadata.tfdbg_run_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return tfdbg_run_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebugMetadata::unsafe_arena_set_allocated_tfdbg_run_id(
    std::string* tfdbg_run_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (tfdbg_run_id != nullptr) {
    
  } else {
    
  }
  tfdbg_run_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      tfdbg_run_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugMetadata.tfdbg_run_id)
}

// -------------------------------------------------------------------

// SourceFile

// string file_path = 1;
inline void SourceFile::clear_file_path() {
  file_path_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SourceFile::file_path() const {
  // @@protoc_insertion_point(field_get:tensorflow.SourceFile.file_path)
  return file_path_.Get();
}
inline void SourceFile::set_file_path(const std::string& value) {
  
  file_path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SourceFile.file_path)
}
inline void SourceFile::set_file_path(std::string&& value) {
  
  file_path_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SourceFile.file_path)
}
inline void SourceFile::set_file_path(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  file_path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SourceFile.file_path)
}
inline void SourceFile::set_file_path(const char* value,
    size_t size) {
  
  file_path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SourceFile.file_path)
}
inline std::string* SourceFile::mutable_file_path() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SourceFile.file_path)
  return file_path_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SourceFile::release_file_path() {
  // @@protoc_insertion_point(field_release:tensorflow.SourceFile.file_path)
  
  return file_path_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SourceFile::set_allocated_file_path(std::string* file_path) {
  if (file_path != nullptr) {
    
  } else {
    
  }
  file_path_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), file_path,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SourceFile.file_path)
}
inline std::string* SourceFile::unsafe_arena_release_file_path() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SourceFile.file_path)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return file_path_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SourceFile::unsafe_arena_set_allocated_file_path(
    std::string* file_path) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (file_path != nullptr) {
    
  } else {
    
  }
  file_path_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      file_path, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SourceFile.file_path)
}

// string host_name = 2;
inline void SourceFile::clear_host_name() {
  host_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SourceFile::host_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SourceFile.host_name)
  return host_name_.Get();
}
inline void SourceFile::set_host_name(const std::string& value) {
  
  host_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SourceFile.host_name)
}
inline void SourceFile::set_host_name(std::string&& value) {
  
  host_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SourceFile.host_name)
}
inline void SourceFile::set_host_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  host_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SourceFile.host_name)
}
inline void SourceFile::set_host_name(const char* value,
    size_t size) {
  
  host_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SourceFile.host_name)
}
inline std::string* SourceFile::mutable_host_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SourceFile.host_name)
  return host_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SourceFile::release_host_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SourceFile.host_name)
  
  return host_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SourceFile::set_allocated_host_name(std::string* host_name) {
  if (host_name != nullptr) {
    
  } else {
    
  }
  host_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), host_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SourceFile.host_name)
}
inline std::string* SourceFile::unsafe_arena_release_host_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SourceFile.host_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return host_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SourceFile::unsafe_arena_set_allocated_host_name(
    std::string* host_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (host_name != nullptr) {
    
  } else {
    
  }
  host_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      host_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SourceFile.host_name)
}

// repeated string lines = 3;
inline int SourceFile::lines_size() const {
  return lines_.size();
}
inline void SourceFile::clear_lines() {
  lines_.Clear();
}
inline const std::string& SourceFile::lines(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SourceFile.lines)
  return lines_.Get(index);
}
inline std::string* SourceFile::mutable_lines(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SourceFile.lines)
  return lines_.Mutable(index);
}
inline void SourceFile::set_lines(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.SourceFile.lines)
  lines_.Mutable(index)->assign(value);
}
inline void SourceFile::set_lines(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.SourceFile.lines)
  lines_.Mutable(index)->assign(std::move(value));
}
inline void SourceFile::set_lines(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  lines_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.SourceFile.lines)
}
inline void SourceFile::set_lines(int index, const char* value, size_t size) {
  lines_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SourceFile.lines)
}
inline std::string* SourceFile::add_lines() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.SourceFile.lines)
  return lines_.Add();
}
inline void SourceFile::add_lines(const std::string& value) {
  lines_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.SourceFile.lines)
}
inline void SourceFile::add_lines(std::string&& value) {
  lines_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.SourceFile.lines)
}
inline void SourceFile::add_lines(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  lines_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.SourceFile.lines)
}
inline void SourceFile::add_lines(const char* value, size_t size) {
  lines_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.SourceFile.lines)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SourceFile::lines() const {
  // @@protoc_insertion_point(field_list:tensorflow.SourceFile.lines)
  return lines_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SourceFile::mutable_lines() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SourceFile.lines)
  return &lines_;
}

// -------------------------------------------------------------------

// StackFrameWithId

// string id = 1;
inline void StackFrameWithId::clear_id() {
  id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& StackFrameWithId::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.StackFrameWithId.id)
  return id_.Get();
}
inline void StackFrameWithId::set_id(const std::string& value) {
  
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.StackFrameWithId.id)
}
inline void StackFrameWithId::set_id(std::string&& value) {
  
  id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.StackFrameWithId.id)
}
inline void StackFrameWithId::set_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.StackFrameWithId.id)
}
inline void StackFrameWithId::set_id(const char* value,
    size_t size) {
  
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.StackFrameWithId.id)
}
inline std::string* StackFrameWithId::mutable_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.StackFrameWithId.id)
  return id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* StackFrameWithId::release_id() {
  // @@protoc_insertion_point(field_release:tensorflow.StackFrameWithId.id)
  
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void StackFrameWithId::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StackFrameWithId.id)
}
inline std::string* StackFrameWithId::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StackFrameWithId.id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void StackFrameWithId::unsafe_arena_set_allocated_id(
    std::string* id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (id != nullptr) {
    
  } else {
    
  }
  id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StackFrameWithId.id)
}

// .tensorflow.GraphDebugInfo.FileLineCol file_line_col = 2;
inline bool StackFrameWithId::has_file_line_col() const {
  return this != internal_default_instance() && file_line_col_ != nullptr;
}
inline const ::tensorflow::GraphDebugInfo_FileLineCol& StackFrameWithId::file_line_col() const {
  const ::tensorflow::GraphDebugInfo_FileLineCol* p = file_line_col_;
  // @@protoc_insertion_point(field_get:tensorflow.StackFrameWithId.file_line_col)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::GraphDebugInfo_FileLineCol*>(
      &::tensorflow::_GraphDebugInfo_FileLineCol_default_instance_);
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* StackFrameWithId::release_file_line_col() {
  // @@protoc_insertion_point(field_release:tensorflow.StackFrameWithId.file_line_col)
  
  ::tensorflow::GraphDebugInfo_FileLineCol* temp = file_line_col_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  file_line_col_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* StackFrameWithId::unsafe_arena_release_file_line_col() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StackFrameWithId.file_line_col)
  
  ::tensorflow::GraphDebugInfo_FileLineCol* temp = file_line_col_;
  file_line_col_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* StackFrameWithId::mutable_file_line_col() {
  
  if (file_line_col_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDebugInfo_FileLineCol>(GetArenaNoVirtual());
    file_line_col_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StackFrameWithId.file_line_col)
  return file_line_col_;
}
inline void StackFrameWithId::set_allocated_file_line_col(::tensorflow::GraphDebugInfo_FileLineCol* file_line_col) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(file_line_col_);
  }
  if (file_line_col) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(file_line_col)->GetArena();
    if (message_arena != submessage_arena) {
      file_line_col = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, file_line_col, submessage_arena);
    }
    
  } else {
    
  }
  file_line_col_ = file_line_col;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StackFrameWithId.file_line_col)
}

// -------------------------------------------------------------------

// CodeLocation

// string host_name = 1;
inline void CodeLocation::clear_host_name() {
  host_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CodeLocation::host_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CodeLocation.host_name)
  return host_name_.Get();
}
inline void CodeLocation::set_host_name(const std::string& value) {
  
  host_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CodeLocation.host_name)
}
inline void CodeLocation::set_host_name(std::string&& value) {
  
  host_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CodeLocation.host_name)
}
inline void CodeLocation::set_host_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  host_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CodeLocation.host_name)
}
inline void CodeLocation::set_host_name(const char* value,
    size_t size) {
  
  host_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CodeLocation.host_name)
}
inline std::string* CodeLocation::mutable_host_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CodeLocation.host_name)
  return host_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CodeLocation::release_host_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CodeLocation.host_name)
  
  return host_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CodeLocation::set_allocated_host_name(std::string* host_name) {
  if (host_name != nullptr) {
    
  } else {
    
  }
  host_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), host_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CodeLocation.host_name)
}
inline std::string* CodeLocation::unsafe_arena_release_host_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CodeLocation.host_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return host_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CodeLocation::unsafe_arena_set_allocated_host_name(
    std::string* host_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (host_name != nullptr) {
    
  } else {
    
  }
  host_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      host_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CodeLocation.host_name)
}

// repeated string stack_frame_ids = 2;
inline int CodeLocation::stack_frame_ids_size() const {
  return stack_frame_ids_.size();
}
inline void CodeLocation::clear_stack_frame_ids() {
  stack_frame_ids_.Clear();
}
inline const std::string& CodeLocation::stack_frame_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CodeLocation.stack_frame_ids)
  return stack_frame_ids_.Get(index);
}
inline std::string* CodeLocation::mutable_stack_frame_ids(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CodeLocation.stack_frame_ids)
  return stack_frame_ids_.Mutable(index);
}
inline void CodeLocation::set_stack_frame_ids(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CodeLocation.stack_frame_ids)
  stack_frame_ids_.Mutable(index)->assign(value);
}
inline void CodeLocation::set_stack_frame_ids(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CodeLocation.stack_frame_ids)
  stack_frame_ids_.Mutable(index)->assign(std::move(value));
}
inline void CodeLocation::set_stack_frame_ids(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  stack_frame_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CodeLocation.stack_frame_ids)
}
inline void CodeLocation::set_stack_frame_ids(int index, const char* value, size_t size) {
  stack_frame_ids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CodeLocation.stack_frame_ids)
}
inline std::string* CodeLocation::add_stack_frame_ids() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CodeLocation.stack_frame_ids)
  return stack_frame_ids_.Add();
}
inline void CodeLocation::add_stack_frame_ids(const std::string& value) {
  stack_frame_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CodeLocation.stack_frame_ids)
}
inline void CodeLocation::add_stack_frame_ids(std::string&& value) {
  stack_frame_ids_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CodeLocation.stack_frame_ids)
}
inline void CodeLocation::add_stack_frame_ids(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  stack_frame_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CodeLocation.stack_frame_ids)
}
inline void CodeLocation::add_stack_frame_ids(const char* value, size_t size) {
  stack_frame_ids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CodeLocation.stack_frame_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CodeLocation::stack_frame_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.CodeLocation.stack_frame_ids)
  return stack_frame_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CodeLocation::mutable_stack_frame_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CodeLocation.stack_frame_ids)
  return &stack_frame_ids_;
}

// -------------------------------------------------------------------

// GraphOpCreation

// string op_type = 1;
inline void GraphOpCreation::clear_op_type() {
  op_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphOpCreation::op_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.op_type)
  return op_type_.Get();
}
inline void GraphOpCreation::set_op_type(const std::string& value) {
  
  op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.op_type)
}
inline void GraphOpCreation::set_op_type(std::string&& value) {
  
  op_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphOpCreation.op_type)
}
inline void GraphOpCreation::set_op_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphOpCreation.op_type)
}
inline void GraphOpCreation::set_op_type(const char* value,
    size_t size) {
  
  op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphOpCreation.op_type)
}
inline std::string* GraphOpCreation::mutable_op_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.op_type)
  return op_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphOpCreation::release_op_type() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.op_type)
  
  return op_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphOpCreation::set_allocated_op_type(std::string* op_type) {
  if (op_type != nullptr) {
    
  } else {
    
  }
  op_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.op_type)
}
inline std::string* GraphOpCreation::unsafe_arena_release_op_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphOpCreation.op_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return op_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphOpCreation::unsafe_arena_set_allocated_op_type(
    std::string* op_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (op_type != nullptr) {
    
  } else {
    
  }
  op_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      op_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphOpCreation.op_type)
}

// string op_name = 2;
inline void GraphOpCreation::clear_op_name() {
  op_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphOpCreation::op_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.op_name)
  return op_name_.Get();
}
inline void GraphOpCreation::set_op_name(const std::string& value) {
  
  op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.op_name)
}
inline void GraphOpCreation::set_op_name(std::string&& value) {
  
  op_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphOpCreation.op_name)
}
inline void GraphOpCreation::set_op_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphOpCreation.op_name)
}
inline void GraphOpCreation::set_op_name(const char* value,
    size_t size) {
  
  op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphOpCreation.op_name)
}
inline std::string* GraphOpCreation::mutable_op_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.op_name)
  return op_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphOpCreation::release_op_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.op_name)
  
  return op_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphOpCreation::set_allocated_op_name(std::string* op_name) {
  if (op_name != nullptr) {
    
  } else {
    
  }
  op_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.op_name)
}
inline std::string* GraphOpCreation::unsafe_arena_release_op_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphOpCreation.op_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return op_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphOpCreation::unsafe_arena_set_allocated_op_name(
    std::string* op_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (op_name != nullptr) {
    
  } else {
    
  }
  op_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      op_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphOpCreation.op_name)
}

// string graph_name = 3;
inline void GraphOpCreation::clear_graph_name() {
  graph_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphOpCreation::graph_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.graph_name)
  return graph_name_.Get();
}
inline void GraphOpCreation::set_graph_name(const std::string& value) {
  
  graph_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.graph_name)
}
inline void GraphOpCreation::set_graph_name(std::string&& value) {
  
  graph_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphOpCreation.graph_name)
}
inline void GraphOpCreation::set_graph_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  graph_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphOpCreation.graph_name)
}
inline void GraphOpCreation::set_graph_name(const char* value,
    size_t size) {
  
  graph_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphOpCreation.graph_name)
}
inline std::string* GraphOpCreation::mutable_graph_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.graph_name)
  return graph_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphOpCreation::release_graph_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.graph_name)
  
  return graph_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphOpCreation::set_allocated_graph_name(std::string* graph_name) {
  if (graph_name != nullptr) {
    
  } else {
    
  }
  graph_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), graph_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.graph_name)
}
inline std::string* GraphOpCreation::unsafe_arena_release_graph_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphOpCreation.graph_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return graph_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphOpCreation::unsafe_arena_set_allocated_graph_name(
    std::string* graph_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (graph_name != nullptr) {
    
  } else {
    
  }
  graph_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      graph_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphOpCreation.graph_name)
}

// string graph_id = 4;
inline void GraphOpCreation::clear_graph_id() {
  graph_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphOpCreation::graph_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.graph_id)
  return graph_id_.Get();
}
inline void GraphOpCreation::set_graph_id(const std::string& value) {
  
  graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.graph_id)
}
inline void GraphOpCreation::set_graph_id(std::string&& value) {
  
  graph_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphOpCreation.graph_id)
}
inline void GraphOpCreation::set_graph_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphOpCreation.graph_id)
}
inline void GraphOpCreation::set_graph_id(const char* value,
    size_t size) {
  
  graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphOpCreation.graph_id)
}
inline std::string* GraphOpCreation::mutable_graph_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.graph_id)
  return graph_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphOpCreation::release_graph_id() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.graph_id)
  
  return graph_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphOpCreation::set_allocated_graph_id(std::string* graph_id) {
  if (graph_id != nullptr) {
    
  } else {
    
  }
  graph_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), graph_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.graph_id)
}
inline std::string* GraphOpCreation::unsafe_arena_release_graph_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphOpCreation.graph_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return graph_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphOpCreation::unsafe_arena_set_allocated_graph_id(
    std::string* graph_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (graph_id != nullptr) {
    
  } else {
    
  }
  graph_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      graph_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphOpCreation.graph_id)
}

// string device_name = 5;
inline void GraphOpCreation::clear_device_name() {
  device_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphOpCreation::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.device_name)
  return device_name_.Get();
}
inline void GraphOpCreation::set_device_name(const std::string& value) {
  
  device_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.device_name)
}
inline void GraphOpCreation::set_device_name(std::string&& value) {
  
  device_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphOpCreation.device_name)
}
inline void GraphOpCreation::set_device_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphOpCreation.device_name)
}
inline void GraphOpCreation::set_device_name(const char* value,
    size_t size) {
  
  device_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphOpCreation.device_name)
}
inline std::string* GraphOpCreation::mutable_device_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.device_name)
  return device_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphOpCreation::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.device_name)
  
  return device_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphOpCreation::set_allocated_device_name(std::string* device_name) {
  if (device_name != nullptr) {
    
  } else {
    
  }
  device_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.device_name)
}
inline std::string* GraphOpCreation::unsafe_arena_release_device_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphOpCreation.device_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphOpCreation::unsafe_arena_set_allocated_device_name(
    std::string* device_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device_name != nullptr) {
    
  } else {
    
  }
  device_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphOpCreation.device_name)
}

// repeated string input_names = 6;
inline int GraphOpCreation::input_names_size() const {
  return input_names_.size();
}
inline void GraphOpCreation::clear_input_names() {
  input_names_.Clear();
}
inline const std::string& GraphOpCreation::input_names(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.input_names)
  return input_names_.Get(index);
}
inline std::string* GraphOpCreation::mutable_input_names(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.input_names)
  return input_names_.Mutable(index);
}
inline void GraphOpCreation::set_input_names(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.input_names)
  input_names_.Mutable(index)->assign(value);
}
inline void GraphOpCreation::set_input_names(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.input_names)
  input_names_.Mutable(index)->assign(std::move(value));
}
inline void GraphOpCreation::set_input_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphOpCreation.input_names)
}
inline void GraphOpCreation::set_input_names(int index, const char* value, size_t size) {
  input_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphOpCreation.input_names)
}
inline std::string* GraphOpCreation::add_input_names() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.GraphOpCreation.input_names)
  return input_names_.Add();
}
inline void GraphOpCreation::add_input_names(const std::string& value) {
  input_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphOpCreation.input_names)
}
inline void GraphOpCreation::add_input_names(std::string&& value) {
  input_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.GraphOpCreation.input_names)
}
inline void GraphOpCreation::add_input_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.GraphOpCreation.input_names)
}
inline void GraphOpCreation::add_input_names(const char* value, size_t size) {
  input_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.GraphOpCreation.input_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
GraphOpCreation::input_names() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphOpCreation.input_names)
  return input_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
GraphOpCreation::mutable_input_names() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphOpCreation.input_names)
  return &input_names_;
}

// int32 num_outputs = 7;
inline void GraphOpCreation::clear_num_outputs() {
  num_outputs_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphOpCreation::num_outputs() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.num_outputs)
  return num_outputs_;
}
inline void GraphOpCreation::set_num_outputs(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_outputs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.num_outputs)
}

// .tensorflow.CodeLocation code_location = 8;
inline bool GraphOpCreation::has_code_location() const {
  return this != internal_default_instance() && code_location_ != nullptr;
}
inline void GraphOpCreation::clear_code_location() {
  if (GetArenaNoVirtual() == nullptr && code_location_ != nullptr) {
    delete code_location_;
  }
  code_location_ = nullptr;
}
inline const ::tensorflow::CodeLocation& GraphOpCreation::code_location() const {
  const ::tensorflow::CodeLocation* p = code_location_;
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.code_location)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::CodeLocation*>(
      &::tensorflow::_CodeLocation_default_instance_);
}
inline ::tensorflow::CodeLocation* GraphOpCreation::release_code_location() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.code_location)
  
  ::tensorflow::CodeLocation* temp = code_location_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  code_location_ = nullptr;
  return temp;
}
inline ::tensorflow::CodeLocation* GraphOpCreation::unsafe_arena_release_code_location() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphOpCreation.code_location)
  
  ::tensorflow::CodeLocation* temp = code_location_;
  code_location_ = nullptr;
  return temp;
}
inline ::tensorflow::CodeLocation* GraphOpCreation::mutable_code_location() {
  
  if (code_location_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CodeLocation>(GetArenaNoVirtual());
    code_location_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.code_location)
  return code_location_;
}
inline void GraphOpCreation::set_allocated_code_location(::tensorflow::CodeLocation* code_location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete code_location_;
  }
  if (code_location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(code_location);
    if (message_arena != submessage_arena) {
      code_location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, code_location, submessage_arena);
    }
    
  } else {
    
  }
  code_location_ = code_location;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.code_location)
}

// repeated int32 output_tensor_ids = 9;
inline int GraphOpCreation::output_tensor_ids_size() const {
  return output_tensor_ids_.size();
}
inline void GraphOpCreation::clear_output_tensor_ids() {
  output_tensor_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphOpCreation::output_tensor_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.output_tensor_ids)
  return output_tensor_ids_.Get(index);
}
inline void GraphOpCreation::set_output_tensor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  output_tensor_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.output_tensor_ids)
}
inline void GraphOpCreation::add_output_tensor_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  output_tensor_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphOpCreation.output_tensor_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
GraphOpCreation::output_tensor_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphOpCreation.output_tensor_ids)
  return output_tensor_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
GraphOpCreation::mutable_output_tensor_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphOpCreation.output_tensor_ids)
  return &output_tensor_ids_;
}

// -------------------------------------------------------------------

// DebuggedGraph

// string graph_id = 1;
inline void DebuggedGraph::clear_graph_id() {
  graph_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebuggedGraph::graph_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.graph_id)
  return graph_id_.Get();
}
inline void DebuggedGraph::set_graph_id(const std::string& value) {
  
  graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.graph_id)
}
inline void DebuggedGraph::set_graph_id(std::string&& value) {
  
  graph_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebuggedGraph.graph_id)
}
inline void DebuggedGraph::set_graph_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedGraph.graph_id)
}
inline void DebuggedGraph::set_graph_id(const char* value,
    size_t size) {
  
  graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedGraph.graph_id)
}
inline std::string* DebuggedGraph::mutable_graph_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.graph_id)
  return graph_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebuggedGraph::release_graph_id() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedGraph.graph_id)
  
  return graph_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebuggedGraph::set_allocated_graph_id(std::string* graph_id) {
  if (graph_id != nullptr) {
    
  } else {
    
  }
  graph_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), graph_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedGraph.graph_id)
}
inline std::string* DebuggedGraph::unsafe_arena_release_graph_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebuggedGraph.graph_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return graph_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebuggedGraph::unsafe_arena_set_allocated_graph_id(
    std::string* graph_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (graph_id != nullptr) {
    
  } else {
    
  }
  graph_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      graph_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebuggedGraph.graph_id)
}

// string graph_name = 2;
inline void DebuggedGraph::clear_graph_name() {
  graph_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebuggedGraph::graph_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.graph_name)
  return graph_name_.Get();
}
inline void DebuggedGraph::set_graph_name(const std::string& value) {
  
  graph_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.graph_name)
}
inline void DebuggedGraph::set_graph_name(std::string&& value) {
  
  graph_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebuggedGraph.graph_name)
}
inline void DebuggedGraph::set_graph_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  graph_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedGraph.graph_name)
}
inline void DebuggedGraph::set_graph_name(const char* value,
    size_t size) {
  
  graph_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedGraph.graph_name)
}
inline std::string* DebuggedGraph::mutable_graph_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.graph_name)
  return graph_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebuggedGraph::release_graph_name() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedGraph.graph_name)
  
  return graph_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebuggedGraph::set_allocated_graph_name(std::string* graph_name) {
  if (graph_name != nullptr) {
    
  } else {
    
  }
  graph_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), graph_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedGraph.graph_name)
}
inline std::string* DebuggedGraph::unsafe_arena_release_graph_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebuggedGraph.graph_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return graph_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebuggedGraph::unsafe_arena_set_allocated_graph_name(
    std::string* graph_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (graph_name != nullptr) {
    
  } else {
    
  }
  graph_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      graph_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebuggedGraph.graph_name)
}

// repeated string instrumented_ops = 3;
inline int DebuggedGraph::instrumented_ops_size() const {
  return instrumented_ops_.size();
}
inline void DebuggedGraph::clear_instrumented_ops() {
  instrumented_ops_.Clear();
}
inline const std::string& DebuggedGraph::instrumented_ops(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.instrumented_ops)
  return instrumented_ops_.Get(index);
}
inline std::string* DebuggedGraph::mutable_instrumented_ops(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.instrumented_ops)
  return instrumented_ops_.Mutable(index);
}
inline void DebuggedGraph::set_instrumented_ops(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.instrumented_ops)
  instrumented_ops_.Mutable(index)->assign(value);
}
inline void DebuggedGraph::set_instrumented_ops(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.instrumented_ops)
  instrumented_ops_.Mutable(index)->assign(std::move(value));
}
inline void DebuggedGraph::set_instrumented_ops(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  instrumented_ops_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedGraph.instrumented_ops)
}
inline void DebuggedGraph::set_instrumented_ops(int index, const char* value, size_t size) {
  instrumented_ops_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedGraph.instrumented_ops)
}
inline std::string* DebuggedGraph::add_instrumented_ops() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebuggedGraph.instrumented_ops)
  return instrumented_ops_.Add();
}
inline void DebuggedGraph::add_instrumented_ops(const std::string& value) {
  instrumented_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedGraph.instrumented_ops)
}
inline void DebuggedGraph::add_instrumented_ops(std::string&& value) {
  instrumented_ops_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedGraph.instrumented_ops)
}
inline void DebuggedGraph::add_instrumented_ops(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  instrumented_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebuggedGraph.instrumented_ops)
}
inline void DebuggedGraph::add_instrumented_ops(const char* value, size_t size) {
  instrumented_ops_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebuggedGraph.instrumented_ops)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebuggedGraph::instrumented_ops() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebuggedGraph.instrumented_ops)
  return instrumented_ops_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebuggedGraph::mutable_instrumented_ops() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebuggedGraph.instrumented_ops)
  return &instrumented_ops_;
}

// bytes original_graph_def = 4;
inline void DebuggedGraph::clear_original_graph_def() {
  original_graph_def_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebuggedGraph::original_graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.original_graph_def)
  return original_graph_def_.Get();
}
inline void DebuggedGraph::set_original_graph_def(const std::string& value) {
  
  original_graph_def_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.original_graph_def)
}
inline void DebuggedGraph::set_original_graph_def(std::string&& value) {
  
  original_graph_def_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebuggedGraph.original_graph_def)
}
inline void DebuggedGraph::set_original_graph_def(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  original_graph_def_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedGraph.original_graph_def)
}
inline void DebuggedGraph::set_original_graph_def(const void* value,
    size_t size) {
  
  original_graph_def_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedGraph.original_graph_def)
}
inline std::string* DebuggedGraph::mutable_original_graph_def() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.original_graph_def)
  return original_graph_def_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebuggedGraph::release_original_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedGraph.original_graph_def)
  
  return original_graph_def_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebuggedGraph::set_allocated_original_graph_def(std::string* original_graph_def) {
  if (original_graph_def != nullptr) {
    
  } else {
    
  }
  original_graph_def_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), original_graph_def,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedGraph.original_graph_def)
}
inline std::string* DebuggedGraph::unsafe_arena_release_original_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebuggedGraph.original_graph_def)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return original_graph_def_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebuggedGraph::unsafe_arena_set_allocated_original_graph_def(
    std::string* original_graph_def) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (original_graph_def != nullptr) {
    
  } else {
    
  }
  original_graph_def_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      original_graph_def, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebuggedGraph.original_graph_def)
}

// bytes instrumented_graph_def = 5;
inline void DebuggedGraph::clear_instrumented_graph_def() {
  instrumented_graph_def_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebuggedGraph::instrumented_graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.instrumented_graph_def)
  return instrumented_graph_def_.Get();
}
inline void DebuggedGraph::set_instrumented_graph_def(const std::string& value) {
  
  instrumented_graph_def_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.instrumented_graph_def)
}
inline void DebuggedGraph::set_instrumented_graph_def(std::string&& value) {
  
  instrumented_graph_def_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebuggedGraph.instrumented_graph_def)
}
inline void DebuggedGraph::set_instrumented_graph_def(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  instrumented_graph_def_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedGraph.instrumented_graph_def)
}
inline void DebuggedGraph::set_instrumented_graph_def(const void* value,
    size_t size) {
  
  instrumented_graph_def_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedGraph.instrumented_graph_def)
}
inline std::string* DebuggedGraph::mutable_instrumented_graph_def() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.instrumented_graph_def)
  return instrumented_graph_def_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebuggedGraph::release_instrumented_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedGraph.instrumented_graph_def)
  
  return instrumented_graph_def_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebuggedGraph::set_allocated_instrumented_graph_def(std::string* instrumented_graph_def) {
  if (instrumented_graph_def != nullptr) {
    
  } else {
    
  }
  instrumented_graph_def_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), instrumented_graph_def,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedGraph.instrumented_graph_def)
}
inline std::string* DebuggedGraph::unsafe_arena_release_instrumented_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebuggedGraph.instrumented_graph_def)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return instrumented_graph_def_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebuggedGraph::unsafe_arena_set_allocated_instrumented_graph_def(
    std::string* instrumented_graph_def) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (instrumented_graph_def != nullptr) {
    
  } else {
    
  }
  instrumented_graph_def_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      instrumented_graph_def, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebuggedGraph.instrumented_graph_def)
}

// string outer_context_id = 6;
inline void DebuggedGraph::clear_outer_context_id() {
  outer_context_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebuggedGraph::outer_context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.outer_context_id)
  return outer_context_id_.Get();
}
inline void DebuggedGraph::set_outer_context_id(const std::string& value) {
  
  outer_context_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.outer_context_id)
}
inline void DebuggedGraph::set_outer_context_id(std::string&& value) {
  
  outer_context_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebuggedGraph.outer_context_id)
}
inline void DebuggedGraph::set_outer_context_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  outer_context_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedGraph.outer_context_id)
}
inline void DebuggedGraph::set_outer_context_id(const char* value,
    size_t size) {
  
  outer_context_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedGraph.outer_context_id)
}
inline std::string* DebuggedGraph::mutable_outer_context_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.outer_context_id)
  return outer_context_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebuggedGraph::release_outer_context_id() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedGraph.outer_context_id)
  
  return outer_context_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebuggedGraph::set_allocated_outer_context_id(std::string* outer_context_id) {
  if (outer_context_id != nullptr) {
    
  } else {
    
  }
  outer_context_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), outer_context_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedGraph.outer_context_id)
}
inline std::string* DebuggedGraph::unsafe_arena_release_outer_context_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebuggedGraph.outer_context_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return outer_context_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebuggedGraph::unsafe_arena_set_allocated_outer_context_id(
    std::string* outer_context_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (outer_context_id != nullptr) {
    
  } else {
    
  }
  outer_context_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      outer_context_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebuggedGraph.outer_context_id)
}

// -------------------------------------------------------------------

// DebuggedDevice

// string device_name = 1;
inline void DebuggedDevice::clear_device_name() {
  device_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebuggedDevice::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedDevice.device_name)
  return device_name_.Get();
}
inline void DebuggedDevice::set_device_name(const std::string& value) {
  
  device_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedDevice.device_name)
}
inline void DebuggedDevice::set_device_name(std::string&& value) {
  
  device_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebuggedDevice.device_name)
}
inline void DebuggedDevice::set_device_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedDevice.device_name)
}
inline void DebuggedDevice::set_device_name(const char* value,
    size_t size) {
  
  device_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedDevice.device_name)
}
inline std::string* DebuggedDevice::mutable_device_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedDevice.device_name)
  return device_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebuggedDevice::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedDevice.device_name)
  
  return device_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebuggedDevice::set_allocated_device_name(std::string* device_name) {
  if (device_name != nullptr) {
    
  } else {
    
  }
  device_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedDevice.device_name)
}
inline std::string* DebuggedDevice::unsafe_arena_release_device_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebuggedDevice.device_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebuggedDevice::unsafe_arena_set_allocated_device_name(
    std::string* device_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device_name != nullptr) {
    
  } else {
    
  }
  device_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebuggedDevice.device_name)
}

// int32 device_id = 2;
inline void DebuggedDevice::clear_device_id() {
  device_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebuggedDevice::device_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedDevice.device_id)
  return device_id_;
}
inline void DebuggedDevice::set_device_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  device_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedDevice.device_id)
}

// -------------------------------------------------------------------

// Execution

// string op_type = 1;
inline void Execution::clear_op_type() {
  op_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& Execution::op_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.op_type)
  return op_type_.Get();
}
inline void Execution::set_op_type(const std::string& value) {
  
  op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Execution.op_type)
}
inline void Execution::set_op_type(std::string&& value) {
  
  op_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Execution.op_type)
}
inline void Execution::set_op_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Execution.op_type)
}
inline void Execution::set_op_type(const char* value,
    size_t size) {
  
  op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Execution.op_type)
}
inline std::string* Execution::mutable_op_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.Execution.op_type)
  return op_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* Execution::release_op_type() {
  // @@protoc_insertion_point(field_release:tensorflow.Execution.op_type)
  
  return op_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Execution::set_allocated_op_type(std::string* op_type) {
  if (op_type != nullptr) {
    
  } else {
    
  }
  op_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Execution.op_type)
}
inline std::string* Execution::unsafe_arena_release_op_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Execution.op_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return op_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Execution::unsafe_arena_set_allocated_op_type(
    std::string* op_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (op_type != nullptr) {
    
  } else {
    
  }
  op_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      op_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Execution.op_type)
}

// int32 num_outputs = 2;
inline void Execution::clear_num_outputs() {
  num_outputs_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Execution::num_outputs() const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.num_outputs)
  return num_outputs_;
}
inline void Execution::set_num_outputs(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_outputs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Execution.num_outputs)
}

// string graph_id = 3;
inline void Execution::clear_graph_id() {
  graph_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& Execution::graph_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.graph_id)
  return graph_id_.Get();
}
inline void Execution::set_graph_id(const std::string& value) {
  
  graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.Execution.graph_id)
}
inline void Execution::set_graph_id(std::string&& value) {
  
  graph_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.Execution.graph_id)
}
inline void Execution::set_graph_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.Execution.graph_id)
}
inline void Execution::set_graph_id(const char* value,
    size_t size) {
  
  graph_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.Execution.graph_id)
}
inline std::string* Execution::mutable_graph_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.Execution.graph_id)
  return graph_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* Execution::release_graph_id() {
  // @@protoc_insertion_point(field_release:tensorflow.Execution.graph_id)
  
  return graph_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Execution::set_allocated_graph_id(std::string* graph_id) {
  if (graph_id != nullptr) {
    
  } else {
    
  }
  graph_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), graph_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Execution.graph_id)
}
inline std::string* Execution::unsafe_arena_release_graph_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Execution.graph_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return graph_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Execution::unsafe_arena_set_allocated_graph_id(
    std::string* graph_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (graph_id != nullptr) {
    
  } else {
    
  }
  graph_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      graph_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Execution.graph_id)
}

// repeated int64 input_tensor_ids = 4;
inline int Execution::input_tensor_ids_size() const {
  return input_tensor_ids_.size();
}
inline void Execution::clear_input_tensor_ids() {
  input_tensor_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Execution::input_tensor_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.input_tensor_ids)
  return input_tensor_ids_.Get(index);
}
inline void Execution::set_input_tensor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  input_tensor_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.Execution.input_tensor_ids)
}
inline void Execution::add_input_tensor_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  input_tensor_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.Execution.input_tensor_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
Execution::input_tensor_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.Execution.input_tensor_ids)
  return input_tensor_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
Execution::mutable_input_tensor_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Execution.input_tensor_ids)
  return &input_tensor_ids_;
}

// repeated int64 output_tensor_ids = 5;
inline int Execution::output_tensor_ids_size() const {
  return output_tensor_ids_.size();
}
inline void Execution::clear_output_tensor_ids() {
  output_tensor_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Execution::output_tensor_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.output_tensor_ids)
  return output_tensor_ids_.Get(index);
}
inline void Execution::set_output_tensor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  output_tensor_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.Execution.output_tensor_ids)
}
inline void Execution::add_output_tensor_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  output_tensor_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.Execution.output_tensor_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
Execution::output_tensor_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.Execution.output_tensor_ids)
  return output_tensor_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
Execution::mutable_output_tensor_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Execution.output_tensor_ids)
  return &output_tensor_ids_;
}

// .tensorflow.TensorDebugMode tensor_debug_mode = 6;
inline void Execution::clear_tensor_debug_mode() {
  tensor_debug_mode_ = 0;
}
inline ::tensorflow::TensorDebugMode Execution::tensor_debug_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.tensor_debug_mode)
  return static_cast< ::tensorflow::TensorDebugMode >(tensor_debug_mode_);
}
inline void Execution::set_tensor_debug_mode(::tensorflow::TensorDebugMode value) {
  
  tensor_debug_mode_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.Execution.tensor_debug_mode)
}

// repeated .tensorflow.TensorProto tensor_protos = 7;
inline int Execution::tensor_protos_size() const {
  return tensor_protos_.size();
}
inline ::tensorflow::TensorProto* Execution::mutable_tensor_protos(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.Execution.tensor_protos)
  return tensor_protos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
Execution::mutable_tensor_protos() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Execution.tensor_protos)
  return &tensor_protos_;
}
inline const ::tensorflow::TensorProto& Execution::tensor_protos(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.tensor_protos)
  return tensor_protos_.Get(index);
}
inline ::tensorflow::TensorProto* Execution::add_tensor_protos() {
  // @@protoc_insertion_point(field_add:tensorflow.Execution.tensor_protos)
  return tensor_protos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
Execution::tensor_protos() const {
  // @@protoc_insertion_point(field_list:tensorflow.Execution.tensor_protos)
  return tensor_protos_;
}

// .tensorflow.CodeLocation code_location = 8;
inline bool Execution::has_code_location() const {
  return this != internal_default_instance() && code_location_ != nullptr;
}
inline void Execution::clear_code_location() {
  if (GetArenaNoVirtual() == nullptr && code_location_ != nullptr) {
    delete code_location_;
  }
  code_location_ = nullptr;
}
inline const ::tensorflow::CodeLocation& Execution::code_location() const {
  const ::tensorflow::CodeLocation* p = code_location_;
  // @@protoc_insertion_point(field_get:tensorflow.Execution.code_location)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::CodeLocation*>(
      &::tensorflow::_CodeLocation_default_instance_);
}
inline ::tensorflow::CodeLocation* Execution::release_code_location() {
  // @@protoc_insertion_point(field_release:tensorflow.Execution.code_location)
  
  ::tensorflow::CodeLocation* temp = code_location_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  code_location_ = nullptr;
  return temp;
}
inline ::tensorflow::CodeLocation* Execution::unsafe_arena_release_code_location() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Execution.code_location)
  
  ::tensorflow::CodeLocation* temp = code_location_;
  code_location_ = nullptr;
  return temp;
}
inline ::tensorflow::CodeLocation* Execution::mutable_code_location() {
  
  if (code_location_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CodeLocation>(GetArenaNoVirtual());
    code_location_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Execution.code_location)
  return code_location_;
}
inline void Execution::set_allocated_code_location(::tensorflow::CodeLocation* code_location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete code_location_;
  }
  if (code_location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(code_location);
    if (message_arena != submessage_arena) {
      code_location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, code_location, submessage_arena);
    }
    
  } else {
    
  }
  code_location_ = code_location;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Execution.code_location)
}

// repeated int32 output_tensor_device_ids = 9;
inline int Execution::output_tensor_device_ids_size() const {
  return output_tensor_device_ids_.size();
}
inline void Execution::clear_output_tensor_device_ids() {
  output_tensor_device_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Execution::output_tensor_device_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.output_tensor_device_ids)
  return output_tensor_device_ids_.Get(index);
}
inline void Execution::set_output_tensor_device_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  output_tensor_device_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.Execution.output_tensor_device_ids)
}
inline void Execution::add_output_tensor_device_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  output_tensor_device_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.Execution.output_tensor_device_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Execution::output_tensor_device_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.Execution.output_tensor_device_ids)
  return output_tensor_device_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Execution::mutable_output_tensor_device_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Execution.output_tensor_device_ids)
  return &output_tensor_device_ids_;
}

// -------------------------------------------------------------------

// GraphExecutionTrace

// string tfdbg_context_id = 1;
inline void GraphExecutionTrace::clear_tfdbg_context_id() {
  tfdbg_context_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphExecutionTrace::tfdbg_context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.tfdbg_context_id)
  return tfdbg_context_id_.Get();
}
inline void GraphExecutionTrace::set_tfdbg_context_id(const std::string& value) {
  
  tfdbg_context_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphExecutionTrace.tfdbg_context_id)
}
inline void GraphExecutionTrace::set_tfdbg_context_id(std::string&& value) {
  
  tfdbg_context_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphExecutionTrace.tfdbg_context_id)
}
inline void GraphExecutionTrace::set_tfdbg_context_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  tfdbg_context_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphExecutionTrace.tfdbg_context_id)
}
inline void GraphExecutionTrace::set_tfdbg_context_id(const char* value,
    size_t size) {
  
  tfdbg_context_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphExecutionTrace.tfdbg_context_id)
}
inline std::string* GraphExecutionTrace::mutable_tfdbg_context_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphExecutionTrace.tfdbg_context_id)
  return tfdbg_context_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphExecutionTrace::release_tfdbg_context_id() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphExecutionTrace.tfdbg_context_id)
  
  return tfdbg_context_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphExecutionTrace::set_allocated_tfdbg_context_id(std::string* tfdbg_context_id) {
  if (tfdbg_context_id != nullptr) {
    
  } else {
    
  }
  tfdbg_context_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tfdbg_context_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphExecutionTrace.tfdbg_context_id)
}
inline std::string* GraphExecutionTrace::unsafe_arena_release_tfdbg_context_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphExecutionTrace.tfdbg_context_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return tfdbg_context_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphExecutionTrace::unsafe_arena_set_allocated_tfdbg_context_id(
    std::string* tfdbg_context_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (tfdbg_context_id != nullptr) {
    
  } else {
    
  }
  tfdbg_context_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      tfdbg_context_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphExecutionTrace.tfdbg_context_id)
}

// string op_name = 2;
inline void GraphExecutionTrace::clear_op_name() {
  op_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphExecutionTrace::op_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.op_name)
  return op_name_.Get();
}
inline void GraphExecutionTrace::set_op_name(const std::string& value) {
  
  op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphExecutionTrace.op_name)
}
inline void GraphExecutionTrace::set_op_name(std::string&& value) {
  
  op_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphExecutionTrace.op_name)
}
inline void GraphExecutionTrace::set_op_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphExecutionTrace.op_name)
}
inline void GraphExecutionTrace::set_op_name(const char* value,
    size_t size) {
  
  op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphExecutionTrace.op_name)
}
inline std::string* GraphExecutionTrace::mutable_op_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphExecutionTrace.op_name)
  return op_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphExecutionTrace::release_op_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphExecutionTrace.op_name)
  
  return op_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphExecutionTrace::set_allocated_op_name(std::string* op_name) {
  if (op_name != nullptr) {
    
  } else {
    
  }
  op_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphExecutionTrace.op_name)
}
inline std::string* GraphExecutionTrace::unsafe_arena_release_op_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphExecutionTrace.op_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return op_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphExecutionTrace::unsafe_arena_set_allocated_op_name(
    std::string* op_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (op_name != nullptr) {
    
  } else {
    
  }
  op_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      op_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphExecutionTrace.op_name)
}

// int32 output_slot = 3;
inline void GraphExecutionTrace::clear_output_slot() {
  output_slot_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphExecutionTrace::output_slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.output_slot)
  return output_slot_;
}
inline void GraphExecutionTrace::set_output_slot(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  output_slot_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphExecutionTrace.output_slot)
}

// .tensorflow.TensorDebugMode tensor_debug_mode = 4;
inline void GraphExecutionTrace::clear_tensor_debug_mode() {
  tensor_debug_mode_ = 0;
}
inline ::tensorflow::TensorDebugMode GraphExecutionTrace::tensor_debug_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.tensor_debug_mode)
  return static_cast< ::tensorflow::TensorDebugMode >(tensor_debug_mode_);
}
inline void GraphExecutionTrace::set_tensor_debug_mode(::tensorflow::TensorDebugMode value) {
  
  tensor_debug_mode_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphExecutionTrace.tensor_debug_mode)
}

// .tensorflow.TensorProto tensor_proto = 5;
inline bool GraphExecutionTrace::has_tensor_proto() const {
  return this != internal_default_instance() && tensor_proto_ != nullptr;
}
inline const ::tensorflow::TensorProto& GraphExecutionTrace::tensor_proto() const {
  const ::tensorflow::TensorProto* p = tensor_proto_;
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.tensor_proto)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* GraphExecutionTrace::release_tensor_proto() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphExecutionTrace.tensor_proto)
  
  ::tensorflow::TensorProto* temp = tensor_proto_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  tensor_proto_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* GraphExecutionTrace::unsafe_arena_release_tensor_proto() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphExecutionTrace.tensor_proto)
  
  ::tensorflow::TensorProto* temp = tensor_proto_;
  tensor_proto_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* GraphExecutionTrace::mutable_tensor_proto() {
  
  if (tensor_proto_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    tensor_proto_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphExecutionTrace.tensor_proto)
  return tensor_proto_;
}
inline void GraphExecutionTrace::set_allocated_tensor_proto(::tensorflow::TensorProto* tensor_proto) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_proto_);
  }
  if (tensor_proto) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_proto)->GetArena();
    if (message_arena != submessage_arena) {
      tensor_proto = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_proto, submessage_arena);
    }
    
  } else {
    
  }
  tensor_proto_ = tensor_proto;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphExecutionTrace.tensor_proto)
}

// string device_name = 6;
inline void GraphExecutionTrace::clear_device_name() {
  device_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphExecutionTrace::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.device_name)
  return device_name_.Get();
}
inline void GraphExecutionTrace::set_device_name(const std::string& value) {
  
  device_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphExecutionTrace.device_name)
}
inline void GraphExecutionTrace::set_device_name(std::string&& value) {
  
  device_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphExecutionTrace.device_name)
}
inline void GraphExecutionTrace::set_device_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphExecutionTrace.device_name)
}
inline void GraphExecutionTrace::set_device_name(const char* value,
    size_t size) {
  
  device_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphExecutionTrace.device_name)
}
inline std::string* GraphExecutionTrace::mutable_device_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphExecutionTrace.device_name)
  return device_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphExecutionTrace::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphExecutionTrace.device_name)
  
  return device_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphExecutionTrace::set_allocated_device_name(std::string* device_name) {
  if (device_name != nullptr) {
    
  } else {
    
  }
  device_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphExecutionTrace.device_name)
}
inline std::string* GraphExecutionTrace::unsafe_arena_release_device_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphExecutionTrace.device_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphExecutionTrace::unsafe_arena_set_allocated_device_name(
    std::string* device_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device_name != nullptr) {
    
  } else {
    
  }
  device_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphExecutionTrace.device_name)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::TensorDebugMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::TensorDebugMode>() {
  return ::tensorflow::TensorDebugMode_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto
