from apps.models.base_model import BaseEntity


class InterfaceLogEntity(BaseEntity):
    """
    接口日志实体
    """
    RequestParam = None  # 请求参数
    ResponseParam = None  # 响应参数

    def to_entity_dict(self):
        # 将当前实体的属性转换为字典，包括嵌套实体的字典
        return {
            'RequestParam': self.RequestParam,
            'ResponseParam': self.ResponseParam
        }


class RequestParam(BaseEntity):
    def __init__(self):
        self.signal = 0  # ECG信号
        self.fs = 0  # 采样率
        self.gain = 0  # 增益
        self.zero = 0  # 零点
