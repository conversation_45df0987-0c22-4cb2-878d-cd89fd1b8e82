{{- if and .Values.ingress.enabled .Values.analytics.enabled -}}
{{- $fullName := include "cvat.fullname" . -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}-analytics
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cvat.labels" . | nindent 4 }}
  annotations:
    {{- if .Values.traefik.enabled }}
    traefik.ingress.kubernetes.io/router.middlewares: {{ $.Release.Namespace }}-stripprefix@kubernetescrd,
      {{ $.Release.Namespace }}-forwardauth@kubernetescrd
    {{- end }}
    {{- with .Values.ingress.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
{{- if .Values.ingress.className }}
  ingressClassName: {{ .Values.ingress.className | toYaml }}
{{- else if and .Values.traefik.enabled .Values.traefik.ingressClass.enabled }}
  ingressClassName: {{ .Values.traefik.ingressClass.name | default (include "traefik.fullname" .Subcharts.traefik) }}
{{- end }}
  {{- if .Values.ingress.tls }}
  tls:
  - hosts:
    - {{ .Values.ingress.hostname }}
    secretName: {{ .Values.ingress.tlsSecretName }}
  {{- end }}
  rules:
  - host: {{ .Values.ingress.hostname }}
    http:
      paths:
      - path: /analytics
        pathType: Prefix
        backend:
          service:
            name: {{ $.Release.Name }}-grafana
            port:
              number: 80
{{- end }}
