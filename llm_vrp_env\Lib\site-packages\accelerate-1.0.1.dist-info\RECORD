../../Scripts/accelerate-config.exe,sha256=JFEETPxrruQbZFF7MRHxP2S0N1IJjRa7FjlqB9RRB6w,108402
../../Scripts/accelerate-estimate-memory.exe,sha256=_9hj4bNJIci09i3p2LJcOvGMM-feunRfXKBKzN-fUkU,108404
../../Scripts/accelerate-launch.exe,sha256=HSBaNMpFyYgnR1Y4_AbqusK8fLbO-tI3kl3jFwdf4RI,108402
../../Scripts/accelerate-merge-weights.exe,sha256=4T6c1eowSZInfXp-yCU9tdlEa1wH0z6pIwU1hryhw2I,108401
../../Scripts/accelerate.exe,sha256=2bAAfIbYvj-JG3evtKNOQIqt6J18L858yZ3JkBYQSmw,108410
accelerate-1.0.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
accelerate-1.0.1.dist-info/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
accelerate-1.0.1.dist-info/METADATA,sha256=imC6JcBi-B3Yw4-6L7p2u6jxNP0e-qPJta9JeGTi7lQ,19257
accelerate-1.0.1.dist-info/RECORD,,
accelerate-1.0.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate-1.0.1.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
accelerate-1.0.1.dist-info/entry_points.txt,sha256=Vpy8gUGfZ-1VnM2229fb8CpJNLBdMH_wtJ9PQ7b_2tQ,296
accelerate-1.0.1.dist-info/top_level.txt,sha256=esVfdxTidsjQ90zsN_rPpjLFJ4ijRlx4mnLrG09hlt4,11
accelerate/__init__.py,sha256=wRaJogKBp9usAcy7NmNvwBxc4wsqeAYqCXZnJ6OPRSI,1504
accelerate/__pycache__/__init__.cpython-38.pyc,,
accelerate/__pycache__/accelerator.cpython-38.pyc,,
accelerate/__pycache__/big_modeling.cpython-38.pyc,,
accelerate/__pycache__/checkpointing.cpython-38.pyc,,
accelerate/__pycache__/data_loader.cpython-38.pyc,,
accelerate/__pycache__/hooks.cpython-38.pyc,,
accelerate/__pycache__/inference.cpython-38.pyc,,
accelerate/__pycache__/launchers.cpython-38.pyc,,
accelerate/__pycache__/local_sgd.cpython-38.pyc,,
accelerate/__pycache__/logging.cpython-38.pyc,,
accelerate/__pycache__/memory_utils.cpython-38.pyc,,
accelerate/__pycache__/optimizer.cpython-38.pyc,,
accelerate/__pycache__/scheduler.cpython-38.pyc,,
accelerate/__pycache__/state.cpython-38.pyc,,
accelerate/__pycache__/tracking.cpython-38.pyc,,
accelerate/accelerator.py,sha256=OioJ0WRbawliEgT4EN2fqmzaUCR_brCuVhywxnFQEyY,161376
accelerate/big_modeling.py,sha256=KX7Z8_dE9eMN4WhC5oaHYjqKEFqVufKGHaJ4n_EH4ts,29574
accelerate/checkpointing.py,sha256=yH8VdsD8clGftOibBzbB4CN3J1FWDzg5B2uM_VaqvIA,12876
accelerate/commands/__init__.py,sha256=m1PPTDT4ziIAvM0-FDSgIMIZ69Konn126s6LwuzH6v8,606
accelerate/commands/__pycache__/__init__.cpython-38.pyc,,
accelerate/commands/__pycache__/accelerate_cli.cpython-38.pyc,,
accelerate/commands/__pycache__/env.cpython-38.pyc,,
accelerate/commands/__pycache__/estimate.cpython-38.pyc,,
accelerate/commands/__pycache__/launch.cpython-38.pyc,,
accelerate/commands/__pycache__/merge.cpython-38.pyc,,
accelerate/commands/__pycache__/test.cpython-38.pyc,,
accelerate/commands/__pycache__/tpu.cpython-38.pyc,,
accelerate/commands/__pycache__/utils.cpython-38.pyc,,
accelerate/commands/accelerate_cli.py,sha256=aaqbgTuvtj0N4FPFI0KBpPTiVtWTPUWSlbSBzsy58l8,1856
accelerate/commands/config/__init__.py,sha256=iJK8dgj3pc5Vdr1E7UuGoFu-BlybyXLxYDoTg9gXngE,1645
accelerate/commands/config/__pycache__/__init__.cpython-38.pyc,,
accelerate/commands/config/__pycache__/cluster.cpython-38.pyc,,
accelerate/commands/config/__pycache__/config.cpython-38.pyc,,
accelerate/commands/config/__pycache__/config_args.cpython-38.pyc,,
accelerate/commands/config/__pycache__/config_utils.cpython-38.pyc,,
accelerate/commands/config/__pycache__/default.cpython-38.pyc,,
accelerate/commands/config/__pycache__/sagemaker.cpython-38.pyc,,
accelerate/commands/config/__pycache__/update.cpython-38.pyc,,
accelerate/commands/config/cluster.py,sha256=1_AakXqkgL13oVv_aB2iQSllZrY7FtxZ4lmxjwnJB4s,35631
accelerate/commands/config/config.py,sha256=FuRlQvOjgATEtyqOSsGD-KEtOCvACOHjs2C-krrtldk,3035
accelerate/commands/config/config_args.py,sha256=-fPg3nj4F_6Bp87dHS25fF6_3U2vP2rzllSd1Mj3TSw,9976
accelerate/commands/config/config_utils.py,sha256=beW-Hc-ka1NqSfId73L9ThnMxUf7K9otXsuxIuif9-A,3141
accelerate/commands/config/default.py,sha256=GI7Q9Asy7Cr81ditzNA6hOjIf6doinynz6-cO0GZwZ4,5705
accelerate/commands/config/sagemaker.py,sha256=GjHE2-h4tRr1P_PFtMF3miiAtJlzkbHbMb6kFXqn8eo,10341
accelerate/commands/config/update.py,sha256=NXW1J7GkUHpg71QlIXsmMB_0z8S8IZo2FWax5POwrhc,2395
accelerate/commands/env.py,sha256=VrG8ufBRMfP0R3sLyN0c8Fe3iPhyqGvLNKtQHX_uGBQ,3871
accelerate/commands/estimate.py,sha256=Ro0jeYOQPlQdR1XjqrUaUpyGFiUpX0cfW-pTgUwio1Q,12409
accelerate/commands/launch.py,sha256=637e9E68218PupSngacKKIX6kxWtM2spjMh5r8p792U,45429
accelerate/commands/menu/__init__.py,sha256=uqSlBM0TFHBwzdv3p3SXfpAk1lZFp4h1a7mbBdscPHs,645
accelerate/commands/menu/__pycache__/__init__.cpython-38.pyc,,
accelerate/commands/menu/__pycache__/cursor.cpython-38.pyc,,
accelerate/commands/menu/__pycache__/helpers.cpython-38.pyc,,
accelerate/commands/menu/__pycache__/input.cpython-38.pyc,,
accelerate/commands/menu/__pycache__/keymap.cpython-38.pyc,,
accelerate/commands/menu/__pycache__/selection_menu.cpython-38.pyc,,
accelerate/commands/menu/cursor.py,sha256=-lmpJVAzvNc0c3EOtSuLoKB59zqylVCbYyWLPnrOmvQ,2028
accelerate/commands/menu/helpers.py,sha256=KrSB5fJjH4MUEUAQJ6bYaN16AYcnl9UalDrPD3DYeeg,1483
accelerate/commands/menu/input.py,sha256=Uj9eDp8-Mb0Fe49nuogqo9W_RCfYd6udfjiPKx7Wjmg,2537
accelerate/commands/menu/keymap.py,sha256=eXj-suyYs1m5dEHoUKN4mKAMLc8DWHnwhP6G6JSU0jQ,4086
accelerate/commands/menu/selection_menu.py,sha256=bxy-DHaKKC6SCToOlMBv5_z0MdUzylEg6Sio9OuV3GM,4921
accelerate/commands/merge.py,sha256=quDKckN3vKn9nsGjdwfoojnfTMFdKRRUkY1DYuuNNmc,2388
accelerate/commands/test.py,sha256=YrPYEaAACOGZ6btn2MV6NbMSEdBUcMWADLbQWaZSHtk,2149
accelerate/commands/tpu.py,sha256=KyxDP7IuveidZrbW4rx2s8Ku3o_ptI6tzwr_R7ck0os,5548
accelerate/commands/utils.py,sha256=ilcfE32oHh28EToM00nc_SR6upfZiuxUI0AjjZu8KYY,3995
accelerate/data_loader.py,sha256=Mrthlx9iZvG2gi2LJFqc59STEQnuVLgOukMuu6NohkU,58459
accelerate/hooks.py,sha256=F3hrs5TK2FjtjhlPq25QEGsBomUd_VLv1WHHaLn-MuE,31614
accelerate/inference.py,sha256=LdFo8N0ivRxYrzrCXQN6oPFzyaRyxmHZ55eJaKOH9cM,7687
accelerate/launchers.py,sha256=g7RBD9QRSsIJBYLaCmaIjVXHKeiSIvgq2TOW0q9R9z0,13763
accelerate/local_sgd.py,sha256=0952iPZv1IkRDQIVvUlLOULX06K-73nM_Sb5BsSCfuk,4042
accelerate/logging.py,sha256=4XcgY_BV7Qn_enh2tZ-8fNtuaE_3n-LsYJbgwhRx_PI,5042
accelerate/memory_utils.py,sha256=3R5LoeHl6GgTZ-IMPrDZMdaEehWarGdPqODushb-6pg,862
accelerate/optimizer.py,sha256=NU4C9cWhb28uUcXJfDf0X7qT4tNBP5b_fbCPcOS9zow,7811
accelerate/scheduler.py,sha256=des_4M_Tt1W8gCYZZbLla0GHBEgJY3Wx2EGBQPTzeiY,4238
accelerate/state.py,sha256=6jOGPqHUyjrGCfaWdVgBrPLz3Y8p2Brm0FlhKXAdMrw,52098
accelerate/test_utils/__init__.py,sha256=OuXzYTvyikwGYXkN_Jc7HVnYG8RlLQojOGlsZ0i2KYI,1559
accelerate/test_utils/__pycache__/__init__.cpython-38.pyc,,
accelerate/test_utils/__pycache__/examples.cpython-38.pyc,,
accelerate/test_utils/__pycache__/testing.cpython-38.pyc,,
accelerate/test_utils/__pycache__/training.cpython-38.pyc,,
accelerate/test_utils/examples.py,sha256=jRm1S9TkmeoLaqprBvtVFN4LesiaDZtKMNIoLNY2euw,7281
accelerate/test_utils/scripts/__init__.py,sha256=m1PPTDT4ziIAvM0-FDSgIMIZ69Konn126s6LwuzH6v8,606
accelerate/test_utils/scripts/__pycache__/__init__.cpython-38.pyc,,
accelerate/test_utils/scripts/__pycache__/test_cli.cpython-38.pyc,,
accelerate/test_utils/scripts/__pycache__/test_ddp_comm_hook.cpython-38.pyc,,
accelerate/test_utils/scripts/__pycache__/test_distributed_data_loop.cpython-38.pyc,,
accelerate/test_utils/scripts/__pycache__/test_merge_weights.cpython-38.pyc,,
accelerate/test_utils/scripts/__pycache__/test_notebook.cpython-38.pyc,,
accelerate/test_utils/scripts/__pycache__/test_ops.cpython-38.pyc,,
accelerate/test_utils/scripts/__pycache__/test_script.cpython-38.pyc,,
accelerate/test_utils/scripts/__pycache__/test_sync.cpython-38.pyc,,
accelerate/test_utils/scripts/external_deps/__init__.py,sha256=m1PPTDT4ziIAvM0-FDSgIMIZ69Konn126s6LwuzH6v8,606
accelerate/test_utils/scripts/external_deps/__pycache__/__init__.cpython-38.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_checkpointing.cpython-38.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_ds_multiple_model.cpython-38.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_metrics.cpython-38.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_peak_memory_usage.cpython-38.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_performance.cpython-38.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_pippy.cpython-38.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_zero3_integration.cpython-38.pyc,,
accelerate/test_utils/scripts/external_deps/test_checkpointing.py,sha256=GukYgSZClft38oMlIh-K9bjkT1BAP-SnRp2Q673BmgQ,10699
accelerate/test_utils/scripts/external_deps/test_ds_multiple_model.py,sha256=Cg4-h0B4UcOQ5CxXjIdrsPVR5fFsWCv24DqZGjXEwW8,13790
accelerate/test_utils/scripts/external_deps/test_metrics.py,sha256=UIvyYY6uQq6GK_QZTkOiIYF31qFITzARjNct79rsc50,12164
accelerate/test_utils/scripts/external_deps/test_peak_memory_usage.py,sha256=ggwLWntGfA0in1Qo1OdlvYV7LA1l4Sjn3GX9ULWGddk,11536
accelerate/test_utils/scripts/external_deps/test_performance.py,sha256=ecKksyw4LwgAFQ9eLGyOoEHA2OisQNGnaXxtbBokXJk,9834
accelerate/test_utils/scripts/external_deps/test_pippy.py,sha256=-nvZsNOe8UEjmAF-hTS1dQBfbPcNpcuvIH6sPNYJ4to,4670
accelerate/test_utils/scripts/external_deps/test_zero3_integration.py,sha256=lXWL9hUE1N7TNDQP5UTSALZVTHvdHs-Blimp18nuUac,1575
accelerate/test_utils/scripts/test_cli.py,sha256=qfk1aYFtdvYFCYPkl05602SNGvk08QTv0xZVVcFVtzM,833
accelerate/test_utils/scripts/test_ddp_comm_hook.py,sha256=3tq_XA0t64GzzqQ818kOy7q86VlR0ibVhd9FsVOQxTk,3153
accelerate/test_utils/scripts/test_distributed_data_loop.py,sha256=4jrjyq9URiKrxN119FNssfbPV6iHpLKwnHMTbuolnDU,15090
accelerate/test_utils/scripts/test_merge_weights.py,sha256=DsbcNX_yxKdP9--YexlVjMyT36_7CA_hwieBd5ZbDGs,6054
accelerate/test_utils/scripts/test_notebook.py,sha256=qfIy3IvH74-kGn8nadBn_k7qrviqvsxy5ijsnUhuY6o,3894
accelerate/test_utils/scripts/test_ops.py,sha256=1kQxHkLu16lT17Xj7C666BUG-G1u8rdI59c3taFK2tM,6204
accelerate/test_utils/scripts/test_script.py,sha256=hnlvCx7R1m57Z8IA5U3NvcHgi92sJdjjZyG6oYLUMCA,33205
accelerate/test_utils/scripts/test_sync.py,sha256=GrYmYWxR06O7_aG_QAsEzuKvAQX_sXsg_-RhfppYy4g,18602
accelerate/test_utils/testing.py,sha256=vk4MZT_CGwxhPcmS50Glu_IQFyoW8V1IwQS0aaBT9JM,23456
accelerate/test_utils/training.py,sha256=8k_YAQ21MzUdb2aFWq1t2fihW1b-iBGh1OJSL3whY68,4019
accelerate/tracking.py,sha256=WLY-H1DTsxrz4BVzle7QZMp0Irg84yFMbA1e6JaY3pM,39789
accelerate/utils/__init__.py,sha256=G32-O6DFydwiPLVeyjOtHje2ezNN16MJiBuUzyjIB0g,7049
accelerate/utils/__pycache__/__init__.cpython-38.pyc,,
accelerate/utils/__pycache__/bnb.cpython-38.pyc,,
accelerate/utils/__pycache__/constants.cpython-38.pyc,,
accelerate/utils/__pycache__/dataclasses.cpython-38.pyc,,
accelerate/utils/__pycache__/deepspeed.cpython-38.pyc,,
accelerate/utils/__pycache__/environment.cpython-38.pyc,,
accelerate/utils/__pycache__/fsdp_utils.cpython-38.pyc,,
accelerate/utils/__pycache__/imports.cpython-38.pyc,,
accelerate/utils/__pycache__/launch.cpython-38.pyc,,
accelerate/utils/__pycache__/megatron_lm.cpython-38.pyc,,
accelerate/utils/__pycache__/memory.cpython-38.pyc,,
accelerate/utils/__pycache__/modeling.cpython-38.pyc,,
accelerate/utils/__pycache__/offload.cpython-38.pyc,,
accelerate/utils/__pycache__/operations.cpython-38.pyc,,
accelerate/utils/__pycache__/other.cpython-38.pyc,,
accelerate/utils/__pycache__/random.cpython-38.pyc,,
accelerate/utils/__pycache__/rich.cpython-38.pyc,,
accelerate/utils/__pycache__/torch_xla.cpython-38.pyc,,
accelerate/utils/__pycache__/tqdm.cpython-38.pyc,,
accelerate/utils/__pycache__/transformer_engine.cpython-38.pyc,,
accelerate/utils/__pycache__/versions.cpython-38.pyc,,
accelerate/utils/bnb.py,sha256=3i59dy8EcBYJEnT2alJ5_M-zeIpFsrceQ4bImiJJKOk,20570
accelerate/utils/constants.py,sha256=wTMK0MHmNTEquQEP-KR7daUPd6WQlNBHk3dSv2cj1KA,3032
accelerate/utils/dataclasses.py,sha256=x1cEqcZn8StQIFN2iGJM_ffuS17b7GamGwEdj_usNcw,119870
accelerate/utils/deepspeed.py,sha256=NtQKj5xwCvh5o2KSX0Jvw4yAGrl0coe_pUrWR88RgvU,11144
accelerate/utils/environment.py,sha256=LFLbhFZgB8XSgBBe9_uBdlbSPKl8K8IJYubGiwoyh7c,10407
accelerate/utils/fsdp_utils.py,sha256=aWLxnSlCT7qVIqWC6NNeMBuSpccLysyQYUydVnO8uKg,16398
accelerate/utils/imports.py,sha256=c2JlcaVJrLGWiLcV8hCUpR1LZKSvHPaHfzxVSqQzVvk,13843
accelerate/utils/launch.py,sha256=U-SrduXgI366tk9BZS6ywpmfqFEyQ1VlVf86QFRrPuc,29533
accelerate/utils/megatron_lm.py,sha256=knkCWt2bRe5OHDMF13b7o23RM1C9DOMWjSkn476dIRM,57920
accelerate/utils/memory.py,sha256=2pUGKga-QQu7o7KhHM_NPupnJgk1d6AQJEG41Plk6OI,5834
accelerate/utils/modeling.py,sha256=mIMl770-JMf-gon6R9VgcuFO7yY4qVXNj-t3KX931pc,83079
accelerate/utils/offload.py,sha256=qjaVai81wbkA0YH2WkmOXvZT0BRphygfRV_4Ua4j4U4,7837
accelerate/utils/operations.py,sha256=LVYAqa7yXWZ7EZCY9Z-kDE56ftCdjQMGnbq2EaUVQkU,31224
accelerate/utils/other.py,sha256=kgON65EhzQN3oQZqzgAOmmNC2vsQkeO77qEuzN7Zv7c,12283
accelerate/utils/random.py,sha256=ssRk26FiM0f2yMiBIwpDkdH5STCsD_WelZDoEGObDis,5373
accelerate/utils/rich.py,sha256=8JZX_uGMQX-BufdXxJpdne7BWd1KyLHSgbiGxrDMYr8,847
accelerate/utils/torch_xla.py,sha256=Pq1tuqN0X_pWDVza6YgjfO45uoJdoRVRForLeLQzFus,1908
accelerate/utils/tqdm.py,sha256=k8e9JnieTEQHCCNBaiBys7hPxWlEbyRASdIma-qy_X8,1657
accelerate/utils/transformer_engine.py,sha256=b7x4Y9DKcgNNVAJzPiryxWlhvRExZfIW2Y0qEErGzms,5883
accelerate/utils/versions.py,sha256=UgmcbjBm--6CIx1ZamSAMjAK_B_2l48LbeaNygqej8M,2149
