# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.__internal__.nest namespace.
"""

from __future__ import print_function as _print_function

import sys as _sys

from tensorflow.python.util.nest import _sequence_like as sequence_like
from tensorflow.python.util.nest import flatten_up_to
from tensorflow.python.util.nest import get_traverse_shallow_structure
from tensorflow.python.util.nest import is_attrs
from tensorflow.python.util.nest import is_mapping
from tensorflow.python.util.nest import list_to_tuple
from tensorflow.python.util.nest import map_structure_up_to
from tensorflow.python.util.nest import yield_flat_paths

del _print_function
