import gzip
import json
import traceback
import requests
from data_frame.global_settings import DOWNLOAD_AIWEIHE_API
from utils.sql_helper import MysqlHelper


def download_log(log_id, environment):
    """
    下载卫和AI服务请求日志
    :param log_id: 请求日志表主键ID
    :param environment: 环境标记
    :return:
    """
    download_url = DOWNLOAD_AIWEIHE_API[environment]['download_url']

    if environment == 'test':
        connect_name = 'ecg_analysis_test'
    else:
        connect_name = 'ecg_analysis_test'

    sql = f'SELECT * FROM `t_analysis_log` WHERE id = {log_id} '

    datas = MysqlHelper(connect_name).datas_query(sql)

    analysis_info_path = datas[0][2]

    response = requests.get(download_url + analysis_info_path)

    try:
        result = json.loads(gzip.decompress(response.content).decode())

        return result
    except Exception as e:
        print(traceback.format_exc())
        return None