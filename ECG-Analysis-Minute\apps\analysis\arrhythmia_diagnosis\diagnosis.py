import traceback
import numpy as np
from biosppy.signals import ecg
from scipy.stats import iqr
from apps.analysis.arrhythmia_diagnosis import sna, af, snt, snb, pvc
from apps.analysis.common import ecg_llm_processing
from apps.analysis.common.multiple_model import conclusion_diagnostic
from apps.analysis.diagnosis_filter.filter import apply_rules
from apps.models.analysis_models import ArrhythmiaDiagnosisEntity
from apps.utils.logger_helper import Logger
from global_settings import mutually_exclusive_conditions


def method_diagnosis(ecg_data, sampling_rate, waveform_info, precomputed_signal_features=None):
    """
    方法诊断
    :param ecg_data: 原始 ECG 信号数据
    :param sampling_rate: 采样率
    :param waveform_info: *原始信号*计算出的波形信息 (来自 get_waveform)
    :param precomputed_signal_features: 预计算的信号特征 (可选)
    :return:
    """
    rr_cv_original = waveform_info['waveform']['rr_cv']
    hr = waveform_info['heart_rate']['hr']

    diag_dict = {
        'SNA': False,
        'SNT': False,
        'SNB': False,
        'AF': False,
        'SN': False,
        'PVC': False
    }

    try:
        # 定义诊断模块及其对应的键
        diagnosis_modules = {
            'AF': af.detect_atrial_fibrillation,
            'SNT': snt.process,
            'SNB': snb.process,
            'PVC': pvc.process,
            'SNA': sna.process
        }

        # 遍历模块并更新诊断结果
        for key, module in diagnosis_modules.items():
            if key == 'AF':
                diag_dict[key] = module(waveform_info, precomputed_signal_features=precomputed_signal_features)
            elif key == 'PVC':
                if waveform_info.get('waveform'):
                    if isinstance(ecg_data, dict):
                        signals_input = ecg_data
                    else:
                        signals_input = {'lead2': ecg_data}

                    diag_dict[key] = module(signals_input, sampling_rate, waveform_info)
                else:
                    diag_dict[key] = False
            else:
                diag_dict[key] = module(waveform_info)

        if not any(diag_dict[key] for key in diagnosis_modules):
            sn_condition = rr_cv_original < 0.65 and (60 <= hr < 100)
            diag_dict['SN'] = sn_condition
    except Exception as e:
        Logger().error(f'方法诊断异常：{traceback.format_exc()}')  # 保留 Error
    finally:
        return diag_dict


def llm_diagnosis(waveform_info, sampling_rate):
    """
    大模型诊断
    :param waveform_info: 波形信息
    :param sampling_rate: 采样率
    :return:
    """
    try:
        waveform = waveform_info['waveform']
        hr = waveform_info['heart_rate']['hr']
        rr_intervals = waveform['rr_intervals']
        rr_cv = waveform['rr_cv']
        rr_std = waveform['rr_std']
        rr_iqr = waveform['rr_iqr']
        rr_average = waveform['rr_average']
        qrs_average = waveform['qrs_average']

        first_diag = ['正常心电图', '窦速', '窦缓', '窦性心律不齐', '房颤', '室性早搏', '噪音']
        diag_zh = {'SNA': '窦性心律不齐', 'SNT': '窦速', 'SNB': '窦缓', 'AF': '房颤', 'SN': '正常心电图',
                   'PVC': '室性早搏'}
        diag_llm_dict = {'SNA': False, 'SNT': False, 'SNB': False, 'AF': False, 'SN': False, 'PVC': False}

        diag_base_list = []

        prompt_intext = '当前心电图数据特征为心率为{}次/min,RR间期数量{}个,RR间期的平均时长为{}ms,RR间期的标准差为{},RR间期变异系数为{},RR间期的IQR为{}，qrs间期时长均值为{}秒。'.format(
            hr, len(rr_intervals), rr_average, rr_std, rr_cv, rr_iqr, qrs_average)
        llm_list = ecg_llm_processing.llm_api(prompt_intext, sampling_rate)

        if isinstance(llm_list, list):
            llm_pred = list(set(first_diag) & set(llm_list))
            if "正常心电图" in llm_pred:
                diag_llm_dict['SN'] = True
                return diag_llm_dict
            else:
                if '房颤' not in diag_base_list and '房颤' in llm_list:
                    diag_base_list.append('房颤')
                if '室性早搏' not in diag_base_list and '室性早搏' in llm_list:
                    diag_base_list.append('室性早搏')

                for key, value in diag_zh.items():
                    if value in diag_base_list:
                        diag_llm_dict[key] = True
                return diag_llm_dict

        return None
    except Exception:
        Logger().error(f'大模型诊断异常：{traceback.format_exc()}')
        return None


def apply_mutually_exclusive_rules(predicted_labels):
    """
    根据互斥规则过滤预测标签
    :param predicted_labels: 模型预测的疾病标签列表
    :return: 经过互斥规则过滤后的疾病标签列表
    """
    final_labels = []

    for label in predicted_labels:
        if label in final_labels:
            continue

        final_labels.append(label)

        if label in mutually_exclusive_conditions:
            mutually_exclusive = mutually_exclusive_conditions[label]
            predicted_labels = [lbl for lbl in predicted_labels if lbl not in mutually_exclusive]

    return final_labels
