/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// LLVMLegalizeForExport
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LLVMLegalizeForExportBase : public ::mlir::OperationPass<> {
public:
  using Base = LLVMLegalizeForExportBase;

  LLVMLegalizeForExportBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LLVMLegalizeForExportBase(const LLVMLegalizeForExportBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("llvm-legalize-for-export");
  }
  ::llvm::StringRef getArgument() const override { return "llvm-legalize-for-export"; }

  ::llvm::StringRef getDescription() const override { return "Legalize LLVM dialect to be convertible to LLVM IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LLVMLegalizeForExport");
  }
  ::llvm::StringRef getName() const override { return "LLVMLegalizeForExport"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// LLVMLegalizeForExport Registration
//===----------------------------------------------------------------------===//

inline void registerLLVMLegalizeForExportPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::LLVM::createLegalizeForExportPass();
  });
}

//===----------------------------------------------------------------------===//
// LLVM Registration
//===----------------------------------------------------------------------===//

inline void registerLLVMPasses() {
  registerLLVMLegalizeForExportPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
