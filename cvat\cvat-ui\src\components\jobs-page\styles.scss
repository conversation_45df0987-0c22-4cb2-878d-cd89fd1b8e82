// Copyright (C) 2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-jobs-page {
    padding-top: $grid-unit-size * 2;
    padding-bottom: $grid-unit-size;
    height: 100%;
    width: 100%;
    overflow: auto;

    .cvat-jobs-page-top-bar {
        margin-bottom: $grid-unit-size;

        > div {
            display: flex;
            justify-content: space-between;

            > div {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;

                .cvat-jobs-page-search-bar {
                    width: $grid-unit-size * 32;
                }

                > div {
                    > *:not(:last-child) {
                        margin-right: $grid-unit-size;
                    }

                    display: flex;
                }
            }
        }
    }

    .cvat-empty-jobs-list {
        .ant-empty {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .cvat-job-page-list-item {
        display: flex;
        flex-direction: column;

        .ant-card-cover {
            flex: 1;
            height: 0;
            margin: 0;
        }

        .ant-card-body {
            padding: $grid-unit-size;

            .ant-descriptions-item {
                padding: 0;
            }
        }

        &:hover {
            .cvat-job-page-list-item-id {
                opacity: 1;
            }

            .cvat-job-page-list-item-dimension {
                opacity: 1;
            }

            .cvat-job-page-list-item-type {
                opacity: 1;
            }
        }

        :nth-child(4n) {
            border-right: 0;
        }

        .cvat-jobs-page-job-item-card-preview-wrapper {
            height: 100%;
            width: 100%;

            > .cvat-jobs-page-job-item-card-preview {
                .ant-empty-image {
                    height: $grid-unit-size * 10;
                }

                height: 100%;
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-around;
                object-fit: cover;
                cursor: pointer;
            }
        }

        .cvat-job-item-loading-preview,
        .cvat-job-item-empty-preview {
            @extend .cvat-base-preview;

            &:hover {
                cursor: pointer;
            }
        }

        .cvat-job-page-list-item-dimension {
            position: absolute;
            top: 0;
            right: 0;
            margin: $grid-unit-size;
            width: $grid-unit-size * 4;
            background: white;
            border-radius: $border-radius-base;
            text-align: center;
            opacity: 0.5;
            padding: $grid-unit-size;
        }

        .cvat-job-page-list-item-id {
            position: absolute;
            top: 0;
            left: 0;
            margin: $grid-unit-size $grid-unit-size $grid-unit-size 0;
            width: fit-content;
            background: white;
            border-radius: 0 $border-radius-base $border-radius-base 0;
            padding: $grid-unit-size;
            opacity: 0.5;
            transition: 0.15s all ease;
            box-shadow: $box-shadow-base;
        }

        .cvat-job-page-list-item-type {
            position: absolute;
            top: $grid-unit-size * 5;
            left: 0;
            margin: $grid-unit-size $grid-unit-size $grid-unit-size 0;
            width: fit-content;
            background: white;
            border-radius: 0 $border-radius-base $border-radius-base 0;
            padding: $grid-unit-size;
            opacity: 0.5;
            transition: 0.15s all ease;
            box-shadow: $box-shadow-base;
        }
    }

    .cvat-jobs-page-pagination {
        display: flex;
        margin-top: $grid-unit-size;
        justify-content: center;
    }

    .cvat-jobs-page-list {
        display: flex;
        flex-wrap: wrap;

        > div {
            width: 100%;
            margin-bottom: $grid-unit-size;

            > div:not(:first-child) {
                padding-left: $grid-unit-size;
            }
        }
    }

    .cvat-job-card-more-button {
        position: absolute;
        bottom: $grid-unit-size * 2;
        right: $grid-unit-size;
        font-size: 16px;
    }
}
