// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/test_log.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2ftest_5flog_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2ftest_5flog_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/wrappers.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2futil_2ftest_5flog_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[17]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
namespace tensorflow {
class AvailableDeviceInfo;
class AvailableDeviceInfoDefaultTypeInternal;
extern AvailableDeviceInfoDefaultTypeInternal _AvailableDeviceInfo_default_instance_;
class BenchmarkEntries;
class BenchmarkEntriesDefaultTypeInternal;
extern BenchmarkEntriesDefaultTypeInternal _BenchmarkEntries_default_instance_;
class BenchmarkEntry;
class BenchmarkEntryDefaultTypeInternal;
extern BenchmarkEntryDefaultTypeInternal _BenchmarkEntry_default_instance_;
class BenchmarkEntry_ExtrasEntry_DoNotUse;
class BenchmarkEntry_ExtrasEntry_DoNotUseDefaultTypeInternal;
extern BenchmarkEntry_ExtrasEntry_DoNotUseDefaultTypeInternal _BenchmarkEntry_ExtrasEntry_DoNotUse_default_instance_;
class BuildConfiguration;
class BuildConfigurationDefaultTypeInternal;
extern BuildConfigurationDefaultTypeInternal _BuildConfiguration_default_instance_;
class CPUInfo;
class CPUInfoDefaultTypeInternal;
extern CPUInfoDefaultTypeInternal _CPUInfo_default_instance_;
class CPUInfo_CacheSizeEntry_DoNotUse;
class CPUInfo_CacheSizeEntry_DoNotUseDefaultTypeInternal;
extern CPUInfo_CacheSizeEntry_DoNotUseDefaultTypeInternal _CPUInfo_CacheSizeEntry_DoNotUse_default_instance_;
class CommitId;
class CommitIdDefaultTypeInternal;
extern CommitIdDefaultTypeInternal _CommitId_default_instance_;
class EntryValue;
class EntryValueDefaultTypeInternal;
extern EntryValueDefaultTypeInternal _EntryValue_default_instance_;
class GPUInfo;
class GPUInfoDefaultTypeInternal;
extern GPUInfoDefaultTypeInternal _GPUInfo_default_instance_;
class MachineConfiguration;
class MachineConfigurationDefaultTypeInternal;
extern MachineConfigurationDefaultTypeInternal _MachineConfiguration_default_instance_;
class MemoryInfo;
class MemoryInfoDefaultTypeInternal;
extern MemoryInfoDefaultTypeInternal _MemoryInfo_default_instance_;
class MetricEntry;
class MetricEntryDefaultTypeInternal;
extern MetricEntryDefaultTypeInternal _MetricEntry_default_instance_;
class PlatformInfo;
class PlatformInfoDefaultTypeInternal;
extern PlatformInfoDefaultTypeInternal _PlatformInfo_default_instance_;
class RunConfiguration;
class RunConfigurationDefaultTypeInternal;
extern RunConfigurationDefaultTypeInternal _RunConfiguration_default_instance_;
class RunConfiguration_EnvVarsEntry_DoNotUse;
class RunConfiguration_EnvVarsEntry_DoNotUseDefaultTypeInternal;
extern RunConfiguration_EnvVarsEntry_DoNotUseDefaultTypeInternal _RunConfiguration_EnvVarsEntry_DoNotUse_default_instance_;
class TestResults;
class TestResultsDefaultTypeInternal;
extern TestResultsDefaultTypeInternal _TestResults_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::AvailableDeviceInfo* Arena::CreateMaybeMessage<::tensorflow::AvailableDeviceInfo>(Arena*);
template<> ::tensorflow::BenchmarkEntries* Arena::CreateMaybeMessage<::tensorflow::BenchmarkEntries>(Arena*);
template<> ::tensorflow::BenchmarkEntry* Arena::CreateMaybeMessage<::tensorflow::BenchmarkEntry>(Arena*);
template<> ::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse>(Arena*);
template<> ::tensorflow::BuildConfiguration* Arena::CreateMaybeMessage<::tensorflow::BuildConfiguration>(Arena*);
template<> ::tensorflow::CPUInfo* Arena::CreateMaybeMessage<::tensorflow::CPUInfo>(Arena*);
template<> ::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse>(Arena*);
template<> ::tensorflow::CommitId* Arena::CreateMaybeMessage<::tensorflow::CommitId>(Arena*);
template<> ::tensorflow::EntryValue* Arena::CreateMaybeMessage<::tensorflow::EntryValue>(Arena*);
template<> ::tensorflow::GPUInfo* Arena::CreateMaybeMessage<::tensorflow::GPUInfo>(Arena*);
template<> ::tensorflow::MachineConfiguration* Arena::CreateMaybeMessage<::tensorflow::MachineConfiguration>(Arena*);
template<> ::tensorflow::MemoryInfo* Arena::CreateMaybeMessage<::tensorflow::MemoryInfo>(Arena*);
template<> ::tensorflow::MetricEntry* Arena::CreateMaybeMessage<::tensorflow::MetricEntry>(Arena*);
template<> ::tensorflow::PlatformInfo* Arena::CreateMaybeMessage<::tensorflow::PlatformInfo>(Arena*);
template<> ::tensorflow::RunConfiguration* Arena::CreateMaybeMessage<::tensorflow::RunConfiguration>(Arena*);
template<> ::tensorflow::RunConfiguration_EnvVarsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::RunConfiguration_EnvVarsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::TestResults* Arena::CreateMaybeMessage<::tensorflow::TestResults>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum TestResults_BenchmarkType : int {
  TestResults_BenchmarkType_UNKNOWN = 0,
  TestResults_BenchmarkType_CPP_MICROBENCHMARK = 1,
  TestResults_BenchmarkType_PYTHON_BENCHMARK = 2,
  TestResults_BenchmarkType_ANDROID_BENCHMARK = 3,
  TestResults_BenchmarkType_EDGE_BENCHMARK = 4,
  TestResults_BenchmarkType_IOS_BENCHMARK = 5,
  TestResults_BenchmarkType_TestResults_BenchmarkType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  TestResults_BenchmarkType_TestResults_BenchmarkType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool TestResults_BenchmarkType_IsValid(int value);
constexpr TestResults_BenchmarkType TestResults_BenchmarkType_BenchmarkType_MIN = TestResults_BenchmarkType_UNKNOWN;
constexpr TestResults_BenchmarkType TestResults_BenchmarkType_BenchmarkType_MAX = TestResults_BenchmarkType_IOS_BENCHMARK;
constexpr int TestResults_BenchmarkType_BenchmarkType_ARRAYSIZE = TestResults_BenchmarkType_BenchmarkType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TestResults_BenchmarkType_descriptor();
template<typename T>
inline const std::string& TestResults_BenchmarkType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TestResults_BenchmarkType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TestResults_BenchmarkType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TestResults_BenchmarkType_descriptor(), enum_t_value);
}
inline bool TestResults_BenchmarkType_Parse(
    const std::string& name, TestResults_BenchmarkType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TestResults_BenchmarkType>(
    TestResults_BenchmarkType_descriptor(), name, value);
}
// ===================================================================

class EntryValue :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.EntryValue) */ {
 public:
  EntryValue();
  virtual ~EntryValue();

  EntryValue(const EntryValue& from);
  EntryValue(EntryValue&& from) noexcept
    : EntryValue() {
    *this = ::std::move(from);
  }

  inline EntryValue& operator=(const EntryValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline EntryValue& operator=(EntryValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const EntryValue& default_instance();

  enum KindCase {
    kDoubleValue = 1,
    kStringValue = 2,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EntryValue* internal_default_instance() {
    return reinterpret_cast<const EntryValue*>(
               &_EntryValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(EntryValue& a, EntryValue& b) {
    a.Swap(&b);
  }
  inline void Swap(EntryValue* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EntryValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EntryValue* New() const final {
    return CreateMaybeMessage<EntryValue>(nullptr);
  }

  EntryValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EntryValue>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const EntryValue& from);
  void MergeFrom(const EntryValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EntryValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.EntryValue";
  }
  protected:
  explicit EntryValue(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDoubleValueFieldNumber = 1,
    kStringValueFieldNumber = 2,
  };
  // double double_value = 1;
  private:
  bool has_double_value() const;
  public:
  void clear_double_value();
  double double_value() const;
  void set_double_value(double value);

  // string string_value = 2;
  private:
  bool has_string_value() const;
  public:
  void clear_string_value();
  const std::string& string_value() const;
  void set_string_value(const std::string& value);
  void set_string_value(std::string&& value);
  void set_string_value(const char* value);
  void set_string_value(const char* value, size_t size);
  std::string* mutable_string_value();
  std::string* release_string_value();
  void set_allocated_string_value(std::string* string_value);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_string_value();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_string_value(
      std::string* string_value);

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.EntryValue)
 private:
  class _Internal;
  void set_has_double_value();
  void set_has_string_value();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union KindUnion {
    KindUnion() {}
    double double_value_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_value_;
  } kind_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class MetricEntry :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MetricEntry) */ {
 public:
  MetricEntry();
  virtual ~MetricEntry();

  MetricEntry(const MetricEntry& from);
  MetricEntry(MetricEntry&& from) noexcept
    : MetricEntry() {
    *this = ::std::move(from);
  }

  inline MetricEntry& operator=(const MetricEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline MetricEntry& operator=(MetricEntry&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MetricEntry& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MetricEntry* internal_default_instance() {
    return reinterpret_cast<const MetricEntry*>(
               &_MetricEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MetricEntry& a, MetricEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(MetricEntry* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MetricEntry* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MetricEntry* New() const final {
    return CreateMaybeMessage<MetricEntry>(nullptr);
  }

  MetricEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MetricEntry>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MetricEntry& from);
  void MergeFrom(const MetricEntry& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MetricEntry* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MetricEntry";
  }
  protected:
  explicit MetricEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kMinValueFieldNumber = 3,
    kMaxValueFieldNumber = 4,
    kValueFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .google.protobuf.DoubleValue min_value = 3;
  bool has_min_value() const;
  void clear_min_value();
  const PROTOBUF_NAMESPACE_ID::DoubleValue& min_value() const;
  PROTOBUF_NAMESPACE_ID::DoubleValue* release_min_value();
  PROTOBUF_NAMESPACE_ID::DoubleValue* mutable_min_value();
  void set_allocated_min_value(PROTOBUF_NAMESPACE_ID::DoubleValue* min_value);
  void unsafe_arena_set_allocated_min_value(
      PROTOBUF_NAMESPACE_ID::DoubleValue* min_value);
  PROTOBUF_NAMESPACE_ID::DoubleValue* unsafe_arena_release_min_value();

  // .google.protobuf.DoubleValue max_value = 4;
  bool has_max_value() const;
  void clear_max_value();
  const PROTOBUF_NAMESPACE_ID::DoubleValue& max_value() const;
  PROTOBUF_NAMESPACE_ID::DoubleValue* release_max_value();
  PROTOBUF_NAMESPACE_ID::DoubleValue* mutable_max_value();
  void set_allocated_max_value(PROTOBUF_NAMESPACE_ID::DoubleValue* max_value);
  void unsafe_arena_set_allocated_max_value(
      PROTOBUF_NAMESPACE_ID::DoubleValue* max_value);
  PROTOBUF_NAMESPACE_ID::DoubleValue* unsafe_arena_release_max_value();

  // double value = 2;
  void clear_value();
  double value() const;
  void set_value(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.MetricEntry)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  PROTOBUF_NAMESPACE_ID::DoubleValue* min_value_;
  PROTOBUF_NAMESPACE_ID::DoubleValue* max_value_;
  double value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class BenchmarkEntry_ExtrasEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<BenchmarkEntry_ExtrasEntry_DoNotUse, 
    std::string, ::tensorflow::EntryValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<BenchmarkEntry_ExtrasEntry_DoNotUse, 
    std::string, ::tensorflow::EntryValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  BenchmarkEntry_ExtrasEntry_DoNotUse();
  BenchmarkEntry_ExtrasEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const BenchmarkEntry_ExtrasEntry_DoNotUse& other);
  static const BenchmarkEntry_ExtrasEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const BenchmarkEntry_ExtrasEntry_DoNotUse*>(&_BenchmarkEntry_ExtrasEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.BenchmarkEntry.ExtrasEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[2];
  }

  public:
};

// -------------------------------------------------------------------

class BenchmarkEntry :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BenchmarkEntry) */ {
 public:
  BenchmarkEntry();
  virtual ~BenchmarkEntry();

  BenchmarkEntry(const BenchmarkEntry& from);
  BenchmarkEntry(BenchmarkEntry&& from) noexcept
    : BenchmarkEntry() {
    *this = ::std::move(from);
  }

  inline BenchmarkEntry& operator=(const BenchmarkEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline BenchmarkEntry& operator=(BenchmarkEntry&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BenchmarkEntry& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BenchmarkEntry* internal_default_instance() {
    return reinterpret_cast<const BenchmarkEntry*>(
               &_BenchmarkEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(BenchmarkEntry& a, BenchmarkEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(BenchmarkEntry* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BenchmarkEntry* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BenchmarkEntry* New() const final {
    return CreateMaybeMessage<BenchmarkEntry>(nullptr);
  }

  BenchmarkEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BenchmarkEntry>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BenchmarkEntry& from);
  void MergeFrom(const BenchmarkEntry& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BenchmarkEntry* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BenchmarkEntry";
  }
  protected:
  explicit BenchmarkEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kExtrasFieldNumber = 6,
    kMetricsFieldNumber = 7,
    kNameFieldNumber = 1,
    kItersFieldNumber = 2,
    kCpuTimeFieldNumber = 3,
    kWallTimeFieldNumber = 4,
    kThroughputFieldNumber = 5,
  };
  // map<string, .tensorflow.EntryValue> extras = 6;
  int extras_size() const;
  void clear_extras();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >&
      extras() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >*
      mutable_extras();

  // repeated .tensorflow.MetricEntry metrics = 7;
  int metrics_size() const;
  void clear_metrics();
  ::tensorflow::MetricEntry* mutable_metrics(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MetricEntry >*
      mutable_metrics();
  const ::tensorflow::MetricEntry& metrics(int index) const;
  ::tensorflow::MetricEntry* add_metrics();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MetricEntry >&
      metrics() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // int64 iters = 2;
  void clear_iters();
  ::PROTOBUF_NAMESPACE_ID::int64 iters() const;
  void set_iters(::PROTOBUF_NAMESPACE_ID::int64 value);

  // double cpu_time = 3;
  void clear_cpu_time();
  double cpu_time() const;
  void set_cpu_time(double value);

  // double wall_time = 4;
  void clear_wall_time();
  double wall_time() const;
  void set_wall_time(double value);

  // double throughput = 5;
  void clear_throughput();
  double throughput() const;
  void set_throughput(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.BenchmarkEntry)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      BenchmarkEntry_ExtrasEntry_DoNotUse,
      std::string, ::tensorflow::EntryValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > extras_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MetricEntry > metrics_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::int64 iters_;
  double cpu_time_;
  double wall_time_;
  double throughput_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class BenchmarkEntries :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BenchmarkEntries) */ {
 public:
  BenchmarkEntries();
  virtual ~BenchmarkEntries();

  BenchmarkEntries(const BenchmarkEntries& from);
  BenchmarkEntries(BenchmarkEntries&& from) noexcept
    : BenchmarkEntries() {
    *this = ::std::move(from);
  }

  inline BenchmarkEntries& operator=(const BenchmarkEntries& from) {
    CopyFrom(from);
    return *this;
  }
  inline BenchmarkEntries& operator=(BenchmarkEntries&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BenchmarkEntries& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BenchmarkEntries* internal_default_instance() {
    return reinterpret_cast<const BenchmarkEntries*>(
               &_BenchmarkEntries_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(BenchmarkEntries& a, BenchmarkEntries& b) {
    a.Swap(&b);
  }
  inline void Swap(BenchmarkEntries* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BenchmarkEntries* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BenchmarkEntries* New() const final {
    return CreateMaybeMessage<BenchmarkEntries>(nullptr);
  }

  BenchmarkEntries* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BenchmarkEntries>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BenchmarkEntries& from);
  void MergeFrom(const BenchmarkEntries& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BenchmarkEntries* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BenchmarkEntries";
  }
  protected:
  explicit BenchmarkEntries(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEntryFieldNumber = 1,
  };
  // repeated .tensorflow.BenchmarkEntry entry = 1;
  int entry_size() const;
  void clear_entry();
  ::tensorflow::BenchmarkEntry* mutable_entry(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BenchmarkEntry >*
      mutable_entry();
  const ::tensorflow::BenchmarkEntry& entry(int index) const;
  ::tensorflow::BenchmarkEntry* add_entry();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BenchmarkEntry >&
      entry() const;

  // @@protoc_insertion_point(class_scope:tensorflow.BenchmarkEntries)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BenchmarkEntry > entry_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class BuildConfiguration :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BuildConfiguration) */ {
 public:
  BuildConfiguration();
  virtual ~BuildConfiguration();

  BuildConfiguration(const BuildConfiguration& from);
  BuildConfiguration(BuildConfiguration&& from) noexcept
    : BuildConfiguration() {
    *this = ::std::move(from);
  }

  inline BuildConfiguration& operator=(const BuildConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline BuildConfiguration& operator=(BuildConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BuildConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BuildConfiguration* internal_default_instance() {
    return reinterpret_cast<const BuildConfiguration*>(
               &_BuildConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(BuildConfiguration& a, BuildConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(BuildConfiguration* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BuildConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BuildConfiguration* New() const final {
    return CreateMaybeMessage<BuildConfiguration>(nullptr);
  }

  BuildConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BuildConfiguration>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BuildConfiguration& from);
  void MergeFrom(const BuildConfiguration& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BuildConfiguration* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BuildConfiguration";
  }
  protected:
  explicit BuildConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCcFlagsFieldNumber = 2,
    kOptsFieldNumber = 3,
    kModeFieldNumber = 1,
  };
  // repeated string cc_flags = 2;
  int cc_flags_size() const;
  void clear_cc_flags();
  const std::string& cc_flags(int index) const;
  std::string* mutable_cc_flags(int index);
  void set_cc_flags(int index, const std::string& value);
  void set_cc_flags(int index, std::string&& value);
  void set_cc_flags(int index, const char* value);
  void set_cc_flags(int index, const char* value, size_t size);
  std::string* add_cc_flags();
  void add_cc_flags(const std::string& value);
  void add_cc_flags(std::string&& value);
  void add_cc_flags(const char* value);
  void add_cc_flags(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& cc_flags() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_cc_flags();

  // repeated string opts = 3;
  int opts_size() const;
  void clear_opts();
  const std::string& opts(int index) const;
  std::string* mutable_opts(int index);
  void set_opts(int index, const std::string& value);
  void set_opts(int index, std::string&& value);
  void set_opts(int index, const char* value);
  void set_opts(int index, const char* value, size_t size);
  std::string* add_opts();
  void add_opts(const std::string& value);
  void add_opts(std::string&& value);
  void add_opts(const char* value);
  void add_opts(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& opts() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_opts();

  // string mode = 1;
  void clear_mode();
  const std::string& mode() const;
  void set_mode(const std::string& value);
  void set_mode(std::string&& value);
  void set_mode(const char* value);
  void set_mode(const char* value, size_t size);
  std::string* mutable_mode();
  std::string* release_mode();
  void set_allocated_mode(std::string* mode);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_mode();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_mode(
      std::string* mode);

  // @@protoc_insertion_point(class_scope:tensorflow.BuildConfiguration)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> cc_flags_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> opts_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr mode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class CommitId :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CommitId) */ {
 public:
  CommitId();
  virtual ~CommitId();

  CommitId(const CommitId& from);
  CommitId(CommitId&& from) noexcept
    : CommitId() {
    *this = ::std::move(from);
  }

  inline CommitId& operator=(const CommitId& from) {
    CopyFrom(from);
    return *this;
  }
  inline CommitId& operator=(CommitId&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CommitId& default_instance();

  enum KindCase {
    kChangelist = 1,
    kHash = 2,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CommitId* internal_default_instance() {
    return reinterpret_cast<const CommitId*>(
               &_CommitId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(CommitId& a, CommitId& b) {
    a.Swap(&b);
  }
  inline void Swap(CommitId* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CommitId* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CommitId* New() const final {
    return CreateMaybeMessage<CommitId>(nullptr);
  }

  CommitId* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CommitId>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CommitId& from);
  void MergeFrom(const CommitId& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CommitId* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CommitId";
  }
  protected:
  explicit CommitId(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSnapshotFieldNumber = 3,
    kPendingChangelistFieldNumber = 4,
    kChangelistFieldNumber = 1,
    kHashFieldNumber = 2,
  };
  // string snapshot = 3;
  void clear_snapshot();
  const std::string& snapshot() const;
  void set_snapshot(const std::string& value);
  void set_snapshot(std::string&& value);
  void set_snapshot(const char* value);
  void set_snapshot(const char* value, size_t size);
  std::string* mutable_snapshot();
  std::string* release_snapshot();
  void set_allocated_snapshot(std::string* snapshot);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_snapshot();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_snapshot(
      std::string* snapshot);

  // int64 pending_changelist = 4;
  void clear_pending_changelist();
  ::PROTOBUF_NAMESPACE_ID::int64 pending_changelist() const;
  void set_pending_changelist(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 changelist = 1;
  private:
  bool has_changelist() const;
  public:
  void clear_changelist();
  ::PROTOBUF_NAMESPACE_ID::int64 changelist() const;
  void set_changelist(::PROTOBUF_NAMESPACE_ID::int64 value);

  // string hash = 2;
  private:
  bool has_hash() const;
  public:
  void clear_hash();
  const std::string& hash() const;
  void set_hash(const std::string& value);
  void set_hash(std::string&& value);
  void set_hash(const char* value);
  void set_hash(const char* value, size_t size);
  std::string* mutable_hash();
  std::string* release_hash();
  void set_allocated_hash(std::string* hash);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_hash();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_hash(
      std::string* hash);

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.CommitId)
 private:
  class _Internal;
  void set_has_changelist();
  void set_has_hash();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr snapshot_;
  ::PROTOBUF_NAMESPACE_ID::int64 pending_changelist_;
  union KindUnion {
    KindUnion() {}
    ::PROTOBUF_NAMESPACE_ID::int64 changelist_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr hash_;
  } kind_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class CPUInfo_CacheSizeEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CPUInfo_CacheSizeEntry_DoNotUse, 
    std::string, ::PROTOBUF_NAMESPACE_ID::int64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CPUInfo_CacheSizeEntry_DoNotUse, 
    std::string, ::PROTOBUF_NAMESPACE_ID::int64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    0 > SuperType;
  CPUInfo_CacheSizeEntry_DoNotUse();
  CPUInfo_CacheSizeEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const CPUInfo_CacheSizeEntry_DoNotUse& other);
  static const CPUInfo_CacheSizeEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CPUInfo_CacheSizeEntry_DoNotUse*>(&_CPUInfo_CacheSizeEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CPUInfo.CacheSizeEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[7];
  }

  public:
};

// -------------------------------------------------------------------

class CPUInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CPUInfo) */ {
 public:
  CPUInfo();
  virtual ~CPUInfo();

  CPUInfo(const CPUInfo& from);
  CPUInfo(CPUInfo&& from) noexcept
    : CPUInfo() {
    *this = ::std::move(from);
  }

  inline CPUInfo& operator=(const CPUInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CPUInfo& operator=(CPUInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CPUInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CPUInfo* internal_default_instance() {
    return reinterpret_cast<const CPUInfo*>(
               &_CPUInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(CPUInfo& a, CPUInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CPUInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CPUInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CPUInfo* New() const final {
    return CreateMaybeMessage<CPUInfo>(nullptr);
  }

  CPUInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CPUInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CPUInfo& from);
  void MergeFrom(const CPUInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CPUInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CPUInfo";
  }
  protected:
  explicit CPUInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kCacheSizeFieldNumber = 6,
    kCpuInfoFieldNumber = 4,
    kCpuGovernorFieldNumber = 5,
    kNumCoresFieldNumber = 1,
    kNumCoresAllowedFieldNumber = 2,
    kMhzPerCpuFieldNumber = 3,
  };
  // map<string, int64> cache_size = 6;
  int cache_size_size() const;
  void clear_cache_size();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int64 >&
      cache_size() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_cache_size();

  // string cpu_info = 4;
  void clear_cpu_info();
  const std::string& cpu_info() const;
  void set_cpu_info(const std::string& value);
  void set_cpu_info(std::string&& value);
  void set_cpu_info(const char* value);
  void set_cpu_info(const char* value, size_t size);
  std::string* mutable_cpu_info();
  std::string* release_cpu_info();
  void set_allocated_cpu_info(std::string* cpu_info);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_cpu_info();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_cpu_info(
      std::string* cpu_info);

  // string cpu_governor = 5;
  void clear_cpu_governor();
  const std::string& cpu_governor() const;
  void set_cpu_governor(const std::string& value);
  void set_cpu_governor(std::string&& value);
  void set_cpu_governor(const char* value);
  void set_cpu_governor(const char* value, size_t size);
  std::string* mutable_cpu_governor();
  std::string* release_cpu_governor();
  void set_allocated_cpu_governor(std::string* cpu_governor);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_cpu_governor();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_cpu_governor(
      std::string* cpu_governor);

  // int64 num_cores = 1;
  void clear_num_cores();
  ::PROTOBUF_NAMESPACE_ID::int64 num_cores() const;
  void set_num_cores(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 num_cores_allowed = 2;
  void clear_num_cores_allowed();
  ::PROTOBUF_NAMESPACE_ID::int64 num_cores_allowed() const;
  void set_num_cores_allowed(::PROTOBUF_NAMESPACE_ID::int64 value);

  // double mhz_per_cpu = 3;
  void clear_mhz_per_cpu();
  double mhz_per_cpu() const;
  void set_mhz_per_cpu(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.CPUInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      CPUInfo_CacheSizeEntry_DoNotUse,
      std::string, ::PROTOBUF_NAMESPACE_ID::int64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      0 > cache_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cpu_info_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cpu_governor_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_cores_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_cores_allowed_;
  double mhz_per_cpu_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class MemoryInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryInfo) */ {
 public:
  MemoryInfo();
  virtual ~MemoryInfo();

  MemoryInfo(const MemoryInfo& from);
  MemoryInfo(MemoryInfo&& from) noexcept
    : MemoryInfo() {
    *this = ::std::move(from);
  }

  inline MemoryInfo& operator=(const MemoryInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryInfo& operator=(MemoryInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemoryInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryInfo* internal_default_instance() {
    return reinterpret_cast<const MemoryInfo*>(
               &_MemoryInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(MemoryInfo& a, MemoryInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemoryInfo* New() const final {
    return CreateMaybeMessage<MemoryInfo>(nullptr);
  }

  MemoryInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemoryInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemoryInfo& from);
  void MergeFrom(const MemoryInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryInfo";
  }
  protected:
  explicit MemoryInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTotalFieldNumber = 1,
    kAvailableFieldNumber = 2,
  };
  // int64 total = 1;
  void clear_total();
  ::PROTOBUF_NAMESPACE_ID::int64 total() const;
  void set_total(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 available = 2;
  void clear_available();
  ::PROTOBUF_NAMESPACE_ID::int64 available() const;
  void set_available(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_;
  ::PROTOBUF_NAMESPACE_ID::int64 available_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class GPUInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GPUInfo) */ {
 public:
  GPUInfo();
  virtual ~GPUInfo();

  GPUInfo(const GPUInfo& from);
  GPUInfo(GPUInfo&& from) noexcept
    : GPUInfo() {
    *this = ::std::move(from);
  }

  inline GPUInfo& operator=(const GPUInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GPUInfo& operator=(GPUInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GPUInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GPUInfo* internal_default_instance() {
    return reinterpret_cast<const GPUInfo*>(
               &_GPUInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GPUInfo& a, GPUInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GPUInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GPUInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GPUInfo* New() const final {
    return CreateMaybeMessage<GPUInfo>(nullptr);
  }

  GPUInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GPUInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GPUInfo& from);
  void MergeFrom(const GPUInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GPUInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GPUInfo";
  }
  protected:
  explicit GPUInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelFieldNumber = 1,
    kUuidFieldNumber = 2,
    kBusIdFieldNumber = 3,
  };
  // string model = 1;
  void clear_model();
  const std::string& model() const;
  void set_model(const std::string& value);
  void set_model(std::string&& value);
  void set_model(const char* value);
  void set_model(const char* value, size_t size);
  std::string* mutable_model();
  std::string* release_model();
  void set_allocated_model(std::string* model);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_model();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_model(
      std::string* model);

  // string uuid = 2;
  void clear_uuid();
  const std::string& uuid() const;
  void set_uuid(const std::string& value);
  void set_uuid(std::string&& value);
  void set_uuid(const char* value);
  void set_uuid(const char* value, size_t size);
  std::string* mutable_uuid();
  std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_uuid();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_uuid(
      std::string* uuid);

  // string bus_id = 3;
  void clear_bus_id();
  const std::string& bus_id() const;
  void set_bus_id(const std::string& value);
  void set_bus_id(std::string&& value);
  void set_bus_id(const char* value);
  void set_bus_id(const char* value, size_t size);
  std::string* mutable_bus_id();
  std::string* release_bus_id();
  void set_allocated_bus_id(std::string* bus_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_bus_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_bus_id(
      std::string* bus_id);

  // @@protoc_insertion_point(class_scope:tensorflow.GPUInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bus_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class PlatformInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.PlatformInfo) */ {
 public:
  PlatformInfo();
  virtual ~PlatformInfo();

  PlatformInfo(const PlatformInfo& from);
  PlatformInfo(PlatformInfo&& from) noexcept
    : PlatformInfo() {
    *this = ::std::move(from);
  }

  inline PlatformInfo& operator=(const PlatformInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlatformInfo& operator=(PlatformInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PlatformInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PlatformInfo* internal_default_instance() {
    return reinterpret_cast<const PlatformInfo*>(
               &_PlatformInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(PlatformInfo& a, PlatformInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(PlatformInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlatformInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PlatformInfo* New() const final {
    return CreateMaybeMessage<PlatformInfo>(nullptr);
  }

  PlatformInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PlatformInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PlatformInfo& from);
  void MergeFrom(const PlatformInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlatformInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.PlatformInfo";
  }
  protected:
  explicit PlatformInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBitsFieldNumber = 1,
    kLinkageFieldNumber = 2,
    kMachineFieldNumber = 3,
    kReleaseFieldNumber = 4,
    kSystemFieldNumber = 5,
    kVersionFieldNumber = 6,
  };
  // string bits = 1;
  void clear_bits();
  const std::string& bits() const;
  void set_bits(const std::string& value);
  void set_bits(std::string&& value);
  void set_bits(const char* value);
  void set_bits(const char* value, size_t size);
  std::string* mutable_bits();
  std::string* release_bits();
  void set_allocated_bits(std::string* bits);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_bits();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_bits(
      std::string* bits);

  // string linkage = 2;
  void clear_linkage();
  const std::string& linkage() const;
  void set_linkage(const std::string& value);
  void set_linkage(std::string&& value);
  void set_linkage(const char* value);
  void set_linkage(const char* value, size_t size);
  std::string* mutable_linkage();
  std::string* release_linkage();
  void set_allocated_linkage(std::string* linkage);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_linkage();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_linkage(
      std::string* linkage);

  // string machine = 3;
  void clear_machine();
  const std::string& machine() const;
  void set_machine(const std::string& value);
  void set_machine(std::string&& value);
  void set_machine(const char* value);
  void set_machine(const char* value, size_t size);
  std::string* mutable_machine();
  std::string* release_machine();
  void set_allocated_machine(std::string* machine);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_machine();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_machine(
      std::string* machine);

  // string release = 4;
  void clear_release();
  const std::string& release() const;
  void set_release(const std::string& value);
  void set_release(std::string&& value);
  void set_release(const char* value);
  void set_release(const char* value, size_t size);
  std::string* mutable_release();
  std::string* release_release();
  void set_allocated_release(std::string* release);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_release();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_release(
      std::string* release);

  // string system = 5;
  void clear_system();
  const std::string& system() const;
  void set_system(const std::string& value);
  void set_system(std::string&& value);
  void set_system(const char* value);
  void set_system(const char* value, size_t size);
  std::string* mutable_system();
  std::string* release_system();
  void set_allocated_system(std::string* system);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_system();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_system(
      std::string* system);

  // string version = 6;
  void clear_version();
  const std::string& version() const;
  void set_version(const std::string& value);
  void set_version(std::string&& value);
  void set_version(const char* value);
  void set_version(const char* value, size_t size);
  std::string* mutable_version();
  std::string* release_version();
  void set_allocated_version(std::string* version);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_version();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_version(
      std::string* version);

  // @@protoc_insertion_point(class_scope:tensorflow.PlatformInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bits_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr linkage_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr machine_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr release_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr system_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr version_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class AvailableDeviceInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AvailableDeviceInfo) */ {
 public:
  AvailableDeviceInfo();
  virtual ~AvailableDeviceInfo();

  AvailableDeviceInfo(const AvailableDeviceInfo& from);
  AvailableDeviceInfo(AvailableDeviceInfo&& from) noexcept
    : AvailableDeviceInfo() {
    *this = ::std::move(from);
  }

  inline AvailableDeviceInfo& operator=(const AvailableDeviceInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline AvailableDeviceInfo& operator=(AvailableDeviceInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AvailableDeviceInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AvailableDeviceInfo* internal_default_instance() {
    return reinterpret_cast<const AvailableDeviceInfo*>(
               &_AvailableDeviceInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(AvailableDeviceInfo& a, AvailableDeviceInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(AvailableDeviceInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AvailableDeviceInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AvailableDeviceInfo* New() const final {
    return CreateMaybeMessage<AvailableDeviceInfo>(nullptr);
  }

  AvailableDeviceInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AvailableDeviceInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AvailableDeviceInfo& from);
  void MergeFrom(const AvailableDeviceInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AvailableDeviceInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AvailableDeviceInfo";
  }
  protected:
  explicit AvailableDeviceInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kTypeFieldNumber = 2,
    kPhysicalDescriptionFieldNumber = 4,
    kMemoryLimitFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string type = 2;
  void clear_type();
  const std::string& type() const;
  void set_type(const std::string& value);
  void set_type(std::string&& value);
  void set_type(const char* value);
  void set_type(const char* value, size_t size);
  std::string* mutable_type();
  std::string* release_type();
  void set_allocated_type(std::string* type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type(
      std::string* type);

  // string physical_description = 4;
  void clear_physical_description();
  const std::string& physical_description() const;
  void set_physical_description(const std::string& value);
  void set_physical_description(std::string&& value);
  void set_physical_description(const char* value);
  void set_physical_description(const char* value, size_t size);
  std::string* mutable_physical_description();
  std::string* release_physical_description();
  void set_allocated_physical_description(std::string* physical_description);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_physical_description();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_physical_description(
      std::string* physical_description);

  // int64 memory_limit = 3;
  void clear_memory_limit();
  ::PROTOBUF_NAMESPACE_ID::int64 memory_limit() const;
  void set_memory_limit(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.AvailableDeviceInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr physical_description_;
  ::PROTOBUF_NAMESPACE_ID::int64 memory_limit_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class MachineConfiguration :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MachineConfiguration) */ {
 public:
  MachineConfiguration();
  virtual ~MachineConfiguration();

  MachineConfiguration(const MachineConfiguration& from);
  MachineConfiguration(MachineConfiguration&& from) noexcept
    : MachineConfiguration() {
    *this = ::std::move(from);
  }

  inline MachineConfiguration& operator=(const MachineConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline MachineConfiguration& operator=(MachineConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MachineConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MachineConfiguration* internal_default_instance() {
    return reinterpret_cast<const MachineConfiguration*>(
               &_MachineConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(MachineConfiguration& a, MachineConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(MachineConfiguration* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MachineConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MachineConfiguration* New() const final {
    return CreateMaybeMessage<MachineConfiguration>(nullptr);
  }

  MachineConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MachineConfiguration>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MachineConfiguration& from);
  void MergeFrom(const MachineConfiguration& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MachineConfiguration* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MachineConfiguration";
  }
  protected:
  explicit MachineConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceInfoFieldNumber = 4,
    kAvailableDeviceInfoFieldNumber = 5,
    kHostnameFieldNumber = 1,
    kSerialIdentifierFieldNumber = 7,
    kPlatformInfoFieldNumber = 2,
    kCpuInfoFieldNumber = 3,
    kMemoryInfoFieldNumber = 6,
  };
  // repeated .google.protobuf.Any device_info = 4;
  int device_info_size() const;
  void clear_device_info();
  PROTOBUF_NAMESPACE_ID::Any* mutable_device_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< PROTOBUF_NAMESPACE_ID::Any >*
      mutable_device_info();
  const PROTOBUF_NAMESPACE_ID::Any& device_info(int index) const;
  PROTOBUF_NAMESPACE_ID::Any* add_device_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< PROTOBUF_NAMESPACE_ID::Any >&
      device_info() const;

  // repeated .tensorflow.AvailableDeviceInfo available_device_info = 5;
  int available_device_info_size() const;
  void clear_available_device_info();
  ::tensorflow::AvailableDeviceInfo* mutable_available_device_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >*
      mutable_available_device_info();
  const ::tensorflow::AvailableDeviceInfo& available_device_info(int index) const;
  ::tensorflow::AvailableDeviceInfo* add_available_device_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >&
      available_device_info() const;

  // string hostname = 1;
  void clear_hostname();
  const std::string& hostname() const;
  void set_hostname(const std::string& value);
  void set_hostname(std::string&& value);
  void set_hostname(const char* value);
  void set_hostname(const char* value, size_t size);
  std::string* mutable_hostname();
  std::string* release_hostname();
  void set_allocated_hostname(std::string* hostname);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_hostname();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_hostname(
      std::string* hostname);

  // string serial_identifier = 7;
  void clear_serial_identifier();
  const std::string& serial_identifier() const;
  void set_serial_identifier(const std::string& value);
  void set_serial_identifier(std::string&& value);
  void set_serial_identifier(const char* value);
  void set_serial_identifier(const char* value, size_t size);
  std::string* mutable_serial_identifier();
  std::string* release_serial_identifier();
  void set_allocated_serial_identifier(std::string* serial_identifier);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_serial_identifier();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_serial_identifier(
      std::string* serial_identifier);

  // .tensorflow.PlatformInfo platform_info = 2;
  bool has_platform_info() const;
  void clear_platform_info();
  const ::tensorflow::PlatformInfo& platform_info() const;
  ::tensorflow::PlatformInfo* release_platform_info();
  ::tensorflow::PlatformInfo* mutable_platform_info();
  void set_allocated_platform_info(::tensorflow::PlatformInfo* platform_info);
  void unsafe_arena_set_allocated_platform_info(
      ::tensorflow::PlatformInfo* platform_info);
  ::tensorflow::PlatformInfo* unsafe_arena_release_platform_info();

  // .tensorflow.CPUInfo cpu_info = 3;
  bool has_cpu_info() const;
  void clear_cpu_info();
  const ::tensorflow::CPUInfo& cpu_info() const;
  ::tensorflow::CPUInfo* release_cpu_info();
  ::tensorflow::CPUInfo* mutable_cpu_info();
  void set_allocated_cpu_info(::tensorflow::CPUInfo* cpu_info);
  void unsafe_arena_set_allocated_cpu_info(
      ::tensorflow::CPUInfo* cpu_info);
  ::tensorflow::CPUInfo* unsafe_arena_release_cpu_info();

  // .tensorflow.MemoryInfo memory_info = 6;
  bool has_memory_info() const;
  void clear_memory_info();
  const ::tensorflow::MemoryInfo& memory_info() const;
  ::tensorflow::MemoryInfo* release_memory_info();
  ::tensorflow::MemoryInfo* mutable_memory_info();
  void set_allocated_memory_info(::tensorflow::MemoryInfo* memory_info);
  void unsafe_arena_set_allocated_memory_info(
      ::tensorflow::MemoryInfo* memory_info);
  ::tensorflow::MemoryInfo* unsafe_arena_release_memory_info();

  // @@protoc_insertion_point(class_scope:tensorflow.MachineConfiguration)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< PROTOBUF_NAMESPACE_ID::Any > device_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo > available_device_info_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr hostname_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serial_identifier_;
  ::tensorflow::PlatformInfo* platform_info_;
  ::tensorflow::CPUInfo* cpu_info_;
  ::tensorflow::MemoryInfo* memory_info_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class RunConfiguration_EnvVarsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RunConfiguration_EnvVarsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RunConfiguration_EnvVarsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  RunConfiguration_EnvVarsEntry_DoNotUse();
  RunConfiguration_EnvVarsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const RunConfiguration_EnvVarsEntry_DoNotUse& other);
  static const RunConfiguration_EnvVarsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const RunConfiguration_EnvVarsEntry_DoNotUse*>(&_RunConfiguration_EnvVarsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.RunConfiguration.EnvVarsEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.RunConfiguration.EnvVarsEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[14];
  }

  public:
};

// -------------------------------------------------------------------

class RunConfiguration :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunConfiguration) */ {
 public:
  RunConfiguration();
  virtual ~RunConfiguration();

  RunConfiguration(const RunConfiguration& from);
  RunConfiguration(RunConfiguration&& from) noexcept
    : RunConfiguration() {
    *this = ::std::move(from);
  }

  inline RunConfiguration& operator=(const RunConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunConfiguration& operator=(RunConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunConfiguration* internal_default_instance() {
    return reinterpret_cast<const RunConfiguration*>(
               &_RunConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(RunConfiguration& a, RunConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(RunConfiguration* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunConfiguration* New() const final {
    return CreateMaybeMessage<RunConfiguration>(nullptr);
  }

  RunConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunConfiguration>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunConfiguration& from);
  void MergeFrom(const RunConfiguration& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunConfiguration* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunConfiguration";
  }
  protected:
  explicit RunConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kArgumentFieldNumber = 1,
    kEnvVarsFieldNumber = 2,
  };
  // repeated string argument = 1;
  int argument_size() const;
  void clear_argument();
  const std::string& argument(int index) const;
  std::string* mutable_argument(int index);
  void set_argument(int index, const std::string& value);
  void set_argument(int index, std::string&& value);
  void set_argument(int index, const char* value);
  void set_argument(int index, const char* value, size_t size);
  std::string* add_argument();
  void add_argument(const std::string& value);
  void add_argument(std::string&& value);
  void add_argument(const char* value);
  void add_argument(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& argument() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_argument();

  // map<string, string> env_vars = 2;
  int env_vars_size() const;
  void clear_env_vars();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      env_vars() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_env_vars();

  // @@protoc_insertion_point(class_scope:tensorflow.RunConfiguration)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> argument_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      RunConfiguration_EnvVarsEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > env_vars_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class TestResults :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TestResults) */ {
 public:
  TestResults();
  virtual ~TestResults();

  TestResults(const TestResults& from);
  TestResults(TestResults&& from) noexcept
    : TestResults() {
    *this = ::std::move(from);
  }

  inline TestResults& operator=(const TestResults& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestResults& operator=(TestResults&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TestResults& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TestResults* internal_default_instance() {
    return reinterpret_cast<const TestResults*>(
               &_TestResults_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(TestResults& a, TestResults& b) {
    a.Swap(&b);
  }
  inline void Swap(TestResults* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestResults* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TestResults* New() const final {
    return CreateMaybeMessage<TestResults>(nullptr);
  }

  TestResults* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TestResults>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TestResults& from);
  void MergeFrom(const TestResults& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestResults* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TestResults";
  }
  protected:
  explicit TestResults(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2ftest_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TestResults_BenchmarkType BenchmarkType;
  static constexpr BenchmarkType UNKNOWN =
    TestResults_BenchmarkType_UNKNOWN;
  static constexpr BenchmarkType CPP_MICROBENCHMARK =
    TestResults_BenchmarkType_CPP_MICROBENCHMARK;
  static constexpr BenchmarkType PYTHON_BENCHMARK =
    TestResults_BenchmarkType_PYTHON_BENCHMARK;
  static constexpr BenchmarkType ANDROID_BENCHMARK =
    TestResults_BenchmarkType_ANDROID_BENCHMARK;
  static constexpr BenchmarkType EDGE_BENCHMARK =
    TestResults_BenchmarkType_EDGE_BENCHMARK;
  static constexpr BenchmarkType IOS_BENCHMARK =
    TestResults_BenchmarkType_IOS_BENCHMARK;
  static inline bool BenchmarkType_IsValid(int value) {
    return TestResults_BenchmarkType_IsValid(value);
  }
  static constexpr BenchmarkType BenchmarkType_MIN =
    TestResults_BenchmarkType_BenchmarkType_MIN;
  static constexpr BenchmarkType BenchmarkType_MAX =
    TestResults_BenchmarkType_BenchmarkType_MAX;
  static constexpr int BenchmarkType_ARRAYSIZE =
    TestResults_BenchmarkType_BenchmarkType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  BenchmarkType_descriptor() {
    return TestResults_BenchmarkType_descriptor();
  }
  template<typename T>
  static inline const std::string& BenchmarkType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, BenchmarkType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function BenchmarkType_Name.");
    return TestResults_BenchmarkType_Name(enum_t_value);
  }
  static inline bool BenchmarkType_Parse(const std::string& name,
      BenchmarkType* value) {
    return TestResults_BenchmarkType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTargetFieldNumber = 1,
    kNameFieldNumber = 9,
    kRunModeFieldNumber = 11,
    kTfVersionFieldNumber = 12,
    kEntriesFieldNumber = 2,
    kBuildConfigurationFieldNumber = 3,
    kCommitIdFieldNumber = 4,
    kMachineConfigurationFieldNumber = 7,
    kRunConfigurationFieldNumber = 8,
    kStartTimeFieldNumber = 5,
    kRunTimeFieldNumber = 6,
    kBenchmarkTypeFieldNumber = 10,
  };
  // string target = 1;
  void clear_target();
  const std::string& target() const;
  void set_target(const std::string& value);
  void set_target(std::string&& value);
  void set_target(const char* value);
  void set_target(const char* value, size_t size);
  std::string* mutable_target();
  std::string* release_target();
  void set_allocated_target(std::string* target);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_target();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_target(
      std::string* target);

  // string name = 9;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string run_mode = 11;
  void clear_run_mode();
  const std::string& run_mode() const;
  void set_run_mode(const std::string& value);
  void set_run_mode(std::string&& value);
  void set_run_mode(const char* value);
  void set_run_mode(const char* value, size_t size);
  std::string* mutable_run_mode();
  std::string* release_run_mode();
  void set_allocated_run_mode(std::string* run_mode);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_run_mode();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_run_mode(
      std::string* run_mode);

  // string tf_version = 12;
  void clear_tf_version();
  const std::string& tf_version() const;
  void set_tf_version(const std::string& value);
  void set_tf_version(std::string&& value);
  void set_tf_version(const char* value);
  void set_tf_version(const char* value, size_t size);
  std::string* mutable_tf_version();
  std::string* release_tf_version();
  void set_allocated_tf_version(std::string* tf_version);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_tf_version();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_tf_version(
      std::string* tf_version);

  // .tensorflow.BenchmarkEntries entries = 2;
  bool has_entries() const;
  void clear_entries();
  const ::tensorflow::BenchmarkEntries& entries() const;
  ::tensorflow::BenchmarkEntries* release_entries();
  ::tensorflow::BenchmarkEntries* mutable_entries();
  void set_allocated_entries(::tensorflow::BenchmarkEntries* entries);
  void unsafe_arena_set_allocated_entries(
      ::tensorflow::BenchmarkEntries* entries);
  ::tensorflow::BenchmarkEntries* unsafe_arena_release_entries();

  // .tensorflow.BuildConfiguration build_configuration = 3;
  bool has_build_configuration() const;
  void clear_build_configuration();
  const ::tensorflow::BuildConfiguration& build_configuration() const;
  ::tensorflow::BuildConfiguration* release_build_configuration();
  ::tensorflow::BuildConfiguration* mutable_build_configuration();
  void set_allocated_build_configuration(::tensorflow::BuildConfiguration* build_configuration);
  void unsafe_arena_set_allocated_build_configuration(
      ::tensorflow::BuildConfiguration* build_configuration);
  ::tensorflow::BuildConfiguration* unsafe_arena_release_build_configuration();

  // .tensorflow.CommitId commit_id = 4;
  bool has_commit_id() const;
  void clear_commit_id();
  const ::tensorflow::CommitId& commit_id() const;
  ::tensorflow::CommitId* release_commit_id();
  ::tensorflow::CommitId* mutable_commit_id();
  void set_allocated_commit_id(::tensorflow::CommitId* commit_id);
  void unsafe_arena_set_allocated_commit_id(
      ::tensorflow::CommitId* commit_id);
  ::tensorflow::CommitId* unsafe_arena_release_commit_id();

  // .tensorflow.MachineConfiguration machine_configuration = 7;
  bool has_machine_configuration() const;
  void clear_machine_configuration();
  const ::tensorflow::MachineConfiguration& machine_configuration() const;
  ::tensorflow::MachineConfiguration* release_machine_configuration();
  ::tensorflow::MachineConfiguration* mutable_machine_configuration();
  void set_allocated_machine_configuration(::tensorflow::MachineConfiguration* machine_configuration);
  void unsafe_arena_set_allocated_machine_configuration(
      ::tensorflow::MachineConfiguration* machine_configuration);
  ::tensorflow::MachineConfiguration* unsafe_arena_release_machine_configuration();

  // .tensorflow.RunConfiguration run_configuration = 8;
  bool has_run_configuration() const;
  void clear_run_configuration();
  const ::tensorflow::RunConfiguration& run_configuration() const;
  ::tensorflow::RunConfiguration* release_run_configuration();
  ::tensorflow::RunConfiguration* mutable_run_configuration();
  void set_allocated_run_configuration(::tensorflow::RunConfiguration* run_configuration);
  void unsafe_arena_set_allocated_run_configuration(
      ::tensorflow::RunConfiguration* run_configuration);
  ::tensorflow::RunConfiguration* unsafe_arena_release_run_configuration();

  // int64 start_time = 5;
  void clear_start_time();
  ::PROTOBUF_NAMESPACE_ID::int64 start_time() const;
  void set_start_time(::PROTOBUF_NAMESPACE_ID::int64 value);

  // double run_time = 6;
  void clear_run_time();
  double run_time() const;
  void set_run_time(double value);

  // .tensorflow.TestResults.BenchmarkType benchmark_type = 10;
  void clear_benchmark_type();
  ::tensorflow::TestResults_BenchmarkType benchmark_type() const;
  void set_benchmark_type(::tensorflow::TestResults_BenchmarkType value);

  // @@protoc_insertion_point(class_scope:tensorflow.TestResults)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr target_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr run_mode_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tf_version_;
  ::tensorflow::BenchmarkEntries* entries_;
  ::tensorflow::BuildConfiguration* build_configuration_;
  ::tensorflow::CommitId* commit_id_;
  ::tensorflow::MachineConfiguration* machine_configuration_;
  ::tensorflow::RunConfiguration* run_configuration_;
  ::PROTOBUF_NAMESPACE_ID::int64 start_time_;
  double run_time_;
  int benchmark_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2ftest_5flog_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// EntryValue

// double double_value = 1;
inline bool EntryValue::has_double_value() const {
  return kind_case() == kDoubleValue;
}
inline void EntryValue::set_has_double_value() {
  _oneof_case_[0] = kDoubleValue;
}
inline void EntryValue::clear_double_value() {
  if (has_double_value()) {
    kind_.double_value_ = 0;
    clear_has_kind();
  }
}
inline double EntryValue::double_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.EntryValue.double_value)
  if (has_double_value()) {
    return kind_.double_value_;
  }
  return 0;
}
inline void EntryValue::set_double_value(double value) {
  if (!has_double_value()) {
    clear_kind();
    set_has_double_value();
  }
  kind_.double_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.EntryValue.double_value)
}

// string string_value = 2;
inline bool EntryValue::has_string_value() const {
  return kind_case() == kStringValue;
}
inline void EntryValue::set_has_string_value() {
  _oneof_case_[0] = kStringValue;
}
inline void EntryValue::clear_string_value() {
  if (has_string_value()) {
    kind_.string_value_.Destroy(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_kind();
  }
}
inline const std::string& EntryValue::string_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.EntryValue.string_value)
  if (has_string_value()) {
    return kind_.string_value_.Get();
  }
  return *&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void EntryValue::set_string_value(const std::string& value) {
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.EntryValue.string_value)
}
inline void EntryValue::set_string_value(std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.EntryValue.string_value)
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.EntryValue.string_value)
}
inline void EntryValue::set_string_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.EntryValue.string_value)
}
inline void EntryValue::set_string_value(const char* value,
                             size_t size) {
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.Set(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.EntryValue.string_value)
}
inline std::string* EntryValue::mutable_string_value() {
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return kind_.string_value_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.EntryValue.string_value)
}
inline std::string* EntryValue::release_string_value() {
  // @@protoc_insertion_point(field_release:tensorflow.EntryValue.string_value)
  if (has_string_value()) {
    clear_has_kind();
    return kind_.string_value_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void EntryValue::set_allocated_string_value(std::string* string_value) {
  if (has_kind()) {
    clear_kind();
  }
  if (string_value != nullptr) {
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(string_value);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EntryValue.string_value)
}
inline std::string* EntryValue::unsafe_arena_release_string_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.EntryValue.string_value)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (has_string_value()) {
    clear_has_kind();
    return kind_.string_value_.UnsafeArenaRelease(
        &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void EntryValue::unsafe_arena_set_allocated_string_value(std::string* string_value) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (!has_string_value()) {
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  clear_kind();
  if (string_value) {
    set_has_string_value();
    kind_.string_value_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), string_value, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.EntryValue.string_value)
}

inline bool EntryValue::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void EntryValue::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline EntryValue::KindCase EntryValue::kind_case() const {
  return EntryValue::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// MetricEntry

// string name = 1;
inline void MetricEntry::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MetricEntry::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetricEntry.name)
  return name_.Get();
}
inline void MetricEntry::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MetricEntry.name)
}
inline void MetricEntry::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MetricEntry.name)
}
inline void MetricEntry::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MetricEntry.name)
}
inline void MetricEntry::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MetricEntry.name)
}
inline std::string* MetricEntry::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MetricEntry.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MetricEntry::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MetricEntry.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MetricEntry::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetricEntry.name)
}
inline std::string* MetricEntry::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetricEntry.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MetricEntry::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetricEntry.name)
}

// double value = 2;
inline void MetricEntry::clear_value() {
  value_ = 0;
}
inline double MetricEntry::value() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetricEntry.value)
  return value_;
}
inline void MetricEntry::set_value(double value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MetricEntry.value)
}

// .google.protobuf.DoubleValue min_value = 3;
inline bool MetricEntry::has_min_value() const {
  return this != internal_default_instance() && min_value_ != nullptr;
}
inline const PROTOBUF_NAMESPACE_ID::DoubleValue& MetricEntry::min_value() const {
  const PROTOBUF_NAMESPACE_ID::DoubleValue* p = min_value_;
  // @@protoc_insertion_point(field_get:tensorflow.MetricEntry.min_value)
  return p != nullptr ? *p : *reinterpret_cast<const PROTOBUF_NAMESPACE_ID::DoubleValue*>(
      &PROTOBUF_NAMESPACE_ID::_DoubleValue_default_instance_);
}
inline PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::release_min_value() {
  // @@protoc_insertion_point(field_release:tensorflow.MetricEntry.min_value)
  
  PROTOBUF_NAMESPACE_ID::DoubleValue* temp = min_value_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  min_value_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::unsafe_arena_release_min_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetricEntry.min_value)
  
  PROTOBUF_NAMESPACE_ID::DoubleValue* temp = min_value_;
  min_value_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::mutable_min_value() {
  
  if (min_value_ == nullptr) {
    auto* p = CreateMaybeMessage<PROTOBUF_NAMESPACE_ID::DoubleValue>(GetArenaNoVirtual());
    min_value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MetricEntry.min_value)
  return min_value_;
}
inline void MetricEntry::set_allocated_min_value(PROTOBUF_NAMESPACE_ID::DoubleValue* min_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(min_value_);
  }
  if (min_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(min_value)->GetArena();
    if (message_arena != submessage_arena) {
      min_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, min_value, submessage_arena);
    }
    
  } else {
    
  }
  min_value_ = min_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetricEntry.min_value)
}

// .google.protobuf.DoubleValue max_value = 4;
inline bool MetricEntry::has_max_value() const {
  return this != internal_default_instance() && max_value_ != nullptr;
}
inline const PROTOBUF_NAMESPACE_ID::DoubleValue& MetricEntry::max_value() const {
  const PROTOBUF_NAMESPACE_ID::DoubleValue* p = max_value_;
  // @@protoc_insertion_point(field_get:tensorflow.MetricEntry.max_value)
  return p != nullptr ? *p : *reinterpret_cast<const PROTOBUF_NAMESPACE_ID::DoubleValue*>(
      &PROTOBUF_NAMESPACE_ID::_DoubleValue_default_instance_);
}
inline PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::release_max_value() {
  // @@protoc_insertion_point(field_release:tensorflow.MetricEntry.max_value)
  
  PROTOBUF_NAMESPACE_ID::DoubleValue* temp = max_value_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  max_value_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::unsafe_arena_release_max_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetricEntry.max_value)
  
  PROTOBUF_NAMESPACE_ID::DoubleValue* temp = max_value_;
  max_value_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::mutable_max_value() {
  
  if (max_value_ == nullptr) {
    auto* p = CreateMaybeMessage<PROTOBUF_NAMESPACE_ID::DoubleValue>(GetArenaNoVirtual());
    max_value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MetricEntry.max_value)
  return max_value_;
}
inline void MetricEntry::set_allocated_max_value(PROTOBUF_NAMESPACE_ID::DoubleValue* max_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(max_value_);
  }
  if (max_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(max_value)->GetArena();
    if (message_arena != submessage_arena) {
      max_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, max_value, submessage_arena);
    }
    
  } else {
    
  }
  max_value_ = max_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetricEntry.max_value)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// BenchmarkEntry

// string name = 1;
inline void BenchmarkEntry::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& BenchmarkEntry::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.name)
  return name_.Get();
}
inline void BenchmarkEntry::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.name)
}
inline void BenchmarkEntry::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.BenchmarkEntry.name)
}
inline void BenchmarkEntry::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.BenchmarkEntry.name)
}
inline void BenchmarkEntry::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BenchmarkEntry.name)
}
inline std::string* BenchmarkEntry::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.BenchmarkEntry.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* BenchmarkEntry::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.BenchmarkEntry.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BenchmarkEntry::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BenchmarkEntry.name)
}
inline std::string* BenchmarkEntry::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.BenchmarkEntry.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BenchmarkEntry::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.BenchmarkEntry.name)
}

// int64 iters = 2;
inline void BenchmarkEntry::clear_iters() {
  iters_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BenchmarkEntry::iters() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.iters)
  return iters_;
}
inline void BenchmarkEntry::set_iters(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  iters_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.iters)
}

// double cpu_time = 3;
inline void BenchmarkEntry::clear_cpu_time() {
  cpu_time_ = 0;
}
inline double BenchmarkEntry::cpu_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.cpu_time)
  return cpu_time_;
}
inline void BenchmarkEntry::set_cpu_time(double value) {
  
  cpu_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.cpu_time)
}

// double wall_time = 4;
inline void BenchmarkEntry::clear_wall_time() {
  wall_time_ = 0;
}
inline double BenchmarkEntry::wall_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.wall_time)
  return wall_time_;
}
inline void BenchmarkEntry::set_wall_time(double value) {
  
  wall_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.wall_time)
}

// double throughput = 5;
inline void BenchmarkEntry::clear_throughput() {
  throughput_ = 0;
}
inline double BenchmarkEntry::throughput() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.throughput)
  return throughput_;
}
inline void BenchmarkEntry::set_throughput(double value) {
  
  throughput_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.throughput)
}

// map<string, .tensorflow.EntryValue> extras = 6;
inline int BenchmarkEntry::extras_size() const {
  return extras_.size();
}
inline void BenchmarkEntry::clear_extras() {
  extras_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >&
BenchmarkEntry::extras() const {
  // @@protoc_insertion_point(field_map:tensorflow.BenchmarkEntry.extras)
  return extras_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >*
BenchmarkEntry::mutable_extras() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.BenchmarkEntry.extras)
  return extras_.MutableMap();
}

// repeated .tensorflow.MetricEntry metrics = 7;
inline int BenchmarkEntry::metrics_size() const {
  return metrics_.size();
}
inline void BenchmarkEntry::clear_metrics() {
  metrics_.Clear();
}
inline ::tensorflow::MetricEntry* BenchmarkEntry::mutable_metrics(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BenchmarkEntry.metrics)
  return metrics_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MetricEntry >*
BenchmarkEntry::mutable_metrics() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BenchmarkEntry.metrics)
  return &metrics_;
}
inline const ::tensorflow::MetricEntry& BenchmarkEntry::metrics(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.metrics)
  return metrics_.Get(index);
}
inline ::tensorflow::MetricEntry* BenchmarkEntry::add_metrics() {
  // @@protoc_insertion_point(field_add:tensorflow.BenchmarkEntry.metrics)
  return metrics_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MetricEntry >&
BenchmarkEntry::metrics() const {
  // @@protoc_insertion_point(field_list:tensorflow.BenchmarkEntry.metrics)
  return metrics_;
}

// -------------------------------------------------------------------

// BenchmarkEntries

// repeated .tensorflow.BenchmarkEntry entry = 1;
inline int BenchmarkEntries::entry_size() const {
  return entry_.size();
}
inline void BenchmarkEntries::clear_entry() {
  entry_.Clear();
}
inline ::tensorflow::BenchmarkEntry* BenchmarkEntries::mutable_entry(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BenchmarkEntries.entry)
  return entry_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BenchmarkEntry >*
BenchmarkEntries::mutable_entry() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BenchmarkEntries.entry)
  return &entry_;
}
inline const ::tensorflow::BenchmarkEntry& BenchmarkEntries::entry(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntries.entry)
  return entry_.Get(index);
}
inline ::tensorflow::BenchmarkEntry* BenchmarkEntries::add_entry() {
  // @@protoc_insertion_point(field_add:tensorflow.BenchmarkEntries.entry)
  return entry_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BenchmarkEntry >&
BenchmarkEntries::entry() const {
  // @@protoc_insertion_point(field_list:tensorflow.BenchmarkEntries.entry)
  return entry_;
}

// -------------------------------------------------------------------

// BuildConfiguration

// string mode = 1;
inline void BuildConfiguration::clear_mode() {
  mode_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& BuildConfiguration::mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.BuildConfiguration.mode)
  return mode_.Get();
}
inline void BuildConfiguration::set_mode(const std::string& value) {
  
  mode_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.mode)
}
inline void BuildConfiguration::set_mode(std::string&& value) {
  
  mode_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.BuildConfiguration.mode)
}
inline void BuildConfiguration::set_mode(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  mode_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.BuildConfiguration.mode)
}
inline void BuildConfiguration::set_mode(const char* value,
    size_t size) {
  
  mode_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BuildConfiguration.mode)
}
inline std::string* BuildConfiguration::mutable_mode() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.BuildConfiguration.mode)
  return mode_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* BuildConfiguration::release_mode() {
  // @@protoc_insertion_point(field_release:tensorflow.BuildConfiguration.mode)
  
  return mode_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BuildConfiguration::set_allocated_mode(std::string* mode) {
  if (mode != nullptr) {
    
  } else {
    
  }
  mode_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), mode,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BuildConfiguration.mode)
}
inline std::string* BuildConfiguration::unsafe_arena_release_mode() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.BuildConfiguration.mode)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return mode_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BuildConfiguration::unsafe_arena_set_allocated_mode(
    std::string* mode) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (mode != nullptr) {
    
  } else {
    
  }
  mode_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      mode, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.BuildConfiguration.mode)
}

// repeated string cc_flags = 2;
inline int BuildConfiguration::cc_flags_size() const {
  return cc_flags_.size();
}
inline void BuildConfiguration::clear_cc_flags() {
  cc_flags_.Clear();
}
inline const std::string& BuildConfiguration::cc_flags(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BuildConfiguration.cc_flags)
  return cc_flags_.Get(index);
}
inline std::string* BuildConfiguration::mutable_cc_flags(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BuildConfiguration.cc_flags)
  return cc_flags_.Mutable(index);
}
inline void BuildConfiguration::set_cc_flags(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.cc_flags)
  cc_flags_.Mutable(index)->assign(value);
}
inline void BuildConfiguration::set_cc_flags(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.cc_flags)
  cc_flags_.Mutable(index)->assign(std::move(value));
}
inline void BuildConfiguration::set_cc_flags(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  cc_flags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::set_cc_flags(int index, const char* value, size_t size) {
  cc_flags_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BuildConfiguration.cc_flags)
}
inline std::string* BuildConfiguration::add_cc_flags() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.BuildConfiguration.cc_flags)
  return cc_flags_.Add();
}
inline void BuildConfiguration::add_cc_flags(const std::string& value) {
  cc_flags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::add_cc_flags(std::string&& value) {
  cc_flags_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::add_cc_flags(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  cc_flags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::add_cc_flags(const char* value, size_t size) {
  cc_flags_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.BuildConfiguration.cc_flags)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
BuildConfiguration::cc_flags() const {
  // @@protoc_insertion_point(field_list:tensorflow.BuildConfiguration.cc_flags)
  return cc_flags_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
BuildConfiguration::mutable_cc_flags() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BuildConfiguration.cc_flags)
  return &cc_flags_;
}

// repeated string opts = 3;
inline int BuildConfiguration::opts_size() const {
  return opts_.size();
}
inline void BuildConfiguration::clear_opts() {
  opts_.Clear();
}
inline const std::string& BuildConfiguration::opts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BuildConfiguration.opts)
  return opts_.Get(index);
}
inline std::string* BuildConfiguration::mutable_opts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BuildConfiguration.opts)
  return opts_.Mutable(index);
}
inline void BuildConfiguration::set_opts(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.opts)
  opts_.Mutable(index)->assign(value);
}
inline void BuildConfiguration::set_opts(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.opts)
  opts_.Mutable(index)->assign(std::move(value));
}
inline void BuildConfiguration::set_opts(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  opts_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::set_opts(int index, const char* value, size_t size) {
  opts_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BuildConfiguration.opts)
}
inline std::string* BuildConfiguration::add_opts() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.BuildConfiguration.opts)
  return opts_.Add();
}
inline void BuildConfiguration::add_opts(const std::string& value) {
  opts_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::add_opts(std::string&& value) {
  opts_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::add_opts(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  opts_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::add_opts(const char* value, size_t size) {
  opts_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.BuildConfiguration.opts)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
BuildConfiguration::opts() const {
  // @@protoc_insertion_point(field_list:tensorflow.BuildConfiguration.opts)
  return opts_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
BuildConfiguration::mutable_opts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BuildConfiguration.opts)
  return &opts_;
}

// -------------------------------------------------------------------

// CommitId

// int64 changelist = 1;
inline bool CommitId::has_changelist() const {
  return kind_case() == kChangelist;
}
inline void CommitId::set_has_changelist() {
  _oneof_case_[0] = kChangelist;
}
inline void CommitId::clear_changelist() {
  if (has_changelist()) {
    kind_.changelist_ = PROTOBUF_LONGLONG(0);
    clear_has_kind();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CommitId::changelist() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.changelist)
  if (has_changelist()) {
    return kind_.changelist_;
  }
  return PROTOBUF_LONGLONG(0);
}
inline void CommitId::set_changelist(::PROTOBUF_NAMESPACE_ID::int64 value) {
  if (!has_changelist()) {
    clear_kind();
    set_has_changelist();
  }
  kind_.changelist_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.changelist)
}

// string hash = 2;
inline bool CommitId::has_hash() const {
  return kind_case() == kHash;
}
inline void CommitId::set_has_hash() {
  _oneof_case_[0] = kHash;
}
inline void CommitId::clear_hash() {
  if (has_hash()) {
    kind_.hash_.Destroy(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_kind();
  }
}
inline const std::string& CommitId::hash() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.hash)
  if (has_hash()) {
    return kind_.hash_.Get();
  }
  return *&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void CommitId::set_hash(const std::string& value) {
  if (!has_hash()) {
    clear_kind();
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.hash_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.hash)
}
inline void CommitId::set_hash(std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.hash)
  if (!has_hash()) {
    clear_kind();
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.hash_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CommitId.hash)
}
inline void CommitId::set_hash(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  if (!has_hash()) {
    clear_kind();
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.hash_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CommitId.hash)
}
inline void CommitId::set_hash(const char* value,
                             size_t size) {
  if (!has_hash()) {
    clear_kind();
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.hash_.Set(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CommitId.hash)
}
inline std::string* CommitId::mutable_hash() {
  if (!has_hash()) {
    clear_kind();
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return kind_.hash_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.CommitId.hash)
}
inline std::string* CommitId::release_hash() {
  // @@protoc_insertion_point(field_release:tensorflow.CommitId.hash)
  if (has_hash()) {
    clear_has_kind();
    return kind_.hash_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void CommitId::set_allocated_hash(std::string* hash) {
  if (has_kind()) {
    clear_kind();
  }
  if (hash != nullptr) {
    set_has_hash();
    kind_.hash_.UnsafeSetDefault(hash);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CommitId.hash)
}
inline std::string* CommitId::unsafe_arena_release_hash() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CommitId.hash)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (has_hash()) {
    clear_has_kind();
    return kind_.hash_.UnsafeArenaRelease(
        &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void CommitId::unsafe_arena_set_allocated_hash(std::string* hash) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (!has_hash()) {
    kind_.hash_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  clear_kind();
  if (hash) {
    set_has_hash();
    kind_.hash_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), hash, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CommitId.hash)
}

// string snapshot = 3;
inline void CommitId::clear_snapshot() {
  snapshot_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CommitId::snapshot() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.snapshot)
  return snapshot_.Get();
}
inline void CommitId::set_snapshot(const std::string& value) {
  
  snapshot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.snapshot)
}
inline void CommitId::set_snapshot(std::string&& value) {
  
  snapshot_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CommitId.snapshot)
}
inline void CommitId::set_snapshot(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  snapshot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CommitId.snapshot)
}
inline void CommitId::set_snapshot(const char* value,
    size_t size) {
  
  snapshot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CommitId.snapshot)
}
inline std::string* CommitId::mutable_snapshot() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CommitId.snapshot)
  return snapshot_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CommitId::release_snapshot() {
  // @@protoc_insertion_point(field_release:tensorflow.CommitId.snapshot)
  
  return snapshot_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CommitId::set_allocated_snapshot(std::string* snapshot) {
  if (snapshot != nullptr) {
    
  } else {
    
  }
  snapshot_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), snapshot,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CommitId.snapshot)
}
inline std::string* CommitId::unsafe_arena_release_snapshot() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CommitId.snapshot)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return snapshot_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CommitId::unsafe_arena_set_allocated_snapshot(
    std::string* snapshot) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (snapshot != nullptr) {
    
  } else {
    
  }
  snapshot_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      snapshot, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CommitId.snapshot)
}

// int64 pending_changelist = 4;
inline void CommitId::clear_pending_changelist() {
  pending_changelist_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CommitId::pending_changelist() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.pending_changelist)
  return pending_changelist_;
}
inline void CommitId::set_pending_changelist(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  pending_changelist_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.pending_changelist)
}

inline bool CommitId::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void CommitId::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline CommitId::KindCase CommitId::kind_case() const {
  return CommitId::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// CPUInfo

// int64 num_cores = 1;
inline void CPUInfo::clear_num_cores() {
  num_cores_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CPUInfo::num_cores() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.num_cores)
  return num_cores_;
}
inline void CPUInfo::set_num_cores(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_cores_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.num_cores)
}

// int64 num_cores_allowed = 2;
inline void CPUInfo::clear_num_cores_allowed() {
  num_cores_allowed_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CPUInfo::num_cores_allowed() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.num_cores_allowed)
  return num_cores_allowed_;
}
inline void CPUInfo::set_num_cores_allowed(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_cores_allowed_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.num_cores_allowed)
}

// double mhz_per_cpu = 3;
inline void CPUInfo::clear_mhz_per_cpu() {
  mhz_per_cpu_ = 0;
}
inline double CPUInfo::mhz_per_cpu() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.mhz_per_cpu)
  return mhz_per_cpu_;
}
inline void CPUInfo::set_mhz_per_cpu(double value) {
  
  mhz_per_cpu_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.mhz_per_cpu)
}

// string cpu_info = 4;
inline void CPUInfo::clear_cpu_info() {
  cpu_info_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CPUInfo::cpu_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.cpu_info)
  return cpu_info_.Get();
}
inline void CPUInfo::set_cpu_info(const std::string& value) {
  
  cpu_info_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.cpu_info)
}
inline void CPUInfo::set_cpu_info(std::string&& value) {
  
  cpu_info_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CPUInfo.cpu_info)
}
inline void CPUInfo::set_cpu_info(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  cpu_info_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CPUInfo.cpu_info)
}
inline void CPUInfo::set_cpu_info(const char* value,
    size_t size) {
  
  cpu_info_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CPUInfo.cpu_info)
}
inline std::string* CPUInfo::mutable_cpu_info() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CPUInfo.cpu_info)
  return cpu_info_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CPUInfo::release_cpu_info() {
  // @@protoc_insertion_point(field_release:tensorflow.CPUInfo.cpu_info)
  
  return cpu_info_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CPUInfo::set_allocated_cpu_info(std::string* cpu_info) {
  if (cpu_info != nullptr) {
    
  } else {
    
  }
  cpu_info_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cpu_info,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CPUInfo.cpu_info)
}
inline std::string* CPUInfo::unsafe_arena_release_cpu_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CPUInfo.cpu_info)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return cpu_info_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CPUInfo::unsafe_arena_set_allocated_cpu_info(
    std::string* cpu_info) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (cpu_info != nullptr) {
    
  } else {
    
  }
  cpu_info_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      cpu_info, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CPUInfo.cpu_info)
}

// string cpu_governor = 5;
inline void CPUInfo::clear_cpu_governor() {
  cpu_governor_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CPUInfo::cpu_governor() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.cpu_governor)
  return cpu_governor_.Get();
}
inline void CPUInfo::set_cpu_governor(const std::string& value) {
  
  cpu_governor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.cpu_governor)
}
inline void CPUInfo::set_cpu_governor(std::string&& value) {
  
  cpu_governor_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CPUInfo.cpu_governor)
}
inline void CPUInfo::set_cpu_governor(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  cpu_governor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CPUInfo.cpu_governor)
}
inline void CPUInfo::set_cpu_governor(const char* value,
    size_t size) {
  
  cpu_governor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CPUInfo.cpu_governor)
}
inline std::string* CPUInfo::mutable_cpu_governor() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CPUInfo.cpu_governor)
  return cpu_governor_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CPUInfo::release_cpu_governor() {
  // @@protoc_insertion_point(field_release:tensorflow.CPUInfo.cpu_governor)
  
  return cpu_governor_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CPUInfo::set_allocated_cpu_governor(std::string* cpu_governor) {
  if (cpu_governor != nullptr) {
    
  } else {
    
  }
  cpu_governor_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cpu_governor,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CPUInfo.cpu_governor)
}
inline std::string* CPUInfo::unsafe_arena_release_cpu_governor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CPUInfo.cpu_governor)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return cpu_governor_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CPUInfo::unsafe_arena_set_allocated_cpu_governor(
    std::string* cpu_governor) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (cpu_governor != nullptr) {
    
  } else {
    
  }
  cpu_governor_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      cpu_governor, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CPUInfo.cpu_governor)
}

// map<string, int64> cache_size = 6;
inline int CPUInfo::cache_size_size() const {
  return cache_size_.size();
}
inline void CPUInfo::clear_cache_size() {
  cache_size_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int64 >&
CPUInfo::cache_size() const {
  // @@protoc_insertion_point(field_map:tensorflow.CPUInfo.cache_size)
  return cache_size_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int64 >*
CPUInfo::mutable_cache_size() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.CPUInfo.cache_size)
  return cache_size_.MutableMap();
}

// -------------------------------------------------------------------

// MemoryInfo

// int64 total = 1;
inline void MemoryInfo::clear_total() {
  total_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryInfo::total() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryInfo.total)
  return total_;
}
inline void MemoryInfo::set_total(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryInfo.total)
}

// int64 available = 2;
inline void MemoryInfo::clear_available() {
  available_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryInfo::available() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryInfo.available)
  return available_;
}
inline void MemoryInfo::set_available(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  available_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryInfo.available)
}

// -------------------------------------------------------------------

// GPUInfo

// string model = 1;
inline void GPUInfo::clear_model() {
  model_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GPUInfo::model() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUInfo.model)
  return model_.Get();
}
inline void GPUInfo::set_model(const std::string& value) {
  
  model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GPUInfo.model)
}
inline void GPUInfo::set_model(std::string&& value) {
  
  model_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GPUInfo.model)
}
inline void GPUInfo::set_model(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GPUInfo.model)
}
inline void GPUInfo::set_model(const char* value,
    size_t size) {
  
  model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GPUInfo.model)
}
inline std::string* GPUInfo::mutable_model() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUInfo.model)
  return model_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GPUInfo::release_model() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUInfo.model)
  
  return model_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GPUInfo::set_allocated_model(std::string* model) {
  if (model != nullptr) {
    
  } else {
    
  }
  model_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUInfo.model)
}
inline std::string* GPUInfo::unsafe_arena_release_model() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GPUInfo.model)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return model_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GPUInfo::unsafe_arena_set_allocated_model(
    std::string* model) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (model != nullptr) {
    
  } else {
    
  }
  model_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      model, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUInfo.model)
}

// string uuid = 2;
inline void GPUInfo::clear_uuid() {
  uuid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GPUInfo::uuid() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUInfo.uuid)
  return uuid_.Get();
}
inline void GPUInfo::set_uuid(const std::string& value) {
  
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GPUInfo.uuid)
}
inline void GPUInfo::set_uuid(std::string&& value) {
  
  uuid_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GPUInfo.uuid)
}
inline void GPUInfo::set_uuid(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GPUInfo.uuid)
}
inline void GPUInfo::set_uuid(const char* value,
    size_t size) {
  
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GPUInfo.uuid)
}
inline std::string* GPUInfo::mutable_uuid() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUInfo.uuid)
  return uuid_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GPUInfo::release_uuid() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUInfo.uuid)
  
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GPUInfo::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUInfo.uuid)
}
inline std::string* GPUInfo::unsafe_arena_release_uuid() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GPUInfo.uuid)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return uuid_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GPUInfo::unsafe_arena_set_allocated_uuid(
    std::string* uuid) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      uuid, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUInfo.uuid)
}

// string bus_id = 3;
inline void GPUInfo::clear_bus_id() {
  bus_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GPUInfo::bus_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUInfo.bus_id)
  return bus_id_.Get();
}
inline void GPUInfo::set_bus_id(const std::string& value) {
  
  bus_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GPUInfo.bus_id)
}
inline void GPUInfo::set_bus_id(std::string&& value) {
  
  bus_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GPUInfo.bus_id)
}
inline void GPUInfo::set_bus_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  bus_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GPUInfo.bus_id)
}
inline void GPUInfo::set_bus_id(const char* value,
    size_t size) {
  
  bus_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GPUInfo.bus_id)
}
inline std::string* GPUInfo::mutable_bus_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUInfo.bus_id)
  return bus_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GPUInfo::release_bus_id() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUInfo.bus_id)
  
  return bus_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GPUInfo::set_allocated_bus_id(std::string* bus_id) {
  if (bus_id != nullptr) {
    
  } else {
    
  }
  bus_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bus_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUInfo.bus_id)
}
inline std::string* GPUInfo::unsafe_arena_release_bus_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GPUInfo.bus_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return bus_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GPUInfo::unsafe_arena_set_allocated_bus_id(
    std::string* bus_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (bus_id != nullptr) {
    
  } else {
    
  }
  bus_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      bus_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUInfo.bus_id)
}

// -------------------------------------------------------------------

// PlatformInfo

// string bits = 1;
inline void PlatformInfo::clear_bits() {
  bits_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& PlatformInfo::bits() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.bits)
  return bits_.Get();
}
inline void PlatformInfo::set_bits(const std::string& value) {
  
  bits_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.bits)
}
inline void PlatformInfo::set_bits(std::string&& value) {
  
  bits_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.bits)
}
inline void PlatformInfo::set_bits(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  bits_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.bits)
}
inline void PlatformInfo::set_bits(const char* value,
    size_t size) {
  
  bits_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.bits)
}
inline std::string* PlatformInfo::mutable_bits() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.bits)
  return bits_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* PlatformInfo::release_bits() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.bits)
  
  return bits_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_bits(std::string* bits) {
  if (bits != nullptr) {
    
  } else {
    
  }
  bits_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bits,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.bits)
}
inline std::string* PlatformInfo::unsafe_arena_release_bits() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.bits)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return bits_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_bits(
    std::string* bits) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (bits != nullptr) {
    
  } else {
    
  }
  bits_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      bits, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.bits)
}

// string linkage = 2;
inline void PlatformInfo::clear_linkage() {
  linkage_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& PlatformInfo::linkage() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.linkage)
  return linkage_.Get();
}
inline void PlatformInfo::set_linkage(const std::string& value) {
  
  linkage_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.linkage)
}
inline void PlatformInfo::set_linkage(std::string&& value) {
  
  linkage_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.linkage)
}
inline void PlatformInfo::set_linkage(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  linkage_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.linkage)
}
inline void PlatformInfo::set_linkage(const char* value,
    size_t size) {
  
  linkage_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.linkage)
}
inline std::string* PlatformInfo::mutable_linkage() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.linkage)
  return linkage_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* PlatformInfo::release_linkage() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.linkage)
  
  return linkage_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_linkage(std::string* linkage) {
  if (linkage != nullptr) {
    
  } else {
    
  }
  linkage_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), linkage,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.linkage)
}
inline std::string* PlatformInfo::unsafe_arena_release_linkage() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.linkage)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return linkage_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_linkage(
    std::string* linkage) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (linkage != nullptr) {
    
  } else {
    
  }
  linkage_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      linkage, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.linkage)
}

// string machine = 3;
inline void PlatformInfo::clear_machine() {
  machine_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& PlatformInfo::machine() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.machine)
  return machine_.Get();
}
inline void PlatformInfo::set_machine(const std::string& value) {
  
  machine_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.machine)
}
inline void PlatformInfo::set_machine(std::string&& value) {
  
  machine_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.machine)
}
inline void PlatformInfo::set_machine(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  machine_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.machine)
}
inline void PlatformInfo::set_machine(const char* value,
    size_t size) {
  
  machine_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.machine)
}
inline std::string* PlatformInfo::mutable_machine() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.machine)
  return machine_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* PlatformInfo::release_machine() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.machine)
  
  return machine_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_machine(std::string* machine) {
  if (machine != nullptr) {
    
  } else {
    
  }
  machine_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), machine,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.machine)
}
inline std::string* PlatformInfo::unsafe_arena_release_machine() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.machine)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return machine_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_machine(
    std::string* machine) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (machine != nullptr) {
    
  } else {
    
  }
  machine_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      machine, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.machine)
}

// string release = 4;
inline void PlatformInfo::clear_release() {
  release_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& PlatformInfo::release() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.release)
  return release_.Get();
}
inline void PlatformInfo::set_release(const std::string& value) {
  
  release_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.release)
}
inline void PlatformInfo::set_release(std::string&& value) {
  
  release_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.release)
}
inline void PlatformInfo::set_release(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  release_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.release)
}
inline void PlatformInfo::set_release(const char* value,
    size_t size) {
  
  release_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.release)
}
inline std::string* PlatformInfo::mutable_release() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.release)
  return release_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* PlatformInfo::release_release() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.release)
  
  return release_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_release(std::string* release) {
  if (release != nullptr) {
    
  } else {
    
  }
  release_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), release,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.release)
}
inline std::string* PlatformInfo::unsafe_arena_release_release() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.release)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return release_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_release(
    std::string* release) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (release != nullptr) {
    
  } else {
    
  }
  release_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      release, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.release)
}

// string system = 5;
inline void PlatformInfo::clear_system() {
  system_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& PlatformInfo::system() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.system)
  return system_.Get();
}
inline void PlatformInfo::set_system(const std::string& value) {
  
  system_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.system)
}
inline void PlatformInfo::set_system(std::string&& value) {
  
  system_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.system)
}
inline void PlatformInfo::set_system(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  system_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.system)
}
inline void PlatformInfo::set_system(const char* value,
    size_t size) {
  
  system_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.system)
}
inline std::string* PlatformInfo::mutable_system() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.system)
  return system_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* PlatformInfo::release_system() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.system)
  
  return system_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_system(std::string* system) {
  if (system != nullptr) {
    
  } else {
    
  }
  system_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), system,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.system)
}
inline std::string* PlatformInfo::unsafe_arena_release_system() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.system)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return system_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_system(
    std::string* system) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (system != nullptr) {
    
  } else {
    
  }
  system_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      system, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.system)
}

// string version = 6;
inline void PlatformInfo::clear_version() {
  version_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& PlatformInfo::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.version)
  return version_.Get();
}
inline void PlatformInfo::set_version(const std::string& value) {
  
  version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.version)
}
inline void PlatformInfo::set_version(std::string&& value) {
  
  version_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PlatformInfo.version)
}
inline void PlatformInfo::set_version(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PlatformInfo.version)
}
inline void PlatformInfo::set_version(const char* value,
    size_t size) {
  
  version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PlatformInfo.version)
}
inline std::string* PlatformInfo::mutable_version() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.version)
  return version_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* PlatformInfo::release_version() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.version)
  
  return version_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PlatformInfo::set_allocated_version(std::string* version) {
  if (version != nullptr) {
    
  } else {
    
  }
  version_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), version,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.version)
}
inline std::string* PlatformInfo::unsafe_arena_release_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PlatformInfo.version)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return version_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PlatformInfo::unsafe_arena_set_allocated_version(
    std::string* version) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (version != nullptr) {
    
  } else {
    
  }
  version_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      version, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PlatformInfo.version)
}

// -------------------------------------------------------------------

// AvailableDeviceInfo

// string name = 1;
inline void AvailableDeviceInfo::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& AvailableDeviceInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.name)
  return name_.Get();
}
inline void AvailableDeviceInfo::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.name)
}
inline void AvailableDeviceInfo::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AvailableDeviceInfo.name)
}
inline void AvailableDeviceInfo::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AvailableDeviceInfo.name)
}
inline void AvailableDeviceInfo::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AvailableDeviceInfo.name)
}
inline std::string* AvailableDeviceInfo::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AvailableDeviceInfo.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* AvailableDeviceInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.AvailableDeviceInfo.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AvailableDeviceInfo.name)
}
inline std::string* AvailableDeviceInfo::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AvailableDeviceInfo.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AvailableDeviceInfo.name)
}

// string type = 2;
inline void AvailableDeviceInfo::clear_type() {
  type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& AvailableDeviceInfo::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.type)
  return type_.Get();
}
inline void AvailableDeviceInfo::set_type(const std::string& value) {
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.type)
}
inline void AvailableDeviceInfo::set_type(std::string&& value) {
  
  type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AvailableDeviceInfo.type)
}
inline void AvailableDeviceInfo::set_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AvailableDeviceInfo.type)
}
inline void AvailableDeviceInfo::set_type(const char* value,
    size_t size) {
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AvailableDeviceInfo.type)
}
inline std::string* AvailableDeviceInfo::mutable_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AvailableDeviceInfo.type)
  return type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* AvailableDeviceInfo::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.AvailableDeviceInfo.type)
  
  return type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AvailableDeviceInfo.type)
}
inline std::string* AvailableDeviceInfo::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AvailableDeviceInfo.type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::unsafe_arena_set_allocated_type(
    std::string* type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (type != nullptr) {
    
  } else {
    
  }
  type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AvailableDeviceInfo.type)
}

// int64 memory_limit = 3;
inline void AvailableDeviceInfo::clear_memory_limit() {
  memory_limit_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AvailableDeviceInfo::memory_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.memory_limit)
  return memory_limit_;
}
inline void AvailableDeviceInfo::set_memory_limit(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  memory_limit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.memory_limit)
}

// string physical_description = 4;
inline void AvailableDeviceInfo::clear_physical_description() {
  physical_description_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& AvailableDeviceInfo::physical_description() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.physical_description)
  return physical_description_.Get();
}
inline void AvailableDeviceInfo::set_physical_description(const std::string& value) {
  
  physical_description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.physical_description)
}
inline void AvailableDeviceInfo::set_physical_description(std::string&& value) {
  
  physical_description_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AvailableDeviceInfo.physical_description)
}
inline void AvailableDeviceInfo::set_physical_description(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  physical_description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AvailableDeviceInfo.physical_description)
}
inline void AvailableDeviceInfo::set_physical_description(const char* value,
    size_t size) {
  
  physical_description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AvailableDeviceInfo.physical_description)
}
inline std::string* AvailableDeviceInfo::mutable_physical_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AvailableDeviceInfo.physical_description)
  return physical_description_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* AvailableDeviceInfo::release_physical_description() {
  // @@protoc_insertion_point(field_release:tensorflow.AvailableDeviceInfo.physical_description)
  
  return physical_description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::set_allocated_physical_description(std::string* physical_description) {
  if (physical_description != nullptr) {
    
  } else {
    
  }
  physical_description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), physical_description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AvailableDeviceInfo.physical_description)
}
inline std::string* AvailableDeviceInfo::unsafe_arena_release_physical_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AvailableDeviceInfo.physical_description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return physical_description_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void AvailableDeviceInfo::unsafe_arena_set_allocated_physical_description(
    std::string* physical_description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (physical_description != nullptr) {
    
  } else {
    
  }
  physical_description_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      physical_description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AvailableDeviceInfo.physical_description)
}

// -------------------------------------------------------------------

// MachineConfiguration

// string hostname = 1;
inline void MachineConfiguration::clear_hostname() {
  hostname_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MachineConfiguration::hostname() const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.hostname)
  return hostname_.Get();
}
inline void MachineConfiguration::set_hostname(const std::string& value) {
  
  hostname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MachineConfiguration.hostname)
}
inline void MachineConfiguration::set_hostname(std::string&& value) {
  
  hostname_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MachineConfiguration.hostname)
}
inline void MachineConfiguration::set_hostname(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  hostname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MachineConfiguration.hostname)
}
inline void MachineConfiguration::set_hostname(const char* value,
    size_t size) {
  
  hostname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MachineConfiguration.hostname)
}
inline std::string* MachineConfiguration::mutable_hostname() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.hostname)
  return hostname_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MachineConfiguration::release_hostname() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.hostname)
  
  return hostname_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MachineConfiguration::set_allocated_hostname(std::string* hostname) {
  if (hostname != nullptr) {
    
  } else {
    
  }
  hostname_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), hostname,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.hostname)
}
inline std::string* MachineConfiguration::unsafe_arena_release_hostname() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MachineConfiguration.hostname)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return hostname_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MachineConfiguration::unsafe_arena_set_allocated_hostname(
    std::string* hostname) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (hostname != nullptr) {
    
  } else {
    
  }
  hostname_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      hostname, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MachineConfiguration.hostname)
}

// string serial_identifier = 7;
inline void MachineConfiguration::clear_serial_identifier() {
  serial_identifier_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MachineConfiguration::serial_identifier() const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.serial_identifier)
  return serial_identifier_.Get();
}
inline void MachineConfiguration::set_serial_identifier(const std::string& value) {
  
  serial_identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MachineConfiguration.serial_identifier)
}
inline void MachineConfiguration::set_serial_identifier(std::string&& value) {
  
  serial_identifier_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MachineConfiguration.serial_identifier)
}
inline void MachineConfiguration::set_serial_identifier(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  serial_identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MachineConfiguration.serial_identifier)
}
inline void MachineConfiguration::set_serial_identifier(const char* value,
    size_t size) {
  
  serial_identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MachineConfiguration.serial_identifier)
}
inline std::string* MachineConfiguration::mutable_serial_identifier() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.serial_identifier)
  return serial_identifier_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MachineConfiguration::release_serial_identifier() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.serial_identifier)
  
  return serial_identifier_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MachineConfiguration::set_allocated_serial_identifier(std::string* serial_identifier) {
  if (serial_identifier != nullptr) {
    
  } else {
    
  }
  serial_identifier_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serial_identifier,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.serial_identifier)
}
inline std::string* MachineConfiguration::unsafe_arena_release_serial_identifier() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MachineConfiguration.serial_identifier)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return serial_identifier_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MachineConfiguration::unsafe_arena_set_allocated_serial_identifier(
    std::string* serial_identifier) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (serial_identifier != nullptr) {
    
  } else {
    
  }
  serial_identifier_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      serial_identifier, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MachineConfiguration.serial_identifier)
}

// .tensorflow.PlatformInfo platform_info = 2;
inline bool MachineConfiguration::has_platform_info() const {
  return this != internal_default_instance() && platform_info_ != nullptr;
}
inline void MachineConfiguration::clear_platform_info() {
  if (GetArenaNoVirtual() == nullptr && platform_info_ != nullptr) {
    delete platform_info_;
  }
  platform_info_ = nullptr;
}
inline const ::tensorflow::PlatformInfo& MachineConfiguration::platform_info() const {
  const ::tensorflow::PlatformInfo* p = platform_info_;
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.platform_info)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::PlatformInfo*>(
      &::tensorflow::_PlatformInfo_default_instance_);
}
inline ::tensorflow::PlatformInfo* MachineConfiguration::release_platform_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.platform_info)
  
  ::tensorflow::PlatformInfo* temp = platform_info_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  platform_info_ = nullptr;
  return temp;
}
inline ::tensorflow::PlatformInfo* MachineConfiguration::unsafe_arena_release_platform_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MachineConfiguration.platform_info)
  
  ::tensorflow::PlatformInfo* temp = platform_info_;
  platform_info_ = nullptr;
  return temp;
}
inline ::tensorflow::PlatformInfo* MachineConfiguration::mutable_platform_info() {
  
  if (platform_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::PlatformInfo>(GetArenaNoVirtual());
    platform_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.platform_info)
  return platform_info_;
}
inline void MachineConfiguration::set_allocated_platform_info(::tensorflow::PlatformInfo* platform_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete platform_info_;
  }
  if (platform_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(platform_info);
    if (message_arena != submessage_arena) {
      platform_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, platform_info, submessage_arena);
    }
    
  } else {
    
  }
  platform_info_ = platform_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.platform_info)
}

// .tensorflow.CPUInfo cpu_info = 3;
inline bool MachineConfiguration::has_cpu_info() const {
  return this != internal_default_instance() && cpu_info_ != nullptr;
}
inline void MachineConfiguration::clear_cpu_info() {
  if (GetArenaNoVirtual() == nullptr && cpu_info_ != nullptr) {
    delete cpu_info_;
  }
  cpu_info_ = nullptr;
}
inline const ::tensorflow::CPUInfo& MachineConfiguration::cpu_info() const {
  const ::tensorflow::CPUInfo* p = cpu_info_;
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.cpu_info)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::CPUInfo*>(
      &::tensorflow::_CPUInfo_default_instance_);
}
inline ::tensorflow::CPUInfo* MachineConfiguration::release_cpu_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.cpu_info)
  
  ::tensorflow::CPUInfo* temp = cpu_info_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  cpu_info_ = nullptr;
  return temp;
}
inline ::tensorflow::CPUInfo* MachineConfiguration::unsafe_arena_release_cpu_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MachineConfiguration.cpu_info)
  
  ::tensorflow::CPUInfo* temp = cpu_info_;
  cpu_info_ = nullptr;
  return temp;
}
inline ::tensorflow::CPUInfo* MachineConfiguration::mutable_cpu_info() {
  
  if (cpu_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CPUInfo>(GetArenaNoVirtual());
    cpu_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.cpu_info)
  return cpu_info_;
}
inline void MachineConfiguration::set_allocated_cpu_info(::tensorflow::CPUInfo* cpu_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete cpu_info_;
  }
  if (cpu_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(cpu_info);
    if (message_arena != submessage_arena) {
      cpu_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cpu_info, submessage_arena);
    }
    
  } else {
    
  }
  cpu_info_ = cpu_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.cpu_info)
}

// repeated .google.protobuf.Any device_info = 4;
inline int MachineConfiguration::device_info_size() const {
  return device_info_.size();
}
inline PROTOBUF_NAMESPACE_ID::Any* MachineConfiguration::mutable_device_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.device_info)
  return device_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< PROTOBUF_NAMESPACE_ID::Any >*
MachineConfiguration::mutable_device_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MachineConfiguration.device_info)
  return &device_info_;
}
inline const PROTOBUF_NAMESPACE_ID::Any& MachineConfiguration::device_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.device_info)
  return device_info_.Get(index);
}
inline PROTOBUF_NAMESPACE_ID::Any* MachineConfiguration::add_device_info() {
  // @@protoc_insertion_point(field_add:tensorflow.MachineConfiguration.device_info)
  return device_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< PROTOBUF_NAMESPACE_ID::Any >&
MachineConfiguration::device_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.MachineConfiguration.device_info)
  return device_info_;
}

// repeated .tensorflow.AvailableDeviceInfo available_device_info = 5;
inline int MachineConfiguration::available_device_info_size() const {
  return available_device_info_.size();
}
inline void MachineConfiguration::clear_available_device_info() {
  available_device_info_.Clear();
}
inline ::tensorflow::AvailableDeviceInfo* MachineConfiguration::mutable_available_device_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.available_device_info)
  return available_device_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >*
MachineConfiguration::mutable_available_device_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MachineConfiguration.available_device_info)
  return &available_device_info_;
}
inline const ::tensorflow::AvailableDeviceInfo& MachineConfiguration::available_device_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.available_device_info)
  return available_device_info_.Get(index);
}
inline ::tensorflow::AvailableDeviceInfo* MachineConfiguration::add_available_device_info() {
  // @@protoc_insertion_point(field_add:tensorflow.MachineConfiguration.available_device_info)
  return available_device_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >&
MachineConfiguration::available_device_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.MachineConfiguration.available_device_info)
  return available_device_info_;
}

// .tensorflow.MemoryInfo memory_info = 6;
inline bool MachineConfiguration::has_memory_info() const {
  return this != internal_default_instance() && memory_info_ != nullptr;
}
inline void MachineConfiguration::clear_memory_info() {
  if (GetArenaNoVirtual() == nullptr && memory_info_ != nullptr) {
    delete memory_info_;
  }
  memory_info_ = nullptr;
}
inline const ::tensorflow::MemoryInfo& MachineConfiguration::memory_info() const {
  const ::tensorflow::MemoryInfo* p = memory_info_;
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.memory_info)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::MemoryInfo*>(
      &::tensorflow::_MemoryInfo_default_instance_);
}
inline ::tensorflow::MemoryInfo* MachineConfiguration::release_memory_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.memory_info)
  
  ::tensorflow::MemoryInfo* temp = memory_info_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  memory_info_ = nullptr;
  return temp;
}
inline ::tensorflow::MemoryInfo* MachineConfiguration::unsafe_arena_release_memory_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MachineConfiguration.memory_info)
  
  ::tensorflow::MemoryInfo* temp = memory_info_;
  memory_info_ = nullptr;
  return temp;
}
inline ::tensorflow::MemoryInfo* MachineConfiguration::mutable_memory_info() {
  
  if (memory_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::MemoryInfo>(GetArenaNoVirtual());
    memory_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.memory_info)
  return memory_info_;
}
inline void MachineConfiguration::set_allocated_memory_info(::tensorflow::MemoryInfo* memory_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete memory_info_;
  }
  if (memory_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(memory_info);
    if (message_arena != submessage_arena) {
      memory_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, memory_info, submessage_arena);
    }
    
  } else {
    
  }
  memory_info_ = memory_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.memory_info)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// RunConfiguration

// repeated string argument = 1;
inline int RunConfiguration::argument_size() const {
  return argument_.size();
}
inline void RunConfiguration::clear_argument() {
  argument_.Clear();
}
inline const std::string& RunConfiguration::argument(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunConfiguration.argument)
  return argument_.Get(index);
}
inline std::string* RunConfiguration::mutable_argument(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunConfiguration.argument)
  return argument_.Mutable(index);
}
inline void RunConfiguration::set_argument(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunConfiguration.argument)
  argument_.Mutable(index)->assign(value);
}
inline void RunConfiguration::set_argument(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunConfiguration.argument)
  argument_.Mutable(index)->assign(std::move(value));
}
inline void RunConfiguration::set_argument(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  argument_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::set_argument(int index, const char* value, size_t size) {
  argument_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunConfiguration.argument)
}
inline std::string* RunConfiguration::add_argument() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunConfiguration.argument)
  return argument_.Add();
}
inline void RunConfiguration::add_argument(const std::string& value) {
  argument_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::add_argument(std::string&& value) {
  argument_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::add_argument(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  argument_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::add_argument(const char* value, size_t size) {
  argument_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunConfiguration.argument)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RunConfiguration::argument() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunConfiguration.argument)
  return argument_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RunConfiguration::mutable_argument() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunConfiguration.argument)
  return &argument_;
}

// map<string, string> env_vars = 2;
inline int RunConfiguration::env_vars_size() const {
  return env_vars_.size();
}
inline void RunConfiguration::clear_env_vars() {
  env_vars_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
RunConfiguration::env_vars() const {
  // @@protoc_insertion_point(field_map:tensorflow.RunConfiguration.env_vars)
  return env_vars_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
RunConfiguration::mutable_env_vars() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.RunConfiguration.env_vars)
  return env_vars_.MutableMap();
}

// -------------------------------------------------------------------

// TestResults

// string target = 1;
inline void TestResults::clear_target() {
  target_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TestResults::target() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.target)
  return target_.Get();
}
inline void TestResults::set_target(const std::string& value) {
  
  target_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.target)
}
inline void TestResults::set_target(std::string&& value) {
  
  target_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TestResults.target)
}
inline void TestResults::set_target(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  target_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TestResults.target)
}
inline void TestResults::set_target(const char* value,
    size_t size) {
  
  target_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TestResults.target)
}
inline std::string* TestResults::mutable_target() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.target)
  return target_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TestResults::release_target() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.target)
  
  return target_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TestResults::set_allocated_target(std::string* target) {
  if (target != nullptr) {
    
  } else {
    
  }
  target_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), target,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.target)
}
inline std::string* TestResults::unsafe_arena_release_target() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.target)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return target_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestResults::unsafe_arena_set_allocated_target(
    std::string* target) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (target != nullptr) {
    
  } else {
    
  }
  target_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      target, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.target)
}

// .tensorflow.BenchmarkEntries entries = 2;
inline bool TestResults::has_entries() const {
  return this != internal_default_instance() && entries_ != nullptr;
}
inline void TestResults::clear_entries() {
  if (GetArenaNoVirtual() == nullptr && entries_ != nullptr) {
    delete entries_;
  }
  entries_ = nullptr;
}
inline const ::tensorflow::BenchmarkEntries& TestResults::entries() const {
  const ::tensorflow::BenchmarkEntries* p = entries_;
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.entries)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::BenchmarkEntries*>(
      &::tensorflow::_BenchmarkEntries_default_instance_);
}
inline ::tensorflow::BenchmarkEntries* TestResults::release_entries() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.entries)
  
  ::tensorflow::BenchmarkEntries* temp = entries_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  entries_ = nullptr;
  return temp;
}
inline ::tensorflow::BenchmarkEntries* TestResults::unsafe_arena_release_entries() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.entries)
  
  ::tensorflow::BenchmarkEntries* temp = entries_;
  entries_ = nullptr;
  return temp;
}
inline ::tensorflow::BenchmarkEntries* TestResults::mutable_entries() {
  
  if (entries_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::BenchmarkEntries>(GetArenaNoVirtual());
    entries_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.entries)
  return entries_;
}
inline void TestResults::set_allocated_entries(::tensorflow::BenchmarkEntries* entries) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete entries_;
  }
  if (entries) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(entries);
    if (message_arena != submessage_arena) {
      entries = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, entries, submessage_arena);
    }
    
  } else {
    
  }
  entries_ = entries;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.entries)
}

// .tensorflow.BuildConfiguration build_configuration = 3;
inline bool TestResults::has_build_configuration() const {
  return this != internal_default_instance() && build_configuration_ != nullptr;
}
inline void TestResults::clear_build_configuration() {
  if (GetArenaNoVirtual() == nullptr && build_configuration_ != nullptr) {
    delete build_configuration_;
  }
  build_configuration_ = nullptr;
}
inline const ::tensorflow::BuildConfiguration& TestResults::build_configuration() const {
  const ::tensorflow::BuildConfiguration* p = build_configuration_;
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.build_configuration)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::BuildConfiguration*>(
      &::tensorflow::_BuildConfiguration_default_instance_);
}
inline ::tensorflow::BuildConfiguration* TestResults::release_build_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.build_configuration)
  
  ::tensorflow::BuildConfiguration* temp = build_configuration_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  build_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::BuildConfiguration* TestResults::unsafe_arena_release_build_configuration() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.build_configuration)
  
  ::tensorflow::BuildConfiguration* temp = build_configuration_;
  build_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::BuildConfiguration* TestResults::mutable_build_configuration() {
  
  if (build_configuration_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::BuildConfiguration>(GetArenaNoVirtual());
    build_configuration_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.build_configuration)
  return build_configuration_;
}
inline void TestResults::set_allocated_build_configuration(::tensorflow::BuildConfiguration* build_configuration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete build_configuration_;
  }
  if (build_configuration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(build_configuration);
    if (message_arena != submessage_arena) {
      build_configuration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, build_configuration, submessage_arena);
    }
    
  } else {
    
  }
  build_configuration_ = build_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.build_configuration)
}

// .tensorflow.CommitId commit_id = 4;
inline bool TestResults::has_commit_id() const {
  return this != internal_default_instance() && commit_id_ != nullptr;
}
inline void TestResults::clear_commit_id() {
  if (GetArenaNoVirtual() == nullptr && commit_id_ != nullptr) {
    delete commit_id_;
  }
  commit_id_ = nullptr;
}
inline const ::tensorflow::CommitId& TestResults::commit_id() const {
  const ::tensorflow::CommitId* p = commit_id_;
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.commit_id)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::CommitId*>(
      &::tensorflow::_CommitId_default_instance_);
}
inline ::tensorflow::CommitId* TestResults::release_commit_id() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.commit_id)
  
  ::tensorflow::CommitId* temp = commit_id_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  commit_id_ = nullptr;
  return temp;
}
inline ::tensorflow::CommitId* TestResults::unsafe_arena_release_commit_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.commit_id)
  
  ::tensorflow::CommitId* temp = commit_id_;
  commit_id_ = nullptr;
  return temp;
}
inline ::tensorflow::CommitId* TestResults::mutable_commit_id() {
  
  if (commit_id_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CommitId>(GetArenaNoVirtual());
    commit_id_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.commit_id)
  return commit_id_;
}
inline void TestResults::set_allocated_commit_id(::tensorflow::CommitId* commit_id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete commit_id_;
  }
  if (commit_id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(commit_id);
    if (message_arena != submessage_arena) {
      commit_id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, commit_id, submessage_arena);
    }
    
  } else {
    
  }
  commit_id_ = commit_id;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.commit_id)
}

// int64 start_time = 5;
inline void TestResults::clear_start_time() {
  start_time_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TestResults::start_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.start_time)
  return start_time_;
}
inline void TestResults::set_start_time(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  start_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.start_time)
}

// double run_time = 6;
inline void TestResults::clear_run_time() {
  run_time_ = 0;
}
inline double TestResults::run_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.run_time)
  return run_time_;
}
inline void TestResults::set_run_time(double value) {
  
  run_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.run_time)
}

// .tensorflow.MachineConfiguration machine_configuration = 7;
inline bool TestResults::has_machine_configuration() const {
  return this != internal_default_instance() && machine_configuration_ != nullptr;
}
inline void TestResults::clear_machine_configuration() {
  if (GetArenaNoVirtual() == nullptr && machine_configuration_ != nullptr) {
    delete machine_configuration_;
  }
  machine_configuration_ = nullptr;
}
inline const ::tensorflow::MachineConfiguration& TestResults::machine_configuration() const {
  const ::tensorflow::MachineConfiguration* p = machine_configuration_;
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.machine_configuration)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::MachineConfiguration*>(
      &::tensorflow::_MachineConfiguration_default_instance_);
}
inline ::tensorflow::MachineConfiguration* TestResults::release_machine_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.machine_configuration)
  
  ::tensorflow::MachineConfiguration* temp = machine_configuration_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  machine_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::MachineConfiguration* TestResults::unsafe_arena_release_machine_configuration() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.machine_configuration)
  
  ::tensorflow::MachineConfiguration* temp = machine_configuration_;
  machine_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::MachineConfiguration* TestResults::mutable_machine_configuration() {
  
  if (machine_configuration_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::MachineConfiguration>(GetArenaNoVirtual());
    machine_configuration_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.machine_configuration)
  return machine_configuration_;
}
inline void TestResults::set_allocated_machine_configuration(::tensorflow::MachineConfiguration* machine_configuration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete machine_configuration_;
  }
  if (machine_configuration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(machine_configuration);
    if (message_arena != submessage_arena) {
      machine_configuration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, machine_configuration, submessage_arena);
    }
    
  } else {
    
  }
  machine_configuration_ = machine_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.machine_configuration)
}

// .tensorflow.RunConfiguration run_configuration = 8;
inline bool TestResults::has_run_configuration() const {
  return this != internal_default_instance() && run_configuration_ != nullptr;
}
inline void TestResults::clear_run_configuration() {
  if (GetArenaNoVirtual() == nullptr && run_configuration_ != nullptr) {
    delete run_configuration_;
  }
  run_configuration_ = nullptr;
}
inline const ::tensorflow::RunConfiguration& TestResults::run_configuration() const {
  const ::tensorflow::RunConfiguration* p = run_configuration_;
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.run_configuration)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::RunConfiguration*>(
      &::tensorflow::_RunConfiguration_default_instance_);
}
inline ::tensorflow::RunConfiguration* TestResults::release_run_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.run_configuration)
  
  ::tensorflow::RunConfiguration* temp = run_configuration_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  run_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::RunConfiguration* TestResults::unsafe_arena_release_run_configuration() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.run_configuration)
  
  ::tensorflow::RunConfiguration* temp = run_configuration_;
  run_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::RunConfiguration* TestResults::mutable_run_configuration() {
  
  if (run_configuration_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunConfiguration>(GetArenaNoVirtual());
    run_configuration_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.run_configuration)
  return run_configuration_;
}
inline void TestResults::set_allocated_run_configuration(::tensorflow::RunConfiguration* run_configuration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete run_configuration_;
  }
  if (run_configuration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(run_configuration);
    if (message_arena != submessage_arena) {
      run_configuration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, run_configuration, submessage_arena);
    }
    
  } else {
    
  }
  run_configuration_ = run_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.run_configuration)
}

// string name = 9;
inline void TestResults::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TestResults::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.name)
  return name_.Get();
}
inline void TestResults::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.name)
}
inline void TestResults::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TestResults.name)
}
inline void TestResults::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TestResults.name)
}
inline void TestResults::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TestResults.name)
}
inline std::string* TestResults::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TestResults::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TestResults::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.name)
}
inline std::string* TestResults::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestResults::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.name)
}

// .tensorflow.TestResults.BenchmarkType benchmark_type = 10;
inline void TestResults::clear_benchmark_type() {
  benchmark_type_ = 0;
}
inline ::tensorflow::TestResults_BenchmarkType TestResults::benchmark_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.benchmark_type)
  return static_cast< ::tensorflow::TestResults_BenchmarkType >(benchmark_type_);
}
inline void TestResults::set_benchmark_type(::tensorflow::TestResults_BenchmarkType value) {
  
  benchmark_type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.benchmark_type)
}

// string run_mode = 11;
inline void TestResults::clear_run_mode() {
  run_mode_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TestResults::run_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.run_mode)
  return run_mode_.Get();
}
inline void TestResults::set_run_mode(const std::string& value) {
  
  run_mode_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.run_mode)
}
inline void TestResults::set_run_mode(std::string&& value) {
  
  run_mode_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TestResults.run_mode)
}
inline void TestResults::set_run_mode(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  run_mode_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TestResults.run_mode)
}
inline void TestResults::set_run_mode(const char* value,
    size_t size) {
  
  run_mode_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TestResults.run_mode)
}
inline std::string* TestResults::mutable_run_mode() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.run_mode)
  return run_mode_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TestResults::release_run_mode() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.run_mode)
  
  return run_mode_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TestResults::set_allocated_run_mode(std::string* run_mode) {
  if (run_mode != nullptr) {
    
  } else {
    
  }
  run_mode_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), run_mode,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.run_mode)
}
inline std::string* TestResults::unsafe_arena_release_run_mode() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.run_mode)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return run_mode_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestResults::unsafe_arena_set_allocated_run_mode(
    std::string* run_mode) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (run_mode != nullptr) {
    
  } else {
    
  }
  run_mode_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      run_mode, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.run_mode)
}

// string tf_version = 12;
inline void TestResults::clear_tf_version() {
  tf_version_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TestResults::tf_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.tf_version)
  return tf_version_.Get();
}
inline void TestResults::set_tf_version(const std::string& value) {
  
  tf_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.tf_version)
}
inline void TestResults::set_tf_version(std::string&& value) {
  
  tf_version_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TestResults.tf_version)
}
inline void TestResults::set_tf_version(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  tf_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TestResults.tf_version)
}
inline void TestResults::set_tf_version(const char* value,
    size_t size) {
  
  tf_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TestResults.tf_version)
}
inline std::string* TestResults::mutable_tf_version() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.tf_version)
  return tf_version_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TestResults::release_tf_version() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.tf_version)
  
  return tf_version_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TestResults::set_allocated_tf_version(std::string* tf_version) {
  if (tf_version != nullptr) {
    
  } else {
    
  }
  tf_version_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tf_version,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.tf_version)
}
inline std::string* TestResults::unsafe_arena_release_tf_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TestResults.tf_version)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return tf_version_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestResults::unsafe_arena_set_allocated_tf_version(
    std::string* tf_version) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (tf_version != nullptr) {
    
  } else {
    
  }
  tf_version_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      tf_version, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.tf_version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::TestResults_BenchmarkType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::TestResults_BenchmarkType>() {
  return ::tensorflow::TestResults_BenchmarkType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2ftest_5flog_2eproto
