# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> Emre Al<PERSON> <<EMAIL>>, 2013
# BouRock, 2018
# BouRock, 2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2018-05-18 09:57+0000\n"
"Last-Translator: BouRock\n"
"Language-Team: Turkish (http://www.transifex.com/django/django/language/"
"tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Humanize"
msgstr "İnsansı"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}."

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}."

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f milyon"
msgstr[1] "%(value).1f milyon"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s milyon"
msgstr[1] "%(value)s milyon"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f milyar"
msgstr[1] "%(value).1f milyar"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s milyar"
msgstr[1] "%(value)s milyar"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f trilyon"
msgstr[1] "%(value).1f trilyon"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s trilyon"
msgstr[1] "%(value)s trilyon"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] "%(value).1f katrilyon"
msgstr[1] "%(value).1f katrilyon"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s katrilyon"
msgstr[1] "%(value)s katrilyon"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] "%(value).1f kentilyon"
msgstr[1] "%(value).1f kentilyon"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s kentilyon"
msgstr[1] "%(value)s kentilyon"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] "%(value).1f seksilyon"
msgstr[1] "%(value).1f seksilyon"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s seksilyon"
msgstr[1] "%(value)s seksilyon"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] "%(value).1f septilyon"
msgstr[1] "%(value).1f septilyon"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septilyon"
msgstr[1] "%(value)s septilyon"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] "%(value).1f oktilyon"
msgstr[1] "%(value).1f oktilyon"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s oktilyon"
msgstr[1] "%(value)s oktilyon"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] "%(value).1f nonilyon"
msgstr[1] "%(value).1f nonilyon"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s nonilyon"
msgstr[1] "%(value)s nonilyon"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] "%(value).1f desilyon"
msgstr[1] "%(value).1f desilyon"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s desilyon"
msgstr[1] "%(value)s desilyon"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] "%(value).1f googol"
msgstr[1] "%(value).1f googol"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googol"

msgid "one"
msgstr "bir"

msgid "two"
msgstr "iki"

msgid "three"
msgstr "üç"

msgid "four"
msgstr "dört"

msgid "five"
msgstr "beş"

msgid "six"
msgstr "altı"

msgid "seven"
msgstr "yedi"

msgid "eight"
msgstr "sekiz"

msgid "nine"
msgstr "dokuz"

msgid "today"
msgstr "bugün"

msgid "tomorrow"
msgstr "yarın"

msgid "yesterday"
msgstr "dün"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s önce"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "bir saat önce"
msgstr[1] "%(count)s saat önce"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "bir dakika önce"
msgstr[1] "%(count)s dakika önce"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "bir saniye önce"
msgstr[1] "%(count)s saniye önce"

msgid "now"
msgstr "şimdi"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "şu andan itibaren bir saniye"
msgstr[1] "şu andan itibaren %(count)s saniye"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "şu andan itibaren bir dakika"
msgstr[1] "şu andan itibaren %(count)s dakika"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "şu andan itibaren bir saat"
msgstr[1] "şu andan itibaren %(count)s saat"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "şu andan itibaren %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d yıl"
msgstr[1] "%d yıl"

#, python-format
msgctxt "naturaltime-past"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d ay"
msgstr[1] "%d ay"

#, python-format
msgctxt "naturaltime-past"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d hafta"
msgstr[1] "%d hafta"

#, python-format
msgctxt "naturaltime-past"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d gün"
msgstr[1] "%d gün"

#, python-format
msgctxt "naturaltime-past"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d saat"
msgstr[1] "%d saat"

#, python-format
msgctxt "naturaltime-past"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d dakika"
msgstr[1] "%d dakika"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d yıl"
msgstr[1] "%d yıl"

#, python-format
msgctxt "naturaltime-future"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d ay"
msgstr[1] "%d ay"

#, python-format
msgctxt "naturaltime-future"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d hafta"
msgstr[1] "%d hafta"

#, python-format
msgctxt "naturaltime-future"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d gün"
msgstr[1] "%d gün"

#, python-format
msgctxt "naturaltime-future"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d saat"
msgstr[1] "%d saat"

#, python-format
msgctxt "naturaltime-future"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d dakika"
msgstr[1] "%d dakika"
