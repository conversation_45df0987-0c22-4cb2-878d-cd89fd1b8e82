# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2011
# <AUTHOR> <EMAIL>, 2015,2019
# <PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2019-01-18 10:34+0000\n"
"Last-Translator: Matas Dailyda <<EMAIL>>\n"
"Language-Team: Lithuanian (http://www.transifex.com/django/django/language/"
"lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < "
"11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? "
"1 : n % 1 != 0 ? 2: 3);\n"

msgid "Advanced options"
msgstr "Sudėtingesni nustatymai"

msgid "Flat Pages"
msgstr "Paprasti puslapiai"

msgid "URL"
msgstr "Nuoroda"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"Pavyzdžiui: '/about/contact/'. Įsitikink, kad yra pasvirieji brūkšniai "
"pradžioj ir gale."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Ši reikšmė gali būti sudaryta tik iš raidžių, skaičių, pabraukimų, brūkšnių "
"ir/arba pasvirų brūkšnių."

msgid "Example: '/about/contact'. Make sure to have a leading slash."
msgstr ""
"Pavizdys: '/apie/kontaktai'. Įsitikinkite kad pradžioje būtų pasvirasis "
"brūkšnys."

msgid "URL is missing a leading slash."
msgstr "Nuorodos pradžioje trūksta pasvirojo brūkšnio."

msgid "URL is missing a trailing slash."
msgstr "Nuorodos pabaigoje trūksta pasvirojo brūkšnio."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Paprastas puslapis su adresu %(url)s %(site)s puslapyje jau egzistuoja"

msgid "title"
msgstr "pavadinimas"

msgid "content"
msgstr "turinys"

msgid "enable comments"
msgstr "įjungti komentavimą"

msgid "template name"
msgstr "šablono vardas"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"Pavyzdžiui: 'flatpages/contact_page.html'. Jeigu bus nenurodytas, sistema "
"naudos 'flatpages/default.html'."

msgid "registration required"
msgstr "registracija privaloma"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Jeigu pažymėta, tik prisijungę vartotojai galės matyti šį puslapį."

msgid "sites"
msgstr "tinklalapiai"

msgid "flat page"
msgstr "paprastas puslapis"

msgid "flat pages"
msgstr "paprasti puslapiai"
