#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试key1系列的创建过程
"""

import pandas as pd
import re

def extract_key_components(key_string):
    """从复杂的键字符串中提取关键组件"""
    # 提取客户ID (CUSTOMER后面的数字)
    customer_match = re.search(r'CUSTOMER(\d+)', key_string)
    customer_id = customer_match.group(1) if customer_match else ''
    
    # 提取完整的时间戳
    datetime_patterns = [
        r'_(\d{14})_',      # _20250401012111_
        r'/(\d{14})$',      # /20250401012111 (文件2格式，以数字结尾)
        r'/(\d{12})$',      # /202504010121 (文件2格式，12位)
        r'_(\d{12})_',      # _202504010121_
        r'_(\d{8})_',       # _20250401_
        r'/(\d{8})$',       # /20250401 (文件2格式，8位)
    ]
    
    date_part = ''
    for pattern in datetime_patterns:
        date_match = re.search(pattern, key_string)
        if date_match:
            date_part = date_match.group(1)
            break
    
    # 提取导联信息
    lead_patterns = [
        r'[Ll][Ee][Aa][Dd]([IVX]+)',  # LEADI, leadI等
        r'_([IVX]+)_[Aa][Cc][Cc]',    # _I_acc, _II_acc等
        r'/(\d+[IVX]+)',              # /20250401063945_I等中的I
    ]
    
    lead_part = ''
    for pattern in lead_patterns:
        lead_match = re.search(pattern, key_string)
        if lead_match:
            lead_part = lead_match.group(1)
            # 标准化导联名称
            lead_part = lead_part.replace('LEAD', '').replace('lead', '')
            break
    
    return customer_id, date_part, lead_part

def main():
    # 加载数据
    df1 = pd.read_csv(r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\10s10步长v2.0\合并结果.csv")
    df2 = pd.read_excel(r"D:\ECG\0723一分钟项目测试\标注平台数据\标注平台数据.xls")
    
    # 模拟增强版匹配器中的key1创建过程
    match_col = df1.columns[-1]
    key1_raw = df1[match_col].astype(str).str.strip()
    key1_raw = key1_raw.str.upper()  # 转换为大写
    
    print("创建key1系列...")
    
    # 解析文件1的键组件
    key1_components = []
    for key in key1_raw:
        customer_id, date_part, lead_part = extract_key_components(key)
        key1_components.append((customer_id, date_part, lead_part))
    
    # 创建完全匹配键
    key1_full = pd.Series([f"{comp[0]}_{comp[1]}_{comp[2]}" for comp in key1_components])
    
    print("创建映射字典...")
    
    # 解析文件2的键组件
    key2_components = []
    for _, row in df2.iterrows():
        es_key = str(row['es_key']).strip()
        lead = str(row['lead']).strip()
        
        customer_id, date_part, _ = extract_key_components(es_key)
        lead_normalized = lead.replace('LEAD', '').strip()
        
        key2_components.append((customer_id, date_part, lead_normalized))
    
    key2_full = pd.Series([f"{comp[0]}_{comp[1]}_{comp[2]}" for comp in key2_components])
    disease_mapping = dict(zip(key2_full, df2['disease_name']))
    
    print(f"映射字典大小: {len(disease_mapping)}")
    
    # 模拟匹配过程
    print("\n模拟匹配过程...")
    
    # 初始化结果列
    df1_copy = df1.copy()
    df1_copy['disease_name'] = pd.NA
    df1_copy['match_strategy'] = ''
    
    # 对每个记录进行匹配
    matched_count = 0
    for idx in df1_copy.index:
        key_value = key1_full.iloc[idx]  # 使用iloc确保索引正确
        if key_value in disease_mapping:
            matched_disease = disease_mapping[key_value]
            df1_copy.loc[idx, 'disease_name'] = matched_disease
            df1_copy.loc[idx, 'match_strategy'] = '严格匹配(客户ID+日期+导联)'
            matched_count += 1
    
    print(f"匹配成功: {matched_count}/{len(df1_copy)}")
    
    # 检查前5条记录的匹配结果
    print("\n前5条记录的匹配结果:")
    for i in range(5):
        original_source = df1_copy.iloc[i][match_col]
        key_value = key1_full.iloc[i]
        matched_disease = df1_copy.iloc[i]['disease_name']
        
        print(f"\n记录 {i}:")
        print(f"  原始: {original_source}")
        print(f"  键值: {key_value}")
        print(f"  疾病: {matched_disease}")
        
        # 验证映射是否正确
        expected_disease = disease_mapping.get(key_value, "未找到")
        if expected_disease == matched_disease:
            print(f"  ✅ 匹配正确")
        else:
            print(f"  ❌ 匹配错误! 期望: {expected_disease}")
    
    # 保存调试结果
    df1_copy.to_csv('debug_matching_result.csv', index=False, encoding='utf-8-sig')
    print(f"\n调试结果已保存到: debug_matching_result.csv")

if __name__ == "__main__":
    main()
