""" coding: utf-8 """
from django.db import connection

from apps.models.data_models import TArrhythmiaDict
from apps.models.report_model import AbnormalInfoModel


def get_signal_quality(datas, normal_data):
    """
    获取信号质量
    """
    # 计算占比
    if len(datas) > 0:
        efficiency = int((len(normal_data) / len(datas)) * 100)
    else:
        efficiency = 0

    if efficiency >= 75:
        signal_quality = '良好'
    elif efficiency >= 35:
        signal_quality = '一般'
    else:
        signal_quality = '差'

    return f'{signal_quality}（{efficiency}%）'


def statistic_abnormal_data(normal_data):
    """
    统计异常数据
    """
    arrhythmia_dicts = TArrhythmiaDict.objects.all()

    abnormal_infos = []

    for arrhythmia_dict in arrhythmia_dicts:
        if arrhythmia_dict.symptom_code != 'arrhythmiadiagnosis_sn' and arrhythmia_dict.symptom_code in normal_data.columns:
            abnormal_datas = normal_data[normal_data[arrhythmia_dict.symptom_code] == 1]

            if len(abnormal_datas) > 0:
                hr_mean = int(abnormal_datas['pqrstc_hr'].fillna(0).mean())
                abnormal_duration = round(len(abnormal_datas) * 10 / 60, 2)  # 分钟

                if abnormal_duration > 0:
                    heart_beat_count = int(hr_mean * abnormal_duration)  # 计算逻辑每个非窦性心律的数据 * 10 秒,最后给出心搏的分钟数

                    abnormal_info = AbnormalInfoModel()
                    abnormal_info.symptom = arrhythmia_dict.symptom_name
                    abnormal_info.heart_beat = heart_beat_count
                    abnormal_info.duration = abnormal_duration
                    abnormal_info.definition = arrhythmia_dict.definition
                    abnormal_info.advice = arrhythmia_dict.advice
                    abnormal_info.daily_attention = arrhythmia_dict.daily_attention

                    abnormal_infos.append(abnormal_info)

    return abnormal_infos


def get_snb_info(normal_data):
    """
    获取心动过缓信息
    """
    snb_count = len(normal_data[normal_data['arrhythmiadiagnosis_snb'] == 1])

    return snb_count, round(snb_count * 10 / 60, 2)


def get_report_advice(report_model, abnormal_infos):
    """
    获取报告建议
    """
    abnormal_infos_str = []

    # 获取报告建议
    report_info = f'这是一份用户24小时心电图数据 ，其中平均心率为{report_model.hr_mean}，最大心率为{report_model.hr_max}，最慢心率为{report_model.hr_min}'

    if report_model.abnormal_heart_beat > 0:
        report_info += f'，异常心搏数为{report_model.abnormal_heart_beat}'

    for abnormal_info in abnormal_infos:
        abnormal_infos_str.append(
            f'{abnormal_info.symptom}心搏数量为{abnormal_info.heart_beat}次，持续时间为{abnormal_info.duration}分钟')

    if len(abnormal_infos_str) > 0:
        report_info += f",{','.join(abnormal_infos_str)}"

    if report_model.snb_count > 0:
        report_info += f', 心动过缓次数为{report_model.snb_count}， 持续时间为{report_model.snb_duration}分钟'

    report_info += f', 情绪指数为{report_model.emotion}, 疲劳指数{report_model.fatigue}, 心率变异性{report_model.hrv}, 压力指数{report_model.pressure}, 活力指数{report_model.vitality}'

    report_info += '，根据这些数据给出建议，不要使用markdown格式。'

    return report_info
