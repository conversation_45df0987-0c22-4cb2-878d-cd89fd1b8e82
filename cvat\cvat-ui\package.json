{"name": "cvat-ui", "version": "2.32.1", "description": "CVAT single-page application", "main": "src/index.tsx", "scripts": {"build": "webpack --config ./webpack.config.js", "start": "webpack serve --env API_URL=http://localhost:7000 --config ./webpack.config.js --mode=development", "type-check": "tsc --noEmit", "type-check:watch": "yarn run type-check --watch", "lint": "eslint './src/**/*.{ts,tsx}'", "lint:fix": "eslint './src/**/*.{ts,tsx}' --fix"}, "browserslist": ["Chrome >= 63", "Firefox > 102", "not IE 11", "> 2%"], "author": "CVAT.ai", "license": "MIT", "dependencies": {"@ant-design/compatible": "^5.1.2", "@ant-design/icons": "^5.5.2", "@react-awesome-query-builder/antd": "^6.5.2", "@types/json-logic-js": "^2.0.2", "@types/lru-cache": "^7.10.10", "@types/platform": "^1.3.4", "@types/react": "18.2.55", "@types/react-color": "^3.0.5", "@types/react-dom": "18.2.19", "@types/react-grid-layout": "^1.3.2", "@types/react-redux": "^7.1.18", "@types/react-router": "^5.1.16", "@types/react-router-dom": "^5.1.9", "@types/redux-logger": "^3.0.9", "@types/resize-observer-browser": "^0.1.6", "@uiw/react-md-editor": "^3.22.0", "antd": "5.17.1", "chart.js": "^4.4.8", "chartjs-plugin-datalabels": "^2.2.0", "cvat-canvas": "link:./../cvat-canvas", "cvat-canvas3d": "link:./../cvat-canvas3d", "cvat-core": "link:./../cvat-core", "dotenv-webpack": "^8.0.1", "error-stack-parser": "^2.0.6", "json-logic-js": "^2.0.2", "lru-cache": "^9.1.1", "moment": "^2.29.2", "mousetrap": "^1.6.5", "onnxruntime-web": "1.14.0", "platform": "^1.3.6", "prop-types": "^15.7.2", "react": "18.2.0", "react-chartjs-2": "^5.2.0", "react-color": "^2.19.3", "react-cookie": "^4.0.3", "react-dom": "18.2.0", "react-grid-layout": "^1.3.4", "react-markdown": "^8.0.4", "react-moment": "^1.1.1", "react-redux": "^8.0.2", "react-router": "^5.1.0", "react-router-dom": "^5.1.0", "react-sortable-hoc": "^2.0.0", "redux": "^4.1.1", "redux-devtools-extension": "^2.13.9", "redux-logger": "^4.0.0", "redux-thunk": "^2.3.0"}}