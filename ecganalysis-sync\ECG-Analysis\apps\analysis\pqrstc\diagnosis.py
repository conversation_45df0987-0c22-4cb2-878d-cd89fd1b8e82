from apps.models.analysis_models import PQRSTCEntity


def process(waveform_info):
    """
    ECG信号指标
    :param waveform_info: 波形信息
    :return: PQRSTCEntity对象或None
    """
    waveform = waveform_info['waveform']

    qrs_average = waveform['qrs_average']
    qt_average = waveform['qt_average']
    qtc_average = waveform['qtc_average']
    pp_average = waveform['pp_average']
    pr_average = waveform['pr_average']
    st_average = waveform['st_average']

    pqrstc = PQRSTCEntity()

    pqrstc.HR = waveform_info['heart_rate']['hr']
    pqrstc.QRS_QRSDuration = int(round(qrs_average * 1000, 5))
    pqrstc.QT = int(round(qt_average * 1000, 5))
    pqrstc.QTc = int(round(qtc_average * 1000, 5))

    p_duration = int(round(pp_average * 1000, 5))
    pqrstc.P_duration = int(p_duration if 70 <= p_duration <= 150 else 154.5)

    pqrstc.PR_interval = int(round(pr_average * 1000, 5))
    pqrstc.ST_duration = int(round(st_average * 1000, 5))

    pqrstc.NN_MAX = waveform['nn_max']
    pqrstc.NN_MIN = waveform['nn_min']
    pqrstc.RR_MAX = waveform['rr_max']
    pqrstc.RR_MIN = waveform['rr_min']

    return pqrstc
