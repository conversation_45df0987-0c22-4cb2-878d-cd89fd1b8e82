// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-models-page {
    padding-top: $grid-unit-size * 2;
    padding-bottom: $grid-unit-size;
    overflow: auto;
    height: 100%;
    width: 100%;

    .cvat-models-page-top-bar {
        padding-bottom: $grid-unit-size;
    }
}

.cvat-empty-models-list {
    .ant-empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}

.cvat-models-list {
    display: flex;
    flex-wrap: wrap;

    > div {
        width: 100%;
        margin-bottom: $grid-unit-size;

        > div:not(:first-child) {
            padding-left: $grid-unit-size;
        }
    }
}

.cvat-models-item-card-removed {
    opacity: 0.5;
    pointer-events: none;
}

.cvat-models-page-top-bar {
    .cvat-models-page-search-bar {
        width: $grid-unit-size * 32;
    }

    > div {
        display: flex;
    }
}

.cvat-models-heading {
    padding: $grid-unit-size * 2;
}

.cvat-models-page-filters-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    > div {
        display: flex;

        > button {
            margin-right: $grid-unit-size;
        }
    }
}

.cvat-model-delete {
    position: absolute;
    top: $grid-unit-size;
    right: $grid-unit-size;
    color: #8c8c8c;
    font-size: 10px;

    &:hover {
        cursor: pointer;
        color: #595959;
    }
}

.cvat-models-item-card {
    height: $grid-unit-size * 28;
    overflow: hidden;

    .ant-card-meta-title {
        margin-bottom: 0 !important;
    }

    &:nth-child(4n) {
        border-right: 0;
    }

    .cvat-models-item-card-preview-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        height: $grid-unit-size * 18;
        overflow: hidden;

        &:hover {
            cursor: pointer;
        }
    }
}

.cvat-model-item-loading-preview,
.cvat-model-item-empty-preview {
    @extend .cvat-base-preview;

    height: $grid-unit-size * 18;

    &:hover {
        cursor: pointer;
    }
}

.cvat-model-item-loading-preview {
    padding-top: $grid-unit-size * 3;
}

.cvat-model-item-top-bar {
    width: 100%;
    position: absolute;
    display: flex;
    justify-content: flex-end;
    top: 0;
    left: 0;
    padding: $grid-unit-size;
}

.cvat-models-item-description {
    font-size: 14px;
    display: flex;
    justify-content: space-between;

    > div > span:nth-child(2) {
        margin-left: $grid-unit-size;
    }

    button {
        position: relative;
        color: black;
        margin-top: -$grid-unit-size * 2;
        margin-right: -$grid-unit-size;
    }

    &:hover {
        cursor: pointer;
    }
}

.cvat-model-info-modal {
    .ant-modal-body {
        position: relative;
        padding: 0;

        >.cvat-model-info-container:not(:last-child) {
            padding: 0 $grid-unit-size * 3;
        }

        >.cvat-model-info-container:last-child {
            padding: 0 $grid-unit-size * 3 $grid-unit-size * 3 $grid-unit-size * 3;
        }
    }

    h3 {
        margin-top: $grid-unit-size * 2;
    }
}

.cvat-model-info-modal-labels-title {
    font-size: 16px;
}

.cvat-model-info-modal-labels-list {
    margin-top: $grid-unit-size;
    max-height: $grid-unit-size * 18;
    overflow: auto;

    .ant-tag {
        margin-top: $grid-unit-size;
    }
}

.cvat-models-item-title {
    &:hover {
        cursor: pointer !important;
    }
}

.cvat-models-item-text-description {
    max-width: 80%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    max-height: $grid-unit-size * 3;
    display: inline;
}
