// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/device_filters.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto;
namespace tensorflow {
class ClusterDeviceFilters;
class ClusterDeviceFiltersDefaultTypeInternal;
extern ClusterDeviceFiltersDefaultTypeInternal _ClusterDeviceFilters_default_instance_;
class JobDeviceFilters;
class JobDeviceFiltersDefaultTypeInternal;
extern JobDeviceFiltersDefaultTypeInternal _JobDeviceFilters_default_instance_;
class JobDeviceFilters_TasksEntry_DoNotUse;
class JobDeviceFilters_TasksEntry_DoNotUseDefaultTypeInternal;
extern JobDeviceFilters_TasksEntry_DoNotUseDefaultTypeInternal _JobDeviceFilters_TasksEntry_DoNotUse_default_instance_;
class TaskDeviceFilters;
class TaskDeviceFiltersDefaultTypeInternal;
extern TaskDeviceFiltersDefaultTypeInternal _TaskDeviceFilters_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ClusterDeviceFilters* Arena::CreateMaybeMessage<::tensorflow::ClusterDeviceFilters>(Arena*);
template<> ::tensorflow::JobDeviceFilters* Arena::CreateMaybeMessage<::tensorflow::JobDeviceFilters>(Arena*);
template<> ::tensorflow::JobDeviceFilters_TasksEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::JobDeviceFilters_TasksEntry_DoNotUse>(Arena*);
template<> ::tensorflow::TaskDeviceFilters* Arena::CreateMaybeMessage<::tensorflow::TaskDeviceFilters>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class TaskDeviceFilters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TaskDeviceFilters) */ {
 public:
  TaskDeviceFilters();
  virtual ~TaskDeviceFilters();

  TaskDeviceFilters(const TaskDeviceFilters& from);
  TaskDeviceFilters(TaskDeviceFilters&& from) noexcept
    : TaskDeviceFilters() {
    *this = ::std::move(from);
  }

  inline TaskDeviceFilters& operator=(const TaskDeviceFilters& from) {
    CopyFrom(from);
    return *this;
  }
  inline TaskDeviceFilters& operator=(TaskDeviceFilters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TaskDeviceFilters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TaskDeviceFilters* internal_default_instance() {
    return reinterpret_cast<const TaskDeviceFilters*>(
               &_TaskDeviceFilters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TaskDeviceFilters& a, TaskDeviceFilters& b) {
    a.Swap(&b);
  }
  inline void Swap(TaskDeviceFilters* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TaskDeviceFilters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TaskDeviceFilters* New() const final {
    return CreateMaybeMessage<TaskDeviceFilters>(nullptr);
  }

  TaskDeviceFilters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TaskDeviceFilters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TaskDeviceFilters& from);
  void MergeFrom(const TaskDeviceFilters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TaskDeviceFilters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TaskDeviceFilters";
  }
  protected:
  explicit TaskDeviceFilters(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceFiltersFieldNumber = 1,
  };
  // repeated string device_filters = 1;
  int device_filters_size() const;
  void clear_device_filters();
  const std::string& device_filters(int index) const;
  std::string* mutable_device_filters(int index);
  void set_device_filters(int index, const std::string& value);
  void set_device_filters(int index, std::string&& value);
  void set_device_filters(int index, const char* value);
  void set_device_filters(int index, const char* value, size_t size);
  std::string* add_device_filters();
  void add_device_filters(const std::string& value);
  void add_device_filters(std::string&& value);
  void add_device_filters(const char* value);
  void add_device_filters(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& device_filters() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_device_filters();

  // @@protoc_insertion_point(class_scope:tensorflow.TaskDeviceFilters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> device_filters_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto;
};
// -------------------------------------------------------------------

class JobDeviceFilters_TasksEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<JobDeviceFilters_TasksEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TaskDeviceFilters,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<JobDeviceFilters_TasksEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TaskDeviceFilters,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  JobDeviceFilters_TasksEntry_DoNotUse();
  JobDeviceFilters_TasksEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const JobDeviceFilters_TasksEntry_DoNotUse& other);
  static const JobDeviceFilters_TasksEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const JobDeviceFilters_TasksEntry_DoNotUse*>(&_JobDeviceFilters_TasksEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto.file_level_metadata[1];
  }

  public:
};

// -------------------------------------------------------------------

class JobDeviceFilters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.JobDeviceFilters) */ {
 public:
  JobDeviceFilters();
  virtual ~JobDeviceFilters();

  JobDeviceFilters(const JobDeviceFilters& from);
  JobDeviceFilters(JobDeviceFilters&& from) noexcept
    : JobDeviceFilters() {
    *this = ::std::move(from);
  }

  inline JobDeviceFilters& operator=(const JobDeviceFilters& from) {
    CopyFrom(from);
    return *this;
  }
  inline JobDeviceFilters& operator=(JobDeviceFilters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const JobDeviceFilters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const JobDeviceFilters* internal_default_instance() {
    return reinterpret_cast<const JobDeviceFilters*>(
               &_JobDeviceFilters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(JobDeviceFilters& a, JobDeviceFilters& b) {
    a.Swap(&b);
  }
  inline void Swap(JobDeviceFilters* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(JobDeviceFilters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline JobDeviceFilters* New() const final {
    return CreateMaybeMessage<JobDeviceFilters>(nullptr);
  }

  JobDeviceFilters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<JobDeviceFilters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const JobDeviceFilters& from);
  void MergeFrom(const JobDeviceFilters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(JobDeviceFilters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.JobDeviceFilters";
  }
  protected:
  explicit JobDeviceFilters(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kTasksFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // map<int32, .tensorflow.TaskDeviceFilters> tasks = 2;
  int tasks_size() const;
  void clear_tasks();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TaskDeviceFilters >&
      tasks() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TaskDeviceFilters >*
      mutable_tasks();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // @@protoc_insertion_point(class_scope:tensorflow.JobDeviceFilters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      JobDeviceFilters_TasksEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TaskDeviceFilters,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > tasks_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto;
};
// -------------------------------------------------------------------

class ClusterDeviceFilters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ClusterDeviceFilters) */ {
 public:
  ClusterDeviceFilters();
  virtual ~ClusterDeviceFilters();

  ClusterDeviceFilters(const ClusterDeviceFilters& from);
  ClusterDeviceFilters(ClusterDeviceFilters&& from) noexcept
    : ClusterDeviceFilters() {
    *this = ::std::move(from);
  }

  inline ClusterDeviceFilters& operator=(const ClusterDeviceFilters& from) {
    CopyFrom(from);
    return *this;
  }
  inline ClusterDeviceFilters& operator=(ClusterDeviceFilters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ClusterDeviceFilters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ClusterDeviceFilters* internal_default_instance() {
    return reinterpret_cast<const ClusterDeviceFilters*>(
               &_ClusterDeviceFilters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ClusterDeviceFilters& a, ClusterDeviceFilters& b) {
    a.Swap(&b);
  }
  inline void Swap(ClusterDeviceFilters* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ClusterDeviceFilters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ClusterDeviceFilters* New() const final {
    return CreateMaybeMessage<ClusterDeviceFilters>(nullptr);
  }

  ClusterDeviceFilters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ClusterDeviceFilters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ClusterDeviceFilters& from);
  void MergeFrom(const ClusterDeviceFilters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ClusterDeviceFilters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ClusterDeviceFilters";
  }
  protected:
  explicit ClusterDeviceFilters(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobsFieldNumber = 1,
  };
  // repeated .tensorflow.JobDeviceFilters jobs = 1;
  int jobs_size() const;
  void clear_jobs();
  ::tensorflow::JobDeviceFilters* mutable_jobs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::JobDeviceFilters >*
      mutable_jobs();
  const ::tensorflow::JobDeviceFilters& jobs(int index) const;
  ::tensorflow::JobDeviceFilters* add_jobs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::JobDeviceFilters >&
      jobs() const;

  // @@protoc_insertion_point(class_scope:tensorflow.ClusterDeviceFilters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::JobDeviceFilters > jobs_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TaskDeviceFilters

// repeated string device_filters = 1;
inline int TaskDeviceFilters::device_filters_size() const {
  return device_filters_.size();
}
inline void TaskDeviceFilters::clear_device_filters() {
  device_filters_.Clear();
}
inline const std::string& TaskDeviceFilters::device_filters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TaskDeviceFilters.device_filters)
  return device_filters_.Get(index);
}
inline std::string* TaskDeviceFilters::mutable_device_filters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TaskDeviceFilters.device_filters)
  return device_filters_.Mutable(index);
}
inline void TaskDeviceFilters::set_device_filters(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.TaskDeviceFilters.device_filters)
  device_filters_.Mutable(index)->assign(value);
}
inline void TaskDeviceFilters::set_device_filters(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.TaskDeviceFilters.device_filters)
  device_filters_.Mutable(index)->assign(std::move(value));
}
inline void TaskDeviceFilters::set_device_filters(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  device_filters_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.TaskDeviceFilters.device_filters)
}
inline void TaskDeviceFilters::set_device_filters(int index, const char* value, size_t size) {
  device_filters_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TaskDeviceFilters.device_filters)
}
inline std::string* TaskDeviceFilters::add_device_filters() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.TaskDeviceFilters.device_filters)
  return device_filters_.Add();
}
inline void TaskDeviceFilters::add_device_filters(const std::string& value) {
  device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.TaskDeviceFilters.device_filters)
}
inline void TaskDeviceFilters::add_device_filters(std::string&& value) {
  device_filters_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.TaskDeviceFilters.device_filters)
}
inline void TaskDeviceFilters::add_device_filters(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.TaskDeviceFilters.device_filters)
}
inline void TaskDeviceFilters::add_device_filters(const char* value, size_t size) {
  device_filters_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.TaskDeviceFilters.device_filters)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TaskDeviceFilters::device_filters() const {
  // @@protoc_insertion_point(field_list:tensorflow.TaskDeviceFilters.device_filters)
  return device_filters_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TaskDeviceFilters::mutable_device_filters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TaskDeviceFilters.device_filters)
  return &device_filters_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// JobDeviceFilters

// string name = 1;
inline void JobDeviceFilters::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& JobDeviceFilters::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.JobDeviceFilters.name)
  return name_.Get();
}
inline void JobDeviceFilters::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.JobDeviceFilters.name)
}
inline void JobDeviceFilters::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.JobDeviceFilters.name)
}
inline void JobDeviceFilters::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.JobDeviceFilters.name)
}
inline void JobDeviceFilters::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.JobDeviceFilters.name)
}
inline std::string* JobDeviceFilters::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.JobDeviceFilters.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* JobDeviceFilters::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.JobDeviceFilters.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void JobDeviceFilters::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.JobDeviceFilters.name)
}
inline std::string* JobDeviceFilters::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.JobDeviceFilters.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void JobDeviceFilters::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.JobDeviceFilters.name)
}

// map<int32, .tensorflow.TaskDeviceFilters> tasks = 2;
inline int JobDeviceFilters::tasks_size() const {
  return tasks_.size();
}
inline void JobDeviceFilters::clear_tasks() {
  tasks_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TaskDeviceFilters >&
JobDeviceFilters::tasks() const {
  // @@protoc_insertion_point(field_map:tensorflow.JobDeviceFilters.tasks)
  return tasks_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TaskDeviceFilters >*
JobDeviceFilters::mutable_tasks() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.JobDeviceFilters.tasks)
  return tasks_.MutableMap();
}

// -------------------------------------------------------------------

// ClusterDeviceFilters

// repeated .tensorflow.JobDeviceFilters jobs = 1;
inline int ClusterDeviceFilters::jobs_size() const {
  return jobs_.size();
}
inline void ClusterDeviceFilters::clear_jobs() {
  jobs_.Clear();
}
inline ::tensorflow::JobDeviceFilters* ClusterDeviceFilters::mutable_jobs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ClusterDeviceFilters.jobs)
  return jobs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::JobDeviceFilters >*
ClusterDeviceFilters::mutable_jobs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ClusterDeviceFilters.jobs)
  return &jobs_;
}
inline const ::tensorflow::JobDeviceFilters& ClusterDeviceFilters::jobs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ClusterDeviceFilters.jobs)
  return jobs_.Get(index);
}
inline ::tensorflow::JobDeviceFilters* ClusterDeviceFilters::add_jobs() {
  // @@protoc_insertion_point(field_add:tensorflow.ClusterDeviceFilters.jobs)
  return jobs_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::JobDeviceFilters >&
ClusterDeviceFilters::jobs() const {
  // @@protoc_insertion_point(field_list:tensorflow.ClusterDeviceFilters.jobs)
  return jobs_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto
