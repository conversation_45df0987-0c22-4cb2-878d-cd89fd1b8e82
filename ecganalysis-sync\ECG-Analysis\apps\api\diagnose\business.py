from apps.analysis.arrhythmia_diagnosis.diagnosis_detail.af_detail import quantify_af, detect_af_episodes
from apps.analysis.arrhythmia_diagnosis.diagnosis_detail.pvc_detail import quantify_pvc, detect_classify_pvc
from apps.models.Interface_log_models import InterfaceLogEntity, RequestParam
from apps.models.analysis_models import DiagnosisDetailEntity
from apps.utils import api_analysis_log


def save_api_log(ecg_data_str, gravity, sampling_rate, gain, zero, analysis_entity, custom_id):
    """
    记录接口日志
    :param ecg_data_str: 心电数据字符串
    :param gravity: 重力数据
    :param sampling_rate: 采样率
    :param gain: 增益
    :param zero: 基线
    :param analysis_entity: 接口分析内容
    :param custom_id: 客户ID
    :return:
    """
    # 设置日志实体
    interface_log = InterfaceLogEntity()

    # 设置请求参数
    request_param = RequestParam()
    request_param.ecg_data = ecg_data_str
    request_param.gravity = gravity
    request_param.fs = sampling_rate
    request_param.gain = gain
    request_param.zero = zero

    interface_log.RequestParam = request_param.to_dict()

    # 设置响应参数
    interface_log.ResponseParam = analysis_entity.to_entity_dict()

    # 写入日志
    ecg_id = api_analysis_log.record(custom_id, interface_log)

    return ecg_id


def get_diagnosis_details(analysis_entity, waveform_info, sampling_rate):
    waveform = waveform_info['waveform']
    diagnosis_details = []

    if analysis_entity.ArrhythmiaDiagnosis.AF == 1:
        rr_intervals = waveform.get('rr_intervals')
        r_peaks = waveform.get('r_peaks')
        if rr_intervals is not None and r_peaks is not None:
            if len(rr_intervals) > 0:
                total_duration_sec = float(sum(rr_intervals))
            elif len(r_peaks) > 1:
                total_duration_sec = float(r_peaks[-1]) / sampling_rate
            else:
                total_duration_sec = 10.0
            af_episodes = detect_af_episodes(rr_intervals, r_peaks, sampling_rate)
            quantification = quantify_af(af_episodes, total_duration_sec)
            diagnosis_detail = DiagnosisDetailEntity()
            diagnosis_detail.disease_code = 'AF'  # 疾病编码
            diagnosis_detail.total_episodes = quantification['total_episodes']  # 总阵数
            diagnosis_detail.total_duration = quantification['total_duration']  # 总时长(秒)
            diagnosis_detail.fastest_hr = quantification['fastest_hr']  # 最快心率(次 / 分钟)
            diagnosis_detail.longest_duration = quantification['longest_episode_duration']  # 最长持续(秒)
            diagnosis_detail.longest_rr = quantification['longest_rr_during_af']  # 最长RR(秒)

            diagnosis_details.append(diagnosis_detail.to_dict())


    if analysis_entity.ArrhythmiaDiagnosis.PVC == 1:
        rr_intervals = waveform.get('rr_intervals')
        r_peaks = waveform.get('r_peaks')
        if rr_intervals is not None and r_peaks is not None:
            quantification = quantify_pvc(detect_classify_pvc(rr_intervals, r_peaks), len(r_peaks))
            diagnosis_detail = DiagnosisDetailEntity()
            diagnosis_detail.disease_code = 'PVC'
            diagnosis_detail.single_count = quantification['counts_by_type']['single']  # 单发个数
            diagnosis_detail.pair_count = quantification['counts_by_type']['pair']  # 成对阵数
            diagnosis_detail.bigeminy_count = quantification['counts_by_type']['bigeminy_count']  # 二联律次数
            diagnosis_detail.trigeminy_count = quantification['counts_by_type']['trigeminy_count']  # 三联律次数
            diagnosis_detail.run_count = len(quantification['counts_by_type']['run'])  # 连发阵数
            diagnosis_detail.max_consecutive = quantification['max_consecutive_pvc']  # 连发最多个数
            diagnosis_detail.fastest_run_hr = quantification['fastest_vt_hr']  # 连发最快心率(次 / 分钟)
            diagnosis_detail.slowest_run_hr = quantification['slowest_vt_hr']  # 连发最慢心率(次 / 分钟)
            diagnosis_detail.hr_total_count = quantification['total_pvc_count']  # 心搏总数

            diagnosis_details.append(diagnosis_detail.to_dict())


    return diagnosis_details
