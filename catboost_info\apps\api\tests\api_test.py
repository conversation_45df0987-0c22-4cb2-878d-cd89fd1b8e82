import json
import requests
import sys
import os
import pandas as pd
import logging
import cProfile
import pstats
from pstats import SortKey
import io
import time
import argparse
from functools import wraps

# 修改日志格式,只输出消息内容
formatter = logging.Formatter('%(message)s')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')

# 修改项目根目录的添加方式
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))
sys.path.append(project_root)

from data_frame import global_settings

# 创建全局会话
session = requests.Session()


def timing_decorator(func):
    """性能监控装饰器"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logging.info(f"{func.__name__} took {end_time - start_time:.2f} seconds")
        return result

    return wrapper


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='ECG Analysis Tool')
    parser.add_argument('--mode', type=int, choices=[1, 2], help='处理模式：1=单文件，2=文件夹')
    parser.add_argument('--input', type=str, help='输入文件或文件夹路径')
    parser.add_argument('--output', type=str, help='输出文件夹路径')
    return parser.parse_args()


def get_token():
    """获取API的认证令牌"""
    try:
        login_url = global_settings.heartVoice['login']['url']
        client_id = global_settings.heartVoice['login']['clientId']
        client_secret = global_settings.heartVoice['login']['clientSecret']

        params = {
            "clientId": client_id,
            "clientSecret": client_secret
        }

        try:
            resp = session.post(login_url, data=params)
            resp.raise_for_status()
            resp_json = resp.json()

            if 'data' in resp_json and 'token' in resp_json['data']:
                token = resp_json['data']['token']
                logging.info(f"Successfully retrieved token: {token}")
                return token
            else:
                logging.error("Token not found in response")
                return None

        except requests.exceptions.RequestException as e:
            logging.error(f"Error during token request: {e}")
            return None

    except Exception as e:
        logging.error(f"Error getting token: {e}")
        return None


@timing_decorator
def profile_api_call(ecg_data, fs, token):
    """对单次API调用进行性能分析"""
    profiler = cProfile.Profile()
    profiler.enable()

    result, sna_features = ecg_analysis(ecg_data, fs, token)

    profiler.disable()

    # 创建字符串流来捕获输出
    s = io.StringIO()
    stats = pstats.Stats(profiler, stream=s).sort_stats(SortKey.TIME)
    stats.print_stats(10)  # 只打印前10个最耗时的函数

    logging.info("Performance Analysis:\n" + s.getvalue())
    return result, sna_features


def ecg_analysis(ecg_data, fs, token):
    """调用心电分析API"""
    global session

    analysis_url = 'http://127.0.0.1:8000/api/diagnose/arrhythmia/'
    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "Authorization": f"Bearer {token}"
    }

    params = {
        "signal": ecg_data,
        "fs": fs,
        "adc_gain": 1,
        "adc_zero": 0,
        'union_id': 'test_user'
    }

    try:
        resp = session.post(analysis_url, headers=headers, data=json.dumps(params))
        resp.raise_for_status()
        resp_json = resp.json()

        if resp_json.get("code") == 0:
            data = resp_json.get("data", {})
            sna_feature = data.get('ArrhythmiaDiagnosis', {}).get('SNA', False)
            sna_features = data.get('SNA_Features', {})
            return data, sna_features
        else:
            logging.error(f"API returned error: {resp_json.get('msg')} (code: {resp_json.get('code')})")
            return None, None

    except requests.exceptions.RequestException as e:
        logging.error(f"Error during API request: {e}")
        # 如果连接出错，尝试重新创建会话
        session = requests.Session()
        return None, None


def get_disease_name(arrhythmia_diagnosis):
    """将识别到的疾病转换为文字"""
    disease_mapping = {
        'SN': '窦性心律',
        'SNA': '窦性心律不齐',
        'SNT': '窦性心动过速',
        'SNB': '窦性心动过缓',
        'PVC': '室性早搏',
        'PSC': '不确定的早搏类型',
        'PJC': '交界性早搏',
        'PAC': '房性早搏',
        'VT': '室性心动过速',
        'SVT': '室上性心动过速',
        'AFL': '心房扑动',
        'AF': '心房颤动',
        'WPW': '应激综合征',
        'VE': '室性逸搏',
        'JE': '交界性逸搏',
        'AE': '房性逸搏',
        'AVBI': '一度房室传导阻滞',
        'AVBII': '二度房室传导阻滞',
        'AVBIII': '三度房室传导阻滞',
        'IVB': '室内传导阻滞',
        'LBBB': '左束支传导阻滞',
        'RBBB': '右束支传导阻滞',
        'LAFB': '左前分支传导阻滞',
        'BRU': 'Brugada综合征',
        'LQT': 'QT间期延长',
        'bPVC': '成对室早'
    }

    diseases = []
    for key, value in arrhythmia_diagnosis.items():
        if value == 1:
            diseases.append(disease_mapping.get(key, '未知疾病'))

    return ', '.join(diseases)


def get_multi_label_disease_name(multi_label_diagnosis):
    """将多结论诊断转换为中文"""
    disease_mapping = {
        'SN': '窦性心律',
        'SNA': '窦性心律不齐',
        'SNT': '窦性心动过速',
        'SNB': '窦性心动过缓',
        'PVC': '室性早搏',
        'PSC': '不确定的早搏类型',
        'PJC': '交界性早搏',
        'PAC': '房性早搏',
        'VT': '室性心动过速',
        'SVT': '室上性心动过速',
        'AFL': '心房扑动',
        'AF': '心房颤动',
        'WPW': 'WPW综合征',
        'VE': '室性逸搏',
        'JE': '交界性逸搏',
        'AE': '房性逸搏',
        'AVBI': '一度房室传导阻滞',
        'AVBII': '二度房室传导阻滞',
        'AVBIII': '三度房室传导阻滞',
        'IVB': '室内传导阻滞',
        'LBBB': '左束支传导阻滞',
        'RBBB': '右束支传导阻滞',
        'LAFB': '左前分支传导阻滞',
        'BRU': 'Brugada综合征',
        'LQT': 'QT间期延长',
        'bPVC': '成对室早',
        # 添加其他可能的多结论诊断映射
        'ISC': '心肌缺血',
        'LVH': '左心室肥大',
        'RVH': '右心室肥大',
        'LAH': '左心房肥大',
        'RAH': '右心房肥大',
        'MI': '心肌梗死'
    }

    # 将每个诊断结果转换为中文
    chinese_diagnoses = []
    for diagnosis in multi_label_diagnosis:
        chinese_diagnosis = disease_mapping.get(diagnosis, diagnosis)  # 如果找不到映射，保留原始值
        chinese_diagnoses.append(chinese_diagnosis)

    return chinese_diagnoses


@timing_decorator
def process_single_file(input_file, sampling_rate, token):
    """处理单个文件的函数 - 单个处理模式"""
    results_list = []
    chunk_size = 1000

    try:
        for chunk in pd.read_csv(input_file, encoding='utf-8', header=None, chunksize=chunk_size):
            for idx, row in chunk.iterrows():
                try:
                    # 数据预处理
                    ecg_data = pd.to_numeric(row, errors='coerce').dropna().tolist()

                    # 数据验证
                    if len(ecg_data) < sampling_rate * 10:
                        logging.warning(f"Row {idx}: 数据长度不足 ({len(ecg_data)} < {sampling_rate * 10})")
                        continue

                    # 截取所需长度的数据
                    ecg_signal = ecg_data[:sampling_rate * 10]

                    # 单个处理
                    result, _ = ecg_analysis(ecg_signal, sampling_rate, token)

                    if result:
                        arrhythmia_diagnosis = result.get('ArrhythmiaDiagnosis', {})
                        diseases = get_disease_name(arrhythmia_diagnosis)
                        multi_label_diagnosis = result.get('MultiLabelDiagnosis', [])
                        chinese_multi_label = get_multi_label_disease_name(multi_label_diagnosis)

                        # 更新日志输出
                        logging.info(f"Row {idx + 1} 诊断结果:")
                        logging.info(f"  传统诊断结果: {diseases}")
                        logging.info(f"  多结论诊断结果: {', '.join(chinese_multi_label)}")  # 使用转换后的中文结果
                        logging.info(f"  详细诊断: {json.dumps(arrhythmia_diagnosis, ensure_ascii=False)}")
                        logging.info(f"  ECG年龄: {result.get('ECGAge')}")
                        logging.info("  健康指标:")
                        health_metrics = result.get('HealthMetrics', {})
                        for metric, value in health_metrics.items():
                            logging.info(f"    {metric}: {value}")
                        logging.info("-" * 50)

                        new_row = {
                            'Row': idx + 1,
                            'ECGAge': result.get('ECGAge'),
                            'ArrhythmiaDiagnosis': arrhythmia_diagnosis,
                            'MultiLabelDiagnosis': chinese_multi_label,  # 保存中文结果
                            'HealthMetrics': health_metrics,
                            'PQRSTC': result.get('PQRSTC', {}),
                            'Status': '成功',
                            'Diseases': diseases,
                            'ErrorMessage': ''
                        }
                    else:
                        logging.warning(f"Row {idx + 1}: 处理失败")
                        new_row = {
                            'Row': idx + 1,
                            'ECGAge': None,
                            'ArrhythmiaDiagnosis': None,
                            'MultiLabelDiagnosis': None,
                            'HealthMetrics': None,
                            'PQRSTC': None,
                            'Status': '失败',
                            'Diseases': None,
                            'ErrorMessage': '接口调用失败'
                        }

                    results_list.append(new_row)

                except Exception as e:
                    logging.error(f"处理第 {idx + 1} 行数据时出错: {str(e)}")
                    results_list.append({
                        'Row': idx + 1,
                        'ECGAge': None,
                        'ArrhythmiaDiagnosis': None,
                        'MultiLabelDiagnosis': None,
                        'HealthMetrics': None,
                        'PQRSTC': None,
                        'Status': '失败',
                        'Diseases': None,
                        'ErrorMessage': f'处理错误: {str(e)}'
                    })

    except Exception as e:
        logging.error(f"处理文件时出错: {str(e)}")

    # 转换为DataFrame并返回
    df = pd.DataFrame(results_list)

    # 打印成功率统计
    total = len(df)
    success = len(df[df['Status'] == '成功'])
    logging.info(f"\n处理统计:")
    logging.info(f"总数: {total}")
    logging.info(f"成功: {success}")
    logging.info(f"失败: {total - success}")
    logging.info(f"成功率: {(success / total * 100):.2f}%")

    return df


def main():
    # 创建主性能分析器
    main_profiler = cProfile.Profile()
    main_profiler.enable()

    try:
        # 获取命令行参数
        args = parse_arguments()

        # 如果没有命令行参数，使用交互式输入
        if args.mode is None:
            mode = input("请选择处理模式（1: 单个文件，2: 整个文件夹）：")
            mode = int(mode)
        else:
            mode = args.mode

        sampling_rate = 500
        token = get_token()
        if not token:
            logging.error("无法获取到有效的token，程序结束")
            sys.exit(1)

        if mode == 1:
            input_file = args.input or input("请输入要处理的CSV文件完整路径：").strip('"')
            if not os.path.exists(input_file):
                logging.error("文件不存在！")
                sys.exit(1)

            output_folder = os.path.dirname(input_file)
            file_name = os.path.basename(input_file)
            output_file_name = f"{os.path.splitext(file_name)[0]}_acc.csv"
            output_file_path = os.path.join(output_folder, output_file_name)

            results_df = process_single_file(input_file, sampling_rate, token)
            results_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            logging.info(f"分析结果已保存到: {output_file_path}")

        elif mode == 2:
            input_folder = args.input or input("请输入要处理的文件夹路径：").strip('"')
            output_folder = args.output or input("请输入结果保存的文件夹路径：").strip('"')

            if not os.path.exists(input_folder):
                logging.error("输入文件夹不存在！")
                sys.exit(1)

            if not os.path.exists(output_folder):
                os.makedirs(output_folder)

            csv_files = [f for f in os.listdir(input_folder) if f.endswith('.csv')]
            logging.info(f"找到以下CSV文件：{csv_files}")

            for csv_file in csv_files:
                input_file_path = os.path.join(input_folder, csv_file)
                output_file_name = f"{os.path.splitext(csv_file)[0]}_acc.csv"
                output_file_path = os.path.join(output_folder, output_file_name)

                logging.info(f"开始处理文件：{csv_file}")
                results_df = process_single_file(input_file_path, sampling_rate, token)
                results_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
                logging.info(f"文件 {csv_file} 的分析结果已保存到: {output_file_path}")

        else:
            logging.error("无效的选择！")
            sys.exit(1)

    finally:
        # 停止性能分析并打印结果
        main_profiler.disable()

        # 保存详细的性能分析结果到文件
        stats_file = "api_test_performance.txt"
        with open(stats_file, 'w', encoding='utf-8') as f:
            stats = pstats.Stats(main_profiler, stream=f)
            stats.sort_stats(SortKey.TIME)
            stats.print_stats()

        logging.info(f"性能分析结果已保存到: {stats_file}")

        # 打印摘要
        logging.info("\n=== 性能分析摘要 ===")
        stats = pstats.Stats(main_profiler)
        stats.sort_stats(SortKey.TIME)
        stats.print_stats(10)  # 只打印前10个最耗时的函数


if __name__ == '__main__':
    main()