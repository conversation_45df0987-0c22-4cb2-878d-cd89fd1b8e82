from .cartan_type import Standard_Cartan
from sympy.core.backend import eye

class TypeC(Standard_Cartan):

    def __new__(cls, n):
        if n < 3:
            raise ValueError("n cannot be less than 3")
        return Standard_Cartan.__new__(cls, "C", n)


    def dimension(self):
        """Dimension of the vector space V underlying the Lie algebra

        Examples
        ========

        >>> from sympy.liealgebras.cartan_type import CartanType
        >>> c = CartanType("C3")
        >>> c.dimension()
        3
        """
        n = self.n
        return n

    def basic_root(self, i, j):
        """Generate roots with 1 in ith position and a -1 in jth position
        """
        n = self.n
        root = [0]*n
        root[i] = 1
        root[j] = -1
        return root

    def simple_root(self, i):
        """The ith simple root for the C series

        Every lie algebra has a unique root system.
        Given a root system Q, there is a subset of the
        roots such that an element of Q is called a
        simple root if it cannot be written as the sum
        of two elements in Q.  If we let D denote the
        set of simple roots, then it is clear that every
        element of Q can be written as a linear combination
        of elements of D with all coefficients non-negative.

        In C_n, the first n-1 simple roots are the same as
        the roots in A_(n-1) (a 1 in the ith position, a -1
        in the (i+1)th position, and zeroes elsewhere).  The
        nth simple root is the root in which there is a 2 in
        the nth position and zeroes elsewhere.

        Examples
        ========

        >>> from sympy.liealgebras.cartan_type import CartanType
        >>> c = CartanType("C3")
        >>> c.simple_root(2)
        [0, 1, -1]

        """

        n = self.n
        if i < n:
            return self.basic_root(i-1,i)
        else:
            root = [0]*self.n
            root[n-1] = 2
            return root


    def positive_roots(self):
        """Generates all the positive roots of A_n

        This is half of all of the roots of C_n; by multiplying all the
        positive roots by -1 we get the negative roots.

        Examples
        ========

        >>> from sympy.liealgebras.cartan_type import CartanType
        >>> c = CartanType("A3")
        >>> c.positive_roots()
        {1: [1, -1, 0, 0], 2: [1, 0, -1, 0], 3: [1, 0, 0, -1], 4: [0, 1, -1, 0],
                5: [0, 1, 0, -1], 6: [0, 0, 1, -1]}

        """

        n = self.n
        posroots = {}
        k = 0
        for i in range(0, n-1):
            for j in range(i+1, n):
               k += 1
               posroots[k] = self.basic_root(i, j)
               k += 1
               root = self.basic_root(i, j)
               root[j] = 1
               posroots[k] = root

        for i in range(0, n):
            k += 1
            root = [0]*n
            root[i] = 2
            posroots[k] = root

        return posroots

    def roots(self):
        """
        Returns the total number of roots for C_n"
        """

        n = self.n
        return 2*(n**2)

    def cartan_matrix(self):
        """The Cartan matrix for C_n

        The Cartan matrix matrix for a Lie algebra is
        generated by assigning an ordering to the simple
        roots, (alpha[1], ...., alpha[l]).  Then the ijth
        entry of the Cartan matrix is (<alpha[i],alpha[j]>).

        Examples
        ========

        >>> from sympy.liealgebras.cartan_type import CartanType
        >>> c = CartanType('C4')
        >>> c.cartan_matrix()
        Matrix([
        [ 2, -1,  0,  0],
        [-1,  2, -1,  0],
        [ 0, -1,  2, -1],
        [ 0,  0, -2,  2]])

        """

        n = self.n
        m = 2 * eye(n)
        i = 1
        while i < n-1:
           m[i, i+1] = -1
           m[i, i-1] = -1
           i += 1
        m[0,1] = -1
        m[n-1, n-2] = -2
        return m


    def basis(self):
        """
        Returns the number of independent generators of C_n
        """

        n = self.n
        return n*(2*n + 1)

    def lie_algebra(self):
        """
        Returns the Lie algebra associated with C_n"
        """

        n = self.n
        return "sp(" + str(2*n) + ")"

    def dynkin_diagram(self):
        n = self.n
        diag = "---".join("0" for i in range(1, n)) + "=<=0\n"
        diag += "   ".join(str(i) for i in range(1, n+1))
        return diag
