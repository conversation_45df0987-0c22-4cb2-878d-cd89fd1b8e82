# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2016,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-10-16 18:27+0000\n"
"Last-Translator: znotdead <<EMAIL>>\n"
"Language-Team: Belarusian (http://www.transifex.com/django/django/language/"
"be/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: be\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Advanced options"
msgstr "Адмысловыя можнасьці"

msgid "Flat Pages"
msgstr "Нязменныя Бачыны"

msgid "URL"
msgstr "Сеціўная спасылка"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Прыклад: «/about/contact/». Упэўніцеся, што адрас пачынаецца й заканчваецца "
"рыскаю «/»."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Дазваляюцца толькі літары, лічбы, кропкі, знак падкрэсьліваньня, злучкі, "
"нахіленыя рыскі, тыльды."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Прыклад: «/about/contact/». Упэўніцеся, што адрас пачынаецца рыскаю «/»."

msgid "URL is missing a leading slash."
msgstr "Спасылка не пачынаецца з рыскі «/»."

msgid "URL is missing a trailing slash."
msgstr "Спасылка не заканчваецца рыскаю «/»."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "На пляцоўцы «%(site)s» ужо існуе нязьменная бачына з адрасам «%(url)s»"

msgid "title"
msgstr "назва"

msgid "content"
msgstr "зьмесьціва"

msgid "enable comments"
msgstr "дазволіць выказваньні"

msgid "template name"
msgstr "назва шаблёну"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Прыклад: “flatpages/contact_page.html”. Калі не пазначаць нічога, сыстэма "
"будзе ўжываць “flatpages/default.html”."

msgid "registration required"
msgstr "трэба запісацца"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Калі абраць гэта, бачыну змогуць пабачыць толькі тыя карыстальнікі, што "
"апазналіся."

msgid "sites"
msgstr "сайты"

msgid "flat page"
msgstr "нязьменная бачына"

msgid "flat pages"
msgstr "нязьменныя бачыны"
