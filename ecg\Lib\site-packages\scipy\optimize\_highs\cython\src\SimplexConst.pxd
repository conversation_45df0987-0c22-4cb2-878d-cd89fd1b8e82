# distutils: language=c++
# cython: language_level=3

from libcpp cimport bool

cdef extern from "SimplexConst.h" nogil:

    cdef enum SimplexAlgorithm:
        PRIMAL "SimplexAlgorithm::PRIMAL" = 0
        DUAL "SimplexAlgorithm::DUAL"

    cdef enum SimplexStrategy:
        SIMPLEX_STRATEGY_MIN = 0
        SIMPLEX_STRATEGY_CHOOSE = SIMPLEX_STRATEGY_MIN
        SIMPLEX_STRATEGY_DUAL
        SIMPLEX_STRATEGY_DUAL_PLAIN = SIMPLEX_STRATEGY_DUAL
        SIMPLEX_STRATEGY_DUAL_TASKS
        SIMPLEX_STRATEGY_DUAL_MULTI
        SIMPLEX_STRATEGY_PRIMAL
        SIMPLEX_STRATEGY_MAX = SIMPLEX_STRATEGY_PRIMAL
        SIMPLEX_STRATEGY_NUM

    cdef enum DualSimplexCleanupStrategy:
        DUAL_SIMPLEX_CLEANUP_STRATEGY_MIN = 0
        DUAL_SIMPLEX_CLEANUP_STRATEGY_NONE = DUAL_SIMPLEX_CLEANUP_STRATEGY_MIN
        DUAL_SIMPLEX_CLEANUP_STRATEGY_HPRIMAL
        DUAL_SIMPLEX_CLEANUP_STRATEGY_HQPRIMAL
        DUAL_SIMPLEX_CLEANUP_STRATEGY_MAX = DUAL_SIMPLEX_CLEANUP_STRATEGY_HQPRIMAL

    cdef enum SimplexScaleStrategy:
        SIMPLEX_SCALE_STRATEGY_MIN = 0
        SIMPLEX_SCALE_STRATEGY_OFF = SIMPLEX_SCALE_STRATEGY_MIN
        SIMPLEX_SCALE_STRATEGY_HIGHS
        SIMPLEX_SCALE_STRATEGY_HIGHS_FORCED
        SIMPLEX_SCALE_STRATEGY_015
        SIMPLEX_SCALE_STRATEGY_0157
        SIMPLEX_SCALE_STRATEGY_MAX = SIMPLEX_SCALE_STRATEGY_0157

    cdef enum SimplexCrashStrategy:
        SIMPLEX_CRASH_STRATEGY_MIN = 0
        SIMPLEX_CRASH_STRATEGY_OFF = SIMPLEX_CRASH_STRATEGY_MIN
        SIMPLEX_CRASH_STRATEGY_LTSSF_K
        SIMPLEX_CRASH_STRATEGY_LTSSF = SIMPLEX_CRASH_STRATEGY_LTSSF_K
        SIMPLEX_CRASH_STRATEGY_BIXBY
        SIMPLEX_CRASH_STRATEGY_LTSSF_PRI
        SIMPLEX_CRASH_STRATEGY_LTSF_K
        SIMPLEX_CRASH_STRATEGY_LTSF_PRI
        SIMPLEX_CRASH_STRATEGY_LTSF
        SIMPLEX_CRASH_STRATEGY_BIXBY_NO_NONZERO_COL_COSTS
        SIMPLEX_CRASH_STRATEGY_BASIC
        SIMPLEX_CRASH_STRATEGY_TEST_SING
        SIMPLEX_CRASH_STRATEGY_MAX = SIMPLEX_CRASH_STRATEGY_TEST_SING

    cdef enum SimplexDualEdgeWeightStrategy:
        SIMPLEX_DUAL_EDGE_WEIGHT_STRATEGY_MIN = 0
        SIMPLEX_DUAL_EDGE_WEIGHT_STRATEGY_DANTZIG = SIMPLEX_DUAL_EDGE_WEIGHT_STRATEGY_MIN
        SIMPLEX_DUAL_EDGE_WEIGHT_STRATEGY_DEVEX
        SIMPLEX_DUAL_EDGE_WEIGHT_STRATEGY_STEEPEST_EDGE_TO_DEVEX_SWITCH
        SIMPLEX_DUAL_EDGE_WEIGHT_STRATEGY_STEEPEST_EDGE
        SIMPLEX_DUAL_EDGE_WEIGHT_STRATEGY_STEEPEST_EDGE_UNIT_INITIAL
        SIMPLEX_DUAL_EDGE_WEIGHT_STRATEGY_MAX = SIMPLEX_DUAL_EDGE_WEIGHT_STRATEGY_STEEPEST_EDGE_UNIT_INITIAL

    cdef enum SimplexPrimalEdgeWeightStrategy:
        SIMPLEX_PRIMAL_EDGE_WEIGHT_STRATEGY_MIN = 0
        SIMPLEX_PRIMAL_EDGE_WEIGHT_STRATEGY_DANTZIG = SIMPLEX_PRIMAL_EDGE_WEIGHT_STRATEGY_MIN
        SIMPLEX_PRIMAL_EDGE_WEIGHT_STRATEGY_DEVEX
        SIMPLEX_PRIMAL_EDGE_WEIGHT_STRATEGY_MAX = SIMPLEX_PRIMAL_EDGE_WEIGHT_STRATEGY_DEVEX

    cdef enum SimplexPriceStrategy:
        SIMPLEX_PRICE_STRATEGY_MIN = 0
        SIMPLEX_PRICE_STRATEGY_COL = SIMPLEX_PRICE_STRATEGY_MIN
        SIMPLEX_PRICE_STRATEGY_ROW
        SIMPLEX_PRICE_STRATEGY_ROW_SWITCH
        SIMPLEX_PRICE_STRATEGY_ROW_SWITCH_COL_SWITCH
        SIMPLEX_PRICE_STRATEGY_MAX = SIMPLEX_PRICE_STRATEGY_ROW_SWITCH_COL_SWITCH

    cdef enum SimplexDualChuzcStrategy:
        SIMPLEX_DUAL_CHUZC_STRATEGY_MIN = 0
        SIMPLEX_DUAL_CHUZC_STRATEGY_CHOOSE = SIMPLEX_DUAL_CHUZC_STRATEGY_MIN
        SIMPLEX_DUAL_CHUZC_STRATEGY_QUAD
        SIMPLEX_DUAL_CHUZC_STRATEGY_HEAP
        SIMPLEX_DUAL_CHUZC_STRATEGY_BOTH
        SIMPLEX_DUAL_CHUZC_STRATEGY_MAX = SIMPLEX_DUAL_CHUZC_STRATEGY_BOTH

    cdef enum InvertHint:
        INVERT_HINT_NO = 0
        INVERT_HINT_UPDATE_LIMIT_REACHED
        INVERT_HINT_SYNTHETIC_CLOCK_SAYS_INVERT
        INVERT_HINT_POSSIBLY_OPTIMAL
        INVERT_HINT_POSSIBLY_PRIMAL_UNBOUNDED
        INVERT_HINT_POSSIBLY_DUAL_UNBOUNDED
        INVERT_HINT_POSSIBLY_SINGULAR_BASIS
        INVERT_HINT_PRIMAL_INFEASIBLE_IN_PRIMAL_SIMPLEX
        INVERT_HINT_CHOOSE_COLUMN_FAIL
        INVERT_HINT_Count

    cdef enum DualEdgeWeightMode:
        DANTZIG "DualEdgeWeightMode::DANTZIG" = 0
        DEVEX "DualEdgeWeightMode::DEVEX"
        STEEPEST_EDGE "DualEdgeWeightMode::STEEPEST_EDGE"
        Count "DualEdgeWeightMode::Count"

    cdef enum PriceMode:
        ROW "PriceMode::ROW" = 0
        COL "PriceMode::COL"

    const int PARALLEL_THREADS_DEFAULT
    const int DUAL_TASKS_MIN_THREADS
    const int DUAL_MULTI_MIN_THREADS

    const bool invert_if_row_out_negative
