{"count": 12, "next": null, "previous": null, "results": [{"assignee": null, "created_date": "2023-05-26T16:25:36.613000Z", "gt_last_updated": "2023-05-26T16:16:50.630000Z", "id": 1, "job_id": null, "parent_id": null, "summary": {"accuracy": 0.4883720930232558, "conflict_count": 37, "conflicts_by_type": {"covered_annotation": 1, "extra_annotation": 10, "low_overlap": 6, "mismatching_attributes": 1, "mismatching_direction": 1, "mismatching_groups": 6, "mismatching_label": 3, "missing_annotation": 9}, "ds_count": 34, "error_count": 22, "frame_count": 3, "frame_share": 0.2727272727272727, "gt_count": 33, "precision": 0.6176470588235294, "recall": 0.6363636363636364, "total_count": 43, "valid_count": 21, "warning_count": 15}, "target": "task", "target_last_updated": "2023-05-26T16:17:02.635000Z", "task_id": 22}, {"assignee": null, "created_date": "2023-05-26T16:25:36.616000Z", "gt_last_updated": "2023-05-26T16:16:50.630000Z", "id": 2, "job_id": 27, "parent_id": 1, "summary": {"accuracy": 0.4883720930232558, "conflict_count": 37, "conflicts_by_type": {"covered_annotation": 1, "extra_annotation": 10, "low_overlap": 6, "mismatching_attributes": 1, "mismatching_direction": 1, "mismatching_groups": 6, "mismatching_label": 3, "missing_annotation": 9}, "ds_count": 34, "error_count": 22, "frame_count": 3, "frame_share": 0.2727272727272727, "gt_count": 33, "precision": 0.6176470588235294, "recall": 0.6363636363636364, "total_count": 43, "valid_count": 21, "warning_count": 15}, "target": "job", "target_last_updated": "2023-05-26T16:11:24.294000Z", "task_id": null}, {"assignee": null, "created_date": "2023-11-24T15:24:25.355000Z", "gt_last_updated": "2023-11-24T15:18:55.216000Z", "id": 3, "job_id": null, "parent_id": null, "summary": {"accuracy": 0.4782608695652174, "conflict_count": 40, "conflicts_by_type": {"covered_annotation": 1, "extra_annotation": 11, "low_overlap": 7, "mismatching_attributes": 1, "mismatching_direction": 1, "mismatching_groups": 6, "mismatching_label": 3, "missing_annotation": 10}, "ds_count": 36, "error_count": 24, "frame_count": 3, "frame_share": 0.2727272727272727, "gt_count": 35, "precision": 0.6111111111111112, "recall": 0.6285714285714286, "total_count": 46, "valid_count": 22, "warning_count": 16}, "target": "task", "target_last_updated": "2023-11-24T15:23:30.045000Z", "task_id": 22}, {"assignee": null, "created_date": "2023-11-24T15:24:25.357000Z", "gt_last_updated": "2023-11-24T15:18:55.216000Z", "id": 4, "job_id": 27, "parent_id": 3, "summary": {"accuracy": 0.4782608695652174, "conflict_count": 40, "conflicts_by_type": {"covered_annotation": 1, "extra_annotation": 11, "low_overlap": 7, "mismatching_attributes": 1, "mismatching_direction": 1, "mismatching_groups": 6, "mismatching_label": 3, "missing_annotation": 10}, "ds_count": 36, "error_count": 24, "frame_count": 3, "frame_share": 0.2727272727272727, "gt_count": 35, "precision": 0.6111111111111112, "recall": 0.6285714285714286, "total_count": 46, "valid_count": 22, "warning_count": 16}, "target": "job", "target_last_updated": "2023-11-24T15:23:30.269000Z", "task_id": null}, {"assignee": null, "created_date": "2024-03-21T11:16:21.845000Z", "gt_last_updated": "2023-11-24T15:18:55.216000Z", "id": 5, "job_id": null, "parent_id": null, "summary": {"accuracy": 0.4782608695652174, "conflict_count": 40, "conflicts_by_type": {"covered_annotation": 1, "extra_annotation": 11, "low_overlap": 7, "mismatching_attributes": 1, "mismatching_direction": 1, "mismatching_groups": 6, "mismatching_label": 3, "missing_annotation": 10}, "ds_count": 36, "error_count": 24, "frame_count": 3, "frame_share": 0.2727272727272727, "gt_count": 35, "precision": 0.6111111111111112, "recall": 0.6285714285714286, "total_count": 46, "valid_count": 22, "warning_count": 16}, "target": "task", "target_last_updated": "2023-11-24T15:23:30.045000Z", "task_id": 22}, {"assignee": null, "created_date": "2024-03-21T11:16:21.847000Z", "gt_last_updated": "2023-11-24T15:18:55.216000Z", "id": 6, "job_id": 27, "parent_id": 5, "summary": {"accuracy": 0.4782608695652174, "conflict_count": 40, "conflicts_by_type": {"covered_annotation": 1, "extra_annotation": 11, "low_overlap": 7, "mismatching_attributes": 1, "mismatching_direction": 1, "mismatching_groups": 6, "mismatching_label": 3, "missing_annotation": 10}, "ds_count": 36, "error_count": 24, "frame_count": 3, "frame_share": 0.2727272727272727, "gt_count": 35, "precision": 0.6111111111111112, "recall": 0.6285714285714286, "total_count": 46, "valid_count": 22, "warning_count": 16}, "target": "job", "target_last_updated": "2023-11-24T15:23:30.269000Z", "task_id": null}, {"assignee": null, "created_date": "2024-03-21T20:52:03.542000Z", "gt_last_updated": "2024-03-21T20:50:20.020000Z", "id": 7, "job_id": null, "parent_id": null, "summary": {"accuracy": 0.5, "conflict_count": 3, "conflicts_by_type": {"extra_annotation": 1, "low_overlap": 1, "missing_annotation": 1}, "ds_count": 3, "error_count": 2, "frame_count": 3, "frame_share": 0.2727272727272727, "gt_count": 3, "precision": 0.6666666666666666, "recall": 0.6666666666666666, "total_count": 4, "valid_count": 2, "warning_count": 1}, "target": "task", "target_last_updated": "2024-03-21T20:50:05.947000Z", "task_id": 23}, {"assignee": null, "created_date": "2024-03-21T20:52:03.552000Z", "gt_last_updated": "2024-03-21T20:50:20.020000Z", "id": 8, "job_id": 31, "parent_id": 7, "summary": {"accuracy": 0.0, "conflict_count": 0, "conflicts_by_type": {}, "ds_count": 0, "error_count": 0, "frame_count": 0, "frame_share": 0.0, "gt_count": 0, "precision": 0.0, "recall": 0.0, "total_count": 0, "valid_count": 0, "warning_count": 0}, "target": "job", "target_last_updated": "2024-03-21T20:50:27.594000Z", "task_id": null}, {"assignee": null, "created_date": "2024-03-21T20:52:03.552000Z", "gt_last_updated": "2024-03-21T20:50:20.020000Z", "id": 9, "job_id": 30, "parent_id": 7, "summary": {"accuracy": 1.0, "conflict_count": 1, "conflicts_by_type": {"low_overlap": 1}, "ds_count": 2, "error_count": 0, "frame_count": 2, "frame_share": 0.4, "gt_count": 2, "precision": 1.0, "recall": 1.0, "total_count": 2, "valid_count": 2, "warning_count": 1}, "target": "job", "target_last_updated": "2024-03-21T20:50:33.610000Z", "task_id": null}, {"assignee": null, "created_date": "2024-03-21T20:52:03.552000Z", "gt_last_updated": "2024-03-21T20:50:20.020000Z", "id": 10, "job_id": 29, "parent_id": 7, "summary": {"accuracy": 0.0, "conflict_count": 2, "conflicts_by_type": {"extra_annotation": 1, "missing_annotation": 1}, "ds_count": 1, "error_count": 2, "frame_count": 1, "frame_share": 0.2, "gt_count": 1, "precision": 0.0, "recall": 0.0, "total_count": 2, "valid_count": 0, "warning_count": 0}, "target": "job", "target_last_updated": "2024-03-21T20:50:39.585000Z", "task_id": null}, {"assignee": null, "created_date": "2024-04-03T09:51:29.971000Z", "gt_last_updated": "2023-11-24T15:18:55.216000Z", "id": 11, "job_id": null, "parent_id": null, "summary": {"accuracy": 0.4583333333333333, "conflict_count": 42, "conflicts_by_type": {"covered_annotation": 1, "extra_annotation": 11, "low_overlap": 7, "mismatching_attributes": 1, "mismatching_direction": 1, "mismatching_groups": 6, "mismatching_label": 3, "missing_annotation": 12}, "ds_count": 36, "error_count": 26, "frame_count": 3, "frame_share": 0.2727272727272727, "gt_count": 37, "precision": 0.6111111111111112, "recall": 0.5945945945945946, "total_count": 48, "valid_count": 22, "warning_count": 16}, "target": "task", "target_last_updated": "2023-11-24T15:23:30.045000Z", "task_id": 22}, {"assignee": null, "created_date": "2024-04-03T09:51:29.992000Z", "gt_last_updated": "2023-11-24T15:18:55.216000Z", "id": 12, "job_id": 27, "parent_id": 11, "summary": {"accuracy": 0.4583333333333333, "conflict_count": 42, "conflicts_by_type": {"covered_annotation": 1, "extra_annotation": 11, "low_overlap": 7, "mismatching_attributes": 1, "mismatching_direction": 1, "mismatching_groups": 6, "mismatching_label": 3, "missing_annotation": 12}, "ds_count": 36, "error_count": 26, "frame_count": 3, "frame_share": 0.2727272727272727, "gt_count": 37, "precision": 0.6111111111111112, "recall": 0.5945945945945946, "total_count": 48, "valid_count": 22, "warning_count": 16}, "target": "job", "target_last_updated": "2023-11-24T15:23:30.269000Z", "task_id": null}]}