import json
from pathlib import Path


def load_rules():
    """
    加载规则文件
    :return:
    """
    # 获取当前执行文件的路径
    current_file = Path(__file__)
    # 构建规则文件的路径
    rules_file_path = current_file.parent / 'filter_rules.json'

    with open(rules_file_path, 'r') as file:
        config = json.load(file)
    return config["rules"]


def method1_handler(result, actions):
    """
    模式1处理
    如果trigger标签的值为1，将action中的所有key的值设置为0
    :param result: 待处理的结果集
    :param actions: 动作集
    :return:
    """
    keys_to_check = [action for action in actions]

    if any(getattr(result, key) == 1 for key in keys_to_check):
        setattr(result, 'SN', 0)

    return result


def method2_handler(result, actions):
    """
    模式2处理
    将action的key按优先级排序，按顺序判断key的value是否为1，如果实则将后续的key的值设置为0
    :param result: 待处理的结果集
    :param actions: 动作集
    :return:
    """
    # 根据index排序
    sorted_actions = sorted(actions, key=lambda x: x['index'])
    for action in sorted_actions:
        if getattr(result, action['key']) == 1:
            for subsequent_action in sorted_actions:
                if subsequent_action['index'] > action['index'] and getattr(result, subsequent_action['key']) == 1:
                    setattr(result, subsequent_action['key'], 0)

    return result


def apply_rules(result):
    """
    应用规则
    :param result: 待处理的结果集
    :return:
    """
    # 加载规则
    rules = load_rules()

    sorted_rules = sorted(rules, key=lambda x: x['priority'])

    for rule in sorted_rules:
        method = rule['method']
        actions = rule['actions']

        if method == 1:
            result = method1_handler(result, actions)

        if method == 2:
            result = method2_handler(result, actions)

    return result
