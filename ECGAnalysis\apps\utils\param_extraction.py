import traceback

from apps.utils.logger_helper import Logger


def param_extraction(request, key, request_type=None):
    """
    提取参数
    :param request: 请求对象
    :param key: 关键字
    :param request_type: 请求类型
    :return: 请求对象中的值
    """
    try:
        if key is None:
            pass
        else:
            if request_type == 'GET':
                try:
                    value = request.GET.get(key)
                    return value
                except Exception as e:
                    return None
            elif request_type == 'POST':
                # 表单取值
                try:
                    value = request.POST.get(key)
                except Exception as e:
                    # 记录错误日志
                    Logger().error(traceback.format_exc())
                    return None
                return value
            else:
                return None

    except Exception as e:
        error = traceback.format_exc()
        # 记录错误日志
        Logger().error(error)
        return error
