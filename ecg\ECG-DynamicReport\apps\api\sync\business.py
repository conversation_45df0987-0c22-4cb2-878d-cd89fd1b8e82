import json
import os
from datetime import datetime
from pathlib import Path
from apps.utils.sql_helper import MysqlHelper


class SyncBusiness:
    @staticmethod
    def create_partition_table(data_date):
        """
        创建数据存储表
        :param data_date: 要保存的数据日期
        """
        # 获取当前执行文件的路径
        current_file = Path(__file__)

        data_source = 'ecg_analysis'
        sql_helper = MysqlHelper(data_source)

        create_sql_list = [
            't_date_ecg.sql',
            't_date_ecg_detail.sql'
        ]

        for create_sql in create_sql_list:
            # 构建规则文件的路径
            sql_file_path = current_file.parent / create_sql

            # 检查文件是否存在
            if os.path.exists(sql_file_path):
                # 读取 SQL 文件内容
                with open(sql_file_path, encoding='utf-8') as file:
                    sql_script = file.read()

                # 替换占位符 {table_name} 为实际的表名
                sql_script = sql_script.replace("{data_date}", data_date)

                sql_helper.execute_sql(sql_script)

    @staticmethod
    def save_data(study_uid, check_time, ecg_info, data_date, diagnosis_details, diagnosis_details_ii):
        """
        保存数据
        :param study_uid: ecg_id
        :param check_time:  检查时间
        :param ecg_info: 心电分析信息
        :param data_date: 要保存的数据日期
        :param diagnosis_details: I导诊断详情
        :param diagnosis_details_ii: II诊断详情
        """
        sql_helper = MysqlHelper('ecg_analysis')

        ecg_id = SyncBusiness().__save_ecg_data(study_uid, check_time, ecg_info, data_date, sql_helper)

        SyncBusiness().__save_ecg_data_detail(ecg_id, ecg_info, check_time, data_date, sql_helper, diagnosis_details,
                                              diagnosis_details_ii)

    @staticmethod
    def __save_ecg_data(study_uid, check_time, ecg_info, data_date, sql_helper):
        """
        保存数据
        :param study_uid: ecg_id
        :param check_time:  检查时间
        :param ecg_info: 心电分析信息
        :param data_date: 要保存的数据日期
        """
        table_name = f't_data_ecg_{data_date}'

        data = {
            'ecg_id': study_uid,
            'check_date': check_time,
            'union_id': ecg_info['unionId']
        }

        report_lead_i_data = None
        report_lead_ii_data = None

        if 'ecgAnalysis' in ecg_info.keys() and ecg_info['ecgAnalysis']:
            data['report_lead_i'] = ecg_info['ecgAnalysis']
            report_lead_i_data = json.loads(ecg_info['ecgAnalysis'])

        if 'ecgAnalysisII' in ecg_info.keys() and ecg_info['ecgAnalysisII']:
            data['report_lead_ii'] = ecg_info['ecgAnalysisII']
            report_lead_ii_data = json.loads(ecg_info['ecgAnalysisII'])

        if 'ecgAnalysisIII' in ecg_info.keys() and ecg_info['ecgAnalysisIII']:
            data['report_lead_iii'] = ecg_info['ecgAnalysisIII']

        # 指标分析（有ii导联用ii导联，没有用i导联）
        if not ecg_info['reportTwo'] == -1:
            data = SyncBusiness().__set_ecg_data(data, report_lead_ii_data)
        elif not ecg_info['reportOne'] == -1:
            data = SyncBusiness().__set_ecg_data(data, report_lead_i_data)
        else:
            data['dead'] = 2

        return sql_helper.insert_data(table_name, data)

    @staticmethod
    def __set_ecg_data(data, report_lead_data):
        """
        设置保存数据
        """
        indicator_dict = {
            'ArrhythmiaDiagnosis': [
                'AF', 'AFL', 'PAC', 'PVC', 'AE', 'AVBI', 'AVBII', 'AVBIII',
                'BRU', 'IVB', 'JE', 'LAFB', 'LBBB', 'LQT', 'PJC', 'PSC',
                'RBBB', 'SN', 'SNA', 'SNB', 'SNT', 'SVT', 'VE', 'VT',
                'WPW', 'bPVC'
            ],
            'CADCardiomyopathy': ['ISC', 'LAH', 'LVH', 'MI', 'RAH', 'RVH'],
            'HealthMetrics': ['Emotion', 'Fatigue', 'HRV', 'Pressure', 'Vitality'],
            'PQRSTC': ['HR', 'PR_interval', 'P_duration', 'QT', 'QTc', 'ST_duration', 'NN_MAX', 'NN_MIN', 'RR_MAX',
                       'RR_MIN'],
        }

        for indicator_name, fields in indicator_dict.items():
            for field in fields:
                if field in report_lead_data[indicator_name]:
                    data[f'{indicator_name}_{field}'] = report_lead_data[indicator_name][field]

        indicators = ['ECGAge', 'HeartFailureRisk', 'OSARisk', 'RespiratoryRate', 'SignalQuantity', 'SleepStage',
                      'SyncopeRisk', 'VentricularFibrillationRisk', 'avgHr', 'RRIntervals', 'NNIntervals']

        for indicator in indicators:
            if indicator in report_lead_data:
                data[indicator] = report_lead_data[indicator]

        return data

    @staticmethod
    def __save_ecg_data_detail(ecg_id, ecg_info, check_time, data_date, sql_helper, diagnosis_details,
                               diagnosis_details_ii):
        """
        保存数据
        :param ecg_id: ecg_data表主键id
        :param ecg_info: 心电分析信息
        :param data_date: 要保存的数据日期
        :param diagnosis_details: I导诊断详情
        :param diagnosis_details_ii: II诊断详情
        """

        if diagnosis_details or diagnosis_details_ii:
            table_name = f't_data_ecg_detail_{data_date}'

            details = None

            if ecg_info['channel'] == 1 and diagnosis_details:
                details = json.loads(diagnosis_details.replace('\\"', '"'))
            else:
                # 指标分析（有ii导联用ii导联，没有用i导联）
                if ecg_info['reportTwo'] == 1 and diagnosis_details_ii:
                    details = json.loads(diagnosis_details_ii.replace('\\"', '"'))
                elif ecg_info['reportOne'] == 1 and diagnosis_details:
                    details = json.loads(diagnosis_details.replace('\\"', '"'))

            if details:
                datas = [['data_ecg_id', 'union_id', 'check_date', 'disease_code', 'total_episodes', 'total_duration',
                          'fastest_hr', 'longest_duration', 'longest_rr', 'single_count', 'pair_count',
                          'bigeminy_count',
                          'trigeminy_count', 'run_count', 'max_consecutive', 'fastest_run_hr', 'slowest_run_hr',
                          'hr_total_count']]

                for detail in details:
                    datas.append(
                        [ecg_id, ecg_info['unionId'], check_time, detail['disease_code'], detail['total_episodes'],
                         detail['total_duration'], detail['fastest_hr'], detail['longest_duration'],
                         detail['longest_rr'],
                         detail['single_count'], detail['pair_count'], detail['bigeminy_count'],
                         detail['trigeminy_count'], detail['run_count'], detail['max_consecutive'], detail['fastest_run_hr'],
                         detail['slowest_run_hr'], detail['hr_total_count']])

                sql_helper.insert_datas(table_name, datas)
