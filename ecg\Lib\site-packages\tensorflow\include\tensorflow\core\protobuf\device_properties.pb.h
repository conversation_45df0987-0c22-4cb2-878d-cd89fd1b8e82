// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/device_properties.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto;
namespace tensorflow {
class DeviceProperties;
class DevicePropertiesDefaultTypeInternal;
extern DevicePropertiesDefaultTypeInternal _DeviceProperties_default_instance_;
class DeviceProperties_EnvironmentEntry_DoNotUse;
class DeviceProperties_EnvironmentEntry_DoNotUseDefaultTypeInternal;
extern DeviceProperties_EnvironmentEntry_DoNotUseDefaultTypeInternal _DeviceProperties_EnvironmentEntry_DoNotUse_default_instance_;
class NamedDevice;
class NamedDeviceDefaultTypeInternal;
extern NamedDeviceDefaultTypeInternal _NamedDevice_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::DeviceProperties* Arena::CreateMaybeMessage<::tensorflow::DeviceProperties>(Arena*);
template<> ::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse>(Arena*);
template<> ::tensorflow::NamedDevice* Arena::CreateMaybeMessage<::tensorflow::NamedDevice>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class DeviceProperties_EnvironmentEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceProperties_EnvironmentEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceProperties_EnvironmentEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  DeviceProperties_EnvironmentEntry_DoNotUse();
  DeviceProperties_EnvironmentEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DeviceProperties_EnvironmentEntry_DoNotUse& other);
  static const DeviceProperties_EnvironmentEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DeviceProperties_EnvironmentEntry_DoNotUse*>(&_DeviceProperties_EnvironmentEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.DeviceProperties.EnvironmentEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.DeviceProperties.EnvironmentEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto.file_level_metadata[0];
  }

  public:
};

// -------------------------------------------------------------------

class DeviceProperties :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceProperties) */ {
 public:
  DeviceProperties();
  virtual ~DeviceProperties();

  DeviceProperties(const DeviceProperties& from);
  DeviceProperties(DeviceProperties&& from) noexcept
    : DeviceProperties() {
    *this = ::std::move(from);
  }

  inline DeviceProperties& operator=(const DeviceProperties& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceProperties& operator=(DeviceProperties&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeviceProperties& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceProperties* internal_default_instance() {
    return reinterpret_cast<const DeviceProperties*>(
               &_DeviceProperties_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DeviceProperties& a, DeviceProperties& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceProperties* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceProperties* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeviceProperties* New() const final {
    return CreateMaybeMessage<DeviceProperties>(nullptr);
  }

  DeviceProperties* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeviceProperties>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeviceProperties& from);
  void MergeFrom(const DeviceProperties& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceProperties* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeviceProperties";
  }
  protected:
  explicit DeviceProperties(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kEnvironmentFieldNumber = 6,
    kTypeFieldNumber = 1,
    kVendorFieldNumber = 2,
    kModelFieldNumber = 3,
    kFrequencyFieldNumber = 4,
    kNumCoresFieldNumber = 5,
    kNumRegistersFieldNumber = 7,
    kL1CacheSizeFieldNumber = 8,
    kL2CacheSizeFieldNumber = 9,
    kL3CacheSizeFieldNumber = 10,
    kSharedMemorySizePerMultiprocessorFieldNumber = 11,
    kMemorySizeFieldNumber = 12,
    kBandwidthFieldNumber = 13,
  };
  // map<string, string> environment = 6;
  int environment_size() const;
  void clear_environment();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      environment() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_environment();

  // string type = 1;
  void clear_type();
  const std::string& type() const;
  void set_type(const std::string& value);
  void set_type(std::string&& value);
  void set_type(const char* value);
  void set_type(const char* value, size_t size);
  std::string* mutable_type();
  std::string* release_type();
  void set_allocated_type(std::string* type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type(
      std::string* type);

  // string vendor = 2;
  void clear_vendor();
  const std::string& vendor() const;
  void set_vendor(const std::string& value);
  void set_vendor(std::string&& value);
  void set_vendor(const char* value);
  void set_vendor(const char* value, size_t size);
  std::string* mutable_vendor();
  std::string* release_vendor();
  void set_allocated_vendor(std::string* vendor);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_vendor();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_vendor(
      std::string* vendor);

  // string model = 3;
  void clear_model();
  const std::string& model() const;
  void set_model(const std::string& value);
  void set_model(std::string&& value);
  void set_model(const char* value);
  void set_model(const char* value, size_t size);
  std::string* mutable_model();
  std::string* release_model();
  void set_allocated_model(std::string* model);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_model();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_model(
      std::string* model);

  // int64 frequency = 4;
  void clear_frequency();
  ::PROTOBUF_NAMESPACE_ID::int64 frequency() const;
  void set_frequency(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 num_cores = 5;
  void clear_num_cores();
  ::PROTOBUF_NAMESPACE_ID::int64 num_cores() const;
  void set_num_cores(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 num_registers = 7;
  void clear_num_registers();
  ::PROTOBUF_NAMESPACE_ID::int64 num_registers() const;
  void set_num_registers(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 l1_cache_size = 8;
  void clear_l1_cache_size();
  ::PROTOBUF_NAMESPACE_ID::int64 l1_cache_size() const;
  void set_l1_cache_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 l2_cache_size = 9;
  void clear_l2_cache_size();
  ::PROTOBUF_NAMESPACE_ID::int64 l2_cache_size() const;
  void set_l2_cache_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 l3_cache_size = 10;
  void clear_l3_cache_size();
  ::PROTOBUF_NAMESPACE_ID::int64 l3_cache_size() const;
  void set_l3_cache_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 shared_memory_size_per_multiprocessor = 11;
  void clear_shared_memory_size_per_multiprocessor();
  ::PROTOBUF_NAMESPACE_ID::int64 shared_memory_size_per_multiprocessor() const;
  void set_shared_memory_size_per_multiprocessor(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 memory_size = 12;
  void clear_memory_size();
  ::PROTOBUF_NAMESPACE_ID::int64 memory_size() const;
  void set_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 bandwidth = 13;
  void clear_bandwidth();
  ::PROTOBUF_NAMESPACE_ID::int64 bandwidth() const;
  void set_bandwidth(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceProperties)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      DeviceProperties_EnvironmentEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > environment_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr vendor_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_;
  ::PROTOBUF_NAMESPACE_ID::int64 frequency_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_cores_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_registers_;
  ::PROTOBUF_NAMESPACE_ID::int64 l1_cache_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 l2_cache_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 l3_cache_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 shared_memory_size_per_multiprocessor_;
  ::PROTOBUF_NAMESPACE_ID::int64 memory_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 bandwidth_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto;
};
// -------------------------------------------------------------------

class NamedDevice :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NamedDevice) */ {
 public:
  NamedDevice();
  virtual ~NamedDevice();

  NamedDevice(const NamedDevice& from);
  NamedDevice(NamedDevice&& from) noexcept
    : NamedDevice() {
    *this = ::std::move(from);
  }

  inline NamedDevice& operator=(const NamedDevice& from) {
    CopyFrom(from);
    return *this;
  }
  inline NamedDevice& operator=(NamedDevice&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NamedDevice& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NamedDevice* internal_default_instance() {
    return reinterpret_cast<const NamedDevice*>(
               &_NamedDevice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(NamedDevice& a, NamedDevice& b) {
    a.Swap(&b);
  }
  inline void Swap(NamedDevice* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NamedDevice* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NamedDevice* New() const final {
    return CreateMaybeMessage<NamedDevice>(nullptr);
  }

  NamedDevice* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NamedDevice>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NamedDevice& from);
  void MergeFrom(const NamedDevice& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NamedDevice* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NamedDevice";
  }
  protected:
  explicit NamedDevice(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kPropertiesFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .tensorflow.DeviceProperties properties = 2;
  bool has_properties() const;
  void clear_properties();
  const ::tensorflow::DeviceProperties& properties() const;
  ::tensorflow::DeviceProperties* release_properties();
  ::tensorflow::DeviceProperties* mutable_properties();
  void set_allocated_properties(::tensorflow::DeviceProperties* properties);
  void unsafe_arena_set_allocated_properties(
      ::tensorflow::DeviceProperties* properties);
  ::tensorflow::DeviceProperties* unsafe_arena_release_properties();

  // @@protoc_insertion_point(class_scope:tensorflow.NamedDevice)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::DeviceProperties* properties_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// DeviceProperties

// string type = 1;
inline void DeviceProperties::clear_type() {
  type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DeviceProperties::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.type)
  return type_.Get();
}
inline void DeviceProperties::set_type(const std::string& value) {
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.type)
}
inline void DeviceProperties::set_type(std::string&& value) {
  
  type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceProperties.type)
}
inline void DeviceProperties::set_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceProperties.type)
}
inline void DeviceProperties::set_type(const char* value,
    size_t size) {
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceProperties.type)
}
inline std::string* DeviceProperties::mutable_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceProperties.type)
  return type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DeviceProperties::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceProperties.type)
  
  return type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceProperties::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceProperties.type)
}
inline std::string* DeviceProperties::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceProperties.type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceProperties::unsafe_arena_set_allocated_type(
    std::string* type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (type != nullptr) {
    
  } else {
    
  }
  type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceProperties.type)
}

// string vendor = 2;
inline void DeviceProperties::clear_vendor() {
  vendor_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DeviceProperties::vendor() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.vendor)
  return vendor_.Get();
}
inline void DeviceProperties::set_vendor(const std::string& value) {
  
  vendor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.vendor)
}
inline void DeviceProperties::set_vendor(std::string&& value) {
  
  vendor_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceProperties.vendor)
}
inline void DeviceProperties::set_vendor(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  vendor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceProperties.vendor)
}
inline void DeviceProperties::set_vendor(const char* value,
    size_t size) {
  
  vendor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceProperties.vendor)
}
inline std::string* DeviceProperties::mutable_vendor() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceProperties.vendor)
  return vendor_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DeviceProperties::release_vendor() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceProperties.vendor)
  
  return vendor_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceProperties::set_allocated_vendor(std::string* vendor) {
  if (vendor != nullptr) {
    
  } else {
    
  }
  vendor_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), vendor,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceProperties.vendor)
}
inline std::string* DeviceProperties::unsafe_arena_release_vendor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceProperties.vendor)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return vendor_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceProperties::unsafe_arena_set_allocated_vendor(
    std::string* vendor) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (vendor != nullptr) {
    
  } else {
    
  }
  vendor_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      vendor, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceProperties.vendor)
}

// string model = 3;
inline void DeviceProperties::clear_model() {
  model_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DeviceProperties::model() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.model)
  return model_.Get();
}
inline void DeviceProperties::set_model(const std::string& value) {
  
  model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.model)
}
inline void DeviceProperties::set_model(std::string&& value) {
  
  model_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceProperties.model)
}
inline void DeviceProperties::set_model(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceProperties.model)
}
inline void DeviceProperties::set_model(const char* value,
    size_t size) {
  
  model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceProperties.model)
}
inline std::string* DeviceProperties::mutable_model() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceProperties.model)
  return model_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DeviceProperties::release_model() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceProperties.model)
  
  return model_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceProperties::set_allocated_model(std::string* model) {
  if (model != nullptr) {
    
  } else {
    
  }
  model_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceProperties.model)
}
inline std::string* DeviceProperties::unsafe_arena_release_model() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceProperties.model)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return model_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceProperties::unsafe_arena_set_allocated_model(
    std::string* model) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (model != nullptr) {
    
  } else {
    
  }
  model_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      model, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceProperties.model)
}

// int64 frequency = 4;
inline void DeviceProperties::clear_frequency() {
  frequency_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceProperties::frequency() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.frequency)
  return frequency_;
}
inline void DeviceProperties::set_frequency(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  frequency_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.frequency)
}

// int64 num_cores = 5;
inline void DeviceProperties::clear_num_cores() {
  num_cores_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceProperties::num_cores() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.num_cores)
  return num_cores_;
}
inline void DeviceProperties::set_num_cores(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_cores_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.num_cores)
}

// map<string, string> environment = 6;
inline int DeviceProperties::environment_size() const {
  return environment_.size();
}
inline void DeviceProperties::clear_environment() {
  environment_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
DeviceProperties::environment() const {
  // @@protoc_insertion_point(field_map:tensorflow.DeviceProperties.environment)
  return environment_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
DeviceProperties::mutable_environment() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.DeviceProperties.environment)
  return environment_.MutableMap();
}

// int64 num_registers = 7;
inline void DeviceProperties::clear_num_registers() {
  num_registers_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceProperties::num_registers() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.num_registers)
  return num_registers_;
}
inline void DeviceProperties::set_num_registers(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_registers_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.num_registers)
}

// int64 l1_cache_size = 8;
inline void DeviceProperties::clear_l1_cache_size() {
  l1_cache_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceProperties::l1_cache_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.l1_cache_size)
  return l1_cache_size_;
}
inline void DeviceProperties::set_l1_cache_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  l1_cache_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.l1_cache_size)
}

// int64 l2_cache_size = 9;
inline void DeviceProperties::clear_l2_cache_size() {
  l2_cache_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceProperties::l2_cache_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.l2_cache_size)
  return l2_cache_size_;
}
inline void DeviceProperties::set_l2_cache_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  l2_cache_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.l2_cache_size)
}

// int64 l3_cache_size = 10;
inline void DeviceProperties::clear_l3_cache_size() {
  l3_cache_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceProperties::l3_cache_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.l3_cache_size)
  return l3_cache_size_;
}
inline void DeviceProperties::set_l3_cache_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  l3_cache_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.l3_cache_size)
}

// int64 shared_memory_size_per_multiprocessor = 11;
inline void DeviceProperties::clear_shared_memory_size_per_multiprocessor() {
  shared_memory_size_per_multiprocessor_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceProperties::shared_memory_size_per_multiprocessor() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.shared_memory_size_per_multiprocessor)
  return shared_memory_size_per_multiprocessor_;
}
inline void DeviceProperties::set_shared_memory_size_per_multiprocessor(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  shared_memory_size_per_multiprocessor_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.shared_memory_size_per_multiprocessor)
}

// int64 memory_size = 12;
inline void DeviceProperties::clear_memory_size() {
  memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceProperties::memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.memory_size)
  return memory_size_;
}
inline void DeviceProperties::set_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.memory_size)
}

// int64 bandwidth = 13;
inline void DeviceProperties::clear_bandwidth() {
  bandwidth_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceProperties::bandwidth() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.bandwidth)
  return bandwidth_;
}
inline void DeviceProperties::set_bandwidth(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  bandwidth_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.bandwidth)
}

// -------------------------------------------------------------------

// NamedDevice

// string name = 1;
inline void NamedDevice::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& NamedDevice::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NamedDevice.name)
  return name_.Get();
}
inline void NamedDevice::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.NamedDevice.name)
}
inline void NamedDevice::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NamedDevice.name)
}
inline void NamedDevice::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.NamedDevice.name)
}
inline void NamedDevice::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NamedDevice.name)
}
inline std::string* NamedDevice::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedDevice.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* NamedDevice::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NamedDevice.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void NamedDevice::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NamedDevice.name)
}
inline std::string* NamedDevice::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NamedDevice.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void NamedDevice::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NamedDevice.name)
}

// .tensorflow.DeviceProperties properties = 2;
inline bool NamedDevice::has_properties() const {
  return this != internal_default_instance() && properties_ != nullptr;
}
inline void NamedDevice::clear_properties() {
  if (GetArenaNoVirtual() == nullptr && properties_ != nullptr) {
    delete properties_;
  }
  properties_ = nullptr;
}
inline const ::tensorflow::DeviceProperties& NamedDevice::properties() const {
  const ::tensorflow::DeviceProperties* p = properties_;
  // @@protoc_insertion_point(field_get:tensorflow.NamedDevice.properties)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::DeviceProperties*>(
      &::tensorflow::_DeviceProperties_default_instance_);
}
inline ::tensorflow::DeviceProperties* NamedDevice::release_properties() {
  // @@protoc_insertion_point(field_release:tensorflow.NamedDevice.properties)
  
  ::tensorflow::DeviceProperties* temp = properties_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  properties_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceProperties* NamedDevice::unsafe_arena_release_properties() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NamedDevice.properties)
  
  ::tensorflow::DeviceProperties* temp = properties_;
  properties_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceProperties* NamedDevice::mutable_properties() {
  
  if (properties_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceProperties>(GetArenaNoVirtual());
    properties_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedDevice.properties)
  return properties_;
}
inline void NamedDevice::set_allocated_properties(::tensorflow::DeviceProperties* properties) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete properties_;
  }
  if (properties) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(properties);
    if (message_arena != submessage_arena) {
      properties = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, properties, submessage_arena);
    }
    
  } else {
    
  }
  properties_ = properties;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NamedDevice.properties)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
