from datetime import datetime


def convert_to_time_format(total_minutes):
    """
    将分钟转换为时间格式，如 120 分钟转换为 2小时0分钟。
    :param total_minutes: 总分钟数
    :return: 时间格式字符串，如 "2小时0分钟"
    """
    hours = total_minutes // 60  # 小时部分
    minutes = total_minutes % 60  # 分钟部分

    if hours == 0:
        return f"{minutes}分钟"
    elif hours == 24 and minutes == 0:
        return f"{hours}小时"
    else:
        return f"{hours}小时{minutes}分钟"


def convert_date_format(date_str):
    # 将输入的日期字符串转换为datetime对象
    date_obj = datetime.strptime(date_str, "%Y%m%d")

    # 将datetime对象格式化为新的日期字符串格式
    new_date_str = date_obj.strftime("%Y-%m-%d")

    return new_date_str
