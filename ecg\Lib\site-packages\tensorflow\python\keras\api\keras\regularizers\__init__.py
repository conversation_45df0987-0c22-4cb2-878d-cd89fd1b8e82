# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Built-in regularizers.
"""

from __future__ import print_function as _print_function

import sys as _sys

from tensorflow.python.keras.regularizers import L1
from tensorflow.python.keras.regularizers import L1 as l1
from tensorflow.python.keras.regularizers import L1L2
from tensorflow.python.keras.regularizers import L2
from tensorflow.python.keras.regularizers import L2 as l2
from tensorflow.python.keras.regularizers import Regularizer
from tensorflow.python.keras.regularizers import deserialize
from tensorflow.python.keras.regularizers import get
from tensorflow.python.keras.regularizers import l1_l2
from tensorflow.python.keras.regularizers import serialize

del _print_function

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.regularizers", public_apis=None, deprecation=True,
      has_lite=False)
