# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.distribute.experimental namespace.
"""

from __future__ import print_function as _print_function

import sys as _sys

from . import coordinator
from . import partitioners
from tensorflow.python.distribute.central_storage_strategy import CentralStorageStrategy
from tensorflow.python.distribute.collective_all_reduce_strategy import _CollectiveAllReduceStrategyExperimental as MultiWorkerMirroredStrategy
from tensorflow.python.distribute.collective_util import CommunicationImplementation
from tensorflow.python.distribute.collective_util import CommunicationImplementation as CollectiveCommunication
from tensorflow.python.distribute.collective_util import Hints as CollectiveHints
from tensorflow.python.distribute.collective_util import _OptionsExported as CommunicationOptions
from tensorflow.python.distribute.distribute_lib import ValueContext
from tensorflow.python.distribute.parameter_server_strategy_v2 import ParameterServerStrategyV2 as ParameterServerStrategy
from tensorflow.python.distribute.tpu_strategy import TPUStrategy

del _print_function
