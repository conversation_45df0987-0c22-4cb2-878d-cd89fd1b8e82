"""
Template for functions of IndexEngine subclasses.

WARNING: DO NOT edit .pxi FILE directly, .pxi is generated from .pxi.in
"""

# ----------------------------------------------------------------------
# IndexEngine Subclass Methods
# ----------------------------------------------------------------------

{{py:

# name, dtype
dtypes = [('Float64', 'float64'),
          ('Float32', 'float32'),
          ('Int64', 'int64'),
          ('Int32', 'int32'),
          ('Int16', 'int16'),
          ('Int8', 'int8'),
          ('UInt64', 'uint64'),
          ('UInt32', 'uint32'),
          ('UInt16', 'uint16'),
          ('UInt8', 'uint8'),
          ]
}}

{{for name, dtype in dtypes}}


cdef class {{name}}Engine(IndexEngine):

    cdef _make_hash_table(self, Py_ssize_t n):
        return _hash.{{name}}HashTable(n)

    cdef _check_type(self, object val):
    {{if name not in {'Float64', 'Float32'} }}
        if not util.is_integer_object(val):
            raise KeyError(val)
        {{if name.startswith("U")}}
        if val < 0:
            # cannot have negative values with unsigned int dtype
            raise KeyError(val)
        {{endif}}
    {{else}}
        if not util.is_integer_object(val) and not util.is_float_object(val):
            # in particular catch bool and avoid casting True -> 1.0
            raise KeyError(val)
    {{endif}}


{{endfor}}
