This directory contains numerical data for testing special functions.
The data is in version control as text files.

The data is automatically packed into npz files by setup.py.
The npz files should not be checked in version control.

The data in gsl is computed using the GNU scientific library, the data
in local is computed using mpmath, and the data in boost is a copy of
data distributed with the boost library and comes with the following
license:

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

=========

Copyright holders of each file are listed here:

Jamfile.v2:# Copyright Daryle Walker, Hubert Holin, John Maddock 2006 - 2007
acosh_data.ipp:// Copyright John Maddock 2008.
acosh_test.hpp://  (C) Copyright Hubert Holin 2003.
almost_equal.ipp:// Copyright (c) 2006 Johan Rade
asinh_data.ipp:// Copyright John Maddock 2008.
asinh_test.hpp://  (C) Copyright Hubert Holin 2003.
assoc_legendre_p.ipp://  (C) Copyright John Maddock 2006-7.
atanh_data.ipp:// Copyright John Maddock 2008.
atanh_test.hpp://  (C) Copyright Hubert Holin 2003.
bessel_i_data.ipp://  Copyright (c) 2007 John Maddock
bessel_i_int_data.ipp://  Copyright (c) 2007 John Maddock
bessel_j_data.ipp://  Copyright (c) 2007 John Maddock
bessel_j_int_data.ipp://  Copyright (c) 2007 John Maddock
bessel_j_large_data.ipp://  Copyright (c) 2007 John Maddock
bessel_k_data.ipp://  Copyright (c) 2007 John Maddock
bessel_k_int_data.ipp://  Copyright (c) 2007 John Maddock
bessel_y01_data.ipp://  Copyright (c) 2007 John Maddock
bessel_yn_data.ipp://  Copyright (c) 2007 John Maddock
bessel_yv_data.ipp://  Copyright (c) 2007 John Maddock
beta_exp_data.ipp://  (C) Copyright John Maddock 2006.
beta_med_data.ipp://  (C) Copyright John Maddock 2006.
beta_small_data.ipp://  (C) Copyright John Maddock 2006.
binomial_data.ipp://  (C) Copyright John Maddock 2006-7.
binomial_large_data.ipp://  (C) Copyright John Maddock 2006-7.
binomial_quantile.ipp://  (C) Copyright John Maddock 2006-7.
cbrt_data.ipp://  (C) Copyright John Maddock 2006-7.
common_factor_test.cpp://  (C) Copyright Daryle Walker 2001, 2006.
compile_test/tools_rational_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_real_cast_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_remez_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_chi_squared_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_complement_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_sign_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_digamma_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_trunc_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/constants_incl_test.cpp://  Copyright John Maddock 2012.
compile_test/sf_sinc_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_binomial_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_binomial_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_test_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_normal_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_sinhc_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_ellint_rc_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_sin_pi_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_sph_harm_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_poisson_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/test_traits.cpp://  Copyright John Maddock 2007.
compile_test/dist_gamma_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_cos_pi_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_logistic_incl_test.cpp://  Copyright John Maddock 2008.
compile_test/sf_fpclassify_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/compl_atanh_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_precision_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_hankel_incl_test.cpp://  Copyright John Maddock 2012.
compile_test/sf_cbrt_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_nc_beta_incl_test.cpp://  Copyright John Maddock 2008.
compile_test/sf_legendre_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_stats_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_polynomial_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_config_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_exponential_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_students_t_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_inv_gamma_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/compl_acosh_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_beta_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_fisher_f_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_triangular_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/instantiate.hpp://  Copyright John Maddock 2006.
compile_test/instantiate.hpp://  Copyright Paul A. Bristow 2007, 2010.
compile_test/tools_solve_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_next_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/generate.sh://  Copyright John Maddock 2006.
compile_test/generate.sh://  Copyright John Maddock 2006.
compile_test/generate.sh://  Copyright John Maddock 2006.
compile_test/distribution_concept_check.cpp://  Copyright John Maddock 2006.
compile_test/sf_laguerre_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/tr1_incl_test.cpp://  Copyright John Maddock 2008.
compile_test/sf_ellint_rj_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_nc_chi_squ_incl_test.cpp://  Copyright John Maddock 2008.
compile_test/dist_skew_norm_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_modf_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_find_location_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/compl_acos_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_ellint_rd_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_roots_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_test_data_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/compl_abs_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_nc_t_incl_test.cpp://  Copyright John Maddock 2008.
compile_test/sf_factorials_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_gamma_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/compl_atan_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_powm1_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_hypot_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_pareto_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_round_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_weibull_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/std_real_concept_check.cpp://  Copyright John Maddock 2006.
compile_test/dist_hypergeo_incl_test.cpp://  Copyright John Maddock 2008.
compile_test/dist_inv_chi_sq_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_sqrt1pm1_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_log1p_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_jacobi_incl_test.cpp://  Copyright John Maddock 2012.
compile_test/dist_neg_binom_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_nc_f_incl_test.cpp://  Copyright John Maddock 2008.
compile_test/dist_find_scale_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_bessel_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_minima_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/compl_asin_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_extreme_val_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_lanczos_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_uniform_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/test_compile_result.hpp://  Copyright John Maddock 2007.
compile_test/tools_series_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_ellint_3_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_ellint_rf_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_ellint_2_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_hermite_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/poison.hpp://  Copyright John Maddock 2013.
compile_test/sf_zeta_incl_test.cpp://  Copyright John Maddock 2007.
compile_test/dist_laplace_incl_test.cpp://  Copyright John Maddock 2008.
compile_test/sf_expint_incl_test.cpp://  Copyright John Maddock 2007.
compile_test/sf_expm1_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_bernoulli_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/compl_asinh_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_beta_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/tools_fraction_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_owens_t_incl_test.cpp://  Copyright John Maddock 2012.
compile_test/tools_toms748_inc_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_ellint_1_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_erf_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/main.cpp://  Copyright John Maddock 2009.
compile_test/sf_math_fwd_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/sf_airy_incl_test.cpp://  Copyright John Maddock 2012.
compile_test/dist_lognormal_incl_test.cpp://  Copyright John Maddock 2006.
compile_test/dist_cauchy_incl_test.cpp://  Copyright John Maddock 2006.
complex_test.cpp://  (C) Copyright John Maddock 2005.
digamma_data.ipp://  (C) Copyright John Maddock 2006-7.
digamma_neg_data.ipp://  (C) Copyright John Maddock 2006-7.
digamma_root_data.ipp://  (C) Copyright John Maddock 2006-7.
digamma_small_data.ipp://  (C) Copyright John Maddock 2006-7.
e_float_concept_check.cpp://  Copyright John Maddock 2011.
ellint_e2_data.ipp://  Copyright (c) 2006 John Maddock
ellint_e_data.ipp://  Copyright (c) 2006 John Maddock
ellint_f_data.ipp://  Copyright (c) 2006 John Maddock
ellint_k_data.ipp://  (C) Copyright John Maddock 2006-7.
ellint_pi2_data.ipp://  Copyright (c) 2006 John Maddock
ellint_pi3_data.ipp://  Copyright (c) 2006 John Maddock
ellint_pi3_large_data.ipp://  Copyright (c) 2006 John Maddock
ellint_rc_data.ipp://  Copyright (c) 2006 John Maddock
ellint_rd_data.ipp://  Copyright (c) 2006 John Maddock
ellint_rf_data.ipp://  Copyright (c) 2006 John Maddock
ellint_rj_data.ipp://  Copyright (c) 2006 John Maddock
erf_data.ipp://  (C) Copyright John Maddock 2006-7.
erf_inv_data.ipp://  (C) Copyright John Maddock 2006-7.
erf_large_data.ipp://  (C) Copyright John Maddock 2006-7.
erf_small_data.ipp://  (C) Copyright John Maddock 2006.
erfc_inv_big_data.ipp://  (C) Copyright John Maddock 2006-7.
erfc_inv_data.ipp://  (C) Copyright John Maddock 2006-7.
expint_1_data.ipp://  Copyright John Maddock 2008.
expint_data.ipp://  Copyright John Maddock 2008.
expint_small_data.ipp://  Copyright John Maddock 2008.
expinti_data.ipp://  Copyright John Maddock 2008.
expinti_data_double.ipp://  Copyright John Maddock 2008.
expinti_data_long.ipp://  Copyright John Maddock 2008.
functor.hpp://  (C) Copyright John Maddock 2007.
gamma_inv_big_data.ipp://  (C) Copyright John Maddock 2006-7.
gamma_inv_data.ipp://  (C) Copyright John Maddock 2006-7.
gamma_inv_small_data.ipp://  (C) Copyright John Maddock 2006-7.
handle_test_result.hpp://  (C) Copyright John Maddock 2006-7.
hermite.ipp://  (C) Copyright John Maddock 2006-7.
hypergeometric_dist_data2.ipp:// Copyright John Maddock 2008
hypergeometric_test_data.ipp:// Copyright Gautam Sewani 2008
hypot_test.cpp://  (C) Copyright John Maddock 2005.
ibeta_data.ipp://  (C) Copyright John Maddock 2006.
ibeta_int_data.ipp://  (C) Copyright John Maddock 2006-7.
ibeta_inv_data.ipp://  (C) Copyright John Maddock 2006-7.
ibeta_inva_data.ipp://  (C) Copyright John Maddock 2006-7.
ibeta_large_data.ipp://  (C) Copyright John Maddock 2006.
ibeta_small_data.ipp://  (C) Copyright John Maddock 2006.
igamma_big_data.ipp://  (C) Copyright John Maddock 2006.
igamma_int_data.ipp://  (C) Copyright John Maddock 2006-7.
igamma_inva_data.ipp://  (C) Copyright John Maddock 2006-7.
igamma_med_data.ipp://  (C) Copyright John Maddock 2006.
igamma_small_data.ipp://  (C) Copyright John Maddock 2006.
jacobi_elliptic.ipp:// Copyright John Maddock 2012.
jacobi_elliptic_small.ipp:// Copyright John Maddock 2012.
jacobi_large_phi.ipp:// Copyright John Maddock 2012.
jacobi_near_1.ipp:// Copyright John Maddock 2012.
laguerre2.ipp://  (C) Copyright John Maddock 2006-7.
laguerre3.ipp://  (C) Copyright John Maddock 2006-7.
legendre_p.ipp://  (C) Copyright John Maddock 2006-7.
legendre_p_large.ipp://  (C) Copyright John Maddock 2006-7.
log1p_expm1_data.ipp://  (C) Copyright John Maddock 2006-7.
log1p_expm1_test.cpp://  Copyright John Maddock 2005.
log1p_expm1_test.cpp://  Copyright Paul A. Bristow 2010
log1p_expm1_test.hpp://  Copyright John Maddock 2005.
log1p_expm1_test.hpp://  Copyright Paul A. Bristow 2010
mpfr_concept_check.cpp://  Copyright John Maddock 2007-8.
mpreal_concept_check.cpp://  Copyright John Maddock 2007-8.
multiprc_concept_check_1.cpp://  Copyright John Maddock 2013.
multiprc_concept_check_2.cpp://  Copyright John Maddock 2013.
multiprc_concept_check_3.cpp://  Copyright John Maddock 2013.
multiprc_concept_check_4.cpp://  Copyright John Maddock 2013.
ncbeta.ipp://  Copyright John Maddock 2008.
ncbeta_big.ipp://  Copyright John Maddock 2008.
nccs.ipp://  Copyright John Maddock 2008.
nccs_big.ipp:// Copyright John Maddock 2008.
nct.ipp:// Copyright John Maddock 2008.
nct_asym.ipp:// Copyright John Maddock 2012.
nct_small_delta.ipp:// Copyright John Maddock 2012.
negative_binomial_quantile.ipp://  (C) Copyright John Maddock 2006-7.
ntl_concept_check.cpp://  Copyright John Maddock 2007-8.
ntl_concept_check.cpp://  Copyright Paul A. Bristow 2009, 2011
owens_t.ipp://  Copyright John Maddock 2012.
owens_t_T7.hpp:// Copyright (C) Benjamin Sobotta 2012
owens_t_large_data.ipp://  Copyright John Maddock 2012.
pch.hpp://  Copyright John Maddock 2008.
pch_light.hpp://  Copyright John Maddock 2008.
poisson_quantile.ipp://  (C) Copyright John Maddock 2006-7.
pow_test.cpp://  (C) Copyright Bruno Lalande 2008.
powm1_sqrtp1m1_test.cpp://  (C) Copyright John Maddock 2006.
powm1_sqrtp1m1_test.hpp://  Copyright John Maddock 2006.
s_.ipp:// Copyright (c) 2006 Johan Rade
s_.ipp:// Copyright (c) 2012 Paul A. Bristow
sinc_test.hpp://  (C) Copyright Hubert Holin 2003.
sinhc_test.hpp://  (C) Copyright Hubert Holin 2003.
special_functions_test.cpp://  (C) Copyright Hubert Holin 2003.
special_functions_test.cpp:    BOOST_TEST_MESSAGE("(C) Copyright Hubert Holin 2003-2005.");
sph_bessel_data.ipp://  Copyright (c) 2007 John Maddock
sph_neumann_data.ipp://  Copyright (c) 2007 John Maddock
spherical_harmonic.ipp://  (C) Copyright John Maddock 2006-7.
std_real_concept_check.cpp://  Copyright John Maddock 2006.
table_type.hpp:// Copyright John Maddock 2012.
test_airy.cpp://  Copyright John Maddock 2012
test_archive.cpp:// Copyright (c) 2006 Johan Rade
test_archive.cpp:// Copyright (c) 2011 Paul A. Bristow - filename changes for boost-trunk.
test_basic_nonfinite.cpp:// Copyright (c) 2006 Johan Rade
test_basic_nonfinite.cpp:// Copyright (c) 2011 Paul A. Bristow comments
test_basic_nonfinite.cpp:// Copyright (c) 2011 John Maddock
test_bernoulli.cpp:// Copyright John Maddock 2006.
test_bernoulli.cpp:// Copyright  Paul A. Bristow 2007, 2012.
test_bessel_airy_zeros.cpp://  Copyright John Maddock 2013
test_bessel_airy_zeros.cpp://  Copyright Christopher Kormanyos 2013.
test_bessel_airy_zeros.cpp://  Copyright Paul A. Bristow 2013.
test_bessel_hooks.hpp://  (C) Copyright John Maddock 2007.
test_bessel_i.cpp://  (C) Copyright John Maddock 2007.
test_bessel_i.hpp://  (C) Copyright John Maddock 2007.
test_bessel_j.cpp://  (C) Copyright John Maddock 2007.
test_bessel_j.hpp://  (C) Copyright John Maddock 2007.
test_bessel_k.cpp://  Copyright John Maddock 2006, 2007
test_bessel_k.cpp://  Copyright Paul A. Bristow 2007
test_bessel_k.hpp://  (C) Copyright John Maddock 2007.
test_bessel_y.cpp://  (C) Copyright John Maddock 2007.
test_bessel_y.hpp://  (C) Copyright John Maddock 2007.
test_beta.cpp:// Copyright John Maddock 2006.
test_beta.cpp:// Copyright Paul A. Bristow 2007, 2009
test_beta.hpp:// Copyright John Maddock 2006.
test_beta.hpp:// Copyright Paul A. Bristow 2007, 2009
test_beta_dist.cpp:// Copyright John Maddock 2006.
test_beta_dist.cpp:// Copyright  Paul A. Bristow 2007, 2009, 2010, 2012.
test_beta_hooks.hpp://  (C) Copyright John Maddock 2006.
test_binomial.cpp:// Copyright John Maddock 2006.
test_binomial.cpp:// Copyright  Paul A. Bristow 2007.
test_binomial_coeff.cpp://  (C) Copyright John Maddock 2006.
test_binomial_coeff.hpp:// Copyright John Maddock 2006.
test_binomial_coeff.hpp:// Copyright Paul A. Bristow 2007, 2009
test_carlson.cpp://  Copyright 2006 John Maddock
test_carlson.cpp:// Copyright Paul A. Bristow 2007.
test_carlson.hpp:// Copyright John Maddock 2006.
test_carlson.hpp:// Copyright Paul A. Bristow 2007, 2009
test_cauchy.cpp:// Copyright John Maddock 2006, 2007.
test_cauchy.cpp:// Copyright Paul A. Bristow 2007
test_cbrt.cpp://  Copyright John Maddock 2006.
test_cbrt.cpp://  Copyright Paul A. Bristow 2010
test_cbrt.hpp:// Copyright John Maddock 2006.
test_cbrt.hpp:// Copyright Paul A. Bristow 2007, 2009
test_chi_squared.cpp:// Copyright Paul A. Bristow 2006.
test_chi_squared.cpp:// Copyright John Maddock 2007.
test_classify.cpp://  Copyright John Maddock 2006.
test_classify.cpp://  Copyright Paul A. Bristow 2007
test_common_factor_gmpxx.cpp://  (C) Copyright John Maddock 2010.
test_constant_generate.cpp:// Copyright John Maddock 2010.
test_constants.cpp:// Copyright Paul Bristow 2007, 2011.
test_constants.cpp:// Copyright John Maddock 2006, 2011.
test_digamma.cpp://  (C) Copyright John Maddock 2006.
test_digamma.hpp:// Copyright John Maddock 2006.
test_digamma.hpp:// Copyright Paul A. Bristow 2007, 2009
test_dist_overloads.cpp:// Copyright John Maddock 2006.
test_dist_overloads.cpp:// Copyright Paul A. Bristow 2007.
test_ellint_1.cpp://  Copyright Xiaogang Zhang 2006
test_ellint_1.cpp://  Copyright John Maddock 2006, 2007
test_ellint_1.cpp://  Copyright Paul A. Bristow 2007
test_ellint_1.hpp:// Copyright John Maddock 2006.
test_ellint_1.hpp:// Copyright Paul A. Bristow 2007, 2009
test_ellint_2.cpp://  Copyright Xiaogang Zhang 2006
test_ellint_2.cpp://  Copyright John Maddock 2006, 2007
test_ellint_2.cpp://  Copyright Paul A. Bristow 2007
test_ellint_2.hpp:// Copyright John Maddock 2006.
test_ellint_2.hpp:// Copyright Paul A. Bristow 2007, 2009
test_ellint_3.cpp://  Copyright Xiaogang Zhang 2006
test_ellint_3.cpp://  Copyright John Maddock 2006, 2007
test_ellint_3.cpp://  Copyright Paul A. Bristow 2007
test_ellint_3.hpp:// Copyright John Maddock 2006.
test_ellint_3.hpp:// Copyright Paul A. Bristow 2007, 2009
test_erf.cpp://  Copyright John Maddock 2006.
test_erf.cpp://  Copyright Paul A. Bristow 2007
test_erf.hpp:// Copyright John Maddock 2006.
test_erf.hpp:// Copyright Paul A. Bristow 2007, 2009
test_erf_hooks.hpp://  (C) Copyright John Maddock 2006.
test_error_handling.cpp:// Copyright Paul A. Bristow 2006-7.
test_error_handling.cpp:// Copyright John Maddock 2006-7.
test_expint.cpp://  (C) Copyright John Maddock 2007.
test_expint.hpp:// Copyright John Maddock 2006.
test_expint.hpp:// Copyright Paul A. Bristow 2007, 2009
test_expint_hooks.hpp://  (C) Copyright John Maddock 2006.
test_exponential_dist.cpp:// Copyright John Maddock 2006.
test_exponential_dist.cpp:// Copyright Paul A. Bristow 2007.
test_extreme_value.cpp:// Copyright John Maddock 2006.
test_factorials.cpp://  Copyright John Maddock 2006.
test_find_location.cpp:// Copyright John Maddock 2007.
test_find_location.cpp:// Copyright Paul A. Bristow 2007.
test_find_scale.cpp:// Copyright John Maddock 2007.
test_find_scale.cpp:// Copyright Paul A. Bristow 2007.
test_fisher_f.cpp:// Copyright Paul A. Bristow 2006.
test_fisher_f.cpp:// Copyright John Maddock 2007.
test_fisher_f.cpp:   // Distcalc version 1.2 Copyright 2002 H Lohninger, TU Wein
test_gamma.cpp://  (C) Copyright John Maddock 2006.
test_gamma.hpp:// Copyright John Maddock 2006.
test_gamma.hpp:// Copyright Paul A. Bristow 2007, 2009
test_gamma_data.ipp://  (C) Copyright John Maddock 2006.
test_gamma_dist.cpp:// Copyright John Maddock 2006.
test_gamma_dist.cpp:// Copyright Paul A. Bristow 2007, 2010.
test_gamma_hooks.hpp://  (C) Copyright John Maddock 2006.
test_geometric.cpp:// Copyright Paul A. Bristow 2010.
test_geometric.cpp:// Copyright John Maddock 2010.
test_hankel.cpp://  Copyright John Maddock 2012
test_hermite.cpp://  Copyright John Maddock 2006, 2007
test_hermite.cpp://  Copyright Paul A. Bristow 2007
test_hermite.hpp:// Copyright John Maddock 2006.
test_hermite.hpp:// Copyright Paul A. Bristow 2007, 2009
test_hypergeometric_dist.cpp:// Copyright John Maddock 2008
test_hypergeometric_dist.cpp:// Copyright Paul A. Bristow 
test_hypergeometric_dist.cpp:// Copyright Gautam Sewani
test_ibeta.cpp://  (C) Copyright John Maddock 2006.
test_ibeta.hpp:// Copyright John Maddock 2006.
test_ibeta.hpp:// Copyright Paul A. Bristow 2007, 2009
test_ibeta_inv.cpp://  (C) Copyright John Maddock 2006.
test_ibeta_inv.hpp:// Copyright John Maddock 2006.
test_ibeta_inv.hpp:// Copyright Paul A. Bristow 2007, 2009
test_ibeta_inv_ab.cpp://  (C) Copyright John Maddock 2006.
test_ibeta_inv_ab.hpp:// Copyright John Maddock 2006.
test_ibeta_inv_ab.hpp:// Copyright Paul A. Bristow 2007, 2009
test_igamma.cpp://  (C) Copyright John Maddock 2006.
test_igamma.hpp:// Copyright John Maddock 2006.
test_igamma.hpp:// Copyright Paul A. Bristow 2007, 2009
test_igamma_inv.cpp://  (C) Copyright John Maddock 2006.
test_igamma_inv.hpp:// Copyright John Maddock 2006.
test_igamma_inv.hpp:// Copyright Paul A. Bristow 2007, 2009
test_igamma_inva.cpp://  (C) Copyright John Maddock 2006.
test_igamma_inva.hpp:// Copyright John Maddock 2006.
test_igamma_inva.hpp:// Copyright Paul A. Bristow 2007, 2009
test_instances/double_test_instances_4.cpp:// Copyright John Maddock 2011.
test_instances/ldouble_test_instances_4.cpp:// Copyright John Maddock 2011.
test_instances/float_test_instances_8.cpp:// Copyright John Maddock 2011.
test_instances/double_test_instances_9.cpp:// Copyright John Maddock 2011.
test_instances/Jamfile.v2:# Copyright ohn Maddock 2012
test_instances/real_concept_test_instances_5.cpp:// Copyright John Maddock 2011.
test_instances/ldouble_test_instances_6.cpp:// Copyright John Maddock 2011.
test_instances/real_concept_test_instances_4.cpp:// Copyright John Maddock 2011.
test_instances/double_test_instances_7.cpp:// Copyright John Maddock 2011.
test_instances/real_concept_test_instances_2.cpp:// Copyright John Maddock 2011.
test_instances/double_test_instances_5.cpp:// Copyright John Maddock 2011.
test_instances/ldouble_test_instances_9.cpp:// Copyright John Maddock 2011.
test_instances/real_concept_test_instances_1.cpp:// Copyright John Maddock 2011.
test_instances/float_test_instances_6.cpp:// Copyright John Maddock 2011.
test_instances/real_concept_test_instances_6.cpp:// Copyright John Maddock 2011.
test_instances/ldouble_test_instances_7.cpp:// Copyright John Maddock 2011.
test_instances/real_concept_test_instances_7.cpp:// Copyright John Maddock 2011.
test_instances/float_test_instances_3.cpp:// Copyright John Maddock 2011.
test_instances/double_test_instances_6.cpp:// Copyright John Maddock 2011.
test_instances/real_concept_test_instances_9.cpp:// Copyright John Maddock 2011.
test_instances/double_test_instances_2.cpp:// Copyright John Maddock 2011.
test_instances/pch.hpp://  Copyright John Maddock 2012.
test_instances/ldouble_test_instances_2.cpp:// Copyright John Maddock 2011.
test_instances/long_double_test_instances_1.cpp:// Copyright John Maddock 2011.
test_instances/float_test_instances_7.cpp:// Copyright John Maddock 2011.
test_instances/test_instances.hpp:// Copyright John Maddock 2011.
test_instances/double_test_instances_10.cpp:// Copyright John Maddock 2011.
test_instances/double_test_instances_3.cpp:// Copyright John Maddock 2011.
test_instances/ldouble_test_instances_3.cpp:// Copyright John Maddock 2011.
test_instances/real_concept_test_instances_10.cpp:// Copyright John Maddock 2011.
test_instances/float_test_instances_5.cpp:// Copyright John Maddock 2011.
test_instances/real_concept_test_instances_8.cpp:// Copyright John Maddock 2011.
test_instances/ldouble_test_instances_8.cpp:// Copyright John Maddock 2011.
test_instances/double_test_instances_1.cpp:// Copyright John Maddock 2011.
test_instances/float_test_instances_10.cpp:// Copyright John Maddock 2011.
test_instances/ldouble_test_instances_10.cpp:// Copyright John Maddock 2011.
test_instances/float_test_instances_9.cpp:// Copyright John Maddock 2011.
test_instances/float_test_instances_4.cpp:// Copyright John Maddock 2011.
test_instances/real_concept_test_instances_3.cpp:// Copyright John Maddock 2011.
test_instances/float_test_instances_2.cpp:// Copyright John Maddock 2011.
test_instances/float_test_instances_1.cpp:// Copyright John Maddock 2011.
test_instances/double_test_instances_8.cpp:// Copyright John Maddock 2011.
test_instances/ldouble_test_instances_5.cpp:// Copyright John Maddock 2011.
test_instantiate1.cpp://  Copyright John Maddock 2006.
test_instantiate2.cpp://  Copyright John Maddock 2006.
test_inv_hyp.cpp://  (C) Copyright John Maddock 2006.
test_inverse_chi_squared.cpp:// Copyright Paul A. Bristow 2010.
test_inverse_chi_squared.cpp:// Copyright John Maddock 2010.
test_inverse_chi_squared_distribution.cpp:// Copyright Paul A. Bristow 2010.
test_inverse_chi_squared_distribution.cpp:// Copyright John Maddock 2010.
test_inverse_gamma_distribution.cpp:// Copyright Paul A. Bristow 2010.
test_inverse_gamma_distribution.cpp:// Copyright John Maddock 2010.
test_inverse_gaussian.cpp:// Copyright Paul A. Bristow 2010.
test_inverse_gaussian.cpp:// Copyright John Maddock 2010.
test_jacobi.cpp://  Copyright John Maddock 2012
test_jacobi.hpp:// Copyright John Maddock 2006.
test_jacobi.hpp:// Copyright Paul A. Bristow 2007, 2009
test_laguerre.cpp://  (C) Copyright John Maddock 2006.
test_laguerre.hpp:// Copyright John Maddock 2006.
test_laguerre.hpp:// Copyright Paul A. Bristow 2007, 2009
test_laplace.cpp://  Copyright Thijs van den Berg, 2008.
test_laplace.cpp://  Copyright John Maddock 2008.
test_laplace.cpp://  Copyright Paul A. Bristow 2008, 2009.
test_ldouble_simple.cpp:// Copyright John Maddock 2013.
test_legacy_nonfinite.cpp:// Copyright (c) 2006 Johan Rade
test_legacy_nonfinite.cpp:// Copyright (c) 2011 Paul A. Bristow comments
test_legendre.cpp://  (C) Copyright John Maddock 2006.
test_legendre.hpp:// Copyright John Maddock 2006.
test_legendre.hpp:// Copyright Paul A. Bristow 2007, 2009
test_legendre_hooks.hpp://  (C) Copyright John Maddock 2006.
test_lexical_cast.cpp:// Copyright (c) 2006 Johan Rade
test_lexical_cast.cpp:// Copyright (c) 2011 Paul A. Bristow incorporated Boost.Math
test_logistic_dist.cpp:// Copyright 2008 Gautam Sewani
test_lognormal.cpp:// Copyright John Maddock 2006.
test_lognormal.cpp:// Copyright Paul A. Bristow 2007
test_long_double_support.cpp:// Copyright John Maddock 2009
test_math_fwd.cpp://  Copyright John Maddock 2010.
test_math_fwd.cpp://  Copyright Paul A. Bristow 2010.
test_minima.cpp://  Copyright John Maddock 2006.
test_minima.cpp://  Copyright Paul A. Bristow 2007.
test_nc_beta.cpp:// Copyright John Maddock 2008.
test_nc_chi_squared.cpp:// Copyright John Maddock 2008.
test_nc_f.cpp:// Copyright John Maddock 2008.
test_nc_t.cpp:// Copyright John Maddock 2008, 2012.
test_nc_t.cpp:// Copyright Paul A. Bristow 2012.
test_ncbeta_hooks.hpp://  (C) Copyright John Maddock 2008.
test_nccs_hooks.hpp://  (C) Copyright John Maddock 2008.
test_negative_binomial.cpp:// Copyright Paul A. Bristow 2007.
test_negative_binomial.cpp:// Copyright John Maddock 2006.
test_next.cpp://  (C) Copyright John Maddock 2008.
test_nonfinite_io.cpp:// Copyright 2011 Paul A. Bristow 
test_nonfinite_trap.cpp:// Copyright (c) 2006 Johan Rade
test_nonfinite_trap.cpp:// Copyright (c) 2011 Paul A. Bristow To incorporate into Boost.Math
test_normal.cpp:// Copyright Paul A. Bristow 2010.
test_normal.cpp:// Copyright John Maddock 2007.
test_out_of_range.hpp:// Copyright John Maddock 2012.
test_owens_t.cpp:// Copyright Paul A. Bristow 2012.
test_owens_t.cpp:// Copyright Benjamin Sobotta 2012.
test_pareto.cpp:// Copyright Paul A. Bristow 2007, 2009.
test_pareto.cpp:// Copyright John Maddock 2006.
test_poisson.cpp:// Copyright Paul A. Bristow 2007.
test_poisson.cpp:// Copyright John Maddock 2006.
test_policy.cpp:// Copyright John Maddock 2007.
test_policy_2.cpp:// Copyright John Maddock 2007.
test_policy_3.cpp:// Copyright John Maddock 2007.
test_policy_4.cpp:// Copyright John Maddock 2007.
test_policy_5.cpp:// Copyright John Maddock 2007.
test_policy_6.cpp:// Copyright John Maddock 2007.
test_policy_7.cpp:// Copyright John Maddock 2007.
test_policy_8.cpp:// Copyright John Maddock 2007.
test_policy_sf.cpp://  (C) Copyright John Maddock 2007.
test_print_info_on_type.cpp:// Copyright John Maddock 2010.
test_rational_instances/test_rational_ldouble2.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_float2.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_double2.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_double3.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_ldouble1.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_float4.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_double5.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_double4.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_real_concept1.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_real_concept3.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational.hpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_ldouble3.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_float3.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_real_concept5.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_ldouble5.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_ldouble4.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_double1.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_real_concept4.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_real_concept2.cpp://  (C) Copyright John Maddock 2006-7.
test_rational_instances/test_rational_float1.cpp://  (C) Copyright John Maddock 2006-7.
test_rationals.cpp://  (C) Copyright John Maddock 2006.
test_rayleigh.cpp:// Copyright John Maddock 2006.
test_real_concept.cpp:// Copyright John Maddock 2010
test_real_concept_neg_bin.cpp:// Copyright Paul A. Bristow 2010.
test_real_concept_neg_bin.cpp:// Copyright John Maddock 2010.
test_remez.cpp://  Copyright John Maddock 2006
test_remez.cpp://  Copyright Paul A. Bristow 2007
test_roots.cpp://  (C) Copyright John Maddock 2006.
test_round.cpp://  (C) Copyright John Maddock 2007.
test_sign.cpp:#define BOOST_TEST_MAIN// Copyright John Maddock 2008
test_sign.cpp://  (C) Copyright Paul A. Bristow 2011 (added tests for changesign)
test_signed_zero.cpp:// Copyright 2006 Johan Rade
test_signed_zero.cpp:// Copyright 2011 Paul A. Bristow  To incorporate into Boost.Math
test_signed_zero.cpp:// Copyright 2012 Paul A. Bristow with new tests.
test_skew_normal.cpp:// Copyright Paul A. Bristow 2012.
test_skew_normal.cpp:// Copyright John Maddock 2012.
test_skew_normal.cpp:// Copyright Benjamin Sobotta 2012
test_spherical_harmonic.cpp://  (C) Copyright John Maddock 2006.
test_students_t.cpp:// Copyright Paul A. Bristow 2006.
test_students_t.cpp:// Copyright John Maddock 2006.
test_tgamma_ratio.cpp://  (C) Copyright John Maddock 2006.
test_tgamma_ratio.hpp:// Copyright John Maddock 2006.
test_tgamma_ratio.hpp:// Copyright Paul A. Bristow 2007, 2009
test_toms748_solve.cpp://  (C) Copyright John Maddock 2006.
test_tr1.c:/*  (C) Copyright John Maddock 2008.
test_tr1.cpp://  (C) Copyright John Maddock 2008.
test_triangular.cpp:// Copyright Paul Bristow 2006, 2007.
test_triangular.cpp:// Copyright John Maddock 2006, 2007.
test_uniform.cpp:// Copyright Paul Bristow 2007.
test_uniform.cpp:// Copyright John Maddock 2006.
test_weibull.cpp:// Copyright John Maddock 2006, 2012.
test_weibull.cpp:// Copyright Paul A. Bristow 2007, 2012.
test_zeta.cpp://  (C) Copyright John Maddock 2006.
test_zeta.hpp:// Copyright John Maddock 2006.
test_zeta.hpp:// Copyright Paul A. Bristow 2007, 2009
test_zeta_hooks.hpp://  (C) Copyright John Maddock 2006.
tgamma_delta_ratio_data.ipp://  (C) Copyright John Maddock 2006-7.
tgamma_delta_ratio_int.ipp://  (C) Copyright John Maddock 2006-7.
tgamma_delta_ratio_int2.ipp://  (C) Copyright John Maddock 2006-7.
tgamma_ratio_data.ipp://  (C) Copyright John Maddock 2006-7.
zeta_1_below_data.ipp://  Copyright John Maddock 2008.
zeta_1_up_data.ipp://  Copyright John Maddock 2008.
zeta_data.ipp://  Copyright John Maddock 2008.
zeta_neg_data.ipp://  Copyright John Maddock 2008.
ztest_max_digits10.cpp: // Copyright 2010 Paul A. Bristow
zztest_max_digits10.cpp:// Copyright 2010 Paul A. Bristow
