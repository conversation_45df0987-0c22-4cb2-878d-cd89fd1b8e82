# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2016,2018
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2018-05-26 10:54+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Lower Sorbian (http://www.transifex.com/django/django/"
"language/dsb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: dsb\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3);\n"

msgid "Humanize"
msgstr "Humanize"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}."

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}."

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f milion"
msgstr[1] "%(value).1f miliona"
msgstr[2] "%(value).1f miliony"
msgstr[3] "%(value).1f milionow"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s milion"
msgstr[1] "%(value)s miliona"
msgstr[2] "%(value)s miliony"
msgstr[3] "%(value)s milionow"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f miliarda"
msgstr[1] "%(value).1f miliarźe"
msgstr[2] "%(value).1f miliardy"
msgstr[3] "%(value).1f miliardow"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s miliarda"
msgstr[1] "%(value)s miliarźe"
msgstr[2] "%(value)s miliardy"
msgstr[3] "%(value)s miliardow"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f bilion"
msgstr[1] "%(value).1f biliona"
msgstr[2] "%(value).1f biliony"
msgstr[3] "%(value).1f bilionow"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s bilion"
msgstr[1] "%(value)s biliona"
msgstr[2] "%(value)s biliony"
msgstr[3] "%(value)s bilionow"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] "%(value).1f biliarda"
msgstr[1] "%(value).1f biliarźe"
msgstr[2] "%(value).1f biliardy"
msgstr[3] "%(value).1f biliardow"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s biliarda"
msgstr[1] "%(value)s biliarźe"
msgstr[2] "%(value)s biliardy"
msgstr[3] "%(value)s biliardow"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] "%(value).1f trilion"
msgstr[1] "%(value).1f triliona"
msgstr[2] "%(value).1f triliony"
msgstr[3] "%(value).1f trilionow"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s trilion"
msgstr[1] "%(value)s triliona"
msgstr[2] "%(value)s triliony"
msgstr[3] "%(value)s trilionow"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] "%(value).1f triliarda"
msgstr[1] "%(value).1f triliarźe"
msgstr[2] "%(value).1f triliardy"
msgstr[3] "%(value).1f triliardow"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s triliarda"
msgstr[1] "%(value)s triliarźe"
msgstr[2] "%(value)s triliardy"
msgstr[3] "%(value)s triliardow"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] "%(value).1f kwadrilion"
msgstr[1] "%(value).1f kwadriliona"
msgstr[2] "%(value).1f kwadriliony"
msgstr[3] "%(value).1f kwadrilionow"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s kwadrilion"
msgstr[1] "%(value)s kwadriliona"
msgstr[2] "%(value)s kwadriliony"
msgstr[3] "%(value)s kwadrilionow"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] "%(value).1f kwadriliarda"
msgstr[1] "%(value).1f kwadriliarźe"
msgstr[2] "%(value).1f kwadriliardy"
msgstr[3] "%(value).1f kwadriliardow"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s kwadriliarda"
msgstr[1] "%(value)s kwadriliarźe"
msgstr[2] "%(value)s kwadriliardy"
msgstr[3] "%(value)s kwadriliardow"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] "%(value).1f kwintilion"
msgstr[1] "%(value).1f kwintiliona"
msgstr[2] "%(value).1f kwintiliony"
msgstr[3] "%(value).1f kwintilionow"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s kwintilion"
msgstr[1] "%(value)s kwintiliona"
msgstr[2] "%(value)s kwintiliony"
msgstr[3] "%(value)s kwintilionow"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] "%(value).1f kwintiliarda"
msgstr[1] "%(value).1f kwintiliarźe"
msgstr[2] "%(value).1f kwintiliardy"
msgstr[3] "%(value).1f kwintiliardow"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s kwintiliarda"
msgstr[1] "%(value)s kwintiliarźe"
msgstr[2] "%(value)s kwintiliardy"
msgstr[3] "%(value)s kwintiliardow"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] "%(value).1f sedeciliarda"
msgstr[1] "%(value).1f sedeciliarźe"
msgstr[2] "%(value).1f sedeciliardy"
msgstr[3] "%(value).1f sedeciliardow"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s sedeciliarda"
msgstr[1] "%(value)s sedeciliarźe"
msgstr[2] "%(value)s sedeciliardy"
msgstr[3] "%(value)s sedeciliardow"

msgid "one"
msgstr "jaden"

msgid "two"
msgstr "dwa"

msgid "three"
msgstr "tśi"

msgid "four"
msgstr "styri"

msgid "five"
msgstr "pěś"

msgid "six"
msgstr "šesć"

msgid "seven"
msgstr "sedym"

msgid "eight"
msgstr "wósym"

msgid "nine"
msgstr "źewjeś"

msgid "today"
msgstr "źinsa"

msgid "tomorrow"
msgstr "witśe"

msgid "yesterday"
msgstr "cora"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "pśed %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "Pśed %(count)s góźinu"
msgstr[1] "Pśed %(count)s góźinoma"
msgstr[2] "Pśed %(count)s góźinami"
msgstr[3] "Pśed %(count)s góźinami"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "Pśed %(count)s minutu"
msgstr[1] "Pśed %(count)s minutoma"
msgstr[2] "Pśed %(count)s minutami"
msgstr[3] "Pśed %(count)s minutami"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "Pśed %(count)s sekundu"
msgstr[1] "Pśed %(count)s sekundoma"
msgstr[2] "Pśed %(count)s sekundami"
msgstr[3] "Pśed %(count)s sekundami"

msgid "now"
msgstr "něnto"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "za %(count)s sekundu"
msgstr[1] "za %(count)s sekunźe"
msgstr[2] "za %(count)s sekundy"
msgstr[3] "za %(count)s sekundow"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "za %(count)s minutu"
msgstr[1] "za %(count)s minuśe"
msgstr[2] "za %(count)s minuty"
msgstr[3] "za %(count)s minutow"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "za %(count)s góźinu"
msgstr[1] "za %(count)s góźinje"
msgstr[2] "za %(count)s góźiny"
msgstr[3] "za %(count)s góźin"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "%(delta)s wótněnta"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%dlětom"
msgstr[1] "%dlětoma"
msgstr[2] "%dlětami"
msgstr[3] "%dlětami"

#, python-format
msgctxt "naturaltime-past"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d mjasecom"
msgstr[1] "%d mjasecoma"
msgstr[2] "%d mjasecami"
msgstr[3] "%d mjasecami"

#, python-format
msgctxt "naturaltime-past"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d tyźenjom"
msgstr[1] "%d tyźenjoma"
msgstr[2] "%d tyźenjami"
msgstr[3] "%d tyźenjami"

#, python-format
msgctxt "naturaltime-past"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dnjom"
msgstr[1] "%d dnjoma"
msgstr[2] "%d dnjami"
msgstr[3] "%d dnjami"

#, python-format
msgctxt "naturaltime-past"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d góźinu"
msgstr[1] "%d góźinoma"
msgstr[2] "%d góźinami"
msgstr[3] "%d góźinami"

#, python-format
msgctxt "naturaltime-past"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minutu"
msgstr[1] "%d minutoma"
msgstr[2] "%d minutami"
msgstr[3] "%d minutami"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%dlěto"
msgstr[1] "%d lěśe"
msgstr[2] "%d lěta"
msgstr[3] "%d lět"

#, python-format
msgctxt "naturaltime-future"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d mjasec"
msgstr[1] "%d mjaseca"
msgstr[2] "%d mjasece"
msgstr[3] "%d mjasecow"

#, python-format
msgctxt "naturaltime-future"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d tyźeń"
msgstr[1] "%d tyźenja"
msgstr[2] "%d tyźenje"
msgstr[3] "%d tyźenjow"

#, python-format
msgctxt "naturaltime-future"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d źeń"
msgstr[1] "%d dnja"
msgstr[2] "%d dny"
msgstr[3] "%d dnjow"

#, python-format
msgctxt "naturaltime-future"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d góźina"
msgstr[1] "%d góźinje"
msgstr[2] "%d góźiny"
msgstr[3] "%d góźinow"

#, python-format
msgctxt "naturaltime-future"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minuta"
msgstr[1] "%d minuśe"
msgstr[2] "%d minuty"
msgstr[3] "%d minuty"
