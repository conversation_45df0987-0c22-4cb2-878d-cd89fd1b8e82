from elasticsearch import Elasticsearch

from data_frame.global_settings import ES_ADDR


def get_elasticsearch_data(es_key):
    # 创建Elasticsearch客户端实例
    es = Elasticsearch(ES_ADDR)

    # 构建查询
    query = {
        "query": {
            "term": {
                "es_key.keyword": es_key
            }
        }
    }

    # 执行搜索
    result = es.search(index="data_ecg_index", body=query)

    return result
