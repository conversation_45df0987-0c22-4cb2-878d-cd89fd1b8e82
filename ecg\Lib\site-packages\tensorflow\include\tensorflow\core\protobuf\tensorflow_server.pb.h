// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tensorflow_server.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/cluster.pb.h"
#include "tensorflow/core/protobuf/config.pb.h"
#include "tensorflow/core/protobuf/device_filters.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto;
namespace tensorflow {
class ServerDef;
class ServerDefDefaultTypeInternal;
extern ServerDefDefaultTypeInternal _ServerDef_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ServerDef* Arena::CreateMaybeMessage<::tensorflow::ServerDef>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class ServerDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ServerDef) */ {
 public:
  ServerDef();
  virtual ~ServerDef();

  ServerDef(const ServerDef& from);
  ServerDef(ServerDef&& from) noexcept
    : ServerDef() {
    *this = ::std::move(from);
  }

  inline ServerDef& operator=(const ServerDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline ServerDef& operator=(ServerDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ServerDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServerDef* internal_default_instance() {
    return reinterpret_cast<const ServerDef*>(
               &_ServerDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ServerDef& a, ServerDef& b) {
    a.Swap(&b);
  }
  inline void Swap(ServerDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ServerDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ServerDef* New() const final {
    return CreateMaybeMessage<ServerDef>(nullptr);
  }

  ServerDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ServerDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ServerDef& from);
  void MergeFrom(const ServerDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ServerDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ServerDef";
  }
  protected:
  explicit ServerDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobNameFieldNumber = 2,
    kProtocolFieldNumber = 5,
    kClusterFieldNumber = 1,
    kDefaultSessionConfigFieldNumber = 4,
    kClusterDeviceFiltersFieldNumber = 7,
    kTaskIndexFieldNumber = 3,
    kPortFieldNumber = 6,
  };
  // string job_name = 2;
  void clear_job_name();
  const std::string& job_name() const;
  void set_job_name(const std::string& value);
  void set_job_name(std::string&& value);
  void set_job_name(const char* value);
  void set_job_name(const char* value, size_t size);
  std::string* mutable_job_name();
  std::string* release_job_name();
  void set_allocated_job_name(std::string* job_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_job_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_job_name(
      std::string* job_name);

  // string protocol = 5;
  void clear_protocol();
  const std::string& protocol() const;
  void set_protocol(const std::string& value);
  void set_protocol(std::string&& value);
  void set_protocol(const char* value);
  void set_protocol(const char* value, size_t size);
  std::string* mutable_protocol();
  std::string* release_protocol();
  void set_allocated_protocol(std::string* protocol);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_protocol();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_protocol(
      std::string* protocol);

  // .tensorflow.ClusterDef cluster = 1;
  bool has_cluster() const;
  void clear_cluster();
  const ::tensorflow::ClusterDef& cluster() const;
  ::tensorflow::ClusterDef* release_cluster();
  ::tensorflow::ClusterDef* mutable_cluster();
  void set_allocated_cluster(::tensorflow::ClusterDef* cluster);
  void unsafe_arena_set_allocated_cluster(
      ::tensorflow::ClusterDef* cluster);
  ::tensorflow::ClusterDef* unsafe_arena_release_cluster();

  // .tensorflow.ConfigProto default_session_config = 4;
  bool has_default_session_config() const;
  void clear_default_session_config();
  const ::tensorflow::ConfigProto& default_session_config() const;
  ::tensorflow::ConfigProto* release_default_session_config();
  ::tensorflow::ConfigProto* mutable_default_session_config();
  void set_allocated_default_session_config(::tensorflow::ConfigProto* default_session_config);
  void unsafe_arena_set_allocated_default_session_config(
      ::tensorflow::ConfigProto* default_session_config);
  ::tensorflow::ConfigProto* unsafe_arena_release_default_session_config();

  // .tensorflow.ClusterDeviceFilters cluster_device_filters = 7;
  bool has_cluster_device_filters() const;
  void clear_cluster_device_filters();
  const ::tensorflow::ClusterDeviceFilters& cluster_device_filters() const;
  ::tensorflow::ClusterDeviceFilters* release_cluster_device_filters();
  ::tensorflow::ClusterDeviceFilters* mutable_cluster_device_filters();
  void set_allocated_cluster_device_filters(::tensorflow::ClusterDeviceFilters* cluster_device_filters);
  void unsafe_arena_set_allocated_cluster_device_filters(
      ::tensorflow::ClusterDeviceFilters* cluster_device_filters);
  ::tensorflow::ClusterDeviceFilters* unsafe_arena_release_cluster_device_filters();

  // int32 task_index = 3;
  void clear_task_index();
  ::PROTOBUF_NAMESPACE_ID::int32 task_index() const;
  void set_task_index(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 port = 6;
  void clear_port();
  ::PROTOBUF_NAMESPACE_ID::int32 port() const;
  void set_port(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ServerDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr job_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr protocol_;
  ::tensorflow::ClusterDef* cluster_;
  ::tensorflow::ConfigProto* default_session_config_;
  ::tensorflow::ClusterDeviceFilters* cluster_device_filters_;
  ::PROTOBUF_NAMESPACE_ID::int32 task_index_;
  ::PROTOBUF_NAMESPACE_ID::int32 port_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ServerDef

// .tensorflow.ClusterDef cluster = 1;
inline bool ServerDef::has_cluster() const {
  return this != internal_default_instance() && cluster_ != nullptr;
}
inline const ::tensorflow::ClusterDef& ServerDef::cluster() const {
  const ::tensorflow::ClusterDef* p = cluster_;
  // @@protoc_insertion_point(field_get:tensorflow.ServerDef.cluster)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ClusterDef*>(
      &::tensorflow::_ClusterDef_default_instance_);
}
inline ::tensorflow::ClusterDef* ServerDef::release_cluster() {
  // @@protoc_insertion_point(field_release:tensorflow.ServerDef.cluster)
  
  ::tensorflow::ClusterDef* temp = cluster_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  cluster_ = nullptr;
  return temp;
}
inline ::tensorflow::ClusterDef* ServerDef::unsafe_arena_release_cluster() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ServerDef.cluster)
  
  ::tensorflow::ClusterDef* temp = cluster_;
  cluster_ = nullptr;
  return temp;
}
inline ::tensorflow::ClusterDef* ServerDef::mutable_cluster() {
  
  if (cluster_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ClusterDef>(GetArenaNoVirtual());
    cluster_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ServerDef.cluster)
  return cluster_;
}
inline void ServerDef::set_allocated_cluster(::tensorflow::ClusterDef* cluster) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(cluster_);
  }
  if (cluster) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cluster)->GetArena();
    if (message_arena != submessage_arena) {
      cluster = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cluster, submessage_arena);
    }
    
  } else {
    
  }
  cluster_ = cluster;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ServerDef.cluster)
}

// string job_name = 2;
inline void ServerDef::clear_job_name() {
  job_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ServerDef::job_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ServerDef.job_name)
  return job_name_.Get();
}
inline void ServerDef::set_job_name(const std::string& value) {
  
  job_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ServerDef.job_name)
}
inline void ServerDef::set_job_name(std::string&& value) {
  
  job_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ServerDef.job_name)
}
inline void ServerDef::set_job_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  job_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ServerDef.job_name)
}
inline void ServerDef::set_job_name(const char* value,
    size_t size) {
  
  job_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ServerDef.job_name)
}
inline std::string* ServerDef::mutable_job_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ServerDef.job_name)
  return job_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ServerDef::release_job_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ServerDef.job_name)
  
  return job_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ServerDef::set_allocated_job_name(std::string* job_name) {
  if (job_name != nullptr) {
    
  } else {
    
  }
  job_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), job_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ServerDef.job_name)
}
inline std::string* ServerDef::unsafe_arena_release_job_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ServerDef.job_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return job_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ServerDef::unsafe_arena_set_allocated_job_name(
    std::string* job_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (job_name != nullptr) {
    
  } else {
    
  }
  job_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      job_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ServerDef.job_name)
}

// int32 task_index = 3;
inline void ServerDef::clear_task_index() {
  task_index_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ServerDef::task_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.ServerDef.task_index)
  return task_index_;
}
inline void ServerDef::set_task_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  task_index_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ServerDef.task_index)
}

// .tensorflow.ConfigProto default_session_config = 4;
inline bool ServerDef::has_default_session_config() const {
  return this != internal_default_instance() && default_session_config_ != nullptr;
}
inline const ::tensorflow::ConfigProto& ServerDef::default_session_config() const {
  const ::tensorflow::ConfigProto* p = default_session_config_;
  // @@protoc_insertion_point(field_get:tensorflow.ServerDef.default_session_config)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ConfigProto*>(
      &::tensorflow::_ConfigProto_default_instance_);
}
inline ::tensorflow::ConfigProto* ServerDef::release_default_session_config() {
  // @@protoc_insertion_point(field_release:tensorflow.ServerDef.default_session_config)
  
  ::tensorflow::ConfigProto* temp = default_session_config_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  default_session_config_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto* ServerDef::unsafe_arena_release_default_session_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ServerDef.default_session_config)
  
  ::tensorflow::ConfigProto* temp = default_session_config_;
  default_session_config_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto* ServerDef::mutable_default_session_config() {
  
  if (default_session_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ConfigProto>(GetArenaNoVirtual());
    default_session_config_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ServerDef.default_session_config)
  return default_session_config_;
}
inline void ServerDef::set_allocated_default_session_config(::tensorflow::ConfigProto* default_session_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(default_session_config_);
  }
  if (default_session_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(default_session_config)->GetArena();
    if (message_arena != submessage_arena) {
      default_session_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, default_session_config, submessage_arena);
    }
    
  } else {
    
  }
  default_session_config_ = default_session_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ServerDef.default_session_config)
}

// string protocol = 5;
inline void ServerDef::clear_protocol() {
  protocol_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ServerDef::protocol() const {
  // @@protoc_insertion_point(field_get:tensorflow.ServerDef.protocol)
  return protocol_.Get();
}
inline void ServerDef::set_protocol(const std::string& value) {
  
  protocol_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ServerDef.protocol)
}
inline void ServerDef::set_protocol(std::string&& value) {
  
  protocol_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ServerDef.protocol)
}
inline void ServerDef::set_protocol(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  protocol_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ServerDef.protocol)
}
inline void ServerDef::set_protocol(const char* value,
    size_t size) {
  
  protocol_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ServerDef.protocol)
}
inline std::string* ServerDef::mutable_protocol() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ServerDef.protocol)
  return protocol_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ServerDef::release_protocol() {
  // @@protoc_insertion_point(field_release:tensorflow.ServerDef.protocol)
  
  return protocol_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ServerDef::set_allocated_protocol(std::string* protocol) {
  if (protocol != nullptr) {
    
  } else {
    
  }
  protocol_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), protocol,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ServerDef.protocol)
}
inline std::string* ServerDef::unsafe_arena_release_protocol() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ServerDef.protocol)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return protocol_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ServerDef::unsafe_arena_set_allocated_protocol(
    std::string* protocol) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (protocol != nullptr) {
    
  } else {
    
  }
  protocol_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      protocol, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ServerDef.protocol)
}

// int32 port = 6;
inline void ServerDef::clear_port() {
  port_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ServerDef::port() const {
  // @@protoc_insertion_point(field_get:tensorflow.ServerDef.port)
  return port_;
}
inline void ServerDef::set_port(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  port_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ServerDef.port)
}

// .tensorflow.ClusterDeviceFilters cluster_device_filters = 7;
inline bool ServerDef::has_cluster_device_filters() const {
  return this != internal_default_instance() && cluster_device_filters_ != nullptr;
}
inline const ::tensorflow::ClusterDeviceFilters& ServerDef::cluster_device_filters() const {
  const ::tensorflow::ClusterDeviceFilters* p = cluster_device_filters_;
  // @@protoc_insertion_point(field_get:tensorflow.ServerDef.cluster_device_filters)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ClusterDeviceFilters*>(
      &::tensorflow::_ClusterDeviceFilters_default_instance_);
}
inline ::tensorflow::ClusterDeviceFilters* ServerDef::release_cluster_device_filters() {
  // @@protoc_insertion_point(field_release:tensorflow.ServerDef.cluster_device_filters)
  
  ::tensorflow::ClusterDeviceFilters* temp = cluster_device_filters_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  cluster_device_filters_ = nullptr;
  return temp;
}
inline ::tensorflow::ClusterDeviceFilters* ServerDef::unsafe_arena_release_cluster_device_filters() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ServerDef.cluster_device_filters)
  
  ::tensorflow::ClusterDeviceFilters* temp = cluster_device_filters_;
  cluster_device_filters_ = nullptr;
  return temp;
}
inline ::tensorflow::ClusterDeviceFilters* ServerDef::mutable_cluster_device_filters() {
  
  if (cluster_device_filters_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ClusterDeviceFilters>(GetArenaNoVirtual());
    cluster_device_filters_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ServerDef.cluster_device_filters)
  return cluster_device_filters_;
}
inline void ServerDef::set_allocated_cluster_device_filters(::tensorflow::ClusterDeviceFilters* cluster_device_filters) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(cluster_device_filters_);
  }
  if (cluster_device_filters) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cluster_device_filters)->GetArena();
    if (message_arena != submessage_arena) {
      cluster_device_filters = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cluster_device_filters, submessage_arena);
    }
    
  } else {
    
  }
  cluster_device_filters_ = cluster_device_filters;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ServerDef.cluster_device_filters)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto
