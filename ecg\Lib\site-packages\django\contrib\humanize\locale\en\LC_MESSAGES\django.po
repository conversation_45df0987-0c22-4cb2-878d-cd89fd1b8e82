# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: contrib/humanize/apps.py:7
msgid "Humanize"
msgstr ""

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
#: contrib/humanize/templatetags/humanize.py:30
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
#: contrib/humanize/templatetags/humanize.py:34
msgctxt "ordinal 0"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
#: contrib/humanize/templatetags/humanize.py:36
msgctxt "ordinal 1"
msgid "{}st"
msgstr ""

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
#: contrib/humanize/templatetags/humanize.py:38
msgctxt "ordinal 2"
msgid "{}nd"
msgstr ""

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
#: contrib/humanize/templatetags/humanize.py:40
msgctxt "ordinal 3"
msgid "{}rd"
msgstr ""

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
#: contrib/humanize/templatetags/humanize.py:42
msgctxt "ordinal 4"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
#: contrib/humanize/templatetags/humanize.py:44
msgctxt "ordinal 5"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
#: contrib/humanize/templatetags/humanize.py:46
msgctxt "ordinal 6"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
#: contrib/humanize/templatetags/humanize.py:48
msgctxt "ordinal 7"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
#: contrib/humanize/templatetags/humanize.py:50
msgctxt "ordinal 8"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
#: contrib/humanize/templatetags/humanize.py:52
msgctxt "ordinal 9"
msgid "{}th"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:83
#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:84
#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:85
#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:86
#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:87
#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:88
#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:89
#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:90
#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:91
#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:92
#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:93
#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:136
msgid "one"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:136
msgid "two"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:136
msgid "three"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:136
msgid "four"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:136
msgid "five"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:137
msgid "six"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:137
msgid "seven"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:137
msgid "eight"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:137
msgid "nine"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:158
msgid "today"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:160
msgid "tomorrow"
msgstr ""

#: contrib/humanize/templatetags/humanize.py:162
msgid "yesterday"
msgstr ""

#. Translators: delta will contain a string like '2 months' or '1 month, 2 weeks'
#: contrib/humanize/templatetags/humanize.py:180
#, python-format
msgid "%(delta)s ago"
msgstr ""

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#: contrib/humanize/templatetags/humanize.py:183
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#: contrib/humanize/templatetags/humanize.py:186
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#: contrib/humanize/templatetags/humanize.py:189
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:190
msgid "now"
msgstr ""

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#: contrib/humanize/templatetags/humanize.py:193
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#: contrib/humanize/templatetags/humanize.py:196
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#: contrib/humanize/templatetags/humanize.py:199
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] ""
msgstr[1] ""

#. Translators: delta will contain a string like '2 months' or '1 month, 2 weeks'
#: contrib/humanize/templatetags/humanize.py:201
#, python-format
msgid "%(delta)s from now"
msgstr ""

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#: contrib/humanize/templatetags/humanize.py:205
#, python-format
msgctxt "naturaltime-past"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:206
#, python-format
msgctxt "naturaltime-past"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:207
#, python-format
msgctxt "naturaltime-past"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:208
#, python-format
msgctxt "naturaltime-past"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:209
#, python-format
msgctxt "naturaltime-past"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:210
#, python-format
msgctxt "naturaltime-past"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] ""
msgstr[1] ""

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s from now'
#: contrib/humanize/templatetags/humanize.py:214
#, python-format
msgctxt "naturaltime-future"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:215
#, python-format
msgctxt "naturaltime-future"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:216
#, python-format
msgctxt "naturaltime-future"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:217
#, python-format
msgctxt "naturaltime-future"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:218
#, python-format
msgctxt "naturaltime-future"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] ""
msgstr[1] ""

#: contrib/humanize/templatetags/humanize.py:219
#, python-format
msgctxt "naturaltime-future"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] ""
msgstr[1] ""
