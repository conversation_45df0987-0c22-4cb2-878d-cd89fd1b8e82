import ast
import json
import traceback
import numpy as np
from django.utils.decorators import method_decorator
from django.views import View

from apps.analysis import whitelist
from apps.analysis.common.custom_exception import BiosppyEcgError
from apps.analysis.ecg_age import diagnosis as ecg_age_diagnosis
from apps.analysis.common.noise_model.noise_recognition import is_noise
from apps.models.ecg_analysis_modes import <PERSON><PERSON><PERSON><PERSON><PERSON>
from apps.utils import api_analysis_log
from apps.models.Interface_log_models import InterfaceLogEntity, RequestParam
from apps.models.arrhythmia_models import ArrhythmiaEntity, ArrhythmiaDiagnosisEntity, CADCardiomyopathyEntity, \
    HealthMetricsEntity, PQRSTCEntity
from apps.utils.get_response import GetResponse
from apps.utils.decorator import authentication
from apps.utils.logger_helper import Logger
import apps.analysis.arrhythmia_diagnosis.diagnosis as arrhythmia_diagnosis_diagnosis
import apps.analysis.cad_cardiomyopathy.diagnosis as cad_cardiomyopathy_diagnosis
import apps.analysis.health_metrics.diagnosis as health_metrics_diagnosis
import apps.analysis.pqrstc.diagnosis as pqrstc_diagnosis


@method_decorator(authentication, name="dispatch")
class ArrhythmiaView(View):
    """
    心率失常诊断
    """

    def post(self, request, *args, **kwargs):
        """
        POST请求接口
        :param request: 请求对象
        :return: 诊断结果
        """
        custom_id = kwargs.get('custom_id', 1)  # 默认给一个整数ID，避免错误
        ecg_data = None
        sampling_rate = None
        gain = None
        zero = None
        union_id = None

        param_list = ['signal', 'fs', 'adc_gain', 'adc_zero', 'union_id']

        try:
            data = json.loads(request.body)

            # 验证参数是否存在
            missing_params = [param for param in param_list if param not in data]

            if len(missing_params) > 0:
                return GetResponse.get_response(code=6, data=f"所需参数 {','.join(missing_params)} 不存在")

            ecg_data_str = data['signal']  # ecg信号
            noise_message = ""

            if isinstance(ecg_data_str, list):
                ecg_data = np.array(data['signal'])
            elif ecg_data_str.find('[') != -1 and ecg_data_str.find(']') != -1:
                ecg_data = np.array(ast.literal_eval(f"{ecg_data_str}"))
            else:
                ecg_data = np.array(ast.literal_eval(f"[{ecg_data_str}]"))  # 将心电信号转为nparray

            sampling_rate = data['fs']  # 采样率
            gain = data['adc_gain']  # 增益
            zero = data['adc_zero']  # 零点（基线）

            union_id = data['union_id']

            # 只获取前10秒数据
            ecg_data = ecg_data[:sampling_rate * 10]

            sampling_rate = int(sampling_rate)

            if len(ecg_data) < sampling_rate * 10:
                return GetResponse.get_response(code=6, data='心电信号不能小于10秒')

            if not self.__verify_ecg_data(ecg_data, sampling_rate):
                return GetResponse.get_response(code=6, data='有效信号太少无法解析')

            if ecg_data is None or sampling_rate is None or gain is None or zero is None or gain <= 0:
                return GetResponse.get_response(code=5)

            if not isinstance(sampling_rate, int):
                return GetResponse.get_response(code=6, data='采样率请传递整数')

            ecg_data = (ecg_data - zero) / gain  # 计算实际电压（检测电压-基线）/ 增益

            # 心率失常分析
            arrhythmia_entity = ArrhythmiaEntity()

            # arrhythmia_entity.SignalQuantity = cal_signalQuantity(ecg_data, sampling_rate)  # 信号质量 1表示信号质量可以，-1表示信号质量差
            noise_result, noise_message = is_noise(ecg_data, sampling_rate)
            arrhythmia_entity.SignalQuantity = noise_result

            # 白名单信息
            whitelist_arrhythmia_diagnosis, whitelist_ecg_age = None, None

            if arrhythmia_entity.SignalQuantity == 1:
                whitelist_arrhythmia_diagnosis, whitelist_ecg_age = whitelist.process(union_id)

            if whitelist_ecg_age:
                arrhythmia_entity.ECGAge = whitelist_ecg_age
            else:
                arrhythmia_entity.ECGAge = ecg_age_diagnosis.process(ecg_data, sampling_rate)  # 心脏年龄

            arrhythmia_entity.HeartFailureRisk = 0  # 心衰风险（0-1）
            arrhythmia_entity.VentricularFibrillationRisk = 0  # 室颤风险（0-1）
            arrhythmia_entity.SyncopeRisk = 0  # 晕厥风险（0-1）
            arrhythmia_entity.SleepStage = 0  # 睡眠阶段 取值范围为（0，1，2，3，4），对应睡眠阶段（Wake，N1， N2， N3, REM）
            arrhythmia_entity.OSARisk = 0  # 阻塞性睡眠呼吸暂停风险，取值范围为（0-1）, -1代表时长小于两分钟
            arrhythmia_entity.RespiratoryRate = 0  # 呼吸次数/min 取值范围[10-25]

            if arrhythmia_entity.SignalQuantity == 1:  # 正常信号处理
                pqrstc = pqrstc_diagnosis.process(ecg_data, sampling_rate)  # ECG信号指标

                if whitelist_arrhythmia_diagnosis:
                    arrhythmia_entity.ArrhythmiaDiagnosis = whitelist_arrhythmia_diagnosis
                else:
                    arrhythmia_entity.ArrhythmiaDiagnosis = arrhythmia_diagnosis_diagnosis.process(ecg_data,
                                                                                                   sampling_rate)  # 心率失常诊断
                arrhythmia_entity.CADCardiomyopathy = cad_cardiomyopathy_diagnosis.process(ecg_data,
                                                                                           sampling_rate)  # 心肌病冠心病诊断
                arrhythmia_entity.HealthMetrics = health_metrics_diagnosis.process(ecg_data, sampling_rate)  # 健康指标
            else:  # 异常信息处理
                pqrstc = PQRSTCEntity()  # 创建默认的 PQRSTCEntity 实例

                arrhythmia_entity.ArrhythmiaDiagnosis = ArrhythmiaDiagnosisEntity()
                arrhythmia_entity.CADCardiomyopathy = CADCardiomyopathyEntity()
                arrhythmia_entity.HealthMetrics = HealthMetricsEntity()

                pqrstc.QRS_duration = 0
                pqrstc.QT = 0
                pqrstc.QTc = 0
                pqrstc.P_duration = 0
                pqrstc.PR_interval = 0
                pqrstc.ST_duration = 0

            arrhythmia_entity.PQRSTC = pqrstc

            # 设置日志实体
            interface_log = InterfaceLogEntity()

            # 设置请求参数
            request_param = RequestParam()
            request_param.ecg_data = ecg_data_str
            request_param.fs = sampling_rate
            request_param.gain = gain
            request_param.zero = zero

            interface_log.RequestParam = request_param.to_dict()

            # 设置响应参数
            interface_log.ResponseParam = arrhythmia_entity.to_entity_dict()

            # 写入日志-外网模式
            ecg_id = api_analysis_log.record(custom_id, interface_log) or 1
            # 内网模式
            # ecg_id = 1

            arrhythmia_entity.ecg_id = ecg_id

            result_data = arrhythmia_entity.to_entity_dict()

            # 如果是噪音信号，在返回结果中添加明确的标识
            if arrhythmia_entity.SignalQuantity == -1:
                result_data['IsNoise'] = True
                result_data['NoiseMessage'] = "信号质量差，检测到噪音"
            else:
                result_data['IsNoise'] = False

            return GetResponse.get_response(code=0, data=result_data)
        except BiosppyEcgError as e:
            # 错误处理
            Logger().error(
                f'\n客户ID: {union_id}, 心电信号：{ecg_data}\n 采样率：{sampling_rate}\n 增益：{gain}\n 基线：{zero}\n{traceback.format_exc()}')
            return GetResponse.get_response(code=8)
        except Exception as e:
            # 错误处理
            Logger().error(
                f'\n客户ID: {union_id}, 心电信号：{ecg_data}\n 采样率：{sampling_rate}\n 增益：{gain}\n 基线：{zero}\n{traceback.format_exc()}')
            return GetResponse.get_response(code=2)

    @staticmethod
    def __verify_ecg_data(ecg_data, sampling_rate):
        """
        验证心电数据是否有效
        :param ecg_data: 心电数据
        :param sampling_rate: 采样率
        :return: 是否有效
        """
        total_length = len(ecg_data)
        zero_segments = []
        zero_count = 0
        sample_time = 1
        min_zero_segment_length = sampling_rate * sample_time

        for value in ecg_data:
            if value == 0:
                zero_count += 1
            else:
                if zero_count >= min_zero_segment_length:
                    zero_segments.append(zero_count)
                zero_count = 0

        if zero_count >= min_zero_segment_length:
            zero_segments.append(zero_count)

        total_zero_length = sum(zero_segments)

        validity_threshold = total_length / 2.0

        if total_zero_length > validity_threshold:
            return False

        return True
