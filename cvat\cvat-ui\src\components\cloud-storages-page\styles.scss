// Copyright (C) 2021-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-cloud-storages-page {
    padding-top: $grid-unit-size * 2;
    padding-bottom: $grid-unit-size;
    height: 100%;
    width: 100%;
    overflow: auto;

    .ant-spin {
        position: absolute;
        top: 50%;
        left: 50%;
    }
}

.cvat-empty-cloud-storages-list-icon {
    font-size: $grid-unit-size * 14;
    opacity: 0.5;
    margin-bottom: $grid-unit-size * 2;
}

.cvat-cloud-storages-pagination {
    margin-top: $grid-unit-size * 2;
}

.cvat-cloud-storages-list-top-bar {
    margin-bottom: $grid-unit-size;

    > div {
        display: flex;
        justify-content: space-between;

        > .cvat-cloudstorages-page-filters-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;

            > div {
                > *:not(:last-child) {
                    margin-right: $grid-unit-size;
                }

                display: flex;
                margin-right: $grid-unit-size * 4;
            }

            .cvat-cloudstorages-page-tasks-search-bar {
                width: $grid-unit-size * 32;
            }
        }
    }
}

.cvat-cloud-storages-list {
    display: flex;
    flex-wrap: wrap;

    > div {
        width: 100%;
        margin-bottom: $grid-unit-size;

        > div:not(:first-child) {
            padding-left: $grid-unit-size;
        }
    }
}

.cvat-empty-cloud-storages-list {
    .ant-empty {
        top: 50%;
        left: 50%;
        position: absolute;
        transform: translate(-50%, -50%);
    }
}

.cvat-cloud-storage-item {
    div.ant-typography {
        margin-bottom: 0;
    }

    cursor: default;

    .cvat-cloud-storage-item-loading-preview,
    .cvat-cloud-storage-item-empty-preview {
        @extend .cvat-base-preview;

        height: $grid-unit-size * 24;

        svg {
            width: 40%;
            height: 40%;
        }
    }

    .cvat-cloud-storage-item-preview {
        height: $grid-unit-size * 24;
        object-fit: cover;
        margin: auto;
        width: 100%;
    }

    .cvat-cloud-storage-item-menu-button {
        position: absolute;
        bottom: 0;
        right: 0;
    }

    .cvat-cloud-storage-description-icon {
        position: absolute;
        top: $grid-unit-size;
        width: auto;
        right: $grid-unit-size;
    }
}
