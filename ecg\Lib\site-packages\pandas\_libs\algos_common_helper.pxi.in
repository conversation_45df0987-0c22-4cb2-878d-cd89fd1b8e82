"""
Template for each `dtype` helper function using 1-d template

WARNING: DO NOT edit .pxi FILE directly, .pxi is generated from .pxi.in
"""

# ----------------------------------------------------------------------
# ensure_dtype
# ----------------------------------------------------------------------


def ensure_platform_int(object arr):
    # GH3033, GH1392
    # platform int is the size of the int pointer, e.g. np.intp
    if util.is_array(arr):
        if (<ndarray>arr).descr.type_num == cnp.NPY_INTP:
            return arr
        else:
            # equiv: arr.astype(np.intp)
            return cnp.PyArray_Cast(<ndarray>arr, cnp.NPY_INTP)
    else:
        return np.array(arr, dtype=np.intp)


def ensure_object(object arr):
    if util.is_array(arr):
        if (<ndarray>arr).descr.type_num == NPY_OBJECT:
            return arr
        else:
            # equiv: arr.astype(object)
            return cnp.PyArray_Cast(<ndarray>arr, NPY_OBJECT)
    else:
        return np.array(arr, dtype=np.object_)

{{py:

# name, c_type, dtype
dtypes = [('float64', 'FLOAT64', 'float64'),
          # ('float32', 'FLOAT32', 'float32'),  # disabling bc unused
          ('int8', 'INT8', 'int8'),
          ('int16', 'INT16', 'int16'),
          ('int32', 'INT32', 'int32'),
          ('int64', 'INT64', 'int64'),
          # Disabling uint and complex dtypes because we do not use them
          #  (and compiling them increases wheel size)
          # ('uint8', 'UINT8', 'uint8'),
          # ('uint16', 'UINT16', 'uint16'),
          # ('uint32', 'UINT32', 'uint32'),
          # ('uint64', 'UINT64', 'uint64'),
          # ('complex64', 'COMPLEX64', 'complex64'),
          # ('complex128', 'COMPLEX128', 'complex128')
]

def get_dispatch(dtypes):

    for name, c_type, dtype in dtypes:
        yield name, c_type, dtype
}}

{{for name, c_type, dtype in get_dispatch(dtypes)}}


def ensure_{{name}}(object arr, copy=True):
    if util.is_array(arr):
        if (<ndarray>arr).descr.type_num == NPY_{{c_type}}:
            return arr
        else:
            return arr.astype(np.{{dtype}}, copy=copy)
    else:
        return np.array(arr, dtype=np.{{dtype}})

{{endfor}}
