// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/config.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/cost_graph.pb.h"
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/step_stats.pb.h"
#include "tensorflow/core/protobuf/cluster.pb.h"
#include "tensorflow/core/protobuf/debug.pb.h"
#include "tensorflow/core/protobuf/rewriter_config.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[20]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
namespace tensorflow {
class CallableOptions;
class CallableOptionsDefaultTypeInternal;
extern CallableOptionsDefaultTypeInternal _CallableOptions_default_instance_;
class CallableOptions_FeedDevicesEntry_DoNotUse;
class CallableOptions_FeedDevicesEntry_DoNotUseDefaultTypeInternal;
extern CallableOptions_FeedDevicesEntry_DoNotUseDefaultTypeInternal _CallableOptions_FeedDevicesEntry_DoNotUse_default_instance_;
class CallableOptions_FetchDevicesEntry_DoNotUse;
class CallableOptions_FetchDevicesEntry_DoNotUseDefaultTypeInternal;
extern CallableOptions_FetchDevicesEntry_DoNotUseDefaultTypeInternal _CallableOptions_FetchDevicesEntry_DoNotUse_default_instance_;
class ConfigProto;
class ConfigProtoDefaultTypeInternal;
extern ConfigProtoDefaultTypeInternal _ConfigProto_default_instance_;
class ConfigProto_DeviceCountEntry_DoNotUse;
class ConfigProto_DeviceCountEntry_DoNotUseDefaultTypeInternal;
extern ConfigProto_DeviceCountEntry_DoNotUseDefaultTypeInternal _ConfigProto_DeviceCountEntry_DoNotUse_default_instance_;
class ConfigProto_Experimental;
class ConfigProto_ExperimentalDefaultTypeInternal;
extern ConfigProto_ExperimentalDefaultTypeInternal _ConfigProto_Experimental_default_instance_;
class GPUOptions;
class GPUOptionsDefaultTypeInternal;
extern GPUOptionsDefaultTypeInternal _GPUOptions_default_instance_;
class GPUOptions_Experimental;
class GPUOptions_ExperimentalDefaultTypeInternal;
extern GPUOptions_ExperimentalDefaultTypeInternal _GPUOptions_Experimental_default_instance_;
class GPUOptions_Experimental_VirtualDevices;
class GPUOptions_Experimental_VirtualDevicesDefaultTypeInternal;
extern GPUOptions_Experimental_VirtualDevicesDefaultTypeInternal _GPUOptions_Experimental_VirtualDevices_default_instance_;
class GraphOptions;
class GraphOptionsDefaultTypeInternal;
extern GraphOptionsDefaultTypeInternal _GraphOptions_default_instance_;
class OptimizerOptions;
class OptimizerOptionsDefaultTypeInternal;
extern OptimizerOptionsDefaultTypeInternal _OptimizerOptions_default_instance_;
class RPCOptions;
class RPCOptionsDefaultTypeInternal;
extern RPCOptionsDefaultTypeInternal _RPCOptions_default_instance_;
class RunMetadata;
class RunMetadataDefaultTypeInternal;
extern RunMetadataDefaultTypeInternal _RunMetadata_default_instance_;
class RunMetadata_FunctionGraphs;
class RunMetadata_FunctionGraphsDefaultTypeInternal;
extern RunMetadata_FunctionGraphsDefaultTypeInternal _RunMetadata_FunctionGraphs_default_instance_;
class RunOptions;
class RunOptionsDefaultTypeInternal;
extern RunOptionsDefaultTypeInternal _RunOptions_default_instance_;
class RunOptions_Experimental;
class RunOptions_ExperimentalDefaultTypeInternal;
extern RunOptions_ExperimentalDefaultTypeInternal _RunOptions_Experimental_default_instance_;
class RunOptions_Experimental_RunHandlerPoolOptions;
class RunOptions_Experimental_RunHandlerPoolOptionsDefaultTypeInternal;
extern RunOptions_Experimental_RunHandlerPoolOptionsDefaultTypeInternal _RunOptions_Experimental_RunHandlerPoolOptions_default_instance_;
class SessionMetadata;
class SessionMetadataDefaultTypeInternal;
extern SessionMetadataDefaultTypeInternal _SessionMetadata_default_instance_;
class TensorConnection;
class TensorConnectionDefaultTypeInternal;
extern TensorConnectionDefaultTypeInternal _TensorConnection_default_instance_;
class ThreadPoolOptionProto;
class ThreadPoolOptionProtoDefaultTypeInternal;
extern ThreadPoolOptionProtoDefaultTypeInternal _ThreadPoolOptionProto_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CallableOptions* Arena::CreateMaybeMessage<::tensorflow::CallableOptions>(Arena*);
template<> ::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ConfigProto* Arena::CreateMaybeMessage<::tensorflow::ConfigProto>(Arena*);
template<> ::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ConfigProto_Experimental* Arena::CreateMaybeMessage<::tensorflow::ConfigProto_Experimental>(Arena*);
template<> ::tensorflow::GPUOptions* Arena::CreateMaybeMessage<::tensorflow::GPUOptions>(Arena*);
template<> ::tensorflow::GPUOptions_Experimental* Arena::CreateMaybeMessage<::tensorflow::GPUOptions_Experimental>(Arena*);
template<> ::tensorflow::GPUOptions_Experimental_VirtualDevices* Arena::CreateMaybeMessage<::tensorflow::GPUOptions_Experimental_VirtualDevices>(Arena*);
template<> ::tensorflow::GraphOptions* Arena::CreateMaybeMessage<::tensorflow::GraphOptions>(Arena*);
template<> ::tensorflow::OptimizerOptions* Arena::CreateMaybeMessage<::tensorflow::OptimizerOptions>(Arena*);
template<> ::tensorflow::RPCOptions* Arena::CreateMaybeMessage<::tensorflow::RPCOptions>(Arena*);
template<> ::tensorflow::RunMetadata* Arena::CreateMaybeMessage<::tensorflow::RunMetadata>(Arena*);
template<> ::tensorflow::RunMetadata_FunctionGraphs* Arena::CreateMaybeMessage<::tensorflow::RunMetadata_FunctionGraphs>(Arena*);
template<> ::tensorflow::RunOptions* Arena::CreateMaybeMessage<::tensorflow::RunOptions>(Arena*);
template<> ::tensorflow::RunOptions_Experimental* Arena::CreateMaybeMessage<::tensorflow::RunOptions_Experimental>(Arena*);
template<> ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* Arena::CreateMaybeMessage<::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions>(Arena*);
template<> ::tensorflow::SessionMetadata* Arena::CreateMaybeMessage<::tensorflow::SessionMetadata>(Arena*);
template<> ::tensorflow::TensorConnection* Arena::CreateMaybeMessage<::tensorflow::TensorConnection>(Arena*);
template<> ::tensorflow::ThreadPoolOptionProto* Arena::CreateMaybeMessage<::tensorflow::ThreadPoolOptionProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum OptimizerOptions_Level : int {
  OptimizerOptions_Level_L1 = 0,
  OptimizerOptions_Level_L0 = -1,
  OptimizerOptions_Level_OptimizerOptions_Level_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  OptimizerOptions_Level_OptimizerOptions_Level_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool OptimizerOptions_Level_IsValid(int value);
constexpr OptimizerOptions_Level OptimizerOptions_Level_Level_MIN = OptimizerOptions_Level_L0;
constexpr OptimizerOptions_Level OptimizerOptions_Level_Level_MAX = OptimizerOptions_Level_L1;
constexpr int OptimizerOptions_Level_Level_ARRAYSIZE = OptimizerOptions_Level_Level_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OptimizerOptions_Level_descriptor();
template<typename T>
inline const std::string& OptimizerOptions_Level_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OptimizerOptions_Level>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OptimizerOptions_Level_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OptimizerOptions_Level_descriptor(), enum_t_value);
}
inline bool OptimizerOptions_Level_Parse(
    const std::string& name, OptimizerOptions_Level* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OptimizerOptions_Level>(
    OptimizerOptions_Level_descriptor(), name, value);
}
enum OptimizerOptions_GlobalJitLevel : int {
  OptimizerOptions_GlobalJitLevel_DEFAULT = 0,
  OptimizerOptions_GlobalJitLevel_OFF = -1,
  OptimizerOptions_GlobalJitLevel_ON_1 = 1,
  OptimizerOptions_GlobalJitLevel_ON_2 = 2,
  OptimizerOptions_GlobalJitLevel_OptimizerOptions_GlobalJitLevel_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  OptimizerOptions_GlobalJitLevel_OptimizerOptions_GlobalJitLevel_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool OptimizerOptions_GlobalJitLevel_IsValid(int value);
constexpr OptimizerOptions_GlobalJitLevel OptimizerOptions_GlobalJitLevel_GlobalJitLevel_MIN = OptimizerOptions_GlobalJitLevel_OFF;
constexpr OptimizerOptions_GlobalJitLevel OptimizerOptions_GlobalJitLevel_GlobalJitLevel_MAX = OptimizerOptions_GlobalJitLevel_ON_2;
constexpr int OptimizerOptions_GlobalJitLevel_GlobalJitLevel_ARRAYSIZE = OptimizerOptions_GlobalJitLevel_GlobalJitLevel_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OptimizerOptions_GlobalJitLevel_descriptor();
template<typename T>
inline const std::string& OptimizerOptions_GlobalJitLevel_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OptimizerOptions_GlobalJitLevel>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OptimizerOptions_GlobalJitLevel_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OptimizerOptions_GlobalJitLevel_descriptor(), enum_t_value);
}
inline bool OptimizerOptions_GlobalJitLevel_Parse(
    const std::string& name, OptimizerOptions_GlobalJitLevel* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OptimizerOptions_GlobalJitLevel>(
    OptimizerOptions_GlobalJitLevel_descriptor(), name, value);
}
enum ConfigProto_Experimental_MlirBridgeRollout : int {
  ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_UNSPECIFIED = 0,
  ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_ENABLED = 1,
  ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_DISABLED = 2,
  ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_SAFE_MODE_ENABLED = 3,
  ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_SAFE_MODE_FALLBACK_ENABLED = 4,
  ConfigProto_Experimental_MlirBridgeRollout_ConfigProto_Experimental_MlirBridgeRollout_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ConfigProto_Experimental_MlirBridgeRollout_ConfigProto_Experimental_MlirBridgeRollout_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ConfigProto_Experimental_MlirBridgeRollout_IsValid(int value);
constexpr ConfigProto_Experimental_MlirBridgeRollout ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_MIN = ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_UNSPECIFIED;
constexpr ConfigProto_Experimental_MlirBridgeRollout ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_MAX = ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_SAFE_MODE_FALLBACK_ENABLED;
constexpr int ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_ARRAYSIZE = ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConfigProto_Experimental_MlirBridgeRollout_descriptor();
template<typename T>
inline const std::string& ConfigProto_Experimental_MlirBridgeRollout_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ConfigProto_Experimental_MlirBridgeRollout>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ConfigProto_Experimental_MlirBridgeRollout_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ConfigProto_Experimental_MlirBridgeRollout_descriptor(), enum_t_value);
}
inline bool ConfigProto_Experimental_MlirBridgeRollout_Parse(
    const std::string& name, ConfigProto_Experimental_MlirBridgeRollout* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ConfigProto_Experimental_MlirBridgeRollout>(
    ConfigProto_Experimental_MlirBridgeRollout_descriptor(), name, value);
}
enum RunOptions_TraceLevel : int {
  RunOptions_TraceLevel_NO_TRACE = 0,
  RunOptions_TraceLevel_SOFTWARE_TRACE = 1,
  RunOptions_TraceLevel_HARDWARE_TRACE = 2,
  RunOptions_TraceLevel_FULL_TRACE = 3,
  RunOptions_TraceLevel_RunOptions_TraceLevel_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  RunOptions_TraceLevel_RunOptions_TraceLevel_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool RunOptions_TraceLevel_IsValid(int value);
constexpr RunOptions_TraceLevel RunOptions_TraceLevel_TraceLevel_MIN = RunOptions_TraceLevel_NO_TRACE;
constexpr RunOptions_TraceLevel RunOptions_TraceLevel_TraceLevel_MAX = RunOptions_TraceLevel_FULL_TRACE;
constexpr int RunOptions_TraceLevel_TraceLevel_ARRAYSIZE = RunOptions_TraceLevel_TraceLevel_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RunOptions_TraceLevel_descriptor();
template<typename T>
inline const std::string& RunOptions_TraceLevel_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RunOptions_TraceLevel>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RunOptions_TraceLevel_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RunOptions_TraceLevel_descriptor(), enum_t_value);
}
inline bool RunOptions_TraceLevel_Parse(
    const std::string& name, RunOptions_TraceLevel* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RunOptions_TraceLevel>(
    RunOptions_TraceLevel_descriptor(), name, value);
}
// ===================================================================

class GPUOptions_Experimental_VirtualDevices :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GPUOptions.Experimental.VirtualDevices) */ {
 public:
  GPUOptions_Experimental_VirtualDevices();
  virtual ~GPUOptions_Experimental_VirtualDevices();

  GPUOptions_Experimental_VirtualDevices(const GPUOptions_Experimental_VirtualDevices& from);
  GPUOptions_Experimental_VirtualDevices(GPUOptions_Experimental_VirtualDevices&& from) noexcept
    : GPUOptions_Experimental_VirtualDevices() {
    *this = ::std::move(from);
  }

  inline GPUOptions_Experimental_VirtualDevices& operator=(const GPUOptions_Experimental_VirtualDevices& from) {
    CopyFrom(from);
    return *this;
  }
  inline GPUOptions_Experimental_VirtualDevices& operator=(GPUOptions_Experimental_VirtualDevices&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GPUOptions_Experimental_VirtualDevices& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GPUOptions_Experimental_VirtualDevices* internal_default_instance() {
    return reinterpret_cast<const GPUOptions_Experimental_VirtualDevices*>(
               &_GPUOptions_Experimental_VirtualDevices_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GPUOptions_Experimental_VirtualDevices& a, GPUOptions_Experimental_VirtualDevices& b) {
    a.Swap(&b);
  }
  inline void Swap(GPUOptions_Experimental_VirtualDevices* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GPUOptions_Experimental_VirtualDevices* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GPUOptions_Experimental_VirtualDevices* New() const final {
    return CreateMaybeMessage<GPUOptions_Experimental_VirtualDevices>(nullptr);
  }

  GPUOptions_Experimental_VirtualDevices* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GPUOptions_Experimental_VirtualDevices>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GPUOptions_Experimental_VirtualDevices& from);
  void MergeFrom(const GPUOptions_Experimental_VirtualDevices& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GPUOptions_Experimental_VirtualDevices* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GPUOptions.Experimental.VirtualDevices";
  }
  protected:
  explicit GPUOptions_Experimental_VirtualDevices(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMemoryLimitMbFieldNumber = 1,
    kPriorityFieldNumber = 2,
  };
  // repeated float memory_limit_mb = 1;
  int memory_limit_mb_size() const;
  void clear_memory_limit_mb();
  float memory_limit_mb(int index) const;
  void set_memory_limit_mb(int index, float value);
  void add_memory_limit_mb(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      memory_limit_mb() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_memory_limit_mb();

  // repeated int32 priority = 2;
  int priority_size() const;
  void clear_priority();
  ::PROTOBUF_NAMESPACE_ID::int32 priority(int index) const;
  void set_priority(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_priority(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      priority() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_priority();

  // @@protoc_insertion_point(class_scope:tensorflow.GPUOptions.Experimental.VirtualDevices)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > memory_limit_mb_;
  mutable std::atomic<int> _memory_limit_mb_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > priority_;
  mutable std::atomic<int> _priority_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class GPUOptions_Experimental :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GPUOptions.Experimental) */ {
 public:
  GPUOptions_Experimental();
  virtual ~GPUOptions_Experimental();

  GPUOptions_Experimental(const GPUOptions_Experimental& from);
  GPUOptions_Experimental(GPUOptions_Experimental&& from) noexcept
    : GPUOptions_Experimental() {
    *this = ::std::move(from);
  }

  inline GPUOptions_Experimental& operator=(const GPUOptions_Experimental& from) {
    CopyFrom(from);
    return *this;
  }
  inline GPUOptions_Experimental& operator=(GPUOptions_Experimental&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GPUOptions_Experimental& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GPUOptions_Experimental* internal_default_instance() {
    return reinterpret_cast<const GPUOptions_Experimental*>(
               &_GPUOptions_Experimental_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GPUOptions_Experimental& a, GPUOptions_Experimental& b) {
    a.Swap(&b);
  }
  inline void Swap(GPUOptions_Experimental* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GPUOptions_Experimental* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GPUOptions_Experimental* New() const final {
    return CreateMaybeMessage<GPUOptions_Experimental>(nullptr);
  }

  GPUOptions_Experimental* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GPUOptions_Experimental>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GPUOptions_Experimental& from);
  void MergeFrom(const GPUOptions_Experimental& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GPUOptions_Experimental* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GPUOptions.Experimental";
  }
  protected:
  explicit GPUOptions_Experimental(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef GPUOptions_Experimental_VirtualDevices VirtualDevices;

  // accessors -------------------------------------------------------

  enum : int {
    kVirtualDevicesFieldNumber = 1,
    kCollectiveRingOrderFieldNumber = 4,
    kNumDevToDevCopyStreamsFieldNumber = 3,
    kUseUnifiedMemoryFieldNumber = 2,
    kTimestampedAllocatorFieldNumber = 5,
    kUseCudaMallocAsyncFieldNumber = 11,
    kKernelTrackerMaxIntervalFieldNumber = 7,
    kKernelTrackerMaxBytesFieldNumber = 8,
    kInternalFragmentationFractionFieldNumber = 10,
    kKernelTrackerMaxPendingFieldNumber = 9,
  };
  // repeated .tensorflow.GPUOptions.Experimental.VirtualDevices virtual_devices = 1;
  int virtual_devices_size() const;
  void clear_virtual_devices();
  ::tensorflow::GPUOptions_Experimental_VirtualDevices* mutable_virtual_devices(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GPUOptions_Experimental_VirtualDevices >*
      mutable_virtual_devices();
  const ::tensorflow::GPUOptions_Experimental_VirtualDevices& virtual_devices(int index) const;
  ::tensorflow::GPUOptions_Experimental_VirtualDevices* add_virtual_devices();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GPUOptions_Experimental_VirtualDevices >&
      virtual_devices() const;

  // string collective_ring_order = 4;
  void clear_collective_ring_order();
  const std::string& collective_ring_order() const;
  void set_collective_ring_order(const std::string& value);
  void set_collective_ring_order(std::string&& value);
  void set_collective_ring_order(const char* value);
  void set_collective_ring_order(const char* value, size_t size);
  std::string* mutable_collective_ring_order();
  std::string* release_collective_ring_order();
  void set_allocated_collective_ring_order(std::string* collective_ring_order);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_collective_ring_order();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_collective_ring_order(
      std::string* collective_ring_order);

  // int32 num_dev_to_dev_copy_streams = 3;
  void clear_num_dev_to_dev_copy_streams();
  ::PROTOBUF_NAMESPACE_ID::int32 num_dev_to_dev_copy_streams() const;
  void set_num_dev_to_dev_copy_streams(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool use_unified_memory = 2;
  void clear_use_unified_memory();
  bool use_unified_memory() const;
  void set_use_unified_memory(bool value);

  // bool timestamped_allocator = 5;
  void clear_timestamped_allocator();
  bool timestamped_allocator() const;
  void set_timestamped_allocator(bool value);

  // bool use_cuda_malloc_async = 11;
  void clear_use_cuda_malloc_async();
  bool use_cuda_malloc_async() const;
  void set_use_cuda_malloc_async(bool value);

  // int32 kernel_tracker_max_interval = 7;
  void clear_kernel_tracker_max_interval();
  ::PROTOBUF_NAMESPACE_ID::int32 kernel_tracker_max_interval() const;
  void set_kernel_tracker_max_interval(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 kernel_tracker_max_bytes = 8;
  void clear_kernel_tracker_max_bytes();
  ::PROTOBUF_NAMESPACE_ID::int32 kernel_tracker_max_bytes() const;
  void set_kernel_tracker_max_bytes(::PROTOBUF_NAMESPACE_ID::int32 value);

  // double internal_fragmentation_fraction = 10;
  void clear_internal_fragmentation_fraction();
  double internal_fragmentation_fraction() const;
  void set_internal_fragmentation_fraction(double value);

  // int32 kernel_tracker_max_pending = 9;
  void clear_kernel_tracker_max_pending();
  ::PROTOBUF_NAMESPACE_ID::int32 kernel_tracker_max_pending() const;
  void set_kernel_tracker_max_pending(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.GPUOptions.Experimental)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GPUOptions_Experimental_VirtualDevices > virtual_devices_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr collective_ring_order_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_dev_to_dev_copy_streams_;
  bool use_unified_memory_;
  bool timestamped_allocator_;
  bool use_cuda_malloc_async_;
  ::PROTOBUF_NAMESPACE_ID::int32 kernel_tracker_max_interval_;
  ::PROTOBUF_NAMESPACE_ID::int32 kernel_tracker_max_bytes_;
  double internal_fragmentation_fraction_;
  ::PROTOBUF_NAMESPACE_ID::int32 kernel_tracker_max_pending_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class GPUOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GPUOptions) */ {
 public:
  GPUOptions();
  virtual ~GPUOptions();

  GPUOptions(const GPUOptions& from);
  GPUOptions(GPUOptions&& from) noexcept
    : GPUOptions() {
    *this = ::std::move(from);
  }

  inline GPUOptions& operator=(const GPUOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline GPUOptions& operator=(GPUOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GPUOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GPUOptions* internal_default_instance() {
    return reinterpret_cast<const GPUOptions*>(
               &_GPUOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GPUOptions& a, GPUOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(GPUOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GPUOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GPUOptions* New() const final {
    return CreateMaybeMessage<GPUOptions>(nullptr);
  }

  GPUOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GPUOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GPUOptions& from);
  void MergeFrom(const GPUOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GPUOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GPUOptions";
  }
  protected:
  explicit GPUOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef GPUOptions_Experimental Experimental;

  // accessors -------------------------------------------------------

  enum : int {
    kAllocatorTypeFieldNumber = 2,
    kVisibleDeviceListFieldNumber = 5,
    kExperimentalFieldNumber = 9,
    kPerProcessGpuMemoryFractionFieldNumber = 1,
    kDeferredDeletionBytesFieldNumber = 3,
    kPollingActiveDelayUsecsFieldNumber = 6,
    kAllowGrowthFieldNumber = 4,
    kForceGpuCompatibleFieldNumber = 8,
    kPollingInactiveDelayMsecsFieldNumber = 7,
  };
  // string allocator_type = 2;
  void clear_allocator_type();
  const std::string& allocator_type() const;
  void set_allocator_type(const std::string& value);
  void set_allocator_type(std::string&& value);
  void set_allocator_type(const char* value);
  void set_allocator_type(const char* value, size_t size);
  std::string* mutable_allocator_type();
  std::string* release_allocator_type();
  void set_allocated_allocator_type(std::string* allocator_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_allocator_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_allocator_type(
      std::string* allocator_type);

  // string visible_device_list = 5;
  void clear_visible_device_list();
  const std::string& visible_device_list() const;
  void set_visible_device_list(const std::string& value);
  void set_visible_device_list(std::string&& value);
  void set_visible_device_list(const char* value);
  void set_visible_device_list(const char* value, size_t size);
  std::string* mutable_visible_device_list();
  std::string* release_visible_device_list();
  void set_allocated_visible_device_list(std::string* visible_device_list);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_visible_device_list();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_visible_device_list(
      std::string* visible_device_list);

  // .tensorflow.GPUOptions.Experimental experimental = 9;
  bool has_experimental() const;
  void clear_experimental();
  const ::tensorflow::GPUOptions_Experimental& experimental() const;
  ::tensorflow::GPUOptions_Experimental* release_experimental();
  ::tensorflow::GPUOptions_Experimental* mutable_experimental();
  void set_allocated_experimental(::tensorflow::GPUOptions_Experimental* experimental);
  void unsafe_arena_set_allocated_experimental(
      ::tensorflow::GPUOptions_Experimental* experimental);
  ::tensorflow::GPUOptions_Experimental* unsafe_arena_release_experimental();

  // double per_process_gpu_memory_fraction = 1;
  void clear_per_process_gpu_memory_fraction();
  double per_process_gpu_memory_fraction() const;
  void set_per_process_gpu_memory_fraction(double value);

  // int64 deferred_deletion_bytes = 3;
  void clear_deferred_deletion_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 deferred_deletion_bytes() const;
  void set_deferred_deletion_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int32 polling_active_delay_usecs = 6;
  void clear_polling_active_delay_usecs();
  ::PROTOBUF_NAMESPACE_ID::int32 polling_active_delay_usecs() const;
  void set_polling_active_delay_usecs(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool allow_growth = 4;
  void clear_allow_growth();
  bool allow_growth() const;
  void set_allow_growth(bool value);

  // bool force_gpu_compatible = 8;
  void clear_force_gpu_compatible();
  bool force_gpu_compatible() const;
  void set_force_gpu_compatible(bool value);

  // int32 polling_inactive_delay_msecs = 7;
  void clear_polling_inactive_delay_msecs();
  ::PROTOBUF_NAMESPACE_ID::int32 polling_inactive_delay_msecs() const;
  void set_polling_inactive_delay_msecs(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.GPUOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr visible_device_list_;
  ::tensorflow::GPUOptions_Experimental* experimental_;
  double per_process_gpu_memory_fraction_;
  ::PROTOBUF_NAMESPACE_ID::int64 deferred_deletion_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int32 polling_active_delay_usecs_;
  bool allow_growth_;
  bool force_gpu_compatible_;
  ::PROTOBUF_NAMESPACE_ID::int32 polling_inactive_delay_msecs_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class OptimizerOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OptimizerOptions) */ {
 public:
  OptimizerOptions();
  virtual ~OptimizerOptions();

  OptimizerOptions(const OptimizerOptions& from);
  OptimizerOptions(OptimizerOptions&& from) noexcept
    : OptimizerOptions() {
    *this = ::std::move(from);
  }

  inline OptimizerOptions& operator=(const OptimizerOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline OptimizerOptions& operator=(OptimizerOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OptimizerOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OptimizerOptions* internal_default_instance() {
    return reinterpret_cast<const OptimizerOptions*>(
               &_OptimizerOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(OptimizerOptions& a, OptimizerOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(OptimizerOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OptimizerOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OptimizerOptions* New() const final {
    return CreateMaybeMessage<OptimizerOptions>(nullptr);
  }

  OptimizerOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OptimizerOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OptimizerOptions& from);
  void MergeFrom(const OptimizerOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptimizerOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OptimizerOptions";
  }
  protected:
  explicit OptimizerOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef OptimizerOptions_Level Level;
  static constexpr Level L1 =
    OptimizerOptions_Level_L1;
  static constexpr Level L0 =
    OptimizerOptions_Level_L0;
  static inline bool Level_IsValid(int value) {
    return OptimizerOptions_Level_IsValid(value);
  }
  static constexpr Level Level_MIN =
    OptimizerOptions_Level_Level_MIN;
  static constexpr Level Level_MAX =
    OptimizerOptions_Level_Level_MAX;
  static constexpr int Level_ARRAYSIZE =
    OptimizerOptions_Level_Level_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Level_descriptor() {
    return OptimizerOptions_Level_descriptor();
  }
  template<typename T>
  static inline const std::string& Level_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Level>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Level_Name.");
    return OptimizerOptions_Level_Name(enum_t_value);
  }
  static inline bool Level_Parse(const std::string& name,
      Level* value) {
    return OptimizerOptions_Level_Parse(name, value);
  }

  typedef OptimizerOptions_GlobalJitLevel GlobalJitLevel;
  static constexpr GlobalJitLevel DEFAULT =
    OptimizerOptions_GlobalJitLevel_DEFAULT;
  static constexpr GlobalJitLevel OFF =
    OptimizerOptions_GlobalJitLevel_OFF;
  static constexpr GlobalJitLevel ON_1 =
    OptimizerOptions_GlobalJitLevel_ON_1;
  static constexpr GlobalJitLevel ON_2 =
    OptimizerOptions_GlobalJitLevel_ON_2;
  static inline bool GlobalJitLevel_IsValid(int value) {
    return OptimizerOptions_GlobalJitLevel_IsValid(value);
  }
  static constexpr GlobalJitLevel GlobalJitLevel_MIN =
    OptimizerOptions_GlobalJitLevel_GlobalJitLevel_MIN;
  static constexpr GlobalJitLevel GlobalJitLevel_MAX =
    OptimizerOptions_GlobalJitLevel_GlobalJitLevel_MAX;
  static constexpr int GlobalJitLevel_ARRAYSIZE =
    OptimizerOptions_GlobalJitLevel_GlobalJitLevel_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  GlobalJitLevel_descriptor() {
    return OptimizerOptions_GlobalJitLevel_descriptor();
  }
  template<typename T>
  static inline const std::string& GlobalJitLevel_Name(T enum_t_value) {
    static_assert(::std::is_same<T, GlobalJitLevel>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function GlobalJitLevel_Name.");
    return OptimizerOptions_GlobalJitLevel_Name(enum_t_value);
  }
  static inline bool GlobalJitLevel_Parse(const std::string& name,
      GlobalJitLevel* value) {
    return OptimizerOptions_GlobalJitLevel_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kDoCommonSubexpressionEliminationFieldNumber = 1,
    kDoConstantFoldingFieldNumber = 2,
    kDoFunctionInliningFieldNumber = 4,
    kOptLevelFieldNumber = 3,
    kMaxFoldedConstantInBytesFieldNumber = 6,
    kGlobalJitLevelFieldNumber = 5,
  };
  // bool do_common_subexpression_elimination = 1;
  void clear_do_common_subexpression_elimination();
  bool do_common_subexpression_elimination() const;
  void set_do_common_subexpression_elimination(bool value);

  // bool do_constant_folding = 2;
  void clear_do_constant_folding();
  bool do_constant_folding() const;
  void set_do_constant_folding(bool value);

  // bool do_function_inlining = 4;
  void clear_do_function_inlining();
  bool do_function_inlining() const;
  void set_do_function_inlining(bool value);

  // .tensorflow.OptimizerOptions.Level opt_level = 3;
  void clear_opt_level();
  ::tensorflow::OptimizerOptions_Level opt_level() const;
  void set_opt_level(::tensorflow::OptimizerOptions_Level value);

  // int64 max_folded_constant_in_bytes = 6;
  void clear_max_folded_constant_in_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 max_folded_constant_in_bytes() const;
  void set_max_folded_constant_in_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 5;
  void clear_global_jit_level();
  ::tensorflow::OptimizerOptions_GlobalJitLevel global_jit_level() const;
  void set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value);

  // @@protoc_insertion_point(class_scope:tensorflow.OptimizerOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool do_common_subexpression_elimination_;
  bool do_constant_folding_;
  bool do_function_inlining_;
  int opt_level_;
  ::PROTOBUF_NAMESPACE_ID::int64 max_folded_constant_in_bytes_;
  int global_jit_level_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class GraphOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphOptions) */ {
 public:
  GraphOptions();
  virtual ~GraphOptions();

  GraphOptions(const GraphOptions& from);
  GraphOptions(GraphOptions&& from) noexcept
    : GraphOptions() {
    *this = ::std::move(from);
  }

  inline GraphOptions& operator=(const GraphOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphOptions& operator=(GraphOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphOptions* internal_default_instance() {
    return reinterpret_cast<const GraphOptions*>(
               &_GraphOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GraphOptions& a, GraphOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphOptions* New() const final {
    return CreateMaybeMessage<GraphOptions>(nullptr);
  }

  GraphOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphOptions& from);
  void MergeFrom(const GraphOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphOptions";
  }
  protected:
  explicit GraphOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOptimizerOptionsFieldNumber = 3,
    kRewriteOptionsFieldNumber = 10,
    kBuildCostModelFieldNumber = 4,
    kEnableRecvSchedulingFieldNumber = 2,
    kInferShapesFieldNumber = 5,
    kPlacePrunedGraphFieldNumber = 6,
    kEnableBfloat16SendrecvFieldNumber = 7,
    kTimelineStepFieldNumber = 8,
    kBuildCostModelAfterFieldNumber = 9,
  };
  // .tensorflow.OptimizerOptions optimizer_options = 3;
  bool has_optimizer_options() const;
  void clear_optimizer_options();
  const ::tensorflow::OptimizerOptions& optimizer_options() const;
  ::tensorflow::OptimizerOptions* release_optimizer_options();
  ::tensorflow::OptimizerOptions* mutable_optimizer_options();
  void set_allocated_optimizer_options(::tensorflow::OptimizerOptions* optimizer_options);
  void unsafe_arena_set_allocated_optimizer_options(
      ::tensorflow::OptimizerOptions* optimizer_options);
  ::tensorflow::OptimizerOptions* unsafe_arena_release_optimizer_options();

  // .tensorflow.RewriterConfig rewrite_options = 10;
  bool has_rewrite_options() const;
  void clear_rewrite_options();
  const ::tensorflow::RewriterConfig& rewrite_options() const;
  ::tensorflow::RewriterConfig* release_rewrite_options();
  ::tensorflow::RewriterConfig* mutable_rewrite_options();
  void set_allocated_rewrite_options(::tensorflow::RewriterConfig* rewrite_options);
  void unsafe_arena_set_allocated_rewrite_options(
      ::tensorflow::RewriterConfig* rewrite_options);
  ::tensorflow::RewriterConfig* unsafe_arena_release_rewrite_options();

  // int64 build_cost_model = 4;
  void clear_build_cost_model();
  ::PROTOBUF_NAMESPACE_ID::int64 build_cost_model() const;
  void set_build_cost_model(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool enable_recv_scheduling = 2;
  void clear_enable_recv_scheduling();
  bool enable_recv_scheduling() const;
  void set_enable_recv_scheduling(bool value);

  // bool infer_shapes = 5;
  void clear_infer_shapes();
  bool infer_shapes() const;
  void set_infer_shapes(bool value);

  // bool place_pruned_graph = 6;
  void clear_place_pruned_graph();
  bool place_pruned_graph() const;
  void set_place_pruned_graph(bool value);

  // bool enable_bfloat16_sendrecv = 7;
  void clear_enable_bfloat16_sendrecv();
  bool enable_bfloat16_sendrecv() const;
  void set_enable_bfloat16_sendrecv(bool value);

  // int32 timeline_step = 8;
  void clear_timeline_step();
  ::PROTOBUF_NAMESPACE_ID::int32 timeline_step() const;
  void set_timeline_step(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int64 build_cost_model_after = 9;
  void clear_build_cost_model_after();
  ::PROTOBUF_NAMESPACE_ID::int64 build_cost_model_after() const;
  void set_build_cost_model_after(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::OptimizerOptions* optimizer_options_;
  ::tensorflow::RewriterConfig* rewrite_options_;
  ::PROTOBUF_NAMESPACE_ID::int64 build_cost_model_;
  bool enable_recv_scheduling_;
  bool infer_shapes_;
  bool place_pruned_graph_;
  bool enable_bfloat16_sendrecv_;
  ::PROTOBUF_NAMESPACE_ID::int32 timeline_step_;
  ::PROTOBUF_NAMESPACE_ID::int64 build_cost_model_after_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class ThreadPoolOptionProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ThreadPoolOptionProto) */ {
 public:
  ThreadPoolOptionProto();
  virtual ~ThreadPoolOptionProto();

  ThreadPoolOptionProto(const ThreadPoolOptionProto& from);
  ThreadPoolOptionProto(ThreadPoolOptionProto&& from) noexcept
    : ThreadPoolOptionProto() {
    *this = ::std::move(from);
  }

  inline ThreadPoolOptionProto& operator=(const ThreadPoolOptionProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ThreadPoolOptionProto& operator=(ThreadPoolOptionProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ThreadPoolOptionProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ThreadPoolOptionProto* internal_default_instance() {
    return reinterpret_cast<const ThreadPoolOptionProto*>(
               &_ThreadPoolOptionProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ThreadPoolOptionProto& a, ThreadPoolOptionProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ThreadPoolOptionProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ThreadPoolOptionProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ThreadPoolOptionProto* New() const final {
    return CreateMaybeMessage<ThreadPoolOptionProto>(nullptr);
  }

  ThreadPoolOptionProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ThreadPoolOptionProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ThreadPoolOptionProto& from);
  void MergeFrom(const ThreadPoolOptionProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ThreadPoolOptionProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ThreadPoolOptionProto";
  }
  protected:
  explicit ThreadPoolOptionProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGlobalNameFieldNumber = 2,
    kNumThreadsFieldNumber = 1,
  };
  // string global_name = 2;
  void clear_global_name();
  const std::string& global_name() const;
  void set_global_name(const std::string& value);
  void set_global_name(std::string&& value);
  void set_global_name(const char* value);
  void set_global_name(const char* value, size_t size);
  std::string* mutable_global_name();
  std::string* release_global_name();
  void set_allocated_global_name(std::string* global_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_global_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_global_name(
      std::string* global_name);

  // int32 num_threads = 1;
  void clear_num_threads();
  ::PROTOBUF_NAMESPACE_ID::int32 num_threads() const;
  void set_num_threads(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ThreadPoolOptionProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr global_name_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_threads_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RPCOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RPCOptions) */ {
 public:
  RPCOptions();
  virtual ~RPCOptions();

  RPCOptions(const RPCOptions& from);
  RPCOptions(RPCOptions&& from) noexcept
    : RPCOptions() {
    *this = ::std::move(from);
  }

  inline RPCOptions& operator=(const RPCOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline RPCOptions& operator=(RPCOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RPCOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RPCOptions* internal_default_instance() {
    return reinterpret_cast<const RPCOptions*>(
               &_RPCOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(RPCOptions& a, RPCOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(RPCOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RPCOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RPCOptions* New() const final {
    return CreateMaybeMessage<RPCOptions>(nullptr);
  }

  RPCOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RPCOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RPCOptions& from);
  void MergeFrom(const RPCOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RPCOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RPCOptions";
  }
  protected:
  explicit RPCOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCompressionAlgorithmFieldNumber = 2,
    kCompressionLevelFieldNumber = 3,
    kUseRpcForInprocessMasterFieldNumber = 1,
    kCacheRpcResponseFieldNumber = 4,
    kDisableSessionConnectionSharingFieldNumber = 5,
    kNumChannelsPerTargetFieldNumber = 6,
  };
  // string compression_algorithm = 2;
  void clear_compression_algorithm();
  const std::string& compression_algorithm() const;
  void set_compression_algorithm(const std::string& value);
  void set_compression_algorithm(std::string&& value);
  void set_compression_algorithm(const char* value);
  void set_compression_algorithm(const char* value, size_t size);
  std::string* mutable_compression_algorithm();
  std::string* release_compression_algorithm();
  void set_allocated_compression_algorithm(std::string* compression_algorithm);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_compression_algorithm();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_compression_algorithm(
      std::string* compression_algorithm);

  // int32 compression_level = 3;
  void clear_compression_level();
  ::PROTOBUF_NAMESPACE_ID::int32 compression_level() const;
  void set_compression_level(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool use_rpc_for_inprocess_master = 1;
  void clear_use_rpc_for_inprocess_master();
  bool use_rpc_for_inprocess_master() const;
  void set_use_rpc_for_inprocess_master(bool value);

  // bool cache_rpc_response = 4;
  void clear_cache_rpc_response();
  bool cache_rpc_response() const;
  void set_cache_rpc_response(bool value);

  // bool disable_session_connection_sharing = 5;
  void clear_disable_session_connection_sharing();
  bool disable_session_connection_sharing() const;
  void set_disable_session_connection_sharing(bool value);

  // int32 num_channels_per_target = 6;
  void clear_num_channels_per_target();
  ::PROTOBUF_NAMESPACE_ID::int32 num_channels_per_target() const;
  void set_num_channels_per_target(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.RPCOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr compression_algorithm_;
  ::PROTOBUF_NAMESPACE_ID::int32 compression_level_;
  bool use_rpc_for_inprocess_master_;
  bool cache_rpc_response_;
  bool disable_session_connection_sharing_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_channels_per_target_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class SessionMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SessionMetadata) */ {
 public:
  SessionMetadata();
  virtual ~SessionMetadata();

  SessionMetadata(const SessionMetadata& from);
  SessionMetadata(SessionMetadata&& from) noexcept
    : SessionMetadata() {
    *this = ::std::move(from);
  }

  inline SessionMetadata& operator=(const SessionMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline SessionMetadata& operator=(SessionMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SessionMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SessionMetadata* internal_default_instance() {
    return reinterpret_cast<const SessionMetadata*>(
               &_SessionMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(SessionMetadata& a, SessionMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(SessionMetadata* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SessionMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SessionMetadata* New() const final {
    return CreateMaybeMessage<SessionMetadata>(nullptr);
  }

  SessionMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SessionMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SessionMetadata& from);
  void MergeFrom(const SessionMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SessionMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SessionMetadata";
  }
  protected:
  explicit SessionMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kVersionFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // int64 version = 2;
  void clear_version();
  ::PROTOBUF_NAMESPACE_ID::int64 version() const;
  void set_version(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.SessionMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::int64 version_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class ConfigProto_DeviceCountEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ConfigProto_DeviceCountEntry_DoNotUse, 
    std::string, ::PROTOBUF_NAMESPACE_ID::int32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ConfigProto_DeviceCountEntry_DoNotUse, 
    std::string, ::PROTOBUF_NAMESPACE_ID::int32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    0 > SuperType;
  ConfigProto_DeviceCountEntry_DoNotUse();
  ConfigProto_DeviceCountEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ConfigProto_DeviceCountEntry_DoNotUse& other);
  static const ConfigProto_DeviceCountEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ConfigProto_DeviceCountEntry_DoNotUse*>(&_ConfigProto_DeviceCountEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ConfigProto.DeviceCountEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[8];
  }

  public:
};

// -------------------------------------------------------------------

class ConfigProto_Experimental :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ConfigProto.Experimental) */ {
 public:
  ConfigProto_Experimental();
  virtual ~ConfigProto_Experimental();

  ConfigProto_Experimental(const ConfigProto_Experimental& from);
  ConfigProto_Experimental(ConfigProto_Experimental&& from) noexcept
    : ConfigProto_Experimental() {
    *this = ::std::move(from);
  }

  inline ConfigProto_Experimental& operator=(const ConfigProto_Experimental& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigProto_Experimental& operator=(ConfigProto_Experimental&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ConfigProto_Experimental& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConfigProto_Experimental* internal_default_instance() {
    return reinterpret_cast<const ConfigProto_Experimental*>(
               &_ConfigProto_Experimental_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(ConfigProto_Experimental& a, ConfigProto_Experimental& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigProto_Experimental* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigProto_Experimental* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ConfigProto_Experimental* New() const final {
    return CreateMaybeMessage<ConfigProto_Experimental>(nullptr);
  }

  ConfigProto_Experimental* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ConfigProto_Experimental>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ConfigProto_Experimental& from);
  void MergeFrom(const ConfigProto_Experimental& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigProto_Experimental* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ConfigProto.Experimental";
  }
  protected:
  explicit ConfigProto_Experimental(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef ConfigProto_Experimental_MlirBridgeRollout MlirBridgeRollout;
  static constexpr MlirBridgeRollout MLIR_BRIDGE_ROLLOUT_UNSPECIFIED =
    ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_UNSPECIFIED;
  static constexpr MlirBridgeRollout MLIR_BRIDGE_ROLLOUT_ENABLED =
    ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_ENABLED;
  static constexpr MlirBridgeRollout MLIR_BRIDGE_ROLLOUT_DISABLED =
    ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_DISABLED;
  static constexpr MlirBridgeRollout MLIR_BRIDGE_ROLLOUT_SAFE_MODE_ENABLED =
    ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_SAFE_MODE_ENABLED;
  static constexpr MlirBridgeRollout MLIR_BRIDGE_ROLLOUT_SAFE_MODE_FALLBACK_ENABLED =
    ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_SAFE_MODE_FALLBACK_ENABLED;
  static inline bool MlirBridgeRollout_IsValid(int value) {
    return ConfigProto_Experimental_MlirBridgeRollout_IsValid(value);
  }
  static constexpr MlirBridgeRollout MlirBridgeRollout_MIN =
    ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_MIN;
  static constexpr MlirBridgeRollout MlirBridgeRollout_MAX =
    ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_MAX;
  static constexpr int MlirBridgeRollout_ARRAYSIZE =
    ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MlirBridgeRollout_descriptor() {
    return ConfigProto_Experimental_MlirBridgeRollout_descriptor();
  }
  template<typename T>
  static inline const std::string& MlirBridgeRollout_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MlirBridgeRollout>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MlirBridgeRollout_Name.");
    return ConfigProto_Experimental_MlirBridgeRollout_Name(enum_t_value);
  }
  static inline bool MlirBridgeRollout_Parse(const std::string& name,
      MlirBridgeRollout* value) {
    return ConfigProto_Experimental_MlirBridgeRollout_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kCollectiveGroupLeaderFieldNumber = 1,
    kExecutorTypeFieldNumber = 3,
    kCoordinationServiceFieldNumber = 19,
    kSessionMetadataFieldNumber = 11,
    kRecvBufMaxChunkFieldNumber = 4,
    kUseNumaAffinityFieldNumber = 5,
    kCollectiveDeterministicSequentialExecutionFieldNumber = 6,
    kCollectiveNcclFieldNumber = 7,
    kShareSessionStateInClusterspecPropagationFieldNumber = 8,
    kDisableThreadSpinningFieldNumber = 9,
    kShareClusterDevicesInSessionFieldNumber = 10,
    kOptimizeForStaticGraphFieldNumber = 12,
    kEnableMlirBridgeFieldNumber = 13,
    kMlirBridgeRolloutFieldNumber = 17,
    kXlaFusionAutotunerThreshFieldNumber = 15,
    kEnableMlirGraphOptimizationFieldNumber = 16,
    kDisableOutputPartitionGraphsFieldNumber = 14,
    kUseTfrtFieldNumber = 18,
    kFetchRemoteDevicesInMultiClientFieldNumber = 20,
  };
  // string collective_group_leader = 1;
  void clear_collective_group_leader();
  const std::string& collective_group_leader() const;
  void set_collective_group_leader(const std::string& value);
  void set_collective_group_leader(std::string&& value);
  void set_collective_group_leader(const char* value);
  void set_collective_group_leader(const char* value, size_t size);
  std::string* mutable_collective_group_leader();
  std::string* release_collective_group_leader();
  void set_allocated_collective_group_leader(std::string* collective_group_leader);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_collective_group_leader();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_collective_group_leader(
      std::string* collective_group_leader);

  // string executor_type = 3;
  void clear_executor_type();
  const std::string& executor_type() const;
  void set_executor_type(const std::string& value);
  void set_executor_type(std::string&& value);
  void set_executor_type(const char* value);
  void set_executor_type(const char* value, size_t size);
  std::string* mutable_executor_type();
  std::string* release_executor_type();
  void set_allocated_executor_type(std::string* executor_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_executor_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_executor_type(
      std::string* executor_type);

  // string coordination_service = 19;
  void clear_coordination_service();
  const std::string& coordination_service() const;
  void set_coordination_service(const std::string& value);
  void set_coordination_service(std::string&& value);
  void set_coordination_service(const char* value);
  void set_coordination_service(const char* value, size_t size);
  std::string* mutable_coordination_service();
  std::string* release_coordination_service();
  void set_allocated_coordination_service(std::string* coordination_service);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_coordination_service();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_coordination_service(
      std::string* coordination_service);

  // .tensorflow.SessionMetadata session_metadata = 11;
  bool has_session_metadata() const;
  void clear_session_metadata();
  const ::tensorflow::SessionMetadata& session_metadata() const;
  ::tensorflow::SessionMetadata* release_session_metadata();
  ::tensorflow::SessionMetadata* mutable_session_metadata();
  void set_allocated_session_metadata(::tensorflow::SessionMetadata* session_metadata);
  void unsafe_arena_set_allocated_session_metadata(
      ::tensorflow::SessionMetadata* session_metadata);
  ::tensorflow::SessionMetadata* unsafe_arena_release_session_metadata();

  // int32 recv_buf_max_chunk = 4;
  void clear_recv_buf_max_chunk();
  ::PROTOBUF_NAMESPACE_ID::int32 recv_buf_max_chunk() const;
  void set_recv_buf_max_chunk(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool use_numa_affinity = 5;
  void clear_use_numa_affinity();
  bool use_numa_affinity() const;
  void set_use_numa_affinity(bool value);

  // bool collective_deterministic_sequential_execution = 6;
  void clear_collective_deterministic_sequential_execution();
  bool collective_deterministic_sequential_execution() const;
  void set_collective_deterministic_sequential_execution(bool value);

  // bool collective_nccl = 7;
  void clear_collective_nccl();
  bool collective_nccl() const;
  void set_collective_nccl(bool value);

  // bool share_session_state_in_clusterspec_propagation = 8;
  void clear_share_session_state_in_clusterspec_propagation();
  bool share_session_state_in_clusterspec_propagation() const;
  void set_share_session_state_in_clusterspec_propagation(bool value);

  // bool disable_thread_spinning = 9;
  void clear_disable_thread_spinning();
  bool disable_thread_spinning() const;
  void set_disable_thread_spinning(bool value);

  // bool share_cluster_devices_in_session = 10;
  void clear_share_cluster_devices_in_session();
  bool share_cluster_devices_in_session() const;
  void set_share_cluster_devices_in_session(bool value);

  // bool optimize_for_static_graph = 12;
  void clear_optimize_for_static_graph();
  bool optimize_for_static_graph() const;
  void set_optimize_for_static_graph(bool value);

  // bool enable_mlir_bridge = 13;
  void clear_enable_mlir_bridge();
  bool enable_mlir_bridge() const;
  void set_enable_mlir_bridge(bool value);

  // .tensorflow.ConfigProto.Experimental.MlirBridgeRollout mlir_bridge_rollout = 17;
  void clear_mlir_bridge_rollout();
  ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout mlir_bridge_rollout() const;
  void set_mlir_bridge_rollout(::tensorflow::ConfigProto_Experimental_MlirBridgeRollout value);

  // int64 xla_fusion_autotuner_thresh = 15;
  void clear_xla_fusion_autotuner_thresh();
  ::PROTOBUF_NAMESPACE_ID::int64 xla_fusion_autotuner_thresh() const;
  void set_xla_fusion_autotuner_thresh(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool enable_mlir_graph_optimization = 16;
  void clear_enable_mlir_graph_optimization();
  bool enable_mlir_graph_optimization() const;
  void set_enable_mlir_graph_optimization(bool value);

  // bool disable_output_partition_graphs = 14;
  void clear_disable_output_partition_graphs();
  bool disable_output_partition_graphs() const;
  void set_disable_output_partition_graphs(bool value);

  // bool use_tfrt = 18;
  void clear_use_tfrt();
  bool use_tfrt() const;
  void set_use_tfrt(bool value);

  // bool fetch_remote_devices_in_multi_client = 20;
  void clear_fetch_remote_devices_in_multi_client();
  bool fetch_remote_devices_in_multi_client() const;
  void set_fetch_remote_devices_in_multi_client(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.ConfigProto.Experimental)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr collective_group_leader_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr executor_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr coordination_service_;
  ::tensorflow::SessionMetadata* session_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int32 recv_buf_max_chunk_;
  bool use_numa_affinity_;
  bool collective_deterministic_sequential_execution_;
  bool collective_nccl_;
  bool share_session_state_in_clusterspec_propagation_;
  bool disable_thread_spinning_;
  bool share_cluster_devices_in_session_;
  bool optimize_for_static_graph_;
  bool enable_mlir_bridge_;
  int mlir_bridge_rollout_;
  ::PROTOBUF_NAMESPACE_ID::int64 xla_fusion_autotuner_thresh_;
  bool enable_mlir_graph_optimization_;
  bool disable_output_partition_graphs_;
  bool use_tfrt_;
  bool fetch_remote_devices_in_multi_client_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class ConfigProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ConfigProto) */ {
 public:
  ConfigProto();
  virtual ~ConfigProto();

  ConfigProto(const ConfigProto& from);
  ConfigProto(ConfigProto&& from) noexcept
    : ConfigProto() {
    *this = ::std::move(from);
  }

  inline ConfigProto& operator=(const ConfigProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigProto& operator=(ConfigProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ConfigProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConfigProto* internal_default_instance() {
    return reinterpret_cast<const ConfigProto*>(
               &_ConfigProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(ConfigProto& a, ConfigProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ConfigProto* New() const final {
    return CreateMaybeMessage<ConfigProto>(nullptr);
  }

  ConfigProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ConfigProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ConfigProto& from);
  void MergeFrom(const ConfigProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ConfigProto";
  }
  protected:
  explicit ConfigProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef ConfigProto_Experimental Experimental;

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceCountFieldNumber = 1,
    kDeviceFiltersFieldNumber = 4,
    kSessionInterOpThreadPoolFieldNumber = 12,
    kGpuOptionsFieldNumber = 6,
    kGraphOptionsFieldNumber = 10,
    kRpcOptionsFieldNumber = 13,
    kClusterDefFieldNumber = 14,
    kExperimentalFieldNumber = 16,
    kIntraOpParallelismThreadsFieldNumber = 2,
    kPlacementPeriodFieldNumber = 3,
    kInterOpParallelismThreadsFieldNumber = 5,
    kUsePerSessionThreadsFieldNumber = 9,
    kAllowSoftPlacementFieldNumber = 7,
    kLogDevicePlacementFieldNumber = 8,
    kIsolateSessionStateFieldNumber = 15,
    kOperationTimeoutInMsFieldNumber = 11,
    kShareClusterDevicesInSessionFieldNumber = 17,
  };
  // map<string, int32> device_count = 1;
  int device_count_size() const;
  void clear_device_count();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int32 >&
      device_count() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_device_count();

  // repeated string device_filters = 4;
  int device_filters_size() const;
  void clear_device_filters();
  const std::string& device_filters(int index) const;
  std::string* mutable_device_filters(int index);
  void set_device_filters(int index, const std::string& value);
  void set_device_filters(int index, std::string&& value);
  void set_device_filters(int index, const char* value);
  void set_device_filters(int index, const char* value, size_t size);
  std::string* add_device_filters();
  void add_device_filters(const std::string& value);
  void add_device_filters(std::string&& value);
  void add_device_filters(const char* value);
  void add_device_filters(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& device_filters() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_device_filters();

  // repeated .tensorflow.ThreadPoolOptionProto session_inter_op_thread_pool = 12;
  int session_inter_op_thread_pool_size() const;
  void clear_session_inter_op_thread_pool();
  ::tensorflow::ThreadPoolOptionProto* mutable_session_inter_op_thread_pool(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ThreadPoolOptionProto >*
      mutable_session_inter_op_thread_pool();
  const ::tensorflow::ThreadPoolOptionProto& session_inter_op_thread_pool(int index) const;
  ::tensorflow::ThreadPoolOptionProto* add_session_inter_op_thread_pool();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ThreadPoolOptionProto >&
      session_inter_op_thread_pool() const;

  // .tensorflow.GPUOptions gpu_options = 6;
  bool has_gpu_options() const;
  void clear_gpu_options();
  const ::tensorflow::GPUOptions& gpu_options() const;
  ::tensorflow::GPUOptions* release_gpu_options();
  ::tensorflow::GPUOptions* mutable_gpu_options();
  void set_allocated_gpu_options(::tensorflow::GPUOptions* gpu_options);
  void unsafe_arena_set_allocated_gpu_options(
      ::tensorflow::GPUOptions* gpu_options);
  ::tensorflow::GPUOptions* unsafe_arena_release_gpu_options();

  // .tensorflow.GraphOptions graph_options = 10;
  bool has_graph_options() const;
  void clear_graph_options();
  const ::tensorflow::GraphOptions& graph_options() const;
  ::tensorflow::GraphOptions* release_graph_options();
  ::tensorflow::GraphOptions* mutable_graph_options();
  void set_allocated_graph_options(::tensorflow::GraphOptions* graph_options);
  void unsafe_arena_set_allocated_graph_options(
      ::tensorflow::GraphOptions* graph_options);
  ::tensorflow::GraphOptions* unsafe_arena_release_graph_options();

  // .tensorflow.RPCOptions rpc_options = 13;
  bool has_rpc_options() const;
  void clear_rpc_options();
  const ::tensorflow::RPCOptions& rpc_options() const;
  ::tensorflow::RPCOptions* release_rpc_options();
  ::tensorflow::RPCOptions* mutable_rpc_options();
  void set_allocated_rpc_options(::tensorflow::RPCOptions* rpc_options);
  void unsafe_arena_set_allocated_rpc_options(
      ::tensorflow::RPCOptions* rpc_options);
  ::tensorflow::RPCOptions* unsafe_arena_release_rpc_options();

  // .tensorflow.ClusterDef cluster_def = 14;
  bool has_cluster_def() const;
  void clear_cluster_def();
  const ::tensorflow::ClusterDef& cluster_def() const;
  ::tensorflow::ClusterDef* release_cluster_def();
  ::tensorflow::ClusterDef* mutable_cluster_def();
  void set_allocated_cluster_def(::tensorflow::ClusterDef* cluster_def);
  void unsafe_arena_set_allocated_cluster_def(
      ::tensorflow::ClusterDef* cluster_def);
  ::tensorflow::ClusterDef* unsafe_arena_release_cluster_def();

  // .tensorflow.ConfigProto.Experimental experimental = 16;
  bool has_experimental() const;
  void clear_experimental();
  const ::tensorflow::ConfigProto_Experimental& experimental() const;
  ::tensorflow::ConfigProto_Experimental* release_experimental();
  ::tensorflow::ConfigProto_Experimental* mutable_experimental();
  void set_allocated_experimental(::tensorflow::ConfigProto_Experimental* experimental);
  void unsafe_arena_set_allocated_experimental(
      ::tensorflow::ConfigProto_Experimental* experimental);
  ::tensorflow::ConfigProto_Experimental* unsafe_arena_release_experimental();

  // int32 intra_op_parallelism_threads = 2;
  void clear_intra_op_parallelism_threads();
  ::PROTOBUF_NAMESPACE_ID::int32 intra_op_parallelism_threads() const;
  void set_intra_op_parallelism_threads(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 placement_period = 3;
  void clear_placement_period();
  ::PROTOBUF_NAMESPACE_ID::int32 placement_period() const;
  void set_placement_period(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 inter_op_parallelism_threads = 5;
  void clear_inter_op_parallelism_threads();
  ::PROTOBUF_NAMESPACE_ID::int32 inter_op_parallelism_threads() const;
  void set_inter_op_parallelism_threads(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool use_per_session_threads = 9;
  void clear_use_per_session_threads();
  bool use_per_session_threads() const;
  void set_use_per_session_threads(bool value);

  // bool allow_soft_placement = 7;
  void clear_allow_soft_placement();
  bool allow_soft_placement() const;
  void set_allow_soft_placement(bool value);

  // bool log_device_placement = 8;
  void clear_log_device_placement();
  bool log_device_placement() const;
  void set_log_device_placement(bool value);

  // bool isolate_session_state = 15;
  void clear_isolate_session_state();
  bool isolate_session_state() const;
  void set_isolate_session_state(bool value);

  // int64 operation_timeout_in_ms = 11;
  void clear_operation_timeout_in_ms();
  ::PROTOBUF_NAMESPACE_ID::int64 operation_timeout_in_ms() const;
  void set_operation_timeout_in_ms(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool share_cluster_devices_in_session = 17;
  void clear_share_cluster_devices_in_session();
  bool share_cluster_devices_in_session() const;
  void set_share_cluster_devices_in_session(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.ConfigProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ConfigProto_DeviceCountEntry_DoNotUse,
      std::string, ::PROTOBUF_NAMESPACE_ID::int32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      0 > device_count_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> device_filters_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ThreadPoolOptionProto > session_inter_op_thread_pool_;
  ::tensorflow::GPUOptions* gpu_options_;
  ::tensorflow::GraphOptions* graph_options_;
  ::tensorflow::RPCOptions* rpc_options_;
  ::tensorflow::ClusterDef* cluster_def_;
  ::tensorflow::ConfigProto_Experimental* experimental_;
  ::PROTOBUF_NAMESPACE_ID::int32 intra_op_parallelism_threads_;
  ::PROTOBUF_NAMESPACE_ID::int32 placement_period_;
  ::PROTOBUF_NAMESPACE_ID::int32 inter_op_parallelism_threads_;
  bool use_per_session_threads_;
  bool allow_soft_placement_;
  bool log_device_placement_;
  bool isolate_session_state_;
  ::PROTOBUF_NAMESPACE_ID::int64 operation_timeout_in_ms_;
  bool share_cluster_devices_in_session_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RunOptions_Experimental_RunHandlerPoolOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunOptions.Experimental.RunHandlerPoolOptions) */ {
 public:
  RunOptions_Experimental_RunHandlerPoolOptions();
  virtual ~RunOptions_Experimental_RunHandlerPoolOptions();

  RunOptions_Experimental_RunHandlerPoolOptions(const RunOptions_Experimental_RunHandlerPoolOptions& from);
  RunOptions_Experimental_RunHandlerPoolOptions(RunOptions_Experimental_RunHandlerPoolOptions&& from) noexcept
    : RunOptions_Experimental_RunHandlerPoolOptions() {
    *this = ::std::move(from);
  }

  inline RunOptions_Experimental_RunHandlerPoolOptions& operator=(const RunOptions_Experimental_RunHandlerPoolOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunOptions_Experimental_RunHandlerPoolOptions& operator=(RunOptions_Experimental_RunHandlerPoolOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunOptions_Experimental_RunHandlerPoolOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunOptions_Experimental_RunHandlerPoolOptions* internal_default_instance() {
    return reinterpret_cast<const RunOptions_Experimental_RunHandlerPoolOptions*>(
               &_RunOptions_Experimental_RunHandlerPoolOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(RunOptions_Experimental_RunHandlerPoolOptions& a, RunOptions_Experimental_RunHandlerPoolOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(RunOptions_Experimental_RunHandlerPoolOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunOptions_Experimental_RunHandlerPoolOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunOptions_Experimental_RunHandlerPoolOptions* New() const final {
    return CreateMaybeMessage<RunOptions_Experimental_RunHandlerPoolOptions>(nullptr);
  }

  RunOptions_Experimental_RunHandlerPoolOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunOptions_Experimental_RunHandlerPoolOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunOptions_Experimental_RunHandlerPoolOptions& from);
  void MergeFrom(const RunOptions_Experimental_RunHandlerPoolOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunOptions_Experimental_RunHandlerPoolOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunOptions.Experimental.RunHandlerPoolOptions";
  }
  protected:
  explicit RunOptions_Experimental_RunHandlerPoolOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPriorityFieldNumber = 1,
  };
  // int64 priority = 1;
  void clear_priority();
  ::PROTOBUF_NAMESPACE_ID::int64 priority() const;
  void set_priority(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunOptions.Experimental.RunHandlerPoolOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 priority_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RunOptions_Experimental :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunOptions.Experimental) */ {
 public:
  RunOptions_Experimental();
  virtual ~RunOptions_Experimental();

  RunOptions_Experimental(const RunOptions_Experimental& from);
  RunOptions_Experimental(RunOptions_Experimental&& from) noexcept
    : RunOptions_Experimental() {
    *this = ::std::move(from);
  }

  inline RunOptions_Experimental& operator=(const RunOptions_Experimental& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunOptions_Experimental& operator=(RunOptions_Experimental&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunOptions_Experimental& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunOptions_Experimental* internal_default_instance() {
    return reinterpret_cast<const RunOptions_Experimental*>(
               &_RunOptions_Experimental_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(RunOptions_Experimental& a, RunOptions_Experimental& b) {
    a.Swap(&b);
  }
  inline void Swap(RunOptions_Experimental* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunOptions_Experimental* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunOptions_Experimental* New() const final {
    return CreateMaybeMessage<RunOptions_Experimental>(nullptr);
  }

  RunOptions_Experimental* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunOptions_Experimental>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunOptions_Experimental& from);
  void MergeFrom(const RunOptions_Experimental& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunOptions_Experimental* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunOptions.Experimental";
  }
  protected:
  explicit RunOptions_Experimental(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef RunOptions_Experimental_RunHandlerPoolOptions RunHandlerPoolOptions;

  // accessors -------------------------------------------------------

  enum : int {
    kRunHandlerPoolOptionsFieldNumber = 3,
    kCollectiveGraphKeyFieldNumber = 1,
    kUseRunHandlerPoolFieldNumber = 2,
  };
  // .tensorflow.RunOptions.Experimental.RunHandlerPoolOptions run_handler_pool_options = 3;
  bool has_run_handler_pool_options() const;
  void clear_run_handler_pool_options();
  const ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions& run_handler_pool_options() const;
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* release_run_handler_pool_options();
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* mutable_run_handler_pool_options();
  void set_allocated_run_handler_pool_options(::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* run_handler_pool_options);
  void unsafe_arena_set_allocated_run_handler_pool_options(
      ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* run_handler_pool_options);
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* unsafe_arena_release_run_handler_pool_options();

  // int64 collective_graph_key = 1;
  void clear_collective_graph_key();
  ::PROTOBUF_NAMESPACE_ID::int64 collective_graph_key() const;
  void set_collective_graph_key(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool use_run_handler_pool = 2;
  void clear_use_run_handler_pool();
  bool use_run_handler_pool() const;
  void set_use_run_handler_pool(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunOptions.Experimental)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* run_handler_pool_options_;
  ::PROTOBUF_NAMESPACE_ID::int64 collective_graph_key_;
  bool use_run_handler_pool_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RunOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunOptions) */ {
 public:
  RunOptions();
  virtual ~RunOptions();

  RunOptions(const RunOptions& from);
  RunOptions(RunOptions&& from) noexcept
    : RunOptions() {
    *this = ::std::move(from);
  }

  inline RunOptions& operator=(const RunOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunOptions& operator=(RunOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunOptions* internal_default_instance() {
    return reinterpret_cast<const RunOptions*>(
               &_RunOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RunOptions& a, RunOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(RunOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunOptions* New() const final {
    return CreateMaybeMessage<RunOptions>(nullptr);
  }

  RunOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunOptions& from);
  void MergeFrom(const RunOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunOptions";
  }
  protected:
  explicit RunOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef RunOptions_Experimental Experimental;

  typedef RunOptions_TraceLevel TraceLevel;
  static constexpr TraceLevel NO_TRACE =
    RunOptions_TraceLevel_NO_TRACE;
  static constexpr TraceLevel SOFTWARE_TRACE =
    RunOptions_TraceLevel_SOFTWARE_TRACE;
  static constexpr TraceLevel HARDWARE_TRACE =
    RunOptions_TraceLevel_HARDWARE_TRACE;
  static constexpr TraceLevel FULL_TRACE =
    RunOptions_TraceLevel_FULL_TRACE;
  static inline bool TraceLevel_IsValid(int value) {
    return RunOptions_TraceLevel_IsValid(value);
  }
  static constexpr TraceLevel TraceLevel_MIN =
    RunOptions_TraceLevel_TraceLevel_MIN;
  static constexpr TraceLevel TraceLevel_MAX =
    RunOptions_TraceLevel_TraceLevel_MAX;
  static constexpr int TraceLevel_ARRAYSIZE =
    RunOptions_TraceLevel_TraceLevel_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  TraceLevel_descriptor() {
    return RunOptions_TraceLevel_descriptor();
  }
  template<typename T>
  static inline const std::string& TraceLevel_Name(T enum_t_value) {
    static_assert(::std::is_same<T, TraceLevel>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function TraceLevel_Name.");
    return RunOptions_TraceLevel_Name(enum_t_value);
  }
  static inline bool TraceLevel_Parse(const std::string& name,
      TraceLevel* value) {
    return RunOptions_TraceLevel_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kDebugOptionsFieldNumber = 6,
    kExperimentalFieldNumber = 8,
    kTimeoutInMsFieldNumber = 2,
    kTraceLevelFieldNumber = 1,
    kInterOpThreadPoolFieldNumber = 3,
    kOutputPartitionGraphsFieldNumber = 5,
    kReportTensorAllocationsUponOomFieldNumber = 7,
  };
  // .tensorflow.DebugOptions debug_options = 6;
  bool has_debug_options() const;
  void clear_debug_options();
  const ::tensorflow::DebugOptions& debug_options() const;
  ::tensorflow::DebugOptions* release_debug_options();
  ::tensorflow::DebugOptions* mutable_debug_options();
  void set_allocated_debug_options(::tensorflow::DebugOptions* debug_options);
  void unsafe_arena_set_allocated_debug_options(
      ::tensorflow::DebugOptions* debug_options);
  ::tensorflow::DebugOptions* unsafe_arena_release_debug_options();

  // .tensorflow.RunOptions.Experimental experimental = 8;
  bool has_experimental() const;
  void clear_experimental();
  const ::tensorflow::RunOptions_Experimental& experimental() const;
  ::tensorflow::RunOptions_Experimental* release_experimental();
  ::tensorflow::RunOptions_Experimental* mutable_experimental();
  void set_allocated_experimental(::tensorflow::RunOptions_Experimental* experimental);
  void unsafe_arena_set_allocated_experimental(
      ::tensorflow::RunOptions_Experimental* experimental);
  ::tensorflow::RunOptions_Experimental* unsafe_arena_release_experimental();

  // int64 timeout_in_ms = 2;
  void clear_timeout_in_ms();
  ::PROTOBUF_NAMESPACE_ID::int64 timeout_in_ms() const;
  void set_timeout_in_ms(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .tensorflow.RunOptions.TraceLevel trace_level = 1;
  void clear_trace_level();
  ::tensorflow::RunOptions_TraceLevel trace_level() const;
  void set_trace_level(::tensorflow::RunOptions_TraceLevel value);

  // int32 inter_op_thread_pool = 3;
  void clear_inter_op_thread_pool();
  ::PROTOBUF_NAMESPACE_ID::int32 inter_op_thread_pool() const;
  void set_inter_op_thread_pool(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool output_partition_graphs = 5;
  void clear_output_partition_graphs();
  bool output_partition_graphs() const;
  void set_output_partition_graphs(bool value);

  // bool report_tensor_allocations_upon_oom = 7;
  void clear_report_tensor_allocations_upon_oom();
  bool report_tensor_allocations_upon_oom() const;
  void set_report_tensor_allocations_upon_oom(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::DebugOptions* debug_options_;
  ::tensorflow::RunOptions_Experimental* experimental_;
  ::PROTOBUF_NAMESPACE_ID::int64 timeout_in_ms_;
  int trace_level_;
  ::PROTOBUF_NAMESPACE_ID::int32 inter_op_thread_pool_;
  bool output_partition_graphs_;
  bool report_tensor_allocations_upon_oom_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RunMetadata_FunctionGraphs :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunMetadata.FunctionGraphs) */ {
 public:
  RunMetadata_FunctionGraphs();
  virtual ~RunMetadata_FunctionGraphs();

  RunMetadata_FunctionGraphs(const RunMetadata_FunctionGraphs& from);
  RunMetadata_FunctionGraphs(RunMetadata_FunctionGraphs&& from) noexcept
    : RunMetadata_FunctionGraphs() {
    *this = ::std::move(from);
  }

  inline RunMetadata_FunctionGraphs& operator=(const RunMetadata_FunctionGraphs& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunMetadata_FunctionGraphs& operator=(RunMetadata_FunctionGraphs&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunMetadata_FunctionGraphs& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunMetadata_FunctionGraphs* internal_default_instance() {
    return reinterpret_cast<const RunMetadata_FunctionGraphs*>(
               &_RunMetadata_FunctionGraphs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(RunMetadata_FunctionGraphs& a, RunMetadata_FunctionGraphs& b) {
    a.Swap(&b);
  }
  inline void Swap(RunMetadata_FunctionGraphs* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunMetadata_FunctionGraphs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunMetadata_FunctionGraphs* New() const final {
    return CreateMaybeMessage<RunMetadata_FunctionGraphs>(nullptr);
  }

  RunMetadata_FunctionGraphs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunMetadata_FunctionGraphs>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunMetadata_FunctionGraphs& from);
  void MergeFrom(const RunMetadata_FunctionGraphs& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunMetadata_FunctionGraphs* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunMetadata.FunctionGraphs";
  }
  protected:
  explicit RunMetadata_FunctionGraphs(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPartitionGraphsFieldNumber = 1,
    kPreOptimizationGraphFieldNumber = 2,
    kPostOptimizationGraphFieldNumber = 3,
  };
  // repeated .tensorflow.GraphDef partition_graphs = 1;
  int partition_graphs_size() const;
  void clear_partition_graphs();
  ::tensorflow::GraphDef* mutable_partition_graphs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
      mutable_partition_graphs();
  const ::tensorflow::GraphDef& partition_graphs(int index) const;
  ::tensorflow::GraphDef* add_partition_graphs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
      partition_graphs() const;

  // .tensorflow.GraphDef pre_optimization_graph = 2;
  bool has_pre_optimization_graph() const;
  void clear_pre_optimization_graph();
  const ::tensorflow::GraphDef& pre_optimization_graph() const;
  ::tensorflow::GraphDef* release_pre_optimization_graph();
  ::tensorflow::GraphDef* mutable_pre_optimization_graph();
  void set_allocated_pre_optimization_graph(::tensorflow::GraphDef* pre_optimization_graph);
  void unsafe_arena_set_allocated_pre_optimization_graph(
      ::tensorflow::GraphDef* pre_optimization_graph);
  ::tensorflow::GraphDef* unsafe_arena_release_pre_optimization_graph();

  // .tensorflow.GraphDef post_optimization_graph = 3;
  bool has_post_optimization_graph() const;
  void clear_post_optimization_graph();
  const ::tensorflow::GraphDef& post_optimization_graph() const;
  ::tensorflow::GraphDef* release_post_optimization_graph();
  ::tensorflow::GraphDef* mutable_post_optimization_graph();
  void set_allocated_post_optimization_graph(::tensorflow::GraphDef* post_optimization_graph);
  void unsafe_arena_set_allocated_post_optimization_graph(
      ::tensorflow::GraphDef* post_optimization_graph);
  ::tensorflow::GraphDef* unsafe_arena_release_post_optimization_graph();

  // @@protoc_insertion_point(class_scope:tensorflow.RunMetadata.FunctionGraphs)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef > partition_graphs_;
  ::tensorflow::GraphDef* pre_optimization_graph_;
  ::tensorflow::GraphDef* post_optimization_graph_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RunMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunMetadata) */ {
 public:
  RunMetadata();
  virtual ~RunMetadata();

  RunMetadata(const RunMetadata& from);
  RunMetadata(RunMetadata&& from) noexcept
    : RunMetadata() {
    *this = ::std::move(from);
  }

  inline RunMetadata& operator=(const RunMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunMetadata& operator=(RunMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunMetadata* internal_default_instance() {
    return reinterpret_cast<const RunMetadata*>(
               &_RunMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(RunMetadata& a, RunMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(RunMetadata* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunMetadata* New() const final {
    return CreateMaybeMessage<RunMetadata>(nullptr);
  }

  RunMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunMetadata& from);
  void MergeFrom(const RunMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunMetadata";
  }
  protected:
  explicit RunMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef RunMetadata_FunctionGraphs FunctionGraphs;

  // accessors -------------------------------------------------------

  enum : int {
    kPartitionGraphsFieldNumber = 3,
    kFunctionGraphsFieldNumber = 4,
    kStepStatsFieldNumber = 1,
    kCostGraphFieldNumber = 2,
  };
  // repeated .tensorflow.GraphDef partition_graphs = 3;
  int partition_graphs_size() const;
  void clear_partition_graphs();
  ::tensorflow::GraphDef* mutable_partition_graphs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
      mutable_partition_graphs();
  const ::tensorflow::GraphDef& partition_graphs(int index) const;
  ::tensorflow::GraphDef* add_partition_graphs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
      partition_graphs() const;

  // repeated .tensorflow.RunMetadata.FunctionGraphs function_graphs = 4;
  int function_graphs_size() const;
  void clear_function_graphs();
  ::tensorflow::RunMetadata_FunctionGraphs* mutable_function_graphs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RunMetadata_FunctionGraphs >*
      mutable_function_graphs();
  const ::tensorflow::RunMetadata_FunctionGraphs& function_graphs(int index) const;
  ::tensorflow::RunMetadata_FunctionGraphs* add_function_graphs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RunMetadata_FunctionGraphs >&
      function_graphs() const;

  // .tensorflow.StepStats step_stats = 1;
  bool has_step_stats() const;
  void clear_step_stats();
  const ::tensorflow::StepStats& step_stats() const;
  ::tensorflow::StepStats* release_step_stats();
  ::tensorflow::StepStats* mutable_step_stats();
  void set_allocated_step_stats(::tensorflow::StepStats* step_stats);
  void unsafe_arena_set_allocated_step_stats(
      ::tensorflow::StepStats* step_stats);
  ::tensorflow::StepStats* unsafe_arena_release_step_stats();

  // .tensorflow.CostGraphDef cost_graph = 2;
  bool has_cost_graph() const;
  void clear_cost_graph();
  const ::tensorflow::CostGraphDef& cost_graph() const;
  ::tensorflow::CostGraphDef* release_cost_graph();
  ::tensorflow::CostGraphDef* mutable_cost_graph();
  void set_allocated_cost_graph(::tensorflow::CostGraphDef* cost_graph);
  void unsafe_arena_set_allocated_cost_graph(
      ::tensorflow::CostGraphDef* cost_graph);
  ::tensorflow::CostGraphDef* unsafe_arena_release_cost_graph();

  // @@protoc_insertion_point(class_scope:tensorflow.RunMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef > partition_graphs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RunMetadata_FunctionGraphs > function_graphs_;
  ::tensorflow::StepStats* step_stats_;
  ::tensorflow::CostGraphDef* cost_graph_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class TensorConnection :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorConnection) */ {
 public:
  TensorConnection();
  virtual ~TensorConnection();

  TensorConnection(const TensorConnection& from);
  TensorConnection(TensorConnection&& from) noexcept
    : TensorConnection() {
    *this = ::std::move(from);
  }

  inline TensorConnection& operator=(const TensorConnection& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorConnection& operator=(TensorConnection&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TensorConnection& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorConnection* internal_default_instance() {
    return reinterpret_cast<const TensorConnection*>(
               &_TensorConnection_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(TensorConnection& a, TensorConnection& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorConnection* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorConnection* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TensorConnection* New() const final {
    return CreateMaybeMessage<TensorConnection>(nullptr);
  }

  TensorConnection* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TensorConnection>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TensorConnection& from);
  void MergeFrom(const TensorConnection& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorConnection* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TensorConnection";
  }
  protected:
  explicit TensorConnection(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFromTensorFieldNumber = 1,
    kToTensorFieldNumber = 2,
  };
  // string from_tensor = 1;
  void clear_from_tensor();
  const std::string& from_tensor() const;
  void set_from_tensor(const std::string& value);
  void set_from_tensor(std::string&& value);
  void set_from_tensor(const char* value);
  void set_from_tensor(const char* value, size_t size);
  std::string* mutable_from_tensor();
  std::string* release_from_tensor();
  void set_allocated_from_tensor(std::string* from_tensor);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_from_tensor();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_from_tensor(
      std::string* from_tensor);

  // string to_tensor = 2;
  void clear_to_tensor();
  const std::string& to_tensor() const;
  void set_to_tensor(const std::string& value);
  void set_to_tensor(std::string&& value);
  void set_to_tensor(const char* value);
  void set_to_tensor(const char* value, size_t size);
  std::string* mutable_to_tensor();
  std::string* release_to_tensor();
  void set_allocated_to_tensor(std::string* to_tensor);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_to_tensor();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_to_tensor(
      std::string* to_tensor);

  // @@protoc_insertion_point(class_scope:tensorflow.TensorConnection)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr from_tensor_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr to_tensor_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class CallableOptions_FeedDevicesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallableOptions_FeedDevicesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallableOptions_FeedDevicesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  CallableOptions_FeedDevicesEntry_DoNotUse();
  CallableOptions_FeedDevicesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const CallableOptions_FeedDevicesEntry_DoNotUse& other);
  static const CallableOptions_FeedDevicesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CallableOptions_FeedDevicesEntry_DoNotUse*>(&_CallableOptions_FeedDevicesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CallableOptions.FeedDevicesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CallableOptions.FeedDevicesEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[17];
  }

  public:
};

// -------------------------------------------------------------------

class CallableOptions_FetchDevicesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallableOptions_FetchDevicesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallableOptions_FetchDevicesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  CallableOptions_FetchDevicesEntry_DoNotUse();
  CallableOptions_FetchDevicesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const CallableOptions_FetchDevicesEntry_DoNotUse& other);
  static const CallableOptions_FetchDevicesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CallableOptions_FetchDevicesEntry_DoNotUse*>(&_CallableOptions_FetchDevicesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CallableOptions.FetchDevicesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CallableOptions.FetchDevicesEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[18];
  }

  public:
};

// -------------------------------------------------------------------

class CallableOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CallableOptions) */ {
 public:
  CallableOptions();
  virtual ~CallableOptions();

  CallableOptions(const CallableOptions& from);
  CallableOptions(CallableOptions&& from) noexcept
    : CallableOptions() {
    *this = ::std::move(from);
  }

  inline CallableOptions& operator=(const CallableOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline CallableOptions& operator=(CallableOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CallableOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CallableOptions* internal_default_instance() {
    return reinterpret_cast<const CallableOptions*>(
               &_CallableOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(CallableOptions& a, CallableOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(CallableOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CallableOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CallableOptions* New() const final {
    return CreateMaybeMessage<CallableOptions>(nullptr);
  }

  CallableOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CallableOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CallableOptions& from);
  void MergeFrom(const CallableOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CallableOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CallableOptions";
  }
  protected:
  explicit CallableOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFeedFieldNumber = 1,
    kFetchFieldNumber = 2,
    kTargetFieldNumber = 3,
    kTensorConnectionFieldNumber = 5,
    kFeedDevicesFieldNumber = 6,
    kFetchDevicesFieldNumber = 7,
    kRunOptionsFieldNumber = 4,
    kFetchSkipSyncFieldNumber = 8,
  };
  // repeated string feed = 1;
  int feed_size() const;
  void clear_feed();
  const std::string& feed(int index) const;
  std::string* mutable_feed(int index);
  void set_feed(int index, const std::string& value);
  void set_feed(int index, std::string&& value);
  void set_feed(int index, const char* value);
  void set_feed(int index, const char* value, size_t size);
  std::string* add_feed();
  void add_feed(const std::string& value);
  void add_feed(std::string&& value);
  void add_feed(const char* value);
  void add_feed(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& feed() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_feed();

  // repeated string fetch = 2;
  int fetch_size() const;
  void clear_fetch();
  const std::string& fetch(int index) const;
  std::string* mutable_fetch(int index);
  void set_fetch(int index, const std::string& value);
  void set_fetch(int index, std::string&& value);
  void set_fetch(int index, const char* value);
  void set_fetch(int index, const char* value, size_t size);
  std::string* add_fetch();
  void add_fetch(const std::string& value);
  void add_fetch(std::string&& value);
  void add_fetch(const char* value);
  void add_fetch(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& fetch() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_fetch();

  // repeated string target = 3;
  int target_size() const;
  void clear_target();
  const std::string& target(int index) const;
  std::string* mutable_target(int index);
  void set_target(int index, const std::string& value);
  void set_target(int index, std::string&& value);
  void set_target(int index, const char* value);
  void set_target(int index, const char* value, size_t size);
  std::string* add_target();
  void add_target(const std::string& value);
  void add_target(std::string&& value);
  void add_target(const char* value);
  void add_target(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& target() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_target();

  // repeated .tensorflow.TensorConnection tensor_connection = 5;
  int tensor_connection_size() const;
  void clear_tensor_connection();
  ::tensorflow::TensorConnection* mutable_tensor_connection(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorConnection >*
      mutable_tensor_connection();
  const ::tensorflow::TensorConnection& tensor_connection(int index) const;
  ::tensorflow::TensorConnection* add_tensor_connection();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorConnection >&
      tensor_connection() const;

  // map<string, string> feed_devices = 6;
  int feed_devices_size() const;
  void clear_feed_devices();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      feed_devices() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_feed_devices();

  // map<string, string> fetch_devices = 7;
  int fetch_devices_size() const;
  void clear_fetch_devices();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      fetch_devices() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_fetch_devices();

  // .tensorflow.RunOptions run_options = 4;
  bool has_run_options() const;
  void clear_run_options();
  const ::tensorflow::RunOptions& run_options() const;
  ::tensorflow::RunOptions* release_run_options();
  ::tensorflow::RunOptions* mutable_run_options();
  void set_allocated_run_options(::tensorflow::RunOptions* run_options);
  void unsafe_arena_set_allocated_run_options(
      ::tensorflow::RunOptions* run_options);
  ::tensorflow::RunOptions* unsafe_arena_release_run_options();

  // bool fetch_skip_sync = 8;
  void clear_fetch_skip_sync();
  bool fetch_skip_sync() const;
  void set_fetch_skip_sync(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.CallableOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> feed_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> fetch_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> target_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorConnection > tensor_connection_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      CallableOptions_FeedDevicesEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > feed_devices_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      CallableOptions_FetchDevicesEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > fetch_devices_;
  ::tensorflow::RunOptions* run_options_;
  bool fetch_skip_sync_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GPUOptions_Experimental_VirtualDevices

// repeated float memory_limit_mb = 1;
inline int GPUOptions_Experimental_VirtualDevices::memory_limit_mb_size() const {
  return memory_limit_mb_.size();
}
inline void GPUOptions_Experimental_VirtualDevices::clear_memory_limit_mb() {
  memory_limit_mb_.Clear();
}
inline float GPUOptions_Experimental_VirtualDevices::memory_limit_mb(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb)
  return memory_limit_mb_.Get(index);
}
inline void GPUOptions_Experimental_VirtualDevices::set_memory_limit_mb(int index, float value) {
  memory_limit_mb_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb)
}
inline void GPUOptions_Experimental_VirtualDevices::add_memory_limit_mb(float value) {
  memory_limit_mb_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
GPUOptions_Experimental_VirtualDevices::memory_limit_mb() const {
  // @@protoc_insertion_point(field_list:tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb)
  return memory_limit_mb_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
GPUOptions_Experimental_VirtualDevices::mutable_memory_limit_mb() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb)
  return &memory_limit_mb_;
}

// repeated int32 priority = 2;
inline int GPUOptions_Experimental_VirtualDevices::priority_size() const {
  return priority_.size();
}
inline void GPUOptions_Experimental_VirtualDevices::clear_priority() {
  priority_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GPUOptions_Experimental_VirtualDevices::priority(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.VirtualDevices.priority)
  return priority_.Get(index);
}
inline void GPUOptions_Experimental_VirtualDevices::set_priority(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  priority_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.VirtualDevices.priority)
}
inline void GPUOptions_Experimental_VirtualDevices::add_priority(::PROTOBUF_NAMESPACE_ID::int32 value) {
  priority_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.GPUOptions.Experimental.VirtualDevices.priority)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
GPUOptions_Experimental_VirtualDevices::priority() const {
  // @@protoc_insertion_point(field_list:tensorflow.GPUOptions.Experimental.VirtualDevices.priority)
  return priority_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
GPUOptions_Experimental_VirtualDevices::mutable_priority() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GPUOptions.Experimental.VirtualDevices.priority)
  return &priority_;
}

// -------------------------------------------------------------------

// GPUOptions_Experimental

// repeated .tensorflow.GPUOptions.Experimental.VirtualDevices virtual_devices = 1;
inline int GPUOptions_Experimental::virtual_devices_size() const {
  return virtual_devices_.size();
}
inline void GPUOptions_Experimental::clear_virtual_devices() {
  virtual_devices_.Clear();
}
inline ::tensorflow::GPUOptions_Experimental_VirtualDevices* GPUOptions_Experimental::mutable_virtual_devices(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.Experimental.virtual_devices)
  return virtual_devices_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GPUOptions_Experimental_VirtualDevices >*
GPUOptions_Experimental::mutable_virtual_devices() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GPUOptions.Experimental.virtual_devices)
  return &virtual_devices_;
}
inline const ::tensorflow::GPUOptions_Experimental_VirtualDevices& GPUOptions_Experimental::virtual_devices(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.virtual_devices)
  return virtual_devices_.Get(index);
}
inline ::tensorflow::GPUOptions_Experimental_VirtualDevices* GPUOptions_Experimental::add_virtual_devices() {
  // @@protoc_insertion_point(field_add:tensorflow.GPUOptions.Experimental.virtual_devices)
  return virtual_devices_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GPUOptions_Experimental_VirtualDevices >&
GPUOptions_Experimental::virtual_devices() const {
  // @@protoc_insertion_point(field_list:tensorflow.GPUOptions.Experimental.virtual_devices)
  return virtual_devices_;
}

// bool use_unified_memory = 2;
inline void GPUOptions_Experimental::clear_use_unified_memory() {
  use_unified_memory_ = false;
}
inline bool GPUOptions_Experimental::use_unified_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.use_unified_memory)
  return use_unified_memory_;
}
inline void GPUOptions_Experimental::set_use_unified_memory(bool value) {
  
  use_unified_memory_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.use_unified_memory)
}

// int32 num_dev_to_dev_copy_streams = 3;
inline void GPUOptions_Experimental::clear_num_dev_to_dev_copy_streams() {
  num_dev_to_dev_copy_streams_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GPUOptions_Experimental::num_dev_to_dev_copy_streams() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.num_dev_to_dev_copy_streams)
  return num_dev_to_dev_copy_streams_;
}
inline void GPUOptions_Experimental::set_num_dev_to_dev_copy_streams(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_dev_to_dev_copy_streams_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.num_dev_to_dev_copy_streams)
}

// string collective_ring_order = 4;
inline void GPUOptions_Experimental::clear_collective_ring_order() {
  collective_ring_order_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GPUOptions_Experimental::collective_ring_order() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.collective_ring_order)
  return collective_ring_order_.Get();
}
inline void GPUOptions_Experimental::set_collective_ring_order(const std::string& value) {
  
  collective_ring_order_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.collective_ring_order)
}
inline void GPUOptions_Experimental::set_collective_ring_order(std::string&& value) {
  
  collective_ring_order_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GPUOptions.Experimental.collective_ring_order)
}
inline void GPUOptions_Experimental::set_collective_ring_order(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  collective_ring_order_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GPUOptions.Experimental.collective_ring_order)
}
inline void GPUOptions_Experimental::set_collective_ring_order(const char* value,
    size_t size) {
  
  collective_ring_order_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GPUOptions.Experimental.collective_ring_order)
}
inline std::string* GPUOptions_Experimental::mutable_collective_ring_order() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.Experimental.collective_ring_order)
  return collective_ring_order_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GPUOptions_Experimental::release_collective_ring_order() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUOptions.Experimental.collective_ring_order)
  
  return collective_ring_order_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GPUOptions_Experimental::set_allocated_collective_ring_order(std::string* collective_ring_order) {
  if (collective_ring_order != nullptr) {
    
  } else {
    
  }
  collective_ring_order_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), collective_ring_order,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUOptions.Experimental.collective_ring_order)
}
inline std::string* GPUOptions_Experimental::unsafe_arena_release_collective_ring_order() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GPUOptions.Experimental.collective_ring_order)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return collective_ring_order_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GPUOptions_Experimental::unsafe_arena_set_allocated_collective_ring_order(
    std::string* collective_ring_order) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (collective_ring_order != nullptr) {
    
  } else {
    
  }
  collective_ring_order_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      collective_ring_order, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUOptions.Experimental.collective_ring_order)
}

// bool timestamped_allocator = 5;
inline void GPUOptions_Experimental::clear_timestamped_allocator() {
  timestamped_allocator_ = false;
}
inline bool GPUOptions_Experimental::timestamped_allocator() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.timestamped_allocator)
  return timestamped_allocator_;
}
inline void GPUOptions_Experimental::set_timestamped_allocator(bool value) {
  
  timestamped_allocator_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.timestamped_allocator)
}

// int32 kernel_tracker_max_interval = 7;
inline void GPUOptions_Experimental::clear_kernel_tracker_max_interval() {
  kernel_tracker_max_interval_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GPUOptions_Experimental::kernel_tracker_max_interval() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.kernel_tracker_max_interval)
  return kernel_tracker_max_interval_;
}
inline void GPUOptions_Experimental::set_kernel_tracker_max_interval(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  kernel_tracker_max_interval_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.kernel_tracker_max_interval)
}

// int32 kernel_tracker_max_bytes = 8;
inline void GPUOptions_Experimental::clear_kernel_tracker_max_bytes() {
  kernel_tracker_max_bytes_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GPUOptions_Experimental::kernel_tracker_max_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.kernel_tracker_max_bytes)
  return kernel_tracker_max_bytes_;
}
inline void GPUOptions_Experimental::set_kernel_tracker_max_bytes(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  kernel_tracker_max_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.kernel_tracker_max_bytes)
}

// int32 kernel_tracker_max_pending = 9;
inline void GPUOptions_Experimental::clear_kernel_tracker_max_pending() {
  kernel_tracker_max_pending_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GPUOptions_Experimental::kernel_tracker_max_pending() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.kernel_tracker_max_pending)
  return kernel_tracker_max_pending_;
}
inline void GPUOptions_Experimental::set_kernel_tracker_max_pending(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  kernel_tracker_max_pending_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.kernel_tracker_max_pending)
}

// double internal_fragmentation_fraction = 10;
inline void GPUOptions_Experimental::clear_internal_fragmentation_fraction() {
  internal_fragmentation_fraction_ = 0;
}
inline double GPUOptions_Experimental::internal_fragmentation_fraction() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.internal_fragmentation_fraction)
  return internal_fragmentation_fraction_;
}
inline void GPUOptions_Experimental::set_internal_fragmentation_fraction(double value) {
  
  internal_fragmentation_fraction_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.internal_fragmentation_fraction)
}

// bool use_cuda_malloc_async = 11;
inline void GPUOptions_Experimental::clear_use_cuda_malloc_async() {
  use_cuda_malloc_async_ = false;
}
inline bool GPUOptions_Experimental::use_cuda_malloc_async() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.use_cuda_malloc_async)
  return use_cuda_malloc_async_;
}
inline void GPUOptions_Experimental::set_use_cuda_malloc_async(bool value) {
  
  use_cuda_malloc_async_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.use_cuda_malloc_async)
}

// -------------------------------------------------------------------

// GPUOptions

// double per_process_gpu_memory_fraction = 1;
inline void GPUOptions::clear_per_process_gpu_memory_fraction() {
  per_process_gpu_memory_fraction_ = 0;
}
inline double GPUOptions::per_process_gpu_memory_fraction() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.per_process_gpu_memory_fraction)
  return per_process_gpu_memory_fraction_;
}
inline void GPUOptions::set_per_process_gpu_memory_fraction(double value) {
  
  per_process_gpu_memory_fraction_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.per_process_gpu_memory_fraction)
}

// bool allow_growth = 4;
inline void GPUOptions::clear_allow_growth() {
  allow_growth_ = false;
}
inline bool GPUOptions::allow_growth() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.allow_growth)
  return allow_growth_;
}
inline void GPUOptions::set_allow_growth(bool value) {
  
  allow_growth_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.allow_growth)
}

// string allocator_type = 2;
inline void GPUOptions::clear_allocator_type() {
  allocator_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GPUOptions::allocator_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.allocator_type)
  return allocator_type_.Get();
}
inline void GPUOptions::set_allocator_type(const std::string& value) {
  
  allocator_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.allocator_type)
}
inline void GPUOptions::set_allocator_type(std::string&& value) {
  
  allocator_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GPUOptions.allocator_type)
}
inline void GPUOptions::set_allocator_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  allocator_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GPUOptions.allocator_type)
}
inline void GPUOptions::set_allocator_type(const char* value,
    size_t size) {
  
  allocator_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GPUOptions.allocator_type)
}
inline std::string* GPUOptions::mutable_allocator_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.allocator_type)
  return allocator_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GPUOptions::release_allocator_type() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUOptions.allocator_type)
  
  return allocator_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GPUOptions::set_allocated_allocator_type(std::string* allocator_type) {
  if (allocator_type != nullptr) {
    
  } else {
    
  }
  allocator_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), allocator_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUOptions.allocator_type)
}
inline std::string* GPUOptions::unsafe_arena_release_allocator_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GPUOptions.allocator_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return allocator_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GPUOptions::unsafe_arena_set_allocated_allocator_type(
    std::string* allocator_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (allocator_type != nullptr) {
    
  } else {
    
  }
  allocator_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      allocator_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUOptions.allocator_type)
}

// int64 deferred_deletion_bytes = 3;
inline void GPUOptions::clear_deferred_deletion_bytes() {
  deferred_deletion_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GPUOptions::deferred_deletion_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.deferred_deletion_bytes)
  return deferred_deletion_bytes_;
}
inline void GPUOptions::set_deferred_deletion_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  deferred_deletion_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.deferred_deletion_bytes)
}

// string visible_device_list = 5;
inline void GPUOptions::clear_visible_device_list() {
  visible_device_list_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GPUOptions::visible_device_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.visible_device_list)
  return visible_device_list_.Get();
}
inline void GPUOptions::set_visible_device_list(const std::string& value) {
  
  visible_device_list_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.visible_device_list)
}
inline void GPUOptions::set_visible_device_list(std::string&& value) {
  
  visible_device_list_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GPUOptions.visible_device_list)
}
inline void GPUOptions::set_visible_device_list(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  visible_device_list_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GPUOptions.visible_device_list)
}
inline void GPUOptions::set_visible_device_list(const char* value,
    size_t size) {
  
  visible_device_list_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GPUOptions.visible_device_list)
}
inline std::string* GPUOptions::mutable_visible_device_list() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.visible_device_list)
  return visible_device_list_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GPUOptions::release_visible_device_list() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUOptions.visible_device_list)
  
  return visible_device_list_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GPUOptions::set_allocated_visible_device_list(std::string* visible_device_list) {
  if (visible_device_list != nullptr) {
    
  } else {
    
  }
  visible_device_list_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), visible_device_list,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUOptions.visible_device_list)
}
inline std::string* GPUOptions::unsafe_arena_release_visible_device_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GPUOptions.visible_device_list)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return visible_device_list_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GPUOptions::unsafe_arena_set_allocated_visible_device_list(
    std::string* visible_device_list) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (visible_device_list != nullptr) {
    
  } else {
    
  }
  visible_device_list_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      visible_device_list, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUOptions.visible_device_list)
}

// int32 polling_active_delay_usecs = 6;
inline void GPUOptions::clear_polling_active_delay_usecs() {
  polling_active_delay_usecs_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GPUOptions::polling_active_delay_usecs() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.polling_active_delay_usecs)
  return polling_active_delay_usecs_;
}
inline void GPUOptions::set_polling_active_delay_usecs(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  polling_active_delay_usecs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.polling_active_delay_usecs)
}

// int32 polling_inactive_delay_msecs = 7;
inline void GPUOptions::clear_polling_inactive_delay_msecs() {
  polling_inactive_delay_msecs_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GPUOptions::polling_inactive_delay_msecs() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.polling_inactive_delay_msecs)
  return polling_inactive_delay_msecs_;
}
inline void GPUOptions::set_polling_inactive_delay_msecs(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  polling_inactive_delay_msecs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.polling_inactive_delay_msecs)
}

// bool force_gpu_compatible = 8;
inline void GPUOptions::clear_force_gpu_compatible() {
  force_gpu_compatible_ = false;
}
inline bool GPUOptions::force_gpu_compatible() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.force_gpu_compatible)
  return force_gpu_compatible_;
}
inline void GPUOptions::set_force_gpu_compatible(bool value) {
  
  force_gpu_compatible_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.force_gpu_compatible)
}

// .tensorflow.GPUOptions.Experimental experimental = 9;
inline bool GPUOptions::has_experimental() const {
  return this != internal_default_instance() && experimental_ != nullptr;
}
inline void GPUOptions::clear_experimental() {
  if (GetArenaNoVirtual() == nullptr && experimental_ != nullptr) {
    delete experimental_;
  }
  experimental_ = nullptr;
}
inline const ::tensorflow::GPUOptions_Experimental& GPUOptions::experimental() const {
  const ::tensorflow::GPUOptions_Experimental* p = experimental_;
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.experimental)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::GPUOptions_Experimental*>(
      &::tensorflow::_GPUOptions_Experimental_default_instance_);
}
inline ::tensorflow::GPUOptions_Experimental* GPUOptions::release_experimental() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUOptions.experimental)
  
  ::tensorflow::GPUOptions_Experimental* temp = experimental_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  experimental_ = nullptr;
  return temp;
}
inline ::tensorflow::GPUOptions_Experimental* GPUOptions::unsafe_arena_release_experimental() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GPUOptions.experimental)
  
  ::tensorflow::GPUOptions_Experimental* temp = experimental_;
  experimental_ = nullptr;
  return temp;
}
inline ::tensorflow::GPUOptions_Experimental* GPUOptions::mutable_experimental() {
  
  if (experimental_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GPUOptions_Experimental>(GetArenaNoVirtual());
    experimental_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.experimental)
  return experimental_;
}
inline void GPUOptions::set_allocated_experimental(::tensorflow::GPUOptions_Experimental* experimental) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete experimental_;
  }
  if (experimental) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(experimental);
    if (message_arena != submessage_arena) {
      experimental = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, experimental, submessage_arena);
    }
    
  } else {
    
  }
  experimental_ = experimental;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUOptions.experimental)
}

// -------------------------------------------------------------------

// OptimizerOptions

// bool do_common_subexpression_elimination = 1;
inline void OptimizerOptions::clear_do_common_subexpression_elimination() {
  do_common_subexpression_elimination_ = false;
}
inline bool OptimizerOptions::do_common_subexpression_elimination() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.do_common_subexpression_elimination)
  return do_common_subexpression_elimination_;
}
inline void OptimizerOptions::set_do_common_subexpression_elimination(bool value) {
  
  do_common_subexpression_elimination_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.do_common_subexpression_elimination)
}

// bool do_constant_folding = 2;
inline void OptimizerOptions::clear_do_constant_folding() {
  do_constant_folding_ = false;
}
inline bool OptimizerOptions::do_constant_folding() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.do_constant_folding)
  return do_constant_folding_;
}
inline void OptimizerOptions::set_do_constant_folding(bool value) {
  
  do_constant_folding_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.do_constant_folding)
}

// int64 max_folded_constant_in_bytes = 6;
inline void OptimizerOptions::clear_max_folded_constant_in_bytes() {
  max_folded_constant_in_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptimizerOptions::max_folded_constant_in_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.max_folded_constant_in_bytes)
  return max_folded_constant_in_bytes_;
}
inline void OptimizerOptions::set_max_folded_constant_in_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  max_folded_constant_in_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.max_folded_constant_in_bytes)
}

// bool do_function_inlining = 4;
inline void OptimizerOptions::clear_do_function_inlining() {
  do_function_inlining_ = false;
}
inline bool OptimizerOptions::do_function_inlining() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.do_function_inlining)
  return do_function_inlining_;
}
inline void OptimizerOptions::set_do_function_inlining(bool value) {
  
  do_function_inlining_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.do_function_inlining)
}

// .tensorflow.OptimizerOptions.Level opt_level = 3;
inline void OptimizerOptions::clear_opt_level() {
  opt_level_ = 0;
}
inline ::tensorflow::OptimizerOptions_Level OptimizerOptions::opt_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.opt_level)
  return static_cast< ::tensorflow::OptimizerOptions_Level >(opt_level_);
}
inline void OptimizerOptions::set_opt_level(::tensorflow::OptimizerOptions_Level value) {
  
  opt_level_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.opt_level)
}

// .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 5;
inline void OptimizerOptions::clear_global_jit_level() {
  global_jit_level_ = 0;
}
inline ::tensorflow::OptimizerOptions_GlobalJitLevel OptimizerOptions::global_jit_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.global_jit_level)
  return static_cast< ::tensorflow::OptimizerOptions_GlobalJitLevel >(global_jit_level_);
}
inline void OptimizerOptions::set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value) {
  
  global_jit_level_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.global_jit_level)
}

// -------------------------------------------------------------------

// GraphOptions

// bool enable_recv_scheduling = 2;
inline void GraphOptions::clear_enable_recv_scheduling() {
  enable_recv_scheduling_ = false;
}
inline bool GraphOptions::enable_recv_scheduling() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.enable_recv_scheduling)
  return enable_recv_scheduling_;
}
inline void GraphOptions::set_enable_recv_scheduling(bool value) {
  
  enable_recv_scheduling_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.enable_recv_scheduling)
}

// .tensorflow.OptimizerOptions optimizer_options = 3;
inline bool GraphOptions::has_optimizer_options() const {
  return this != internal_default_instance() && optimizer_options_ != nullptr;
}
inline void GraphOptions::clear_optimizer_options() {
  if (GetArenaNoVirtual() == nullptr && optimizer_options_ != nullptr) {
    delete optimizer_options_;
  }
  optimizer_options_ = nullptr;
}
inline const ::tensorflow::OptimizerOptions& GraphOptions::optimizer_options() const {
  const ::tensorflow::OptimizerOptions* p = optimizer_options_;
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.optimizer_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::OptimizerOptions*>(
      &::tensorflow::_OptimizerOptions_default_instance_);
}
inline ::tensorflow::OptimizerOptions* GraphOptions::release_optimizer_options() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOptions.optimizer_options)
  
  ::tensorflow::OptimizerOptions* temp = optimizer_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  optimizer_options_ = nullptr;
  return temp;
}
inline ::tensorflow::OptimizerOptions* GraphOptions::unsafe_arena_release_optimizer_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphOptions.optimizer_options)
  
  ::tensorflow::OptimizerOptions* temp = optimizer_options_;
  optimizer_options_ = nullptr;
  return temp;
}
inline ::tensorflow::OptimizerOptions* GraphOptions::mutable_optimizer_options() {
  
  if (optimizer_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OptimizerOptions>(GetArenaNoVirtual());
    optimizer_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOptions.optimizer_options)
  return optimizer_options_;
}
inline void GraphOptions::set_allocated_optimizer_options(::tensorflow::OptimizerOptions* optimizer_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete optimizer_options_;
  }
  if (optimizer_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(optimizer_options);
    if (message_arena != submessage_arena) {
      optimizer_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optimizer_options, submessage_arena);
    }
    
  } else {
    
  }
  optimizer_options_ = optimizer_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOptions.optimizer_options)
}

// int64 build_cost_model = 4;
inline void GraphOptions::clear_build_cost_model() {
  build_cost_model_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphOptions::build_cost_model() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.build_cost_model)
  return build_cost_model_;
}
inline void GraphOptions::set_build_cost_model(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  build_cost_model_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.build_cost_model)
}

// int64 build_cost_model_after = 9;
inline void GraphOptions::clear_build_cost_model_after() {
  build_cost_model_after_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphOptions::build_cost_model_after() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.build_cost_model_after)
  return build_cost_model_after_;
}
inline void GraphOptions::set_build_cost_model_after(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  build_cost_model_after_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.build_cost_model_after)
}

// bool infer_shapes = 5;
inline void GraphOptions::clear_infer_shapes() {
  infer_shapes_ = false;
}
inline bool GraphOptions::infer_shapes() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.infer_shapes)
  return infer_shapes_;
}
inline void GraphOptions::set_infer_shapes(bool value) {
  
  infer_shapes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.infer_shapes)
}

// bool place_pruned_graph = 6;
inline void GraphOptions::clear_place_pruned_graph() {
  place_pruned_graph_ = false;
}
inline bool GraphOptions::place_pruned_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.place_pruned_graph)
  return place_pruned_graph_;
}
inline void GraphOptions::set_place_pruned_graph(bool value) {
  
  place_pruned_graph_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.place_pruned_graph)
}

// bool enable_bfloat16_sendrecv = 7;
inline void GraphOptions::clear_enable_bfloat16_sendrecv() {
  enable_bfloat16_sendrecv_ = false;
}
inline bool GraphOptions::enable_bfloat16_sendrecv() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.enable_bfloat16_sendrecv)
  return enable_bfloat16_sendrecv_;
}
inline void GraphOptions::set_enable_bfloat16_sendrecv(bool value) {
  
  enable_bfloat16_sendrecv_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.enable_bfloat16_sendrecv)
}

// int32 timeline_step = 8;
inline void GraphOptions::clear_timeline_step() {
  timeline_step_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphOptions::timeline_step() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.timeline_step)
  return timeline_step_;
}
inline void GraphOptions::set_timeline_step(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  timeline_step_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.timeline_step)
}

// .tensorflow.RewriterConfig rewrite_options = 10;
inline bool GraphOptions::has_rewrite_options() const {
  return this != internal_default_instance() && rewrite_options_ != nullptr;
}
inline const ::tensorflow::RewriterConfig& GraphOptions::rewrite_options() const {
  const ::tensorflow::RewriterConfig* p = rewrite_options_;
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.rewrite_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::RewriterConfig*>(
      &::tensorflow::_RewriterConfig_default_instance_);
}
inline ::tensorflow::RewriterConfig* GraphOptions::release_rewrite_options() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOptions.rewrite_options)
  
  ::tensorflow::RewriterConfig* temp = rewrite_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  rewrite_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RewriterConfig* GraphOptions::unsafe_arena_release_rewrite_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphOptions.rewrite_options)
  
  ::tensorflow::RewriterConfig* temp = rewrite_options_;
  rewrite_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RewriterConfig* GraphOptions::mutable_rewrite_options() {
  
  if (rewrite_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RewriterConfig>(GetArenaNoVirtual());
    rewrite_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOptions.rewrite_options)
  return rewrite_options_;
}
inline void GraphOptions::set_allocated_rewrite_options(::tensorflow::RewriterConfig* rewrite_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(rewrite_options_);
  }
  if (rewrite_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rewrite_options)->GetArena();
    if (message_arena != submessage_arena) {
      rewrite_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rewrite_options, submessage_arena);
    }
    
  } else {
    
  }
  rewrite_options_ = rewrite_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOptions.rewrite_options)
}

// -------------------------------------------------------------------

// ThreadPoolOptionProto

// int32 num_threads = 1;
inline void ThreadPoolOptionProto::clear_num_threads() {
  num_threads_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ThreadPoolOptionProto::num_threads() const {
  // @@protoc_insertion_point(field_get:tensorflow.ThreadPoolOptionProto.num_threads)
  return num_threads_;
}
inline void ThreadPoolOptionProto::set_num_threads(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_threads_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ThreadPoolOptionProto.num_threads)
}

// string global_name = 2;
inline void ThreadPoolOptionProto::clear_global_name() {
  global_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ThreadPoolOptionProto::global_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ThreadPoolOptionProto.global_name)
  return global_name_.Get();
}
inline void ThreadPoolOptionProto::set_global_name(const std::string& value) {
  
  global_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ThreadPoolOptionProto.global_name)
}
inline void ThreadPoolOptionProto::set_global_name(std::string&& value) {
  
  global_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ThreadPoolOptionProto.global_name)
}
inline void ThreadPoolOptionProto::set_global_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  global_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ThreadPoolOptionProto.global_name)
}
inline void ThreadPoolOptionProto::set_global_name(const char* value,
    size_t size) {
  
  global_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ThreadPoolOptionProto.global_name)
}
inline std::string* ThreadPoolOptionProto::mutable_global_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ThreadPoolOptionProto.global_name)
  return global_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ThreadPoolOptionProto::release_global_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ThreadPoolOptionProto.global_name)
  
  return global_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ThreadPoolOptionProto::set_allocated_global_name(std::string* global_name) {
  if (global_name != nullptr) {
    
  } else {
    
  }
  global_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), global_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ThreadPoolOptionProto.global_name)
}
inline std::string* ThreadPoolOptionProto::unsafe_arena_release_global_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ThreadPoolOptionProto.global_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return global_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ThreadPoolOptionProto::unsafe_arena_set_allocated_global_name(
    std::string* global_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (global_name != nullptr) {
    
  } else {
    
  }
  global_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      global_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ThreadPoolOptionProto.global_name)
}

// -------------------------------------------------------------------

// RPCOptions

// bool use_rpc_for_inprocess_master = 1;
inline void RPCOptions::clear_use_rpc_for_inprocess_master() {
  use_rpc_for_inprocess_master_ = false;
}
inline bool RPCOptions::use_rpc_for_inprocess_master() const {
  // @@protoc_insertion_point(field_get:tensorflow.RPCOptions.use_rpc_for_inprocess_master)
  return use_rpc_for_inprocess_master_;
}
inline void RPCOptions::set_use_rpc_for_inprocess_master(bool value) {
  
  use_rpc_for_inprocess_master_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RPCOptions.use_rpc_for_inprocess_master)
}

// string compression_algorithm = 2;
inline void RPCOptions::clear_compression_algorithm() {
  compression_algorithm_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RPCOptions::compression_algorithm() const {
  // @@protoc_insertion_point(field_get:tensorflow.RPCOptions.compression_algorithm)
  return compression_algorithm_.Get();
}
inline void RPCOptions::set_compression_algorithm(const std::string& value) {
  
  compression_algorithm_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RPCOptions.compression_algorithm)
}
inline void RPCOptions::set_compression_algorithm(std::string&& value) {
  
  compression_algorithm_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RPCOptions.compression_algorithm)
}
inline void RPCOptions::set_compression_algorithm(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  compression_algorithm_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RPCOptions.compression_algorithm)
}
inline void RPCOptions::set_compression_algorithm(const char* value,
    size_t size) {
  
  compression_algorithm_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RPCOptions.compression_algorithm)
}
inline std::string* RPCOptions::mutable_compression_algorithm() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RPCOptions.compression_algorithm)
  return compression_algorithm_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RPCOptions::release_compression_algorithm() {
  // @@protoc_insertion_point(field_release:tensorflow.RPCOptions.compression_algorithm)
  
  return compression_algorithm_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RPCOptions::set_allocated_compression_algorithm(std::string* compression_algorithm) {
  if (compression_algorithm != nullptr) {
    
  } else {
    
  }
  compression_algorithm_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), compression_algorithm,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RPCOptions.compression_algorithm)
}
inline std::string* RPCOptions::unsafe_arena_release_compression_algorithm() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RPCOptions.compression_algorithm)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return compression_algorithm_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RPCOptions::unsafe_arena_set_allocated_compression_algorithm(
    std::string* compression_algorithm) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (compression_algorithm != nullptr) {
    
  } else {
    
  }
  compression_algorithm_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      compression_algorithm, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RPCOptions.compression_algorithm)
}

// int32 compression_level = 3;
inline void RPCOptions::clear_compression_level() {
  compression_level_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RPCOptions::compression_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.RPCOptions.compression_level)
  return compression_level_;
}
inline void RPCOptions::set_compression_level(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  compression_level_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RPCOptions.compression_level)
}

// bool cache_rpc_response = 4;
inline void RPCOptions::clear_cache_rpc_response() {
  cache_rpc_response_ = false;
}
inline bool RPCOptions::cache_rpc_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.RPCOptions.cache_rpc_response)
  return cache_rpc_response_;
}
inline void RPCOptions::set_cache_rpc_response(bool value) {
  
  cache_rpc_response_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RPCOptions.cache_rpc_response)
}

// bool disable_session_connection_sharing = 5;
inline void RPCOptions::clear_disable_session_connection_sharing() {
  disable_session_connection_sharing_ = false;
}
inline bool RPCOptions::disable_session_connection_sharing() const {
  // @@protoc_insertion_point(field_get:tensorflow.RPCOptions.disable_session_connection_sharing)
  return disable_session_connection_sharing_;
}
inline void RPCOptions::set_disable_session_connection_sharing(bool value) {
  
  disable_session_connection_sharing_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RPCOptions.disable_session_connection_sharing)
}

// int32 num_channels_per_target = 6;
inline void RPCOptions::clear_num_channels_per_target() {
  num_channels_per_target_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RPCOptions::num_channels_per_target() const {
  // @@protoc_insertion_point(field_get:tensorflow.RPCOptions.num_channels_per_target)
  return num_channels_per_target_;
}
inline void RPCOptions::set_num_channels_per_target(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_channels_per_target_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RPCOptions.num_channels_per_target)
}

// -------------------------------------------------------------------

// SessionMetadata

// string name = 1;
inline void SessionMetadata::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SessionMetadata::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionMetadata.name)
  return name_.Get();
}
inline void SessionMetadata::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SessionMetadata.name)
}
inline void SessionMetadata::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SessionMetadata.name)
}
inline void SessionMetadata::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SessionMetadata.name)
}
inline void SessionMetadata::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SessionMetadata.name)
}
inline std::string* SessionMetadata::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SessionMetadata.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SessionMetadata::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SessionMetadata.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SessionMetadata::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SessionMetadata.name)
}
inline std::string* SessionMetadata::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SessionMetadata.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SessionMetadata::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SessionMetadata.name)
}

// int64 version = 2;
inline void SessionMetadata::clear_version() {
  version_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SessionMetadata::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionMetadata.version)
  return version_;
}
inline void SessionMetadata::set_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SessionMetadata.version)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ConfigProto_Experimental

// string collective_group_leader = 1;
inline void ConfigProto_Experimental::clear_collective_group_leader() {
  collective_group_leader_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ConfigProto_Experimental::collective_group_leader() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.collective_group_leader)
  return collective_group_leader_.Get();
}
inline void ConfigProto_Experimental::set_collective_group_leader(const std::string& value) {
  
  collective_group_leader_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.collective_group_leader)
}
inline void ConfigProto_Experimental::set_collective_group_leader(std::string&& value) {
  
  collective_group_leader_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ConfigProto.Experimental.collective_group_leader)
}
inline void ConfigProto_Experimental::set_collective_group_leader(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  collective_group_leader_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ConfigProto.Experimental.collective_group_leader)
}
inline void ConfigProto_Experimental::set_collective_group_leader(const char* value,
    size_t size) {
  
  collective_group_leader_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ConfigProto.Experimental.collective_group_leader)
}
inline std::string* ConfigProto_Experimental::mutable_collective_group_leader() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.Experimental.collective_group_leader)
  return collective_group_leader_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ConfigProto_Experimental::release_collective_group_leader() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.Experimental.collective_group_leader)
  
  return collective_group_leader_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ConfigProto_Experimental::set_allocated_collective_group_leader(std::string* collective_group_leader) {
  if (collective_group_leader != nullptr) {
    
  } else {
    
  }
  collective_group_leader_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), collective_group_leader,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.Experimental.collective_group_leader)
}
inline std::string* ConfigProto_Experimental::unsafe_arena_release_collective_group_leader() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ConfigProto.Experimental.collective_group_leader)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return collective_group_leader_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ConfigProto_Experimental::unsafe_arena_set_allocated_collective_group_leader(
    std::string* collective_group_leader) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (collective_group_leader != nullptr) {
    
  } else {
    
  }
  collective_group_leader_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      collective_group_leader, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.Experimental.collective_group_leader)
}

// string executor_type = 3;
inline void ConfigProto_Experimental::clear_executor_type() {
  executor_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ConfigProto_Experimental::executor_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.executor_type)
  return executor_type_.Get();
}
inline void ConfigProto_Experimental::set_executor_type(const std::string& value) {
  
  executor_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.executor_type)
}
inline void ConfigProto_Experimental::set_executor_type(std::string&& value) {
  
  executor_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ConfigProto.Experimental.executor_type)
}
inline void ConfigProto_Experimental::set_executor_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  executor_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ConfigProto.Experimental.executor_type)
}
inline void ConfigProto_Experimental::set_executor_type(const char* value,
    size_t size) {
  
  executor_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ConfigProto.Experimental.executor_type)
}
inline std::string* ConfigProto_Experimental::mutable_executor_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.Experimental.executor_type)
  return executor_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ConfigProto_Experimental::release_executor_type() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.Experimental.executor_type)
  
  return executor_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ConfigProto_Experimental::set_allocated_executor_type(std::string* executor_type) {
  if (executor_type != nullptr) {
    
  } else {
    
  }
  executor_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), executor_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.Experimental.executor_type)
}
inline std::string* ConfigProto_Experimental::unsafe_arena_release_executor_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ConfigProto.Experimental.executor_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return executor_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ConfigProto_Experimental::unsafe_arena_set_allocated_executor_type(
    std::string* executor_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (executor_type != nullptr) {
    
  } else {
    
  }
  executor_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      executor_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.Experimental.executor_type)
}

// int32 recv_buf_max_chunk = 4;
inline void ConfigProto_Experimental::clear_recv_buf_max_chunk() {
  recv_buf_max_chunk_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ConfigProto_Experimental::recv_buf_max_chunk() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.recv_buf_max_chunk)
  return recv_buf_max_chunk_;
}
inline void ConfigProto_Experimental::set_recv_buf_max_chunk(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  recv_buf_max_chunk_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.recv_buf_max_chunk)
}

// bool use_numa_affinity = 5;
inline void ConfigProto_Experimental::clear_use_numa_affinity() {
  use_numa_affinity_ = false;
}
inline bool ConfigProto_Experimental::use_numa_affinity() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.use_numa_affinity)
  return use_numa_affinity_;
}
inline void ConfigProto_Experimental::set_use_numa_affinity(bool value) {
  
  use_numa_affinity_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.use_numa_affinity)
}

// bool collective_deterministic_sequential_execution = 6;
inline void ConfigProto_Experimental::clear_collective_deterministic_sequential_execution() {
  collective_deterministic_sequential_execution_ = false;
}
inline bool ConfigProto_Experimental::collective_deterministic_sequential_execution() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.collective_deterministic_sequential_execution)
  return collective_deterministic_sequential_execution_;
}
inline void ConfigProto_Experimental::set_collective_deterministic_sequential_execution(bool value) {
  
  collective_deterministic_sequential_execution_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.collective_deterministic_sequential_execution)
}

// bool collective_nccl = 7;
inline void ConfigProto_Experimental::clear_collective_nccl() {
  collective_nccl_ = false;
}
inline bool ConfigProto_Experimental::collective_nccl() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.collective_nccl)
  return collective_nccl_;
}
inline void ConfigProto_Experimental::set_collective_nccl(bool value) {
  
  collective_nccl_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.collective_nccl)
}

// bool share_session_state_in_clusterspec_propagation = 8;
inline void ConfigProto_Experimental::clear_share_session_state_in_clusterspec_propagation() {
  share_session_state_in_clusterspec_propagation_ = false;
}
inline bool ConfigProto_Experimental::share_session_state_in_clusterspec_propagation() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.share_session_state_in_clusterspec_propagation)
  return share_session_state_in_clusterspec_propagation_;
}
inline void ConfigProto_Experimental::set_share_session_state_in_clusterspec_propagation(bool value) {
  
  share_session_state_in_clusterspec_propagation_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.share_session_state_in_clusterspec_propagation)
}

// bool disable_thread_spinning = 9;
inline void ConfigProto_Experimental::clear_disable_thread_spinning() {
  disable_thread_spinning_ = false;
}
inline bool ConfigProto_Experimental::disable_thread_spinning() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.disable_thread_spinning)
  return disable_thread_spinning_;
}
inline void ConfigProto_Experimental::set_disable_thread_spinning(bool value) {
  
  disable_thread_spinning_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.disable_thread_spinning)
}

// bool share_cluster_devices_in_session = 10;
inline void ConfigProto_Experimental::clear_share_cluster_devices_in_session() {
  share_cluster_devices_in_session_ = false;
}
inline bool ConfigProto_Experimental::share_cluster_devices_in_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.share_cluster_devices_in_session)
  return share_cluster_devices_in_session_;
}
inline void ConfigProto_Experimental::set_share_cluster_devices_in_session(bool value) {
  
  share_cluster_devices_in_session_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.share_cluster_devices_in_session)
}

// .tensorflow.SessionMetadata session_metadata = 11;
inline bool ConfigProto_Experimental::has_session_metadata() const {
  return this != internal_default_instance() && session_metadata_ != nullptr;
}
inline void ConfigProto_Experimental::clear_session_metadata() {
  if (GetArenaNoVirtual() == nullptr && session_metadata_ != nullptr) {
    delete session_metadata_;
  }
  session_metadata_ = nullptr;
}
inline const ::tensorflow::SessionMetadata& ConfigProto_Experimental::session_metadata() const {
  const ::tensorflow::SessionMetadata* p = session_metadata_;
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.session_metadata)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::SessionMetadata*>(
      &::tensorflow::_SessionMetadata_default_instance_);
}
inline ::tensorflow::SessionMetadata* ConfigProto_Experimental::release_session_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.Experimental.session_metadata)
  
  ::tensorflow::SessionMetadata* temp = session_metadata_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  session_metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::SessionMetadata* ConfigProto_Experimental::unsafe_arena_release_session_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ConfigProto.Experimental.session_metadata)
  
  ::tensorflow::SessionMetadata* temp = session_metadata_;
  session_metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::SessionMetadata* ConfigProto_Experimental::mutable_session_metadata() {
  
  if (session_metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SessionMetadata>(GetArenaNoVirtual());
    session_metadata_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.Experimental.session_metadata)
  return session_metadata_;
}
inline void ConfigProto_Experimental::set_allocated_session_metadata(::tensorflow::SessionMetadata* session_metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete session_metadata_;
  }
  if (session_metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(session_metadata);
    if (message_arena != submessage_arena) {
      session_metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, session_metadata, submessage_arena);
    }
    
  } else {
    
  }
  session_metadata_ = session_metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.Experimental.session_metadata)
}

// bool optimize_for_static_graph = 12;
inline void ConfigProto_Experimental::clear_optimize_for_static_graph() {
  optimize_for_static_graph_ = false;
}
inline bool ConfigProto_Experimental::optimize_for_static_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.optimize_for_static_graph)
  return optimize_for_static_graph_;
}
inline void ConfigProto_Experimental::set_optimize_for_static_graph(bool value) {
  
  optimize_for_static_graph_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.optimize_for_static_graph)
}

// bool enable_mlir_bridge = 13;
inline void ConfigProto_Experimental::clear_enable_mlir_bridge() {
  enable_mlir_bridge_ = false;
}
inline bool ConfigProto_Experimental::enable_mlir_bridge() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.enable_mlir_bridge)
  return enable_mlir_bridge_;
}
inline void ConfigProto_Experimental::set_enable_mlir_bridge(bool value) {
  
  enable_mlir_bridge_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.enable_mlir_bridge)
}

// .tensorflow.ConfigProto.Experimental.MlirBridgeRollout mlir_bridge_rollout = 17;
inline void ConfigProto_Experimental::clear_mlir_bridge_rollout() {
  mlir_bridge_rollout_ = 0;
}
inline ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout ConfigProto_Experimental::mlir_bridge_rollout() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.mlir_bridge_rollout)
  return static_cast< ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout >(mlir_bridge_rollout_);
}
inline void ConfigProto_Experimental::set_mlir_bridge_rollout(::tensorflow::ConfigProto_Experimental_MlirBridgeRollout value) {
  
  mlir_bridge_rollout_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.mlir_bridge_rollout)
}

// bool enable_mlir_graph_optimization = 16;
inline void ConfigProto_Experimental::clear_enable_mlir_graph_optimization() {
  enable_mlir_graph_optimization_ = false;
}
inline bool ConfigProto_Experimental::enable_mlir_graph_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.enable_mlir_graph_optimization)
  return enable_mlir_graph_optimization_;
}
inline void ConfigProto_Experimental::set_enable_mlir_graph_optimization(bool value) {
  
  enable_mlir_graph_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.enable_mlir_graph_optimization)
}

// bool disable_output_partition_graphs = 14;
inline void ConfigProto_Experimental::clear_disable_output_partition_graphs() {
  disable_output_partition_graphs_ = false;
}
inline bool ConfigProto_Experimental::disable_output_partition_graphs() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.disable_output_partition_graphs)
  return disable_output_partition_graphs_;
}
inline void ConfigProto_Experimental::set_disable_output_partition_graphs(bool value) {
  
  disable_output_partition_graphs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.disable_output_partition_graphs)
}

// int64 xla_fusion_autotuner_thresh = 15;
inline void ConfigProto_Experimental::clear_xla_fusion_autotuner_thresh() {
  xla_fusion_autotuner_thresh_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConfigProto_Experimental::xla_fusion_autotuner_thresh() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.xla_fusion_autotuner_thresh)
  return xla_fusion_autotuner_thresh_;
}
inline void ConfigProto_Experimental::set_xla_fusion_autotuner_thresh(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  xla_fusion_autotuner_thresh_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.xla_fusion_autotuner_thresh)
}

// bool use_tfrt = 18;
inline void ConfigProto_Experimental::clear_use_tfrt() {
  use_tfrt_ = false;
}
inline bool ConfigProto_Experimental::use_tfrt() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.use_tfrt)
  return use_tfrt_;
}
inline void ConfigProto_Experimental::set_use_tfrt(bool value) {
  
  use_tfrt_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.use_tfrt)
}

// string coordination_service = 19;
inline void ConfigProto_Experimental::clear_coordination_service() {
  coordination_service_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ConfigProto_Experimental::coordination_service() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.coordination_service)
  return coordination_service_.Get();
}
inline void ConfigProto_Experimental::set_coordination_service(const std::string& value) {
  
  coordination_service_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.coordination_service)
}
inline void ConfigProto_Experimental::set_coordination_service(std::string&& value) {
  
  coordination_service_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ConfigProto.Experimental.coordination_service)
}
inline void ConfigProto_Experimental::set_coordination_service(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  coordination_service_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ConfigProto.Experimental.coordination_service)
}
inline void ConfigProto_Experimental::set_coordination_service(const char* value,
    size_t size) {
  
  coordination_service_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ConfigProto.Experimental.coordination_service)
}
inline std::string* ConfigProto_Experimental::mutable_coordination_service() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.Experimental.coordination_service)
  return coordination_service_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ConfigProto_Experimental::release_coordination_service() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.Experimental.coordination_service)
  
  return coordination_service_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ConfigProto_Experimental::set_allocated_coordination_service(std::string* coordination_service) {
  if (coordination_service != nullptr) {
    
  } else {
    
  }
  coordination_service_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), coordination_service,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.Experimental.coordination_service)
}
inline std::string* ConfigProto_Experimental::unsafe_arena_release_coordination_service() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ConfigProto.Experimental.coordination_service)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return coordination_service_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ConfigProto_Experimental::unsafe_arena_set_allocated_coordination_service(
    std::string* coordination_service) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (coordination_service != nullptr) {
    
  } else {
    
  }
  coordination_service_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      coordination_service, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.Experimental.coordination_service)
}

// bool fetch_remote_devices_in_multi_client = 20;
inline void ConfigProto_Experimental::clear_fetch_remote_devices_in_multi_client() {
  fetch_remote_devices_in_multi_client_ = false;
}
inline bool ConfigProto_Experimental::fetch_remote_devices_in_multi_client() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.fetch_remote_devices_in_multi_client)
  return fetch_remote_devices_in_multi_client_;
}
inline void ConfigProto_Experimental::set_fetch_remote_devices_in_multi_client(bool value) {
  
  fetch_remote_devices_in_multi_client_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.fetch_remote_devices_in_multi_client)
}

// -------------------------------------------------------------------

// ConfigProto

// map<string, int32> device_count = 1;
inline int ConfigProto::device_count_size() const {
  return device_count_.size();
}
inline void ConfigProto::clear_device_count() {
  device_count_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int32 >&
ConfigProto::device_count() const {
  // @@protoc_insertion_point(field_map:tensorflow.ConfigProto.device_count)
  return device_count_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::PROTOBUF_NAMESPACE_ID::int32 >*
ConfigProto::mutable_device_count() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.ConfigProto.device_count)
  return device_count_.MutableMap();
}

// int32 intra_op_parallelism_threads = 2;
inline void ConfigProto::clear_intra_op_parallelism_threads() {
  intra_op_parallelism_threads_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ConfigProto::intra_op_parallelism_threads() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.intra_op_parallelism_threads)
  return intra_op_parallelism_threads_;
}
inline void ConfigProto::set_intra_op_parallelism_threads(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  intra_op_parallelism_threads_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.intra_op_parallelism_threads)
}

// int32 inter_op_parallelism_threads = 5;
inline void ConfigProto::clear_inter_op_parallelism_threads() {
  inter_op_parallelism_threads_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ConfigProto::inter_op_parallelism_threads() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.inter_op_parallelism_threads)
  return inter_op_parallelism_threads_;
}
inline void ConfigProto::set_inter_op_parallelism_threads(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  inter_op_parallelism_threads_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.inter_op_parallelism_threads)
}

// bool use_per_session_threads = 9;
inline void ConfigProto::clear_use_per_session_threads() {
  use_per_session_threads_ = false;
}
inline bool ConfigProto::use_per_session_threads() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.use_per_session_threads)
  return use_per_session_threads_;
}
inline void ConfigProto::set_use_per_session_threads(bool value) {
  
  use_per_session_threads_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.use_per_session_threads)
}

// repeated .tensorflow.ThreadPoolOptionProto session_inter_op_thread_pool = 12;
inline int ConfigProto::session_inter_op_thread_pool_size() const {
  return session_inter_op_thread_pool_.size();
}
inline void ConfigProto::clear_session_inter_op_thread_pool() {
  session_inter_op_thread_pool_.Clear();
}
inline ::tensorflow::ThreadPoolOptionProto* ConfigProto::mutable_session_inter_op_thread_pool(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.session_inter_op_thread_pool)
  return session_inter_op_thread_pool_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ThreadPoolOptionProto >*
ConfigProto::mutable_session_inter_op_thread_pool() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ConfigProto.session_inter_op_thread_pool)
  return &session_inter_op_thread_pool_;
}
inline const ::tensorflow::ThreadPoolOptionProto& ConfigProto::session_inter_op_thread_pool(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.session_inter_op_thread_pool)
  return session_inter_op_thread_pool_.Get(index);
}
inline ::tensorflow::ThreadPoolOptionProto* ConfigProto::add_session_inter_op_thread_pool() {
  // @@protoc_insertion_point(field_add:tensorflow.ConfigProto.session_inter_op_thread_pool)
  return session_inter_op_thread_pool_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ThreadPoolOptionProto >&
ConfigProto::session_inter_op_thread_pool() const {
  // @@protoc_insertion_point(field_list:tensorflow.ConfigProto.session_inter_op_thread_pool)
  return session_inter_op_thread_pool_;
}

// int32 placement_period = 3;
inline void ConfigProto::clear_placement_period() {
  placement_period_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ConfigProto::placement_period() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.placement_period)
  return placement_period_;
}
inline void ConfigProto::set_placement_period(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  placement_period_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.placement_period)
}

// repeated string device_filters = 4;
inline int ConfigProto::device_filters_size() const {
  return device_filters_.size();
}
inline void ConfigProto::clear_device_filters() {
  device_filters_.Clear();
}
inline const std::string& ConfigProto::device_filters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.device_filters)
  return device_filters_.Get(index);
}
inline std::string* ConfigProto::mutable_device_filters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.device_filters)
  return device_filters_.Mutable(index);
}
inline void ConfigProto::set_device_filters(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.device_filters)
  device_filters_.Mutable(index)->assign(value);
}
inline void ConfigProto::set_device_filters(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.device_filters)
  device_filters_.Mutable(index)->assign(std::move(value));
}
inline void ConfigProto::set_device_filters(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  device_filters_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ConfigProto.device_filters)
}
inline void ConfigProto::set_device_filters(int index, const char* value, size_t size) {
  device_filters_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ConfigProto.device_filters)
}
inline std::string* ConfigProto::add_device_filters() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ConfigProto.device_filters)
  return device_filters_.Add();
}
inline void ConfigProto::add_device_filters(const std::string& value) {
  device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ConfigProto.device_filters)
}
inline void ConfigProto::add_device_filters(std::string&& value) {
  device_filters_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ConfigProto.device_filters)
}
inline void ConfigProto::add_device_filters(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ConfigProto.device_filters)
}
inline void ConfigProto::add_device_filters(const char* value, size_t size) {
  device_filters_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ConfigProto.device_filters)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ConfigProto::device_filters() const {
  // @@protoc_insertion_point(field_list:tensorflow.ConfigProto.device_filters)
  return device_filters_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ConfigProto::mutable_device_filters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ConfigProto.device_filters)
  return &device_filters_;
}

// .tensorflow.GPUOptions gpu_options = 6;
inline bool ConfigProto::has_gpu_options() const {
  return this != internal_default_instance() && gpu_options_ != nullptr;
}
inline void ConfigProto::clear_gpu_options() {
  if (GetArenaNoVirtual() == nullptr && gpu_options_ != nullptr) {
    delete gpu_options_;
  }
  gpu_options_ = nullptr;
}
inline const ::tensorflow::GPUOptions& ConfigProto::gpu_options() const {
  const ::tensorflow::GPUOptions* p = gpu_options_;
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.gpu_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::GPUOptions*>(
      &::tensorflow::_GPUOptions_default_instance_);
}
inline ::tensorflow::GPUOptions* ConfigProto::release_gpu_options() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.gpu_options)
  
  ::tensorflow::GPUOptions* temp = gpu_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  gpu_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GPUOptions* ConfigProto::unsafe_arena_release_gpu_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ConfigProto.gpu_options)
  
  ::tensorflow::GPUOptions* temp = gpu_options_;
  gpu_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GPUOptions* ConfigProto::mutable_gpu_options() {
  
  if (gpu_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GPUOptions>(GetArenaNoVirtual());
    gpu_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.gpu_options)
  return gpu_options_;
}
inline void ConfigProto::set_allocated_gpu_options(::tensorflow::GPUOptions* gpu_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete gpu_options_;
  }
  if (gpu_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(gpu_options);
    if (message_arena != submessage_arena) {
      gpu_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, gpu_options, submessage_arena);
    }
    
  } else {
    
  }
  gpu_options_ = gpu_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.gpu_options)
}

// bool allow_soft_placement = 7;
inline void ConfigProto::clear_allow_soft_placement() {
  allow_soft_placement_ = false;
}
inline bool ConfigProto::allow_soft_placement() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.allow_soft_placement)
  return allow_soft_placement_;
}
inline void ConfigProto::set_allow_soft_placement(bool value) {
  
  allow_soft_placement_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.allow_soft_placement)
}

// bool log_device_placement = 8;
inline void ConfigProto::clear_log_device_placement() {
  log_device_placement_ = false;
}
inline bool ConfigProto::log_device_placement() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.log_device_placement)
  return log_device_placement_;
}
inline void ConfigProto::set_log_device_placement(bool value) {
  
  log_device_placement_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.log_device_placement)
}

// .tensorflow.GraphOptions graph_options = 10;
inline bool ConfigProto::has_graph_options() const {
  return this != internal_default_instance() && graph_options_ != nullptr;
}
inline void ConfigProto::clear_graph_options() {
  if (GetArenaNoVirtual() == nullptr && graph_options_ != nullptr) {
    delete graph_options_;
  }
  graph_options_ = nullptr;
}
inline const ::tensorflow::GraphOptions& ConfigProto::graph_options() const {
  const ::tensorflow::GraphOptions* p = graph_options_;
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.graph_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::GraphOptions*>(
      &::tensorflow::_GraphOptions_default_instance_);
}
inline ::tensorflow::GraphOptions* ConfigProto::release_graph_options() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.graph_options)
  
  ::tensorflow::GraphOptions* temp = graph_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  graph_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphOptions* ConfigProto::unsafe_arena_release_graph_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ConfigProto.graph_options)
  
  ::tensorflow::GraphOptions* temp = graph_options_;
  graph_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphOptions* ConfigProto::mutable_graph_options() {
  
  if (graph_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphOptions>(GetArenaNoVirtual());
    graph_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.graph_options)
  return graph_options_;
}
inline void ConfigProto::set_allocated_graph_options(::tensorflow::GraphOptions* graph_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete graph_options_;
  }
  if (graph_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(graph_options);
    if (message_arena != submessage_arena) {
      graph_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_options, submessage_arena);
    }
    
  } else {
    
  }
  graph_options_ = graph_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.graph_options)
}

// int64 operation_timeout_in_ms = 11;
inline void ConfigProto::clear_operation_timeout_in_ms() {
  operation_timeout_in_ms_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConfigProto::operation_timeout_in_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.operation_timeout_in_ms)
  return operation_timeout_in_ms_;
}
inline void ConfigProto::set_operation_timeout_in_ms(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  operation_timeout_in_ms_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.operation_timeout_in_ms)
}

// .tensorflow.RPCOptions rpc_options = 13;
inline bool ConfigProto::has_rpc_options() const {
  return this != internal_default_instance() && rpc_options_ != nullptr;
}
inline void ConfigProto::clear_rpc_options() {
  if (GetArenaNoVirtual() == nullptr && rpc_options_ != nullptr) {
    delete rpc_options_;
  }
  rpc_options_ = nullptr;
}
inline const ::tensorflow::RPCOptions& ConfigProto::rpc_options() const {
  const ::tensorflow::RPCOptions* p = rpc_options_;
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.rpc_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::RPCOptions*>(
      &::tensorflow::_RPCOptions_default_instance_);
}
inline ::tensorflow::RPCOptions* ConfigProto::release_rpc_options() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.rpc_options)
  
  ::tensorflow::RPCOptions* temp = rpc_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  rpc_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RPCOptions* ConfigProto::unsafe_arena_release_rpc_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ConfigProto.rpc_options)
  
  ::tensorflow::RPCOptions* temp = rpc_options_;
  rpc_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RPCOptions* ConfigProto::mutable_rpc_options() {
  
  if (rpc_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RPCOptions>(GetArenaNoVirtual());
    rpc_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.rpc_options)
  return rpc_options_;
}
inline void ConfigProto::set_allocated_rpc_options(::tensorflow::RPCOptions* rpc_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete rpc_options_;
  }
  if (rpc_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(rpc_options);
    if (message_arena != submessage_arena) {
      rpc_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rpc_options, submessage_arena);
    }
    
  } else {
    
  }
  rpc_options_ = rpc_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.rpc_options)
}

// .tensorflow.ClusterDef cluster_def = 14;
inline bool ConfigProto::has_cluster_def() const {
  return this != internal_default_instance() && cluster_def_ != nullptr;
}
inline const ::tensorflow::ClusterDef& ConfigProto::cluster_def() const {
  const ::tensorflow::ClusterDef* p = cluster_def_;
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.cluster_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ClusterDef*>(
      &::tensorflow::_ClusterDef_default_instance_);
}
inline ::tensorflow::ClusterDef* ConfigProto::release_cluster_def() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.cluster_def)
  
  ::tensorflow::ClusterDef* temp = cluster_def_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  cluster_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ClusterDef* ConfigProto::unsafe_arena_release_cluster_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ConfigProto.cluster_def)
  
  ::tensorflow::ClusterDef* temp = cluster_def_;
  cluster_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ClusterDef* ConfigProto::mutable_cluster_def() {
  
  if (cluster_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ClusterDef>(GetArenaNoVirtual());
    cluster_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.cluster_def)
  return cluster_def_;
}
inline void ConfigProto::set_allocated_cluster_def(::tensorflow::ClusterDef* cluster_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(cluster_def_);
  }
  if (cluster_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cluster_def)->GetArena();
    if (message_arena != submessage_arena) {
      cluster_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cluster_def, submessage_arena);
    }
    
  } else {
    
  }
  cluster_def_ = cluster_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.cluster_def)
}

// bool isolate_session_state = 15;
inline void ConfigProto::clear_isolate_session_state() {
  isolate_session_state_ = false;
}
inline bool ConfigProto::isolate_session_state() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.isolate_session_state)
  return isolate_session_state_;
}
inline void ConfigProto::set_isolate_session_state(bool value) {
  
  isolate_session_state_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.isolate_session_state)
}

// bool share_cluster_devices_in_session = 17;
inline void ConfigProto::clear_share_cluster_devices_in_session() {
  share_cluster_devices_in_session_ = false;
}
inline bool ConfigProto::share_cluster_devices_in_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.share_cluster_devices_in_session)
  return share_cluster_devices_in_session_;
}
inline void ConfigProto::set_share_cluster_devices_in_session(bool value) {
  
  share_cluster_devices_in_session_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.share_cluster_devices_in_session)
}

// .tensorflow.ConfigProto.Experimental experimental = 16;
inline bool ConfigProto::has_experimental() const {
  return this != internal_default_instance() && experimental_ != nullptr;
}
inline void ConfigProto::clear_experimental() {
  if (GetArenaNoVirtual() == nullptr && experimental_ != nullptr) {
    delete experimental_;
  }
  experimental_ = nullptr;
}
inline const ::tensorflow::ConfigProto_Experimental& ConfigProto::experimental() const {
  const ::tensorflow::ConfigProto_Experimental* p = experimental_;
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.experimental)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ConfigProto_Experimental*>(
      &::tensorflow::_ConfigProto_Experimental_default_instance_);
}
inline ::tensorflow::ConfigProto_Experimental* ConfigProto::release_experimental() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.experimental)
  
  ::tensorflow::ConfigProto_Experimental* temp = experimental_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  experimental_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto_Experimental* ConfigProto::unsafe_arena_release_experimental() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ConfigProto.experimental)
  
  ::tensorflow::ConfigProto_Experimental* temp = experimental_;
  experimental_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto_Experimental* ConfigProto::mutable_experimental() {
  
  if (experimental_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ConfigProto_Experimental>(GetArenaNoVirtual());
    experimental_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.experimental)
  return experimental_;
}
inline void ConfigProto::set_allocated_experimental(::tensorflow::ConfigProto_Experimental* experimental) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete experimental_;
  }
  if (experimental) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(experimental);
    if (message_arena != submessage_arena) {
      experimental = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, experimental, submessage_arena);
    }
    
  } else {
    
  }
  experimental_ = experimental;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.experimental)
}

// -------------------------------------------------------------------

// RunOptions_Experimental_RunHandlerPoolOptions

// int64 priority = 1;
inline void RunOptions_Experimental_RunHandlerPoolOptions::clear_priority() {
  priority_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RunOptions_Experimental_RunHandlerPoolOptions::priority() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.Experimental.RunHandlerPoolOptions.priority)
  return priority_;
}
inline void RunOptions_Experimental_RunHandlerPoolOptions::set_priority(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  priority_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.Experimental.RunHandlerPoolOptions.priority)
}

// -------------------------------------------------------------------

// RunOptions_Experimental

// int64 collective_graph_key = 1;
inline void RunOptions_Experimental::clear_collective_graph_key() {
  collective_graph_key_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RunOptions_Experimental::collective_graph_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.Experimental.collective_graph_key)
  return collective_graph_key_;
}
inline void RunOptions_Experimental::set_collective_graph_key(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  collective_graph_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.Experimental.collective_graph_key)
}

// bool use_run_handler_pool = 2;
inline void RunOptions_Experimental::clear_use_run_handler_pool() {
  use_run_handler_pool_ = false;
}
inline bool RunOptions_Experimental::use_run_handler_pool() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.Experimental.use_run_handler_pool)
  return use_run_handler_pool_;
}
inline void RunOptions_Experimental::set_use_run_handler_pool(bool value) {
  
  use_run_handler_pool_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.Experimental.use_run_handler_pool)
}

// .tensorflow.RunOptions.Experimental.RunHandlerPoolOptions run_handler_pool_options = 3;
inline bool RunOptions_Experimental::has_run_handler_pool_options() const {
  return this != internal_default_instance() && run_handler_pool_options_ != nullptr;
}
inline void RunOptions_Experimental::clear_run_handler_pool_options() {
  if (GetArenaNoVirtual() == nullptr && run_handler_pool_options_ != nullptr) {
    delete run_handler_pool_options_;
  }
  run_handler_pool_options_ = nullptr;
}
inline const ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions& RunOptions_Experimental::run_handler_pool_options() const {
  const ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* p = run_handler_pool_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.Experimental.run_handler_pool_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions*>(
      &::tensorflow::_RunOptions_Experimental_RunHandlerPoolOptions_default_instance_);
}
inline ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* RunOptions_Experimental::release_run_handler_pool_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RunOptions.Experimental.run_handler_pool_options)
  
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* temp = run_handler_pool_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  run_handler_pool_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* RunOptions_Experimental::unsafe_arena_release_run_handler_pool_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunOptions.Experimental.run_handler_pool_options)
  
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* temp = run_handler_pool_options_;
  run_handler_pool_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* RunOptions_Experimental::mutable_run_handler_pool_options() {
  
  if (run_handler_pool_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions>(GetArenaNoVirtual());
    run_handler_pool_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunOptions.Experimental.run_handler_pool_options)
  return run_handler_pool_options_;
}
inline void RunOptions_Experimental::set_allocated_run_handler_pool_options(::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* run_handler_pool_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete run_handler_pool_options_;
  }
  if (run_handler_pool_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(run_handler_pool_options);
    if (message_arena != submessage_arena) {
      run_handler_pool_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, run_handler_pool_options, submessage_arena);
    }
    
  } else {
    
  }
  run_handler_pool_options_ = run_handler_pool_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunOptions.Experimental.run_handler_pool_options)
}

// -------------------------------------------------------------------

// RunOptions

// .tensorflow.RunOptions.TraceLevel trace_level = 1;
inline void RunOptions::clear_trace_level() {
  trace_level_ = 0;
}
inline ::tensorflow::RunOptions_TraceLevel RunOptions::trace_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.trace_level)
  return static_cast< ::tensorflow::RunOptions_TraceLevel >(trace_level_);
}
inline void RunOptions::set_trace_level(::tensorflow::RunOptions_TraceLevel value) {
  
  trace_level_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.trace_level)
}

// int64 timeout_in_ms = 2;
inline void RunOptions::clear_timeout_in_ms() {
  timeout_in_ms_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RunOptions::timeout_in_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.timeout_in_ms)
  return timeout_in_ms_;
}
inline void RunOptions::set_timeout_in_ms(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  timeout_in_ms_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.timeout_in_ms)
}

// int32 inter_op_thread_pool = 3;
inline void RunOptions::clear_inter_op_thread_pool() {
  inter_op_thread_pool_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RunOptions::inter_op_thread_pool() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.inter_op_thread_pool)
  return inter_op_thread_pool_;
}
inline void RunOptions::set_inter_op_thread_pool(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  inter_op_thread_pool_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.inter_op_thread_pool)
}

// bool output_partition_graphs = 5;
inline void RunOptions::clear_output_partition_graphs() {
  output_partition_graphs_ = false;
}
inline bool RunOptions::output_partition_graphs() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.output_partition_graphs)
  return output_partition_graphs_;
}
inline void RunOptions::set_output_partition_graphs(bool value) {
  
  output_partition_graphs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.output_partition_graphs)
}

// .tensorflow.DebugOptions debug_options = 6;
inline bool RunOptions::has_debug_options() const {
  return this != internal_default_instance() && debug_options_ != nullptr;
}
inline const ::tensorflow::DebugOptions& RunOptions::debug_options() const {
  const ::tensorflow::DebugOptions* p = debug_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.debug_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::DebugOptions*>(
      &::tensorflow::_DebugOptions_default_instance_);
}
inline ::tensorflow::DebugOptions* RunOptions::release_debug_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RunOptions.debug_options)
  
  ::tensorflow::DebugOptions* temp = debug_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  debug_options_ = nullptr;
  return temp;
}
inline ::tensorflow::DebugOptions* RunOptions::unsafe_arena_release_debug_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunOptions.debug_options)
  
  ::tensorflow::DebugOptions* temp = debug_options_;
  debug_options_ = nullptr;
  return temp;
}
inline ::tensorflow::DebugOptions* RunOptions::mutable_debug_options() {
  
  if (debug_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DebugOptions>(GetArenaNoVirtual());
    debug_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunOptions.debug_options)
  return debug_options_;
}
inline void RunOptions::set_allocated_debug_options(::tensorflow::DebugOptions* debug_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(debug_options_);
  }
  if (debug_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(debug_options)->GetArena();
    if (message_arena != submessage_arena) {
      debug_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, debug_options, submessage_arena);
    }
    
  } else {
    
  }
  debug_options_ = debug_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunOptions.debug_options)
}

// bool report_tensor_allocations_upon_oom = 7;
inline void RunOptions::clear_report_tensor_allocations_upon_oom() {
  report_tensor_allocations_upon_oom_ = false;
}
inline bool RunOptions::report_tensor_allocations_upon_oom() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.report_tensor_allocations_upon_oom)
  return report_tensor_allocations_upon_oom_;
}
inline void RunOptions::set_report_tensor_allocations_upon_oom(bool value) {
  
  report_tensor_allocations_upon_oom_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.report_tensor_allocations_upon_oom)
}

// .tensorflow.RunOptions.Experimental experimental = 8;
inline bool RunOptions::has_experimental() const {
  return this != internal_default_instance() && experimental_ != nullptr;
}
inline void RunOptions::clear_experimental() {
  if (GetArenaNoVirtual() == nullptr && experimental_ != nullptr) {
    delete experimental_;
  }
  experimental_ = nullptr;
}
inline const ::tensorflow::RunOptions_Experimental& RunOptions::experimental() const {
  const ::tensorflow::RunOptions_Experimental* p = experimental_;
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.experimental)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::RunOptions_Experimental*>(
      &::tensorflow::_RunOptions_Experimental_default_instance_);
}
inline ::tensorflow::RunOptions_Experimental* RunOptions::release_experimental() {
  // @@protoc_insertion_point(field_release:tensorflow.RunOptions.experimental)
  
  ::tensorflow::RunOptions_Experimental* temp = experimental_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  experimental_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions_Experimental* RunOptions::unsafe_arena_release_experimental() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunOptions.experimental)
  
  ::tensorflow::RunOptions_Experimental* temp = experimental_;
  experimental_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions_Experimental* RunOptions::mutable_experimental() {
  
  if (experimental_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunOptions_Experimental>(GetArenaNoVirtual());
    experimental_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunOptions.experimental)
  return experimental_;
}
inline void RunOptions::set_allocated_experimental(::tensorflow::RunOptions_Experimental* experimental) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete experimental_;
  }
  if (experimental) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(experimental);
    if (message_arena != submessage_arena) {
      experimental = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, experimental, submessage_arena);
    }
    
  } else {
    
  }
  experimental_ = experimental;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunOptions.experimental)
}

// -------------------------------------------------------------------

// RunMetadata_FunctionGraphs

// repeated .tensorflow.GraphDef partition_graphs = 1;
inline int RunMetadata_FunctionGraphs::partition_graphs_size() const {
  return partition_graphs_.size();
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::mutable_partition_graphs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.FunctionGraphs.partition_graphs)
  return partition_graphs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
RunMetadata_FunctionGraphs::mutable_partition_graphs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunMetadata.FunctionGraphs.partition_graphs)
  return &partition_graphs_;
}
inline const ::tensorflow::GraphDef& RunMetadata_FunctionGraphs::partition_graphs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.FunctionGraphs.partition_graphs)
  return partition_graphs_.Get(index);
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::add_partition_graphs() {
  // @@protoc_insertion_point(field_add:tensorflow.RunMetadata.FunctionGraphs.partition_graphs)
  return partition_graphs_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
RunMetadata_FunctionGraphs::partition_graphs() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunMetadata.FunctionGraphs.partition_graphs)
  return partition_graphs_;
}

// .tensorflow.GraphDef pre_optimization_graph = 2;
inline bool RunMetadata_FunctionGraphs::has_pre_optimization_graph() const {
  return this != internal_default_instance() && pre_optimization_graph_ != nullptr;
}
inline const ::tensorflow::GraphDef& RunMetadata_FunctionGraphs::pre_optimization_graph() const {
  const ::tensorflow::GraphDef* p = pre_optimization_graph_;
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::GraphDef*>(
      &::tensorflow::_GraphDef_default_instance_);
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::release_pre_optimization_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
  
  ::tensorflow::GraphDef* temp = pre_optimization_graph_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  pre_optimization_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::unsafe_arena_release_pre_optimization_graph() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
  
  ::tensorflow::GraphDef* temp = pre_optimization_graph_;
  pre_optimization_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::mutable_pre_optimization_graph() {
  
  if (pre_optimization_graph_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaNoVirtual());
    pre_optimization_graph_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
  return pre_optimization_graph_;
}
inline void RunMetadata_FunctionGraphs::set_allocated_pre_optimization_graph(::tensorflow::GraphDef* pre_optimization_graph) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(pre_optimization_graph_);
  }
  if (pre_optimization_graph) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(pre_optimization_graph)->GetArena();
    if (message_arena != submessage_arena) {
      pre_optimization_graph = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, pre_optimization_graph, submessage_arena);
    }
    
  } else {
    
  }
  pre_optimization_graph_ = pre_optimization_graph;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
}

// .tensorflow.GraphDef post_optimization_graph = 3;
inline bool RunMetadata_FunctionGraphs::has_post_optimization_graph() const {
  return this != internal_default_instance() && post_optimization_graph_ != nullptr;
}
inline const ::tensorflow::GraphDef& RunMetadata_FunctionGraphs::post_optimization_graph() const {
  const ::tensorflow::GraphDef* p = post_optimization_graph_;
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::GraphDef*>(
      &::tensorflow::_GraphDef_default_instance_);
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::release_post_optimization_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
  
  ::tensorflow::GraphDef* temp = post_optimization_graph_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  post_optimization_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::unsafe_arena_release_post_optimization_graph() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
  
  ::tensorflow::GraphDef* temp = post_optimization_graph_;
  post_optimization_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::mutable_post_optimization_graph() {
  
  if (post_optimization_graph_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaNoVirtual());
    post_optimization_graph_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
  return post_optimization_graph_;
}
inline void RunMetadata_FunctionGraphs::set_allocated_post_optimization_graph(::tensorflow::GraphDef* post_optimization_graph) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(post_optimization_graph_);
  }
  if (post_optimization_graph) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(post_optimization_graph)->GetArena();
    if (message_arena != submessage_arena) {
      post_optimization_graph = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, post_optimization_graph, submessage_arena);
    }
    
  } else {
    
  }
  post_optimization_graph_ = post_optimization_graph;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
}

// -------------------------------------------------------------------

// RunMetadata

// .tensorflow.StepStats step_stats = 1;
inline bool RunMetadata::has_step_stats() const {
  return this != internal_default_instance() && step_stats_ != nullptr;
}
inline const ::tensorflow::StepStats& RunMetadata::step_stats() const {
  const ::tensorflow::StepStats* p = step_stats_;
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.step_stats)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::StepStats*>(
      &::tensorflow::_StepStats_default_instance_);
}
inline ::tensorflow::StepStats* RunMetadata::release_step_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.RunMetadata.step_stats)
  
  ::tensorflow::StepStats* temp = step_stats_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  step_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::StepStats* RunMetadata::unsafe_arena_release_step_stats() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunMetadata.step_stats)
  
  ::tensorflow::StepStats* temp = step_stats_;
  step_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::StepStats* RunMetadata::mutable_step_stats() {
  
  if (step_stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StepStats>(GetArenaNoVirtual());
    step_stats_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.step_stats)
  return step_stats_;
}
inline void RunMetadata::set_allocated_step_stats(::tensorflow::StepStats* step_stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(step_stats_);
  }
  if (step_stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(step_stats)->GetArena();
    if (message_arena != submessage_arena) {
      step_stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, step_stats, submessage_arena);
    }
    
  } else {
    
  }
  step_stats_ = step_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunMetadata.step_stats)
}

// .tensorflow.CostGraphDef cost_graph = 2;
inline bool RunMetadata::has_cost_graph() const {
  return this != internal_default_instance() && cost_graph_ != nullptr;
}
inline const ::tensorflow::CostGraphDef& RunMetadata::cost_graph() const {
  const ::tensorflow::CostGraphDef* p = cost_graph_;
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.cost_graph)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::CostGraphDef*>(
      &::tensorflow::_CostGraphDef_default_instance_);
}
inline ::tensorflow::CostGraphDef* RunMetadata::release_cost_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.RunMetadata.cost_graph)
  
  ::tensorflow::CostGraphDef* temp = cost_graph_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  cost_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::CostGraphDef* RunMetadata::unsafe_arena_release_cost_graph() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunMetadata.cost_graph)
  
  ::tensorflow::CostGraphDef* temp = cost_graph_;
  cost_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::CostGraphDef* RunMetadata::mutable_cost_graph() {
  
  if (cost_graph_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CostGraphDef>(GetArenaNoVirtual());
    cost_graph_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.cost_graph)
  return cost_graph_;
}
inline void RunMetadata::set_allocated_cost_graph(::tensorflow::CostGraphDef* cost_graph) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(cost_graph_);
  }
  if (cost_graph) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cost_graph)->GetArena();
    if (message_arena != submessage_arena) {
      cost_graph = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cost_graph, submessage_arena);
    }
    
  } else {
    
  }
  cost_graph_ = cost_graph;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunMetadata.cost_graph)
}

// repeated .tensorflow.GraphDef partition_graphs = 3;
inline int RunMetadata::partition_graphs_size() const {
  return partition_graphs_.size();
}
inline ::tensorflow::GraphDef* RunMetadata::mutable_partition_graphs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.partition_graphs)
  return partition_graphs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
RunMetadata::mutable_partition_graphs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunMetadata.partition_graphs)
  return &partition_graphs_;
}
inline const ::tensorflow::GraphDef& RunMetadata::partition_graphs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.partition_graphs)
  return partition_graphs_.Get(index);
}
inline ::tensorflow::GraphDef* RunMetadata::add_partition_graphs() {
  // @@protoc_insertion_point(field_add:tensorflow.RunMetadata.partition_graphs)
  return partition_graphs_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
RunMetadata::partition_graphs() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunMetadata.partition_graphs)
  return partition_graphs_;
}

// repeated .tensorflow.RunMetadata.FunctionGraphs function_graphs = 4;
inline int RunMetadata::function_graphs_size() const {
  return function_graphs_.size();
}
inline void RunMetadata::clear_function_graphs() {
  function_graphs_.Clear();
}
inline ::tensorflow::RunMetadata_FunctionGraphs* RunMetadata::mutable_function_graphs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.function_graphs)
  return function_graphs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RunMetadata_FunctionGraphs >*
RunMetadata::mutable_function_graphs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunMetadata.function_graphs)
  return &function_graphs_;
}
inline const ::tensorflow::RunMetadata_FunctionGraphs& RunMetadata::function_graphs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.function_graphs)
  return function_graphs_.Get(index);
}
inline ::tensorflow::RunMetadata_FunctionGraphs* RunMetadata::add_function_graphs() {
  // @@protoc_insertion_point(field_add:tensorflow.RunMetadata.function_graphs)
  return function_graphs_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RunMetadata_FunctionGraphs >&
RunMetadata::function_graphs() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunMetadata.function_graphs)
  return function_graphs_;
}

// -------------------------------------------------------------------

// TensorConnection

// string from_tensor = 1;
inline void TensorConnection::clear_from_tensor() {
  from_tensor_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TensorConnection::from_tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorConnection.from_tensor)
  return from_tensor_.Get();
}
inline void TensorConnection::set_from_tensor(const std::string& value) {
  
  from_tensor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TensorConnection.from_tensor)
}
inline void TensorConnection::set_from_tensor(std::string&& value) {
  
  from_tensor_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TensorConnection.from_tensor)
}
inline void TensorConnection::set_from_tensor(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  from_tensor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorConnection.from_tensor)
}
inline void TensorConnection::set_from_tensor(const char* value,
    size_t size) {
  
  from_tensor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorConnection.from_tensor)
}
inline std::string* TensorConnection::mutable_from_tensor() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorConnection.from_tensor)
  return from_tensor_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TensorConnection::release_from_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorConnection.from_tensor)
  
  return from_tensor_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TensorConnection::set_allocated_from_tensor(std::string* from_tensor) {
  if (from_tensor != nullptr) {
    
  } else {
    
  }
  from_tensor_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from_tensor,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorConnection.from_tensor)
}
inline std::string* TensorConnection::unsafe_arena_release_from_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorConnection.from_tensor)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return from_tensor_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TensorConnection::unsafe_arena_set_allocated_from_tensor(
    std::string* from_tensor) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (from_tensor != nullptr) {
    
  } else {
    
  }
  from_tensor_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      from_tensor, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorConnection.from_tensor)
}

// string to_tensor = 2;
inline void TensorConnection::clear_to_tensor() {
  to_tensor_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TensorConnection::to_tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorConnection.to_tensor)
  return to_tensor_.Get();
}
inline void TensorConnection::set_to_tensor(const std::string& value) {
  
  to_tensor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TensorConnection.to_tensor)
}
inline void TensorConnection::set_to_tensor(std::string&& value) {
  
  to_tensor_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TensorConnection.to_tensor)
}
inline void TensorConnection::set_to_tensor(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  to_tensor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorConnection.to_tensor)
}
inline void TensorConnection::set_to_tensor(const char* value,
    size_t size) {
  
  to_tensor_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorConnection.to_tensor)
}
inline std::string* TensorConnection::mutable_to_tensor() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorConnection.to_tensor)
  return to_tensor_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TensorConnection::release_to_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorConnection.to_tensor)
  
  return to_tensor_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TensorConnection::set_allocated_to_tensor(std::string* to_tensor) {
  if (to_tensor != nullptr) {
    
  } else {
    
  }
  to_tensor_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), to_tensor,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorConnection.to_tensor)
}
inline std::string* TensorConnection::unsafe_arena_release_to_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorConnection.to_tensor)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return to_tensor_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TensorConnection::unsafe_arena_set_allocated_to_tensor(
    std::string* to_tensor) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (to_tensor != nullptr) {
    
  } else {
    
  }
  to_tensor_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      to_tensor, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorConnection.to_tensor)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// CallableOptions

// repeated string feed = 1;
inline int CallableOptions::feed_size() const {
  return feed_.size();
}
inline void CallableOptions::clear_feed() {
  feed_.Clear();
}
inline const std::string& CallableOptions::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.feed)
  return feed_.Get(index);
}
inline std::string* CallableOptions::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CallableOptions.feed)
  return feed_.Mutable(index);
}
inline void CallableOptions::set_feed(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.feed)
  feed_.Mutable(index)->assign(value);
}
inline void CallableOptions::set_feed(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.feed)
  feed_.Mutable(index)->assign(std::move(value));
}
inline void CallableOptions::set_feed(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  feed_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CallableOptions.feed)
}
inline void CallableOptions::set_feed(int index, const char* value, size_t size) {
  feed_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CallableOptions.feed)
}
inline std::string* CallableOptions::add_feed() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CallableOptions.feed)
  return feed_.Add();
}
inline void CallableOptions::add_feed(const std::string& value) {
  feed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.feed)
}
inline void CallableOptions::add_feed(std::string&& value) {
  feed_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.feed)
}
inline void CallableOptions::add_feed(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  feed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CallableOptions.feed)
}
inline void CallableOptions::add_feed(const char* value, size_t size) {
  feed_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CallableOptions.feed)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CallableOptions::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.CallableOptions.feed)
  return feed_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CallableOptions::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CallableOptions.feed)
  return &feed_;
}

// repeated string fetch = 2;
inline int CallableOptions::fetch_size() const {
  return fetch_.size();
}
inline void CallableOptions::clear_fetch() {
  fetch_.Clear();
}
inline const std::string& CallableOptions::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.fetch)
  return fetch_.Get(index);
}
inline std::string* CallableOptions::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CallableOptions.fetch)
  return fetch_.Mutable(index);
}
inline void CallableOptions::set_fetch(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.fetch)
  fetch_.Mutable(index)->assign(value);
}
inline void CallableOptions::set_fetch(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.fetch)
  fetch_.Mutable(index)->assign(std::move(value));
}
inline void CallableOptions::set_fetch(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CallableOptions.fetch)
}
inline void CallableOptions::set_fetch(int index, const char* value, size_t size) {
  fetch_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CallableOptions.fetch)
}
inline std::string* CallableOptions::add_fetch() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CallableOptions.fetch)
  return fetch_.Add();
}
inline void CallableOptions::add_fetch(const std::string& value) {
  fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.fetch)
}
inline void CallableOptions::add_fetch(std::string&& value) {
  fetch_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.fetch)
}
inline void CallableOptions::add_fetch(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CallableOptions.fetch)
}
inline void CallableOptions::add_fetch(const char* value, size_t size) {
  fetch_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CallableOptions.fetch)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CallableOptions::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.CallableOptions.fetch)
  return fetch_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CallableOptions::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CallableOptions.fetch)
  return &fetch_;
}

// repeated string target = 3;
inline int CallableOptions::target_size() const {
  return target_.size();
}
inline void CallableOptions::clear_target() {
  target_.Clear();
}
inline const std::string& CallableOptions::target(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.target)
  return target_.Get(index);
}
inline std::string* CallableOptions::mutable_target(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CallableOptions.target)
  return target_.Mutable(index);
}
inline void CallableOptions::set_target(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.target)
  target_.Mutable(index)->assign(value);
}
inline void CallableOptions::set_target(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.target)
  target_.Mutable(index)->assign(std::move(value));
}
inline void CallableOptions::set_target(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CallableOptions.target)
}
inline void CallableOptions::set_target(int index, const char* value, size_t size) {
  target_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CallableOptions.target)
}
inline std::string* CallableOptions::add_target() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CallableOptions.target)
  return target_.Add();
}
inline void CallableOptions::add_target(const std::string& value) {
  target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.target)
}
inline void CallableOptions::add_target(std::string&& value) {
  target_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.target)
}
inline void CallableOptions::add_target(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CallableOptions.target)
}
inline void CallableOptions::add_target(const char* value, size_t size) {
  target_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CallableOptions.target)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CallableOptions::target() const {
  // @@protoc_insertion_point(field_list:tensorflow.CallableOptions.target)
  return target_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CallableOptions::mutable_target() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CallableOptions.target)
  return &target_;
}

// .tensorflow.RunOptions run_options = 4;
inline bool CallableOptions::has_run_options() const {
  return this != internal_default_instance() && run_options_ != nullptr;
}
inline void CallableOptions::clear_run_options() {
  if (GetArenaNoVirtual() == nullptr && run_options_ != nullptr) {
    delete run_options_;
  }
  run_options_ = nullptr;
}
inline const ::tensorflow::RunOptions& CallableOptions::run_options() const {
  const ::tensorflow::RunOptions* p = run_options_;
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.run_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::RunOptions*>(
      &::tensorflow::_RunOptions_default_instance_);
}
inline ::tensorflow::RunOptions* CallableOptions::release_run_options() {
  // @@protoc_insertion_point(field_release:tensorflow.CallableOptions.run_options)
  
  ::tensorflow::RunOptions* temp = run_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  run_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions* CallableOptions::unsafe_arena_release_run_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CallableOptions.run_options)
  
  ::tensorflow::RunOptions* temp = run_options_;
  run_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions* CallableOptions::mutable_run_options() {
  
  if (run_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunOptions>(GetArenaNoVirtual());
    run_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CallableOptions.run_options)
  return run_options_;
}
inline void CallableOptions::set_allocated_run_options(::tensorflow::RunOptions* run_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete run_options_;
  }
  if (run_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(run_options);
    if (message_arena != submessage_arena) {
      run_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, run_options, submessage_arena);
    }
    
  } else {
    
  }
  run_options_ = run_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallableOptions.run_options)
}

// repeated .tensorflow.TensorConnection tensor_connection = 5;
inline int CallableOptions::tensor_connection_size() const {
  return tensor_connection_.size();
}
inline void CallableOptions::clear_tensor_connection() {
  tensor_connection_.Clear();
}
inline ::tensorflow::TensorConnection* CallableOptions::mutable_tensor_connection(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CallableOptions.tensor_connection)
  return tensor_connection_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorConnection >*
CallableOptions::mutable_tensor_connection() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CallableOptions.tensor_connection)
  return &tensor_connection_;
}
inline const ::tensorflow::TensorConnection& CallableOptions::tensor_connection(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.tensor_connection)
  return tensor_connection_.Get(index);
}
inline ::tensorflow::TensorConnection* CallableOptions::add_tensor_connection() {
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.tensor_connection)
  return tensor_connection_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorConnection >&
CallableOptions::tensor_connection() const {
  // @@protoc_insertion_point(field_list:tensorflow.CallableOptions.tensor_connection)
  return tensor_connection_;
}

// map<string, string> feed_devices = 6;
inline int CallableOptions::feed_devices_size() const {
  return feed_devices_.size();
}
inline void CallableOptions::clear_feed_devices() {
  feed_devices_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
CallableOptions::feed_devices() const {
  // @@protoc_insertion_point(field_map:tensorflow.CallableOptions.feed_devices)
  return feed_devices_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
CallableOptions::mutable_feed_devices() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.CallableOptions.feed_devices)
  return feed_devices_.MutableMap();
}

// map<string, string> fetch_devices = 7;
inline int CallableOptions::fetch_devices_size() const {
  return fetch_devices_.size();
}
inline void CallableOptions::clear_fetch_devices() {
  fetch_devices_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
CallableOptions::fetch_devices() const {
  // @@protoc_insertion_point(field_map:tensorflow.CallableOptions.fetch_devices)
  return fetch_devices_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
CallableOptions::mutable_fetch_devices() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.CallableOptions.fetch_devices)
  return fetch_devices_.MutableMap();
}

// bool fetch_skip_sync = 8;
inline void CallableOptions::clear_fetch_skip_sync() {
  fetch_skip_sync_ = false;
}
inline bool CallableOptions::fetch_skip_sync() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.fetch_skip_sync)
  return fetch_skip_sync_;
}
inline void CallableOptions::set_fetch_skip_sync(bool value) {
  
  fetch_skip_sync_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.fetch_skip_sync)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::OptimizerOptions_Level> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::OptimizerOptions_Level>() {
  return ::tensorflow::OptimizerOptions_Level_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::OptimizerOptions_GlobalJitLevel> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::OptimizerOptions_GlobalJitLevel>() {
  return ::tensorflow::OptimizerOptions_GlobalJitLevel_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout>() {
  return ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::RunOptions_TraceLevel> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RunOptions_TraceLevel>() {
  return ::tensorflow::RunOptions_TraceLevel_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto
