<html><head>
<meta http-equiv="Content-Type" content="text/html; charset=gbk">
<title> 验证- 中羽在线</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<link href="/cbo_mobile/style/style.css?2025050201" rel="stylesheet" type="text/css">
<meta name="robots" content="noindex, nofollow">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="mobile-web-app-capable" content="yes">
<meta name="format-detection" content="telephone=no">
<meta name="wap-font-scale" content="no">
<meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no,minimum-scale=1.0,maximum-scale=1.0">
<script src="//hm.baidu.com/hm.js?cfc948fc40dd345b6e12298c5c40ba13"></script><script type="text/javascript" src="/cbo_include/js/jquery-3.3.1.min.js"></script>
<script type="text/javascript" src="/cbo_include/js/layer/mobile/layer.js"></script><link href="https://www.badmintoncn.com/cbo_include/js/layer/mobile/need/layer.css?2.0" type="text/css" rel="styleSheet" id="layermcss">
</head>
<body class="no-select">
<div align="center" style="max-width:500px;margin:100px auto;">
<img src="/cbo_include/images/error.png">
<form name="form" method="post" action="/cbo_function.php?action=clickcookie" target="goPostGet">
<table class="table_info" cellspacing="10">
  <tbody><tr>
<td class="bigtext"><strong>您的网络有问题，请回答问题，验证后可正常使用网站</strong></td>
  </tr>
  <tr>
<td class="bigtext">问题：ABC后3个大写字母是哪3个？<input type="hidden" name="ak" value="6"></td>
  </tr>
   <tr>
<td class="bigtext">答案：<input name="a" type="text" size="10" value=""> <input type="submit" name="submit" value=" 提 交 " class="postbutton" size="20"></td>
  </tr> 
</tbody></table> 
<input type="hidden" name="referer" value="">  
</form>
</div>
<div id="showbg" class="showbg" style="display:none;"></div>
<div id="loginam" style="display:none;height:100px;background-color:#CCCCCC"></div>
<iframe id="goPostGet" name="goPostGet" width="0" height="0" frameborder="0" style="display:none;"></iframe>
<br>
<br>
<br>
<script type="text/javascript" src="/cbo_include/js/layer.js"></script>
<script type="text/javascript" src="/cbo_mobile/include/global.js?2025050401"></script>
<script language="JavaScript"> 
$(function(){
piclazyLoad();
});
$(window).scroll(function() {
  piclazyLoad();
});
function setMenuNavColor(id){
showProgress();
$('#'+id).siblings().removeClass();
$('#'+id).addClass('orangetext');
}
//判断是否APP打开连接 
//@u=URL
//@t=是否手机浏览器打开
//@h=非APP内是否当前页打开
function clickOpenUrl(u,t,h){
if(u.substr(0,9).toLowerCase() == "cboapp://"){
toastMsg('请在中羽在线APP里打开！');
return;
}
if(h==1){
window.open(u);//新窗口打开
}else{
location=''+u+'';//当前页打开
}
}
//关闭当前窗口 
function closeWin(){
history.back(-1);
}
//提示框 
function toastMsg(txt){
//alert(txt);
layer.open({
content: txt
,skin: 'msg'
,time: 2 //2秒后自动关闭
  });	
}

//加载框  t=1 隐藏
function showProgress(t){
}

//导航栏更多  t=1 隐藏
function showhideMore(t){
}
function isOkReload(){//返回刷新
}

function userCenter(id){
    //用户中心 userCenter(userId)
alert('请下载中羽在线APP使用');
jumpAppLink();
}
function login(){
var r = encodeURIComponent('https://www.badmintoncn.com/cbo_eq/view_buy.php?eid=22974');//多加一次编码，防止论坛那边无法转跳
location="https://bbs.badmintoncn.com/member.php?mod=logging&action=login&referer="+encodeURIComponent('https://bbs.badmintoncn.com/cbo_referer.php?r='+r);   
}
</script>

</body></html>