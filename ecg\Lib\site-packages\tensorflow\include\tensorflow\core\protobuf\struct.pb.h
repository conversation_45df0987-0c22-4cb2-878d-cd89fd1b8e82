// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/struct.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[11]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
namespace tensorflow {
class BoundedTensorSpecProto;
class BoundedTensorSpecProtoDefaultTypeInternal;
extern BoundedTensorSpecProtoDefaultTypeInternal _BoundedTensorSpecProto_default_instance_;
class DictValue;
class DictValueDefaultTypeInternal;
extern DictValueDefaultTypeInternal _DictValue_default_instance_;
class DictValue_FieldsEntry_DoNotUse;
class DictValue_FieldsEntry_DoNotUseDefaultTypeInternal;
extern DictValue_FieldsEntry_DoNotUseDefaultTypeInternal _DictValue_FieldsEntry_DoNotUse_default_instance_;
class ListValue;
class ListValueDefaultTypeInternal;
extern ListValueDefaultTypeInternal _ListValue_default_instance_;
class NamedTupleValue;
class NamedTupleValueDefaultTypeInternal;
extern NamedTupleValueDefaultTypeInternal _NamedTupleValue_default_instance_;
class NoneValue;
class NoneValueDefaultTypeInternal;
extern NoneValueDefaultTypeInternal _NoneValue_default_instance_;
class PairValue;
class PairValueDefaultTypeInternal;
extern PairValueDefaultTypeInternal _PairValue_default_instance_;
class StructuredValue;
class StructuredValueDefaultTypeInternal;
extern StructuredValueDefaultTypeInternal _StructuredValue_default_instance_;
class TensorSpecProto;
class TensorSpecProtoDefaultTypeInternal;
extern TensorSpecProtoDefaultTypeInternal _TensorSpecProto_default_instance_;
class TupleValue;
class TupleValueDefaultTypeInternal;
extern TupleValueDefaultTypeInternal _TupleValue_default_instance_;
class TypeSpecProto;
class TypeSpecProtoDefaultTypeInternal;
extern TypeSpecProtoDefaultTypeInternal _TypeSpecProto_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::BoundedTensorSpecProto* Arena::CreateMaybeMessage<::tensorflow::BoundedTensorSpecProto>(Arena*);
template<> ::tensorflow::DictValue* Arena::CreateMaybeMessage<::tensorflow::DictValue>(Arena*);
template<> ::tensorflow::DictValue_FieldsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::DictValue_FieldsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ListValue* Arena::CreateMaybeMessage<::tensorflow::ListValue>(Arena*);
template<> ::tensorflow::NamedTupleValue* Arena::CreateMaybeMessage<::tensorflow::NamedTupleValue>(Arena*);
template<> ::tensorflow::NoneValue* Arena::CreateMaybeMessage<::tensorflow::NoneValue>(Arena*);
template<> ::tensorflow::PairValue* Arena::CreateMaybeMessage<::tensorflow::PairValue>(Arena*);
template<> ::tensorflow::StructuredValue* Arena::CreateMaybeMessage<::tensorflow::StructuredValue>(Arena*);
template<> ::tensorflow::TensorSpecProto* Arena::CreateMaybeMessage<::tensorflow::TensorSpecProto>(Arena*);
template<> ::tensorflow::TupleValue* Arena::CreateMaybeMessage<::tensorflow::TupleValue>(Arena*);
template<> ::tensorflow::TypeSpecProto* Arena::CreateMaybeMessage<::tensorflow::TypeSpecProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum TypeSpecProto_TypeSpecClass : int {
  TypeSpecProto_TypeSpecClass_UNKNOWN = 0,
  TypeSpecProto_TypeSpecClass_SPARSE_TENSOR_SPEC = 1,
  TypeSpecProto_TypeSpecClass_INDEXED_SLICES_SPEC = 2,
  TypeSpecProto_TypeSpecClass_RAGGED_TENSOR_SPEC = 3,
  TypeSpecProto_TypeSpecClass_TENSOR_ARRAY_SPEC = 4,
  TypeSpecProto_TypeSpecClass_DATA_DATASET_SPEC = 5,
  TypeSpecProto_TypeSpecClass_DATA_ITERATOR_SPEC = 6,
  TypeSpecProto_TypeSpecClass_OPTIONAL_SPEC = 7,
  TypeSpecProto_TypeSpecClass_PER_REPLICA_SPEC = 8,
  TypeSpecProto_TypeSpecClass_VARIABLE_SPEC = 9,
  TypeSpecProto_TypeSpecClass_ROW_PARTITION_SPEC = 10,
  TypeSpecProto_TypeSpecClass_REGISTERED_TYPE_SPEC = 12,
  TypeSpecProto_TypeSpecClass_EXTENSION_TYPE_SPEC = 13,
  TypeSpecProto_TypeSpecClass_TypeSpecProto_TypeSpecClass_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  TypeSpecProto_TypeSpecClass_TypeSpecProto_TypeSpecClass_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool TypeSpecProto_TypeSpecClass_IsValid(int value);
constexpr TypeSpecProto_TypeSpecClass TypeSpecProto_TypeSpecClass_TypeSpecClass_MIN = TypeSpecProto_TypeSpecClass_UNKNOWN;
constexpr TypeSpecProto_TypeSpecClass TypeSpecProto_TypeSpecClass_TypeSpecClass_MAX = TypeSpecProto_TypeSpecClass_EXTENSION_TYPE_SPEC;
constexpr int TypeSpecProto_TypeSpecClass_TypeSpecClass_ARRAYSIZE = TypeSpecProto_TypeSpecClass_TypeSpecClass_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TypeSpecProto_TypeSpecClass_descriptor();
template<typename T>
inline const std::string& TypeSpecProto_TypeSpecClass_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TypeSpecProto_TypeSpecClass>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TypeSpecProto_TypeSpecClass_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TypeSpecProto_TypeSpecClass_descriptor(), enum_t_value);
}
inline bool TypeSpecProto_TypeSpecClass_Parse(
    const std::string& name, TypeSpecProto_TypeSpecClass* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TypeSpecProto_TypeSpecClass>(
    TypeSpecProto_TypeSpecClass_descriptor(), name, value);
}
// ===================================================================

class StructuredValue :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.StructuredValue) */ {
 public:
  StructuredValue();
  virtual ~StructuredValue();

  StructuredValue(const StructuredValue& from);
  StructuredValue(StructuredValue&& from) noexcept
    : StructuredValue() {
    *this = ::std::move(from);
  }

  inline StructuredValue& operator=(const StructuredValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline StructuredValue& operator=(StructuredValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StructuredValue& default_instance();

  enum KindCase {
    kNoneValue = 1,
    kFloat64Value = 11,
    kInt64Value = 12,
    kStringValue = 13,
    kBoolValue = 14,
    kTensorShapeValue = 31,
    kTensorDtypeValue = 32,
    kTensorSpecValue = 33,
    kTypeSpecValue = 34,
    kBoundedTensorSpecValue = 35,
    kListValue = 51,
    kTupleValue = 52,
    kDictValue = 53,
    kNamedTupleValue = 54,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StructuredValue* internal_default_instance() {
    return reinterpret_cast<const StructuredValue*>(
               &_StructuredValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(StructuredValue& a, StructuredValue& b) {
    a.Swap(&b);
  }
  inline void Swap(StructuredValue* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StructuredValue* New() const final {
    return CreateMaybeMessage<StructuredValue>(nullptr);
  }

  StructuredValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StructuredValue>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StructuredValue& from);
  void MergeFrom(const StructuredValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StructuredValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.StructuredValue";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNoneValueFieldNumber = 1,
    kFloat64ValueFieldNumber = 11,
    kInt64ValueFieldNumber = 12,
    kStringValueFieldNumber = 13,
    kBoolValueFieldNumber = 14,
    kTensorShapeValueFieldNumber = 31,
    kTensorDtypeValueFieldNumber = 32,
    kTensorSpecValueFieldNumber = 33,
    kTypeSpecValueFieldNumber = 34,
    kBoundedTensorSpecValueFieldNumber = 35,
    kListValueFieldNumber = 51,
    kTupleValueFieldNumber = 52,
    kDictValueFieldNumber = 53,
    kNamedTupleValueFieldNumber = 54,
  };
  // .tensorflow.NoneValue none_value = 1;
  bool has_none_value() const;
  void clear_none_value();
  const ::tensorflow::NoneValue& none_value() const;
  ::tensorflow::NoneValue* release_none_value();
  ::tensorflow::NoneValue* mutable_none_value();
  void set_allocated_none_value(::tensorflow::NoneValue* none_value);

  // double float64_value = 11;
  private:
  bool has_float64_value() const;
  public:
  void clear_float64_value();
  double float64_value() const;
  void set_float64_value(double value);

  // sint64 int64_value = 12;
  private:
  bool has_int64_value() const;
  public:
  void clear_int64_value();
  ::PROTOBUF_NAMESPACE_ID::int64 int64_value() const;
  void set_int64_value(::PROTOBUF_NAMESPACE_ID::int64 value);

  // string string_value = 13;
  private:
  bool has_string_value() const;
  public:
  void clear_string_value();
  const std::string& string_value() const;
  void set_string_value(const std::string& value);
  void set_string_value(std::string&& value);
  void set_string_value(const char* value);
  void set_string_value(const char* value, size_t size);
  std::string* mutable_string_value();
  std::string* release_string_value();
  void set_allocated_string_value(std::string* string_value);

  // bool bool_value = 14;
  private:
  bool has_bool_value() const;
  public:
  void clear_bool_value();
  bool bool_value() const;
  void set_bool_value(bool value);

  // .tensorflow.TensorShapeProto tensor_shape_value = 31;
  bool has_tensor_shape_value() const;
  void clear_tensor_shape_value();
  const ::tensorflow::TensorShapeProto& tensor_shape_value() const;
  ::tensorflow::TensorShapeProto* release_tensor_shape_value();
  ::tensorflow::TensorShapeProto* mutable_tensor_shape_value();
  void set_allocated_tensor_shape_value(::tensorflow::TensorShapeProto* tensor_shape_value);

  // .tensorflow.DataType tensor_dtype_value = 32;
  private:
  bool has_tensor_dtype_value() const;
  public:
  void clear_tensor_dtype_value();
  ::tensorflow::DataType tensor_dtype_value() const;
  void set_tensor_dtype_value(::tensorflow::DataType value);

  // .tensorflow.TensorSpecProto tensor_spec_value = 33;
  bool has_tensor_spec_value() const;
  void clear_tensor_spec_value();
  const ::tensorflow::TensorSpecProto& tensor_spec_value() const;
  ::tensorflow::TensorSpecProto* release_tensor_spec_value();
  ::tensorflow::TensorSpecProto* mutable_tensor_spec_value();
  void set_allocated_tensor_spec_value(::tensorflow::TensorSpecProto* tensor_spec_value);

  // .tensorflow.TypeSpecProto type_spec_value = 34;
  bool has_type_spec_value() const;
  void clear_type_spec_value();
  const ::tensorflow::TypeSpecProto& type_spec_value() const;
  ::tensorflow::TypeSpecProto* release_type_spec_value();
  ::tensorflow::TypeSpecProto* mutable_type_spec_value();
  void set_allocated_type_spec_value(::tensorflow::TypeSpecProto* type_spec_value);

  // .tensorflow.BoundedTensorSpecProto bounded_tensor_spec_value = 35;
  bool has_bounded_tensor_spec_value() const;
  void clear_bounded_tensor_spec_value();
  const ::tensorflow::BoundedTensorSpecProto& bounded_tensor_spec_value() const;
  ::tensorflow::BoundedTensorSpecProto* release_bounded_tensor_spec_value();
  ::tensorflow::BoundedTensorSpecProto* mutable_bounded_tensor_spec_value();
  void set_allocated_bounded_tensor_spec_value(::tensorflow::BoundedTensorSpecProto* bounded_tensor_spec_value);

  // .tensorflow.ListValue list_value = 51;
  bool has_list_value() const;
  void clear_list_value();
  const ::tensorflow::ListValue& list_value() const;
  ::tensorflow::ListValue* release_list_value();
  ::tensorflow::ListValue* mutable_list_value();
  void set_allocated_list_value(::tensorflow::ListValue* list_value);

  // .tensorflow.TupleValue tuple_value = 52;
  bool has_tuple_value() const;
  void clear_tuple_value();
  const ::tensorflow::TupleValue& tuple_value() const;
  ::tensorflow::TupleValue* release_tuple_value();
  ::tensorflow::TupleValue* mutable_tuple_value();
  void set_allocated_tuple_value(::tensorflow::TupleValue* tuple_value);

  // .tensorflow.DictValue dict_value = 53;
  bool has_dict_value() const;
  void clear_dict_value();
  const ::tensorflow::DictValue& dict_value() const;
  ::tensorflow::DictValue* release_dict_value();
  ::tensorflow::DictValue* mutable_dict_value();
  void set_allocated_dict_value(::tensorflow::DictValue* dict_value);

  // .tensorflow.NamedTupleValue named_tuple_value = 54;
  bool has_named_tuple_value() const;
  void clear_named_tuple_value();
  const ::tensorflow::NamedTupleValue& named_tuple_value() const;
  ::tensorflow::NamedTupleValue* release_named_tuple_value();
  ::tensorflow::NamedTupleValue* mutable_named_tuple_value();
  void set_allocated_named_tuple_value(::tensorflow::NamedTupleValue* named_tuple_value);

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.StructuredValue)
 private:
  class _Internal;
  void set_has_none_value();
  void set_has_float64_value();
  void set_has_int64_value();
  void set_has_string_value();
  void set_has_bool_value();
  void set_has_tensor_shape_value();
  void set_has_tensor_dtype_value();
  void set_has_tensor_spec_value();
  void set_has_type_spec_value();
  void set_has_bounded_tensor_spec_value();
  void set_has_list_value();
  void set_has_tuple_value();
  void set_has_dict_value();
  void set_has_named_tuple_value();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  union KindUnion {
    KindUnion() {}
    ::tensorflow::NoneValue* none_value_;
    double float64_value_;
    ::PROTOBUF_NAMESPACE_ID::int64 int64_value_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_value_;
    bool bool_value_;
    ::tensorflow::TensorShapeProto* tensor_shape_value_;
    int tensor_dtype_value_;
    ::tensorflow::TensorSpecProto* tensor_spec_value_;
    ::tensorflow::TypeSpecProto* type_spec_value_;
    ::tensorflow::BoundedTensorSpecProto* bounded_tensor_spec_value_;
    ::tensorflow::ListValue* list_value_;
    ::tensorflow::TupleValue* tuple_value_;
    ::tensorflow::DictValue* dict_value_;
    ::tensorflow::NamedTupleValue* named_tuple_value_;
  } kind_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class NoneValue :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NoneValue) */ {
 public:
  NoneValue();
  virtual ~NoneValue();

  NoneValue(const NoneValue& from);
  NoneValue(NoneValue&& from) noexcept
    : NoneValue() {
    *this = ::std::move(from);
  }

  inline NoneValue& operator=(const NoneValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline NoneValue& operator=(NoneValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NoneValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NoneValue* internal_default_instance() {
    return reinterpret_cast<const NoneValue*>(
               &_NoneValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(NoneValue& a, NoneValue& b) {
    a.Swap(&b);
  }
  inline void Swap(NoneValue* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NoneValue* New() const final {
    return CreateMaybeMessage<NoneValue>(nullptr);
  }

  NoneValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NoneValue>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NoneValue& from);
  void MergeFrom(const NoneValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NoneValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NoneValue";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.NoneValue)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class ListValue :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ListValue) */ {
 public:
  ListValue();
  virtual ~ListValue();

  ListValue(const ListValue& from);
  ListValue(ListValue&& from) noexcept
    : ListValue() {
    *this = ::std::move(from);
  }

  inline ListValue& operator=(const ListValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListValue& operator=(ListValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ListValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ListValue* internal_default_instance() {
    return reinterpret_cast<const ListValue*>(
               &_ListValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ListValue& a, ListValue& b) {
    a.Swap(&b);
  }
  inline void Swap(ListValue* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ListValue* New() const final {
    return CreateMaybeMessage<ListValue>(nullptr);
  }

  ListValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ListValue>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ListValue& from);
  void MergeFrom(const ListValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ListValue";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesFieldNumber = 1,
  };
  // repeated .tensorflow.StructuredValue values = 1;
  int values_size() const;
  void clear_values();
  ::tensorflow::StructuredValue* mutable_values(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >*
      mutable_values();
  const ::tensorflow::StructuredValue& values(int index) const;
  ::tensorflow::StructuredValue* add_values();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >&
      values() const;

  // @@protoc_insertion_point(class_scope:tensorflow.ListValue)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue > values_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class TupleValue :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TupleValue) */ {
 public:
  TupleValue();
  virtual ~TupleValue();

  TupleValue(const TupleValue& from);
  TupleValue(TupleValue&& from) noexcept
    : TupleValue() {
    *this = ::std::move(from);
  }

  inline TupleValue& operator=(const TupleValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline TupleValue& operator=(TupleValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TupleValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TupleValue* internal_default_instance() {
    return reinterpret_cast<const TupleValue*>(
               &_TupleValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TupleValue& a, TupleValue& b) {
    a.Swap(&b);
  }
  inline void Swap(TupleValue* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TupleValue* New() const final {
    return CreateMaybeMessage<TupleValue>(nullptr);
  }

  TupleValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TupleValue>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TupleValue& from);
  void MergeFrom(const TupleValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TupleValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TupleValue";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesFieldNumber = 1,
  };
  // repeated .tensorflow.StructuredValue values = 1;
  int values_size() const;
  void clear_values();
  ::tensorflow::StructuredValue* mutable_values(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >*
      mutable_values();
  const ::tensorflow::StructuredValue& values(int index) const;
  ::tensorflow::StructuredValue* add_values();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >&
      values() const;

  // @@protoc_insertion_point(class_scope:tensorflow.TupleValue)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue > values_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class DictValue_FieldsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DictValue_FieldsEntry_DoNotUse, 
    std::string, ::tensorflow::StructuredValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DictValue_FieldsEntry_DoNotUse, 
    std::string, ::tensorflow::StructuredValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  DictValue_FieldsEntry_DoNotUse();
  DictValue_FieldsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DictValue_FieldsEntry_DoNotUse& other);
  static const DictValue_FieldsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DictValue_FieldsEntry_DoNotUse*>(&_DictValue_FieldsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.DictValue.FieldsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[4];
  }

  public:
};

// -------------------------------------------------------------------

class DictValue :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DictValue) */ {
 public:
  DictValue();
  virtual ~DictValue();

  DictValue(const DictValue& from);
  DictValue(DictValue&& from) noexcept
    : DictValue() {
    *this = ::std::move(from);
  }

  inline DictValue& operator=(const DictValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline DictValue& operator=(DictValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DictValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DictValue* internal_default_instance() {
    return reinterpret_cast<const DictValue*>(
               &_DictValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(DictValue& a, DictValue& b) {
    a.Swap(&b);
  }
  inline void Swap(DictValue* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DictValue* New() const final {
    return CreateMaybeMessage<DictValue>(nullptr);
  }

  DictValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DictValue>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DictValue& from);
  void MergeFrom(const DictValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DictValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DictValue";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFieldsFieldNumber = 1,
  };
  // map<string, .tensorflow.StructuredValue> fields = 1;
  int fields_size() const;
  void clear_fields();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >&
      fields() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >*
      mutable_fields();

  // @@protoc_insertion_point(class_scope:tensorflow.DictValue)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      DictValue_FieldsEntry_DoNotUse,
      std::string, ::tensorflow::StructuredValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > fields_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class PairValue :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.PairValue) */ {
 public:
  PairValue();
  virtual ~PairValue();

  PairValue(const PairValue& from);
  PairValue(PairValue&& from) noexcept
    : PairValue() {
    *this = ::std::move(from);
  }

  inline PairValue& operator=(const PairValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline PairValue& operator=(PairValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PairValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PairValue* internal_default_instance() {
    return reinterpret_cast<const PairValue*>(
               &_PairValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(PairValue& a, PairValue& b) {
    a.Swap(&b);
  }
  inline void Swap(PairValue* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PairValue* New() const final {
    return CreateMaybeMessage<PairValue>(nullptr);
  }

  PairValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PairValue>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PairValue& from);
  void MergeFrom(const PairValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PairValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.PairValue";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  void set_key(const std::string& value);
  void set_key(std::string&& value);
  void set_key(const char* value);
  void set_key(const char* value, size_t size);
  std::string* mutable_key();
  std::string* release_key();
  void set_allocated_key(std::string* key);

  // .tensorflow.StructuredValue value = 2;
  bool has_value() const;
  void clear_value();
  const ::tensorflow::StructuredValue& value() const;
  ::tensorflow::StructuredValue* release_value();
  ::tensorflow::StructuredValue* mutable_value();
  void set_allocated_value(::tensorflow::StructuredValue* value);

  // @@protoc_insertion_point(class_scope:tensorflow.PairValue)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  ::tensorflow::StructuredValue* value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class NamedTupleValue :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NamedTupleValue) */ {
 public:
  NamedTupleValue();
  virtual ~NamedTupleValue();

  NamedTupleValue(const NamedTupleValue& from);
  NamedTupleValue(NamedTupleValue&& from) noexcept
    : NamedTupleValue() {
    *this = ::std::move(from);
  }

  inline NamedTupleValue& operator=(const NamedTupleValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline NamedTupleValue& operator=(NamedTupleValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NamedTupleValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NamedTupleValue* internal_default_instance() {
    return reinterpret_cast<const NamedTupleValue*>(
               &_NamedTupleValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(NamedTupleValue& a, NamedTupleValue& b) {
    a.Swap(&b);
  }
  inline void Swap(NamedTupleValue* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NamedTupleValue* New() const final {
    return CreateMaybeMessage<NamedTupleValue>(nullptr);
  }

  NamedTupleValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NamedTupleValue>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NamedTupleValue& from);
  void MergeFrom(const NamedTupleValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NamedTupleValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NamedTupleValue";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // repeated .tensorflow.PairValue values = 2;
  int values_size() const;
  void clear_values();
  ::tensorflow::PairValue* mutable_values(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::PairValue >*
      mutable_values();
  const ::tensorflow::PairValue& values(int index) const;
  ::tensorflow::PairValue* add_values();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::PairValue >&
      values() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // @@protoc_insertion_point(class_scope:tensorflow.NamedTupleValue)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::PairValue > values_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class TensorSpecProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorSpecProto) */ {
 public:
  TensorSpecProto();
  virtual ~TensorSpecProto();

  TensorSpecProto(const TensorSpecProto& from);
  TensorSpecProto(TensorSpecProto&& from) noexcept
    : TensorSpecProto() {
    *this = ::std::move(from);
  }

  inline TensorSpecProto& operator=(const TensorSpecProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorSpecProto& operator=(TensorSpecProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TensorSpecProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorSpecProto* internal_default_instance() {
    return reinterpret_cast<const TensorSpecProto*>(
               &_TensorSpecProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(TensorSpecProto& a, TensorSpecProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorSpecProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TensorSpecProto* New() const final {
    return CreateMaybeMessage<TensorSpecProto>(nullptr);
  }

  TensorSpecProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TensorSpecProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TensorSpecProto& from);
  void MergeFrom(const TensorSpecProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorSpecProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TensorSpecProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kShapeFieldNumber = 2,
    kDtypeFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);

  // .tensorflow.DataType dtype = 3;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.TensorSpecProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::TensorShapeProto* shape_;
  int dtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class BoundedTensorSpecProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BoundedTensorSpecProto) */ {
 public:
  BoundedTensorSpecProto();
  virtual ~BoundedTensorSpecProto();

  BoundedTensorSpecProto(const BoundedTensorSpecProto& from);
  BoundedTensorSpecProto(BoundedTensorSpecProto&& from) noexcept
    : BoundedTensorSpecProto() {
    *this = ::std::move(from);
  }

  inline BoundedTensorSpecProto& operator=(const BoundedTensorSpecProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline BoundedTensorSpecProto& operator=(BoundedTensorSpecProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BoundedTensorSpecProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BoundedTensorSpecProto* internal_default_instance() {
    return reinterpret_cast<const BoundedTensorSpecProto*>(
               &_BoundedTensorSpecProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(BoundedTensorSpecProto& a, BoundedTensorSpecProto& b) {
    a.Swap(&b);
  }
  inline void Swap(BoundedTensorSpecProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BoundedTensorSpecProto* New() const final {
    return CreateMaybeMessage<BoundedTensorSpecProto>(nullptr);
  }

  BoundedTensorSpecProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BoundedTensorSpecProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BoundedTensorSpecProto& from);
  void MergeFrom(const BoundedTensorSpecProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BoundedTensorSpecProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BoundedTensorSpecProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kShapeFieldNumber = 2,
    kMinimumFieldNumber = 4,
    kMaximumFieldNumber = 5,
    kDtypeFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);

  // .tensorflow.TensorProto minimum = 4;
  bool has_minimum() const;
  void clear_minimum();
  const ::tensorflow::TensorProto& minimum() const;
  ::tensorflow::TensorProto* release_minimum();
  ::tensorflow::TensorProto* mutable_minimum();
  void set_allocated_minimum(::tensorflow::TensorProto* minimum);

  // .tensorflow.TensorProto maximum = 5;
  bool has_maximum() const;
  void clear_maximum();
  const ::tensorflow::TensorProto& maximum() const;
  ::tensorflow::TensorProto* release_maximum();
  ::tensorflow::TensorProto* mutable_maximum();
  void set_allocated_maximum(::tensorflow::TensorProto* maximum);

  // .tensorflow.DataType dtype = 3;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.BoundedTensorSpecProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::TensorShapeProto* shape_;
  ::tensorflow::TensorProto* minimum_;
  ::tensorflow::TensorProto* maximum_;
  int dtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class TypeSpecProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TypeSpecProto) */ {
 public:
  TypeSpecProto();
  virtual ~TypeSpecProto();

  TypeSpecProto(const TypeSpecProto& from);
  TypeSpecProto(TypeSpecProto&& from) noexcept
    : TypeSpecProto() {
    *this = ::std::move(from);
  }

  inline TypeSpecProto& operator=(const TypeSpecProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeSpecProto& operator=(TypeSpecProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TypeSpecProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TypeSpecProto* internal_default_instance() {
    return reinterpret_cast<const TypeSpecProto*>(
               &_TypeSpecProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(TypeSpecProto& a, TypeSpecProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeSpecProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TypeSpecProto* New() const final {
    return CreateMaybeMessage<TypeSpecProto>(nullptr);
  }

  TypeSpecProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TypeSpecProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TypeSpecProto& from);
  void MergeFrom(const TypeSpecProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeSpecProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TypeSpecProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TypeSpecProto_TypeSpecClass TypeSpecClass;
  static constexpr TypeSpecClass UNKNOWN =
    TypeSpecProto_TypeSpecClass_UNKNOWN;
  static constexpr TypeSpecClass SPARSE_TENSOR_SPEC =
    TypeSpecProto_TypeSpecClass_SPARSE_TENSOR_SPEC;
  static constexpr TypeSpecClass INDEXED_SLICES_SPEC =
    TypeSpecProto_TypeSpecClass_INDEXED_SLICES_SPEC;
  static constexpr TypeSpecClass RAGGED_TENSOR_SPEC =
    TypeSpecProto_TypeSpecClass_RAGGED_TENSOR_SPEC;
  static constexpr TypeSpecClass TENSOR_ARRAY_SPEC =
    TypeSpecProto_TypeSpecClass_TENSOR_ARRAY_SPEC;
  static constexpr TypeSpecClass DATA_DATASET_SPEC =
    TypeSpecProto_TypeSpecClass_DATA_DATASET_SPEC;
  static constexpr TypeSpecClass DATA_ITERATOR_SPEC =
    TypeSpecProto_TypeSpecClass_DATA_ITERATOR_SPEC;
  static constexpr TypeSpecClass OPTIONAL_SPEC =
    TypeSpecProto_TypeSpecClass_OPTIONAL_SPEC;
  static constexpr TypeSpecClass PER_REPLICA_SPEC =
    TypeSpecProto_TypeSpecClass_PER_REPLICA_SPEC;
  static constexpr TypeSpecClass VARIABLE_SPEC =
    TypeSpecProto_TypeSpecClass_VARIABLE_SPEC;
  static constexpr TypeSpecClass ROW_PARTITION_SPEC =
    TypeSpecProto_TypeSpecClass_ROW_PARTITION_SPEC;
  static constexpr TypeSpecClass REGISTERED_TYPE_SPEC =
    TypeSpecProto_TypeSpecClass_REGISTERED_TYPE_SPEC;
  static constexpr TypeSpecClass EXTENSION_TYPE_SPEC =
    TypeSpecProto_TypeSpecClass_EXTENSION_TYPE_SPEC;
  static inline bool TypeSpecClass_IsValid(int value) {
    return TypeSpecProto_TypeSpecClass_IsValid(value);
  }
  static constexpr TypeSpecClass TypeSpecClass_MIN =
    TypeSpecProto_TypeSpecClass_TypeSpecClass_MIN;
  static constexpr TypeSpecClass TypeSpecClass_MAX =
    TypeSpecProto_TypeSpecClass_TypeSpecClass_MAX;
  static constexpr int TypeSpecClass_ARRAYSIZE =
    TypeSpecProto_TypeSpecClass_TypeSpecClass_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  TypeSpecClass_descriptor() {
    return TypeSpecProto_TypeSpecClass_descriptor();
  }
  template<typename T>
  static inline const std::string& TypeSpecClass_Name(T enum_t_value) {
    static_assert(::std::is_same<T, TypeSpecClass>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function TypeSpecClass_Name.");
    return TypeSpecProto_TypeSpecClass_Name(enum_t_value);
  }
  static inline bool TypeSpecClass_Parse(const std::string& name,
      TypeSpecClass* value) {
    return TypeSpecProto_TypeSpecClass_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTypeSpecClassNameFieldNumber = 3,
    kTypeStateFieldNumber = 2,
    kTypeSpecClassFieldNumber = 1,
  };
  // string type_spec_class_name = 3;
  void clear_type_spec_class_name();
  const std::string& type_spec_class_name() const;
  void set_type_spec_class_name(const std::string& value);
  void set_type_spec_class_name(std::string&& value);
  void set_type_spec_class_name(const char* value);
  void set_type_spec_class_name(const char* value, size_t size);
  std::string* mutable_type_spec_class_name();
  std::string* release_type_spec_class_name();
  void set_allocated_type_spec_class_name(std::string* type_spec_class_name);

  // .tensorflow.StructuredValue type_state = 2;
  bool has_type_state() const;
  void clear_type_state();
  const ::tensorflow::StructuredValue& type_state() const;
  ::tensorflow::StructuredValue* release_type_state();
  ::tensorflow::StructuredValue* mutable_type_state();
  void set_allocated_type_state(::tensorflow::StructuredValue* type_state);

  // .tensorflow.TypeSpecProto.TypeSpecClass type_spec_class = 1;
  void clear_type_spec_class();
  ::tensorflow::TypeSpecProto_TypeSpecClass type_spec_class() const;
  void set_type_spec_class(::tensorflow::TypeSpecProto_TypeSpecClass value);

  // @@protoc_insertion_point(class_scope:tensorflow.TypeSpecProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_spec_class_name_;
  ::tensorflow::StructuredValue* type_state_;
  int type_spec_class_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// StructuredValue

// .tensorflow.NoneValue none_value = 1;
inline bool StructuredValue::has_none_value() const {
  return kind_case() == kNoneValue;
}
inline void StructuredValue::set_has_none_value() {
  _oneof_case_[0] = kNoneValue;
}
inline void StructuredValue::clear_none_value() {
  if (has_none_value()) {
    delete kind_.none_value_;
    clear_has_kind();
  }
}
inline ::tensorflow::NoneValue* StructuredValue::release_none_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.none_value)
  if (has_none_value()) {
    clear_has_kind();
      ::tensorflow::NoneValue* temp = kind_.none_value_;
    kind_.none_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::NoneValue& StructuredValue::none_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.none_value)
  return has_none_value()
      ? *kind_.none_value_
      : *reinterpret_cast< ::tensorflow::NoneValue*>(&::tensorflow::_NoneValue_default_instance_);
}
inline ::tensorflow::NoneValue* StructuredValue::mutable_none_value() {
  if (!has_none_value()) {
    clear_kind();
    set_has_none_value();
    kind_.none_value_ = CreateMaybeMessage< ::tensorflow::NoneValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.none_value)
  return kind_.none_value_;
}

// double float64_value = 11;
inline bool StructuredValue::has_float64_value() const {
  return kind_case() == kFloat64Value;
}
inline void StructuredValue::set_has_float64_value() {
  _oneof_case_[0] = kFloat64Value;
}
inline void StructuredValue::clear_float64_value() {
  if (has_float64_value()) {
    kind_.float64_value_ = 0;
    clear_has_kind();
  }
}
inline double StructuredValue::float64_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.float64_value)
  if (has_float64_value()) {
    return kind_.float64_value_;
  }
  return 0;
}
inline void StructuredValue::set_float64_value(double value) {
  if (!has_float64_value()) {
    clear_kind();
    set_has_float64_value();
  }
  kind_.float64_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.float64_value)
}

// sint64 int64_value = 12;
inline bool StructuredValue::has_int64_value() const {
  return kind_case() == kInt64Value;
}
inline void StructuredValue::set_has_int64_value() {
  _oneof_case_[0] = kInt64Value;
}
inline void StructuredValue::clear_int64_value() {
  if (has_int64_value()) {
    kind_.int64_value_ = PROTOBUF_LONGLONG(0);
    clear_has_kind();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int64 StructuredValue::int64_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.int64_value)
  if (has_int64_value()) {
    return kind_.int64_value_;
  }
  return PROTOBUF_LONGLONG(0);
}
inline void StructuredValue::set_int64_value(::PROTOBUF_NAMESPACE_ID::int64 value) {
  if (!has_int64_value()) {
    clear_kind();
    set_has_int64_value();
  }
  kind_.int64_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.int64_value)
}

// string string_value = 13;
inline bool StructuredValue::has_string_value() const {
  return kind_case() == kStringValue;
}
inline void StructuredValue::set_has_string_value() {
  _oneof_case_[0] = kStringValue;
}
inline void StructuredValue::clear_string_value() {
  if (has_string_value()) {
    kind_.string_value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
    clear_has_kind();
  }
}
inline const std::string& StructuredValue::string_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.string_value)
  if (has_string_value()) {
    return kind_.string_value_.GetNoArena();
  }
  return *&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void StructuredValue::set_string_value(const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.string_value)
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.string_value)
}
inline void StructuredValue::set_string_value(std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.string_value)
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.StructuredValue.string_value)
}
inline void StructuredValue::set_string_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.StructuredValue.string_value)
}
inline void StructuredValue::set_string_value(const char* value, size_t size) {
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.StructuredValue.string_value)
}
inline std::string* StructuredValue::mutable_string_value() {
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.string_value)
  return kind_.string_value_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* StructuredValue::release_string_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.string_value)
  if (has_string_value()) {
    clear_has_kind();
    return kind_.string_value_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  } else {
    return nullptr;
  }
}
inline void StructuredValue::set_allocated_string_value(std::string* string_value) {
  if (has_kind()) {
    clear_kind();
  }
  if (string_value != nullptr) {
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(string_value);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StructuredValue.string_value)
}

// bool bool_value = 14;
inline bool StructuredValue::has_bool_value() const {
  return kind_case() == kBoolValue;
}
inline void StructuredValue::set_has_bool_value() {
  _oneof_case_[0] = kBoolValue;
}
inline void StructuredValue::clear_bool_value() {
  if (has_bool_value()) {
    kind_.bool_value_ = false;
    clear_has_kind();
  }
}
inline bool StructuredValue::bool_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.bool_value)
  if (has_bool_value()) {
    return kind_.bool_value_;
  }
  return false;
}
inline void StructuredValue::set_bool_value(bool value) {
  if (!has_bool_value()) {
    clear_kind();
    set_has_bool_value();
  }
  kind_.bool_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.bool_value)
}

// .tensorflow.TensorShapeProto tensor_shape_value = 31;
inline bool StructuredValue::has_tensor_shape_value() const {
  return kind_case() == kTensorShapeValue;
}
inline void StructuredValue::set_has_tensor_shape_value() {
  _oneof_case_[0] = kTensorShapeValue;
}
inline ::tensorflow::TensorShapeProto* StructuredValue::release_tensor_shape_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.tensor_shape_value)
  if (has_tensor_shape_value()) {
    clear_has_kind();
      ::tensorflow::TensorShapeProto* temp = kind_.tensor_shape_value_;
    kind_.tensor_shape_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorShapeProto& StructuredValue::tensor_shape_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tensor_shape_value)
  return has_tensor_shape_value()
      ? *kind_.tensor_shape_value_
      : *reinterpret_cast< ::tensorflow::TensorShapeProto*>(&::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* StructuredValue::mutable_tensor_shape_value() {
  if (!has_tensor_shape_value()) {
    clear_kind();
    set_has_tensor_shape_value();
    kind_.tensor_shape_value_ = CreateMaybeMessage< ::tensorflow::TensorShapeProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.tensor_shape_value)
  return kind_.tensor_shape_value_;
}

// .tensorflow.DataType tensor_dtype_value = 32;
inline bool StructuredValue::has_tensor_dtype_value() const {
  return kind_case() == kTensorDtypeValue;
}
inline void StructuredValue::set_has_tensor_dtype_value() {
  _oneof_case_[0] = kTensorDtypeValue;
}
inline void StructuredValue::clear_tensor_dtype_value() {
  if (has_tensor_dtype_value()) {
    kind_.tensor_dtype_value_ = 0;
    clear_has_kind();
  }
}
inline ::tensorflow::DataType StructuredValue::tensor_dtype_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tensor_dtype_value)
  if (has_tensor_dtype_value()) {
    return static_cast< ::tensorflow::DataType >(kind_.tensor_dtype_value_);
  }
  return static_cast< ::tensorflow::DataType >(0);
}
inline void StructuredValue::set_tensor_dtype_value(::tensorflow::DataType value) {
  if (!has_tensor_dtype_value()) {
    clear_kind();
    set_has_tensor_dtype_value();
  }
  kind_.tensor_dtype_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.tensor_dtype_value)
}

// .tensorflow.TensorSpecProto tensor_spec_value = 33;
inline bool StructuredValue::has_tensor_spec_value() const {
  return kind_case() == kTensorSpecValue;
}
inline void StructuredValue::set_has_tensor_spec_value() {
  _oneof_case_[0] = kTensorSpecValue;
}
inline void StructuredValue::clear_tensor_spec_value() {
  if (has_tensor_spec_value()) {
    delete kind_.tensor_spec_value_;
    clear_has_kind();
  }
}
inline ::tensorflow::TensorSpecProto* StructuredValue::release_tensor_spec_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.tensor_spec_value)
  if (has_tensor_spec_value()) {
    clear_has_kind();
      ::tensorflow::TensorSpecProto* temp = kind_.tensor_spec_value_;
    kind_.tensor_spec_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorSpecProto& StructuredValue::tensor_spec_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tensor_spec_value)
  return has_tensor_spec_value()
      ? *kind_.tensor_spec_value_
      : *reinterpret_cast< ::tensorflow::TensorSpecProto*>(&::tensorflow::_TensorSpecProto_default_instance_);
}
inline ::tensorflow::TensorSpecProto* StructuredValue::mutable_tensor_spec_value() {
  if (!has_tensor_spec_value()) {
    clear_kind();
    set_has_tensor_spec_value();
    kind_.tensor_spec_value_ = CreateMaybeMessage< ::tensorflow::TensorSpecProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.tensor_spec_value)
  return kind_.tensor_spec_value_;
}

// .tensorflow.TypeSpecProto type_spec_value = 34;
inline bool StructuredValue::has_type_spec_value() const {
  return kind_case() == kTypeSpecValue;
}
inline void StructuredValue::set_has_type_spec_value() {
  _oneof_case_[0] = kTypeSpecValue;
}
inline void StructuredValue::clear_type_spec_value() {
  if (has_type_spec_value()) {
    delete kind_.type_spec_value_;
    clear_has_kind();
  }
}
inline ::tensorflow::TypeSpecProto* StructuredValue::release_type_spec_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.type_spec_value)
  if (has_type_spec_value()) {
    clear_has_kind();
      ::tensorflow::TypeSpecProto* temp = kind_.type_spec_value_;
    kind_.type_spec_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TypeSpecProto& StructuredValue::type_spec_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.type_spec_value)
  return has_type_spec_value()
      ? *kind_.type_spec_value_
      : *reinterpret_cast< ::tensorflow::TypeSpecProto*>(&::tensorflow::_TypeSpecProto_default_instance_);
}
inline ::tensorflow::TypeSpecProto* StructuredValue::mutable_type_spec_value() {
  if (!has_type_spec_value()) {
    clear_kind();
    set_has_type_spec_value();
    kind_.type_spec_value_ = CreateMaybeMessage< ::tensorflow::TypeSpecProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.type_spec_value)
  return kind_.type_spec_value_;
}

// .tensorflow.BoundedTensorSpecProto bounded_tensor_spec_value = 35;
inline bool StructuredValue::has_bounded_tensor_spec_value() const {
  return kind_case() == kBoundedTensorSpecValue;
}
inline void StructuredValue::set_has_bounded_tensor_spec_value() {
  _oneof_case_[0] = kBoundedTensorSpecValue;
}
inline void StructuredValue::clear_bounded_tensor_spec_value() {
  if (has_bounded_tensor_spec_value()) {
    delete kind_.bounded_tensor_spec_value_;
    clear_has_kind();
  }
}
inline ::tensorflow::BoundedTensorSpecProto* StructuredValue::release_bounded_tensor_spec_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.bounded_tensor_spec_value)
  if (has_bounded_tensor_spec_value()) {
    clear_has_kind();
      ::tensorflow::BoundedTensorSpecProto* temp = kind_.bounded_tensor_spec_value_;
    kind_.bounded_tensor_spec_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::BoundedTensorSpecProto& StructuredValue::bounded_tensor_spec_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.bounded_tensor_spec_value)
  return has_bounded_tensor_spec_value()
      ? *kind_.bounded_tensor_spec_value_
      : *reinterpret_cast< ::tensorflow::BoundedTensorSpecProto*>(&::tensorflow::_BoundedTensorSpecProto_default_instance_);
}
inline ::tensorflow::BoundedTensorSpecProto* StructuredValue::mutable_bounded_tensor_spec_value() {
  if (!has_bounded_tensor_spec_value()) {
    clear_kind();
    set_has_bounded_tensor_spec_value();
    kind_.bounded_tensor_spec_value_ = CreateMaybeMessage< ::tensorflow::BoundedTensorSpecProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.bounded_tensor_spec_value)
  return kind_.bounded_tensor_spec_value_;
}

// .tensorflow.ListValue list_value = 51;
inline bool StructuredValue::has_list_value() const {
  return kind_case() == kListValue;
}
inline void StructuredValue::set_has_list_value() {
  _oneof_case_[0] = kListValue;
}
inline void StructuredValue::clear_list_value() {
  if (has_list_value()) {
    delete kind_.list_value_;
    clear_has_kind();
  }
}
inline ::tensorflow::ListValue* StructuredValue::release_list_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.list_value)
  if (has_list_value()) {
    clear_has_kind();
      ::tensorflow::ListValue* temp = kind_.list_value_;
    kind_.list_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ListValue& StructuredValue::list_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.list_value)
  return has_list_value()
      ? *kind_.list_value_
      : *reinterpret_cast< ::tensorflow::ListValue*>(&::tensorflow::_ListValue_default_instance_);
}
inline ::tensorflow::ListValue* StructuredValue::mutable_list_value() {
  if (!has_list_value()) {
    clear_kind();
    set_has_list_value();
    kind_.list_value_ = CreateMaybeMessage< ::tensorflow::ListValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.list_value)
  return kind_.list_value_;
}

// .tensorflow.TupleValue tuple_value = 52;
inline bool StructuredValue::has_tuple_value() const {
  return kind_case() == kTupleValue;
}
inline void StructuredValue::set_has_tuple_value() {
  _oneof_case_[0] = kTupleValue;
}
inline void StructuredValue::clear_tuple_value() {
  if (has_tuple_value()) {
    delete kind_.tuple_value_;
    clear_has_kind();
  }
}
inline ::tensorflow::TupleValue* StructuredValue::release_tuple_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.tuple_value)
  if (has_tuple_value()) {
    clear_has_kind();
      ::tensorflow::TupleValue* temp = kind_.tuple_value_;
    kind_.tuple_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TupleValue& StructuredValue::tuple_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tuple_value)
  return has_tuple_value()
      ? *kind_.tuple_value_
      : *reinterpret_cast< ::tensorflow::TupleValue*>(&::tensorflow::_TupleValue_default_instance_);
}
inline ::tensorflow::TupleValue* StructuredValue::mutable_tuple_value() {
  if (!has_tuple_value()) {
    clear_kind();
    set_has_tuple_value();
    kind_.tuple_value_ = CreateMaybeMessage< ::tensorflow::TupleValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.tuple_value)
  return kind_.tuple_value_;
}

// .tensorflow.DictValue dict_value = 53;
inline bool StructuredValue::has_dict_value() const {
  return kind_case() == kDictValue;
}
inline void StructuredValue::set_has_dict_value() {
  _oneof_case_[0] = kDictValue;
}
inline void StructuredValue::clear_dict_value() {
  if (has_dict_value()) {
    delete kind_.dict_value_;
    clear_has_kind();
  }
}
inline ::tensorflow::DictValue* StructuredValue::release_dict_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.dict_value)
  if (has_dict_value()) {
    clear_has_kind();
      ::tensorflow::DictValue* temp = kind_.dict_value_;
    kind_.dict_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::DictValue& StructuredValue::dict_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.dict_value)
  return has_dict_value()
      ? *kind_.dict_value_
      : *reinterpret_cast< ::tensorflow::DictValue*>(&::tensorflow::_DictValue_default_instance_);
}
inline ::tensorflow::DictValue* StructuredValue::mutable_dict_value() {
  if (!has_dict_value()) {
    clear_kind();
    set_has_dict_value();
    kind_.dict_value_ = CreateMaybeMessage< ::tensorflow::DictValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.dict_value)
  return kind_.dict_value_;
}

// .tensorflow.NamedTupleValue named_tuple_value = 54;
inline bool StructuredValue::has_named_tuple_value() const {
  return kind_case() == kNamedTupleValue;
}
inline void StructuredValue::set_has_named_tuple_value() {
  _oneof_case_[0] = kNamedTupleValue;
}
inline void StructuredValue::clear_named_tuple_value() {
  if (has_named_tuple_value()) {
    delete kind_.named_tuple_value_;
    clear_has_kind();
  }
}
inline ::tensorflow::NamedTupleValue* StructuredValue::release_named_tuple_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.named_tuple_value)
  if (has_named_tuple_value()) {
    clear_has_kind();
      ::tensorflow::NamedTupleValue* temp = kind_.named_tuple_value_;
    kind_.named_tuple_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::NamedTupleValue& StructuredValue::named_tuple_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.named_tuple_value)
  return has_named_tuple_value()
      ? *kind_.named_tuple_value_
      : *reinterpret_cast< ::tensorflow::NamedTupleValue*>(&::tensorflow::_NamedTupleValue_default_instance_);
}
inline ::tensorflow::NamedTupleValue* StructuredValue::mutable_named_tuple_value() {
  if (!has_named_tuple_value()) {
    clear_kind();
    set_has_named_tuple_value();
    kind_.named_tuple_value_ = CreateMaybeMessage< ::tensorflow::NamedTupleValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.named_tuple_value)
  return kind_.named_tuple_value_;
}

inline bool StructuredValue::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void StructuredValue::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline StructuredValue::KindCase StructuredValue::kind_case() const {
  return StructuredValue::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// NoneValue

// -------------------------------------------------------------------

// ListValue

// repeated .tensorflow.StructuredValue values = 1;
inline int ListValue::values_size() const {
  return values_.size();
}
inline void ListValue::clear_values() {
  values_.Clear();
}
inline ::tensorflow::StructuredValue* ListValue::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ListValue.values)
  return values_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >*
ListValue::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ListValue.values)
  return &values_;
}
inline const ::tensorflow::StructuredValue& ListValue::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ListValue.values)
  return values_.Get(index);
}
inline ::tensorflow::StructuredValue* ListValue::add_values() {
  // @@protoc_insertion_point(field_add:tensorflow.ListValue.values)
  return values_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >&
ListValue::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.ListValue.values)
  return values_;
}

// -------------------------------------------------------------------

// TupleValue

// repeated .tensorflow.StructuredValue values = 1;
inline int TupleValue::values_size() const {
  return values_.size();
}
inline void TupleValue::clear_values() {
  values_.Clear();
}
inline ::tensorflow::StructuredValue* TupleValue::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TupleValue.values)
  return values_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >*
TupleValue::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TupleValue.values)
  return &values_;
}
inline const ::tensorflow::StructuredValue& TupleValue::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TupleValue.values)
  return values_.Get(index);
}
inline ::tensorflow::StructuredValue* TupleValue::add_values() {
  // @@protoc_insertion_point(field_add:tensorflow.TupleValue.values)
  return values_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >&
TupleValue::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.TupleValue.values)
  return values_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// DictValue

// map<string, .tensorflow.StructuredValue> fields = 1;
inline int DictValue::fields_size() const {
  return fields_.size();
}
inline void DictValue::clear_fields() {
  fields_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >&
DictValue::fields() const {
  // @@protoc_insertion_point(field_map:tensorflow.DictValue.fields)
  return fields_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >*
DictValue::mutable_fields() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.DictValue.fields)
  return fields_.MutableMap();
}

// -------------------------------------------------------------------

// PairValue

// string key = 1;
inline void PairValue::clear_key() {
  key_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& PairValue::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.PairValue.key)
  return key_.GetNoArena();
}
inline void PairValue::set_key(const std::string& value) {
  
  key_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.PairValue.key)
}
inline void PairValue::set_key(std::string&& value) {
  
  key_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PairValue.key)
}
inline void PairValue::set_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  key_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.PairValue.key)
}
inline void PairValue::set_key(const char* value, size_t size) {
  
  key_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PairValue.key)
}
inline std::string* PairValue::mutable_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PairValue.key)
  return key_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* PairValue::release_key() {
  // @@protoc_insertion_point(field_release:tensorflow.PairValue.key)
  
  return key_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void PairValue::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PairValue.key)
}

// .tensorflow.StructuredValue value = 2;
inline bool PairValue::has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline void PairValue::clear_value() {
  if (GetArenaNoVirtual() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
inline const ::tensorflow::StructuredValue& PairValue::value() const {
  const ::tensorflow::StructuredValue* p = value_;
  // @@protoc_insertion_point(field_get:tensorflow.PairValue.value)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* PairValue::release_value() {
  // @@protoc_insertion_point(field_release:tensorflow.PairValue.value)
  
  ::tensorflow::StructuredValue* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* PairValue::mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.PairValue.value)
  return value_;
}
inline void PairValue::set_allocated_value(::tensorflow::StructuredValue* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete value_;
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PairValue.value)
}

// -------------------------------------------------------------------

// NamedTupleValue

// string name = 1;
inline void NamedTupleValue::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& NamedTupleValue::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NamedTupleValue.name)
  return name_.GetNoArena();
}
inline void NamedTupleValue::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.NamedTupleValue.name)
}
inline void NamedTupleValue::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NamedTupleValue.name)
}
inline void NamedTupleValue::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.NamedTupleValue.name)
}
inline void NamedTupleValue::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NamedTupleValue.name)
}
inline std::string* NamedTupleValue::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedTupleValue.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* NamedTupleValue::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NamedTupleValue.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void NamedTupleValue::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NamedTupleValue.name)
}

// repeated .tensorflow.PairValue values = 2;
inline int NamedTupleValue::values_size() const {
  return values_.size();
}
inline void NamedTupleValue::clear_values() {
  values_.Clear();
}
inline ::tensorflow::PairValue* NamedTupleValue::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedTupleValue.values)
  return values_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::PairValue >*
NamedTupleValue::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NamedTupleValue.values)
  return &values_;
}
inline const ::tensorflow::PairValue& NamedTupleValue::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NamedTupleValue.values)
  return values_.Get(index);
}
inline ::tensorflow::PairValue* NamedTupleValue::add_values() {
  // @@protoc_insertion_point(field_add:tensorflow.NamedTupleValue.values)
  return values_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::PairValue >&
NamedTupleValue::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.NamedTupleValue.values)
  return values_;
}

// -------------------------------------------------------------------

// TensorSpecProto

// string name = 1;
inline void TensorSpecProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& TensorSpecProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSpecProto.name)
  return name_.GetNoArena();
}
inline void TensorSpecProto::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorSpecProto.name)
}
inline void TensorSpecProto::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TensorSpecProto.name)
}
inline void TensorSpecProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorSpecProto.name)
}
inline void TensorSpecProto::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorSpecProto.name)
}
inline std::string* TensorSpecProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorSpecProto.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TensorSpecProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorSpecProto.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TensorSpecProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorSpecProto.name)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool TensorSpecProto::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& TensorSpecProto::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.TensorSpecProto.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* TensorSpecProto::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorSpecProto.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorSpecProto::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorSpecProto.shape)
  return shape_;
}
inline void TensorSpecProto::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorSpecProto.shape)
}

// .tensorflow.DataType dtype = 3;
inline void TensorSpecProto::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType TensorSpecProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSpecProto.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void TensorSpecProto::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TensorSpecProto.dtype)
}

// -------------------------------------------------------------------

// BoundedTensorSpecProto

// string name = 1;
inline void BoundedTensorSpecProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& BoundedTensorSpecProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.BoundedTensorSpecProto.name)
  return name_.GetNoArena();
}
inline void BoundedTensorSpecProto::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.BoundedTensorSpecProto.name)
}
inline void BoundedTensorSpecProto::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.BoundedTensorSpecProto.name)
}
inline void BoundedTensorSpecProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.BoundedTensorSpecProto.name)
}
inline void BoundedTensorSpecProto::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BoundedTensorSpecProto.name)
}
inline std::string* BoundedTensorSpecProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.BoundedTensorSpecProto.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* BoundedTensorSpecProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.BoundedTensorSpecProto.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void BoundedTensorSpecProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BoundedTensorSpecProto.name)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool BoundedTensorSpecProto::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& BoundedTensorSpecProto::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.BoundedTensorSpecProto.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* BoundedTensorSpecProto::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.BoundedTensorSpecProto.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* BoundedTensorSpecProto::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.BoundedTensorSpecProto.shape)
  return shape_;
}
inline void BoundedTensorSpecProto::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BoundedTensorSpecProto.shape)
}

// .tensorflow.DataType dtype = 3;
inline void BoundedTensorSpecProto::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType BoundedTensorSpecProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.BoundedTensorSpecProto.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void BoundedTensorSpecProto::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BoundedTensorSpecProto.dtype)
}

// .tensorflow.TensorProto minimum = 4;
inline bool BoundedTensorSpecProto::has_minimum() const {
  return this != internal_default_instance() && minimum_ != nullptr;
}
inline const ::tensorflow::TensorProto& BoundedTensorSpecProto::minimum() const {
  const ::tensorflow::TensorProto* p = minimum_;
  // @@protoc_insertion_point(field_get:tensorflow.BoundedTensorSpecProto.minimum)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::release_minimum() {
  // @@protoc_insertion_point(field_release:tensorflow.BoundedTensorSpecProto.minimum)
  
  ::tensorflow::TensorProto* temp = minimum_;
  minimum_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::mutable_minimum() {
  
  if (minimum_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    minimum_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.BoundedTensorSpecProto.minimum)
  return minimum_;
}
inline void BoundedTensorSpecProto::set_allocated_minimum(::tensorflow::TensorProto* minimum) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(minimum_);
  }
  if (minimum) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(minimum)->GetArena();
    if (message_arena != submessage_arena) {
      minimum = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, minimum, submessage_arena);
    }
    
  } else {
    
  }
  minimum_ = minimum;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BoundedTensorSpecProto.minimum)
}

// .tensorflow.TensorProto maximum = 5;
inline bool BoundedTensorSpecProto::has_maximum() const {
  return this != internal_default_instance() && maximum_ != nullptr;
}
inline const ::tensorflow::TensorProto& BoundedTensorSpecProto::maximum() const {
  const ::tensorflow::TensorProto* p = maximum_;
  // @@protoc_insertion_point(field_get:tensorflow.BoundedTensorSpecProto.maximum)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::release_maximum() {
  // @@protoc_insertion_point(field_release:tensorflow.BoundedTensorSpecProto.maximum)
  
  ::tensorflow::TensorProto* temp = maximum_;
  maximum_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::mutable_maximum() {
  
  if (maximum_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    maximum_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.BoundedTensorSpecProto.maximum)
  return maximum_;
}
inline void BoundedTensorSpecProto::set_allocated_maximum(::tensorflow::TensorProto* maximum) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(maximum_);
  }
  if (maximum) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(maximum)->GetArena();
    if (message_arena != submessage_arena) {
      maximum = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, maximum, submessage_arena);
    }
    
  } else {
    
  }
  maximum_ = maximum;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BoundedTensorSpecProto.maximum)
}

// -------------------------------------------------------------------

// TypeSpecProto

// .tensorflow.TypeSpecProto.TypeSpecClass type_spec_class = 1;
inline void TypeSpecProto::clear_type_spec_class() {
  type_spec_class_ = 0;
}
inline ::tensorflow::TypeSpecProto_TypeSpecClass TypeSpecProto::type_spec_class() const {
  // @@protoc_insertion_point(field_get:tensorflow.TypeSpecProto.type_spec_class)
  return static_cast< ::tensorflow::TypeSpecProto_TypeSpecClass >(type_spec_class_);
}
inline void TypeSpecProto::set_type_spec_class(::tensorflow::TypeSpecProto_TypeSpecClass value) {
  
  type_spec_class_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TypeSpecProto.type_spec_class)
}

// .tensorflow.StructuredValue type_state = 2;
inline bool TypeSpecProto::has_type_state() const {
  return this != internal_default_instance() && type_state_ != nullptr;
}
inline void TypeSpecProto::clear_type_state() {
  if (GetArenaNoVirtual() == nullptr && type_state_ != nullptr) {
    delete type_state_;
  }
  type_state_ = nullptr;
}
inline const ::tensorflow::StructuredValue& TypeSpecProto::type_state() const {
  const ::tensorflow::StructuredValue* p = type_state_;
  // @@protoc_insertion_point(field_get:tensorflow.TypeSpecProto.type_state)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* TypeSpecProto::release_type_state() {
  // @@protoc_insertion_point(field_release:tensorflow.TypeSpecProto.type_state)
  
  ::tensorflow::StructuredValue* temp = type_state_;
  type_state_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* TypeSpecProto::mutable_type_state() {
  
  if (type_state_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    type_state_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TypeSpecProto.type_state)
  return type_state_;
}
inline void TypeSpecProto::set_allocated_type_state(::tensorflow::StructuredValue* type_state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete type_state_;
  }
  if (type_state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      type_state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type_state, submessage_arena);
    }
    
  } else {
    
  }
  type_state_ = type_state;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TypeSpecProto.type_state)
}

// string type_spec_class_name = 3;
inline void TypeSpecProto::clear_type_spec_class_name() {
  type_spec_class_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& TypeSpecProto::type_spec_class_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TypeSpecProto.type_spec_class_name)
  return type_spec_class_name_.GetNoArena();
}
inline void TypeSpecProto::set_type_spec_class_name(const std::string& value) {
  
  type_spec_class_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.TypeSpecProto.type_spec_class_name)
}
inline void TypeSpecProto::set_type_spec_class_name(std::string&& value) {
  
  type_spec_class_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TypeSpecProto.type_spec_class_name)
}
inline void TypeSpecProto::set_type_spec_class_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  type_spec_class_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.TypeSpecProto.type_spec_class_name)
}
inline void TypeSpecProto::set_type_spec_class_name(const char* value, size_t size) {
  
  type_spec_class_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TypeSpecProto.type_spec_class_name)
}
inline std::string* TypeSpecProto::mutable_type_spec_class_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TypeSpecProto.type_spec_class_name)
  return type_spec_class_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TypeSpecProto::release_type_spec_class_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TypeSpecProto.type_spec_class_name)
  
  return type_spec_class_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TypeSpecProto::set_allocated_type_spec_class_name(std::string* type_spec_class_name) {
  if (type_spec_class_name != nullptr) {
    
  } else {
    
  }
  type_spec_class_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type_spec_class_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TypeSpecProto.type_spec_class_name)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::TypeSpecProto_TypeSpecClass> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::TypeSpecProto_TypeSpecClass>() {
  return ::tensorflow::TypeSpecProto_TypeSpecClass_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
