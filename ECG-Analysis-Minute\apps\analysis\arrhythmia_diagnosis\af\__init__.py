"""
房颤检测模块
"""

from .af_detector import detect_atrial_fibrillation, analyze_af_features, clear_all_caches
from .af_config import AF_WEIGHTS, THRESHOLDS, LEAD_SPECIFIC, AF_LEVELS, AF_LEVEL_THRESHOLDS, AF_<PERSON>RAMS

def process(waveform_info, precomputed_signal_features=None):
    """
    房颤检测的标准接口函数
    :param waveform_info: 波形信息
    :param precomputed_signal_features: 预计算的信号特征 (可选)
    :return: True 如果检测到房颤，否则 False
    """
    return detect_atrial_fibrillation(waveform_info, precomputed_signal_features=precomputed_signal_features)

__all__ = [
    'detect_atrial_fibrillation',
    'analyze_af_features',
    'clear_all_caches',
    'process',
    'AF_WEIGHTS',
    'THRESHOLDS',
    'LEAD_SPECIFIC',
    'AF_LEVELS',
    'AF_LEVEL_THRESHOLDS',
    'AF_PARAMS'
] 