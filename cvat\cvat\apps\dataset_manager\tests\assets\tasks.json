{"main": {"name": "main task", "overlap": 0, "segment_size": 100, "labels": [{"name": "car", "color": "#2080c0", "attributes": [{"name": "select_name", "mutable": false, "input_type": "select", "default_value": "bmw", "values": ["bmw", "mazda", "renault"]}, {"name": "radio_name", "mutable": false, "input_type": "radio", "default_value": "x1", "values": ["x1", "x2", "x3"]}, {"name": "check_name", "mutable": true, "input_type": "checkbox", "default_value": "false", "values": ["false"]}, {"name": "text_name", "mutable": false, "input_type": "text", "default_value": "qwerty", "values": ["qwerty"]}, {"name": "number_name", "mutable": false, "input_type": "number", "default_value": "-4", "values": ["-4", "4", "1"]}]}, {"name": "person", "color": "#c06060", "attributes": []}]}, "ICDAR Localization 1.0": {"name": "icdar localization/recogntion task", "overlap": 0, "segment_size": 100, "labels": [{"name": "ic<PERSON>", "attributes": [{"name": "text", "mutable": false, "input_type": "text", "values": ["word_1", "word_2", "word_3"]}]}]}, "ICDAR Recognition 1.0": {"name": "icdar localization/recogntion task", "overlap": 0, "segment_size": 100, "labels": [{"name": "ic<PERSON>", "attributes": [{"name": "text", "mutable": false, "input_type": "text", "values": ["word_1", "word_2", "word_3"]}]}]}, "ICDAR Segmentation 1.0": {"name": "icdar segmentation task", "overlap": 0, "segment_size": 100, "labels": [{"name": "ic<PERSON>", "attributes": [{"name": "text", "mutable": false, "input_type": "text", "values": ["word_1", "word_2", "word_3"]}, {"name": "index", "mutable": false, "input_type": "number", "values": ["0", "1", "2"]}, {"name": "color", "mutable": false, "input_type": "text", "values": ["100 110 240", "10 15 20", "120 128 64"]}, {"name": "center", "mutable": false, "input_type": "text", "values": ["1 2", "2 4", "10 45"]}]}]}, "Market-1501 1.0": {"name": "market1501 task", "overlap": 0, "segment_size": 100, "labels": [{"name": "market-1501", "attributes": [{"name": "query", "mutable": false, "input_type": "select", "values": ["True", "False"]}, {"name": "camera_id", "mutable": false, "input_type": "number", "values": ["1", "5", "2"]}, {"name": "person_id", "mutable": false, "input_type": "number", "values": ["1", "6", "1"]}]}]}, "Cityscapes 1.0": {"name": "cityscapes task", "overlap": 0, "segment_size": 100, "labels": [{"name": "car", "color": "#2080c0", "attributes": [{"name": "is_crowd", "mutable": false, "input_type": "checkbox", "default_value": "false", "values": ["false", "true"]}]}, {"name": "person", "color": "#c06060", "attributes": []}, {"name": "background", "color": "#000000", "attributes": []}]}, "KITTI 1.0": {"name": "kitti task", "overlap": 0, "segment_size": 100, "labels": [{"name": "car", "color": "#2080c0", "attributes": [{"name": "is_crowd", "mutable": false, "input_type": "checkbox", "default_value": "false", "values": ["false", "true"]}]}, {"name": "person", "color": "#c06060", "attributes": []}, {"name": "background", "color": "#000000", "attributes": []}]}, "MOT 1.1": {"name": "MOT task", "overlap": 0, "segment_size": 100, "labels": [{"name": "car", "color": "#2080c0", "attributes": [{"name": "ignored", "mutable": true, "input_type": "checkbox", "default_value": "false", "values": ["false", "true"]}, {"name": "visibility", "mutable": false, "input_type": "number", "default_value": "1", "values": ["0", "1", "1"]}]}, {"name": "person", "color": "#c06060", "attributes": [{"name": "ignored", "mutable": true, "input_type": "checkbox", "default_value": "false", "values": ["false", "true"]}, {"name": "visibility", "mutable": false, "input_type": "number", "default_value": "1", "values": ["0", "1", "1"]}]}]}, "wrong_checkbox_value": {"name": "wrong checkbox value task", "overlap": 0, "segment_size": 100, "labels": [{"name": "car", "color": "#2080c0", "attributes": [{"name": "select_name", "mutable": false, "input_type": "select", "default_value": "bmw", "values": ["bmw", "mazda", "renault"]}, {"name": "radio_name", "mutable": false, "input_type": "radio", "default_value": "x1", "values": ["x1", "x2", "x3"]}, {"name": "check_name", "mutable": true, "input_type": "checkbox", "default_value": "false", "values": ["false"]}, {"name": "text_name", "mutable": false, "input_type": "text", "default_value": "qwerty", "values": ["qwerty"]}, {"name": "number_name", "mutable": false, "input_type": "number", "default_value": "-4", "values": ["-4", "4", "1"]}]}, {"name": "person", "color": "#c06060", "attributes": []}]}, "no attributes": {"name": "no attributes", "overlap": 0, "segment_size": 100, "labels": [{"name": "car", "color": "#2080c0", "attributes": []}]}, "many jobs": {"name": "many jobs", "overlap": 0, "segment_size": 5, "labels": [{"name": "car", "color": "#2080c0", "attributes": []}]}, "many jobs skeleton": {"name": "many jobs", "overlap": 5, "segment_size": 10, "labels": [{"name": "skeleton", "color": "#2080c0", "type": "skeleton", "attributes": [{"name": "attr", "mutable": false, "input_type": "select", "values": ["1", "2", "3"]}], "sublabels": [{"name": "1", "color": "#d12345", "attributes": [], "type": "points"}, {"name": "2", "color": "#350dea", "attributes": [], "type": "points"}, {"name": "3", "color": "#479ffe", "attributes": [], "type": "points"}], "svg": "<line x1=\"38.92810821533203\" y1=\"53.31378173828125\" x2=\"80.23341369628906\" y2=\"18.36313819885254\" stroke=\"black\" data-type=\"edge\" data-node-from=\"2\" stroke-width=\"0.5\" data-node-to=\"3\"></line><line x1=\"30.399484634399414\" y1=\"32.74474334716797\" x2=\"38.92810821533203\" y2=\"53.31378173828125\" stroke=\"black\" data-type=\"edge\" data-node-from=\"1\" stroke-width=\"0.5\" data-node-to=\"2\"></line><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"30.399484634399414\" cy=\"32.74474334716797\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"1\" data-node-id=\"1\" data-label-name=\"1\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"38.92810821533203\" cy=\"53.31378173828125\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"2\" data-node-id=\"2\" data-label-name=\"2\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"80.23341369628906\" cy=\"18.36313819885254\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"3\" data-node-id=\"3\" data-label-name=\"3\"></circle>"}]}, "change overlap and segment size": {"name": "change overlap and segment size", "overlap": 3, "segment_size": 6, "labels": [{"name": "car", "color": "#2080c0", "attributes": []}]}, "widerface with all attributes": {"name": "widerface task", "overlap": 0, "segment_size": 100, "labels": [{"name": "face", "attributes": [{"name": "blur", "mutable": false, "input_type": "select", "values": ["0", "1", "2"]}, {"name": "expression", "mutable": false, "input_type": "select", "values": ["0", "1"]}, {"name": "illumination", "mutable": false, "input_type": "select", "values": ["0", "1"]}, {"name": "pose", "mutable": false, "input_type": "select", "values": ["0", "1"]}, {"name": "invalid", "mutable": false, "input_type": "select", "values": ["0", "1"]}]}]}, "task in project #1": {"name": "First task in project", "project_id": 1, "overlap": 0, "segment_size": 100}, "task in project #2": {"name": "Second task in project", "project_id": 1, "overlap": 0, "segment_size": 100}, "COCO Keypoints 1.0": {"name": "coco keupoints task", "overlap": 0, "segment_size": 100, "labels": [{"name": "skeleton", "color": "#2080c0", "type": "skeleton", "attributes": [{"name": "attr", "mutable": false, "input_type": "select", "values": ["0", "1", "2"]}], "sublabels": [{"name": "1", "color": "#d12345", "attributes": [], "type": "points"}, {"name": "2", "color": "#350dea", "attributes": [], "type": "points"}, {"name": "3", "color": "#479ffe", "attributes": [], "type": "points"}, {"name": "4", "color": "#ff7f0e", "attributes": [], "type": "points"}, {"name": "5", "color": "#2ca02c", "attributes": [], "type": "points"}, {"name": "6", "color": "#9467bd", "attributes": [], "type": "points"}, {"name": "7", "color": "#8c564b", "attributes": [], "type": "points"}, {"name": "8", "color": "#e377c2", "attributes": [], "type": "points"}, {"name": "9", "color": "#7f7f7f", "attributes": [], "type": "points"}, {"name": "10", "color": "#bcbd22", "attributes": [], "type": "points"}, {"name": "11", "color": "#17becf", "attributes": [], "type": "points"}, {"name": "12", "color": "#1f77b4", "attributes": [], "type": "points"}, {"name": "13", "color": "#ff9896", "attributes": [], "type": "points"}, {"name": "14", "color": "#98df8a", "attributes": [], "type": "points"}, {"name": "15", "color": "#c49c94", "attributes": [], "type": "points"}, {"name": "16", "color": "#f7b6d2", "attributes": [], "type": "points"}, {"name": "17", "color": "#c5b0d5", "attributes": [], "type": "points"}, {"name": "18", "color": "#ffbb78", "attributes": [], "type": "points"}, {"name": "19", "color": "#aec7e8", "attributes": [], "type": "points"}, {"name": "20", "color": "#ffbb78", "attributes": [], "type": "points"}, {"name": "21", "color": "#dbdb8d", "attributes": [], "type": "points"}, {"name": "22", "color": "#9edae5", "attributes": [], "type": "points"}], "svg": "<line x1=\"38.92810821533203\" y1=\"53.31378173828125\" x2=\"80.23341369628906\" y2=\"18.36313819885254\" stroke=\"black\" data-type=\"edge\" data-node-from=\"2\" stroke-width=\"0.5\" data-node-to=\"3\"></line><line x1=\"30.399484634399414\" y1=\"32.74474334716797\" x2=\"38.92810821533203\" y2=\"53.31378173828125\" stroke=\"black\" data-type=\"edge\" data-node-from=\"1\" stroke-width=\"0.5\" data-node-to=\"2\"></line><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"30.399484634399414\" cy=\"32.74474334716797\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"1\" data-node-id=\"1\" data-label-name=\"1\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"38.92810821533203\" cy=\"53.31378173828125\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"2\" data-node-id=\"2\" data-label-name=\"2\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"80.23341369628906\" cy=\"18.36313819885254\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"3\" data-node-id=\"3\" data-label-name=\"3\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#ff7f0e\" cx=\"45\" cy=\"60\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"4\" data-node-id=\"4\" data-label-name=\"4\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#2ca02c\" cx=\"50\" cy=\"65\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"5\" data-node-id=\"5\" data-label-name=\"5\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#9467bd\" cx=\"55\" cy=\"70\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"6\" data-node-id=\"6\" data-label-name=\"6\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#8c564b\" cx=\"60\" cy=\"75\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"7\" data-node-id=\"7\" data-label-name=\"7\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#e377c2\" cx=\"65\" cy=\"80\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"8\" data-node-id=\"8\" data-label-name=\"8\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#7f7f7f\" cx=\"70\" cy=\"85\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"9\" data-node-id=\"9\" data-label-name=\"9\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#bcbd22\" cx=\"75\" cy=\"90\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"10\" data-node-id=\"10\" data-label-name=\"10\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#17becf\" cx=\"80\" cy=\"95\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"11\" data-node-id=\"11\" data-label-name=\"11\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#1f77b4\" cx=\"85\" cy=\"100\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"12\" data-node-id=\"12\" data-label-name=\"12\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#ff9896\" cx=\"90\" cy=\"105\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"13\" data-node-id=\"13\" data-label-name=\"13\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#98df8a\" cx=\"95\" cy=\"110\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"14\" data-node-id=\"14\" data-label-name=\"14\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#c49c94\" cx=\"100\" cy=\"115\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"15\" data-node-id=\"15\" data-label-name=\"15\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#f7b6d2\" cx=\"105\" cy=\"120\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"16\" data-node-id=\"16\" data-label-name=\"16\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#c5b0d5\" cx=\"110\" cy=\"125\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"17\" data-node-id=\"17\" data-label-name=\"17\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#ffbb78\" cx=\"115\" cy=\"130\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"18\" data-node-id=\"18\" data-label-name=\"18\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#aec7e8\" cx=\"120\" cy=\"135\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"19\" data-node-id=\"19\" data-label-name=\"19\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#ffbb78\" cx=\"125\" cy=\"140\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"20\" data-node-id=\"20\" data-label-name=\"20\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#dbdb8d\" cx=\"130\" cy=\"145\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"21\" data-node-id=\"21\" data-label-name=\"21\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#9edae5\" cx=\"135\" cy=\"150\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"22\" data-node-id=\"22\" data-label-name=\"22\"></circle>"}, {"name": "skeleton2", "color": "#2080c0", "type": "skeleton", "attributes": [{"name": "attr", "mutable": false, "input_type": "select", "values": ["0", "1", "2"]}], "sublabels": [{"name": "1", "color": "#d12345", "attributes": [], "type": "points"}, {"name": "2", "color": "#350dea", "attributes": [], "type": "points"}, {"name": "3", "color": "#479ffe", "attributes": [], "type": "points"}], "svg": "<line x1=\"38.92810821533203\" y1=\"53.31378173828125\" x2=\"80.23341369628906\" y2=\"18.36313819885254\" stroke=\"black\" data-type=\"edge\" data-node-from=\"2\" stroke-width=\"0.5\" data-node-to=\"3\"></line><line x1=\"30.399484634399414\" y1=\"32.74474334716797\" x2=\"38.92810821533203\" y2=\"53.31378173828125\" stroke=\"black\" data-type=\"edge\" data-node-from=\"1\" stroke-width=\"0.5\" data-node-to=\"2\"></line><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"30.399484634399414\" cy=\"32.74474334716797\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"1\" data-node-id=\"1\" data-label-name=\"1\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"38.92810821533203\" cy=\"53.31378173828125\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"2\" data-node-id=\"2\" data-label-name=\"2\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"80.23341369628906\" cy=\"18.36313819885254\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"3\" data-node-id=\"3\" data-label-name=\"3\"></circle>"}]}, "Ultralytics YOLO Pose 1.0": {"name": "Ultralytics YOLO pose task", "overlap": 0, "segment_size": 100, "labels": [{"name": "skeleton", "color": "#2080c0", "type": "skeleton", "attributes": [{"name": "attr", "mutable": false, "input_type": "select", "values": ["0", "1", "2"]}], "sublabels": [{"name": "1", "color": "#d12345", "attributes": [], "type": "points"}, {"name": "2", "color": "#350dea", "attributes": [], "type": "points"}, {"name": "3", "color": "#479ffe", "attributes": [], "type": "points"}], "svg": "<line x1=\"38.92810821533203\" y1=\"53.31378173828125\" x2=\"80.23341369628906\" y2=\"18.36313819885254\" stroke=\"black\" data-type=\"edge\" data-node-from=\"2\" stroke-width=\"0.5\" data-node-to=\"3\"></line><line x1=\"30.399484634399414\" y1=\"32.74474334716797\" x2=\"38.92810821533203\" y2=\"53.31378173828125\" stroke=\"black\" data-type=\"edge\" data-node-from=\"1\" stroke-width=\"0.5\" data-node-to=\"2\"></line><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"38.92810821533203\" cy=\"53.31378173828125\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"2\" data-node-id=\"2\" data-label-name=\"2\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"80.23341369628906\" cy=\"18.36313819885254\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"3\" data-node-id=\"3\" data-label-name=\"3\"></circle><circle r=\"1.5\" stroke=\"black\" fill=\"#b3b3b3\" cx=\"30.399484634399414\" cy=\"32.74474334716797\" stroke-width=\"0.1\" data-type=\"element node\" data-element-id=\"1\" data-node-id=\"1\" data-label-name=\"1\"></circle>"}]}}