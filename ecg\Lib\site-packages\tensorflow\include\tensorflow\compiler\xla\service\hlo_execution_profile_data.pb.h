// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/xla/service/hlo_execution_profile_data.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/compiler/xla/service/hlo_profile_printer_data.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto;
namespace xla {
class HloExecutionProfileData;
class HloExecutionProfileDataDefaultTypeInternal;
extern HloExecutionProfileDataDefaultTypeInternal _HloExecutionProfileData_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::HloExecutionProfileData* Arena::CreateMaybeMessage<::xla::HloExecutionProfileData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

// ===================================================================

class HloExecutionProfileData :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloExecutionProfileData) */ {
 public:
  HloExecutionProfileData();
  virtual ~HloExecutionProfileData();

  HloExecutionProfileData(const HloExecutionProfileData& from);
  HloExecutionProfileData(HloExecutionProfileData&& from) noexcept
    : HloExecutionProfileData() {
    *this = ::std::move(from);
  }

  inline HloExecutionProfileData& operator=(const HloExecutionProfileData& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloExecutionProfileData& operator=(HloExecutionProfileData&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloExecutionProfileData& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloExecutionProfileData* internal_default_instance() {
    return reinterpret_cast<const HloExecutionProfileData*>(
               &_HloExecutionProfileData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(HloExecutionProfileData& a, HloExecutionProfileData& b) {
    a.Swap(&b);
  }
  inline void Swap(HloExecutionProfileData* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloExecutionProfileData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloExecutionProfileData* New() const final {
    return CreateMaybeMessage<HloExecutionProfileData>(nullptr);
  }

  HloExecutionProfileData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloExecutionProfileData>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloExecutionProfileData& from);
  void MergeFrom(const HloExecutionProfileData& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloExecutionProfileData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloExecutionProfileData";
  }
  protected:
  explicit HloExecutionProfileData(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProfileCountersFieldNumber = 2,
    kPrinterDataFieldNumber = 1,
  };
  // repeated int64 profile_counters = 2;
  int profile_counters_size() const;
  void clear_profile_counters();
  ::PROTOBUF_NAMESPACE_ID::int64 profile_counters(int index) const;
  void set_profile_counters(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_profile_counters(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      profile_counters() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_profile_counters();

  // .xla.HloProfilePrinterData printer_data = 1;
  bool has_printer_data() const;
  void clear_printer_data();
  const ::xla::HloProfilePrinterData& printer_data() const;
  ::xla::HloProfilePrinterData* release_printer_data();
  ::xla::HloProfilePrinterData* mutable_printer_data();
  void set_allocated_printer_data(::xla::HloProfilePrinterData* printer_data);
  void unsafe_arena_set_allocated_printer_data(
      ::xla::HloProfilePrinterData* printer_data);
  ::xla::HloProfilePrinterData* unsafe_arena_release_printer_data();

  // @@protoc_insertion_point(class_scope:xla.HloExecutionProfileData)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > profile_counters_;
  mutable std::atomic<int> _profile_counters_cached_byte_size_;
  ::xla::HloProfilePrinterData* printer_data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// HloExecutionProfileData

// .xla.HloProfilePrinterData printer_data = 1;
inline bool HloExecutionProfileData::has_printer_data() const {
  return this != internal_default_instance() && printer_data_ != nullptr;
}
inline const ::xla::HloProfilePrinterData& HloExecutionProfileData::printer_data() const {
  const ::xla::HloProfilePrinterData* p = printer_data_;
  // @@protoc_insertion_point(field_get:xla.HloExecutionProfileData.printer_data)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloProfilePrinterData*>(
      &::xla::_HloProfilePrinterData_default_instance_);
}
inline ::xla::HloProfilePrinterData* HloExecutionProfileData::release_printer_data() {
  // @@protoc_insertion_point(field_release:xla.HloExecutionProfileData.printer_data)
  
  ::xla::HloProfilePrinterData* temp = printer_data_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  printer_data_ = nullptr;
  return temp;
}
inline ::xla::HloProfilePrinterData* HloExecutionProfileData::unsafe_arena_release_printer_data() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloExecutionProfileData.printer_data)
  
  ::xla::HloProfilePrinterData* temp = printer_data_;
  printer_data_ = nullptr;
  return temp;
}
inline ::xla::HloProfilePrinterData* HloExecutionProfileData::mutable_printer_data() {
  
  if (printer_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloProfilePrinterData>(GetArenaNoVirtual());
    printer_data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloExecutionProfileData.printer_data)
  return printer_data_;
}
inline void HloExecutionProfileData::set_allocated_printer_data(::xla::HloProfilePrinterData* printer_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(printer_data_);
  }
  if (printer_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(printer_data)->GetArena();
    if (message_arena != submessage_arena) {
      printer_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, printer_data, submessage_arena);
    }
    
  } else {
    
  }
  printer_data_ = printer_data;
  // @@protoc_insertion_point(field_set_allocated:xla.HloExecutionProfileData.printer_data)
}

// repeated int64 profile_counters = 2;
inline int HloExecutionProfileData::profile_counters_size() const {
  return profile_counters_.size();
}
inline void HloExecutionProfileData::clear_profile_counters() {
  profile_counters_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloExecutionProfileData::profile_counters(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloExecutionProfileData.profile_counters)
  return profile_counters_.Get(index);
}
inline void HloExecutionProfileData::set_profile_counters(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  profile_counters_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloExecutionProfileData.profile_counters)
}
inline void HloExecutionProfileData::add_profile_counters(::PROTOBUF_NAMESPACE_ID::int64 value) {
  profile_counters_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloExecutionProfileData.profile_counters)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloExecutionProfileData::profile_counters() const {
  // @@protoc_insertion_point(field_list:xla.HloExecutionProfileData.profile_counters)
  return profile_counters_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloExecutionProfileData::mutable_profile_counters() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloExecutionProfileData.profile_counters)
  return &profile_counters_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto
