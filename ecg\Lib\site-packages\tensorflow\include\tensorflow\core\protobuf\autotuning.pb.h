// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/autotuning.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/duration.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[8]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto;
namespace tensorflow {
class AutotuneResult;
class AutotuneResultDefaultTypeInternal;
extern AutotuneResultDefaultTypeInternal _AutotuneResult_default_instance_;
class AutotuneResult_ConvKey;
class AutotuneResult_ConvKeyDefaultTypeInternal;
extern AutotuneResult_ConvKeyDefaultTypeInternal _AutotuneResult_ConvKey_default_instance_;
class AutotuneResult_CudaConvPlanKey;
class AutotuneResult_CudaConvPlanKeyDefaultTypeInternal;
extern AutotuneResult_CudaConvPlanKeyDefaultTypeInternal _AutotuneResult_CudaConvPlanKey_default_instance_;
class AutotuneResult_FailureResult;
class AutotuneResult_FailureResultDefaultTypeInternal;
extern AutotuneResult_FailureResultDefaultTypeInternal _AutotuneResult_FailureResult_default_instance_;
class AutotuneResult_GemmKey;
class AutotuneResult_GemmKeyDefaultTypeInternal;
extern AutotuneResult_GemmKeyDefaultTypeInternal _AutotuneResult_GemmKey_default_instance_;
class AutotuningLog;
class AutotuningLogDefaultTypeInternal;
extern AutotuningLogDefaultTypeInternal _AutotuningLog_default_instance_;
class ComputeCapability;
class ComputeCapabilityDefaultTypeInternal;
extern ComputeCapabilityDefaultTypeInternal _ComputeCapability_default_instance_;
class CudnnVersion;
class CudnnVersionDefaultTypeInternal;
extern CudnnVersionDefaultTypeInternal _CudnnVersion_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::AutotuneResult* Arena::CreateMaybeMessage<::tensorflow::AutotuneResult>(Arena*);
template<> ::tensorflow::AutotuneResult_ConvKey* Arena::CreateMaybeMessage<::tensorflow::AutotuneResult_ConvKey>(Arena*);
template<> ::tensorflow::AutotuneResult_CudaConvPlanKey* Arena::CreateMaybeMessage<::tensorflow::AutotuneResult_CudaConvPlanKey>(Arena*);
template<> ::tensorflow::AutotuneResult_FailureResult* Arena::CreateMaybeMessage<::tensorflow::AutotuneResult_FailureResult>(Arena*);
template<> ::tensorflow::AutotuneResult_GemmKey* Arena::CreateMaybeMessage<::tensorflow::AutotuneResult_GemmKey>(Arena*);
template<> ::tensorflow::AutotuningLog* Arena::CreateMaybeMessage<::tensorflow::AutotuningLog>(Arena*);
template<> ::tensorflow::ComputeCapability* Arena::CreateMaybeMessage<::tensorflow::ComputeCapability>(Arena*);
template<> ::tensorflow::CudnnVersion* Arena::CreateMaybeMessage<::tensorflow::CudnnVersion>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum AutotuneResult_FailureKind : int {
  AutotuneResult_FailureKind_UNKNOWN = 0,
  AutotuneResult_FailureKind_REDZONE_MODIFIED = 1,
  AutotuneResult_FailureKind_WRONG_RESULT = 2,
  AutotuneResult_FailureKind_AutotuneResult_FailureKind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  AutotuneResult_FailureKind_AutotuneResult_FailureKind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool AutotuneResult_FailureKind_IsValid(int value);
constexpr AutotuneResult_FailureKind AutotuneResult_FailureKind_FailureKind_MIN = AutotuneResult_FailureKind_UNKNOWN;
constexpr AutotuneResult_FailureKind AutotuneResult_FailureKind_FailureKind_MAX = AutotuneResult_FailureKind_WRONG_RESULT;
constexpr int AutotuneResult_FailureKind_FailureKind_ARRAYSIZE = AutotuneResult_FailureKind_FailureKind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AutotuneResult_FailureKind_descriptor();
template<typename T>
inline const std::string& AutotuneResult_FailureKind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AutotuneResult_FailureKind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AutotuneResult_FailureKind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AutotuneResult_FailureKind_descriptor(), enum_t_value);
}
inline bool AutotuneResult_FailureKind_Parse(
    const std::string& name, AutotuneResult_FailureKind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AutotuneResult_FailureKind>(
    AutotuneResult_FailureKind_descriptor(), name, value);
}
// ===================================================================

class CudnnVersion :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CudnnVersion) */ {
 public:
  CudnnVersion();
  virtual ~CudnnVersion();

  CudnnVersion(const CudnnVersion& from);
  CudnnVersion(CudnnVersion&& from) noexcept
    : CudnnVersion() {
    *this = ::std::move(from);
  }

  inline CudnnVersion& operator=(const CudnnVersion& from) {
    CopyFrom(from);
    return *this;
  }
  inline CudnnVersion& operator=(CudnnVersion&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CudnnVersion& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CudnnVersion* internal_default_instance() {
    return reinterpret_cast<const CudnnVersion*>(
               &_CudnnVersion_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CudnnVersion& a, CudnnVersion& b) {
    a.Swap(&b);
  }
  inline void Swap(CudnnVersion* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CudnnVersion* New() const final {
    return CreateMaybeMessage<CudnnVersion>(nullptr);
  }

  CudnnVersion* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CudnnVersion>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CudnnVersion& from);
  void MergeFrom(const CudnnVersion& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CudnnVersion* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CudnnVersion";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMajorFieldNumber = 1,
    kMinorFieldNumber = 2,
    kPatchFieldNumber = 3,
  };
  // int32 major = 1;
  void clear_major();
  ::PROTOBUF_NAMESPACE_ID::int32 major() const;
  void set_major(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 minor = 2;
  void clear_minor();
  ::PROTOBUF_NAMESPACE_ID::int32 minor() const;
  void set_minor(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 patch = 3;
  void clear_patch();
  ::PROTOBUF_NAMESPACE_ID::int32 patch() const;
  void set_patch(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CudnnVersion)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int32 major_;
  ::PROTOBUF_NAMESPACE_ID::int32 minor_;
  ::PROTOBUF_NAMESPACE_ID::int32 patch_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class ComputeCapability :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ComputeCapability) */ {
 public:
  ComputeCapability();
  virtual ~ComputeCapability();

  ComputeCapability(const ComputeCapability& from);
  ComputeCapability(ComputeCapability&& from) noexcept
    : ComputeCapability() {
    *this = ::std::move(from);
  }

  inline ComputeCapability& operator=(const ComputeCapability& from) {
    CopyFrom(from);
    return *this;
  }
  inline ComputeCapability& operator=(ComputeCapability&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ComputeCapability& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ComputeCapability* internal_default_instance() {
    return reinterpret_cast<const ComputeCapability*>(
               &_ComputeCapability_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ComputeCapability& a, ComputeCapability& b) {
    a.Swap(&b);
  }
  inline void Swap(ComputeCapability* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ComputeCapability* New() const final {
    return CreateMaybeMessage<ComputeCapability>(nullptr);
  }

  ComputeCapability* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ComputeCapability>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ComputeCapability& from);
  void MergeFrom(const ComputeCapability& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ComputeCapability* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ComputeCapability";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMajorFieldNumber = 1,
    kMinorFieldNumber = 2,
  };
  // int32 major = 1;
  void clear_major();
  ::PROTOBUF_NAMESPACE_ID::int32 major() const;
  void set_major(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 minor = 2;
  void clear_minor();
  ::PROTOBUF_NAMESPACE_ID::int32 minor() const;
  void set_minor(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ComputeCapability)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int32 major_;
  ::PROTOBUF_NAMESPACE_ID::int32 minor_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult_FailureResult :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutotuneResult.FailureResult) */ {
 public:
  AutotuneResult_FailureResult();
  virtual ~AutotuneResult_FailureResult();

  AutotuneResult_FailureResult(const AutotuneResult_FailureResult& from);
  AutotuneResult_FailureResult(AutotuneResult_FailureResult&& from) noexcept
    : AutotuneResult_FailureResult() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_FailureResult& operator=(const AutotuneResult_FailureResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult_FailureResult& operator=(AutotuneResult_FailureResult&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AutotuneResult_FailureResult& default_instance();

  enum KeyCase {
    kReferenceConv = 11,
    kReferenceGemm = 12,
    kReferenceCudaConvPlan = 14,
    KEY_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutotuneResult_FailureResult* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_FailureResult*>(
               &_AutotuneResult_FailureResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(AutotuneResult_FailureResult& a, AutotuneResult_FailureResult& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult_FailureResult* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AutotuneResult_FailureResult* New() const final {
    return CreateMaybeMessage<AutotuneResult_FailureResult>(nullptr);
  }

  AutotuneResult_FailureResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AutotuneResult_FailureResult>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AutotuneResult_FailureResult& from);
  void MergeFrom(const AutotuneResult_FailureResult& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_FailureResult* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AutotuneResult.FailureResult";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMsgFieldNumber = 2,
    kKindFieldNumber = 1,
    kBufferAddressFieldNumber = 13,
    kReferenceConvFieldNumber = 11,
    kReferenceGemmFieldNumber = 12,
    kReferenceCudaConvPlanFieldNumber = 14,
  };
  // string msg = 2;
  void clear_msg();
  const std::string& msg() const;
  void set_msg(const std::string& value);
  void set_msg(std::string&& value);
  void set_msg(const char* value);
  void set_msg(const char* value, size_t size);
  std::string* mutable_msg();
  std::string* release_msg();
  void set_allocated_msg(std::string* msg);

  // .tensorflow.AutotuneResult.FailureKind kind = 1;
  void clear_kind();
  ::tensorflow::AutotuneResult_FailureKind kind() const;
  void set_kind(::tensorflow::AutotuneResult_FailureKind value);

  // int64 buffer_address = 13;
  void clear_buffer_address();
  ::PROTOBUF_NAMESPACE_ID::int64 buffer_address() const;
  void set_buffer_address(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .tensorflow.AutotuneResult.ConvKey reference_conv = 11;
  bool has_reference_conv() const;
  void clear_reference_conv();
  const ::tensorflow::AutotuneResult_ConvKey& reference_conv() const;
  ::tensorflow::AutotuneResult_ConvKey* release_reference_conv();
  ::tensorflow::AutotuneResult_ConvKey* mutable_reference_conv();
  void set_allocated_reference_conv(::tensorflow::AutotuneResult_ConvKey* reference_conv);

  // .tensorflow.AutotuneResult.GemmKey reference_gemm = 12;
  bool has_reference_gemm() const;
  void clear_reference_gemm();
  const ::tensorflow::AutotuneResult_GemmKey& reference_gemm() const;
  ::tensorflow::AutotuneResult_GemmKey* release_reference_gemm();
  ::tensorflow::AutotuneResult_GemmKey* mutable_reference_gemm();
  void set_allocated_reference_gemm(::tensorflow::AutotuneResult_GemmKey* reference_gemm);

  // .tensorflow.AutotuneResult.CudaConvPlanKey reference_cuda_conv_plan = 14;
  bool has_reference_cuda_conv_plan() const;
  void clear_reference_cuda_conv_plan();
  const ::tensorflow::AutotuneResult_CudaConvPlanKey& reference_cuda_conv_plan() const;
  ::tensorflow::AutotuneResult_CudaConvPlanKey* release_reference_cuda_conv_plan();
  ::tensorflow::AutotuneResult_CudaConvPlanKey* mutable_reference_cuda_conv_plan();
  void set_allocated_reference_cuda_conv_plan(::tensorflow::AutotuneResult_CudaConvPlanKey* reference_cuda_conv_plan);

  void clear_key();
  KeyCase key_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.AutotuneResult.FailureResult)
 private:
  class _Internal;
  void set_has_reference_conv();
  void set_has_reference_gemm();
  void set_has_reference_cuda_conv_plan();

  inline bool has_key() const;
  inline void clear_has_key();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr msg_;
  int kind_;
  ::PROTOBUF_NAMESPACE_ID::int64 buffer_address_;
  union KeyUnion {
    KeyUnion() {}
    ::tensorflow::AutotuneResult_ConvKey* reference_conv_;
    ::tensorflow::AutotuneResult_GemmKey* reference_gemm_;
    ::tensorflow::AutotuneResult_CudaConvPlanKey* reference_cuda_conv_plan_;
  } key_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult_ConvKey :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutotuneResult.ConvKey) */ {
 public:
  AutotuneResult_ConvKey();
  virtual ~AutotuneResult_ConvKey();

  AutotuneResult_ConvKey(const AutotuneResult_ConvKey& from);
  AutotuneResult_ConvKey(AutotuneResult_ConvKey&& from) noexcept
    : AutotuneResult_ConvKey() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_ConvKey& operator=(const AutotuneResult_ConvKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult_ConvKey& operator=(AutotuneResult_ConvKey&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AutotuneResult_ConvKey& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutotuneResult_ConvKey* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_ConvKey*>(
               &_AutotuneResult_ConvKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(AutotuneResult_ConvKey& a, AutotuneResult_ConvKey& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult_ConvKey* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AutotuneResult_ConvKey* New() const final {
    return CreateMaybeMessage<AutotuneResult_ConvKey>(nullptr);
  }

  AutotuneResult_ConvKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AutotuneResult_ConvKey>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AutotuneResult_ConvKey& from);
  void MergeFrom(const AutotuneResult_ConvKey& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_ConvKey* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AutotuneResult.ConvKey";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlgorithmFieldNumber = 1,
    kTensorOpsEnabledFieldNumber = 2,
  };
  // int64 algorithm = 1;
  void clear_algorithm();
  ::PROTOBUF_NAMESPACE_ID::int64 algorithm() const;
  void set_algorithm(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool tensor_ops_enabled = 2;
  void clear_tensor_ops_enabled();
  bool tensor_ops_enabled() const;
  void set_tensor_ops_enabled(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.AutotuneResult.ConvKey)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 algorithm_;
  bool tensor_ops_enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult_GemmKey :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutotuneResult.GemmKey) */ {
 public:
  AutotuneResult_GemmKey();
  virtual ~AutotuneResult_GemmKey();

  AutotuneResult_GemmKey(const AutotuneResult_GemmKey& from);
  AutotuneResult_GemmKey(AutotuneResult_GemmKey&& from) noexcept
    : AutotuneResult_GemmKey() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_GemmKey& operator=(const AutotuneResult_GemmKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult_GemmKey& operator=(AutotuneResult_GemmKey&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AutotuneResult_GemmKey& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutotuneResult_GemmKey* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_GemmKey*>(
               &_AutotuneResult_GemmKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(AutotuneResult_GemmKey& a, AutotuneResult_GemmKey& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult_GemmKey* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AutotuneResult_GemmKey* New() const final {
    return CreateMaybeMessage<AutotuneResult_GemmKey>(nullptr);
  }

  AutotuneResult_GemmKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AutotuneResult_GemmKey>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AutotuneResult_GemmKey& from);
  void MergeFrom(const AutotuneResult_GemmKey& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_GemmKey* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AutotuneResult.GemmKey";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlgorithmFieldNumber = 1,
  };
  // int64 algorithm = 1;
  void clear_algorithm();
  ::PROTOBUF_NAMESPACE_ID::int64 algorithm() const;
  void set_algorithm(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.AutotuneResult.GemmKey)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 algorithm_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult_CudaConvPlanKey :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutotuneResult.CudaConvPlanKey) */ {
 public:
  AutotuneResult_CudaConvPlanKey();
  virtual ~AutotuneResult_CudaConvPlanKey();

  AutotuneResult_CudaConvPlanKey(const AutotuneResult_CudaConvPlanKey& from);
  AutotuneResult_CudaConvPlanKey(AutotuneResult_CudaConvPlanKey&& from) noexcept
    : AutotuneResult_CudaConvPlanKey() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_CudaConvPlanKey& operator=(const AutotuneResult_CudaConvPlanKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult_CudaConvPlanKey& operator=(AutotuneResult_CudaConvPlanKey&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AutotuneResult_CudaConvPlanKey& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutotuneResult_CudaConvPlanKey* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_CudaConvPlanKey*>(
               &_AutotuneResult_CudaConvPlanKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(AutotuneResult_CudaConvPlanKey& a, AutotuneResult_CudaConvPlanKey& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult_CudaConvPlanKey* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AutotuneResult_CudaConvPlanKey* New() const final {
    return CreateMaybeMessage<AutotuneResult_CudaConvPlanKey>(nullptr);
  }

  AutotuneResult_CudaConvPlanKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AutotuneResult_CudaConvPlanKey>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AutotuneResult_CudaConvPlanKey& from);
  void MergeFrom(const AutotuneResult_CudaConvPlanKey& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_CudaConvPlanKey* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AutotuneResult.CudaConvPlanKey";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExecPlanIdFieldNumber = 1,
  };
  // string exec_plan_id = 1;
  void clear_exec_plan_id();
  const std::string& exec_plan_id() const;
  void set_exec_plan_id(const std::string& value);
  void set_exec_plan_id(std::string&& value);
  void set_exec_plan_id(const char* value);
  void set_exec_plan_id(const char* value, size_t size);
  std::string* mutable_exec_plan_id();
  std::string* release_exec_plan_id();
  void set_allocated_exec_plan_id(std::string* exec_plan_id);

  // @@protoc_insertion_point(class_scope:tensorflow.AutotuneResult.CudaConvPlanKey)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr exec_plan_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutotuneResult) */ {
 public:
  AutotuneResult();
  virtual ~AutotuneResult();

  AutotuneResult(const AutotuneResult& from);
  AutotuneResult(AutotuneResult&& from) noexcept
    : AutotuneResult() {
    *this = ::std::move(from);
  }

  inline AutotuneResult& operator=(const AutotuneResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult& operator=(AutotuneResult&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AutotuneResult& default_instance();

  enum KeyCase {
    kConv = 5,
    kGemm = 6,
    kCudaConvPlan = 15,
    KEY_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutotuneResult* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult*>(
               &_AutotuneResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(AutotuneResult& a, AutotuneResult& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AutotuneResult* New() const final {
    return CreateMaybeMessage<AutotuneResult>(nullptr);
  }

  AutotuneResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AutotuneResult>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AutotuneResult& from);
  void MergeFrom(const AutotuneResult& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AutotuneResult";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef AutotuneResult_FailureResult FailureResult;
  typedef AutotuneResult_ConvKey ConvKey;
  typedef AutotuneResult_GemmKey GemmKey;
  typedef AutotuneResult_CudaConvPlanKey CudaConvPlanKey;

  typedef AutotuneResult_FailureKind FailureKind;
  static constexpr FailureKind UNKNOWN =
    AutotuneResult_FailureKind_UNKNOWN;
  static constexpr FailureKind REDZONE_MODIFIED =
    AutotuneResult_FailureKind_REDZONE_MODIFIED;
  static constexpr FailureKind WRONG_RESULT =
    AutotuneResult_FailureKind_WRONG_RESULT;
  static inline bool FailureKind_IsValid(int value) {
    return AutotuneResult_FailureKind_IsValid(value);
  }
  static constexpr FailureKind FailureKind_MIN =
    AutotuneResult_FailureKind_FailureKind_MIN;
  static constexpr FailureKind FailureKind_MAX =
    AutotuneResult_FailureKind_FailureKind_MAX;
  static constexpr int FailureKind_ARRAYSIZE =
    AutotuneResult_FailureKind_FailureKind_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  FailureKind_descriptor() {
    return AutotuneResult_FailureKind_descriptor();
  }
  template<typename T>
  static inline const std::string& FailureKind_Name(T enum_t_value) {
    static_assert(::std::is_same<T, FailureKind>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function FailureKind_Name.");
    return AutotuneResult_FailureKind_Name(enum_t_value);
  }
  static inline bool FailureKind_Parse(const std::string& name,
      FailureKind* value) {
    return AutotuneResult_FailureKind_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kFailureFieldNumber = 7,
    kRunTimeFieldNumber = 9,
    kScratchBytesFieldNumber = 8,
    kConvFieldNumber = 5,
    kGemmFieldNumber = 6,
    kCudaConvPlanFieldNumber = 15,
  };
  // .tensorflow.AutotuneResult.FailureResult failure = 7;
  bool has_failure() const;
  void clear_failure();
  const ::tensorflow::AutotuneResult_FailureResult& failure() const;
  ::tensorflow::AutotuneResult_FailureResult* release_failure();
  ::tensorflow::AutotuneResult_FailureResult* mutable_failure();
  void set_allocated_failure(::tensorflow::AutotuneResult_FailureResult* failure);

  // .google.protobuf.Duration run_time = 9;
  bool has_run_time() const;
  void clear_run_time();
  const PROTOBUF_NAMESPACE_ID::Duration& run_time() const;
  PROTOBUF_NAMESPACE_ID::Duration* release_run_time();
  PROTOBUF_NAMESPACE_ID::Duration* mutable_run_time();
  void set_allocated_run_time(PROTOBUF_NAMESPACE_ID::Duration* run_time);

  // int64 scratch_bytes = 8;
  void clear_scratch_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 scratch_bytes() const;
  void set_scratch_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .tensorflow.AutotuneResult.ConvKey conv = 5;
  bool has_conv() const;
  void clear_conv();
  const ::tensorflow::AutotuneResult_ConvKey& conv() const;
  ::tensorflow::AutotuneResult_ConvKey* release_conv();
  ::tensorflow::AutotuneResult_ConvKey* mutable_conv();
  void set_allocated_conv(::tensorflow::AutotuneResult_ConvKey* conv);

  // .tensorflow.AutotuneResult.GemmKey gemm = 6;
  bool has_gemm() const;
  void clear_gemm();
  const ::tensorflow::AutotuneResult_GemmKey& gemm() const;
  ::tensorflow::AutotuneResult_GemmKey* release_gemm();
  ::tensorflow::AutotuneResult_GemmKey* mutable_gemm();
  void set_allocated_gemm(::tensorflow::AutotuneResult_GemmKey* gemm);

  // .tensorflow.AutotuneResult.CudaConvPlanKey cuda_conv_plan = 15;
  bool has_cuda_conv_plan() const;
  void clear_cuda_conv_plan();
  const ::tensorflow::AutotuneResult_CudaConvPlanKey& cuda_conv_plan() const;
  ::tensorflow::AutotuneResult_CudaConvPlanKey* release_cuda_conv_plan();
  ::tensorflow::AutotuneResult_CudaConvPlanKey* mutable_cuda_conv_plan();
  void set_allocated_cuda_conv_plan(::tensorflow::AutotuneResult_CudaConvPlanKey* cuda_conv_plan);

  void clear_key();
  KeyCase key_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.AutotuneResult)
 private:
  class _Internal;
  void set_has_conv();
  void set_has_gemm();
  void set_has_cuda_conv_plan();

  inline bool has_key() const;
  inline void clear_has_key();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::AutotuneResult_FailureResult* failure_;
  PROTOBUF_NAMESPACE_ID::Duration* run_time_;
  ::PROTOBUF_NAMESPACE_ID::int64 scratch_bytes_;
  union KeyUnion {
    KeyUnion() {}
    ::tensorflow::AutotuneResult_ConvKey* conv_;
    ::tensorflow::AutotuneResult_GemmKey* gemm_;
    ::tensorflow::AutotuneResult_CudaConvPlanKey* cuda_conv_plan_;
  } key_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuningLog :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutotuningLog) */ {
 public:
  AutotuningLog();
  virtual ~AutotuningLog();

  AutotuningLog(const AutotuningLog& from);
  AutotuningLog(AutotuningLog&& from) noexcept
    : AutotuningLog() {
    *this = ::std::move(from);
  }

  inline AutotuningLog& operator=(const AutotuningLog& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuningLog& operator=(AutotuningLog&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AutotuningLog& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutotuningLog* internal_default_instance() {
    return reinterpret_cast<const AutotuningLog*>(
               &_AutotuningLog_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(AutotuningLog& a, AutotuningLog& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuningLog* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AutotuningLog* New() const final {
    return CreateMaybeMessage<AutotuningLog>(nullptr);
  }

  AutotuningLog* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AutotuningLog>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AutotuningLog& from);
  void MergeFrom(const AutotuningLog& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuningLog* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AutotuningLog";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kResultsFieldNumber = 2,
    kDevicePciBusIdFieldNumber = 5,
    kBlasVersionFieldNumber = 6,
    kInstrFieldNumber = 1,
    kCudnnVersionFieldNumber = 3,
    kComputeCapabilityFieldNumber = 4,
  };
  // repeated .tensorflow.AutotuneResult results = 2;
  int results_size() const;
  void clear_results();
  ::tensorflow::AutotuneResult* mutable_results(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AutotuneResult >*
      mutable_results();
  const ::tensorflow::AutotuneResult& results(int index) const;
  ::tensorflow::AutotuneResult* add_results();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AutotuneResult >&
      results() const;

  // string device_pci_bus_id = 5;
  void clear_device_pci_bus_id();
  const std::string& device_pci_bus_id() const;
  void set_device_pci_bus_id(const std::string& value);
  void set_device_pci_bus_id(std::string&& value);
  void set_device_pci_bus_id(const char* value);
  void set_device_pci_bus_id(const char* value, size_t size);
  std::string* mutable_device_pci_bus_id();
  std::string* release_device_pci_bus_id();
  void set_allocated_device_pci_bus_id(std::string* device_pci_bus_id);

  // string blas_version = 6;
  void clear_blas_version();
  const std::string& blas_version() const;
  void set_blas_version(const std::string& value);
  void set_blas_version(std::string&& value);
  void set_blas_version(const char* value);
  void set_blas_version(const char* value, size_t size);
  std::string* mutable_blas_version();
  std::string* release_blas_version();
  void set_allocated_blas_version(std::string* blas_version);

  // .google.protobuf.Any instr = 1;
  bool has_instr() const;
  void clear_instr();
  const PROTOBUF_NAMESPACE_ID::Any& instr() const;
  PROTOBUF_NAMESPACE_ID::Any* release_instr();
  PROTOBUF_NAMESPACE_ID::Any* mutable_instr();
  void set_allocated_instr(PROTOBUF_NAMESPACE_ID::Any* instr);

  // .tensorflow.CudnnVersion cudnn_version = 3;
  bool has_cudnn_version() const;
  void clear_cudnn_version();
  const ::tensorflow::CudnnVersion& cudnn_version() const;
  ::tensorflow::CudnnVersion* release_cudnn_version();
  ::tensorflow::CudnnVersion* mutable_cudnn_version();
  void set_allocated_cudnn_version(::tensorflow::CudnnVersion* cudnn_version);

  // .tensorflow.ComputeCapability compute_capability = 4;
  bool has_compute_capability() const;
  void clear_compute_capability();
  const ::tensorflow::ComputeCapability& compute_capability() const;
  ::tensorflow::ComputeCapability* release_compute_capability();
  ::tensorflow::ComputeCapability* mutable_compute_capability();
  void set_allocated_compute_capability(::tensorflow::ComputeCapability* compute_capability);

  // @@protoc_insertion_point(class_scope:tensorflow.AutotuningLog)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AutotuneResult > results_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_pci_bus_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr blas_version_;
  PROTOBUF_NAMESPACE_ID::Any* instr_;
  ::tensorflow::CudnnVersion* cudnn_version_;
  ::tensorflow::ComputeCapability* compute_capability_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CudnnVersion

// int32 major = 1;
inline void CudnnVersion::clear_major() {
  major_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CudnnVersion::major() const {
  // @@protoc_insertion_point(field_get:tensorflow.CudnnVersion.major)
  return major_;
}
inline void CudnnVersion::set_major(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  major_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CudnnVersion.major)
}

// int32 minor = 2;
inline void CudnnVersion::clear_minor() {
  minor_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CudnnVersion::minor() const {
  // @@protoc_insertion_point(field_get:tensorflow.CudnnVersion.minor)
  return minor_;
}
inline void CudnnVersion::set_minor(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  minor_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CudnnVersion.minor)
}

// int32 patch = 3;
inline void CudnnVersion::clear_patch() {
  patch_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CudnnVersion::patch() const {
  // @@protoc_insertion_point(field_get:tensorflow.CudnnVersion.patch)
  return patch_;
}
inline void CudnnVersion::set_patch(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  patch_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CudnnVersion.patch)
}

// -------------------------------------------------------------------

// ComputeCapability

// int32 major = 1;
inline void ComputeCapability::clear_major() {
  major_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ComputeCapability::major() const {
  // @@protoc_insertion_point(field_get:tensorflow.ComputeCapability.major)
  return major_;
}
inline void ComputeCapability::set_major(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  major_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ComputeCapability.major)
}

// int32 minor = 2;
inline void ComputeCapability::clear_minor() {
  minor_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ComputeCapability::minor() const {
  // @@protoc_insertion_point(field_get:tensorflow.ComputeCapability.minor)
  return minor_;
}
inline void ComputeCapability::set_minor(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  minor_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ComputeCapability.minor)
}

// -------------------------------------------------------------------

// AutotuneResult_FailureResult

// .tensorflow.AutotuneResult.FailureKind kind = 1;
inline void AutotuneResult_FailureResult::clear_kind() {
  kind_ = 0;
}
inline ::tensorflow::AutotuneResult_FailureKind AutotuneResult_FailureResult::kind() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.FailureResult.kind)
  return static_cast< ::tensorflow::AutotuneResult_FailureKind >(kind_);
}
inline void AutotuneResult_FailureResult::set_kind(::tensorflow::AutotuneResult_FailureKind value) {
  
  kind_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.FailureResult.kind)
}

// string msg = 2;
inline void AutotuneResult_FailureResult::clear_msg() {
  msg_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& AutotuneResult_FailureResult::msg() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.FailureResult.msg)
  return msg_.GetNoArena();
}
inline void AutotuneResult_FailureResult::set_msg(const std::string& value) {
  
  msg_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.FailureResult.msg)
}
inline void AutotuneResult_FailureResult::set_msg(std::string&& value) {
  
  msg_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AutotuneResult.FailureResult.msg)
}
inline void AutotuneResult_FailureResult::set_msg(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  msg_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.AutotuneResult.FailureResult.msg)
}
inline void AutotuneResult_FailureResult::set_msg(const char* value, size_t size) {
  
  msg_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AutotuneResult.FailureResult.msg)
}
inline std::string* AutotuneResult_FailureResult::mutable_msg() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.FailureResult.msg)
  return msg_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AutotuneResult_FailureResult::release_msg() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.FailureResult.msg)
  
  return msg_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AutotuneResult_FailureResult::set_allocated_msg(std::string* msg) {
  if (msg != nullptr) {
    
  } else {
    
  }
  msg_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), msg);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuneResult.FailureResult.msg)
}

// .tensorflow.AutotuneResult.ConvKey reference_conv = 11;
inline bool AutotuneResult_FailureResult::has_reference_conv() const {
  return key_case() == kReferenceConv;
}
inline void AutotuneResult_FailureResult::set_has_reference_conv() {
  _oneof_case_[0] = kReferenceConv;
}
inline void AutotuneResult_FailureResult::clear_reference_conv() {
  if (has_reference_conv()) {
    delete key_.reference_conv_;
    clear_has_key();
  }
}
inline ::tensorflow::AutotuneResult_ConvKey* AutotuneResult_FailureResult::release_reference_conv() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.FailureResult.reference_conv)
  if (has_reference_conv()) {
    clear_has_key();
      ::tensorflow::AutotuneResult_ConvKey* temp = key_.reference_conv_;
    key_.reference_conv_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::AutotuneResult_ConvKey& AutotuneResult_FailureResult::reference_conv() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.FailureResult.reference_conv)
  return has_reference_conv()
      ? *key_.reference_conv_
      : *reinterpret_cast< ::tensorflow::AutotuneResult_ConvKey*>(&::tensorflow::_AutotuneResult_ConvKey_default_instance_);
}
inline ::tensorflow::AutotuneResult_ConvKey* AutotuneResult_FailureResult::mutable_reference_conv() {
  if (!has_reference_conv()) {
    clear_key();
    set_has_reference_conv();
    key_.reference_conv_ = CreateMaybeMessage< ::tensorflow::AutotuneResult_ConvKey >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.FailureResult.reference_conv)
  return key_.reference_conv_;
}

// .tensorflow.AutotuneResult.GemmKey reference_gemm = 12;
inline bool AutotuneResult_FailureResult::has_reference_gemm() const {
  return key_case() == kReferenceGemm;
}
inline void AutotuneResult_FailureResult::set_has_reference_gemm() {
  _oneof_case_[0] = kReferenceGemm;
}
inline void AutotuneResult_FailureResult::clear_reference_gemm() {
  if (has_reference_gemm()) {
    delete key_.reference_gemm_;
    clear_has_key();
  }
}
inline ::tensorflow::AutotuneResult_GemmKey* AutotuneResult_FailureResult::release_reference_gemm() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.FailureResult.reference_gemm)
  if (has_reference_gemm()) {
    clear_has_key();
      ::tensorflow::AutotuneResult_GemmKey* temp = key_.reference_gemm_;
    key_.reference_gemm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::AutotuneResult_GemmKey& AutotuneResult_FailureResult::reference_gemm() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.FailureResult.reference_gemm)
  return has_reference_gemm()
      ? *key_.reference_gemm_
      : *reinterpret_cast< ::tensorflow::AutotuneResult_GemmKey*>(&::tensorflow::_AutotuneResult_GemmKey_default_instance_);
}
inline ::tensorflow::AutotuneResult_GemmKey* AutotuneResult_FailureResult::mutable_reference_gemm() {
  if (!has_reference_gemm()) {
    clear_key();
    set_has_reference_gemm();
    key_.reference_gemm_ = CreateMaybeMessage< ::tensorflow::AutotuneResult_GemmKey >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.FailureResult.reference_gemm)
  return key_.reference_gemm_;
}

// .tensorflow.AutotuneResult.CudaConvPlanKey reference_cuda_conv_plan = 14;
inline bool AutotuneResult_FailureResult::has_reference_cuda_conv_plan() const {
  return key_case() == kReferenceCudaConvPlan;
}
inline void AutotuneResult_FailureResult::set_has_reference_cuda_conv_plan() {
  _oneof_case_[0] = kReferenceCudaConvPlan;
}
inline void AutotuneResult_FailureResult::clear_reference_cuda_conv_plan() {
  if (has_reference_cuda_conv_plan()) {
    delete key_.reference_cuda_conv_plan_;
    clear_has_key();
  }
}
inline ::tensorflow::AutotuneResult_CudaConvPlanKey* AutotuneResult_FailureResult::release_reference_cuda_conv_plan() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.FailureResult.reference_cuda_conv_plan)
  if (has_reference_cuda_conv_plan()) {
    clear_has_key();
      ::tensorflow::AutotuneResult_CudaConvPlanKey* temp = key_.reference_cuda_conv_plan_;
    key_.reference_cuda_conv_plan_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::AutotuneResult_CudaConvPlanKey& AutotuneResult_FailureResult::reference_cuda_conv_plan() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.FailureResult.reference_cuda_conv_plan)
  return has_reference_cuda_conv_plan()
      ? *key_.reference_cuda_conv_plan_
      : *reinterpret_cast< ::tensorflow::AutotuneResult_CudaConvPlanKey*>(&::tensorflow::_AutotuneResult_CudaConvPlanKey_default_instance_);
}
inline ::tensorflow::AutotuneResult_CudaConvPlanKey* AutotuneResult_FailureResult::mutable_reference_cuda_conv_plan() {
  if (!has_reference_cuda_conv_plan()) {
    clear_key();
    set_has_reference_cuda_conv_plan();
    key_.reference_cuda_conv_plan_ = CreateMaybeMessage< ::tensorflow::AutotuneResult_CudaConvPlanKey >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.FailureResult.reference_cuda_conv_plan)
  return key_.reference_cuda_conv_plan_;
}

// int64 buffer_address = 13;
inline void AutotuneResult_FailureResult::clear_buffer_address() {
  buffer_address_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AutotuneResult_FailureResult::buffer_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.FailureResult.buffer_address)
  return buffer_address_;
}
inline void AutotuneResult_FailureResult::set_buffer_address(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  buffer_address_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.FailureResult.buffer_address)
}

inline bool AutotuneResult_FailureResult::has_key() const {
  return key_case() != KEY_NOT_SET;
}
inline void AutotuneResult_FailureResult::clear_has_key() {
  _oneof_case_[0] = KEY_NOT_SET;
}
inline AutotuneResult_FailureResult::KeyCase AutotuneResult_FailureResult::key_case() const {
  return AutotuneResult_FailureResult::KeyCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// AutotuneResult_ConvKey

// int64 algorithm = 1;
inline void AutotuneResult_ConvKey::clear_algorithm() {
  algorithm_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AutotuneResult_ConvKey::algorithm() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.ConvKey.algorithm)
  return algorithm_;
}
inline void AutotuneResult_ConvKey::set_algorithm(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  algorithm_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.ConvKey.algorithm)
}

// bool tensor_ops_enabled = 2;
inline void AutotuneResult_ConvKey::clear_tensor_ops_enabled() {
  tensor_ops_enabled_ = false;
}
inline bool AutotuneResult_ConvKey::tensor_ops_enabled() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.ConvKey.tensor_ops_enabled)
  return tensor_ops_enabled_;
}
inline void AutotuneResult_ConvKey::set_tensor_ops_enabled(bool value) {
  
  tensor_ops_enabled_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.ConvKey.tensor_ops_enabled)
}

// -------------------------------------------------------------------

// AutotuneResult_GemmKey

// int64 algorithm = 1;
inline void AutotuneResult_GemmKey::clear_algorithm() {
  algorithm_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AutotuneResult_GemmKey::algorithm() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.GemmKey.algorithm)
  return algorithm_;
}
inline void AutotuneResult_GemmKey::set_algorithm(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  algorithm_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.GemmKey.algorithm)
}

// -------------------------------------------------------------------

// AutotuneResult_CudaConvPlanKey

// string exec_plan_id = 1;
inline void AutotuneResult_CudaConvPlanKey::clear_exec_plan_id() {
  exec_plan_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& AutotuneResult_CudaConvPlanKey::exec_plan_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.CudaConvPlanKey.exec_plan_id)
  return exec_plan_id_.GetNoArena();
}
inline void AutotuneResult_CudaConvPlanKey::set_exec_plan_id(const std::string& value) {
  
  exec_plan_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.CudaConvPlanKey.exec_plan_id)
}
inline void AutotuneResult_CudaConvPlanKey::set_exec_plan_id(std::string&& value) {
  
  exec_plan_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AutotuneResult.CudaConvPlanKey.exec_plan_id)
}
inline void AutotuneResult_CudaConvPlanKey::set_exec_plan_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  exec_plan_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.AutotuneResult.CudaConvPlanKey.exec_plan_id)
}
inline void AutotuneResult_CudaConvPlanKey::set_exec_plan_id(const char* value, size_t size) {
  
  exec_plan_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AutotuneResult.CudaConvPlanKey.exec_plan_id)
}
inline std::string* AutotuneResult_CudaConvPlanKey::mutable_exec_plan_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.CudaConvPlanKey.exec_plan_id)
  return exec_plan_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AutotuneResult_CudaConvPlanKey::release_exec_plan_id() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.CudaConvPlanKey.exec_plan_id)
  
  return exec_plan_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AutotuneResult_CudaConvPlanKey::set_allocated_exec_plan_id(std::string* exec_plan_id) {
  if (exec_plan_id != nullptr) {
    
  } else {
    
  }
  exec_plan_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), exec_plan_id);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuneResult.CudaConvPlanKey.exec_plan_id)
}

// -------------------------------------------------------------------

// AutotuneResult

// int64 scratch_bytes = 8;
inline void AutotuneResult::clear_scratch_bytes() {
  scratch_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AutotuneResult::scratch_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.scratch_bytes)
  return scratch_bytes_;
}
inline void AutotuneResult::set_scratch_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  scratch_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.scratch_bytes)
}

// .google.protobuf.Duration run_time = 9;
inline bool AutotuneResult::has_run_time() const {
  return this != internal_default_instance() && run_time_ != nullptr;
}
inline const PROTOBUF_NAMESPACE_ID::Duration& AutotuneResult::run_time() const {
  const PROTOBUF_NAMESPACE_ID::Duration* p = run_time_;
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.run_time)
  return p != nullptr ? *p : *reinterpret_cast<const PROTOBUF_NAMESPACE_ID::Duration*>(
      &PROTOBUF_NAMESPACE_ID::_Duration_default_instance_);
}
inline PROTOBUF_NAMESPACE_ID::Duration* AutotuneResult::release_run_time() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.run_time)
  
  PROTOBUF_NAMESPACE_ID::Duration* temp = run_time_;
  run_time_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::Duration* AutotuneResult::mutable_run_time() {
  
  if (run_time_ == nullptr) {
    auto* p = CreateMaybeMessage<PROTOBUF_NAMESPACE_ID::Duration>(GetArenaNoVirtual());
    run_time_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.run_time)
  return run_time_;
}
inline void AutotuneResult::set_allocated_run_time(PROTOBUF_NAMESPACE_ID::Duration* run_time) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(run_time_);
  }
  if (run_time) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(run_time)->GetArena();
    if (message_arena != submessage_arena) {
      run_time = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, run_time, submessage_arena);
    }
    
  } else {
    
  }
  run_time_ = run_time;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuneResult.run_time)
}

// .tensorflow.AutotuneResult.FailureResult failure = 7;
inline bool AutotuneResult::has_failure() const {
  return this != internal_default_instance() && failure_ != nullptr;
}
inline void AutotuneResult::clear_failure() {
  if (GetArenaNoVirtual() == nullptr && failure_ != nullptr) {
    delete failure_;
  }
  failure_ = nullptr;
}
inline const ::tensorflow::AutotuneResult_FailureResult& AutotuneResult::failure() const {
  const ::tensorflow::AutotuneResult_FailureResult* p = failure_;
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.failure)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::AutotuneResult_FailureResult*>(
      &::tensorflow::_AutotuneResult_FailureResult_default_instance_);
}
inline ::tensorflow::AutotuneResult_FailureResult* AutotuneResult::release_failure() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.failure)
  
  ::tensorflow::AutotuneResult_FailureResult* temp = failure_;
  failure_ = nullptr;
  return temp;
}
inline ::tensorflow::AutotuneResult_FailureResult* AutotuneResult::mutable_failure() {
  
  if (failure_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::AutotuneResult_FailureResult>(GetArenaNoVirtual());
    failure_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.failure)
  return failure_;
}
inline void AutotuneResult::set_allocated_failure(::tensorflow::AutotuneResult_FailureResult* failure) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete failure_;
  }
  if (failure) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      failure = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, failure, submessage_arena);
    }
    
  } else {
    
  }
  failure_ = failure;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuneResult.failure)
}

// .tensorflow.AutotuneResult.ConvKey conv = 5;
inline bool AutotuneResult::has_conv() const {
  return key_case() == kConv;
}
inline void AutotuneResult::set_has_conv() {
  _oneof_case_[0] = kConv;
}
inline void AutotuneResult::clear_conv() {
  if (has_conv()) {
    delete key_.conv_;
    clear_has_key();
  }
}
inline ::tensorflow::AutotuneResult_ConvKey* AutotuneResult::release_conv() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.conv)
  if (has_conv()) {
    clear_has_key();
      ::tensorflow::AutotuneResult_ConvKey* temp = key_.conv_;
    key_.conv_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::AutotuneResult_ConvKey& AutotuneResult::conv() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.conv)
  return has_conv()
      ? *key_.conv_
      : *reinterpret_cast< ::tensorflow::AutotuneResult_ConvKey*>(&::tensorflow::_AutotuneResult_ConvKey_default_instance_);
}
inline ::tensorflow::AutotuneResult_ConvKey* AutotuneResult::mutable_conv() {
  if (!has_conv()) {
    clear_key();
    set_has_conv();
    key_.conv_ = CreateMaybeMessage< ::tensorflow::AutotuneResult_ConvKey >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.conv)
  return key_.conv_;
}

// .tensorflow.AutotuneResult.GemmKey gemm = 6;
inline bool AutotuneResult::has_gemm() const {
  return key_case() == kGemm;
}
inline void AutotuneResult::set_has_gemm() {
  _oneof_case_[0] = kGemm;
}
inline void AutotuneResult::clear_gemm() {
  if (has_gemm()) {
    delete key_.gemm_;
    clear_has_key();
  }
}
inline ::tensorflow::AutotuneResult_GemmKey* AutotuneResult::release_gemm() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.gemm)
  if (has_gemm()) {
    clear_has_key();
      ::tensorflow::AutotuneResult_GemmKey* temp = key_.gemm_;
    key_.gemm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::AutotuneResult_GemmKey& AutotuneResult::gemm() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.gemm)
  return has_gemm()
      ? *key_.gemm_
      : *reinterpret_cast< ::tensorflow::AutotuneResult_GemmKey*>(&::tensorflow::_AutotuneResult_GemmKey_default_instance_);
}
inline ::tensorflow::AutotuneResult_GemmKey* AutotuneResult::mutable_gemm() {
  if (!has_gemm()) {
    clear_key();
    set_has_gemm();
    key_.gemm_ = CreateMaybeMessage< ::tensorflow::AutotuneResult_GemmKey >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.gemm)
  return key_.gemm_;
}

// .tensorflow.AutotuneResult.CudaConvPlanKey cuda_conv_plan = 15;
inline bool AutotuneResult::has_cuda_conv_plan() const {
  return key_case() == kCudaConvPlan;
}
inline void AutotuneResult::set_has_cuda_conv_plan() {
  _oneof_case_[0] = kCudaConvPlan;
}
inline void AutotuneResult::clear_cuda_conv_plan() {
  if (has_cuda_conv_plan()) {
    delete key_.cuda_conv_plan_;
    clear_has_key();
  }
}
inline ::tensorflow::AutotuneResult_CudaConvPlanKey* AutotuneResult::release_cuda_conv_plan() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.cuda_conv_plan)
  if (has_cuda_conv_plan()) {
    clear_has_key();
      ::tensorflow::AutotuneResult_CudaConvPlanKey* temp = key_.cuda_conv_plan_;
    key_.cuda_conv_plan_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::AutotuneResult_CudaConvPlanKey& AutotuneResult::cuda_conv_plan() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.cuda_conv_plan)
  return has_cuda_conv_plan()
      ? *key_.cuda_conv_plan_
      : *reinterpret_cast< ::tensorflow::AutotuneResult_CudaConvPlanKey*>(&::tensorflow::_AutotuneResult_CudaConvPlanKey_default_instance_);
}
inline ::tensorflow::AutotuneResult_CudaConvPlanKey* AutotuneResult::mutable_cuda_conv_plan() {
  if (!has_cuda_conv_plan()) {
    clear_key();
    set_has_cuda_conv_plan();
    key_.cuda_conv_plan_ = CreateMaybeMessage< ::tensorflow::AutotuneResult_CudaConvPlanKey >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.cuda_conv_plan)
  return key_.cuda_conv_plan_;
}

inline bool AutotuneResult::has_key() const {
  return key_case() != KEY_NOT_SET;
}
inline void AutotuneResult::clear_has_key() {
  _oneof_case_[0] = KEY_NOT_SET;
}
inline AutotuneResult::KeyCase AutotuneResult::key_case() const {
  return AutotuneResult::KeyCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// AutotuningLog

// .google.protobuf.Any instr = 1;
inline bool AutotuningLog::has_instr() const {
  return this != internal_default_instance() && instr_ != nullptr;
}
inline const PROTOBUF_NAMESPACE_ID::Any& AutotuningLog::instr() const {
  const PROTOBUF_NAMESPACE_ID::Any* p = instr_;
  // @@protoc_insertion_point(field_get:tensorflow.AutotuningLog.instr)
  return p != nullptr ? *p : *reinterpret_cast<const PROTOBUF_NAMESPACE_ID::Any*>(
      &PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline PROTOBUF_NAMESPACE_ID::Any* AutotuningLog::release_instr() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuningLog.instr)
  
  PROTOBUF_NAMESPACE_ID::Any* temp = instr_;
  instr_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::Any* AutotuningLog::mutable_instr() {
  
  if (instr_ == nullptr) {
    auto* p = CreateMaybeMessage<PROTOBUF_NAMESPACE_ID::Any>(GetArenaNoVirtual());
    instr_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuningLog.instr)
  return instr_;
}
inline void AutotuningLog::set_allocated_instr(PROTOBUF_NAMESPACE_ID::Any* instr) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(instr_);
  }
  if (instr) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      instr = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, instr, submessage_arena);
    }
    
  } else {
    
  }
  instr_ = instr;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuningLog.instr)
}

// repeated .tensorflow.AutotuneResult results = 2;
inline int AutotuningLog::results_size() const {
  return results_.size();
}
inline void AutotuningLog::clear_results() {
  results_.Clear();
}
inline ::tensorflow::AutotuneResult* AutotuningLog::mutable_results(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuningLog.results)
  return results_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AutotuneResult >*
AutotuningLog::mutable_results() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AutotuningLog.results)
  return &results_;
}
inline const ::tensorflow::AutotuneResult& AutotuningLog::results(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuningLog.results)
  return results_.Get(index);
}
inline ::tensorflow::AutotuneResult* AutotuningLog::add_results() {
  // @@protoc_insertion_point(field_add:tensorflow.AutotuningLog.results)
  return results_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AutotuneResult >&
AutotuningLog::results() const {
  // @@protoc_insertion_point(field_list:tensorflow.AutotuningLog.results)
  return results_;
}

// .tensorflow.CudnnVersion cudnn_version = 3;
inline bool AutotuningLog::has_cudnn_version() const {
  return this != internal_default_instance() && cudnn_version_ != nullptr;
}
inline void AutotuningLog::clear_cudnn_version() {
  if (GetArenaNoVirtual() == nullptr && cudnn_version_ != nullptr) {
    delete cudnn_version_;
  }
  cudnn_version_ = nullptr;
}
inline const ::tensorflow::CudnnVersion& AutotuningLog::cudnn_version() const {
  const ::tensorflow::CudnnVersion* p = cudnn_version_;
  // @@protoc_insertion_point(field_get:tensorflow.AutotuningLog.cudnn_version)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::CudnnVersion*>(
      &::tensorflow::_CudnnVersion_default_instance_);
}
inline ::tensorflow::CudnnVersion* AutotuningLog::release_cudnn_version() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuningLog.cudnn_version)
  
  ::tensorflow::CudnnVersion* temp = cudnn_version_;
  cudnn_version_ = nullptr;
  return temp;
}
inline ::tensorflow::CudnnVersion* AutotuningLog::mutable_cudnn_version() {
  
  if (cudnn_version_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CudnnVersion>(GetArenaNoVirtual());
    cudnn_version_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuningLog.cudnn_version)
  return cudnn_version_;
}
inline void AutotuningLog::set_allocated_cudnn_version(::tensorflow::CudnnVersion* cudnn_version) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete cudnn_version_;
  }
  if (cudnn_version) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      cudnn_version = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cudnn_version, submessage_arena);
    }
    
  } else {
    
  }
  cudnn_version_ = cudnn_version;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuningLog.cudnn_version)
}

// .tensorflow.ComputeCapability compute_capability = 4;
inline bool AutotuningLog::has_compute_capability() const {
  return this != internal_default_instance() && compute_capability_ != nullptr;
}
inline void AutotuningLog::clear_compute_capability() {
  if (GetArenaNoVirtual() == nullptr && compute_capability_ != nullptr) {
    delete compute_capability_;
  }
  compute_capability_ = nullptr;
}
inline const ::tensorflow::ComputeCapability& AutotuningLog::compute_capability() const {
  const ::tensorflow::ComputeCapability* p = compute_capability_;
  // @@protoc_insertion_point(field_get:tensorflow.AutotuningLog.compute_capability)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ComputeCapability*>(
      &::tensorflow::_ComputeCapability_default_instance_);
}
inline ::tensorflow::ComputeCapability* AutotuningLog::release_compute_capability() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuningLog.compute_capability)
  
  ::tensorflow::ComputeCapability* temp = compute_capability_;
  compute_capability_ = nullptr;
  return temp;
}
inline ::tensorflow::ComputeCapability* AutotuningLog::mutable_compute_capability() {
  
  if (compute_capability_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ComputeCapability>(GetArenaNoVirtual());
    compute_capability_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuningLog.compute_capability)
  return compute_capability_;
}
inline void AutotuningLog::set_allocated_compute_capability(::tensorflow::ComputeCapability* compute_capability) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete compute_capability_;
  }
  if (compute_capability) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      compute_capability = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, compute_capability, submessage_arena);
    }
    
  } else {
    
  }
  compute_capability_ = compute_capability;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuningLog.compute_capability)
}

// string device_pci_bus_id = 5;
inline void AutotuningLog::clear_device_pci_bus_id() {
  device_pci_bus_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& AutotuningLog::device_pci_bus_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuningLog.device_pci_bus_id)
  return device_pci_bus_id_.GetNoArena();
}
inline void AutotuningLog::set_device_pci_bus_id(const std::string& value) {
  
  device_pci_bus_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.AutotuningLog.device_pci_bus_id)
}
inline void AutotuningLog::set_device_pci_bus_id(std::string&& value) {
  
  device_pci_bus_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AutotuningLog.device_pci_bus_id)
}
inline void AutotuningLog::set_device_pci_bus_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_pci_bus_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.AutotuningLog.device_pci_bus_id)
}
inline void AutotuningLog::set_device_pci_bus_id(const char* value, size_t size) {
  
  device_pci_bus_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AutotuningLog.device_pci_bus_id)
}
inline std::string* AutotuningLog::mutable_device_pci_bus_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuningLog.device_pci_bus_id)
  return device_pci_bus_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AutotuningLog::release_device_pci_bus_id() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuningLog.device_pci_bus_id)
  
  return device_pci_bus_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AutotuningLog::set_allocated_device_pci_bus_id(std::string* device_pci_bus_id) {
  if (device_pci_bus_id != nullptr) {
    
  } else {
    
  }
  device_pci_bus_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_pci_bus_id);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuningLog.device_pci_bus_id)
}

// string blas_version = 6;
inline void AutotuningLog::clear_blas_version() {
  blas_version_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& AutotuningLog::blas_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuningLog.blas_version)
  return blas_version_.GetNoArena();
}
inline void AutotuningLog::set_blas_version(const std::string& value) {
  
  blas_version_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.AutotuningLog.blas_version)
}
inline void AutotuningLog::set_blas_version(std::string&& value) {
  
  blas_version_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AutotuningLog.blas_version)
}
inline void AutotuningLog::set_blas_version(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  blas_version_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.AutotuningLog.blas_version)
}
inline void AutotuningLog::set_blas_version(const char* value, size_t size) {
  
  blas_version_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AutotuningLog.blas_version)
}
inline std::string* AutotuningLog::mutable_blas_version() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuningLog.blas_version)
  return blas_version_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AutotuningLog::release_blas_version() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuningLog.blas_version)
  
  return blas_version_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AutotuningLog::set_allocated_blas_version(std::string* blas_version) {
  if (blas_version != nullptr) {
    
  } else {
    
  }
  blas_version_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), blas_version);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuningLog.blas_version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::AutotuneResult_FailureKind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::AutotuneResult_FailureKind>() {
  return ::tensorflow::AutotuneResult_FailureKind_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto
