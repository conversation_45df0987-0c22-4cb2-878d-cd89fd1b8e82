<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>24小时心电报告</title>
    <style>
        .section {
            margin: 20px 0;
        }
        .section h1 {
            text-align: center;
        }
        .section h2 {
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        .note {
            font-style: italic;
            color: #666;
        }
        h1 {
            text-align: center;
        }
        h2 {
            color: #569be0;
        }
        table{
            width: 100%;
        }
        td, tr {
            line-height: 20px; /* 设置行高 */
            vertical-align: middle; /* 文本垂直居中 */
            word-wrap: break-word; /* 允许强制换行 */
            white-space: normal; /* 默认换行 */
            overflow-wrap: break-word;
            padding: 10px 8px;
        }
        td:first-child {
            width: 25%;
        }
        
        /* 表格样式 */
        .full-bordered-table {
          width: 100%; /* 表格宽度 */
          border-collapse: collapse; /* 合并边框 */
          margin: 0;
          padding: 0;
        }
        
        /* 表格头部和单元格的边框 */
        .full-bordered-table th,
        .full-bordered-table td {
          border: 1px solid #000; /* 设置边框颜色和宽度 */
          padding: 8px; /* 单元格内边距 */
          text-align: left; /* 文本对齐方式 */
        }
        
        /* 表头样式 */
        .full-bordered-table th {
          font-weight: bold; /* 表头字体加粗 */
        }
        .preserve-newlines {
            white-space: pre-line; /* 保留换行符，但忽略多余的空格 */
        }
    </style>
</head>
<body style="line-height: 1.5">
    <h1>24小时心电报告</h1>
    <div class="section">
        <h2>基本信息</h2>
        <table>
            <tr>
                <td>开始时间：</td>
                <td>{{ report.start_time }}</td>
            </tr>
            <tr>
                <td>记录时长：</td>
                <td>{{ report.record_duration }}</td>
            </tr>
            <tr>
                <td>有效时长：</td>
                <td>{{ report.effective_duration }}</td>
            </tr>
            <tr>
                <td>分析时间：</td>
                <td>{{ report.analysis_time }}</td>
            </tr>
            <tr>
                <td>信号质量：</td>
                <td>{{ report.signal_quality }}</td>
            </tr>
            <tr>
                <td>心脏年龄：</td>
                <td>{{ report.ecg_age }}</td>
            </tr>
        </table>
    </div>

    <div class="section">
        <h2>报告概述</h2>
        <table>
            <tr>
                <td colspan="2"><b>心率：</b>心脏每分钟跳动的次数</td>
            </tr>
            <tr>
                <td>平均心率</td>
                <td>{{ report.hr_mean }}</td>
            </tr>
            <tr>
                <td>最大心率</td>
                <td>{{ report.hr_max }}</td>
            </tr>
            <tr>
                <td>最慢心率</td>
                <td>{{ report.hr_min }}</td>
            </tr>
            <tr>
                <td>正常范围</td>
                <td>60 - 100 次 / 分钟</td>
            </tr>
            <tr>
                <td>解读</td>
                <td>低于 60 次 / 分钟为心动过缓，高于 100 次 / 分钟为心动过速，都可能与心脏疾病或其他健康问题有关</td>
            </tr>
            <tr>
                <td colspan="2"><b>总心搏数量</b></td>
            </tr>
            <tr>
                <td>测量值：</td>
                <td >{{ report.total_heart_beat }}</td>
            </tr>
            <tr>
                <td>解读</td>
                <td>该数值代表在本次监测期间心脏跳动的总次数，反映心脏在一段时间内的整体活动量。总心搏数量的异常增多或减少，都可能暗示心脏功能出现问题，比如运动、情绪激动等生理因素，或者心脏疾病、内分泌失调等病理因素都可能导致其变化。
                </td>
            </tr>
            <tr>
                <td colspan="2"><b>异常心搏数量</b></td>
            </tr>
            <tr>
                <td>测量值：</td>
                <td >{{ report.abnormal_heart_beat }}</td>
            </tr>
            <tr>
                <td>解读</td>
                <td>
                    异常心搏是指与正常心脏跳动节律、频率不同的搏动。这一数值体现了心脏跳动不规律的程度，异常心搏数量越多，说明心脏节律紊乱的情况可能越严重，需要进一步排查心律失常等心脏疾病。
                </td>
            </tr>
            <tr>
                <td colspan="2"><b>异常心率相关</b></td>
            </tr>
            {% for abnormal_info in report.abnormal_infos %}
            <tr>
                <td>{{ abnormal_info.symptom }}心搏数量：</td>
                <td>{{ abnormal_info.heart_beat }}</td>
            </tr>
            <tr>
                <td>{{ abnormal_info.symptom }}持续时间：</td>
                <td>{{ abnormal_info.duration }} 分钟</td>
            </tr>
            {% endfor %}
            <tr>
                <td>解读</td>
                <td>
                    这些数据分别展示了不同类型异常心律的发生情况。心搏数量和持续时间反映了异常心律发作的频繁程度和持续状态，对于判断心律失常的类型、严重程度以及制定治疗方案都有着关键作用。例如，室性心动过速若持续时间较长或发作频繁，可能会影响心脏的泵血功能，增加心脏骤停的风险。
                </td>
            </tr>
            <tr>
                <td colspan="2"><b>心动过缓情况</b></td>
            </tr>
            <tr>
                <td>心动过缓次数：</td>
                <td>{{ report.snb_count }}</td>
            </tr>
            <tr>
                <td>心动过缓持续时间：</td>
                <td>{{ report.snb_duration }} 分钟</td>
            </tr>
        </table>
    </div>

    <div class="section">
        <h2>健康指标</h2>
        <table>
            <tr>
                <td colspan="2"><b>情绪状态：</b>反映心理情绪状况，如焦虑、抑郁等程度</td>
            </tr>
            <tr>
                <td>测量值：</td>
                <td>{{ report.emotion }}</td>
            </tr>
            <tr>
                <td>解读</td>
                <td>长期处于不良情绪状态可能影响心血管健康</td>
            </tr>

            <tr>
                <td colspan="2"><b>疲劳程度：</b>身体和精神的疲惫程度</td>
            </tr>
            <tr>
                <td>测量值：</td>
                <td>{{ report.fatigue }}</td>
            </tr>
            <tr>
                <td>解读</td>
                <td>过度疲劳可能加重心脏负担</td>
            </tr>

            <tr>
                <td colspan="2"><b>心率变异性：</b>逐次心跳周期差异的变化情况，体现心脏自主神经系统的调节功能</td>
            </tr>
            <tr>
                <td>测量值：</td>
                <td>{{ report.hrv }}</td>
            </tr>
            <tr>
                <td>正常范围：</td>
                <td>高值一般表示心脏调节功能良好</td>
            </tr>
            <tr>
                <td>解读</td>
                <td>HRV 降低可能与心血管疾病风险增加有关</td>
            </tr>

            <tr>
                <td colspan="2"><b>压力水平：</b>感知到的心理压力大小</td>
            </tr>
            <tr>
                <td>测量值：</td>
                <td>{{ report.pressure }}</td>
            </tr>
            <tr>
                <td>解读</td>
                <td>长期高压状态可能引发血压波动，影响心脏</td>
            </tr>
            
            <tr>
                <td colspan="2"><b>活力状态：</b>身体精力充沛程度</td>
            </tr>
            <tr>
                <td>测量值：</td>
                <td>{{ report.vitality }}</td>
            </tr>
            <tr>
                <td>解读</td>
                <td>活力低可能暗示身体处于亚健康状态，影响心脏功能</td>
            </tr>
        </table>
    </div>

    <div class="section">
        <h2>异常情况</h2>
        <table class="full-bordered-table">
            <tr>
                <th style="width: 5%">症状</th>
                <th style="width: 20%">名词解释</th>
                <th style="width: 40%">发生次数及其他症状相关就医建议</th>
                <th style="width: 35%">日常注意</th>
            </tr>
            {% for abnormal_info in report.abnormal_infos %}
            <tr>
                <td>{{ abnormal_info.symptom }}</td>
                <td>{{ abnormal_info.definition }}</td>
                <td>{{ abnormal_info.advice }}</td>
                <td>{{ abnormal_info.daily_attention }}</td>
            </tr>
            {% endfor %}
        </table>
    </div>

    <div class="section">
        <h2>报告建议</h2>
        <p class="preserve-newlines">
            {{ report.report_advice }}
        </p>
        <p class="note">
            *1. 由于心电图事件的偶发性和瞬时性，每次测量结果不同是正常的。建议您及时提高监控和捕获事件的频率。<br>
            *2. 本分析结果仅供日常心脏健康监测参考，不能替代医学诊断结果，不能用于临床诊断和治疗。
        </p>
    </div>
</body>
</html>