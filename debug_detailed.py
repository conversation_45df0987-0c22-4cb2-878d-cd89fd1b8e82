#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细调试匹配过程
"""

import pandas as pd
import re

def extract_key_components(key_string):
    """从复杂的键字符串中提取关键组件"""
    # 提取客户ID (CUSTOMER后面的数字)
    customer_match = re.search(r'CUSTOMER(\d+)', key_string)
    customer_id = customer_match.group(1) if customer_match else ''
    
    # 提取完整的时间戳
    datetime_patterns = [
        r'_(\d{14})_',      # _20250401012111_
        r'/(\d{14})$',      # /20250401012111 (文件2格式，以数字结尾)
        r'/(\d{12})$',      # /202504010121 (文件2格式，12位)
        r'_(\d{12})_',      # _202504010121_
        r'_(\d{8})_',       # _20250401_
        r'/(\d{8})$',       # /20250401 (文件2格式，8位)
    ]
    
    date_part = ''
    for pattern in datetime_patterns:
        date_match = re.search(pattern, key_string)
        if date_match:
            date_part = date_match.group(1)
            break
    
    # 提取导联信息
    lead_patterns = [
        r'[Ll][Ee][Aa][Dd]([IVX]+)',  # LEADI, leadI等
        r'_([IVX]+)_[Aa][Cc][Cc]',    # _I_acc, _II_acc等
        r'/(\d+[IVX]+)',              # /20250401063945_I等中的I
    ]
    
    lead_part = ''
    for pattern in lead_patterns:
        lead_match = re.search(pattern, key_string)
        if lead_match:
            lead_part = lead_match.group(1)
            # 标准化导联名称
            lead_part = lead_part.replace('LEAD', '').replace('lead', '')
            break
    
    return customer_id, date_part, lead_part

def main():
    # 加载数据
    print("加载数据...")
    df1 = pd.read_csv(r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\10s10步长v2.0\合并结果.csv")
    df2 = pd.read_excel(r"D:\ECG\0723一分钟项目测试\标注平台数据\标注平台数据.xls")
    
    print(f"文件1形状: {df1.shape}")
    print(f"文件2形状: {df2.shape}")
    
    # 获取文件1的匹配键列
    key1_raw = df1[df1.columns[-1]].astype(str).str.strip().str.upper()
    
    # 解析文件1的键组件
    print("\n解析文件1的前5条记录:")
    key1_components = []
    for i, key in enumerate(key1_raw.head()):
        customer_id, date_part, lead_part = extract_key_components(key)
        key1_components.append((customer_id, date_part, lead_part))
        full_key = f"{customer_id}_{date_part}_{lead_part}"
        print(f"{i}: {key} -> {full_key}")
    
    # 创建完整的key1系列
    all_key1_components = []
    for key in key1_raw:
        customer_id, date_part, lead_part = extract_key_components(key)
        all_key1_components.append((customer_id, date_part, lead_part))
    
    key1_full = pd.Series([f"{comp[0]}_{comp[1]}_{comp[2]}" for comp in all_key1_components])
    
    print("\n解析文件2的前5条记录:")
    key2_components = []
    for i, (_, row) in enumerate(df2.head().iterrows()):
        es_key = str(row['es_key']).strip()
        lead = str(row['lead']).strip()
        
        customer_id, date_part, _ = extract_key_components(es_key)
        lead_normalized = lead.replace('LEAD', '').strip()
        
        key2_components.append((customer_id, date_part, lead_normalized))
        full_key = f"{customer_id}_{date_part}_{lead_normalized}"
        disease = row['disease_name']
        print(f"{i}: {es_key} + {lead} -> {full_key} -> {disease}")
    
    # 创建完整的key2系列和映射
    all_key2_components = []
    for _, row in df2.iterrows():
        es_key = str(row['es_key']).strip()
        lead = str(row['lead']).strip()
        
        customer_id, date_part, _ = extract_key_components(es_key)
        lead_normalized = lead.replace('LEAD', '').strip()
        
        all_key2_components.append((customer_id, date_part, lead_normalized))
    
    key2_full = pd.Series([f"{comp[0]}_{comp[1]}_{comp[2]}" for comp in all_key2_components])
    disease_mapping = dict(zip(key2_full, df2['disease_name']))
    
    print(f"\n映射字典大小: {len(disease_mapping)}")
    
    # 测试特定的匹配
    test_indices = [0, 1, 2]  # 测试前3条记录
    
    print("\n详细匹配测试:")
    for idx in test_indices:
        original_source = df1.iloc[idx][df1.columns[-1]]
        key1_value = key1_full.iloc[idx]
        
        print(f"\n记录 {idx}:")
        print(f"  原始数据来源: {original_source}")
        print(f"  解析后的键: {key1_value}")
        
        if key1_value in disease_mapping:
            mapped_disease = disease_mapping[key1_value]
            print(f"  映射到的疾病: {mapped_disease}")
        else:
            print(f"  ❌ 键不存在于映射字典中")
            # 查找相似的键
            similar_keys = [k for k in disease_mapping.keys() if key1_value in k or k in key1_value]
            if similar_keys:
                print(f"  相似的键: {similar_keys[:3]}")
    
    # 检查实际的合并结果
    print("\n检查实际合并结果:")
    merged_df = pd.read_csv('merged_result_with_disease.csv')
    
    for idx in test_indices:
        actual_disease = merged_df.iloc[idx]['disease_name']
        expected_key = key1_full.iloc[idx]
        expected_disease = disease_mapping.get(expected_key, "未找到")
        
        print(f"\n记录 {idx}:")
        print(f"  期望疾病: {expected_disease}")
        print(f"  实际疾病: {actual_disease}")
        print(f"  匹配正确: {'✅' if expected_disease == actual_disease else '❌'}")

if __name__ == "__main__":
    main()
