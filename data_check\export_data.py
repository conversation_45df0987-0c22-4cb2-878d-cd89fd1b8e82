import pandas as pd
import pymysql
from datetime import datetime
import json

def export_ecg_data():
    try:
        conn = pymysql.connect(
            host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
            user='ai',
            password='wq$$4r%ixg',
            database='backend_v2',
            port=3306,
            charset='utf8mb4'
        )
        
        cursor = conn.cursor()
        
        # 查询2024年2月18日的所有数据，包含ecg_analysis字段
        sql = """
        SELECT 
            id,
            union_id,
            create_time,
            source,
            sample_rate,
            ecg_analysis
        FROM t_data_ecg_20250218
        WHERE deleted = 0
        """
        
        cursor.execute(sql)
        results = cursor.fetchall()
        
        # 转换为基础DataFrame
        base_columns = ['id', 'union_id', 'create_time', 'source', 'sample_rate', 'ecg_analysis']
        df = pd.DataFrame(results, columns=base_columns)
        
        # 解析ecg_analysis JSON字段
        def parse_ecg_analysis(json_str):
            if not json_str:
                return {}
            try:
                data = json.loads(json_str)
                return {
                    'SignalQuantity': data.get('SignalQuantity'),
                    'RespiratoryRate': data.get('RespiratoryRate'),
                    'ArrhythmiaDiagnosis': json.dumps(data.get('ArrhythmiaDiagnosis', {})),
                    'CADCardiomyopathy': data.get('CADCardiomyopathy'),
                    'ECGAge': data.get('ECGAge'),
                    'avgHr': data.get('avgHr'),
                    'HealthMetrics': json.dumps(data.get('HealthMetrics', {})),
                    'HeartFailureRisk': data.get('HeartFailureRisk'),
                    'VentricularFibrillationRisk': data.get('VentricularFibrillationRisk'),
                    'OSARisk': data.get('OSARisk'),
                    'SleepStage': data.get('SleepStage'),
                    'SyncopeRisk': data.get('SyncopeRisk'),
                    'PQRSTC': json.dumps(data.get('PQRSTC', {}))
                }
            except json.JSONDecodeError:
                return {}
        
        # 解析每行的ecg_analysis字段并添加到DataFrame
        analysis_data = df['ecg_analysis'].apply(parse_ecg_analysis).apply(pd.Series)
        
        # 合并基础数据和解析后的数据
        df = pd.concat([df.drop('ecg_analysis', axis=1), analysis_data], axis=1)
        
        # 保存为Excel文件
        output_file = 'D:/Project/data_check/ecg_data_20250218.xlsx'
        df.to_excel(output_file, index=False)
        
        print(f"\n成功导出 {len(df)} 条记录")
        print(f"文件保存在: {output_file}")
        
        cursor.close()
        conn.close()
            
    except Exception as e:
        print(f"发生错误: {str(e)}")
        print(f"SQL查询: {sql}")

if __name__ == "__main__":
    export_ecg_data()