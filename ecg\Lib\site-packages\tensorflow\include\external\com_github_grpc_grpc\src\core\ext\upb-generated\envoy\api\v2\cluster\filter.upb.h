/* This file was generated by upbc (the upb compiler) from the input
 * file:
 *
 *     envoy/api/v2/cluster/filter.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated. */

#ifndef ENVOY_API_V2_CLUSTER_FILTER_PROTO_UPB_H_
#define ENVOY_API_V2_CLUSTER_FILTER_PROTO_UPB_H_

#include "upb/generated_util.h"
#include "upb/msg.h"
#include "upb/decode.h"
#include "upb/encode.h"

#include "upb/port_def.inc"

#ifdef __cplusplus
extern "C" {
#endif

struct envoy_api_v2_cluster_Filter;
typedef struct envoy_api_v2_cluster_Filter envoy_api_v2_cluster_Filter;
extern const upb_msglayout envoy_api_v2_cluster_Filter_msginit;
struct google_protobuf_Any;
extern const upb_msglayout google_protobuf_Any_msginit;


/* envoy.api.v2.cluster.Filter */

UPB_INLINE envoy_api_v2_cluster_Filter *envoy_api_v2_cluster_Filter_new(upb_arena *arena) {
  return (envoy_api_v2_cluster_Filter *)upb_msg_new(&envoy_api_v2_cluster_Filter_msginit, arena);
}
UPB_INLINE envoy_api_v2_cluster_Filter *envoy_api_v2_cluster_Filter_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  envoy_api_v2_cluster_Filter *ret = envoy_api_v2_cluster_Filter_new(arena);
  return (ret && upb_decode(buf, size, ret, &envoy_api_v2_cluster_Filter_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *envoy_api_v2_cluster_Filter_serialize(const envoy_api_v2_cluster_Filter *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &envoy_api_v2_cluster_Filter_msginit, arena, len);
}

UPB_INLINE upb_strview envoy_api_v2_cluster_Filter_name(const envoy_api_v2_cluster_Filter *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)); }
UPB_INLINE const struct google_protobuf_Any* envoy_api_v2_cluster_Filter_typed_config(const envoy_api_v2_cluster_Filter *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_Any*, UPB_SIZE(8, 16)); }

UPB_INLINE void envoy_api_v2_cluster_Filter_set_name(envoy_api_v2_cluster_Filter *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE void envoy_api_v2_cluster_Filter_set_typed_config(envoy_api_v2_cluster_Filter *msg, struct google_protobuf_Any* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_Any*, UPB_SIZE(8, 16)) = value;
}
UPB_INLINE struct google_protobuf_Any* envoy_api_v2_cluster_Filter_mutable_typed_config(envoy_api_v2_cluster_Filter *msg, upb_arena *arena) {
  struct google_protobuf_Any* sub = (struct google_protobuf_Any*)envoy_api_v2_cluster_Filter_typed_config(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Any*)upb_msg_new(&google_protobuf_Any_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_cluster_Filter_set_typed_config(msg, sub);
  }
  return sub;
}

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port_undef.inc"

#endif  /* ENVOY_API_V2_CLUSTER_FILTER_PROTO_UPB_H_ */
