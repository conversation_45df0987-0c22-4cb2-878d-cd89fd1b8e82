# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013-2016,2018-2019,2021
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-15 14:25+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: French (http://www.transifex.com/django/django/language/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Administrative Documentation"
msgstr "Documentation technique"

msgid "Home"
msgstr "Accueil"

msgid "Documentation"
msgstr "Documentation"

msgid "Bookmarklets"
msgstr "Signets"

msgid "Documentation bookmarklets"
msgstr "Documentation des signets"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Pour installer des signets, faites glisser le lien vers votre barre de "
"marques-pages, ou effectuez un clic droit sur le lien et ajoutez-le. "
"Maintenant, vous pouvez le sélectionner depuis n'importe quelle page du site."

msgid "Documentation for this page"
msgstr "Documentation pour cette page"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Vous envoie de n'importe quelle page vers la documentation de la vue qui a "
"généré cette page."

msgid "Tags"
msgstr "Balises"

msgid "List of all the template tags and their functions."
msgstr "Liste de toutes les balises de gabarit et leur fonction."

msgid "Filters"
msgstr "Filtres"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Les filtres sont des actions qui peuvent être appliquées à des variables "
"dans un gabarit pour modifier leur valeur affichée."

msgid "Models"
msgstr "Modèles"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Les modèles sont des descriptions de tous les objets du système avec leurs "
"champs associés. Chaque modèle possède une liste de champs auxquels on peut "
"accéder comme pour les variables de gabarit"

msgid "Views"
msgstr "Vues"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Chaque page du site public est générée par une vue. La vue détermine le "
"gabarit utilisé pour générer la page ainsi que les objets qui sont "
"disponibles dans le gabarit."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Outils permettant au navigateur d'accéder rapidement aux fonctionnalités de "
"l'interface d'administration."

msgid "Please install docutils"
msgstr "Veuillez installer docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Le système de documentation de l'interface d'administration nécessite la "
"bibliothèque Python <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Demandez à votre administrateur système d'installer <a href=\"%(link)s"
"\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Modèle : %(name)s"

msgid "Fields"
msgstr "Champs"

msgid "Field"
msgstr "Champ"

msgid "Type"
msgstr "Type"

msgid "Description"
msgstr "Description"

msgid "Methods with arguments"
msgstr "Méthodes avec paramètres"

msgid "Method"
msgstr "Méthode"

msgid "Arguments"
msgstr "Paramètres"

msgid "Back to Model documentation"
msgstr "Retour à la documentation des modèles"

msgid "Model documentation"
msgstr "Documentation des modèles"

msgid "Model groups"
msgstr "Groupes de modèles"

msgid "Templates"
msgstr "Gabarits"

#, python-format
msgid "Template: %(name)s"
msgstr "Gabarit : %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Gabarit : <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Chemin de recherche du gabarit <q>%(name)s</q> :"

msgid "(does not exist)"
msgstr "(n'existe pas)"

msgid "Back to Documentation"
msgstr "Retour à la documentation"

msgid "Template filters"
msgstr "Filtres de gabarit"

msgid "Template filter documentation"
msgstr "Documentation des filtres de gabarit"

msgid "Built-in filters"
msgstr "Filtres intégrés"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Pour utiliser ces filtres, placez <code>%(code)s</code> dans votre gabarit "
"avant d'utiliser un des filtres."

msgid "Template tags"
msgstr "Balises de gabarit"

msgid "Template tag documentation"
msgstr "Documentation des balises de gabarit"

msgid "Built-in tags"
msgstr "Balises intégrées"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Pour utiliser ces balises, placez <code>%(code)s</code> dans le gabarit "
"avant d'utiliser une des balises."

#, python-format
msgid "View: %(name)s"
msgstr "Vue : %(name)s"

msgid "Context:"
msgstr "Contexte :"

msgid "Templates:"
msgstr "Gabarits :"

msgid "Back to View documentation"
msgstr "Retour à la documentation des vues"

msgid "View documentation"
msgstr "Documentation des vues"

msgid "Jump to namespace"
msgstr "Aller à l'espace de noms"

msgid "Empty namespace"
msgstr "Espace de noms vide"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Vues par espace de noms %(name)s"

msgid "Views by empty namespace"
msgstr "Vues par espace de noms vide"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Fonction de vue : <code>%(full_name)s</code>. Nom : <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "mot-clé :"

msgid "filter:"
msgstr "filtre :"

msgid "view:"
msgstr "vue :"

#, python-format
msgid "App %(app_label)r not found"
msgstr "L'application %(app_label)r n'a pas été trouvée"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""
"Le modèle %(model_name)r n'a pas été trouvé dans l'application %(app_label)r"

msgid "model:"
msgstr "modèle :"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "l'objet « %(app_label)s.%(data_type)s » en relation"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "les objets « %(app_label)s.%(object_name)s » en relation"

#, python-format
msgid "all %s"
msgstr "tous les %s"

#, python-format
msgid "number of %s"
msgstr "nombre de %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ne semble pas être un objet urlpattern"
