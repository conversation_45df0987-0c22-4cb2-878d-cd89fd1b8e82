# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/hparams/api.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n%tensorboard/plugins/hparams/api.proto\x12\x13tensorboard.hparams\x1a\x1cgoogle/protobuf/struct.proto\"\xc6\x01\n\nExperiment\x12\x0c\n\x04name\x18\x06 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\x12\x0c\n\x04user\x18\x02 \x01(\t\x12\x19\n\x11time_created_secs\x18\x03 \x01(\x01\x12\x35\n\x0chparam_infos\x18\x04 \x03(\x0b\x32\x1f.tensorboard.hparams.HParamInfo\x12\x35\n\x0cmetric_infos\x18\x05 \x03(\x0b\x32\x1f.tensorboard.hparams.MetricInfo\"\xfe\x01\n\nHParamInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x14\n\x0c\x64isplay_name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12+\n\x04type\x18\x04 \x01(\x0e\x32\x1d.tensorboard.hparams.DataType\x12\x35\n\x0f\x64omain_discrete\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.ListValueH\x00\x12\x38\n\x0f\x64omain_interval\x18\x06 \x01(\x0b\x32\x1d.tensorboard.hparams.IntervalH\x00\x12\x0f\n\x07\x64iffers\x18\x07 \x01(\x08\x42\x08\n\x06\x64omain\"0\n\x08Interval\x12\x11\n\tmin_value\x18\x01 \x01(\x01\x12\x11\n\tmax_value\x18\x02 \x01(\x01\"(\n\nMetricName\x12\r\n\x05group\x18\x01 \x01(\t\x12\x0b\n\x03tag\x18\x02 \x01(\t\"\x9e\x01\n\nMetricInfo\x12-\n\x04name\x18\x01 \x01(\x0b\x32\x1f.tensorboard.hparams.MetricName\x12\x14\n\x0c\x64isplay_name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x36\n\x0c\x64\x61taset_type\x18\x05 \x01(\x0e\x32 .tensorboard.hparams.DatasetType\"\xa3\x02\n\x0cSessionGroup\x12\x0c\n\x04name\x18\x01 \x01(\t\x12?\n\x07hparams\x18\x02 \x03(\x0b\x32..tensorboard.hparams.SessionGroup.HparamsEntry\x12\x37\n\rmetric_values\x18\x03 \x03(\x0b\x32 .tensorboard.hparams.MetricValue\x12.\n\x08sessions\x18\x04 \x03(\x0b\x32\x1c.tensorboard.hparams.Session\x12\x13\n\x0bmonitor_url\x18\x05 \x01(\t\x1a\x46\n\x0cHparamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\"z\n\x0bMetricValue\x12-\n\x04name\x18\x01 \x01(\x0b\x32\x1f.tensorboard.hparams.MetricName\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x15\n\rtraining_step\x18\x03 \x01(\x05\x12\x16\n\x0ewall_time_secs\x18\x04 \x01(\x01\"\xd5\x01\n\x07Session\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x17\n\x0fstart_time_secs\x18\x02 \x01(\x01\x12\x15\n\rend_time_secs\x18\x03 \x01(\x01\x12+\n\x06status\x18\x04 \x01(\x0e\x32\x1b.tensorboard.hparams.Status\x12\x11\n\tmodel_uri\x18\x05 \x01(\t\x12\x37\n\rmetric_values\x18\x06 \x03(\x0b\x32 .tensorboard.hparams.MetricValue\x12\x13\n\x0bmonitor_url\x18\x07 \x01(\t\"\x8f\x01\n\x14GetExperimentRequest\x12\x17\n\x0f\x65xperiment_name\x18\x01 \x01(\t\x12\x1c\n\x0finclude_metrics\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x1a\n\rhparams_limit\x18\x03 \x01(\x05H\x01\x88\x01\x01\x42\x12\n\x10_include_metricsB\x10\n\x0e_hparams_limit\"\xf6\x02\n\x18ListSessionGroupsRequest\x12\x17\n\x0f\x65xperiment_name\x18\x06 \x01(\t\x12\x35\n\x10\x61llowed_statuses\x18\x07 \x03(\x0e\x32\x1b.tensorboard.hparams.Status\x12\x32\n\ncol_params\x18\x01 \x03(\x0b\x32\x1e.tensorboard.hparams.ColParams\x12>\n\x10\x61ggregation_type\x18\x02 \x01(\x0e\x32$.tensorboard.hparams.AggregationType\x12;\n\x12\x61ggregation_metric\x18\x03 \x01(\x0b\x32\x1f.tensorboard.hparams.MetricName\x12\x13\n\x0bstart_index\x18\x04 \x01(\x05\x12\x12\n\nslice_size\x18\x05 \x01(\x05\x12\x1c\n\x0finclude_metrics\x18\x08 \x01(\x08H\x00\x88\x01\x01\x42\x12\n\x10_include_metrics\"\x8f\x03\n\tColParams\x12\x31\n\x06metric\x18\x01 \x01(\x0b\x32\x1f.tensorboard.hparams.MetricNameH\x00\x12\x10\n\x06hparam\x18\x02 \x01(\tH\x00\x12-\n\x05order\x18\x03 \x01(\x0e\x32\x1e.tensorboard.hparams.SortOrder\x12\x1c\n\x14missing_values_first\x18\x04 \x01(\x08\x12\x17\n\rfilter_regexp\x18\x05 \x01(\tH\x01\x12\x38\n\x0f\x66ilter_interval\x18\x06 \x01(\x0b\x32\x1d.tensorboard.hparams.IntervalH\x01\x12\x35\n\x0f\x66ilter_discrete\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.ListValueH\x01\x12\x1e\n\x16\x65xclude_missing_values\x18\x08 \x01(\x08\x12\x1e\n\x11include_in_result\x18\t \x01(\x08H\x02\x88\x01\x01\x42\x06\n\x04nameB\x08\n\x06\x66ilterB\x14\n\x12_include_in_result\"j\n\x19ListSessionGroupsResponse\x12\x39\n\x0esession_groups\x18\x01 \x03(\x0b\x32!.tensorboard.hparams.SessionGroup\x12\x12\n\ntotal_size\x18\x03 \x01(\x05\"}\n\x16ListMetricEvalsRequest\x12\x17\n\x0f\x65xperiment_name\x18\x03 \x01(\t\x12\x14\n\x0csession_name\x18\x01 \x01(\t\x12\x34\n\x0bmetric_name\x18\x02 \x01(\x0b\x32\x1f.tensorboard.hparams.MetricName*`\n\x08\x44\x61taType\x12\x13\n\x0f\x44\x41TA_TYPE_UNSET\x10\x00\x12\x14\n\x10\x44\x41TA_TYPE_STRING\x10\x01\x12\x12\n\x0e\x44\x41TA_TYPE_BOOL\x10\x02\x12\x15\n\x11\x44\x41TA_TYPE_FLOAT64\x10\x03*P\n\x0b\x44\x61tasetType\x12\x13\n\x0f\x44\x41TASET_UNKNOWN\x10\x00\x12\x14\n\x10\x44\x41TASET_TRAINING\x10\x01\x12\x16\n\x12\x44\x41TASET_VALIDATION\x10\x02*X\n\x06Status\x12\x12\n\x0eSTATUS_UNKNOWN\x10\x00\x12\x12\n\x0eSTATUS_SUCCESS\x10\x01\x12\x12\n\x0eSTATUS_FAILURE\x10\x02\x12\x12\n\x0eSTATUS_RUNNING\x10\x03*A\n\tSortOrder\x12\x15\n\x11ORDER_UNSPECIFIED\x10\x00\x12\r\n\tORDER_ASC\x10\x01\x12\x0e\n\nORDER_DESC\x10\x02*\x7f\n\x0f\x41ggregationType\x12\x15\n\x11\x41GGREGATION_UNSET\x10\x00\x12\x13\n\x0f\x41GGREGATION_AVG\x10\x01\x12\x16\n\x12\x41GGREGATION_MEDIAN\x10\x02\x12\x13\n\x0f\x41GGREGATION_MIN\x10\x03\x12\x13\n\x0f\x41GGREGATION_MAX\x10\x04\x62\x06proto3')

_DATATYPE = DESCRIPTOR.enum_types_by_name['DataType']
DataType = enum_type_wrapper.EnumTypeWrapper(_DATATYPE)
_DATASETTYPE = DESCRIPTOR.enum_types_by_name['DatasetType']
DatasetType = enum_type_wrapper.EnumTypeWrapper(_DATASETTYPE)
_STATUS = DESCRIPTOR.enum_types_by_name['Status']
Status = enum_type_wrapper.EnumTypeWrapper(_STATUS)
_SORTORDER = DESCRIPTOR.enum_types_by_name['SortOrder']
SortOrder = enum_type_wrapper.EnumTypeWrapper(_SORTORDER)
_AGGREGATIONTYPE = DESCRIPTOR.enum_types_by_name['AggregationType']
AggregationType = enum_type_wrapper.EnumTypeWrapper(_AGGREGATIONTYPE)
DATA_TYPE_UNSET = 0
DATA_TYPE_STRING = 1
DATA_TYPE_BOOL = 2
DATA_TYPE_FLOAT64 = 3
DATASET_UNKNOWN = 0
DATASET_TRAINING = 1
DATASET_VALIDATION = 2
STATUS_UNKNOWN = 0
STATUS_SUCCESS = 1
STATUS_FAILURE = 2
STATUS_RUNNING = 3
ORDER_UNSPECIFIED = 0
ORDER_ASC = 1
ORDER_DESC = 2
AGGREGATION_UNSET = 0
AGGREGATION_AVG = 1
AGGREGATION_MEDIAN = 2
AGGREGATION_MIN = 3
AGGREGATION_MAX = 4


_EXPERIMENT = DESCRIPTOR.message_types_by_name['Experiment']
_HPARAMINFO = DESCRIPTOR.message_types_by_name['HParamInfo']
_INTERVAL = DESCRIPTOR.message_types_by_name['Interval']
_METRICNAME = DESCRIPTOR.message_types_by_name['MetricName']
_METRICINFO = DESCRIPTOR.message_types_by_name['MetricInfo']
_SESSIONGROUP = DESCRIPTOR.message_types_by_name['SessionGroup']
_SESSIONGROUP_HPARAMSENTRY = _SESSIONGROUP.nested_types_by_name['HparamsEntry']
_METRICVALUE = DESCRIPTOR.message_types_by_name['MetricValue']
_SESSION = DESCRIPTOR.message_types_by_name['Session']
_GETEXPERIMENTREQUEST = DESCRIPTOR.message_types_by_name['GetExperimentRequest']
_LISTSESSIONGROUPSREQUEST = DESCRIPTOR.message_types_by_name['ListSessionGroupsRequest']
_COLPARAMS = DESCRIPTOR.message_types_by_name['ColParams']
_LISTSESSIONGROUPSRESPONSE = DESCRIPTOR.message_types_by_name['ListSessionGroupsResponse']
_LISTMETRICEVALSREQUEST = DESCRIPTOR.message_types_by_name['ListMetricEvalsRequest']
Experiment = _reflection.GeneratedProtocolMessageType('Experiment', (_message.Message,), {
  'DESCRIPTOR' : _EXPERIMENT,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.Experiment)
  })
_sym_db.RegisterMessage(Experiment)

HParamInfo = _reflection.GeneratedProtocolMessageType('HParamInfo', (_message.Message,), {
  'DESCRIPTOR' : _HPARAMINFO,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.HParamInfo)
  })
_sym_db.RegisterMessage(HParamInfo)

Interval = _reflection.GeneratedProtocolMessageType('Interval', (_message.Message,), {
  'DESCRIPTOR' : _INTERVAL,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.Interval)
  })
_sym_db.RegisterMessage(Interval)

MetricName = _reflection.GeneratedProtocolMessageType('MetricName', (_message.Message,), {
  'DESCRIPTOR' : _METRICNAME,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.MetricName)
  })
_sym_db.RegisterMessage(MetricName)

MetricInfo = _reflection.GeneratedProtocolMessageType('MetricInfo', (_message.Message,), {
  'DESCRIPTOR' : _METRICINFO,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.MetricInfo)
  })
_sym_db.RegisterMessage(MetricInfo)

SessionGroup = _reflection.GeneratedProtocolMessageType('SessionGroup', (_message.Message,), {

  'HparamsEntry' : _reflection.GeneratedProtocolMessageType('HparamsEntry', (_message.Message,), {
    'DESCRIPTOR' : _SESSIONGROUP_HPARAMSENTRY,
    '__module__' : 'tensorboard.plugins.hparams.api_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.hparams.SessionGroup.HparamsEntry)
    })
  ,
  'DESCRIPTOR' : _SESSIONGROUP,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.SessionGroup)
  })
_sym_db.RegisterMessage(SessionGroup)
_sym_db.RegisterMessage(SessionGroup.HparamsEntry)

MetricValue = _reflection.GeneratedProtocolMessageType('MetricValue', (_message.Message,), {
  'DESCRIPTOR' : _METRICVALUE,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.MetricValue)
  })
_sym_db.RegisterMessage(MetricValue)

Session = _reflection.GeneratedProtocolMessageType('Session', (_message.Message,), {
  'DESCRIPTOR' : _SESSION,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.Session)
  })
_sym_db.RegisterMessage(Session)

GetExperimentRequest = _reflection.GeneratedProtocolMessageType('GetExperimentRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETEXPERIMENTREQUEST,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.GetExperimentRequest)
  })
_sym_db.RegisterMessage(GetExperimentRequest)

ListSessionGroupsRequest = _reflection.GeneratedProtocolMessageType('ListSessionGroupsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTSESSIONGROUPSREQUEST,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.ListSessionGroupsRequest)
  })
_sym_db.RegisterMessage(ListSessionGroupsRequest)

ColParams = _reflection.GeneratedProtocolMessageType('ColParams', (_message.Message,), {
  'DESCRIPTOR' : _COLPARAMS,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.ColParams)
  })
_sym_db.RegisterMessage(ColParams)

ListSessionGroupsResponse = _reflection.GeneratedProtocolMessageType('ListSessionGroupsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTSESSIONGROUPSRESPONSE,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.ListSessionGroupsResponse)
  })
_sym_db.RegisterMessage(ListSessionGroupsResponse)

ListMetricEvalsRequest = _reflection.GeneratedProtocolMessageType('ListMetricEvalsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTMETRICEVALSREQUEST,
  '__module__' : 'tensorboard.plugins.hparams.api_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.ListMetricEvalsRequest)
  })
_sym_db.RegisterMessage(ListMetricEvalsRequest)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _SESSIONGROUP_HPARAMSENTRY._options = None
  _SESSIONGROUP_HPARAMSENTRY._serialized_options = b'8\001'
  _DATATYPE._serialized_start=2597
  _DATATYPE._serialized_end=2693
  _DATASETTYPE._serialized_start=2695
  _DATASETTYPE._serialized_end=2775
  _STATUS._serialized_start=2777
  _STATUS._serialized_end=2865
  _SORTORDER._serialized_start=2867
  _SORTORDER._serialized_end=2932
  _AGGREGATIONTYPE._serialized_start=2934
  _AGGREGATIONTYPE._serialized_end=3061
  _EXPERIMENT._serialized_start=93
  _EXPERIMENT._serialized_end=291
  _HPARAMINFO._serialized_start=294
  _HPARAMINFO._serialized_end=548
  _INTERVAL._serialized_start=550
  _INTERVAL._serialized_end=598
  _METRICNAME._serialized_start=600
  _METRICNAME._serialized_end=640
  _METRICINFO._serialized_start=643
  _METRICINFO._serialized_end=801
  _SESSIONGROUP._serialized_start=804
  _SESSIONGROUP._serialized_end=1095
  _SESSIONGROUP_HPARAMSENTRY._serialized_start=1025
  _SESSIONGROUP_HPARAMSENTRY._serialized_end=1095
  _METRICVALUE._serialized_start=1097
  _METRICVALUE._serialized_end=1219
  _SESSION._serialized_start=1222
  _SESSION._serialized_end=1435
  _GETEXPERIMENTREQUEST._serialized_start=1438
  _GETEXPERIMENTREQUEST._serialized_end=1581
  _LISTSESSIONGROUPSREQUEST._serialized_start=1584
  _LISTSESSIONGROUPSREQUEST._serialized_end=1958
  _COLPARAMS._serialized_start=1961
  _COLPARAMS._serialized_end=2360
  _LISTSESSIONGROUPSRESPONSE._serialized_start=2362
  _LISTSESSIONGROUPSRESPONSE._serialized_end=2468
  _LISTMETRICEVALSREQUEST._serialized_start=2470
  _LISTMETRICEVALSREQUEST._serialized_end=2595
# @@protoc_insertion_point(module_scope)
