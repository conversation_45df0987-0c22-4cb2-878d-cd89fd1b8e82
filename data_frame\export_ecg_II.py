import requests
import os
import json
import csv
from qiniu import Auth

# 要下载的特定es_key
TARGET_ES_KEY = "CUSTOMER19054257726245560321078/20250401152805"

# 七牛云配置
QINIU = {
    'test': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://test.upload.weiheyixue.com'
    },
    'prod': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://upload.weiheyixue.com'
    }
}

# 本地保存路径
SAVE_DIR = r'D:\ECG\0331标注平台数据\特定查询'

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")

def sanitize_filename(filename):
    """将 es_key 转换为有效的文件名"""
    return filename.replace('/', '_').replace('\\', '_')

def extract_ecg_II_data(data):
    """从JSON数据中提取ecgII字段数据"""
    if not isinstance(data, dict):
        print("数据不是JSON对象格式")
        return None
    
    print(f"JSON 顶层键: {list(data.keys())}")
    
    # 检查是否有ecgII字段
    if "ecgII" in data:
        ecgII_value = data["ecgII"]
        print(f"ecgII 字段类型: {type(ecgII_value)}")
        
        # 如果ecgII字段是字符串，尝试将其解析为JSON数组
        if isinstance(ecgII_value, str):
            print(f"ecgII 字段是字符串，长度: {len(ecgII_value)}")
            print(f"ecgII 字段前50个字符: {ecgII_value[:50]}...")
            
            # 尝试将字符串解析为JSON
            try:
                ecgII_data = json.loads(ecgII_value)
                if isinstance(ecgII_data, list):
                    print(f"成功将ecgII字段解析为数组，包含 {len(ecgII_data)} 个数据点")
                    return ecgII_data
                else:
                    print(f"ecgII字段解析后不是数组，而是 {type(ecgII_data)}")
            except json.JSONDecodeError as e:
                print(f"ecgII字段无法解析为JSON: {e}")
                
                # 尝试手动解析
                if ecgII_value.startswith('[') and ecgII_value.endswith(']'):
                    try:
                        values_str = ecgII_value[1:-1].split(',')
                        values = [float(x.strip()) for x in values_str]
                        print(f"手动解析出 {len(values)} 个数据点")
                        return values
                    except Exception as e:
                        print(f"手动解析失败: {e}")
        
        # 如果ecgII字段直接是列表，直接返回
        elif isinstance(ecgII_value, list):
            print(f"ecgII 字段直接是列表，包含 {len(ecgII_value)} 个数据点")
            return ecgII_value
            
    print("未找到ecgII字段或无法解析")
    return None

def download_and_save_ecg_II(es_key, environment='test'):
    """从七牛云下载特定es_key的数据，并提取保存ecgII导联数据"""
    # 确保保存目录存在
    ensure_dir_exists(SAVE_DIR)
    
    try:
        # 获取七牛云配置
        access_key = QINIU[environment]['access_key']
        secret_key = QINIU[environment]['secret_key']
        domain_prefix = QINIU[environment]['domain_prefix']
        
        # 尝试几种不同的路径格式
        paths_to_try = [
            f'ecg/{es_key}',                 # 标准路径
            f'{es_key}',                     # 直接使用es_key
            f'data/{es_key}',                # 可能的替代路径
            f'file/{es_key}',                # 可能的替代路径
            f'ecg_data/{es_key}',            # 可能的替代路径
            f'ecg/{es_key}.json',            # 添加可能的扩展名
            f'ecg/{es_key}.dat'              # 添加可能的扩展名
        ]
        
        # 构建鉴权对象
        q = Auth(access_key, secret_key)
        
        for path in paths_to_try:
            print(f"\n尝试路径: {path}")
            
            # 生成私有下载链接
            private_url = q.private_download_url(domain_prefix + '/' + path, expires=3600)
            
            # 下载文件
            print(f"请求URL: {private_url.split('?')[0]}")  # 不打印完整URL以隐藏签名
            response = requests.get(private_url)
            
            if response.status_code == 200:
                print(f"成功! 状态码: {response.status_code}, 内容长度: {len(response.content)} 字节")
                
                # 准备文件名
                filename_base = sanitize_filename(es_key)
                
                # 保存原始文件
                ext = '.dat'
                if 'json' in path or 'application/json' in response.headers.get('Content-Type', ''):
                    ext = '.json'
                    
                raw_filename = f"{filename_base}_from_{path.replace('/', '_')}{ext}"
                raw_path = os.path.join(SAVE_DIR, raw_filename)
                
                with open(raw_path, 'wb') as f:
                    f.write(response.content)
                print(f"已保存原始文件: {raw_path}")
                
                # 尝试解析为JSON
                try:
                    data = json.loads(response.content)
                    
                    # 保存美化的JSON便于查看
                    pretty_path = os.path.join(SAVE_DIR, f"{filename_base}_pretty.json")
                    with open(pretty_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2, ensure_ascii=False)
                    print(f"已保存美化的JSON: {pretty_path}")
                    
                    # 提取ecgII数据
                    ecgII_data = extract_ecg_II_data(data)
                    
                    # 如果成功提取了ecgII数据，保存为CSV
                    if ecgII_data:
                        csv_path = os.path.join(SAVE_DIR, f"{filename_base}_ecgII.csv")
                        with open(csv_path, 'w', newline='') as csvfile:
                            writer = csv.writer(csvfile)
                            writer.writerow(ecgII_data)  # 写入单行，每个数据点一列
                        print(f"已提取并保存ecgII数据为CSV: {csv_path}")
                        print(f"ecgII数据包含 {len(ecgII_data)} 个数据点")
                        return True
                    else:
                        print("未能提取ecgII数据")
                except Exception as e:
                    print(f"解析JSON或提取数据时出错: {e}")
                
                # 即使未能提取ecgII数据，也返回True因为已保存原始文件
                return True
            else:
                print(f"失败: 状态码 {response.status_code}")
        
        print("\n所有尝试路径均失败")
        return False
    
    except Exception as e:
        print(f"下载过程中出错: {e}")
        return False

if __name__ == "__main__":
    print(f"开始从测试环境下载 es_key: {TARGET_ES_KEY} 的ecgII导联数据")
    
    success = download_and_save_ecg_II(TARGET_ES_KEY, environment='test')
    
    if success:
        print("\n处理完成!")
    else:
        print("\n下载失败。")
        print("可能原因:")
        print("1. 这个es_key数据可能不存在或已被删除")
        print("2. 存储方式不是使用七牛云") 
        print("3. 路径格式完全不同")
        print("4. 权限不足")
        
    print("\n任务完成") 