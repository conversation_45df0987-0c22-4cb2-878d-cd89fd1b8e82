metadata:
  name: pth-saic-vul-fbrs
  namespace: cvat
  annotations:
    name: f-BRS
    version: 2
    type: interactor
    spec:
    min_pos_points: 1
    min_neg_points: 0
    animated_gif: https://raw.githubusercontent.com/cvat-ai/cvat/0fbb19ae3846a017853d52e187f0ce149adced7d/site/content/en/images/fbrs_example.gif
    help_message: The interactor allows to get a mask for an object using positive points, and negative points

spec:
  description: f-BRS interactive segmentation
  runtime: 'python:3.9'
  handler: main:handler
  eventTimeout: 30s
  env:
    - name: PYTHONPATH
      value: /opt/nuclio/fbrs

  build:
    image: cvat.pth.saic-vul.fbrs

    directives:
      preCopy:
        - kind: WORKDIR
          value: /opt/nuclio
        - kind: RUN
          value: git clone https://github.com/saic-vul/fbrs_interactive_segmentation.git fbrs
        - kind: WORKDIR
          value: /opt/nuclio/fbrs
        - kind: RUN
          value: pip3 install gdown
        - kind: ENV
          value: fileid=1Z9dQtpWVTobEdmUBntpUU0pJl-pEXUwR
        - kind: ENV
          value: filename=resnet101_dh256_sbd.pth
        - kind: RUN
          value: gdown https://drive.google.com/uc?id=${fileid} -O ${filename}
        - kind: RUN
          value: apt update && apt install -y libgl1-mesa-glx
        - kind: RUN
          value: pip3 install -r requirements.txt
        - kind: WORKDIR
          value: /opt/nuclio

  triggers:
    myHttpTrigger:
      numWorkers: 2
      kind: 'http'
      workerAvailabilityTimeoutMilliseconds: 10000
      attributes:
        maxRequestBodySize: 33554432 # 32MB

  platform:
    attributes:
      restartPolicy:
        name: always
        maximumRetryCount: 3
      mountMode: volume
