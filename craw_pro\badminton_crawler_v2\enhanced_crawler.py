#!/usr/bin/env python3
"""
增强版羽毛球装备爬虫 - 专注于完整数据提取
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedBadmintonCrawler:
    def __init__(self, max_items=5):
        """初始化爬虫"""
        self.session = requests.Session()
        self.max_items = max_items
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.session.headers.update(self.headers)
        self.equipment_data = []
        
        # 确保输出目录存在
        os.makedirs('output', exist_ok=True)
    
    def ask_ai_for_answer(self, question):
        """向AI询问验证问题的答案"""
        try:
            logger.info(f"向AI询问问题: {question}")
            
            # 预处理常见问题
            question_lower = str(question).lower()
            
            # 直接回答的问题
            direct_answers = {
                '羽毛球有几根毛': '16',
                '羽毛球几根毛': '16', 
                'zyzx小写怎么写': 'zyzx',
                'zyzx大写怎么写': 'ZYZX',
                'zyzx怎么写': 'zyzx',
                '中羽在线英文缩写': 'ZYZX',
                '中羽缩写': 'ZYZX',
            }
            
            for key, answer in direct_answers.items():
                if key in question_lower:
                    logger.info(f"直接回答: {answer}")
                    return answer
            
            # 数学计算
            math_result = self.calculate_math_expression(question)
            if math_result is not None:
                logger.info(f"数学计算结果: {math_result}")
                return str(math_result)
            
            # 调用AI API
            api_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer pat-20241215-QGKMa1gNWFqZCeaQ4nO5Wjrd5e1YdOZWGWH1GsQN35BUKi5m39sVhL4iGQXO1Pj5"
            }
            
            system_prompt = """你是一个专门回答验证问题的助手。请直接给出准确的答案，不要解释。

常见问题类型和答案：
1. 羽毛球有几根毛？答案：16
2. ZYZX小写怎么写？答案：zyzx  
3. ZYZX大写怎么写？答案：ZYZX
4. 数学运算：如1+1=2, 5×10=50, 8-3=5等
5. 中羽在线英文缩写？答案：ZYZX

请只返回答案内容，不要其他文字。"""
            
            data = {
                "model": "ep-20241215142258-fwxf9",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"问题：{question}\n请直接给出答案："}
                ],
                "max_tokens": 50,
                "temperature": 0.1
            }
            
            response = requests.post(api_url, headers=headers, json=data, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                answer = result['choices'][0]['message']['content'].strip()
                
                # 提取数字答案
                numbers = re.findall(r'\d+', answer)
                if numbers:
                    final_answer = numbers[0]
                    logger.info(f"AI回答: {final_answer}")
                    return final_answer
                else:
                    logger.info(f"AI回答: {answer}")
                    return answer
            else:
                logger.error(f"AI API请求失败: {response.status_code}")
                return self.get_fallback_answer(question)
                
        except Exception as e:
            logger.error(f"AI请求失败: {e}")
            return self.get_fallback_answer(question)
    
    def calculate_math_expression(self, question):
        """计算数学表达式"""
        try:
            # 提取数学表达式
            patterns = [
                r'(\d+)\s*[×*]\s*(\d+)',
                r'(\d+)\s*[+]\s*(\d+)',
                r'(\d+)\s*[-]\s*(\d+)',
                r'(\d+)\s*[/÷]\s*(\d+)',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, str(question))
                if match:
                    num1, num2 = int(match.group(1)), int(match.group(2))
                    
                    if '×' in question or '*' in question:
                        return num1 * num2
                    elif '+' in question:
                        return num1 + num2
                    elif '-' in question:
                        return num1 - num2
                    elif '/' in question or '÷' in question:
                        return num1 // num2 if num2 != 0 else None
            
            return None
            
        except Exception:
            return None
    
    def get_fallback_answer(self, question):
        """备用答案策略"""
        # 简单的数学计算
        if '×' in question or '*' in question:
            parts = re.findall(r'\d+', question)
            if len(parts) >= 2:
                return str(int(parts[0]) * int(parts[1]))
        elif '+' in question:
            parts = re.findall(r'\d+', question)
            if len(parts) >= 2:
                return str(int(parts[0]) + int(parts[1]))
        elif '-' in question:
            parts = re.findall(r'\d+', question)
            if len(parts) >= 2:
                return str(int(parts[0]) - int(parts[1]))
        
        return "42"  # 默认答案
    
    def bypass_verification(self, url, max_retries=3):
        """绕过验证页面"""
        for attempt in range(max_retries):
            try:
                logger.info(f"访问 (尝试 {attempt + 1}/{max_retries}): {url}")
                response = self.session.get(url, timeout=15)
                
                if response.status_code != 200:
                    logger.warning(f"请求状态码异常: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                        continue
                    else:
                        logger.error(f"最终请求失败，状态码: {response.status_code}")
                        return None
                    
                # 检查是否是验证页面
                response_text = response.text
                
                # 检查验证问题的具体模式
                verification_patterns = [
                    r'\d+[×*]\d+=？',
                    r'\d+[+]\d+=？', 
                    r'\d+[-]\d+=？',
                    r'请回答.*?\d+[×*+\-]\d+',
                    r'羽毛球有几根毛',
                    r'ZYZX.*?怎么写',
                    r'中羽.*?缩写',
                    r'验证.*?问题',
                    r'请输入.*?答案',
                ]
                
                has_verification_question = any(re.search(pattern, response_text, re.IGNORECASE) 
                                              for pattern in verification_patterns)
                
                # 检查是否有验证表单
                soup = BeautifulSoup(response_text, 'html.parser')
                verification_form = soup.find('form', {'name': 'form2'}) or soup.find('input', {'name': 'seccode'})
                
                if has_verification_question or verification_form:
                    logger.info(f"检测到验证页面，尝试自动处理...")
                    verified_content = self.handle_verification(response_text, url)
                    if verified_content:
                        logger.info(f"✅ 验证成功 (尝试 {attempt + 1})")
                        return verified_content
                    else:
                        logger.warning(f"❌ 验证失败 (尝试 {attempt + 1})")
                        if attempt < max_retries - 1:
                            time.sleep(3)
                            continue
                else:
                    logger.info(f"✅ 直接访问成功，无需验证 (尝试 {attempt + 1})")
                    return response_text
                    
            except Exception as e:
                logger.error(f"访问失败 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
        
        logger.error(f"经过 {max_retries} 次尝试仍无法访问: {url}")
        return None
    
    def handle_verification(self, html_content, original_url):
        """处理验证页面"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找验证问题
            question_text = ""
            
            # 方法1: 查找包含数学表达式的文本
            all_text = soup.get_text()
            math_patterns = [
                r'(\d+[×*+\-]\d+)=？',
                r'请回答.*?(\d+[×*+\-]\d+)',
                r'(\d+)\s*[×*]\s*(\d+)\s*=？',
                r'(\d+)\s*[+]\s*(\d+)\s*=？',
                r'(\d+)\s*[-]\s*(\d+)\s*=？'
            ]
            
            for pattern in math_patterns:
                match = re.search(pattern, all_text)
                if match:
                    question_text = match.group(1) if len(match.groups()) == 1 else f"{match.group(1)}{match.group(0)[match.group(1).__len__():match.group(0).find('=')]}"
                    break
            
            # 方法2: 查找特定的验证问题
            if not question_text:
                common_questions = [
                    r'羽毛球有几根毛',
                    r'ZYZX.*?怎么写',
                    r'中羽.*?缩写'
                ]
                
                for pattern in common_questions:
                    match = re.search(pattern, all_text, re.IGNORECASE)
                    if match:
                        question_text = match.group(0)
                        break
            
            if not question_text:
                logger.error("未找到验证问题")
                return None
            
            # 获取答案
            answer = self.ask_ai_for_answer(question_text)
            if not answer:
                logger.error("未获得验证答案")
                return None
            
            # 查找验证表单
            form = soup.find('form', {'name': 'form2'})
            if not form:
                form = soup.find('form')
            
            if not form:
                logger.error("未找到验证表单")
                return None
            
            # 构建验证请求
            form_data = {}
            
            # 获取所有隐藏字段
            for input_tag in form.find_all('input'):
                input_type = input_tag.get('type', '').lower()
                input_name = input_tag.get('name', '')
                input_value = input_tag.get('value', '')
                
                if input_type in ['hidden', 'text'] and input_name:
                    if input_name in ['seccode', 'answer', 'verification', 'code']:
                        form_data[input_name] = answer
                    else:
                        form_data[input_name] = input_value
            
            # 获取表单提交URL
            form_action = form.get('action', '')
            if form_action:
                if form_action.startswith('/'):
                    submit_url = 'https://www.badmintoncn.com' + form_action
                elif form_action.startswith('http'):
                    submit_url = form_action
                else:
                    submit_url = original_url.rsplit('/', 1)[0] + '/' + form_action
            else:
                submit_url = original_url
            
            logger.info(f"提交验证答案: {answer} 到 {submit_url}")
            
            # 提交验证表单
            response = self.session.post(submit_url, data=form_data, timeout=15)
            
            if response.status_code == 200:
                # 检查是否还有验证页面
                if not any(re.search(pattern, response.text, re.IGNORECASE) 
                          for pattern in [r'验证.*?问题', r'\d+[×*+\-]\d+=？']):
                    return response.text
                else:
                    logger.warning("验证后仍显示验证页面，可能答案错误")
                    return None
            else:
                logger.error(f"验证提交失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"处理验证页面失败: {e}")
            return None
    
    def get_equipment_list(self):
        """获取装备列表"""
        try:
            list_url = "https://www.badmintoncn.com/cbo_eq/list.php"
            html_content = self.bypass_verification(list_url)
            
            if not html_content:
                return []
            
            equipment_links = self.parse_equipment_links(html_content)
            logger.info(f"找到 {len(equipment_links)} 个装备链接")
            return equipment_links
            
        except Exception as e:
            logger.error(f"获取装备列表失败: {e}")
            return []
    
    def parse_equipment_links(self, html_content):
        """解析装备链接"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            links = []
            
            # 查找装备详情链接
            for link in soup.find_all('a', href=True):
                href = link['href']
                if 'view.php?eid=' in href:
                    if href.startswith('/'):
                        full_url = 'https://www.badmintoncn.com' + href
                    elif href.startswith('http'):
                        full_url = href
                    else:
                        full_url = 'https://www.badmintoncn.com/cbo_eq/' + href
                    
                    if full_url not in links:
                        links.append(full_url)
            
            return links[:self.max_items] if self.max_items else links
            
        except Exception as e:
            logger.error(f"解析装备链接失败: {e}")
            return []
    
    def parse_equipment_detail(self, url, max_retries=2):
        """解析装备详情页面 - 增强版数据提取"""
        equipment_id = url.split('eid=')[-1]
        
        for attempt in range(max_retries):
            try:
                logger.info(f"解析装备详情 (尝试 {attempt + 1}/{max_retries}): {url}")
                
                # 获取主页面
                html_content = self.bypass_verification(url)
                if not html_content:
                    continue
                
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 初始化装备数据
                equipment_data = {
                    'equipment_id': equipment_id,
                    'equipment_name': '',
                    'equipment_type': '',
                    'brand': '',
                    'series': '',
                    'description': '',
                    'release_date': '',
                    'introduction': '',
                    'specifications': '',
                    'frame_material': '',
                    'shaft_material': '',
                    'weight': '',
                    'length': '',
                    'grip_size': '',
                    'shaft_stiffness': '',
                    'string_tension': '',
                    'balance_point': '',
                    'purchase_price': '',
                    'new_avg_price': '',
                    'used_avg_price': '',
                    'total_users': '',
                    'review_count': '',
                    'pro_players': '',
                    'image_url': '',
                    'detail_url': url,
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # 提取基础信息
                self.extract_basic_info(soup, equipment_data)
                
                # 提取技术规格参数
                self.extract_technical_specs(soup, equipment_data)
                
                # 获取价格信息（需要访问入手价页面）
                self.extract_price_info(equipment_id, equipment_data)
                
                # 获取使用球员信息
                self.extract_pro_players(equipment_id, equipment_data)
                
                # 提取图片
                self.extract_image_url(soup, equipment_data)
                
                # 提取描述和介绍
                self.extract_description_and_intro(html_content, equipment_data)
                
                # 从文本中提取技术规格
                self.extract_tech_specs_from_text(soup, equipment_data)
                
                # 提取评价数量
                self.extract_review_count(soup, equipment_data)
                
                # 清理装备名称
                equipment_data['equipment_name'] = self.extract_equipment_name(equipment_data['equipment_name'])
                
                logger.info(f"✅ 解析完成: {equipment_data['equipment_name']} - {equipment_data['brand']}")
                return equipment_data
                
            except Exception as e:
                logger.error(f"解析装备详情失败 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    return None
        
        return None

    def extract_basic_info(self, soup, equipment_data):
        """提取基础信息：类型、品牌、系列、上市日期"""
        try:
            # 提取装备名称
            title_elem = soup.find('title')
            if title_elem:
                equipment_data['equipment_name'] = title_elem.text.strip()
            
            # 查找基础信息表格
            for table in soup.find_all('table'):
                for row in table.find_all('tr'):
                    cells = row.find_all('td')
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if '类型' in label and not equipment_data['equipment_type']:
                            equipment_data['equipment_type'] = value
                        elif '品牌' in label and not equipment_data['brand']:
                            equipment_data['brand'] = value
                        elif '系列' in label and not equipment_data['series']:
                            equipment_data['series'] = value
                        elif '上市' in label and not equipment_data['release_date']:
                            equipment_data['release_date'] = value
                            
        except Exception as e:
            logger.warning(f"提取基础信息失败: {e}")

    def extract_technical_specs(self, soup, equipment_data):
        """提取技术规格参数"""
        try:
            # 查找规格参数表格
            for table in soup.find_all('table'):
                for row in table.find_all('tr'):
                    cells = row.find_all('td')
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if '拍框材质' in label and not equipment_data['frame_material']:
                            equipment_data['frame_material'] = value
                        elif '拍杆材质' in label and not equipment_data['shaft_material']:
                            equipment_data['shaft_material'] = value
                        elif '拍身重量' in label and not equipment_data['weight']:
                            equipment_data['weight'] = value
                        elif '拍身长度' in label and not equipment_data['length']:
                            equipment_data['length'] = value
                        elif '拍柄粗细' in label and not equipment_data['grip_size']:
                            equipment_data['grip_size'] = value
                        elif '中管韧度' in label and not equipment_data['shaft_stiffness']:
                            equipment_data['shaft_stiffness'] = value
                        elif '拉线磅数' in label and not equipment_data['string_tension']:
                            equipment_data['string_tension'] = value
                        elif ('平衡点' in label or '平 衡 点' in label) and not equipment_data['balance_point']:
                            equipment_data['balance_point'] = value
                            
        except Exception as e:
            logger.warning(f"提取技术规格失败: {e}")

    def extract_price_info(self, equipment_id, equipment_data):
        """提取价格信息：入手价、全新均价、二手均价、总用户数"""
        try:
            price_url = f"https://www.badmintoncn.com/cbo_eq/view_buy.php?eid={equipment_id}"
            price_html = self.bypass_verification(price_url)
            
            if price_html:
                price_soup = BeautifulSoup(price_html, 'html.parser')
                
                # 查找价格相关的强调文本
                for strong in price_soup.find_all('strong', class_='bluetext2 bigtext'):
                    text = strong.get_text(strip=True)
                    
                    # 获取后续文本来判断价格类型
                    parent = strong.parent
                    if parent:
                        parent_text = parent.get_text(strip=True)
                        
                        if '最近全新均价' in parent_text and not equipment_data['new_avg_price']:
                            price_match = re.search(r'([\d,.]+)', text)
                            if price_match:
                                equipment_data['new_avg_price'] = price_match.group(1).replace(',', '')
                        elif '最近二手均价' in parent_text and not equipment_data['used_avg_price']:
                            price_match = re.search(r'([\d,.]+)', text)
                            if price_match:
                                equipment_data['used_avg_price'] = price_match.group(1).replace(',', '')
                        elif '总登记球友' in parent_text and not equipment_data['total_users']:
                            users_match = re.search(r'(\d+)', text)
                            if users_match:
                                equipment_data['total_users'] = users_match.group(1)
                
                # 也从表格中查找价格信息
                for table in price_soup.find_all('table'):
                    for row in table.find_all('tr'):
                        cells = row.find_all('td')
                        for cell in cells:
                            cell_text = cell.get_text(strip=True)
                            if '全新均价' in cell_text and not equipment_data['new_avg_price']:
                                strong_elem = cell.find('strong')
                                if strong_elem:
                                    price_text = strong_elem.get_text(strip=True)
                                    price_match = re.search(r'([\d,.]+)', price_text)
                                    if price_match:
                                        equipment_data['new_avg_price'] = price_match.group(1).replace(',', '')
                            elif '二手均价' in cell_text and not equipment_data['used_avg_price']:
                                strong_elem = cell.find('strong')
                                if strong_elem:
                                    price_text = strong_elem.get_text(strip=True)
                                    price_match = re.search(r'([\d,.]+)', price_text)
                                    if price_match:
                                        equipment_data['used_avg_price'] = price_match.group(1).replace(',', '')
                                    
        except Exception as e:
            logger.warning(f"提取价格信息失败: {e}")

    def extract_pro_players(self, equipment_id, equipment_data):
        """提取使用球员信息"""
        try:
            players_url = f"https://www.badmintoncn.com/cbo_eq/view_star.php?eid={equipment_id}"
            players_html = self.bypass_verification(players_url)
            
            if players_html:
                players_soup = BeautifulSoup(players_html, 'html.parser')
                players = []
                
                # 查找球员链接
                for link in players_soup.find_all('a'):
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    
                    # 如果链接指向球星页面，则是球员
                    if '/cbo_star/' in href and text and len(text) <= 6:
                        players.append(text)
                
                # 从页面文本中提取球员名
                text_content = players_soup.get_text()
                # 查找中文名字模式
                chinese_names = re.findall(r'[\u4e00-\u9fff]{2,4}', text_content)
                for name in chinese_names:
                    if len(name) >= 2 and len(name) <= 4 and name not in players and not any(char in name for char in '页面搜索评分装备'):
                        players.append(name)
                
                if players:
                    equipment_data['pro_players'] = '; '.join(list(set(players))[:5])  # 去重并限制最多5个球员
                    
        except Exception as e:
            logger.warning(f"提取球员信息失败: {e}")

    def extract_image_url(self, soup, equipment_data):
        """提取装备图片URL"""
        try:
            # 查找装备图片
            for img in soup.find_all('img'):
                src = img.get('src', '')
                alt = img.get('alt', '')
                
                if 'upload' in src and ('jpg' in src or 'png' in src or 'jpeg' in src):
                    if src.startswith('/'):
                        equipment_data['image_url'] = 'https://www.badmintoncn.com' + src
                    elif src.startswith('http'):
                        equipment_data['image_url'] = src
                    break
                    
        except Exception as e:
            logger.warning(f"提取图片URL失败: {e}")

    def extract_description_and_intro(self, html_content, equipment_data):
        """提取描述和介绍"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 从页面中提取描述性文本
            text_content = soup.get_text()
            
            # 查找描述关键词
            description_keywords = ['亮点：', '特点：', '优势：', '特色：']
            for keyword in description_keywords:
                if keyword in text_content:
                    start_idx = text_content.find(keyword)
                    if start_idx != -1:
                        desc_text = text_content[start_idx:start_idx+50].strip()
                        if not equipment_data['description']:
                            equipment_data['description'] = desc_text.replace(keyword, '').strip()
                        break
            
            # 查找介绍信息
            intro_keywords = ['介绍：', '简介：', '说明：']
            for keyword in intro_keywords:
                if keyword in text_content:
                    start_idx = text_content.find(keyword)
                    if start_idx != -1:
                        intro_text = text_content[start_idx:start_idx+100].strip()
                        if not equipment_data['introduction']:
                            equipment_data['introduction'] = intro_text.replace(keyword, '').strip()
                        break
                        
        except Exception as e:
            logger.warning(f"提取描述和介绍失败: {e}")

    def extract_tech_specs_from_text(self, soup, equipment_data):
        """从文本中提取技术规格"""
        try:
            text_content = soup.get_text()
            
            # 提取重量信息
            if not equipment_data['weight']:
                weight_patterns = [r'(\d+U)', r'重量.*?(\d+)g', r'(\d+)克']
                for pattern in weight_patterns:
                    match = re.search(pattern, text_content, re.IGNORECASE)
                    if match:
                        equipment_data['weight'] = match.group(1)
                        break
            
            # 提取中管韧度
            if not equipment_data['shaft_stiffness']:
                stiffness_patterns = [r'中杆.*?(硬|软|适中)', r'韧度.*?(硬|软|适中)', r'(偏软|偏硬)']
                for pattern in stiffness_patterns:
                    match = re.search(pattern, text_content, re.IGNORECASE)
                    if match:
                        equipment_data['shaft_stiffness'] = match.group(1)
                        break
            
            # 提取拉线磅数
            if not equipment_data['string_tension']:
                tension_patterns = [r'(\d+)磅', r'(\d+)LBS', r'拉线.*?(\d+)']
                for pattern in tension_patterns:
                    match = re.search(pattern, text_content, re.IGNORECASE)
                    if match:
                        equipment_data['string_tension'] = match.group(1)
                        break
                        
        except Exception as e:
            logger.warning(f"从文本提取技术规格失败: {e}")

    def extract_review_count(self, soup, equipment_data):
        """提取评价数量"""
        try:
            text_content = soup.get_text()
            
            # 查找评价数量
            review_patterns = [r'(\d+).*?评价', r'评价.*?(\d+)', r'(\d+).*?条评论']
            for pattern in review_patterns:
                match = re.search(pattern, text_content)
                if match:
                    equipment_data['review_count'] = match.group(1)
                    break
                    
        except Exception as e:
            logger.warning(f"提取评价数量失败: {e}")

    def extract_equipment_name(self, title):
        """从标题中提取装备名称"""
        try:
            # 移除网站标识
            name = title.replace('中羽在线 badmintoncn.com', '').strip()
            name = re.sub(r'\s+', ' ', name)
            return name
        except:
            return title

    def run(self, max_items=None):
        """运行爬虫"""
        if max_items:
            self.max_items = max_items
            
        logger.info(f"🚀 开始爬取装备数据，最大数量: {self.max_items}")
        
        # 获取装备列表
        equipment_links = self.get_equipment_list()
        if not equipment_links:
            logger.error("❌ 未获取到装备链接")
            return
        
        # 爬取装备详情
        for i, link in enumerate(equipment_links[:self.max_items]):
            try:
                logger.info(f"\n📦 正在爬取 ({i+1}/{min(len(equipment_links), self.max_items)}): {link}")
                
                equipment_data = self.parse_equipment_detail(link)
                if equipment_data:
                    self.equipment_data.append(equipment_data)
                
                # 控制请求频率
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"爬取装备失败: {e}")
                continue
        
        logger.info(f"\n🎉 爬取完成！成功获取 {len(self.equipment_data)} 条装备数据")
        
        # 保存数据
        self.save_data()
        
        # 分析数据
        self.analyze_data()

    def save_data(self):
        """保存数据"""
        if not self.equipment_data:
            logger.warning("没有数据需要保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存CSV
        csv_filename = f"output/enhanced_equipment_{timestamp}.csv"
        self.save_to_csv(self.equipment_data, csv_filename)
        logger.info(f"📁 CSV数据已保存到: {csv_filename}")
        
        # 保存JSON
        json_filename = f"output/enhanced_equipment_{timestamp}.json"
        self.save_to_json(self.equipment_data, json_filename)
        logger.info(f"📁 JSON数据已保存到: {json_filename}")

    def save_to_csv(self, data, filename):
        """保存为CSV格式"""
        if not data:
            return
        
        fieldnames = [
            'equipment_id', 'equipment_name', 'equipment_type', 'brand', 'series',
            'description', 'release_date', 'introduction', 'specifications',
            'frame_material', 'shaft_material', 'weight', 'length', 'grip_size',
            'shaft_stiffness', 'string_tension', 'balance_point',
            'purchase_price', 'new_avg_price', 'used_avg_price',
            'total_users', 'review_count', 'pro_players',
            'image_url', 'detail_url', 'crawl_time'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for item in data:
                writer.writerow(item)

    def save_to_json(self, data, filename):
        """保存为JSON格式"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def analyze_data(self):
        """分析数据"""
        if not self.equipment_data:
            return
        
        logger.info(f"\n📊 数据分析报告:")
        logger.info(f"总装备数量: {len(self.equipment_data)}")
        
        # 品牌分布
        brands = {}
        for item in self.equipment_data:
            brand = item.get('brand', '未知')
            brands[brand] = brands.get(brand, 0) + 1
        
        logger.info(f"品牌分布:")
        for brand, count in sorted(brands.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {brand}: {count}")
        
        # 类型分布
        types = {}
        for item in self.equipment_data:
            eq_type = item.get('equipment_type', '未知')
            types[eq_type] = types.get(eq_type, 0) + 1
        
        logger.info(f"类型分布:")
        for eq_type, count in sorted(types.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {eq_type}: {count}")
        
        # 价格分析
        prices = []
        for item in self.equipment_data:
            new_price = item.get('new_avg_price', '')
            if new_price and str(new_price).replace('.', '').isdigit():
                prices.append(float(new_price))
        
        if prices:
            logger.info(f"价格分析:")
            logger.info(f"  最低价: ¥{min(prices):.0f}")
            logger.info(f"  最高价: ¥{max(prices):.0f}")
            logger.info(f"  平均价: ¥{sum(prices) / len(prices):.0f}")

def main():
    """主函数"""
    # 可以通过命令行参数设置爬取数量
    import sys
    max_items = 5 if len(sys.argv) < 2 else int(sys.argv[1])
    
    crawler = EnhancedBadmintonCrawler(max_items=max_items)
    crawler.run()
    
    logger.info(f"\n🎯 爬取任务完成！")

if __name__ == "__main__":
    main() 