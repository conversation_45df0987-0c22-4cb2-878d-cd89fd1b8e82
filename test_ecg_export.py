#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ECG导联数据导出功能
"""

import json
import os
import sys

# 添加路径以便导入模块
sys.path.append('data_frame')

# 导入修改后的函数
from 七牛云数据查询_备份 import save_single_result

def test_ecg_export():
    """测试ECG导联数据导出"""
    
    # 加载测试数据
    file_path = r'qiniu_query_results\低电压异常数据\肖总0726异常数据.json'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("=== 测试ECG导联数据导出 ===")
    print(f"数据中包含的ECG相关字段:")
    
    ecg_fields = []
    for key in data.keys():
        if 'ecg' in key.lower():
            ecg_fields.append(key)
            print(f"  - {key}: {type(data[key])}")
            if isinstance(data[key], str):
                print(f"    长度: {len(data[key])} 字符")
    
    print()
    
    # 创建测试输出目录
    test_output_dir = "test_ecg_export"
    os.makedirs(test_output_dir, exist_ok=True)
    
    # 测试导出功能
    print("开始测试导出功能...")
    result = save_single_result("test_ecg", data, test_output_dir)
    
    print(f"导出结果: {result}")
    
    # 检查生成的文件
    print("\n=== 检查生成的文件 ===")
    if os.path.exists(test_output_dir):
        files = os.listdir(test_output_dir)
        print(f"生成的文件: {files}")
        
        for file in files:
            filepath = os.path.join(test_output_dir, file)
            if os.path.isfile(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"\n文件: {file}")
                    print(f"  大小: {len(content)} 字符")
                    print(f"  前100字符: {content[:100]}...")
    else:
        print("输出目录不存在")

if __name__ == "__main__":
    test_ecg_export()
