#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的羽毛球装备数据分析脚本
"""

import csv
import json
import os
from collections import Counter
from datetime import datetime

def analyze_csv_data():
    """分析CSV数据"""
    
    # 查找最新的CSV文件
    csv_files = [f for f in os.listdir('.') if f.startswith('browser_crawl_') and f.endswith('.csv')]
    if not csv_files:
        print("❌ 未找到爬取结果文件")
        return
    
    latest_file = max(csv_files, key=lambda x: os.path.getctime(x))
    print(f"📁 分析文件: {latest_file}")
    
    # 读取CSV数据
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            data = list(reader)
        
        print(f"📊 数据总量: {len(data)} 条记录")
        
        if not data:
            print("❌ 文件为空")
            return
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    # 获取所有字段
    all_fields = data[0].keys() if data else []
    print(f"📋 总字段数: {len(all_fields)}")
    
    print("\n" + "="*60)
    print("📈 字段覆盖率分析")
    print("="*60)
    
    # 字段分类
    field_categories = {
        '基本信息': ['name', 'title', 'brand', 'series'],
        '技术参数': ['shaft_material', 'frame_material', 'technology', 'shaft_diameter', 'frame_design'],
        '价格信息': ['msrp_price', 'price_range', 'min_market_price', 'max_market_price', 'avg_market_price'],
        '性能参数': ['weight', 'balance_point', 'stiffness', 'string_tension'],
        '用户反馈': ['user_tags', 'rating', 'keywords'],
        '其他信息': ['description', 'release_date', 'crawl_time', 'url']
    }
    
    total_coverage = 0
    total_possible = 0
    
    for category, fields in field_categories.items():
        print(f"\n🏷️ {category}:")
        category_coverage = 0
        category_possible = 0
        
        for field in fields:
            if field in all_fields:
                # 计算有值的记录数
                filled_count = sum(1 for record in data if record.get(field) and record.get(field).strip())
                coverage_rate = (filled_count / len(data)) * 100
                print(f"  {field:20} | {filled_count:2d}/{len(data)} | {coverage_rate:5.1f}%")
                category_coverage += filled_count
                category_possible += len(data)
            else:
                print(f"  {field:20} | -- | 字段不存在")
        
        if category_possible > 0:
            category_rate = (category_coverage / category_possible) * 100
            print(f"  {'📊 分类覆盖率':20} | {category_coverage}/{category_possible} | {category_rate:5.1f}%")
        
        total_coverage += category_coverage
        total_possible += category_possible
    
    overall_rate = (total_coverage / total_possible) * 100 if total_possible > 0 else 0
    print(f"\n🎯 总体字段覆盖率: {total_coverage}/{total_possible} | {overall_rate:.1f}%")
    
    print("\n" + "="*60)
    print("🔍 数据质量分析")
    print("="*60)
    
    # 品牌分布
    if 'brand' in all_fields:
        brands = [record.get('brand', '').strip() for record in data if record.get('brand', '').strip()]
        brand_counts = Counter(brands)
        print(f"\n🏢 品牌分布:")
        for brand, count in brand_counts.most_common():
            print(f"  {brand:20} | {count} 个产品")
    
    # 系列分布
    if 'series' in all_fields:
        series_list = [record.get('series', '').strip() for record in data if record.get('series', '').strip()]
        series_counts = Counter(series_list)
        print(f"\n📱 系列分布:")
        for series, count in list(series_counts.most_common(5)):
            print(f"  {series:20} | {count} 个产品")
    
    # 价格分析
    if 'msrp_price' in all_fields:
        prices = []
        for record in data:
            price_str = record.get('msrp_price', '').strip()
            if price_str and price_str.isdigit():
                prices.append(int(price_str))
        
        if prices:
            print(f"\n💰 价格分析 (基于 {len(prices)} 个有效价格):")
            print(f"  最低价格: ¥{min(prices)}")
            print(f"  最高价格: ¥{max(prices)}")
            print(f"  平均价格: ¥{sum(prices)//len(prices)}")
            prices_sorted = sorted(prices)
            median_price = prices_sorted[len(prices)//2]
            print(f"  中位价格: ¥{median_price}")
    
    # 技术参数统计
    tech_fields = ['shaft_material', 'frame_material', 'technology']
    print(f"\n⚙️ 技术参数提取情况:")
    for field in tech_fields:
        if field in all_fields:
            filled = sum(1 for record in data if record.get(field, '').strip())
            print(f"  {field:20} | {filled}/{len(data)} 个产品")
    
    # 用户标签分析
    if 'user_tags' in all_fields:
        tags_records = [record.get('user_tags', '').strip() for record in data if record.get('user_tags', '').strip()]
        tag_filled = len(tags_records)
        print(f"\n👥 用户评价标签: {tag_filled}/{len(data)} 个产品有标签")
        
        # 提取最常见的标签关键词
        all_tags = []
        for tags in tags_records:
            if tags:
                # 简单分割标签
                tag_list = [t.strip() for t in tags.replace(',', ' ').split()]
                # 提取中文标签
                chinese_tags = [t for t in tag_list if any('\u4e00' <= c <= '\u9fff' for c in t)]
                all_tags.extend(chinese_tags)
        
        if all_tags:
            common_tags = Counter(all_tags).most_common(8)
            print("  常见标签词:")
            for tag, count in common_tags:
                print(f"    {tag:10} | {count} 次提及")
    
    # 样本数据展示
    print(f"\n" + "="*60)
    print("📋 样本数据展示")
    print("="*60)
    
    # 计算数据完整性并显示最完整的记录
    important_fields = ['name', 'brand', 'series', 'msrp_price', 'shaft_material', 'user_tags']
    available_important = [f for f in important_fields if f in all_fields]
    
    records_with_scores = []
    for record in data:
        filled_count = sum(1 for field in available_important if record.get(field, '').strip())
        score = (filled_count / len(available_important)) * 100 if available_important else 0
        records_with_scores.append((record, score))
    
    # 按完整性排序，取前3
    top_records = sorted(records_with_scores, key=lambda x: x[1], reverse=True)[:3]
    
    for idx, (record, score) in enumerate(top_records, 1):
        print(f"\n🏸 样本 {idx} (完整性: {score:.1f}%):")
        print(f"  产品名称: {record.get('name', 'N/A')}")
        print(f"  品牌: {record.get('brand', 'N/A')}")
        print(f"  系列: {record.get('series', 'N/A')}")
        print(f"  价格: {record.get('msrp_price', 'N/A')}")
        print(f"  中管材料: {record.get('shaft_material', 'N/A')}")
        if record.get('user_tags', '').strip():
            tags = record['user_tags']
            tags_display = tags[:60] + "..." if len(tags) > 60 else tags
            print(f"  用户标签: {tags_display}")
    
    # 数据完整性统计
    completeness_scores = [score for _, score in records_with_scores]
    avg_completeness = sum(completeness_scores) / len(completeness_scores) if completeness_scores else 0
    
    print(f"\n🎯 数据完整性统计:")
    print(f"  平均完整性: {avg_completeness:.1f}%")
    print(f"  完整性分布:")
    print(f"    90-100%: {sum(1 for s in completeness_scores if s >= 90)} 条记录")
    print(f"    80-89%:  {sum(1 for s in completeness_scores if 80 <= s < 90)} 条记录")
    print(f"    70-79%:  {sum(1 for s in completeness_scores if 70 <= s < 80)} 条记录")
    print(f"    <70%:    {sum(1 for s in completeness_scores if s < 70)} 条记录")
    
    # 保存分析报告
    report = {
        'analysis_time': datetime.now().isoformat(),
        'file_analyzed': latest_file,
        'total_records': len(data),
        'total_fields': len(all_fields),
        'overall_coverage_rate': overall_rate,
        'average_completeness': avg_completeness,
        'available_fields': list(all_fields),
        'summary': {
            'brands': len(Counter([r.get('brand', '') for r in data if r.get('brand', '').strip()])),
            'series': len(Counter([r.get('series', '') for r in data if r.get('series', '').strip()])),
            'with_prices': len([r for r in data if r.get('msrp_price', '').strip()]),
            'with_tech_params': len([r for r in data if r.get('shaft_material', '').strip()]),
            'with_user_tags': len([r for r in data if r.get('user_tags', '').strip()])
        }
    }
    
    with open('simple_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细分析报告已保存到: simple_analysis_report.json")
    print(f"\n✅ 分析完成！数据质量评估: {'优秀' if avg_completeness >= 80 else '良好' if avg_completeness >= 60 else '一般'}")

if __name__ == "__main__":
    analyze_csv_data() 