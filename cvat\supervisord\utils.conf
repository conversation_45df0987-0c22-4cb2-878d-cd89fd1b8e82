[unix_http_server]
file = /tmp/supervisord/supervisor.sock

[supervisorctl]
serverurl = unix:///tmp/supervisord/supervisor.sock


[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisord]
nodaemon=true
logfile=%(ENV_HOME)s/logs/supervisord.log ; supervisord log file
logfile_maxbytes=50MB       ; maximum size of logfile before rotation
logfile_backups=10          ; number of backed up logfiles
loglevel=debug              ; info, debug, warn, trace
pidfile=/tmp/supervisord/supervisord.pid ; pidfile location

[program:rqscheduler]
command=%(ENV_HOME)s/wait_for_deps.sh
    python3 %(ENV_HOME)s/rqscheduler.py
        --host "%(ENV_CVAT_REDIS_INMEM_HOST)s" --port "%(ENV_CVAT_REDIS_INMEM_PORT)s"
        --password "%(ENV_CVAT_REDIS_INMEM_PASSWORD)s"
        -i 30 --path %(ENV_HOME)s
environment=VECTOR_EVENT_HANDLER="SynchronousLogstashHandler"
numprocs=1
autorestart=true

[program:rqworker]
command=%(ENV_HOME)s/wait_for_deps.sh
    python3 %(ENV_HOME)s/manage.py rqworker -v 3 notifications cleaning
        --worker-class cvat.rqworker.DefaultWorker
environment=VECTOR_EVENT_HANDLER="SynchronousLogstashHandler",CVAT_POSTGRES_APPLICATION_NAME="cvat:worker:notifications+cleaning"
numprocs=%(ENV_NUMPROCS)s
autorestart=true
