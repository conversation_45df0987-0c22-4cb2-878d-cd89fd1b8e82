""" coding: utf-8 """

from apps.common.information import get_signal_quality, statistic_abnormal_data, get_snb_info, get_report_advice
from apps.utils.ai_chat import ai_reply



def get_report(report_model, datas, normal_data):
    """
    获取报告
    :param report_model: 报告模型
    :param datas: 原始数据
    :param normal_data: 正常数据
    :return: 报告模型
    """
    # 获取信号质量
    report_model.signal_quality = get_signal_quality(datas, normal_data)
    # 获取年龄
    report_model.ecg_age = 0 if normal_data['ecgage'].empty else int(normal_data['ecgage'].mean())
    # 获取心率
    if normal_data['pqrstc_hr'].empty:
        report_model.hr_mean = 0
        report_model.hr_max = 0
        report_model.hr_min = 0
    else:
        report_model.hr_mean = int(normal_data['pqrstc_hr'].mean())
        report_model.hr_max = int(normal_data['pqrstc_hr'].max())
        report_model.hr_min = int(normal_data['pqrstc_hr'].min())

    report_model.total_heart_beat = report_model.hr_mean * len(normal_data)

    # 获取异常数据
    abnormal_infos = statistic_abnormal_data(normal_data)
    report_model.abnormal_heart_beat = sum(abnormal_info.heart_beat for abnormal_info in abnormal_infos)
    report_model.abnormal_infos = abnormal_infos

    # 获取心动过缓信息
    snb_count, snb_duration = get_snb_info(normal_data)
    report_model.snb_count = snb_count
    report_model.snb_duration = snb_duration

    # 获取健康指数
    report_model.emotion = 0 if normal_data['healthmetrics_emotion'].empty else int(normal_data['healthmetrics_emotion'].mean())
    report_model.fatigue = 0 if normal_data['healthmetrics_fatigue'].empty else int(normal_data['healthmetrics_fatigue'].mean())
    report_model.hrv = 0 if normal_data['healthmetrics_hrv'].empty else int(normal_data['healthmetrics_hrv'].mean())
    report_model.pressure = 0 if normal_data['healthmetrics_pressure'].empty else int(normal_data['healthmetrics_pressure'].mean())
    report_model.vitality = 0 if normal_data['healthmetrics_vitality'].empty else int(normal_data['healthmetrics_vitality'].mean())

    # 获取报告建议
    report_model.report_advice = ai_reply(get_report_advice(report_model, abnormal_infos))

    return report_model
