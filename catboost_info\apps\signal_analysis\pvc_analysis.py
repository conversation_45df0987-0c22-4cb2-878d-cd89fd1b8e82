import numpy as np
from apps.utils.logger_helper import Logger

# --- 室性早搏检测函数 ---

def detect_classify_pvc(rr_intervals, r_peaks, sampling_rate, prematurity_threshold=0.85, compensatory_threshold=1.15,
                        min_vt_run=3):
    """简化版室性早搏检测与分类"""
    pvc_indices = []
    classification = {'single': 0, 'pair': 0, 'run': [], 'bigeminy_count': 0, 'trigeminy_count': 0}

    if rr_intervals is None or len(rr_intervals) < 2 or r_peaks is None or len(r_peaks) < 3:
        return {'pvc_indices': [], 'classification': classification}

    try:
        rr_intervals = np.array(rr_intervals)
        r_peaks = np.array(r_peaks)
        beat_types = ['N'] * len(r_peaks)  # N: Normal, V: PVC

        # 计算局部平均RR
        window_size = 5
        if len(rr_intervals) >= window_size:
            local_avg_rr = np.convolve(rr_intervals, np.ones(window_size) / window_size, mode='valid')
            padding_start = (window_size - 1) // 2
            padding_end = (window_size - 1) - padding_start
            local_avg_rr_padded = np.pad(local_avg_rr, (padding_start, padding_end), mode='edge')
        elif len(rr_intervals) > 0:
            local_avg_rr_padded = np.full(len(rr_intervals), np.mean(rr_intervals))
        else:
            return {'pvc_indices': [], 'classification': classification}

        # 根据时序特征检测PVC
        for i in range(len(rr_intervals)):
            baseline_rr = local_avg_rr_padded[i]
            if baseline_rr <= 0:
                continue

            # 提前性检查
            is_premature = rr_intervals[i] < prematurity_threshold * baseline_rr

            # 代偿性间歇检查
            is_compensatory = False
            if i + 1 < len(rr_intervals):
                baseline_rr_comp = local_avg_rr_padded[i + 1]
                if baseline_rr_comp > 0:
                    is_compensatory = rr_intervals[i + 1] > compensatory_threshold * baseline_rr_comp

            # 简化PVC规则：主要基于提前性
            if is_premature:
                pvc_r_peak_index = i + 1
                if pvc_r_peak_index < len(beat_types):
                    pvc_indices.append(int(r_peaks[pvc_r_peak_index]))
                    beat_types[pvc_r_peak_index] = 'V'

        # 根据beat_types序列分类
        singles = 0
        pairs = 0
        runs = []
        bigeminy_count = 0
        trigeminy_count = 0

        i = 0
        while i < len(beat_types):
            if beat_types[i] == 'V':
                run_count = 0
                start_beat_idx = i
                while i < len(beat_types) and beat_types[i] == 'V':
                    run_count += 1
                    i += 1

                # 分类PVC类型
                if run_count == 1:
                    singles += 1
                    # 二联律和三联律检查
                    if start_beat_idx > 0 and beat_types[start_beat_idx - 1] == 'N':
                        bigeminy_count += 1
                    if start_beat_idx > 1 and beat_types[start_beat_idx - 1] == 'N' and beat_types[
                        start_beat_idx - 2] == 'N':
                        trigeminy_count += 1
                elif run_count == 2:
                    pairs += 1
                elif run_count >= min_vt_run:
                    # 室速发作
                    run_start_rpeak_idx = start_beat_idx
                    run_end_rpeak_idx = i - 1

                    if run_start_rpeak_idx < len(rr_intervals) and run_end_rpeak_idx > run_start_rpeak_idx:
                        run_rrs = rr_intervals[run_start_rpeak_idx: run_end_rpeak_idx]
                        if len(run_rrs) > 0:
                            avg_rr = np.mean(run_rrs)
                            min_hr = 60 / np.max(run_rrs) if np.max(run_rrs) > 0 else 0
                            max_hr = 60 / np.min(run_rrs) if np.min(run_rrs) > 0 else 0
                            runs.append({
                                'start_index': int(r_peaks[run_start_rpeak_idx]),
                                'count': run_count,
                                'avg_rr': avg_rr,
                                'min_hr': min_hr,
                                'max_hr': max_hr
                            })
                        else:
                            runs.append(
                                {'start_index': int(r_peaks[run_start_rpeak_idx]), 'count': run_count, 'avg_rr': 0,
                                 'min_hr': 0, 'max_hr': 0})
                    else:
                        runs.append({'start_index': int(r_peaks[run_start_rpeak_idx]), 'count': run_count, 'avg_rr': 0,
                                     'min_hr': 0, 'max_hr': 0})
                continue
            i += 1

        classification = {
            'single': singles,
            'pair': pairs,
            'run': runs,
            'bigeminy_count': bigeminy_count,
            'trigeminy_count': trigeminy_count
        }

    except Exception as e:
        Logger().error(f"PVC检测错误: {str(e)}")
        return {'pvc_indices': [], 'classification': classification}

    return {'pvc_indices': [int(idx) for idx in pvc_indices], 'classification': classification}


def quantify_pvc(pvc_classification, total_beats):
    """简化版室性早搏量化"""
    classification = pvc_classification.get('classification', {})
    runs = classification.get('run', [])

    if total_beats is None:
        total_beats = 0

    # 初始化结果字典，使用建议的字段名
    result = {
        'pvc_single_count': 0,
        'pvc_pair_count': 0,
        'pvc_bigeminy_count': 0,
        'pvc_trigeminy_count': 0,
        'pvc_run_count': len(runs),
        'pvc_total_count': 0,
        'pvc_percentage': 0,
        'pvc_max_consecutive': 0,
        'pvc_fastest_run_hr': None,
        'pvc_slowest_run_hr': None
    }

    try:
        # 从 classification 字典获取原始计数
        singles = classification.get('single', 0)
        pairs = classification.get('pair', 0)
        bigeminy = classification.get('bigeminy_count', 0)
        trigeminy = classification.get('trigeminy_count', 0)
        run_beats = sum(r.get('count', 0) for r in runs)

        # 总PVC计数
        total_pvc_count = singles + (pairs * 2) + run_beats
        percentage = (total_pvc_count / total_beats) * 100 if total_beats > 0 else 0

        # 最大连续PVC
        max_consecutive_pvc = max([r.get('count', 0) for r in runs] + [0]) if runs else 0
        if pairs > 0 and max_consecutive_pvc < 2:
            max_consecutive_pvc = 2
        if singles > 0 and max_consecutive_pvc < 1:
            max_consecutive_pvc = 1

        # 室速心率范围
        fastest_vt_hr = max([r.get('max_hr', 0) for r in runs] + [0]) if runs else None
        if fastest_vt_hr == 0:
            fastest_vt_hr = None

        slowest_vt_hr = min(
            [r.get('min_hr', float('inf')) for r in runs if r.get('min_hr', 0) > 0] + [float('inf')]) if runs else None
        if slowest_vt_hr == float('inf'):
            slowest_vt_hr = None

        # 更新结果字典
        result.update({
            'pvc_single_count': int(singles),
            'pvc_pair_count': int(pairs),
            'pvc_bigeminy_count': int(bigeminy),
            'pvc_trigeminy_count': int(trigeminy),
            # pvc_run_count 已经在初始化时计算
            'pvc_total_count': int(total_pvc_count),
            'pvc_percentage': percentage,
            'pvc_max_consecutive': int(max_consecutive_pvc),
            'pvc_fastest_run_hr': fastest_vt_hr,
            'pvc_slowest_run_hr': slowest_vt_hr
        })
    except Exception as e:
        Logger().error(f"PVC量化错误: {str(e)}")

    return result

def analyze_pvc(rr_intervals, r_peaks, sampling_rate, total_beats):
    """执行PVC检测和量化"""
    detection_results = detect_classify_pvc(rr_intervals, r_peaks, sampling_rate)
    quantification = quantify_pvc(detection_results, total_beats)
    # 为了减小API响应体积，可以选择不返回 detection_classification
    # return {'quantification': quantification}
    return {
        'detection_classification': detection_results,
        'quantification': quantification
    } 