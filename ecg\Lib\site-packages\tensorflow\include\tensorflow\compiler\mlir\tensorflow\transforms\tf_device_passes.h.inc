/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// AnnotateParameterReplicationPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AnnotateParameterReplicationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AnnotateParameterReplicationPassBase;

  AnnotateParameterReplicationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AnnotateParameterReplicationPassBase(const AnnotateParameterReplicationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-annotate-parameter-replication");
  }
  ::llvm::StringRef getArgument() const override { return "tf-annotate-parameter-replication"; }

  ::llvm::StringRef getDescription() const override { return "Annotate whether a ClusterFuncOp's parameters have the same data across replicas."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AnnotateParameterReplicationPass");
  }
  ::llvm::StringRef getName() const override { return "AnnotateParameterReplicationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// DecomposeResourceOpsInClusterPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class DecomposeResourceOpsInClusterPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = DecomposeResourceOpsInClusterPassBase;

  DecomposeResourceOpsInClusterPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  DecomposeResourceOpsInClusterPassBase(const DecomposeResourceOpsInClusterPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-decompose-resource-ops-in-cluster");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-decompose-resource-ops-in-cluster"; }

  ::llvm::StringRef getDescription() const override { return "Decompose composite resource variable operations into primitive Read/AssignVariableOp and raw computation within device cluster and reachable functions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DecomposeResourceOpsInClusterPass");
  }
  ::llvm::StringRef getName() const override { return "DecomposeResourceOpsInClusterPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// DecomposeResourceOpsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class DecomposeResourceOpsPassBase : public ::mlir::FunctionPass {
public:
  using Base = DecomposeResourceOpsPassBase;

  DecomposeResourceOpsPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  DecomposeResourceOpsPassBase(const DecomposeResourceOpsPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-decompose-resource-ops");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-decompose-resource-ops"; }

  ::llvm::StringRef getDescription() const override { return "Decompose composite resource variable operations into primitive Read/AssignVariableOp and raw computation."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DecomposeResourceOpsPass");
  }
  ::llvm::StringRef getName() const override { return "DecomposeResourceOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// DeviceAttributeToLaunchPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class DeviceAttributeToLaunchPassBase : public ::mlir::FunctionPass {
public:
  using Base = DeviceAttributeToLaunchPassBase;

  DeviceAttributeToLaunchPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  DeviceAttributeToLaunchPassBase(const DeviceAttributeToLaunchPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-attribute-to-launch");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-attribute-to-launch"; }

  ::llvm::StringRef getDescription() const override { return "Wraps each TF op which has a non-empty device attribute in a tf_device.launch."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DeviceAttributeToLaunchPass");
  }
  ::llvm::StringRef getName() const override { return "DeviceAttributeToLaunchPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// HostLaunchToOutsideCompiledPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class HostLaunchToOutsideCompiledPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = HostLaunchToOutsideCompiledPassBase;

  HostLaunchToOutsideCompiledPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  HostLaunchToOutsideCompiledPassBase(const HostLaunchToOutsideCompiledPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-host-launch-to-outside-compiled");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-host-launch-to-outside-compiled"; }

  ::llvm::StringRef getDescription() const override { return "Converts each op wrapped in launch op with host device assignnment to op with _xla_outside_compiled attribute."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HostLaunchToOutsideCompiledPass");
  }
  ::llvm::StringRef getName() const override { return "HostLaunchToOutsideCompiledPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LaunchToDeviceAttributePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LaunchToDeviceAttributePassBase : public ::mlir::FunctionPass {
public:
  using Base = LaunchToDeviceAttributePassBase;

  LaunchToDeviceAttributePassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LaunchToDeviceAttributePassBase(const LaunchToDeviceAttributePassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-launch-to-device-attribute");
  }
  ::llvm::StringRef getArgument() const override { return "tf-launch-to-device-attribute"; }

  ::llvm::StringRef getDescription() const override { return "Hoists and annotates device launch inner ops with associated device attribute."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LaunchToDeviceAttributePass");
  }
  ::llvm::StringRef getName() const override { return "LaunchToDeviceAttributePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// ResourceOpLiftingForMainFunctionPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ResourceOpLiftingForMainFunctionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ResourceOpLiftingForMainFunctionPassBase;

  ResourceOpLiftingForMainFunctionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ResourceOpLiftingForMainFunctionPassBase(const ResourceOpLiftingForMainFunctionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-resource-op-lifting-for-main-function");
  }
  ::llvm::StringRef getArgument() const override { return "tf-resource-op-lifting-for-main-function"; }

  ::llvm::StringRef getDescription() const override { return "Lifting resource operations out of control flow statements for the main function"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResourceOpLiftingForMainFunctionPass");
  }
  ::llvm::StringRef getName() const override { return "ResourceOpLiftingForMainFunctionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// ResourceOpLiftingPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ResourceOpLiftingPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ResourceOpLiftingPassBase;

  ResourceOpLiftingPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ResourceOpLiftingPassBase(const ResourceOpLiftingPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-resource-op-lifting");
  }
  ::llvm::StringRef getArgument() const override { return "tf-resource-op-lifting"; }

  ::llvm::StringRef getDescription() const override { return "Lifting resource operations out of device computation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResourceOpLiftingPass");
  }
  ::llvm::StringRef getName() const override { return "ResourceOpLiftingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AnnotateParameterReplicationPass Registration
//===----------------------------------------------------------------------===//

inline void registerAnnotateParameterReplicationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateAnnotateParameterReplicationPass();
  });
}

//===----------------------------------------------------------------------===//
// DecomposeResourceOpsInClusterPass Registration
//===----------------------------------------------------------------------===//

inline void registerDecomposeResourceOpsInClusterPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateDecomposeResourceOpsInClusterPass();
  });
}

//===----------------------------------------------------------------------===//
// DecomposeResourceOpsPass Registration
//===----------------------------------------------------------------------===//

inline void registerDecomposeResourceOpsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateDecomposeResourceOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// DeviceAttributeToLaunchPass Registration
//===----------------------------------------------------------------------===//

inline void registerDeviceAttributeToLaunchPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateDeviceAttributeToLaunchPass();
  });
}

//===----------------------------------------------------------------------===//
// HostLaunchToOutsideCompiledPass Registration
//===----------------------------------------------------------------------===//

inline void registerHostLaunchToOutsideCompiledPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateHostLaunchToOutsideCompiledPass();
  });
}

//===----------------------------------------------------------------------===//
// LaunchToDeviceAttributePass Registration
//===----------------------------------------------------------------------===//

inline void registerLaunchToDeviceAttributePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateLaunchToDeviceAttributePass();
  });
}

//===----------------------------------------------------------------------===//
// ResourceOpLiftingForMainFunctionPass Registration
//===----------------------------------------------------------------------===//

inline void registerResourceOpLiftingForMainFunctionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateResourceOpLiftingForMainFunctionPass();
  });
}

//===----------------------------------------------------------------------===//
// ResourceOpLiftingPass Registration
//===----------------------------------------------------------------------===//

inline void registerResourceOpLiftingPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateResourceOpLiftingPass();
  });
}

//===----------------------------------------------------------------------===//
// TensorFlowDevice Registration
//===----------------------------------------------------------------------===//

inline void registerTensorFlowDevicePasses() {
  registerAnnotateParameterReplicationPassPass();
  registerDecomposeResourceOpsInClusterPassPass();
  registerDecomposeResourceOpsPassPass();
  registerDeviceAttributeToLaunchPassPass();
  registerHostLaunchToOutsideCompiledPassPass();
  registerLaunchToDeviceAttributePassPass();
  registerResourceOpLiftingForMainFunctionPassPass();
  registerResourceOpLiftingPassPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
