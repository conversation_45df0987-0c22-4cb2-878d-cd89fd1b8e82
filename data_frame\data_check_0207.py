import pymysql
import pandas as pd
from datetime import datetime

def query_ecg_data(query_date, database='backend_v2'):
    """
    根据指定日期查询ECG数据
    Args:
        query_date: 查询日期
        database: 要查询的数据库名称，默认为backend_v2
    Returns:
        DataFrame: 查询结果
    """
    # 生成表名
    table_name = f"t_data_ecg_{query_date.strftime('%Y%m%d')}"
    
    # 使用指定数据库的连接
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database=database,
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 只按日期查询
        query = """
        SELECT id as ecg_id, union_id, start_time, end_time, source, sample_rate
        FROM t_data_ecg 
        WHERE DATE(start_time) = %s
        AND deleted = 0
        """
        
        cursor.execute(query, (query_date,))
        results = cursor.fetchall()
        
        if not results:
            print("未找到匹配的ECG记录")
            return pd.DataFrame()
            
        print(f"\n在backend_v2数据库中找到 {len(results)} 条记录")
        
        # 转换为DataFrame
        columns = ['ecg_id', 'union_id', 'start_time', 'end_time', 'source', 'sample_rate']
        df = pd.DataFrame(results, columns=columns)
        
        # 保存到Excel
        excel_file = f"ECG数据_{query_date.strftime('%Y%m%d')}.xlsx"
        df.to_excel(excel_file, index=False)
        print(f"\n数据已保存到文件: {excel_file}")
        
        # 显示数据统计信息
        print("\n数据统计信息:")
        print(f"总记录数: {len(df)}")
        print("\n数据来源分布:")
        print(df['source'].value_counts())
        print("\n采样率分布:")
        print(df['sample_rate'].value_counts())
        
        return df
            
    except Exception as e:
        print(f"查询数据时出错: {str(e)}")
        return pd.DataFrame()
        
    finally:
        cursor.close()
        connection.close()

# 使用示例
if __name__ == '__main__':
    query_date = datetime(2024, 2, 18).date()
    query_ecg_data(query_date)

def show_table_structure():
    """
    查看表结构
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_v2',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 查看表结构
        query = """
        DESCRIBE t_data_ecg;
        """
        
        cursor.execute(query)
        columns = cursor.fetchall()
        
        print("\n表结构:")
        for col in columns:
            print(f"字段名: {col[0]}, 类型: {col[1]}, 是否可空: {col[2]}, 键: {col[3]}, 默认值: {col[4]}")
            
    except Exception as e:
        print(f"查询表结构时出错: {str(e)}")
        
    finally:
        cursor.close()
        connection.close()

def check_user_data(union_id):
    """
    查看指定用户的所有ECG记录
    Args:
        union_id: 用户union_id
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_v2',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 查询用户所有记录
        query = """
        SELECT id as ecg_id, 
               start_time, 
               end_time,
               source,
               sample_rate,
               create_time
        FROM t_data_ecg 
        WHERE union_id = %s 
        AND deleted = 0
        ORDER BY start_time DESC
        """
        
        cursor.execute(query, (union_id,))
        results = cursor.fetchall()
        
        if not results:
            print(f"\n未找到用户 {union_id} 的任何ECG记录")
            return
            
        print(f"\n用户 {union_id} 共有 {len(results)} 条ECG记录：")
        print("\n记录详情：")
        print("-" * 100)
        for row in results:
            print(f"ECG ID: {row[0]}")
            print(f"开始时间: {row[1]}")
            print(f"结束时间: {row[2]}")
            print(f"数据来源: {row[3]}")
            print(f"采样率: {row[4]}")
            print(f"创建时间: {row[5]}")
            print("-" * 100)
            
    except Exception as e:
        print(f"查询数据时出错: {str(e)}")
        
    finally:
        cursor.close()
        connection.close()

def check_databases():
    """
    查看所有可用的数据库及其表数量
    Returns:
        list: 数据库名称列表
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 查看所有数据库
        query = "SHOW DATABASES"
        cursor.execute(query)
        databases = cursor.fetchall()
        db_list = [db[0] for db in databases]
        
        print("\n可用的数据库列表:")
        print("-" * 60)
        print(f"{'序号':^6}|{'数据库名':^30}|{'表数量':^10}|{'备注':^15}")
        print("-" * 60)
        
        for i, db_name in enumerate(db_list, 1):
            # 跳过系统数据库的详细信息显示
            if db_name in ['information_schema', 'mysql', 'performance_schema', 'sys']:
                print(f"{i:^6}|{db_name:^30}|{'-':^10}|{'系统数据库':^15}")
                continue
                
            try:
                # 获取表数量
                cursor.execute(f"USE {db_name}")
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                table_count = len(tables)
                print(f"{i:^6}|{db_name:^30}|{table_count:^10}|{'-':^15}")
            except Exception as e:
                print(f"{i:^6}|{db_name:^30}|{'错误':^10}|{str(e)[:15]:^15}")
        
        print("-" * 60)
        return db_list
            
    except Exception as e:
        print(f"查询数据库列表时出错: {str(e)}")
        return []
        
    finally:
        cursor.close()
        connection.close()

def check_user_in_all_dbs(union_id):
    """
    在所有相关数据库中查找用户数据
    Args:
        union_id: 用户union_id
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 先获取所有数据库
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall()]
        
        # 遍历每个数据库
        for db in databases:
            # 跳过系统数据库
            if db in ['information_schema', 'mysql', 'performance_schema', 'sys']:
                continue
                
            try:
                # 切换到当前数据库
                cursor.execute(f"USE {db}")
                
                # 检查是否有t_data_ecg表
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = %s 
                    AND table_name = 't_data_ecg'
                """, (db,))
                
                if cursor.fetchone()[0] == 0:
                    continue
                
                # 查询用户数据
                query = """
                SELECT id as ecg_id, 
                       start_time, 
                       end_time,
                       source,
                       sample_rate,
                       create_time
                FROM t_data_ecg 
                WHERE union_id = %s 
                AND deleted = 0
                ORDER BY start_time DESC
                """
                
                cursor.execute(query, (union_id,))
                results = cursor.fetchall()
                
                if results:
                    print(f"\n在数据库 {db} 中找到 {len(results)} 条记录：")
                    print("\n记录详情：")
                    print("-" * 100)
                    for row in results:
                        print(f"ECG ID: {row[0]}")
                        print(f"开始时间: {row[1]}")
                        print(f"结束时间: {row[2]}")
                        print(f"数据来源: {row[3]}")
                        print(f"采样率: {row[4]}")
                        print(f"创建时间: {row[5]}")
                        print("-" * 100)
                
            except Exception as e:
                print(f"检查数据库 {db} 时出错: {str(e)}")
                continue
            
    except Exception as e:
        print(f"查询数据时出错: {str(e)}")
        
    finally:
        cursor.close()
        connection.close()

def search_by_id(search_id):
    """
    根据ID在各个可能的字段中查找相关记录
    Args:
        search_id: 要搜索的ID
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_v2',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 在多个字段中查找
        queries = [
            """
            SELECT id, union_id, start_time, end_time, source, sample_rate, create_time
            FROM t_data_ecg 
            WHERE id = %s AND deleted = 0
            """,
            """
            SELECT id, union_id, start_time, end_time, source, sample_rate, create_time
            FROM t_data_ecg 
            WHERE union_id LIKE %s AND deleted = 0
            """,
            """
            SELECT id, union_id, start_time, end_time, source, sample_rate, create_time
            FROM t_data_ecg 
            WHERE create_by LIKE %s AND deleted = 0
            """
        ]
        
        print("\n开始搜索ID相关记录...")
        print("-" * 100)
        
        for query in queries:
            try:
                cursor.execute(query, (search_id,))
                results = cursor.fetchall()
                
                if results:
                    print(f"\n找到 {len(results)} 条记录：")
                    for row in results:
                        print("\n记录详情：")
                        print(f"ID: {row[0]}")
                        print(f"Union ID: {row[1]}")
                        print(f"开始时间: {row[2]}")
                        print(f"结束时间: {row[3]}")
                        print(f"数据来源: {row[4]}")
                        print(f"采样率: {row[5]}")
                        print(f"创建时间: {row[6]}")
                        print("-" * 50)
            
            except Exception as e:
                print(f"执行查询时出错: {str(e)}")
                continue
        
        # 尝试在其他相关表中查找
        other_tables_query = """
        SELECT TABLE_NAME 
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = 'backend_v2'
        """
        cursor.execute(other_tables_query)
        tables = cursor.fetchall()
        
        print("\n在其他表中搜索相关记录...")
        for table in tables:
            table_name = table[0]
            try:
                # 获取表的列信息
                cursor.execute(f"SHOW COLUMNS FROM {table_name}")
                columns = cursor.fetchall()
                
                # 在包含id的列中搜索
                for col in columns:
                    if 'id' in col[0].lower():
                        query = f"""
                        SELECT * FROM {table_name}
                        WHERE {col[0]} = %s
                        LIMIT 5
                        """
                        try:
                            cursor.execute(query, (search_id,))
                            results = cursor.fetchall()
                            if results:
                                print(f"\n在表 {table_name} 的字段 {col[0]} 中找到 {len(results)} 条记录")
                                print(f"列名: {[col[0] for col in cursor.description]}")
                                for row in results:
                                    print(row)
                                print("-" * 50)
                        except:
                            continue
                            
            except Exception as e:
                continue
                
    except Exception as e:
        print(f"查询数据时出错: {str(e)}")
        
    finally:
        cursor.close()
        connection.close()

def export_table_to_excel(table_date):
    """
    导出指定日期的ECG数据表到Excel
    Args:
        table_date: 指定日期
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_v2',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        table_name = f"t_data_ecg_{table_date.strftime('%Y%m%d')}"
        
        # 查询表中所有数据
        query = f"""
        SELECT *
        FROM {table_name}
        WHERE deleted = 0
        """
        
        print(f"开始从表 {table_name} 导出数据...")
        cursor.execute(query)
        
        # 获取列名和数据
        columns = [desc[0] for desc in cursor.description]
        results = cursor.fetchall()
        
        # 转换为DataFrame
        df = pd.DataFrame(results, columns=columns)
        
        if not df.empty:
            # 生成Excel文件名
            excel_file = f"ECG数据_{table_date.strftime('%Y%m%d')}.xlsx"
            
            # 导出到Excel
            df.to_excel(excel_file, index=False)
            print(f"\n成功导出 {len(df)} 条记录到文件: {excel_file}")
            print(f"数据概览:")
            print(f"总行数: {len(df)}")
            print(f"总列数: {len(df.columns)}")
            print("\n前5行数据预览:")
            print(df.head())
        else:
            print(f"表 {table_name} 中没有数据")
            
    except Exception as e:
        print(f"导出数据时出错: {str(e)}")
        
    finally:
        cursor.close()
        connection.close()

def search_by_phone(phone_numbers):
    """
    根据手机号查询对应的union_id
    Args:
        phone_numbers: 手机号列表
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_v2',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 查询所有表
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = 'backend_v2'
        """)
        tables = cursor.fetchall()
        
        print("\n开始搜索手机号相关记录...")
        print("-" * 100)
        
        for table in tables:
            table_name = table[0]
            try:
                # 获取表的列信息
                cursor.execute(f"SHOW COLUMNS FROM {table_name}")
                columns = cursor.fetchall()
                
                # 查找可能包含手机号的字段
                phone_columns = [col[0] for col in columns if any(x in col[0].lower() for x in ['phone', 'mobile', 'tel'])]
                
                for col in phone_columns:
                    # 构建查询条件
                    phone_conditions = " OR ".join([f"{col} = %s" for _ in phone_numbers])
                    query = f"""
                    SELECT {col}, union_id, id
                    FROM {table_name}
                    WHERE {phone_conditions}
                    """
                    
                    try:
                        cursor.execute(query, phone_numbers)
                        results = cursor.fetchall()
                        
                        if results:
                            print(f"\n在表 {table_name} 中找到记录：")
                            print(f"字段名: {[desc[0] for desc in cursor.description]}")
                            for row in results:
                                print(row)
                            print("-" * 50)
                    except:
                        continue
                    
            except Exception as e:
                continue
                
    except Exception as e:
        print(f"查询数据时出错: {str(e)}")
        
    finally:
        cursor.close()
        connection.close()

def check_ecg_age_prediction_data(phone_numbers):
    """
    检查ECG年龄预测相关的数据
    Args:
        phone_numbers: 手机号列表
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_v2',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("\n开始检查ECG年龄预测相关数据...")
        print("-" * 100)
        
        # 1. 首先查找用户基本信息（union_id, 性别，年龄等）
        query = """
        SELECT t.id, t.union_id, t.mobile, t.gender, t.birthday, t.create_time
        FROM t_user t
        WHERE t.mobile IN (%s, %s, %s)
        """
        
        cursor.execute(query, phone_numbers)
        user_results = cursor.fetchall()
        
        if user_results:
            print("\n用户基本信息：")
            for user in user_results:
                print(f"ID: {user[0]}")
                print(f"Union ID: {user[1]}")
                print(f"手机号: {user[2]}")
                print(f"性别: {user[3]}")
                print(f"出生日期: {user[4]}")
                print(f"创建时间: {user[5]}")
                print("-" * 50)
                
                # 2. 查找该用户的ECG记录
                if user[1]:  # 如果有union_id
                    ecg_query = """
                    SELECT id, start_time, end_time, source, sample_rate
                    FROM t_data_ecg
                    WHERE union_id = %s
                    AND deleted = 0
                    ORDER BY create_time DESC
                    LIMIT 5
                    """
                    
                    cursor.execute(ecg_query, (user[1],))
                    ecg_results = cursor.fetchall()
                    
                    if ecg_results:
                        print(f"\n最近5条ECG记录：")
                        for ecg in ecg_results:
                            print(f"ECG ID: {ecg[0]}")
                            print(f"开始时间: {ecg[1]}")
                            print(f"结束时间: {ecg[2]}")
                            print(f"数据来源: {ecg[3]}")
                            print(f"采样率: {ecg[4]}")
                            print("-" * 30)
                    else:
                        print(f"未找到该用户的ECG记录")
        else:
            print("未找到用户信息")
            
            # 3. 尝试在t_data_ecg表中直接查找
            print("\n尝试在t_data_ecg表中直接查找...")
            for phone in phone_numbers:
                query = """
                SELECT DISTINCT union_id, create_time, source
                FROM t_data_ecg
                WHERE union_id IN (
                    SELECT union_id 
                    FROM t_user 
                    WHERE mobile = %s
                )
                AND deleted = 0
                ORDER BY create_time DESC
                LIMIT 5
                """
                cursor.execute(query, (phone,))
                results = cursor.fetchall()
                
                if results:
                    print(f"\n手机号 {phone} 的ECG记录：")
                    for row in results:
                        print(f"Union ID: {row[0]}")
                        print(f"创建时间: {row[1]}")
                        print(f"数据来源: {row[2]}")
                        print("-" * 30)
                else:
                    print(f"\n未找到手机号 {phone} 的任何记录")
            
    except Exception as e:
        print(f"查询数据时出错: {str(e)}")
        
    finally:
        cursor.close()
        connection.close()

# 使用示例
if __name__ == '__main__':
    # 首先查询所有可用的数据库
    print("\n=== 第一步：查询所有可用的数据库 ===")
    databases = check_databases()
    
    # 询问用户是否继续查询ECG数据
    continue_query = input("\n是否继续查询ECG数据？(y/n): ")
    if continue_query.lower() != 'y':
        print("程序结束")
        exit(0)
    
    # 设置查询参数
    print("\n=== 第二步：查询ECG数据 ===")
    union_id = input("请输入union_id (默认为'CUSTOMER18838092628219043846502'): ") or 'CUSTOMER18838092628219043846502'
    date_str = input("请输入查询日期 (格式: YYYY-MM-DD, 默认为2024-02-18): ") or '2024-02-18'
    try:
        query_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        print("日期格式错误，使用默认日期2024-02-18")
        query_date = datetime(2024, 2, 18).date()
    
    # 查询数据
    print(f"\n开始查询用户 {union_id} 在 {query_date} 的数据...")
    df = query_ecg_data(query_date)
    
    if not df.empty:
        # 导出数据到CSV文件
        output_file = f'ecg_data_{query_date.strftime("%Y%m%d")}.csv'
        df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"\n数据已成功导出到文件: {output_file}")
    else:
        print("\n没有找到符合条件的数据")
