# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Inception-ResNet V2 model for Keras.

Reference:
  - [Inception-v4, Inception-ResNet and the Impact of
     Residual Connections on Learning](https://arxiv.org/abs/1602.07261)
    (AAAI 2017)

"""

from __future__ import print_function as _print_function

import sys as _sys

from tensorflow.python.keras.applications.inception_resnet_v2 import InceptionResNetV2
from tensorflow.python.keras.applications.inception_resnet_v2 import decode_predictions
from tensorflow.python.keras.applications.inception_resnet_v2 import preprocess_input

del _print_function

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.applications.inception_resnet_v2", public_apis=None, deprecation=True,
      has_lite=False)
