{% extends "admin/base_site.html" %}
{% load i18n %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; {% translate 'Documentation' %}
</div>
{% endblock %}
{% block title %}{% translate 'Documentation' %}{% endblock %}

{% block content %}

<h1>{% translate 'Documentation' %}</h1>

<div id="content-main">
  <h3><a href="tags/">{% translate 'Tags' %}</a></h3>
  <p>{% translate 'List of all the template tags and their functions.' %}</p>

  <h3><a href="filters/">{% translate 'Filters' %}</a></h3>
  <p>{% translate 'Filters are actions which can be applied to variables in a template to alter the output.' %}</p>

  <h3><a href="models/">{% translate 'Models' %}</a></h3>
  <p>{% translate 'Models are descriptions of all the objects in the system and their associated fields. Each model has a list of fields which can be accessed as template variables' %}.</p>

  <h3><a href="views/">{% translate 'Views' %}</a></h3>
  <p>{% translate 'Each page on the public site is generated by a view. The view defines which template is used to generate the page and which objects are available to that template.' %}</p>

  <h3><a href="bookmarklets/">{% translate 'Bookmarklets' %}</a></h3>
  <p>{% translate 'Tools for your browser to quickly access admin functionality.' %}</p>
</div>

{% endblock %}

