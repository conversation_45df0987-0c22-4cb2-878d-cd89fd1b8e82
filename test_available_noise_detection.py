#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试available.py中的噪音检测逻辑
"""

import json
import numpy as np
from scipy import signal
from scipy.fft import fft, fftfreq
import sys
import os

# 添加路径以便导入模块
sys.path.append('ecganalysis-sync/ECG-Analysis')

def load_ecg_data(file_path):
    """加载ECG数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 解析ECG数据
    ecg_str = data['ecg']
    ecg_data = np.array([float(x) for x in ecg_str.strip('[]').split(',')])
    
    return {
        'ecg_data': ecg_data,
        'sample_rate': data['sampleRate'],
        'zero': data['zero'],
        'gain': data['gain'],
        'start_time': data['startTime'],
        'end_time': data['endTime']
    }

def wavelet_filter(signal_data, sample_rate):
    """简化的小波滤波函数"""
    # 使用简单的带通滤波器代替小波滤波
    nyquist = sample_rate / 2
    low = 0.5 / nyquist
    high = 40 / nyquist
    
    b, a = signal.butter(4, [low, high], btype='band')
    filtered_signal = signal.filtfilt(b, a, signal_data)
    
    return filtered_signal

def zero_cross_rate(sig):
    """过零率计算"""
    if len(sig) < 2:
        return 0.0

    std = np.std(sig)

    if std > 0:
        threshold_multiplier = np.percentile(np.abs(sig), 75) / (std + 1e-6)
    else:
        threshold_multiplier = 1.0

    threshold = threshold_multiplier * np.std(sig)
    sig_diff = np.diff(sig)
    crosses = np.where(np.abs(sig_diff) > threshold)[0]

    return len(crosses) / len(sig_diff)

def high_freq_energy(sig, fs, cutoff=25):
    """高频能量检测"""
    if len(sig) == 0:
        return 0.0

    sig_centered = sig - np.mean(sig)
    fft_vals = np.abs(fft(sig_centered))
    freqs = np.abs(fftfreq(len(sig), 1 / fs))

    max_freq = min(fs // 2, 100)
    hf_mask = (freqs > cutoff) & (freqs < max_freq)

    total_energy = np.sum(fft_vals)
    hf_energy = np.sum(fft_vals[hf_mask]) / (total_energy + 1e-6)

    return hf_energy

def baseline_variation(sig, fs, cutoff=0.5):
    """基线漂移检测"""
    if len(sig) == 0:
        return 0.0

    b, a = signal.butter(3, cutoff, btype='highpass', fs=fs)
    baseline = signal.filtfilt(b, a, sig)

    return np.std(baseline)

def slope_detection(ecg_data, sample_rate, slope_threshold=0.035, window_size=0.2):
    """斜率检测"""
    slopes = []
    sliding_window = int(window_size * sample_rate)
    
    for i in range(0, len(ecg_data) - sliding_window + 1, sliding_window // 2):
        window = ecg_data[i:i + sliding_window]
        x = np.arange(sliding_window)
        slope, _ = np.polyfit(x, window, 1)
        slopes.append(slope)

    slopes = np.array(slopes)
    large_slopes = np.abs(slopes) > slope_threshold

    return np.any(large_slopes)

def is_noisy_window(features):
    """噪声判断"""
    rules = [
        features['amp_range'] < 0.3,  # 信号丢失
        features['zcr'] > 0.4,  # 过零率
        features['hf_energy'] > 0.35,  # 肌电干扰阈值
        features['baseline_drift'] > 0.4,  # 基线漂移阈值
        features['slopes']  # 斜率检测
    ]
    
    return any(rules)

def identify_noise_type(features):
    """识别是否为平直线+高频振荡噪声"""
    if features['amp_range'] < 0.3 and features['zcr'] > 0.4:
        if features['amp_range'] < 0.001:
            return True
    return False

def analyze_signal_with_available_logic(ecg_data, sample_rate, window_sec=10):
    """使用available.py的逻辑分析信号"""
    
    # 滤波处理
    filter_raw_signal = wavelet_filter(ecg_data, sample_rate)
    
    print(f"原始信号范围: {np.min(ecg_data):.6f} ~ {np.max(ecg_data):.6f}")
    print(f"滤波后信号范围: {np.min(filter_raw_signal):.6f} ~ {np.max(filter_raw_signal):.6f}")
    print()
    
    # 计算窗口大小
    window_size = int(window_sec * sample_rate)
    num_samples = len(filter_raw_signal)
    
    print(f"窗口大小: {window_size} 样本点 ({window_sec} 秒)")
    print(f"信号总长度: {num_samples} 样本点")
    print()
    
    # 分析第一个窗口（前10秒）
    segment = filter_raw_signal[:window_size]
    
    # 特征提取
    features = {
        'amp_range': np.ptp(segment),  # 幅度范围
        'zcr': zero_cross_rate(segment),  # 过零率
        'hf_energy': high_freq_energy(segment, sample_rate),
        'baseline_drift': baseline_variation(segment, sample_rate),
        'slopes': slope_detection(segment, sample_rate)
    }
    
    print("=== 特征分析结果 ===")
    for key, value in features.items():
        print(f"{key}: {value}")
    print()
    
    # 噪声判断
    is_noisy = is_noisy_window(features)
    noise_type_detected = identify_noise_type(features)
    
    print("=== 噪声判断结果 ===")
    print(f"是否为噪声窗口: {is_noisy}")
    print(f"是否为平直线+高频振荡噪声: {noise_type_detected}")
    print()
    
    # 详细分析每个规则
    print("=== 详细规则分析 ===")
    rules = [
        (features['amp_range'] < 0.3, f"幅度范围 < 0.3: {features['amp_range']:.6f} < 0.3"),
        (features['zcr'] > 0.4, f"过零率 > 0.4: {features['zcr']:.6f} > 0.4"),
        (features['hf_energy'] > 0.35, f"高频能量 > 0.35: {features['hf_energy']:.6f} > 0.35"),
        (features['baseline_drift'] > 0.4, f"基线漂移 > 0.4: {features['baseline_drift']:.6f} > 0.4"),
        (features['slopes'], f"斜率检测: {features['slopes']}")
    ]
    
    for i, (rule_result, description) in enumerate(rules, 1):
        status = "✓ 触发" if rule_result else "✗ 未触发"
        print(f"规则{i}: {status} - {description}")
    
    print()
    
    # 分析为什么没有被识别为噪音
    if not is_noisy:
        print("=== 为什么没有被识别为噪音 ===")
        print("所有噪声检测规则都未被触发:")
        print(f"1. 幅度范围正常: {features['amp_range']:.6f} >= 0.3")
        print(f"2. 过零率正常: {features['zcr']:.6f} <= 0.4")
        print(f"3. 高频能量正常: {features['hf_energy']:.6f} <= 0.35")
        print(f"4. 基线漂移正常: {features['baseline_drift']:.6f} <= 0.4")
        print(f"5. 斜率检测正常: {features['slopes']} = False")
        print()
        print("建议改进措施:")
        print("1. 降低幅度范围阈值（当前0.3可能过低）")
        print("2. 调整过零率阈值（当前0.4可能过高）")
        print("3. 考虑添加更多噪声检测特征")
        print("4. 使用机器学习方法进行噪声分类")
    else:
        print("=== 噪声检测成功 ===")
        if noise_type_detected:
            print("检测到平直线+高频振荡噪声")
        else:
            print("检测到其他类型噪声")
    
    return features, is_noisy, noise_type_detected

def main():
    # 加载数据
    file_path = r'qiniu_query_results\低电压异常数据\肖总0726异常数据.json'
    data = load_ecg_data(file_path)
    
    print("=== ECG数据信息 ===")
    print(f"采样率: {data['sample_rate']} Hz")
    print(f"数据长度: {len(data['ecg_data'])} 样本点")
    print(f"时长: {len(data['ecg_data']) / data['sample_rate']:.2f} 秒")
    print(f"信号范围: {np.min(data['ecg_data']):.6f} ~ {np.max(data['ecg_data']):.6f}")
    print(f"信号标准差: {np.std(data['ecg_data']):.6f}")
    print()
    
    # 使用available.py的逻辑分析
    features, is_noisy, noise_type_detected = analyze_signal_with_available_logic(
        data['ecg_data'], data['sample_rate']
    )

if __name__ == "__main__":
    main()
