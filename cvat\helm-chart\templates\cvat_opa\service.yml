apiVersion: v1
kind: Service
metadata:
  {{- if .Values.cvat.opa.composeCompatibleServiceName }}
  name: opa
  {{- else }}
  name: {{ .Release.Name }}-opa-service
  {{- end }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cvat.labels" . | nindent 4 }}
    app: cvat-app
    tier: opa
spec:
  selector:
    app: cvat-app
    tier: opa
    {{- include "cvat.labels" . | nindent 4 }}
  {{- with .Values.cvat.opa.service }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
