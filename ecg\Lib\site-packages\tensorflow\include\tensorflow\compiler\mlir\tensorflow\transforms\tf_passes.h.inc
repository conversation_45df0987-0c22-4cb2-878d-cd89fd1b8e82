/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// ClusterConstantSinkingPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ClusterConstantSinkingPassBase : public ::mlir::FunctionPass {
public:
  using Base = ClusterConstantSinkingPassBase;

  ClusterConstantSinkingPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ClusterConstantSinkingPassBase(const ClusterConstantSinkingPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-constant-sinking");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-constant-sinking"; }

  ::llvm::StringRef getDescription() const override { return "Sinks constants implicitly captured in a tf_device.cluster region."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ClusterConstantSinkingPass");
  }
  ::llvm::StringRef getName() const override { return "ClusterConstantSinkingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// ClusterOpsByPolicyPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ClusterOpsByPolicyPassBase : public ::mlir::FunctionPass {
public:
  using Base = ClusterOpsByPolicyPassBase;

  ClusterOpsByPolicyPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ClusterOpsByPolicyPassBase(const ClusterOpsByPolicyPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("cluster-ops-by-policy");
  }
  ::llvm::StringRef getArgument() const override { return "cluster-ops-by-policy"; }

  ::llvm::StringRef getDescription() const override { return "Clusters ops according to specified policy."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ClusterOpsByPolicyPass");
  }
  ::llvm::StringRef getName() const override { return "ClusterOpsByPolicyPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<mlir::tf_device::TensorFlowDeviceDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> policy_name{*this, "policy-name", ::llvm::cl::desc("Adds a policy string attribute to all extracted clusters. This attribute allows to distinguish clusters formed by different policies or maybe other clustering algorithms.")};
  ::mlir::Pass::Option<int> min_cluster_size{*this, "min-cluster-size", ::llvm::cl::desc("Do not form clusters smaller of the given size."), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<std::string> algorithm{*this, "algorithm", ::llvm::cl::desc("Clustering algorithm type: `use-def` or `union-find`"), ::llvm::cl::init("use-def")};
  ::mlir::Pass::ListOption<std::string> oplist{*this, "oplist", ::llvm::cl::desc("Cluster listed ops when they form a single use def-use chain, such that each op's single user is the next op in the list."), llvm::cl::MiscFlags::CommaSeparated};
};

//===----------------------------------------------------------------------===//
// ClusterOutliningPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ClusterOutliningPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ClusterOutliningPassBase;

  ClusterOutliningPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ClusterOutliningPassBase(const ClusterOutliningPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-cluster-outlining");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-cluster-outlining"; }

  ::llvm::StringRef getDescription() const override { return "Outlines regions of tf_device.cluster operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ClusterOutliningPass");
  }
  ::llvm::StringRef getName() const override { return "ClusterOutliningPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertLaunchFuncToTFCallPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertLaunchFuncToTFCallPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLaunchFuncToTFCallPassBase;

  ConvertLaunchFuncToTFCallPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLaunchFuncToTFCallPassBase(const ConvertLaunchFuncToTFCallPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-convert-launch-func-to-tf-call");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-convert-launch-func-to-tf-call"; }

  ::llvm::StringRef getDescription() const override { return "Rewrites tf_device::LaunchFuncOp to TF::PartitionedCallOp"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLaunchFuncToTFCallPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertLaunchFuncToTFCallPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// DropWhileShapeInvariantInDeviceClusterPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class DropWhileShapeInvariantInDeviceClusterPassBase : public ::mlir::FunctionPass {
public:
  using Base = DropWhileShapeInvariantInDeviceClusterPassBase;

  DropWhileShapeInvariantInDeviceClusterPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  DropWhileShapeInvariantInDeviceClusterPassBase(const DropWhileShapeInvariantInDeviceClusterPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-drop-while-shape-invariant-in-device-cluster");
  }
  ::llvm::StringRef getArgument() const override { return "tf-drop-while-shape-invariant-in-device-cluster"; }

  ::llvm::StringRef getDescription() const override { return "Drop `shape_invariant` attrbute from While/WhileRegion ops inside device cluster."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DropWhileShapeInvariantInDeviceClusterPass");
  }
  ::llvm::StringRef getName() const override { return "DropWhileShapeInvariantInDeviceClusterPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// DropWhileShapeInvariantPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class DropWhileShapeInvariantPassBase : public ::mlir::FunctionPass {
public:
  using Base = DropWhileShapeInvariantPassBase;

  DropWhileShapeInvariantPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  DropWhileShapeInvariantPassBase(const DropWhileShapeInvariantPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-drop-while-shape-invariant");
  }
  ::llvm::StringRef getArgument() const override { return "tf-drop-while-shape-invariant"; }

  ::llvm::StringRef getDescription() const override { return "Drop `shape_invariant` attrbute from While/WhileRegion ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DropWhileShapeInvariantPass");
  }
  ::llvm::StringRef getName() const override { return "DropWhileShapeInvariantPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// ExecutorDialectToFunctionalPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ExecutorDialectToFunctionalPassBase : public ::mlir::FunctionPass {
public:
  using Base = ExecutorDialectToFunctionalPassBase;

  ExecutorDialectToFunctionalPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ExecutorDialectToFunctionalPassBase(const ExecutorDialectToFunctionalPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-executor-to-functional-conversion");
  }
  ::llvm::StringRef getArgument() const override { return "tf-executor-to-functional-conversion"; }

  ::llvm::StringRef getDescription() const override { return "Lifts tf_executor.island inner ops from a tf_executor.graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExecutorDialectToFunctionalPass");
  }
  ::llvm::StringRef getName() const override { return "ExecutorDialectToFunctionalPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// ExecutorGraphPruningPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ExecutorGraphPruningPassBase : public ::mlir::FunctionPass {
public:
  using Base = ExecutorGraphPruningPassBase;

  ExecutorGraphPruningPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ExecutorGraphPruningPassBase(const ExecutorGraphPruningPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-executor-graph-pruning");
  }
  ::llvm::StringRef getArgument() const override { return "tf-executor-graph-pruning"; }

  ::llvm::StringRef getDescription() const override { return "Prunes unreachable ops in a tf_executor.graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExecutorGraphPruningPass");
  }
  ::llvm::StringRef getName() const override { return "ExecutorGraphPruningPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::ListOption<std::string> ops_to_preserve_{*this, "ops-to-preserve", ::llvm::cl::desc("Comma separated list of ops that should not be pruned regardless of reachability"), llvm::cl::MiscFlags::CommaSeparated};
};

//===----------------------------------------------------------------------===//
// FunctionalControlFlowToRegionsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class FunctionalControlFlowToRegionsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FunctionalControlFlowToRegionsPassBase;

  FunctionalControlFlowToRegionsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FunctionalControlFlowToRegionsPassBase(const FunctionalControlFlowToRegionsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-functional-control-flow-to-regions");
  }
  ::llvm::StringRef getArgument() const override { return "tf-functional-control-flow-to-regions"; }

  ::llvm::StringRef getDescription() const override { return "Transforms functional control flow operations to their region-based counterparts"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FunctionalControlFlowToRegionsPass");
  }
  ::llvm::StringRef getName() const override { return "FunctionalControlFlowToRegionsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// HoistReplicateInvariantResourceWritesPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class HoistReplicateInvariantResourceWritesPassBase : public ::mlir::FunctionPass {
public:
  using Base = HoistReplicateInvariantResourceWritesPassBase;

  HoistReplicateInvariantResourceWritesPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  HoistReplicateInvariantResourceWritesPassBase(const HoistReplicateInvariantResourceWritesPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-hoist-replicate-invariant-resource-writes");
  }
  ::llvm::StringRef getArgument() const override { return "tf-hoist-replicate-invariant-resource-writes"; }

  ::llvm::StringRef getDescription() const override { return "Hoists writes to replicate invariant resource variables."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HoistReplicateInvariantResourceWritesPass");
  }
  ::llvm::StringRef getName() const override { return "HoistReplicateInvariantResourceWritesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LaunchOutliningPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LaunchOutliningPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LaunchOutliningPassBase;

  LaunchOutliningPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LaunchOutliningPassBase(const LaunchOutliningPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-launch-outlining");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-launch-outlining"; }

  ::llvm::StringRef getDescription() const override { return "Outlines regions of tf_device.launch operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LaunchOutliningPass");
  }
  ::llvm::StringRef getName() const override { return "LaunchOutliningPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LowerQuantizedPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LowerQuantizedPassBase : public ::mlir::FunctionPass {
public:
  using Base = LowerQuantizedPassBase;

  LowerQuantizedPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LowerQuantizedPassBase(const LowerQuantizedPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-lower-quantized");
  }
  ::llvm::StringRef getArgument() const override { return "tf-lower-quantized"; }

  ::llvm::StringRef getDescription() const override { return "Lowers ops that require quantized input or output."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerQuantizedPass");
  }
  ::llvm::StringRef getName() const override { return "LowerQuantizedPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// MarkInputOutputAliasesPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class MarkInputOutputAliasesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = MarkInputOutputAliasesPassBase;

  MarkInputOutputAliasesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  MarkInputOutputAliasesPassBase(const MarkInputOutputAliasesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-mark-input-output-aliases");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-mark-input-output-aliases"; }

  ::llvm::StringRef getDescription() const override { return "Marks device cluster inputs-output pairs that read/write to the same variable as aliases"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MarkInputOutputAliasesPass");
  }
  ::llvm::StringRef getName() const override { return "MarkInputOutputAliasesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// MarkOpsForOutsideCompilationPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class MarkOpsForOutsideCompilationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = MarkOpsForOutsideCompilationPassBase;

  MarkOpsForOutsideCompilationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  MarkOpsForOutsideCompilationPassBase(const MarkOpsForOutsideCompilationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-mark-ops-for-outside-compilation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-mark-ops-for-outside-compilation"; }

  ::llvm::StringRef getDescription() const override { return "Marks ops in device cluster for outside compilation if they are unsupported on device."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MarkOpsForOutsideCompilationPass");
  }
  ::llvm::StringRef getName() const override { return "MarkOpsForOutsideCompilationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// OutsideCompiledToHostLaunchPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class OutsideCompiledToHostLaunchPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = OutsideCompiledToHostLaunchPassBase;

  OutsideCompiledToHostLaunchPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  OutsideCompiledToHostLaunchPassBase(const OutsideCompiledToHostLaunchPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-outside-compiled-to-host-launch");
  }
  ::llvm::StringRef getArgument() const override { return "tf-outside-compiled-to-host-launch"; }

  ::llvm::StringRef getDescription() const override { return "Wraps each op with the _xla_outside_compiled attribute in a separate tf_device.launch on replicated host device."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OutsideCompiledToHostLaunchPass");
  }
  ::llvm::StringRef getName() const override { return "OutsideCompiledToHostLaunchPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// PrepareTpuComputationForTfExportPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class PrepareTpuComputationForTfExportPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = PrepareTpuComputationForTfExportPassBase;

  PrepareTpuComputationForTfExportPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  PrepareTpuComputationForTfExportPassBase(const PrepareTpuComputationForTfExportPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("prepare-tpu-computation-for-tf-export");
  }
  ::llvm::StringRef getArgument() const override { return "prepare-tpu-computation-for-tf-export"; }

  ::llvm::StringRef getDescription() const override { return "Prepare TPU computation to be legal for export to TensorFlow"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrepareTpuComputationForTfExportPass");
  }
  ::llvm::StringRef getName() const override { return "PrepareTpuComputationForTfExportPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// PromoteResourcesToArgsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class PromoteResourcesToArgsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = PromoteResourcesToArgsPassBase;

  PromoteResourcesToArgsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  PromoteResourcesToArgsPassBase(const PromoteResourcesToArgsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-promote-resources-to-args");
  }
  ::llvm::StringRef getArgument() const override { return "tf-promote-resources-to-args"; }

  ::llvm::StringRef getDescription() const override { return "Promote resources reads/writes to function inputs/outputs."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PromoteResourcesToArgsPass");
  }
  ::llvm::StringRef getName() const override { return "PromoteResourcesToArgsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// PromoteVarHandlesToArgsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class PromoteVarHandlesToArgsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = PromoteVarHandlesToArgsPassBase;

  PromoteVarHandlesToArgsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  PromoteVarHandlesToArgsPassBase(const PromoteVarHandlesToArgsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-promote-var-handles-to-args");
  }
  ::llvm::StringRef getArgument() const override { return "tf-promote-var-handles-to-args"; }

  ::llvm::StringRef getDescription() const override { return "Promote tf.VarHandleOps to function arguments."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PromoteVarHandlesToArgsPass");
  }
  ::llvm::StringRef getName() const override { return "PromoteVarHandlesToArgsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// RegionControlFlowToFunctionalPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class RegionControlFlowToFunctionalPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = RegionControlFlowToFunctionalPassBase;

  RegionControlFlowToFunctionalPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  RegionControlFlowToFunctionalPassBase(const RegionControlFlowToFunctionalPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-region-control-flow-to-functional");
  }
  ::llvm::StringRef getArgument() const override { return "tf-region-control-flow-to-functional"; }

  ::llvm::StringRef getDescription() const override { return "Transforms region-based control flow operations to their functional counterparts"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RegionControlFlowToFunctionalPass");
  }
  ::llvm::StringRef getName() const override { return "RegionControlFlowToFunctionalPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// ReplicateInvariantOpHoistingPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ReplicateInvariantOpHoistingPassBase : public ::mlir::FunctionPass {
public:
  using Base = ReplicateInvariantOpHoistingPassBase;

  ReplicateInvariantOpHoistingPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ReplicateInvariantOpHoistingPassBase(const ReplicateInvariantOpHoistingPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-replicate-invariant-op-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "tf-replicate-invariant-op-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Hoists replicate invariant operations out of replicate"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ReplicateInvariantOpHoistingPass");
  }
  ::llvm::StringRef getName() const override { return "ReplicateInvariantOpHoistingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPUCleanupClusterAttributesPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPUCleanupClusterAttributesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUCleanupClusterAttributesPassBase;

  TPUCleanupClusterAttributesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUCleanupClusterAttributesPassBase(const TPUCleanupClusterAttributesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-cleanup-cluster-attributes");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-cleanup-cluster-attributes"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate _tpu_replicate and other attributes from ops in a cluster"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUCleanupClusterAttributesPass");
  }
  ::llvm::StringRef getName() const override { return "TPUCleanupClusterAttributesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPUClusterFormationPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPUClusterFormationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUClusterFormationPassBase;

  TPUClusterFormationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUClusterFormationPassBase(const TPUClusterFormationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-cluster-formation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-cluster-formation"; }

  ::llvm::StringRef getDescription() const override { return "Forms clusters from operations assigned to the same TPU computation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUClusterFormationPass");
  }
  ::llvm::StringRef getName() const override { return "TPUClusterFormationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPUExtractHeadTailOutsideCompilationPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPUExtractHeadTailOutsideCompilationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUExtractHeadTailOutsideCompilationPassBase;

  TPUExtractHeadTailOutsideCompilationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUExtractHeadTailOutsideCompilationPassBase(const TPUExtractHeadTailOutsideCompilationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-extract-head-tail-outside-compilation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-extract-head-tail-outside-compilation"; }

  ::llvm::StringRef getDescription() const override { return "Extracts TPU head or tail outside compilation to separate host launches before/after device cluster."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUExtractHeadTailOutsideCompilationPass");
  }
  ::llvm::StringRef getName() const override { return "TPUExtractHeadTailOutsideCompilationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPUExtractOutsideCompilationPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPUExtractOutsideCompilationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUExtractOutsideCompilationPassBase;

  TPUExtractOutsideCompilationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUExtractOutsideCompilationPassBase(const TPUExtractOutsideCompilationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-extract-outside-compilation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-extract-outside-compilation"; }

  ::llvm::StringRef getDescription() const override { return "Extracts TPU outside compilation computation to a separate tf_device.parallel_execute region."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUExtractOutsideCompilationPass");
  }
  ::llvm::StringRef getName() const override { return "TPUExtractOutsideCompilationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPUHostComputationExpansionPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPUHostComputationExpansionPassBase : public ::mlir::FunctionPass {
public:
  using Base = TPUHostComputationExpansionPassBase;

  TPUHostComputationExpansionPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TPUHostComputationExpansionPassBase(const TPUHostComputationExpansionPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-host-computation-expansion");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-host-computation-expansion"; }

  ::llvm::StringRef getDescription() const override { return "Expands host computation before and after TPU computation."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUHostComputationExpansionPass");
  }
  ::llvm::StringRef getName() const override { return "TPUHostComputationExpansionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPUMergeVariablesWithExecutePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPUMergeVariablesWithExecutePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUMergeVariablesWithExecutePassBase;

  TPUMergeVariablesWithExecutePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUMergeVariablesWithExecutePassBase(const TPUMergeVariablesWithExecutePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-merge-variables-with-execute");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-merge-variables-with-execute"; }

  ::llvm::StringRef getDescription() const override { return "Merges device variable reads and updates into TPU execute ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUMergeVariablesWithExecutePass");
  }
  ::llvm::StringRef getName() const override { return "TPUMergeVariablesWithExecutePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPUReorderReplicateAndPartitionedInputsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPUReorderReplicateAndPartitionedInputsPassBase : public ::mlir::FunctionPass {
public:
  using Base = TPUReorderReplicateAndPartitionedInputsPassBase;

  TPUReorderReplicateAndPartitionedInputsPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TPUReorderReplicateAndPartitionedInputsPassBase(const TPUReorderReplicateAndPartitionedInputsPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-reorder-replicate-partitioned-inputs");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-reorder-replicate-partitioned-inputs"; }

  ::llvm::StringRef getDescription() const override { return "Reorder replicated and partitioned input ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUReorderReplicateAndPartitionedInputsPass");
  }
  ::llvm::StringRef getName() const override { return "TPUReorderReplicateAndPartitionedInputsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPUResourceReadForWritePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPUResourceReadForWritePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUResourceReadForWritePassBase;

  TPUResourceReadForWritePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUResourceReadForWritePassBase(const TPUResourceReadForWritePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-resource-read-for-write");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-resource-read-for-write"; }

  ::llvm::StringRef getDescription() const override { return "Inserts tf.ReadVariableOp inputs to a TPU cluster for resource writes with no reads"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUResourceReadForWritePass");
  }
  ::llvm::StringRef getName() const override { return "TPUResourceReadForWritePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPUResourceReadsWritesPartitioningPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPUResourceReadsWritesPartitioningPassBase : public ::mlir::FunctionPass {
public:
  using Base = TPUResourceReadsWritesPartitioningPassBase;

  TPUResourceReadsWritesPartitioningPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TPUResourceReadsWritesPartitioningPassBase(const TPUResourceReadsWritesPartitioningPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-resource-partition");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-resource-partition"; }

  ::llvm::StringRef getDescription() const override { return "Partitions unpartitioned resource read/write to partitioned resource variables."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUResourceReadsWritesPartitioningPass");
  }
  ::llvm::StringRef getName() const override { return "TPUResourceReadsWritesPartitioningPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPURewritePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPURewritePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPURewritePassBase;

  TPURewritePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPURewritePassBase(const TPURewritePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Rewrites a `tf_device.cluster_func` on TPUs into TPU runtime operations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPURewritePass");
  }
  ::llvm::StringRef getName() const override { return "TPURewritePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TPUUpdateEmbeddingEnqueueOpInputsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TPUUpdateEmbeddingEnqueueOpInputsPassBase : public ::mlir::FunctionPass {
public:
  using Base = TPUUpdateEmbeddingEnqueueOpInputsPassBase;

  TPUUpdateEmbeddingEnqueueOpInputsPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TPUUpdateEmbeddingEnqueueOpInputsPassBase(const TPUUpdateEmbeddingEnqueueOpInputsPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-update-embedding-enqueue-op-inputs");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-update-embedding-enqueue-op-inputs"; }

  ::llvm::StringRef getDescription() const override { return "Updates inputs to TPU embedding enqueue ops depending on whether graph is in training mode or in evaluation mode."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUUpdateEmbeddingEnqueueOpInputsPass");
  }
  ::llvm::StringRef getName() const override { return "TPUUpdateEmbeddingEnqueueOpInputsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TensorArrayOpsDecompositionPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TensorArrayOpsDecompositionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TensorArrayOpsDecompositionPassBase;

  TensorArrayOpsDecompositionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TensorArrayOpsDecompositionPassBase(const TensorArrayOpsDecompositionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tensor-array-ops-decomposition");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tensor-array-ops-decomposition"; }

  ::llvm::StringRef getDescription() const override { return "Decompose tensor array operations into local variable operations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TensorArrayOpsDecompositionPass");
  }
  ::llvm::StringRef getName() const override { return "TensorArrayOpsDecompositionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<tensor::TensorDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// TensorDeviceCopyConversionPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TensorDeviceCopyConversionPassBase : public ::mlir::FunctionPass {
public:
  using Base = TensorDeviceCopyConversionPassBase;

  TensorDeviceCopyConversionPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TensorDeviceCopyConversionPassBase(const TensorDeviceCopyConversionPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tensor-device-copy");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tensor-device-copy"; }

  ::llvm::StringRef getDescription() const override { return "Fold the tf.Identity op and the tf.IdentityN op if the op has the same device as its operand"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TensorDeviceCopyConversionPass");
  }
  ::llvm::StringRef getName() const override { return "TensorDeviceCopyConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TensorFlowEnsureStaticShapesPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TensorFlowEnsureStaticShapesPassBase : public ::mlir::FunctionPass {
public:
  using Base = TensorFlowEnsureStaticShapesPassBase;

  TensorFlowEnsureStaticShapesPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TensorFlowEnsureStaticShapesPassBase(const TensorFlowEnsureStaticShapesPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-ensure-static-shapes");
  }
  ::llvm::StringRef getArgument() const override { return "tf-ensure-static-shapes"; }

  ::llvm::StringRef getDescription() const override { return "Performs checks that the whole module does not contain dynamic shapes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TensorFlowEnsureStaticShapesPass");
  }
  ::llvm::StringRef getName() const override { return "TensorFlowEnsureStaticShapesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TensorFlowOptimizePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TensorFlowOptimizePassBase : public ::mlir::FunctionPass {
public:
  using Base = TensorFlowOptimizePassBase;

  TensorFlowOptimizePassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TensorFlowOptimizePassBase(const TensorFlowOptimizePassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-optimize");
  }
  ::llvm::StringRef getArgument() const override { return "tf-optimize"; }

  ::llvm::StringRef getDescription() const override { return "Optimize TensorFlow module"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TensorFlowOptimizePass");
  }
  ::llvm::StringRef getName() const override { return "TensorFlowOptimizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TensorFlowShapeInferencePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TensorFlowShapeInferencePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TensorFlowShapeInferencePassBase;

  TensorFlowShapeInferencePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TensorFlowShapeInferencePassBase(const TensorFlowShapeInferencePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-shape-inference");
  }
  ::llvm::StringRef getArgument() const override { return "tf-shape-inference"; }

  ::llvm::StringRef getDescription() const override { return "Simple Shape Inference on TensorFlow Dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TensorFlowShapeInferencePass");
  }
  ::llvm::StringRef getName() const override { return "TensorFlowShapeInferencePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<int64_t> max_iterations_{*this, "max-iterations", ::llvm::cl::desc("Maximum shape inference iterations"), ::llvm::cl::init(10)};
};

//===----------------------------------------------------------------------===//
// VerifySuitableForExportPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class VerifySuitableForExportPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = VerifySuitableForExportPassBase;

  VerifySuitableForExportPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifySuitableForExportPassBase(const VerifySuitableForExportPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-verify-for-export");
  }
  ::llvm::StringRef getArgument() const override { return "tf-verify-for-export"; }

  ::llvm::StringRef getDescription() const override { return "Verify module is suitable for export back to TF Graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifySuitableForExportPass");
  }
  ::llvm::StringRef getName() const override { return "VerifySuitableForExportPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ClusterConstantSinkingPass Registration
//===----------------------------------------------------------------------===//

inline void registerClusterConstantSinkingPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateClusterConstantSinkingPass();
  });
}

//===----------------------------------------------------------------------===//
// ClusterOpsByPolicyPass Registration
//===----------------------------------------------------------------------===//

inline void registerClusterOpsByPolicyPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateClusterOpsByPolicyPass();
  });
}

//===----------------------------------------------------------------------===//
// ClusterOutliningPass Registration
//===----------------------------------------------------------------------===//

inline void registerClusterOutliningPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateClusterOutliningPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLaunchFuncToTFCallPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLaunchFuncToTFCallPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateConvertLaunchFuncToTFCallPass();
  });
}

//===----------------------------------------------------------------------===//
// DropWhileShapeInvariantInDeviceClusterPass Registration
//===----------------------------------------------------------------------===//

inline void registerDropWhileShapeInvariantInDeviceClusterPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateDropWhileShapeInvariantInDeviceClusterPass();
  });
}

//===----------------------------------------------------------------------===//
// DropWhileShapeInvariantPass Registration
//===----------------------------------------------------------------------===//

inline void registerDropWhileShapeInvariantPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateDropWhileShapeInvariantPass();
  });
}

//===----------------------------------------------------------------------===//
// ExecutorDialectToFunctionalPass Registration
//===----------------------------------------------------------------------===//

inline void registerExecutorDialectToFunctionalPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateExecutorDialectToFunctionalConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// ExecutorGraphPruningPass Registration
//===----------------------------------------------------------------------===//

inline void registerExecutorGraphPruningPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tf_executor::CreateTFExecutorGraphPruningPass();
  });
}

//===----------------------------------------------------------------------===//
// FunctionalControlFlowToRegionsPass Registration
//===----------------------------------------------------------------------===//

inline void registerFunctionalControlFlowToRegionsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateTFFunctionalControlFlowToRegions();
  });
}

//===----------------------------------------------------------------------===//
// HoistReplicateInvariantResourceWritesPass Registration
//===----------------------------------------------------------------------===//

inline void registerHoistReplicateInvariantResourceWritesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateHoistReplicateInvariantResourceWritesPass();
  });
}

//===----------------------------------------------------------------------===//
// LaunchOutliningPass Registration
//===----------------------------------------------------------------------===//

inline void registerLaunchOutliningPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateLaunchOutliningPass();
  });
}

//===----------------------------------------------------------------------===//
// LowerQuantizedPass Registration
//===----------------------------------------------------------------------===//

inline void registerLowerQuantizedPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateLowerQuantizedPass();
  });
}

//===----------------------------------------------------------------------===//
// MarkInputOutputAliasesPass Registration
//===----------------------------------------------------------------------===//

inline void registerMarkInputOutputAliasesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateMarkInputOutputAliasesPass();
  });
}

//===----------------------------------------------------------------------===//
// MarkOpsForOutsideCompilationPass Registration
//===----------------------------------------------------------------------===//

inline void registerMarkOpsForOutsideCompilationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateMarkOpsForOutsideCompilationPass();
  });
}

//===----------------------------------------------------------------------===//
// OutsideCompiledToHostLaunchPass Registration
//===----------------------------------------------------------------------===//

inline void registerOutsideCompiledToHostLaunchPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateOutsideCompiledToHostLaunchPass();
  });
}

//===----------------------------------------------------------------------===//
// PrepareTpuComputationForTfExportPass Registration
//===----------------------------------------------------------------------===//

inline void registerPrepareTpuComputationForTfExportPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreatePrepareTpuComputationForTfExportPass();
  });
}

//===----------------------------------------------------------------------===//
// PromoteResourcesToArgsPass Registration
//===----------------------------------------------------------------------===//

inline void registerPromoteResourcesToArgsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreatePromoteResourcesToArgsPass();
  });
}

//===----------------------------------------------------------------------===//
// PromoteVarHandlesToArgsPass Registration
//===----------------------------------------------------------------------===//

inline void registerPromoteVarHandlesToArgsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreatePromoteVarHandlesToArgsPass();
  });
}

//===----------------------------------------------------------------------===//
// RegionControlFlowToFunctionalPass Registration
//===----------------------------------------------------------------------===//

inline void registerRegionControlFlowToFunctionalPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateTFRegionControlFlowToFunctional();
  });
}

//===----------------------------------------------------------------------===//
// ReplicateInvariantOpHoistingPass Registration
//===----------------------------------------------------------------------===//

inline void registerReplicateInvariantOpHoistingPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateReplicateInvariantOpHoistingPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUCleanupClusterAttributesPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUCleanupClusterAttributesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUClusterCleanupAttributesPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUClusterFormationPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUClusterFormationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUClusterFormationPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUExtractHeadTailOutsideCompilationPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUExtractHeadTailOutsideCompilationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUExtractHeadTailOutsideCompilationPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUExtractOutsideCompilationPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUExtractOutsideCompilationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUExtractOutsideCompilationPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUHostComputationExpansionPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUHostComputationExpansionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUHostComputationExpansionPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUMergeVariablesWithExecutePass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUMergeVariablesWithExecutePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUMergeVariablesWithExecutePass();
  });
}

//===----------------------------------------------------------------------===//
// TPUReorderReplicateAndPartitionedInputsPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUReorderReplicateAndPartitionedInputsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUReorderReplicateAndPartitionedInputsPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUResourceReadForWritePass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUResourceReadForWritePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUResourceReadForWritePass();
  });
}

//===----------------------------------------------------------------------===//
// TPUResourceReadsWritesPartitioningPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUResourceReadsWritesPartitioningPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUResourceReadsWritesPartitioningPass();
  });
}

//===----------------------------------------------------------------------===//
// TPURewritePass Registration
//===----------------------------------------------------------------------===//

inline void registerTPURewritePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPURewritePass();
  });
}

//===----------------------------------------------------------------------===//
// TPUUpdateEmbeddingEnqueueOpInputsPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUUpdateEmbeddingEnqueueOpInputsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUUpdateEmbeddingEnqueueOpInputsPass();
  });
}

//===----------------------------------------------------------------------===//
// TensorArrayOpsDecompositionPass Registration
//===----------------------------------------------------------------------===//

inline void registerTensorArrayOpsDecompositionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateTensorArrayOpsDecompositionPass();
  });
}

//===----------------------------------------------------------------------===//
// TensorDeviceCopyConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerTensorDeviceCopyConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateTensorDeviceCopyConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// TensorFlowEnsureStaticShapesPass Registration
//===----------------------------------------------------------------------===//

inline void registerTensorFlowEnsureStaticShapesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateTFEnsureStaticShapesPass();
  });
}

//===----------------------------------------------------------------------===//
// TensorFlowOptimizePass Registration
//===----------------------------------------------------------------------===//

inline void registerTensorFlowOptimizePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateTFOptimizePass();
  });
}

//===----------------------------------------------------------------------===//
// TensorFlowShapeInferencePass Registration
//===----------------------------------------------------------------------===//

inline void registerTensorFlowShapeInferencePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateTFShapeInferencePass();
  });
}

//===----------------------------------------------------------------------===//
// VerifySuitableForExportPass Registration
//===----------------------------------------------------------------------===//

inline void registerVerifySuitableForExportPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TF::CreateVerifySuitableForExportPass();
  });
}

//===----------------------------------------------------------------------===//
// TensorFlow Registration
//===----------------------------------------------------------------------===//

inline void registerTensorFlowPasses() {
  registerClusterConstantSinkingPassPass();
  registerClusterOpsByPolicyPassPass();
  registerClusterOutliningPassPass();
  registerConvertLaunchFuncToTFCallPassPass();
  registerDropWhileShapeInvariantInDeviceClusterPassPass();
  registerDropWhileShapeInvariantPassPass();
  registerExecutorDialectToFunctionalPassPass();
  registerExecutorGraphPruningPassPass();
  registerFunctionalControlFlowToRegionsPassPass();
  registerHoistReplicateInvariantResourceWritesPassPass();
  registerLaunchOutliningPassPass();
  registerLowerQuantizedPassPass();
  registerMarkInputOutputAliasesPassPass();
  registerMarkOpsForOutsideCompilationPassPass();
  registerOutsideCompiledToHostLaunchPassPass();
  registerPrepareTpuComputationForTfExportPassPass();
  registerPromoteResourcesToArgsPassPass();
  registerPromoteVarHandlesToArgsPassPass();
  registerRegionControlFlowToFunctionalPassPass();
  registerReplicateInvariantOpHoistingPassPass();
  registerTPUCleanupClusterAttributesPassPass();
  registerTPUClusterFormationPassPass();
  registerTPUExtractHeadTailOutsideCompilationPassPass();
  registerTPUExtractOutsideCompilationPassPass();
  registerTPUHostComputationExpansionPassPass();
  registerTPUMergeVariablesWithExecutePassPass();
  registerTPUReorderReplicateAndPartitionedInputsPassPass();
  registerTPUResourceReadForWritePassPass();
  registerTPUResourceReadsWritesPartitioningPassPass();
  registerTPURewritePassPass();
  registerTPUUpdateEmbeddingEnqueueOpInputsPassPass();
  registerTensorArrayOpsDecompositionPassPass();
  registerTensorDeviceCopyConversionPassPass();
  registerTensorFlowEnsureStaticShapesPassPass();
  registerTensorFlowOptimizePassPass();
  registerTensorFlowShapeInferencePassPass();
  registerVerifySuitableForExportPassPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
