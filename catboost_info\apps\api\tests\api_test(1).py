import ast
import json
import requests
import global_settings
from utils.sql_helper import MysqlHelper


def get_token():
    login_url = f'{url}/api/login/'
    client_id = global_settings.heartVoice['login']['clientId']
    client_secret = global_settings.heartVoice['login']['clientSecret']

    params = {
        "clientId": client_id,
        "clientSecret": client_secret
    }
    resp = requests.post(login_url, data=params)
    resp_json = resp.json()

    data = resp_json.get("data")
    return data.get("token")


def ecg_analysis(ecg_data, fs, token):
    analysis_url = f'{url}/api/diagnose/arrhythmia/'
    headers = {
        "X-Auth-Token": token,
        "Content-Type": "application/json;charset=utf-8"
    }

    params = {
        "signal": ecg_data,
        "fs": fs,
        "adc_gain": 1,
        "adc_zero": 0
    }

    resp = requests.post(analysis_url, headers=headers, data=json.dumps(params))

    if resp.status_code == 200:
        resp_json = resp.json()

        if resp_json["code"] == 0:
            return resp_json["data"]
        else:
            return resp_json
    else:
        return resp.status_code


if __name__ == '__main__':
    url = 'http://127.0.0.1:8000'
    # url = 'http://ecggpt.aiweihe.com'
    # url = 'http://test.ecggpt.aiweihe.com'
    # url = global_settings.heartVoice['login']['url']

    token = get_token()

    fs = 500

    sql = f'''
                  SELECT b.patient_no, a.apply_no, a.ecg_data , a.conclusion
                  FROM t_patient_ecg as a
               LEFT JOIN t_patient as b on b.id = a.patient_id
#                where conclusion like '%房性早搏%'
               Limit 1
              '''
    datas = MysqlHelper().datas_query(sql)

    for patient_no, apply_no, ecg_data, conclusion in datas:
        result = ecg_analysis(ecg_data, fs, token)

        print(conclusion, result)


