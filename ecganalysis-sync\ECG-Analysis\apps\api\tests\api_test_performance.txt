         607289683 function calls (607021236 primitive calls) in 36188.447 seconds

   Ordered by: internal time

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
    20999 34904.982    1.662 34904.982    1.662 {method 'recv_into' of '_socket.socket' objects}
     6703  838.443    0.125  838.443    0.125 {built-in method time.sleep}
     6721  255.528    0.038  255.528    0.038 {method 'connect' of '_socket.socket' objects}
6638/6564   35.370    0.005 36119.519    5.503 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:223(ecg_analysis)
        4   29.502    7.375   29.502    7.375 {built-in method builtins.input}
 80180764   16.944    0.000   31.591    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:150(_isna)
 80180764   12.375    0.000   43.966    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:67(isna)
     6638   11.219    0.002   11.248    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\encoder.py:306(iterencode)
 40093564   10.421    0.000   34.731    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:288(notna)
 80174084    9.945    0.000    9.945    0.000 {built-in method pandas._libs.missing.checknull}
       40    8.706    0.218   12.898    0.322 {method 'read_low_memory' of 'pandas._libs.parsers.TextReader' objects}
120281212    6.673    0.000    6.673    0.000 {pandas._libs.lib.is_scalar}
55328505/55315298    4.525    0.000    5.166    0.000 {built-in method builtins.isinstance}
 40580710    2.686    0.000    2.686    0.000 {method 'append' of 'list' objects}
 40087000    2.389    0.000    2.389    0.000 {built-in method builtins.abs}
     6818    0.961    0.000    0.961    0.000 {built-in method builtins.min}
     6564    0.868    0.000    0.868    0.000 {method 'tolist' of 'numpy.ndarray' objects}
    13349    0.845    0.000    0.846    0.000 {built-in method nt.stat}
  2068176    0.833    0.000    1.993    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:670(__getitem__)
       20    0.814    0.041 36144.528 1807.226 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:557(process_single_file)
  2201677    0.804    0.000    3.115    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:742(__iter__)
     6721    0.780    0.000    0.780    0.000 {function socket.close at 0x000002206C3BD5E0}
   177674    0.774    0.000    2.418    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:288(__init__)
  1853882    0.753    0.000    1.410    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1429(is_extension_array_dtype)
  2078496    0.689    0.000    1.490    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1587(_is_dtype_type)
   177674    0.612    0.000    0.612    0.000 {method 'write' of '_io.TextIOWrapper' objects}
432250/432158    0.598    0.000    0.600    0.000 {built-in method numpy.core._multiarray_umath.implement_array_function}
  1455282    0.588    0.000    1.829    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:680(is_integer_dtype)
  2068176    0.578    0.000    1.160    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:740(encodekey)
    13254    0.573    0.000    0.573    0.000 {method 'sendall' of '_socket.socket' objects}
    13300    0.553    0.000    3.571    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2494(getproxies_environment)
    19950    0.524    0.000    0.524    0.000 {built-in method winreg.QueryValueEx}
     6661    0.455    0.000    0.479    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:227(_isna_array)
   207170    0.451    0.000    1.366    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:470(sanitize_array)
       21    0.445    0.021    2.063    0.098 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:344(_concatenate_chunks)
   191121    0.417    0.000    0.417    0.000 {built-in method builtins.max}
   184239    0.411    0.000    0.833    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:180(split)
   177674    0.371    0.000    1.023    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1514(findCaller)
  2532556    0.355    0.000    0.355    0.000 {method 'lower' of 'str' objects}
  2032144    0.340    0.000    0.459    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\generic.py:43(_check)
  1462111    0.334    0.000    0.491    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:155(<lambda>)
  2021600    0.323    0.000    0.323    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:693(__iter__)
  4560092    0.319    0.000    0.330    0.000 {built-in method builtins.getattr}
2647914/2406592    0.293    0.000    0.347    0.000 {built-in method builtins.len}
    20092    0.293    0.000    0.420    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:227(_encode_invalid_chars)
  2068176    0.290    0.000    0.396    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:734(check_str)
    13300    0.251    0.000    0.251    0.000 {built-in method winreg.OpenKey}
  3572013    0.251    0.000    0.273    0.000 {built-in method builtins.issubclass}
      178    0.250    0.001    0.251    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2074(_stack_arrays)
   355277    0.248    0.000    0.524    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:44(normcase)
   608936    0.246    0.000    0.657    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1240(is_float_dtype)
   184319    0.242    0.000    0.306    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:124(splitdrive)
   177674    0.240    0.000    6.253    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1565(_log)
     6584    0.228    0.000    0.348    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:392(raw_decode)
     6721    0.223    0.000    0.223    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:219(__init__)
   177674    0.211    0.000    1.825    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1073(emit)
  2101580    0.193    0.000    0.193    0.000 {method 'upper' of 'str' objects}
   177714    0.187    0.000    0.319    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:121(_splitext)
  1178702    0.178    0.000    0.178    0.000 {built-in method builtins.hasattr}
       40    0.173    0.004    1.543    0.039 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:596(_homogenize)
19869/19848    0.172    0.000    1.173    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:323(__init__)
     6627    0.172    0.000 34907.471    5.267 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:481(getresponse)
   177674    0.164    0.000    0.381    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1062(flush)
   177674    0.163    0.000    2.159    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:941(handle)
   177401    0.162    0.000    6.429    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1436(info)
   207170    0.161    0.000    0.244    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:698(_try_cast)
  1462111    0.158    0.000    0.158    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:150(classes_and_not_datetimelike)
   177674    0.156    0.000    0.550    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:655(format)
    66341    0.153    0.000    0.406    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:366(urlparse)
   185969    0.147    0.000    0.147    0.000 {method 'match' of 're.Pattern' objects}
     6657    0.144    0.000    0.144    0.000 {built-in method builtins.sum}
     6564    0.144    0.000    0.144    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:310(_slice)
   385521    0.142    0.000    0.208    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\base.py:465(find)
   177674    0.139    0.000    2.298    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1645(callHandlers)
   177401    0.137    0.000    6.581    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2074(info)
   387076    0.135    0.000    0.755    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:497(is_categorical_dtype)
6721/6650    0.129    0.000 35167.170    5.288 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:594(urlopen)
   177674    0.128    0.000    2.546    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1550(makeRecord)
     6650    0.124    0.000 35169.800    5.289 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:673(send)
    13371    0.124    0.000    0.677    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:369(parse_url)
    53751    0.122    0.000    0.228    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1208(putheader)
     6721    0.122    0.000  257.648    0.038 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:365(request)
   764862    0.121    0.000    0.121    0.000 {method 'get' of 'dict' objects}
   177714    0.120    0.000    0.467    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:203(splitext)
   387095    0.117    0.000    0.592    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\base.py:286(is_dtype)
     6721    0.113    0.000 35165.310    5.232 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:379(_make_request)
   193500    0.112    0.000    0.212    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:356(<listcomp>)
       19    0.111    0.006    0.117    0.006 {pandas._libs.writers.write_csv_rows}
   177674    0.110    0.000    2.444    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1591(handle)
     6584    0.110    0.000    0.110    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:33(select_wait_for_socket)
   355348    0.107    0.000    0.170    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:898(acquire)
   901653    0.107    0.000    0.107    0.000 {method 'pop' of 'dict' objects}
     6650    0.107    0.000 35168.642    5.289 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:613(send)
    46625    0.105    0.000    0.276    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:824(update)
     6650    0.101    0.000 35189.581    5.292 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:500(request)
    66387    0.096    0.000    0.160    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2084(debug)
     6584    0.095    0.000    1.200    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:204(parse_headers)
    72991    0.094    0.000    0.160    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:417(urlsplit)
     6627    0.094    0.000    0.135    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:301(makefile)
     6627    0.093    0.000 34906.363    5.267 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:300(begin)
   533154    0.093    0.000    0.093    0.000 {method 'rfind' of 'str' objects}
     6650    0.093    0.000   14.726    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:457(prepare_request)
   152632    0.091    0.000    0.102    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:110(_coerce_args)
     6650    0.091    0.000    0.110    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:95(_default_key_normalizer)
     6721    0.089    0.000    0.104    0.000 {built-in method _socket.getaddrinfo}
266940/260219    0.089    0.000    0.116    0.000 {method 'encode' of 'str' objects}
   391590    0.089    0.000    0.108    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:52(bounded_int)
    52715    0.088    0.000 34905.074    0.662 {method 'readline' of '_io.BufferedReader' objects}
    13168    0.088    0.000    0.552    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:218(_parsegen)
     6640    0.087    0.000    0.145    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5037(__getitem__)
     6674    0.087    0.000    0.087    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:204(iterencode)
   159983    0.083    0.000    0.083    0.000 {built-in method _abc._abc_instancecheck}
     6584    0.082    0.000    0.187    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:471(_parse_headers)
   355348    0.082    0.000    0.115    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:905(release)
     6650    0.081    0.000    1.113    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:204(get_netrc_auth)
    46550    0.081    0.000    0.484    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:61(merge_setting)
    72832    0.080    0.000    0.219    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:657(get)
    20144    0.079    0.000    0.079    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:81(RLock)
      462    0.079    0.000    0.081    0.000 {pandas._libs.lib.maybe_convert_objects}
   173220    0.079    0.000    0.097    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:46(__setitem__)
   177674    0.079    0.000    0.079    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:435(_format)
   207170    0.078    0.000    0.156    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:630(_sanitize_ndim)
   539627    0.078    0.000    0.078    0.000 {method 'replace' of 'str' objects}
   616385    0.078    0.000    0.120    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:147(<lambda>)
     6627    0.077    0.000 34905.024    5.267 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:267(_read_status)
       20    0.075    0.004    0.097    0.005 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:45(__init__)
   237957    0.074    0.000    0.280    0.000 <__array_function__ internals>:177(putmask)
       40    0.074    0.002   17.283    0.432 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1276(get_chunk)
    33328    0.073    0.000    0.084    0.000 {built-in method builtins.sorted}
   915034    0.073    0.000    0.073    0.000 {built-in method nt.fspath}
   257502    0.073    0.000    0.073    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1689(isEnabledFor)
   179874    0.072    0.000    0.091    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:51(__getitem__)
   184239    0.071    0.000    0.904    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:214(basename)
   381824    0.070    0.000    0.070    0.000 {method 'acquire' of '_thread.RLock' objects}
   177674    0.070    0.000    0.620    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:918(format)
    13299    0.069    0.000    0.302    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:521(cookiejar_from_dict)
    20999    0.069    0.000 34905.077    1.662 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:655(readinto)
     6584    0.069    0.000    0.262    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:337(extend)
    46562    0.067    0.000    0.214    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:345(to_key_val_list)
   193592    0.066    0.000    0.490    0.000 <__array_function__ internals>:177(concatenate)
     6650    0.066    0.000    0.680    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:409(prepare_url)
   177674    0.065    0.000    0.103    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:427(usesTime)
   193500    0.064    0.000    0.452    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:360(<setcomp>)
     6721    0.064    0.000    0.187    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1052(putrequest)
     6721    0.063    0.000  256.119    0.038 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:27(create_connection)
    59850    0.062    0.000    0.146    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:155(hostname)
   177674    0.061    0.000    0.090    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:160(<lambda>)
   193500    0.061    0.000    0.061    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:358(<setcomp>)
    53751    0.061    0.000    0.335    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:351(putheader)
   616385    0.061    0.000    0.061    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:145(classes)
   177674    0.061    0.000    0.164    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:633(usesTime)
     6584    0.061    0.000    0.107    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:139(__init__)
    13168    0.060    0.000    0.577    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:858(_raw_read)
   207267    0.060    0.000    0.341    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:379(extract_array)
     6584    0.059    0.000    0.240    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:658(__init__)
    87422    0.059    0.000    0.059    0.000 {method 'search' of 're.Pattern' objects}
   355348    0.059    0.000    0.059    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:796(filter)
    52672    0.058    0.000    0.135    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:462(get)
     6721    0.058    0.000  256.198    0.038 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:193(_new_conn)
     6584    0.058    0.000    0.217    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:476(readinto)
     6650    0.058    0.000   11.463    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:494(prepare_body)
    19949    0.057    0.000    0.172    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1258(__init__)
   177674    0.057    0.000    0.072    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1306(current_thread)
   177674    0.056    0.000    0.079    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:119(getLevelName)
     6564    0.056    0.000    0.813    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:943(__getitem__)
34603/27944    0.054    0.000    0.079    0.000 {built-in method numpy.asarray}
    20199    0.054    0.000    0.183    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5577(__setattr__)
    20023    0.053    0.000    0.072    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:239(__init__)
     6832    0.053    0.000    0.088    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5517(__finalize__)
     6584    0.053    0.000    0.196    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:573(__init__)
   207069    0.052    0.000    0.183    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:552(require_length_match)
    13303    0.052    0.000    0.137    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2043(new_block)
   177674    0.052    0.000    0.180    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:639(formatMessage)
     6584    0.051    0.000    0.083    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:315(__init__)
   230555    0.051    0.000    0.051    0.000 {method 'find' of 'str' objects}
     6650    0.051    0.000    2.508    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:114(proxy_bypass)
   177674    0.051    0.000    0.051    0.000 {method 'flush' of '_io.TextIOWrapper' objects}
    33254    0.050    0.000    0.295    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:40(__init__)
   521659    0.050    0.000    0.050    0.000 {method 'decode' of 'bytes' objects}
    13321    0.050    0.000    0.117    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:121(put)
   193823    0.050    0.000    0.094    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2006(_grouping_func)
     6742    0.050    0.000    0.900    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:316(close)
   177674    0.050    0.000    0.129    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:438(format)
     6650    0.050    0.000    2.759    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:765(should_bypass_proxies)
    66500    0.050    0.000    0.076    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:195(_hostinfo)
   177674    0.049    0.000    0.050    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:364(getMessage)
     6650    0.048    0.000    0.251    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:483(prepare_headers)
    13417    0.048    0.000    0.110    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:289(expanduser)
     6584    0.048    0.000    0.938    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:59(parsestr)
     6585    0.048    0.000    0.809    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:1279(iterrows)
     6650    0.048    0.000    4.892    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:750(merge_environment_settings)
     6627    0.047    0.000 34906.590    5.267 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1300(getresponse)
     6537    0.047    0.000    0.075    0.000 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:468(get_disease_name)
       39    0.047    0.001    0.047    0.001 {built-in method io.open}
     6564    0.046    0.000    0.411    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\tools\numeric.py:27(to_numeric)
     6584    0.046    0.000    0.665    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:359(build_response)
    79828    0.046    0.000    0.084    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1424(debug)
   217349    0.046    0.000    0.046    0.000 {built-in method time.time}
     1837    0.046    0.000    0.046    0.000 {built-in method _codecs.utf_8_decode}
   237564    0.046    0.000    0.046    0.000 {method 'rstrip' of 'str' objects}
   233828    0.045    0.000    0.045    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:58(<genexpr>)
    26042    0.045    0.000    0.065    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:341(notify)
   207170    0.045    0.000    0.062    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:667(_sanitize_str_dtypes)
   206836    0.045    0.000    0.045    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1711(sanitize_to_nanoseconds)
     6584    0.044    0.000    0.729    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:910(read)
   207170    0.044    0.000    0.061    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:687(_maybe_repeat)
   159983    0.044    0.000    0.127    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\abc.py:96(__instancecheck__)
    12781    0.043    0.000    0.111    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:153(get)
    19818    0.043    0.000    0.213    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:35(__init__)
     6627    0.043    0.000    0.177    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:237(__init__)
    39504    0.043    0.000    0.072    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:293(header_source_parse)
     6721    0.042    0.000    1.190    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:258(_get_conn)
     6640    0.042    0.000    0.077    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexers\utils.py:457(check_array_indexer)
       40    0.042    0.001    0.395    0.010 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2022(_form_blocks)
   197539    0.042    0.000    0.060    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:34(_get_bothseps)
    19818    0.041    0.000    0.053    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:82(__init__)
     6564    0.041    0.000    0.337    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1820(getitem_mgr)
   159729    0.041    0.000    0.041    0.000 {method 'startswith' of 'str' objects}
    13348    0.041    0.000  256.847    0.019 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:939(send)
    26336    0.040    0.000    0.173    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:564(get_content_type)
    13348    0.040    0.000    0.040    0.000 {method 'settimeout' of '_socket.socket' objects}
     6721    0.040    0.000    0.040    0.000 {method 'setsockopt' of '_socket.socket' objects}
    65840    0.040    0.000    0.050    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:259(__getitem__)
     6650    0.039    0.000   12.840    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:351(prepare)
    13168    0.039    0.000    0.088    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1596(make_cookies)
    13371    0.039    0.000    0.066    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:263(_remove_path_dot_segments)
   118994    0.039    0.000    0.039    0.000 {method 'split' of 'str' objects}
     6650    0.038    0.000    0.093    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1353(add_cookie_header)
     6650    0.038    0.000    0.053    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:258(__init__)
   113620    0.038    0.000    0.038    0.000 {built-in method __new__ of type object at 0x00007FFB995CB810}
   177674    0.037    0.000    0.037    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1031(name)
     6584    0.037    0.000    0.890    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:41(parse)
   355356    0.037    0.000    0.037    0.000 {built-in method _thread.get_ident}
    33249    0.036    0.000    0.064    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1219(vals_sorted_by_key)
    19949    0.036    0.000    0.036    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:877(__init__)
     6674    0.036    0.000    0.163    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:183(dumps)
   381824    0.036    0.000    0.036    0.000 {method 'release' of '_thread.RLock' objects}
    59256    0.035    0.000    0.039    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:78(readline)
   508284    0.035    0.000    0.035    0.000 {built-in method builtins.ord}
    20158    0.035    0.000    0.063    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:109(__init__)
    13168    0.034    0.000    0.134    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1677(extract_cookies)
     6627    0.034    0.000    0.034    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:643(__init__)
    26568    0.034    0.000    0.091    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:640(name)
     6584    0.034    0.000    0.035    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:53(__init__)
    33229    0.034    0.000    0.055    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:590(name)
     6650    0.033    0.000 35189.614    5.292 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:626(post)
    13168    0.033    0.000    0.257    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:444(read)
     6584    0.033    0.000    0.045    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:255(get)
    13371    0.033    0.000    0.051    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:100(__new__)
       21    0.033    0.002    0.053    0.003 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:494(<listcomp>)
    79776    0.033    0.000    0.077    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1045(_validate_header_part)
    72424    0.032    0.000    0.069    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:281(_sanitize_header)
    13168    0.032    0.000    0.304    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:124(extract_cookies_to_jar)
    13300    0.032    0.000    0.184    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:838(select_proxy)
     6650    0.032    0.000    0.494    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:446(get_connection_with_tls_context)
     6564    0.032    0.000    0.049    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1858(external_values)
    46484    0.032    0.000    0.092    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:664(__contains__)
       21    0.032    0.002    0.032    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:340(_maybe_dedup_names)
     6721    0.031    0.000  256.230    0.038 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:278(connect)
    53271    0.031    0.000    0.044    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:676(items)
     6742    0.031    0.000    0.850    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:925(close)
    39504    0.031    0.000    0.048    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:302(add)
    13204    0.031    0.000    0.088    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:105(is_bool_indexer)
   194431    0.030    0.000    0.043    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:909(__len__)
     6584    0.030    0.000    0.319    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:242(__init__)
    53751    0.030    0.000    0.030    0.000 {method 'fullmatch' of 're.Pattern' objects}
   254123    0.030    0.000    0.030    0.000 {pandas._libs.lib.is_list_like}
    13187    0.030    0.000    0.055    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:255(inner)
     6650    0.030    0.000    0.616    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:76(proxy_bypass_registry)
   177674    0.030    0.000    0.030    0.000 {built-in method nt.getpid}
    13442    0.030    0.000    0.042    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\idna.py:147(encode)
    33392    0.029    0.000    0.036    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:57(__iter__)
     6662    0.029    0.000    0.029    0.000 {method 'clear' of 'dict' objects}
    53751    0.029    0.000    0.054    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:400(<genexpr>)
     6650    0.029    0.000    0.230    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:305(connection_from_context)
     6584    0.029    0.000    0.077    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:101(push)
    33111    0.029    0.000    0.029    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5561(__getattr__)
   177674    0.029    0.000    0.029    0.000 {built-in method sys._getframe}
    26336    0.029    0.000    0.043    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:497(get_all)
    13168    0.028    0.000    0.028    0.000 {method 'readlines' of '_io._IOBase' objects}
    13362    0.028    0.000    0.070    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:109(_get_single_key)
    13211    0.028    0.000    0.044    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:411(close)
     6584    0.028    0.000    0.072    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:40(assert_header_parsing)
     6818    0.028    0.000    0.030    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:868(__new__)
     6650    0.027    0.000    0.091    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:90(_urllib3_request_context)
   219186    0.027    0.000    0.027    0.000 {method 'partition' of 'str' objects}
     6564    0.027    0.000    1.559    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:5301(dropna)
     6564    0.027    0.000    1.471    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:616(remove_na_arraylike)
     6650    0.027    0.000    0.118    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1018(get_auth_from_url)
   421165    0.026    0.000    0.026    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:1149(cast)
     6638    0.026    0.000   11.279    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\encoder.py:277(encode)
    19940    0.026    0.000    0.059    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1274(is_bool_dtype)
    13246    0.026    0.000    0.187    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1745(from_array)
     6650    0.026    0.000    0.026    0.000 {method 'Close' of 'PyHKEY' objects}
    73640    0.026    0.000    0.806    0.000 {method 'join' of 'bytes' objects}
    19818    0.026    0.000    0.078    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:238(helper)
   207126    0.025    0.000    0.037    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1416(is_1d_only_ea_dtype)
    59256    0.025    0.000    0.064    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:128(__next__)
    60474    0.025    0.000    0.029    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:130(_validate_timeout)
     6584    0.025    0.000    0.046    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:679(_init_length)
    60459    0.024    0.000    0.048    0.000 {built-in method builtins.any}
39983/39962    0.024    0.000    0.056    0.000 {built-in method builtins.all}
    26336    0.024    0.000    0.034    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:29(_splitparam)
    33249    0.024    0.000    0.087    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1223(deepvalues)
    79008    0.023    0.000    0.033    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\utils.py:51(_has_surrogates)
     6650    0.023    0.000    0.088    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:330(connection_from_pool_key)
    19752    0.023    0.000    0.822    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:890(content)
     6564    0.023    0.000    0.519    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:1042(_get_values)
     6674    0.022    0.000    0.114    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:182(encode)
    19818    0.022    0.000    0.173    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:117(__exit__)
     6548    0.022    0.000   20.866    0.003 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:105(get_token)
    13302    0.022    0.000    0.864    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:16(exists)
     6721    0.022    0.000    0.032    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\request.py:189(body_to_chunks)
   158482    0.021    0.000    0.021    0.000 {method 'join' of 'str' objects}
     6721    0.021    0.000    0.172    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:901(getaddrinfo)
     6662    0.021    0.000    0.054    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:106(_encode_params)
    46795    0.021    0.000    0.031    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:321(is_hashable)
   107502    0.021    0.000    0.024    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:353(<genexpr>)
    39888    0.021    0.000    0.098    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1034(check_header_validity)
     6721    0.020    0.000  256.618    0.038 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:998(_send_output)
    72424    0.020    0.000    0.089    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:311(header_fetch_parse)
    86464    0.020    0.000    0.020    0.000 {method 'rpartition' of 'str' objects}
     6650    0.020    0.000    0.147    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:546(request_url)
    60472    0.020    0.000    0.026    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:977(_output)
    13300    0.020    0.000    0.056    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:542(merge_cookies)
     6650    0.019    0.000    0.197    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:140(get_cookie_header)
    13303    0.019    0.000    0.035    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2057(check_ndim)
     6627    0.019    0.000    0.030    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:732(close)
     6650    0.019    0.000    0.234    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2662(getproxies_registry)
      101    0.019    0.000    0.020    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1962(construct_1d_object_array_from_listlike)
    19848    0.019    0.000    0.019    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1714(__init__)
     6584    0.019    0.000    0.034    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:213(get_payload)
    19790    0.019    0.000    0.025    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:720(__hash__)
    13168    0.019    0.000    0.762    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:1038(stream)
    26102    0.018    0.000    0.024    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:246(__enter__)
     6564    0.018    0.000    0.022    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:178(_can_hold_na)
     6584    0.018    0.000    0.026    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:514(_parse_content_type_header)
    13527    0.018    0.000    0.022    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1991(get_block_type)
     6640    0.018    0.000    0.143    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2357(check_bool_indexer)
    20023    0.018    0.000    0.018    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:47(__init__)
   237957    0.018    0.000    0.018    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:1106(putmask)
     6721    0.018    0.000    0.115    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:296(_put_conn)
     6741    0.018    0.000    0.027    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:654(_simple_new)
    13485    0.018    0.000    0.023    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:164(host)
    26568    0.018    0.000    0.057    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1721(validate_all_hashable)
     6650    0.018    0.000    0.051    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:572(prepare_content_length)
     6650    0.018    0.000    0.025    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:781(get_adapter)
     6584    0.017    0.000    0.074    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:159(resolve_redirects)
     6584    0.017    0.000    0.053    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:539(get_encoding_from_headers)
    26336    0.017    0.000    0.144    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:735(_error_catcher)
     6584    0.017    0.000    0.417    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:947(json)
    13450    0.017    0.000    0.030    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:313(__call__)
     6638    0.017    0.000   11.296    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\__init__.py:276(dumps)
     6650    0.017    0.000    0.025    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:334(__init__)
    13362    0.017    0.000    0.020    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:589(_get_root)
    20092    0.017    0.000    0.017    0.000 {method 'subn' of 're.Pattern' objects}
13292/13221    0.017    0.000    0.017    0.000 {built-in method _abc._abc_subclasscheck}
    19818    0.017    0.000    0.028    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:108(__enter__)
     6584    0.016    0.000    0.370    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:379(decode)
    26724    0.016    0.000    0.016    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:603(_get_deprecated_option)
    13168    0.016    0.000    0.274    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:801(_fp_read)
     6650    0.016    0.000    0.054    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:474(urlunparse)
    66661    0.016    0.000    0.016    0.000 {built-in method builtins.hash}
     6627    0.016    0.000    0.047    0.000 {method 'close' of '_io.BufferedReader' objects}
    13362    0.016    0.000    0.107    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:127(_get_option)
     6650    0.016    0.000    4.749    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:826(get_environ_proxies)
    73843    0.016    0.000    0.021    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\util.py:19(to_str)
    13442    0.016    0.000    0.046    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:97(_intenum_converter)
     6650    0.016    0.000    0.038    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:207(register_hook)
    33249    0.016    0.000    0.016    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1750(__iter__)
    13168    0.016    0.000    0.016    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:120(__init__)
     6650    0.016    0.000    0.259    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:276(connection_from_host)
     6564    0.016    0.000    0.039    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1877(_can_hold_na)
     6564    0.015    0.000    0.042    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1198(is_numeric_dtype)
   119640    0.015    0.000    0.015    0.000 {method 'items' of 'dict' objects}
     6650    0.015    0.000    0.023    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:485(urlunsplit)
    13349    0.015    0.000    0.027    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1962(maybe_coerce_values)
    13168    0.015    0.000    0.780    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:816(generate)
    13168    0.015    0.000    0.567    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:178(_call_parse)
    53136    0.015    0.000    0.026    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1740(<genexpr>)
   152632    0.015    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:99(_noop)
     6584    0.015    0.000    0.017    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:909(text)
     6650    0.015    0.000    0.016    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:624(unquote)
     6781    0.015    0.000    0.015    0.000 {method 'pop' of 'collections.OrderedDict' objects}
    39723    0.015    0.000    0.163    0.000 {built-in method builtins.next}
    13442    0.015    0.000    0.060    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:348(_get_timeout)
     6721    0.015    0.000    0.206    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:331(putrequest)
     6650    0.014    0.000    0.027    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:135(super_len)
    39504    0.014    0.000    0.017    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:479(set_raw)
   200084    0.014    0.000    0.014    0.000 {method 'pop' of 'set' objects}
    13442    0.014    0.000    0.044    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:188(clone)
    13365    0.014    0.000    0.021    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:15(default_hooks)
    59497    0.014    0.000    0.014    0.000 {method 'setdefault' of 'dict' objects}
     6721    0.014    0.000  256.632    0.038 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1236(endheaders)
     6674    0.014    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:104(__init__)
     6742    0.014    0.000    0.803    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:496(close)
     6584    0.014    0.000    0.620    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:173(feed)
     6698    0.014    0.000    0.037    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:799(quote)
    13490    0.013    0.000    0.022    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7099(maybe_extract_name)
    46088    0.013    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:291(__iter__)
     6650    0.013    0.000    0.028    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:95(__getitem__)
     6584    0.013    0.000    1.716    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_decorators.py:302(wrapper)
     6698    0.013    0.000    0.021    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:870(quote_from_bytes)
     6584    0.013    0.000    0.048    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:459(<listcomp>)
   193592    0.013    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:148(concatenate)
    26102    0.013    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:245(_qsize)
    13168    0.013    0.000    0.125    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:635(release_conn)
    26042    0.013    0.000    0.020    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:261(_is_owned)
    13450    0.013    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:631(__new__)
    53271    0.013    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:698(__init__)
     6584    0.013    0.000    0.106    0.000 {method 'readinto' of '_io.BufferedReader' objects}
    13288    0.013    0.000    0.028    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:805(is_empty_data)
    33309    0.013    0.000    0.017    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:884(__len__)
    13299    0.013    0.000    0.072    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:534(<listcomp>)
    98763    0.013    0.000    0.013    0.000 {method 'strip' of 'str' objects}
    20999    0.013    0.000    0.019    0.000 {method '_checkReadable' of '_io._IOBase' objects}
    19752    0.012    0.000    0.109    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:588(get_content_maintype)
    27626    0.012    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:694(readable)
    13300    0.012    0.000    0.033    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:358(update)
     6650    0.012    0.000    0.056    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:774(get_proxy)
    26102    0.012    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:249(__exit__)
     6650    0.012    0.000    0.133    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:588(prepare_auth)
    13267    0.012    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:542(_set_axis)
    13362    0.012    0.000    0.119    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:255(__call__)
     6584    0.012    0.000    0.029    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:446(_init_decoder)
     6584    0.012    0.000    0.119    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:184(close)
     6650    0.012    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:304(cert_verify)
     6584    0.012    0.000    0.382    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\__init__.py:459(loads)
     6584    0.012    0.000    0.021    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:70(close)
     6627    0.012    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:245(read_timeout)
    13168    0.012    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:251(put)
     6537    0.012    0.000    0.013    0.000 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:514(get_multi_label_disease_name)
    13503    0.012    0.000    0.021    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:303(_normalize_host)
    19950    0.012    0.000    0.056    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:84(<listcomp>)
     6584    0.012    0.000    0.142    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:15(is_connection_dropped)
     6650    0.012    0.000    0.023    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:85(path_url)
    13207    0.012    0.000    0.057    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:768(__instancecheck__)
     6650    0.012    0.000    0.210    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:610(prepare_cookies)
       20    0.012    0.001    0.012    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:205(<dictcomp>)
    13207    0.012    0.000    0.045    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:771(__subclasscheck__)
     6584    0.011    0.000    0.047    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:802(__getitem__)
     6650    0.011    0.000    0.028    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1731(clear_expired_cookies)
    19838    0.011    0.000    0.017    0.000 {function SocketIO.close at 0x000002206C3BE040}
     6584    0.011    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:799(iter_content)
     6650    0.011    0.000    0.049    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:630(prepare_hooks)
     6584    0.011    0.000    0.130    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:299(is_connected)
     6662    0.011    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:892(urlencode)
     6650    0.011    0.000    0.017    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:166(port)
    46180    0.011    0.000    0.011    0.000 {method 'lstrip' of 'str' objects}
     6584    0.010    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:197(_new_message)
    46538    0.010    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\_internal_utils.py:25(to_native_string)
     6650    0.010    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:393(prepare_method)
     6650    0.010    0.000    1.974    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2707(getproxies)
     6650    0.010    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:636(unquote_unreserved)
     6650    0.010    0.000    0.059    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:660(requote_uri)
     6650    0.010    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:374(_merge_pool_kwargs)
     6721    0.010    0.000    0.112    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:351(_encode_target)
     6630    0.010    0.000    0.014    0.000 <frozen importlib._bootstrap>:389(parent)
    13385    0.010    0.000    0.021    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:687(_values)
    13362    0.010    0.000    0.023    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:642(_warn_if_deprecated)
     7089    0.010    0.000    0.039    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:161(is_object_dtype)
    20092    0.010    0.000    0.010    0.000 {method 'count' of 'bytes' objects}
    13168    0.010    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:403(retries)
    13385    0.010    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1862(internal_values)
       40    0.010    0.000   17.209    0.430 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1243(read)
       21    0.010    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:308(<dictcomp>)
     6626    0.009    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:379(_is_method_retryable)
    13362    0.009    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:571(_select_options)
     6584    0.009    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:1079(closed)
     6564    0.009    0.000    0.889    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\base.py:723(tolist)
    13168    0.009    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:9(is_fp_closed)
     6584    0.009    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:244(__init__)
    13374    0.009    0.000    0.011    0.000 {method 'extend' of 'list' objects}
    13168    0.009    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:122(pushlines)
    20193    0.009    0.000    0.009    0.000 {method 'copy' of 'dict' objects}
     6721    0.009    0.000    0.078    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:76(copy)
     6721    0.009    0.000    0.789    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:492(_real_close)
     6584    0.009    0.000    0.119    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:113(wait_for_read)
     6537    0.009    0.000    0.009    0.000 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:396(<listcomp>)
    26742    0.009    0.000    0.009    0.000 {method 'groups' of 're.Match' objects}
     6584    0.009    0.000    0.023    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:769(is_redirect)
     6721    0.009    0.000    0.049    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:93(_set_socket_options)
     6640    0.009    0.000    0.023    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:825(__array__)
    19950    0.008    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:211(<genexpr>)
    20092    0.008    0.000    0.008    0.000 {method 'decode' of 'bytearray' objects}
       21    0.008    0.000    2.119    0.101 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:425(dict_to_mgr)
    19950    0.008    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:60(__len__)
     6650    0.008    0.000    0.099    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:396(build_connection_pool_key_attributes)
     6650    0.008    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:183(_userinfo)
     6584    0.008    0.000    0.032    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:107(get_redirect_target)
     6832    0.008    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:83(allows_duplicate_labels)
    20598    0.008    0.000    0.008    0.000 {method 'endswith' of 'str' objects}
    20786    0.008    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1552(get_dtype)
     6650    0.008    0.000    0.019    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:147(username)
     6481    0.008    0.000    0.008    0.000 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:508(<listcomp>)
     6650    0.008    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1294(_cookie_attrs)
     6735    0.008    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2486(check_deprecated_indexers)
     6721    0.008    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:202(start_connect)
     6564    0.008    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:209(external_values)
     6908    0.008    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1148(needs_i8_conversion)
    26336    0.007    0.000    0.007    0.000 {method 'count' of 'str' objects}
     6929    0.007    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:546(_get_axis_number)
    13634    0.007    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:438(ensure_wrapped_if_datetimelike)
     6627    0.007    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:486(_decref_socketios)
    13365    0.007    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:16(<dictcomp>)
     6584    0.007    0.000    0.007    0.000 {method 'tobytes' of 'memoryview' objects}
    40113    0.007    0.000    0.007    0.000 {method 'values' of 'collections.OrderedDict' objects}
     6584    0.007    0.000    0.018    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:387(is_retry)
     6564    0.007    0.000    0.056    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:645(values)
     6584    0.007    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:303(set_payload)
    26042    0.007    0.000    0.007    0.000 {method 'acquire' of '_thread.lock' objects}
      667    0.007    0.000    0.007    0.000 {built-in method numpy.array}
     6584    0.007    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:210(_pop_message)
     6584    0.007    0.000    0.055    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:451(items)
    13356    0.007    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:184(is_array_like)
    20999    0.007    0.000    0.007    0.000 {method '_checkClosed' of '_io._IOBase' objects}
    32552    0.007    0.000    0.007    0.000 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:506(<genexpr>)
     6584    0.007    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:706(<setcomp>)
     6564    0.007    0.000    0.046    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:538(_can_hold_na)
      188    0.007    0.000    0.007    0.000 {method 'astype' of 'numpy.ndarray' objects}
    53192    0.007    0.000    0.007    0.000 {method 'keys' of 'dict' objects}
     6721    0.007    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:103(allowed_gai_family)
    13484    0.007    0.000    0.109    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7004(ensure_index)
    32920    0.007    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:434(isclosed)
    13168    0.007    0.000    0.007    0.000 {method 'read' of '_io.StringIO' objects}
     6584    0.006    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:377(_check_close)
     6830    0.006    0.000    0.006    0.000 {method 'update' of 'dict' objects}
    19818    0.006    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:44(_debug)
     7096    0.006    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:575(dtype)
     6819    0.006    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:834(_reset_identity)
     6650    0.006    0.000    0.009    0.000 <string>:1(__new__)
    13442    0.006    0.000    0.006    0.000 {method 'split' of 'bytes' objects}
     6721    0.006    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1183(_validate_method)
13292/13221    0.006    0.000    0.022    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\abc.py:100(__subclasscheck__)
    19848    0.006    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1806(_block)
    12721    0.006    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:251(_get)
     6650    0.006    0.000    0.006    0.000 {method 'sort' of 'list' objects}
     6627    0.006    0.000    0.052    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:406(_close_conn)
    26102    0.006    0.000    0.006    0.000 {method '__enter__' of '_thread.lock' objects}
       21    0.006    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:304(<listcomp>)
     6650    0.006    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\_internal_utils.py:38(unicode_is_ascii)
     6661    0.005    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:533(is_string_or_object_np_dtype)
     6585    0.005    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_validators.py:218(validate_bool_kwarg)
     6564    0.005    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2241(external_values)
       20    0.005    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:206(<listcomp>)
       19    0.005    0.000    0.005    0.000 {pandas._libs.missing.isnaobj2d}
    13168    0.005    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:181(is_multipart)
    13321    0.005    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:248(_put)
    13168    0.005    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:110(__init__)
     6627    0.005    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:423(flush)
     6941    0.005    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1747(pandas_dtype)
    13168    0.005    0.000    0.005    0.000 {method 'extend' of 'collections.deque' objects}
     6698    0.005    0.000    0.005    0.000 {method 'rstrip' of 'bytes' objects}
    13168    0.005    0.000    0.005    0.000 {method 'write' of '_io.BytesIO' objects}
     6735    0.005    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:346(apply_if_callable)
    19324    0.005    0.000    0.005    0.000 {method 'pop' of 'list' objects}
    59256    0.005    0.000    0.005    0.000 {method 'popleft' of 'collections.deque' objects}
    13485    0.005    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:126(resolve_default_timeout)
     6584    0.005    0.000    0.005    0.000 {method 'write' of '_io.StringIO' objects}
       57    0.005    0.000    0.005    0.000 {method 'close' of 'pandas._libs.parsers.TextReader' objects}
     6581    0.005    0.000    0.005    0.000 {built-in method _codecs.utf_8_encode}
     6650    0.005    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:91(merge_hooks)
    13362    0.005    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:630(_translate_key)
    13300    0.005    0.000    0.023    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:701(__len__)
     6721    0.004    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1192(_validate_path)
     6584    0.004    0.000    0.004    0.000 {method 'getvalue' of '_io.BytesIO' objects}
       40    0.004    0.000    0.004    0.000 {built-in method pandas._libs.missing.isnaobj}
     6584    0.004    0.000    0.079    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:724(<listcomp>)
     6585    0.004    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:427(__iter__)
       19    0.004    0.000    0.004    0.000 {pandas._libs.lib.dicts_to_array}
     6584    0.004    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:17(__init__)
     6584    0.004    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:22(dispatch_hook)
    13168    0.004    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:343(<genexpr>)
     6650    0.004    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1287(_cookies_for_request)
    13300    0.004    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:743(set_environ)
       40    0.004    0.000    0.404    0.010 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1951(create_block_manager_from_column_arrays)
     6721    0.004    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\request.py:134(set_file_position)
     6481    0.004    0.000    0.005    0.000 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:509(<listcomp>)
    13365    0.004    0.000    0.004    0.000 {pandas._libs.lib.is_integer}
     7096    0.004    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1851(dtype)
     6627    0.004    0.000    0.004    0.000 {built-in method sys.audit}
    13234    0.004    0.000    0.004    0.000 {built-in method time.perf_counter}
    13579    0.004    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:227(connect_timeout)
    26336    0.004    0.000    0.004    0.000 {method 'seek' of '_io.StringIO' objects}
    26336    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:248(__len__)
    26336    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:117(info)
       40    0.003    0.000   15.077    0.377 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:215(read)
    13168    0.003    0.000    0.003    0.000 {method 'truncate' of '_io.StringIO' objects}
     6721    0.003    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1179(_encode_request)
     6564    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1847(index)
     6584    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_decorators.py:214(_format_argument_list)
       38    0.003    0.000    0.003    0.000 {method 'close' of '_io.TextIOWrapper' objects}
    26600    0.003    0.000    0.003    0.000 {method 'items' of 'collections.OrderedDict' objects}
     6581    0.003    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:958(<genexpr>)
    13168    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:465(_decode)
    13300    0.003    0.000    0.003    0.000 {method 'update' of 'collections.OrderedDict' objects}
    26102    0.003    0.000    0.003    0.000 {method '__exit__' of '_thread.lock' objects}
     6832    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:328(attrs)
     6900    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:354(dtype)
       66    0.003    0.000    0.026    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:177(__init__)
  202/101    0.003    0.000    0.096    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:397(__new__)
    13183    0.003    0.000    0.003    0.000 {method 'values' of 'dict' objects}
    13664    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:349(flags)
    13225    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:523(_constructor)
     6721    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\util.py:7(to_bytes)
     6721    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\proxy.py:11(connection_requires_http_tunnel)
    13267    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:331(_is_all_dates)
     1083    0.002    0.000    0.002    0.000 {built-in method numpy.empty}
     6606    0.002    0.000    0.002    0.000 {pandas._libs.lib.is_bool}
     6826    0.002    0.000    0.002    0.000 {built-in method builtins.callable}
     6762    0.002    0.000    0.002    0.000 {pandas._libs.lib.is_iterator}
     1837    0.002    0.000    0.048    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:319(decode)
    13168    0.002    0.000    0.002    0.000 {method 'end' of 're.Match' objects}
     6627    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:295(is_closed)
    13168    0.002    0.000    0.002    0.000 {method 'append' of 'collections.deque' objects}
     6832    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:51(allows_duplicate_labels)
       21    0.002    0.000    0.049    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:463(_init_dict)
     6584    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:79(<listcomp>)
     6650    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:216(<genexpr>)
     6650    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:87(get_new_headers)
     6721    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:339(_validate_conn)
    13168    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:125(__iter__)
     6721    0.002    0.000    0.002    0.000 {built-in method time.monotonic}
     6650    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:578(add_headers)
      289    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:202(__init__)
     6739    0.002    0.000    0.002    0.000 {pandas._libs.lib.is_float}
     6584    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:581(iter_slices)
     6581    0.002    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:31(encode)
      136    0.002    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:428(increment)
      438    0.001    0.000    0.001    0.000 {method 'reduce' of 'numpy.ufunc' objects}
       19    0.001    0.000    0.005    0.000 {pandas._libs.lib.fast_unique_multiple_list_gen}
      154    0.001    0.000    2.157    0.014 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:587(__init__)
       60    0.001    0.000    0.017    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:1174(_close_pool_connections)
       60    0.001    0.000    0.001    0.000 {pandas._libs.ops.scalar_compare}
       65    0.001    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:390(__init__)
      195    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:240(init_poolmanager)
      195    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:199(__init__)
      100    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numeric.py:2402(array_equal)
     6537    0.001    0.000    0.001    0.000 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:552(<listcomp>)
      195    0.001    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:202(__init__)
      323    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:120(_take_nd_ndarray)
      323    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1579(maybe_cast_to_datetime)
     6627    0.001    0.000    0.001    0.000 {function HTTPResponse.flush at 0x000002206ECDEDC0}
       76    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexers\utils.py:249(maybe_convert_indices)
       94    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\exceptions.py:142(__init__)
      101    0.001    0.000    0.099    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:672(_with_infer)
      137    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:816(__init__)
      260    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:799(mount)
      137    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:238(_new_conn)
       94    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:247(new)
      424    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1466(maybe_infer_to_datetimelike)
       57    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2105(_merge_blocks)
      152    0.001    0.000    0.018    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3463(__getitem__)
       19    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:710(_slice_take_blocks_ax0)
       19    0.001    0.000    0.135    0.007 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:296(_save_body)
      323    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:554(_take_preprocess_indexer_and_fill_value)
      198    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:222(__init__)
       19    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:965(<listcomp>)
       39    0.001    0.000    0.055    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:601(get_handle)
      285    0.001    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1116(take_nd)
       65    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:900(default_headers)
      137    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:134(__init__)
      462    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numeric.py:289(full)
      178    0.001    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:22(require)
      154    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5192(equals)
       76    0.001    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:5613(_cmp_method)
     1837    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:331(getstate)
       76    0.001    0.000    0.001    0.000 {pandas._libs.algos.take_2d_axis1_object_object}
       66    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\weakref.py:517(__init__)
       57    0.000    0.000    0.001    0.000 {method '_rebuild_blknos_and_blklocs' of 'pandas._libs.internals.BlockManager' objects}
       66    0.000    0.000    0.027    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:229(_new_pool)
       71    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:289(get_backoff_time)
      195    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:84(__init__)
       66    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:33(__init__)
      101    0.000    0.000    0.075    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7123(_maybe_cast_data_without_dtype)
      323    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:57(take_nd)
      101    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:130(filterwarnings)
      194    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:32(seterr)
       76    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\array_ops.py:227(comparison_op)
      139    0.000    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:229(asarray_tuplesafe)
      195    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:140(__init__)
     1156    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:242(<genexpr>)
       95    0.000    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:634(reindex_indexer)
       76    0.000    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3609(take)
       40    0.000    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4719(reindex)
       40    0.000    0.000    1.947    0.049 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:102(arrays_to_mgr)
      424    0.000    0.000    0.000    0.000 {pandas._libs.lib.infer_datetimelike_array}
      182    0.000    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1448(warning)
      110    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:91(_path_join)
       76    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1106(take)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:102(__setitem__)
      386    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:581(is_dtype_equal)
       39    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:251(_get_filepath_or_buffer)
       57    0.000    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2153(to_native_types)
      135    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:391(array_equivalent)
       38    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2240(is_unique)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:83(__init__)
      101    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:554(_dtype_to_subclass)
      323    0.000    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1067(convert)
      194    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:131(geterr)
       19    0.000    0.000    0.155    0.008 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3376(to_csv)
       65    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:270(from_int)
       76    0.000    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:872(take)
      118    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:289(_compile)
      213    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:344(_name_get)
      462    0.000    0.000    0.001    0.000 <__array_function__ internals>:177(copyto)
       76    0.000    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3530(_getitem_bool_array)
       91    0.000    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2041(error)
      157    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:166(_consolidate_key)
      137    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5632(_protect_consolidate)
       21    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:112(__new__)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1154(_process_date_conversion)
      323    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1038(iget_values)
       20    0.000    0.000    0.138    0.007 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1191(_make_engine)
       19    0.000    0.000    0.134    0.007 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:306(_save_chunk)
       44    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\exceptions.py:34(__init__)
      323    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:389(is_timedelta64_dtype)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1017(_clean_options)
      576    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:333(_iterencode_dict)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:939(_get_options_with_defaults)
       38    0.000    0.000    0.000    0.000 {pandas._libs.lib.infer_dtype}
      285    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:267(make_block_same_class)
       19    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:560(__init__)
      101    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:458(__enter__)
      260    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:805(<listcomp>)
       91    0.000    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1465(error)
      152    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:53(shape)
       66    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:81(__init__)
       65    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\exceptions.py:17(__init__)
      404    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1483(is_ea_or_datetimelike_dtype)
       76    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:2988(_construct_result)
       60    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:486(validate_integer)
       19    0.000    0.000    0.000    0.000 {method 'get_slice' of 'pandas._libs.internals.BlockManager' objects}
       20    0.000    0.000    0.139    0.007 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:885(__init__)
       71    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:525(__repr__)
       42    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:360(_raise_timeout)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:167(_simple_new)
       19    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis0_object_object}
       94    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:409(is_exhausted)
       74    0.000    0.000    0.000    0.000 {built-in method _thread.allocate_lock}
       20    0.000    0.000    0.140    0.007 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:539(_read)
       19    0.000    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:971(_finalize_columns_and_data)
       19    0.000    0.000    0.000    0.000 {method 'get_indexer' of 'pandas._libs.index.IndexEngine' objects}
       63    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:139(_ensure_array)
      266    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:468(maybe_promote)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1689(<listcomp>)
      101    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:181(_add_filter)
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1109(_is_binary_mode)
       19    0.000    0.000    0.014    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:934(_list_of_dict_to_arrays)
       23    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\exceptions.py:96(__init__)
       63    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1289(_is_potential_multi_index)
      211    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:560(_get_axis)
       38    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_1d_int64_int64}
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:845(_engine)
       52    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis0_int64_int64}
       76    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:689(<listcomp>)
      111    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2059(warning)
       76    0.000    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3708(_take_with_is_copy)
      137    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5650(f)
      323    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:326(_get_take_nd_function)
      178    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:109(<setcomp>)
       19    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5009(_reindex_with_indexers)
      203    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:1017(_handle_fromlist)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1026(iget)
      281    0.000    0.000    0.001    0.000 {method 'any' of 'numpy.ndarray' objects}
       63    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1987(maybe_cast_to_integer_array)
       19    0.000    0.000    0.012    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:253(apply)
      576    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:413(_iterencode)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\base.py:744(__iter__)
       19    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:3714(get_indexer)
       21    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:4435(_reduce)
        3    0.000    0.000    0.000    0.000 {built-in method marshal.loads}
      116    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:572(na_value_for_dtype)
       19    0.000    0.000    0.154    0.008 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:1131(to_csv)
       76    0.000    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\common.py:55(new_method)
       76    0.000    0.000    0.000    0.000 {method 'take' of 'numpy.ndarray' objects}
       59    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1683(_consolidate_check)
        3    0.000    0.000    0.000    0.000 {built-in method io.open_code}
       22    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap_external>:1498(find_spec)
       39    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:497(infer_compression)
       19    0.000    0.000    0.152    0.008 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:236(save)
       60    0.000    0.000    0.017    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\weakref.py:561(__call__)
      114    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:970(from_blocks)
       65    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:891(default_user_agent)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1551(as_array)
       97    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:429(__enter__)
      136    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:365(_is_connection_error)
      102    0.000    0.000    0.000    0.000 {method 'remove' of 'list' objects}
      101    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:477(__exit__)
       46    0.000    0.000    0.000    0.000 {method 'argsort' of 'numpy.ndarray' objects}
      137    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1200(_validate_host)
       60    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\array_ops.py:58(comp_method_OBJECT_ARRAY)
       21    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:10400(_logical_func)
      137    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5646(_consolidate_inplace)
       19    0.000    0.000    0.000    0.000 {method 'writerow' of '_csv.writer' objects}
       57    0.000    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:631(to_native_types)
       44    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:94(__str__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:64(parse_parts)
      194    0.000    0.000    0.000    0.000 {built-in method numpy.seterrobj}
       22    0.000    0.000    0.000    0.000 {built-in method builtins.locals}
       76    0.000    0.000    0.000    0.000 {method 'nonzero' of 'numpy.ndarray' objects}
      128    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\types.py:171(__get__)
       19    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2088(_consolidate)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:172(from_float)
       76    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3923(_get_item_cache)
       43    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_int64_int64}
      156    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1675(is_consolidated)
       38    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3411(_ixs)
      823    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4834(_values)
      101    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:437(__init__)
      361    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:358(iget)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:77(join)
       38    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1451(_format_native_types)
       19    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:50(__init__)
      261    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_request_methods.py:51(__init__)
       19    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4123(reindex)
       59    0.000    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:268(_isna_string_dtype)
       19    0.000    0.000    0.024    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:505(nested_data_to_arrays)
       62    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:262(is_dict_like)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:1149(_normalize_host)
       76    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4987(__contains__)
       19    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:338(_format_native_types)
       76    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\algorithms.py:1356(take)
       57    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:103(close)
       44    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\exceptions.py:46(__init__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:390(is_dataclass)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:176(_validate_parse_dates_presence)
       20    0.000    0.000    0.017    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:194(_set_noconvert_columns)
      314    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2093(<lambda>)
       19    0.000    0.000    0.024    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:798(to_arrays)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:223(encoded_labels)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\nanops.py:257(_get_values)
       21    0.000    0.000    0.046    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:825(create_series_with_explicit_dtype)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:514(_construct_axes_from_arguments)
       80    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4952(<genexpr>)
      456    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:55(<genexpr>)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:495(_array_equivalent_object)
      281    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_methods.py:54(_any)
       38    0.000    0.000    0.000    0.000 {built-in method numpy.arange}
       76    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1042(is_numeric_v_string_like)
       80    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_all_arraylike}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1427(_refine_defaults_read)
       44    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_float64_float64}
      152    0.000    0.000    0.000    0.000 {method 'copy' of 'numpy.ndarray' objects}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1034(get_data)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3906(_box_col_values)
      100    0.000    0.000    0.001    0.000 {method 'all' of 'numpy.ndarray' objects}
      213    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:330(_name_includes_bit_suffix)
       76    0.000    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\arraylike.py:38(__eq__)
       19    0.000    0.000    0.010    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:4788(reindex)
      117    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:155(_expand_user)
      164    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:732(is_signed_integer_dtype)
      136    0.000    0.000    0.000    0.000 {built-in method sys.exc_info}
       39    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:127(is_url)
       20    0.000    0.000    0.140    0.007 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:584(read_csv)
      118    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:45(__len__)
       19    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3908(_slice)
      154    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:803(is_)
      152    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:347(dtype)
      101    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:786(is_unsigned_integer_dtype)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:195(stringify_path)
      137    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:618(consolidate)
      388    0.000    0.000    0.000    0.000 {built-in method numpy.geterrobj}
      456    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:156(blknos)
       21    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:4655(reindex)
       19    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:556(check_parent_directory)
      114    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2121(extend_blocks)
       46    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:222(vstack)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:189(_data)
       19    0.000    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1073(<listcomp>)
       97    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:434(__exit__)
        3    0.000    0.000    0.000    0.000 {method 'read' of '_io.BufferedReader' objects}
       71    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:343(_sleep_backoff)
       40    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1693(_consolidate_inplace)
      100    0.000    0.000    0.002    0.000 <__array_function__ internals>:177(array_equal)
      114    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:1413(__len__)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:610(_set_noconvert_dtype_columns)
      114    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:238(fill_value)
       26    0.000    0.000    0.000    0.000 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:1088(<dictcomp>)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:168(_number_format)
        7    0.000    0.000    0.000    0.000 {built-in method builtins.__build_class__}
      141    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:297(<lambda>)
      137    0.000    0.000    0.000    0.000 {built-in method _socket.getdefaulttimeout}
       38    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:934(close)
       40    0.000    0.000   17.283    0.432 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1184(__next__)
      500    0.000    0.000    0.000    0.000 {pandas._libs.algos.ensure_object}
      118    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:250(compile)
       46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:81(atleast_2d)
        1    0.000    0.000    0.000    0.000 {built-in method nt.listdir}
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:259(_make_iterencode)
       19    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:954(__getitem__)
       76    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\common.py:75(get_op_result_name)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\util.py:31(reraise)
       86    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numerictypes.py:356(issubdtype)
       46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2116(<listcomp>)
       40    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5279(identical)
       19    0.000    0.000    0.000    0.000 {built-in method _csv.writer}
      114    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:566(_get_block_manager_axis)
       39    0.000    0.000    0.001    0.000 {built-in method _codecs.lookup}
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:678(_from_parts)
       19    0.000    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:4652(_reindex_columns)
       19    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:1527(_get_slice_axis)
       19    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:312(<listcomp>)
      172    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numerictypes.py:282(issubclass_)
       19    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:144(_initialize_columns)
       79    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3925(_set_is_copy)
      212    0.000    0.000    0.000    0.000 {method 'ravel' of 'numpy.ndarray' objects}
       19    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1039(__new__)
      456    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:172(blklocs)
       71    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:349(sleep)
      456    0.000    0.000    0.000    0.000 {pandas._libs.algos.ensure_platform_int}
       38    0.000    0.000    0.000    0.000 {pandas._libs.lib.array_equivalent_object}
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:27(__init__)
       63    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:199(_ensure_dtype)
      462    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:1071(copyto)
       21    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:10817(values)
       76    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2323(convert_to_index_sliceable)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:493(_parse)
      152    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:522(equals)
      187    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:288(<genexpr>)
       32    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_bool_bool}
       59    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\missing.py:911(clean_reindex_fill_method)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:812(_do_date_conversions)
       16    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\array_ops.py:134(_na_arithmetic_op)
      126    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1314(<genexpr>)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:222(get_values)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_validators.py:258(validate_axis_style_args)
       48    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:853(quote_plus)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:252(make_block)
      137    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:183(host)
      128    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:753(value)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:3585(get_loc)
       46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2032(new_block_2d)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:658(_parse_args)
       94    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:411(<listcomp>)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:383(_make_index)
       38    0.000    0.000    0.000    0.000 {method 'get_loc' of 'pandas._libs.index.IndexEngine' objects}
       97    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:425(__init__)
      498    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\base.py:326(ndim)
       19    0.000    0.000    0.136    0.007 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:263(_save)
      137    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:862(_get_hostport)
       37    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1091(is_datetimelike_v_numeric)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:208(write_cols)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6298(_maybe_cast_indexer)
     12/4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:71(_compile)
       19    0.000    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:4615(_reindex_axes)
      100    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_methods.py:60(_all)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6018(_should_compare)
       21    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:10458(any)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:718(__str__)
       46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\fromnumeric.py:1012(argsort)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:459(is_interval_dtype)
       39    0.000    0.000    0.000    0.000 <string>:2(__init__)
      213    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:24(_kind_name)
      180    0.000    0.000    0.000    0.000 {built-in method _json.encode_basestring}
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:287(maybe_iterable_to_list)
       84    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:188(_validate_dtype)
       39    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:239(is_fsspec_url)
      304    0.000    0.000    0.000    0.000 {pandas._libs.lib.item_from_zerodim}
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:3794(_get_indexer)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:944(_getitem_slice)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:140(has_mi_columns)
       21    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:7235(isna)
       57    0.000    0.000    0.000    0.000 {method 'max' of 'numpy.ndarray' objects}
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\dtypes.py:1206(is_dtype)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:115(ensure_python_int)
       19    0.000    0.000    0.012    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:468(to_native_types)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:233(mgr_to_mgr)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4997(_needs_reindex_multi)
      303    0.000    0.000    0.000    0.000 {built-in method _warnings._filters_mutated}
       60    0.000    0.000    0.000    0.000 {method 'reshape' of 'numpy.ndarray' objects}
       16    0.000    0.000    0.000    0.000 {built-in method _operator.eq}
       80    0.000    0.000    0.000    0.000 {method 'view' of 'numpy.ndarray' objects}
       43    0.000    0.000    0.000    0.000 {method 'flush' of '_io.BufferedReader' objects}
       37    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1496(is_complex_dtype)
        4    0.000    0.000    0.002    0.001 <frozen importlib._bootstrap>:890(_find_spec)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:309(__init__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:146(splitroot)
       21    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\nanops.py:488(nanany)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\api.py:331(default_index)
       19    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1417(is_dir)
       19    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:268(_save_header)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:3837(_check_indexing_method)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:441(_validate_dtype)
       76    0.000    0.000    0.000    0.000 {built-in method sys.intern}
      110    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:114(<listcomp>)
      118    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:743(__len__)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:200(_has_aliases)
       76    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\dispatch.py:11(should_extension_dispatch)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:475(get_adjustment)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:350(wrapper)
      114    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:160(cast_scalar_indexer)
      177    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:945(dtype)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4860(_get_engine_target)
        7    0.000    0.000    0.000    0.000 {built-in method nt.get_terminal_size}
       89    0.000    0.000    0.000    0.000 {method 'format' of 'str' objects}
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4244(_maybe_preserve_names)
       11    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis0_float64_float64}
       57    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:187(close)
      173    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:916(__init__)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:1238(_set_as_cached)
      101    0.000    0.000    0.000    0.000 {method 'insert' of 'list' objects}
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5919(_index_as_unique)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2280(is_boolean)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:534(treat_as_nested)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:201(all_none)
      222    0.000    0.000    0.000    0.000 {built-in method numpy.asanyarray}
      175    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1658(name)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:242(_init)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5931(_maybe_promote)
       97    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\compat\numpy\function.py:49(__call__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2660(_isnan)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:919(__getitem__)
       19    0.000    0.000    0.010    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_decorators.py:322(wrapper)
       46    0.000    0.000    0.001    0.000 <__array_function__ internals>:177(vstack)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:373(_is_read_error)
       59    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:98(is_file_like)
       39    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1055(_maybe_memory_map)
        4    0.000    0.000    0.002    0.001 <frozen importlib._bootstrap_external>:1367(_get_spec)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:130(iloc)
      137    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:222(items)
       46    0.000    0.000    0.000    0.000 <__array_function__ internals>:177(argsort)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\copy.py:66(copy)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4013(_validate_positional_slice)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:785(truncate)
       46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\fromnumeric.py:51(_wrapfunc)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2151(_preprocess_slice_or_indexer)
        8    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis0_bool_bool}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1318(_validate_parse_dates_arg)
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:914(get_code)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:554(_get_axis_name)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:204(_need_to_save_header)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:543(<dictcomp>)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:890(__array__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:121(_get_index_label_from_obj)
       39    0.000    0.000    0.000    0.000 {built-in method _codecs.lookup_error}
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:945(parent)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1337(add_argument)
       16    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\missing.py:138(dispatch_fill_zeros)
      114    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:578(_constructor)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_methods.py:38(_amax)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2650(_na_value)
       21    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:10880(any)
       21    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:5276(isna)
       46    0.000    0.000    0.000    0.000 <__array_function__ internals>:177(atleast_2d)
        4    0.000    0.000    0.004    0.001 <frozen importlib._bootstrap>:986(_find_and_load)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2604(inferred_type)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:679(_initialize_justify)
       46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:218(_vhstack_dispatcher)
       21    0.000    0.000    0.000    0.000 {method 'transpose' of 'numpy.ndarray' objects}
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:288(_get_take_nd_function_cached)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2440(is_object)
       19    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:1490(_getitem_axis)
       19    0.000    0.000    0.000    0.000 {pandas._libs.internals.get_blkno_placements}
      146    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:244(mgr_locs)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:241(start)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:993(_validate_or_indexify_columns)
       59    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\missing.py:107(clean_fill_method)
       39    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:462(get_compression_method)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:371(_maybe_make_multi_index_columns)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:832(_check_data_length)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:217(is_single_block)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6313(_validate_indexer)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\dataclasses.py:1045(is_dataclass)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:1(<module>)
       19    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:123(__exit__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:728(__fspath__)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:456(<genexpr>)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\shutil.py:1312(get_terminal_size)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:761(_is_in_terminal)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1049(_init)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1372(_clear_cache)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:179(data_index)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:701(_format_parsed_parts)
       19    0.000    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1051(_convert_object_array)
       19    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1193(stat)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:423(__init__)
       16    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\computation\expressions.py:223(evaluate)
       46    0.000    0.000    0.000    0.000 {built-in method pandas._libs.writers.word_len}
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:728(_calc_max_rows_fitted)
     12/4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:174(getwidth)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:222(count_not_none)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2685(isna)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:161(__init__)
       76    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1132(_maybe_disallow_fill)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:717(_calc_max_cols_fitted)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1255(__init__)
      101    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:229(disallow_kwargs)
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:354(cache_from_source)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:186(__init__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:112(_initialize_index_label)
      113    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:222(_verbose_message)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2352(is_floating)
       19    0.000    0.000    0.000    0.000 {method 'fill' of 'numpy.ndarray' objects}
       92    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:164(__getitem__)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:157(_get_module_lock)
       71    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2157(handle)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1352(_clean_na_values)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:130(_get_index_label_flat)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\nanops.py:213(_maybe_get_mask)
       61    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1340(is_index_col)
      100    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numeric.py:2398(_array_equal_dispatcher)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5292(<genexpr>)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:685(_initialize_columns)
       45    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:399(_checknetloc)
       46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2125(<listcomp>)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:536(_compile_info)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\nanops.py:346(_na_ok_dtype)
        4    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap>:956(_find_and_load_unlocked)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:759(compile)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1006(_check_file_or_buffer)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:691(_from_parsed_parts)
       47    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:233(__next)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2632(_is_multi)
       28    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap_external>:135(_path_stat)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:99(_can_hold_na)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:163(_initialize_chunksize)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:435(_parse_sub)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1478(_get_optional_kwargs)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4240(_wrap_reindex_result)
       46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:207(_arrays_for_stack_dispatcher)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:661(_initialize_sparsify)
       16    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\computation\expressions.py:63(_evaluate_standard)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:391(_splitnetloc)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:240(_has_complex_date_col)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:959(<genexpr>)
        2    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\__init__.py:70(search_function)
       24    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1330(_path_importer_cache)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:515(_maybe_promote)
       37    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1308(register)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:477(_init_module_attrs)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:92(na_rep)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:408(ensure_dtype_objs)
        4    0.000    0.000    0.000    0.000 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:1424(<listcomp>)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:689(spec_from_file_location)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:997(raise_for_status)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:104(header)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:540(_ensure_array)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7063(ensure_has_len)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:264(stop)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:937(parse)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2473(need_slice)
       22    0.000    0.000    0.000    0.000 {built-in method builtins.iter}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:260(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1638(__init__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7185(unpack_nested_dtype)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:287(step)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:174(validate_header_arg)
       36    0.000    0.000    0.000    0.000 {built-in method builtins.id}
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap>:650(_load_unlocked)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1062(_make_date_converter)
       61    0.000    0.000    0.000    0.000 {method 'reverse' of 'list' objects}
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:518(<genexpr>)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:205(<genexpr>)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:193(nlevels)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:294(is_named_tuple)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:226(<genexpr>)
       20    0.000    0.000    0.000    0.000 {built-in method sys.getfilesystemencoding}
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:586(_format_args)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1398(_add_action)
       39    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:172(append)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1703(_validate_skipfooter)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:78(acquire)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:1019(__init__)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:58(__init__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4106(_validate_can_reindex)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:629(is_truncated_horizontally)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:693(_initialize_colspace)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:47(CSVFormatter)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:692(_constructor)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1605(_extract_dialect)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:220(<setcomp>)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:538(find)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:108(index)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:254(get)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:920(_validate_usecols_arg)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:100(decimal)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:626(is_valid_na_for_dtype)
       46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\fromnumeric.py:1008(_argsort_dispatcher)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:218(_acquireLock)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:212(_expand_lang)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:103(release)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:638(_compile_bytecode)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:276(_optimize_charset)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\__init__.py:43(normalize_encoding)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:183(_constructor)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:96(float_format)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:134(_initialize_quotechar)
       19    0.000    0.000    0.000    0.000 {built-in method _stat.S_ISDIR}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\_distutils_hack\__init__.py:102(find_spec)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:809(__init__)
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:842(exec_module)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:633(is_truncated_vertically)
       22    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:64(_relax_case)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6307(_maybe_cast_listlike_indexer)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:579(translation)
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:127(_path_split)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1809(_parse_known_args)
        1    0.000    0.000    0.001    0.001 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:89(parse_arguments)
        1    0.000    0.000    0.000    0.000 D:\Project\性能优化\ECG-Analysis\apps\api\tests\api_test.py:1419(<listcomp>)
        2    0.000    0.000    0.001    0.000 {built-in method builtins.__import__}
        9    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:79(_unpack_uint32)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:938(__and__)
       46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:77(_atleast_2d_dispatcher)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1774(parse_known_args)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:598(_code)
        4    0.000    0.000    0.002    0.001 <frozen importlib._bootstrap_external>:1399(find_spec)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:167(_path_isabs)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:515(_validate_names)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:553(_classify_pyc)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:666(_initialize_formatters)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:147(__enter__)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:549(module_from_spec)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1493(_get_spec)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:224(__init__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:355(_escape)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:1(<module>)
       45    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:392(__subclasshook__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1567(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\idna.py:300(getregentry)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:160(__len__)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\compat\_optional.py:87(import_optional_dependency)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2480(_get_formatter)
       21    0.000    0.000    0.000    0.000 {method 'with_traceback' of 'BaseException' objects}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:268(__iter__)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\nanops.py:191(_get_fill_value)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:282(<dictcomp>)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:232(<listcomp>)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1589(_add_action)
       21    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:150(is_consolidated)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:484(_get_cached)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:94(__new__)
       17    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:863(__enter__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:227(_releaseLock)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:176(cb)
       17    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:867(__exit__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:461(_get_literal_prefix)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:249(_compile_charset)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1675(getEffectiveLevel)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:120(__enter__)
        8    0.000    0.000    0.000    0.000 {built-in method builtins.setattr}
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1906(nlevels)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6036(_is_comparable_dtype)
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:376(cached)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:423(_simple)
       18    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:129(<genexpr>)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:111(__init__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:286(tell)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1276(disable)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\copy.py:107(_copy_immutable)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:1(<module>)
        2    0.000    0.000    0.000    0.000 {built-in method _imp.is_builtin}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:151(__exit__)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:342(__init__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:747(_adjust_max_rows)
        3    0.000    0.000    0.000    0.000 {built-in method builtins.exec}
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1514(_pop_action_class)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:145(_path_is_mode_type)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:72(_check_methods)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:570(_metavar_formatter)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:660(dgettext)
       25    0.000    0.000    0.000    0.000 {built-in method _imp.release_lock}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\locale.py:384(normalize)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:586(_validate_timestamp_pyc)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:851(__init__)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1075(path_stats)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:82(_have_working_poll)
       25    0.000    0.000    0.000    0.000 {built-in method _imp.acquire_lock}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:35(_new_module)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:516(_check_name_wrapper)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\importlib\__init__.py:109(import_module)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:921(fix_flags)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1312(_registry_get)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1518(_get_handler)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:725(find_spec)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1747(_add_action)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1417(setLevel)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:154(_path_isfile)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1954(consume_positionals)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:800(find_spec)
        1    0.000    0.000    0.001    0.001 <frozen importlib._bootstrap>:1002(_gcd_import)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:76(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:95(wait_for_socket)
        4    0.000    0.000    0.000    0.000 {built-in method _sre.compile}
       21    0.000    0.000    0.000    0.000 {built-in method _sre.unicode_iscased}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:143(__init__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:595(isstring)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:579(format)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:121(getregentry)
        3    0.000    0.000    0.000    0.000 {built-in method nt._path_splitroot}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:84(clear_cache)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:202(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1388(add_argument_group)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:507(_maybe_promote_cached)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:492(_get_charset_prefix)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1004(__init__)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:453(_get_iscased)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:454(ensure_dtype_can_hold_na)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1130(_get_binary_io_classes)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:81(groups)
        7    0.000    0.000    0.000    0.000 {method 'fileno' of '_io.TextIOWrapper' objects}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:211(_call_with_frames_removed)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:249(match)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1527(_check_conflict)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:649(_ensure_dtype_type)
       19    0.000    0.000    0.000    0.000 {built-in method _sre.unicode_tolower}
        9    0.000    0.000    0.000    0.000 {built-in method from_bytes}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:881(_find_spec_legacy)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:909(__init__)
       13    0.000    0.000    0.000    0.000 {method 'isalnum' of 'str' objects}
        4    0.000    0.000    0.000    0.000 {built-in method _imp.is_frozen}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:937(_sanity_check)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:193(_checkLevel)
        4    0.000    0.000    0.000    0.000 {method 'find' of 'bytearray' objects}
        6    0.000    0.000    0.000    0.000 {method 'isascii' of 'str' objects}
        3    0.000    0.000    0.000    0.000 {built-in method _imp._fix_co_filename}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:367(__subclasshook__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:432(_generate_overlap_table)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:168(__setitem__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:886(__init__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:735(gettext)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1031(__init__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:302(__subclasshook__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:406(__subclasshook__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\six.py:184(find_module)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:574(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:26(IncrementalEncoder)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:162(__delitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1767(parse_args)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2106(_match_arguments_partial)
        1    0.000    0.000    0.000    0.000 <__array_function__ internals>:177(can_cast)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1759(_get_positional_actions)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:22(netrc)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:49(IncrementalDecoder)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:839(create_module)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2018(getLogger)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:10(NetrcParseError)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:397(has_location)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1029(get_filename)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1760(<listcomp>)
        1    0.000    0.000    0.000    0.000 {built-in method atexit.register}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\_distutils_hack\__init__.py:109(<lambda>)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:604(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:1246(url)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1240(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:85(StreamWriter)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:97(StreamReader)
        1    0.000    0.000    0.000    0.000 {method 'disable' of '_lsprof.Profiler' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:498(can_cast)


