import numpy as np
from scipy import signal # Added for baseline_variation
from scipy.fft import fft, fftfreq # Added for high_freq_energy
from apps.utils.logger_helper import Logger

def evaluate_signal_quality(ecg_signal, sampling_rate, r_peaks):

    try:
        if ecg_signal is None or len(ecg_signal) == 0 or len(r_peaks) == 0:
            return 0.5, {'error': '无有效信号或R峰'}

        quality_factors = {}

        min_length = 5 * sampling_rate  # 至少5秒
        length_score = min(1.0, len(ecg_signal) / min_length)
        quality_factors['length'] = length_score

        min_beats = 6
        beats_score = min(1.0, len(r_peaks) / min_beats)
        quality_factors['beats'] = beats_score

        # Original baseline calculation - kept for reference or if new ones are problematic
        # segment_size = int(2 * sampling_rate)  # 2秒段
        # n_segments = len(ecg_signal) // segment_size
        # baseline_scores_orig = []
        # for i in range(n_segments):
        #     start = i * segment_size
        #     end = start + segment_size
        #     segment = ecg_signal[start:end]
        #     amplitude_range_seg = np.ptp(segment)
        #     std_dev_seg = np.std(segment)
        #     if std_dev_seg > 0:
        #         segment_score = min(1.0, std_dev_seg / (amplitude_range_seg + 1e-6))
        #         baseline_scores_orig.append(segment_score)
        # baseline_score_orig = np.mean(baseline_scores_orig) if baseline_scores_orig else 0.5
        # quality_factors['baseline_original'] = baseline_score_orig 

        # New: High Frequency Energy Score
        raw_hf_energy = _calculate_high_freq_energy_metric(ecg_signal, sampling_rate)
        # Assuming raw_hf_energy is 0 (good) to 1 (bad), so score is 1 - energy
        hf_energy_score = 1.0 - min(1.0, raw_hf_energy / 0.35) # Normalize against typical noisy threshold from available.py
        quality_factors['hf_energy'] = max(0.0, hf_energy_score) # Ensure score is not negative

        # New: Baseline Drift Score
        raw_baseline_drift = _calculate_baseline_drift_metric(ecg_signal, sampling_rate)
        # Assuming raw_baseline_drift is std dev, higher is worse. Normalize against typical noisy threshold.
        # Max expected drift for a good signal might be low, e.g. 0.1 mV. Noisy threshold in available.py is > 0.4 mV.
        # Let's try a simple inverse relationship, scaled by a typical "bad" value.
        baseline_drift_score = 1.0 - min(1.0, raw_baseline_drift / 0.4) # Normalize against typical noisy threshold
        quality_factors['baseline_drift'] = max(0.0, baseline_drift_score) # Ensure score is not negative

        if len(r_peaks) >= 3:
            rr_intervals = np.diff(r_peaks)
            rr_mean_val = np.mean(rr_intervals)
            if rr_mean_val > 0: # Prevent division by zero
                rr_variability = np.std(rr_intervals) / rr_mean_val
                noise_score_original_method = 1.0 / (1.0 + rr_variability)  # 转换为0-1分数
            else:
                noise_score_original_method = 0.0
        else:
            noise_score_original_method = 0.5
        quality_factors['noise_rr_var'] = noise_score_original_method


        amplitude_range = np.ptp(ecg_signal)
        expected_range = 1000
        amplitude_score = min(1.0, amplitude_range / expected_range)
        quality_factors['amplitude'] = amplitude_score

        # Adjusted weights - Sum must be 1.0
        # Original: length: 0.1, beats: 0.2, baseline: 0.3, noise: 0.3, amplitude: 0.1
        # New proposal: distribute baseline and noise weights among new factors and rr_var
        weights = {
            'length': 0.1,
            'beats': 0.15,
            'hf_energy': 0.25,       # New factor
            'baseline_drift': 0.25,  # New factor
            'noise_rr_var': 0.15,    # Renamed from 'noise'
            'amplitude': 0.1
        }

        # Ensure all factors in weights are present in quality_factors, providing a default if not (e.g. 0.5)
        quality_score = sum(quality_factors.get(factor, 0.5) * weights[factor] for factor in weights)

        quality_score = max(0.0, min(1.0, quality_score))

        return quality_score, quality_factors

    except Exception as e:
        Logger().error(f"信号质量评估错误: {str(e)}")
        return 0.5, {'error': str(e)}

def get_adaptive_thresholds(quality_score, lead_type='I'):
    """
    根据信号质量和导联类型获取自适应阈值 (重构后逻辑)
    """
    try:
        from apps.analysis.arrhythmia_diagnosis.af.af_config import THRESHOLDS, LEAD_SPECIFIC, QUALITY_CONFIG

        base_thresholds = THRESHOLDS.copy()
        adaptive_thresholds = {}

        # 1. 计算质量调整因子
        # 低质量信号(quality_score低) -> quality_factor > 1 -> 阈值变高(更难诊断)
        quality_factor_multiplier = QUALITY_CONFIG.get('quality_threshold_factor', 0.15)
        quality_factor = 1.0 + (1.0 - quality_score) * quality_factor_multiplier

        # 2. 首先对所有基础阈值应用质量调整
        for key, value in base_thresholds.items():
            adaptive_thresholds[key] = value * quality_factor

        # 3. 然后，应用导联特定的阈值，并确保它们也经过质量调整
        if lead_type in LEAD_SPECIFIC:
            lead_config = LEAD_SPECIFIC[lead_type]

            # 处理导联特定的总分阈值
            if 'af_score' in lead_config:
                # I导联有特殊的调整因子
                if lead_type == 'I':
                    i_lead_factor = 1.0 + (1.0 - quality_score) * (quality_factor_multiplier * 0.7)
                    adaptive_thresholds['af_score'] = lead_config['af_score'] * i_lead_factor
                else:
                    adaptive_thresholds['af_score'] = lead_config['af_score'] * quality_factor

            # 处理导联特定的其他特征阈值
            if 'thresholds' in lead_config:
                for key, value in lead_config['thresholds'].items():
                    # 覆盖已经质量调整过的基础值
                    adaptive_thresholds[key] = value * quality_factor

        return adaptive_thresholds

    except Exception as e:
        Logger().error(f"获取自适应阈值错误: {str(e)}")
        from apps.analysis.arrhythmia_diagnosis.af.af_config import THRESHOLDS
        return THRESHOLDS

def get_adaptive_weights(quality_score, lead_type='I'):
    """
    根据信号质量和导联类型获取自适应权重
    """
    try:
        quality_score_rounded = round(quality_score * 20) / 20

        from apps.analysis.arrhythmia_diagnosis.af.af_config import AF_WEIGHTS, LEAD_SPECIFIC, QUALITY_CONFIG


        weights = AF_WEIGHTS.copy()


        if lead_type in LEAD_SPECIFIC:

            for key in weights.keys():
                specific_weight_key = f'{key}_weight'
                if specific_weight_key in LEAD_SPECIFIC[lead_type]:
                    weights[key] = LEAD_SPECIFIC[lead_type][specific_weight_key]

        if lead_type == 'I':
            if quality_score < 0.7:
                weight_adjust = QUALITY_CONFIG.get('low_quality_weight_adjust', 0.2)

                weights['rp_diff'] *= (1.0 - weight_adjust)
                weights['rr_std'] *= (1.0 + weight_adjust)
                weights['rr_cv'] *= (1.0 + weight_adjust)
                weights['f_wave'] *= (1.0 + weight_adjust * 0.5)

        weight_sum = sum(weights.values())
        if weight_sum != 1.0:
            scale = 1.0 / weight_sum
            for key in weights:
                weights[key] *= scale

        return weights
    except Exception as e:

        from apps.analysis.arrhythmia_diagnosis.af.af_config import AF_WEIGHTS
        return AF_WEIGHTS

# --- Logic adapted from apps.signal_analysis.available.py ---

def _calculate_high_freq_energy_metric(sig, fs, cutoff=25):
    """
    计算高频能量占比。
    Adapted from apps.signal_analysis.available.py:high_freq_energy
    """
    if len(sig) == 0:
        return 0.0

    sig_centered = sig - np.mean(sig) # 去除直流分量
    fft_vals = np.abs(fft(sig_centered)) # 计算FFT
    freqs = np.abs(fftfreq(len(sig), 1 / fs)) # 计算频率

    max_freq = min(fs // 2, 100) # 高频掩码
    hf_mask = (freqs > cutoff) & (freqs < max_freq)

    total_energy = np.sum(fft_vals)
    hf_energy = np.sum(fft_vals[hf_mask]) / (total_energy + 1e-6) # 计算高频能量占比

    return hf_energy

def _calculate_baseline_drift_metric(sig, fs, cutoff=0.5):
    """
    计算基线漂移的标准差。
    Adapted from apps.signal_analysis.available.py:baseline_variation
    """
    if len(sig) == 0:
        return 0.0

    # 设计高通滤波器
    # Note: available.py uses butter order 3, af_thresholds.py's wavelet_filter uses order 4 for its highpass.
    # We will stick to order 3 as in the original baseline_variation for now.
    b, a = signal.butter(3, cutoff, btype='highpass', fs=fs)

    # 应用滤波器
    baseline_filtered_signal = signal.filtfilt(b, a, sig)

    # 计算基线漂移的标准差
    return np.std(baseline_filtered_signal) 