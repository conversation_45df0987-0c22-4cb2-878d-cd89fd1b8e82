import csv
import os
import time
import json
import requests
from qiniu import Auth
import os.path as osp

# 七牛云配置
QINIU = {
    'prod': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://upload.weiheyixue.com'
    }
}

# 本地保存路径
SAVE_DIR = r'D:\ECG\0331标注平台数据'

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")

def sanitize_filename(filename):
    """
    将 es_key 转换为有效的文件名
    替换可能在文件名中无效的字符
    """
    # 替换斜杠为下划线
    return filename.replace('/', '_').replace('\\', '_')

def parse_ecg_data(content, content_type):
    """
    解析 ECG 数据，尝试将其转换为 ECG 信号点列表
    
    Args:
        content: 原始内容（字节或文本）
        content_type: 内容类型（HTTP头中的Content-Type）
        
    Returns:
        list: ECG信号点列表，如果解析失败则返回None
    """
    try:
        # 首先尝试将内容解析为 JSON，不管 content_type 是什么
        # 因为用户表示 .dat 文件实际上是 JSON 格式
        try:
            # 尝试解码内容
            decoded_content = content.decode('utf-8')
            
            # 打印前200个字符，帮助调试
            print(f"文件内容前200字符: {decoded_content[:200]}...")
            
            # 解析JSON
            data = json.loads(decoded_content)
            
            # 打印JSON的顶层键，帮助了解数据结构
            if isinstance(data, dict):
                print(f"JSON 顶层键: {list(data.keys())}")
                
                # 直接检查"ecg"字段 - 根据输出我们知道它存在
                if "ecg" in data:
                    ecg_value = data["ecg"]
                    print(f"ecg 字段类型: {type(ecg_value)}")
                    
                    # 如果ecg字段是字符串，尝试将其解析为JSON数组
                    if isinstance(ecg_value, str):
                        print(f"ecg 字段是字符串，长度: {len(ecg_value)}")
                        print(f"ecg 字段前50个字符: {ecg_value[:50]}...")
                        
                        # 尝试将字符串解析为JSON
                        try:
                            ecg_data = json.loads(ecg_value)
                            if isinstance(ecg_data, list):
                                print(f"成功将ecg字段解析为数组，包含 {len(ecg_data)} 个数据点")
                                return ecg_data
                            else:
                                print(f"ecg字段解析后不是数组，而是 {type(ecg_data)}")
                        except json.JSONDecodeError as e:
                            print(f"ecg字段无法解析为JSON: {e}")
                            
                            # 尝试手动解析，可能是类似 "[0.123,0.456,...]" 的格式
                            if ecg_value.startswith('[') and ecg_value.endswith(']'):
                                try:
                                    # 去掉两端的方括号，按逗号分割
                                    values_str = ecg_value[1:-1].split(',')
                                    values = [float(x.strip()) for x in values_str]
                                    print(f"手动解析出 {len(values)} 个数据点")
                                    return values
                                except Exception as e:
                                    print(f"手动解析失败: {e}")
                    
                    # 如果ecg字段直接是列表，直接返回
                    elif isinstance(ecg_value, list):
                        print(f"ecg 字段直接是列表，包含 {len(ecg_value)} 个数据点")
                        return ecg_value
                
                # 检查其他可能的字段 (排除 ecgII 和 ecgIII)
                ecg_fields = ["ecgData", "data", "signal"]  # 移除了 ecgII 和 ecgIII
                for field in ecg_fields:
                    if field in data:
                        field_value = data[field]
                        if isinstance(field_value, str):
                            try:
                                field_data = json.loads(field_value)
                                if isinstance(field_data, list):
                                    print(f"成功从 {field} 字段解析出 {len(field_data)} 个数据点")
                                    return field_data
                            except:
                                pass
                        elif isinstance(field_value, list):
                            print(f"{field} 字段直接是列表，包含 {len(field_value)} 个数据点")
                            return field_value
            
            # 如果上面的尝试都失败，使用递归函数搜索，但排除 ecgII 和 ecgIII 字段
            def find_ecg_data(obj, path="root"):
                """递归搜索可能的ECG数据数组，排除 ecgII 和 ecgIII 字段"""
                if isinstance(obj, list) and len(obj) > 100:  # 假设ECG数据至少有100个点
                    # 检查是否是可能的ECG数据 - 检查前5个元素是否是数字
                    if all(isinstance(x, (int, float)) for x in obj[:5]):
                        print(f"在路径 {path} 发现可能的ECG数据数组，长度: {len(obj)}")
                        return obj
                
                if isinstance(obj, dict):
                    # 首先检查特定键名，排除 ecgII 和 ecgIII
                    ecg_keys = ["ecg", "data", "signal", "ecgData", "points", "values", "ECG"]
                    for key in ecg_keys:
                        if key in obj and key not in ["ecgII", "ecgIII"] and isinstance(obj[key], list) and len(obj[key]) > 100:
                            print(f"在键 '{key}' 下发现可能的ECG数据数组，长度: {len(obj[key])}")
                            return obj[key]
                    
                    # 递归检查所有键，排除 ecgII 和 ecgIII
                    for key, value in obj.items():
                        if key not in ["ecgII", "ecgIII"]:  # 排除这两个字段
                            result = find_ecg_data(value, f"{path}.{key}")
                            if result:
                                return result
                
                # 检查嵌套的列表
                if isinstance(obj, list):
                    for i, item in enumerate(obj[:10]):  # 只检查前10个以避免过长的列表
                        if isinstance(item, (dict, list)):
                            result = find_ecg_data(item, f"{path}[{i}]")
                            if result:
                                return result
                
                return None
            
            # 使用递归函数搜索ECG数据
            ecg_data = find_ecg_data(data)
            if ecg_data:
                return ecg_data
            
            print("无法在JSON中找到ECG数据点列表")
            return None
            
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            
            # 尝试读取文件内容的不同部分，可能是带有头部的特殊格式
            try:
                # 尝试跳过前面的字节，查找JSON起始标记 "{"
                json_start = decoded_content.find('{')
                if json_start > 0:
                    print(f"尝试从位置 {json_start} 开始解析JSON")
                    data = json.loads(decoded_content[json_start:])
                    return find_ecg_data(data, "skipped_header")
            except:
                pass
            
            # 如果不是 JSON 格式，根据 content_type 尝试其他解析方法
            pass
        
        # 如果JSON解析失败，尝试检查是否为字节分隔的浮点数列表
        try:
            import struct
            import numpy as np
            
            # 1. 尝试作为浮点数数组 (32位浮点)
            float_values = np.frombuffer(content, dtype=np.float32).tolist()
            if len(float_values) > 100:
                print(f"将内容解析为float32数组，长度: {len(float_values)}")
                return float_values
                
            # 2. 尝试作为双精度浮点数
            double_values = np.frombuffer(content, dtype=np.float64).tolist()
            if len(double_values) > 100:
                print(f"将内容解析为float64数组，长度: {len(double_values)}")
                return double_values
            
            # 3. 尝试作为16位整数
            int_values = np.frombuffer(content, dtype=np.int16).tolist()
            if len(int_values) > 100:
                print(f"将内容解析为int16数组，长度: {len(int_values)}")
                return int_values
        except Exception as e:
            print(f"二进制解析错误: {e}")
        
        # 如果所有尝试都失败，保存一个样本文件供手动检查
        print("所有自动解析方法失败。")
        return None
    
    except Exception as e:
        print(f"解析ECG数据时出错: {e}")
        return None

def download_ecg_file(es_key, environment='prod'):
    """
    从七牛云下载给定 es_key 对应的 ECG 文件，并转换为 CSV 格式
    
    Args:
        es_key: 文件的 es_key
        environment: 环境，默认为 'prod'
        
    Returns:
        bool: 下载是否成功
    """
    try:
        # 从配置获取七牛云参数
        access_key = QINIU[environment]['access_key']
        secret_key = QINIU[environment]['secret_key']
        domain_prefix = QINIU[environment]['domain_prefix']
        
        # 构建鉴权对象
        q = Auth(access_key, secret_key)
        
        # 构建完整的文件路径
        file_path = f'ecg/{es_key}'
        
        # 生成私有下载链接
        # 链接有效期设为1小时（3600秒）
        private_url = q.private_download_url(domain_prefix + '/' + file_path, expires=3600)
        
        # 下载文件
        print(f"尝试下载: {es_key}")
        response = requests.get(private_url)
        
        if response.status_code == 200:
            # 准备保存的文件名
            safe_filename = sanitize_filename(es_key)
            content_type = response.headers.get('Content-Type', '')
            
            # 1. 保存原始文件作为备份
            original_ext = '.json' if 'json' in content_type.lower() else '.dat'
            original_path = os.path.join(SAVE_DIR, safe_filename + original_ext)
            
            with open(original_path, 'wb') as f:
                f.write(response.content)
            
            print(f"已保存原始文件: {original_path}")
            
            # 如果是第一个文件，保存一个样本以便手动检查
            global sample_saved
            if not globals().get('sample_saved', False):
                sample_path = os.path.join(SAVE_DIR, 'sample_for_inspection' + original_ext)
                with open(sample_path, 'wb') as f:
                    f.write(response.content)
                print(f"已保存样本文件供手动检查: {sample_path}")
                globals()['sample_saved'] = True
            
            # 2. 解析ECG数据并保存为CSV
            ecg_data = parse_ecg_data(response.content, content_type)
            
            if ecg_data:
                # 将ECG数据保存为CSV格式，一行包含所有数据点，每个数据点一个单元格
                csv_path = os.path.join(SAVE_DIR, safe_filename + '.csv')
                
                with open(csv_path, 'w', newline='') as csvfile:
                    writer = csv.writer(csvfile)
                    # 写入单行数据，每个数据点为一个单元格
                    writer.writerow(ecg_data)
                
                print(f"已成功将ECG数据转换并保存为CSV: {csv_path}")
                return True
            else:
                print(f"无法解析ECG数据，但已保存原始文件: {original_path}")
                return True  # 仍然返回True因为至少保存了原始文件
        else:
            print(f"下载失败: {es_key}, 状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"下载过程中出现错误: {e}")
        return False

def download_all_from_csv(csv_path='single_conclusion_ecg.csv'):
    """
    从 CSV 文件中读取所有 es_key 并下载相应的 ECG 文件
    
    Args:
        csv_path: CSV 文件路径
    """
    # 确保保存目录存在
    ensure_dir_exists(SAVE_DIR)
    
    try:
        es_keys = []
        
        # 读取 CSV 文件
        with open(csv_path, 'r', encoding='utf-8-sig') as csvfile:
            reader = csv.reader(csvfile)
            next(reader)  # 跳过表头
            
            for row in reader:
                if row:  # 确保行不为空
                    es_key = row[0]  # 第一列是 es_key
                    es_keys.append(es_key)
        
        print(f"从 CSV 文件中读取了 {len(es_keys)} 个 es_key")
        
        # 下载所有文件
        success_count = 0
        fail_count = 0
        
        for i, es_key in enumerate(es_keys):
            print(f"[{i+1}/{len(es_keys)}] 处理: {es_key}")
            
            # 尝试下载
            if download_ecg_file(es_key):
                success_count += 1
            else:
                fail_count += 1
            
            # 为防止请求过于频繁，每次下载后稍作暂停
            time.sleep(0.5)
        
        print(f"下载完成。成功: {success_count}, 失败: {fail_count}")
        
    except Exception as e:
        print(f"处理 CSV 文件时出错: {e}")

if __name__ == "__main__":
    # 全局变量，用于控制样本文件保存
    sample_saved = False
    
    # 检查 CSV 文件是否存在
    csv_file = 'single_conclusion_ecg.csv'
    
    if os.path.exists(csv_file):
        print(f"找到 CSV 文件: {csv_file}")
        
        # 尝试仅下载一个文件进行调试
        debug_mode = False  # 设置为False以处理所有文件
        
        if debug_mode:
            print("调试模式：仅处理前2个文件")
            with open(csv_file, 'r', encoding='utf-8-sig') as csvfile:
                reader = csv.reader(csvfile)
                next(reader)  # 跳过表头
                
                for i, row in enumerate(reader):
                    if i >= 2:  # 只处理前2个
                        break
                    if row:
                        es_key = row[0]
                        print(f"调试: 处理 es_key: {es_key}")
                        download_ecg_file(es_key)
            
            print("调试完成。请查看输出调整代码后再运行完整下载。")
            print("要处理所有文件，请将 debug_mode 设置为 False")
        else:
            # 正常处理所有文件
            download_all_from_csv(csv_file)
    else:
        print(f"CSV 文件 {csv_file} 不存在。请先运行 mark_db_check.py 生成该文件。") 