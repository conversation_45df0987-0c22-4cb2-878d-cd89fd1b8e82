import json
import traceback
from datetime import datetime

import pandas as pd
from django.views import View

from apps.api.xh_report.business import get_report_data, get_hourly_statistics, get_report
from apps.common.information import get_signal_quality
from apps.models.data_models import TReportLog
from apps.models.xh_report_model import ReportModel
from apps.utils.datetime_helper import convert_to_time_format
from apps.utils.get_response import GetResponse
from apps.utils.logger_helper import Logger
from apps.utils.param_extraction import param_extraction
from apps.utils.pdf_helper import generate_pdf


# @method_decorator(authentication, name="dispatch")
class XHReportView(View):
    """
    报告
    """

    def post(self, request, *args, **kwargs):
        """
        POST请求接口
        :param request: 请求对象
        :return: 诊断结果
        """
        custom_id = kwargs.get('custom_id')

        try:
            union_id = param_extraction(request, 'union_id', 'POST')
            start_date = param_extraction(request, 'start_date', 'POST')
            end_date = param_extraction(request, 'end_date', 'POST')

            if start_date is None or end_date is None:
                start_date = datetime.now().strftime('%Y-%m-%d')
                end_date = datetime.now().strftime('%Y-%m-%d')

            # 检查参数是否为空
            if union_id is None or start_date is None or end_date is None:
                return GetResponse.get_response(code=5)

            report_model = ReportModel()

            ecg_datas, detail_datas = get_report_data(union_id, start_date, end_date)

            if len(ecg_datas) == 0:
                return GetResponse.get_response(code=0, data='未查询到数据')

            datas = pd.DataFrame([item.__dict__ for item in ecg_datas])

            normal_data = datas[datas['dead'] == 1].copy()  # 获取信号质量正常的数据

            # 获取信号质量
            report_model.signal_quality = get_signal_quality(datas, normal_data)

            # 获取小时统计数据
            hourly_statistics_list = get_hourly_statistics(start_date, end_date, normal_data, detail_datas)

            # 获取报告主体数据
            report_model = get_report(report_model, datas, normal_data, hourly_statistics_list, detail_datas)

            report_model.hourly_statistics_infos = hourly_statistics_list

            # 设置记录和有效时长
            report_model.analysis_time = f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            report_model.record_duration = convert_to_time_format(len(datas))
            report_model.effective_duration = convert_to_time_format(len(normal_data))
            report_model.record_date = f"{start_date} 至 {end_date}"

            # 记录请求日志
            report_log = TReportLog()
            report_log.custom_id = custom_id
            report_log.request_param = json.dumps(request.POST)
            report_log.create_date = datetime.now()
            report_log.save()

            return generate_pdf(report_model, 'xh_report_template.html')
        except Exception:
            Logger().error(traceback.format_exc())
            return GetResponse.get_response(code=2)






