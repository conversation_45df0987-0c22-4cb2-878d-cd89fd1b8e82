Metadata-Version: 2.3
Name: easydev
Version: 0.13.3
Summary: Common utilities to ease development of Python packages
Home-page: https://easydev.readthedocs.io
License: BSD-3-Clause
Keywords: config,decorators,development
Author: <PERSON>
Author-email: <EMAIL>
Requires-Python: >=3.8,<4.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: POSIX :: Linux
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Bio-Informatics
Classifier: Topic :: Scientific/Engineering :: Information Analysis
Requires-Dist: colorama (>=0.4.6,<0.5.0)
Requires-Dist: colorlog (>=6.8.2,<7.0.0)
Requires-Dist: line-profiler (>=4.1.2,<5.0.0)
Requires-Dist: pexpect (>=4.9.0,<5.0.0)
Requires-Dist: platformdirs (>=4.2.0,<5.0.0)
Project-URL: Documentation, https://easydev.readthedocs.io
Project-URL: Issues, https://github.com/cokelaer/easydev/issues
Project-URL: Repository, https://github.com/cokelaer/easydev
Description-Content-Type: text/x-rst

easydev
##########

.. image:: https://badge.fury.io/py/easydev.svg
    :target: https://pypi.python.org/pypi/easydev

.. image:: https://github.com/cokelaer/easydev/actions/workflows/main.yml/badge.svg
    :target: https://github.com/cokelaer/easydev/actions/workflows/main.yml


.. image:: https://coveralls.io/repos/cokelaer/easydev/badge.svg?branch=main
   :target: https://coveralls.io/r/cokelaer/easydev?branch=main




:documentation: http://easydev-python.readthedocs.io/en/latest/
:contributions: Please join https://github.com/cokelaer/easydev
:source: Please use https://github.com/cokelaer/easydev
:issues: Please use https://github.com/cokelaer/easydev/issues
:Python version supported: 3.9, 3.10, 3.11, 3.12


The  `easydev <http://pypi.python.org/pypi/easydev/>`_ package
provides miscellaneous functions that are repeatidly used during
the development of Python packages. The goal is to help developers on
speeding up their own dev. It has been used also as an incubator for other
packages (e.g., http://pypi.python.org/pypi/colormap) and is stable.

.. warning:: I'm not pretending to provide universal and bug-free tools. The
    tools provided may also change. However, **easydev** is used
    in a few other packages such as
    `bioservices <https://pypi.python.org/pypi/bioservices>`_,
    `sequana <https://sequana.readthedocs.io>`_ or
    `GDSCTools <https://sequana.readthedocs.io>`_ to give a few
    examples.


Note 10/01/2025: Although I tried to keep back-compatibility as much as possible, test can be done only on newest version. support for python 3.7 and 3.8 were droppe because the continuous integration does not support it. However,  easydev may still be python3.7 and 3.8 compatible. Have a try.



Changelog
~~~~~~~~~

========= ==========================================================================
Version   Description
========= ==========================================================================
0.13.3    * update pyproject with contribs from @s-t-e-v-e-n-k see PR37
0.13.2    * replace mock with unittest.mock (fixes
            https://github.com/cokelaer/easydev/issues/20)
0.13.1    * fix get_dependencies
0.13.0    * fix requirements (line_profiler) and CI
0.12.2    * For developers: move to pyprojet. add precomit
          * replace pkg_resources (deprecated) with importlib
          * replace appdirs with more generic platformdirs
========= ==========================================================================

