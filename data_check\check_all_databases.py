import pymysql

def check_all_databases():
    """
    查看所有可用的数据库
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 查看所有数据库
        query = "SHOW DATABASES"
        cursor.execute(query)
        databases = cursor.fetchall()
        
        print("\n可用的数据库列表:")
        for i, db in enumerate(databases, 1):
            print(f"{i}. {db[0]}")
            
            # 尝试获取每个数据库的基本信息
            try:
                cursor.execute(f"USE {db[0]}")
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                print(f"   - 包含 {len(tables)} 个表")
            except Exception as e:
                print(f"   - 无法访问此数据库的详细信息: {str(e)}")
            
    except Exception as e:
        print(f"查询数据库列表时出错: {str(e)}")
        
    finally:
        cursor.close()
        connection.close()

if __name__ == '__main__':
    check_all_databases()