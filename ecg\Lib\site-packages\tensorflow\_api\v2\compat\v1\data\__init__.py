# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""`tf.data.Dataset` API for input pipelines.

See [Importing Data](https://tensorflow.org/guide/data) for an overview.

"""

from __future__ import print_function as _print_function

import sys as _sys

from . import experimental
from tensorflow.python.data.experimental.ops.threading_options import ThreadingOptions
from tensorflow.python.data.ops.dataset_ops import AUTOTUNE
from tensorflow.python.data.ops.dataset_ops import DatasetSpec
from tensorflow.python.data.ops.dataset_ops import DatasetV1 as Dataset
from tensorflow.python.data.ops.dataset_ops import INFINITE as INFINITE_CARDINALITY
from tensorflow.python.data.ops.dataset_ops import Options
from tensorflow.python.data.ops.dataset_ops import UNKNOWN as UNKNOWN_CARDINALITY
from tensorflow.python.data.ops.dataset_ops import get_legacy_output_classes as get_output_classes
from tensorflow.python.data.ops.dataset_ops import get_legacy_output_shapes as get_output_shapes
from tensorflow.python.data.ops.dataset_ops import get_legacy_output_types as get_output_types
from tensorflow.python.data.ops.dataset_ops import make_initializable_iterator
from tensorflow.python.data.ops.dataset_ops import make_one_shot_iterator
from tensorflow.python.data.ops.iterator_ops import Iterator
from tensorflow.python.data.ops.readers import FixedLengthRecordDatasetV1 as FixedLengthRecordDataset
from tensorflow.python.data.ops.readers import TFRecordDatasetV1 as TFRecordDataset
from tensorflow.python.data.ops.readers import TextLineDatasetV1 as TextLineDataset

del _print_function
