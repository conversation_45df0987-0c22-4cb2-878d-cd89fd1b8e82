---
title: 'FiftyOne'
linkTitle: 'FiftyOne'
weight: 1
---

![](/images/fiftyone_logo.png)

[FiftyOne](https://github.com/voxel51/fiftyone) is an open-source tool for building high-quality datasets
and computer vision models.
FiftyOne supercharges your machine learning workflows by enabling you to visualize datasets and interpret
models faster and more effectively.

FiftyOne provides an API to create tasks and jobs, upload data, define label schemas,
and download annotations using CVAT, all programmatically in Python.
All of the following label types are supported, for both image and video datasets:

- Classifications
- Detections
- Instance segmentations
- Polygons and polylines
- Keypoints
- Scalar fields
- Semantic segmentation

![](/images/image264.jpg)
