//===- BuiltinDialect.h - MLIR Builtin Dialect ------------------*- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//
//
// This file contains the Builtin dialect that contains all of the attributes,
// operations, and types that are necessary for the validity of the IR.
//
//===----------------------------------------------------------------------===//

#ifndef MLIR_IR_BUILTINDIALECT_H_
#define MLIR_IR_BUILTINDIALECT_H_

#include "mlir/IR/Dialect.h"

//===----------------------------------------------------------------------===//
// Dialect
//===----------------------------------------------------------------------===//

#include "mlir/IR/BuiltinDialect.h.inc"

#endif // MLIR_IR_BUILTINDIALECT_H_
