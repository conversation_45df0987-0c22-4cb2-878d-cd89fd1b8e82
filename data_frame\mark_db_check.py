import mysql.connector
from mysql.connector import Error
import csv # 导入 CSV 模块

# 数据库连接配置
db_config = {
    'host': '**************',
    'port': 3308,
    'user': 'ai',
    'password': 'z8^#g4r4mz',
    'database': 'ecg_analysis_test'
}

def test_db_connection(config):
    """
    测试数据库连接。

    Args:
        config (dict): 包含数据库连接信息的字典。

    Returns:
        bool: 如果连接成功返回 True，否则返回 False。
    """
    connection = None
    try:
        connection = mysql.connector.connect(**config)
        if connection.is_connected():
            print(f"成功连接到数据库: {config['database']}@{config['host']}:{config['port']}")
            return True
        else:
            print("连接已建立，但状态为非连接状态。") # 这种情况比较少见
            return False
    except Error as e:
        print(f"连接失败: {e}")
        return False
    finally:
        if connection and connection.is_connected():
            connection.close()
            print("数据库连接已关闭。")

def query_ecg_data(config, query):
    """
    连接到数据库并执行指定的查询。

    Args:
        config (dict): 包含数据库连接信息的字典。
        query (str): 要执行的 SQL 查询语句。

    Returns:
        list: 查询结果列表，如果出错则返回 None。
    """
    connection = None
    cursor = None
    results = None
    try:
        connection = mysql.connector.connect(**config)
        if connection.is_connected():
            print("数据库连接成功，准备执行查询...")
            cursor = connection.cursor()
            cursor.execute(query)
            results = cursor.fetchall() # 获取所有查询结果
            print(f"查询执行完毕，获取到 {len(results)} 条记录。")
            return results
        else:
            print("无法建立数据库连接以执行查询。")
            return None
    except Error as e:
        print(f"查询数据时出错: {e}")
        return None
    finally:
        if cursor:
            cursor.close()
            print("游标已关闭。")
        if connection and connection.is_connected():
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    if test_db_connection(db_config):
        
        print("\n------ 列出数据库中的表 ------")
        # 查询并打印所有表名
        tables_query = "SHOW TABLES;"
        print(f"将执行查询: {tables_query}")
        tables = query_ecg_data(db_config, tables_query)
        if tables is not None:
            print("数据库中的表:")
            for table in tables:
                print(f"- {table[0]}") 
        else:
            print("无法获取表列表。")

        print("\n------ 查看 t_patient_ecg 表结构 ------")
        # 查询并打印 t_patient_ecg 表的列信息
        schema_query = "DESCRIBE t_patient_ecg;"
        print(f"将执行查询: {schema_query}")
        schema_info = query_ecg_data(db_config, schema_query)
        if schema_info is not None:
            print("t_patient_ecg 表的列信息:")
            print("字段名 (Field)\t类型 (Type)\t是否允许为空 (Null)\t键 (Key)\t默认值 (Default)\t额外信息 (Extra)")
            for col in schema_info:
                print("\t".join(map(str, col))) # 打印列信息，用 Tab 分隔
        else:
            print("无法获取 t_patient_ecg 的表结构信息。")

        # ----- 原有的 ECG 数据查询、筛选和导出逻辑保持不变 -----
        print("\n------ 开始查询 ECG 元数据 ------")
        # 定义要执行的 SQL 查询
        # 选择 es_key 和 new_report_conclusion 以便区分结果
        sql_query = "SELECT es_key, new_report_conclusion FROM t_patient_ecg WHERE ecg_status = 3 and sample_rate = 250"
        print(f"将执行查询: {sql_query}")

        # 执行查询并获取数据
        data = query_ecg_data(db_config, sql_query)

        if data is not None:
            print("\n------ 数据筛选与导出 ------")
            
            # 筛选出只包含单个结论的记录
            single_conclusion_data = []
            if data:
                for row in data:
                    # row[1] 是 new_report_conclusion 字段
                    # 检查结论字符串中是否不包含 ' | '
                    if row[1] is not None and ' | ' not in row[1]: 
                        single_conclusion_data.append(row)
            
            if single_conclusion_data:
                print(f"筛选出 {len(single_conclusion_data)} 条单结论记录。")
                
                # 定义 CSV 文件名
                csv_filename = 'single_conclusion_ecg.csv'
                
                try:
                    # 写入 CSV 文件，使用 utf-8-sig 编码以兼容 Excel
                    with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                        writer = csv.writer(csvfile)
                        # 写入表头
                        writer.writerow(['es_key', 'new_report_conclusion'])
                        # 写入筛选后的数据
                        writer.writerows(single_conclusion_data)
                    print(f"单结论数据已成功导出到文件: {csv_filename}")
                except IOError as e:
                    print(f"写入 CSV 文件时出错: {e}")
                    
            else:
                print("没有找到符合条件的单结论记录。")
        else:
            print("获取数据失败，无法进行筛选和导出。")

    else:
        print("数据库连接测试失败，无法执行查询。")

# 您提供的 SQL 查询（仅供参考，当前代码不执行它）
# SELECT es_key FROM t_patient_ecg WHERE ecg_status = 3
