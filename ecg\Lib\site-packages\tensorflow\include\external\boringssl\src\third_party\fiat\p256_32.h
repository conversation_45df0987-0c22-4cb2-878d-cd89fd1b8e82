/* Autogenerated */
/* curve description: p256 */
/* requested operations: (all) */
/* m = 0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff (from "2^256 - 2^224 + 2^192 + 2^96 - 1") */
/* machine_wordsize = 32 (from "32") */
/*                                                                    */
/* NOTE: In addition to the bounds specified above each function, all */
/*   functions synthesized for this Montgomery arithmetic require the */
/*   input to be strictly less than the prime modulus (m), and also   */
/*   require the input to be in the unique saturated representation.  */
/*   All functions also ensure that these two properties are true of  */
/*   return values.                                                   */

#include <stdint.h>
typedef unsigned char fiat_p256_uint1;
typedef signed char fiat_p256_int1;


/*
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffffff]
 *   arg3: [0x0 ~> 0xffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_p256_addcarryx_u32(uint32_t* out1, fiat_p256_uint1* out2, fiat_p256_uint1 arg1, uint32_t arg2, uint32_t arg3) {
  uint64_t x1 = ((arg1 + (uint64_t)arg2) + arg3);
  uint32_t x2 = (uint32_t)(x1 & UINT32_C(0xffffffff));
  fiat_p256_uint1 x3 = (fiat_p256_uint1)(x1 >> 32);
  *out1 = x2;
  *out2 = x3;
}

/*
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffffff]
 *   arg3: [0x0 ~> 0xffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_p256_subborrowx_u32(uint32_t* out1, fiat_p256_uint1* out2, fiat_p256_uint1 arg1, uint32_t arg2, uint32_t arg3) {
  int64_t x1 = ((arg2 - (int64_t)arg1) - arg3);
  fiat_p256_int1 x2 = (fiat_p256_int1)(x1 >> 32);
  uint32_t x3 = (uint32_t)(x1 & UINT32_C(0xffffffff));
  *out1 = x3;
  *out2 = (fiat_p256_uint1)(0x0 - x2);
}

/*
 * Input Bounds:
 *   arg1: [0x0 ~> 0xffffffff]
 *   arg2: [0x0 ~> 0xffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffff]
 *   out2: [0x0 ~> 0xffffffff]
 */
static void fiat_p256_mulx_u32(uint32_t* out1, uint32_t* out2, uint32_t arg1, uint32_t arg2) {
  uint64_t x1 = ((uint64_t)arg1 * arg2);
  uint32_t x2 = (uint32_t)(x1 & UINT32_C(0xffffffff));
  uint32_t x3 = (uint32_t)(x1 >> 32);
  *out1 = x2;
  *out2 = x3;
}

/*
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffffff]
 *   arg3: [0x0 ~> 0xffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffff]
 */
static void fiat_p256_cmovznz_u32(uint32_t* out1, fiat_p256_uint1 arg1, uint32_t arg2, uint32_t arg3) {
  fiat_p256_uint1 x1 = (!(!arg1));
  uint32_t x2 = ((fiat_p256_int1)(0x0 - x1) & UINT32_C(0xffffffff));
  // Note this line has been patched from the synthesized code to add value
  // barriers.
  //
  // Clang recognizes this pattern as a select. While it usually transforms it
  // to a cmov, it sometimes further transforms it into a branch, which we do
  // not want.
  uint32_t x3 = ((value_barrier_u32(x2) & arg3) | (value_barrier_u32(~x2) & arg2));
  *out1 = x3;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 *   arg2: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 */
static void fiat_p256_mul(uint32_t out1[8], const uint32_t arg1[8], const uint32_t arg2[8]) {
  uint32_t x1 = (arg1[1]);
  uint32_t x2 = (arg1[2]);
  uint32_t x3 = (arg1[3]);
  uint32_t x4 = (arg1[4]);
  uint32_t x5 = (arg1[5]);
  uint32_t x6 = (arg1[6]);
  uint32_t x7 = (arg1[7]);
  uint32_t x8 = (arg1[0]);
  uint32_t x9;
  uint32_t x10;
  fiat_p256_mulx_u32(&x9, &x10, x8, (arg2[7]));
  uint32_t x11;
  uint32_t x12;
  fiat_p256_mulx_u32(&x11, &x12, x8, (arg2[6]));
  uint32_t x13;
  uint32_t x14;
  fiat_p256_mulx_u32(&x13, &x14, x8, (arg2[5]));
  uint32_t x15;
  uint32_t x16;
  fiat_p256_mulx_u32(&x15, &x16, x8, (arg2[4]));
  uint32_t x17;
  uint32_t x18;
  fiat_p256_mulx_u32(&x17, &x18, x8, (arg2[3]));
  uint32_t x19;
  uint32_t x20;
  fiat_p256_mulx_u32(&x19, &x20, x8, (arg2[2]));
  uint32_t x21;
  uint32_t x22;
  fiat_p256_mulx_u32(&x21, &x22, x8, (arg2[1]));
  uint32_t x23;
  uint32_t x24;
  fiat_p256_mulx_u32(&x23, &x24, x8, (arg2[0]));
  uint32_t x25;
  fiat_p256_uint1 x26;
  fiat_p256_addcarryx_u32(&x25, &x26, 0x0, x21, x24);
  uint32_t x27;
  fiat_p256_uint1 x28;
  fiat_p256_addcarryx_u32(&x27, &x28, x26, x19, x22);
  uint32_t x29;
  fiat_p256_uint1 x30;
  fiat_p256_addcarryx_u32(&x29, &x30, x28, x17, x20);
  uint32_t x31;
  fiat_p256_uint1 x32;
  fiat_p256_addcarryx_u32(&x31, &x32, x30, x15, x18);
  uint32_t x33;
  fiat_p256_uint1 x34;
  fiat_p256_addcarryx_u32(&x33, &x34, x32, x13, x16);
  uint32_t x35;
  fiat_p256_uint1 x36;
  fiat_p256_addcarryx_u32(&x35, &x36, x34, x11, x14);
  uint32_t x37;
  fiat_p256_uint1 x38;
  fiat_p256_addcarryx_u32(&x37, &x38, x36, x9, x12);
  uint32_t x39;
  fiat_p256_uint1 x40;
  fiat_p256_addcarryx_u32(&x39, &x40, x38, 0x0, x10);
  uint32_t x41;
  uint32_t x42;
  fiat_p256_mulx_u32(&x41, &x42, x23, UINT32_C(0xffffffff));
  uint32_t x43;
  uint32_t x44;
  fiat_p256_mulx_u32(&x43, &x44, x23, UINT32_C(0xffffffff));
  uint32_t x45;
  uint32_t x46;
  fiat_p256_mulx_u32(&x45, &x46, x23, UINT32_C(0xffffffff));
  uint32_t x47;
  uint32_t x48;
  fiat_p256_mulx_u32(&x47, &x48, x23, UINT32_C(0xffffffff));
  uint32_t x49;
  fiat_p256_uint1 x50;
  fiat_p256_addcarryx_u32(&x49, &x50, 0x0, x45, x48);
  uint32_t x51;
  fiat_p256_uint1 x52;
  fiat_p256_addcarryx_u32(&x51, &x52, x50, x43, x46);
  uint32_t x53;
  fiat_p256_uint1 x54;
  fiat_p256_addcarryx_u32(&x53, &x54, x52, 0x0, x44);
  uint32_t x55;
  fiat_p256_uint1 x56;
  fiat_p256_addcarryx_u32(&x55, &x56, 0x0, x47, x23);
  uint32_t x57;
  fiat_p256_uint1 x58;
  fiat_p256_addcarryx_u32(&x57, &x58, x56, x49, x25);
  uint32_t x59;
  fiat_p256_uint1 x60;
  fiat_p256_addcarryx_u32(&x59, &x60, x58, x51, x27);
  uint32_t x61;
  fiat_p256_uint1 x62;
  fiat_p256_addcarryx_u32(&x61, &x62, x60, x53, x29);
  uint32_t x63;
  fiat_p256_uint1 x64;
  fiat_p256_addcarryx_u32(&x63, &x64, x62, 0x0, x31);
  uint32_t x65;
  fiat_p256_uint1 x66;
  fiat_p256_addcarryx_u32(&x65, &x66, x64, 0x0, x33);
  uint32_t x67;
  fiat_p256_uint1 x68;
  fiat_p256_addcarryx_u32(&x67, &x68, x66, x23, x35);
  uint32_t x69;
  fiat_p256_uint1 x70;
  fiat_p256_addcarryx_u32(&x69, &x70, x68, x41, x37);
  uint32_t x71;
  fiat_p256_uint1 x72;
  fiat_p256_addcarryx_u32(&x71, &x72, x70, x42, x39);
  uint32_t x73;
  fiat_p256_uint1 x74;
  fiat_p256_addcarryx_u32(&x73, &x74, x72, 0x0, 0x0);
  uint32_t x75;
  uint32_t x76;
  fiat_p256_mulx_u32(&x75, &x76, x1, (arg2[7]));
  uint32_t x77;
  uint32_t x78;
  fiat_p256_mulx_u32(&x77, &x78, x1, (arg2[6]));
  uint32_t x79;
  uint32_t x80;
  fiat_p256_mulx_u32(&x79, &x80, x1, (arg2[5]));
  uint32_t x81;
  uint32_t x82;
  fiat_p256_mulx_u32(&x81, &x82, x1, (arg2[4]));
  uint32_t x83;
  uint32_t x84;
  fiat_p256_mulx_u32(&x83, &x84, x1, (arg2[3]));
  uint32_t x85;
  uint32_t x86;
  fiat_p256_mulx_u32(&x85, &x86, x1, (arg2[2]));
  uint32_t x87;
  uint32_t x88;
  fiat_p256_mulx_u32(&x87, &x88, x1, (arg2[1]));
  uint32_t x89;
  uint32_t x90;
  fiat_p256_mulx_u32(&x89, &x90, x1, (arg2[0]));
  uint32_t x91;
  fiat_p256_uint1 x92;
  fiat_p256_addcarryx_u32(&x91, &x92, 0x0, x87, x90);
  uint32_t x93;
  fiat_p256_uint1 x94;
  fiat_p256_addcarryx_u32(&x93, &x94, x92, x85, x88);
  uint32_t x95;
  fiat_p256_uint1 x96;
  fiat_p256_addcarryx_u32(&x95, &x96, x94, x83, x86);
  uint32_t x97;
  fiat_p256_uint1 x98;
  fiat_p256_addcarryx_u32(&x97, &x98, x96, x81, x84);
  uint32_t x99;
  fiat_p256_uint1 x100;
  fiat_p256_addcarryx_u32(&x99, &x100, x98, x79, x82);
  uint32_t x101;
  fiat_p256_uint1 x102;
  fiat_p256_addcarryx_u32(&x101, &x102, x100, x77, x80);
  uint32_t x103;
  fiat_p256_uint1 x104;
  fiat_p256_addcarryx_u32(&x103, &x104, x102, x75, x78);
  uint32_t x105;
  fiat_p256_uint1 x106;
  fiat_p256_addcarryx_u32(&x105, &x106, x104, 0x0, x76);
  uint32_t x107;
  fiat_p256_uint1 x108;
  fiat_p256_addcarryx_u32(&x107, &x108, 0x0, x89, x57);
  uint32_t x109;
  fiat_p256_uint1 x110;
  fiat_p256_addcarryx_u32(&x109, &x110, x108, x91, x59);
  uint32_t x111;
  fiat_p256_uint1 x112;
  fiat_p256_addcarryx_u32(&x111, &x112, x110, x93, x61);
  uint32_t x113;
  fiat_p256_uint1 x114;
  fiat_p256_addcarryx_u32(&x113, &x114, x112, x95, x63);
  uint32_t x115;
  fiat_p256_uint1 x116;
  fiat_p256_addcarryx_u32(&x115, &x116, x114, x97, x65);
  uint32_t x117;
  fiat_p256_uint1 x118;
  fiat_p256_addcarryx_u32(&x117, &x118, x116, x99, x67);
  uint32_t x119;
  fiat_p256_uint1 x120;
  fiat_p256_addcarryx_u32(&x119, &x120, x118, x101, x69);
  uint32_t x121;
  fiat_p256_uint1 x122;
  fiat_p256_addcarryx_u32(&x121, &x122, x120, x103, x71);
  uint32_t x123;
  fiat_p256_uint1 x124;
  fiat_p256_addcarryx_u32(&x123, &x124, x122, x105, (fiat_p256_uint1)x73);
  uint32_t x125;
  uint32_t x126;
  fiat_p256_mulx_u32(&x125, &x126, x107, UINT32_C(0xffffffff));
  uint32_t x127;
  uint32_t x128;
  fiat_p256_mulx_u32(&x127, &x128, x107, UINT32_C(0xffffffff));
  uint32_t x129;
  uint32_t x130;
  fiat_p256_mulx_u32(&x129, &x130, x107, UINT32_C(0xffffffff));
  uint32_t x131;
  uint32_t x132;
  fiat_p256_mulx_u32(&x131, &x132, x107, UINT32_C(0xffffffff));
  uint32_t x133;
  fiat_p256_uint1 x134;
  fiat_p256_addcarryx_u32(&x133, &x134, 0x0, x129, x132);
  uint32_t x135;
  fiat_p256_uint1 x136;
  fiat_p256_addcarryx_u32(&x135, &x136, x134, x127, x130);
  uint32_t x137;
  fiat_p256_uint1 x138;
  fiat_p256_addcarryx_u32(&x137, &x138, x136, 0x0, x128);
  uint32_t x139;
  fiat_p256_uint1 x140;
  fiat_p256_addcarryx_u32(&x139, &x140, 0x0, x131, x107);
  uint32_t x141;
  fiat_p256_uint1 x142;
  fiat_p256_addcarryx_u32(&x141, &x142, x140, x133, x109);
  uint32_t x143;
  fiat_p256_uint1 x144;
  fiat_p256_addcarryx_u32(&x143, &x144, x142, x135, x111);
  uint32_t x145;
  fiat_p256_uint1 x146;
  fiat_p256_addcarryx_u32(&x145, &x146, x144, x137, x113);
  uint32_t x147;
  fiat_p256_uint1 x148;
  fiat_p256_addcarryx_u32(&x147, &x148, x146, 0x0, x115);
  uint32_t x149;
  fiat_p256_uint1 x150;
  fiat_p256_addcarryx_u32(&x149, &x150, x148, 0x0, x117);
  uint32_t x151;
  fiat_p256_uint1 x152;
  fiat_p256_addcarryx_u32(&x151, &x152, x150, x107, x119);
  uint32_t x153;
  fiat_p256_uint1 x154;
  fiat_p256_addcarryx_u32(&x153, &x154, x152, x125, x121);
  uint32_t x155;
  fiat_p256_uint1 x156;
  fiat_p256_addcarryx_u32(&x155, &x156, x154, x126, x123);
  uint32_t x157;
  fiat_p256_uint1 x158;
  fiat_p256_addcarryx_u32(&x157, &x158, x156, 0x0, x124);
  uint32_t x159;
  uint32_t x160;
  fiat_p256_mulx_u32(&x159, &x160, x2, (arg2[7]));
  uint32_t x161;
  uint32_t x162;
  fiat_p256_mulx_u32(&x161, &x162, x2, (arg2[6]));
  uint32_t x163;
  uint32_t x164;
  fiat_p256_mulx_u32(&x163, &x164, x2, (arg2[5]));
  uint32_t x165;
  uint32_t x166;
  fiat_p256_mulx_u32(&x165, &x166, x2, (arg2[4]));
  uint32_t x167;
  uint32_t x168;
  fiat_p256_mulx_u32(&x167, &x168, x2, (arg2[3]));
  uint32_t x169;
  uint32_t x170;
  fiat_p256_mulx_u32(&x169, &x170, x2, (arg2[2]));
  uint32_t x171;
  uint32_t x172;
  fiat_p256_mulx_u32(&x171, &x172, x2, (arg2[1]));
  uint32_t x173;
  uint32_t x174;
  fiat_p256_mulx_u32(&x173, &x174, x2, (arg2[0]));
  uint32_t x175;
  fiat_p256_uint1 x176;
  fiat_p256_addcarryx_u32(&x175, &x176, 0x0, x171, x174);
  uint32_t x177;
  fiat_p256_uint1 x178;
  fiat_p256_addcarryx_u32(&x177, &x178, x176, x169, x172);
  uint32_t x179;
  fiat_p256_uint1 x180;
  fiat_p256_addcarryx_u32(&x179, &x180, x178, x167, x170);
  uint32_t x181;
  fiat_p256_uint1 x182;
  fiat_p256_addcarryx_u32(&x181, &x182, x180, x165, x168);
  uint32_t x183;
  fiat_p256_uint1 x184;
  fiat_p256_addcarryx_u32(&x183, &x184, x182, x163, x166);
  uint32_t x185;
  fiat_p256_uint1 x186;
  fiat_p256_addcarryx_u32(&x185, &x186, x184, x161, x164);
  uint32_t x187;
  fiat_p256_uint1 x188;
  fiat_p256_addcarryx_u32(&x187, &x188, x186, x159, x162);
  uint32_t x189;
  fiat_p256_uint1 x190;
  fiat_p256_addcarryx_u32(&x189, &x190, x188, 0x0, x160);
  uint32_t x191;
  fiat_p256_uint1 x192;
  fiat_p256_addcarryx_u32(&x191, &x192, 0x0, x173, x141);
  uint32_t x193;
  fiat_p256_uint1 x194;
  fiat_p256_addcarryx_u32(&x193, &x194, x192, x175, x143);
  uint32_t x195;
  fiat_p256_uint1 x196;
  fiat_p256_addcarryx_u32(&x195, &x196, x194, x177, x145);
  uint32_t x197;
  fiat_p256_uint1 x198;
  fiat_p256_addcarryx_u32(&x197, &x198, x196, x179, x147);
  uint32_t x199;
  fiat_p256_uint1 x200;
  fiat_p256_addcarryx_u32(&x199, &x200, x198, x181, x149);
  uint32_t x201;
  fiat_p256_uint1 x202;
  fiat_p256_addcarryx_u32(&x201, &x202, x200, x183, x151);
  uint32_t x203;
  fiat_p256_uint1 x204;
  fiat_p256_addcarryx_u32(&x203, &x204, x202, x185, x153);
  uint32_t x205;
  fiat_p256_uint1 x206;
  fiat_p256_addcarryx_u32(&x205, &x206, x204, x187, x155);
  uint32_t x207;
  fiat_p256_uint1 x208;
  fiat_p256_addcarryx_u32(&x207, &x208, x206, x189, x157);
  uint32_t x209;
  uint32_t x210;
  fiat_p256_mulx_u32(&x209, &x210, x191, UINT32_C(0xffffffff));
  uint32_t x211;
  uint32_t x212;
  fiat_p256_mulx_u32(&x211, &x212, x191, UINT32_C(0xffffffff));
  uint32_t x213;
  uint32_t x214;
  fiat_p256_mulx_u32(&x213, &x214, x191, UINT32_C(0xffffffff));
  uint32_t x215;
  uint32_t x216;
  fiat_p256_mulx_u32(&x215, &x216, x191, UINT32_C(0xffffffff));
  uint32_t x217;
  fiat_p256_uint1 x218;
  fiat_p256_addcarryx_u32(&x217, &x218, 0x0, x213, x216);
  uint32_t x219;
  fiat_p256_uint1 x220;
  fiat_p256_addcarryx_u32(&x219, &x220, x218, x211, x214);
  uint32_t x221;
  fiat_p256_uint1 x222;
  fiat_p256_addcarryx_u32(&x221, &x222, x220, 0x0, x212);
  uint32_t x223;
  fiat_p256_uint1 x224;
  fiat_p256_addcarryx_u32(&x223, &x224, 0x0, x215, x191);
  uint32_t x225;
  fiat_p256_uint1 x226;
  fiat_p256_addcarryx_u32(&x225, &x226, x224, x217, x193);
  uint32_t x227;
  fiat_p256_uint1 x228;
  fiat_p256_addcarryx_u32(&x227, &x228, x226, x219, x195);
  uint32_t x229;
  fiat_p256_uint1 x230;
  fiat_p256_addcarryx_u32(&x229, &x230, x228, x221, x197);
  uint32_t x231;
  fiat_p256_uint1 x232;
  fiat_p256_addcarryx_u32(&x231, &x232, x230, 0x0, x199);
  uint32_t x233;
  fiat_p256_uint1 x234;
  fiat_p256_addcarryx_u32(&x233, &x234, x232, 0x0, x201);
  uint32_t x235;
  fiat_p256_uint1 x236;
  fiat_p256_addcarryx_u32(&x235, &x236, x234, x191, x203);
  uint32_t x237;
  fiat_p256_uint1 x238;
  fiat_p256_addcarryx_u32(&x237, &x238, x236, x209, x205);
  uint32_t x239;
  fiat_p256_uint1 x240;
  fiat_p256_addcarryx_u32(&x239, &x240, x238, x210, x207);
  uint32_t x241;
  fiat_p256_uint1 x242;
  fiat_p256_addcarryx_u32(&x241, &x242, x240, 0x0, x208);
  uint32_t x243;
  uint32_t x244;
  fiat_p256_mulx_u32(&x243, &x244, x3, (arg2[7]));
  uint32_t x245;
  uint32_t x246;
  fiat_p256_mulx_u32(&x245, &x246, x3, (arg2[6]));
  uint32_t x247;
  uint32_t x248;
  fiat_p256_mulx_u32(&x247, &x248, x3, (arg2[5]));
  uint32_t x249;
  uint32_t x250;
  fiat_p256_mulx_u32(&x249, &x250, x3, (arg2[4]));
  uint32_t x251;
  uint32_t x252;
  fiat_p256_mulx_u32(&x251, &x252, x3, (arg2[3]));
  uint32_t x253;
  uint32_t x254;
  fiat_p256_mulx_u32(&x253, &x254, x3, (arg2[2]));
  uint32_t x255;
  uint32_t x256;
  fiat_p256_mulx_u32(&x255, &x256, x3, (arg2[1]));
  uint32_t x257;
  uint32_t x258;
  fiat_p256_mulx_u32(&x257, &x258, x3, (arg2[0]));
  uint32_t x259;
  fiat_p256_uint1 x260;
  fiat_p256_addcarryx_u32(&x259, &x260, 0x0, x255, x258);
  uint32_t x261;
  fiat_p256_uint1 x262;
  fiat_p256_addcarryx_u32(&x261, &x262, x260, x253, x256);
  uint32_t x263;
  fiat_p256_uint1 x264;
  fiat_p256_addcarryx_u32(&x263, &x264, x262, x251, x254);
  uint32_t x265;
  fiat_p256_uint1 x266;
  fiat_p256_addcarryx_u32(&x265, &x266, x264, x249, x252);
  uint32_t x267;
  fiat_p256_uint1 x268;
  fiat_p256_addcarryx_u32(&x267, &x268, x266, x247, x250);
  uint32_t x269;
  fiat_p256_uint1 x270;
  fiat_p256_addcarryx_u32(&x269, &x270, x268, x245, x248);
  uint32_t x271;
  fiat_p256_uint1 x272;
  fiat_p256_addcarryx_u32(&x271, &x272, x270, x243, x246);
  uint32_t x273;
  fiat_p256_uint1 x274;
  fiat_p256_addcarryx_u32(&x273, &x274, x272, 0x0, x244);
  uint32_t x275;
  fiat_p256_uint1 x276;
  fiat_p256_addcarryx_u32(&x275, &x276, 0x0, x257, x225);
  uint32_t x277;
  fiat_p256_uint1 x278;
  fiat_p256_addcarryx_u32(&x277, &x278, x276, x259, x227);
  uint32_t x279;
  fiat_p256_uint1 x280;
  fiat_p256_addcarryx_u32(&x279, &x280, x278, x261, x229);
  uint32_t x281;
  fiat_p256_uint1 x282;
  fiat_p256_addcarryx_u32(&x281, &x282, x280, x263, x231);
  uint32_t x283;
  fiat_p256_uint1 x284;
  fiat_p256_addcarryx_u32(&x283, &x284, x282, x265, x233);
  uint32_t x285;
  fiat_p256_uint1 x286;
  fiat_p256_addcarryx_u32(&x285, &x286, x284, x267, x235);
  uint32_t x287;
  fiat_p256_uint1 x288;
  fiat_p256_addcarryx_u32(&x287, &x288, x286, x269, x237);
  uint32_t x289;
  fiat_p256_uint1 x290;
  fiat_p256_addcarryx_u32(&x289, &x290, x288, x271, x239);
  uint32_t x291;
  fiat_p256_uint1 x292;
  fiat_p256_addcarryx_u32(&x291, &x292, x290, x273, x241);
  uint32_t x293;
  uint32_t x294;
  fiat_p256_mulx_u32(&x293, &x294, x275, UINT32_C(0xffffffff));
  uint32_t x295;
  uint32_t x296;
  fiat_p256_mulx_u32(&x295, &x296, x275, UINT32_C(0xffffffff));
  uint32_t x297;
  uint32_t x298;
  fiat_p256_mulx_u32(&x297, &x298, x275, UINT32_C(0xffffffff));
  uint32_t x299;
  uint32_t x300;
  fiat_p256_mulx_u32(&x299, &x300, x275, UINT32_C(0xffffffff));
  uint32_t x301;
  fiat_p256_uint1 x302;
  fiat_p256_addcarryx_u32(&x301, &x302, 0x0, x297, x300);
  uint32_t x303;
  fiat_p256_uint1 x304;
  fiat_p256_addcarryx_u32(&x303, &x304, x302, x295, x298);
  uint32_t x305;
  fiat_p256_uint1 x306;
  fiat_p256_addcarryx_u32(&x305, &x306, x304, 0x0, x296);
  uint32_t x307;
  fiat_p256_uint1 x308;
  fiat_p256_addcarryx_u32(&x307, &x308, 0x0, x299, x275);
  uint32_t x309;
  fiat_p256_uint1 x310;
  fiat_p256_addcarryx_u32(&x309, &x310, x308, x301, x277);
  uint32_t x311;
  fiat_p256_uint1 x312;
  fiat_p256_addcarryx_u32(&x311, &x312, x310, x303, x279);
  uint32_t x313;
  fiat_p256_uint1 x314;
  fiat_p256_addcarryx_u32(&x313, &x314, x312, x305, x281);
  uint32_t x315;
  fiat_p256_uint1 x316;
  fiat_p256_addcarryx_u32(&x315, &x316, x314, 0x0, x283);
  uint32_t x317;
  fiat_p256_uint1 x318;
  fiat_p256_addcarryx_u32(&x317, &x318, x316, 0x0, x285);
  uint32_t x319;
  fiat_p256_uint1 x320;
  fiat_p256_addcarryx_u32(&x319, &x320, x318, x275, x287);
  uint32_t x321;
  fiat_p256_uint1 x322;
  fiat_p256_addcarryx_u32(&x321, &x322, x320, x293, x289);
  uint32_t x323;
  fiat_p256_uint1 x324;
  fiat_p256_addcarryx_u32(&x323, &x324, x322, x294, x291);
  uint32_t x325;
  fiat_p256_uint1 x326;
  fiat_p256_addcarryx_u32(&x325, &x326, x324, 0x0, x292);
  uint32_t x327;
  uint32_t x328;
  fiat_p256_mulx_u32(&x327, &x328, x4, (arg2[7]));
  uint32_t x329;
  uint32_t x330;
  fiat_p256_mulx_u32(&x329, &x330, x4, (arg2[6]));
  uint32_t x331;
  uint32_t x332;
  fiat_p256_mulx_u32(&x331, &x332, x4, (arg2[5]));
  uint32_t x333;
  uint32_t x334;
  fiat_p256_mulx_u32(&x333, &x334, x4, (arg2[4]));
  uint32_t x335;
  uint32_t x336;
  fiat_p256_mulx_u32(&x335, &x336, x4, (arg2[3]));
  uint32_t x337;
  uint32_t x338;
  fiat_p256_mulx_u32(&x337, &x338, x4, (arg2[2]));
  uint32_t x339;
  uint32_t x340;
  fiat_p256_mulx_u32(&x339, &x340, x4, (arg2[1]));
  uint32_t x341;
  uint32_t x342;
  fiat_p256_mulx_u32(&x341, &x342, x4, (arg2[0]));
  uint32_t x343;
  fiat_p256_uint1 x344;
  fiat_p256_addcarryx_u32(&x343, &x344, 0x0, x339, x342);
  uint32_t x345;
  fiat_p256_uint1 x346;
  fiat_p256_addcarryx_u32(&x345, &x346, x344, x337, x340);
  uint32_t x347;
  fiat_p256_uint1 x348;
  fiat_p256_addcarryx_u32(&x347, &x348, x346, x335, x338);
  uint32_t x349;
  fiat_p256_uint1 x350;
  fiat_p256_addcarryx_u32(&x349, &x350, x348, x333, x336);
  uint32_t x351;
  fiat_p256_uint1 x352;
  fiat_p256_addcarryx_u32(&x351, &x352, x350, x331, x334);
  uint32_t x353;
  fiat_p256_uint1 x354;
  fiat_p256_addcarryx_u32(&x353, &x354, x352, x329, x332);
  uint32_t x355;
  fiat_p256_uint1 x356;
  fiat_p256_addcarryx_u32(&x355, &x356, x354, x327, x330);
  uint32_t x357;
  fiat_p256_uint1 x358;
  fiat_p256_addcarryx_u32(&x357, &x358, x356, 0x0, x328);
  uint32_t x359;
  fiat_p256_uint1 x360;
  fiat_p256_addcarryx_u32(&x359, &x360, 0x0, x341, x309);
  uint32_t x361;
  fiat_p256_uint1 x362;
  fiat_p256_addcarryx_u32(&x361, &x362, x360, x343, x311);
  uint32_t x363;
  fiat_p256_uint1 x364;
  fiat_p256_addcarryx_u32(&x363, &x364, x362, x345, x313);
  uint32_t x365;
  fiat_p256_uint1 x366;
  fiat_p256_addcarryx_u32(&x365, &x366, x364, x347, x315);
  uint32_t x367;
  fiat_p256_uint1 x368;
  fiat_p256_addcarryx_u32(&x367, &x368, x366, x349, x317);
  uint32_t x369;
  fiat_p256_uint1 x370;
  fiat_p256_addcarryx_u32(&x369, &x370, x368, x351, x319);
  uint32_t x371;
  fiat_p256_uint1 x372;
  fiat_p256_addcarryx_u32(&x371, &x372, x370, x353, x321);
  uint32_t x373;
  fiat_p256_uint1 x374;
  fiat_p256_addcarryx_u32(&x373, &x374, x372, x355, x323);
  uint32_t x375;
  fiat_p256_uint1 x376;
  fiat_p256_addcarryx_u32(&x375, &x376, x374, x357, x325);
  uint32_t x377;
  uint32_t x378;
  fiat_p256_mulx_u32(&x377, &x378, x359, UINT32_C(0xffffffff));
  uint32_t x379;
  uint32_t x380;
  fiat_p256_mulx_u32(&x379, &x380, x359, UINT32_C(0xffffffff));
  uint32_t x381;
  uint32_t x382;
  fiat_p256_mulx_u32(&x381, &x382, x359, UINT32_C(0xffffffff));
  uint32_t x383;
  uint32_t x384;
  fiat_p256_mulx_u32(&x383, &x384, x359, UINT32_C(0xffffffff));
  uint32_t x385;
  fiat_p256_uint1 x386;
  fiat_p256_addcarryx_u32(&x385, &x386, 0x0, x381, x384);
  uint32_t x387;
  fiat_p256_uint1 x388;
  fiat_p256_addcarryx_u32(&x387, &x388, x386, x379, x382);
  uint32_t x389;
  fiat_p256_uint1 x390;
  fiat_p256_addcarryx_u32(&x389, &x390, x388, 0x0, x380);
  uint32_t x391;
  fiat_p256_uint1 x392;
  fiat_p256_addcarryx_u32(&x391, &x392, 0x0, x383, x359);
  uint32_t x393;
  fiat_p256_uint1 x394;
  fiat_p256_addcarryx_u32(&x393, &x394, x392, x385, x361);
  uint32_t x395;
  fiat_p256_uint1 x396;
  fiat_p256_addcarryx_u32(&x395, &x396, x394, x387, x363);
  uint32_t x397;
  fiat_p256_uint1 x398;
  fiat_p256_addcarryx_u32(&x397, &x398, x396, x389, x365);
  uint32_t x399;
  fiat_p256_uint1 x400;
  fiat_p256_addcarryx_u32(&x399, &x400, x398, 0x0, x367);
  uint32_t x401;
  fiat_p256_uint1 x402;
  fiat_p256_addcarryx_u32(&x401, &x402, x400, 0x0, x369);
  uint32_t x403;
  fiat_p256_uint1 x404;
  fiat_p256_addcarryx_u32(&x403, &x404, x402, x359, x371);
  uint32_t x405;
  fiat_p256_uint1 x406;
  fiat_p256_addcarryx_u32(&x405, &x406, x404, x377, x373);
  uint32_t x407;
  fiat_p256_uint1 x408;
  fiat_p256_addcarryx_u32(&x407, &x408, x406, x378, x375);
  uint32_t x409;
  fiat_p256_uint1 x410;
  fiat_p256_addcarryx_u32(&x409, &x410, x408, 0x0, x376);
  uint32_t x411;
  uint32_t x412;
  fiat_p256_mulx_u32(&x411, &x412, x5, (arg2[7]));
  uint32_t x413;
  uint32_t x414;
  fiat_p256_mulx_u32(&x413, &x414, x5, (arg2[6]));
  uint32_t x415;
  uint32_t x416;
  fiat_p256_mulx_u32(&x415, &x416, x5, (arg2[5]));
  uint32_t x417;
  uint32_t x418;
  fiat_p256_mulx_u32(&x417, &x418, x5, (arg2[4]));
  uint32_t x419;
  uint32_t x420;
  fiat_p256_mulx_u32(&x419, &x420, x5, (arg2[3]));
  uint32_t x421;
  uint32_t x422;
  fiat_p256_mulx_u32(&x421, &x422, x5, (arg2[2]));
  uint32_t x423;
  uint32_t x424;
  fiat_p256_mulx_u32(&x423, &x424, x5, (arg2[1]));
  uint32_t x425;
  uint32_t x426;
  fiat_p256_mulx_u32(&x425, &x426, x5, (arg2[0]));
  uint32_t x427;
  fiat_p256_uint1 x428;
  fiat_p256_addcarryx_u32(&x427, &x428, 0x0, x423, x426);
  uint32_t x429;
  fiat_p256_uint1 x430;
  fiat_p256_addcarryx_u32(&x429, &x430, x428, x421, x424);
  uint32_t x431;
  fiat_p256_uint1 x432;
  fiat_p256_addcarryx_u32(&x431, &x432, x430, x419, x422);
  uint32_t x433;
  fiat_p256_uint1 x434;
  fiat_p256_addcarryx_u32(&x433, &x434, x432, x417, x420);
  uint32_t x435;
  fiat_p256_uint1 x436;
  fiat_p256_addcarryx_u32(&x435, &x436, x434, x415, x418);
  uint32_t x437;
  fiat_p256_uint1 x438;
  fiat_p256_addcarryx_u32(&x437, &x438, x436, x413, x416);
  uint32_t x439;
  fiat_p256_uint1 x440;
  fiat_p256_addcarryx_u32(&x439, &x440, x438, x411, x414);
  uint32_t x441;
  fiat_p256_uint1 x442;
  fiat_p256_addcarryx_u32(&x441, &x442, x440, 0x0, x412);
  uint32_t x443;
  fiat_p256_uint1 x444;
  fiat_p256_addcarryx_u32(&x443, &x444, 0x0, x425, x393);
  uint32_t x445;
  fiat_p256_uint1 x446;
  fiat_p256_addcarryx_u32(&x445, &x446, x444, x427, x395);
  uint32_t x447;
  fiat_p256_uint1 x448;
  fiat_p256_addcarryx_u32(&x447, &x448, x446, x429, x397);
  uint32_t x449;
  fiat_p256_uint1 x450;
  fiat_p256_addcarryx_u32(&x449, &x450, x448, x431, x399);
  uint32_t x451;
  fiat_p256_uint1 x452;
  fiat_p256_addcarryx_u32(&x451, &x452, x450, x433, x401);
  uint32_t x453;
  fiat_p256_uint1 x454;
  fiat_p256_addcarryx_u32(&x453, &x454, x452, x435, x403);
  uint32_t x455;
  fiat_p256_uint1 x456;
  fiat_p256_addcarryx_u32(&x455, &x456, x454, x437, x405);
  uint32_t x457;
  fiat_p256_uint1 x458;
  fiat_p256_addcarryx_u32(&x457, &x458, x456, x439, x407);
  uint32_t x459;
  fiat_p256_uint1 x460;
  fiat_p256_addcarryx_u32(&x459, &x460, x458, x441, x409);
  uint32_t x461;
  uint32_t x462;
  fiat_p256_mulx_u32(&x461, &x462, x443, UINT32_C(0xffffffff));
  uint32_t x463;
  uint32_t x464;
  fiat_p256_mulx_u32(&x463, &x464, x443, UINT32_C(0xffffffff));
  uint32_t x465;
  uint32_t x466;
  fiat_p256_mulx_u32(&x465, &x466, x443, UINT32_C(0xffffffff));
  uint32_t x467;
  uint32_t x468;
  fiat_p256_mulx_u32(&x467, &x468, x443, UINT32_C(0xffffffff));
  uint32_t x469;
  fiat_p256_uint1 x470;
  fiat_p256_addcarryx_u32(&x469, &x470, 0x0, x465, x468);
  uint32_t x471;
  fiat_p256_uint1 x472;
  fiat_p256_addcarryx_u32(&x471, &x472, x470, x463, x466);
  uint32_t x473;
  fiat_p256_uint1 x474;
  fiat_p256_addcarryx_u32(&x473, &x474, x472, 0x0, x464);
  uint32_t x475;
  fiat_p256_uint1 x476;
  fiat_p256_addcarryx_u32(&x475, &x476, 0x0, x467, x443);
  uint32_t x477;
  fiat_p256_uint1 x478;
  fiat_p256_addcarryx_u32(&x477, &x478, x476, x469, x445);
  uint32_t x479;
  fiat_p256_uint1 x480;
  fiat_p256_addcarryx_u32(&x479, &x480, x478, x471, x447);
  uint32_t x481;
  fiat_p256_uint1 x482;
  fiat_p256_addcarryx_u32(&x481, &x482, x480, x473, x449);
  uint32_t x483;
  fiat_p256_uint1 x484;
  fiat_p256_addcarryx_u32(&x483, &x484, x482, 0x0, x451);
  uint32_t x485;
  fiat_p256_uint1 x486;
  fiat_p256_addcarryx_u32(&x485, &x486, x484, 0x0, x453);
  uint32_t x487;
  fiat_p256_uint1 x488;
  fiat_p256_addcarryx_u32(&x487, &x488, x486, x443, x455);
  uint32_t x489;
  fiat_p256_uint1 x490;
  fiat_p256_addcarryx_u32(&x489, &x490, x488, x461, x457);
  uint32_t x491;
  fiat_p256_uint1 x492;
  fiat_p256_addcarryx_u32(&x491, &x492, x490, x462, x459);
  uint32_t x493;
  fiat_p256_uint1 x494;
  fiat_p256_addcarryx_u32(&x493, &x494, x492, 0x0, x460);
  uint32_t x495;
  uint32_t x496;
  fiat_p256_mulx_u32(&x495, &x496, x6, (arg2[7]));
  uint32_t x497;
  uint32_t x498;
  fiat_p256_mulx_u32(&x497, &x498, x6, (arg2[6]));
  uint32_t x499;
  uint32_t x500;
  fiat_p256_mulx_u32(&x499, &x500, x6, (arg2[5]));
  uint32_t x501;
  uint32_t x502;
  fiat_p256_mulx_u32(&x501, &x502, x6, (arg2[4]));
  uint32_t x503;
  uint32_t x504;
  fiat_p256_mulx_u32(&x503, &x504, x6, (arg2[3]));
  uint32_t x505;
  uint32_t x506;
  fiat_p256_mulx_u32(&x505, &x506, x6, (arg2[2]));
  uint32_t x507;
  uint32_t x508;
  fiat_p256_mulx_u32(&x507, &x508, x6, (arg2[1]));
  uint32_t x509;
  uint32_t x510;
  fiat_p256_mulx_u32(&x509, &x510, x6, (arg2[0]));
  uint32_t x511;
  fiat_p256_uint1 x512;
  fiat_p256_addcarryx_u32(&x511, &x512, 0x0, x507, x510);
  uint32_t x513;
  fiat_p256_uint1 x514;
  fiat_p256_addcarryx_u32(&x513, &x514, x512, x505, x508);
  uint32_t x515;
  fiat_p256_uint1 x516;
  fiat_p256_addcarryx_u32(&x515, &x516, x514, x503, x506);
  uint32_t x517;
  fiat_p256_uint1 x518;
  fiat_p256_addcarryx_u32(&x517, &x518, x516, x501, x504);
  uint32_t x519;
  fiat_p256_uint1 x520;
  fiat_p256_addcarryx_u32(&x519, &x520, x518, x499, x502);
  uint32_t x521;
  fiat_p256_uint1 x522;
  fiat_p256_addcarryx_u32(&x521, &x522, x520, x497, x500);
  uint32_t x523;
  fiat_p256_uint1 x524;
  fiat_p256_addcarryx_u32(&x523, &x524, x522, x495, x498);
  uint32_t x525;
  fiat_p256_uint1 x526;
  fiat_p256_addcarryx_u32(&x525, &x526, x524, 0x0, x496);
  uint32_t x527;
  fiat_p256_uint1 x528;
  fiat_p256_addcarryx_u32(&x527, &x528, 0x0, x509, x477);
  uint32_t x529;
  fiat_p256_uint1 x530;
  fiat_p256_addcarryx_u32(&x529, &x530, x528, x511, x479);
  uint32_t x531;
  fiat_p256_uint1 x532;
  fiat_p256_addcarryx_u32(&x531, &x532, x530, x513, x481);
  uint32_t x533;
  fiat_p256_uint1 x534;
  fiat_p256_addcarryx_u32(&x533, &x534, x532, x515, x483);
  uint32_t x535;
  fiat_p256_uint1 x536;
  fiat_p256_addcarryx_u32(&x535, &x536, x534, x517, x485);
  uint32_t x537;
  fiat_p256_uint1 x538;
  fiat_p256_addcarryx_u32(&x537, &x538, x536, x519, x487);
  uint32_t x539;
  fiat_p256_uint1 x540;
  fiat_p256_addcarryx_u32(&x539, &x540, x538, x521, x489);
  uint32_t x541;
  fiat_p256_uint1 x542;
  fiat_p256_addcarryx_u32(&x541, &x542, x540, x523, x491);
  uint32_t x543;
  fiat_p256_uint1 x544;
  fiat_p256_addcarryx_u32(&x543, &x544, x542, x525, x493);
  uint32_t x545;
  uint32_t x546;
  fiat_p256_mulx_u32(&x545, &x546, x527, UINT32_C(0xffffffff));
  uint32_t x547;
  uint32_t x548;
  fiat_p256_mulx_u32(&x547, &x548, x527, UINT32_C(0xffffffff));
  uint32_t x549;
  uint32_t x550;
  fiat_p256_mulx_u32(&x549, &x550, x527, UINT32_C(0xffffffff));
  uint32_t x551;
  uint32_t x552;
  fiat_p256_mulx_u32(&x551, &x552, x527, UINT32_C(0xffffffff));
  uint32_t x553;
  fiat_p256_uint1 x554;
  fiat_p256_addcarryx_u32(&x553, &x554, 0x0, x549, x552);
  uint32_t x555;
  fiat_p256_uint1 x556;
  fiat_p256_addcarryx_u32(&x555, &x556, x554, x547, x550);
  uint32_t x557;
  fiat_p256_uint1 x558;
  fiat_p256_addcarryx_u32(&x557, &x558, x556, 0x0, x548);
  uint32_t x559;
  fiat_p256_uint1 x560;
  fiat_p256_addcarryx_u32(&x559, &x560, 0x0, x551, x527);
  uint32_t x561;
  fiat_p256_uint1 x562;
  fiat_p256_addcarryx_u32(&x561, &x562, x560, x553, x529);
  uint32_t x563;
  fiat_p256_uint1 x564;
  fiat_p256_addcarryx_u32(&x563, &x564, x562, x555, x531);
  uint32_t x565;
  fiat_p256_uint1 x566;
  fiat_p256_addcarryx_u32(&x565, &x566, x564, x557, x533);
  uint32_t x567;
  fiat_p256_uint1 x568;
  fiat_p256_addcarryx_u32(&x567, &x568, x566, 0x0, x535);
  uint32_t x569;
  fiat_p256_uint1 x570;
  fiat_p256_addcarryx_u32(&x569, &x570, x568, 0x0, x537);
  uint32_t x571;
  fiat_p256_uint1 x572;
  fiat_p256_addcarryx_u32(&x571, &x572, x570, x527, x539);
  uint32_t x573;
  fiat_p256_uint1 x574;
  fiat_p256_addcarryx_u32(&x573, &x574, x572, x545, x541);
  uint32_t x575;
  fiat_p256_uint1 x576;
  fiat_p256_addcarryx_u32(&x575, &x576, x574, x546, x543);
  uint32_t x577;
  fiat_p256_uint1 x578;
  fiat_p256_addcarryx_u32(&x577, &x578, x576, 0x0, x544);
  uint32_t x579;
  uint32_t x580;
  fiat_p256_mulx_u32(&x579, &x580, x7, (arg2[7]));
  uint32_t x581;
  uint32_t x582;
  fiat_p256_mulx_u32(&x581, &x582, x7, (arg2[6]));
  uint32_t x583;
  uint32_t x584;
  fiat_p256_mulx_u32(&x583, &x584, x7, (arg2[5]));
  uint32_t x585;
  uint32_t x586;
  fiat_p256_mulx_u32(&x585, &x586, x7, (arg2[4]));
  uint32_t x587;
  uint32_t x588;
  fiat_p256_mulx_u32(&x587, &x588, x7, (arg2[3]));
  uint32_t x589;
  uint32_t x590;
  fiat_p256_mulx_u32(&x589, &x590, x7, (arg2[2]));
  uint32_t x591;
  uint32_t x592;
  fiat_p256_mulx_u32(&x591, &x592, x7, (arg2[1]));
  uint32_t x593;
  uint32_t x594;
  fiat_p256_mulx_u32(&x593, &x594, x7, (arg2[0]));
  uint32_t x595;
  fiat_p256_uint1 x596;
  fiat_p256_addcarryx_u32(&x595, &x596, 0x0, x591, x594);
  uint32_t x597;
  fiat_p256_uint1 x598;
  fiat_p256_addcarryx_u32(&x597, &x598, x596, x589, x592);
  uint32_t x599;
  fiat_p256_uint1 x600;
  fiat_p256_addcarryx_u32(&x599, &x600, x598, x587, x590);
  uint32_t x601;
  fiat_p256_uint1 x602;
  fiat_p256_addcarryx_u32(&x601, &x602, x600, x585, x588);
  uint32_t x603;
  fiat_p256_uint1 x604;
  fiat_p256_addcarryx_u32(&x603, &x604, x602, x583, x586);
  uint32_t x605;
  fiat_p256_uint1 x606;
  fiat_p256_addcarryx_u32(&x605, &x606, x604, x581, x584);
  uint32_t x607;
  fiat_p256_uint1 x608;
  fiat_p256_addcarryx_u32(&x607, &x608, x606, x579, x582);
  uint32_t x609;
  fiat_p256_uint1 x610;
  fiat_p256_addcarryx_u32(&x609, &x610, x608, 0x0, x580);
  uint32_t x611;
  fiat_p256_uint1 x612;
  fiat_p256_addcarryx_u32(&x611, &x612, 0x0, x593, x561);
  uint32_t x613;
  fiat_p256_uint1 x614;
  fiat_p256_addcarryx_u32(&x613, &x614, x612, x595, x563);
  uint32_t x615;
  fiat_p256_uint1 x616;
  fiat_p256_addcarryx_u32(&x615, &x616, x614, x597, x565);
  uint32_t x617;
  fiat_p256_uint1 x618;
  fiat_p256_addcarryx_u32(&x617, &x618, x616, x599, x567);
  uint32_t x619;
  fiat_p256_uint1 x620;
  fiat_p256_addcarryx_u32(&x619, &x620, x618, x601, x569);
  uint32_t x621;
  fiat_p256_uint1 x622;
  fiat_p256_addcarryx_u32(&x621, &x622, x620, x603, x571);
  uint32_t x623;
  fiat_p256_uint1 x624;
  fiat_p256_addcarryx_u32(&x623, &x624, x622, x605, x573);
  uint32_t x625;
  fiat_p256_uint1 x626;
  fiat_p256_addcarryx_u32(&x625, &x626, x624, x607, x575);
  uint32_t x627;
  fiat_p256_uint1 x628;
  fiat_p256_addcarryx_u32(&x627, &x628, x626, x609, x577);
  uint32_t x629;
  uint32_t x630;
  fiat_p256_mulx_u32(&x629, &x630, x611, UINT32_C(0xffffffff));
  uint32_t x631;
  uint32_t x632;
  fiat_p256_mulx_u32(&x631, &x632, x611, UINT32_C(0xffffffff));
  uint32_t x633;
  uint32_t x634;
  fiat_p256_mulx_u32(&x633, &x634, x611, UINT32_C(0xffffffff));
  uint32_t x635;
  uint32_t x636;
  fiat_p256_mulx_u32(&x635, &x636, x611, UINT32_C(0xffffffff));
  uint32_t x637;
  fiat_p256_uint1 x638;
  fiat_p256_addcarryx_u32(&x637, &x638, 0x0, x633, x636);
  uint32_t x639;
  fiat_p256_uint1 x640;
  fiat_p256_addcarryx_u32(&x639, &x640, x638, x631, x634);
  uint32_t x641;
  fiat_p256_uint1 x642;
  fiat_p256_addcarryx_u32(&x641, &x642, x640, 0x0, x632);
  uint32_t x643;
  fiat_p256_uint1 x644;
  fiat_p256_addcarryx_u32(&x643, &x644, 0x0, x635, x611);
  uint32_t x645;
  fiat_p256_uint1 x646;
  fiat_p256_addcarryx_u32(&x645, &x646, x644, x637, x613);
  uint32_t x647;
  fiat_p256_uint1 x648;
  fiat_p256_addcarryx_u32(&x647, &x648, x646, x639, x615);
  uint32_t x649;
  fiat_p256_uint1 x650;
  fiat_p256_addcarryx_u32(&x649, &x650, x648, x641, x617);
  uint32_t x651;
  fiat_p256_uint1 x652;
  fiat_p256_addcarryx_u32(&x651, &x652, x650, 0x0, x619);
  uint32_t x653;
  fiat_p256_uint1 x654;
  fiat_p256_addcarryx_u32(&x653, &x654, x652, 0x0, x621);
  uint32_t x655;
  fiat_p256_uint1 x656;
  fiat_p256_addcarryx_u32(&x655, &x656, x654, x611, x623);
  uint32_t x657;
  fiat_p256_uint1 x658;
  fiat_p256_addcarryx_u32(&x657, &x658, x656, x629, x625);
  uint32_t x659;
  fiat_p256_uint1 x660;
  fiat_p256_addcarryx_u32(&x659, &x660, x658, x630, x627);
  uint32_t x661;
  fiat_p256_uint1 x662;
  fiat_p256_addcarryx_u32(&x661, &x662, x660, 0x0, x628);
  uint32_t x663;
  fiat_p256_uint1 x664;
  fiat_p256_subborrowx_u32(&x663, &x664, 0x0, x645, UINT32_C(0xffffffff));
  uint32_t x665;
  fiat_p256_uint1 x666;
  fiat_p256_subborrowx_u32(&x665, &x666, x664, x647, UINT32_C(0xffffffff));
  uint32_t x667;
  fiat_p256_uint1 x668;
  fiat_p256_subborrowx_u32(&x667, &x668, x666, x649, UINT32_C(0xffffffff));
  uint32_t x669;
  fiat_p256_uint1 x670;
  fiat_p256_subborrowx_u32(&x669, &x670, x668, x651, 0x0);
  uint32_t x671;
  fiat_p256_uint1 x672;
  fiat_p256_subborrowx_u32(&x671, &x672, x670, x653, 0x0);
  uint32_t x673;
  fiat_p256_uint1 x674;
  fiat_p256_subborrowx_u32(&x673, &x674, x672, x655, 0x0);
  uint32_t x675;
  fiat_p256_uint1 x676;
  fiat_p256_subborrowx_u32(&x675, &x676, x674, x657, 0x1);
  uint32_t x677;
  fiat_p256_uint1 x678;
  fiat_p256_subborrowx_u32(&x677, &x678, x676, x659, UINT32_C(0xffffffff));
  uint32_t x679;
  fiat_p256_uint1 x680;
  fiat_p256_subborrowx_u32(&x679, &x680, x678, x661, 0x0);
  uint32_t x681;
  fiat_p256_cmovznz_u32(&x681, x680, x663, x645);
  uint32_t x682;
  fiat_p256_cmovznz_u32(&x682, x680, x665, x647);
  uint32_t x683;
  fiat_p256_cmovznz_u32(&x683, x680, x667, x649);
  uint32_t x684;
  fiat_p256_cmovznz_u32(&x684, x680, x669, x651);
  uint32_t x685;
  fiat_p256_cmovznz_u32(&x685, x680, x671, x653);
  uint32_t x686;
  fiat_p256_cmovznz_u32(&x686, x680, x673, x655);
  uint32_t x687;
  fiat_p256_cmovznz_u32(&x687, x680, x675, x657);
  uint32_t x688;
  fiat_p256_cmovznz_u32(&x688, x680, x677, x659);
  out1[0] = x681;
  out1[1] = x682;
  out1[2] = x683;
  out1[3] = x684;
  out1[4] = x685;
  out1[5] = x686;
  out1[6] = x687;
  out1[7] = x688;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 */
static void fiat_p256_square(uint32_t out1[8], const uint32_t arg1[8]) {
  uint32_t x1 = (arg1[1]);
  uint32_t x2 = (arg1[2]);
  uint32_t x3 = (arg1[3]);
  uint32_t x4 = (arg1[4]);
  uint32_t x5 = (arg1[5]);
  uint32_t x6 = (arg1[6]);
  uint32_t x7 = (arg1[7]);
  uint32_t x8 = (arg1[0]);
  uint32_t x9;
  uint32_t x10;
  fiat_p256_mulx_u32(&x9, &x10, x8, (arg1[7]));
  uint32_t x11;
  uint32_t x12;
  fiat_p256_mulx_u32(&x11, &x12, x8, (arg1[6]));
  uint32_t x13;
  uint32_t x14;
  fiat_p256_mulx_u32(&x13, &x14, x8, (arg1[5]));
  uint32_t x15;
  uint32_t x16;
  fiat_p256_mulx_u32(&x15, &x16, x8, (arg1[4]));
  uint32_t x17;
  uint32_t x18;
  fiat_p256_mulx_u32(&x17, &x18, x8, (arg1[3]));
  uint32_t x19;
  uint32_t x20;
  fiat_p256_mulx_u32(&x19, &x20, x8, (arg1[2]));
  uint32_t x21;
  uint32_t x22;
  fiat_p256_mulx_u32(&x21, &x22, x8, (arg1[1]));
  uint32_t x23;
  uint32_t x24;
  fiat_p256_mulx_u32(&x23, &x24, x8, (arg1[0]));
  uint32_t x25;
  fiat_p256_uint1 x26;
  fiat_p256_addcarryx_u32(&x25, &x26, 0x0, x21, x24);
  uint32_t x27;
  fiat_p256_uint1 x28;
  fiat_p256_addcarryx_u32(&x27, &x28, x26, x19, x22);
  uint32_t x29;
  fiat_p256_uint1 x30;
  fiat_p256_addcarryx_u32(&x29, &x30, x28, x17, x20);
  uint32_t x31;
  fiat_p256_uint1 x32;
  fiat_p256_addcarryx_u32(&x31, &x32, x30, x15, x18);
  uint32_t x33;
  fiat_p256_uint1 x34;
  fiat_p256_addcarryx_u32(&x33, &x34, x32, x13, x16);
  uint32_t x35;
  fiat_p256_uint1 x36;
  fiat_p256_addcarryx_u32(&x35, &x36, x34, x11, x14);
  uint32_t x37;
  fiat_p256_uint1 x38;
  fiat_p256_addcarryx_u32(&x37, &x38, x36, x9, x12);
  uint32_t x39;
  fiat_p256_uint1 x40;
  fiat_p256_addcarryx_u32(&x39, &x40, x38, 0x0, x10);
  uint32_t x41;
  uint32_t x42;
  fiat_p256_mulx_u32(&x41, &x42, x23, UINT32_C(0xffffffff));
  uint32_t x43;
  uint32_t x44;
  fiat_p256_mulx_u32(&x43, &x44, x23, UINT32_C(0xffffffff));
  uint32_t x45;
  uint32_t x46;
  fiat_p256_mulx_u32(&x45, &x46, x23, UINT32_C(0xffffffff));
  uint32_t x47;
  uint32_t x48;
  fiat_p256_mulx_u32(&x47, &x48, x23, UINT32_C(0xffffffff));
  uint32_t x49;
  fiat_p256_uint1 x50;
  fiat_p256_addcarryx_u32(&x49, &x50, 0x0, x45, x48);
  uint32_t x51;
  fiat_p256_uint1 x52;
  fiat_p256_addcarryx_u32(&x51, &x52, x50, x43, x46);
  uint32_t x53;
  fiat_p256_uint1 x54;
  fiat_p256_addcarryx_u32(&x53, &x54, x52, 0x0, x44);
  uint32_t x55;
  fiat_p256_uint1 x56;
  fiat_p256_addcarryx_u32(&x55, &x56, 0x0, x47, x23);
  uint32_t x57;
  fiat_p256_uint1 x58;
  fiat_p256_addcarryx_u32(&x57, &x58, x56, x49, x25);
  uint32_t x59;
  fiat_p256_uint1 x60;
  fiat_p256_addcarryx_u32(&x59, &x60, x58, x51, x27);
  uint32_t x61;
  fiat_p256_uint1 x62;
  fiat_p256_addcarryx_u32(&x61, &x62, x60, x53, x29);
  uint32_t x63;
  fiat_p256_uint1 x64;
  fiat_p256_addcarryx_u32(&x63, &x64, x62, 0x0, x31);
  uint32_t x65;
  fiat_p256_uint1 x66;
  fiat_p256_addcarryx_u32(&x65, &x66, x64, 0x0, x33);
  uint32_t x67;
  fiat_p256_uint1 x68;
  fiat_p256_addcarryx_u32(&x67, &x68, x66, x23, x35);
  uint32_t x69;
  fiat_p256_uint1 x70;
  fiat_p256_addcarryx_u32(&x69, &x70, x68, x41, x37);
  uint32_t x71;
  fiat_p256_uint1 x72;
  fiat_p256_addcarryx_u32(&x71, &x72, x70, x42, x39);
  uint32_t x73;
  fiat_p256_uint1 x74;
  fiat_p256_addcarryx_u32(&x73, &x74, x72, 0x0, 0x0);
  uint32_t x75;
  uint32_t x76;
  fiat_p256_mulx_u32(&x75, &x76, x1, (arg1[7]));
  uint32_t x77;
  uint32_t x78;
  fiat_p256_mulx_u32(&x77, &x78, x1, (arg1[6]));
  uint32_t x79;
  uint32_t x80;
  fiat_p256_mulx_u32(&x79, &x80, x1, (arg1[5]));
  uint32_t x81;
  uint32_t x82;
  fiat_p256_mulx_u32(&x81, &x82, x1, (arg1[4]));
  uint32_t x83;
  uint32_t x84;
  fiat_p256_mulx_u32(&x83, &x84, x1, (arg1[3]));
  uint32_t x85;
  uint32_t x86;
  fiat_p256_mulx_u32(&x85, &x86, x1, (arg1[2]));
  uint32_t x87;
  uint32_t x88;
  fiat_p256_mulx_u32(&x87, &x88, x1, (arg1[1]));
  uint32_t x89;
  uint32_t x90;
  fiat_p256_mulx_u32(&x89, &x90, x1, (arg1[0]));
  uint32_t x91;
  fiat_p256_uint1 x92;
  fiat_p256_addcarryx_u32(&x91, &x92, 0x0, x87, x90);
  uint32_t x93;
  fiat_p256_uint1 x94;
  fiat_p256_addcarryx_u32(&x93, &x94, x92, x85, x88);
  uint32_t x95;
  fiat_p256_uint1 x96;
  fiat_p256_addcarryx_u32(&x95, &x96, x94, x83, x86);
  uint32_t x97;
  fiat_p256_uint1 x98;
  fiat_p256_addcarryx_u32(&x97, &x98, x96, x81, x84);
  uint32_t x99;
  fiat_p256_uint1 x100;
  fiat_p256_addcarryx_u32(&x99, &x100, x98, x79, x82);
  uint32_t x101;
  fiat_p256_uint1 x102;
  fiat_p256_addcarryx_u32(&x101, &x102, x100, x77, x80);
  uint32_t x103;
  fiat_p256_uint1 x104;
  fiat_p256_addcarryx_u32(&x103, &x104, x102, x75, x78);
  uint32_t x105;
  fiat_p256_uint1 x106;
  fiat_p256_addcarryx_u32(&x105, &x106, x104, 0x0, x76);
  uint32_t x107;
  fiat_p256_uint1 x108;
  fiat_p256_addcarryx_u32(&x107, &x108, 0x0, x89, x57);
  uint32_t x109;
  fiat_p256_uint1 x110;
  fiat_p256_addcarryx_u32(&x109, &x110, x108, x91, x59);
  uint32_t x111;
  fiat_p256_uint1 x112;
  fiat_p256_addcarryx_u32(&x111, &x112, x110, x93, x61);
  uint32_t x113;
  fiat_p256_uint1 x114;
  fiat_p256_addcarryx_u32(&x113, &x114, x112, x95, x63);
  uint32_t x115;
  fiat_p256_uint1 x116;
  fiat_p256_addcarryx_u32(&x115, &x116, x114, x97, x65);
  uint32_t x117;
  fiat_p256_uint1 x118;
  fiat_p256_addcarryx_u32(&x117, &x118, x116, x99, x67);
  uint32_t x119;
  fiat_p256_uint1 x120;
  fiat_p256_addcarryx_u32(&x119, &x120, x118, x101, x69);
  uint32_t x121;
  fiat_p256_uint1 x122;
  fiat_p256_addcarryx_u32(&x121, &x122, x120, x103, x71);
  uint32_t x123;
  fiat_p256_uint1 x124;
  fiat_p256_addcarryx_u32(&x123, &x124, x122, x105, (fiat_p256_uint1)x73);
  uint32_t x125;
  uint32_t x126;
  fiat_p256_mulx_u32(&x125, &x126, x107, UINT32_C(0xffffffff));
  uint32_t x127;
  uint32_t x128;
  fiat_p256_mulx_u32(&x127, &x128, x107, UINT32_C(0xffffffff));
  uint32_t x129;
  uint32_t x130;
  fiat_p256_mulx_u32(&x129, &x130, x107, UINT32_C(0xffffffff));
  uint32_t x131;
  uint32_t x132;
  fiat_p256_mulx_u32(&x131, &x132, x107, UINT32_C(0xffffffff));
  uint32_t x133;
  fiat_p256_uint1 x134;
  fiat_p256_addcarryx_u32(&x133, &x134, 0x0, x129, x132);
  uint32_t x135;
  fiat_p256_uint1 x136;
  fiat_p256_addcarryx_u32(&x135, &x136, x134, x127, x130);
  uint32_t x137;
  fiat_p256_uint1 x138;
  fiat_p256_addcarryx_u32(&x137, &x138, x136, 0x0, x128);
  uint32_t x139;
  fiat_p256_uint1 x140;
  fiat_p256_addcarryx_u32(&x139, &x140, 0x0, x131, x107);
  uint32_t x141;
  fiat_p256_uint1 x142;
  fiat_p256_addcarryx_u32(&x141, &x142, x140, x133, x109);
  uint32_t x143;
  fiat_p256_uint1 x144;
  fiat_p256_addcarryx_u32(&x143, &x144, x142, x135, x111);
  uint32_t x145;
  fiat_p256_uint1 x146;
  fiat_p256_addcarryx_u32(&x145, &x146, x144, x137, x113);
  uint32_t x147;
  fiat_p256_uint1 x148;
  fiat_p256_addcarryx_u32(&x147, &x148, x146, 0x0, x115);
  uint32_t x149;
  fiat_p256_uint1 x150;
  fiat_p256_addcarryx_u32(&x149, &x150, x148, 0x0, x117);
  uint32_t x151;
  fiat_p256_uint1 x152;
  fiat_p256_addcarryx_u32(&x151, &x152, x150, x107, x119);
  uint32_t x153;
  fiat_p256_uint1 x154;
  fiat_p256_addcarryx_u32(&x153, &x154, x152, x125, x121);
  uint32_t x155;
  fiat_p256_uint1 x156;
  fiat_p256_addcarryx_u32(&x155, &x156, x154, x126, x123);
  uint32_t x157;
  fiat_p256_uint1 x158;
  fiat_p256_addcarryx_u32(&x157, &x158, x156, 0x0, x124);
  uint32_t x159;
  uint32_t x160;
  fiat_p256_mulx_u32(&x159, &x160, x2, (arg1[7]));
  uint32_t x161;
  uint32_t x162;
  fiat_p256_mulx_u32(&x161, &x162, x2, (arg1[6]));
  uint32_t x163;
  uint32_t x164;
  fiat_p256_mulx_u32(&x163, &x164, x2, (arg1[5]));
  uint32_t x165;
  uint32_t x166;
  fiat_p256_mulx_u32(&x165, &x166, x2, (arg1[4]));
  uint32_t x167;
  uint32_t x168;
  fiat_p256_mulx_u32(&x167, &x168, x2, (arg1[3]));
  uint32_t x169;
  uint32_t x170;
  fiat_p256_mulx_u32(&x169, &x170, x2, (arg1[2]));
  uint32_t x171;
  uint32_t x172;
  fiat_p256_mulx_u32(&x171, &x172, x2, (arg1[1]));
  uint32_t x173;
  uint32_t x174;
  fiat_p256_mulx_u32(&x173, &x174, x2, (arg1[0]));
  uint32_t x175;
  fiat_p256_uint1 x176;
  fiat_p256_addcarryx_u32(&x175, &x176, 0x0, x171, x174);
  uint32_t x177;
  fiat_p256_uint1 x178;
  fiat_p256_addcarryx_u32(&x177, &x178, x176, x169, x172);
  uint32_t x179;
  fiat_p256_uint1 x180;
  fiat_p256_addcarryx_u32(&x179, &x180, x178, x167, x170);
  uint32_t x181;
  fiat_p256_uint1 x182;
  fiat_p256_addcarryx_u32(&x181, &x182, x180, x165, x168);
  uint32_t x183;
  fiat_p256_uint1 x184;
  fiat_p256_addcarryx_u32(&x183, &x184, x182, x163, x166);
  uint32_t x185;
  fiat_p256_uint1 x186;
  fiat_p256_addcarryx_u32(&x185, &x186, x184, x161, x164);
  uint32_t x187;
  fiat_p256_uint1 x188;
  fiat_p256_addcarryx_u32(&x187, &x188, x186, x159, x162);
  uint32_t x189;
  fiat_p256_uint1 x190;
  fiat_p256_addcarryx_u32(&x189, &x190, x188, 0x0, x160);
  uint32_t x191;
  fiat_p256_uint1 x192;
  fiat_p256_addcarryx_u32(&x191, &x192, 0x0, x173, x141);
  uint32_t x193;
  fiat_p256_uint1 x194;
  fiat_p256_addcarryx_u32(&x193, &x194, x192, x175, x143);
  uint32_t x195;
  fiat_p256_uint1 x196;
  fiat_p256_addcarryx_u32(&x195, &x196, x194, x177, x145);
  uint32_t x197;
  fiat_p256_uint1 x198;
  fiat_p256_addcarryx_u32(&x197, &x198, x196, x179, x147);
  uint32_t x199;
  fiat_p256_uint1 x200;
  fiat_p256_addcarryx_u32(&x199, &x200, x198, x181, x149);
  uint32_t x201;
  fiat_p256_uint1 x202;
  fiat_p256_addcarryx_u32(&x201, &x202, x200, x183, x151);
  uint32_t x203;
  fiat_p256_uint1 x204;
  fiat_p256_addcarryx_u32(&x203, &x204, x202, x185, x153);
  uint32_t x205;
  fiat_p256_uint1 x206;
  fiat_p256_addcarryx_u32(&x205, &x206, x204, x187, x155);
  uint32_t x207;
  fiat_p256_uint1 x208;
  fiat_p256_addcarryx_u32(&x207, &x208, x206, x189, x157);
  uint32_t x209;
  uint32_t x210;
  fiat_p256_mulx_u32(&x209, &x210, x191, UINT32_C(0xffffffff));
  uint32_t x211;
  uint32_t x212;
  fiat_p256_mulx_u32(&x211, &x212, x191, UINT32_C(0xffffffff));
  uint32_t x213;
  uint32_t x214;
  fiat_p256_mulx_u32(&x213, &x214, x191, UINT32_C(0xffffffff));
  uint32_t x215;
  uint32_t x216;
  fiat_p256_mulx_u32(&x215, &x216, x191, UINT32_C(0xffffffff));
  uint32_t x217;
  fiat_p256_uint1 x218;
  fiat_p256_addcarryx_u32(&x217, &x218, 0x0, x213, x216);
  uint32_t x219;
  fiat_p256_uint1 x220;
  fiat_p256_addcarryx_u32(&x219, &x220, x218, x211, x214);
  uint32_t x221;
  fiat_p256_uint1 x222;
  fiat_p256_addcarryx_u32(&x221, &x222, x220, 0x0, x212);
  uint32_t x223;
  fiat_p256_uint1 x224;
  fiat_p256_addcarryx_u32(&x223, &x224, 0x0, x215, x191);
  uint32_t x225;
  fiat_p256_uint1 x226;
  fiat_p256_addcarryx_u32(&x225, &x226, x224, x217, x193);
  uint32_t x227;
  fiat_p256_uint1 x228;
  fiat_p256_addcarryx_u32(&x227, &x228, x226, x219, x195);
  uint32_t x229;
  fiat_p256_uint1 x230;
  fiat_p256_addcarryx_u32(&x229, &x230, x228, x221, x197);
  uint32_t x231;
  fiat_p256_uint1 x232;
  fiat_p256_addcarryx_u32(&x231, &x232, x230, 0x0, x199);
  uint32_t x233;
  fiat_p256_uint1 x234;
  fiat_p256_addcarryx_u32(&x233, &x234, x232, 0x0, x201);
  uint32_t x235;
  fiat_p256_uint1 x236;
  fiat_p256_addcarryx_u32(&x235, &x236, x234, x191, x203);
  uint32_t x237;
  fiat_p256_uint1 x238;
  fiat_p256_addcarryx_u32(&x237, &x238, x236, x209, x205);
  uint32_t x239;
  fiat_p256_uint1 x240;
  fiat_p256_addcarryx_u32(&x239, &x240, x238, x210, x207);
  uint32_t x241;
  fiat_p256_uint1 x242;
  fiat_p256_addcarryx_u32(&x241, &x242, x240, 0x0, x208);
  uint32_t x243;
  uint32_t x244;
  fiat_p256_mulx_u32(&x243, &x244, x3, (arg1[7]));
  uint32_t x245;
  uint32_t x246;
  fiat_p256_mulx_u32(&x245, &x246, x3, (arg1[6]));
  uint32_t x247;
  uint32_t x248;
  fiat_p256_mulx_u32(&x247, &x248, x3, (arg1[5]));
  uint32_t x249;
  uint32_t x250;
  fiat_p256_mulx_u32(&x249, &x250, x3, (arg1[4]));
  uint32_t x251;
  uint32_t x252;
  fiat_p256_mulx_u32(&x251, &x252, x3, (arg1[3]));
  uint32_t x253;
  uint32_t x254;
  fiat_p256_mulx_u32(&x253, &x254, x3, (arg1[2]));
  uint32_t x255;
  uint32_t x256;
  fiat_p256_mulx_u32(&x255, &x256, x3, (arg1[1]));
  uint32_t x257;
  uint32_t x258;
  fiat_p256_mulx_u32(&x257, &x258, x3, (arg1[0]));
  uint32_t x259;
  fiat_p256_uint1 x260;
  fiat_p256_addcarryx_u32(&x259, &x260, 0x0, x255, x258);
  uint32_t x261;
  fiat_p256_uint1 x262;
  fiat_p256_addcarryx_u32(&x261, &x262, x260, x253, x256);
  uint32_t x263;
  fiat_p256_uint1 x264;
  fiat_p256_addcarryx_u32(&x263, &x264, x262, x251, x254);
  uint32_t x265;
  fiat_p256_uint1 x266;
  fiat_p256_addcarryx_u32(&x265, &x266, x264, x249, x252);
  uint32_t x267;
  fiat_p256_uint1 x268;
  fiat_p256_addcarryx_u32(&x267, &x268, x266, x247, x250);
  uint32_t x269;
  fiat_p256_uint1 x270;
  fiat_p256_addcarryx_u32(&x269, &x270, x268, x245, x248);
  uint32_t x271;
  fiat_p256_uint1 x272;
  fiat_p256_addcarryx_u32(&x271, &x272, x270, x243, x246);
  uint32_t x273;
  fiat_p256_uint1 x274;
  fiat_p256_addcarryx_u32(&x273, &x274, x272, 0x0, x244);
  uint32_t x275;
  fiat_p256_uint1 x276;
  fiat_p256_addcarryx_u32(&x275, &x276, 0x0, x257, x225);
  uint32_t x277;
  fiat_p256_uint1 x278;
  fiat_p256_addcarryx_u32(&x277, &x278, x276, x259, x227);
  uint32_t x279;
  fiat_p256_uint1 x280;
  fiat_p256_addcarryx_u32(&x279, &x280, x278, x261, x229);
  uint32_t x281;
  fiat_p256_uint1 x282;
  fiat_p256_addcarryx_u32(&x281, &x282, x280, x263, x231);
  uint32_t x283;
  fiat_p256_uint1 x284;
  fiat_p256_addcarryx_u32(&x283, &x284, x282, x265, x233);
  uint32_t x285;
  fiat_p256_uint1 x286;
  fiat_p256_addcarryx_u32(&x285, &x286, x284, x267, x235);
  uint32_t x287;
  fiat_p256_uint1 x288;
  fiat_p256_addcarryx_u32(&x287, &x288, x286, x269, x237);
  uint32_t x289;
  fiat_p256_uint1 x290;
  fiat_p256_addcarryx_u32(&x289, &x290, x288, x271, x239);
  uint32_t x291;
  fiat_p256_uint1 x292;
  fiat_p256_addcarryx_u32(&x291, &x292, x290, x273, x241);
  uint32_t x293;
  uint32_t x294;
  fiat_p256_mulx_u32(&x293, &x294, x275, UINT32_C(0xffffffff));
  uint32_t x295;
  uint32_t x296;
  fiat_p256_mulx_u32(&x295, &x296, x275, UINT32_C(0xffffffff));
  uint32_t x297;
  uint32_t x298;
  fiat_p256_mulx_u32(&x297, &x298, x275, UINT32_C(0xffffffff));
  uint32_t x299;
  uint32_t x300;
  fiat_p256_mulx_u32(&x299, &x300, x275, UINT32_C(0xffffffff));
  uint32_t x301;
  fiat_p256_uint1 x302;
  fiat_p256_addcarryx_u32(&x301, &x302, 0x0, x297, x300);
  uint32_t x303;
  fiat_p256_uint1 x304;
  fiat_p256_addcarryx_u32(&x303, &x304, x302, x295, x298);
  uint32_t x305;
  fiat_p256_uint1 x306;
  fiat_p256_addcarryx_u32(&x305, &x306, x304, 0x0, x296);
  uint32_t x307;
  fiat_p256_uint1 x308;
  fiat_p256_addcarryx_u32(&x307, &x308, 0x0, x299, x275);
  uint32_t x309;
  fiat_p256_uint1 x310;
  fiat_p256_addcarryx_u32(&x309, &x310, x308, x301, x277);
  uint32_t x311;
  fiat_p256_uint1 x312;
  fiat_p256_addcarryx_u32(&x311, &x312, x310, x303, x279);
  uint32_t x313;
  fiat_p256_uint1 x314;
  fiat_p256_addcarryx_u32(&x313, &x314, x312, x305, x281);
  uint32_t x315;
  fiat_p256_uint1 x316;
  fiat_p256_addcarryx_u32(&x315, &x316, x314, 0x0, x283);
  uint32_t x317;
  fiat_p256_uint1 x318;
  fiat_p256_addcarryx_u32(&x317, &x318, x316, 0x0, x285);
  uint32_t x319;
  fiat_p256_uint1 x320;
  fiat_p256_addcarryx_u32(&x319, &x320, x318, x275, x287);
  uint32_t x321;
  fiat_p256_uint1 x322;
  fiat_p256_addcarryx_u32(&x321, &x322, x320, x293, x289);
  uint32_t x323;
  fiat_p256_uint1 x324;
  fiat_p256_addcarryx_u32(&x323, &x324, x322, x294, x291);
  uint32_t x325;
  fiat_p256_uint1 x326;
  fiat_p256_addcarryx_u32(&x325, &x326, x324, 0x0, x292);
  uint32_t x327;
  uint32_t x328;
  fiat_p256_mulx_u32(&x327, &x328, x4, (arg1[7]));
  uint32_t x329;
  uint32_t x330;
  fiat_p256_mulx_u32(&x329, &x330, x4, (arg1[6]));
  uint32_t x331;
  uint32_t x332;
  fiat_p256_mulx_u32(&x331, &x332, x4, (arg1[5]));
  uint32_t x333;
  uint32_t x334;
  fiat_p256_mulx_u32(&x333, &x334, x4, (arg1[4]));
  uint32_t x335;
  uint32_t x336;
  fiat_p256_mulx_u32(&x335, &x336, x4, (arg1[3]));
  uint32_t x337;
  uint32_t x338;
  fiat_p256_mulx_u32(&x337, &x338, x4, (arg1[2]));
  uint32_t x339;
  uint32_t x340;
  fiat_p256_mulx_u32(&x339, &x340, x4, (arg1[1]));
  uint32_t x341;
  uint32_t x342;
  fiat_p256_mulx_u32(&x341, &x342, x4, (arg1[0]));
  uint32_t x343;
  fiat_p256_uint1 x344;
  fiat_p256_addcarryx_u32(&x343, &x344, 0x0, x339, x342);
  uint32_t x345;
  fiat_p256_uint1 x346;
  fiat_p256_addcarryx_u32(&x345, &x346, x344, x337, x340);
  uint32_t x347;
  fiat_p256_uint1 x348;
  fiat_p256_addcarryx_u32(&x347, &x348, x346, x335, x338);
  uint32_t x349;
  fiat_p256_uint1 x350;
  fiat_p256_addcarryx_u32(&x349, &x350, x348, x333, x336);
  uint32_t x351;
  fiat_p256_uint1 x352;
  fiat_p256_addcarryx_u32(&x351, &x352, x350, x331, x334);
  uint32_t x353;
  fiat_p256_uint1 x354;
  fiat_p256_addcarryx_u32(&x353, &x354, x352, x329, x332);
  uint32_t x355;
  fiat_p256_uint1 x356;
  fiat_p256_addcarryx_u32(&x355, &x356, x354, x327, x330);
  uint32_t x357;
  fiat_p256_uint1 x358;
  fiat_p256_addcarryx_u32(&x357, &x358, x356, 0x0, x328);
  uint32_t x359;
  fiat_p256_uint1 x360;
  fiat_p256_addcarryx_u32(&x359, &x360, 0x0, x341, x309);
  uint32_t x361;
  fiat_p256_uint1 x362;
  fiat_p256_addcarryx_u32(&x361, &x362, x360, x343, x311);
  uint32_t x363;
  fiat_p256_uint1 x364;
  fiat_p256_addcarryx_u32(&x363, &x364, x362, x345, x313);
  uint32_t x365;
  fiat_p256_uint1 x366;
  fiat_p256_addcarryx_u32(&x365, &x366, x364, x347, x315);
  uint32_t x367;
  fiat_p256_uint1 x368;
  fiat_p256_addcarryx_u32(&x367, &x368, x366, x349, x317);
  uint32_t x369;
  fiat_p256_uint1 x370;
  fiat_p256_addcarryx_u32(&x369, &x370, x368, x351, x319);
  uint32_t x371;
  fiat_p256_uint1 x372;
  fiat_p256_addcarryx_u32(&x371, &x372, x370, x353, x321);
  uint32_t x373;
  fiat_p256_uint1 x374;
  fiat_p256_addcarryx_u32(&x373, &x374, x372, x355, x323);
  uint32_t x375;
  fiat_p256_uint1 x376;
  fiat_p256_addcarryx_u32(&x375, &x376, x374, x357, x325);
  uint32_t x377;
  uint32_t x378;
  fiat_p256_mulx_u32(&x377, &x378, x359, UINT32_C(0xffffffff));
  uint32_t x379;
  uint32_t x380;
  fiat_p256_mulx_u32(&x379, &x380, x359, UINT32_C(0xffffffff));
  uint32_t x381;
  uint32_t x382;
  fiat_p256_mulx_u32(&x381, &x382, x359, UINT32_C(0xffffffff));
  uint32_t x383;
  uint32_t x384;
  fiat_p256_mulx_u32(&x383, &x384, x359, UINT32_C(0xffffffff));
  uint32_t x385;
  fiat_p256_uint1 x386;
  fiat_p256_addcarryx_u32(&x385, &x386, 0x0, x381, x384);
  uint32_t x387;
  fiat_p256_uint1 x388;
  fiat_p256_addcarryx_u32(&x387, &x388, x386, x379, x382);
  uint32_t x389;
  fiat_p256_uint1 x390;
  fiat_p256_addcarryx_u32(&x389, &x390, x388, 0x0, x380);
  uint32_t x391;
  fiat_p256_uint1 x392;
  fiat_p256_addcarryx_u32(&x391, &x392, 0x0, x383, x359);
  uint32_t x393;
  fiat_p256_uint1 x394;
  fiat_p256_addcarryx_u32(&x393, &x394, x392, x385, x361);
  uint32_t x395;
  fiat_p256_uint1 x396;
  fiat_p256_addcarryx_u32(&x395, &x396, x394, x387, x363);
  uint32_t x397;
  fiat_p256_uint1 x398;
  fiat_p256_addcarryx_u32(&x397, &x398, x396, x389, x365);
  uint32_t x399;
  fiat_p256_uint1 x400;
  fiat_p256_addcarryx_u32(&x399, &x400, x398, 0x0, x367);
  uint32_t x401;
  fiat_p256_uint1 x402;
  fiat_p256_addcarryx_u32(&x401, &x402, x400, 0x0, x369);
  uint32_t x403;
  fiat_p256_uint1 x404;
  fiat_p256_addcarryx_u32(&x403, &x404, x402, x359, x371);
  uint32_t x405;
  fiat_p256_uint1 x406;
  fiat_p256_addcarryx_u32(&x405, &x406, x404, x377, x373);
  uint32_t x407;
  fiat_p256_uint1 x408;
  fiat_p256_addcarryx_u32(&x407, &x408, x406, x378, x375);
  uint32_t x409;
  fiat_p256_uint1 x410;
  fiat_p256_addcarryx_u32(&x409, &x410, x408, 0x0, x376);
  uint32_t x411;
  uint32_t x412;
  fiat_p256_mulx_u32(&x411, &x412, x5, (arg1[7]));
  uint32_t x413;
  uint32_t x414;
  fiat_p256_mulx_u32(&x413, &x414, x5, (arg1[6]));
  uint32_t x415;
  uint32_t x416;
  fiat_p256_mulx_u32(&x415, &x416, x5, (arg1[5]));
  uint32_t x417;
  uint32_t x418;
  fiat_p256_mulx_u32(&x417, &x418, x5, (arg1[4]));
  uint32_t x419;
  uint32_t x420;
  fiat_p256_mulx_u32(&x419, &x420, x5, (arg1[3]));
  uint32_t x421;
  uint32_t x422;
  fiat_p256_mulx_u32(&x421, &x422, x5, (arg1[2]));
  uint32_t x423;
  uint32_t x424;
  fiat_p256_mulx_u32(&x423, &x424, x5, (arg1[1]));
  uint32_t x425;
  uint32_t x426;
  fiat_p256_mulx_u32(&x425, &x426, x5, (arg1[0]));
  uint32_t x427;
  fiat_p256_uint1 x428;
  fiat_p256_addcarryx_u32(&x427, &x428, 0x0, x423, x426);
  uint32_t x429;
  fiat_p256_uint1 x430;
  fiat_p256_addcarryx_u32(&x429, &x430, x428, x421, x424);
  uint32_t x431;
  fiat_p256_uint1 x432;
  fiat_p256_addcarryx_u32(&x431, &x432, x430, x419, x422);
  uint32_t x433;
  fiat_p256_uint1 x434;
  fiat_p256_addcarryx_u32(&x433, &x434, x432, x417, x420);
  uint32_t x435;
  fiat_p256_uint1 x436;
  fiat_p256_addcarryx_u32(&x435, &x436, x434, x415, x418);
  uint32_t x437;
  fiat_p256_uint1 x438;
  fiat_p256_addcarryx_u32(&x437, &x438, x436, x413, x416);
  uint32_t x439;
  fiat_p256_uint1 x440;
  fiat_p256_addcarryx_u32(&x439, &x440, x438, x411, x414);
  uint32_t x441;
  fiat_p256_uint1 x442;
  fiat_p256_addcarryx_u32(&x441, &x442, x440, 0x0, x412);
  uint32_t x443;
  fiat_p256_uint1 x444;
  fiat_p256_addcarryx_u32(&x443, &x444, 0x0, x425, x393);
  uint32_t x445;
  fiat_p256_uint1 x446;
  fiat_p256_addcarryx_u32(&x445, &x446, x444, x427, x395);
  uint32_t x447;
  fiat_p256_uint1 x448;
  fiat_p256_addcarryx_u32(&x447, &x448, x446, x429, x397);
  uint32_t x449;
  fiat_p256_uint1 x450;
  fiat_p256_addcarryx_u32(&x449, &x450, x448, x431, x399);
  uint32_t x451;
  fiat_p256_uint1 x452;
  fiat_p256_addcarryx_u32(&x451, &x452, x450, x433, x401);
  uint32_t x453;
  fiat_p256_uint1 x454;
  fiat_p256_addcarryx_u32(&x453, &x454, x452, x435, x403);
  uint32_t x455;
  fiat_p256_uint1 x456;
  fiat_p256_addcarryx_u32(&x455, &x456, x454, x437, x405);
  uint32_t x457;
  fiat_p256_uint1 x458;
  fiat_p256_addcarryx_u32(&x457, &x458, x456, x439, x407);
  uint32_t x459;
  fiat_p256_uint1 x460;
  fiat_p256_addcarryx_u32(&x459, &x460, x458, x441, x409);
  uint32_t x461;
  uint32_t x462;
  fiat_p256_mulx_u32(&x461, &x462, x443, UINT32_C(0xffffffff));
  uint32_t x463;
  uint32_t x464;
  fiat_p256_mulx_u32(&x463, &x464, x443, UINT32_C(0xffffffff));
  uint32_t x465;
  uint32_t x466;
  fiat_p256_mulx_u32(&x465, &x466, x443, UINT32_C(0xffffffff));
  uint32_t x467;
  uint32_t x468;
  fiat_p256_mulx_u32(&x467, &x468, x443, UINT32_C(0xffffffff));
  uint32_t x469;
  fiat_p256_uint1 x470;
  fiat_p256_addcarryx_u32(&x469, &x470, 0x0, x465, x468);
  uint32_t x471;
  fiat_p256_uint1 x472;
  fiat_p256_addcarryx_u32(&x471, &x472, x470, x463, x466);
  uint32_t x473;
  fiat_p256_uint1 x474;
  fiat_p256_addcarryx_u32(&x473, &x474, x472, 0x0, x464);
  uint32_t x475;
  fiat_p256_uint1 x476;
  fiat_p256_addcarryx_u32(&x475, &x476, 0x0, x467, x443);
  uint32_t x477;
  fiat_p256_uint1 x478;
  fiat_p256_addcarryx_u32(&x477, &x478, x476, x469, x445);
  uint32_t x479;
  fiat_p256_uint1 x480;
  fiat_p256_addcarryx_u32(&x479, &x480, x478, x471, x447);
  uint32_t x481;
  fiat_p256_uint1 x482;
  fiat_p256_addcarryx_u32(&x481, &x482, x480, x473, x449);
  uint32_t x483;
  fiat_p256_uint1 x484;
  fiat_p256_addcarryx_u32(&x483, &x484, x482, 0x0, x451);
  uint32_t x485;
  fiat_p256_uint1 x486;
  fiat_p256_addcarryx_u32(&x485, &x486, x484, 0x0, x453);
  uint32_t x487;
  fiat_p256_uint1 x488;
  fiat_p256_addcarryx_u32(&x487, &x488, x486, x443, x455);
  uint32_t x489;
  fiat_p256_uint1 x490;
  fiat_p256_addcarryx_u32(&x489, &x490, x488, x461, x457);
  uint32_t x491;
  fiat_p256_uint1 x492;
  fiat_p256_addcarryx_u32(&x491, &x492, x490, x462, x459);
  uint32_t x493;
  fiat_p256_uint1 x494;
  fiat_p256_addcarryx_u32(&x493, &x494, x492, 0x0, x460);
  uint32_t x495;
  uint32_t x496;
  fiat_p256_mulx_u32(&x495, &x496, x6, (arg1[7]));
  uint32_t x497;
  uint32_t x498;
  fiat_p256_mulx_u32(&x497, &x498, x6, (arg1[6]));
  uint32_t x499;
  uint32_t x500;
  fiat_p256_mulx_u32(&x499, &x500, x6, (arg1[5]));
  uint32_t x501;
  uint32_t x502;
  fiat_p256_mulx_u32(&x501, &x502, x6, (arg1[4]));
  uint32_t x503;
  uint32_t x504;
  fiat_p256_mulx_u32(&x503, &x504, x6, (arg1[3]));
  uint32_t x505;
  uint32_t x506;
  fiat_p256_mulx_u32(&x505, &x506, x6, (arg1[2]));
  uint32_t x507;
  uint32_t x508;
  fiat_p256_mulx_u32(&x507, &x508, x6, (arg1[1]));
  uint32_t x509;
  uint32_t x510;
  fiat_p256_mulx_u32(&x509, &x510, x6, (arg1[0]));
  uint32_t x511;
  fiat_p256_uint1 x512;
  fiat_p256_addcarryx_u32(&x511, &x512, 0x0, x507, x510);
  uint32_t x513;
  fiat_p256_uint1 x514;
  fiat_p256_addcarryx_u32(&x513, &x514, x512, x505, x508);
  uint32_t x515;
  fiat_p256_uint1 x516;
  fiat_p256_addcarryx_u32(&x515, &x516, x514, x503, x506);
  uint32_t x517;
  fiat_p256_uint1 x518;
  fiat_p256_addcarryx_u32(&x517, &x518, x516, x501, x504);
  uint32_t x519;
  fiat_p256_uint1 x520;
  fiat_p256_addcarryx_u32(&x519, &x520, x518, x499, x502);
  uint32_t x521;
  fiat_p256_uint1 x522;
  fiat_p256_addcarryx_u32(&x521, &x522, x520, x497, x500);
  uint32_t x523;
  fiat_p256_uint1 x524;
  fiat_p256_addcarryx_u32(&x523, &x524, x522, x495, x498);
  uint32_t x525;
  fiat_p256_uint1 x526;
  fiat_p256_addcarryx_u32(&x525, &x526, x524, 0x0, x496);
  uint32_t x527;
  fiat_p256_uint1 x528;
  fiat_p256_addcarryx_u32(&x527, &x528, 0x0, x509, x477);
  uint32_t x529;
  fiat_p256_uint1 x530;
  fiat_p256_addcarryx_u32(&x529, &x530, x528, x511, x479);
  uint32_t x531;
  fiat_p256_uint1 x532;
  fiat_p256_addcarryx_u32(&x531, &x532, x530, x513, x481);
  uint32_t x533;
  fiat_p256_uint1 x534;
  fiat_p256_addcarryx_u32(&x533, &x534, x532, x515, x483);
  uint32_t x535;
  fiat_p256_uint1 x536;
  fiat_p256_addcarryx_u32(&x535, &x536, x534, x517, x485);
  uint32_t x537;
  fiat_p256_uint1 x538;
  fiat_p256_addcarryx_u32(&x537, &x538, x536, x519, x487);
  uint32_t x539;
  fiat_p256_uint1 x540;
  fiat_p256_addcarryx_u32(&x539, &x540, x538, x521, x489);
  uint32_t x541;
  fiat_p256_uint1 x542;
  fiat_p256_addcarryx_u32(&x541, &x542, x540, x523, x491);
  uint32_t x543;
  fiat_p256_uint1 x544;
  fiat_p256_addcarryx_u32(&x543, &x544, x542, x525, x493);
  uint32_t x545;
  uint32_t x546;
  fiat_p256_mulx_u32(&x545, &x546, x527, UINT32_C(0xffffffff));
  uint32_t x547;
  uint32_t x548;
  fiat_p256_mulx_u32(&x547, &x548, x527, UINT32_C(0xffffffff));
  uint32_t x549;
  uint32_t x550;
  fiat_p256_mulx_u32(&x549, &x550, x527, UINT32_C(0xffffffff));
  uint32_t x551;
  uint32_t x552;
  fiat_p256_mulx_u32(&x551, &x552, x527, UINT32_C(0xffffffff));
  uint32_t x553;
  fiat_p256_uint1 x554;
  fiat_p256_addcarryx_u32(&x553, &x554, 0x0, x549, x552);
  uint32_t x555;
  fiat_p256_uint1 x556;
  fiat_p256_addcarryx_u32(&x555, &x556, x554, x547, x550);
  uint32_t x557;
  fiat_p256_uint1 x558;
  fiat_p256_addcarryx_u32(&x557, &x558, x556, 0x0, x548);
  uint32_t x559;
  fiat_p256_uint1 x560;
  fiat_p256_addcarryx_u32(&x559, &x560, 0x0, x551, x527);
  uint32_t x561;
  fiat_p256_uint1 x562;
  fiat_p256_addcarryx_u32(&x561, &x562, x560, x553, x529);
  uint32_t x563;
  fiat_p256_uint1 x564;
  fiat_p256_addcarryx_u32(&x563, &x564, x562, x555, x531);
  uint32_t x565;
  fiat_p256_uint1 x566;
  fiat_p256_addcarryx_u32(&x565, &x566, x564, x557, x533);
  uint32_t x567;
  fiat_p256_uint1 x568;
  fiat_p256_addcarryx_u32(&x567, &x568, x566, 0x0, x535);
  uint32_t x569;
  fiat_p256_uint1 x570;
  fiat_p256_addcarryx_u32(&x569, &x570, x568, 0x0, x537);
  uint32_t x571;
  fiat_p256_uint1 x572;
  fiat_p256_addcarryx_u32(&x571, &x572, x570, x527, x539);
  uint32_t x573;
  fiat_p256_uint1 x574;
  fiat_p256_addcarryx_u32(&x573, &x574, x572, x545, x541);
  uint32_t x575;
  fiat_p256_uint1 x576;
  fiat_p256_addcarryx_u32(&x575, &x576, x574, x546, x543);
  uint32_t x577;
  fiat_p256_uint1 x578;
  fiat_p256_addcarryx_u32(&x577, &x578, x576, 0x0, x544);
  uint32_t x579;
  uint32_t x580;
  fiat_p256_mulx_u32(&x579, &x580, x7, (arg1[7]));
  uint32_t x581;
  uint32_t x582;
  fiat_p256_mulx_u32(&x581, &x582, x7, (arg1[6]));
  uint32_t x583;
  uint32_t x584;
  fiat_p256_mulx_u32(&x583, &x584, x7, (arg1[5]));
  uint32_t x585;
  uint32_t x586;
  fiat_p256_mulx_u32(&x585, &x586, x7, (arg1[4]));
  uint32_t x587;
  uint32_t x588;
  fiat_p256_mulx_u32(&x587, &x588, x7, (arg1[3]));
  uint32_t x589;
  uint32_t x590;
  fiat_p256_mulx_u32(&x589, &x590, x7, (arg1[2]));
  uint32_t x591;
  uint32_t x592;
  fiat_p256_mulx_u32(&x591, &x592, x7, (arg1[1]));
  uint32_t x593;
  uint32_t x594;
  fiat_p256_mulx_u32(&x593, &x594, x7, (arg1[0]));
  uint32_t x595;
  fiat_p256_uint1 x596;
  fiat_p256_addcarryx_u32(&x595, &x596, 0x0, x591, x594);
  uint32_t x597;
  fiat_p256_uint1 x598;
  fiat_p256_addcarryx_u32(&x597, &x598, x596, x589, x592);
  uint32_t x599;
  fiat_p256_uint1 x600;
  fiat_p256_addcarryx_u32(&x599, &x600, x598, x587, x590);
  uint32_t x601;
  fiat_p256_uint1 x602;
  fiat_p256_addcarryx_u32(&x601, &x602, x600, x585, x588);
  uint32_t x603;
  fiat_p256_uint1 x604;
  fiat_p256_addcarryx_u32(&x603, &x604, x602, x583, x586);
  uint32_t x605;
  fiat_p256_uint1 x606;
  fiat_p256_addcarryx_u32(&x605, &x606, x604, x581, x584);
  uint32_t x607;
  fiat_p256_uint1 x608;
  fiat_p256_addcarryx_u32(&x607, &x608, x606, x579, x582);
  uint32_t x609;
  fiat_p256_uint1 x610;
  fiat_p256_addcarryx_u32(&x609, &x610, x608, 0x0, x580);
  uint32_t x611;
  fiat_p256_uint1 x612;
  fiat_p256_addcarryx_u32(&x611, &x612, 0x0, x593, x561);
  uint32_t x613;
  fiat_p256_uint1 x614;
  fiat_p256_addcarryx_u32(&x613, &x614, x612, x595, x563);
  uint32_t x615;
  fiat_p256_uint1 x616;
  fiat_p256_addcarryx_u32(&x615, &x616, x614, x597, x565);
  uint32_t x617;
  fiat_p256_uint1 x618;
  fiat_p256_addcarryx_u32(&x617, &x618, x616, x599, x567);
  uint32_t x619;
  fiat_p256_uint1 x620;
  fiat_p256_addcarryx_u32(&x619, &x620, x618, x601, x569);
  uint32_t x621;
  fiat_p256_uint1 x622;
  fiat_p256_addcarryx_u32(&x621, &x622, x620, x603, x571);
  uint32_t x623;
  fiat_p256_uint1 x624;
  fiat_p256_addcarryx_u32(&x623, &x624, x622, x605, x573);
  uint32_t x625;
  fiat_p256_uint1 x626;
  fiat_p256_addcarryx_u32(&x625, &x626, x624, x607, x575);
  uint32_t x627;
  fiat_p256_uint1 x628;
  fiat_p256_addcarryx_u32(&x627, &x628, x626, x609, x577);
  uint32_t x629;
  uint32_t x630;
  fiat_p256_mulx_u32(&x629, &x630, x611, UINT32_C(0xffffffff));
  uint32_t x631;
  uint32_t x632;
  fiat_p256_mulx_u32(&x631, &x632, x611, UINT32_C(0xffffffff));
  uint32_t x633;
  uint32_t x634;
  fiat_p256_mulx_u32(&x633, &x634, x611, UINT32_C(0xffffffff));
  uint32_t x635;
  uint32_t x636;
  fiat_p256_mulx_u32(&x635, &x636, x611, UINT32_C(0xffffffff));
  uint32_t x637;
  fiat_p256_uint1 x638;
  fiat_p256_addcarryx_u32(&x637, &x638, 0x0, x633, x636);
  uint32_t x639;
  fiat_p256_uint1 x640;
  fiat_p256_addcarryx_u32(&x639, &x640, x638, x631, x634);
  uint32_t x641;
  fiat_p256_uint1 x642;
  fiat_p256_addcarryx_u32(&x641, &x642, x640, 0x0, x632);
  uint32_t x643;
  fiat_p256_uint1 x644;
  fiat_p256_addcarryx_u32(&x643, &x644, 0x0, x635, x611);
  uint32_t x645;
  fiat_p256_uint1 x646;
  fiat_p256_addcarryx_u32(&x645, &x646, x644, x637, x613);
  uint32_t x647;
  fiat_p256_uint1 x648;
  fiat_p256_addcarryx_u32(&x647, &x648, x646, x639, x615);
  uint32_t x649;
  fiat_p256_uint1 x650;
  fiat_p256_addcarryx_u32(&x649, &x650, x648, x641, x617);
  uint32_t x651;
  fiat_p256_uint1 x652;
  fiat_p256_addcarryx_u32(&x651, &x652, x650, 0x0, x619);
  uint32_t x653;
  fiat_p256_uint1 x654;
  fiat_p256_addcarryx_u32(&x653, &x654, x652, 0x0, x621);
  uint32_t x655;
  fiat_p256_uint1 x656;
  fiat_p256_addcarryx_u32(&x655, &x656, x654, x611, x623);
  uint32_t x657;
  fiat_p256_uint1 x658;
  fiat_p256_addcarryx_u32(&x657, &x658, x656, x629, x625);
  uint32_t x659;
  fiat_p256_uint1 x660;
  fiat_p256_addcarryx_u32(&x659, &x660, x658, x630, x627);
  uint32_t x661;
  fiat_p256_uint1 x662;
  fiat_p256_addcarryx_u32(&x661, &x662, x660, 0x0, x628);
  uint32_t x663;
  fiat_p256_uint1 x664;
  fiat_p256_subborrowx_u32(&x663, &x664, 0x0, x645, UINT32_C(0xffffffff));
  uint32_t x665;
  fiat_p256_uint1 x666;
  fiat_p256_subborrowx_u32(&x665, &x666, x664, x647, UINT32_C(0xffffffff));
  uint32_t x667;
  fiat_p256_uint1 x668;
  fiat_p256_subborrowx_u32(&x667, &x668, x666, x649, UINT32_C(0xffffffff));
  uint32_t x669;
  fiat_p256_uint1 x670;
  fiat_p256_subborrowx_u32(&x669, &x670, x668, x651, 0x0);
  uint32_t x671;
  fiat_p256_uint1 x672;
  fiat_p256_subborrowx_u32(&x671, &x672, x670, x653, 0x0);
  uint32_t x673;
  fiat_p256_uint1 x674;
  fiat_p256_subborrowx_u32(&x673, &x674, x672, x655, 0x0);
  uint32_t x675;
  fiat_p256_uint1 x676;
  fiat_p256_subborrowx_u32(&x675, &x676, x674, x657, 0x1);
  uint32_t x677;
  fiat_p256_uint1 x678;
  fiat_p256_subborrowx_u32(&x677, &x678, x676, x659, UINT32_C(0xffffffff));
  uint32_t x679;
  fiat_p256_uint1 x680;
  fiat_p256_subborrowx_u32(&x679, &x680, x678, x661, 0x0);
  uint32_t x681;
  fiat_p256_cmovznz_u32(&x681, x680, x663, x645);
  uint32_t x682;
  fiat_p256_cmovznz_u32(&x682, x680, x665, x647);
  uint32_t x683;
  fiat_p256_cmovznz_u32(&x683, x680, x667, x649);
  uint32_t x684;
  fiat_p256_cmovznz_u32(&x684, x680, x669, x651);
  uint32_t x685;
  fiat_p256_cmovznz_u32(&x685, x680, x671, x653);
  uint32_t x686;
  fiat_p256_cmovznz_u32(&x686, x680, x673, x655);
  uint32_t x687;
  fiat_p256_cmovznz_u32(&x687, x680, x675, x657);
  uint32_t x688;
  fiat_p256_cmovznz_u32(&x688, x680, x677, x659);
  out1[0] = x681;
  out1[1] = x682;
  out1[2] = x683;
  out1[3] = x684;
  out1[4] = x685;
  out1[5] = x686;
  out1[6] = x687;
  out1[7] = x688;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 *   arg2: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 */
static void fiat_p256_add(uint32_t out1[8], const uint32_t arg1[8], const uint32_t arg2[8]) {
  uint32_t x1;
  fiat_p256_uint1 x2;
  fiat_p256_addcarryx_u32(&x1, &x2, 0x0, (arg2[0]), (arg1[0]));
  uint32_t x3;
  fiat_p256_uint1 x4;
  fiat_p256_addcarryx_u32(&x3, &x4, x2, (arg2[1]), (arg1[1]));
  uint32_t x5;
  fiat_p256_uint1 x6;
  fiat_p256_addcarryx_u32(&x5, &x6, x4, (arg2[2]), (arg1[2]));
  uint32_t x7;
  fiat_p256_uint1 x8;
  fiat_p256_addcarryx_u32(&x7, &x8, x6, (arg2[3]), (arg1[3]));
  uint32_t x9;
  fiat_p256_uint1 x10;
  fiat_p256_addcarryx_u32(&x9, &x10, x8, (arg2[4]), (arg1[4]));
  uint32_t x11;
  fiat_p256_uint1 x12;
  fiat_p256_addcarryx_u32(&x11, &x12, x10, (arg2[5]), (arg1[5]));
  uint32_t x13;
  fiat_p256_uint1 x14;
  fiat_p256_addcarryx_u32(&x13, &x14, x12, (arg2[6]), (arg1[6]));
  uint32_t x15;
  fiat_p256_uint1 x16;
  fiat_p256_addcarryx_u32(&x15, &x16, x14, (arg2[7]), (arg1[7]));
  uint32_t x17;
  fiat_p256_uint1 x18;
  fiat_p256_subborrowx_u32(&x17, &x18, 0x0, x1, UINT32_C(0xffffffff));
  uint32_t x19;
  fiat_p256_uint1 x20;
  fiat_p256_subborrowx_u32(&x19, &x20, x18, x3, UINT32_C(0xffffffff));
  uint32_t x21;
  fiat_p256_uint1 x22;
  fiat_p256_subborrowx_u32(&x21, &x22, x20, x5, UINT32_C(0xffffffff));
  uint32_t x23;
  fiat_p256_uint1 x24;
  fiat_p256_subborrowx_u32(&x23, &x24, x22, x7, 0x0);
  uint32_t x25;
  fiat_p256_uint1 x26;
  fiat_p256_subborrowx_u32(&x25, &x26, x24, x9, 0x0);
  uint32_t x27;
  fiat_p256_uint1 x28;
  fiat_p256_subborrowx_u32(&x27, &x28, x26, x11, 0x0);
  uint32_t x29;
  fiat_p256_uint1 x30;
  fiat_p256_subborrowx_u32(&x29, &x30, x28, x13, 0x1);
  uint32_t x31;
  fiat_p256_uint1 x32;
  fiat_p256_subborrowx_u32(&x31, &x32, x30, x15, UINT32_C(0xffffffff));
  uint32_t x33;
  fiat_p256_uint1 x34;
  fiat_p256_subborrowx_u32(&x33, &x34, x32, x16, 0x0);
  uint32_t x35;
  fiat_p256_cmovznz_u32(&x35, x34, x17, x1);
  uint32_t x36;
  fiat_p256_cmovznz_u32(&x36, x34, x19, x3);
  uint32_t x37;
  fiat_p256_cmovznz_u32(&x37, x34, x21, x5);
  uint32_t x38;
  fiat_p256_cmovznz_u32(&x38, x34, x23, x7);
  uint32_t x39;
  fiat_p256_cmovznz_u32(&x39, x34, x25, x9);
  uint32_t x40;
  fiat_p256_cmovznz_u32(&x40, x34, x27, x11);
  uint32_t x41;
  fiat_p256_cmovznz_u32(&x41, x34, x29, x13);
  uint32_t x42;
  fiat_p256_cmovznz_u32(&x42, x34, x31, x15);
  out1[0] = x35;
  out1[1] = x36;
  out1[2] = x37;
  out1[3] = x38;
  out1[4] = x39;
  out1[5] = x40;
  out1[6] = x41;
  out1[7] = x42;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 *   arg2: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 */
static void fiat_p256_sub(uint32_t out1[8], const uint32_t arg1[8], const uint32_t arg2[8]) {
  uint32_t x1;
  fiat_p256_uint1 x2;
  fiat_p256_subborrowx_u32(&x1, &x2, 0x0, (arg1[0]), (arg2[0]));
  uint32_t x3;
  fiat_p256_uint1 x4;
  fiat_p256_subborrowx_u32(&x3, &x4, x2, (arg1[1]), (arg2[1]));
  uint32_t x5;
  fiat_p256_uint1 x6;
  fiat_p256_subborrowx_u32(&x5, &x6, x4, (arg1[2]), (arg2[2]));
  uint32_t x7;
  fiat_p256_uint1 x8;
  fiat_p256_subborrowx_u32(&x7, &x8, x6, (arg1[3]), (arg2[3]));
  uint32_t x9;
  fiat_p256_uint1 x10;
  fiat_p256_subborrowx_u32(&x9, &x10, x8, (arg1[4]), (arg2[4]));
  uint32_t x11;
  fiat_p256_uint1 x12;
  fiat_p256_subborrowx_u32(&x11, &x12, x10, (arg1[5]), (arg2[5]));
  uint32_t x13;
  fiat_p256_uint1 x14;
  fiat_p256_subborrowx_u32(&x13, &x14, x12, (arg1[6]), (arg2[6]));
  uint32_t x15;
  fiat_p256_uint1 x16;
  fiat_p256_subborrowx_u32(&x15, &x16, x14, (arg1[7]), (arg2[7]));
  uint32_t x17;
  fiat_p256_cmovznz_u32(&x17, x16, 0x0, UINT32_C(0xffffffff));
  uint32_t x18;
  fiat_p256_uint1 x19;
  fiat_p256_addcarryx_u32(&x18, &x19, 0x0, (x17 & UINT32_C(0xffffffff)), x1);
  uint32_t x20;
  fiat_p256_uint1 x21;
  fiat_p256_addcarryx_u32(&x20, &x21, x19, (x17 & UINT32_C(0xffffffff)), x3);
  uint32_t x22;
  fiat_p256_uint1 x23;
  fiat_p256_addcarryx_u32(&x22, &x23, x21, (x17 & UINT32_C(0xffffffff)), x5);
  uint32_t x24;
  fiat_p256_uint1 x25;
  fiat_p256_addcarryx_u32(&x24, &x25, x23, 0x0, x7);
  uint32_t x26;
  fiat_p256_uint1 x27;
  fiat_p256_addcarryx_u32(&x26, &x27, x25, 0x0, x9);
  uint32_t x28;
  fiat_p256_uint1 x29;
  fiat_p256_addcarryx_u32(&x28, &x29, x27, 0x0, x11);
  uint32_t x30;
  fiat_p256_uint1 x31;
  fiat_p256_addcarryx_u32(&x30, &x31, x29, (fiat_p256_uint1)(x17 & 0x1), x13);
  uint32_t x32;
  fiat_p256_uint1 x33;
  fiat_p256_addcarryx_u32(&x32, &x33, x31, (x17 & UINT32_C(0xffffffff)), x15);
  out1[0] = x18;
  out1[1] = x20;
  out1[2] = x22;
  out1[3] = x24;
  out1[4] = x26;
  out1[5] = x28;
  out1[6] = x30;
  out1[7] = x32;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 */
static void fiat_p256_opp(uint32_t out1[8], const uint32_t arg1[8]) {
  uint32_t x1;
  fiat_p256_uint1 x2;
  fiat_p256_subborrowx_u32(&x1, &x2, 0x0, 0x0, (arg1[0]));
  uint32_t x3;
  fiat_p256_uint1 x4;
  fiat_p256_subborrowx_u32(&x3, &x4, x2, 0x0, (arg1[1]));
  uint32_t x5;
  fiat_p256_uint1 x6;
  fiat_p256_subborrowx_u32(&x5, &x6, x4, 0x0, (arg1[2]));
  uint32_t x7;
  fiat_p256_uint1 x8;
  fiat_p256_subborrowx_u32(&x7, &x8, x6, 0x0, (arg1[3]));
  uint32_t x9;
  fiat_p256_uint1 x10;
  fiat_p256_subborrowx_u32(&x9, &x10, x8, 0x0, (arg1[4]));
  uint32_t x11;
  fiat_p256_uint1 x12;
  fiat_p256_subborrowx_u32(&x11, &x12, x10, 0x0, (arg1[5]));
  uint32_t x13;
  fiat_p256_uint1 x14;
  fiat_p256_subborrowx_u32(&x13, &x14, x12, 0x0, (arg1[6]));
  uint32_t x15;
  fiat_p256_uint1 x16;
  fiat_p256_subborrowx_u32(&x15, &x16, x14, 0x0, (arg1[7]));
  uint32_t x17;
  fiat_p256_cmovznz_u32(&x17, x16, 0x0, UINT32_C(0xffffffff));
  uint32_t x18;
  fiat_p256_uint1 x19;
  fiat_p256_addcarryx_u32(&x18, &x19, 0x0, (x17 & UINT32_C(0xffffffff)), x1);
  uint32_t x20;
  fiat_p256_uint1 x21;
  fiat_p256_addcarryx_u32(&x20, &x21, x19, (x17 & UINT32_C(0xffffffff)), x3);
  uint32_t x22;
  fiat_p256_uint1 x23;
  fiat_p256_addcarryx_u32(&x22, &x23, x21, (x17 & UINT32_C(0xffffffff)), x5);
  uint32_t x24;
  fiat_p256_uint1 x25;
  fiat_p256_addcarryx_u32(&x24, &x25, x23, 0x0, x7);
  uint32_t x26;
  fiat_p256_uint1 x27;
  fiat_p256_addcarryx_u32(&x26, &x27, x25, 0x0, x9);
  uint32_t x28;
  fiat_p256_uint1 x29;
  fiat_p256_addcarryx_u32(&x28, &x29, x27, 0x0, x11);
  uint32_t x30;
  fiat_p256_uint1 x31;
  fiat_p256_addcarryx_u32(&x30, &x31, x29, (fiat_p256_uint1)(x17 & 0x1), x13);
  uint32_t x32;
  fiat_p256_uint1 x33;
  fiat_p256_addcarryx_u32(&x32, &x33, x31, (x17 & UINT32_C(0xffffffff)), x15);
  out1[0] = x18;
  out1[1] = x20;
  out1[2] = x22;
  out1[3] = x24;
  out1[4] = x26;
  out1[5] = x28;
  out1[6] = x30;
  out1[7] = x32;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 */
static void fiat_p256_from_montgomery(uint32_t out1[8], const uint32_t arg1[8]) {
  uint32_t x1 = (arg1[0]);
  uint32_t x2;
  uint32_t x3;
  fiat_p256_mulx_u32(&x2, &x3, x1, UINT32_C(0xffffffff));
  uint32_t x4;
  uint32_t x5;
  fiat_p256_mulx_u32(&x4, &x5, x1, UINT32_C(0xffffffff));
  uint32_t x6;
  uint32_t x7;
  fiat_p256_mulx_u32(&x6, &x7, x1, UINT32_C(0xffffffff));
  uint32_t x8;
  uint32_t x9;
  fiat_p256_mulx_u32(&x8, &x9, x1, UINT32_C(0xffffffff));
  uint32_t x10;
  fiat_p256_uint1 x11;
  fiat_p256_addcarryx_u32(&x10, &x11, 0x0, x6, x9);
  uint32_t x12;
  fiat_p256_uint1 x13;
  fiat_p256_addcarryx_u32(&x12, &x13, x11, x4, x7);
  uint32_t x14;
  fiat_p256_uint1 x15;
  fiat_p256_addcarryx_u32(&x14, &x15, 0x0, x8, x1);
  uint32_t x16;
  fiat_p256_uint1 x17;
  fiat_p256_addcarryx_u32(&x16, &x17, x15, x10, 0x0);
  uint32_t x18;
  fiat_p256_uint1 x19;
  fiat_p256_addcarryx_u32(&x18, &x19, x17, x12, 0x0);
  uint32_t x20;
  fiat_p256_uint1 x21;
  fiat_p256_addcarryx_u32(&x20, &x21, x13, 0x0, x5);
  uint32_t x22;
  fiat_p256_uint1 x23;
  fiat_p256_addcarryx_u32(&x22, &x23, x19, x20, 0x0);
  uint32_t x24;
  fiat_p256_uint1 x25;
  fiat_p256_addcarryx_u32(&x24, &x25, 0x0, (arg1[1]), x16);
  uint32_t x26;
  fiat_p256_uint1 x27;
  fiat_p256_addcarryx_u32(&x26, &x27, x25, 0x0, x18);
  uint32_t x28;
  fiat_p256_uint1 x29;
  fiat_p256_addcarryx_u32(&x28, &x29, x27, 0x0, x22);
  uint32_t x30;
  uint32_t x31;
  fiat_p256_mulx_u32(&x30, &x31, x24, UINT32_C(0xffffffff));
  uint32_t x32;
  uint32_t x33;
  fiat_p256_mulx_u32(&x32, &x33, x24, UINT32_C(0xffffffff));
  uint32_t x34;
  uint32_t x35;
  fiat_p256_mulx_u32(&x34, &x35, x24, UINT32_C(0xffffffff));
  uint32_t x36;
  uint32_t x37;
  fiat_p256_mulx_u32(&x36, &x37, x24, UINT32_C(0xffffffff));
  uint32_t x38;
  fiat_p256_uint1 x39;
  fiat_p256_addcarryx_u32(&x38, &x39, 0x0, x34, x37);
  uint32_t x40;
  fiat_p256_uint1 x41;
  fiat_p256_addcarryx_u32(&x40, &x41, x39, x32, x35);
  uint32_t x42;
  fiat_p256_uint1 x43;
  fiat_p256_addcarryx_u32(&x42, &x43, 0x0, x36, x24);
  uint32_t x44;
  fiat_p256_uint1 x45;
  fiat_p256_addcarryx_u32(&x44, &x45, x43, x38, x26);
  uint32_t x46;
  fiat_p256_uint1 x47;
  fiat_p256_addcarryx_u32(&x46, &x47, x45, x40, x28);
  uint32_t x48;
  fiat_p256_uint1 x49;
  fiat_p256_addcarryx_u32(&x48, &x49, x23, 0x0, 0x0);
  uint32_t x50;
  fiat_p256_uint1 x51;
  fiat_p256_addcarryx_u32(&x50, &x51, x29, 0x0, (fiat_p256_uint1)x48);
  uint32_t x52;
  fiat_p256_uint1 x53;
  fiat_p256_addcarryx_u32(&x52, &x53, x41, 0x0, x33);
  uint32_t x54;
  fiat_p256_uint1 x55;
  fiat_p256_addcarryx_u32(&x54, &x55, x47, x52, x50);
  uint32_t x56;
  fiat_p256_uint1 x57;
  fiat_p256_addcarryx_u32(&x56, &x57, 0x0, x24, x2);
  uint32_t x58;
  fiat_p256_uint1 x59;
  fiat_p256_addcarryx_u32(&x58, &x59, x57, x30, x3);
  uint32_t x60;
  fiat_p256_uint1 x61;
  fiat_p256_addcarryx_u32(&x60, &x61, 0x0, (arg1[2]), x44);
  uint32_t x62;
  fiat_p256_uint1 x63;
  fiat_p256_addcarryx_u32(&x62, &x63, x61, 0x0, x46);
  uint32_t x64;
  fiat_p256_uint1 x65;
  fiat_p256_addcarryx_u32(&x64, &x65, x63, 0x0, x54);
  uint32_t x66;
  uint32_t x67;
  fiat_p256_mulx_u32(&x66, &x67, x60, UINT32_C(0xffffffff));
  uint32_t x68;
  uint32_t x69;
  fiat_p256_mulx_u32(&x68, &x69, x60, UINT32_C(0xffffffff));
  uint32_t x70;
  uint32_t x71;
  fiat_p256_mulx_u32(&x70, &x71, x60, UINT32_C(0xffffffff));
  uint32_t x72;
  uint32_t x73;
  fiat_p256_mulx_u32(&x72, &x73, x60, UINT32_C(0xffffffff));
  uint32_t x74;
  fiat_p256_uint1 x75;
  fiat_p256_addcarryx_u32(&x74, &x75, 0x0, x70, x73);
  uint32_t x76;
  fiat_p256_uint1 x77;
  fiat_p256_addcarryx_u32(&x76, &x77, x75, x68, x71);
  uint32_t x78;
  fiat_p256_uint1 x79;
  fiat_p256_addcarryx_u32(&x78, &x79, 0x0, x72, x60);
  uint32_t x80;
  fiat_p256_uint1 x81;
  fiat_p256_addcarryx_u32(&x80, &x81, x79, x74, x62);
  uint32_t x82;
  fiat_p256_uint1 x83;
  fiat_p256_addcarryx_u32(&x82, &x83, x81, x76, x64);
  uint32_t x84;
  fiat_p256_uint1 x85;
  fiat_p256_addcarryx_u32(&x84, &x85, x55, 0x0, 0x0);
  uint32_t x86;
  fiat_p256_uint1 x87;
  fiat_p256_addcarryx_u32(&x86, &x87, x65, 0x0, (fiat_p256_uint1)x84);
  uint32_t x88;
  fiat_p256_uint1 x89;
  fiat_p256_addcarryx_u32(&x88, &x89, x77, 0x0, x69);
  uint32_t x90;
  fiat_p256_uint1 x91;
  fiat_p256_addcarryx_u32(&x90, &x91, x83, x88, x86);
  uint32_t x92;
  fiat_p256_uint1 x93;
  fiat_p256_addcarryx_u32(&x92, &x93, x91, 0x0, x1);
  uint32_t x94;
  fiat_p256_uint1 x95;
  fiat_p256_addcarryx_u32(&x94, &x95, x93, 0x0, x56);
  uint32_t x96;
  fiat_p256_uint1 x97;
  fiat_p256_addcarryx_u32(&x96, &x97, x95, x60, x58);
  uint32_t x98;
  fiat_p256_uint1 x99;
  fiat_p256_addcarryx_u32(&x98, &x99, x59, x31, 0x0);
  uint32_t x100;
  fiat_p256_uint1 x101;
  fiat_p256_addcarryx_u32(&x100, &x101, x97, x66, x98);
  uint32_t x102;
  fiat_p256_uint1 x103;
  fiat_p256_addcarryx_u32(&x102, &x103, 0x0, (arg1[3]), x80);
  uint32_t x104;
  fiat_p256_uint1 x105;
  fiat_p256_addcarryx_u32(&x104, &x105, x103, 0x0, x82);
  uint32_t x106;
  fiat_p256_uint1 x107;
  fiat_p256_addcarryx_u32(&x106, &x107, x105, 0x0, x90);
  uint32_t x108;
  fiat_p256_uint1 x109;
  fiat_p256_addcarryx_u32(&x108, &x109, x107, 0x0, x92);
  uint32_t x110;
  fiat_p256_uint1 x111;
  fiat_p256_addcarryx_u32(&x110, &x111, x109, 0x0, x94);
  uint32_t x112;
  fiat_p256_uint1 x113;
  fiat_p256_addcarryx_u32(&x112, &x113, x111, 0x0, x96);
  uint32_t x114;
  fiat_p256_uint1 x115;
  fiat_p256_addcarryx_u32(&x114, &x115, x113, 0x0, x100);
  uint32_t x116;
  fiat_p256_uint1 x117;
  fiat_p256_addcarryx_u32(&x116, &x117, x101, x67, 0x0);
  uint32_t x118;
  fiat_p256_uint1 x119;
  fiat_p256_addcarryx_u32(&x118, &x119, x115, 0x0, x116);
  uint32_t x120;
  uint32_t x121;
  fiat_p256_mulx_u32(&x120, &x121, x102, UINT32_C(0xffffffff));
  uint32_t x122;
  uint32_t x123;
  fiat_p256_mulx_u32(&x122, &x123, x102, UINT32_C(0xffffffff));
  uint32_t x124;
  uint32_t x125;
  fiat_p256_mulx_u32(&x124, &x125, x102, UINT32_C(0xffffffff));
  uint32_t x126;
  uint32_t x127;
  fiat_p256_mulx_u32(&x126, &x127, x102, UINT32_C(0xffffffff));
  uint32_t x128;
  fiat_p256_uint1 x129;
  fiat_p256_addcarryx_u32(&x128, &x129, 0x0, x124, x127);
  uint32_t x130;
  fiat_p256_uint1 x131;
  fiat_p256_addcarryx_u32(&x130, &x131, x129, x122, x125);
  uint32_t x132;
  fiat_p256_uint1 x133;
  fiat_p256_addcarryx_u32(&x132, &x133, 0x0, x126, x102);
  uint32_t x134;
  fiat_p256_uint1 x135;
  fiat_p256_addcarryx_u32(&x134, &x135, x133, x128, x104);
  uint32_t x136;
  fiat_p256_uint1 x137;
  fiat_p256_addcarryx_u32(&x136, &x137, x135, x130, x106);
  uint32_t x138;
  fiat_p256_uint1 x139;
  fiat_p256_addcarryx_u32(&x138, &x139, x131, 0x0, x123);
  uint32_t x140;
  fiat_p256_uint1 x141;
  fiat_p256_addcarryx_u32(&x140, &x141, x137, x138, x108);
  uint32_t x142;
  fiat_p256_uint1 x143;
  fiat_p256_addcarryx_u32(&x142, &x143, x141, 0x0, x110);
  uint32_t x144;
  fiat_p256_uint1 x145;
  fiat_p256_addcarryx_u32(&x144, &x145, x143, 0x0, x112);
  uint32_t x146;
  fiat_p256_uint1 x147;
  fiat_p256_addcarryx_u32(&x146, &x147, x145, x102, x114);
  uint32_t x148;
  fiat_p256_uint1 x149;
  fiat_p256_addcarryx_u32(&x148, &x149, x147, x120, x118);
  uint32_t x150;
  fiat_p256_uint1 x151;
  fiat_p256_addcarryx_u32(&x150, &x151, x119, 0x0, 0x0);
  uint32_t x152;
  fiat_p256_uint1 x153;
  fiat_p256_addcarryx_u32(&x152, &x153, x149, x121, (fiat_p256_uint1)x150);
  uint32_t x154;
  fiat_p256_uint1 x155;
  fiat_p256_addcarryx_u32(&x154, &x155, 0x0, (arg1[4]), x134);
  uint32_t x156;
  fiat_p256_uint1 x157;
  fiat_p256_addcarryx_u32(&x156, &x157, x155, 0x0, x136);
  uint32_t x158;
  fiat_p256_uint1 x159;
  fiat_p256_addcarryx_u32(&x158, &x159, x157, 0x0, x140);
  uint32_t x160;
  fiat_p256_uint1 x161;
  fiat_p256_addcarryx_u32(&x160, &x161, x159, 0x0, x142);
  uint32_t x162;
  fiat_p256_uint1 x163;
  fiat_p256_addcarryx_u32(&x162, &x163, x161, 0x0, x144);
  uint32_t x164;
  fiat_p256_uint1 x165;
  fiat_p256_addcarryx_u32(&x164, &x165, x163, 0x0, x146);
  uint32_t x166;
  fiat_p256_uint1 x167;
  fiat_p256_addcarryx_u32(&x166, &x167, x165, 0x0, x148);
  uint32_t x168;
  fiat_p256_uint1 x169;
  fiat_p256_addcarryx_u32(&x168, &x169, x167, 0x0, x152);
  uint32_t x170;
  uint32_t x171;
  fiat_p256_mulx_u32(&x170, &x171, x154, UINT32_C(0xffffffff));
  uint32_t x172;
  uint32_t x173;
  fiat_p256_mulx_u32(&x172, &x173, x154, UINT32_C(0xffffffff));
  uint32_t x174;
  uint32_t x175;
  fiat_p256_mulx_u32(&x174, &x175, x154, UINT32_C(0xffffffff));
  uint32_t x176;
  uint32_t x177;
  fiat_p256_mulx_u32(&x176, &x177, x154, UINT32_C(0xffffffff));
  uint32_t x178;
  fiat_p256_uint1 x179;
  fiat_p256_addcarryx_u32(&x178, &x179, 0x0, x174, x177);
  uint32_t x180;
  fiat_p256_uint1 x181;
  fiat_p256_addcarryx_u32(&x180, &x181, x179, x172, x175);
  uint32_t x182;
  fiat_p256_uint1 x183;
  fiat_p256_addcarryx_u32(&x182, &x183, 0x0, x176, x154);
  uint32_t x184;
  fiat_p256_uint1 x185;
  fiat_p256_addcarryx_u32(&x184, &x185, x183, x178, x156);
  uint32_t x186;
  fiat_p256_uint1 x187;
  fiat_p256_addcarryx_u32(&x186, &x187, x185, x180, x158);
  uint32_t x188;
  fiat_p256_uint1 x189;
  fiat_p256_addcarryx_u32(&x188, &x189, x181, 0x0, x173);
  uint32_t x190;
  fiat_p256_uint1 x191;
  fiat_p256_addcarryx_u32(&x190, &x191, x187, x188, x160);
  uint32_t x192;
  fiat_p256_uint1 x193;
  fiat_p256_addcarryx_u32(&x192, &x193, x191, 0x0, x162);
  uint32_t x194;
  fiat_p256_uint1 x195;
  fiat_p256_addcarryx_u32(&x194, &x195, x193, 0x0, x164);
  uint32_t x196;
  fiat_p256_uint1 x197;
  fiat_p256_addcarryx_u32(&x196, &x197, x195, x154, x166);
  uint32_t x198;
  fiat_p256_uint1 x199;
  fiat_p256_addcarryx_u32(&x198, &x199, x197, x170, x168);
  uint32_t x200;
  fiat_p256_uint1 x201;
  fiat_p256_addcarryx_u32(&x200, &x201, x153, 0x0, 0x0);
  uint32_t x202;
  fiat_p256_uint1 x203;
  fiat_p256_addcarryx_u32(&x202, &x203, x169, 0x0, (fiat_p256_uint1)x200);
  uint32_t x204;
  fiat_p256_uint1 x205;
  fiat_p256_addcarryx_u32(&x204, &x205, x199, x171, x202);
  uint32_t x206;
  fiat_p256_uint1 x207;
  fiat_p256_addcarryx_u32(&x206, &x207, 0x0, (arg1[5]), x184);
  uint32_t x208;
  fiat_p256_uint1 x209;
  fiat_p256_addcarryx_u32(&x208, &x209, x207, 0x0, x186);
  uint32_t x210;
  fiat_p256_uint1 x211;
  fiat_p256_addcarryx_u32(&x210, &x211, x209, 0x0, x190);
  uint32_t x212;
  fiat_p256_uint1 x213;
  fiat_p256_addcarryx_u32(&x212, &x213, x211, 0x0, x192);
  uint32_t x214;
  fiat_p256_uint1 x215;
  fiat_p256_addcarryx_u32(&x214, &x215, x213, 0x0, x194);
  uint32_t x216;
  fiat_p256_uint1 x217;
  fiat_p256_addcarryx_u32(&x216, &x217, x215, 0x0, x196);
  uint32_t x218;
  fiat_p256_uint1 x219;
  fiat_p256_addcarryx_u32(&x218, &x219, x217, 0x0, x198);
  uint32_t x220;
  fiat_p256_uint1 x221;
  fiat_p256_addcarryx_u32(&x220, &x221, x219, 0x0, x204);
  uint32_t x222;
  uint32_t x223;
  fiat_p256_mulx_u32(&x222, &x223, x206, UINT32_C(0xffffffff));
  uint32_t x224;
  uint32_t x225;
  fiat_p256_mulx_u32(&x224, &x225, x206, UINT32_C(0xffffffff));
  uint32_t x226;
  uint32_t x227;
  fiat_p256_mulx_u32(&x226, &x227, x206, UINT32_C(0xffffffff));
  uint32_t x228;
  uint32_t x229;
  fiat_p256_mulx_u32(&x228, &x229, x206, UINT32_C(0xffffffff));
  uint32_t x230;
  fiat_p256_uint1 x231;
  fiat_p256_addcarryx_u32(&x230, &x231, 0x0, x226, x229);
  uint32_t x232;
  fiat_p256_uint1 x233;
  fiat_p256_addcarryx_u32(&x232, &x233, x231, x224, x227);
  uint32_t x234;
  fiat_p256_uint1 x235;
  fiat_p256_addcarryx_u32(&x234, &x235, 0x0, x228, x206);
  uint32_t x236;
  fiat_p256_uint1 x237;
  fiat_p256_addcarryx_u32(&x236, &x237, x235, x230, x208);
  uint32_t x238;
  fiat_p256_uint1 x239;
  fiat_p256_addcarryx_u32(&x238, &x239, x237, x232, x210);
  uint32_t x240;
  fiat_p256_uint1 x241;
  fiat_p256_addcarryx_u32(&x240, &x241, x233, 0x0, x225);
  uint32_t x242;
  fiat_p256_uint1 x243;
  fiat_p256_addcarryx_u32(&x242, &x243, x239, x240, x212);
  uint32_t x244;
  fiat_p256_uint1 x245;
  fiat_p256_addcarryx_u32(&x244, &x245, x243, 0x0, x214);
  uint32_t x246;
  fiat_p256_uint1 x247;
  fiat_p256_addcarryx_u32(&x246, &x247, x245, 0x0, x216);
  uint32_t x248;
  fiat_p256_uint1 x249;
  fiat_p256_addcarryx_u32(&x248, &x249, x247, x206, x218);
  uint32_t x250;
  fiat_p256_uint1 x251;
  fiat_p256_addcarryx_u32(&x250, &x251, x249, x222, x220);
  uint32_t x252;
  fiat_p256_uint1 x253;
  fiat_p256_addcarryx_u32(&x252, &x253, x205, 0x0, 0x0);
  uint32_t x254;
  fiat_p256_uint1 x255;
  fiat_p256_addcarryx_u32(&x254, &x255, x221, 0x0, (fiat_p256_uint1)x252);
  uint32_t x256;
  fiat_p256_uint1 x257;
  fiat_p256_addcarryx_u32(&x256, &x257, x251, x223, x254);
  uint32_t x258;
  fiat_p256_uint1 x259;
  fiat_p256_addcarryx_u32(&x258, &x259, 0x0, (arg1[6]), x236);
  uint32_t x260;
  fiat_p256_uint1 x261;
  fiat_p256_addcarryx_u32(&x260, &x261, x259, 0x0, x238);
  uint32_t x262;
  fiat_p256_uint1 x263;
  fiat_p256_addcarryx_u32(&x262, &x263, x261, 0x0, x242);
  uint32_t x264;
  fiat_p256_uint1 x265;
  fiat_p256_addcarryx_u32(&x264, &x265, x263, 0x0, x244);
  uint32_t x266;
  fiat_p256_uint1 x267;
  fiat_p256_addcarryx_u32(&x266, &x267, x265, 0x0, x246);
  uint32_t x268;
  fiat_p256_uint1 x269;
  fiat_p256_addcarryx_u32(&x268, &x269, x267, 0x0, x248);
  uint32_t x270;
  fiat_p256_uint1 x271;
  fiat_p256_addcarryx_u32(&x270, &x271, x269, 0x0, x250);
  uint32_t x272;
  fiat_p256_uint1 x273;
  fiat_p256_addcarryx_u32(&x272, &x273, x271, 0x0, x256);
  uint32_t x274;
  uint32_t x275;
  fiat_p256_mulx_u32(&x274, &x275, x258, UINT32_C(0xffffffff));
  uint32_t x276;
  uint32_t x277;
  fiat_p256_mulx_u32(&x276, &x277, x258, UINT32_C(0xffffffff));
  uint32_t x278;
  uint32_t x279;
  fiat_p256_mulx_u32(&x278, &x279, x258, UINT32_C(0xffffffff));
  uint32_t x280;
  uint32_t x281;
  fiat_p256_mulx_u32(&x280, &x281, x258, UINT32_C(0xffffffff));
  uint32_t x282;
  fiat_p256_uint1 x283;
  fiat_p256_addcarryx_u32(&x282, &x283, 0x0, x278, x281);
  uint32_t x284;
  fiat_p256_uint1 x285;
  fiat_p256_addcarryx_u32(&x284, &x285, x283, x276, x279);
  uint32_t x286;
  fiat_p256_uint1 x287;
  fiat_p256_addcarryx_u32(&x286, &x287, 0x0, x280, x258);
  uint32_t x288;
  fiat_p256_uint1 x289;
  fiat_p256_addcarryx_u32(&x288, &x289, x287, x282, x260);
  uint32_t x290;
  fiat_p256_uint1 x291;
  fiat_p256_addcarryx_u32(&x290, &x291, x289, x284, x262);
  uint32_t x292;
  fiat_p256_uint1 x293;
  fiat_p256_addcarryx_u32(&x292, &x293, x285, 0x0, x277);
  uint32_t x294;
  fiat_p256_uint1 x295;
  fiat_p256_addcarryx_u32(&x294, &x295, x291, x292, x264);
  uint32_t x296;
  fiat_p256_uint1 x297;
  fiat_p256_addcarryx_u32(&x296, &x297, x295, 0x0, x266);
  uint32_t x298;
  fiat_p256_uint1 x299;
  fiat_p256_addcarryx_u32(&x298, &x299, x297, 0x0, x268);
  uint32_t x300;
  fiat_p256_uint1 x301;
  fiat_p256_addcarryx_u32(&x300, &x301, x299, x258, x270);
  uint32_t x302;
  fiat_p256_uint1 x303;
  fiat_p256_addcarryx_u32(&x302, &x303, x301, x274, x272);
  uint32_t x304;
  fiat_p256_uint1 x305;
  fiat_p256_addcarryx_u32(&x304, &x305, x257, 0x0, 0x0);
  uint32_t x306;
  fiat_p256_uint1 x307;
  fiat_p256_addcarryx_u32(&x306, &x307, x273, 0x0, (fiat_p256_uint1)x304);
  uint32_t x308;
  fiat_p256_uint1 x309;
  fiat_p256_addcarryx_u32(&x308, &x309, x303, x275, x306);
  uint32_t x310;
  fiat_p256_uint1 x311;
  fiat_p256_addcarryx_u32(&x310, &x311, 0x0, (arg1[7]), x288);
  uint32_t x312;
  fiat_p256_uint1 x313;
  fiat_p256_addcarryx_u32(&x312, &x313, x311, 0x0, x290);
  uint32_t x314;
  fiat_p256_uint1 x315;
  fiat_p256_addcarryx_u32(&x314, &x315, x313, 0x0, x294);
  uint32_t x316;
  fiat_p256_uint1 x317;
  fiat_p256_addcarryx_u32(&x316, &x317, x315, 0x0, x296);
  uint32_t x318;
  fiat_p256_uint1 x319;
  fiat_p256_addcarryx_u32(&x318, &x319, x317, 0x0, x298);
  uint32_t x320;
  fiat_p256_uint1 x321;
  fiat_p256_addcarryx_u32(&x320, &x321, x319, 0x0, x300);
  uint32_t x322;
  fiat_p256_uint1 x323;
  fiat_p256_addcarryx_u32(&x322, &x323, x321, 0x0, x302);
  uint32_t x324;
  fiat_p256_uint1 x325;
  fiat_p256_addcarryx_u32(&x324, &x325, x323, 0x0, x308);
  uint32_t x326;
  uint32_t x327;
  fiat_p256_mulx_u32(&x326, &x327, x310, UINT32_C(0xffffffff));
  uint32_t x328;
  uint32_t x329;
  fiat_p256_mulx_u32(&x328, &x329, x310, UINT32_C(0xffffffff));
  uint32_t x330;
  uint32_t x331;
  fiat_p256_mulx_u32(&x330, &x331, x310, UINT32_C(0xffffffff));
  uint32_t x332;
  uint32_t x333;
  fiat_p256_mulx_u32(&x332, &x333, x310, UINT32_C(0xffffffff));
  uint32_t x334;
  fiat_p256_uint1 x335;
  fiat_p256_addcarryx_u32(&x334, &x335, 0x0, x330, x333);
  uint32_t x336;
  fiat_p256_uint1 x337;
  fiat_p256_addcarryx_u32(&x336, &x337, x335, x328, x331);
  uint32_t x338;
  fiat_p256_uint1 x339;
  fiat_p256_addcarryx_u32(&x338, &x339, 0x0, x332, x310);
  uint32_t x340;
  fiat_p256_uint1 x341;
  fiat_p256_addcarryx_u32(&x340, &x341, x339, x334, x312);
  uint32_t x342;
  fiat_p256_uint1 x343;
  fiat_p256_addcarryx_u32(&x342, &x343, x341, x336, x314);
  uint32_t x344;
  fiat_p256_uint1 x345;
  fiat_p256_addcarryx_u32(&x344, &x345, x337, 0x0, x329);
  uint32_t x346;
  fiat_p256_uint1 x347;
  fiat_p256_addcarryx_u32(&x346, &x347, x343, x344, x316);
  uint32_t x348;
  fiat_p256_uint1 x349;
  fiat_p256_addcarryx_u32(&x348, &x349, x347, 0x0, x318);
  uint32_t x350;
  fiat_p256_uint1 x351;
  fiat_p256_addcarryx_u32(&x350, &x351, x349, 0x0, x320);
  uint32_t x352;
  fiat_p256_uint1 x353;
  fiat_p256_addcarryx_u32(&x352, &x353, x351, x310, x322);
  uint32_t x354;
  fiat_p256_uint1 x355;
  fiat_p256_addcarryx_u32(&x354, &x355, x353, x326, x324);
  uint32_t x356;
  fiat_p256_uint1 x357;
  fiat_p256_addcarryx_u32(&x356, &x357, x309, 0x0, 0x0);
  uint32_t x358;
  fiat_p256_uint1 x359;
  fiat_p256_addcarryx_u32(&x358, &x359, x325, 0x0, (fiat_p256_uint1)x356);
  uint32_t x360;
  fiat_p256_uint1 x361;
  fiat_p256_addcarryx_u32(&x360, &x361, x355, x327, x358);
  uint32_t x362;
  fiat_p256_uint1 x363;
  fiat_p256_subborrowx_u32(&x362, &x363, 0x0, x340, UINT32_C(0xffffffff));
  uint32_t x364;
  fiat_p256_uint1 x365;
  fiat_p256_subborrowx_u32(&x364, &x365, x363, x342, UINT32_C(0xffffffff));
  uint32_t x366;
  fiat_p256_uint1 x367;
  fiat_p256_subborrowx_u32(&x366, &x367, x365, x346, UINT32_C(0xffffffff));
  uint32_t x368;
  fiat_p256_uint1 x369;
  fiat_p256_subborrowx_u32(&x368, &x369, x367, x348, 0x0);
  uint32_t x370;
  fiat_p256_uint1 x371;
  fiat_p256_subborrowx_u32(&x370, &x371, x369, x350, 0x0);
  uint32_t x372;
  fiat_p256_uint1 x373;
  fiat_p256_subborrowx_u32(&x372, &x373, x371, x352, 0x0);
  uint32_t x374;
  fiat_p256_uint1 x375;
  fiat_p256_subborrowx_u32(&x374, &x375, x373, x354, 0x1);
  uint32_t x376;
  fiat_p256_uint1 x377;
  fiat_p256_subborrowx_u32(&x376, &x377, x375, x360, UINT32_C(0xffffffff));
  uint32_t x378;
  fiat_p256_uint1 x379;
  fiat_p256_addcarryx_u32(&x378, &x379, x361, 0x0, 0x0);
  uint32_t x380;
  fiat_p256_uint1 x381;
  fiat_p256_subborrowx_u32(&x380, &x381, x377, (fiat_p256_uint1)x378, 0x0);
  uint32_t x382;
  fiat_p256_cmovznz_u32(&x382, x381, x362, x340);
  uint32_t x383;
  fiat_p256_cmovznz_u32(&x383, x381, x364, x342);
  uint32_t x384;
  fiat_p256_cmovznz_u32(&x384, x381, x366, x346);
  uint32_t x385;
  fiat_p256_cmovznz_u32(&x385, x381, x368, x348);
  uint32_t x386;
  fiat_p256_cmovznz_u32(&x386, x381, x370, x350);
  uint32_t x387;
  fiat_p256_cmovznz_u32(&x387, x381, x372, x352);
  uint32_t x388;
  fiat_p256_cmovznz_u32(&x388, x381, x374, x354);
  uint32_t x389;
  fiat_p256_cmovznz_u32(&x389, x381, x376, x360);
  out1[0] = x382;
  out1[1] = x383;
  out1[2] = x384;
  out1[3] = x385;
  out1[4] = x386;
  out1[5] = x387;
  out1[6] = x388;
  out1[7] = x389;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffff]
 */
static void fiat_p256_nonzero(uint32_t* out1, const uint32_t arg1[8]) {
  uint32_t x1 = ((arg1[0]) | ((arg1[1]) | ((arg1[2]) | ((arg1[3]) | ((arg1[4]) | ((arg1[5]) | ((arg1[6]) | ((arg1[7]) | (uint32_t)0x0))))))));
  *out1 = x1;
}

/*
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 *   arg3: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 */
static void fiat_p256_selectznz(uint32_t out1[8], fiat_p256_uint1 arg1, const uint32_t arg2[8], const uint32_t arg3[8]) {
  uint32_t x1;
  fiat_p256_cmovznz_u32(&x1, arg1, (arg2[0]), (arg3[0]));
  uint32_t x2;
  fiat_p256_cmovznz_u32(&x2, arg1, (arg2[1]), (arg3[1]));
  uint32_t x3;
  fiat_p256_cmovznz_u32(&x3, arg1, (arg2[2]), (arg3[2]));
  uint32_t x4;
  fiat_p256_cmovznz_u32(&x4, arg1, (arg2[3]), (arg3[3]));
  uint32_t x5;
  fiat_p256_cmovznz_u32(&x5, arg1, (arg2[4]), (arg3[4]));
  uint32_t x6;
  fiat_p256_cmovznz_u32(&x6, arg1, (arg2[5]), (arg3[5]));
  uint32_t x7;
  fiat_p256_cmovznz_u32(&x7, arg1, (arg2[6]), (arg3[6]));
  uint32_t x8;
  fiat_p256_cmovznz_u32(&x8, arg1, (arg2[7]), (arg3[7]));
  out1[0] = x1;
  out1[1] = x2;
  out1[2] = x3;
  out1[3] = x4;
  out1[4] = x5;
  out1[5] = x6;
  out1[6] = x7;
  out1[7] = x8;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 */
static void fiat_p256_to_bytes(uint8_t out1[32], const uint32_t arg1[8]) {
  uint32_t x1 = (arg1[7]);
  uint32_t x2 = (arg1[6]);
  uint32_t x3 = (arg1[5]);
  uint32_t x4 = (arg1[4]);
  uint32_t x5 = (arg1[3]);
  uint32_t x6 = (arg1[2]);
  uint32_t x7 = (arg1[1]);
  uint32_t x8 = (arg1[0]);
  uint32_t x9 = (x8 >> 8);
  uint8_t x10 = (uint8_t)(x8 & UINT8_C(0xff));
  uint32_t x11 = (x9 >> 8);
  uint8_t x12 = (uint8_t)(x9 & UINT8_C(0xff));
  uint8_t x13 = (uint8_t)(x11 >> 8);
  uint8_t x14 = (uint8_t)(x11 & UINT8_C(0xff));
  uint8_t x15 = (uint8_t)(x13 & UINT8_C(0xff));
  uint32_t x16 = (x7 >> 8);
  uint8_t x17 = (uint8_t)(x7 & UINT8_C(0xff));
  uint32_t x18 = (x16 >> 8);
  uint8_t x19 = (uint8_t)(x16 & UINT8_C(0xff));
  uint8_t x20 = (uint8_t)(x18 >> 8);
  uint8_t x21 = (uint8_t)(x18 & UINT8_C(0xff));
  uint8_t x22 = (uint8_t)(x20 & UINT8_C(0xff));
  uint32_t x23 = (x6 >> 8);
  uint8_t x24 = (uint8_t)(x6 & UINT8_C(0xff));
  uint32_t x25 = (x23 >> 8);
  uint8_t x26 = (uint8_t)(x23 & UINT8_C(0xff));
  uint8_t x27 = (uint8_t)(x25 >> 8);
  uint8_t x28 = (uint8_t)(x25 & UINT8_C(0xff));
  uint8_t x29 = (uint8_t)(x27 & UINT8_C(0xff));
  uint32_t x30 = (x5 >> 8);
  uint8_t x31 = (uint8_t)(x5 & UINT8_C(0xff));
  uint32_t x32 = (x30 >> 8);
  uint8_t x33 = (uint8_t)(x30 & UINT8_C(0xff));
  uint8_t x34 = (uint8_t)(x32 >> 8);
  uint8_t x35 = (uint8_t)(x32 & UINT8_C(0xff));
  uint8_t x36 = (uint8_t)(x34 & UINT8_C(0xff));
  uint32_t x37 = (x4 >> 8);
  uint8_t x38 = (uint8_t)(x4 & UINT8_C(0xff));
  uint32_t x39 = (x37 >> 8);
  uint8_t x40 = (uint8_t)(x37 & UINT8_C(0xff));
  uint8_t x41 = (uint8_t)(x39 >> 8);
  uint8_t x42 = (uint8_t)(x39 & UINT8_C(0xff));
  uint8_t x43 = (uint8_t)(x41 & UINT8_C(0xff));
  uint32_t x44 = (x3 >> 8);
  uint8_t x45 = (uint8_t)(x3 & UINT8_C(0xff));
  uint32_t x46 = (x44 >> 8);
  uint8_t x47 = (uint8_t)(x44 & UINT8_C(0xff));
  uint8_t x48 = (uint8_t)(x46 >> 8);
  uint8_t x49 = (uint8_t)(x46 & UINT8_C(0xff));
  uint8_t x50 = (uint8_t)(x48 & UINT8_C(0xff));
  uint32_t x51 = (x2 >> 8);
  uint8_t x52 = (uint8_t)(x2 & UINT8_C(0xff));
  uint32_t x53 = (x51 >> 8);
  uint8_t x54 = (uint8_t)(x51 & UINT8_C(0xff));
  uint8_t x55 = (uint8_t)(x53 >> 8);
  uint8_t x56 = (uint8_t)(x53 & UINT8_C(0xff));
  uint8_t x57 = (uint8_t)(x55 & UINT8_C(0xff));
  uint32_t x58 = (x1 >> 8);
  uint8_t x59 = (uint8_t)(x1 & UINT8_C(0xff));
  uint32_t x60 = (x58 >> 8);
  uint8_t x61 = (uint8_t)(x58 & UINT8_C(0xff));
  uint8_t x62 = (uint8_t)(x60 >> 8);
  uint8_t x63 = (uint8_t)(x60 & UINT8_C(0xff));
  out1[0] = x10;
  out1[1] = x12;
  out1[2] = x14;
  out1[3] = x15;
  out1[4] = x17;
  out1[5] = x19;
  out1[6] = x21;
  out1[7] = x22;
  out1[8] = x24;
  out1[9] = x26;
  out1[10] = x28;
  out1[11] = x29;
  out1[12] = x31;
  out1[13] = x33;
  out1[14] = x35;
  out1[15] = x36;
  out1[16] = x38;
  out1[17] = x40;
  out1[18] = x42;
  out1[19] = x43;
  out1[20] = x45;
  out1[21] = x47;
  out1[22] = x49;
  out1[23] = x50;
  out1[24] = x52;
  out1[25] = x54;
  out1[26] = x56;
  out1[27] = x57;
  out1[28] = x59;
  out1[29] = x61;
  out1[30] = x63;
  out1[31] = x62;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 */
static void fiat_p256_from_bytes(uint32_t out1[8], const uint8_t arg1[32]) {
  uint32_t x1 = ((uint32_t)(arg1[31]) << 24);
  uint32_t x2 = ((uint32_t)(arg1[30]) << 16);
  uint32_t x3 = ((uint32_t)(arg1[29]) << 8);
  uint8_t x4 = (arg1[28]);
  uint32_t x5 = ((uint32_t)(arg1[27]) << 24);
  uint32_t x6 = ((uint32_t)(arg1[26]) << 16);
  uint32_t x7 = ((uint32_t)(arg1[25]) << 8);
  uint8_t x8 = (arg1[24]);
  uint32_t x9 = ((uint32_t)(arg1[23]) << 24);
  uint32_t x10 = ((uint32_t)(arg1[22]) << 16);
  uint32_t x11 = ((uint32_t)(arg1[21]) << 8);
  uint8_t x12 = (arg1[20]);
  uint32_t x13 = ((uint32_t)(arg1[19]) << 24);
  uint32_t x14 = ((uint32_t)(arg1[18]) << 16);
  uint32_t x15 = ((uint32_t)(arg1[17]) << 8);
  uint8_t x16 = (arg1[16]);
  uint32_t x17 = ((uint32_t)(arg1[15]) << 24);
  uint32_t x18 = ((uint32_t)(arg1[14]) << 16);
  uint32_t x19 = ((uint32_t)(arg1[13]) << 8);
  uint8_t x20 = (arg1[12]);
  uint32_t x21 = ((uint32_t)(arg1[11]) << 24);
  uint32_t x22 = ((uint32_t)(arg1[10]) << 16);
  uint32_t x23 = ((uint32_t)(arg1[9]) << 8);
  uint8_t x24 = (arg1[8]);
  uint32_t x25 = ((uint32_t)(arg1[7]) << 24);
  uint32_t x26 = ((uint32_t)(arg1[6]) << 16);
  uint32_t x27 = ((uint32_t)(arg1[5]) << 8);
  uint8_t x28 = (arg1[4]);
  uint32_t x29 = ((uint32_t)(arg1[3]) << 24);
  uint32_t x30 = ((uint32_t)(arg1[2]) << 16);
  uint32_t x31 = ((uint32_t)(arg1[1]) << 8);
  uint8_t x32 = (arg1[0]);
  uint32_t x33 = (x32 + (x31 + (x30 + x29)));
  uint32_t x34 = (x33 & UINT32_C(0xffffffff));
  uint32_t x35 = (x4 + (x3 + (x2 + x1)));
  uint32_t x36 = (x8 + (x7 + (x6 + x5)));
  uint32_t x37 = (x12 + (x11 + (x10 + x9)));
  uint32_t x38 = (x16 + (x15 + (x14 + x13)));
  uint32_t x39 = (x20 + (x19 + (x18 + x17)));
  uint32_t x40 = (x24 + (x23 + (x22 + x21)));
  uint32_t x41 = (x28 + (x27 + (x26 + x25)));
  uint32_t x42 = (x41 & UINT32_C(0xffffffff));
  uint32_t x43 = (x40 & UINT32_C(0xffffffff));
  uint32_t x44 = (x39 & UINT32_C(0xffffffff));
  uint32_t x45 = (x38 & UINT32_C(0xffffffff));
  uint32_t x46 = (x37 & UINT32_C(0xffffffff));
  uint32_t x47 = (x36 & UINT32_C(0xffffffff));
  out1[0] = x34;
  out1[1] = x42;
  out1[2] = x43;
  out1[3] = x44;
  out1[4] = x45;
  out1[5] = x46;
  out1[6] = x47;
  out1[7] = x35;
}

