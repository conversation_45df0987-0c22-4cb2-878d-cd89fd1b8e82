# Copyright 2019 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Defines an error (exception) class for the HParams plugin."""


class HParamsError(Exception):
    """Represents an error that is meaningful to the end-user.

    Such an error should have a meaningful error message. Other errors,
    (such as resulting from some internal invariants being violated)
    should be represented by other exceptions.
    """

    pass
