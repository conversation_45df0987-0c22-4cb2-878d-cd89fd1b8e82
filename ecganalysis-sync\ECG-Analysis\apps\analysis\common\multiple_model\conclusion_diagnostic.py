import os

import tensorflow as tf
import numpy as np
from apps.analysis.common.ecg_signal_processing import resample
from global_settings import conclusion_diagnostic_dict


def diagnostic(ecg_data, sampling_rate):
    """
    多结论诊断
    :param ecg_data: ECG信号数据
    :param sampling_rate: 采样率
    :return: 诊断结果集合
    """
    ecg_data = resample(ecg_data, sampling_rate)    # 重采样

    # 获取当前文件的绝对路径
    current_file_path = os.path.abspath(__file__)
    # 获取当前文件所在的目录
    current_dir = os.path.dirname(current_file_path)
    # 加载模型
    model_path = os.path.join(current_dir, 'model', 'tf_model_multi_label.keras')
    model = tf.keras.models.load_model(model_path)
    # 转换为模型需求格式
    predict = model.predict(np.expand_dims([ecg_data], axis=2))

    # 动态阈值参数
    initial_threshold = 0.9  # 起始阈值
    min_threshold = 0.5  # 最低阈值
    step = 0.1  # 每次下降的步长

    # 根据字典顺序获得结果对应的标签
    values = list(conclusion_diagnostic_dict.values())

    # 初始化输出
    predicted_labels = []

    # 动态调整阈值以找到符合条件的标签
    threshold = initial_threshold
    while threshold >= min_threshold:
        predicted_labels = [values[i] for i, score in enumerate(predict[0]) if score >= threshold]
        # 如果找到了符合条件的标签，则停止降低阈值
        if predicted_labels:
            break
        # 否则继续降低阈值
        threshold -= step

    return predicted_labels

multi_label_diagnosis = diagnostic


