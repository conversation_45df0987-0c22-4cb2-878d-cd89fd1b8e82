{"name": "cvat", "version": "1.0.0", "description": "Eslint && remarklint dependencies", "main": ".eslintrc.cjs", "private": true, "directories": {"test": "tests"}, "workspaces": ["cvat-data", "cvat-core", "cvat-canvas", "cvat-canvas3d", "cvat-ui"], "devDependencies": {"@babel/cli": "^7.13.16", "@babel/core": "^7.6.0", "@babel/eslint-parser": "^7.23.3", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.11.0", "@babel/preset-env": "^7.6.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.6.0", "@babel/register": "^7.22.5", "@istanbuljs/nyc-config-babel": "^3.0.0", "@types/mousetrap": "^1.6.5", "@types/node": "^18.0.3", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "airbnb": "0.0.2", "babel-loader": "^8.0.6", "babel-plugin-import": "^1.12.2", "babel-plugin-istanbul": "^6.0.0", "bundle-declarations-webpack-plugin": "^3.1.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "eslint": "^8.54.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "14.2.1", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-plugin-cypress": "^2.11.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-no-unsanitized": "^3.0.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-security": "^1.4.0", "html-webpack-plugin": "^5.5.0", "husky": "^6.0.0", "lint-staged": "^13.0.3", "micromatch": "^4.0.8", "nodemon": "^3.0.1", "nyc": "^15.1.0", "postcss": "8", "postcss-loader": "^7.3.3", "postcss-preset-env": "^9.0.0", "react-svg-loader": "^3.0.3", "remark-cli": "^9.0.0", "remark-frontmatter": "^3.0.0", "remark-gfm": "^1.0.0", "remark-lint-emphasis-marker": "^2.0.0", "remark-lint-list-item-spacing": "^2.0.0", "remark-lint-maximum-heading-length": "^2.0.0", "remark-lint-maximum-line-length": "^2.0.0", "remark-lint-no-dead-urls": "^1.0.2", "remark-lint-no-file-name-irregular-characters": "^1.0.3", "remark-lint-ordered-list-marker-style": "^2.0.0", "remark-lint-strong-marker": "^2.0.0", "remark-lint-unordered-list-marker-style": "^2.0.0", "remark-preset-lint-consistent": "^4.0.0", "remark-preset-lint-markdown-style-guide": "^4.0.0", "remark-preset-lint-recommended": "^5.0.0", "sass": "^1.42.1", "sass-loader": "^10.0.0", "source-map-support": "^0.5.19", "style-loader": "^1.0.0", "stylelint": "^15.10.2", "stylelint-config-standard-scss": "^11.1.0", "typescript": "5.0.2", "vfile-reporter-json": "^2.0.2", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "4.15.1"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "coverage": "yarn run instrument && yarn run cp && yarn run rm", "instrument": "nyc instrument cvat-ui cvat-ui_cov && nyc instrument cvat-canvas cvat-canvas_cov && nyc instrument cvat-canvas3d cvat-canvas3d_cov && nyc instrument cvat-data cvat-data_cov && nyc instrument cvat-core cvat-core_cov", "cp": "cp -r cvat-ui_cov/* cvat-ui && cp -r cvat-canvas_cov/* cvat-canvas && cp -r cvat-canvas3d_cov/* cvat-canvas3d && cp -r cvat-data_cov/* cvat-data && cp -r cvat-core_cov/* cvat-core", "rm": "rm -rf cvat-ui_cov cvat-canvas_cov cvat-canvas3d_cov cvat-data_cov cvat-core_cov", "prepare": "if [ -z \"${DISABLE_HUSKY}\" ]; then yarn run setup:husky; fi", "setup:husky": "husky install && rm .husky/pre-commit; npx husky add .husky/pre-commit \"npx lint-staged\"", "precommit:cvat-tests": "cd tests && eslint --fix", "precommit:cvat-data": "cd cvat-data && eslint --fix", "precommit:cvat-core": "cd cvat-core && eslint --fix", "precommit:cvat-canvas": "cd cvat-canvas && eslint --fix", "precommit:cvat-canvas3d": "cd cvat-canvas3d && eslint --fix", "precommit:cvat-ui": "cd cvat-ui && eslint --fix", "build:cvat-ui": "yarn workspace cvat-ui run build", "build:cvat-canvas": "yarn workspace cvat-canvas run build", "build:cvat-canvas3d": "yarn workspace cvat-canvas3d run build", "build:cvat-core": "yarn workspace cvat-core run build", "build:cvat-data": "yarn workspace cvat-data run build", "start:cvat-ui": "yarn workspace cvat-ui run start"}, "repository": {"type": "git", "url": "git+https://github.com/cvat-ai/cvat.git"}, "author": "CVAT.ai", "license": "MIT", "bugs": {"url": "https://github.com/cvat-ai/cvat/issues"}, "resolutions": {"@types/react": "18.2.55", "@types/react-dom": "18.2.19"}, "homepage": "https://github.com/cvat-ai/cvat#readme", "dependencies": {"@types/fabric": "^4.5.7", "@types/lodash": "^4.14.191", "fabric": "^5.2.1", "lodash": "^4.17.21"}}