import pymysql
import json
import traceback
import requests
from qiniu import Auth
import pandas as pd
from datetime import datetime

# 七牛云配置
QINIU = {
    'test': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://test.upload.weiheyixue.com'
    },
    'prod': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://upload.weiheyixue.com'
    }
}

# 数据库配置
DB_CONFIG = {
    "HOST": "**************",  # 替换为实际的主机地址
    "PORT": 3308,
    "USER": "ai",  # 替换为实际的用户名
    "PASSWORD": "z8^#g4r4mz",  # 替换为实际的密码
    "DBNAME": "ecg_marking",
    "CHARSET": "utf8mb4"
}

def get_qiniu_data(file_path, environment='prod'):
    """
    从七牛云下载数据
    Args:
        file_path: 文件路径
        environment: 环境，默认为prod
    Returns:
        下载的数据，JSON格式
    """
    try:
        access_key = QINIU[environment]['access_key']
        secret_key = QINIU[environment]['secret_key']
        domain_prefix = QINIU[environment]['domain_prefix']  # 存储空间域名前缀
        # 构建鉴权对象
        q = Auth(access_key, secret_key)

        # 构建私有空间的下载链接
        private_url = q.private_download_url(domain_prefix + '/ecg/' + file_path)

        # 使用requests下载文件
        response = requests.get(private_url)

        if response.status_code == 200:
            return json.loads(response.content)

        print(f"下载失败，状态码: {response.status_code}")
        return None
    except Exception as e:
        print(f"下载出错: {str(e)}")
        print(traceback.format_exc())
        return None


def query_ecg_marking_data(limit=10):
    """
    查询ecg_marking数据库中的心电数据
    Args:
        limit: 限制返回的记录数，默认为10
    Returns:
        包含心电数据的DataFrame
    """
    try:
        # 建立数据库连接
        connection = pymysql.connect(
            host=DB_CONFIG['HOST'],
            port=DB_CONFIG['PORT'],
            user=DB_CONFIG['USER'],
            password=DB_CONFIG['PASSWORD'],
            database=DB_CONFIG['DBNAME'],
            charset=DB_CONFIG['CHARSET']
        )

        cursor = connection.cursor()

        # 查询ecg_status为3的记录
        query = """
        SELECT id, es_key, create_date 
        FROM t_patient_ecg 
        WHERE ecg_status = 3
        LIMIT %s
        """

        cursor.execute(query, (limit,))
        results = cursor.fetchall()

        if not results:
            print("未找到符合条件的记录")
            return None

        print(f"找到 {len(results)} 条记录")

        # 转换为DataFrame
        columns = ['id', 'es_key', 'create_date']
        df = pd.DataFrame(results, columns=columns)

        # 关闭数据库连接
        cursor.close()
        connection.close()

        return df

    except Exception as e:
        print(f"查询数据库出错: {str(e)}")
        print(traceback.format_exc())
        return None


def download_ecg_data(limit=10, save_to_file=True):
    """
    下载心电数据并可选保存到文件
    Args:
        limit: 限制查询的记录数，默认为10
        save_to_file: 是否保存到文件，默认为True
    Returns:
        包含心电数据的字典
    """
    # 查询数据库获取es_key
    df = query_ecg_marking_data(limit)
    if df is None or df.empty:
        return None

    # 下载并处理每条记录的心电数据
    ecg_data_dict = {}
    for idx, row in df.iterrows():
        record_id = row['id']
        es_key = row['es_key']
        create_time = row['create_date']

        print(f"\n处理记录 {record_id}, es_key: {es_key}")

        # 从七牛云下载数据
        ecg_data = get_qiniu_data(es_key, 'prod')
        if ecg_data:
            print(f"成功下载记录 {record_id} 的心电数据")
            ecg_data_dict[record_id] = {
                'es_key': es_key,
                'create_date': create_time,
                'data': ecg_data
            }
        else:
            print(f"下载记录 {record_id} 的心电数据失败")

    # 保存到文件
    if save_to_file and ecg_data_dict:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"ecg_marking_data_{timestamp}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(ecg_data_dict, f, ensure_ascii=False, indent=2)
        print(f"\n数据已保存到文件: {filename}")

    return ecg_data_dict


def main():
    """
    主函数
    """
    print("开始查询ecg_marking数据库并下载心电数据...")
    
    # 设置要查询的记录数
    limit = int(input("请输入要查询的记录数量 (默认10): ") or "10")
    
    # 下载数据
    ecg_data = download_ecg_data(limit=limit)
    
    if ecg_data:
        print(f"\n成功下载 {len(ecg_data)} 条心电数据记录")
        
        # 显示第一条记录的部分数据
        first_record_id = list(ecg_data.keys())[0]
        first_record = ecg_data[first_record_id]
        print(f"\n第一条记录 (ID: {first_record_id}) 的信息:")
        print(f"es_key: {first_record['es_key']}")
        print(f"创建时间: {first_record['create_date']}")
        
        # 显示数据结构
        data_sample = first_record['data']
        if isinstance(data_sample, dict):
            print("\n数据结构:")
            for key in data_sample.keys():
                print(f"- {key}")
    else:
        print("\n未能获取任何心电数据")


if __name__ == "__main__":
    main()