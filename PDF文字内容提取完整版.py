#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文字内容提取完整版
支持多种PDF文字提取库，自动选择最佳提取方案
"""

import os
import sys
from pathlib import Path

def extract_text_with_pypdf2(pdf_path):
    """使用PyPDF2提取PDF文字"""
    try:
        from PyPDF2 import PdfReader
        print("使用PyPDF2提取文字...")
        
        reader = PdfReader(pdf_path)
        text_content = []
        
        print(f"PDF文件共 {len(reader.pages)} 页")
        
        for i, page in enumerate(reader.pages, 1):
            print(f"正在提取第 {i} 页...")
            page_text = page.extract_text()
            if page_text.strip():
                text_content.append(f"=== 第 {i} 页 ===\n{page_text}\n")
        
        return "\n".join(text_content)
    except ImportError:
        print("PyPDF2 未安装")
        return None
    except Exception as e:
        print(f"PyPDF2 提取失败: {e}")
        return None

def extract_text_with_pdfplumber(pdf_path):
    """使用pdfplumber提取PDF文字"""
    try:
        import pdfplumber
        print("使用pdfplumber提取文字...")
        
        text_content = []
        
        with pdfplumber.open(pdf_path) as pdf:
            print(f"PDF文件共 {len(pdf.pages)} 页")
            
            for i, page in enumerate(pdf.pages, 1):
                print(f"正在提取第 {i} 页...")
                page_text = page.extract_text()
                if page_text and page_text.strip():
                    text_content.append(f"=== 第 {i} 页 ===\n{page_text}\n")
        
        return "\n".join(text_content)
    except ImportError:
        print("pdfplumber 未安装")
        return None
    except Exception as e:
        print(f"pdfplumber 提取失败: {e}")
        return None

def extract_text_with_pymupdf(pdf_path):
    """使用PyMuPDF (fitz)提取PDF文字"""
    try:
        import fitz  # PyMuPDF
        print("使用PyMuPDF提取文字...")
        
        doc = fitz.open(pdf_path)
        text_content = []
        
        print(f"PDF文件共 {len(doc)} 页")
        
        for i in range(len(doc)):
            print(f"正在提取第 {i+1} 页...")
            page = doc[i]
            page_text = page.get_text()
            if page_text.strip():
                text_content.append(f"=== 第 {i+1} 页 ===\n{page_text}\n")
        
        doc.close()
        return "\n".join(text_content)
    except ImportError:
        print("PyMuPDF 未安装")
        return None
    except Exception as e:
        print(f"PyMuPDF 提取失败: {e}")
        return None

def install_package(package_name):
    """安装Python包"""
    try:
        import subprocess
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"{package_name} 安装成功")
        return True
    except Exception as e:
        print(f"安装 {package_name} 失败: {e}")
        return False

def extract_pdf_text(pdf_path, output_path=None):
    """
    提取PDF文字内容的主函数
    
    Args:
        pdf_path: PDF文件路径
        output_path: 输出文件路径，如果为None则自动生成
    """
    # 检查PDF文件是否存在
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在: {pdf_path}")
        return False
    
    print(f"开始提取PDF文字内容: {pdf_path}")
    
    # 尝试不同的提取方法
    extracted_text = None
    
    # 方法1: 尝试使用pdfplumber (通常效果最好)
    extracted_text = extract_text_with_pdfplumber(pdf_path)
    
    # 方法2: 如果pdfplumber失败，尝试PyMuPDF
    if not extracted_text:
        extracted_text = extract_text_with_pymupdf(pdf_path)
    
    # 方法3: 如果PyMuPDF失败，尝试PyPDF2
    if not extracted_text:
        extracted_text = extract_text_with_pypdf2(pdf_path)
    
    # 如果所有方法都失败，尝试安装缺失的包
    if not extracted_text:
        print("所有提取方法都失败，尝试安装缺失的包...")
        
        # 尝试安装pdfplumber
        if install_package("pdfplumber"):
            extracted_text = extract_text_with_pdfplumber(pdf_path)
        
        # 如果还是失败，尝试安装PyMuPDF
        if not extracted_text and install_package("PyMuPDF"):
            extracted_text = extract_text_with_pymupdf(pdf_path)
        
        # 最后尝试PyPDF2
        if not extracted_text and install_package("PyPDF2"):
            extracted_text = extract_text_with_pypdf2(pdf_path)
    
    if not extracted_text:
        print("错误: 无法提取PDF文字内容")
        return False
    
    # 生成输出文件路径
    if output_path is None:
        pdf_name = Path(pdf_path).stem
        output_path = f"{pdf_name}_提取的文字内容.txt"
    
    # 保存提取的文字内容
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(extracted_text)
        
        print(f"\n✅ 文字内容提取完成!")
        print(f"📄 输出文件: {os.path.abspath(output_path)}")
        print(f"📊 提取的文字长度: {len(extracted_text)} 字符")
        
        return True
    except Exception as e:
        print(f"错误: 保存文件失败: {e}")
        return False

def main():
    """主函数"""
    # 指定PDF文件路径
    pdf_path = r"D:\Project\checkpoints\【高频成语+高频实词800词（上册）】四海2025下半年.pdf"
    
    # 提取文字内容
    success = extract_pdf_text(pdf_path)
    
    if success:
        print("\n🎉 PDF文字提取任务完成!")
    else:
        print("\n❌ PDF文字提取任务失败!")

if __name__ == "__main__":
    main()
