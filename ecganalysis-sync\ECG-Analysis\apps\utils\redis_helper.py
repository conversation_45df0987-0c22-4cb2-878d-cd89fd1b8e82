import redis
import json
from typing import Any, Dict, List, Optional

from global_settings import redis_config


class RedisHelper:
    client = None

    def __init__(self):
        # Redis 服务器配置
        self.host: str = redis_config['host']  # Redis 服务器地址
        self.port: int = redis_config['port']  # Redis 服务器端口
        self.db: int = redis_config['db']      # Redis 数据库编号
        self.password: Optional[str] = redis_config['password']  # Redis 密码（可选）
        self.decode_responses: bool = True     # 是否将响应解码为字符串
        self.socket_timeout: int = 10          # 套接字超时时间（秒）

        # 创建 Redis 连接池
        self.create_pool()

    def create_pool(self):
        """
        初始化 Redis 客户端连接池
        """
        # 创建连接池实例
        pool = redis.ConnectionPool(
            host=self.host,
            port=self.port,
            db=self.db,
            password=self.password,
            decode_responses=self.decode_responses,
            socket_timeout=self.socket_timeout
        )
        # 使用连接池创建 Redis 客户端
        self.client = redis.Redis(connection_pool=pool)

    def set(self, name: str, value: Any, ex: int = None):
        """
        设置键值对

        Args:
            name (str): 键
            value (Any): 值
            ex (int, optional): 过期时间（秒）。 Defaults to None.

        Returns:
            bool: 操作是否成功
        """
        try:
            # 将值序列化为 JSON 字符串
            serialized_value = json.dumps(value)
            # 使用 Redis 客户端设置键值对
            return self.client.set(name, serialized_value, ex=ex)
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in set: {e}")
            return False

    def get(self, name: str) -> Any:
        """
        获取键对应的值

        Args:
            name (str): 键

        Returns:
            Any: 值或 None（如果键不存在）
        """
        try:
            # 使用 Redis 客户端获取键对应的值
            value = self.client.get(name)
            if value:
                # 将值反序列化为 Python 对象
                return json.loads(value)
            return None
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in get: {e}")
            return None

    def delete(self, name: str) -> int:
        """
        删除键

        Args:
            name (str): 键

        Returns:
            int: 被删除的键的数量
        """
        try:
            # 使用 Redis 客户端删除键
            return self.client.delete(name)
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in delete: {e}")
            return 0

    def hset(self, name: str, key: str, value: Any) -> int:
        """
        在哈希表中设置字段的值

        Args:
            name (str): 哈希表的键
            key (str): 字段
            value (Any): 值

        Returns:
            int: 如果字段是哈希表中的一个新字段，返回 1；否则返回 0
        """
        try:
            # 将值序列化为 JSON 字符串
            serialized_value = json.dumps(value)
            # 使用 Redis 客户端在哈希表中设置字段的值
            return self.client.hset(name, key, serialized_value)
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in hset: {e}")
            return 0

    def hget(self, name: str, key: str) -> Any:
        """
        获取哈希表中字段的值

        Args:
            name (str): 哈希表的键
            key (str): 字段

        Returns:
            Any: 字段的值或 None（如果字段不存在）
        """
        try:
            # 使用 Redis 客户端获取哈希表中字段的值
            value = self.client.hget(name, key)
            if value:
                # 将值反序列化为 Python 对象
                return json.loads(value)
            return None
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in hget: {e}")
            return None

    def hdel(self, name: str, key: str) -> int:
        """
        删除哈希表中的字段

        Args:
            name (str): 哈希表的键
            key (str): 字段

        Returns:
            int: 被删除的字段的数量
        """
        try:
            # 使用 Redis 客户端删除哈希表中的字段
            return self.client.hdel(name, key)
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in hdel: {e}")
            return 0

    def lpush(self, name: str, value: Any) -> int:
        """
        将值插入列表头部

        Args:
            name (str): 列表的键
            value (Any): 值

        Returns:
            int: 列表的长度
        """
        try:
            # 将值序列化为 JSON 字符串
            serialized_value = json.dumps(value)
            # 使用 Redis 客户端将值插入列表头部
            return self.client.lpush(name, serialized_value)
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in lpush: {e}")
            return 0

    def rpush(self, name: str, value: Any) -> int:
        """
        将值插入列表尾部

        Args:
            name (str): 列表的键
            value (Any): 值

        Returns:
            int: 列表的长度
        """
        try:
            # 将值序列化为 JSON 字符串
            serialized_value = json.dumps(value)
            # 使用 Redis 客户端将值插入列表尾部
            return self.client.rpush(name, serialized_value)
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in rpush: {e}")
            return 0

    def lpop(self, name: str) -> Any:
        """
        移除并返回列表的第一个元素

        Args:
            name (str): 列表的键

        Returns:
            Any: 列表的第一个元素或 None（如果列表为空）
        """
        try:
            # 使用 Redis 客户端移除并返回列表的第一个元素
            value = self.client.lpop(name)
            if value:
                # 将值反序列化为 Python 对象
                return json.loads(value)
            return None
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in lpop: {e}")
            return None

    def rpop(self, name: str) -> Any:
        """
        移除并返回列表的最后一个元素

        Args:
            name (str): 列表的键

        Returns:
            Any: 列表的最后一个元素或 None（如果列表为空）
        """
        try:
            # 使用 Redis 客户端移除并返回列表的最后一个元素
            value = self.client.rpop(name)
            if value:
                # 将值反序列化为 Python 对象
                return json.loads(value)
            return None
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in rpop: {e}")
            return None

    def expire(self, name: str, time: int) -> bool:
        """
        设置键的过期时间

        Args:
            name (str): 键
            time (int): 过期时间（秒）

        Returns:
            bool: 操作是否成功
        """
        try:
            # 使用 Redis 客户端设置键的过期时间
            return self.client.expire(name, time)
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in expire: {e}")
            return False

    def ttl(self, name: str) -> int:
        """
        获取键的剩余生存时间

        Args:
            name (str): 键

        Returns:
            int: 剩余生存时间（秒），如果键不存在或没有设置过期时间，返回 -1
        """
        try:
            # 使用 Redis 客户端获取键的剩余生存时间
            return self.client.ttl(name)
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in ttl: {e}")
            return -1

    def exists(self, name: str) -> int:
        """
        检查键是否存在

        Args:
            name (str): 键

        Returns:
            int: 如果键存在，返回 1；否则返回 0
        """
        try:
            # 使用 Redis 客户端检查键是否存在
            return self.client.exists(name)
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in exists: {e}")
            return 0

    def keys(self, pattern: str = '*') -> List[str]:
        """
        获取符合给定模式的键列表

        Args:
            pattern (str, optional): 键模式（默认为 '*'，表示所有键）。 Defaults to '*'.


        Returns:
            List[str]: 符合模式的键列表
        """
        try:
            # 使用 Redis 客户端获取符合给定模式的键列表
            return self.client.keys(pattern)
        except Exception as e:
            # 捕获并打印异常
            print(f"Error occurred in keys: {e}")
            return []