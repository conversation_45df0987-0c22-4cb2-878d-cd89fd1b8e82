from functools import wraps

from django.conf import settings  # 用于访问 Django 配置
from django.utils import timezone

from apps.utils.get_response import GetResponse
from apps.utils.redis_helper import RedisHelper


def authentication(func):
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        if settings.DEBUG:
            # 在调试模式下，为 custom_id 设置默认值
            kwargs['custom_id'] = 'default_custom_id'  # 设置你期望的默认值
            return func(request, *args, **kwargs)

        # 正常的token验证逻辑
        token = request.META.get('HTTP_X_AUTH_TOKEN')
        if not token:
            return GetResponse.get_response(code=4)

        cache_key = f"token_{token}"
        token_info = RedisHelper().get(cache_key)

        if not token_info:
            # 如果缓存中没有找到 token，返回错误响应
            return GetResponse.get_response(code=4)

        # 从缓存中获取 Token 的过期时间和 custom_id
        kwargs['custom_id'] = token_info['custom_id']
        return func(request, *args, **kwargs)

    return wrapper
