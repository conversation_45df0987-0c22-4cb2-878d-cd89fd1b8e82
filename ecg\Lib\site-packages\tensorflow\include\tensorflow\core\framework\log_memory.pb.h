// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/log_memory.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_description.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
namespace tensorflow {
class MemoryLogRawAllocation;
class MemoryLogRawAllocationDefaultTypeInternal;
extern MemoryLogRawAllocationDefaultTypeInternal _MemoryLogRawAllocation_default_instance_;
class MemoryLogRawDeallocation;
class MemoryLogRawDeallocationDefaultTypeInternal;
extern MemoryLogRawDeallocationDefaultTypeInternal _MemoryLogRawDeallocation_default_instance_;
class MemoryLogStep;
class MemoryLogStepDefaultTypeInternal;
extern MemoryLogStepDefaultTypeInternal _MemoryLogStep_default_instance_;
class MemoryLogTensorAllocation;
class MemoryLogTensorAllocationDefaultTypeInternal;
extern MemoryLogTensorAllocationDefaultTypeInternal _MemoryLogTensorAllocation_default_instance_;
class MemoryLogTensorDeallocation;
class MemoryLogTensorDeallocationDefaultTypeInternal;
extern MemoryLogTensorDeallocationDefaultTypeInternal _MemoryLogTensorDeallocation_default_instance_;
class MemoryLogTensorOutput;
class MemoryLogTensorOutputDefaultTypeInternal;
extern MemoryLogTensorOutputDefaultTypeInternal _MemoryLogTensorOutput_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::MemoryLogRawAllocation* Arena::CreateMaybeMessage<::tensorflow::MemoryLogRawAllocation>(Arena*);
template<> ::tensorflow::MemoryLogRawDeallocation* Arena::CreateMaybeMessage<::tensorflow::MemoryLogRawDeallocation>(Arena*);
template<> ::tensorflow::MemoryLogStep* Arena::CreateMaybeMessage<::tensorflow::MemoryLogStep>(Arena*);
template<> ::tensorflow::MemoryLogTensorAllocation* Arena::CreateMaybeMessage<::tensorflow::MemoryLogTensorAllocation>(Arena*);
template<> ::tensorflow::MemoryLogTensorDeallocation* Arena::CreateMaybeMessage<::tensorflow::MemoryLogTensorDeallocation>(Arena*);
template<> ::tensorflow::MemoryLogTensorOutput* Arena::CreateMaybeMessage<::tensorflow::MemoryLogTensorOutput>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class MemoryLogStep :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogStep) */ {
 public:
  MemoryLogStep();
  virtual ~MemoryLogStep();

  MemoryLogStep(const MemoryLogStep& from);
  MemoryLogStep(MemoryLogStep&& from) noexcept
    : MemoryLogStep() {
    *this = ::std::move(from);
  }

  inline MemoryLogStep& operator=(const MemoryLogStep& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogStep& operator=(MemoryLogStep&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemoryLogStep& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryLogStep* internal_default_instance() {
    return reinterpret_cast<const MemoryLogStep*>(
               &_MemoryLogStep_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MemoryLogStep& a, MemoryLogStep& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogStep* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogStep* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemoryLogStep* New() const final {
    return CreateMaybeMessage<MemoryLogStep>(nullptr);
  }

  MemoryLogStep* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemoryLogStep>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemoryLogStep& from);
  void MergeFrom(const MemoryLogStep& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogStep* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogStep";
  }
  protected:
  explicit MemoryLogStep(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleFieldNumber = 2,
    kStepIdFieldNumber = 1,
  };
  // string handle = 2;
  void clear_handle();
  const std::string& handle() const;
  void set_handle(const std::string& value);
  void set_handle(std::string&& value);
  void set_handle(const char* value);
  void set_handle(const char* value, size_t size);
  std::string* mutable_handle();
  std::string* release_handle();
  void set_allocated_handle(std::string* handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_handle(
      std::string* handle);

  // int64 step_id = 1;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogStep)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr handle_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// -------------------------------------------------------------------

class MemoryLogTensorAllocation :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogTensorAllocation) */ {
 public:
  MemoryLogTensorAllocation();
  virtual ~MemoryLogTensorAllocation();

  MemoryLogTensorAllocation(const MemoryLogTensorAllocation& from);
  MemoryLogTensorAllocation(MemoryLogTensorAllocation&& from) noexcept
    : MemoryLogTensorAllocation() {
    *this = ::std::move(from);
  }

  inline MemoryLogTensorAllocation& operator=(const MemoryLogTensorAllocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogTensorAllocation& operator=(MemoryLogTensorAllocation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemoryLogTensorAllocation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryLogTensorAllocation* internal_default_instance() {
    return reinterpret_cast<const MemoryLogTensorAllocation*>(
               &_MemoryLogTensorAllocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MemoryLogTensorAllocation& a, MemoryLogTensorAllocation& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogTensorAllocation* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogTensorAllocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemoryLogTensorAllocation* New() const final {
    return CreateMaybeMessage<MemoryLogTensorAllocation>(nullptr);
  }

  MemoryLogTensorAllocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemoryLogTensorAllocation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemoryLogTensorAllocation& from);
  void MergeFrom(const MemoryLogTensorAllocation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogTensorAllocation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogTensorAllocation";
  }
  protected:
  explicit MemoryLogTensorAllocation(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKernelNameFieldNumber = 2,
    kTensorFieldNumber = 3,
    kStepIdFieldNumber = 1,
  };
  // string kernel_name = 2;
  void clear_kernel_name();
  const std::string& kernel_name() const;
  void set_kernel_name(const std::string& value);
  void set_kernel_name(std::string&& value);
  void set_kernel_name(const char* value);
  void set_kernel_name(const char* value, size_t size);
  std::string* mutable_kernel_name();
  std::string* release_kernel_name();
  void set_allocated_kernel_name(std::string* kernel_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_kernel_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_kernel_name(
      std::string* kernel_name);

  // .tensorflow.TensorDescription tensor = 3;
  bool has_tensor() const;
  void clear_tensor();
  const ::tensorflow::TensorDescription& tensor() const;
  ::tensorflow::TensorDescription* release_tensor();
  ::tensorflow::TensorDescription* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorDescription* tensor);
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorDescription* tensor);
  ::tensorflow::TensorDescription* unsafe_arena_release_tensor();

  // int64 step_id = 1;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogTensorAllocation)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr kernel_name_;
  ::tensorflow::TensorDescription* tensor_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// -------------------------------------------------------------------

class MemoryLogTensorDeallocation :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogTensorDeallocation) */ {
 public:
  MemoryLogTensorDeallocation();
  virtual ~MemoryLogTensorDeallocation();

  MemoryLogTensorDeallocation(const MemoryLogTensorDeallocation& from);
  MemoryLogTensorDeallocation(MemoryLogTensorDeallocation&& from) noexcept
    : MemoryLogTensorDeallocation() {
    *this = ::std::move(from);
  }

  inline MemoryLogTensorDeallocation& operator=(const MemoryLogTensorDeallocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogTensorDeallocation& operator=(MemoryLogTensorDeallocation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemoryLogTensorDeallocation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryLogTensorDeallocation* internal_default_instance() {
    return reinterpret_cast<const MemoryLogTensorDeallocation*>(
               &_MemoryLogTensorDeallocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(MemoryLogTensorDeallocation& a, MemoryLogTensorDeallocation& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogTensorDeallocation* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogTensorDeallocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemoryLogTensorDeallocation* New() const final {
    return CreateMaybeMessage<MemoryLogTensorDeallocation>(nullptr);
  }

  MemoryLogTensorDeallocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemoryLogTensorDeallocation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemoryLogTensorDeallocation& from);
  void MergeFrom(const MemoryLogTensorDeallocation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogTensorDeallocation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogTensorDeallocation";
  }
  protected:
  explicit MemoryLogTensorDeallocation(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAllocatorNameFieldNumber = 2,
    kAllocationIdFieldNumber = 1,
  };
  // string allocator_name = 2;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  void set_allocator_name(const std::string& value);
  void set_allocator_name(std::string&& value);
  void set_allocator_name(const char* value);
  void set_allocator_name(const char* value, size_t size);
  std::string* mutable_allocator_name();
  std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_allocator_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_allocator_name(
      std::string* allocator_name);

  // int64 allocation_id = 1;
  void clear_allocation_id();
  ::PROTOBUF_NAMESPACE_ID::int64 allocation_id() const;
  void set_allocation_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogTensorDeallocation)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 allocation_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// -------------------------------------------------------------------

class MemoryLogTensorOutput :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogTensorOutput) */ {
 public:
  MemoryLogTensorOutput();
  virtual ~MemoryLogTensorOutput();

  MemoryLogTensorOutput(const MemoryLogTensorOutput& from);
  MemoryLogTensorOutput(MemoryLogTensorOutput&& from) noexcept
    : MemoryLogTensorOutput() {
    *this = ::std::move(from);
  }

  inline MemoryLogTensorOutput& operator=(const MemoryLogTensorOutput& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogTensorOutput& operator=(MemoryLogTensorOutput&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemoryLogTensorOutput& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryLogTensorOutput* internal_default_instance() {
    return reinterpret_cast<const MemoryLogTensorOutput*>(
               &_MemoryLogTensorOutput_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MemoryLogTensorOutput& a, MemoryLogTensorOutput& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogTensorOutput* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogTensorOutput* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemoryLogTensorOutput* New() const final {
    return CreateMaybeMessage<MemoryLogTensorOutput>(nullptr);
  }

  MemoryLogTensorOutput* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemoryLogTensorOutput>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemoryLogTensorOutput& from);
  void MergeFrom(const MemoryLogTensorOutput& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogTensorOutput* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogTensorOutput";
  }
  protected:
  explicit MemoryLogTensorOutput(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKernelNameFieldNumber = 2,
    kTensorFieldNumber = 4,
    kStepIdFieldNumber = 1,
    kIndexFieldNumber = 3,
  };
  // string kernel_name = 2;
  void clear_kernel_name();
  const std::string& kernel_name() const;
  void set_kernel_name(const std::string& value);
  void set_kernel_name(std::string&& value);
  void set_kernel_name(const char* value);
  void set_kernel_name(const char* value, size_t size);
  std::string* mutable_kernel_name();
  std::string* release_kernel_name();
  void set_allocated_kernel_name(std::string* kernel_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_kernel_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_kernel_name(
      std::string* kernel_name);

  // .tensorflow.TensorDescription tensor = 4;
  bool has_tensor() const;
  void clear_tensor();
  const ::tensorflow::TensorDescription& tensor() const;
  ::tensorflow::TensorDescription* release_tensor();
  ::tensorflow::TensorDescription* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorDescription* tensor);
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorDescription* tensor);
  ::tensorflow::TensorDescription* unsafe_arena_release_tensor();

  // int64 step_id = 1;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int32 index = 3;
  void clear_index();
  ::PROTOBUF_NAMESPACE_ID::int32 index() const;
  void set_index(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogTensorOutput)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr kernel_name_;
  ::tensorflow::TensorDescription* tensor_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 index_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// -------------------------------------------------------------------

class MemoryLogRawAllocation :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogRawAllocation) */ {
 public:
  MemoryLogRawAllocation();
  virtual ~MemoryLogRawAllocation();

  MemoryLogRawAllocation(const MemoryLogRawAllocation& from);
  MemoryLogRawAllocation(MemoryLogRawAllocation&& from) noexcept
    : MemoryLogRawAllocation() {
    *this = ::std::move(from);
  }

  inline MemoryLogRawAllocation& operator=(const MemoryLogRawAllocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogRawAllocation& operator=(MemoryLogRawAllocation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemoryLogRawAllocation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryLogRawAllocation* internal_default_instance() {
    return reinterpret_cast<const MemoryLogRawAllocation*>(
               &_MemoryLogRawAllocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(MemoryLogRawAllocation& a, MemoryLogRawAllocation& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogRawAllocation* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogRawAllocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemoryLogRawAllocation* New() const final {
    return CreateMaybeMessage<MemoryLogRawAllocation>(nullptr);
  }

  MemoryLogRawAllocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemoryLogRawAllocation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemoryLogRawAllocation& from);
  void MergeFrom(const MemoryLogRawAllocation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogRawAllocation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogRawAllocation";
  }
  protected:
  explicit MemoryLogRawAllocation(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperationFieldNumber = 2,
    kAllocatorNameFieldNumber = 6,
    kStepIdFieldNumber = 1,
    kNumBytesFieldNumber = 3,
    kPtrFieldNumber = 4,
    kAllocationIdFieldNumber = 5,
  };
  // string operation = 2;
  void clear_operation();
  const std::string& operation() const;
  void set_operation(const std::string& value);
  void set_operation(std::string&& value);
  void set_operation(const char* value);
  void set_operation(const char* value, size_t size);
  std::string* mutable_operation();
  std::string* release_operation();
  void set_allocated_operation(std::string* operation);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_operation();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_operation(
      std::string* operation);

  // string allocator_name = 6;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  void set_allocator_name(const std::string& value);
  void set_allocator_name(std::string&& value);
  void set_allocator_name(const char* value);
  void set_allocator_name(const char* value, size_t size);
  std::string* mutable_allocator_name();
  std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_allocator_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_allocator_name(
      std::string* allocator_name);

  // int64 step_id = 1;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 num_bytes = 3;
  void clear_num_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 num_bytes() const;
  void set_num_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // uint64 ptr = 4;
  void clear_ptr();
  ::PROTOBUF_NAMESPACE_ID::uint64 ptr() const;
  void set_ptr(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int64 allocation_id = 5;
  void clear_allocation_id();
  ::PROTOBUF_NAMESPACE_ID::int64 allocation_id() const;
  void set_allocation_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogRawAllocation)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr operation_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_bytes_;
  ::PROTOBUF_NAMESPACE_ID::uint64 ptr_;
  ::PROTOBUF_NAMESPACE_ID::int64 allocation_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// -------------------------------------------------------------------

class MemoryLogRawDeallocation :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogRawDeallocation) */ {
 public:
  MemoryLogRawDeallocation();
  virtual ~MemoryLogRawDeallocation();

  MemoryLogRawDeallocation(const MemoryLogRawDeallocation& from);
  MemoryLogRawDeallocation(MemoryLogRawDeallocation&& from) noexcept
    : MemoryLogRawDeallocation() {
    *this = ::std::move(from);
  }

  inline MemoryLogRawDeallocation& operator=(const MemoryLogRawDeallocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogRawDeallocation& operator=(MemoryLogRawDeallocation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemoryLogRawDeallocation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryLogRawDeallocation* internal_default_instance() {
    return reinterpret_cast<const MemoryLogRawDeallocation*>(
               &_MemoryLogRawDeallocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(MemoryLogRawDeallocation& a, MemoryLogRawDeallocation& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogRawDeallocation* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogRawDeallocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemoryLogRawDeallocation* New() const final {
    return CreateMaybeMessage<MemoryLogRawDeallocation>(nullptr);
  }

  MemoryLogRawDeallocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemoryLogRawDeallocation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemoryLogRawDeallocation& from);
  void MergeFrom(const MemoryLogRawDeallocation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogRawDeallocation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogRawDeallocation";
  }
  protected:
  explicit MemoryLogRawDeallocation(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperationFieldNumber = 2,
    kAllocatorNameFieldNumber = 4,
    kStepIdFieldNumber = 1,
    kAllocationIdFieldNumber = 3,
    kDeferredFieldNumber = 5,
  };
  // string operation = 2;
  void clear_operation();
  const std::string& operation() const;
  void set_operation(const std::string& value);
  void set_operation(std::string&& value);
  void set_operation(const char* value);
  void set_operation(const char* value, size_t size);
  std::string* mutable_operation();
  std::string* release_operation();
  void set_allocated_operation(std::string* operation);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_operation();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_operation(
      std::string* operation);

  // string allocator_name = 4;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  void set_allocator_name(const std::string& value);
  void set_allocator_name(std::string&& value);
  void set_allocator_name(const char* value);
  void set_allocator_name(const char* value, size_t size);
  std::string* mutable_allocator_name();
  std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_allocator_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_allocator_name(
      std::string* allocator_name);

  // int64 step_id = 1;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 allocation_id = 3;
  void clear_allocation_id();
  ::PROTOBUF_NAMESPACE_ID::int64 allocation_id() const;
  void set_allocation_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool deferred = 5;
  void clear_deferred();
  bool deferred() const;
  void set_deferred(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogRawDeallocation)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr operation_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 allocation_id_;
  bool deferred_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MemoryLogStep

// int64 step_id = 1;
inline void MemoryLogStep::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryLogStep::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogStep.step_id)
  return step_id_;
}
inline void MemoryLogStep::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogStep.step_id)
}

// string handle = 2;
inline void MemoryLogStep::clear_handle() {
  handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MemoryLogStep::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogStep.handle)
  return handle_.Get();
}
inline void MemoryLogStep::set_handle(const std::string& value) {
  
  handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogStep.handle)
}
inline void MemoryLogStep::set_handle(std::string&& value) {
  
  handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MemoryLogStep.handle)
}
inline void MemoryLogStep::set_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MemoryLogStep.handle)
}
inline void MemoryLogStep::set_handle(const char* value,
    size_t size) {
  
  handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MemoryLogStep.handle)
}
inline std::string* MemoryLogStep::mutable_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogStep.handle)
  return handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MemoryLogStep::release_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogStep.handle)
  
  return handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MemoryLogStep::set_allocated_handle(std::string* handle) {
  if (handle != nullptr) {
    
  } else {
    
  }
  handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogStep.handle)
}
inline std::string* MemoryLogStep::unsafe_arena_release_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MemoryLogStep.handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MemoryLogStep::unsafe_arena_set_allocated_handle(
    std::string* handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (handle != nullptr) {
    
  } else {
    
  }
  handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogStep.handle)
}

// -------------------------------------------------------------------

// MemoryLogTensorAllocation

// int64 step_id = 1;
inline void MemoryLogTensorAllocation::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryLogTensorAllocation::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorAllocation.step_id)
  return step_id_;
}
inline void MemoryLogTensorAllocation::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorAllocation.step_id)
}

// string kernel_name = 2;
inline void MemoryLogTensorAllocation::clear_kernel_name() {
  kernel_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MemoryLogTensorAllocation::kernel_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorAllocation.kernel_name)
  return kernel_name_.Get();
}
inline void MemoryLogTensorAllocation::set_kernel_name(const std::string& value) {
  
  kernel_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorAllocation.kernel_name)
}
inline void MemoryLogTensorAllocation::set_kernel_name(std::string&& value) {
  
  kernel_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MemoryLogTensorAllocation.kernel_name)
}
inline void MemoryLogTensorAllocation::set_kernel_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  kernel_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MemoryLogTensorAllocation.kernel_name)
}
inline void MemoryLogTensorAllocation::set_kernel_name(const char* value,
    size_t size) {
  
  kernel_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MemoryLogTensorAllocation.kernel_name)
}
inline std::string* MemoryLogTensorAllocation::mutable_kernel_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogTensorAllocation.kernel_name)
  return kernel_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MemoryLogTensorAllocation::release_kernel_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogTensorAllocation.kernel_name)
  
  return kernel_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MemoryLogTensorAllocation::set_allocated_kernel_name(std::string* kernel_name) {
  if (kernel_name != nullptr) {
    
  } else {
    
  }
  kernel_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), kernel_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogTensorAllocation.kernel_name)
}
inline std::string* MemoryLogTensorAllocation::unsafe_arena_release_kernel_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MemoryLogTensorAllocation.kernel_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return kernel_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MemoryLogTensorAllocation::unsafe_arena_set_allocated_kernel_name(
    std::string* kernel_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (kernel_name != nullptr) {
    
  } else {
    
  }
  kernel_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      kernel_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogTensorAllocation.kernel_name)
}

// .tensorflow.TensorDescription tensor = 3;
inline bool MemoryLogTensorAllocation::has_tensor() const {
  return this != internal_default_instance() && tensor_ != nullptr;
}
inline const ::tensorflow::TensorDescription& MemoryLogTensorAllocation::tensor() const {
  const ::tensorflow::TensorDescription* p = tensor_;
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorAllocation.tensor)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorDescription*>(
      &::tensorflow::_TensorDescription_default_instance_);
}
inline ::tensorflow::TensorDescription* MemoryLogTensorAllocation::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogTensorAllocation.tensor)
  
  ::tensorflow::TensorDescription* temp = tensor_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorDescription* MemoryLogTensorAllocation::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MemoryLogTensorAllocation.tensor)
  
  ::tensorflow::TensorDescription* temp = tensor_;
  tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorDescription* MemoryLogTensorAllocation::mutable_tensor() {
  
  if (tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorDescription>(GetArenaNoVirtual());
    tensor_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogTensorAllocation.tensor)
  return tensor_;
}
inline void MemoryLogTensorAllocation::set_allocated_tensor(::tensorflow::TensorDescription* tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_);
  }
  if (tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor)->GetArena();
    if (message_arena != submessage_arena) {
      tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogTensorAllocation.tensor)
}

// -------------------------------------------------------------------

// MemoryLogTensorDeallocation

// int64 allocation_id = 1;
inline void MemoryLogTensorDeallocation::clear_allocation_id() {
  allocation_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryLogTensorDeallocation::allocation_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorDeallocation.allocation_id)
  return allocation_id_;
}
inline void MemoryLogTensorDeallocation::set_allocation_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  allocation_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorDeallocation.allocation_id)
}

// string allocator_name = 2;
inline void MemoryLogTensorDeallocation::clear_allocator_name() {
  allocator_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MemoryLogTensorDeallocation::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorDeallocation.allocator_name)
  return allocator_name_.Get();
}
inline void MemoryLogTensorDeallocation::set_allocator_name(const std::string& value) {
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorDeallocation.allocator_name)
}
inline void MemoryLogTensorDeallocation::set_allocator_name(std::string&& value) {
  
  allocator_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MemoryLogTensorDeallocation.allocator_name)
}
inline void MemoryLogTensorDeallocation::set_allocator_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MemoryLogTensorDeallocation.allocator_name)
}
inline void MemoryLogTensorDeallocation::set_allocator_name(const char* value,
    size_t size) {
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MemoryLogTensorDeallocation.allocator_name)
}
inline std::string* MemoryLogTensorDeallocation::mutable_allocator_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogTensorDeallocation.allocator_name)
  return allocator_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MemoryLogTensorDeallocation::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogTensorDeallocation.allocator_name)
  
  return allocator_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MemoryLogTensorDeallocation::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  allocator_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), allocator_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogTensorDeallocation.allocator_name)
}
inline std::string* MemoryLogTensorDeallocation::unsafe_arena_release_allocator_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MemoryLogTensorDeallocation.allocator_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return allocator_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MemoryLogTensorDeallocation::unsafe_arena_set_allocated_allocator_name(
    std::string* allocator_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  allocator_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      allocator_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogTensorDeallocation.allocator_name)
}

// -------------------------------------------------------------------

// MemoryLogTensorOutput

// int64 step_id = 1;
inline void MemoryLogTensorOutput::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryLogTensorOutput::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorOutput.step_id)
  return step_id_;
}
inline void MemoryLogTensorOutput::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorOutput.step_id)
}

// string kernel_name = 2;
inline void MemoryLogTensorOutput::clear_kernel_name() {
  kernel_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MemoryLogTensorOutput::kernel_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorOutput.kernel_name)
  return kernel_name_.Get();
}
inline void MemoryLogTensorOutput::set_kernel_name(const std::string& value) {
  
  kernel_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorOutput.kernel_name)
}
inline void MemoryLogTensorOutput::set_kernel_name(std::string&& value) {
  
  kernel_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MemoryLogTensorOutput.kernel_name)
}
inline void MemoryLogTensorOutput::set_kernel_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  kernel_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MemoryLogTensorOutput.kernel_name)
}
inline void MemoryLogTensorOutput::set_kernel_name(const char* value,
    size_t size) {
  
  kernel_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MemoryLogTensorOutput.kernel_name)
}
inline std::string* MemoryLogTensorOutput::mutable_kernel_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogTensorOutput.kernel_name)
  return kernel_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MemoryLogTensorOutput::release_kernel_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogTensorOutput.kernel_name)
  
  return kernel_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MemoryLogTensorOutput::set_allocated_kernel_name(std::string* kernel_name) {
  if (kernel_name != nullptr) {
    
  } else {
    
  }
  kernel_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), kernel_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogTensorOutput.kernel_name)
}
inline std::string* MemoryLogTensorOutput::unsafe_arena_release_kernel_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MemoryLogTensorOutput.kernel_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return kernel_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MemoryLogTensorOutput::unsafe_arena_set_allocated_kernel_name(
    std::string* kernel_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (kernel_name != nullptr) {
    
  } else {
    
  }
  kernel_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      kernel_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogTensorOutput.kernel_name)
}

// int32 index = 3;
inline void MemoryLogTensorOutput::clear_index() {
  index_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MemoryLogTensorOutput::index() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorOutput.index)
  return index_;
}
inline void MemoryLogTensorOutput::set_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  index_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorOutput.index)
}

// .tensorflow.TensorDescription tensor = 4;
inline bool MemoryLogTensorOutput::has_tensor() const {
  return this != internal_default_instance() && tensor_ != nullptr;
}
inline const ::tensorflow::TensorDescription& MemoryLogTensorOutput::tensor() const {
  const ::tensorflow::TensorDescription* p = tensor_;
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorOutput.tensor)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorDescription*>(
      &::tensorflow::_TensorDescription_default_instance_);
}
inline ::tensorflow::TensorDescription* MemoryLogTensorOutput::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogTensorOutput.tensor)
  
  ::tensorflow::TensorDescription* temp = tensor_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorDescription* MemoryLogTensorOutput::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MemoryLogTensorOutput.tensor)
  
  ::tensorflow::TensorDescription* temp = tensor_;
  tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorDescription* MemoryLogTensorOutput::mutable_tensor() {
  
  if (tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorDescription>(GetArenaNoVirtual());
    tensor_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogTensorOutput.tensor)
  return tensor_;
}
inline void MemoryLogTensorOutput::set_allocated_tensor(::tensorflow::TensorDescription* tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_);
  }
  if (tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor)->GetArena();
    if (message_arena != submessage_arena) {
      tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogTensorOutput.tensor)
}

// -------------------------------------------------------------------

// MemoryLogRawAllocation

// int64 step_id = 1;
inline void MemoryLogRawAllocation::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryLogRawAllocation::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.step_id)
  return step_id_;
}
inline void MemoryLogRawAllocation::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.step_id)
}

// string operation = 2;
inline void MemoryLogRawAllocation::clear_operation() {
  operation_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MemoryLogRawAllocation::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.operation)
  return operation_.Get();
}
inline void MemoryLogRawAllocation::set_operation(const std::string& value) {
  
  operation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.operation)
}
inline void MemoryLogRawAllocation::set_operation(std::string&& value) {
  
  operation_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MemoryLogRawAllocation.operation)
}
inline void MemoryLogRawAllocation::set_operation(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  operation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MemoryLogRawAllocation.operation)
}
inline void MemoryLogRawAllocation::set_operation(const char* value,
    size_t size) {
  
  operation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MemoryLogRawAllocation.operation)
}
inline std::string* MemoryLogRawAllocation::mutable_operation() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogRawAllocation.operation)
  return operation_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MemoryLogRawAllocation::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogRawAllocation.operation)
  
  return operation_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MemoryLogRawAllocation::set_allocated_operation(std::string* operation) {
  if (operation != nullptr) {
    
  } else {
    
  }
  operation_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), operation,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogRawAllocation.operation)
}
inline std::string* MemoryLogRawAllocation::unsafe_arena_release_operation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MemoryLogRawAllocation.operation)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return operation_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MemoryLogRawAllocation::unsafe_arena_set_allocated_operation(
    std::string* operation) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (operation != nullptr) {
    
  } else {
    
  }
  operation_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      operation, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogRawAllocation.operation)
}

// int64 num_bytes = 3;
inline void MemoryLogRawAllocation::clear_num_bytes() {
  num_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryLogRawAllocation::num_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.num_bytes)
  return num_bytes_;
}
inline void MemoryLogRawAllocation::set_num_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.num_bytes)
}

// uint64 ptr = 4;
inline void MemoryLogRawAllocation::clear_ptr() {
  ptr_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 MemoryLogRawAllocation::ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.ptr)
  return ptr_;
}
inline void MemoryLogRawAllocation::set_ptr(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  ptr_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.ptr)
}

// int64 allocation_id = 5;
inline void MemoryLogRawAllocation::clear_allocation_id() {
  allocation_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryLogRawAllocation::allocation_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.allocation_id)
  return allocation_id_;
}
inline void MemoryLogRawAllocation::set_allocation_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  allocation_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.allocation_id)
}

// string allocator_name = 6;
inline void MemoryLogRawAllocation::clear_allocator_name() {
  allocator_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MemoryLogRawAllocation::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.allocator_name)
  return allocator_name_.Get();
}
inline void MemoryLogRawAllocation::set_allocator_name(const std::string& value) {
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.allocator_name)
}
inline void MemoryLogRawAllocation::set_allocator_name(std::string&& value) {
  
  allocator_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MemoryLogRawAllocation.allocator_name)
}
inline void MemoryLogRawAllocation::set_allocator_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MemoryLogRawAllocation.allocator_name)
}
inline void MemoryLogRawAllocation::set_allocator_name(const char* value,
    size_t size) {
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MemoryLogRawAllocation.allocator_name)
}
inline std::string* MemoryLogRawAllocation::mutable_allocator_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogRawAllocation.allocator_name)
  return allocator_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MemoryLogRawAllocation::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogRawAllocation.allocator_name)
  
  return allocator_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MemoryLogRawAllocation::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  allocator_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), allocator_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogRawAllocation.allocator_name)
}
inline std::string* MemoryLogRawAllocation::unsafe_arena_release_allocator_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MemoryLogRawAllocation.allocator_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return allocator_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MemoryLogRawAllocation::unsafe_arena_set_allocated_allocator_name(
    std::string* allocator_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  allocator_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      allocator_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogRawAllocation.allocator_name)
}

// -------------------------------------------------------------------

// MemoryLogRawDeallocation

// int64 step_id = 1;
inline void MemoryLogRawDeallocation::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryLogRawDeallocation::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawDeallocation.step_id)
  return step_id_;
}
inline void MemoryLogRawDeallocation::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawDeallocation.step_id)
}

// string operation = 2;
inline void MemoryLogRawDeallocation::clear_operation() {
  operation_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MemoryLogRawDeallocation::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawDeallocation.operation)
  return operation_.Get();
}
inline void MemoryLogRawDeallocation::set_operation(const std::string& value) {
  
  operation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawDeallocation.operation)
}
inline void MemoryLogRawDeallocation::set_operation(std::string&& value) {
  
  operation_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MemoryLogRawDeallocation.operation)
}
inline void MemoryLogRawDeallocation::set_operation(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  operation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MemoryLogRawDeallocation.operation)
}
inline void MemoryLogRawDeallocation::set_operation(const char* value,
    size_t size) {
  
  operation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MemoryLogRawDeallocation.operation)
}
inline std::string* MemoryLogRawDeallocation::mutable_operation() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogRawDeallocation.operation)
  return operation_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MemoryLogRawDeallocation::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogRawDeallocation.operation)
  
  return operation_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MemoryLogRawDeallocation::set_allocated_operation(std::string* operation) {
  if (operation != nullptr) {
    
  } else {
    
  }
  operation_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), operation,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogRawDeallocation.operation)
}
inline std::string* MemoryLogRawDeallocation::unsafe_arena_release_operation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MemoryLogRawDeallocation.operation)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return operation_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MemoryLogRawDeallocation::unsafe_arena_set_allocated_operation(
    std::string* operation) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (operation != nullptr) {
    
  } else {
    
  }
  operation_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      operation, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogRawDeallocation.operation)
}

// int64 allocation_id = 3;
inline void MemoryLogRawDeallocation::clear_allocation_id() {
  allocation_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryLogRawDeallocation::allocation_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawDeallocation.allocation_id)
  return allocation_id_;
}
inline void MemoryLogRawDeallocation::set_allocation_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  allocation_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawDeallocation.allocation_id)
}

// string allocator_name = 4;
inline void MemoryLogRawDeallocation::clear_allocator_name() {
  allocator_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MemoryLogRawDeallocation::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawDeallocation.allocator_name)
  return allocator_name_.Get();
}
inline void MemoryLogRawDeallocation::set_allocator_name(const std::string& value) {
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawDeallocation.allocator_name)
}
inline void MemoryLogRawDeallocation::set_allocator_name(std::string&& value) {
  
  allocator_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MemoryLogRawDeallocation.allocator_name)
}
inline void MemoryLogRawDeallocation::set_allocator_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MemoryLogRawDeallocation.allocator_name)
}
inline void MemoryLogRawDeallocation::set_allocator_name(const char* value,
    size_t size) {
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MemoryLogRawDeallocation.allocator_name)
}
inline std::string* MemoryLogRawDeallocation::mutable_allocator_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogRawDeallocation.allocator_name)
  return allocator_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MemoryLogRawDeallocation::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogRawDeallocation.allocator_name)
  
  return allocator_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MemoryLogRawDeallocation::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  allocator_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), allocator_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogRawDeallocation.allocator_name)
}
inline std::string* MemoryLogRawDeallocation::unsafe_arena_release_allocator_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MemoryLogRawDeallocation.allocator_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return allocator_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MemoryLogRawDeallocation::unsafe_arena_set_allocated_allocator_name(
    std::string* allocator_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  allocator_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      allocator_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogRawDeallocation.allocator_name)
}

// bool deferred = 5;
inline void MemoryLogRawDeallocation::clear_deferred() {
  deferred_ = false;
}
inline bool MemoryLogRawDeallocation::deferred() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawDeallocation.deferred)
  return deferred_;
}
inline void MemoryLogRawDeallocation::set_deferred(bool value) {
  
  deferred_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawDeallocation.deferred)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto
