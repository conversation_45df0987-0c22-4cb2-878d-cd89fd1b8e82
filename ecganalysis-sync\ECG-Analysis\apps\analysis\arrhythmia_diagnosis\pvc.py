import traceback

import numpy as np
from scipy.signal import find_peaks
from scipy.spatial.distance import euclidean
from fastdtw import fastdtw

from apps.utils.logger_helper import Logger


def process(signal_segment, sampling_rate, waveform_info):
    """
    心电信号PVC检测
    :param signal_segment: 经过 gain/zero 调整并切割后的有效信号段
    :param waveform_info: *原始信号*计算出的完整波形信息 (来自 get_waveform)
    :param sampling_rate: 采样率
    :return: True or False
    """
    try:
        waveform = waveform_info.get('waveform', {})
        if not waveform:
            return False

        rr_intervals = waveform['rr_intervals']
        if len(rr_intervals) < 3:
            return False

        q_indices_hamilton = waveform['q_peaks']
        s_indices_hamilton = waveform['s_peaks']
        rpeaks = waveform['r_peaks']
        p_positions = waveform['p_peaks']

        median_rr = np.median(rr_intervals)
        window_size = int(0.2 * sampling_rate)

        qrs_widths = []
        qrs_widths_adaptive = []

        for i in range(len(rpeaks)):
            q_idx = -1
            s_idx = -1
            if i < len(q_indices_hamilton):
                q_idx = q_indices_hamilton[i]
            if i < len(s_indices_hamilton):
                s_idx = s_indices_hamilton[i]

            if q_idx != -1 and s_idx != -1 and s_idx > q_idx:
                qrs_width = (s_idx - q_idx) / sampling_rate
                if 0.05 < qrs_width < 0.2:
                    qrs_widths.append(qrs_width)

        for i in range(len(rpeaks)):
            if rpeaks[i] - window_size < 0 or rpeaks[i] + window_size >= len(signal_segment):
                continue

            beat_window = signal_segment[rpeaks[i] - window_size:rpeaks[i] + window_size]

            energy = np.square(beat_window)
            threshold = 0.2 * np.max(energy)
            qrs_start = rpeaks[i] - window_size
            qrs_end = rpeaks[i] + window_size - 1

            for j in range(len(energy) // 2, 0, -1):
                if energy[j] < threshold:
                    qrs_start = rpeaks[i] - window_size + j
                    break

            for j in range(len(energy) // 2, len(energy) - 1):
                if energy[j] < threshold:
                    qrs_end = rpeaks[i] - window_size + j
                    break

            qrs_width_adaptive = (qrs_end - qrs_start) / sampling_rate
            if 0.05 < qrs_width_adaptive < 0.2:
                qrs_widths_adaptive.append(qrs_width_adaptive)

        if qrs_widths_adaptive:
            normal_qrs_width = np.median(qrs_widths_adaptive)
        elif qrs_widths:
            normal_qrs_width = np.median(qrs_widths)
        else:
            normal_qrs_width = 0.08

        qrs_width_threshold = max(0.12, normal_qrs_width * 1.5)

        pvc_count = 0
        pvc_beats = []
        borderline_pvc_beats = []
        templates = []

        for i in range(len(rpeaks)):
            if i > 0 and i < len(rr_intervals) and rpeaks[i] - window_size >= 0 and rpeaks[i] + window_size < len(
                    signal_segment):
                if 0.9 * median_rr <= rr_intervals[i - 1] <= 1.1 * median_rr:  # 正常RR间期
                    beat = signal_segment[rpeaks[i] - window_size:rpeaks[i] + window_size]
                    if len(beat) == 2 * window_size:
                        templates.append(beat)

                        # 只收集最多10个模板
                        if len(templates) >= 10:
                            break


        if len(templates) < 3:
            

            all_beats = []
            for i in range(len(rpeaks)):
                if rpeaks[i] - window_size >= 0 and rpeaks[i] + window_size < len(signal_segment):
                    beat = signal_segment[rpeaks[i] - window_size:rpeaks[i] + window_size]
                    if len(beat) == 2 * window_size:
                        all_beats.append(beat)


            if len(all_beats) >= 5:
                all_beats_array = np.array(all_beats)

                median_template = np.median(all_beats_array, axis=0)
                templates = [median_template]
        def compare_with_dtw(beat, templates):
            min_distance = float('inf')
            for template in templates:
                if len(template) == len(beat):
                    try:
                        # 确保输入是一维数组
                        beat_1d = np.ravel(beat)
                        template_1d = np.ravel(template)
                        distance, _ = fastdtw(beat_1d, template_1d, dist=euclidean)
                        distance = distance / len(beat)  # 归一化距离
                        min_distance = min(min_distance, distance)
                    except Exception as e:
                        # 使用相关性分析作为备选方法，不输出警告
                        try:
                            corr = np.corrcoef(beat, template)[0, 1]
                            min_distance = min(min_distance, 1.0 - corr)
                        except:
                            pass
            return min_distance

        for i in range(1, len(rr_intervals) - 1):
            if rpeaks[i] - window_size < 0 or rpeaks[i] + window_size >= len(signal_segment):
                continue
            current_qrs = signal_segment[rpeaks[i] - window_size:rpeaks[i] + window_size]
            if len(current_qrs) != 2 * window_size:
                continue

            current_rr = rr_intervals[i]
            next_rr = rr_intervals[i + 1]

            is_premature = current_rr < 0.85 * median_rr
            has_compensation = next_rr > 1.15 * median_rr

            is_wide_qrs = False

            qrs_width_traditional = -1
            q_idx_curr = -1
            s_idx_curr = -1
            if i < len(q_indices_hamilton):
                q_idx_curr = q_indices_hamilton[i]
            if i < len(s_indices_hamilton):
                s_idx_curr = s_indices_hamilton[i]

            if q_idx_curr != -1 and s_idx_curr != -1 and s_idx_curr > q_idx_curr:
                qrs_width_traditional = (s_idx_curr - q_idx_curr) / sampling_rate

            qrs_width_energy = -1
            try:
                energy = np.square(current_qrs)
                threshold = 0.2 * np.max(energy)

                start_idx = 0
                end_idx = len(energy) - 1

                for j in range(len(energy) // 2, 0, -1):
                    if energy[j] < threshold:
                        start_idx = j
                        break

                for j in range(len(energy) // 2, len(energy) - 1):
                    if energy[j] < threshold:
                        end_idx = j
                        break

                qrs_width_energy = (end_idx - start_idx) / sampling_rate
            except Exception as e:
                pass

            if qrs_width_energy > 0:
                qrs_width_current = qrs_width_energy
            elif qrs_width_traditional > 0:
                qrs_width_current = qrs_width_traditional
            else:
                qrs_width_current = -1

            is_wide_qrs = qrs_width_current > qrs_width_threshold if qrs_width_current > 0 else False

            p_wave_present = False
            p_wave_distance = float('inf')

            for p_pos in p_positions:
                pr_distance = rpeaks[i] - p_pos
                if 0.08 * sampling_rate < pr_distance < 0.2 * sampling_rate:  # P-R间期正常范围约为80-200ms
                    p_wave_present = True
                    p_wave_distance = min(p_wave_distance, pr_distance / sampling_rate)
                    break

            morphology_different = True
            morphology_distance = float('inf')

            if templates:
                morphology_distance = compare_with_dtw(current_qrs, templates)
                morphology_different = morphology_distance > 0.5  # 阈值可调整
            else:
                reference_beats = []
                for j in range(max(0, i - 5), min(len(rpeaks), i + 6)):
                    if j != i and j - 1 >= 0 and rr_intervals[j - 1] >= 0.9 * median_rr:
                        if rpeaks[j] - window_size >= 0 and rpeaks[j] + window_size < len(signal_segment):
                            beat = signal_segment[rpeaks[j] - window_size:rpeaks[j] + window_size]
                            if len(beat) == 2 * window_size:
                                reference_beats.append(beat)

                if reference_beats:
                    correlations = []
                    try:
                        for ref_beat in reference_beats:
                            if np.std(current_qrs) > 1e-6 and np.std(ref_beat) > 1e-6:
                                corr = np.corrcoef(current_qrs, ref_beat)[0, 1]
                                correlations.append(corr)
                            else:
                                correlations.append(1.0)
                        avg_correlation = np.mean(correlations) if correlations else 1.0
                        morphology_different = avg_correlation < 0.7
                    except Exception as e:
                        morphology_different = True

            current_amplitude = np.max(np.abs(current_qrs))
            reference_amplitudes = [np.max(np.abs(beat)) for beat in templates] if templates else []

            is_high_amplitude = False
            if reference_amplitudes:
                median_ref_amp = np.median(reference_amplitudes)
                if median_ref_amp > 1e-6:
                    is_high_amplitude = current_amplitude > 1.4 * median_ref_amp

            pvc_score = 0
            pac_score = 0

            # 时间特征
            if is_premature:
                pvc_score += 2.2
                pac_score += 2.0
            if has_compensation:
                pvc_score += 1.8
                pac_score += 0.4

            if is_wide_qrs:
                pvc_score += 3.5
            else:
                pac_score += 2.5
                if qrs_width_current > 0 and qrs_width_current < 0.08:
                    pac_score += 0.8

            if p_wave_present:
                pac_score += 3.5
                if p_wave_distance < 0.18:
                    pac_score += 1.0
            else:
                pvc_score += 3.0

            if morphology_different:
                pvc_score += 2.5
                if morphology_distance > 0.8:
                    pvc_score += 1.2
                elif morphology_distance > 0.6:
                    pvc_score += 0.7
            else:
                pac_score += 2.0

            if is_high_amplitude:
                pvc_score += 1.8
            else:
                pac_score += 0.5

            try:
                mid_point = len(current_qrs) // 2
                r_segment = current_qrs[mid_point - int(0.03 * sampling_rate):mid_point + int(0.03 * sampling_rate)]
                if len(r_segment) > 0:
                    max_deflection_idx = np.argmax(np.abs(r_segment))
                    max_deflection = r_segment[max_deflection_idx]

                    normal_deflections = []
                    for template in templates:
                        if len(template) > 0:
                            template_r = template[
                                         mid_point - int(0.03 * sampling_rate):mid_point + int(0.03 * sampling_rate)]
                            if len(template_r) > 0:
                                template_max_idx = np.argmax(np.abs(template_r))
                                normal_deflections.append(template_r[template_max_idx])

                    if normal_deflections:
                        median_normal = np.median(normal_deflections)
                        is_polarity_reversed = (np.sign(max_deflection) != np.sign(median_normal))
                        is_amplitude_different = abs(max_deflection) > 1.5 * abs(median_normal) or abs(
                            max_deflection) < 0.5 * abs(median_normal)


                        if is_polarity_reversed:
                            pvc_score += 2.0
                        if is_amplitude_different:
                            pvc_score += 1.0
            except Exception as e:
                pass

            try:
                t_start = mid_point + int(0.2 * sampling_rate)
                t_end = min(len(current_qrs), mid_point + int(0.35 * sampling_rate))

                if t_end > t_start + 10:
                    t_segment = current_qrs[t_start:t_end]

                    t_max_idx = np.argmax(t_segment)
                    t_min_idx = np.argmin(t_segment)
                    t_max_val = t_segment[t_max_idx]
                    t_min_val = t_segment[t_min_idx]

                    current_t_direction = 1 if t_max_idx < t_min_idx else -1

                    normal_t_directions = []
                    for template in templates:
                        if len(template) > t_end:
                            template_t = template[t_start:t_end]
                            t_template_max = np.argmax(template_t)
                            t_template_min = np.argmin(template_t)
                            direction = 1 if t_template_max < t_template_min else -1
                            normal_t_directions.append(direction)

                    if normal_t_directions:
                        normal_direction = 1 if sum(normal_t_directions) > 0 else -1

                        is_t_inverted = current_t_direction != normal_direction

                        if is_t_inverted:
                            pvc_score += 1.5  # T波反转是PVC的特征

                        t_amplitude = abs(t_max_val - t_min_val)

                        normal_t_amplitudes = []
                        for template in templates:
                            if len(template) > t_end:
                                template_t = template[t_start:t_end]
                                template_t_amp = abs(np.max(template_t) - np.min(template_t))
                                normal_t_amplitudes.append(template_t_amp)

                        if normal_t_amplitudes:
                            median_t_amp = np.median(normal_t_amplitudes)

                            if t_amplitude > 1.5 * median_t_amp:
                                pvc_score += 0.8
                            elif t_amplitude < 0.5 * median_t_amp:
                                pvc_score += 1.2
            except Exception as e:
                pass

            pvc_threshold = 5.2
            pac_advantage = 1.5

            heart_rate = 60.0 / median_rr if median_rr > 0 else 75

            if heart_rate > 100:
                pvc_threshold += 0.8
                pac_advantage += 0.3
            elif heart_rate < 60:
                pvc_threshold -= 0.3

            signal_quality_penalty = 0

            if qrs_width_current < 0:
                signal_quality_penalty += 0.8

            if not templates or len(templates) < 2:
                signal_quality_penalty += 0.6

            if p_wave_present and p_wave_distance > 0.22:
                signal_quality_penalty += 0.4

            pvc_threshold += min(signal_quality_penalty, 2.0)

            if is_premature and is_wide_qrs and morphology_different:
                pvc_threshold -= 0.8

            is_pvc = pvc_score > pac_score + pac_advantage and pvc_score >= pvc_threshold
            is_borderline_pvc = pvc_score > pac_score + 0.8 and pvc_score >= pvc_threshold - 1.0

            if is_pvc:
                pvc_count += 1
                pvc_beats.append((i, rpeaks[i], pvc_score, pac_score))
            elif is_borderline_pvc:
                borderline_pvc_beats.append((i, rpeaks[i], pvc_score, pac_score))

        # --- 连发PVC和特殊模式检测 ---
        has_coupled_pvc = False  # 二联律
        has_bigeminy = False  # 二段律
        has_trigeminy = False  # 三段律
        has_run_pvc = False  # 短阵PVC

        if len(pvc_beats) >= 2:
            pvc_indices = [b[0] for b in pvc_beats]

            consecutive_count = 0
            consecutive_runs = []
            current_run = []

            for i in range(len(pvc_indices)):
                if i == 0 or pvc_indices[i] == pvc_indices[i - 1] + 1:
                    consecutive_count += 1
                    current_run.append(pvc_indices[i])
                else:
                    if consecutive_count >= 2:
                        consecutive_runs.append(current_run.copy())
                    current_run = [pvc_indices[i]]
                    consecutive_count = 1

            if consecutive_count >= 2:
                consecutive_runs.append(current_run)

            if consecutive_runs:
                has_coupled_pvc = True
                if any(len(run) >= 3 for run in consecutive_runs):
                    has_run_pvc = True

            bigeminy_count = 0
            for i in range(len(rpeaks) - 2):
                if i in pvc_indices and i + 2 in pvc_indices and i + 1 not in pvc_indices:
                    bigeminy_count += 1

            if bigeminy_count >= 2:
                has_bigeminy = True

            trigeminy_count = 0
            for i in range(len(rpeaks) - 3):
                if i in pvc_indices and i + 3 in pvc_indices and i + 1 not in pvc_indices and i + 2 not in pvc_indices:
                    trigeminy_count += 1

            if trigeminy_count >= 2:
                has_trigeminy = True

        special_patterns_detected = has_coupled_pvc or has_bigeminy or has_trigeminy or has_run_pvc

        if special_patterns_detected:
            special_pattern_info = []
            if has_coupled_pvc: special_pattern_info.append("连发PVC")
            if has_bigeminy: special_pattern_info.append("二段律")
            if has_trigeminy: special_pattern_info.append("三段律")
            if has_run_pvc: special_pattern_info.append("短阵PVC")

            if pvc_count >= 1:
                return True

        has_definite_pvc = pvc_count > 0
        has_borderline_pvc = len(borderline_pvc_beats) > 0
        has_pattern_evidence = has_coupled_pvc or has_bigeminy or has_trigeminy

        if not has_definite_pvc and has_borderline_pvc:
            strong_features_count = 0
            for _, _, score, _ in borderline_pvc_beats:
                if score > pvc_threshold - 0.5:
                    strong_features_count += 1

            if strong_features_count >= 2:
                return True

            if has_pattern_evidence and strong_features_count >= 1:
                return True

        if has_definite_pvc or (has_pattern_evidence and has_borderline_pvc):
            return True
        else:
            return False
    except Exception as e:
        return False
