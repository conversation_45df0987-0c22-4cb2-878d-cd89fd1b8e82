// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/device_attributes.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto;
namespace tensorflow {
class DeviceAttributes;
class DeviceAttributesDefaultTypeInternal;
extern DeviceAttributesDefaultTypeInternal _DeviceAttributes_default_instance_;
class DeviceLocality;
class DeviceLocalityDefaultTypeInternal;
extern DeviceLocalityDefaultTypeInternal _DeviceLocality_default_instance_;
class InterconnectLink;
class InterconnectLinkDefaultTypeInternal;
extern InterconnectLinkDefaultTypeInternal _InterconnectLink_default_instance_;
class LocalLinks;
class LocalLinksDefaultTypeInternal;
extern LocalLinksDefaultTypeInternal _LocalLinks_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::DeviceAttributes* Arena::CreateMaybeMessage<::tensorflow::DeviceAttributes>(Arena*);
template<> ::tensorflow::DeviceLocality* Arena::CreateMaybeMessage<::tensorflow::DeviceLocality>(Arena*);
template<> ::tensorflow::InterconnectLink* Arena::CreateMaybeMessage<::tensorflow::InterconnectLink>(Arena*);
template<> ::tensorflow::LocalLinks* Arena::CreateMaybeMessage<::tensorflow::LocalLinks>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class InterconnectLink :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.InterconnectLink) */ {
 public:
  InterconnectLink();
  virtual ~InterconnectLink();

  InterconnectLink(const InterconnectLink& from);
  InterconnectLink(InterconnectLink&& from) noexcept
    : InterconnectLink() {
    *this = ::std::move(from);
  }

  inline InterconnectLink& operator=(const InterconnectLink& from) {
    CopyFrom(from);
    return *this;
  }
  inline InterconnectLink& operator=(InterconnectLink&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const InterconnectLink& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const InterconnectLink* internal_default_instance() {
    return reinterpret_cast<const InterconnectLink*>(
               &_InterconnectLink_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(InterconnectLink& a, InterconnectLink& b) {
    a.Swap(&b);
  }
  inline void Swap(InterconnectLink* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(InterconnectLink* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline InterconnectLink* New() const final {
    return CreateMaybeMessage<InterconnectLink>(nullptr);
  }

  InterconnectLink* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<InterconnectLink>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const InterconnectLink& from);
  void MergeFrom(const InterconnectLink& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(InterconnectLink* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.InterconnectLink";
  }
  protected:
  explicit InterconnectLink(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypeFieldNumber = 2,
    kDeviceIdFieldNumber = 1,
    kStrengthFieldNumber = 3,
  };
  // string type = 2;
  void clear_type();
  const std::string& type() const;
  void set_type(const std::string& value);
  void set_type(std::string&& value);
  void set_type(const char* value);
  void set_type(const char* value, size_t size);
  std::string* mutable_type();
  std::string* release_type();
  void set_allocated_type(std::string* type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type(
      std::string* type);

  // int32 device_id = 1;
  void clear_device_id();
  ::PROTOBUF_NAMESPACE_ID::int32 device_id() const;
  void set_device_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 strength = 3;
  void clear_strength();
  ::PROTOBUF_NAMESPACE_ID::int32 strength() const;
  void set_strength(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.InterconnectLink)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  ::PROTOBUF_NAMESPACE_ID::int32 device_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 strength_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto;
};
// -------------------------------------------------------------------

class LocalLinks :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LocalLinks) */ {
 public:
  LocalLinks();
  virtual ~LocalLinks();

  LocalLinks(const LocalLinks& from);
  LocalLinks(LocalLinks&& from) noexcept
    : LocalLinks() {
    *this = ::std::move(from);
  }

  inline LocalLinks& operator=(const LocalLinks& from) {
    CopyFrom(from);
    return *this;
  }
  inline LocalLinks& operator=(LocalLinks&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LocalLinks& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LocalLinks* internal_default_instance() {
    return reinterpret_cast<const LocalLinks*>(
               &_LocalLinks_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(LocalLinks& a, LocalLinks& b) {
    a.Swap(&b);
  }
  inline void Swap(LocalLinks* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LocalLinks* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LocalLinks* New() const final {
    return CreateMaybeMessage<LocalLinks>(nullptr);
  }

  LocalLinks* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LocalLinks>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LocalLinks& from);
  void MergeFrom(const LocalLinks& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LocalLinks* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LocalLinks";
  }
  protected:
  explicit LocalLinks(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLinkFieldNumber = 1,
  };
  // repeated .tensorflow.InterconnectLink link = 1;
  int link_size() const;
  void clear_link();
  ::tensorflow::InterconnectLink* mutable_link(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::InterconnectLink >*
      mutable_link();
  const ::tensorflow::InterconnectLink& link(int index) const;
  ::tensorflow::InterconnectLink* add_link();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::InterconnectLink >&
      link() const;

  // @@protoc_insertion_point(class_scope:tensorflow.LocalLinks)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::InterconnectLink > link_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto;
};
// -------------------------------------------------------------------

class DeviceLocality :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceLocality) */ {
 public:
  DeviceLocality();
  virtual ~DeviceLocality();

  DeviceLocality(const DeviceLocality& from);
  DeviceLocality(DeviceLocality&& from) noexcept
    : DeviceLocality() {
    *this = ::std::move(from);
  }

  inline DeviceLocality& operator=(const DeviceLocality& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceLocality& operator=(DeviceLocality&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeviceLocality& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceLocality* internal_default_instance() {
    return reinterpret_cast<const DeviceLocality*>(
               &_DeviceLocality_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DeviceLocality& a, DeviceLocality& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceLocality* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceLocality* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeviceLocality* New() const final {
    return CreateMaybeMessage<DeviceLocality>(nullptr);
  }

  DeviceLocality* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeviceLocality>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeviceLocality& from);
  void MergeFrom(const DeviceLocality& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceLocality* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeviceLocality";
  }
  protected:
  explicit DeviceLocality(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLinksFieldNumber = 3,
    kBusIdFieldNumber = 1,
    kNumaNodeFieldNumber = 2,
  };
  // .tensorflow.LocalLinks links = 3;
  bool has_links() const;
  void clear_links();
  const ::tensorflow::LocalLinks& links() const;
  ::tensorflow::LocalLinks* release_links();
  ::tensorflow::LocalLinks* mutable_links();
  void set_allocated_links(::tensorflow::LocalLinks* links);
  void unsafe_arena_set_allocated_links(
      ::tensorflow::LocalLinks* links);
  ::tensorflow::LocalLinks* unsafe_arena_release_links();

  // int32 bus_id = 1;
  void clear_bus_id();
  ::PROTOBUF_NAMESPACE_ID::int32 bus_id() const;
  void set_bus_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 numa_node = 2;
  void clear_numa_node();
  ::PROTOBUF_NAMESPACE_ID::int32 numa_node() const;
  void set_numa_node(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceLocality)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::LocalLinks* links_;
  ::PROTOBUF_NAMESPACE_ID::int32 bus_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 numa_node_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto;
};
// -------------------------------------------------------------------

class DeviceAttributes :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceAttributes) */ {
 public:
  DeviceAttributes();
  virtual ~DeviceAttributes();

  DeviceAttributes(const DeviceAttributes& from);
  DeviceAttributes(DeviceAttributes&& from) noexcept
    : DeviceAttributes() {
    *this = ::std::move(from);
  }

  inline DeviceAttributes& operator=(const DeviceAttributes& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceAttributes& operator=(DeviceAttributes&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeviceAttributes& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceAttributes* internal_default_instance() {
    return reinterpret_cast<const DeviceAttributes*>(
               &_DeviceAttributes_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DeviceAttributes& a, DeviceAttributes& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceAttributes* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceAttributes* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeviceAttributes* New() const final {
    return CreateMaybeMessage<DeviceAttributes>(nullptr);
  }

  DeviceAttributes* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeviceAttributes>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeviceAttributes& from);
  void MergeFrom(const DeviceAttributes& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceAttributes* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeviceAttributes";
  }
  protected:
  explicit DeviceAttributes(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kDeviceTypeFieldNumber = 2,
    kPhysicalDeviceDescFieldNumber = 7,
    kLocalityFieldNumber = 5,
    kMemoryLimitFieldNumber = 4,
    kIncarnationFieldNumber = 6,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string device_type = 2;
  void clear_device_type();
  const std::string& device_type() const;
  void set_device_type(const std::string& value);
  void set_device_type(std::string&& value);
  void set_device_type(const char* value);
  void set_device_type(const char* value, size_t size);
  std::string* mutable_device_type();
  std::string* release_device_type();
  void set_allocated_device_type(std::string* device_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_type(
      std::string* device_type);

  // string physical_device_desc = 7;
  void clear_physical_device_desc();
  const std::string& physical_device_desc() const;
  void set_physical_device_desc(const std::string& value);
  void set_physical_device_desc(std::string&& value);
  void set_physical_device_desc(const char* value);
  void set_physical_device_desc(const char* value, size_t size);
  std::string* mutable_physical_device_desc();
  std::string* release_physical_device_desc();
  void set_allocated_physical_device_desc(std::string* physical_device_desc);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_physical_device_desc();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_physical_device_desc(
      std::string* physical_device_desc);

  // .tensorflow.DeviceLocality locality = 5;
  bool has_locality() const;
  void clear_locality();
  const ::tensorflow::DeviceLocality& locality() const;
  ::tensorflow::DeviceLocality* release_locality();
  ::tensorflow::DeviceLocality* mutable_locality();
  void set_allocated_locality(::tensorflow::DeviceLocality* locality);
  void unsafe_arena_set_allocated_locality(
      ::tensorflow::DeviceLocality* locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_locality();

  // int64 memory_limit = 4;
  void clear_memory_limit();
  ::PROTOBUF_NAMESPACE_ID::int64 memory_limit() const;
  void set_memory_limit(::PROTOBUF_NAMESPACE_ID::int64 value);

  // fixed64 incarnation = 6;
  void clear_incarnation();
  ::PROTOBUF_NAMESPACE_ID::uint64 incarnation() const;
  void set_incarnation(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceAttributes)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr physical_device_desc_;
  ::tensorflow::DeviceLocality* locality_;
  ::PROTOBUF_NAMESPACE_ID::int64 memory_limit_;
  ::PROTOBUF_NAMESPACE_ID::uint64 incarnation_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// InterconnectLink

// int32 device_id = 1;
inline void InterconnectLink::clear_device_id() {
  device_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 InterconnectLink::device_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.InterconnectLink.device_id)
  return device_id_;
}
inline void InterconnectLink::set_device_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  device_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.InterconnectLink.device_id)
}

// string type = 2;
inline void InterconnectLink::clear_type() {
  type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& InterconnectLink::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.InterconnectLink.type)
  return type_.Get();
}
inline void InterconnectLink::set_type(const std::string& value) {
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.InterconnectLink.type)
}
inline void InterconnectLink::set_type(std::string&& value) {
  
  type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.InterconnectLink.type)
}
inline void InterconnectLink::set_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.InterconnectLink.type)
}
inline void InterconnectLink::set_type(const char* value,
    size_t size) {
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.InterconnectLink.type)
}
inline std::string* InterconnectLink::mutable_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.InterconnectLink.type)
  return type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* InterconnectLink::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.InterconnectLink.type)
  
  return type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void InterconnectLink::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.InterconnectLink.type)
}
inline std::string* InterconnectLink::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.InterconnectLink.type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void InterconnectLink::unsafe_arena_set_allocated_type(
    std::string* type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (type != nullptr) {
    
  } else {
    
  }
  type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.InterconnectLink.type)
}

// int32 strength = 3;
inline void InterconnectLink::clear_strength() {
  strength_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 InterconnectLink::strength() const {
  // @@protoc_insertion_point(field_get:tensorflow.InterconnectLink.strength)
  return strength_;
}
inline void InterconnectLink::set_strength(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  strength_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.InterconnectLink.strength)
}

// -------------------------------------------------------------------

// LocalLinks

// repeated .tensorflow.InterconnectLink link = 1;
inline int LocalLinks::link_size() const {
  return link_.size();
}
inline void LocalLinks::clear_link() {
  link_.Clear();
}
inline ::tensorflow::InterconnectLink* LocalLinks::mutable_link(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.LocalLinks.link)
  return link_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::InterconnectLink >*
LocalLinks::mutable_link() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.LocalLinks.link)
  return &link_;
}
inline const ::tensorflow::InterconnectLink& LocalLinks::link(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.LocalLinks.link)
  return link_.Get(index);
}
inline ::tensorflow::InterconnectLink* LocalLinks::add_link() {
  // @@protoc_insertion_point(field_add:tensorflow.LocalLinks.link)
  return link_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::InterconnectLink >&
LocalLinks::link() const {
  // @@protoc_insertion_point(field_list:tensorflow.LocalLinks.link)
  return link_;
}

// -------------------------------------------------------------------

// DeviceLocality

// int32 bus_id = 1;
inline void DeviceLocality::clear_bus_id() {
  bus_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DeviceLocality::bus_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceLocality.bus_id)
  return bus_id_;
}
inline void DeviceLocality::set_bus_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  bus_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceLocality.bus_id)
}

// int32 numa_node = 2;
inline void DeviceLocality::clear_numa_node() {
  numa_node_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DeviceLocality::numa_node() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceLocality.numa_node)
  return numa_node_;
}
inline void DeviceLocality::set_numa_node(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  numa_node_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceLocality.numa_node)
}

// .tensorflow.LocalLinks links = 3;
inline bool DeviceLocality::has_links() const {
  return this != internal_default_instance() && links_ != nullptr;
}
inline void DeviceLocality::clear_links() {
  if (GetArenaNoVirtual() == nullptr && links_ != nullptr) {
    delete links_;
  }
  links_ = nullptr;
}
inline const ::tensorflow::LocalLinks& DeviceLocality::links() const {
  const ::tensorflow::LocalLinks* p = links_;
  // @@protoc_insertion_point(field_get:tensorflow.DeviceLocality.links)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::LocalLinks*>(
      &::tensorflow::_LocalLinks_default_instance_);
}
inline ::tensorflow::LocalLinks* DeviceLocality::release_links() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceLocality.links)
  
  ::tensorflow::LocalLinks* temp = links_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  links_ = nullptr;
  return temp;
}
inline ::tensorflow::LocalLinks* DeviceLocality::unsafe_arena_release_links() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceLocality.links)
  
  ::tensorflow::LocalLinks* temp = links_;
  links_ = nullptr;
  return temp;
}
inline ::tensorflow::LocalLinks* DeviceLocality::mutable_links() {
  
  if (links_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::LocalLinks>(GetArenaNoVirtual());
    links_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceLocality.links)
  return links_;
}
inline void DeviceLocality::set_allocated_links(::tensorflow::LocalLinks* links) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete links_;
  }
  if (links) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(links);
    if (message_arena != submessage_arena) {
      links = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, links, submessage_arena);
    }
    
  } else {
    
  }
  links_ = links;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceLocality.links)
}

// -------------------------------------------------------------------

// DeviceAttributes

// string name = 1;
inline void DeviceAttributes::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DeviceAttributes::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.name)
  return name_.Get();
}
inline void DeviceAttributes::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.name)
}
inline void DeviceAttributes::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceAttributes.name)
}
inline void DeviceAttributes::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceAttributes.name)
}
inline void DeviceAttributes::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceAttributes.name)
}
inline std::string* DeviceAttributes::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DeviceAttributes::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceAttributes::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.name)
}
inline std::string* DeviceAttributes::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceAttributes.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceAttributes::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceAttributes.name)
}

// string device_type = 2;
inline void DeviceAttributes::clear_device_type() {
  device_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DeviceAttributes::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.device_type)
  return device_type_.Get();
}
inline void DeviceAttributes::set_device_type(const std::string& value) {
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.device_type)
}
inline void DeviceAttributes::set_device_type(std::string&& value) {
  
  device_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceAttributes.device_type)
}
inline void DeviceAttributes::set_device_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceAttributes.device_type)
}
inline void DeviceAttributes::set_device_type(const char* value,
    size_t size) {
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceAttributes.device_type)
}
inline std::string* DeviceAttributes::mutable_device_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.device_type)
  return device_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DeviceAttributes::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.device_type)
  
  return device_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceAttributes::set_allocated_device_type(std::string* device_type) {
  if (device_type != nullptr) {
    
  } else {
    
  }
  device_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.device_type)
}
inline std::string* DeviceAttributes::unsafe_arena_release_device_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceAttributes.device_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceAttributes::unsafe_arena_set_allocated_device_type(
    std::string* device_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device_type != nullptr) {
    
  } else {
    
  }
  device_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceAttributes.device_type)
}

// int64 memory_limit = 4;
inline void DeviceAttributes::clear_memory_limit() {
  memory_limit_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceAttributes::memory_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.memory_limit)
  return memory_limit_;
}
inline void DeviceAttributes::set_memory_limit(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  memory_limit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.memory_limit)
}

// .tensorflow.DeviceLocality locality = 5;
inline bool DeviceAttributes::has_locality() const {
  return this != internal_default_instance() && locality_ != nullptr;
}
inline void DeviceAttributes::clear_locality() {
  if (GetArenaNoVirtual() == nullptr && locality_ != nullptr) {
    delete locality_;
  }
  locality_ = nullptr;
}
inline const ::tensorflow::DeviceLocality& DeviceAttributes::locality() const {
  const ::tensorflow::DeviceLocality* p = locality_;
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.locality)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::DeviceLocality*>(
      &::tensorflow::_DeviceLocality_default_instance_);
}
inline ::tensorflow::DeviceLocality* DeviceAttributes::release_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.locality)
  
  ::tensorflow::DeviceLocality* temp = locality_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* DeviceAttributes::unsafe_arena_release_locality() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceAttributes.locality)
  
  ::tensorflow::DeviceLocality* temp = locality_;
  locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* DeviceAttributes::mutable_locality() {
  
  if (locality_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaNoVirtual());
    locality_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.locality)
  return locality_;
}
inline void DeviceAttributes::set_allocated_locality(::tensorflow::DeviceLocality* locality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete locality_;
  }
  if (locality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(locality);
    if (message_arena != submessage_arena) {
      locality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, locality, submessage_arena);
    }
    
  } else {
    
  }
  locality_ = locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.locality)
}

// fixed64 incarnation = 6;
inline void DeviceAttributes::clear_incarnation() {
  incarnation_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 DeviceAttributes::incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.incarnation)
  return incarnation_;
}
inline void DeviceAttributes::set_incarnation(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  incarnation_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.incarnation)
}

// string physical_device_desc = 7;
inline void DeviceAttributes::clear_physical_device_desc() {
  physical_device_desc_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DeviceAttributes::physical_device_desc() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.physical_device_desc)
  return physical_device_desc_.Get();
}
inline void DeviceAttributes::set_physical_device_desc(const std::string& value) {
  
  physical_device_desc_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.physical_device_desc)
}
inline void DeviceAttributes::set_physical_device_desc(std::string&& value) {
  
  physical_device_desc_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceAttributes.physical_device_desc)
}
inline void DeviceAttributes::set_physical_device_desc(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  physical_device_desc_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceAttributes.physical_device_desc)
}
inline void DeviceAttributes::set_physical_device_desc(const char* value,
    size_t size) {
  
  physical_device_desc_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceAttributes.physical_device_desc)
}
inline std::string* DeviceAttributes::mutable_physical_device_desc() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.physical_device_desc)
  return physical_device_desc_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DeviceAttributes::release_physical_device_desc() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.physical_device_desc)
  
  return physical_device_desc_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceAttributes::set_allocated_physical_device_desc(std::string* physical_device_desc) {
  if (physical_device_desc != nullptr) {
    
  } else {
    
  }
  physical_device_desc_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), physical_device_desc,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.physical_device_desc)
}
inline std::string* DeviceAttributes::unsafe_arena_release_physical_device_desc() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceAttributes.physical_device_desc)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return physical_device_desc_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceAttributes::unsafe_arena_set_allocated_physical_device_desc(
    std::string* physical_device_desc) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (physical_device_desc != nullptr) {
    
  } else {
    
  }
  physical_device_desc_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      physical_device_desc, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceAttributes.physical_device_desc)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
