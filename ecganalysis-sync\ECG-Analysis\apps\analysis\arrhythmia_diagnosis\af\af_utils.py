"""
房颤诊断工具函数
包含房颤检测和分析所需的特定信号处理函数
"""

import numpy as np
from scipy.stats import iqr

from apps.utils.logger_helper import Logger

def calculate_poincare_descriptors(rr_intervals):
    """
    计算庞加莱图的描述符 (SD1, SD2, Ratio).
    庞加莱图分析是评估心率变异性(HRV)的非线性方法。
    SD1: 反映短期变异性，与副交感神经活动相关。在房颤中通常显著增大。
    SD2: 反映长期变异性。
    Ratio(SD1/SD2): 描述椭圆形状，房颤时趋向于1。
    """
    if len(rr_intervals) < 2:
        # 数据点不足，返回中性/默认值
        return 0.0, 0.0, 1.0

    try:
        rr_n = np.array(rr_intervals[:-1])
        rr_n_plus_1 = np.array(rr_intervals[1:])

        # 计算连续RR间期的差值和总和
        diff_rr = rr_n_plus_1 - rr_n
        sum_rr = rr_n_plus_1 + rr_n

        # 计算SD1和SD2
        # SD1与差值的标准差成正比
        sd1 = np.sqrt(np.var(diff_rr) / 2)
        # SD2与总和的标准差成正比
        sd2 = np.sqrt(np.var(sum_rr) / 2)

        # 该比值是一个重要的指标
        ratio = sd1 / (sd2 + 1e-6)  # 添加极小值避免除以零

        return sd1, sd2, ratio
    except Exception as e:
        Logger().error(f"庞加莱图描述符计算错误: {str(e)}")
        return 0.0, 0.0, 1.0  # 发生错误时返回中性值

def analyze_rr_intervals(waveform_data, clear_cache=False):
    """
    分析RR间期特征，计算与房颤相关的各项统计指标
    直接使用 waveform_data 中预计算的基础RR统计量
    """
    try:
        rr_intervals = waveform_data.get('rr_intervals', [])
        
        if len(rr_intervals) < 5:
            return {
                'rr_mean': 0, 'rr_std': 0, 'rr_cv': 0, 'rr_iqr': 0,
                'rr_rmssd': 0, 'rr_irregularity': 0, 
                'rr_pct_above_50ms': 0, 'rr_autocorr': 0,
                'poincare_sd1': 0.0, 'poincare_sd2': 0.0, 'poincare_ratio': 1.0
            }

        # 使用 rr_intervals 作为缓存键的基础，与之前保持一致
        # 但确保 rr_intervals 是从 waveform_data 中获取的
        if len(rr_intervals) > 20:
            cache_key_tuple = tuple(rr_intervals[:20])
        else:
            cache_key_tuple = tuple(rr_intervals)
        
        # 考虑将其他可能影响结果的 waveform_data 字段加入缓存键，如果它们被使用
        # 例如，如果采样率等影响了这些指标的计算（虽然当前函数不直接使用）
        # 为简化，当前仍主要基于rr_intervals
        cache_key = cache_key_tuple 

        # 直接从 waveform_data 获取预计算的基础统计量
        rr_mean = waveform_data.get('rr_mean', np.mean(rr_intervals)) # Fallback if not provided
        rr_std = waveform_data.get('rr_std', np.std(rr_intervals))     # Fallback if not provided
        rr_cv = waveform_data.get('rr_cv', (rr_std / rr_mean if rr_mean > 0 else 0)) # Fallback
        rr_iqr = waveform_data.get('rr_iqr', iqr(rr_intervals))       # Fallback

        # 计算 waveform.py 未提供的额外指标
        rr_diff = np.diff(rr_intervals)
        if len(rr_diff) == 0: # 处理 rr_intervals 只有一个元素的情况
             return {
                'rr_mean': rr_mean, 'rr_std': 0, 'rr_cv': 0, 'rr_iqr': 0,
                'rr_rmssd': 0, 'rr_irregularity': 0,
                'rr_pct_above_50ms': 0, 'rr_autocorr': calculate_rr_autocorrelation(rr_intervals),
                'poincare_sd1': 0.0, 'poincare_sd2': 0.0, 'poincare_ratio': 1.0
            }

        rr_rmssd = np.sqrt(np.mean(np.square(rr_diff)))
        above_50ms = np.sum(np.abs(rr_diff) > 0.05)
        rr_pct_above_50ms = above_50ms / len(rr_diff) if len(rr_diff) > 0 else 0
        rr_irregularity = (rr_cv * rr_rmssd) / (rr_mean + 1e-10) if rr_mean > 0 else 0
        
        # 调用统一的自相关函数
        rr_autocorr = calculate_rr_autocorrelation(rr_intervals)

        # 新增：调用庞加莱图分析函数
        poincare_sd1, poincare_sd2, poincare_ratio = calculate_poincare_descriptors(rr_intervals)

        result = {
            'rr_mean': rr_mean,
            'rr_std': rr_std,
            'rr_cv': rr_cv,
            'rr_iqr': rr_iqr,
            'rr_rmssd': rr_rmssd,
            'rr_irregularity': rr_irregularity,
            'rr_pct_above_50ms': rr_pct_above_50ms,
            'rr_autocorr': rr_autocorr,
            'poincare_sd1': poincare_sd1,
            'poincare_sd2': poincare_sd2,
            'poincare_ratio': poincare_ratio
        }

        return result
    except Exception as e:
        Logger().error(f"RR间期分析错误: {str(e)}")
        return {
            'rr_mean': 0, 'rr_std': 0, 'rr_cv': 0, 'rr_iqr': 0,
            'rr_rmssd': 0, 'rr_irregularity': 0,
            'rr_pct_above_50ms': 0, 'rr_autocorr': 0,
            'poincare_sd1': 0.0, 'poincare_sd2': 0.0, 'poincare_ratio': 1.0
        }

def calculate_rr_autocorrelation(rr_intervals, lag=1):
    """
    计算RR间期的自相关性，用于区分房颤和窦性心律
    (从 af_detector.py 迁移并整合)
    """
    try:
        if len(rr_intervals) <= lag + 1:
            return 0.5  # 中性值，适用于短序列
            
        rr_mean = np.mean(rr_intervals)
        rr_std = np.std(rr_intervals)
        
        if rr_std < 1e-6: # 避免除以零或接近零的情况
            return 0.9  # 如果标准差极小（规则心律），则自相关性高
            
        rr_centered = rr_intervals - rr_mean
        
        n = len(rr_centered) - lag
        if n <= 0: # 理论上不应发生，如果 len(rr_intervals) > lag + 1
            return 0.5  
            
        # 使用 np.dot 进行高效计算
        numerator = np.dot(rr_centered[lag:], rr_centered[:n])
        
        # 分母是 n * variance (variance = std*std)
        denominator = rr_std * rr_std * n
        
        autocorr = numerator / denominator if denominator != 0 else 0.5 # 最后检查分母
        
        return autocorr
    except Exception as e:
        Logger().error(f"RR间期自相关计算错误: {str(e)}")
        return 0.5 # 出现错误时返回中性值

def calculate_af_score(features, weights):
    """
    计算房颤得分
    """
    try:
        # 计算加权得分
        score = 0.0
        for key, weight in weights.items():
            if key in features:
                score += features[key] * weight
                
        return score
    except Exception as e:
        Logger().error(f"房颤得分计算错误: {str(e)}")
        return 0.0

def combine_p_wave_detections(p_positions_cwt, p_positions_phasor, r_peaks, sampling_rate):
    """
    结合多种方法的P波检测结果
    """
    try:
        p_peaks_cwt = p_positions_cwt.get('P_positions', []) if p_positions_cwt else []
        p_peaks_phasor = p_positions_phasor.get('P_positions', []) if p_positions_phasor else []

        all_p_peaks = []
        all_p_peaks.extend(p_peaks_cwt)
        all_p_peaks.extend(p_peaks_phasor)

        if not all_p_peaks:
            return {
                'P_positions': [],
                'P_start_positions': [],
                'P_end_positions': []
            }

        all_p_peaks.sort()

        merged_p_peaks = []
        min_distance = int(0.05 * sampling_rate)  # 50ms
        
        i = 0
        while i < len(all_p_peaks):
            current_peak = all_p_peaks[i]
            merged_p_peaks.append(current_peak)

            j = i + 1
            while j < len(all_p_peaks) and all_p_peaks[j] - current_peak < min_distance:
                j += 1
            
            i = j

        valid_p_peaks = []
        valid_p_starts = []
        valid_p_ends = []

        pr_min = int(0.12 * sampling_rate)
        pr_max = int(0.22 * sampling_rate)
        p_duration = int(0.10 * sampling_rate)
        
        for p_peak in merged_p_peaks:

            nearest_r = find_nearest_r_peak(p_peak, r_peaks)
            
            if nearest_r is not None and pr_min <= nearest_r - p_peak <= pr_max:
                valid_p_peaks.append(p_peak)
                valid_p_starts.append(max(0, p_peak - p_duration // 2))
                valid_p_ends.append(min(p_peak + p_duration // 2, nearest_r - 1))
                
        return {
            'P_positions': valid_p_peaks,
            'P_start_positions': valid_p_starts,
            'P_end_positions': valid_p_ends
        }
    except Exception as e:
        Logger().error(f"P波合并错误: {str(e)}")
        return {
            'P_positions': [],
            'P_start_positions': [],
            'P_end_positions': []
        }

def find_nearest_r_peak(p_peak, r_peaks):
    """
    查找离P波最近的R峰
    """
    try:
        if not r_peaks or len(r_peaks) == 0:
            return None

        future_rs = [r for r in r_peaks if r > p_peak]
        
        if not future_rs:
            return None

        return future_rs[0]
    except Exception as e:
        Logger().error(f"查找最近R峰错误: {str(e)}")
        return None