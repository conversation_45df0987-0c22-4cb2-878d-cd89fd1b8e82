metadata:
  name: pth-shiyinzhang-iog
  namespace: cvat
  annotations:
    name: IOG
    version: 2
    type: interactor
    spec:
    min_pos_points: 1
    min_neg_points: 0
    startswith_box: true
    animated_gif: https://raw.githubusercontent.com/cvat-ai/cvat/0fbb19ae3846a017853d52e187f0ce149adced7d/site/content/en/images/iog_example.gif
    help_message: The interactor allows to get a mask of an object using its wrapping bounding box, positive, and negative points inside it

spec:
  description: Interactive Object Segmentation with Inside-Outside Guidance
  runtime: 'python:3.9'
  handler: main:handler
  eventTimeout: 30s
  env:
    - name: PYTHONPATH
      value: /opt/nuclio/iog

  build:
    image: cvat.pth.shiyinzhang.iog
    baseImage: continuumio/miniconda3

    directives:
      preCopy:
        - kind: WORKDIR
          value: /opt/nuclio
        - kind: RUN
          value: conda create -y -n iog python=3.9
        - kind: SHELL
          value: '["conda", "run", "-n", "iog", "/bin/bash", "-c"]'
        - kind: RUN
          value: conda install -y -c anaconda curl
        - kind: RUN
          value: conda install -y pytorch torchvision -c pytorch
        - kind: RUN
          value: apt update && apt install -y libgl1-mesa-glx
        - kind: RUN
          value: conda install -y -c conda-forge pycocotools scipy
        - kind: RUN
          value: pip install opencv-python gdown
        - kind: RUN
          value: git clone https://github.com/shiyinzhang/Inside-Outside-Guidance.git iog
        - kind: WORKDIR
          value: /opt/nuclio/iog
        - kind: ENV
          value: fileid=1Lm1hhMhhjjnNwO4Pf7SC6tXLayH2iH0l
        - kind: ENV
          value: filename=IOG_PASCAL_SBD.pth
        - kind: RUN
          value: gdown https://drive.google.com/uc?id=${fileid} -O ${filename}
        - kind: WORKDIR
          value: /opt/nuclio
        - kind: ENTRYPOINT
          value: '["conda", "run", "-n", "iog"]'

  triggers:
    myHttpTrigger:
      numWorkers: 2
      kind: 'http'
      workerAvailabilityTimeoutMilliseconds: 10000
      attributes:
        maxRequestBodySize: 33554432 # 32MB

  platform:
    attributes:
      restartPolicy:
        name: always
        maximumRetryCount: 3
      mountMode: volume
