from dataclasses import dataclass, field
from typing import List
from apps.models.base_model import BaseEntity


@dataclass
class HourlyStatisticsDetailModel(BaseEntity):
    hour: str = ""  # 小时
    hr_total_count: int = 0  # 心搏总数
    sdnn: float = 0.0  # sdnn
    hr_mean: float = 0.0  # 平均心率
    hr_max: float = 0.0  # 最快心率(次 / 分钟)
    hr_min: float = 0.0  # 最长持续(秒)
    pvc_single_count: int = 0  # 室性早搏单发个数
    pvc_pair_count: int = 0  # 室性早搏成对阵数
    pvc_run_count: int = 0  # 室性早搏连发阵数
    pvc_total_count: int = 0  # 室性早搏总个数
    svpc_single_count: int = 0  # 室上性早搏单发个数
    svpc_pair_count: int = 0  # 室上性早搏成对阵数
    svpc_run_count: int = 0  # 室上性早搏连发阵数
    svpc_total_count: int = 0  # 室上性早搏总个数
    sa_count: int = 0  # 停搏个数


@dataclass
class HourlyStatisticsModel(BaseEntity):
    date: str = ""  # 日期
    hourly_details: List[HourlyStatisticsDetailModel] = field(default_factory=list)  # 每小时的数据列表


@dataclass
class ReportModel(BaseEntity):
    record_date: str = ""  # 记录日期
    record_duration: str = ""  # 记录时长
    effective_duration: str = ""  # 有效时长
    analysis_time: str = ""  # 分析时间
    signal_quality: str = ""  # 信号质量

    # 概述
    hr_total_count: int = 0  # 心搏总数
    # 心率
    hr_mean: float = 0.0  # 平均心率
    nn_max: str = ""  # 最大心率
    nn_min: str = ""  # 最小心率
    hourly_mean_max: str = ""  # 每小时平均最大心率
    hourly_mean_min: str = ""  # 每小时平均最小心率
    longest_rr: str = ""  # 最长RR
    sa_count: int = 0  # 停搏次数
    # 房颤/房扑
    af_afl_total_episodes: int = 0  # 总阵数
    af_afl_total_duration: float = 0.0  # 总时长(秒)
    af_afl_total_rate: float = 0.0  # 总占时比（%）
    af_afl_hr_max: str = ""  # 最快心率(次 / 分钟)
    af_afl_longest_duration: str = ""  # 最长持续(秒)
    af_afl_longest_rr: str = ""  # 最长RR(秒)
    # 室性早搏与节律
    pvc_single_count: int = 0  # 单发个数
    pvc_pair_count: int = 0  # 成对阵数
    pvc_bigeminy_count: int = 0  # 二联律次数
    pvc_trigeminy_count: int = 0  # 三联律次数
    pvc_run_count: int = 0  # 连发阵数
    pvc_max_consecutive: int = 0  # 连发最多个数
    pvc_fastest_run_hr: int = 0  # 连发最快心率(次 / 分钟)
    pvc_slowest_run_hr: int = 0  # 连发最慢心率(次 / 分钟)
    pvc_hr_total_count: int = 0  # 心搏总数
    # 室上性早搏与节律
    svpc_single_count: int = 0  # 单发个数
    svpc_pair_count: int = 0  # 成对阵数
    svpc_bigeminy_count: int = 0  # 二联律次数
    svpc_trigeminy_count: int = 0  # 三联律次数
    svpc_run_count: int = 0  # 连发阵数
    svpc_max_consecutive: int = 0  # 连发最多个数
    svpc_fastest_run_hr: int = 0  # 连发最快心率(次 / 分钟)
    svpc_slowest_run_hr: int = 0  # 连发最慢心率(次 / 分钟)
    svpc_hr_total_count: int = 0  # 心搏总数
    # 心率变异
    nn_rr_rate: float = 0.0  # NN / RR 占时比（%）
    sdnn: float = 0.0
    sdann: float = 0.0
    sdnnidx: float = 0.0
    rmmsd: float = 0.0
    pnn20: float = 0.0
    pnn50: float = 0.0
    total: float = 0.0
    lf: float = 0.0
    hf: float = 0.0
    vlf: float = 0.0
    lf_hf: float = 0.0

    conclusion: str = ""  # 结论

    hourly_statistics_infos: List[HourlyStatisticsModel] = field(default_factory=list)  # 每小时统计数据集合