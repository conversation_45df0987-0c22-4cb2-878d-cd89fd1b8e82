__pycache__/pylab.cpython-39.pyc,,
matplotlib-3.2.2-py3.9-nspkg.pth,sha256=g9pwhlfLQRispACfr-Zaah4Psceyhyx9K_qv929IpMo,570
matplotlib-3.2.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
matplotlib-3.2.2.dist-info/METADATA,sha256=gDCqhcNDtqEbtwCx8YsUosvlLzH7QauSXKgd6MjhWxU,5170
matplotlib-3.2.2.dist-info/RECORD,,
matplotlib-3.2.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib-3.2.2.dist-info/WHEEL,sha256=e27QJz6-b0dCBkCPeIvg4jVbKrxvXp5iE3rJRU_m6NE,105
matplotlib-3.2.2.dist-info/namespace_packages.txt,sha256=A2PHFg9NKYOU4pEQ1h97U0Qd-rB-65W34XqC-56ZN9g,13
matplotlib-3.2.2.dist-info/top_level.txt,sha256=9tEw2ni8DdgX8CceoYHqSH1s50vrJ9SDfgtLIG8e3Y4,30
matplotlib/__init__.py,sha256=FLEIOOmlbJDBzqg4aq8W6w0wSpQF9tVwGu0VW8EwqsA,54615
matplotlib/__pycache__/__init__.cpython-39.pyc,,
matplotlib/__pycache__/_animation_data.cpython-39.pyc,,
matplotlib/__pycache__/_cm.cpython-39.pyc,,
matplotlib/__pycache__/_cm_listed.cpython-39.pyc,,
matplotlib/__pycache__/_color_data.cpython-39.pyc,,
matplotlib/__pycache__/_constrained_layout.cpython-39.pyc,,
matplotlib/__pycache__/_layoutbox.cpython-39.pyc,,
matplotlib/__pycache__/_mathtext_data.cpython-39.pyc,,
matplotlib/__pycache__/_pylab_helpers.cpython-39.pyc,,
matplotlib/__pycache__/_text_layout.cpython-39.pyc,,
matplotlib/__pycache__/_version.cpython-39.pyc,,
matplotlib/__pycache__/afm.cpython-39.pyc,,
matplotlib/__pycache__/animation.cpython-39.pyc,,
matplotlib/__pycache__/artist.cpython-39.pyc,,
matplotlib/__pycache__/axis.cpython-39.pyc,,
matplotlib/__pycache__/backend_bases.cpython-39.pyc,,
matplotlib/__pycache__/backend_managers.cpython-39.pyc,,
matplotlib/__pycache__/backend_tools.cpython-39.pyc,,
matplotlib/__pycache__/bezier.cpython-39.pyc,,
matplotlib/__pycache__/blocking_input.cpython-39.pyc,,
matplotlib/__pycache__/category.cpython-39.pyc,,
matplotlib/__pycache__/cm.cpython-39.pyc,,
matplotlib/__pycache__/collections.cpython-39.pyc,,
matplotlib/__pycache__/colorbar.cpython-39.pyc,,
matplotlib/__pycache__/colors.cpython-39.pyc,,
matplotlib/__pycache__/container.cpython-39.pyc,,
matplotlib/__pycache__/contour.cpython-39.pyc,,
matplotlib/__pycache__/dates.cpython-39.pyc,,
matplotlib/__pycache__/docstring.cpython-39.pyc,,
matplotlib/__pycache__/dviread.cpython-39.pyc,,
matplotlib/__pycache__/figure.cpython-39.pyc,,
matplotlib/__pycache__/font_manager.cpython-39.pyc,,
matplotlib/__pycache__/fontconfig_pattern.cpython-39.pyc,,
matplotlib/__pycache__/gridspec.cpython-39.pyc,,
matplotlib/__pycache__/hatch.cpython-39.pyc,,
matplotlib/__pycache__/image.cpython-39.pyc,,
matplotlib/__pycache__/legend.cpython-39.pyc,,
matplotlib/__pycache__/legend_handler.cpython-39.pyc,,
matplotlib/__pycache__/lines.cpython-39.pyc,,
matplotlib/__pycache__/markers.cpython-39.pyc,,
matplotlib/__pycache__/mathtext.cpython-39.pyc,,
matplotlib/__pycache__/mlab.cpython-39.pyc,,
matplotlib/__pycache__/offsetbox.cpython-39.pyc,,
matplotlib/__pycache__/patches.cpython-39.pyc,,
matplotlib/__pycache__/path.cpython-39.pyc,,
matplotlib/__pycache__/patheffects.cpython-39.pyc,,
matplotlib/__pycache__/pylab.cpython-39.pyc,,
matplotlib/__pycache__/pyplot.cpython-39.pyc,,
matplotlib/__pycache__/quiver.cpython-39.pyc,,
matplotlib/__pycache__/rcsetup.cpython-39.pyc,,
matplotlib/__pycache__/sankey.cpython-39.pyc,,
matplotlib/__pycache__/scale.cpython-39.pyc,,
matplotlib/__pycache__/spines.cpython-39.pyc,,
matplotlib/__pycache__/stackplot.cpython-39.pyc,,
matplotlib/__pycache__/streamplot.cpython-39.pyc,,
matplotlib/__pycache__/table.cpython-39.pyc,,
matplotlib/__pycache__/texmanager.cpython-39.pyc,,
matplotlib/__pycache__/text.cpython-39.pyc,,
matplotlib/__pycache__/textpath.cpython-39.pyc,,
matplotlib/__pycache__/ticker.cpython-39.pyc,,
matplotlib/__pycache__/tight_bbox.cpython-39.pyc,,
matplotlib/__pycache__/tight_layout.cpython-39.pyc,,
matplotlib/__pycache__/transforms.cpython-39.pyc,,
matplotlib/__pycache__/type1font.cpython-39.pyc,,
matplotlib/__pycache__/units.cpython-39.pyc,,
matplotlib/__pycache__/widgets.cpython-39.pyc,,
matplotlib/_animation_data.py,sha256=yClmMx6K-y6pjG3FdHancRyRhyneFuBEbQZ_lhezVys,7499
matplotlib/_cm.py,sha256=nZCQdTWsPc5aJ-n08l2g293Wwg0kSWIgcfStzv9Dtxg,66643
matplotlib/_cm_listed.py,sha256=9aMZ1uoTkxeDKlXfUNmY99HEtAXsW_JcSpwYaBs0kHs,98165
matplotlib/_color_data.py,sha256=K2HSKblmuh-X_1ZZ9TcXcP7iKHaGC4mC_ScWqX_tdXE,34947
matplotlib/_constrained_layout.py,sha256=eafuhM2rw5SL_ilqf0ImxQwtmFX_pR_boeKUrjTHvB0,29678
matplotlib/_contour.cp39-win_amd64.pyd,sha256=vCSLxDIMOHUcN1vMdEXrfCtWoufMcBTiejzK_LnTgrE,61440
matplotlib/_image.cp39-win_amd64.pyd,sha256=3vohEjHuQ78vFWgMpBRCtGL0rXfPckjM5mKRIEylZtI,170496
matplotlib/_layoutbox.py,sha256=LG5KEmNm5apZa1putqzafEpX9w4lLwkoOPxg4owiO2Y,23872
matplotlib/_mathtext_data.py,sha256=CmKFRW6mXCJqgZSQaiNOSG_VUn9WiSx5Hrg-4qKIn14,89371
matplotlib/_path.cp39-win_amd64.pyd,sha256=x6PR4wHXe9bZsNHtJjprxAjK4Y7F5ljboF9FQQ7MCck,144384
matplotlib/_png.cp39-win_amd64.pyd,sha256=bpnp5WT52MceoHRPnBOEDrcZLoLNuFRZhf6Kh0HQrYc,191488
matplotlib/_pylab_helpers.py,sha256=RscVbvWEk6QyznzVvX-lSHkWCUOo9Lik7D7j4xelQJ4,3445
matplotlib/_qhull.cp39-win_amd64.pyd,sha256=ALauEwgY9iZTQLHxc-vC8gA32q6r-QoVRF_ev58K9Ug,368640
matplotlib/_text_layout.py,sha256=88DxzfAOPzpRjpu0OwLaRl6eOVJ5Var8ZxrDyhAQ7C8,1036
matplotlib/_tri.cp39-win_amd64.pyd,sha256=vCuxbqpQtXs4VN6-r8zVw5reaQfTvg9gZuNVBKtDET4,100864
matplotlib/_version.py,sha256=lXNyD8hCBblNoClS2_91s10Znh3RnOsrlMHWpVZ2Nyk,492
matplotlib/afm.py,sha256=Y2FmLqutIXI__QiAbMNpFclleQrpGf1xsqBgjyCZir0,16642
matplotlib/animation.py,sha256=GAN6TitKtR_8CvZrVFQ5wEJeTkJ2Nx_NfZ0Hn1B756s,68072
matplotlib/artist.py,sha256=2Qd4EUb9n4FAizD1fdb48diR7yRivgwvY8JESg4r4mE,53229
matplotlib/axes/__init__.py,sha256=npQuBvs_xEBEGUP2-BBZzCrelsAQYgB1U96kSZTSWIs,46
matplotlib/axes/__pycache__/__init__.cpython-39.pyc,,
matplotlib/axes/__pycache__/_axes.cpython-39.pyc,,
matplotlib/axes/__pycache__/_base.cpython-39.pyc,,
matplotlib/axes/__pycache__/_secondary_axes.cpython-39.pyc,,
matplotlib/axes/__pycache__/_subplots.cpython-39.pyc,,
matplotlib/axes/_axes.py,sha256=U5SiUsncZiTc7A5Py1IQMkmppuCqcoHLy8qobcW2r7k,313031
matplotlib/axes/_base.py,sha256=V5zDvirbvuoKXltf9UjiwcTxpyH0ag-qR9C4cMeK2rI,160074
matplotlib/axes/_secondary_axes.py,sha256=plMlrvrznuuBSRakuznyCyr_ZNnSiuc_3J-YD3Hg8e8,14352
matplotlib/axes/_subplots.py,sha256=rkT6vCCbCTItY_oAmlT-1rSPOoIerjScJC91I2YLTp0,10203
matplotlib/axis.py,sha256=50zN9IvoaYGqq8t8zUiaaTtcUJeNjxdAnmXioRlfNWc,90049
matplotlib/backend_bases.py,sha256=zJiAa5KudnMyuKYdcRF1gJUdlcYvr1PcU5S1zRCtcQg,118625
matplotlib/backend_managers.py,sha256=HuHOXPFjVF1R6ttBLxlucsLcEsWlRXtbm_wr1OJer2Q,12819
matplotlib/backend_tools.py,sha256=zVBxfyPoQx0_lQe1Su0o-zTycOQt-zKWTp3HEY-jTxI,36252
matplotlib/backends/__init__.py,sha256=cKAeiC5g0Up9svoxT24paOcnKc4tb5cbfbF-AWxXLUE,1722
matplotlib/backends/__pycache__/__init__.cpython-39.pyc,,
matplotlib/backends/__pycache__/_backend_pdf_ps.cpython-39.pyc,,
matplotlib/backends/__pycache__/_backend_tk.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_agg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_cairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_gtk3.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_gtk3agg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_gtk3cairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_macosx.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_mixed.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_nbagg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_pdf.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_pgf.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_ps.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qt4.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qt4agg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qt4cairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qt5.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qt5agg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qt5cairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_svg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_template.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_tkagg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_tkcairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_webagg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_webagg_core.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_wx.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_wxagg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_wxcairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/qt_compat.cpython-39.pyc,,
matplotlib/backends/_backend_agg.cp39-win_amd64.pyd,sha256=6SLTzuG6BIYpc2gSXMb-BuK65NRpF3Knfn3Ikmxvl8E,189440
matplotlib/backends/_backend_pdf_ps.py,sha256=T8x_NWAo9_Wvnpw6HnsneRI9AyAxcp80TDe-z2eosbM,2709
matplotlib/backends/_backend_tk.py,sha256=y2RH0KtUQIBXQRaOZm92GIpv8swQCAMsNNdNZ7Qgd-w,32657
matplotlib/backends/_tkagg.cp39-win_amd64.pyd,sha256=qpEKQPiBbea9a_uN7Z_yyp_KSsSPcwUJMewalQcO5ts,26624
matplotlib/backends/backend_agg.py,sha256=bjt43QEsQ-AeEeWXSBwRcbJsuDU7Dex-JsdbApekXzA,22347
matplotlib/backends/backend_cairo.py,sha256=zJAQ5_OkKchNrItE8jN9CriAw4aQ0PP3qKqfINu3CSc,16562
matplotlib/backends/backend_gtk3.py,sha256=ED_iXiCIXQlMJmh8YlJJcPkCxnm9Wly7kSod0UogtRc,33832
matplotlib/backends/backend_gtk3agg.py,sha256=iKLBQ48c9Vh4krSxYeSDbHZ4G9wXoezk2VTIgNR4BWw,3072
matplotlib/backends/backend_gtk3cairo.py,sha256=TDQUuYq5KCPvAU-MY2wBxpx2zAn9vOO_ieeE_7fdtS4,1591
matplotlib/backends/backend_macosx.py,sha256=fALNUVEkZrqRrMHe5G_wy5i7FODvXXIum7RU2PXOiG4,5829
matplotlib/backends/backend_mixed.py,sha256=R3-Z5AW7anq31csMjylsY4pSehsAsBLrxFD5YhZkZ6w,5280
matplotlib/backends/backend_nbagg.py,sha256=xAv_OZfz6NtMrbAXWqwX9GCgTEsb5e5FpyAMHSIE-j4,8902
matplotlib/backends/backend_pdf.py,sha256=WHaS_D6t0JJTSJhNUY8iA7DYaoFTpKK_0oz1CAS_YN0,96195
matplotlib/backends/backend_pgf.py,sha256=R34xfvJyVm0k4IgjCHPU0bKeP02zJ0_Pr0hV6yaCVv8,43431
matplotlib/backends/backend_ps.py,sha256=cO6kf012Xg8dZBgtfuDKJY9_0ld07UoS11cqIa_SLfc,50514
matplotlib/backends/backend_qt4.py,sha256=H9AsG4NAF27LdGUJDi1n5RwJhK1lI43sa9VXtUgW3K8,397
matplotlib/backends/backend_qt4agg.py,sha256=BXMTBYmqyT_uvEpgSMWts5m2q1YK0vhPeybt0ZMVd0I,292
matplotlib/backends/backend_qt4cairo.py,sha256=cvXKhk8CZvceDiEl9OXmumjJ6dZ3k0u93xy8w8i24gQ,229
matplotlib/backends/backend_qt5.py,sha256=t-ue-y5Z4R3g5lzsHl9trszH4tIWIwmxxXQ6PTubo-0,39155
matplotlib/backends/backend_qt5agg.py,sha256=Uhgbrfuc4GakgaD-wkSnk5mvAH-IJonlaRZb-JdZMWU,3652
matplotlib/backends/backend_qt5cairo.py,sha256=H3BSQNQD4GxzA6zeQ05M2h0Semd5zJIxzhvK6fzVVPg,1819
matplotlib/backends/backend_svg.py,sha256=KGdJMf-cXIW82s0MC9-wOsmtEeo8P3np_uK06z2FGkI,43552
matplotlib/backends/backend_template.py,sha256=SVPx4bl2n0eKgEolfJH7ljJnqlak0XhFE6Zr3yr6GU4,8407
matplotlib/backends/backend_tkagg.py,sha256=WMslLWYmtxlmAaBH4tx4HjmRDWMKiSV91KHF9yeMRng,676
matplotlib/backends/backend_tkcairo.py,sha256=dVCh7ZD_2OR0DBQ0N3icD8cDV1SeEzCsRja446wWhPw,1069
matplotlib/backends/backend_webagg.py,sha256=NtP1VSSXQNvB_EBQi14No7flt1C64VwekiTG58tt7LU,10794
matplotlib/backends/backend_webagg_core.py,sha256=u026hAuD_dwL0a2D7g--jzBr_KFS5UqJdFZDvIdlmII,17558
matplotlib/backends/backend_wx.py,sha256=C3lGdZs7i7sRAWuoULNLbfVasG6Fg8zHO-9bSABXiYk,66847
matplotlib/backends/backend_wxagg.py,sha256=Jhb24f2W6e5yCqSi4F971hor_I8Epf_WMfutq3VoTYI,3027
matplotlib/backends/backend_wxcairo.py,sha256=VC5TyJaX8TPLSgHv5ckAreoGrY_KiNRMQjVInMLlcFk,1843
matplotlib/backends/qt_compat.py,sha256=tLWY0x58y7TVbBBI7vRRENe8ON4ggTomMAR4mcMClqc,7549
matplotlib/backends/qt_editor/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/qt_editor/__pycache__/__init__.cpython-39.pyc,,
matplotlib/backends/qt_editor/__pycache__/_formlayout.cpython-39.pyc,,
matplotlib/backends/qt_editor/__pycache__/figureoptions.cpython-39.pyc,,
matplotlib/backends/qt_editor/__pycache__/formlayout.cpython-39.pyc,,
matplotlib/backends/qt_editor/__pycache__/formsubplottool.cpython-39.pyc,,
matplotlib/backends/qt_editor/_formlayout.py,sha256=aHC33AfwtVjly7sPjLTFrVABZqAD2RAloR6KCEAUv5A,20592
matplotlib/backends/qt_editor/figureoptions.py,sha256=YySx6A_wZSACMEHbXYkoQ6wR0_6PPwON2c6YwejUeVs,9481
matplotlib/backends/qt_editor/formlayout.py,sha256=ERfmFwpvhl168PWNTJ0SFhQmPuSrmjzFNOe_puUCoSE,177
matplotlib/backends/qt_editor/formsubplottool.py,sha256=HiiXkwCotra_hI9JU208KOs8Q9JuGH1uAW3mV5l3Evg,1934
matplotlib/backends/web_backend/all_figures.html,sha256=m20aQIhuI4GBdrgZg_j02zSVjAcTRUufPOMSe4i7ayc,1525
matplotlib/backends/web_backend/css/boilerplate.css,sha256=qui16QXRnQFNJDbcMasfH6KtN9hLjv8883U9cJmsVCE,2310
matplotlib/backends/web_backend/css/fbm.css,sha256=Us0osu_rK8EUAdp_GXrh89tN_hUNCN-r7N1T1NvmmwI,1473
matplotlib/backends/web_backend/css/page.css,sha256=Djf6ZNMFaM6_hVaizSkDFoqk-jn81qgduwles4AroGk,1599
matplotlib/backends/web_backend/ipython_inline_figure.html,sha256=mzi-yWg4fcO6PdtTBCfiNuvcv04T53lcRQi-8hphwuE,1305
matplotlib/backends/web_backend/jquery-ui-1.12.1/AUTHORS.txt,sha256=W2Lh1mbGo3Owc0oXX9U1-TFVSZYaC72KvSRrrRp3UII,12660
matplotlib/backends/web_backend/jquery-ui-1.12.1/LICENSE.txt,sha256=3jP7aViA0LB2FdS4b3jNQ3lpBpWa3l_f73CWiCeg23g,1817
matplotlib/backends/web_backend/jquery-ui-1.12.1/external/jquery/jquery.js,sha256=Qw82-bXyGq6MydymqBxNPYTaUXXq7c8v3CwiYwLLNXU,293430
matplotlib/backends/web_backend/jquery-ui-1.12.1/images/ui-icons_444444_256x240.png,sha256=6vfH7idHJ13abFPnMaENsaexX0-7RuG2nWuyBWvJ_YE,7006
matplotlib/backends/web_backend/jquery-ui-1.12.1/images/ui-icons_555555_256x240.png,sha256=XQQFHf2dLXQDVUBPmKaD0ewP6y_KfXblM8Gm5c6S3S4,7074
matplotlib/backends/web_backend/jquery-ui-1.12.1/images/ui-icons_777620_256x240.png,sha256=nb5KDQP-7W9l6yVgoKi0ukJkVF7o_THBdjo7IZ0DKNY,4676
matplotlib/backends/web_backend/jquery-ui-1.12.1/images/ui-icons_777777_256x240.png,sha256=51snIR4W_PlHFRaAAbtwVco3bUb5KBELo9CCUjJFLlo,7013
matplotlib/backends/web_backend/jquery-ui-1.12.1/images/ui-icons_cc0000_256x240.png,sha256=AokVddQ1jp7d4-QtlAV_jp-CqdZDdvce6GzvFJ0wU34,4632
matplotlib/backends/web_backend/jquery-ui-1.12.1/images/ui-icons_ffffff_256x240.png,sha256=trBt7vK5JMw4NdY_SIPUeIJzSjPnGyEtkXpozt47jp0,6313
matplotlib/backends/web_backend/jquery-ui-1.12.1/index.html,sha256=5g7_MLZlkh92FXWOR0q02My8knssXq20DXz-BkiYiP4,32588
matplotlib/backends/web_backend/jquery-ui-1.12.1/jquery-ui.css,sha256=p6xU9YulB7E2Ic62_PX-h59ayb3PBJ0WFTEQxq0EjHw,37326
matplotlib/backends/web_backend/jquery-ui-1.12.1/jquery-ui.js,sha256=T0Vest3yCU7pafRw9r-settMBX6JkKN06dqBnpQ8d30,520714
matplotlib/backends/web_backend/jquery-ui-1.12.1/jquery-ui.min.css,sha256=rByPlHULObEjJ6XQxW_flG2r-22R5dKiAoef-aXWfik,32076
matplotlib/backends/web_backend/jquery-ui-1.12.1/jquery-ui.min.js,sha256=KM512VNnjElC30ehFwehXjx1YCHPiQkOPmqnrWtpccM,253669
matplotlib/backends/web_backend/jquery-ui-1.12.1/jquery-ui.structure.css,sha256=E1uqV-d412nbSI-oqDMIQsTSttP-FS7Bxwc7mQdQYOo,18705
matplotlib/backends/web_backend/jquery-ui-1.12.1/jquery-ui.structure.min.css,sha256=rxais37anKUnpL5QzSYte-JnIsmkGmLG-ZhKSkZkwVM,15548
matplotlib/backends/web_backend/jquery-ui-1.12.1/jquery-ui.theme.css,sha256=mEMD30TTg-vIEGUmHHgcgSOgm0FBfLipyQ97Jr0TTH8,18671
matplotlib/backends/web_backend/jquery-ui-1.12.1/jquery-ui.theme.min.css,sha256=AjyoyaRtnGVTywKH_Isxxu5PXI0s4CcE0BzPAX83Ppc,13849
matplotlib/backends/web_backend/jquery-ui-1.12.1/package.json,sha256=kjEW8xMYuqRSwEE58KqTDNLgkx_6YL7tb1M9vlMK98w,1847
matplotlib/backends/web_backend/js/mpl.js,sha256=xrBOaet7K2P2iq7wgIexceDdc9T_WFb4TPJXDX55JuU,16991
matplotlib/backends/web_backend/js/mpl_tornado.js,sha256=lSxC7-yqF1GYY-6SheaHanx6SujMdcG7Vx2_3qbi-9Q,272
matplotlib/backends/web_backend/js/nbagg_mpl.js,sha256=nqIF0zFBQGpOo5Tmq2uRkyFJDeali66PWQDSYySgpnQ,7428
matplotlib/backends/web_backend/nbagg_uat.ipynb,sha256=y1N8hQzBJ05rJ2hZla2_Mw6tOUfNP1UHKo636W1e098,15933
matplotlib/backends/web_backend/single_figure.html,sha256=-iFrlIsaY1rOK9bNiDxcX8fdc0WP7DXXq-MEuLYfOvM,1216
matplotlib/bezier.py,sha256=Fqb2gqRnmS2wYjEZVFcdSngRxh5g_hFnxX82RFpimec,17271
matplotlib/blocking_input.py,sha256=soMcLeXT2mKxkH5fyQqYEOP_Dd-TUjoA1_L7imb2B2Q,11105
matplotlib/category.py,sha256=_iJbNlZzi3kqPtz_bhEBCj8q6RsBp5Urd3yNS81dOQg,7131
matplotlib/cbook/__init__.py,sha256=k7_Gdpbg26mrQlfQJr3eTnRGvXInhfuEjWeBrY5-IlY,70426
matplotlib/cbook/__pycache__/__init__.cpython-39.pyc,,
matplotlib/cbook/__pycache__/deprecation.cpython-39.pyc,,
matplotlib/cbook/deprecation.py,sha256=UdXgde_IwT5BF6DKn2DLslSvbp6y-0iTkHLT8hVXSOo,15111
matplotlib/cm.py,sha256=qo1ZhJvKa7dCfALcj1X6rsy7R60pbJht7zK6pi_9EcA,12623
matplotlib/collections.py,sha256=oHifrrIgTm-fe9jep9tMi1MSL6CG977S5PGZUv3I-1w,74251
matplotlib/colorbar.py,sha256=fH-7JMvO_mjCD6nsoMnyVxm7Nby9qM-WmclpZodbCkg,63075
matplotlib/colors.py,sha256=auWiOGPMdI5twHSoVVI0AyEe7O9LJfn3fKetp_8yMzA,75537
matplotlib/compat/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/compat/__pycache__/__init__.cpython-39.pyc,,
matplotlib/container.py,sha256=yAq9s8ylaQxkt2N-qTSnBdF7zQZ5_-g96XgKzlySaLI,4302
matplotlib/contour.py,sha256=p8PJvj7N7wd-yA94NP7cz230VHmYG3rkSsdYSoTP4hE,68387
matplotlib/dates.py,sha256=hGrXqJ-O1ahP2yCYK0HjFWJu0jt-EDke1S33VZ2uOe0,66867
matplotlib/docstring.py,sha256=TuDl6xhQwCISvIV5_rPKvT85V1k1IA5HVSyK-JMY9k4,3970
matplotlib/dviread.py,sha256=frNSd6lkqNERFM603pP4clwgyafofl-ldVze3ny_upM,39860
matplotlib/figure.py,sha256=FnPQ38yzHNnQf5X0kp3LMVf2rslH2KC57quD5X1W_Nw,101081
matplotlib/font_manager.py,sha256=OZ2BAmPgFrO4VjeBA-DKGM0H0I5T4ev5gtAdirbXwR0,45282
matplotlib/fontconfig_pattern.py,sha256=El7xTXT3EnEESVONX9bKz_Yvaq4lLQp3KIYBgNbAguI,6719
matplotlib/ft2font.cp39-win_amd64.pyd,sha256=SG_9aF3dbmlMSGnGz0B_iE2FEq1oF-TcU-qAAxBT9c0,690688
matplotlib/gridspec.py,sha256=GP8GieOSr-wi8tFtx2pwsO_TFenN6CNuE2T3iDfRvBQ,25052
matplotlib/hatch.py,sha256=6AcnVeBs4d_Uv_FWQoU48v28iW5F7oKI_RKkv68L_WM,6971
matplotlib/image.py,sha256=0FR_I8bEV0XfM6kYyAS8j965LCbtapgnpxDWLrEV390,62787
matplotlib/legend.py,sha256=QBSq0VyGWFsTB7QFGx8bpmUR-MLw9xJPrhMcJSlpVtU,48268
matplotlib/legend_handler.py,sha256=hDMDvDJepK6UUyTJCkGQcAMVgLYW7eD16hQoQNVGQFY,26428
matplotlib/lines.py,sha256=8u3vEQXb_ceOivl4zyL5MIDuVDlxgZwOsH9xf37OIYo,51362
matplotlib/markers.py,sha256=Xpuq9O1t7OB3DBK2nZa4IbPjqV0ROGY9mxXW_NUKDKk,32533
matplotlib/mathtext.py,sha256=udfWXa-87xV1Mhve65rO0jf5On3DAQwJ-FgD6BSojhM,120491
matplotlib/mlab.py,sha256=1V0nvAkZF5RJbsIAp3AfTgzim2wRQCql0FCq7HvLknI,49040
matplotlib/mpl-data/fonts/afm/cmex10.afm,sha256=blR3ERmrVBV5XKkAnDCj4NMeYVgzH7cXtJ3u59u9GuE,12070
matplotlib/mpl-data/fonts/afm/cmmi10.afm,sha256=5qwEOpedEo76bDUahyuuF1q0cD84tRrX-VQ4p3MlfBo,10416
matplotlib/mpl-data/fonts/afm/cmr10.afm,sha256=WDvgC_D3UkGJg9u-J0U6RaT02lF4oz3lQxHtg1r3lYw,10101
matplotlib/mpl-data/fonts/afm/cmsy10.afm,sha256=AbmzvCVWBceHRfmRfeJ9E6xzOQTFLk0U1zDfpf3_MaM,8295
matplotlib/mpl-data/fonts/afm/cmtt10.afm,sha256=4ji7_mTpeWMa93o_UHBWPKCnqsBfhJJNllat1lJArP4,6501
matplotlib/mpl-data/fonts/afm/pagd8a.afm,sha256=jjFrigwkTpYLqa26cpzZvKQNBo-PuF4bmDVqaM4pMWw,17183
matplotlib/mpl-data/fonts/afm/pagdo8a.afm,sha256=sgNQdeYyx8J-itGw9h31y95aMBiTCRvmNSPTXwwS7xg,17255
matplotlib/mpl-data/fonts/afm/pagk8a.afm,sha256=ZUtfHPloNqcvGMHMxaKDSlshhOcjwheUx143RwpGdIU,17241
matplotlib/mpl-data/fonts/afm/pagko8a.afm,sha256=Yj1wBg6Jsqqz1KBfhRoJ3ACR-CMQol8Fj_ZM5NZ1gDk,17346
matplotlib/mpl-data/fonts/afm/pbkd8a.afm,sha256=Zl5o6J_di9Y5j2EpHtjew-_sfg7-WoeVmO9PzOYSTUc,15157
matplotlib/mpl-data/fonts/afm/pbkdi8a.afm,sha256=JAOno930iTyfZILMf11vWtiaTgrJcPpP6FRTRhEMMD4,15278
matplotlib/mpl-data/fonts/afm/pbkl8a.afm,sha256=UJqJjOJ6xQDgDBLX157mKpohIJFVmHM-N6x2-DiGv14,15000
matplotlib/mpl-data/fonts/afm/pbkli8a.afm,sha256=AWislZ2hDbs0ox_qOWREugsbS8_8lpL48LPMR40qpi0,15181
matplotlib/mpl-data/fonts/afm/pcrb8a.afm,sha256=6j1TS2Uc7DWSc-8l42TGDc1u0Fg8JspeWfxFayjUwi8,15352
matplotlib/mpl-data/fonts/afm/pcrbo8a.afm,sha256=smg3mjl9QaBDtQIt06ko5GvaxLsO9QtTvYANuE5hfG0,15422
matplotlib/mpl-data/fonts/afm/pcrr8a.afm,sha256=7nxFr0Ehz4E5KG_zSE5SZOhxRH8MyfnCbw-7x5wu7tw,15339
matplotlib/mpl-data/fonts/afm/pcrro8a.afm,sha256=NKEz7XtdFkh9cA8MvY-S3UOZlV2Y_J3tMEWFFxj7QSg,15443
matplotlib/mpl-data/fonts/afm/phvb8a.afm,sha256=NAx4M4HjL7vANCJbc-tk04Vkol-T0oaXeQ3T2h-XUvM,17155
matplotlib/mpl-data/fonts/afm/phvb8an.afm,sha256=8e_myD-AQkNF7q9XNLb2m76_lX2TUr3a5wog_LIE1sk,17086
matplotlib/mpl-data/fonts/afm/phvbo8a.afm,sha256=8fkBRmJ-SWY2YrBg8fFyjJyrJp8daQ6JPO6LvhM8xPI,17230
matplotlib/mpl-data/fonts/afm/phvbo8an.afm,sha256=aeVRvV4r15BBvxuRJ0MG8ZHuH2HViuIiCYkvuapmkmM,17195
matplotlib/mpl-data/fonts/afm/phvl8a.afm,sha256=IyMYM-bgl-gI6rG0EuZZ2OLzlxJfGeSh8xqsh0t-eJQ,15627
matplotlib/mpl-data/fonts/afm/phvlo8a.afm,sha256=s12C-eNnIDHJ_UVbuiprjxBjCiHIbS3Y8ORTC-qTpuI,15729
matplotlib/mpl-data/fonts/afm/phvr8a.afm,sha256=Kt8KaRidts89EBIK29X2JomDUEDxvroeaJz_RNTi6r4,17839
matplotlib/mpl-data/fonts/afm/phvr8an.afm,sha256=lL5fAHTRwODl-sB5mH7IfsD1tnnea4yRUK-_Ca2bQHM,17781
matplotlib/mpl-data/fonts/afm/phvro8a.afm,sha256=3KqK3eejiR4hIFBUynuSX_4lMdE2V2T58xOF8lX-fwc,17919
matplotlib/mpl-data/fonts/afm/phvro8an.afm,sha256=Vx9rRf3YfasMY7tz-njSxz67xHKk-fNkN7yBi0X2IP0,17877
matplotlib/mpl-data/fonts/afm/pncb8a.afm,sha256=aoXepTcDQtQa_mspflMJkEFKefzXHoyjz6ioJVI0YNc,16028
matplotlib/mpl-data/fonts/afm/pncbi8a.afm,sha256=pCWW1MYgy0EmvwaYsaYJaAI_LfrsKmDANHu7Pk0RaiU,17496
matplotlib/mpl-data/fonts/afm/pncr8a.afm,sha256=0CIB2BLe9r-6_Wl5ObRTTf98UOrezmGQ8ZOuBX5kLks,16665
matplotlib/mpl-data/fonts/afm/pncri8a.afm,sha256=5R-pLZOnaHNG8pjV6MP3Ai-d2OTQYR_cYCb5zQhzfSU,16920
matplotlib/mpl-data/fonts/afm/pplb8a.afm,sha256=3EzUbNnXr5Ft5eFLY00W9oWu59rHORgDXUuJaOoKN58,15662
matplotlib/mpl-data/fonts/afm/pplbi8a.afm,sha256=X_9tVspvrcMer3OS8qvdwjFFqpAXYZneyCL2NHA902g,15810
matplotlib/mpl-data/fonts/afm/pplr8a.afm,sha256=ijMb497FDJ9nVdVMb21F7W3-cu9sb_9nF0oriFpSn8k,15752
matplotlib/mpl-data/fonts/afm/pplri8a.afm,sha256=8KITbarcUUMi_hdoRLLmNHtlqs0TtOSKqtPFft7X5nY,15733
matplotlib/mpl-data/fonts/afm/psyr.afm,sha256=Iyt8ajE4B2Tm34oBj2pKtctIf9kPfq05suQefq8p3Ro,9644
matplotlib/mpl-data/fonts/afm/ptmb8a.afm,sha256=bL1fA1NC4_nW14Zrnxz4nHlXJb4dzELJPvodqKnYeMg,17983
matplotlib/mpl-data/fonts/afm/ptmbi8a.afm,sha256=-_Ui6XlKaFTHEnkoS_-1GtIr5VtGa3gFQ2ezLOYHs08,18070
matplotlib/mpl-data/fonts/afm/ptmr8a.afm,sha256=IEcsWcmzJyjCwkgsw4o6hIMmzlyXUglJat9s1PZNnEU,17942
matplotlib/mpl-data/fonts/afm/ptmri8a.afm,sha256=49fQMg5fIGguZ7rgc_2styMK55Pv5bPTs7wCzqpcGpk,18068
matplotlib/mpl-data/fonts/afm/putb8a.afm,sha256=qMaHTdpkrNL-m4DWhjpxJCSmgYkCv1qIzLlFfM0rl40,21532
matplotlib/mpl-data/fonts/afm/putbi8a.afm,sha256=g7AVJyiTxeMpNk_1cSfmYgM09uNUfPlZyWGv3D1vcAk,21931
matplotlib/mpl-data/fonts/afm/putr8a.afm,sha256=XYmNC5GQgSVAZKTIYdYeNksE6znNm9GF_0SmQlriqx0,22148
matplotlib/mpl-data/fonts/afm/putri8a.afm,sha256=i7fVe-iLyLtQxCfAa4IxdxH-ufcHmMk7hbCGG5TxAY4,21891
matplotlib/mpl-data/fonts/afm/pzcmi8a.afm,sha256=wyuoIWEZOcoXrSl1tPzLkEahik7kGi91JJj-tkFRG4A,16250
matplotlib/mpl-data/fonts/afm/pzdr.afm,sha256=MyjLAnzKYRdQBfof1W3k_hf30MvqOkqL__G22mQ5xww,9467
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Bold.afm,sha256=sIDDI-B82VZ3C0mI_mHFITCZ7PVn37AIYMv1CrHX4sE,15333
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-BoldOblique.afm,sha256=zg61QobD3YU9UBfCXmvmhBNaFKno-xj8sY0b2RpgfLw,15399
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Oblique.afm,sha256=vRQm5j1sTUN4hicT1PcVZ9P9DTTUHhEzfPXqUUzVZhE,15441
matplotlib/mpl-data/fonts/pdfcorefonts/Courier.afm,sha256=Mdcq2teZEBJrIqVXnsnhee7oZnTs6-P8_292kWGTrw4,15335
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Bold.afm,sha256=i2l4gcjuYXoXf28uK7yIVwuf0rnw6J7PwPVQeHj5iPw,69269
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-BoldOblique.afm,sha256=Um5O6qK11DXLt8uj_0IoWkc84TKqHK3bObSKUswQqvY,69365
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Oblique.afm,sha256=hVYDg2b52kqtbVeCzmiv25bW1yYdpkZS-LXlGREN2Rs,74392
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica.afm,sha256=23cvKDD7bQAJB3kdjSahJSTZaUOppznlIO6FXGslyW8,74292
matplotlib/mpl-data/fonts/pdfcorefonts/Symbol.afm,sha256=P5UaoXr4y0qh4SiMa5uqijDT6ZDr2-jPmj1ayry593E,9740
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Bold.afm,sha256=cQTmr2LFPwKQE_sGQageMcmFicjye16mKJslsJLHQyE,64251
matplotlib/mpl-data/fonts/pdfcorefonts/Times-BoldItalic.afm,sha256=pzWOdycm6RqocBWgAVY5Jq0z3Fp7LuqWgLNMx4q6OFw,59642
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Italic.afm,sha256=bK5puSMpGT_YUILwyJrXoxjfj7XJOdfv5TQ_iKsJRzw,66328
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Roman.afm,sha256=hhNrUnpazuDDKD1WpraPxqPWCYLrO7D7bMVOg-zI13o,60460
matplotlib/mpl-data/fonts/pdfcorefonts/ZapfDingbats.afm,sha256=ZuOmt9GcKofjdOq8kqhPhtAIhOwkL2rTJTmZxAjFakA,9527
matplotlib/mpl-data/fonts/pdfcorefonts/readme.txt,sha256=MRv8ppSITYYAb7lt5EOw9DWWNZIblfxsFhu5TQE7cpI,828
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf,sha256=sYS4njwQdfIva3FXW2_CDUlys8_TsjMiym_Vltyu8Wc,704128
matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf,sha256=bt8CgxYBhq9FHL7nHnuEXy5Mq_Jku5ks5mjIPCVGXm8,641720
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf,sha256=zN90s1DxH9PdV3TeUOXmNGoaXaH1t9X7g1kGZel6UhM,633840
matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf,sha256=P99pyr8GBJ6nCgC1kZNA4s4ebQKwzDxLRPtoAb0eDSI,756072
matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf,sha256=ggmdz7paqGjN_CdFGYlSX-MpL3N_s8ngMozpzvWWUvY,25712
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf,sha256=uq2ppRcv4giGJRr_BDP8OEYZEtXa8HKH577lZiCo2pY,331536
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf,sha256=ppCBwVx2yCfgonpaf1x0thNchDSZlVSV_6jCDTqYKIs,253116
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf,sha256=KAUoE_enCfyJ9S0ZLcmV708P3Fw9e3OknWhJsZFtDNA,251472
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf,sha256=YC7Ia4lIz82VZIL-ZPlMNshndwFJ7y95HUYT9EO87LM,340240
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf,sha256=w3U_Lta8Zz8VhG3EWt2-s7nIcvMvsY_VOiHxvvHtdnY,355692
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf,sha256=2T7-x6nS6CZ2jRou6VuVhw4V4pWZqE80hK8d4c7C4YE,347064
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf,sha256=PnmU-8VPoQzjNSpC1Uj63X2crbacsRCbydlg9trFfwQ,345612
matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf,sha256=EHJElW6ZYrnpb6zNxVGCXgrgiYrhNzcTPhuSGi_TX_o,379740
matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf,sha256=KRTzLkfHd8J75Wd6-ufbTeefnkXeb8kJfZlJwjwU99U,14300
matplotlib/mpl-data/fonts/ttf/LICENSE_DEJAVU,sha256=11k43sCY8G8Kw8AIUwZdlPAgvhw8Yu8dwpdboVtNmw4,4816
matplotlib/mpl-data/fonts/ttf/LICENSE_STIX,sha256=cxFOZdp1AxNhXR6XxCzf5iJpNcu-APm-geOHhD-s0h8,5475
matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf,sha256=FnN4Ax4t3cYhbWeBnJJg6aBv_ExHjk4jy5im_USxg8I,448228
matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf,sha256=6FM9xwg_o0a9oZM9YOpKg7Z9CUW86vGzVB-CtKDixqA,237360
matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf,sha256=mHiP1LpI37sr0CbA4gokeosGxzcoeWKLemuw1bsJc2w,181152
matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf,sha256=bPyzM9IrfDxiO9_UAXTxTIXD1nMcphZsHtyAFA6uhSc,175040
matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf,sha256=Ulb34CEzWsSFTRgPDovxmJZOwvyCAXYnbhaqvGU3u1c,59108
matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf,sha256=XRBqW3jR_8MBdFU0ObhiV7-kXwiBIMs7QVClHcT5tgs,30512
matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf,sha256=pb22DnbDf2yQqizotc3wBDqFGC_g27YcCGJivH9-Le8,41272
matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf,sha256=BMr9pWiBv2YIZdq04X4c3CgL6NPLUPrl64aV1N4w9Ug,46752
matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf,sha256=wYuH1gYUpCuusqItRH5kf9p_s6mUD-9X3L5RvRtKSxs,13656
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf,sha256=yNdvjUoSmsZCULmD7SVq9HabndG9P4dPhboL1JpAf0s,12228
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf,sha256=-9xVMYL4_1rcO8FiCKrCfR4PaSmKtA42ddLGqwtei1w,15972
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf,sha256=cYexyo8rZcdqMlpa9fNF5a2IoXLUTZuIvh0JD1Qp0i4,12556
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf,sha256=0lbHzpndzJmO8S42mlkhsz5NbvJLQCaH5Mcc7QZRDzc,19760
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf,sha256=3eBc-VtYbhQU3BnxiypfO6eAzEu8BdDvtIJSFbkS2oY,12192
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf,sha256=XFSKCptbESM8uxHtUFSAV2cybwxhSjd8dWVByq6f3w0,15836
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf,sha256=MUCYHrA0ZqFiSE_PjIGlJZgMuv79aUgQqE7Dtu3kuo0,12116
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf,sha256=_sdxDuEwBDtADpu9CyIXQxV7sIqA2TZVBCUiUjq5UCk,15704
matplotlib/mpl-data/fonts/ttf/cmb10.ttf,sha256=B0SXtQxD6ldZcYFZH5iT04_BKofpUQT1ZX_CSB9hojo,25680
matplotlib/mpl-data/fonts/ttf/cmex10.ttf,sha256=ryjwwXByOsd2pxv6WVrKCemNFa5cPVTOGa_VYZyWqQU,21092
matplotlib/mpl-data/fonts/ttf/cmmi10.ttf,sha256=MJKWW4gR_WpnZXmWZIRRgfwd0TMLk3-RWAjEhdMWI00,32560
matplotlib/mpl-data/fonts/ttf/cmr10.ttf,sha256=Tdl2GwWMAJ25shRfVe5mF9CTwnPdPWxbPkP_YRD6m_Y,26348
matplotlib/mpl-data/fonts/ttf/cmss10.ttf,sha256=ffkag9BbLkcexjjLC0NaNgo8eSsJ_EKn2mfpHy55EVo,20376
matplotlib/mpl-data/fonts/ttf/cmsy10.ttf,sha256=uyJu2TLz8QDNDlL15JEu5VO0G2nnv9uNOFTbDrZgUjI,29396
matplotlib/mpl-data/fonts/ttf/cmtt10.ttf,sha256=YhHwmuk1mZka_alwwkZp2tGnfiU9kVYk-_IS9wLwcdc,28136
matplotlib/mpl-data/images/back.gif,sha256=sdkxFRAh-Mgs44DTvruO5OxcI3Av9CS1g5MqMA_DDkQ,608
matplotlib/mpl-data/images/back.pdf,sha256=ZR7CJo_dAeCM-KlaGvskgtHQyRtrPIolc8REOmcoqJk,1623
matplotlib/mpl-data/images/back.png,sha256=E4dGf4Gnz1xJ1v2tMygHV0YNQgShreDeVApaMb-74mU,380
matplotlib/mpl-data/images/back.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back_large.gif,sha256=tqCtecrxNrPuDCUj7FGs8UXWftljKcwgp5cSBBhXwiQ,799
matplotlib/mpl-data/images/back_large.png,sha256=9A6hUSQeszhYONE4ZuH3kvOItM0JfDVu6tkfromCbsQ,620
matplotlib/mpl-data/images/filesave.gif,sha256=wAyNwOPd9c-EIPwcUAlqHSfLmxq167nhDVppOWPy9UA,723
matplotlib/mpl-data/images/filesave.pdf,sha256=P1EPPV2g50WTt8UaX-6kFoTZM1xVqo6S2H6FJ6Zd1ec,1734
matplotlib/mpl-data/images/filesave.png,sha256=b7ctucrM_F2mG-DycTedG_a_y4pHkx3F-zM7l18GLhk,458
matplotlib/mpl-data/images/filesave.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave_large.gif,sha256=IXrenlwu3wwO8WTRvxHt_q62NF6ZWyqk3jZhm6GE-G8,1498
matplotlib/mpl-data/images/filesave_large.png,sha256=LNbRD5KZ3Kf7nbp-stx_a1_6XfGBSWUfDdpgmnzoRvk,720
matplotlib/mpl-data/images/forward.gif,sha256=VNL9R-dECOX7wUAYPtU_DWn5hwi3SwLR17DhmBvUIxE,590
matplotlib/mpl-data/images/forward.pdf,sha256=KIqIL4YId43LkcOxV_TT5uvz1SP8k5iUNUeJmAElMV8,1630
matplotlib/mpl-data/images/forward.png,sha256=pKbLepgGiGeyY2TCBl8svjvm7Z4CS3iysFxcq4GR-wk,357
matplotlib/mpl-data/images/forward.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward_large.gif,sha256=H6Jbcc7qJwHJAE294YqI5Bm-5irofX40cKRvYdrG_Ig,786
matplotlib/mpl-data/images/forward_large.png,sha256=36h7m7DZDHql6kkdpNPckyi2LKCe_xhhyavWARz_2kQ,593
matplotlib/mpl-data/images/hand.gif,sha256=3lRfmAqQU7A2t1YXXsB9IbwzK7FaRh-IZO84D5-xCrw,1267
matplotlib/mpl-data/images/hand.pdf,sha256=hspwkNY915KPD7AMWnVQs7LFPOtlcj0VUiLu76dMabQ,4172
matplotlib/mpl-data/images/hand.png,sha256=2cchRETGKa0hYNKUxnJABwkyYXEBPqJy_VqSPlT0W2Q,979
matplotlib/mpl-data/images/hand.svg,sha256=tsVIES_nINrAbH4FqdsCGOx0SVE37vcofSYBhnnaOP0,4888
matplotlib/mpl-data/images/hand_large.gif,sha256=H5IHmVTvOqHQb9FZ_7g7AlPt9gv-zRq0L5_Q9B7OuvU,973
matplotlib/mpl-data/images/help.pdf,sha256=CeE978IMi0YWznWKjIT1R8IrP4KhZ0S7usPUvreSgcA,1813
matplotlib/mpl-data/images/help.png,sha256=s4pQrqaQ0py8I7vc9hv3BI3DO_tky-7YBMpaHuBDCBY,472
matplotlib/mpl-data/images/help.ppm,sha256=mVPvgwcddzCM-nGZd8Lnl_CorzDkRIXQE17b7qo8vlU,1741
matplotlib/mpl-data/images/help.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help_large.png,sha256=1IwEyWfGRgnoCWM-r9CJHEogTJVD5n1c8LXTK4AJ4RE,747
matplotlib/mpl-data/images/help_large.ppm,sha256=MiCSKp1Su88FXOi9MTtkQDA2srwbX3w5navi6cneAi4,6925
matplotlib/mpl-data/images/home.gif,sha256=NKuFM7tTtFngdfsOpJ4AxYTL8PYS5GWKAoiJjBMwLlU,666
matplotlib/mpl-data/images/home.pdf,sha256=e0e0pI-XRtPmvUCW2VTKL1DeYu1pvPmUUeRSgEbWmik,1737
matplotlib/mpl-data/images/home.png,sha256=IcFdAAUa6_A0qt8IO3I8p4rpXpQgAlJ8ndBECCh7C1w,468
matplotlib/mpl-data/images/home.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home_large.gif,sha256=k86PJCgED46sCFkOlUYHA0s5U7OjRsc517bpAtU2JSw,1422
matplotlib/mpl-data/images/home_large.png,sha256=uxS2O3tWOHh1iau7CaVV4ermIJaZ007ibm5Z3i8kXYg,790
matplotlib/mpl-data/images/matplotlib.pdf,sha256=BkSUf-2xoij-eXfpV2t7y1JFKG1zD1gtV6aAg3Xi_wE,22852
matplotlib/mpl-data/images/matplotlib.png,sha256=w8KLRYVa-voUZXa41hgJauQuoois23f3NFfdc72pUYY,1283
matplotlib/mpl-data/images/matplotlib.svg,sha256=QiTIcqlQwGaVPtHsEk-vtmJk1wxwZSvijhqBe_b9VCI,62087
matplotlib/mpl-data/images/matplotlib_128.ppm,sha256=IHPRWXpLFRq3Vb7UjiCkFrN_N86lSPcfrEGunST08d8,49167
matplotlib/mpl-data/images/matplotlib_large.png,sha256=ElRoue9grUqkZXJngk-nvh4GKfpvJ4gE69WryjCbX5U,3088
matplotlib/mpl-data/images/move.gif,sha256=FN52MptH4FZiwmV2rQgYCO2FvO3m5LtqYv8jk6Xbeyk,679
matplotlib/mpl-data/images/move.pdf,sha256=CXk3PGK9WL5t-5J-G2X5Tl-nb6lcErTBS5oUj2St6aU,1867
matplotlib/mpl-data/images/move.png,sha256=TmjR41IzSzxGbhiUcV64X0zx2BjrxbWH3cSKvnG2vzc,481
matplotlib/mpl-data/images/move.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move_large.gif,sha256=RMIAr-G9OOY7vWC04oN6qv5TAHJxhQGhLsw_bNsvWbg,951
matplotlib/mpl-data/images/move_large.png,sha256=Skjz2nW_RTA5s_0g88gdq2hrVbm6DOcfYW4Fu42Fn9U,767
matplotlib/mpl-data/images/qt4_editor_options.pdf,sha256=2qu6GVyBrJvVHxychQoJUiXPYxBylbH2j90QnytXs_w,1568
matplotlib/mpl-data/images/qt4_editor_options.png,sha256=EryQjQ5hh2dwmIxtzCFiMN1U6Tnd11p1CDfgH5ZHjNM,380
matplotlib/mpl-data/images/qt4_editor_options.svg,sha256=E00YoX7u4NrxMHm_L1TM8PDJ88bX5qRdCrO-Uj59CEA,1244
matplotlib/mpl-data/images/qt4_editor_options_large.png,sha256=-Pd-9Vh5aIr3PZa8O6Ge_BLo41kiEnpmkdDj8a11JkY,619
matplotlib/mpl-data/images/subplots.gif,sha256=QfhmUdcrko08-WtrzCJUjrVFDTvUZCJEXpARNtzEwkg,691
matplotlib/mpl-data/images/subplots.pdf,sha256=Q0syPMI5EvtgM-CE-YXKOkL9eFUAZnj_X2Ihoj6R4p4,1714
matplotlib/mpl-data/images/subplots.png,sha256=MUfCItq3_yzb9yRieGOglpn0Y74h8IA7m5i70B63iRc,445
matplotlib/mpl-data/images/subplots.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots_large.gif,sha256=Ff3ERmtVAaGP9i1QGUNnIIKac6LGuSW2Qf4DrockZSI,1350
matplotlib/mpl-data/images/subplots_large.png,sha256=Edu9SwVMQEXJZ5ogU5cyW7VLcwXJdhdf-EtxxmxdkIs,662
matplotlib/mpl-data/images/zoom_to_rect.gif,sha256=mTX6h9fh2W9zmvUYqeibK0TZ7qIMKOB1nAXMpD_jDys,696
matplotlib/mpl-data/images/zoom_to_rect.pdf,sha256=SEvPc24gfZRpl-dHv7nx8KkxPyU66Kq4zgQTvGFm9KA,1609
matplotlib/mpl-data/images/zoom_to_rect.png,sha256=aNz3QZBrIgxu9E-fFfaQweCVNitGuDUFoC27e5NU2L4,530
matplotlib/mpl-data/images/zoom_to_rect.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect_large.gif,sha256=nx5LUpTAH6ZynM3ZfZDS-wR87jbMUsUnyQ27NGkV0_c,1456
matplotlib/mpl-data/images/zoom_to_rect_large.png,sha256=V6pkxmm6VwFExdg_PEJWdK37HB7k3cE_corLa7RbUMk,1016
matplotlib/mpl-data/matplotlibrc,sha256=bGwDKQCzOKyaAB1EluRtR-diuNy3Bwo265DKo6E--qo,41027
matplotlib/mpl-data/sample_data/Minduka_Present_Blue_Pack.png,sha256=XnKGiCanpDKalQ5anvo5NZSAeDP7fyflzQAaivuc0IE,13634
matplotlib/mpl-data/sample_data/None_vs_nearest-pdf.png,sha256=5CPvcG3SDNfOXx39CMKHCNS9JKZ-fmOUwIfpppNXsQ0,106228
matplotlib/mpl-data/sample_data/README.txt,sha256=ABz19VBKfGewdY39QInG9Qccgn1MTYV3bT5Ph7TCy2Y,128
matplotlib/mpl-data/sample_data/aapl.npz,sha256=GssVYka_EccteiXbNRJJ5GsuqU7G8F597qX7srYXZsw,107503
matplotlib/mpl-data/sample_data/ada.png,sha256=X1hjJK1_1Nc8DN-EEhey3G7Sq8jBwQDKNSl4cCAE0uY,308313
matplotlib/mpl-data/sample_data/axes_grid/bivariate_normal.npy,sha256=DpWZ9udAh6ospYqneEa27D6EkRgORFwHosacZXVu98U,1880
matplotlib/mpl-data/sample_data/ct.raw.gz,sha256=LDvvgH-mycRQF2D29-w5MW94ZI0opvwKUoFI8euNpMk,256159
matplotlib/mpl-data/sample_data/data_x_x2_x3.csv,sha256=A0SU3buOUGhT-NI_6LQ6p70fFSIU3iLFdgzvzrKR6SE,132
matplotlib/mpl-data/sample_data/demodata.csv,sha256=MRybziqnyrqMCH9qG7Mr6BwcohIhftVG5dejXV2AX2M,659
matplotlib/mpl-data/sample_data/eeg.dat,sha256=KGVjFt8ABKz7p6XZirNfcxSTOpGGNuyA8JYErRKLRBc,25600
matplotlib/mpl-data/sample_data/embedding_in_wx3.xrc,sha256=cUqVw5vDHNSZoaO4J0ebZUf5SrJP36775abs7R9Bclg,2186
matplotlib/mpl-data/sample_data/goog.npz,sha256=QAkXzzDmtmT3sNqT18dFhg06qQCNqLfxYNLdEuajGLE,22845
matplotlib/mpl-data/sample_data/grace_hopper.jpg,sha256=qMptc0dlcDsJcoq0f-WfRz2Trjln_CTHwCiMPHrbcTA,61306
matplotlib/mpl-data/sample_data/grace_hopper.png,sha256=MCf0ju2kpC40srQ0xw4HEyOoKhLL4khP3jHfU9_dR7s,628280
matplotlib/mpl-data/sample_data/jacksboro_fault_dem.npz,sha256=1JP1CjPoKkQgSUxU0fyhU50Xe9wnqxkLxf5ukvYvtjc,174061
matplotlib/mpl-data/sample_data/logo2.png,sha256=ITxkJUsan2oqXgJDy6DJvwJ4aHviKeWGnxPkTjXUt7A,33541
matplotlib/mpl-data/sample_data/membrane.dat,sha256=q3lbQpIBpbtXXGNw1eFwkN_PwxdDGqk4L46IE2b0M1c,48000
matplotlib/mpl-data/sample_data/msft.csv,sha256=GArKb0O3DgKZRsKdJf6lX3rMSf-PCekIiBoLNdgF7Mk,3211
matplotlib/mpl-data/sample_data/percent_bachelors_degrees_women_usa.csv,sha256=TzoqamsV_N3d3lW7SKmj14zZVX4FOOg9jJcsC5U9pbA,5681
matplotlib/mpl-data/sample_data/s1045.ima.gz,sha256=MrQk1k9it-ccsk0p_VOTitVmTWCAVaZ6srKvQ2n4uJ4,33229
matplotlib/mpl-data/sample_data/topobathy.npz,sha256=AkTgMpFwLfRQJNy1ysvE89TLMNct-n_TccSsYcQrT78,45224
matplotlib/mpl-data/stylelib/Solarize_Light2.mplstyle,sha256=PECeO60wwJe2sSDvxapBJRuKGek0qLcoaN8qOX6tgNQ,1255
matplotlib/mpl-data/stylelib/_classic_test_patch.mplstyle,sha256=iopHpMaM3im_AK2aiHGuM2DKM5i9Kc84v6NQEoSb10Q,167
matplotlib/mpl-data/stylelib/bmh.mplstyle,sha256=-KbhaI859BITHIoyUZIfpQDjfckgLAlDAS_ydKsm6mc,712
matplotlib/mpl-data/stylelib/classic.mplstyle,sha256=0RjtrDi0vZOzWGnt9cme_At_9GqMwHzwEBCAH9OQZ7I,24511
matplotlib/mpl-data/stylelib/dark_background.mplstyle,sha256=-EGmoFm_35Zk7oRp29UalT56HsOSuJbYMeQGdAATnz4,477
matplotlib/mpl-data/stylelib/fast.mplstyle,sha256=yTa2YEIIP9xi5V_G0p2vSlxghuhNwjRi9gPECMxyRiM,288
matplotlib/mpl-data/stylelib/fivethirtyeight.mplstyle,sha256=WNUmAFuBPcqQPVgt6AS1ldy8Be2XO01N-1YQL__Q6ZY,832
matplotlib/mpl-data/stylelib/ggplot.mplstyle,sha256=xhjLwr8hiikEXKy8APMy0Bmvtz1g0WnG84gX7e9lArs,957
matplotlib/mpl-data/stylelib/grayscale.mplstyle,sha256=KCLg-pXpns9cnKDXKN2WH6mV41OH-6cbT-5zKQotSdw,526
matplotlib/mpl-data/stylelib/seaborn-bright.mplstyle,sha256=pDqn3-NUyVLvlfkYs8n8HzNZvmslVMChkeH-HtZuJIc,144
matplotlib/mpl-data/stylelib/seaborn-colorblind.mplstyle,sha256=eCSzFj5_2vR6n5qu1rHE46wvSVGZcdVqz85ov40ZsH8,148
matplotlib/mpl-data/stylelib/seaborn-dark-palette.mplstyle,sha256=p5ABKNQHRG7bk4HXqMQrRBjDlxGAo3RCXHdQmP7g-Ng,142
matplotlib/mpl-data/stylelib/seaborn-dark.mplstyle,sha256=I4xQ75vE5_9X4k0cNDiqhhnF3OcrZ2xlPX8Ll7OCkoE,667
matplotlib/mpl-data/stylelib/seaborn-darkgrid.mplstyle,sha256=2bXOSzS5gmPzRBrRmzVWyhg_7ZaBRQ6t_-O-cRuyZoA,670
matplotlib/mpl-data/stylelib/seaborn-deep.mplstyle,sha256=44dLcXjjRgR-6yaopgGRInaVgz3jk8VJVQTbBIcxRB0,142
matplotlib/mpl-data/stylelib/seaborn-muted.mplstyle,sha256=T4o3jvqKD_ImXDkp66XFOV_xrBVFUolJU34JDFk1Xkk,143
matplotlib/mpl-data/stylelib/seaborn-notebook.mplstyle,sha256=PcvZQbYrDdducrNlavBPmQ1g2minio_9GkUUFRdgtoM,382
matplotlib/mpl-data/stylelib/seaborn-paper.mplstyle,sha256=n0mboUp2C4Usq2j6tNWcu4TZ_YT4-kKgrYO0t-rz1yw,393
matplotlib/mpl-data/stylelib/seaborn-pastel.mplstyle,sha256=8nV8qRpbUrnFZeyE6VcQ1oRuZPLil2W74M2U37DNMOE,144
matplotlib/mpl-data/stylelib/seaborn-poster.mplstyle,sha256=dUaKqTE4MRfUq2rWVXbbou7kzD7Z9PE9Ko8aXLza8JA,403
matplotlib/mpl-data/stylelib/seaborn-talk.mplstyle,sha256=7FnBaBEdWBbncTm6_ER-EQVa_bZgU7dncgez-ez8R74,403
matplotlib/mpl-data/stylelib/seaborn-ticks.mplstyle,sha256=CITZmZFUFp40MK2Oz8tI8a7WRoCizQU9Z4J172YWfWw,665
matplotlib/mpl-data/stylelib/seaborn-white.mplstyle,sha256=WjJ6LEU6rlCwUugToawciAbKP9oERFHr9rfFlUrdTx0,665
matplotlib/mpl-data/stylelib/seaborn-whitegrid.mplstyle,sha256=ec4BjsNzmOvHptcJ3mdPxULF3S1_U1EUocuqfIpw-Nk,664
matplotlib/mpl-data/stylelib/seaborn.mplstyle,sha256=_Xu6qXKzi4b3GymCOB1b1-ykKTQ8xhDliZ8ezHGTiAs,1130
matplotlib/mpl-data/stylelib/tableau-colorblind10.mplstyle,sha256=BsirZVd1LmPWT4tBIz6loZPjZcInoQrIGfC7rvzqmJw,190
matplotlib/offsetbox.py,sha256=1oReQSHV50aRsHqHIa3Km234FXx8sUZRLdiGbdGu2zU,58812
matplotlib/patches.py,sha256=E7A9VRdd9nc1V_1VcBIs1bV-hPi78q-FsRLUdZH9AXQ,152779
matplotlib/path.py,sha256=ov2ghbmWhCV-PhxUPGEfsG409Nrkf9HP4u7oQSpGFtc,37640
matplotlib/patheffects.py,sha256=ERzaWP5RZTnaEVWx77c8oB-JQeMpVJtY-hBdRqkQ4ZM,13235
matplotlib/projections/__init__.py,sha256=4b447LtxkDz1nC1B9tb_J7cCpq1fjxwaEvzkHy-Y3KA,1816
matplotlib/projections/__pycache__/__init__.cpython-39.pyc,,
matplotlib/projections/__pycache__/geo.cpython-39.pyc,,
matplotlib/projections/__pycache__/polar.cpython-39.pyc,,
matplotlib/projections/geo.py,sha256=OrTW_dk4QqrTv9lYBean9uz24MBIWyWcnkADyTOdNuc,17690
matplotlib/projections/polar.py,sha256=hAZsPMOFZnEVbil76UIsRCjsurjNNHfm8CgLUFWv9wI,52540
matplotlib/pylab.py,sha256=VOs5onux2-yPGn0exAPsR75U9ty2hOzvg8Nz1K7TWBg,9595
matplotlib/pyplot.py,sha256=6ze3C1_-j90SUSr_9ByR68DKFHcnVL8EEgVu2opTXfM,111042
matplotlib/quiver.py,sha256=B3CGGxSNVLwOPx2IgyG2DCSPQHs1MAS0o9sL_4Mm7rY,48018
matplotlib/rcsetup.py,sha256=BhPaDqHrifbncC47epnKBtvlkk-MzhNp1HlVYgcgvF8,58589
matplotlib/sankey.py,sha256=3PygeZ3h19RFICw1hL6qt5TwRoBrP1AKSznWvKHc6Z4,36986
matplotlib/scale.py,sha256=_G5Aq3ta2vc2WOLuL3_UlnZ8XsXbGRrwBuqAG_OxCxU,25209
matplotlib/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/sphinxext/__pycache__/__init__.cpython-39.pyc,,
matplotlib/sphinxext/__pycache__/mathmpl.cpython-39.pyc,,
matplotlib/sphinxext/__pycache__/plot_directive.cpython-39.pyc,,
matplotlib/sphinxext/mathmpl.py,sha256=jTqwBfxLLGjx-4TwEzwYvi2WdCoJpqHfDcbLKrgcKss,4276
matplotlib/sphinxext/plot_directive.py,sha256=dAECzhJ1qVCq3nN3poGjKK2AAaAIwGikEHQIz_V0U0Y,26574
matplotlib/spines.py,sha256=Xl9hIsBHzYaGq79UQb3Amfwfrq1-ZNsiS4wpPwstbp8,21227
matplotlib/stackplot.py,sha256=fj4oqFbYA1G3RaTnV_Jic5mn5K3rza-JYp6jGR7e0yk,3917
matplotlib/streamplot.py,sha256=9zniqozPg42NfXk4bx_Ro2RlkYYuR2CEJwJfJkt7jtI,22641
matplotlib/style/__init__.py,sha256=EExOAUAq3u_rscUwkfKtZoEgLA5npmltCrYZOP9ftjw,67
matplotlib/style/__pycache__/__init__.cpython-39.pyc,,
matplotlib/style/__pycache__/core.cpython-39.pyc,,
matplotlib/style/core.py,sha256=yuCCRZCnSowGvgK9zPnQKk-dMdtwQxGF4c4m9LrmTAQ,8266
matplotlib/table.py,sha256=FXhhFOy_-ZkHwyphkjFBQku1WgDjR3iYlmV4Wvls0cY,26674
matplotlib/testing/__init__.py,sha256=MVV0ao_RHOKgDhpSN7QJBnyunje7tDeco-No_HtvvcQ,1428
matplotlib/testing/__pycache__/__init__.cpython-39.pyc,,
matplotlib/testing/__pycache__/compare.cpython-39.pyc,,
matplotlib/testing/__pycache__/conftest.cpython-39.pyc,,
matplotlib/testing/__pycache__/decorators.cpython-39.pyc,,
matplotlib/testing/__pycache__/disable_internet.cpython-39.pyc,,
matplotlib/testing/__pycache__/exceptions.cpython-39.pyc,,
matplotlib/testing/compare.py,sha256=6LjyhbzkKfAmf1sM_tfZvbxGBqn7-HgLzwQSNOppfZI,16979
matplotlib/testing/conftest.py,sha256=juolFb13qZ_jwO6DsOxkfCKuPmPsQwbY6Oznx76tLYo,4317
matplotlib/testing/decorators.py,sha256=ds1Js84nL5GY4e1hD0wqu1h6syDCHGj4a5u9Ed28aCM,18008
matplotlib/testing/disable_internet.py,sha256=ovCho7Nu6w-uoJeUPjJS7XGKJN0ktSNyF6NODaEyjb4,4925
matplotlib/testing/exceptions.py,sha256=72QmjiHG7DwxSvlJf8mei-hRit5AH3NKh0-osBo4YbY,138
matplotlib/testing/jpl_units/Duration.py,sha256=Leomw6a4XHddFgqMoYj63HfxV_-u6_MuQ3iaQ26TJcg,4946
matplotlib/testing/jpl_units/Epoch.py,sha256=XpQMTIOs6VIVzuFYxRcv6JUuz4kqVCb1nGfBpYYbeJA,6305
matplotlib/testing/jpl_units/EpochConverter.py,sha256=TAhtAyDHvvxDJL036DWEXrkZl_CwpVl2y5FNXloAoxo,4067
matplotlib/testing/jpl_units/StrConverter.py,sha256=36hFTYbCM3Dh9_QR82dBbHh_7DSpsEthpnJYRICLlIc,4107
matplotlib/testing/jpl_units/UnitDbl.py,sha256=sL0U984sJmka_0gzp6uhxSJuxnuh77aDAjtfCYdYJZ0,7793
matplotlib/testing/jpl_units/UnitDblConverter.py,sha256=M5h3lOB4IEysqnR7VbkjUnMPS7wBtrZYWKvU3JUaJtI,4145
matplotlib/testing/jpl_units/UnitDblFormatter.py,sha256=CRcbPtE3K0FlFJ4hkhi-SgQl1MUV-VlmIeOPIEPNwuI,681
matplotlib/testing/jpl_units/__init__.py,sha256=Bp4Lz_eqRuxNWO_l3VvhuurFZaUmZIlvcF3N0UBmzHc,2692
matplotlib/testing/jpl_units/__pycache__/Duration.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/Epoch.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/EpochConverter.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/StrConverter.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDbl.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblConverter.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblFormatter.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/__init__.cpython-39.pyc,,
matplotlib/tests/__init__.py,sha256=ns6SIKdszYNXD5h5PqKRCR06Z45H-sXrUX2VwujSRIM,366
matplotlib/tests/__pycache__/__init__.cpython-39.pyc,,
matplotlib/tests/__pycache__/conftest.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_afm.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_agg.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_agg_filter.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_animation.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_arrow_patches.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_artist.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_axes.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_bases.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_cairo.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_nbagg.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_pdf.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_pgf.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_ps.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_qt.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_svg.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_tk.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_tools.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_webagg.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backends_interactive.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_basic.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_bbox_tight.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_category.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_cbook.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_collections.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_colorbar.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_colors.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_compare_images.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_constrainedlayout.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_container.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_contour.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_cycles.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_dates.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_determinism.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_dviread.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_figure.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_font_manager.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_fontconfig_pattern.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_gridspec.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_image.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_legend.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_lines.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_marker.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_mathtext.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_matplotlib.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_mlab.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_offsetbox.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_patches.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_path.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_patheffects.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_pickle.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_png.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_preprocess_data.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_pyplot.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_quiver.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_rcparams.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_sankey.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_scale.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_simplification.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_skew.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_sphinxext.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_spines.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_streamplot.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_style.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_subplots.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_table.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_testing.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_texmanager.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_text.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_ticker.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_tightlayout.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_transforms.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_triangulation.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_ttconv.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_type1font.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_units.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_usetex.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_widgets.cpython-39.pyc,,
matplotlib/tests/conftest.py,sha256=QtpdWPUoXL_9F8WIytDc3--h0nPjbo8PToig7svIT1Y,258
matplotlib/tests/test_afm.py,sha256=DGVfvABg6FRmbAq2ldRhM2wlqNfVrmRtSz12MCyqDXk,3710
matplotlib/tests/test_agg.py,sha256=SC6WsFbq9Hs4dokpEqGqGPaFGS7zJpNxKRIRLUzD3Qk,7558
matplotlib/tests/test_agg_filter.py,sha256=sfntvGVUuCaGqU3DdOnTRXqq0P1afVqWuaV_ZEYh8kQ,969
matplotlib/tests/test_animation.py,sha256=WZ2kU7tjlDXL5Ryf89irQok884cWGtkVOJpNSjVyj3o,8916
matplotlib/tests/test_arrow_patches.py,sha256=3EB9HIfdLCg_Nvq8zxEH9LJAV5OUbo6yndbZ-lIVcnQ,5593
matplotlib/tests/test_artist.py,sha256=hMSfbg8Le0Li-vLEk2N9tZQtC1cUUE5Xn0O4CgYLugs,9418
matplotlib/tests/test_axes.py,sha256=dV5n-JwZ8iT3UopXvRoNXTEuZMexC-LwaskCU6X_dD4,217760
matplotlib/tests/test_backend_bases.py,sha256=I3Oiqk6Fm-fctj-aOxq5Av8Kqe3QLjlY-oTwTbdO6dw,3811
matplotlib/tests/test_backend_cairo.py,sha256=gIjbQesBuvIBdpzoXXSdRmq3E2axCVX4h1Qfv6PNeKM,1936
matplotlib/tests/test_backend_nbagg.py,sha256=dp9pYxWXN8ZBBjJcVE-1tJ61K7oeWtzcqIsAd7ABnfk,948
matplotlib/tests/test_backend_pdf.py,sha256=GUqvImL8-HgpZGG0Zb41AE3KqVwtt8i00xcPutzGM1w,6696
matplotlib/tests/test_backend_pgf.py,sha256=GAArsyhcLvWIHDtKgdv2kscRlgpNQ_oqyOXVGdVTw3Q,8636
matplotlib/tests/test_backend_ps.py,sha256=E3eheQhOHcUhub4IzcWcDVvvnnVC6bHn6tv1NAF_KA4,3633
matplotlib/tests/test_backend_qt.py,sha256=WUjiw3IZJf8BwUkDPfz8-XippUHT0Lcz6mMnGJkQ_h4,10663
matplotlib/tests/test_backend_svg.py,sha256=KCkU22vudHluXIE5SJna6ikoVeeQX8KkKYO6aQjlsVU,6049
matplotlib/tests/test_backend_tk.py,sha256=sLqemf0mAFMIpv2414wKSVInLekL8bvzzaeREhE2UBU,934
matplotlib/tests/test_backend_tools.py,sha256=C-B7NCkyWsQ5KzQEnI5Be16DsAHHZJU9P5v9--wsF-o,501
matplotlib/tests/test_backend_webagg.py,sha256=u-UvO04iqxZbFlUrNIfyD0KIAxEhSOyqlrOqxO4DnDw,702
matplotlib/tests/test_backends_interactive.py,sha256=fBN_V5G1C7DLmRcOaX0DsZSR0NN4MKeIhas8aghZBvM,5861
matplotlib/tests/test_basic.py,sha256=KRMwLx3J53urIx689lWHzkWkaWGTf0ZwhTyGrFDUfao,1268
matplotlib/tests/test_bbox_tight.py,sha256=GsSOpaRmGfDXAWChaeFarfD-uUISN7giJ_FjgtMDako,4618
matplotlib/tests/test_category.py,sha256=SKBDW4R4q_7A0K1H-q801mTivxBgcLXigw4E6-tb1P4,10011
matplotlib/tests/test_cbook.py,sha256=Z2Hp8UkeebK-BglQXm9WhiTpZC8CbSsSKUNy6hJoJbU,18684
matplotlib/tests/test_collections.py,sha256=S-uxTOvphALBmGYutD98dNP73hfD4Jt9PmtlaOMgd5E,24206
matplotlib/tests/test_colorbar.py,sha256=OW2F_D6Vs7jEV8HOBh2kv_iV21u7sdyIqJcRakwEkTg,22065
matplotlib/tests/test_colors.py,sha256=KYXZa1yFlkEGriaLMm-v2WnhROS2lvOUgseSHV2xC7w,33376
matplotlib/tests/test_compare_images.py,sha256=n3Uoukid0GcjyQpd6ZrqIY9u3RLNE2XAPWwtcHIsqto,3155
matplotlib/tests/test_constrainedlayout.py,sha256=9689kXb21xPGT3rqgZMxVT8HT4HXlzjO_aS6wq7b8kM,12981
matplotlib/tests/test_container.py,sha256=75Di6cic-JY7JbOnsaz7N6Hcm3KUAJKaKbCoVSDPAuU,576
matplotlib/tests/test_contour.py,sha256=G4dN6t8yrbpKp0aEI1VTrb-woyBKidGUqdDJ3b5D55U,12018
matplotlib/tests/test_cycles.py,sha256=mgYXt63ov7th7IwGpLEoCh-ImubEp6wltzTbDchfQYI,7112
matplotlib/tests/test_dates.py,sha256=1oDBr8xj2v_5tLHP_1rMkKA2nikoXSxK5u44ekL_NLA,32805
matplotlib/tests/test_determinism.py,sha256=bu8E_NMdeN0X2KRMStjAgK3tklL5SG_IqYu4hov6Z-4,4586
matplotlib/tests/test_dviread.py,sha256=kTk9Qv6q9Kk3fJcDAEWm35HF-sKsP6Ybec6N8jEHasE,2342
matplotlib/tests/test_figure.py,sha256=QSVkQoDe4fP52JGGuMhO8zb3aaZCKyYhqSd2CFi1gzo,15500
matplotlib/tests/test_font_manager.py,sha256=OrOm9CD4FnUnSzoQEpRNcsDktw7gNBz3h7YMPGkudjI,7217
matplotlib/tests/test_fontconfig_pattern.py,sha256=1MhBrF914MKxjbpdxKDqkfVyCZGnFBA06D2I6rqHRHo,2012
matplotlib/tests/test_gridspec.py,sha256=uodt74jFjux4CV4BDF1vnA-LHfIdlqZvsnrDv3MWJJk,868
matplotlib/tests/test_image.py,sha256=RMReqvIC2celG9NhUkJzv7Cl2us5Pc3f8I4uhxAzGQs,36254
matplotlib/tests/test_legend.py,sha256=oXMRtvzQgYMqNC0vPR8mhqQQl5n-IZRBSIZbr_N4hVk,20051
matplotlib/tests/test_lines.py,sha256=zbpM3DO0F-aclKrTE2JliUZpBR1UV5coR6I5OXOvJbg,5970
matplotlib/tests/test_marker.py,sha256=yGEoHoMji04-BPN-mmddO60pcpGwvL6FMO101xz70dg,4768
matplotlib/tests/test_mathtext.py,sha256=xqmDclBoB-7253xFBgf_yI9JxdkYZfuYgJ66HFcyqQA,13331
matplotlib/tests/test_matplotlib.py,sha256=DIBqISzUIYanSxNWJL9n2oob1dRLOOAr6TIz2BTWK1I,706
matplotlib/tests/test_mlab.py,sha256=ZpoL_7s2ARzt3uhq1InSVEWahB-svI0siIutKqyyUpM,89334
matplotlib/tests/test_offsetbox.py,sha256=f_I8sNuPmN4TXQAKGp5w3XQvnpWVSrHgEZr_0ZR7siE,6633
matplotlib/tests/test_patches.py,sha256=x5A87wDyR0_yziwRLoMlEEoYE0q-QZcHMDQaw73g_pg,18830
matplotlib/tests/test_path.py,sha256=X6kEMiXLHVdRaZ9w2JF2ID2_DtIVonQDByvXpLbKXSo,12736
matplotlib/tests/test_patheffects.py,sha256=I1C81Cms_QBK6cPg_xxKZBXKykZuGI5H2wwFtwnALZk,5207
matplotlib/tests/test_pickle.py,sha256=BahI72bPhtSEylUvRwKgksK4N5i2vjsQ1APvEw3Om-0,5653
matplotlib/tests/test_png.py,sha256=-ik0JJOAuZCED13VshP5RZsBOQpXu_Kv9I1oaZ_grxs,1705
matplotlib/tests/test_preprocess_data.py,sha256=vVnEzVU-jd1hAQwh-b9IbWo-S_pIg4I9NCpSQ1lLChI,10034
matplotlib/tests/test_pyplot.py,sha256=KUZ-9q9zS_nVj7PwzIw9_e1D6raWNCr79okKKpIddYQ,1649
matplotlib/tests/test_quiver.py,sha256=_dGa3bJmvNqcnBvFhGntvhBKRipROZUNOrSm-lMtc7Y,8087
matplotlib/tests/test_rcparams.py,sha256=HnXA81intnBhogrXbCuaAfOV2nyvbGvOCESYElue6C4,20896
matplotlib/tests/test_sankey.py,sha256=KdWhgZpEiCJ8o7yuFjnCgqFeodwKSCuuhV_mhIc04dM,310
matplotlib/tests/test_scale.py,sha256=kolkvXNDsSm7P44yrVDNuz25lve4iGGreaG-JC8-VIA,5895
matplotlib/tests/test_simplification.py,sha256=_dIImjiA0VHBEbOrGQluQKCS6SQFwEVvfL9pqIn_SHI,11048
matplotlib/tests/test_skew.py,sha256=hngaWfqV6zqZSnRmVadBvtMIrg1snTZvu3fsqv5YddU,6310
matplotlib/tests/test_sphinxext.py,sha256=VfFfCq5T3jaVN0Ut67SHpilUOzl-_zaJDnGxfGEtrgA,2022
matplotlib/tests/test_spines.py,sha256=5jGy42khH7ndaHRCgJONXBOvM5ZnrzOhPSR42IAUAA0,3132
matplotlib/tests/test_streamplot.py,sha256=_9iDNwQ0M1djl0eEX94QJtPYqCKFN5ncU_cIKJlm39w,3909
matplotlib/tests/test_style.py,sha256=FKsTSWx43sls_PqZzOEJ-ZRaiBmSmVQCgCxfGTlyiNc,5677
matplotlib/tests/test_subplots.py,sha256=RIk5cOGJmZwQz4UzZ_IEe1rzx4riAjO3bdltY5p1F6k,5992
matplotlib/tests/test_table.py,sha256=UEvwlff7Jdx7ezPMc8nAdaCO0u11PzNpCZqYGLDS3O4,5685
matplotlib/tests/test_testing.py,sha256=qXLI2etvF964zK9tviBk9XOQywzV5mtiNk7bVY02DG8,629
matplotlib/tests/test_texmanager.py,sha256=zCtJ3JnZNfP2AQNy7q2LQAgaflSe7S5htJkJNylQSGE,459
matplotlib/tests/test_text.py,sha256=49TKkDPy_tFRyxPLSp8-GgeS8TdU87JW24IUvA13NVM,20461
matplotlib/tests/test_ticker.py,sha256=niDdQLrvTIUa4x0jWchca_LJJFQc7EQVfBopnF-DjEM,49453
matplotlib/tests/test_tightlayout.py,sha256=Rg-Vw7VJSFxonhO5qnb2StPkMK7R4jpohMdFQbtPVSc,9685
matplotlib/tests/test_transforms.py,sha256=1CO_STcfUU4yo4bJZLdTpJ7dF3E285LVbG-OBpFUchA,24892
matplotlib/tests/test_triangulation.py,sha256=KWSRvg-_xE5Wz-_LAqB-EvI_5uy9isc9GTmhmi189Kw,45372
matplotlib/tests/test_ttconv.py,sha256=w6U4-5OiEuXsOskvMz4E-f9iqPoxCt8dIC2TNB7PwXo,655
matplotlib/tests/test_type1font.py,sha256=C0pCPBGOv49SR2xxDOq6LSXAEH_ZNvIWvr_jG-23Gmc,2097
matplotlib/tests/test_units.py,sha256=DhE6O7PZhMUf4FMoc1ByTcMgpS9-gMmjqsiQnqBBTKc,5710
matplotlib/tests/test_usetex.py,sha256=E1-9CP-k-wf7v-lf6_ilMYQjAMV3tHpnI-jP5IQcSeg,2065
matplotlib/tests/test_widgets.py,sha256=ijPT0E0-nS1J3kBpxoWSok1GbqlZf22J5Csbto3nl4U,17654
matplotlib/texmanager.py,sha256=pg8vbYD_uhfMsmElbAMJ402ZWcJJmFYCqvGAEbZYRRo,17708
matplotlib/text.py,sha256=bt04zgIbjJUOqdZ8gomc_zWF3UOYL5mHWaHb9OxxYHM,81090
matplotlib/textpath.py,sha256=dEZ1Vg4ZUI5M4iTHn-7I45KeR1FTit78jQ-3EyK6orc,17010
matplotlib/ticker.py,sha256=tiT2e6g63nDkAQciJV2YXF-cBctkrhAJ9JcE_jB6ndU,100196
matplotlib/tight_bbox.py,sha256=bQAOXPKfVWoBkGZbC0ckRcldgLJ8eBUe2IsT8UQpgaY,2590
matplotlib/tight_layout.py,sha256=BrNob3eytUkmtXtwgfD3jdRgNh_tIjDE89AZjAnhFdY,14586
matplotlib/transforms.py,sha256=cynqPdAO9gtYxmDBr_zr1VTSlIlbtOAwfNzGqsVV208,97572
matplotlib/tri/__init__.py,sha256=XMaejh88uov7Neu7MuYMyaNQqaxg49nXaiJfvjifrRM,256
matplotlib/tri/__pycache__/__init__.cpython-39.pyc,,
matplotlib/tri/__pycache__/triangulation.cpython-39.pyc,,
matplotlib/tri/__pycache__/tricontour.cpython-39.pyc,,
matplotlib/tri/__pycache__/trifinder.cpython-39.pyc,,
matplotlib/tri/__pycache__/triinterpolate.cpython-39.pyc,,
matplotlib/tri/__pycache__/tripcolor.cpython-39.pyc,,
matplotlib/tri/__pycache__/triplot.cpython-39.pyc,,
matplotlib/tri/__pycache__/trirefine.cpython-39.pyc,,
matplotlib/tri/__pycache__/tritools.cpython-39.pyc,,
matplotlib/tri/triangulation.py,sha256=NCY1Fx66H3MHrMA1-NW6G_shopBtFu1Ehm1f6XyRDBE,8315
matplotlib/tri/tricontour.py,sha256=4gKIAi1CFKWK8BCqAXMD6PlUKxnrC9GvzeQz0Yknvik,9794
matplotlib/tri/trifinder.py,sha256=umsDJX2kach9MVDvEirLdN9q31QQ9tdP67U71eFyGj8,3497
matplotlib/tri/triinterpolate.py,sha256=EP-ZwNC3HlOGB4jNaHGUCncXeO7LocvT2QhTr-jKCrk,64886
matplotlib/tri/tripcolor.py,sha256=H6DEToUjfU12hDkfekshYyuQoZc7Fa4r4W6MOR5d6Jw,5169
matplotlib/tri/triplot.py,sha256=aZ9O_VVLH0AOne31u11ltLlyVyhqKtyzec7WH3b3pkk,2857
matplotlib/tri/trirefine.py,sha256=uwAminHKuxgE0ZuwwllIpqK2Ww_q--VKzMicWUslA0s,13765
matplotlib/tri/tritools.py,sha256=BggcSXDVoc2mSyJp7wQ51qaw8WhdL7ianZ-D7ezkuC8,12398
matplotlib/ttconv.cp39-win_amd64.pyd,sha256=EnbrpryW_39Atlw9T92-2zatqXs0m1qzCfCFbSBIyW8,64000
matplotlib/type1font.py,sha256=-D1P81hhDUVNAHZ55-Df4cPc-3R8ycY_judVBZphgsk,12168
matplotlib/units.py,sha256=dgSbQsrJPRiKf5IJv-PM0xYMcvT3kle3gdPJsdjbRZk,7332
matplotlib/widgets.py,sha256=lMHpWCFdFTwSCEO5uL4fjcsoDQMzkzlWyVXnx80FVn4,94615
mpl_toolkits/axes_grid/__init__.py,sha256=VLlc0DaOkr9JumPa8W4zt9lGHp180ie8_WLPZVNSJMw,537
mpl_toolkits/axes_grid/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/anchored_artists.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/angle_helper.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_divider.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_grid.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_rgb.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_size.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/axis_artist.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/axisline_style.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/axislines.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/clip_path.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/colorbar.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/floating_axes.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/grid_finder.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/grid_helper_curvelinear.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/inset_locator.cpython-39.pyc,,
mpl_toolkits/axes_grid/__pycache__/parasite_axes.cpython-39.pyc,,
mpl_toolkits/axes_grid/anchored_artists.py,sha256=_F6-9iacZidb5JpJ8jCOZ9PdiZaR5qpfBjf-3VjTzNc,291
mpl_toolkits/axes_grid/angle_helper.py,sha256=Tb4Mb_NGkUdkisebe2dqfBdFmUZiSmGyUnftiSeSIls,51
mpl_toolkits/axes_grid/axes_divider.py,sha256=tJlPia3Z8xLq6uXehBwAlD_4ywMvRTTkM73qNnCpo7Q,178
mpl_toolkits/axes_grid/axes_grid.py,sha256=UPlVDwsze_w2aZeLaMg4WZVK3q2EvWePXTFZFvjCQz4,89
mpl_toolkits/axes_grid/axes_rgb.py,sha256=d3h2tImoPxvVtl8i4IBA_i1vBQykZDYizcNDGdjRltE,201
mpl_toolkits/axes_grid/axes_size.py,sha256=v4Nhxe7DVp1FkKX03DqJJ1aevDanDvgKT9r0ouDzTxw,48
mpl_toolkits/axes_grid/axis_artist.py,sha256=zUlJFUHueDsMtzLi_mK2_Wf-nSBQgiTsMOFpo_SngZ0,50
mpl_toolkits/axes_grid/axisline_style.py,sha256=lNVHXkFWhSWPXOOfF-wlVkDPzmzuStJyJzF-NS5Wf_g,53
mpl_toolkits/axes_grid/axislines.py,sha256=kVyhb6laiImmuNE53QTQh3kgxz0sO1mcSMpnqIdjylA,48
mpl_toolkits/axes_grid/clip_path.py,sha256=s-d36hUiy9I9BSr9wpxjgoAACCQrczHjw072JvArNvE,48
mpl_toolkits/axes_grid/colorbar.py,sha256=DckRf6tadLeTNjx-Zk1u3agnSGZgizDjd0Dxw1-GRdw,171
mpl_toolkits/axes_grid/floating_axes.py,sha256=i35OfV1ZMF-DkLo4bKmzFZP6LgCwXfdDKxYlGqjyKOM,52
mpl_toolkits/axes_grid/grid_finder.py,sha256=Y221c-Jh_AFd3Oolzvr0B1Zrz9MoXPatUABQdLsFdpw,50
mpl_toolkits/axes_grid/grid_helper_curvelinear.py,sha256=nRl_B-755X7UpVqqdwkqc_IwiTmM48z3eOMHuvJT5HI,62
mpl_toolkits/axes_grid/inset_locator.py,sha256=qqXlT8JWokP0kV-8NHknZDINtK-jbXfkutH_1tcRe_o,216
mpl_toolkits/axes_grid/parasite_axes.py,sha256=kCFtaRTd0O8ePL78GOYvhEKqn8rE9bk61v0kVgMb6UE,469
mpl_toolkits/axes_grid1/__init__.py,sha256=-lw0ZfG4XUpuAolCpXKFwtS3w1LJ1ZToSEC9OSmB-4Q,204
mpl_toolkits/axes_grid1/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/anchored_artists.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_divider.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_grid.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_rgb.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_size.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/colorbar.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/inset_locator.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/mpl_axes.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/parasite_axes.cpython-39.pyc,,
mpl_toolkits/axes_grid1/anchored_artists.py,sha256=F9396ifiWLedXEsivC3-rJjhYOxZ84dHYFhlRMVt4wE,21031
mpl_toolkits/axes_grid1/axes_divider.py,sha256=rZOhnowUzJ3mXb6LKkLPjDyxkz-EPJmA3MCafFa8VN8,29175
mpl_toolkits/axes_grid1/axes_grid.py,sha256=2ThJGeKXb9avu0KN3ujBliY2HV26IHC1daxy74t8ITk,27888
mpl_toolkits/axes_grid1/axes_rgb.py,sha256=NsMKkaMEeXd6N1AYVP1Pz-ulwNeof1pU4qkReoQ8xFc,6652
mpl_toolkits/axes_grid1/axes_size.py,sha256=YBC1PUmUK1-0c7cuqgJQBpDwVrbZQaWXyWadvuOO5bs,8602
mpl_toolkits/axes_grid1/colorbar.py,sha256=Y0-Uf9osbz31YBOTxxs_UFXbp6Xyc9Rais47tBMjTXI,27123
mpl_toolkits/axes_grid1/inset_locator.py,sha256=0BA8z3BiT78sS2-ksVe8iKS6KLxVv_NTV38NTLmRuu4,23675
mpl_toolkits/axes_grid1/mpl_axes.py,sha256=MJVYUN4YRtTWrq1wmyv_y61O002tiyFESmBSaJ8xkG4,4380
mpl_toolkits/axes_grid1/parasite_axes.py,sha256=ug0YRJiTtA3YVikdKMGj-qjoeOFnqNw2QnRab0Qp7wI,13570
mpl_toolkits/axisartist/__init__.py,sha256=2zsgjqTtP_NXv78MEaKabmfmkjA7yhy77pIcaR57YWs,748
mpl_toolkits/axisartist/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/angle_helper.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_divider.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_grid.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_rgb.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axis_artist.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axisline_style.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axislines.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/clip_path.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/floating_axes.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_finder.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_helper_curvelinear.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/parasite_axes.cpython-39.pyc,,
mpl_toolkits/axisartist/angle_helper.py,sha256=KZiXfi0IPcGp6JFXewj0VLrUycSfI93ezoimkti3wpY,12632
mpl_toolkits/axisartist/axes_divider.py,sha256=baPCBjM20SvAUeMjhvlS_cccRSM1y7ZKybtoW8upo2k,127
mpl_toolkits/axisartist/axes_grid.py,sha256=vfd_EXHuYQ7iIVK2FOm6inLhb7huZxtOSvFyOVW2GmU,610
mpl_toolkits/axisartist/axes_rgb.py,sha256=TpJCB8eA0wHZVXOxxfFoy1Tk_KFj68sZvo74doDeHYE,179
mpl_toolkits/axisartist/axis_artist.py,sha256=ejE-rQQP0bbopIJ82dn2i2zXh0siRXWaKP9lYOJR-5A,43660
mpl_toolkits/axisartist/axisline_style.py,sha256=bQ3M5gAxS7HbCC3oOQgrSyNWdk_FbvXfX24Eatge0UE,5098
mpl_toolkits/axisartist/axislines.py,sha256=mrQIihqbfBi2F-QgLRjZvHGM6rEojStBzojYpVNkjg0,20852
mpl_toolkits/axisartist/clip_path.py,sha256=LE_IIP0byNr5ELJlD8_8fsAh215MUDoK19-BISuFB80,3777
mpl_toolkits/axisartist/floating_axes.py,sha256=xSAJ5Myaa5Xm9DkOhJcdaPwzdPyvJnw5K9YPmDwB5ME,13122
mpl_toolkits/axisartist/grid_finder.py,sha256=K1Gae38Jt2OFfScZaKbxkLLcWrHDm-2bAVsl56SC1F0,10388
mpl_toolkits/axisartist/grid_helper_curvelinear.py,sha256=6QT9nTQ2dJ_NPsazS0Q0hC8W5IV7yzqYd_Fa8_gJXmI,14384
mpl_toolkits/axisartist/parasite_axes.py,sha256=1sQwBEYuXHpaEeObb7cXh0I1xWroYtcvFiEmwrzqK3w,447
mpl_toolkits/mplot3d/__init__.py,sha256=V2iPIP9VyRhoJsFWnQf5AkfyI1GSSP9H6hICEe9edJo,27
mpl_toolkits/mplot3d/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/mplot3d/__pycache__/art3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/__pycache__/axes3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/__pycache__/axis3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/__pycache__/proj3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/art3d.py,sha256=MAR_fFyBwe3VkwlOeWMccwDWo8RR1VqhUjuipLPuIFU,27647
mpl_toolkits/mplot3d/axes3d.py,sha256=iJ7LN-UKY7xMaGjYbeu-J1IuC54QX3irx7Kkh5gInzY,98609
mpl_toolkits/mplot3d/axis3d.py,sha256=UdxPxUW7GDoOpBy4uByLmLZJfle2wu0oYGenDHjjcLA,16737
mpl_toolkits/mplot3d/proj3d.py,sha256=5xwjogevdUCBaV9sx1RlNqO6fMCSIBi_1_uVZniy2pU,5499
mpl_toolkits/tests/__init__.py,sha256=iPdasxJf0vpIi11tQ98OVSQgS0UaPUyOEGGfAryAhIA,381
mpl_toolkits/tests/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/conftest.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/test_axes_grid.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/test_axes_grid1.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_angle_helper.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_axis_artist.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_axislines.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_clip_path.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_floating_axes.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_grid_finder.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_grid_helper_curvelinear.cpython-39.pyc,,
mpl_toolkits/tests/__pycache__/test_mplot3d.cpython-39.pyc,,
mpl_toolkits/tests/conftest.py,sha256=Ph6QZKdfAnkPwU52StddC-uwtCHfANKX1dDXgtX122g,213
mpl_toolkits/tests/test_axes_grid.py,sha256=SUnj9aImCEI-Q3_cM8e5BlizoNle9E1joKkE_q57OAg,2694
mpl_toolkits/tests/test_axes_grid1.py,sha256=pUw19PTds6WRlLON5U9ZWmt6HxV56Q6DKDPVGM34ysk,17113
mpl_toolkits/tests/test_axisartist_angle_helper.py,sha256=PwhJwBm2kk4uMyhdO5arQs8IlqSX2vN0hvUzI7YHqrw,5670
mpl_toolkits/tests/test_axisartist_axis_artist.py,sha256=N4Khx8jSxkoiMz3KvumodmFKHZUtdwtjkzxLWPSdyuw,3008
mpl_toolkits/tests/test_axisartist_axislines.py,sha256=4ujhndnDq-6albE4WwVFTVURfjG1xK1597smUGMxfFg,2331
mpl_toolkits/tests/test_axisartist_clip_path.py,sha256=afS3nvNqCgvDpJdg_MvbwydtSWv5b6ciP-Iq2aNcNFQ,1004
mpl_toolkits/tests/test_axisartist_floating_axes.py,sha256=xENnUpFU8EHPgnON6W1xqMVWIq8qxIzuGf1oMmSMFJo,4127
mpl_toolkits/tests/test_axisartist_grid_finder.py,sha256=e65sLudWFIXeU08Sis3_SI1JEI6eq8YqKj-80F_Nohk,325
mpl_toolkits/tests/test_axisartist_grid_helper_curvelinear.py,sha256=0AT6TdbxHGHlircgjNZyK2u1dw05YLXdOhLqXmsKyw4,7572
mpl_toolkits/tests/test_mplot3d.py,sha256=TojtKh1gbRWl-kCWQcYaFMOS89txDRd02O0NBJuu-vE,31991
pylab.py,sha256=u_By3CHla-rBMg57egFXIxZ3P_J6zEkSu_dNpBcH5pw,90
