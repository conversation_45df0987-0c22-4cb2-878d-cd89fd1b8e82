from dataclasses import dataclass, field
from typing import List
from apps.models.base_model import BaseEntity


@dataclass
class AbnormalInfoModel(BaseEntity):
    """
    异常信息
    """
    symptom = str  # 症状名称
    heart_beat = int  # 心搏数量
    duration = int  # 持续时间（分钟）
    definition = str  # 解释
    advice = str  # 建议
    daily_attention = str  # 日常注意


@dataclass
class ReportModel(BaseEntity):
    start_time = str  # 开始时间
    record_duration = str  # 记录时长
    effective_duration = str  # 有效时长
    analysis_time = str  # 分析时间
    signal_quality = str  # 信号质量
    ecg_age = int  # 心脏年龄
    hr_mean = int  # 平均心率
    hr_max = int  # 最大心率
    hr_min = int  # 最慢心率
    total_heart_beat = int  # 总心搏数
    abnormal_heart_beat = int  # 异常心搏数
    abnormal_infos: List[AbnormalInfoModel] = field(default_factory=list)  # 异常信息集合
    snb_count = int  # 心动过缓数量
    snb_duration = int  # 持续时间（分钟）
    emotion = int  # 情绪指数
    fatigue = int  # 疲劳指数
    hrv = int  # 心率变异性
    pressure = int  # 压力指数
    vitality = int  # 活力指数
    report_advice = str  # 报告建议
