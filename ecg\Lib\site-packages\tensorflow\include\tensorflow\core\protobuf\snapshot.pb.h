// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/snapshot.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
namespace tensorflow {
namespace data {
namespace experimental {
class SnapshotMetadataRecord;
class SnapshotMetadataRecordDefaultTypeInternal;
extern SnapshotMetadataRecordDefaultTypeInternal _SnapshotMetadataRecord_default_instance_;
class SnapshotRecord;
class SnapshotRecordDefaultTypeInternal;
extern SnapshotRecordDefaultTypeInternal _SnapshotRecord_default_instance_;
class SnapshotTensorMetadata;
class SnapshotTensorMetadataDefaultTypeInternal;
extern SnapshotTensorMetadataDefaultTypeInternal _SnapshotTensorMetadata_default_instance_;
class TensorMetadata;
class TensorMetadataDefaultTypeInternal;
extern TensorMetadataDefaultTypeInternal _TensorMetadata_default_instance_;
}  // namespace experimental
}  // namespace data
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::data::experimental::SnapshotMetadataRecord* Arena::CreateMaybeMessage<::tensorflow::data::experimental::SnapshotMetadataRecord>(Arena*);
template<> ::tensorflow::data::experimental::SnapshotRecord* Arena::CreateMaybeMessage<::tensorflow::data::experimental::SnapshotRecord>(Arena*);
template<> ::tensorflow::data::experimental::SnapshotTensorMetadata* Arena::CreateMaybeMessage<::tensorflow::data::experimental::SnapshotTensorMetadata>(Arena*);
template<> ::tensorflow::data::experimental::TensorMetadata* Arena::CreateMaybeMessage<::tensorflow::data::experimental::TensorMetadata>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace data {
namespace experimental {

// ===================================================================

class SnapshotRecord :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.SnapshotRecord) */ {
 public:
  SnapshotRecord();
  virtual ~SnapshotRecord();

  SnapshotRecord(const SnapshotRecord& from);
  SnapshotRecord(SnapshotRecord&& from) noexcept
    : SnapshotRecord() {
    *this = ::std::move(from);
  }

  inline SnapshotRecord& operator=(const SnapshotRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline SnapshotRecord& operator=(SnapshotRecord&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SnapshotRecord& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SnapshotRecord* internal_default_instance() {
    return reinterpret_cast<const SnapshotRecord*>(
               &_SnapshotRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SnapshotRecord& a, SnapshotRecord& b) {
    a.Swap(&b);
  }
  inline void Swap(SnapshotRecord* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SnapshotRecord* New() const final {
    return CreateMaybeMessage<SnapshotRecord>(nullptr);
  }

  SnapshotRecord* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SnapshotRecord>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SnapshotRecord& from);
  void MergeFrom(const SnapshotRecord& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SnapshotRecord* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.SnapshotRecord";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorFieldNumber = 1,
  };
  // repeated .tensorflow.TensorProto tensor = 1;
  int tensor_size() const;
  void clear_tensor();
  ::tensorflow::TensorProto* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensor();
  const ::tensorflow::TensorProto& tensor(int index) const;
  ::tensorflow::TensorProto* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensor() const;

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.SnapshotRecord)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensor_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
};
// -------------------------------------------------------------------

class SnapshotMetadataRecord :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.SnapshotMetadataRecord) */ {
 public:
  SnapshotMetadataRecord();
  virtual ~SnapshotMetadataRecord();

  SnapshotMetadataRecord(const SnapshotMetadataRecord& from);
  SnapshotMetadataRecord(SnapshotMetadataRecord&& from) noexcept
    : SnapshotMetadataRecord() {
    *this = ::std::move(from);
  }

  inline SnapshotMetadataRecord& operator=(const SnapshotMetadataRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline SnapshotMetadataRecord& operator=(SnapshotMetadataRecord&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SnapshotMetadataRecord& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SnapshotMetadataRecord* internal_default_instance() {
    return reinterpret_cast<const SnapshotMetadataRecord*>(
               &_SnapshotMetadataRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SnapshotMetadataRecord& a, SnapshotMetadataRecord& b) {
    a.Swap(&b);
  }
  inline void Swap(SnapshotMetadataRecord* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SnapshotMetadataRecord* New() const final {
    return CreateMaybeMessage<SnapshotMetadataRecord>(nullptr);
  }

  SnapshotMetadataRecord* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SnapshotMetadataRecord>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SnapshotMetadataRecord& from);
  void MergeFrom(const SnapshotMetadataRecord& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SnapshotMetadataRecord* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.SnapshotMetadataRecord";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDtypeFieldNumber = 5,
    kGraphHashFieldNumber = 1,
    kRunIdFieldNumber = 2,
    kCreationTimestampFieldNumber = 3,
    kVersionFieldNumber = 4,
    kNumElementsFieldNumber = 6,
    kFinalizedFieldNumber = 1000,
  };
  // repeated .tensorflow.DataType dtype = 5;
  int dtype_size() const;
  void clear_dtype();
  ::tensorflow::DataType dtype(int index) const;
  void set_dtype(int index, ::tensorflow::DataType value);
  void add_dtype(::tensorflow::DataType value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& dtype() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_dtype();

  // string graph_hash = 1;
  void clear_graph_hash();
  const std::string& graph_hash() const;
  void set_graph_hash(const std::string& value);
  void set_graph_hash(std::string&& value);
  void set_graph_hash(const char* value);
  void set_graph_hash(const char* value, size_t size);
  std::string* mutable_graph_hash();
  std::string* release_graph_hash();
  void set_allocated_graph_hash(std::string* graph_hash);

  // string run_id = 2;
  void clear_run_id();
  const std::string& run_id() const;
  void set_run_id(const std::string& value);
  void set_run_id(std::string&& value);
  void set_run_id(const char* value);
  void set_run_id(const char* value, size_t size);
  std::string* mutable_run_id();
  std::string* release_run_id();
  void set_allocated_run_id(std::string* run_id);

  // int64 creation_timestamp = 3;
  void clear_creation_timestamp();
  ::PROTOBUF_NAMESPACE_ID::int64 creation_timestamp() const;
  void set_creation_timestamp(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 version = 4;
  void clear_version();
  ::PROTOBUF_NAMESPACE_ID::int64 version() const;
  void set_version(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 num_elements = 6;
  void clear_num_elements();
  ::PROTOBUF_NAMESPACE_ID::int64 num_elements() const;
  void set_num_elements(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool finalized = 1000;
  void clear_finalized();
  bool finalized() const;
  void set_finalized(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.SnapshotMetadataRecord)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> dtype_;
  mutable std::atomic<int> _dtype_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_hash_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr run_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 creation_timestamp_;
  ::PROTOBUF_NAMESPACE_ID::int64 version_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_elements_;
  bool finalized_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
};
// -------------------------------------------------------------------

class TensorMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.TensorMetadata) */ {
 public:
  TensorMetadata();
  virtual ~TensorMetadata();

  TensorMetadata(const TensorMetadata& from);
  TensorMetadata(TensorMetadata&& from) noexcept
    : TensorMetadata() {
    *this = ::std::move(from);
  }

  inline TensorMetadata& operator=(const TensorMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorMetadata& operator=(TensorMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TensorMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorMetadata* internal_default_instance() {
    return reinterpret_cast<const TensorMetadata*>(
               &_TensorMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TensorMetadata& a, TensorMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorMetadata* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TensorMetadata* New() const final {
    return CreateMaybeMessage<TensorMetadata>(nullptr);
  }

  TensorMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TensorMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TensorMetadata& from);
  void MergeFrom(const TensorMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.TensorMetadata";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorShapeFieldNumber = 2,
    kTensorSizeBytesFieldNumber = 3,
  };
  // .tensorflow.TensorShapeProto tensor_shape = 2;
  bool has_tensor_shape() const;
  void clear_tensor_shape();
  const ::tensorflow::TensorShapeProto& tensor_shape() const;
  ::tensorflow::TensorShapeProto* release_tensor_shape();
  ::tensorflow::TensorShapeProto* mutable_tensor_shape();
  void set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape);

  // int64 tensor_size_bytes = 3;
  void clear_tensor_size_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 tensor_size_bytes() const;
  void set_tensor_size_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.TensorMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::TensorShapeProto* tensor_shape_;
  ::PROTOBUF_NAMESPACE_ID::int64 tensor_size_bytes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
};
// -------------------------------------------------------------------

class SnapshotTensorMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.SnapshotTensorMetadata) */ {
 public:
  SnapshotTensorMetadata();
  virtual ~SnapshotTensorMetadata();

  SnapshotTensorMetadata(const SnapshotTensorMetadata& from);
  SnapshotTensorMetadata(SnapshotTensorMetadata&& from) noexcept
    : SnapshotTensorMetadata() {
    *this = ::std::move(from);
  }

  inline SnapshotTensorMetadata& operator=(const SnapshotTensorMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline SnapshotTensorMetadata& operator=(SnapshotTensorMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SnapshotTensorMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SnapshotTensorMetadata* internal_default_instance() {
    return reinterpret_cast<const SnapshotTensorMetadata*>(
               &_SnapshotTensorMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SnapshotTensorMetadata& a, SnapshotTensorMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(SnapshotTensorMetadata* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SnapshotTensorMetadata* New() const final {
    return CreateMaybeMessage<SnapshotTensorMetadata>(nullptr);
  }

  SnapshotTensorMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SnapshotTensorMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SnapshotTensorMetadata& from);
  void MergeFrom(const SnapshotTensorMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SnapshotTensorMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.SnapshotTensorMetadata";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorMetadataFieldNumber = 1,
  };
  // repeated .tensorflow.data.experimental.TensorMetadata tensor_metadata = 1;
  int tensor_metadata_size() const;
  void clear_tensor_metadata();
  ::tensorflow::data::experimental::TensorMetadata* mutable_tensor_metadata(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::experimental::TensorMetadata >*
      mutable_tensor_metadata();
  const ::tensorflow::data::experimental::TensorMetadata& tensor_metadata(int index) const;
  ::tensorflow::data::experimental::TensorMetadata* add_tensor_metadata();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::experimental::TensorMetadata >&
      tensor_metadata() const;

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.SnapshotTensorMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::experimental::TensorMetadata > tensor_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SnapshotRecord

// repeated .tensorflow.TensorProto tensor = 1;
inline int SnapshotRecord::tensor_size() const {
  return tensor_.size();
}
inline ::tensorflow::TensorProto* SnapshotRecord::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.SnapshotRecord.tensor)
  return tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
SnapshotRecord::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.experimental.SnapshotRecord.tensor)
  return &tensor_;
}
inline const ::tensorflow::TensorProto& SnapshotRecord::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotRecord.tensor)
  return tensor_.Get(index);
}
inline ::tensorflow::TensorProto* SnapshotRecord::add_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.data.experimental.SnapshotRecord.tensor)
  return tensor_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
SnapshotRecord::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.experimental.SnapshotRecord.tensor)
  return tensor_;
}

// -------------------------------------------------------------------

// SnapshotMetadataRecord

// string graph_hash = 1;
inline void SnapshotMetadataRecord::clear_graph_hash() {
  graph_hash_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& SnapshotMetadataRecord::graph_hash() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
  return graph_hash_.GetNoArena();
}
inline void SnapshotMetadataRecord::set_graph_hash(const std::string& value) {
  
  graph_hash_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
}
inline void SnapshotMetadataRecord::set_graph_hash(std::string&& value) {
  
  graph_hash_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
}
inline void SnapshotMetadataRecord::set_graph_hash(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  graph_hash_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
}
inline void SnapshotMetadataRecord::set_graph_hash(const char* value, size_t size) {
  
  graph_hash_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
}
inline std::string* SnapshotMetadataRecord::mutable_graph_hash() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
  return graph_hash_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* SnapshotMetadataRecord::release_graph_hash() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
  
  return graph_hash_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void SnapshotMetadataRecord::set_allocated_graph_hash(std::string* graph_hash) {
  if (graph_hash != nullptr) {
    
  } else {
    
  }
  graph_hash_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), graph_hash);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
}

// string run_id = 2;
inline void SnapshotMetadataRecord::clear_run_id() {
  run_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& SnapshotMetadataRecord::run_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
  return run_id_.GetNoArena();
}
inline void SnapshotMetadataRecord::set_run_id(const std::string& value) {
  
  run_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
}
inline void SnapshotMetadataRecord::set_run_id(std::string&& value) {
  
  run_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
}
inline void SnapshotMetadataRecord::set_run_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  run_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
}
inline void SnapshotMetadataRecord::set_run_id(const char* value, size_t size) {
  
  run_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
}
inline std::string* SnapshotMetadataRecord::mutable_run_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
  return run_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* SnapshotMetadataRecord::release_run_id() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
  
  return run_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void SnapshotMetadataRecord::set_allocated_run_id(std::string* run_id) {
  if (run_id != nullptr) {
    
  } else {
    
  }
  run_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), run_id);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
}

// int64 creation_timestamp = 3;
inline void SnapshotMetadataRecord::clear_creation_timestamp() {
  creation_timestamp_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SnapshotMetadataRecord::creation_timestamp() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.creation_timestamp)
  return creation_timestamp_;
}
inline void SnapshotMetadataRecord::set_creation_timestamp(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  creation_timestamp_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.creation_timestamp)
}

// int64 version = 4;
inline void SnapshotMetadataRecord::clear_version() {
  version_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SnapshotMetadataRecord::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.version)
  return version_;
}
inline void SnapshotMetadataRecord::set_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.version)
}

// repeated .tensorflow.DataType dtype = 5;
inline int SnapshotMetadataRecord::dtype_size() const {
  return dtype_.size();
}
inline void SnapshotMetadataRecord::clear_dtype() {
  dtype_.Clear();
}
inline ::tensorflow::DataType SnapshotMetadataRecord::dtype(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_.Get(index));
}
inline void SnapshotMetadataRecord::set_dtype(int index, ::tensorflow::DataType value) {
  dtype_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.dtype)
}
inline void SnapshotMetadataRecord::add_dtype(::tensorflow::DataType value) {
  dtype_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.data.experimental.SnapshotMetadataRecord.dtype)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
SnapshotMetadataRecord::dtype() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.experimental.SnapshotMetadataRecord.dtype)
  return dtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
SnapshotMetadataRecord::mutable_dtype() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.experimental.SnapshotMetadataRecord.dtype)
  return &dtype_;
}

// int64 num_elements = 6;
inline void SnapshotMetadataRecord::clear_num_elements() {
  num_elements_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SnapshotMetadataRecord::num_elements() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.num_elements)
  return num_elements_;
}
inline void SnapshotMetadataRecord::set_num_elements(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_elements_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.num_elements)
}

// bool finalized = 1000;
inline void SnapshotMetadataRecord::clear_finalized() {
  finalized_ = false;
}
inline bool SnapshotMetadataRecord::finalized() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.finalized)
  return finalized_;
}
inline void SnapshotMetadataRecord::set_finalized(bool value) {
  
  finalized_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.finalized)
}

// -------------------------------------------------------------------

// TensorMetadata

// .tensorflow.TensorShapeProto tensor_shape = 2;
inline bool TensorMetadata::has_tensor_shape() const {
  return this != internal_default_instance() && tensor_shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& TensorMetadata::tensor_shape() const {
  const ::tensorflow::TensorShapeProto* p = tensor_shape_;
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.TensorMetadata.tensor_shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::release_tensor_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.TensorMetadata.tensor_shape)
  
  ::tensorflow::TensorShapeProto* temp = tensor_shape_;
  tensor_shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::mutable_tensor_shape() {
  
  if (tensor_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    tensor_shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.TensorMetadata.tensor_shape)
  return tensor_shape_;
}
inline void TensorMetadata::set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_shape_);
  }
  if (tensor_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_shape)->GetArena();
    if (message_arena != submessage_arena) {
      tensor_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_shape, submessage_arena);
    }
    
  } else {
    
  }
  tensor_shape_ = tensor_shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.TensorMetadata.tensor_shape)
}

// int64 tensor_size_bytes = 3;
inline void TensorMetadata::clear_tensor_size_bytes() {
  tensor_size_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorMetadata::tensor_size_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.TensorMetadata.tensor_size_bytes)
  return tensor_size_bytes_;
}
inline void TensorMetadata::set_tensor_size_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  tensor_size_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.TensorMetadata.tensor_size_bytes)
}

// -------------------------------------------------------------------

// SnapshotTensorMetadata

// repeated .tensorflow.data.experimental.TensorMetadata tensor_metadata = 1;
inline int SnapshotTensorMetadata::tensor_metadata_size() const {
  return tensor_metadata_.size();
}
inline void SnapshotTensorMetadata::clear_tensor_metadata() {
  tensor_metadata_.Clear();
}
inline ::tensorflow::data::experimental::TensorMetadata* SnapshotTensorMetadata::mutable_tensor_metadata(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata)
  return tensor_metadata_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::experimental::TensorMetadata >*
SnapshotTensorMetadata::mutable_tensor_metadata() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata)
  return &tensor_metadata_;
}
inline const ::tensorflow::data::experimental::TensorMetadata& SnapshotTensorMetadata::tensor_metadata(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata)
  return tensor_metadata_.Get(index);
}
inline ::tensorflow::data::experimental::TensorMetadata* SnapshotTensorMetadata::add_tensor_metadata() {
  // @@protoc_insertion_point(field_add:tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata)
  return tensor_metadata_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::experimental::TensorMetadata >&
SnapshotTensorMetadata::tensor_metadata() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata)
  return tensor_metadata_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace experimental
}  // namespace data
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto
