CREATE TABLE IF NOT EXISTS t_data_ecg_detail_{data_date} (
    id BIGINT AUTO_INCREMENT,
    union_id VARCHAR(255),
    data_ecg_id BIGINT,
    check_date DATETIME,
    disease_code VARCHAR(255) COMMENT '疾病代码',
    total_episodes INT COMMENT '总阵数',
    total_duration FLOAT COMMENT '总时长(秒)',
    fastest_hr INT COMMENT '最快心率(次/分钟)',
    longest_duration FLOAT COMMENT '最长持续(秒)',
    longest_rr FLOAT COMMENT '最长RR(秒)',
    single_count INT COMMENT '单发个数',
    pair_count INT COMMENT '成对阵数',
    bigeminy_count INT COMMENT '二联律次数',
    trigeminy_count INT COMMENT '三联律次数',
    run_count INT COMMENT '连发阵数',
    max_consecutive INT COMMENT '连发最多个数',
    fastest_run_hr FLOAT COMMENT '连发最快心率(次/分钟)',
    slowest_run_hr FLOAT COMMENT '连发最慢心率(次/分钟)',
    hr_total_count INT COMMENT '心搏总数',

    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id)
)