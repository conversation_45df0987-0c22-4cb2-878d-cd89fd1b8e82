"""
内存监控和GPU管理工具
"""
import os
import gc
import time
import threading
import psutil
import warnings
from typing import Dict, Any, Optional
from functools import wraps

try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False

from apps.utils.logger_helper import Logger

# 禁用TensorFlow警告
if TF_AVAILABLE:
    tf.get_logger().setLevel('ERROR')
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# 禁用其他警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.start_time = time.time()
        self.peak_memory = 0
        self.monitoring = False
        self.monitor_thread = None
        self._lock = threading.Lock()
        
    def start_monitoring(self, interval: float = 5.0):
        """开始内存监控"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop, 
            args=(interval,), 
            daemon=True
        )
        self.monitor_thread.start()
        Logger().info("Memory monitoring started")
    
    def stop_monitoring(self):
        """停止内存监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        Logger().info("Memory monitoring stopped")
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        while self.monitoring:
            try:
                memory_info = self.get_memory_info()
                current_memory = memory_info['memory_percent']
                
                # 更新峰值内存
                if current_memory > self.peak_memory:
                    self.peak_memory = current_memory
                
                # 如果内存使用率过高，记录警告
                if current_memory > 85:
                    Logger().warning(f"High memory usage: {current_memory:.1f}%")
                    
                    # 如果超过90%，触发紧急清理
                    if current_memory > 90:
                        Logger().error(f"Critical memory usage: {current_memory:.1f}%, triggering emergency cleanup")
                        self.emergency_cleanup()
                
                time.sleep(interval)
                
            except Exception as e:
                Logger().error(f"Memory monitoring error: {e}")
                time.sleep(interval)
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取内存信息"""
        try:
            # 系统内存信息
            system_memory = psutil.virtual_memory()
            
            # 进程内存信息
            process_memory = self.process.memory_info()
            
            # GPU内存信息（如果可用）
            gpu_info = {}
            if TF_AVAILABLE:
                try:
                    gpus = tf.config.list_physical_devices('GPU')
                    if gpus:
                        # 获取GPU内存使用情况（这个API可能不是所有版本都支持）
                        try:
                            gpu_details = tf.config.experimental.get_memory_info('GPU:0')
                            gpu_info = {
                                'gpu_current': gpu_details['current'] / (1024**3),  # GB
                                'gpu_peak': gpu_details['peak'] / (1024**3)  # GB
                            }
                        except:
                            gpu_info = {'gpu_available': len(gpus)}
                except:
                    pass
            
            return {
                'memory_percent': system_memory.percent,
                'memory_available_gb': system_memory.available / (1024**3),
                'memory_used_gb': system_memory.used / (1024**3),
                'process_memory_mb': process_memory.rss / (1024**2),
                'process_memory_percent': (process_memory.rss / system_memory.total) * 100,
                'peak_memory_percent': self.peak_memory,
                **gpu_info
            }
        except Exception as e:
            Logger().error(f"Failed to get memory info: {e}")
            return {'memory_percent': 0}
    
    def emergency_cleanup(self):
        """紧急内存清理"""
        try:
            Logger().info("Starting emergency memory cleanup")
            
            # 强制垃圾回收
            for _ in range(5):
                gc.collect()
            
            # 清理TensorFlow会话和GPU内存
            if TF_AVAILABLE:
                tf.keras.backend.clear_session()
                
                # 尝试清理GPU内存
                try:
                    gpus = tf.config.list_physical_devices('GPU')
                    if gpus:
                        # 重置内存统计（如果支持）
                        try:
                            tf.config.experimental.reset_memory_stats('GPU:0')
                        except:
                            pass
                except:
                    pass
            
            # 等待一下让系统回收内存
            time.sleep(1)
            
            memory_after = self.get_memory_info()
            Logger().info(f"Emergency cleanup completed, memory usage: {memory_after['memory_percent']:.1f}%")
            
        except Exception as e:
            Logger().error(f"Emergency cleanup failed: {e}")


class GPUManager:
    """GPU内存管理器"""
    
    @staticmethod
    def configure_gpu():
        """配置GPU内存增长"""
        if not TF_AVAILABLE:
            return False
            
        try:
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                for gpu in gpus:
                    # 启用内存增长
                    tf.config.experimental.set_memory_growth(gpu, True)
                    
                Logger().info(f"Configured {len(gpus)} GPU(s) with memory growth")
                return True
            else:
                Logger().info("No GPU devices found")
                return False
                
        except Exception as e:
            Logger().error(f"GPU configuration failed: {e}")
            return False
    
    @staticmethod
    def clear_gpu_memory():
        """清理GPU内存"""
        if not TF_AVAILABLE:
            return
            
        try:
            tf.keras.backend.clear_session()
            
            # 强制垃圾回收
            gc.collect()
            
            Logger().debug("GPU memory cleared")
            
        except Exception as e:
            Logger().error(f"GPU memory cleanup failed: {e}")
    
    @staticmethod
    def get_gpu_info() -> Dict[str, Any]:
        """获取GPU信息"""
        if not TF_AVAILABLE:
            return {'gpu_available': False}
            
        try:
            gpus = tf.config.list_physical_devices('GPU')
            if not gpus:
                return {'gpu_available': False}
            
            gpu_info = {
                'gpu_available': True,
                'gpu_count': len(gpus),
                'gpu_names': [gpu.name for gpu in gpus]
            }
            
            # 尝试获取内存信息
            try:
                memory_info = tf.config.experimental.get_memory_info('GPU:0')
                gpu_info.update({
                    'gpu_memory_current_gb': memory_info['current'] / (1024**3),
                    'gpu_memory_peak_gb': memory_info['peak'] / (1024**3)
                })
            except:
                pass
            
            return gpu_info
            
        except Exception as e:
            Logger().error(f"Failed to get GPU info: {e}")
            return {'gpu_available': False}


# 全局内存监控器实例
memory_monitor = MemoryMonitor()


def monitor_memory(func):
    """内存监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 记录开始时的内存状态
        start_memory = memory_monitor.get_memory_info()
        start_time = time.time()
        
        try:
            # 执行函数
            result = func(*args, **kwargs)
            
            # 记录结束时的内存状态
            end_memory = memory_monitor.get_memory_info()
            end_time = time.time()
            
            # 计算内存变化
            memory_diff = end_memory['memory_percent'] - start_memory['memory_percent']
            execution_time = end_time - start_time
            
            # 如果内存增长过多，记录警告
            if memory_diff > 5:  # 内存增长超过5%
                Logger().warning(
                    f"Function {func.__name__} caused significant memory increase: "
                    f"{memory_diff:.1f}% in {execution_time:.2f}s"
                )
            
            # 如果执行后内存使用率过高，触发清理
            if end_memory['memory_percent'] > 85:
                Logger().warning(f"High memory usage after {func.__name__}: {end_memory['memory_percent']:.1f}%")
                gc.collect()
                
                if TF_AVAILABLE:
                    tf.keras.backend.clear_session()
            
            return result
            
        except Exception as e:
            # 出错时也进行内存清理
            Logger().error(f"Function {func.__name__} failed: {e}")
            gc.collect()
            if TF_AVAILABLE:
                tf.keras.backend.clear_session()
            raise
    
    return wrapper


def get_memory_monitor() -> MemoryMonitor:
    """获取全局内存监控器"""
    return memory_monitor


def start_memory_monitoring(interval: float = 10.0):
    """启动内存监控"""
    memory_monitor.start_monitoring(interval)


def stop_memory_monitoring():
    """停止内存监控"""
    memory_monitor.stop_monitoring()
