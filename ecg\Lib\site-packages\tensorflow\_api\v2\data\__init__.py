# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""`tf.data.Dataset` API for input pipelines.

See [Importing Data](https://tensorflow.org/guide/data) for an overview.

"""

from __future__ import print_function as _print_function

import sys as _sys

from . import experimental
from tensorflow.python.data.experimental.ops.threading_options import ThreadingOptions
from tensorflow.python.data.ops.dataset_ops import AUTOTUNE
from tensorflow.python.data.ops.dataset_ops import DatasetSpec
from tensorflow.python.data.ops.dataset_ops import DatasetV2 as Dataset
from tensorflow.python.data.ops.dataset_ops import INFINITE as INFINITE_CARDINALITY
from tensorflow.python.data.ops.dataset_ops import Options
from tensorflow.python.data.ops.dataset_ops import UNKNOWN as UNKNOWN_CARDINALITY
from tensorflow.python.data.ops.iterator_ops import IteratorBase as Iterator
from tensorflow.python.data.ops.iterator_ops import IteratorSpec
from tensorflow.python.data.ops.readers import FixedLengthRecordDatasetV2 as FixedLengthRecordDataset
from tensorflow.python.data.ops.readers import TFRecordDatasetV2 as TFRecordDataset
from tensorflow.python.data.ops.readers import TextLineDatasetV2 as TextLineDataset

del _print_function
