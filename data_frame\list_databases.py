import mysql.connector
import json

# 要查询的特定es_key
TARGET_ES_KEY = "CUSTOMER19054257726245560321078/20250401152805"

def list_all_databases():
    """列出服务器上的所有数据库"""
    connection = None
    try:
        # 数据库连接配置 - 不指定特定数据库
        db_config = {
            'host': '**************',
            'port': 3308,
            'user': 'ai',
            'password': 'z8^#g4r4mz'
        }
        
        print(f"尝试连接到数据库服务器: {db_config['host']}:{db_config['port']}")
        
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 查询所有数据库
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall()]
        
        print(f"\n找到 {len(databases)} 个数据库:")
        for db in databases:
            print(f"- {db}")
            
        return databases
    except Exception as e:
        print(f"\n查询数据库服务器时出错: {e}")
        return []
    finally:
        if connection:
            connection.close()

def search_es_key_in_all_databases(es_key):
    """在所有数据库中搜索特定es_key"""
    # 首先获取所有数据库
    databases = list_all_databases()
    if not databases:
        print("无法获取数据库列表")
        return
    
    print(f"\n开始在所有数据库中搜索 es_key: {es_key}")
    found_in_databases = []
    
    for db_name in databases:
        # 系统数据库通常不包含应用数据，可以跳过
        if db_name in ['information_schema', 'mysql', 'performance_schema', 'sys']:
            print(f"跳过系统数据库: {db_name}")
            continue
            
        print(f"\n正在查询数据库: {db_name}")
        connection = None
        try:
            # 连接到当前数据库
            db_config = {
                'host': '**************',
                'port': 3308,
                'user': 'ai',
                'password': 'z8^#g4r4mz',
                'database': db_name
            }
            
            connection = mysql.connector.connect(**db_config)
            cursor = connection.cursor(dictionary=True)
            
            # 首先列出所有表
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            print(f"数据库 {db_name} 中有 {len(tables)} 张表")
            
            # 在每个表中查找含有'es_key'或'esKey'等字段的表
            potential_tables = []
            for table in tables:
                try:
                    cursor.execute(f"DESCRIBE {table}")
                    columns = [col['Field'] for col in cursor.fetchall()]
                    
                    # 查找可能包含es_key的字段
                    es_key_columns = [col for col in columns if 'es_key' in col.lower() or 'eskey' in col.lower()]
                    if es_key_columns:
                        potential_tables.append((table, es_key_columns))
                except:
                    continue
            
            if not potential_tables:
                print(f"数据库 {db_name} 中没有发现含有'es_key'字段的表")
                continue
                
            print(f"发现 {len(potential_tables)} 张可能包含es_key的表:")
            for table, columns in potential_tables:
                print(f"  - 表: {table}, 字段: {', '.join(columns)}")
                
                # 在每个可能的表中查询
                for column in columns:
                    try:
                        query = f"SELECT * FROM {table} WHERE {column} = %s LIMIT 1"
                        cursor.execute(query, (es_key,))
                        result = cursor.fetchone()
                        
                        if result:
                            print(f"\n*** 在数据库 {db_name} 的表 {table} 中找到匹配记录! ***")
                            found_in_databases.append((db_name, table, column))
                            
                            # 打印一些基本信息
                            print(f"字段列表: {', '.join(result.keys())}")
                            if 'create_time' in result:
                                print(f"创建时间: {result['create_time']}")
                                
                            # 检查是否有存储路径相关信息
                            storage_fields = ["path", "url", "file", "data", "oss", "storage"]
                            for field in result.keys():
                                for storage_key in storage_fields:
                                    if storage_key in field.lower() and result[field]:
                                        print(f"发现可能的存储路径 - {field}: {result[field]}")
                    except Exception as e:
                        print(f"查询表 {table} 时出错: {e}")
            
        except Exception as e:
            print(f"处理数据库 {db_name} 时出错: {e}")
        finally:
            if connection:
                connection.close()
    
    if found_in_databases:
        print("\n=== 搜索结果摘要 ===")
        print(f"在以下位置找到 es_key: {es_key}")
        for db_name, table, column in found_in_databases:
            print(f"- 数据库: {db_name}, 表: {table}, 字段: {column}")
    else:
        print(f"\n未在任何数据库中找到 es_key: {es_key}")
    
if __name__ == "__main__":
    print(f"开始在所有数据库中查询 es_key: {TARGET_ES_KEY}")
    search_es_key_in_all_databases(TARGET_ES_KEY)
    print("\n查询完成") 