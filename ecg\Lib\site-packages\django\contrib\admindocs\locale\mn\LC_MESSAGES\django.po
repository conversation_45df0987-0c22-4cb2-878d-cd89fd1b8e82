# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON>aa, 2014
# <PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2013-2014,2016-2017
# <AUTHOR> <EMAIL>, 2014-2015
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: Zorig <<EMAIL>>\n"
"Language-Team: Mongolian (http://www.transifex.com/django/django/language/"
"mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Удирдлагын баримт"

msgid "Home"
msgstr "Админ эхлэл"

msgid "Documentation"
msgstr "Баримтжуулалт"

msgid "Bookmarklets"
msgstr "Bookmarklet-ууд"

msgid "Documentation bookmarklets"
msgstr "Мookmarklet-уудын баримтжуулалт"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Bookmarklet тэмдэглэл суулгахын тулд холбоосыг өөрийн bookmark талбар руу "
"чирэх эсвэл хулганы баруун даралт дарж bookmark -т нэмэх сонголтыг сонгоно. "
"Ингэснээр аль ч хуудаснаас bookmarklet-ээ сонгох боломжтой болно."

msgid "Documentation for this page"
msgstr "Энэ хуудасны баримтжуулалт"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Таныг ямар ч хамаагүй хуудаснаас тухайн хуудсыг гаргаж байгаа "
"баримтжуулалтанд аваачна."

msgid "Tags"
msgstr "Таг-ууд"

msgid "List of all the template tags and their functions."
msgstr "Бүх загваруудын шошго мөн тэдний функц"

msgid "Filters"
msgstr "Шүүлтүүрүүд"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr "Шүүлтүүр нь тэмплэйтийн илэрцэд хувисагчаар оролцох үйлдэл"

msgid "Models"
msgstr "Моделууд"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Model-ууд бол системтэй холбоотой талбарууд бүхий объектуудын тайлбар, Model "
"бүр тэмплэйт хувьсагч болох боломтой талбаруудтай "

msgid "Views"
msgstr "View-үүд"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Нийтэд ил хуудас бүр view-ээс бүтнэ. View нь ямар объектийг, тухайн template-"
"д  ашиглахыг тодорхойлно."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Админы ажиллагаанд хурдан нэвтрэхын тулд таны вэб хөтөчид зориулагдсан "
"хэрэгсэл."

msgid "Please install docutils"
msgstr "docutils ийг суулгана уу"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Админ хэсгийн баримтжуулалтын ажиллуулхад  Python ийн <a href=\"%(link)s"
"\">docutils</a> санг суулгах шаардлагатай."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Админуудаас <a href=\"%(link)s\">docutils</a> санг суулгасан эсхийг асууна "
"уу."

#, python-format
msgid "Model: %(name)s"
msgstr "Модель: %(name)s"

msgid "Fields"
msgstr "Талбарууд"

msgid "Field"
msgstr "Талбар"

msgid "Type"
msgstr "Төрөл"

msgid "Description"
msgstr "Товчхон"

msgid "Methods with arguments"
msgstr "Аргументтэй функцүүд"

msgid "Method"
msgstr "Функц"

msgid "Arguments"
msgstr "Аргументүүд"

msgid "Back to Model documentation"
msgstr "Моделийн баримтруу буцах"

msgid "Model documentation"
msgstr "Моделийн баримт"

msgid "Model groups"
msgstr "Модел бүлэгүүд"

msgid "Templates"
msgstr "Загварууд"

#, python-format
msgid "Template: %(name)s"
msgstr "Загвар: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "Загвар: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr "\"%(name)s\" тэмплэйтын зам хайх"

msgid "(does not exist)"
msgstr "(Оршдоггүй)"

msgid "Back to Documentation"
msgstr "Баримтруу буцах"

msgid "Template filters"
msgstr "Загвар шүүлт"

msgid "Template filter documentation"
msgstr "Загвар шүүлтийн баримт"

msgid "Built-in filters"
msgstr "Өгөгдмөл шүүлтүүрүүд"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Энэ шошгыг ашиглахын тулд үүнийг <code>%(code)s</code> шошгоны өмнө загвар "
"дотроо бич"

msgid "Template tags"
msgstr "Загвар шошго"

msgid "Template tag documentation"
msgstr "Template tag баримтжуулалт"

msgid "Built-in tags"
msgstr "Өгөгдмөл шошгууд"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Энэ шошгыг ашиглахын тулд үүнийг <code>%(code)s</code> шошгоны өмнө загвар "
"дотроо бич"

#, python-format
msgid "View: %(name)s"
msgstr "Харагдац: %(name)s"

msgid "Context:"
msgstr "Уул үг: "

msgid "Templates:"
msgstr "Загварууд:"

msgid "Back to View documentation"
msgstr "Харагдац баримтруу буцах"

msgid "View documentation"
msgstr "Харагдац баримт"

msgid "Jump to namespace"
msgstr "Хоосон зай үүсгэх"

msgid "Empty namespace"
msgstr "Хоосон нэрний зай"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "%(name)s нэрсээр харах"

msgid "Views by empty namespace"
msgstr "Хоосон зайгаар харах"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Функц харах: <code>%(full_name)s</code>. Нэр: <code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "шошго:"

msgid "filter:"
msgstr "шүүлтүүр:"

msgid "view:"
msgstr "харах:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "%(app_label)r аппликэйшн олдсонгүй"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "  %(app_label)r програмд %(model_name)r модел олдсонгүй"

msgid "model:"
msgstr "модел:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "холбогдох `%(app_label)s.%(data_type)s`объект"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "холбогдох `%(app_label)s.%(object_name)s` объектууд"

#, python-format
msgid "all %s"
msgstr "бүх %s"

#, python-format
msgid "number of %s"
msgstr "%s-ийн тоо"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s нь url хэлбэрийн объект биш байна"
