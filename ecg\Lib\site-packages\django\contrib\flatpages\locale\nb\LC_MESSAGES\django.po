# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2015
# <PERSON> <<EMAIL>>, 2019-2020
# <PERSON> <<EMAIL>>, 2011-2012
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-01-21 12:12+0000\n"
"Last-Translator: Jon <<EMAIL>>\n"
"Language-Team: Norwegian Bokmål (http://www.transifex.com/django/django/"
"language/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Avanserte innstillinger"

msgid "Flat Pages"
msgstr "Flatsider"

msgid "URL"
msgstr "Nettadresse"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Eksempel: \"/om/kontakt/\". Kontroller at det er en skråstrek foran og bak."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Dette feltet kan kun inneholde bokstaver, nummer, skilletegn, understreker, "
"bindestreker, skråstreker eller tilder."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Eksempel: \"/om/kontakt\". Kontroller at det er en skråstrek foran."

msgid "URL is missing a leading slash."
msgstr "URL mangler innledende skråstrek."

msgid "URL is missing a trailing slash."
msgstr "URL mangler avsluttende skråstrek."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Flatside med URL-en %(url)s finnes allerede for siden %(site)s"

msgid "title"
msgstr "tittel"

msgid "content"
msgstr "innhold"

msgid "enable comments"
msgstr "tillat kommentarer"

msgid "template name"
msgstr "malnavn"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Eksempel: \"flatpages/contact_page.html\". Hvis denne ikke er gitt, vil "
"\"flatpages/default.html\" bli brukt."

msgid "registration required"
msgstr "krever registrering"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Hvis denne er krysset av, kan kun innloggede brukere se siden."

msgid "sites"
msgstr "nettsteder"

msgid "flat page"
msgstr "flatside"

msgid "flat pages"
msgstr "flatsider"
