"""
增强版健康指标诊断模块
整合WESAD HRV分类器和原有的机器学习模型
"""

import traceback
from apps.analysis.health_metrics.wesad_hrv_classifier import WESADHRVClassifier
from apps.analysis.health_metrics import rf_model_index
from apps.models.analysis_models import HealthMetricsEntity
from apps.utils.logger_helper import Logger


class EnhancedHealthMetricsEntity(HealthMetricsEntity):
    """
    增强版健康指标实体，添加WESAD分级信息
    """
    
    def __init__(self):
        super().__init__()
        # 新增WESAD分级字段
        self.StressLevel = 'medium'          # 压力等级: low/medium/high
        self.StressRiskCategory = '中等风险'   # 压力风险分类
        self.FatigueLevel = 'medium'         # 疲劳等级: low/medium/high  
        self.FatigueRiskCategory = '中等风险'  # 疲劳风险分类
        self.VitalityLevel = 'medium'        # 活力等级: low/medium/high
        self.OverallStatus = '一般状态'       # 整体状态
        self.ClusterDescription = '基线状态'  # 聚类描述
        self.AssessmentConfidence = 0.5      # 评估置信度
        
        # HRV详细特征
        self.HRVFeatures = {}


def process(waveform_info):
    """
    增强版健康指标处理函数
    结合WESAD分类器和原有机器学习模型
    
    :param waveform_info: 波形信息
    :return: 增强版健康指标
    """
    logger = Logger()
    
    try:
        # 初始化分类器
        wesad_classifier = WESADHRVClassifier()
        
        # 获取WESAD综合评估
        wesad_assessment = wesad_classifier.comprehensive_assessment(waveform_info)
        
        # 获取原有机器学习模型预测
        nn_intervals = waveform_info['waveform']['nn_intervals']
        sdnn_value = waveform_info['hrv']['linear']['sdnn']
        
        # 使用原有模型获取基础预测
        ml_prediction = rf_model_index.run_index_main(nn_intervals)
        
        # 创建增强版健康指标实体
        health_metrics = EnhancedHealthMetricsEntity()
        
        # 融合WESAD评估和机器学习预测
        health_metrics = _integrate_assessments(health_metrics, wesad_assessment, ml_prediction, sdnn_value)
        
        logger.info(f"增强版健康指标评估完成: 压力={health_metrics.Pressure}, "
                   f"疲劳={health_metrics.Fatigue}, 活力={health_metrics.Vitality}")
        
        return health_metrics
        
    except Exception as e:
        logger.error(f"增强版健康指标处理异常: {traceback.format_exc()}")
        return _get_default_health_metrics()


def _integrate_assessments(health_metrics, wesad_assessment, ml_prediction, sdnn_value):
    """
    整合WESAD评估和机器学习预测结果
    
    :param health_metrics: 健康指标实体
    :param wesad_assessment: WESAD评估结果
    :param ml_prediction: 机器学习预测结果
    :param sdnn_value: SDNN值
    :return: 整合后的健康指标
    """
    try:
        # 获取WESAD评估结果
        stress_assessment = wesad_assessment.get('stress_assessment', {})
        fatigue_assessment = wesad_assessment.get('fatigue_assessment', {})
        vitality_assessment = wesad_assessment.get('vitality_assessment', {})
        cluster_analysis = wesad_assessment.get('cluster_analysis', {})
        
        # 压力指标整合 (WESAD权重0.7, ML权重0.3)
        wesad_stress = stress_assessment.get('score', 50)
        ml_stress = ml_prediction[0] if len(ml_prediction) > 0 else 50
        health_metrics.Pressure = round(wesad_stress * 0.7 + ml_stress * 0.3, 1)
        health_metrics.StressLevel = stress_assessment.get('level', 'medium')
        health_metrics.StressRiskCategory = stress_assessment.get('risk_category', '中等风险')
        
        # 疲劳指标整合 (WESAD权重0.6, ML权重0.4)
        wesad_fatigue = fatigue_assessment.get('score', 50)
        ml_fatigue = ml_prediction[2] if len(ml_prediction) > 2 else 50
        health_metrics.Fatigue = round(wesad_fatigue * 0.6 + ml_fatigue * 0.4, 1)
        health_metrics.FatigueLevel = fatigue_assessment.get('level', 'medium')
        health_metrics.FatigueRiskCategory = fatigue_assessment.get('risk_category', '中等风险')
        
        # 活力指标整合 (WESAD权重0.8, ML权重0.2)
        wesad_vitality = vitality_assessment.get('score', 50)
        ml_vitality = ml_prediction[3] if len(ml_prediction) > 3 else 50
        health_metrics.Vitality = round(wesad_vitality * 0.8 + ml_vitality * 0.2, 1)
        health_metrics.VitalityLevel = vitality_assessment.get('level', 'medium')
        
        # 情绪指标 (主要使用ML预测)
        ml_emotion = ml_prediction[1] if len(ml_prediction) > 1 else 50
        # 根据压力和活力调整情绪
        emotion_adjustment = (health_metrics.Vitality - health_metrics.Pressure) * 0.1
        health_metrics.Emotion = round(ml_emotion + emotion_adjustment, 1)
        health_metrics.Emotion = max(0, min(100, health_metrics.Emotion))
        
        # HRV指标处理
        if 20 <= sdnn_value <= 200:
            health_metrics.HRV = sdnn_value
        else:
            # 基于其他HRV特征估算
            hrv_features = wesad_assessment.get('hrv_features', {})
            rmssd = hrv_features.get('rmssd', 0)
            if rmssd > 0:
                health_metrics.HRV = min(200, max(20, rmssd * 2))  # 简单映射
            else:
                health_metrics.HRV = 50  # 默认值
        
        # 整体状态和聚类信息
        health_metrics.OverallStatus = wesad_assessment.get('overall_status', '一般状态')
        health_metrics.ClusterDescription = cluster_analysis.get('description', '基线状态')
        
        # 计算综合置信度
        stress_conf = stress_assessment.get('confidence', 0.5)
        fatigue_conf = fatigue_assessment.get('confidence', 0.5)
        vitality_conf = vitality_assessment.get('confidence', 0.5)
        health_metrics.AssessmentConfidence = round((stress_conf + fatigue_conf + vitality_conf) / 3, 2)
        
        # 保存HRV特征
        health_metrics.HRVFeatures = wesad_assessment.get('hrv_features', {})
        
        return health_metrics
        
    except Exception as e:
        Logger().error(f"整合评估结果时出错: {traceback.format_exc()}")
        return _get_default_health_metrics()


def _get_default_health_metrics():
    """获取默认健康指标"""
    health_metrics = EnhancedHealthMetricsEntity()
    health_metrics.Pressure = 50
    health_metrics.HRV = 50
    health_metrics.Emotion = 50
    health_metrics.Fatigue = 50
    health_metrics.Vitality = 50
    health_metrics.HeartAge = 0
    return health_metrics


def get_detailed_assessment_report(waveform_info):
    """
    获取详细的评估报告
    
    :param waveform_info: 波形信息
    :return: 详细评估报告
    """
    try:
        wesad_classifier = WESADHRVClassifier()
        assessment = wesad_classifier.comprehensive_assessment(waveform_info)
        
        # 生成详细报告
        report = {
            'summary': {
                'overall_status': assessment.get('overall_status', '数据不足'),
                'cluster_description': assessment.get('cluster_analysis', {}).get('description', '基线状态'),
                'assessment_time': '当前时间'  # 可以添加时间戳
            },
            'stress_analysis': {
                'level': assessment.get('stress_assessment', {}).get('level', 'medium'),
                'score': assessment.get('stress_assessment', {}).get('score', 50),
                'risk_category': assessment.get('stress_assessment', {}).get('risk_category', '中等风险'),
                'confidence': assessment.get('stress_assessment', {}).get('confidence', 0.5),
                'recommendations': _get_stress_recommendations(
                    assessment.get('stress_assessment', {}).get('level', 'medium')
                )
            },
            'fatigue_analysis': {
                'level': assessment.get('fatigue_assessment', {}).get('level', 'medium'),
                'score': assessment.get('fatigue_assessment', {}).get('score', 50),
                'risk_category': assessment.get('fatigue_assessment', {}).get('risk_category', '中等风险'),
                'confidence': assessment.get('fatigue_assessment', {}).get('confidence', 0.5),
                'recommendations': _get_fatigue_recommendations(
                    assessment.get('fatigue_assessment', {}).get('level', 'medium')
                )
            },
            'vitality_analysis': {
                'level': assessment.get('vitality_assessment', {}).get('level', 'medium'),
                'score': assessment.get('vitality_assessment', {}).get('score', 50),
                'confidence': assessment.get('vitality_assessment', {}).get('confidence', 0.5),
                'recommendations': _get_vitality_recommendations(
                    assessment.get('vitality_assessment', {}).get('level', 'medium')
                )
            },
            'hrv_features': assessment.get('hrv_features', {}),
            'technical_details': {
                'classification_method': 'WESAD-based HRV Classification',
                'features_used': list(assessment.get('hrv_features', {}).keys()),
                'cluster_id': assessment.get('cluster_analysis', {}).get('cluster_id', 1)
            }
        }
        
        return report
        
    except Exception as e:
        Logger().error(f"生成详细评估报告时出错: {traceback.format_exc()}")
        return {'error': '报告生成失败'}


def _get_stress_recommendations(stress_level):
    """获取压力管理建议"""
    recommendations = {
        'low': [
            "保持当前良好的压力管理状态",
            "继续进行规律的放松活动",
            "维持健康的生活方式"
        ],
        'medium': [
            "适当增加放松时间",
            "尝试深呼吸或冥想练习",
            "保证充足的睡眠",
            "适度运动有助于压力缓解"
        ],
        'high': [
            "建议立即采取压力缓解措施",
            "考虑专业心理咨询",
            "减少工作强度，增加休息时间",
            "进行规律的有氧运动",
            "学习压力管理技巧"
        ]
    }
    return recommendations.get(stress_level, recommendations['medium'])


def _get_fatigue_recommendations(fatigue_level):
    """获取疲劳管理建议"""
    recommendations = {
        'low': [
            "保持当前良好的精力状态",
            "维持规律的作息时间",
            "继续健康的生活习惯"
        ],
        'medium': [
            "注意劳逸结合",
            "保证7-8小时充足睡眠",
            "适当补充营养",
            "避免过度用眼和久坐"
        ],
        'high': [
            "建议充分休息",
            "检查是否存在睡眠问题",
            "考虑医学检查排除疾病因素",
            "调整工作强度",
            "增加户外活动时间"
        ]
    }
    return recommendations.get(fatigue_level, recommendations['medium'])


def _get_vitality_recommendations(vitality_level):
    """获取活力提升建议"""
    recommendations = {
        'low': [
            "增加有氧运动",
            "改善饮食营养",
            "保证充足睡眠",
            "多参与社交活动",
            "考虑维生素补充"
        ],
        'medium': [
            "保持规律运动",
            "均衡饮食",
            "适当增加户外活动",
            "培养兴趣爱好"
        ],
        'high': [
            "保持当前良好状态",
            "继续健康的生活方式",
            "可以适当挑战新的运动项目"
        ]
    }
    return recommendations.get(vitality_level, recommendations['medium'])
