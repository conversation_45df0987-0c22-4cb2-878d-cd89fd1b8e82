#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试匹配过程
"""

import pandas as pd
import re

def extract_key_components(key_string):
    """从复杂的键字符串中提取关键组件"""
    print(f"解析键字符串: {key_string}")
    
    # 提取客户ID (CUSTOMER后面的数字)
    customer_match = re.search(r'CUSTOMER(\d+)', key_string)
    customer_id = customer_match.group(1) if customer_match else ''
    
    # 提取完整的时间戳 (YYYYMMDDHHMMSS格式，通常是14位数字)
    # 针对不同的格式进行精确匹配
    datetime_patterns = [
        r'_(\d{14})_',      # _20250401012111_
        r'/(\d{14})$',      # /20250401012111 (文件2格式，以数字结尾)
        r'/(\d{12})$',      # /202504010121 (文件2格式，12位)
        r'_(\d{12})_',      # _202504010121_
        r'_(\d{8})_',       # _20250401_
        r'/(\d{8})$',       # /20250401 (文件2格式，8位)
    ]
    
    date_part = ''
    for pattern in datetime_patterns:
        date_match = re.search(pattern, key_string)
        if date_match:
            date_part = date_match.group(1)
            break
    
    # 提取导联信息
    lead_patterns = [
        r'[Ll][Ee][Aa][Dd]([IVX]+)',  # LEADI, leadI等
        r'_([IVX]+)_[Aa][Cc][Cc]',    # _I_acc, _II_acc等
        r'/(\d+[IVX]+)',              # /20250401063945_I等中的I
    ]
    
    lead_part = ''
    for pattern in lead_patterns:
        lead_match = re.search(pattern, key_string)
        if lead_match:
            lead_part = lead_match.group(1)
            # 标准化导联名称
            lead_part = lead_part.replace('LEAD', '').replace('lead', '')
            break
    
    print(f"解析结果: 客户ID={customer_id}, 日期={date_part}, 导联={lead_part}")
    return customer_id, date_part, lead_part

def main():
    # 加载数据
    print("加载数据...")
    df1 = pd.read_csv(r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\10s10步长v2.0\合并结果.csv")
    df2 = pd.read_excel(r"D:\ECG\0723一分钟项目测试\标注平台数据\标注平台数据.xls")
    
    print(f"文件1形状: {df1.shape}")
    print(f"文件2形状: {df2.shape}")
    
    # 测试特定的匹配案例
    test_cases = [
        "CUSTOMER16678053292851298_20250403135142_leadI_acc.csv",
        "CUSTOMER18838092628219043846502_20250401012011_leadII_acc.csv"
    ]
    
    print("\n" + "="*60)
    print("测试文件1的键解析:")
    for case in test_cases:
        customer_id, date_part, lead_part = extract_key_components(case)
        key1_full = f"{customer_id}_{date_part}_{lead_part}"
        print(f"完整匹配键: {key1_full}")
        print()
    
    print("="*60)
    print("测试文件2的键解析和映射:")
    
    # 解析文件2的键组件
    key2_components = []
    for _, row in df2.iterrows():
        es_key = str(row['es_key']).strip()
        lead = str(row['lead']).strip()
        
        # 从es_key中提取客户ID和时间
        customer_id, date_part, _ = extract_key_components(es_key)
        
        # 标准化导联名称
        lead_normalized = lead.replace('LEAD', '').strip()
        
        key2_components.append((customer_id, date_part, lead_normalized))
    
    # 创建完整匹配键
    key2_full = pd.Series([f"{comp[0]}_{comp[1]}_{comp[2]}" for comp in key2_components])
    
    # 创建映射字典
    disease_mapping = dict(zip(key2_full, df2['disease_name']))
    
    print(f"映射字典大小: {len(disease_mapping)}")
    
    # 测试特定的匹配
    test_keys = [
        "16678053292851298_20250403135142_I",
        "18838092628219043846502_20250401012011_II"
    ]
    
    for test_key in test_keys:
        if test_key in disease_mapping:
            disease = disease_mapping[test_key]
            print(f"✅ 键 '{test_key}' 映射到: '{disease}'")
        else:
            print(f"❌ 键 '{test_key}' 未找到映射")
            # 查找相似的键
            similar_keys = [k for k in disease_mapping.keys() if test_key in k or k in test_key]
            if similar_keys:
                print(f"   相似的键: {similar_keys[:5]}")
    
    print("\n" + "="*60)
    print("检查映射字典中的重复值:")
    
    # 检查是否有重复的键
    key_counts = pd.Series(list(disease_mapping.keys())).value_counts()
    duplicates = key_counts[key_counts > 1]
    
    if not duplicates.empty:
        print("发现重复键:")
        for key, count in duplicates.items():
            print(f"  {key}: {count} 次")
            # 显示对应的疾病名称
            matching_diseases = [disease_mapping[k] for k in disease_mapping.keys() if k == key]
            print(f"    对应疾病: {set(matching_diseases)}")
    else:
        print("没有重复键")
    
    print("\n" + "="*60)
    print("验证具体的匹配案例:")
    
    # 手动验证第二个案例
    target_customer = "18838092628219043846502"
    target_date = "20250401012011"
    target_lead = "II"
    
    print(f"查找: 客户={target_customer}, 日期={target_date}, 导联={target_lead}")
    
    # 在文件2中查找对应记录
    matching_rows = df2[
        (df2['es_key'].str.contains(target_customer)) & 
        (df2['es_key'].str.contains(target_date)) & 
        (df2['lead'] == target_lead)
    ]
    
    print("文件2中的匹配记录:")
    print(matching_rows[['es_key', 'lead', 'disease_name']])
    
    if not matching_rows.empty:
        expected_disease = matching_rows.iloc[0]['disease_name']
        print(f"期望的疾病名称: {expected_disease}")
        
        # 检查映射字典中的值
        full_key = f"{target_customer}_{target_date}_{target_lead}"
        actual_disease = disease_mapping.get(full_key, "未找到")
        print(f"映射字典中的疾病名称: {actual_disease}")
        
        if expected_disease == actual_disease:
            print("✅ 映射正确")
        else:
            print("❌ 映射错误")

if __name__ == "__main__":
    main()
