# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.keras.metrics namespace.
"""

from __future__ import print_function as _print_function

import sys as _sys

from keras.losses import binary_crossentropy
from keras.losses import categorical_crossentropy
from keras.losses import cosine_similarity as cosine
from keras.losses import cosine_similarity as cosine_proximity
from keras.losses import hinge
from keras.losses import kl_divergence
from keras.losses import kl_divergence as KLD
from keras.losses import kl_divergence as kld
from keras.losses import kl_divergence as kullback_leibler_divergence
from keras.losses import log_cosh
from keras.losses import log_cosh as logcosh
from keras.losses import mean_absolute_error
from keras.losses import mean_absolute_error as MAE
from keras.losses import mean_absolute_error as mae
from keras.losses import mean_absolute_percentage_error
from keras.losses import mean_absolute_percentage_error as MAPE
from keras.losses import mean_absolute_percentage_error as mape
from keras.losses import mean_squared_error
from keras.losses import mean_squared_error as MSE
from keras.losses import mean_squared_error as mse
from keras.losses import mean_squared_logarithmic_error
from keras.losses import mean_squared_logarithmic_error as MSLE
from keras.losses import mean_squared_logarithmic_error as msle
from keras.losses import poisson
from keras.losses import sparse_categorical_crossentropy
from keras.losses import squared_hinge
from keras.metrics import AUC
from keras.metrics import Accuracy
from keras.metrics import BinaryAccuracy
from keras.metrics import BinaryCrossentropy
from keras.metrics import CategoricalAccuracy
from keras.metrics import CategoricalCrossentropy
from keras.metrics import CategoricalHinge
from keras.metrics import CosineSimilarity
from keras.metrics import FalseNegatives
from keras.metrics import FalsePositives
from keras.metrics import Hinge
from keras.metrics import KLDivergence
from keras.metrics import LogCoshError
from keras.metrics import Mean
from keras.metrics import MeanAbsoluteError
from keras.metrics import MeanAbsolutePercentageError
from keras.metrics import MeanIoU
from keras.metrics import MeanMetricWrapper
from keras.metrics import MeanRelativeError
from keras.metrics import MeanSquaredError
from keras.metrics import MeanSquaredLogarithmicError
from keras.metrics import MeanTensor
from keras.metrics import Metric
from keras.metrics import Poisson
from keras.metrics import Precision
from keras.metrics import PrecisionAtRecall
from keras.metrics import Recall
from keras.metrics import RecallAtPrecision
from keras.metrics import RootMeanSquaredError
from keras.metrics import SensitivityAtSpecificity
from keras.metrics import SparseCategoricalAccuracy
from keras.metrics import SparseCategoricalCrossentropy
from keras.metrics import SparseTopKCategoricalAccuracy
from keras.metrics import SpecificityAtSensitivity
from keras.metrics import SquaredHinge
from keras.metrics import Sum
from keras.metrics import TopKCategoricalAccuracy
from keras.metrics import TrueNegatives
from keras.metrics import TruePositives
from keras.metrics import binary_accuracy
from keras.metrics import categorical_accuracy
from keras.metrics import deserialize
from keras.metrics import get
from keras.metrics import serialize
from keras.metrics import sparse_categorical_accuracy
from keras.metrics import sparse_top_k_categorical_accuracy
from keras.metrics import top_k_categorical_accuracy

del _print_function

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.metrics", public_apis=None, deprecation=True,
      has_lite=False)
