import mysql.connector
import json

# 要查询的特定es_key
TARGET_ES_KEY = "CUSTOMER19054257726245560321078/20250401152805"

def query_db_for_es_key(es_key):
    """从数据库查询特定es_key的详细信息"""
    try:
        # 数据库连接配置
        db_config = {
            'host': '**************',
            'port': 3308,
            'user': 'ai',
            'password': 'z8^#g4r4mz',
            'database': 'ecg_marking'
        }
        
        print(f"尝试连接到数据库: {db_config['database']}@{db_config['host']}:{db_config['port']}")
        
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        
        # 执行查询
        query = "SELECT * FROM t_patient_ecg WHERE es_key = %s"
        cursor.execute(query, (es_key,))
        
        # 获取结果
        result = cursor.fetchone()
        
        # 检查结果是否存在
        if result:
            print(f"\n找到 es_key: {es_key} 的记录")
            print(f"记录日期: {result.get('create_time', '未知')}")
            print(f"诊断结论: {result.get('new_report_conclusion', '无')}")
            print(f"ECG状态: {result.get('ecg_status', '未知')}")
            print(f"采样率: {result.get('sample_rate', '未知')}")
            
            # 检查是否有其他可能的存储路径信息
            storage_related_fields = ["oss_path", "file_path", "storage_path", "data_path", "cloud_path", "url"]
            found_storage_info = False
            
            for field in storage_related_fields:
                if field in result and result[field]:
                    print(f"\n找到可能的存储路径信息 - {field}: {result[field]}")
                    found_storage_info = True
            
            if not found_storage_info:
                print("\n未找到明确的存储路径信息")
            
            # 打印记录中的所有字段名称
            print("\n记录包含的所有字段:")
            for key in result.keys():
                print(f"- {key}")
                
            # 尝试通过 SQL 查询其他相关表，可能包含存储信息
            print("\n尝试查找与该 es_key 相关的其他表中的信息...")
            
            # 例如，查询可能存在的关联表
            tables_to_check = ["t_patient", "t_patient_ecg_files", "t_ecg_data", "t_ecg_files"]
            for table in tables_to_check:
                try:
                    check_query = f"SELECT * FROM {table} WHERE es_key = %s LIMIT 1"
                    cursor.execute(check_query, (es_key,))
                    related_result = cursor.fetchone()
                    if related_result:
                        print(f"在表 {table} 中找到相关记录")
                except Exception:
                    # 表可能不存在，忽略错误
                    pass
                    
            return result
        else:
            print(f"\n数据库中未找到 es_key: {es_key} 的记录")
            
            # 尝试使用LIKE模糊查询，查找相似的es_key
            fuzzy_query = "SELECT es_key FROM t_patient_ecg WHERE es_key LIKE %s LIMIT 10"
            cursor.execute(fuzzy_query, (f"%{es_key.split('/')[0]}%",))
            similar_keys = cursor.fetchall()
            
            if similar_keys:
                print("\n找到一些相似的es_key:")
                for key in similar_keys:
                    print(f"- {key['es_key']}")
            
            return None
    except Exception as e:
        print(f"\n查询数据库时出错: {e}")
        return None
    finally:
        try:
            # 关闭连接
            if 'cursor' in locals() and cursor:
                cursor.close()
            if 'connection' in locals() and connection:
                connection.close()
            print("\n数据库连接已关闭")
        except:
            pass

if __name__ == "__main__":
    print(f"开始在数据库中查询 es_key: {TARGET_ES_KEY}")
    result = query_db_for_es_key(TARGET_ES_KEY)
    
    print("\n查询完成") 