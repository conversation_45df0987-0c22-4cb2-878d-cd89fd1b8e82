// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/protobuf/xplane.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[9]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto;
namespace tensorflow {
namespace profiler {
class XEvent;
class XEventDefaultTypeInternal;
extern XEventDefaultTypeInternal _XEvent_default_instance_;
class XEventMetadata;
class XEventMetadataDefaultTypeInternal;
extern XEventMetadataDefaultTypeInternal _XEventMetadata_default_instance_;
class XLine;
class XLineDefaultTypeInternal;
extern XLineDefaultTypeInternal _XLine_default_instance_;
class XPlane;
class XPlaneDefaultTypeInternal;
extern XPlaneDefaultTypeInternal _XPlane_default_instance_;
class XPlane_EventMetadataEntry_DoNotUse;
class XPlane_EventMetadataEntry_DoNotUseDefaultTypeInternal;
extern XPlane_EventMetadataEntry_DoNotUseDefaultTypeInternal _XPlane_EventMetadataEntry_DoNotUse_default_instance_;
class XPlane_StatMetadataEntry_DoNotUse;
class XPlane_StatMetadataEntry_DoNotUseDefaultTypeInternal;
extern XPlane_StatMetadataEntry_DoNotUseDefaultTypeInternal _XPlane_StatMetadataEntry_DoNotUse_default_instance_;
class XSpace;
class XSpaceDefaultTypeInternal;
extern XSpaceDefaultTypeInternal _XSpace_default_instance_;
class XStat;
class XStatDefaultTypeInternal;
extern XStatDefaultTypeInternal _XStat_default_instance_;
class XStatMetadata;
class XStatMetadataDefaultTypeInternal;
extern XStatMetadataDefaultTypeInternal _XStatMetadata_default_instance_;
}  // namespace profiler
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::profiler::XEvent* Arena::CreateMaybeMessage<::tensorflow::profiler::XEvent>(Arena*);
template<> ::tensorflow::profiler::XEventMetadata* Arena::CreateMaybeMessage<::tensorflow::profiler::XEventMetadata>(Arena*);
template<> ::tensorflow::profiler::XLine* Arena::CreateMaybeMessage<::tensorflow::profiler::XLine>(Arena*);
template<> ::tensorflow::profiler::XPlane* Arena::CreateMaybeMessage<::tensorflow::profiler::XPlane>(Arena*);
template<> ::tensorflow::profiler::XPlane_EventMetadataEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::profiler::XPlane_EventMetadataEntry_DoNotUse>(Arena*);
template<> ::tensorflow::profiler::XPlane_StatMetadataEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::profiler::XPlane_StatMetadataEntry_DoNotUse>(Arena*);
template<> ::tensorflow::profiler::XSpace* Arena::CreateMaybeMessage<::tensorflow::profiler::XSpace>(Arena*);
template<> ::tensorflow::profiler::XStat* Arena::CreateMaybeMessage<::tensorflow::profiler::XStat>(Arena*);
template<> ::tensorflow::profiler::XStatMetadata* Arena::CreateMaybeMessage<::tensorflow::profiler::XStatMetadata>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace profiler {

// ===================================================================

class XSpace :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XSpace) */ {
 public:
  XSpace();
  virtual ~XSpace();

  XSpace(const XSpace& from);
  XSpace(XSpace&& from) noexcept
    : XSpace() {
    *this = ::std::move(from);
  }

  inline XSpace& operator=(const XSpace& from) {
    CopyFrom(from);
    return *this;
  }
  inline XSpace& operator=(XSpace&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XSpace& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XSpace* internal_default_instance() {
    return reinterpret_cast<const XSpace*>(
               &_XSpace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(XSpace& a, XSpace& b) {
    a.Swap(&b);
  }
  inline void Swap(XSpace* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XSpace* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XSpace* New() const final {
    return CreateMaybeMessage<XSpace>(nullptr);
  }

  XSpace* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XSpace>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XSpace& from);
  void MergeFrom(const XSpace& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XSpace* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XSpace";
  }
  protected:
  explicit XSpace(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlanesFieldNumber = 1,
    kErrorsFieldNumber = 2,
    kWarningsFieldNumber = 3,
    kHostnamesFieldNumber = 4,
  };
  // repeated .tensorflow.profiler.XPlane planes = 1;
  int planes_size() const;
  void clear_planes();
  ::tensorflow::profiler::XPlane* mutable_planes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XPlane >*
      mutable_planes();
  const ::tensorflow::profiler::XPlane& planes(int index) const;
  ::tensorflow::profiler::XPlane* add_planes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XPlane >&
      planes() const;

  // repeated string errors = 2;
  int errors_size() const;
  void clear_errors();
  const std::string& errors(int index) const;
  std::string* mutable_errors(int index);
  void set_errors(int index, const std::string& value);
  void set_errors(int index, std::string&& value);
  void set_errors(int index, const char* value);
  void set_errors(int index, const char* value, size_t size);
  std::string* add_errors();
  void add_errors(const std::string& value);
  void add_errors(std::string&& value);
  void add_errors(const char* value);
  void add_errors(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& errors() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_errors();

  // repeated string warnings = 3;
  int warnings_size() const;
  void clear_warnings();
  const std::string& warnings(int index) const;
  std::string* mutable_warnings(int index);
  void set_warnings(int index, const std::string& value);
  void set_warnings(int index, std::string&& value);
  void set_warnings(int index, const char* value);
  void set_warnings(int index, const char* value, size_t size);
  std::string* add_warnings();
  void add_warnings(const std::string& value);
  void add_warnings(std::string&& value);
  void add_warnings(const char* value);
  void add_warnings(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& warnings() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_warnings();

  // repeated string hostnames = 4;
  int hostnames_size() const;
  void clear_hostnames();
  const std::string& hostnames(int index) const;
  std::string* mutable_hostnames(int index);
  void set_hostnames(int index, const std::string& value);
  void set_hostnames(int index, std::string&& value);
  void set_hostnames(int index, const char* value);
  void set_hostnames(int index, const char* value, size_t size);
  std::string* add_hostnames();
  void add_hostnames(const std::string& value);
  void add_hostnames(std::string&& value);
  void add_hostnames(const char* value);
  void add_hostnames(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& hostnames() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_hostnames();

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XSpace)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XPlane > planes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> errors_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> warnings_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> hostnames_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XPlane_EventMetadataEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<XPlane_EventMetadataEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XEventMetadata,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<XPlane_EventMetadataEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XEventMetadata,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  XPlane_EventMetadataEntry_DoNotUse();
  XPlane_EventMetadataEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const XPlane_EventMetadataEntry_DoNotUse& other);
  static const XPlane_EventMetadataEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const XPlane_EventMetadataEntry_DoNotUse*>(&_XPlane_EventMetadataEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto.file_level_metadata[1];
  }

  public:
};

// -------------------------------------------------------------------

class XPlane_StatMetadataEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<XPlane_StatMetadataEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XStatMetadata,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<XPlane_StatMetadataEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XStatMetadata,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  XPlane_StatMetadataEntry_DoNotUse();
  XPlane_StatMetadataEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const XPlane_StatMetadataEntry_DoNotUse& other);
  static const XPlane_StatMetadataEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const XPlane_StatMetadataEntry_DoNotUse*>(&_XPlane_StatMetadataEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto.file_level_metadata[2];
  }

  public:
};

// -------------------------------------------------------------------

class XPlane :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XPlane) */ {
 public:
  XPlane();
  virtual ~XPlane();

  XPlane(const XPlane& from);
  XPlane(XPlane&& from) noexcept
    : XPlane() {
    *this = ::std::move(from);
  }

  inline XPlane& operator=(const XPlane& from) {
    CopyFrom(from);
    return *this;
  }
  inline XPlane& operator=(XPlane&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XPlane& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XPlane* internal_default_instance() {
    return reinterpret_cast<const XPlane*>(
               &_XPlane_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(XPlane& a, XPlane& b) {
    a.Swap(&b);
  }
  inline void Swap(XPlane* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XPlane* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XPlane* New() const final {
    return CreateMaybeMessage<XPlane>(nullptr);
  }

  XPlane* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XPlane>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XPlane& from);
  void MergeFrom(const XPlane& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XPlane* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XPlane";
  }
  protected:
  explicit XPlane(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kLinesFieldNumber = 3,
    kEventMetadataFieldNumber = 4,
    kStatMetadataFieldNumber = 5,
    kStatsFieldNumber = 6,
    kNameFieldNumber = 2,
    kIdFieldNumber = 1,
  };
  // repeated .tensorflow.profiler.XLine lines = 3;
  int lines_size() const;
  void clear_lines();
  ::tensorflow::profiler::XLine* mutable_lines(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XLine >*
      mutable_lines();
  const ::tensorflow::profiler::XLine& lines(int index) const;
  ::tensorflow::profiler::XLine* add_lines();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XLine >&
      lines() const;

  // map<int64, .tensorflow.profiler.XEventMetadata> event_metadata = 4;
  int event_metadata_size() const;
  void clear_event_metadata();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XEventMetadata >&
      event_metadata() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XEventMetadata >*
      mutable_event_metadata();

  // map<int64, .tensorflow.profiler.XStatMetadata> stat_metadata = 5;
  int stat_metadata_size() const;
  void clear_stat_metadata();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XStatMetadata >&
      stat_metadata() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XStatMetadata >*
      mutable_stat_metadata();

  // repeated .tensorflow.profiler.XStat stats = 6;
  int stats_size() const;
  void clear_stats();
  ::tensorflow::profiler::XStat* mutable_stats(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
      mutable_stats();
  const ::tensorflow::profiler::XStat& stats(int index) const;
  ::tensorflow::profiler::XStat* add_stats();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
      stats() const;

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XPlane)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XLine > lines_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      XPlane_EventMetadataEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XEventMetadata,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > event_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      XPlane_StatMetadataEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XStatMetadata,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > stat_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat > stats_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XLine :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XLine) */ {
 public:
  XLine();
  virtual ~XLine();

  XLine(const XLine& from);
  XLine(XLine&& from) noexcept
    : XLine() {
    *this = ::std::move(from);
  }

  inline XLine& operator=(const XLine& from) {
    CopyFrom(from);
    return *this;
  }
  inline XLine& operator=(XLine&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XLine& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XLine* internal_default_instance() {
    return reinterpret_cast<const XLine*>(
               &_XLine_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(XLine& a, XLine& b) {
    a.Swap(&b);
  }
  inline void Swap(XLine* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XLine* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XLine* New() const final {
    return CreateMaybeMessage<XLine>(nullptr);
  }

  XLine* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XLine>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XLine& from);
  void MergeFrom(const XLine& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XLine* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XLine";
  }
  protected:
  explicit XLine(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEventsFieldNumber = 4,
    kNameFieldNumber = 2,
    kDisplayNameFieldNumber = 11,
    kIdFieldNumber = 1,
    kTimestampNsFieldNumber = 3,
    kDurationPsFieldNumber = 9,
    kDisplayIdFieldNumber = 10,
  };
  // repeated .tensorflow.profiler.XEvent events = 4;
  int events_size() const;
  void clear_events();
  ::tensorflow::profiler::XEvent* mutable_events(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XEvent >*
      mutable_events();
  const ::tensorflow::profiler::XEvent& events(int index) const;
  ::tensorflow::profiler::XEvent* add_events();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XEvent >&
      events() const;

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string display_name = 11;
  void clear_display_name();
  const std::string& display_name() const;
  void set_display_name(const std::string& value);
  void set_display_name(std::string&& value);
  void set_display_name(const char* value);
  void set_display_name(const char* value, size_t size);
  std::string* mutable_display_name();
  std::string* release_display_name();
  void set_allocated_display_name(std::string* display_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_display_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_display_name(
      std::string* display_name);

  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 timestamp_ns = 3;
  void clear_timestamp_ns();
  ::PROTOBUF_NAMESPACE_ID::int64 timestamp_ns() const;
  void set_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 duration_ps = 9;
  void clear_duration_ps();
  ::PROTOBUF_NAMESPACE_ID::int64 duration_ps() const;
  void set_duration_ps(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 display_id = 10;
  void clear_display_id();
  ::PROTOBUF_NAMESPACE_ID::int64 display_id() const;
  void set_display_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XLine)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XEvent > events_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr display_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  ::PROTOBUF_NAMESPACE_ID::int64 timestamp_ns_;
  ::PROTOBUF_NAMESPACE_ID::int64 duration_ps_;
  ::PROTOBUF_NAMESPACE_ID::int64 display_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XEvent :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XEvent) */ {
 public:
  XEvent();
  virtual ~XEvent();

  XEvent(const XEvent& from);
  XEvent(XEvent&& from) noexcept
    : XEvent() {
    *this = ::std::move(from);
  }

  inline XEvent& operator=(const XEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline XEvent& operator=(XEvent&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XEvent& default_instance();

  enum DataCase {
    kOffsetPs = 2,
    kNumOccurrences = 5,
    DATA_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XEvent* internal_default_instance() {
    return reinterpret_cast<const XEvent*>(
               &_XEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(XEvent& a, XEvent& b) {
    a.Swap(&b);
  }
  inline void Swap(XEvent* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XEvent* New() const final {
    return CreateMaybeMessage<XEvent>(nullptr);
  }

  XEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XEvent>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XEvent& from);
  void MergeFrom(const XEvent& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XEvent* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XEvent";
  }
  protected:
  explicit XEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStatsFieldNumber = 4,
    kMetadataIdFieldNumber = 1,
    kDurationPsFieldNumber = 3,
    kOffsetPsFieldNumber = 2,
    kNumOccurrencesFieldNumber = 5,
  };
  // repeated .tensorflow.profiler.XStat stats = 4;
  int stats_size() const;
  void clear_stats();
  ::tensorflow::profiler::XStat* mutable_stats(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
      mutable_stats();
  const ::tensorflow::profiler::XStat& stats(int index) const;
  ::tensorflow::profiler::XStat* add_stats();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
      stats() const;

  // int64 metadata_id = 1;
  void clear_metadata_id();
  ::PROTOBUF_NAMESPACE_ID::int64 metadata_id() const;
  void set_metadata_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 duration_ps = 3;
  void clear_duration_ps();
  ::PROTOBUF_NAMESPACE_ID::int64 duration_ps() const;
  void set_duration_ps(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 offset_ps = 2;
  private:
  bool has_offset_ps() const;
  public:
  void clear_offset_ps();
  ::PROTOBUF_NAMESPACE_ID::int64 offset_ps() const;
  void set_offset_ps(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 num_occurrences = 5;
  private:
  bool has_num_occurrences() const;
  public:
  void clear_num_occurrences();
  ::PROTOBUF_NAMESPACE_ID::int64 num_occurrences() const;
  void set_num_occurrences(::PROTOBUF_NAMESPACE_ID::int64 value);

  void clear_data();
  DataCase data_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XEvent)
 private:
  class _Internal;
  void set_has_offset_ps();
  void set_has_num_occurrences();

  inline bool has_data() const;
  inline void clear_has_data();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat > stats_;
  ::PROTOBUF_NAMESPACE_ID::int64 metadata_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 duration_ps_;
  union DataUnion {
    DataUnion() {}
    ::PROTOBUF_NAMESPACE_ID::int64 offset_ps_;
    ::PROTOBUF_NAMESPACE_ID::int64 num_occurrences_;
  } data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XStat :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XStat) */ {
 public:
  XStat();
  virtual ~XStat();

  XStat(const XStat& from);
  XStat(XStat&& from) noexcept
    : XStat() {
    *this = ::std::move(from);
  }

  inline XStat& operator=(const XStat& from) {
    CopyFrom(from);
    return *this;
  }
  inline XStat& operator=(XStat&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XStat& default_instance();

  enum ValueCase {
    kDoubleValue = 2,
    kUint64Value = 3,
    kInt64Value = 4,
    kStrValue = 5,
    kBytesValue = 6,
    kRefValue = 7,
    VALUE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XStat* internal_default_instance() {
    return reinterpret_cast<const XStat*>(
               &_XStat_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(XStat& a, XStat& b) {
    a.Swap(&b);
  }
  inline void Swap(XStat* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XStat* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XStat* New() const final {
    return CreateMaybeMessage<XStat>(nullptr);
  }

  XStat* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XStat>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XStat& from);
  void MergeFrom(const XStat& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XStat* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XStat";
  }
  protected:
  explicit XStat(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMetadataIdFieldNumber = 1,
    kDoubleValueFieldNumber = 2,
    kUint64ValueFieldNumber = 3,
    kInt64ValueFieldNumber = 4,
    kStrValueFieldNumber = 5,
    kBytesValueFieldNumber = 6,
    kRefValueFieldNumber = 7,
  };
  // int64 metadata_id = 1;
  void clear_metadata_id();
  ::PROTOBUF_NAMESPACE_ID::int64 metadata_id() const;
  void set_metadata_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // double double_value = 2;
  private:
  bool has_double_value() const;
  public:
  void clear_double_value();
  double double_value() const;
  void set_double_value(double value);

  // uint64 uint64_value = 3;
  private:
  bool has_uint64_value() const;
  public:
  void clear_uint64_value();
  ::PROTOBUF_NAMESPACE_ID::uint64 uint64_value() const;
  void set_uint64_value(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int64 int64_value = 4;
  private:
  bool has_int64_value() const;
  public:
  void clear_int64_value();
  ::PROTOBUF_NAMESPACE_ID::int64 int64_value() const;
  void set_int64_value(::PROTOBUF_NAMESPACE_ID::int64 value);

  // string str_value = 5;
  private:
  bool has_str_value() const;
  public:
  void clear_str_value();
  const std::string& str_value() const;
  void set_str_value(const std::string& value);
  void set_str_value(std::string&& value);
  void set_str_value(const char* value);
  void set_str_value(const char* value, size_t size);
  std::string* mutable_str_value();
  std::string* release_str_value();
  void set_allocated_str_value(std::string* str_value);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_str_value();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_str_value(
      std::string* str_value);

  // bytes bytes_value = 6;
  private:
  bool has_bytes_value() const;
  public:
  void clear_bytes_value();
  const std::string& bytes_value() const;
  void set_bytes_value(const std::string& value);
  void set_bytes_value(std::string&& value);
  void set_bytes_value(const char* value);
  void set_bytes_value(const void* value, size_t size);
  std::string* mutable_bytes_value();
  std::string* release_bytes_value();
  void set_allocated_bytes_value(std::string* bytes_value);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_bytes_value();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_bytes_value(
      std::string* bytes_value);

  // uint64 ref_value = 7;
  private:
  bool has_ref_value() const;
  public:
  void clear_ref_value();
  ::PROTOBUF_NAMESPACE_ID::uint64 ref_value() const;
  void set_ref_value(::PROTOBUF_NAMESPACE_ID::uint64 value);

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XStat)
 private:
  class _Internal;
  void set_has_double_value();
  void set_has_uint64_value();
  void set_has_int64_value();
  void set_has_str_value();
  void set_has_bytes_value();
  void set_has_ref_value();

  inline bool has_value() const;
  inline void clear_has_value();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 metadata_id_;
  union ValueUnion {
    ValueUnion() {}
    double double_value_;
    ::PROTOBUF_NAMESPACE_ID::uint64 uint64_value_;
    ::PROTOBUF_NAMESPACE_ID::int64 int64_value_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr str_value_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bytes_value_;
    ::PROTOBUF_NAMESPACE_ID::uint64 ref_value_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XEventMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XEventMetadata) */ {
 public:
  XEventMetadata();
  virtual ~XEventMetadata();

  XEventMetadata(const XEventMetadata& from);
  XEventMetadata(XEventMetadata&& from) noexcept
    : XEventMetadata() {
    *this = ::std::move(from);
  }

  inline XEventMetadata& operator=(const XEventMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline XEventMetadata& operator=(XEventMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XEventMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XEventMetadata* internal_default_instance() {
    return reinterpret_cast<const XEventMetadata*>(
               &_XEventMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(XEventMetadata& a, XEventMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(XEventMetadata* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XEventMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XEventMetadata* New() const final {
    return CreateMaybeMessage<XEventMetadata>(nullptr);
  }

  XEventMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XEventMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XEventMetadata& from);
  void MergeFrom(const XEventMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XEventMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XEventMetadata";
  }
  protected:
  explicit XEventMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStatsFieldNumber = 5,
    kChildIdFieldNumber = 6,
    kNameFieldNumber = 2,
    kMetadataFieldNumber = 3,
    kDisplayNameFieldNumber = 4,
    kIdFieldNumber = 1,
  };
  // repeated .tensorflow.profiler.XStat stats = 5;
  int stats_size() const;
  void clear_stats();
  ::tensorflow::profiler::XStat* mutable_stats(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
      mutable_stats();
  const ::tensorflow::profiler::XStat& stats(int index) const;
  ::tensorflow::profiler::XStat* add_stats();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
      stats() const;

  // repeated int64 child_id = 6;
  int child_id_size() const;
  void clear_child_id();
  ::PROTOBUF_NAMESPACE_ID::int64 child_id(int index) const;
  void set_child_id(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_child_id(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      child_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_child_id();

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // bytes metadata = 3;
  void clear_metadata();
  const std::string& metadata() const;
  void set_metadata(const std::string& value);
  void set_metadata(std::string&& value);
  void set_metadata(const char* value);
  void set_metadata(const void* value, size_t size);
  std::string* mutable_metadata();
  std::string* release_metadata();
  void set_allocated_metadata(std::string* metadata);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_metadata();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_metadata(
      std::string* metadata);

  // string display_name = 4;
  void clear_display_name();
  const std::string& display_name() const;
  void set_display_name(const std::string& value);
  void set_display_name(std::string&& value);
  void set_display_name(const char* value);
  void set_display_name(const char* value, size_t size);
  std::string* mutable_display_name();
  std::string* release_display_name();
  void set_allocated_display_name(std::string* display_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_display_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_display_name(
      std::string* display_name);

  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XEventMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat > stats_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > child_id_;
  mutable std::atomic<int> _child_id_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr display_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XStatMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XStatMetadata) */ {
 public:
  XStatMetadata();
  virtual ~XStatMetadata();

  XStatMetadata(const XStatMetadata& from);
  XStatMetadata(XStatMetadata&& from) noexcept
    : XStatMetadata() {
    *this = ::std::move(from);
  }

  inline XStatMetadata& operator=(const XStatMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline XStatMetadata& operator=(XStatMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XStatMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XStatMetadata* internal_default_instance() {
    return reinterpret_cast<const XStatMetadata*>(
               &_XStatMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(XStatMetadata& a, XStatMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(XStatMetadata* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XStatMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XStatMetadata* New() const final {
    return CreateMaybeMessage<XStatMetadata>(nullptr);
  }

  XStatMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XStatMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XStatMetadata& from);
  void MergeFrom(const XStatMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XStatMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XStatMetadata";
  }
  protected:
  explicit XStatMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 2,
    kDescriptionFieldNumber = 3,
    kIdFieldNumber = 1,
  };
  // string name = 2;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string description = 3;
  void clear_description();
  const std::string& description() const;
  void set_description(const std::string& value);
  void set_description(std::string&& value);
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  std::string* mutable_description();
  std::string* release_description();
  void set_allocated_description(std::string* description);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_description();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description(
      std::string* description);

  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XStatMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// XSpace

// repeated .tensorflow.profiler.XPlane planes = 1;
inline int XSpace::planes_size() const {
  return planes_.size();
}
inline void XSpace::clear_planes() {
  planes_.Clear();
}
inline ::tensorflow::profiler::XPlane* XSpace::mutable_planes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XSpace.planes)
  return planes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XPlane >*
XSpace::mutable_planes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XSpace.planes)
  return &planes_;
}
inline const ::tensorflow::profiler::XPlane& XSpace::planes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XSpace.planes)
  return planes_.Get(index);
}
inline ::tensorflow::profiler::XPlane* XSpace::add_planes() {
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.planes)
  return planes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XPlane >&
XSpace::planes() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XSpace.planes)
  return planes_;
}

// repeated string errors = 2;
inline int XSpace::errors_size() const {
  return errors_.size();
}
inline void XSpace::clear_errors() {
  errors_.Clear();
}
inline const std::string& XSpace::errors(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XSpace.errors)
  return errors_.Get(index);
}
inline std::string* XSpace::mutable_errors(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XSpace.errors)
  return errors_.Mutable(index);
}
inline void XSpace::set_errors(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.errors)
  errors_.Mutable(index)->assign(value);
}
inline void XSpace::set_errors(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.errors)
  errors_.Mutable(index)->assign(std::move(value));
}
inline void XSpace::set_errors(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  errors_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XSpace.errors)
}
inline void XSpace::set_errors(int index, const char* value, size_t size) {
  errors_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XSpace.errors)
}
inline std::string* XSpace::add_errors() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.profiler.XSpace.errors)
  return errors_.Add();
}
inline void XSpace::add_errors(const std::string& value) {
  errors_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.errors)
}
inline void XSpace::add_errors(std::string&& value) {
  errors_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.errors)
}
inline void XSpace::add_errors(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  errors_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.profiler.XSpace.errors)
}
inline void XSpace::add_errors(const char* value, size_t size) {
  errors_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.profiler.XSpace.errors)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
XSpace::errors() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XSpace.errors)
  return errors_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
XSpace::mutable_errors() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XSpace.errors)
  return &errors_;
}

// repeated string warnings = 3;
inline int XSpace::warnings_size() const {
  return warnings_.size();
}
inline void XSpace::clear_warnings() {
  warnings_.Clear();
}
inline const std::string& XSpace::warnings(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XSpace.warnings)
  return warnings_.Get(index);
}
inline std::string* XSpace::mutable_warnings(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XSpace.warnings)
  return warnings_.Mutable(index);
}
inline void XSpace::set_warnings(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.warnings)
  warnings_.Mutable(index)->assign(value);
}
inline void XSpace::set_warnings(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.warnings)
  warnings_.Mutable(index)->assign(std::move(value));
}
inline void XSpace::set_warnings(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  warnings_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XSpace.warnings)
}
inline void XSpace::set_warnings(int index, const char* value, size_t size) {
  warnings_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XSpace.warnings)
}
inline std::string* XSpace::add_warnings() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.profiler.XSpace.warnings)
  return warnings_.Add();
}
inline void XSpace::add_warnings(const std::string& value) {
  warnings_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.warnings)
}
inline void XSpace::add_warnings(std::string&& value) {
  warnings_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.warnings)
}
inline void XSpace::add_warnings(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  warnings_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.profiler.XSpace.warnings)
}
inline void XSpace::add_warnings(const char* value, size_t size) {
  warnings_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.profiler.XSpace.warnings)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
XSpace::warnings() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XSpace.warnings)
  return warnings_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
XSpace::mutable_warnings() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XSpace.warnings)
  return &warnings_;
}

// repeated string hostnames = 4;
inline int XSpace::hostnames_size() const {
  return hostnames_.size();
}
inline void XSpace::clear_hostnames() {
  hostnames_.Clear();
}
inline const std::string& XSpace::hostnames(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XSpace.hostnames)
  return hostnames_.Get(index);
}
inline std::string* XSpace::mutable_hostnames(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XSpace.hostnames)
  return hostnames_.Mutable(index);
}
inline void XSpace::set_hostnames(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.hostnames)
  hostnames_.Mutable(index)->assign(value);
}
inline void XSpace::set_hostnames(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.hostnames)
  hostnames_.Mutable(index)->assign(std::move(value));
}
inline void XSpace::set_hostnames(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  hostnames_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XSpace.hostnames)
}
inline void XSpace::set_hostnames(int index, const char* value, size_t size) {
  hostnames_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XSpace.hostnames)
}
inline std::string* XSpace::add_hostnames() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.profiler.XSpace.hostnames)
  return hostnames_.Add();
}
inline void XSpace::add_hostnames(const std::string& value) {
  hostnames_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.hostnames)
}
inline void XSpace::add_hostnames(std::string&& value) {
  hostnames_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.hostnames)
}
inline void XSpace::add_hostnames(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  hostnames_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.profiler.XSpace.hostnames)
}
inline void XSpace::add_hostnames(const char* value, size_t size) {
  hostnames_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.profiler.XSpace.hostnames)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
XSpace::hostnames() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XSpace.hostnames)
  return hostnames_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
XSpace::mutable_hostnames() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XSpace.hostnames)
  return &hostnames_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// XPlane

// int64 id = 1;
inline void XPlane::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XPlane::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XPlane.id)
  return id_;
}
inline void XPlane::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XPlane.id)
}

// string name = 2;
inline void XPlane::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& XPlane::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XPlane.name)
  return name_.Get();
}
inline void XPlane::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XPlane.name)
}
inline void XPlane::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.XPlane.name)
}
inline void XPlane::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XPlane.name)
}
inline void XPlane::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XPlane.name)
}
inline std::string* XPlane::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XPlane.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* XPlane::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XPlane.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void XPlane::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XPlane.name)
}
inline std::string* XPlane::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.profiler.XPlane.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void XPlane::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.profiler.XPlane.name)
}

// repeated .tensorflow.profiler.XLine lines = 3;
inline int XPlane::lines_size() const {
  return lines_.size();
}
inline void XPlane::clear_lines() {
  lines_.Clear();
}
inline ::tensorflow::profiler::XLine* XPlane::mutable_lines(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XPlane.lines)
  return lines_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XLine >*
XPlane::mutable_lines() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XPlane.lines)
  return &lines_;
}
inline const ::tensorflow::profiler::XLine& XPlane::lines(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XPlane.lines)
  return lines_.Get(index);
}
inline ::tensorflow::profiler::XLine* XPlane::add_lines() {
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XPlane.lines)
  return lines_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XLine >&
XPlane::lines() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XPlane.lines)
  return lines_;
}

// map<int64, .tensorflow.profiler.XEventMetadata> event_metadata = 4;
inline int XPlane::event_metadata_size() const {
  return event_metadata_.size();
}
inline void XPlane::clear_event_metadata() {
  event_metadata_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XEventMetadata >&
XPlane::event_metadata() const {
  // @@protoc_insertion_point(field_map:tensorflow.profiler.XPlane.event_metadata)
  return event_metadata_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XEventMetadata >*
XPlane::mutable_event_metadata() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.profiler.XPlane.event_metadata)
  return event_metadata_.MutableMap();
}

// map<int64, .tensorflow.profiler.XStatMetadata> stat_metadata = 5;
inline int XPlane::stat_metadata_size() const {
  return stat_metadata_.size();
}
inline void XPlane::clear_stat_metadata() {
  stat_metadata_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XStatMetadata >&
XPlane::stat_metadata() const {
  // @@protoc_insertion_point(field_map:tensorflow.profiler.XPlane.stat_metadata)
  return stat_metadata_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::profiler::XStatMetadata >*
XPlane::mutable_stat_metadata() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.profiler.XPlane.stat_metadata)
  return stat_metadata_.MutableMap();
}

// repeated .tensorflow.profiler.XStat stats = 6;
inline int XPlane::stats_size() const {
  return stats_.size();
}
inline void XPlane::clear_stats() {
  stats_.Clear();
}
inline ::tensorflow::profiler::XStat* XPlane::mutable_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XPlane.stats)
  return stats_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
XPlane::mutable_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XPlane.stats)
  return &stats_;
}
inline const ::tensorflow::profiler::XStat& XPlane::stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XPlane.stats)
  return stats_.Get(index);
}
inline ::tensorflow::profiler::XStat* XPlane::add_stats() {
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XPlane.stats)
  return stats_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
XPlane::stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XPlane.stats)
  return stats_;
}

// -------------------------------------------------------------------

// XLine

// int64 id = 1;
inline void XLine::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XLine::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.id)
  return id_;
}
inline void XLine::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.id)
}

// int64 display_id = 10;
inline void XLine::clear_display_id() {
  display_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XLine::display_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.display_id)
  return display_id_;
}
inline void XLine::set_display_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  display_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.display_id)
}

// string name = 2;
inline void XLine::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& XLine::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.name)
  return name_.Get();
}
inline void XLine::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.name)
}
inline void XLine::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.XLine.name)
}
inline void XLine::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XLine.name)
}
inline void XLine::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XLine.name)
}
inline std::string* XLine::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XLine.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* XLine::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XLine.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void XLine::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XLine.name)
}
inline std::string* XLine::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.profiler.XLine.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void XLine::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.profiler.XLine.name)
}

// string display_name = 11;
inline void XLine::clear_display_name() {
  display_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& XLine::display_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.display_name)
  return display_name_.Get();
}
inline void XLine::set_display_name(const std::string& value) {
  
  display_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.display_name)
}
inline void XLine::set_display_name(std::string&& value) {
  
  display_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.XLine.display_name)
}
inline void XLine::set_display_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  display_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XLine.display_name)
}
inline void XLine::set_display_name(const char* value,
    size_t size) {
  
  display_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XLine.display_name)
}
inline std::string* XLine::mutable_display_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XLine.display_name)
  return display_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* XLine::release_display_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XLine.display_name)
  
  return display_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void XLine::set_allocated_display_name(std::string* display_name) {
  if (display_name != nullptr) {
    
  } else {
    
  }
  display_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), display_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XLine.display_name)
}
inline std::string* XLine::unsafe_arena_release_display_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.profiler.XLine.display_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return display_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void XLine::unsafe_arena_set_allocated_display_name(
    std::string* display_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (display_name != nullptr) {
    
  } else {
    
  }
  display_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      display_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.profiler.XLine.display_name)
}

// int64 timestamp_ns = 3;
inline void XLine::clear_timestamp_ns() {
  timestamp_ns_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XLine::timestamp_ns() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.timestamp_ns)
  return timestamp_ns_;
}
inline void XLine::set_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  timestamp_ns_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.timestamp_ns)
}

// int64 duration_ps = 9;
inline void XLine::clear_duration_ps() {
  duration_ps_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XLine::duration_ps() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.duration_ps)
  return duration_ps_;
}
inline void XLine::set_duration_ps(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  duration_ps_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.duration_ps)
}

// repeated .tensorflow.profiler.XEvent events = 4;
inline int XLine::events_size() const {
  return events_.size();
}
inline void XLine::clear_events() {
  events_.Clear();
}
inline ::tensorflow::profiler::XEvent* XLine::mutable_events(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XLine.events)
  return events_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XEvent >*
XLine::mutable_events() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XLine.events)
  return &events_;
}
inline const ::tensorflow::profiler::XEvent& XLine::events(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.events)
  return events_.Get(index);
}
inline ::tensorflow::profiler::XEvent* XLine::add_events() {
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XLine.events)
  return events_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XEvent >&
XLine::events() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XLine.events)
  return events_;
}

// -------------------------------------------------------------------

// XEvent

// int64 metadata_id = 1;
inline void XEvent::clear_metadata_id() {
  metadata_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XEvent::metadata_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEvent.metadata_id)
  return metadata_id_;
}
inline void XEvent::set_metadata_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  metadata_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEvent.metadata_id)
}

// int64 offset_ps = 2;
inline bool XEvent::has_offset_ps() const {
  return data_case() == kOffsetPs;
}
inline void XEvent::set_has_offset_ps() {
  _oneof_case_[0] = kOffsetPs;
}
inline void XEvent::clear_offset_ps() {
  if (has_offset_ps()) {
    data_.offset_ps_ = PROTOBUF_LONGLONG(0);
    clear_has_data();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XEvent::offset_ps() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEvent.offset_ps)
  if (has_offset_ps()) {
    return data_.offset_ps_;
  }
  return PROTOBUF_LONGLONG(0);
}
inline void XEvent::set_offset_ps(::PROTOBUF_NAMESPACE_ID::int64 value) {
  if (!has_offset_ps()) {
    clear_data();
    set_has_offset_ps();
  }
  data_.offset_ps_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEvent.offset_ps)
}

// int64 num_occurrences = 5;
inline bool XEvent::has_num_occurrences() const {
  return data_case() == kNumOccurrences;
}
inline void XEvent::set_has_num_occurrences() {
  _oneof_case_[0] = kNumOccurrences;
}
inline void XEvent::clear_num_occurrences() {
  if (has_num_occurrences()) {
    data_.num_occurrences_ = PROTOBUF_LONGLONG(0);
    clear_has_data();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XEvent::num_occurrences() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEvent.num_occurrences)
  if (has_num_occurrences()) {
    return data_.num_occurrences_;
  }
  return PROTOBUF_LONGLONG(0);
}
inline void XEvent::set_num_occurrences(::PROTOBUF_NAMESPACE_ID::int64 value) {
  if (!has_num_occurrences()) {
    clear_data();
    set_has_num_occurrences();
  }
  data_.num_occurrences_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEvent.num_occurrences)
}

// int64 duration_ps = 3;
inline void XEvent::clear_duration_ps() {
  duration_ps_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XEvent::duration_ps() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEvent.duration_ps)
  return duration_ps_;
}
inline void XEvent::set_duration_ps(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  duration_ps_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEvent.duration_ps)
}

// repeated .tensorflow.profiler.XStat stats = 4;
inline int XEvent::stats_size() const {
  return stats_.size();
}
inline void XEvent::clear_stats() {
  stats_.Clear();
}
inline ::tensorflow::profiler::XStat* XEvent::mutable_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XEvent.stats)
  return stats_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
XEvent::mutable_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XEvent.stats)
  return &stats_;
}
inline const ::tensorflow::profiler::XStat& XEvent::stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEvent.stats)
  return stats_.Get(index);
}
inline ::tensorflow::profiler::XStat* XEvent::add_stats() {
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XEvent.stats)
  return stats_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
XEvent::stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XEvent.stats)
  return stats_;
}

inline bool XEvent::has_data() const {
  return data_case() != DATA_NOT_SET;
}
inline void XEvent::clear_has_data() {
  _oneof_case_[0] = DATA_NOT_SET;
}
inline XEvent::DataCase XEvent::data_case() const {
  return XEvent::DataCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// XStat

// int64 metadata_id = 1;
inline void XStat::clear_metadata_id() {
  metadata_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XStat::metadata_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.metadata_id)
  return metadata_id_;
}
inline void XStat::set_metadata_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  metadata_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.metadata_id)
}

// double double_value = 2;
inline bool XStat::has_double_value() const {
  return value_case() == kDoubleValue;
}
inline void XStat::set_has_double_value() {
  _oneof_case_[0] = kDoubleValue;
}
inline void XStat::clear_double_value() {
  if (has_double_value()) {
    value_.double_value_ = 0;
    clear_has_value();
  }
}
inline double XStat::double_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.double_value)
  if (has_double_value()) {
    return value_.double_value_;
  }
  return 0;
}
inline void XStat::set_double_value(double value) {
  if (!has_double_value()) {
    clear_value();
    set_has_double_value();
  }
  value_.double_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.double_value)
}

// uint64 uint64_value = 3;
inline bool XStat::has_uint64_value() const {
  return value_case() == kUint64Value;
}
inline void XStat::set_has_uint64_value() {
  _oneof_case_[0] = kUint64Value;
}
inline void XStat::clear_uint64_value() {
  if (has_uint64_value()) {
    value_.uint64_value_ = PROTOBUF_ULONGLONG(0);
    clear_has_value();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 XStat::uint64_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.uint64_value)
  if (has_uint64_value()) {
    return value_.uint64_value_;
  }
  return PROTOBUF_ULONGLONG(0);
}
inline void XStat::set_uint64_value(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  if (!has_uint64_value()) {
    clear_value();
    set_has_uint64_value();
  }
  value_.uint64_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.uint64_value)
}

// int64 int64_value = 4;
inline bool XStat::has_int64_value() const {
  return value_case() == kInt64Value;
}
inline void XStat::set_has_int64_value() {
  _oneof_case_[0] = kInt64Value;
}
inline void XStat::clear_int64_value() {
  if (has_int64_value()) {
    value_.int64_value_ = PROTOBUF_LONGLONG(0);
    clear_has_value();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XStat::int64_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.int64_value)
  if (has_int64_value()) {
    return value_.int64_value_;
  }
  return PROTOBUF_LONGLONG(0);
}
inline void XStat::set_int64_value(::PROTOBUF_NAMESPACE_ID::int64 value) {
  if (!has_int64_value()) {
    clear_value();
    set_has_int64_value();
  }
  value_.int64_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.int64_value)
}

// string str_value = 5;
inline bool XStat::has_str_value() const {
  return value_case() == kStrValue;
}
inline void XStat::set_has_str_value() {
  _oneof_case_[0] = kStrValue;
}
inline void XStat::clear_str_value() {
  if (has_str_value()) {
    value_.str_value_.Destroy(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_value();
  }
}
inline const std::string& XStat::str_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.str_value)
  if (has_str_value()) {
    return value_.str_value_.Get();
  }
  return *&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void XStat::set_str_value(const std::string& value) {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.str_value)
}
inline void XStat::set_str_value(std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.str_value)
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.XStat.str_value)
}
inline void XStat::set_str_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XStat.str_value)
}
inline void XStat::set_str_value(const char* value,
                             size_t size) {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.Set(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XStat.str_value)
}
inline std::string* XStat::mutable_str_value() {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return value_.str_value_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XStat.str_value)
}
inline std::string* XStat::release_str_value() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XStat.str_value)
  if (has_str_value()) {
    clear_has_value();
    return value_.str_value_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void XStat::set_allocated_str_value(std::string* str_value) {
  if (has_value()) {
    clear_value();
  }
  if (str_value != nullptr) {
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(str_value);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XStat.str_value)
}
inline std::string* XStat::unsafe_arena_release_str_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.profiler.XStat.str_value)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (has_str_value()) {
    clear_has_value();
    return value_.str_value_.UnsafeArenaRelease(
        &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void XStat::unsafe_arena_set_allocated_str_value(std::string* str_value) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (!has_str_value()) {
    value_.str_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (str_value) {
    set_has_str_value();
    value_.str_value_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), str_value, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.profiler.XStat.str_value)
}

// bytes bytes_value = 6;
inline bool XStat::has_bytes_value() const {
  return value_case() == kBytesValue;
}
inline void XStat::set_has_bytes_value() {
  _oneof_case_[0] = kBytesValue;
}
inline void XStat::clear_bytes_value() {
  if (has_bytes_value()) {
    value_.bytes_value_.Destroy(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_value();
  }
}
inline const std::string& XStat::bytes_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.bytes_value)
  if (has_bytes_value()) {
    return value_.bytes_value_.Get();
  }
  return *&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void XStat::set_bytes_value(const std::string& value) {
  if (!has_bytes_value()) {
    clear_value();
    set_has_bytes_value();
    value_.bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.bytes_value)
}
inline void XStat::set_bytes_value(std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.bytes_value)
  if (!has_bytes_value()) {
    clear_value();
    set_has_bytes_value();
    value_.bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.bytes_value_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.XStat.bytes_value)
}
inline void XStat::set_bytes_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  if (!has_bytes_value()) {
    clear_value();
    set_has_bytes_value();
    value_.bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XStat.bytes_value)
}
inline void XStat::set_bytes_value(const void* value,
                             size_t size) {
  if (!has_bytes_value()) {
    clear_value();
    set_has_bytes_value();
    value_.bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.bytes_value_.Set(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XStat.bytes_value)
}
inline std::string* XStat::mutable_bytes_value() {
  if (!has_bytes_value()) {
    clear_value();
    set_has_bytes_value();
    value_.bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return value_.bytes_value_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XStat.bytes_value)
}
inline std::string* XStat::release_bytes_value() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XStat.bytes_value)
  if (has_bytes_value()) {
    clear_has_value();
    return value_.bytes_value_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void XStat::set_allocated_bytes_value(std::string* bytes_value) {
  if (has_value()) {
    clear_value();
  }
  if (bytes_value != nullptr) {
    set_has_bytes_value();
    value_.bytes_value_.UnsafeSetDefault(bytes_value);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XStat.bytes_value)
}
inline std::string* XStat::unsafe_arena_release_bytes_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.profiler.XStat.bytes_value)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (has_bytes_value()) {
    clear_has_value();
    return value_.bytes_value_.UnsafeArenaRelease(
        &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void XStat::unsafe_arena_set_allocated_bytes_value(std::string* bytes_value) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (!has_bytes_value()) {
    value_.bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (bytes_value) {
    set_has_bytes_value();
    value_.bytes_value_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bytes_value, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.profiler.XStat.bytes_value)
}

// uint64 ref_value = 7;
inline bool XStat::has_ref_value() const {
  return value_case() == kRefValue;
}
inline void XStat::set_has_ref_value() {
  _oneof_case_[0] = kRefValue;
}
inline void XStat::clear_ref_value() {
  if (has_ref_value()) {
    value_.ref_value_ = PROTOBUF_ULONGLONG(0);
    clear_has_value();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 XStat::ref_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.ref_value)
  if (has_ref_value()) {
    return value_.ref_value_;
  }
  return PROTOBUF_ULONGLONG(0);
}
inline void XStat::set_ref_value(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  if (!has_ref_value()) {
    clear_value();
    set_has_ref_value();
  }
  value_.ref_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.ref_value)
}

inline bool XStat::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void XStat::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline XStat::ValueCase XStat::value_case() const {
  return XStat::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// XEventMetadata

// int64 id = 1;
inline void XEventMetadata::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XEventMetadata::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.id)
  return id_;
}
inline void XEventMetadata::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEventMetadata.id)
}

// string name = 2;
inline void XEventMetadata::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& XEventMetadata::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.name)
  return name_.Get();
}
inline void XEventMetadata::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEventMetadata.name)
}
inline void XEventMetadata::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.XEventMetadata.name)
}
inline void XEventMetadata::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XEventMetadata.name)
}
inline void XEventMetadata::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XEventMetadata.name)
}
inline std::string* XEventMetadata::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XEventMetadata.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* XEventMetadata::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XEventMetadata.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void XEventMetadata::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XEventMetadata.name)
}
inline std::string* XEventMetadata::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.profiler.XEventMetadata.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void XEventMetadata::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.profiler.XEventMetadata.name)
}

// string display_name = 4;
inline void XEventMetadata::clear_display_name() {
  display_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& XEventMetadata::display_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.display_name)
  return display_name_.Get();
}
inline void XEventMetadata::set_display_name(const std::string& value) {
  
  display_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEventMetadata.display_name)
}
inline void XEventMetadata::set_display_name(std::string&& value) {
  
  display_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.XEventMetadata.display_name)
}
inline void XEventMetadata::set_display_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  display_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XEventMetadata.display_name)
}
inline void XEventMetadata::set_display_name(const char* value,
    size_t size) {
  
  display_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XEventMetadata.display_name)
}
inline std::string* XEventMetadata::mutable_display_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XEventMetadata.display_name)
  return display_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* XEventMetadata::release_display_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XEventMetadata.display_name)
  
  return display_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void XEventMetadata::set_allocated_display_name(std::string* display_name) {
  if (display_name != nullptr) {
    
  } else {
    
  }
  display_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), display_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XEventMetadata.display_name)
}
inline std::string* XEventMetadata::unsafe_arena_release_display_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.profiler.XEventMetadata.display_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return display_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void XEventMetadata::unsafe_arena_set_allocated_display_name(
    std::string* display_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (display_name != nullptr) {
    
  } else {
    
  }
  display_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      display_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.profiler.XEventMetadata.display_name)
}

// bytes metadata = 3;
inline void XEventMetadata::clear_metadata() {
  metadata_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& XEventMetadata::metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.metadata)
  return metadata_.Get();
}
inline void XEventMetadata::set_metadata(const std::string& value) {
  
  metadata_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEventMetadata.metadata)
}
inline void XEventMetadata::set_metadata(std::string&& value) {
  
  metadata_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.XEventMetadata.metadata)
}
inline void XEventMetadata::set_metadata(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  metadata_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XEventMetadata.metadata)
}
inline void XEventMetadata::set_metadata(const void* value,
    size_t size) {
  
  metadata_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XEventMetadata.metadata)
}
inline std::string* XEventMetadata::mutable_metadata() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XEventMetadata.metadata)
  return metadata_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* XEventMetadata::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XEventMetadata.metadata)
  
  return metadata_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void XEventMetadata::set_allocated_metadata(std::string* metadata) {
  if (metadata != nullptr) {
    
  } else {
    
  }
  metadata_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), metadata,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XEventMetadata.metadata)
}
inline std::string* XEventMetadata::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.profiler.XEventMetadata.metadata)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return metadata_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void XEventMetadata::unsafe_arena_set_allocated_metadata(
    std::string* metadata) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (metadata != nullptr) {
    
  } else {
    
  }
  metadata_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      metadata, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.profiler.XEventMetadata.metadata)
}

// repeated .tensorflow.profiler.XStat stats = 5;
inline int XEventMetadata::stats_size() const {
  return stats_.size();
}
inline void XEventMetadata::clear_stats() {
  stats_.Clear();
}
inline ::tensorflow::profiler::XStat* XEventMetadata::mutable_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XEventMetadata.stats)
  return stats_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
XEventMetadata::mutable_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XEventMetadata.stats)
  return &stats_;
}
inline const ::tensorflow::profiler::XStat& XEventMetadata::stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.stats)
  return stats_.Get(index);
}
inline ::tensorflow::profiler::XStat* XEventMetadata::add_stats() {
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XEventMetadata.stats)
  return stats_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
XEventMetadata::stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XEventMetadata.stats)
  return stats_;
}

// repeated int64 child_id = 6;
inline int XEventMetadata::child_id_size() const {
  return child_id_.size();
}
inline void XEventMetadata::clear_child_id() {
  child_id_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XEventMetadata::child_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.child_id)
  return child_id_.Get(index);
}
inline void XEventMetadata::set_child_id(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  child_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEventMetadata.child_id)
}
inline void XEventMetadata::add_child_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  child_id_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XEventMetadata.child_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
XEventMetadata::child_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XEventMetadata.child_id)
  return child_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
XEventMetadata::mutable_child_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XEventMetadata.child_id)
  return &child_id_;
}

// -------------------------------------------------------------------

// XStatMetadata

// int64 id = 1;
inline void XStatMetadata::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XStatMetadata::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStatMetadata.id)
  return id_;
}
inline void XStatMetadata::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStatMetadata.id)
}

// string name = 2;
inline void XStatMetadata::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& XStatMetadata::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStatMetadata.name)
  return name_.Get();
}
inline void XStatMetadata::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStatMetadata.name)
}
inline void XStatMetadata::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.XStatMetadata.name)
}
inline void XStatMetadata::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XStatMetadata.name)
}
inline void XStatMetadata::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XStatMetadata.name)
}
inline std::string* XStatMetadata::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XStatMetadata.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* XStatMetadata::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XStatMetadata.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void XStatMetadata::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XStatMetadata.name)
}
inline std::string* XStatMetadata::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.profiler.XStatMetadata.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void XStatMetadata::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.profiler.XStatMetadata.name)
}

// string description = 3;
inline void XStatMetadata::clear_description() {
  description_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& XStatMetadata::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStatMetadata.description)
  return description_.Get();
}
inline void XStatMetadata::set_description(const std::string& value) {
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStatMetadata.description)
}
inline void XStatMetadata::set_description(std::string&& value) {
  
  description_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.XStatMetadata.description)
}
inline void XStatMetadata::set_description(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XStatMetadata.description)
}
inline void XStatMetadata::set_description(const char* value,
    size_t size) {
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XStatMetadata.description)
}
inline std::string* XStatMetadata::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XStatMetadata.description)
  return description_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* XStatMetadata::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XStatMetadata.description)
  
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void XStatMetadata::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XStatMetadata.description)
}
inline std::string* XStatMetadata::unsafe_arena_release_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.profiler.XStatMetadata.description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return description_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void XStatMetadata::unsafe_arena_set_allocated_description(
    std::string* description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (description != nullptr) {
    
  } else {
    
  }
  description_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.profiler.XStatMetadata.description)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace profiler
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprotobuf_2fxplane_2eproto
