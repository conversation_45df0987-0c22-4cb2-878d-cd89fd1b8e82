import json
from pathlib import Path


def load_rules():
    """
    加载规则文件
    :return:
    """
    # 获取当前执行文件的路径
    current_file = Path(__file__)
    # 构建规则文件的路径
    rules_file_path = current_file.parent / 'filter_rules.json'

    with open(rules_file_path, 'r') as file:
        config = json.load(file)
    return config["rules"]


def method1_handler(analysis_entity, actions):
    """
    模式1处理
    如果trigger标签的值为1，将SN的值设置为0
    :param analysis_entity: 分析实体对象
    :param actions: 动作集
    :return:
    """
    keys_to_check = [action for action in actions]

    if any(getattr(analysis_entity.ArrhythmiaDiagnosis, key) == 1 for key in keys_to_check):
        setattr(analysis_entity.ArrhythmiaDiagnosis, 'SN', 0)

    return analysis_entity


def method2_handler(analysis_entity, actions):
    """
    模式2处理
    将action的key按优先级排序，按顺序判断key的value是否为1，如果实则将后续的key的值设置为0
    :param analysis_entity: 分析实体对象
    :param actions: 动作集
    :return:
    """
    # 根据index排序
    sorted_actions = sorted(actions, key=lambda x: x['index'])
    for action in sorted_actions:
        if getattr(analysis_entity.ArrhythmiaDiagnosis, action['key']) == 1:
            for subsequent_action in sorted_actions:
                if subsequent_action['index'] > action['index'] and getattr(analysis_entity.ArrhythmiaDiagnosis, subsequent_action['key']) == 1:
                    setattr(analysis_entity.ArrhythmiaDiagnosis, subsequent_action['key'], 0)

    return analysis_entity


def method3_handler(analysis_entity, actions):
    """
    模式3处理
    min 当待判断的结论符合时，hr需要大于default_hr, 否则为0
    max 当待判断的结论符合时，hr需要小于default_hr, 否则为0
    :param analysis_entity: 分析实体对象
    :param actions:
    :return:
    """
    hr = analysis_entity.PQRSTC.HR

    for conclusion in actions['min']['conclusion']:
        if getattr(analysis_entity.ArrhythmiaDiagnosis, conclusion) == 1 and hr < actions['min']['default_hr']:
            setattr(analysis_entity.ArrhythmiaDiagnosis, conclusion, 0)

    for conclusion in actions['max']['conclusion']:
        if getattr(analysis_entity.ArrhythmiaDiagnosis, conclusion) == 1 and hr > actions['max']['default_hr']:
            setattr(analysis_entity.ArrhythmiaDiagnosis, conclusion, 0)

    return analysis_entity


def method4_handler(analysis_entity, actions):
    """
    模式4处理
    当诊断为LQT时，判断QTC的值是否超过阈值
    :param analysis_entity: 分析实体对象
    :param actions:
    :return:
    """
    comparison_operators = {
        '>': lambda a, b: a > b,
        '<': lambda a, b: a < b,
        '==': lambda a, b: a == b,
        '>=': lambda a, b: a >= b,
        '<=': lambda a, b: a <= b,
        '!=': lambda a, b: a != b
    }

    for action in actions:
        if getattr(analysis_entity.ArrhythmiaDiagnosis, action['key']) == 1:

            # 动态获取比较函数
            compare = comparison_operators.get(action['condition'])

            if compare(__get_nested_attribute(analysis_entity, action['indicators']), action["value"]):
                setattr(analysis_entity.ArrhythmiaDiagnosis, action['key'], 0)

    return analysis_entity


def apply_rules(analysis_entity):
    """
    应用规则
    :param analysis_entity: 疾病分析实体对象
    :return: 规则处理后的分析实体
    """
    handler_map = {
        1: method1_handler,
        2: method2_handler,
        3: method3_handler,
        4: method4_handler
    }

    # 加载规则
    rules = load_rules()

    sorted_rules = sorted(rules, key=lambda x: x['priority'])

    for rule in sorted_rules:
        method = rule['method']
        actions = rule['actions']

        # 根据方法获取对应的处理策略
        analysis_entity = handler_map.get(method)(analysis_entity, actions)

    return analysis_entity


def __get_nested_attribute(obj, attr_path):
    """
    根据属性路径获取嵌套属性的值
    :param obj: 对象
    :param attr_path: 属性路径，如 "PQRSTC.QTC"
    :return: 属性值
    """
    attributes = attr_path.split('.')
    for attr in attributes:
        obj = getattr(obj, attr, None)
        if obj is None:
            break
    return obj
