// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/rewriter_config.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/protobuf/verifier_config.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
namespace tensorflow {
class AutoParallelOptions;
class AutoParallelOptionsDefaultTypeInternal;
extern AutoParallelOptionsDefaultTypeInternal _AutoParallelOptions_default_instance_;
class RewriterConfig;
class RewriterConfigDefaultTypeInternal;
extern RewriterConfigDefaultTypeInternal _RewriterConfig_default_instance_;
class RewriterConfig_CustomGraphOptimizer;
class RewriterConfig_CustomGraphOptimizerDefaultTypeInternal;
extern RewriterConfig_CustomGraphOptimizerDefaultTypeInternal _RewriterConfig_CustomGraphOptimizer_default_instance_;
class RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse;
class RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUseDefaultTypeInternal;
extern RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUseDefaultTypeInternal _RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse_default_instance_;
class ScopedAllocatorOptions;
class ScopedAllocatorOptionsDefaultTypeInternal;
extern ScopedAllocatorOptionsDefaultTypeInternal _ScopedAllocatorOptions_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::AutoParallelOptions* Arena::CreateMaybeMessage<::tensorflow::AutoParallelOptions>(Arena*);
template<> ::tensorflow::RewriterConfig* Arena::CreateMaybeMessage<::tensorflow::RewriterConfig>(Arena*);
template<> ::tensorflow::RewriterConfig_CustomGraphOptimizer* Arena::CreateMaybeMessage<::tensorflow::RewriterConfig_CustomGraphOptimizer>(Arena*);
template<> ::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ScopedAllocatorOptions* Arena::CreateMaybeMessage<::tensorflow::ScopedAllocatorOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum RewriterConfig_Toggle : int {
  RewriterConfig_Toggle_DEFAULT = 0,
  RewriterConfig_Toggle_ON = 1,
  RewriterConfig_Toggle_OFF = 2,
  RewriterConfig_Toggle_AGGRESSIVE = 3,
  RewriterConfig_Toggle_RewriterConfig_Toggle_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  RewriterConfig_Toggle_RewriterConfig_Toggle_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool RewriterConfig_Toggle_IsValid(int value);
constexpr RewriterConfig_Toggle RewriterConfig_Toggle_Toggle_MIN = RewriterConfig_Toggle_DEFAULT;
constexpr RewriterConfig_Toggle RewriterConfig_Toggle_Toggle_MAX = RewriterConfig_Toggle_AGGRESSIVE;
constexpr int RewriterConfig_Toggle_Toggle_ARRAYSIZE = RewriterConfig_Toggle_Toggle_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RewriterConfig_Toggle_descriptor();
template<typename T>
inline const std::string& RewriterConfig_Toggle_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RewriterConfig_Toggle>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RewriterConfig_Toggle_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RewriterConfig_Toggle_descriptor(), enum_t_value);
}
inline bool RewriterConfig_Toggle_Parse(
    const std::string& name, RewriterConfig_Toggle* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RewriterConfig_Toggle>(
    RewriterConfig_Toggle_descriptor(), name, value);
}
enum RewriterConfig_CpuLayout : int {
  RewriterConfig_CpuLayout_NO_CONVERSION_ON_CPU = 0,
  RewriterConfig_CpuLayout_NCHW_TO_NHWC = 1,
  RewriterConfig_CpuLayout_NHWC_TO_NCHW = 2,
  RewriterConfig_CpuLayout_RewriterConfig_CpuLayout_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  RewriterConfig_CpuLayout_RewriterConfig_CpuLayout_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool RewriterConfig_CpuLayout_IsValid(int value);
constexpr RewriterConfig_CpuLayout RewriterConfig_CpuLayout_CpuLayout_MIN = RewriterConfig_CpuLayout_NO_CONVERSION_ON_CPU;
constexpr RewriterConfig_CpuLayout RewriterConfig_CpuLayout_CpuLayout_MAX = RewriterConfig_CpuLayout_NHWC_TO_NCHW;
constexpr int RewriterConfig_CpuLayout_CpuLayout_ARRAYSIZE = RewriterConfig_CpuLayout_CpuLayout_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RewriterConfig_CpuLayout_descriptor();
template<typename T>
inline const std::string& RewriterConfig_CpuLayout_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RewriterConfig_CpuLayout>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RewriterConfig_CpuLayout_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RewriterConfig_CpuLayout_descriptor(), enum_t_value);
}
inline bool RewriterConfig_CpuLayout_Parse(
    const std::string& name, RewriterConfig_CpuLayout* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RewriterConfig_CpuLayout>(
    RewriterConfig_CpuLayout_descriptor(), name, value);
}
enum RewriterConfig_NumIterationsType : int {
  RewriterConfig_NumIterationsType_DEFAULT_NUM_ITERS = 0,
  RewriterConfig_NumIterationsType_ONE = 1,
  RewriterConfig_NumIterationsType_TWO = 2,
  RewriterConfig_NumIterationsType_RewriterConfig_NumIterationsType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  RewriterConfig_NumIterationsType_RewriterConfig_NumIterationsType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool RewriterConfig_NumIterationsType_IsValid(int value);
constexpr RewriterConfig_NumIterationsType RewriterConfig_NumIterationsType_NumIterationsType_MIN = RewriterConfig_NumIterationsType_DEFAULT_NUM_ITERS;
constexpr RewriterConfig_NumIterationsType RewriterConfig_NumIterationsType_NumIterationsType_MAX = RewriterConfig_NumIterationsType_TWO;
constexpr int RewriterConfig_NumIterationsType_NumIterationsType_ARRAYSIZE = RewriterConfig_NumIterationsType_NumIterationsType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RewriterConfig_NumIterationsType_descriptor();
template<typename T>
inline const std::string& RewriterConfig_NumIterationsType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RewriterConfig_NumIterationsType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RewriterConfig_NumIterationsType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RewriterConfig_NumIterationsType_descriptor(), enum_t_value);
}
inline bool RewriterConfig_NumIterationsType_Parse(
    const std::string& name, RewriterConfig_NumIterationsType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RewriterConfig_NumIterationsType>(
    RewriterConfig_NumIterationsType_descriptor(), name, value);
}
enum RewriterConfig_MemOptType : int {
  RewriterConfig_MemOptType_DEFAULT_MEM_OPT = 0,
  RewriterConfig_MemOptType_NO_MEM_OPT = 1,
  RewriterConfig_MemOptType_MANUAL = 2,
  RewriterConfig_MemOptType_SWAPPING_HEURISTICS = 4,
  RewriterConfig_MemOptType_RECOMPUTATION_HEURISTICS = 5,
  RewriterConfig_MemOptType_SCHEDULING_HEURISTICS = 6,
  RewriterConfig_MemOptType_HEURISTICS = 3,
  RewriterConfig_MemOptType_RewriterConfig_MemOptType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  RewriterConfig_MemOptType_RewriterConfig_MemOptType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool RewriterConfig_MemOptType_IsValid(int value);
constexpr RewriterConfig_MemOptType RewriterConfig_MemOptType_MemOptType_MIN = RewriterConfig_MemOptType_DEFAULT_MEM_OPT;
constexpr RewriterConfig_MemOptType RewriterConfig_MemOptType_MemOptType_MAX = RewriterConfig_MemOptType_SCHEDULING_HEURISTICS;
constexpr int RewriterConfig_MemOptType_MemOptType_ARRAYSIZE = RewriterConfig_MemOptType_MemOptType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RewriterConfig_MemOptType_descriptor();
template<typename T>
inline const std::string& RewriterConfig_MemOptType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RewriterConfig_MemOptType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RewriterConfig_MemOptType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RewriterConfig_MemOptType_descriptor(), enum_t_value);
}
inline bool RewriterConfig_MemOptType_Parse(
    const std::string& name, RewriterConfig_MemOptType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RewriterConfig_MemOptType>(
    RewriterConfig_MemOptType_descriptor(), name, value);
}
// ===================================================================

class AutoParallelOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutoParallelOptions) */ {
 public:
  AutoParallelOptions();
  virtual ~AutoParallelOptions();

  AutoParallelOptions(const AutoParallelOptions& from);
  AutoParallelOptions(AutoParallelOptions&& from) noexcept
    : AutoParallelOptions() {
    *this = ::std::move(from);
  }

  inline AutoParallelOptions& operator=(const AutoParallelOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutoParallelOptions& operator=(AutoParallelOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AutoParallelOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutoParallelOptions* internal_default_instance() {
    return reinterpret_cast<const AutoParallelOptions*>(
               &_AutoParallelOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AutoParallelOptions& a, AutoParallelOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(AutoParallelOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutoParallelOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AutoParallelOptions* New() const final {
    return CreateMaybeMessage<AutoParallelOptions>(nullptr);
  }

  AutoParallelOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AutoParallelOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AutoParallelOptions& from);
  void MergeFrom(const AutoParallelOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutoParallelOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AutoParallelOptions";
  }
  protected:
  explicit AutoParallelOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnableFieldNumber = 1,
    kNumReplicasFieldNumber = 2,
  };
  // bool enable = 1;
  void clear_enable();
  bool enable() const;
  void set_enable(bool value);

  // int32 num_replicas = 2;
  void clear_num_replicas();
  ::PROTOBUF_NAMESPACE_ID::int32 num_replicas() const;
  void set_num_replicas(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.AutoParallelOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool enable_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_replicas_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class ScopedAllocatorOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ScopedAllocatorOptions) */ {
 public:
  ScopedAllocatorOptions();
  virtual ~ScopedAllocatorOptions();

  ScopedAllocatorOptions(const ScopedAllocatorOptions& from);
  ScopedAllocatorOptions(ScopedAllocatorOptions&& from) noexcept
    : ScopedAllocatorOptions() {
    *this = ::std::move(from);
  }

  inline ScopedAllocatorOptions& operator=(const ScopedAllocatorOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline ScopedAllocatorOptions& operator=(ScopedAllocatorOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ScopedAllocatorOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ScopedAllocatorOptions* internal_default_instance() {
    return reinterpret_cast<const ScopedAllocatorOptions*>(
               &_ScopedAllocatorOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ScopedAllocatorOptions& a, ScopedAllocatorOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(ScopedAllocatorOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ScopedAllocatorOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ScopedAllocatorOptions* New() const final {
    return CreateMaybeMessage<ScopedAllocatorOptions>(nullptr);
  }

  ScopedAllocatorOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ScopedAllocatorOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ScopedAllocatorOptions& from);
  void MergeFrom(const ScopedAllocatorOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ScopedAllocatorOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ScopedAllocatorOptions";
  }
  protected:
  explicit ScopedAllocatorOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnableOpFieldNumber = 1,
  };
  // repeated string enable_op = 1;
  int enable_op_size() const;
  void clear_enable_op();
  const std::string& enable_op(int index) const;
  std::string* mutable_enable_op(int index);
  void set_enable_op(int index, const std::string& value);
  void set_enable_op(int index, std::string&& value);
  void set_enable_op(int index, const char* value);
  void set_enable_op(int index, const char* value, size_t size);
  std::string* add_enable_op();
  void add_enable_op(const std::string& value);
  void add_enable_op(std::string&& value);
  void add_enable_op(const char* value);
  void add_enable_op(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& enable_op() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_enable_op();

  // @@protoc_insertion_point(class_scope:tensorflow.ScopedAllocatorOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> enable_op_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse();
  RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse& other);
  static const RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse*>(&_RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.RewriterConfig.CustomGraphOptimizer.ParameterMapEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto.file_level_metadata[2];
  }

  public:
};

// -------------------------------------------------------------------

class RewriterConfig_CustomGraphOptimizer :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RewriterConfig.CustomGraphOptimizer) */ {
 public:
  RewriterConfig_CustomGraphOptimizer();
  virtual ~RewriterConfig_CustomGraphOptimizer();

  RewriterConfig_CustomGraphOptimizer(const RewriterConfig_CustomGraphOptimizer& from);
  RewriterConfig_CustomGraphOptimizer(RewriterConfig_CustomGraphOptimizer&& from) noexcept
    : RewriterConfig_CustomGraphOptimizer() {
    *this = ::std::move(from);
  }

  inline RewriterConfig_CustomGraphOptimizer& operator=(const RewriterConfig_CustomGraphOptimizer& from) {
    CopyFrom(from);
    return *this;
  }
  inline RewriterConfig_CustomGraphOptimizer& operator=(RewriterConfig_CustomGraphOptimizer&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RewriterConfig_CustomGraphOptimizer& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RewriterConfig_CustomGraphOptimizer* internal_default_instance() {
    return reinterpret_cast<const RewriterConfig_CustomGraphOptimizer*>(
               &_RewriterConfig_CustomGraphOptimizer_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(RewriterConfig_CustomGraphOptimizer& a, RewriterConfig_CustomGraphOptimizer& b) {
    a.Swap(&b);
  }
  inline void Swap(RewriterConfig_CustomGraphOptimizer* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RewriterConfig_CustomGraphOptimizer* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RewriterConfig_CustomGraphOptimizer* New() const final {
    return CreateMaybeMessage<RewriterConfig_CustomGraphOptimizer>(nullptr);
  }

  RewriterConfig_CustomGraphOptimizer* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RewriterConfig_CustomGraphOptimizer>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RewriterConfig_CustomGraphOptimizer& from);
  void MergeFrom(const RewriterConfig_CustomGraphOptimizer& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RewriterConfig_CustomGraphOptimizer* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RewriterConfig.CustomGraphOptimizer";
  }
  protected:
  explicit RewriterConfig_CustomGraphOptimizer(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kParameterMapFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // map<string, .tensorflow.AttrValue> parameter_map = 2;
  int parameter_map_size() const;
  void clear_parameter_map();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      parameter_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_parameter_map();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // @@protoc_insertion_point(class_scope:tensorflow.RewriterConfig.CustomGraphOptimizer)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse,
      std::string, ::tensorflow::AttrValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > parameter_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class RewriterConfig :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RewriterConfig) */ {
 public:
  RewriterConfig();
  virtual ~RewriterConfig();

  RewriterConfig(const RewriterConfig& from);
  RewriterConfig(RewriterConfig&& from) noexcept
    : RewriterConfig() {
    *this = ::std::move(from);
  }

  inline RewriterConfig& operator=(const RewriterConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline RewriterConfig& operator=(RewriterConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RewriterConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RewriterConfig* internal_default_instance() {
    return reinterpret_cast<const RewriterConfig*>(
               &_RewriterConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(RewriterConfig& a, RewriterConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(RewriterConfig* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RewriterConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RewriterConfig* New() const final {
    return CreateMaybeMessage<RewriterConfig>(nullptr);
  }

  RewriterConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RewriterConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RewriterConfig& from);
  void MergeFrom(const RewriterConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RewriterConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RewriterConfig";
  }
  protected:
  explicit RewriterConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef RewriterConfig_CustomGraphOptimizer CustomGraphOptimizer;

  typedef RewriterConfig_Toggle Toggle;
  static constexpr Toggle DEFAULT =
    RewriterConfig_Toggle_DEFAULT;
  static constexpr Toggle ON =
    RewriterConfig_Toggle_ON;
  static constexpr Toggle OFF =
    RewriterConfig_Toggle_OFF;
  static constexpr Toggle AGGRESSIVE =
    RewriterConfig_Toggle_AGGRESSIVE;
  static inline bool Toggle_IsValid(int value) {
    return RewriterConfig_Toggle_IsValid(value);
  }
  static constexpr Toggle Toggle_MIN =
    RewriterConfig_Toggle_Toggle_MIN;
  static constexpr Toggle Toggle_MAX =
    RewriterConfig_Toggle_Toggle_MAX;
  static constexpr int Toggle_ARRAYSIZE =
    RewriterConfig_Toggle_Toggle_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Toggle_descriptor() {
    return RewriterConfig_Toggle_descriptor();
  }
  template<typename T>
  static inline const std::string& Toggle_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Toggle>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Toggle_Name.");
    return RewriterConfig_Toggle_Name(enum_t_value);
  }
  static inline bool Toggle_Parse(const std::string& name,
      Toggle* value) {
    return RewriterConfig_Toggle_Parse(name, value);
  }

  typedef RewriterConfig_CpuLayout CpuLayout;
  static constexpr CpuLayout NO_CONVERSION_ON_CPU =
    RewriterConfig_CpuLayout_NO_CONVERSION_ON_CPU;
  static constexpr CpuLayout NCHW_TO_NHWC =
    RewriterConfig_CpuLayout_NCHW_TO_NHWC;
  static constexpr CpuLayout NHWC_TO_NCHW =
    RewriterConfig_CpuLayout_NHWC_TO_NCHW;
  static inline bool CpuLayout_IsValid(int value) {
    return RewriterConfig_CpuLayout_IsValid(value);
  }
  static constexpr CpuLayout CpuLayout_MIN =
    RewriterConfig_CpuLayout_CpuLayout_MIN;
  static constexpr CpuLayout CpuLayout_MAX =
    RewriterConfig_CpuLayout_CpuLayout_MAX;
  static constexpr int CpuLayout_ARRAYSIZE =
    RewriterConfig_CpuLayout_CpuLayout_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  CpuLayout_descriptor() {
    return RewriterConfig_CpuLayout_descriptor();
  }
  template<typename T>
  static inline const std::string& CpuLayout_Name(T enum_t_value) {
    static_assert(::std::is_same<T, CpuLayout>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function CpuLayout_Name.");
    return RewriterConfig_CpuLayout_Name(enum_t_value);
  }
  static inline bool CpuLayout_Parse(const std::string& name,
      CpuLayout* value) {
    return RewriterConfig_CpuLayout_Parse(name, value);
  }

  typedef RewriterConfig_NumIterationsType NumIterationsType;
  static constexpr NumIterationsType DEFAULT_NUM_ITERS =
    RewriterConfig_NumIterationsType_DEFAULT_NUM_ITERS;
  static constexpr NumIterationsType ONE =
    RewriterConfig_NumIterationsType_ONE;
  static constexpr NumIterationsType TWO =
    RewriterConfig_NumIterationsType_TWO;
  static inline bool NumIterationsType_IsValid(int value) {
    return RewriterConfig_NumIterationsType_IsValid(value);
  }
  static constexpr NumIterationsType NumIterationsType_MIN =
    RewriterConfig_NumIterationsType_NumIterationsType_MIN;
  static constexpr NumIterationsType NumIterationsType_MAX =
    RewriterConfig_NumIterationsType_NumIterationsType_MAX;
  static constexpr int NumIterationsType_ARRAYSIZE =
    RewriterConfig_NumIterationsType_NumIterationsType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  NumIterationsType_descriptor() {
    return RewriterConfig_NumIterationsType_descriptor();
  }
  template<typename T>
  static inline const std::string& NumIterationsType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, NumIterationsType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function NumIterationsType_Name.");
    return RewriterConfig_NumIterationsType_Name(enum_t_value);
  }
  static inline bool NumIterationsType_Parse(const std::string& name,
      NumIterationsType* value) {
    return RewriterConfig_NumIterationsType_Parse(name, value);
  }

  typedef RewriterConfig_MemOptType MemOptType;
  static constexpr MemOptType DEFAULT_MEM_OPT =
    RewriterConfig_MemOptType_DEFAULT_MEM_OPT;
  static constexpr MemOptType NO_MEM_OPT =
    RewriterConfig_MemOptType_NO_MEM_OPT;
  static constexpr MemOptType MANUAL =
    RewriterConfig_MemOptType_MANUAL;
  static constexpr MemOptType SWAPPING_HEURISTICS =
    RewriterConfig_MemOptType_SWAPPING_HEURISTICS;
  static constexpr MemOptType RECOMPUTATION_HEURISTICS =
    RewriterConfig_MemOptType_RECOMPUTATION_HEURISTICS;
  static constexpr MemOptType SCHEDULING_HEURISTICS =
    RewriterConfig_MemOptType_SCHEDULING_HEURISTICS;
  static constexpr MemOptType HEURISTICS =
    RewriterConfig_MemOptType_HEURISTICS;
  static inline bool MemOptType_IsValid(int value) {
    return RewriterConfig_MemOptType_IsValid(value);
  }
  static constexpr MemOptType MemOptType_MIN =
    RewriterConfig_MemOptType_MemOptType_MIN;
  static constexpr MemOptType MemOptType_MAX =
    RewriterConfig_MemOptType_MemOptType_MAX;
  static constexpr int MemOptType_ARRAYSIZE =
    RewriterConfig_MemOptType_MemOptType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MemOptType_descriptor() {
    return RewriterConfig_MemOptType_descriptor();
  }
  template<typename T>
  static inline const std::string& MemOptType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MemOptType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MemOptType_Name.");
    return RewriterConfig_MemOptType_Name(enum_t_value);
  }
  static inline bool MemOptType_Parse(const std::string& name,
      MemOptType* value) {
    return RewriterConfig_MemOptType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kOptimizersFieldNumber = 100,
    kCustomOptimizersFieldNumber = 200,
    kMemoryOptimizerTargetNodeNameScopeFieldNumber = 6,
    kAutoParallelFieldNumber = 5,
    kScopedAllocatorOptsFieldNumber = 16,
    kInterOptimizerVerifierConfigFieldNumber = 300,
    kPostOptimizationVerifierConfigFieldNumber = 301,
    kLayoutOptimizerFieldNumber = 1,
    kConstantFoldingFieldNumber = 3,
    kMemoryOptimizationFieldNumber = 4,
    kArithmeticOptimizationFieldNumber = 7,
    kDependencyOptimizationFieldNumber = 8,
    kLoopOptimizationFieldNumber = 9,
    kFunctionOptimizationFieldNumber = 10,
    kDebugStripperFieldNumber = 11,
    kMetaOptimizerIterationsFieldNumber = 12,
    kShapeOptimizationFieldNumber = 13,
    kRemappingFieldNumber = 14,
    kScopedAllocatorOptimizationFieldNumber = 15,
    kMinGraphNodesFieldNumber = 17,
    kPinToHostOptimizationFieldNumber = 18,
    kDisableModelPruningFieldNumber = 2,
    kDisableMetaOptimizerFieldNumber = 19,
    kExperimentalDisableCompressedTensorOptimizationFieldNumber = 26,
    kExperimentalDisableFoldingQuantizationEmulationFieldNumber = 27,
    kFailOnOptimizerErrorsFieldNumber = 21,
    kMetaOptimizerTimeoutMsFieldNumber = 20,
    kImplementationSelectorFieldNumber = 22,
    kAutoMixedPrecisionFieldNumber = 23,
    kCommonSubgraphEliminationFieldNumber = 24,
    kAutoMixedPrecisionMklFieldNumber = 25,
    kUsePluginOptimizersFieldNumber = 28,
    kCpuLayoutConversionFieldNumber = 50,
  };
  // repeated string optimizers = 100;
  int optimizers_size() const;
  void clear_optimizers();
  const std::string& optimizers(int index) const;
  std::string* mutable_optimizers(int index);
  void set_optimizers(int index, const std::string& value);
  void set_optimizers(int index, std::string&& value);
  void set_optimizers(int index, const char* value);
  void set_optimizers(int index, const char* value, size_t size);
  std::string* add_optimizers();
  void add_optimizers(const std::string& value);
  void add_optimizers(std::string&& value);
  void add_optimizers(const char* value);
  void add_optimizers(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& optimizers() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_optimizers();

  // repeated .tensorflow.RewriterConfig.CustomGraphOptimizer custom_optimizers = 200;
  int custom_optimizers_size() const;
  void clear_custom_optimizers();
  ::tensorflow::RewriterConfig_CustomGraphOptimizer* mutable_custom_optimizers(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >*
      mutable_custom_optimizers();
  const ::tensorflow::RewriterConfig_CustomGraphOptimizer& custom_optimizers(int index) const;
  ::tensorflow::RewriterConfig_CustomGraphOptimizer* add_custom_optimizers();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >&
      custom_optimizers() const;

  // string memory_optimizer_target_node_name_scope = 6;
  void clear_memory_optimizer_target_node_name_scope();
  const std::string& memory_optimizer_target_node_name_scope() const;
  void set_memory_optimizer_target_node_name_scope(const std::string& value);
  void set_memory_optimizer_target_node_name_scope(std::string&& value);
  void set_memory_optimizer_target_node_name_scope(const char* value);
  void set_memory_optimizer_target_node_name_scope(const char* value, size_t size);
  std::string* mutable_memory_optimizer_target_node_name_scope();
  std::string* release_memory_optimizer_target_node_name_scope();
  void set_allocated_memory_optimizer_target_node_name_scope(std::string* memory_optimizer_target_node_name_scope);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_memory_optimizer_target_node_name_scope();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_memory_optimizer_target_node_name_scope(
      std::string* memory_optimizer_target_node_name_scope);

  // .tensorflow.AutoParallelOptions auto_parallel = 5;
  bool has_auto_parallel() const;
  void clear_auto_parallel();
  const ::tensorflow::AutoParallelOptions& auto_parallel() const;
  ::tensorflow::AutoParallelOptions* release_auto_parallel();
  ::tensorflow::AutoParallelOptions* mutable_auto_parallel();
  void set_allocated_auto_parallel(::tensorflow::AutoParallelOptions* auto_parallel);
  void unsafe_arena_set_allocated_auto_parallel(
      ::tensorflow::AutoParallelOptions* auto_parallel);
  ::tensorflow::AutoParallelOptions* unsafe_arena_release_auto_parallel();

  // .tensorflow.ScopedAllocatorOptions scoped_allocator_opts = 16;
  bool has_scoped_allocator_opts() const;
  void clear_scoped_allocator_opts();
  const ::tensorflow::ScopedAllocatorOptions& scoped_allocator_opts() const;
  ::tensorflow::ScopedAllocatorOptions* release_scoped_allocator_opts();
  ::tensorflow::ScopedAllocatorOptions* mutable_scoped_allocator_opts();
  void set_allocated_scoped_allocator_opts(::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts);
  void unsafe_arena_set_allocated_scoped_allocator_opts(
      ::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts);
  ::tensorflow::ScopedAllocatorOptions* unsafe_arena_release_scoped_allocator_opts();

  // .tensorflow.VerifierConfig inter_optimizer_verifier_config = 300;
  bool has_inter_optimizer_verifier_config() const;
  void clear_inter_optimizer_verifier_config();
  const ::tensorflow::VerifierConfig& inter_optimizer_verifier_config() const;
  ::tensorflow::VerifierConfig* release_inter_optimizer_verifier_config();
  ::tensorflow::VerifierConfig* mutable_inter_optimizer_verifier_config();
  void set_allocated_inter_optimizer_verifier_config(::tensorflow::VerifierConfig* inter_optimizer_verifier_config);
  void unsafe_arena_set_allocated_inter_optimizer_verifier_config(
      ::tensorflow::VerifierConfig* inter_optimizer_verifier_config);
  ::tensorflow::VerifierConfig* unsafe_arena_release_inter_optimizer_verifier_config();

  // .tensorflow.VerifierConfig post_optimization_verifier_config = 301;
  bool has_post_optimization_verifier_config() const;
  void clear_post_optimization_verifier_config();
  const ::tensorflow::VerifierConfig& post_optimization_verifier_config() const;
  ::tensorflow::VerifierConfig* release_post_optimization_verifier_config();
  ::tensorflow::VerifierConfig* mutable_post_optimization_verifier_config();
  void set_allocated_post_optimization_verifier_config(::tensorflow::VerifierConfig* post_optimization_verifier_config);
  void unsafe_arena_set_allocated_post_optimization_verifier_config(
      ::tensorflow::VerifierConfig* post_optimization_verifier_config);
  ::tensorflow::VerifierConfig* unsafe_arena_release_post_optimization_verifier_config();

  // .tensorflow.RewriterConfig.Toggle layout_optimizer = 1;
  void clear_layout_optimizer();
  ::tensorflow::RewriterConfig_Toggle layout_optimizer() const;
  void set_layout_optimizer(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle constant_folding = 3;
  void clear_constant_folding();
  ::tensorflow::RewriterConfig_Toggle constant_folding() const;
  void set_constant_folding(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.MemOptType memory_optimization = 4;
  void clear_memory_optimization();
  ::tensorflow::RewriterConfig_MemOptType memory_optimization() const;
  void set_memory_optimization(::tensorflow::RewriterConfig_MemOptType value);

  // .tensorflow.RewriterConfig.Toggle arithmetic_optimization = 7;
  void clear_arithmetic_optimization();
  ::tensorflow::RewriterConfig_Toggle arithmetic_optimization() const;
  void set_arithmetic_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle dependency_optimization = 8;
  void clear_dependency_optimization();
  ::tensorflow::RewriterConfig_Toggle dependency_optimization() const;
  void set_dependency_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle loop_optimization = 9;
  void clear_loop_optimization();
  ::tensorflow::RewriterConfig_Toggle loop_optimization() const;
  void set_loop_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle function_optimization = 10;
  void clear_function_optimization();
  ::tensorflow::RewriterConfig_Toggle function_optimization() const;
  void set_function_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle debug_stripper = 11;
  void clear_debug_stripper();
  ::tensorflow::RewriterConfig_Toggle debug_stripper() const;
  void set_debug_stripper(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.NumIterationsType meta_optimizer_iterations = 12;
  void clear_meta_optimizer_iterations();
  ::tensorflow::RewriterConfig_NumIterationsType meta_optimizer_iterations() const;
  void set_meta_optimizer_iterations(::tensorflow::RewriterConfig_NumIterationsType value);

  // .tensorflow.RewriterConfig.Toggle shape_optimization = 13;
  void clear_shape_optimization();
  ::tensorflow::RewriterConfig_Toggle shape_optimization() const;
  void set_shape_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle remapping = 14;
  void clear_remapping();
  ::tensorflow::RewriterConfig_Toggle remapping() const;
  void set_remapping(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle scoped_allocator_optimization = 15;
  void clear_scoped_allocator_optimization();
  ::tensorflow::RewriterConfig_Toggle scoped_allocator_optimization() const;
  void set_scoped_allocator_optimization(::tensorflow::RewriterConfig_Toggle value);

  // int32 min_graph_nodes = 17;
  void clear_min_graph_nodes();
  ::PROTOBUF_NAMESPACE_ID::int32 min_graph_nodes() const;
  void set_min_graph_nodes(::PROTOBUF_NAMESPACE_ID::int32 value);

  // .tensorflow.RewriterConfig.Toggle pin_to_host_optimization = 18;
  void clear_pin_to_host_optimization();
  ::tensorflow::RewriterConfig_Toggle pin_to_host_optimization() const;
  void set_pin_to_host_optimization(::tensorflow::RewriterConfig_Toggle value);

  // bool disable_model_pruning = 2;
  void clear_disable_model_pruning();
  bool disable_model_pruning() const;
  void set_disable_model_pruning(bool value);

  // bool disable_meta_optimizer = 19;
  void clear_disable_meta_optimizer();
  bool disable_meta_optimizer() const;
  void set_disable_meta_optimizer(bool value);

  // bool experimental_disable_compressed_tensor_optimization = 26;
  void clear_experimental_disable_compressed_tensor_optimization();
  bool experimental_disable_compressed_tensor_optimization() const;
  void set_experimental_disable_compressed_tensor_optimization(bool value);

  // bool experimental_disable_folding_quantization_emulation = 27;
  void clear_experimental_disable_folding_quantization_emulation();
  bool experimental_disable_folding_quantization_emulation() const;
  void set_experimental_disable_folding_quantization_emulation(bool value);

  // bool fail_on_optimizer_errors = 21;
  void clear_fail_on_optimizer_errors();
  bool fail_on_optimizer_errors() const;
  void set_fail_on_optimizer_errors(bool value);

  // int64 meta_optimizer_timeout_ms = 20;
  void clear_meta_optimizer_timeout_ms();
  ::PROTOBUF_NAMESPACE_ID::int64 meta_optimizer_timeout_ms() const;
  void set_meta_optimizer_timeout_ms(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .tensorflow.RewriterConfig.Toggle implementation_selector = 22;
  void clear_implementation_selector();
  ::tensorflow::RewriterConfig_Toggle implementation_selector() const;
  void set_implementation_selector(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle auto_mixed_precision = 23;
  void clear_auto_mixed_precision();
  ::tensorflow::RewriterConfig_Toggle auto_mixed_precision() const;
  void set_auto_mixed_precision(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle common_subgraph_elimination = 24;
  void clear_common_subgraph_elimination();
  ::tensorflow::RewriterConfig_Toggle common_subgraph_elimination() const;
  void set_common_subgraph_elimination(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle auto_mixed_precision_mkl = 25;
  void clear_auto_mixed_precision_mkl();
  ::tensorflow::RewriterConfig_Toggle auto_mixed_precision_mkl() const;
  void set_auto_mixed_precision_mkl(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle use_plugin_optimizers = 28;
  void clear_use_plugin_optimizers();
  ::tensorflow::RewriterConfig_Toggle use_plugin_optimizers() const;
  void set_use_plugin_optimizers(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.CpuLayout cpu_layout_conversion = 50;
  void clear_cpu_layout_conversion();
  ::tensorflow::RewriterConfig_CpuLayout cpu_layout_conversion() const;
  void set_cpu_layout_conversion(::tensorflow::RewriterConfig_CpuLayout value);

  // @@protoc_insertion_point(class_scope:tensorflow.RewriterConfig)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> optimizers_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer > custom_optimizers_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr memory_optimizer_target_node_name_scope_;
  ::tensorflow::AutoParallelOptions* auto_parallel_;
  ::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts_;
  ::tensorflow::VerifierConfig* inter_optimizer_verifier_config_;
  ::tensorflow::VerifierConfig* post_optimization_verifier_config_;
  int layout_optimizer_;
  int constant_folding_;
  int memory_optimization_;
  int arithmetic_optimization_;
  int dependency_optimization_;
  int loop_optimization_;
  int function_optimization_;
  int debug_stripper_;
  int meta_optimizer_iterations_;
  int shape_optimization_;
  int remapping_;
  int scoped_allocator_optimization_;
  ::PROTOBUF_NAMESPACE_ID::int32 min_graph_nodes_;
  int pin_to_host_optimization_;
  bool disable_model_pruning_;
  bool disable_meta_optimizer_;
  bool experimental_disable_compressed_tensor_optimization_;
  bool experimental_disable_folding_quantization_emulation_;
  bool fail_on_optimizer_errors_;
  ::PROTOBUF_NAMESPACE_ID::int64 meta_optimizer_timeout_ms_;
  int implementation_selector_;
  int auto_mixed_precision_;
  int common_subgraph_elimination_;
  int auto_mixed_precision_mkl_;
  int use_plugin_optimizers_;
  int cpu_layout_conversion_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AutoParallelOptions

// bool enable = 1;
inline void AutoParallelOptions::clear_enable() {
  enable_ = false;
}
inline bool AutoParallelOptions::enable() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutoParallelOptions.enable)
  return enable_;
}
inline void AutoParallelOptions::set_enable(bool value) {
  
  enable_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutoParallelOptions.enable)
}

// int32 num_replicas = 2;
inline void AutoParallelOptions::clear_num_replicas() {
  num_replicas_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 AutoParallelOptions::num_replicas() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutoParallelOptions.num_replicas)
  return num_replicas_;
}
inline void AutoParallelOptions::set_num_replicas(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_replicas_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutoParallelOptions.num_replicas)
}

// -------------------------------------------------------------------

// ScopedAllocatorOptions

// repeated string enable_op = 1;
inline int ScopedAllocatorOptions::enable_op_size() const {
  return enable_op_.size();
}
inline void ScopedAllocatorOptions::clear_enable_op() {
  enable_op_.Clear();
}
inline const std::string& ScopedAllocatorOptions::enable_op(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ScopedAllocatorOptions.enable_op)
  return enable_op_.Get(index);
}
inline std::string* ScopedAllocatorOptions::mutable_enable_op(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ScopedAllocatorOptions.enable_op)
  return enable_op_.Mutable(index);
}
inline void ScopedAllocatorOptions::set_enable_op(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ScopedAllocatorOptions.enable_op)
  enable_op_.Mutable(index)->assign(value);
}
inline void ScopedAllocatorOptions::set_enable_op(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ScopedAllocatorOptions.enable_op)
  enable_op_.Mutable(index)->assign(std::move(value));
}
inline void ScopedAllocatorOptions::set_enable_op(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  enable_op_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::set_enable_op(int index, const char* value, size_t size) {
  enable_op_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline std::string* ScopedAllocatorOptions::add_enable_op() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ScopedAllocatorOptions.enable_op)
  return enable_op_.Add();
}
inline void ScopedAllocatorOptions::add_enable_op(const std::string& value) {
  enable_op_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::add_enable_op(std::string&& value) {
  enable_op_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::add_enable_op(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  enable_op_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::add_enable_op(const char* value, size_t size) {
  enable_op_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ScopedAllocatorOptions::enable_op() const {
  // @@protoc_insertion_point(field_list:tensorflow.ScopedAllocatorOptions.enable_op)
  return enable_op_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ScopedAllocatorOptions::mutable_enable_op() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ScopedAllocatorOptions.enable_op)
  return &enable_op_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// RewriterConfig_CustomGraphOptimizer

// string name = 1;
inline void RewriterConfig_CustomGraphOptimizer::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RewriterConfig_CustomGraphOptimizer::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  return name_.Get();
}
inline void RewriterConfig_CustomGraphOptimizer::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
inline void RewriterConfig_CustomGraphOptimizer::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
inline void RewriterConfig_CustomGraphOptimizer::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
inline void RewriterConfig_CustomGraphOptimizer::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
inline std::string* RewriterConfig_CustomGraphOptimizer::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RewriterConfig_CustomGraphOptimizer::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RewriterConfig_CustomGraphOptimizer::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
inline std::string* RewriterConfig_CustomGraphOptimizer::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RewriterConfig_CustomGraphOptimizer::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}

// map<string, .tensorflow.AttrValue> parameter_map = 2;
inline int RewriterConfig_CustomGraphOptimizer::parameter_map_size() const {
  return parameter_map_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
RewriterConfig_CustomGraphOptimizer::parameter_map() const {
  // @@protoc_insertion_point(field_map:tensorflow.RewriterConfig.CustomGraphOptimizer.parameter_map)
  return parameter_map_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
RewriterConfig_CustomGraphOptimizer::mutable_parameter_map() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.RewriterConfig.CustomGraphOptimizer.parameter_map)
  return parameter_map_.MutableMap();
}

// -------------------------------------------------------------------

// RewriterConfig

// .tensorflow.RewriterConfig.CpuLayout cpu_layout_conversion = 50;
inline void RewriterConfig::clear_cpu_layout_conversion() {
  cpu_layout_conversion_ = 0;
}
inline ::tensorflow::RewriterConfig_CpuLayout RewriterConfig::cpu_layout_conversion() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.cpu_layout_conversion)
  return static_cast< ::tensorflow::RewriterConfig_CpuLayout >(cpu_layout_conversion_);
}
inline void RewriterConfig::set_cpu_layout_conversion(::tensorflow::RewriterConfig_CpuLayout value) {
  
  cpu_layout_conversion_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.cpu_layout_conversion)
}

// .tensorflow.RewriterConfig.Toggle layout_optimizer = 1;
inline void RewriterConfig::clear_layout_optimizer() {
  layout_optimizer_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::layout_optimizer() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.layout_optimizer)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(layout_optimizer_);
}
inline void RewriterConfig::set_layout_optimizer(::tensorflow::RewriterConfig_Toggle value) {
  
  layout_optimizer_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.layout_optimizer)
}

// .tensorflow.RewriterConfig.Toggle constant_folding = 3;
inline void RewriterConfig::clear_constant_folding() {
  constant_folding_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::constant_folding() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.constant_folding)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(constant_folding_);
}
inline void RewriterConfig::set_constant_folding(::tensorflow::RewriterConfig_Toggle value) {
  
  constant_folding_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.constant_folding)
}

// .tensorflow.RewriterConfig.Toggle shape_optimization = 13;
inline void RewriterConfig::clear_shape_optimization() {
  shape_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::shape_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.shape_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(shape_optimization_);
}
inline void RewriterConfig::set_shape_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  shape_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.shape_optimization)
}

// .tensorflow.RewriterConfig.Toggle remapping = 14;
inline void RewriterConfig::clear_remapping() {
  remapping_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::remapping() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.remapping)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(remapping_);
}
inline void RewriterConfig::set_remapping(::tensorflow::RewriterConfig_Toggle value) {
  
  remapping_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.remapping)
}

// .tensorflow.RewriterConfig.Toggle common_subgraph_elimination = 24;
inline void RewriterConfig::clear_common_subgraph_elimination() {
  common_subgraph_elimination_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::common_subgraph_elimination() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.common_subgraph_elimination)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(common_subgraph_elimination_);
}
inline void RewriterConfig::set_common_subgraph_elimination(::tensorflow::RewriterConfig_Toggle value) {
  
  common_subgraph_elimination_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.common_subgraph_elimination)
}

// .tensorflow.RewriterConfig.Toggle arithmetic_optimization = 7;
inline void RewriterConfig::clear_arithmetic_optimization() {
  arithmetic_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::arithmetic_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.arithmetic_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(arithmetic_optimization_);
}
inline void RewriterConfig::set_arithmetic_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  arithmetic_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.arithmetic_optimization)
}

// .tensorflow.RewriterConfig.Toggle dependency_optimization = 8;
inline void RewriterConfig::clear_dependency_optimization() {
  dependency_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::dependency_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.dependency_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(dependency_optimization_);
}
inline void RewriterConfig::set_dependency_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  dependency_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.dependency_optimization)
}

// .tensorflow.RewriterConfig.Toggle loop_optimization = 9;
inline void RewriterConfig::clear_loop_optimization() {
  loop_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::loop_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.loop_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(loop_optimization_);
}
inline void RewriterConfig::set_loop_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  loop_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.loop_optimization)
}

// .tensorflow.RewriterConfig.Toggle function_optimization = 10;
inline void RewriterConfig::clear_function_optimization() {
  function_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::function_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.function_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(function_optimization_);
}
inline void RewriterConfig::set_function_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  function_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.function_optimization)
}

// .tensorflow.RewriterConfig.Toggle debug_stripper = 11;
inline void RewriterConfig::clear_debug_stripper() {
  debug_stripper_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::debug_stripper() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.debug_stripper)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(debug_stripper_);
}
inline void RewriterConfig::set_debug_stripper(::tensorflow::RewriterConfig_Toggle value) {
  
  debug_stripper_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.debug_stripper)
}

// bool disable_model_pruning = 2;
inline void RewriterConfig::clear_disable_model_pruning() {
  disable_model_pruning_ = false;
}
inline bool RewriterConfig::disable_model_pruning() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.disable_model_pruning)
  return disable_model_pruning_;
}
inline void RewriterConfig::set_disable_model_pruning(bool value) {
  
  disable_model_pruning_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.disable_model_pruning)
}

// .tensorflow.RewriterConfig.Toggle scoped_allocator_optimization = 15;
inline void RewriterConfig::clear_scoped_allocator_optimization() {
  scoped_allocator_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::scoped_allocator_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.scoped_allocator_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(scoped_allocator_optimization_);
}
inline void RewriterConfig::set_scoped_allocator_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  scoped_allocator_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.scoped_allocator_optimization)
}

// .tensorflow.RewriterConfig.Toggle pin_to_host_optimization = 18;
inline void RewriterConfig::clear_pin_to_host_optimization() {
  pin_to_host_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::pin_to_host_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.pin_to_host_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(pin_to_host_optimization_);
}
inline void RewriterConfig::set_pin_to_host_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  pin_to_host_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.pin_to_host_optimization)
}

// .tensorflow.RewriterConfig.Toggle implementation_selector = 22;
inline void RewriterConfig::clear_implementation_selector() {
  implementation_selector_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::implementation_selector() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.implementation_selector)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(implementation_selector_);
}
inline void RewriterConfig::set_implementation_selector(::tensorflow::RewriterConfig_Toggle value) {
  
  implementation_selector_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.implementation_selector)
}

// .tensorflow.RewriterConfig.Toggle auto_mixed_precision = 23;
inline void RewriterConfig::clear_auto_mixed_precision() {
  auto_mixed_precision_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::auto_mixed_precision() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.auto_mixed_precision)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(auto_mixed_precision_);
}
inline void RewriterConfig::set_auto_mixed_precision(::tensorflow::RewriterConfig_Toggle value) {
  
  auto_mixed_precision_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.auto_mixed_precision)
}

// .tensorflow.RewriterConfig.Toggle auto_mixed_precision_mkl = 25;
inline void RewriterConfig::clear_auto_mixed_precision_mkl() {
  auto_mixed_precision_mkl_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::auto_mixed_precision_mkl() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.auto_mixed_precision_mkl)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(auto_mixed_precision_mkl_);
}
inline void RewriterConfig::set_auto_mixed_precision_mkl(::tensorflow::RewriterConfig_Toggle value) {
  
  auto_mixed_precision_mkl_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.auto_mixed_precision_mkl)
}

// bool disable_meta_optimizer = 19;
inline void RewriterConfig::clear_disable_meta_optimizer() {
  disable_meta_optimizer_ = false;
}
inline bool RewriterConfig::disable_meta_optimizer() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.disable_meta_optimizer)
  return disable_meta_optimizer_;
}
inline void RewriterConfig::set_disable_meta_optimizer(bool value) {
  
  disable_meta_optimizer_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.disable_meta_optimizer)
}

// .tensorflow.RewriterConfig.Toggle use_plugin_optimizers = 28;
inline void RewriterConfig::clear_use_plugin_optimizers() {
  use_plugin_optimizers_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::use_plugin_optimizers() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.use_plugin_optimizers)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(use_plugin_optimizers_);
}
inline void RewriterConfig::set_use_plugin_optimizers(::tensorflow::RewriterConfig_Toggle value) {
  
  use_plugin_optimizers_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.use_plugin_optimizers)
}

// .tensorflow.RewriterConfig.NumIterationsType meta_optimizer_iterations = 12;
inline void RewriterConfig::clear_meta_optimizer_iterations() {
  meta_optimizer_iterations_ = 0;
}
inline ::tensorflow::RewriterConfig_NumIterationsType RewriterConfig::meta_optimizer_iterations() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.meta_optimizer_iterations)
  return static_cast< ::tensorflow::RewriterConfig_NumIterationsType >(meta_optimizer_iterations_);
}
inline void RewriterConfig::set_meta_optimizer_iterations(::tensorflow::RewriterConfig_NumIterationsType value) {
  
  meta_optimizer_iterations_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.meta_optimizer_iterations)
}

// int32 min_graph_nodes = 17;
inline void RewriterConfig::clear_min_graph_nodes() {
  min_graph_nodes_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RewriterConfig::min_graph_nodes() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.min_graph_nodes)
  return min_graph_nodes_;
}
inline void RewriterConfig::set_min_graph_nodes(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  min_graph_nodes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.min_graph_nodes)
}

// bool experimental_disable_compressed_tensor_optimization = 26;
inline void RewriterConfig::clear_experimental_disable_compressed_tensor_optimization() {
  experimental_disable_compressed_tensor_optimization_ = false;
}
inline bool RewriterConfig::experimental_disable_compressed_tensor_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.experimental_disable_compressed_tensor_optimization)
  return experimental_disable_compressed_tensor_optimization_;
}
inline void RewriterConfig::set_experimental_disable_compressed_tensor_optimization(bool value) {
  
  experimental_disable_compressed_tensor_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.experimental_disable_compressed_tensor_optimization)
}

// bool experimental_disable_folding_quantization_emulation = 27;
inline void RewriterConfig::clear_experimental_disable_folding_quantization_emulation() {
  experimental_disable_folding_quantization_emulation_ = false;
}
inline bool RewriterConfig::experimental_disable_folding_quantization_emulation() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.experimental_disable_folding_quantization_emulation)
  return experimental_disable_folding_quantization_emulation_;
}
inline void RewriterConfig::set_experimental_disable_folding_quantization_emulation(bool value) {
  
  experimental_disable_folding_quantization_emulation_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.experimental_disable_folding_quantization_emulation)
}

// .tensorflow.RewriterConfig.MemOptType memory_optimization = 4;
inline void RewriterConfig::clear_memory_optimization() {
  memory_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_MemOptType RewriterConfig::memory_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.memory_optimization)
  return static_cast< ::tensorflow::RewriterConfig_MemOptType >(memory_optimization_);
}
inline void RewriterConfig::set_memory_optimization(::tensorflow::RewriterConfig_MemOptType value) {
  
  memory_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.memory_optimization)
}

// string memory_optimizer_target_node_name_scope = 6;
inline void RewriterConfig::clear_memory_optimizer_target_node_name_scope() {
  memory_optimizer_target_node_name_scope_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RewriterConfig::memory_optimizer_target_node_name_scope() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  return memory_optimizer_target_node_name_scope_.Get();
}
inline void RewriterConfig::set_memory_optimizer_target_node_name_scope(const std::string& value) {
  
  memory_optimizer_target_node_name_scope_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
inline void RewriterConfig::set_memory_optimizer_target_node_name_scope(std::string&& value) {
  
  memory_optimizer_target_node_name_scope_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
inline void RewriterConfig::set_memory_optimizer_target_node_name_scope(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  memory_optimizer_target_node_name_scope_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
inline void RewriterConfig::set_memory_optimizer_target_node_name_scope(const char* value,
    size_t size) {
  
  memory_optimizer_target_node_name_scope_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
inline std::string* RewriterConfig::mutable_memory_optimizer_target_node_name_scope() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  return memory_optimizer_target_node_name_scope_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RewriterConfig::release_memory_optimizer_target_node_name_scope() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  
  return memory_optimizer_target_node_name_scope_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RewriterConfig::set_allocated_memory_optimizer_target_node_name_scope(std::string* memory_optimizer_target_node_name_scope) {
  if (memory_optimizer_target_node_name_scope != nullptr) {
    
  } else {
    
  }
  memory_optimizer_target_node_name_scope_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), memory_optimizer_target_node_name_scope,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
inline std::string* RewriterConfig::unsafe_arena_release_memory_optimizer_target_node_name_scope() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return memory_optimizer_target_node_name_scope_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RewriterConfig::unsafe_arena_set_allocated_memory_optimizer_target_node_name_scope(
    std::string* memory_optimizer_target_node_name_scope) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (memory_optimizer_target_node_name_scope != nullptr) {
    
  } else {
    
  }
  memory_optimizer_target_node_name_scope_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      memory_optimizer_target_node_name_scope, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}

// int64 meta_optimizer_timeout_ms = 20;
inline void RewriterConfig::clear_meta_optimizer_timeout_ms() {
  meta_optimizer_timeout_ms_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RewriterConfig::meta_optimizer_timeout_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.meta_optimizer_timeout_ms)
  return meta_optimizer_timeout_ms_;
}
inline void RewriterConfig::set_meta_optimizer_timeout_ms(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  meta_optimizer_timeout_ms_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.meta_optimizer_timeout_ms)
}

// .tensorflow.AutoParallelOptions auto_parallel = 5;
inline bool RewriterConfig::has_auto_parallel() const {
  return this != internal_default_instance() && auto_parallel_ != nullptr;
}
inline void RewriterConfig::clear_auto_parallel() {
  if (GetArenaNoVirtual() == nullptr && auto_parallel_ != nullptr) {
    delete auto_parallel_;
  }
  auto_parallel_ = nullptr;
}
inline const ::tensorflow::AutoParallelOptions& RewriterConfig::auto_parallel() const {
  const ::tensorflow::AutoParallelOptions* p = auto_parallel_;
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.auto_parallel)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::AutoParallelOptions*>(
      &::tensorflow::_AutoParallelOptions_default_instance_);
}
inline ::tensorflow::AutoParallelOptions* RewriterConfig::release_auto_parallel() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.auto_parallel)
  
  ::tensorflow::AutoParallelOptions* temp = auto_parallel_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  auto_parallel_ = nullptr;
  return temp;
}
inline ::tensorflow::AutoParallelOptions* RewriterConfig::unsafe_arena_release_auto_parallel() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.auto_parallel)
  
  ::tensorflow::AutoParallelOptions* temp = auto_parallel_;
  auto_parallel_ = nullptr;
  return temp;
}
inline ::tensorflow::AutoParallelOptions* RewriterConfig::mutable_auto_parallel() {
  
  if (auto_parallel_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::AutoParallelOptions>(GetArenaNoVirtual());
    auto_parallel_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.auto_parallel)
  return auto_parallel_;
}
inline void RewriterConfig::set_allocated_auto_parallel(::tensorflow::AutoParallelOptions* auto_parallel) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete auto_parallel_;
  }
  if (auto_parallel) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(auto_parallel);
    if (message_arena != submessage_arena) {
      auto_parallel = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, auto_parallel, submessage_arena);
    }
    
  } else {
    
  }
  auto_parallel_ = auto_parallel;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.auto_parallel)
}

// bool fail_on_optimizer_errors = 21;
inline void RewriterConfig::clear_fail_on_optimizer_errors() {
  fail_on_optimizer_errors_ = false;
}
inline bool RewriterConfig::fail_on_optimizer_errors() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.fail_on_optimizer_errors)
  return fail_on_optimizer_errors_;
}
inline void RewriterConfig::set_fail_on_optimizer_errors(bool value) {
  
  fail_on_optimizer_errors_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.fail_on_optimizer_errors)
}

// .tensorflow.ScopedAllocatorOptions scoped_allocator_opts = 16;
inline bool RewriterConfig::has_scoped_allocator_opts() const {
  return this != internal_default_instance() && scoped_allocator_opts_ != nullptr;
}
inline void RewriterConfig::clear_scoped_allocator_opts() {
  if (GetArenaNoVirtual() == nullptr && scoped_allocator_opts_ != nullptr) {
    delete scoped_allocator_opts_;
  }
  scoped_allocator_opts_ = nullptr;
}
inline const ::tensorflow::ScopedAllocatorOptions& RewriterConfig::scoped_allocator_opts() const {
  const ::tensorflow::ScopedAllocatorOptions* p = scoped_allocator_opts_;
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.scoped_allocator_opts)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ScopedAllocatorOptions*>(
      &::tensorflow::_ScopedAllocatorOptions_default_instance_);
}
inline ::tensorflow::ScopedAllocatorOptions* RewriterConfig::release_scoped_allocator_opts() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.scoped_allocator_opts)
  
  ::tensorflow::ScopedAllocatorOptions* temp = scoped_allocator_opts_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  scoped_allocator_opts_ = nullptr;
  return temp;
}
inline ::tensorflow::ScopedAllocatorOptions* RewriterConfig::unsafe_arena_release_scoped_allocator_opts() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.scoped_allocator_opts)
  
  ::tensorflow::ScopedAllocatorOptions* temp = scoped_allocator_opts_;
  scoped_allocator_opts_ = nullptr;
  return temp;
}
inline ::tensorflow::ScopedAllocatorOptions* RewriterConfig::mutable_scoped_allocator_opts() {
  
  if (scoped_allocator_opts_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ScopedAllocatorOptions>(GetArenaNoVirtual());
    scoped_allocator_opts_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.scoped_allocator_opts)
  return scoped_allocator_opts_;
}
inline void RewriterConfig::set_allocated_scoped_allocator_opts(::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete scoped_allocator_opts_;
  }
  if (scoped_allocator_opts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(scoped_allocator_opts);
    if (message_arena != submessage_arena) {
      scoped_allocator_opts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, scoped_allocator_opts, submessage_arena);
    }
    
  } else {
    
  }
  scoped_allocator_opts_ = scoped_allocator_opts;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.scoped_allocator_opts)
}

// repeated string optimizers = 100;
inline int RewriterConfig::optimizers_size() const {
  return optimizers_.size();
}
inline void RewriterConfig::clear_optimizers() {
  optimizers_.Clear();
}
inline const std::string& RewriterConfig::optimizers(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.optimizers)
  return optimizers_.Get(index);
}
inline std::string* RewriterConfig::mutable_optimizers(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.optimizers)
  return optimizers_.Mutable(index);
}
inline void RewriterConfig::set_optimizers(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.optimizers)
  optimizers_.Mutable(index)->assign(value);
}
inline void RewriterConfig::set_optimizers(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.optimizers)
  optimizers_.Mutable(index)->assign(std::move(value));
}
inline void RewriterConfig::set_optimizers(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  optimizers_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::set_optimizers(int index, const char* value, size_t size) {
  optimizers_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RewriterConfig.optimizers)
}
inline std::string* RewriterConfig::add_optimizers() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RewriterConfig.optimizers)
  return optimizers_.Add();
}
inline void RewriterConfig::add_optimizers(const std::string& value) {
  optimizers_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::add_optimizers(std::string&& value) {
  optimizers_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::add_optimizers(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  optimizers_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::add_optimizers(const char* value, size_t size) {
  optimizers_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RewriterConfig.optimizers)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RewriterConfig::optimizers() const {
  // @@protoc_insertion_point(field_list:tensorflow.RewriterConfig.optimizers)
  return optimizers_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RewriterConfig::mutable_optimizers() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RewriterConfig.optimizers)
  return &optimizers_;
}

// repeated .tensorflow.RewriterConfig.CustomGraphOptimizer custom_optimizers = 200;
inline int RewriterConfig::custom_optimizers_size() const {
  return custom_optimizers_.size();
}
inline void RewriterConfig::clear_custom_optimizers() {
  custom_optimizers_.Clear();
}
inline ::tensorflow::RewriterConfig_CustomGraphOptimizer* RewriterConfig::mutable_custom_optimizers(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.custom_optimizers)
  return custom_optimizers_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >*
RewriterConfig::mutable_custom_optimizers() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RewriterConfig.custom_optimizers)
  return &custom_optimizers_;
}
inline const ::tensorflow::RewriterConfig_CustomGraphOptimizer& RewriterConfig::custom_optimizers(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.custom_optimizers)
  return custom_optimizers_.Get(index);
}
inline ::tensorflow::RewriterConfig_CustomGraphOptimizer* RewriterConfig::add_custom_optimizers() {
  // @@protoc_insertion_point(field_add:tensorflow.RewriterConfig.custom_optimizers)
  return custom_optimizers_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >&
RewriterConfig::custom_optimizers() const {
  // @@protoc_insertion_point(field_list:tensorflow.RewriterConfig.custom_optimizers)
  return custom_optimizers_;
}

// .tensorflow.VerifierConfig inter_optimizer_verifier_config = 300;
inline bool RewriterConfig::has_inter_optimizer_verifier_config() const {
  return this != internal_default_instance() && inter_optimizer_verifier_config_ != nullptr;
}
inline const ::tensorflow::VerifierConfig& RewriterConfig::inter_optimizer_verifier_config() const {
  const ::tensorflow::VerifierConfig* p = inter_optimizer_verifier_config_;
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::VerifierConfig*>(
      &::tensorflow::_VerifierConfig_default_instance_);
}
inline ::tensorflow::VerifierConfig* RewriterConfig::release_inter_optimizer_verifier_config() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  
  ::tensorflow::VerifierConfig* temp = inter_optimizer_verifier_config_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  inter_optimizer_verifier_config_ = nullptr;
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::unsafe_arena_release_inter_optimizer_verifier_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  
  ::tensorflow::VerifierConfig* temp = inter_optimizer_verifier_config_;
  inter_optimizer_verifier_config_ = nullptr;
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::mutable_inter_optimizer_verifier_config() {
  
  if (inter_optimizer_verifier_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VerifierConfig>(GetArenaNoVirtual());
    inter_optimizer_verifier_config_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  return inter_optimizer_verifier_config_;
}
inline void RewriterConfig::set_allocated_inter_optimizer_verifier_config(::tensorflow::VerifierConfig* inter_optimizer_verifier_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(inter_optimizer_verifier_config_);
  }
  if (inter_optimizer_verifier_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(inter_optimizer_verifier_config)->GetArena();
    if (message_arena != submessage_arena) {
      inter_optimizer_verifier_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, inter_optimizer_verifier_config, submessage_arena);
    }
    
  } else {
    
  }
  inter_optimizer_verifier_config_ = inter_optimizer_verifier_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
}

// .tensorflow.VerifierConfig post_optimization_verifier_config = 301;
inline bool RewriterConfig::has_post_optimization_verifier_config() const {
  return this != internal_default_instance() && post_optimization_verifier_config_ != nullptr;
}
inline const ::tensorflow::VerifierConfig& RewriterConfig::post_optimization_verifier_config() const {
  const ::tensorflow::VerifierConfig* p = post_optimization_verifier_config_;
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.post_optimization_verifier_config)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::VerifierConfig*>(
      &::tensorflow::_VerifierConfig_default_instance_);
}
inline ::tensorflow::VerifierConfig* RewriterConfig::release_post_optimization_verifier_config() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.post_optimization_verifier_config)
  
  ::tensorflow::VerifierConfig* temp = post_optimization_verifier_config_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  post_optimization_verifier_config_ = nullptr;
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::unsafe_arena_release_post_optimization_verifier_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.post_optimization_verifier_config)
  
  ::tensorflow::VerifierConfig* temp = post_optimization_verifier_config_;
  post_optimization_verifier_config_ = nullptr;
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::mutable_post_optimization_verifier_config() {
  
  if (post_optimization_verifier_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VerifierConfig>(GetArenaNoVirtual());
    post_optimization_verifier_config_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.post_optimization_verifier_config)
  return post_optimization_verifier_config_;
}
inline void RewriterConfig::set_allocated_post_optimization_verifier_config(::tensorflow::VerifierConfig* post_optimization_verifier_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(post_optimization_verifier_config_);
  }
  if (post_optimization_verifier_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(post_optimization_verifier_config)->GetArena();
    if (message_arena != submessage_arena) {
      post_optimization_verifier_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, post_optimization_verifier_config, submessage_arena);
    }
    
  } else {
    
  }
  post_optimization_verifier_config_ = post_optimization_verifier_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.post_optimization_verifier_config)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::RewriterConfig_Toggle> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_Toggle>() {
  return ::tensorflow::RewriterConfig_Toggle_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::RewriterConfig_CpuLayout> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_CpuLayout>() {
  return ::tensorflow::RewriterConfig_CpuLayout_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::RewriterConfig_NumIterationsType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_NumIterationsType>() {
  return ::tensorflow::RewriterConfig_NumIterationsType_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::RewriterConfig_MemOptType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_MemOptType>() {
  return ::tensorflow::RewriterConfig_MemOptType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
