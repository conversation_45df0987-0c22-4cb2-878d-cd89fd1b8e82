metadata:
  name: openvino-omz-intel-semantic-segmentation-adas-0001
  namespace: cvat
  annotations:
    name: Semantic segmentation for ADAS
    type: detector
    spec: |
      [
        { "id": 0, "name": "road", "type": "mask" },
        { "id": 1, "name": "sidewalk", "type": "mask" },
        { "id": 2, "name": "building", "type": "mask" },
        { "id": 3, "name": "wall", "type": "mask" },
        { "id": 4, "name": "fence", "type": "mask" },
        { "id": 5, "name": "pole", "type": "mask" },
        { "id": 6, "name": "traffic light", "type": "mask" },
        { "id": 7, "name": "traffic sign", "type": "mask" },
        { "id": 8, "name": "vegetation", "type": "mask" },
        { "id": 9, "name": "terrain", "type": "mask" },
        { "id": 10, "name": "sky", "type": "mask" },
        { "id": 11, "name": "person", "type": "mask" },
        { "id": 12, "name": "rider", "type": "mask" },
        { "id": 13, "name": "car", "type": "mask" },
        { "id": 14, "name": "truck", "type": "mask" },
        { "id": 15, "name": "bus", "type": "mask" },
        { "id": 16, "name": "train", "type": "mask" },
        { "id": 17, "name": "motorcycle", "type": "mask" },
        { "id": 18, "name": "bicycle", "type": "mask" },
        { "id": 19, "name": "ego-vehicle", "type": "mask" },
        { "id": 20, "name": "background", "type": "mask" }
      ]

spec:
  description: Segmentation network to classify each pixel into typical 20 classes for ADAS
  runtime: 'python:3.8'
  handler: main:handler
  eventTimeout: 30s

  build:
    image: cvat.openvino.omz.intel.semantic-segmentation-adas-0001
    baseImage: cvat.openvino.omz.intel.semantic-segmentation-adas-0001.base

  triggers:
    myHttpTrigger:
      numWorkers: 2
      kind: 'http'
      workerAvailabilityTimeoutMilliseconds: 10000
      attributes:
        maxRequestBodySize: 33554432 # 32MB

  platform:
    attributes:
      restartPolicy:
        name: always
        maximumRetryCount: 3
      mountMode: volume
