// Copyright (C) 2021-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-organization-page {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-top: $grid-unit-size * 2;
    width: $grid-unit-size * 120;
    margin: 0 auto;

    .ant-empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    > div:nth-child(1) {
        align-items: flex-end;

        .cvat-organization-top-bar-buttons-block {
            text-align: right;
        }

        .cvat-organization-top-bar-descriptions {
            > div {
                > button {
                    margin-top: $grid-unit-size;
                }
            }

            > span {
                display: block;
            }

            > span:nth-child(3) {
                max-height: 7em;
                display: grid;
                overflow: auto;
                margin-bottom: $grid-unit-size;
            }

            > span:not(.cvat-title),
            div {
                font-size: 12px;

                span.anticon {
                    margin-right: $grid-unit-size;
                }

                span.anticon[aria-label="edit"] {
                    margin-left: $grid-unit-size;
                }
            }
        }

        .cvat-organization-top-bar-contacts {
            button {
                margin-top: $grid-unit-size;
                float: right;
            }

            > span {
                display: block;
            }

            > span,
            div {
                font-size: 12px;

                span.anticon {
                    margin-right: $grid-unit-size;
                }

                span.anticon[aria-label="edit"] {
                    margin-left: $grid-unit-size;
                }
            }
        }
    }

    .cvat-organization-members-list {
        flex-grow: 1;
        overflow: auto;
        margin-top: $grid-unit-size;
    }

    .cvat-organization-control-bar {
        margin-top: $grid-unit-size;
    }
}

.cvat-organization-member-item {
    border: 1px solid $border-color-1;
    border-radius: $border-radius-base;
    padding: $grid-unit-size;
    background: $background-color-1;
    margin-top: $grid-unit-size;
    align-items: center;

    > .cvat-organization-member-item-username,
    .cvat-organization-member-item-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    > .cvat-organization-member-item-dates {
        font-size: 12px;
        display: grid;
    }

    > .cvat-organization-member-item-remove {
        text-align: center;
    }

    > .cvat-organization-member-item-role {
        > .ant-select {
            width: 100%;
        }
    }
}

.cvat-organization-members-pagination-block {
    display: flex;
    justify-content: center;
    margin-top: 8px;
    margin-bottom: 8px;
}

.cvat-remove-organization-submit {
    > input {
        margin-top: $grid-unit-size;
    }
}

.cvat-organization-invitation-field {
    align-items: baseline;
}

.cvat-organization-page-actions-button {
    padding-right: $grid-unit-size * 0.5;
}

.cvat-organization-page-filters-wrapper {
    margin-top: $grid-unit-size * 2;

     > div {
        display: flex;

        > button:not(:last-child) {
            margin-right: $grid-unit-size;
        }
     }
}