#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据匹配和合并功能
创建示例数据来演示匹配逻辑
"""

import pandas as pd
import numpy as np
import os
from match_and_merge import DataMatcher

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 创建测试文件1 - 模拟合并结果.csv
    data1 = {
        'patient_id': ['P001', 'P002', 'P003', 'P004', 'P005'],
        'age': [45, 52, 38, 61, 29],
        'gender': ['M', 'F', 'M', 'F', 'M'],
        'result': ['Normal', 'Abnormal', 'Normal', 'Abnormal', 'Normal'],
        'match_key': ['KEY001_I', 'KEY002_II', 'KEY003_I', 'KEY004_III', 'KEY005_II']  # 最后一列作为匹配键
    }
    df1 = pd.DataFrame(data1)
    df1.to_csv('test_file1.csv', index=False, encoding='utf-8-sig')
    print("已创建 test_file1.csv")
    
    # 创建测试文件2 - 模拟无标题.xls
    data2 = {
        'es_key': ['KEY001', 'KEY002', 'KEY003', 'KEY004', 'KEY005', 'KEY006'],
        'lead': ['I', 'II', 'I', 'III', 'II', 'I'],
        'disease_name': ['心律不齐', '房颤', '正常', '室性早搏', '窦性心动过缓', '左束支传导阻滞'],
        'confidence': [0.85, 0.92, 0.98, 0.76, 0.88, 0.91]
    }
    df2 = pd.DataFrame(data2)
    df2.to_excel('test_file2.xlsx', index=False)
    print("已创建 test_file2.xlsx")
    
    return 'test_file1.csv', 'test_file2.xlsx'

def test_matching():
    """测试匹配功能"""
    print("\n" + "="*50)
    print("开始测试数据匹配功能")
    print("="*50)
    
    # 创建测试数据
    file1, file2 = create_test_data()
    
    # 显示测试数据内容
    print("\n测试文件1内容:")
    df1 = pd.read_csv(file1)
    print(df1)
    
    print("\n测试文件2内容:")
    df2 = pd.read_excel(file2)
    print(df2)
    
    # 执行匹配
    print("\n执行匹配...")
    matcher = DataMatcher(file1, file2, 'test_merged_result.csv')
    success = matcher.run()
    
    if success:
        print("\n匹配结果:")
        result_df = pd.read_csv('test_merged_result.csv')
        print(result_df)
        
        print("\n匹配分析:")
        print(f"总记录数: {len(result_df)}")
        print(f"成功匹配: {result_df['disease_name'].notna().sum()}")
        print(f"未匹配: {result_df['disease_name'].isna().sum()}")
        
        # 显示匹配详情
        print("\n匹配详情:")
        for idx, row in result_df.iterrows():
            status = "✓" if pd.notna(row['disease_name']) else "✗"
            disease = row['disease_name'] if pd.notna(row['disease_name']) else "未匹配"
            print(f"{status} {row['match_key']} -> {disease}")
    
    return success

def test_with_real_data():
    """使用真实数据测试（如果可用）"""
    print("\n" + "="*50)
    print("尝试使用真实数据测试")
    print("="*50)
    
    # 检查是否有真实的数据文件
    real_file1 = r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\一分钟接口结论\合并结果.csv"
    real_file2 = r"D:\ECG\0723一分钟项目测试\标注平台数据\无标题.xls"
    
    if os.path.exists(real_file1) and os.path.exists(real_file2):
        print("找到真实数据文件，开始测试...")
        matcher = DataMatcher(real_file1, real_file2, 'real_merged_result.csv')
        success = matcher.run()
        
        if success:
            print("真实数据匹配成功！")
            result_df = pd.read_csv('real_merged_result.csv')
            print(f"结果包含 {len(result_df)} 条记录")
            print(f"成功匹配 {result_df['disease_name'].notna().sum()} 条")
        
        return success
    else:
        print("未找到真实数据文件，跳过真实数据测试")
        print(f"文件1存在: {os.path.exists(real_file1)}")
        print(f"文件2存在: {os.path.exists(real_file2)}")
        return None

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_file1.csv', 
        'test_file2.xlsx', 
        'test_merged_result.csv',
        'match_and_merge.log',
        'matching_report.txt'
    ]
    
    print(f"\n清理测试文件...")
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"已删除: {file}")
            except Exception as e:
                print(f"删除失败 {file}: {e}")

def main():
    """主测试函数"""
    print("数据匹配和合并功能测试")
    print("="*50)
    
    try:
        # 测试基本匹配功能
        test_success = test_matching()
        
        if test_success:
            print("\n✓ 基本匹配功能测试通过")
        else:
            print("\n✗ 基本匹配功能测试失败")
        
        # 尝试真实数据测试
        real_test_result = test_with_real_data()
        
        if real_test_result is True:
            print("\n✓ 真实数据测试通过")
        elif real_test_result is False:
            print("\n✗ 真实数据测试失败")
        else:
            print("\n- 真实数据测试跳过")
        
        # 询问是否清理测试文件
        cleanup = input("\n是否清理测试文件? (y/n): ").lower().strip()
        if cleanup == 'y':
            cleanup_test_files()
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
