// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-remote-browser-nav-breadcrumb {
    transition: all 0.5s;
    border-radius: $border-radius-base * 0.5;
    padding-left: $grid-unit-size * 0.5;
    padding-right: $grid-unit-size * 0.5;
    opacity: 0.8;
    cursor: pointer;

    &:hover {
        opacity: 1;
        background: $background-color-2;
    }
}

.cvat-remote-browser-empty {
    text-align: center;
    width: 50%;
    margin-left: 25%;
    margin-top: $grid-unit-size * 4;
}

.cvat-remote-browser-search-wrapper {
    margin-bottom: $grid-unit-size;
    margin-top: $grid-unit-size;
}

.cvat-remote-browser-incorrect-cs-prefix-wrapper {
    margin-top: $grid-unit-size;
}

.cvat-remote-browser-table-wrapper {
    .ant-table-wrapper {
        height: $grid-unit-size * 64;

        .ant-table-body {
            overflow-y: auto !important;
        }

        .ant-pagination {
            display: none;
        }

        .ant-empty {
            margin: $grid-unit-size * 24 0 $grid-unit-size * 24 0;
        }
    }

    .cvat-remote-browser-file-icon {
        margin-right: $grid-unit-size;
    }

    .cvat-remote-browser-pages {
        display: flex;
        justify-content: center;
        margin-top: $grid-unit-size;

        .ant-pagination-next {
            display: flex;
            align-items: center;

            .cvat-remote-browser-receive-more-btn {
                color: rgba(0, 0, 0, 85%) !important;
                cursor: pointer !important;

                > span {
                    pointer-events: all;
                    line-height: $grid-unit-size * 4;
                    width: 100%;
                }
            }
        }
    }

    button {
        padding: 0;
    }
}

.cvat-file-manager-local-tab {
    > span:nth-child(1) {
        display: block;
    }
}

.cvat-create-task-page-cloud-storages-tab {
    display: flex;
    justify-content: space-between;
}
