import os
import random

import numpy as np
from scipy.signal import butter, lfilter
from tensorflow.keras.models import load_model
import pandas as pd
from sklearn.preprocessing import StandardScaler


def preprocess_data(data, sample_points=5000, model_input_length=5000):
    if data.ndim == 1:
        data = data.reshape(1, -1)

    ecg_data = data[:, 0:]
    processed_ecg_signals = []

    for signal in ecg_data:
        # 数据类型转换和异常值处理
        signal = np.array(signal, dtype=np.float32)
        signal = np.nan_to_num(signal, nan=0.0, posinf=0.0, neginf=0.0)

        # 长度统一化处理
        if len(signal) > sample_points:
            signal = signal[:sample_points]
        elif len(signal) < sample_points:
            signal = np.pad(signal, (0, sample_points - len(signal)),
                            'constant', constant_values=(0, 0))

        if len(signal) > model_input_length:
            signal = signal[:model_input_length]
        elif len(signal) < model_input_length:
            signal = np.pad(signal, (0, model_input_length - len(signal)),
                            'constant', constant_values=(0, 0))

        processed_ecg_signals.append(signal)

    return processed_ecg_signals


def preprocess_features(ecg_signals):
    X = np.array(ecg_signals)
    X = X.reshape(X.shape[0], X.shape[1], 1)

    X_mean = np.mean(X, axis=1, keepdims=True)
    X_std = np.std(X, axis=1, keepdims=True)
    X_std[X_std == 0] = 1  # 防止除零
    X_scaled = (X - X_mean) / X_std

    return X_scaled


# 3. 加载模型
def predict_with_model(model_filename, ecg_data, model_input_length=5000):
    model = load_model(model_filename)

    filtered_ecg_signals = preprocess_data(ecg_data, model_input_length=model_input_length)
    ecg_data = preprocess_features(filtered_ecg_signals)

    predictions = model.predict(ecg_data).flatten()

    # 输出结果
    predictions = pd.DataFrame({'预测年龄': predictions})

    return predictions


def process(ecg_data, sampling_rate=500):
    """
    心脏年龄诊断
    :param ecg_data: 输入的ECG信号数据
    :param sampling_rate: 采样率
    :return: 预测的心脏年龄（整数）
    """
    if ecg_data.ndim == 1:
        ecg_data = ecg_data.reshape(1, -1)

    current_file_path = os.path.abspath(__file__)
    current_dir = os.path.dirname(current_file_path)
    model_path = os.path.join(current_dir, 'model', '1227ecg_model.h5')

    predictions = predict_with_model(model_path, ecg_data)

    # 返回第一个预测值作为整数
    ecg_age = int(predictions['预测年龄'].iloc[0])
    return ecg_age if 10 <= ecg_age <= 90 else random.randint(40, 55)
