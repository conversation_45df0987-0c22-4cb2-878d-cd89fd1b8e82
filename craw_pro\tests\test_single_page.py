#!/usr/bin/env python3
"""
测试单个装备页面的数据提取
分析页面结构并优化提取逻辑
"""

import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_driver():
    """设置Chrome浏览器驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver

def solve_verification(driver):
    """处理验证问题"""
    try:
        page_source = driver.page_source
        verification_keywords = ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛']
        
        if not any(keyword in page_source for keyword in verification_keywords):
            return True
        
        logger.info("🔐 检测到验证页面，尝试解决...")
        
        # 查找验证问题
        question_patterns = [
            r'(\d+[×*x]\d+)=？',
            r'(\d+[+]\d+)=？', 
            r'(\d+[-]\d+)=？',
            r'羽毛球有几根毛',
            r'ZYZX.*?怎么写',
        ]
        
        question = None
        for pattern in question_patterns:
            matches = re.findall(pattern, page_source, re.IGNORECASE)
            if matches:
                question = matches[0]
                break
        
        if not question:
            return False
        
        # 获取答案
        if '羽毛球' in question and ('几根毛' in question or '多少毛' in question):
            answer = "16"
        elif 'ZYZX' in question and ('小写' in question or '怎么写' in question):
            answer = "zyzx"
        elif re.match(r'\d+[×*x+\-]\d+', question):
            math_expr = question.replace('×', '*').replace('x', '*')
            try:
                answer = str(eval(math_expr))
            except:
                return False
        else:
            return False
        
        # 提交答案
        input_element = driver.find_element(By.CSS_SELECTOR, "input[type='text']")
        input_element.clear()
        input_element.send_keys(answer)
        
        submit_button = driver.find_element(By.CSS_SELECTOR, "input[type='submit']")
        submit_button.click()
        
        logger.info(f"🔐 已提交验证答案: {question} = {answer}")
        time.sleep(3)
        return True
        
    except Exception as e:
        logger.error(f"验证处理失败: {e}")
        return False

def analyze_page_structure(url):
    """分析页面结构并提取所有可能的数据"""
    driver = setup_driver()
    
    try:
        logger.info(f"🌐 访问页面: {url}")
        driver.get(url)
        time.sleep(3)
        
        # 处理验证
        if not solve_verification(driver):
            logger.warning("验证失败")
            return
        
        # 获取页面源码
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # 保存页面源码用于分析
        with open('debug_single_page.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        
        logger.info("✅ 页面源码已保存到 debug_single_page.html")
        
        # 基本信息提取
        title = driver.title
        logger.info(f"📋 页面标题: {title}")
        
        # 查找所有表格
        tables = soup.find_all('table')
        logger.info(f"🔍 找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            logger.info(f"\n--- 表格 {i+1} ---")
            rows = table.find_all('tr')
            for j, row in enumerate(rows):
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    key = cells[0].get_text(strip=True)
                    value = cells[1].get_text(strip=True)
                    if key and value:
                        logger.info(f"  {key}: {value}")
        
        # 查找所有div
        divs = soup.find_all('div')
        logger.info(f"\n🔍 找到 {len(divs)} 个div元素")
        
        # 查找包含参数的div
        param_divs = []
        for div in divs:
            text = div.get_text(strip=True)
            if any(keyword in text for keyword in ['重量', '平衡', '硬度', '材质', '价格', '品牌']):
                param_divs.append(div)
        
        logger.info(f"🎯 找到 {len(param_divs)} 个可能包含参数的div")
        
        for i, div in enumerate(param_divs[:10]):  # 只显示前10个
            text = div.get_text(strip=True)
            if text and len(text) < 200:  # 过滤太长的文本
                logger.info(f"  参数div {i+1}: {text}")
        
        # 查找所有span
        spans = soup.find_all('span')
        param_spans = []
        for span in spans:
            text = span.get_text(strip=True)
            if any(keyword in text for keyword in ['重量', '平衡', '硬度', '材质', '价格', '品牌']):
                param_spans.append(span)
        
        logger.info(f"\n🎯 找到 {len(param_spans)} 个可能包含参数的span")
        
        for i, span in enumerate(param_spans[:10]):
            text = span.get_text(strip=True)
            if text and len(text) < 100:
                logger.info(f"  参数span {i+1}: {text}")
        
        # 使用正则表达式查找所有参数
        full_text = soup.get_text()
        
        logger.info("\n🔍 正则表达式匹配结果:")
        
        patterns = {
            '重量': [
                r'重量[：:\s]*([0-9]+(?:\.[0-9]+)?)\s*[gG克]',
                r'拍身重量[：:\s]*([0-9]+(?:\.[0-9]+)?)\s*[gG克]',
                r'(?:重量|Weight)[：:\s]*([0-9]+(?:\.[0-9]+)?)\s*[gG克]'
            ],
            '平衡点': [
                r'平衡点[：:\s]*([0-9]+(?:\.[0-9]+)?)\s*[mM毫米]*',
                r'平衡[：:\s]*([0-9]+(?:\.[0-9]+)?)\s*[mM毫米]*',
                r'(?:平衡点|Balance)[：:\s]*([0-9]+(?:\.[0-9]+)?)\s*[mM毫米]*'
            ],
            '硬度': [
                r'硬度[：:\s]*(偏?[软硬中]硬?|[0-9]+)',
                r'拍杆硬度[：:\s]*(偏?[软硬中]硬?|[0-9]+)',
                r'(?:硬度|Stiffness)[：:\s]*(偏?[软硬中]硬?|[0-9]+)'
            ],
            '价格': [
                r'新品均价[：:\s]*[¥￥]?\s*(\d+)',
                r'均价[：:\s]*[¥￥]?\s*(\d+)',
                r'价格[：:\s]*[¥￥]?\s*(\d+)',
                r'二手均价[：:\s]*[¥￥]?\s*(\d+)'
            ],
            '材质': [
                r'材质[：:\s]*([^\n\r\t]{1,50})',
                r'拍框材质[：:\s]*([^\n\r\t]{1,50})',
                r'拍杆材质[：:\s]*([^\n\r\t]{1,50})'
            ],
            '品牌': [
                r'品牌[：:\s]*([^\n\r\t]{1,30})',
                r'装备品牌[：:\s]*([^\n\r\t]{1,30})'
            ]
        }
        
        extracted_data = {}
        for param_name, param_patterns in patterns.items():
            for pattern in param_patterns:
                matches = re.findall(pattern, full_text, re.IGNORECASE)
                if matches:
                    value = matches[0].strip()
                    if value and len(value) < 100:
                        extracted_data[param_name] = value
                        logger.info(f"  ✅ {param_name}: {value}")
                        break
        
        logger.info(f"\n📊 成功提取的参数: {len(extracted_data)} 个")
        logger.info(f"📋 提取结果: {extracted_data}")
        
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    # 测试指定页面
    test_url = "https://www.badmintoncn.com/cbo_eq/view.php?eid=22974"
    analyze_page_structure(test_url) 