         1320 function calls (1296 primitive calls) in 0.829 seconds

   Ordered by: internal time

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.828    0.828    0.828    0.828 {built-in method builtins.input}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:493(_parse)
     10/3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:71(_compile)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:288(__init__)
        4    0.000    0.000    0.000    0.000 {built-in method nt.get_terminal_size}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1337(add_argument)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1255(__init__)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:289(_compile)
        2    0.000    0.000    0.000    0.000 {method 'write' of '_io.TextIOWrapper' objects}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\shutil.py:1312(get_terminal_size)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:161(__init__)
        1    0.000    0.000    0.000    0.000 D:\Project\ECGAnalysis\apps\api\tests\api_test.py:85(get_token)
     10/3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:174(getwidth)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:670(__getitem__)
       63    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:164(__getitem__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:180(split)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:536(_compile_info)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:355(_escape)
      146    0.000    0.000    0.000    0.000 {method 'append' of 'list' objects}
       37    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1308(register)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1565(_log)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:435(_parse_sub)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1638(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1514(findCaller)
      129    0.000    0.000    0.000    0.000 {built-in method builtins.isinstance}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:319(decode)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1478(_get_optional_kwargs)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:759(compile)
  120/110    0.000    0.000    0.000    0.000 {built-in method builtins.len}
       23    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:233(__next)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:586(_format_args)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:212(_expand_lang)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1689(isEnabledFor)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:538(find)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1809(_parse_known_args)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:276(_optimize_charset)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:579(translation)
        1    0.000    0.000    0.001    0.001 D:\Project\ECGAnalysis\apps\api\tests\api_test.py:76(parse_arguments)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:740(encodekey)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:124(splitdrive)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2041(error)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1398(_add_action)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:937(parse)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1774(parse_known_args)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:570(_metavar_formatter)
       33    0.000    0.000    0.000    0.000 {method 'get' of 'dict' objects}
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:657(get)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1567(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1073(emit)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:44(normcase)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:809(__init__)
       26    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:160(__len__)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:172(append)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:941(handle)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:655(format)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:938(__and__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:121(_splitext)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:254(get)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1465(error)
       18    0.000    0.000    0.000    0.000 {built-in method builtins.hasattr}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1062(flush)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1645(callHandlers)
       29    0.000    0.000    0.000    0.000 {built-in method builtins.min}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:734(check_str)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:598(_code)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1589(_add_action)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:249(_compile_charset)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\locale.py:384(normalize)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2480(_get_formatter)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1550(makeRecord)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:660(dgettext)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1388(add_argument_group)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1518(_get_handler)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:461(_get_literal_prefix)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:224(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:203(splitext)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:214(basename)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:250(compile)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:218(_acquireLock)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:631(__new__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:851(__init__)
        5    0.000    0.000    0.000    0.000 {method 'match' of 're.Pattern' objects}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\types.py:171(__get__)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:313(__call__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:286(tell)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:435(_format)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1954(consume_positionals)
        1    0.000    0.000    0.000    0.000 {built-in method _codecs.utf_8_decode}
       43    0.000    0.000    0.000    0.000 {method 'setdefault' of 'dict' objects}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:160(<lambda>)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1514(_pop_action_class)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:111(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1591(handle)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:202(__init__)
       11    0.000    0.000    0.000    0.000 {method 'find' of 'str' objects}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:921(fix_flags)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:438(format)
        5    0.000    0.000    0.000    0.000 {method 'acquire' of '_thread.RLock' objects}
       20    0.000    0.000    0.000    0.000 {method 'upper' of 'str' objects}
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:423(_simple)
        8    0.000    0.000    0.000    0.000 {built-in method builtins.max}
       11    0.000    0.000    0.000    0.000 {method 'replace' of 'str' objects}
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:595(isstring)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1312(_registry_get)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1747(_add_action)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:633(usesTime)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:918(format)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1031(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:492(_get_charset_prefix)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:249(match)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:898(acquire)
        3    0.000    0.000    0.000    0.000 {built-in method _sre.compile}
        2    0.000    0.000    0.000    0.000 {method 'flush' of '_io.TextIOWrapper' objects}
        6    0.000    0.000    0.000    0.000 {method 'rfind' of 'str' objects}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:579(format)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:81(groups)
        4    0.000    0.000    0.000    0.000 {method 'find' of 'bytearray' objects}
        3    0.000    0.000    0.000    0.000 {built-in method builtins.setattr}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:76(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:227(_releaseLock)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:427(usesTime)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:639(formatMessage)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1527(_check_conflict)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:119(getLevelName)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:796(filter)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1306(current_thread)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:735(gettext)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:432(_generate_overlap_table)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:364(getMessage)
        3    0.000    0.000    0.000    0.000 {built-in method builtins.getattr}
       12    0.000    0.000    0.000    0.000 {built-in method nt.fspath}
        3    0.000    0.000    0.000    0.000 {built-in method time.time}
        4    0.000    0.000    0.000    0.000 {method 'fileno' of '_io.TextIOWrapper' objects}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:34(_get_bothseps)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:905(release)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\multiprocessing\process.py:37(current_process)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1031(name)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2106(_match_arguments_partial)
        7    0.000    0.000    0.000    0.000 {method 'lower' of 'str' objects}
        3    0.000    0.000    0.000    0.000 {method 'rstrip' of 'str' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:574(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1759(_get_positional_actions)
        2    0.000    0.000    0.000    0.000 {built-in method sys._getframe}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:331(getstate)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:453(_get_iscased)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:162(__delitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1767(parse_args)
        8    0.000    0.000    0.000    0.000 {method 'pop' of 'dict' objects}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:753(value)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:168(__setitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1675(getEffectiveLevel)
        2    0.000    0.000    0.000    0.000 {built-in method nt.getpid}
        5    0.000    0.000    0.000    0.000 {method 'release' of '_thread.RLock' objects}
        4    0.000    0.000    0.000    0.000 {built-in method _thread.get_ident}
        8    0.000    0.000    0.000    0.000 {built-in method builtins.callable}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1276(disable)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\multiprocessing\process.py:189(name)
        4    0.000    0.000    0.000    0.000 {method 'lstrip' of 'str' objects}
        3    0.000    0.000    0.000    0.000 {method 'join' of 'str' objects}
        1    0.000    0.000    0.000    0.000 {built-in method sys.exit}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1240(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1760(<listcomp>)
        3    0.000    0.000    0.000    0.000 {method 'items' of 'dict' objects}
        3    0.000    0.000    0.000    0.000 {method 'reverse' of 'list' objects}
        3    0.000    0.000    0.000    0.000 {method 'extend' of 'list' objects}
        3    0.000    0.000    0.000    0.000 {built-in method builtins.ord}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:604(<listcomp>)
        1    0.000    0.000    0.000    0.000 {built-in method builtins.iter}
        1    0.000    0.000    0.000    0.000 {method 'disable' of '_lsprof.Profiler' objects}


