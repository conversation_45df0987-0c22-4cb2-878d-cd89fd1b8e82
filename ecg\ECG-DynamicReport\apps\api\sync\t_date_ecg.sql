CREATE TABLE IF NOT EXISTS t_data_ecg_{data_date} (
    id BIGINT AUTO_INCREMENT,
    ecg_id BIGINT,
    union_id VARCHAR(255),
    dead INT DEFAULT 1 COMMENT '数据状态标记（1:有效, 2:无效）',
    check_date DATETIME,

    -- 三导联报告字段
    report_lead_i TEXT COMMENT 'I导联报告',
    report_lead_ii TEXT COMMENT 'II导联报告',
    report_lead_iii TEXT COMMENT 'III导联报告',

    -- 心律失常诊断字段
    ArrhythmiaDiagnosis_AF INT,
    ArrhythmiaDiagnosis_AFL INT,
    ArrhythmiaDiagnosis_PAC INT,
    ArrhythmiaDiagnosis_PVC INT,
    ArrhythmiaDiagnosis_AE INT,
    ArrhythmiaDiagnosis_AVBI INT,
    ArrhythmiaDiagnosis_AVBII INT,
    ArrhythmiaDiagnosis_AVBIII INT,
    ArrhythmiaDiagnosis_BRU INT,
    ArrhythmiaDiagnosis_IVB INT,
    ArrhythmiaDiagnosis_JE INT,
    ArrhythmiaDiagnosis_LAFB INT,
    ArrhythmiaDiagnosis_LBBB INT,
    ArrhythmiaDiagnosis_LQT INT,
    ArrhythmiaDiagnosis_PJC INT,
    ArrhythmiaDiagnosis_PSC INT,
    ArrhythmiaDiagnosis_RBBB INT,
    ArrhythmiaDiagnosis_SN INT,
    ArrhythmiaDiagnosis_SNA INT,
    ArrhythmiaDiagnosis_SNB INT,
    ArrhythmiaDiagnosis_SNT INT,
    ArrhythmiaDiagnosis_SVT INT,
    ArrhythmiaDiagnosis_VE INT,
    ArrhythmiaDiagnosis_VT INT,
    ArrhythmiaDiagnosis_WPW INT,
    ArrhythmiaDiagnosis_bPVC INT,
    ArrhythmiaDiagnosis_SA INT,
    -- CAD心肌病相关字段
    CADCardiomyopathy_ISC INT,
    CADCardiomyopathy_LAH INT,
    CADCardiomyopathy_LVH INT,
    CADCardiomyopathy_MI INT,
    CADCardiomyopathy_RAH INT,
    CADCardiomyopathy_RVH INT,

    -- 健康指标字段
    HealthMetrics_Emotion INT,
    HealthMetrics_Fatigue INT,
    HealthMetrics_HRV INT,
    HealthMetrics_Pressure INT,
    HealthMetrics_Vitality INT,

    -- PQRST相关参数
    PQRSTC_HR INT,
    PQRSTC_PR_interval INT,
    PQRSTC_P_duration INT,
    PQRSTC_QT INT,
    PQRSTC_QTc INT,
    PQRSTC_ST_duration INT,
    PQRSTC_NN_MAX FLOAT,
    PQRSTC_NN_MIN FLOAT,
    PQRSTC_RR_MAX FLOAT,
    PQRSTC_RR_MIN FLOAT,

    -- 其他
    ECGAge INT,
    HeartFailureRisk INT,
    OSARisk INT,
    RespiratoryRate INT,
    SignalQuantity INT,
    SleepStage INT,
    SyncopeRisk INT,
    VentricularFibrillationRisk INT,
    avgHr INT,
    RRIntervals VARCHAR(255),
    NNIntervals VARCHAR(255),

    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id)
)