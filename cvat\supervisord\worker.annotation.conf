[unix_http_server]
file = /tmp/supervisord/supervisor.sock

[supervisorctl]
serverurl = unix:///tmp/supervisord/supervisor.sock


[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisord]
nodaemon=true
logfile=%(ENV_HOME)s/logs/supervisord.log ; supervisord log file
logfile_maxbytes=50MB       ; maximum size of logfile before rotation
logfile_backups=10          ; number of backed up logfiles
loglevel=debug              ; info, debug, warn, trace
pidfile=/tmp/supervisord/supervisord.pid ; pidfile location

[program:rqworker-annotation]
command=%(ENV_HOME)s/wait_for_deps.sh
    python3 %(ENV_HOME)s/manage.py rqworker -v 3 annotation
        --worker-class cvat.rqworker.DefaultWorker
environment=VECTOR_EVENT_HANDLER="SynchronousLogstashHandler",CVAT_POSTGRES_APPLICATION_NAME="cvat:worker:annotation"
numprocs=%(ENV_NUMPROCS)s
process_name=%(program_name)s-%(process_num)d
autorestart=true

[program:smokescreen]
command=smokescreen --listen-ip=127.0.0.1 %(ENV_SMOKESCREEN_OPTS)s
