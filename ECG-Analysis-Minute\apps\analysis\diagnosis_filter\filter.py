from dataclasses import dataclass, field
import json
from pathlib import Path
from typing import List


def load_rules(rule_type=None):
    """
    加载规则文件
    :param rule_type: 规则类型
    :return:
    """
    # 获取当前执行文件的路径
    current_file = Path(__file__)
    # 构建规则文件的路径
    rules_file_path = current_file.parent / 'filter_rules.json'

    with open(rules_file_path) as file:
        configs = json.load(file)

    if rule_type:
        return [rule for rule in configs['rules'] if rule['type'] == rule_type]

    return configs["rules"]


def method1_handler(filter_entity, actions):
    """
    模式1处理
    如果trigger标签的值为1，将SN的值设置为0
    :param filter_entity: 过滤实体对象
    :param actions: 动作集
    :return:
    """
    keys_to_check = [action for action in actions]
    arrhythmia_diagnosis = filter_entity.arrhythmia_diagnosis

    # 检查 arrhythmia_diagnosis 中是否包含 actions 中的任意一个字段
    if any(item in keys_to_check for item in arrhythmia_diagnosis):
        # 如果包含，则删除所有 'SN'
        arrhythmia_diagnosis = [item for item in arrhythmia_diagnosis if item != 'SN']

    return arrhythmia_diagnosis


def method2_handler(filter_entity, actions):
    """
    模式2处理
    将action的key按优先级排序，按顺序判断key的value是否为1，如果是则将后续的key的值设置为0
    :param filter_entity: 过滤实体对象
    :param actions: 动作集
    :return:
    """
    arrhythmia_diagnosis = filter_entity.arrhythmia_diagnosis

    # 根据index排序
    sorted_actions = sorted(actions, key=lambda x: x['index'])

    # 用于记录是否已经发现了一个匹配的元素
    found_one = False

    # 遍历 actions 列表
    for action in sorted_actions:
        if action['key'] in arrhythmia_diagnosis:
            if found_one:
                # 如果已经发现了一个匹配的元素，则删除当前匹配的元素
                arrhythmia_diagnosis.remove(action['key'])
            else:
                # 如果这是第一个匹配的元素，标记为已找到
                found_one = True

    return arrhythmia_diagnosis


def method3_handler(filter_entity, actions):
    """
    模式3处理
    min 当待判断的结论符合时，hr需要大于default_hr, 否则为0
    max 当待判断的结论符合时，hr需要小于default_hr, 否则为0
    :param filter_entity: 过滤实体对象
    :param actions:
    :return:
    """
    arrhythmia_diagnosis = filter_entity.arrhythmia_diagnosis

    for conclusion in actions['min']['conclusion']:
        if conclusion in arrhythmia_diagnosis:
            if filter_entity.hr < actions['min']['default_hr']:
                arrhythmia_diagnosis.remove(conclusion)

    for conclusion in actions['max']['conclusion']:
        if conclusion in arrhythmia_diagnosis:
            if filter_entity.hr > actions['max']['default_hr']:
                arrhythmia_diagnosis.remove(conclusion)

    return arrhythmia_diagnosis


def method4_handler(filter_entity, actions):
    """
    模式4处理
    当诊断为LQT时，判断QTC的值是否超过阈值
    :param filter_entity: 过滤实体对象
    :param actions: 动作集
    :return:
    """
    comparison_operators = {
        '>': lambda a, b: a > b,
        '<': lambda a, b: a < b,
        '==': lambda a, b: a == b,
        '>=': lambda a, b: a >= b,
        '<=': lambda a, b: a <= b,
        '!=': lambda a, b: a != b
    }

    arrhythmia_diagnosis = filter_entity.arrhythmia_diagnosis

    for action in actions:
        if action['key'] in arrhythmia_diagnosis:

            # 动态获取比较函数
            compare = comparison_operators.get(action['condition'])

            if compare(__get_nested_attribute(filter_entity, action['indicators']), action["value"]):
                arrhythmia_diagnosis.remove(action['key'])

    return arrhythmia_diagnosis


def apply_rules(filter_entity, rule_type=None):
    """
    应用规则
    :param filter_entity: 过滤实体
    :param rule_type: 1 心律失常 2 心电指标
    :return: 规则处理后的分析实体
    """
    handler_map = {
        1: method1_handler,
        2: method2_handler,
        3: method3_handler,
        4: method4_handler
    }

    # 加载规则
    rules = load_rules(rule_type)

    sorted_rules = sorted(rules, key=lambda x: x['method'])

    for rule in sorted_rules:
        method = rule['method']
        actions = rule['actions']

        # 根据方法获取对应的处理策略
        filter_entity.arrhythmia_diagnosis = handler_map.get(method)(filter_entity, actions)

    return filter_entity.arrhythmia_diagnosis


def __get_nested_attribute(obj, attr_path):
    """
    根据属性路径获取嵌套属性的值
    :param obj: 对象
    :param attr_path: 属性路径，如 "PQRSTC.QTC"
    :return: 属性值
    """
    attributes = attr_path.split('.')
    for attr in attributes:
        obj = getattr(obj, attr, None)
        if obj is None:
            break
    return obj


@dataclass
class FilterEntity:
    arrhythmia_diagnosis: List[str] = field(default_factory=list)  # 默认为空列表
    hr: int = field(default=0)  # 默认值为 0
    qtc: int = field(default=0)  # 默认值为 0
