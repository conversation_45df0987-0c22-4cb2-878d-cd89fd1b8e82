# 快速开始指南

## 立即使用

### 1. 基础版本（推荐新手）
```bash
python match_and_merge.py
```
- 如果默认文件路径存在，直接开始匹配
- 如果不存在，会启动交互式文件选择

### 2. 增强版本（推荐高级用户）
```bash
python match_and_merge_enhanced.py
```
- 更多配置选项
- 详细的分析报告
- 支持命令行参数

### 3. 测试功能
```bash
python test_match_and_merge.py
```
- 创建示例数据进行测试
- 验证匹配功能是否正常

## 命令行选项（增强版）

```bash
# 指定文件路径
python match_and_merge_enhanced.py --file1 "path/to/file1.csv" --file2 "path/to/file2.xlsx"

# 使用配置文件
python match_and_merge_enhanced.py --config config_example.json

# 区分大小写匹配
python match_and_merge_enhanced.py --case-sensitive

# 设置日志级别
python match_and_merge_enhanced.py --log-level DEBUG
```

## 文件要求

### 源文件1（合并结果.csv）
- 最后一列将用作匹配键
- 格式示例：`KEY001_I`, `KEY002_II`

### 源文件2（无标题.xls）
必须包含以下列：
- `es_key`: 匹配键第一部分（如：KEY001）
- `lead`: 匹配键第二部分（如：I）
- `disease_name`: 疾病名称（如：心律不齐）

## 输出文件

1. **主要结果**: `merged_result_with_disease.csv`
2. **匹配报告**: `matching_report_YYYYMMDD_HHMMSS.txt`
3. **日志文件**: `match_and_merge_YYYYMMDD_HHMMSS.log`

## 常见问题解决

### 问题1：匹配率为0%
**解决方案**：
1. 检查匹配键格式是否一致
2. 查看日志文件中的匹配键示例
3. 确认源文件2包含必需的列

### 问题2：文件编码错误
**解决方案**：
1. 将CSV文件另存为UTF-8编码
2. 或者程序会自动尝试GBK编码

### 问题3：Excel文件读取失败
**解决方案**：
1. 确保Excel文件未被占用
2. 检查文件是否损坏
3. 尝试另存为新的Excel文件

## 配置文件示例

创建 `my_config.json`：
```json
{
  "file1_path": "你的文件1路径.csv",
  "file2_path": "你的文件2路径.xlsx",
  "output_path": "自定义输出文件名.csv",
  "case_sensitive": false,
  "remove_duplicates": true,
  "backup_original": true
}
```

然后运行：
```bash
python match_and_merge_enhanced.py --config my_config.json
```

## 成功示例

运行成功后，您会看到类似输出：
```
2025-07-30 11:29:57 - INFO - 成功加载文件1: test_file1.csv
2025-07-30 11:29:57 - INFO - 文件1形状: (5, 5)
2025-07-30 11:29:57 - INFO - 成功加载文件2: test_file2.xlsx
2025-07-30 11:29:57 - INFO - 文件2形状: (6, 4)
2025-07-30 11:29:57 - INFO - 最终匹配统计:
2025-07-30 11:29:57 - INFO -   总记录数: 5
2025-07-30 11:29:57 - INFO -   成功匹配: 5
2025-07-30 11:29:57 - INFO -   匹配率: 100.00%
```

## 获取帮助

查看完整帮助信息：
```bash
python match_and_merge_enhanced.py --help
```

查看详细文档：
```bash
# 查看README文件
cat README_match_and_merge.md
```
