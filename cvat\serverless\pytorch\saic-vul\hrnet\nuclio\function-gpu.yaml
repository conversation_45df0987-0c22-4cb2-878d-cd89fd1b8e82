metadata:
  name: pth-saic-vul-hrnet
  namespace: cvat
  annotations:
    name: HRNET
    version: 2
    type: interactor
    spec:
    min_pos_points: 1
    min_neg_points: 0
    animated_gif: https://raw.githubusercontent.com/cvat-ai/cvat/develop/site/content/en/images/hrnet_example.gif
    help_message: The interactor allows to get a mask for an object using positive points, and negative points

spec:
  description: HRNet18 for click based interactive segmentation
  runtime: 'python:3.8'
  handler: main:handler
  eventTimeout: 30s
  env:
    - name: PYTHONPATH
      value: /opt/nuclio/hrnet

  build:
    image: cvat.pth.saic-vul.hrnet:latest-gpu
    baseImage: ubuntu:20.04

    directives:
      preCopy:
        - kind: ENV
          value: DEBIAN_FRONTEND=noninteractive
        - kind: RUN
          value: apt-get update && apt-get install software-properties-common -y
        - kind: RUN
          value: add-apt-repository ppa:deadsnakes/ppa
        - kind: RUN
          value: apt remove python* -y
        - kind: RUN
          value: apt-get update && apt-get install -y --no-install-recommends build-essential git curl libglib2.0-0 software-properties-common python3 python3.6-dev python3-pip python3-tk
        - kind: RUN
          value: ln -s /usr/bin/pip3 /usr/local/bin/pip && ln -s /usr/bin/python3 /usr/bin/python
        - kind: RUN
          value: pip3 install --upgrade pip
        - kind: WORKDIR
          value: /opt/nuclio
        - kind: RUN
          value: git clone https://github.com/saic-vul/ritm_interactive_segmentation.git hrnet
        - kind: WORKDIR
          value: /opt/nuclio/hrnet
        - kind: RUN
          value: apt-get install -y --no-install-recommends wget
        - kind: RUN
          value: wget https://github.com/saic-vul/ritm_interactive_segmentation/releases/download/v1.0/coco_lvis_h18_itermask.pth
        - kind: RUN
          value: pip3 install setuptools
        - kind: RUN
          value: pip3 install -r requirements.txt "numpy<1.24"  # the NumPy version is not pinned and HRNet uses np.int which throws an error for NumPy >= 1.24 (PR#5574)
        - kind: RUN
          value: apt update && apt install -y libgl1-mesa-glx
        - kind: RUN
          value: pip3 uninstall torch torch vision -y
        - kind: RUN
          value: pip install torch==1.8.1+cu111 torchvision==0.9.1+cu111 torchaudio==0.8.1 -f https://download.pytorch.org/whl/torch_stable.html
        - kind: WORKDIR
          value: /opt/nuclio

  triggers:
    myHttpTrigger:
      numWorkers: 1
      kind: 'http'
      workerAvailabilityTimeoutMilliseconds: 10000
      attributes:
        maxRequestBodySize: 33554432 # 32MB

  resources:
    limits:
      nvidia.com/gpu: 1

  platform:
    attributes:
      restartPolicy:
        name: always
        maximumRetryCount: 3
      mountMode: volume
