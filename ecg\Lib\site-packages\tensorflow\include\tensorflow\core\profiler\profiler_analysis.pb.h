// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/profiler_analysis.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/profiler/profiler_service.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[8]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto;
namespace tensorflow {
class EnumProfileSessionsAndToolsRequest;
class EnumProfileSessionsAndToolsRequestDefaultTypeInternal;
extern EnumProfileSessionsAndToolsRequestDefaultTypeInternal _EnumProfileSessionsAndToolsRequest_default_instance_;
class EnumProfileSessionsAndToolsResponse;
class EnumProfileSessionsAndToolsResponseDefaultTypeInternal;
extern EnumProfileSessionsAndToolsResponseDefaultTypeInternal _EnumProfileSessionsAndToolsResponse_default_instance_;
class NewProfileSessionRequest;
class NewProfileSessionRequestDefaultTypeInternal;
extern NewProfileSessionRequestDefaultTypeInternal _NewProfileSessionRequest_default_instance_;
class NewProfileSessionResponse;
class NewProfileSessionResponseDefaultTypeInternal;
extern NewProfileSessionResponseDefaultTypeInternal _NewProfileSessionResponse_default_instance_;
class ProfileSessionDataRequest;
class ProfileSessionDataRequestDefaultTypeInternal;
extern ProfileSessionDataRequestDefaultTypeInternal _ProfileSessionDataRequest_default_instance_;
class ProfileSessionDataRequest_ParametersEntry_DoNotUse;
class ProfileSessionDataRequest_ParametersEntry_DoNotUseDefaultTypeInternal;
extern ProfileSessionDataRequest_ParametersEntry_DoNotUseDefaultTypeInternal _ProfileSessionDataRequest_ParametersEntry_DoNotUse_default_instance_;
class ProfileSessionDataResponse;
class ProfileSessionDataResponseDefaultTypeInternal;
extern ProfileSessionDataResponseDefaultTypeInternal _ProfileSessionDataResponse_default_instance_;
class ProfileSessionInfo;
class ProfileSessionInfoDefaultTypeInternal;
extern ProfileSessionInfoDefaultTypeInternal _ProfileSessionInfo_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::EnumProfileSessionsAndToolsRequest* Arena::CreateMaybeMessage<::tensorflow::EnumProfileSessionsAndToolsRequest>(Arena*);
template<> ::tensorflow::EnumProfileSessionsAndToolsResponse* Arena::CreateMaybeMessage<::tensorflow::EnumProfileSessionsAndToolsResponse>(Arena*);
template<> ::tensorflow::NewProfileSessionRequest* Arena::CreateMaybeMessage<::tensorflow::NewProfileSessionRequest>(Arena*);
template<> ::tensorflow::NewProfileSessionResponse* Arena::CreateMaybeMessage<::tensorflow::NewProfileSessionResponse>(Arena*);
template<> ::tensorflow::ProfileSessionDataRequest* Arena::CreateMaybeMessage<::tensorflow::ProfileSessionDataRequest>(Arena*);
template<> ::tensorflow::ProfileSessionDataRequest_ParametersEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::ProfileSessionDataRequest_ParametersEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ProfileSessionDataResponse* Arena::CreateMaybeMessage<::tensorflow::ProfileSessionDataResponse>(Arena*);
template<> ::tensorflow::ProfileSessionInfo* Arena::CreateMaybeMessage<::tensorflow::ProfileSessionInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class NewProfileSessionRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NewProfileSessionRequest) */ {
 public:
  NewProfileSessionRequest();
  virtual ~NewProfileSessionRequest();

  NewProfileSessionRequest(const NewProfileSessionRequest& from);
  NewProfileSessionRequest(NewProfileSessionRequest&& from) noexcept
    : NewProfileSessionRequest() {
    *this = ::std::move(from);
  }

  inline NewProfileSessionRequest& operator=(const NewProfileSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline NewProfileSessionRequest& operator=(NewProfileSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NewProfileSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NewProfileSessionRequest* internal_default_instance() {
    return reinterpret_cast<const NewProfileSessionRequest*>(
               &_NewProfileSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(NewProfileSessionRequest& a, NewProfileSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(NewProfileSessionRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NewProfileSessionRequest* New() const final {
    return CreateMaybeMessage<NewProfileSessionRequest>(nullptr);
  }

  NewProfileSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NewProfileSessionRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NewProfileSessionRequest& from);
  void MergeFrom(const NewProfileSessionRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NewProfileSessionRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NewProfileSessionRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHostsFieldNumber = 3,
    kRepositoryRootFieldNumber = 2,
    kSessionIdFieldNumber = 4,
    kRequestFieldNumber = 1,
  };
  // repeated string hosts = 3;
  int hosts_size() const;
  void clear_hosts();
  const std::string& hosts(int index) const;
  std::string* mutable_hosts(int index);
  void set_hosts(int index, const std::string& value);
  void set_hosts(int index, std::string&& value);
  void set_hosts(int index, const char* value);
  void set_hosts(int index, const char* value, size_t size);
  std::string* add_hosts();
  void add_hosts(const std::string& value);
  void add_hosts(std::string&& value);
  void add_hosts(const char* value);
  void add_hosts(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& hosts() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_hosts();

  // string repository_root = 2;
  void clear_repository_root();
  const std::string& repository_root() const;
  void set_repository_root(const std::string& value);
  void set_repository_root(std::string&& value);
  void set_repository_root(const char* value);
  void set_repository_root(const char* value, size_t size);
  std::string* mutable_repository_root();
  std::string* release_repository_root();
  void set_allocated_repository_root(std::string* repository_root);

  // string session_id = 4;
  void clear_session_id();
  const std::string& session_id() const;
  void set_session_id(const std::string& value);
  void set_session_id(std::string&& value);
  void set_session_id(const char* value);
  void set_session_id(const char* value, size_t size);
  std::string* mutable_session_id();
  std::string* release_session_id();
  void set_allocated_session_id(std::string* session_id);

  // .tensorflow.ProfileRequest request = 1;
  bool has_request() const;
  void clear_request();
  const ::tensorflow::ProfileRequest& request() const;
  ::tensorflow::ProfileRequest* release_request();
  ::tensorflow::ProfileRequest* mutable_request();
  void set_allocated_request(::tensorflow::ProfileRequest* request);

  // @@protoc_insertion_point(class_scope:tensorflow.NewProfileSessionRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> hosts_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr repository_root_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_id_;
  ::tensorflow::ProfileRequest* request_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class NewProfileSessionResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NewProfileSessionResponse) */ {
 public:
  NewProfileSessionResponse();
  virtual ~NewProfileSessionResponse();

  NewProfileSessionResponse(const NewProfileSessionResponse& from);
  NewProfileSessionResponse(NewProfileSessionResponse&& from) noexcept
    : NewProfileSessionResponse() {
    *this = ::std::move(from);
  }

  inline NewProfileSessionResponse& operator=(const NewProfileSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline NewProfileSessionResponse& operator=(NewProfileSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NewProfileSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NewProfileSessionResponse* internal_default_instance() {
    return reinterpret_cast<const NewProfileSessionResponse*>(
               &_NewProfileSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(NewProfileSessionResponse& a, NewProfileSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(NewProfileSessionResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NewProfileSessionResponse* New() const final {
    return CreateMaybeMessage<NewProfileSessionResponse>(nullptr);
  }

  NewProfileSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NewProfileSessionResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NewProfileSessionResponse& from);
  void MergeFrom(const NewProfileSessionResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NewProfileSessionResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NewProfileSessionResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrorMessageFieldNumber = 1,
    kEmptyTraceFieldNumber = 2,
  };
  // string error_message = 1;
  void clear_error_message();
  const std::string& error_message() const;
  void set_error_message(const std::string& value);
  void set_error_message(std::string&& value);
  void set_error_message(const char* value);
  void set_error_message(const char* value, size_t size);
  std::string* mutable_error_message();
  std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);

  // bool empty_trace = 2;
  void clear_empty_trace();
  bool empty_trace() const;
  void set_empty_trace(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.NewProfileSessionResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
  bool empty_trace_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class EnumProfileSessionsAndToolsRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.EnumProfileSessionsAndToolsRequest) */ {
 public:
  EnumProfileSessionsAndToolsRequest();
  virtual ~EnumProfileSessionsAndToolsRequest();

  EnumProfileSessionsAndToolsRequest(const EnumProfileSessionsAndToolsRequest& from);
  EnumProfileSessionsAndToolsRequest(EnumProfileSessionsAndToolsRequest&& from) noexcept
    : EnumProfileSessionsAndToolsRequest() {
    *this = ::std::move(from);
  }

  inline EnumProfileSessionsAndToolsRequest& operator=(const EnumProfileSessionsAndToolsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnumProfileSessionsAndToolsRequest& operator=(EnumProfileSessionsAndToolsRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const EnumProfileSessionsAndToolsRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EnumProfileSessionsAndToolsRequest* internal_default_instance() {
    return reinterpret_cast<const EnumProfileSessionsAndToolsRequest*>(
               &_EnumProfileSessionsAndToolsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(EnumProfileSessionsAndToolsRequest& a, EnumProfileSessionsAndToolsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(EnumProfileSessionsAndToolsRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EnumProfileSessionsAndToolsRequest* New() const final {
    return CreateMaybeMessage<EnumProfileSessionsAndToolsRequest>(nullptr);
  }

  EnumProfileSessionsAndToolsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EnumProfileSessionsAndToolsRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const EnumProfileSessionsAndToolsRequest& from);
  void MergeFrom(const EnumProfileSessionsAndToolsRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnumProfileSessionsAndToolsRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.EnumProfileSessionsAndToolsRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepositoryRootFieldNumber = 1,
  };
  // string repository_root = 1;
  void clear_repository_root();
  const std::string& repository_root() const;
  void set_repository_root(const std::string& value);
  void set_repository_root(std::string&& value);
  void set_repository_root(const char* value);
  void set_repository_root(const char* value, size_t size);
  std::string* mutable_repository_root();
  std::string* release_repository_root();
  void set_allocated_repository_root(std::string* repository_root);

  // @@protoc_insertion_point(class_scope:tensorflow.EnumProfileSessionsAndToolsRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr repository_root_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class ProfileSessionInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileSessionInfo) */ {
 public:
  ProfileSessionInfo();
  virtual ~ProfileSessionInfo();

  ProfileSessionInfo(const ProfileSessionInfo& from);
  ProfileSessionInfo(ProfileSessionInfo&& from) noexcept
    : ProfileSessionInfo() {
    *this = ::std::move(from);
  }

  inline ProfileSessionInfo& operator=(const ProfileSessionInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileSessionInfo& operator=(ProfileSessionInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProfileSessionInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProfileSessionInfo* internal_default_instance() {
    return reinterpret_cast<const ProfileSessionInfo*>(
               &_ProfileSessionInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ProfileSessionInfo& a, ProfileSessionInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileSessionInfo* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProfileSessionInfo* New() const final {
    return CreateMaybeMessage<ProfileSessionInfo>(nullptr);
  }

  ProfileSessionInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProfileSessionInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProfileSessionInfo& from);
  void MergeFrom(const ProfileSessionInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileSessionInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileSessionInfo";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAvailableToolsFieldNumber = 2,
    kSessionIdFieldNumber = 1,
  };
  // repeated string available_tools = 2;
  int available_tools_size() const;
  void clear_available_tools();
  const std::string& available_tools(int index) const;
  std::string* mutable_available_tools(int index);
  void set_available_tools(int index, const std::string& value);
  void set_available_tools(int index, std::string&& value);
  void set_available_tools(int index, const char* value);
  void set_available_tools(int index, const char* value, size_t size);
  std::string* add_available_tools();
  void add_available_tools(const std::string& value);
  void add_available_tools(std::string&& value);
  void add_available_tools(const char* value);
  void add_available_tools(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& available_tools() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_available_tools();

  // string session_id = 1;
  void clear_session_id();
  const std::string& session_id() const;
  void set_session_id(const std::string& value);
  void set_session_id(std::string&& value);
  void set_session_id(const char* value);
  void set_session_id(const char* value, size_t size);
  std::string* mutable_session_id();
  std::string* release_session_id();
  void set_allocated_session_id(std::string* session_id);

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileSessionInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> available_tools_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class EnumProfileSessionsAndToolsResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.EnumProfileSessionsAndToolsResponse) */ {
 public:
  EnumProfileSessionsAndToolsResponse();
  virtual ~EnumProfileSessionsAndToolsResponse();

  EnumProfileSessionsAndToolsResponse(const EnumProfileSessionsAndToolsResponse& from);
  EnumProfileSessionsAndToolsResponse(EnumProfileSessionsAndToolsResponse&& from) noexcept
    : EnumProfileSessionsAndToolsResponse() {
    *this = ::std::move(from);
  }

  inline EnumProfileSessionsAndToolsResponse& operator=(const EnumProfileSessionsAndToolsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnumProfileSessionsAndToolsResponse& operator=(EnumProfileSessionsAndToolsResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const EnumProfileSessionsAndToolsResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EnumProfileSessionsAndToolsResponse* internal_default_instance() {
    return reinterpret_cast<const EnumProfileSessionsAndToolsResponse*>(
               &_EnumProfileSessionsAndToolsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(EnumProfileSessionsAndToolsResponse& a, EnumProfileSessionsAndToolsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(EnumProfileSessionsAndToolsResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EnumProfileSessionsAndToolsResponse* New() const final {
    return CreateMaybeMessage<EnumProfileSessionsAndToolsResponse>(nullptr);
  }

  EnumProfileSessionsAndToolsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EnumProfileSessionsAndToolsResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const EnumProfileSessionsAndToolsResponse& from);
  void MergeFrom(const EnumProfileSessionsAndToolsResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnumProfileSessionsAndToolsResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.EnumProfileSessionsAndToolsResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionsFieldNumber = 2,
    kErrorMessageFieldNumber = 1,
  };
  // repeated .tensorflow.ProfileSessionInfo sessions = 2;
  int sessions_size() const;
  void clear_sessions();
  ::tensorflow::ProfileSessionInfo* mutable_sessions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileSessionInfo >*
      mutable_sessions();
  const ::tensorflow::ProfileSessionInfo& sessions(int index) const;
  ::tensorflow::ProfileSessionInfo* add_sessions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileSessionInfo >&
      sessions() const;

  // string error_message = 1;
  void clear_error_message();
  const std::string& error_message() const;
  void set_error_message(const std::string& value);
  void set_error_message(std::string&& value);
  void set_error_message(const char* value);
  void set_error_message(const char* value, size_t size);
  std::string* mutable_error_message();
  std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);

  // @@protoc_insertion_point(class_scope:tensorflow.EnumProfileSessionsAndToolsResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileSessionInfo > sessions_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class ProfileSessionDataRequest_ParametersEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileSessionDataRequest_ParametersEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileSessionDataRequest_ParametersEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  ProfileSessionDataRequest_ParametersEntry_DoNotUse();
  ProfileSessionDataRequest_ParametersEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileSessionDataRequest_ParametersEntry_DoNotUse& other);
  static const ProfileSessionDataRequest_ParametersEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileSessionDataRequest_ParametersEntry_DoNotUse*>(&_ProfileSessionDataRequest_ParametersEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ProfileSessionDataRequest.ParametersEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ProfileSessionDataRequest.ParametersEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto.file_level_metadata[5];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileSessionDataRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileSessionDataRequest) */ {
 public:
  ProfileSessionDataRequest();
  virtual ~ProfileSessionDataRequest();

  ProfileSessionDataRequest(const ProfileSessionDataRequest& from);
  ProfileSessionDataRequest(ProfileSessionDataRequest&& from) noexcept
    : ProfileSessionDataRequest() {
    *this = ::std::move(from);
  }

  inline ProfileSessionDataRequest& operator=(const ProfileSessionDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileSessionDataRequest& operator=(ProfileSessionDataRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProfileSessionDataRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProfileSessionDataRequest* internal_default_instance() {
    return reinterpret_cast<const ProfileSessionDataRequest*>(
               &_ProfileSessionDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(ProfileSessionDataRequest& a, ProfileSessionDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileSessionDataRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProfileSessionDataRequest* New() const final {
    return CreateMaybeMessage<ProfileSessionDataRequest>(nullptr);
  }

  ProfileSessionDataRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProfileSessionDataRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProfileSessionDataRequest& from);
  void MergeFrom(const ProfileSessionDataRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileSessionDataRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileSessionDataRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kParametersFieldNumber = 4,
    kRepositoryRootFieldNumber = 1,
    kSessionIdFieldNumber = 2,
    kToolNameFieldNumber = 3,
    kHostNameFieldNumber = 5,
  };
  // map<string, string> parameters = 4;
  int parameters_size() const;
  void clear_parameters();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      parameters() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_parameters();

  // string repository_root = 1;
  void clear_repository_root();
  const std::string& repository_root() const;
  void set_repository_root(const std::string& value);
  void set_repository_root(std::string&& value);
  void set_repository_root(const char* value);
  void set_repository_root(const char* value, size_t size);
  std::string* mutable_repository_root();
  std::string* release_repository_root();
  void set_allocated_repository_root(std::string* repository_root);

  // string session_id = 2;
  void clear_session_id();
  const std::string& session_id() const;
  void set_session_id(const std::string& value);
  void set_session_id(std::string&& value);
  void set_session_id(const char* value);
  void set_session_id(const char* value, size_t size);
  std::string* mutable_session_id();
  std::string* release_session_id();
  void set_allocated_session_id(std::string* session_id);

  // string tool_name = 3;
  void clear_tool_name();
  const std::string& tool_name() const;
  void set_tool_name(const std::string& value);
  void set_tool_name(std::string&& value);
  void set_tool_name(const char* value);
  void set_tool_name(const char* value, size_t size);
  std::string* mutable_tool_name();
  std::string* release_tool_name();
  void set_allocated_tool_name(std::string* tool_name);

  // string host_name = 5;
  void clear_host_name();
  const std::string& host_name() const;
  void set_host_name(const std::string& value);
  void set_host_name(std::string&& value);
  void set_host_name(const char* value);
  void set_host_name(const char* value, size_t size);
  std::string* mutable_host_name();
  std::string* release_host_name();
  void set_allocated_host_name(std::string* host_name);

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileSessionDataRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileSessionDataRequest_ParametersEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > parameters_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr repository_root_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tool_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class ProfileSessionDataResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileSessionDataResponse) */ {
 public:
  ProfileSessionDataResponse();
  virtual ~ProfileSessionDataResponse();

  ProfileSessionDataResponse(const ProfileSessionDataResponse& from);
  ProfileSessionDataResponse(ProfileSessionDataResponse&& from) noexcept
    : ProfileSessionDataResponse() {
    *this = ::std::move(from);
  }

  inline ProfileSessionDataResponse& operator=(const ProfileSessionDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileSessionDataResponse& operator=(ProfileSessionDataResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProfileSessionDataResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProfileSessionDataResponse* internal_default_instance() {
    return reinterpret_cast<const ProfileSessionDataResponse*>(
               &_ProfileSessionDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(ProfileSessionDataResponse& a, ProfileSessionDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileSessionDataResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProfileSessionDataResponse* New() const final {
    return CreateMaybeMessage<ProfileSessionDataResponse>(nullptr);
  }

  ProfileSessionDataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProfileSessionDataResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProfileSessionDataResponse& from);
  void MergeFrom(const ProfileSessionDataResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileSessionDataResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileSessionDataResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrorMessageFieldNumber = 1,
    kOutputFormatFieldNumber = 2,
    kOutputFieldNumber = 3,
  };
  // string error_message = 1;
  void clear_error_message();
  const std::string& error_message() const;
  void set_error_message(const std::string& value);
  void set_error_message(std::string&& value);
  void set_error_message(const char* value);
  void set_error_message(const char* value, size_t size);
  std::string* mutable_error_message();
  std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);

  // string output_format = 2;
  void clear_output_format();
  const std::string& output_format() const;
  void set_output_format(const std::string& value);
  void set_output_format(std::string&& value);
  void set_output_format(const char* value);
  void set_output_format(const char* value, size_t size);
  std::string* mutable_output_format();
  std::string* release_output_format();
  void set_allocated_output_format(std::string* output_format);

  // bytes output = 3;
  void clear_output();
  const std::string& output() const;
  void set_output(const std::string& value);
  void set_output(std::string&& value);
  void set_output(const char* value);
  void set_output(const void* value, size_t size);
  std::string* mutable_output();
  std::string* release_output();
  void set_allocated_output(std::string* output);

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileSessionDataResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr output_format_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr output_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// NewProfileSessionRequest

// .tensorflow.ProfileRequest request = 1;
inline bool NewProfileSessionRequest::has_request() const {
  return this != internal_default_instance() && request_ != nullptr;
}
inline const ::tensorflow::ProfileRequest& NewProfileSessionRequest::request() const {
  const ::tensorflow::ProfileRequest* p = request_;
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionRequest.request)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ProfileRequest*>(
      &::tensorflow::_ProfileRequest_default_instance_);
}
inline ::tensorflow::ProfileRequest* NewProfileSessionRequest::release_request() {
  // @@protoc_insertion_point(field_release:tensorflow.NewProfileSessionRequest.request)
  
  ::tensorflow::ProfileRequest* temp = request_;
  request_ = nullptr;
  return temp;
}
inline ::tensorflow::ProfileRequest* NewProfileSessionRequest::mutable_request() {
  
  if (request_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ProfileRequest>(GetArenaNoVirtual());
    request_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.NewProfileSessionRequest.request)
  return request_;
}
inline void NewProfileSessionRequest::set_allocated_request(::tensorflow::ProfileRequest* request) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(request_);
  }
  if (request) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      request = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, request, submessage_arena);
    }
    
  } else {
    
  }
  request_ = request;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewProfileSessionRequest.request)
}

// string repository_root = 2;
inline void NewProfileSessionRequest::clear_repository_root() {
  repository_root_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& NewProfileSessionRequest::repository_root() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionRequest.repository_root)
  return repository_root_.GetNoArena();
}
inline void NewProfileSessionRequest::set_repository_root(const std::string& value) {
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionRequest.repository_root)
}
inline void NewProfileSessionRequest::set_repository_root(std::string&& value) {
  
  repository_root_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NewProfileSessionRequest.repository_root)
}
inline void NewProfileSessionRequest::set_repository_root(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.NewProfileSessionRequest.repository_root)
}
inline void NewProfileSessionRequest::set_repository_root(const char* value, size_t size) {
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NewProfileSessionRequest.repository_root)
}
inline std::string* NewProfileSessionRequest::mutable_repository_root() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NewProfileSessionRequest.repository_root)
  return repository_root_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* NewProfileSessionRequest::release_repository_root() {
  // @@protoc_insertion_point(field_release:tensorflow.NewProfileSessionRequest.repository_root)
  
  return repository_root_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void NewProfileSessionRequest::set_allocated_repository_root(std::string* repository_root) {
  if (repository_root != nullptr) {
    
  } else {
    
  }
  repository_root_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), repository_root);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewProfileSessionRequest.repository_root)
}

// repeated string hosts = 3;
inline int NewProfileSessionRequest::hosts_size() const {
  return hosts_.size();
}
inline void NewProfileSessionRequest::clear_hosts() {
  hosts_.Clear();
}
inline const std::string& NewProfileSessionRequest::hosts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionRequest.hosts)
  return hosts_.Get(index);
}
inline std::string* NewProfileSessionRequest::mutable_hosts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NewProfileSessionRequest.hosts)
  return hosts_.Mutable(index);
}
inline void NewProfileSessionRequest::set_hosts(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionRequest.hosts)
  hosts_.Mutable(index)->assign(value);
}
inline void NewProfileSessionRequest::set_hosts(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionRequest.hosts)
  hosts_.Mutable(index)->assign(std::move(value));
}
inline void NewProfileSessionRequest::set_hosts(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  hosts_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.NewProfileSessionRequest.hosts)
}
inline void NewProfileSessionRequest::set_hosts(int index, const char* value, size_t size) {
  hosts_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NewProfileSessionRequest.hosts)
}
inline std::string* NewProfileSessionRequest::add_hosts() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.NewProfileSessionRequest.hosts)
  return hosts_.Add();
}
inline void NewProfileSessionRequest::add_hosts(const std::string& value) {
  hosts_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.NewProfileSessionRequest.hosts)
}
inline void NewProfileSessionRequest::add_hosts(std::string&& value) {
  hosts_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.NewProfileSessionRequest.hosts)
}
inline void NewProfileSessionRequest::add_hosts(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  hosts_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.NewProfileSessionRequest.hosts)
}
inline void NewProfileSessionRequest::add_hosts(const char* value, size_t size) {
  hosts_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.NewProfileSessionRequest.hosts)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
NewProfileSessionRequest::hosts() const {
  // @@protoc_insertion_point(field_list:tensorflow.NewProfileSessionRequest.hosts)
  return hosts_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
NewProfileSessionRequest::mutable_hosts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NewProfileSessionRequest.hosts)
  return &hosts_;
}

// string session_id = 4;
inline void NewProfileSessionRequest::clear_session_id() {
  session_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& NewProfileSessionRequest::session_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionRequest.session_id)
  return session_id_.GetNoArena();
}
inline void NewProfileSessionRequest::set_session_id(const std::string& value) {
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionRequest.session_id)
}
inline void NewProfileSessionRequest::set_session_id(std::string&& value) {
  
  session_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NewProfileSessionRequest.session_id)
}
inline void NewProfileSessionRequest::set_session_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.NewProfileSessionRequest.session_id)
}
inline void NewProfileSessionRequest::set_session_id(const char* value, size_t size) {
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NewProfileSessionRequest.session_id)
}
inline std::string* NewProfileSessionRequest::mutable_session_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NewProfileSessionRequest.session_id)
  return session_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* NewProfileSessionRequest::release_session_id() {
  // @@protoc_insertion_point(field_release:tensorflow.NewProfileSessionRequest.session_id)
  
  return session_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void NewProfileSessionRequest::set_allocated_session_id(std::string* session_id) {
  if (session_id != nullptr) {
    
  } else {
    
  }
  session_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_id);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewProfileSessionRequest.session_id)
}

// -------------------------------------------------------------------

// NewProfileSessionResponse

// string error_message = 1;
inline void NewProfileSessionResponse::clear_error_message() {
  error_message_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& NewProfileSessionResponse::error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionResponse.error_message)
  return error_message_.GetNoArena();
}
inline void NewProfileSessionResponse::set_error_message(const std::string& value) {
  
  error_message_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionResponse.error_message)
}
inline void NewProfileSessionResponse::set_error_message(std::string&& value) {
  
  error_message_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NewProfileSessionResponse.error_message)
}
inline void NewProfileSessionResponse::set_error_message(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  error_message_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.NewProfileSessionResponse.error_message)
}
inline void NewProfileSessionResponse::set_error_message(const char* value, size_t size) {
  
  error_message_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NewProfileSessionResponse.error_message)
}
inline std::string* NewProfileSessionResponse::mutable_error_message() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NewProfileSessionResponse.error_message)
  return error_message_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* NewProfileSessionResponse::release_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.NewProfileSessionResponse.error_message)
  
  return error_message_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void NewProfileSessionResponse::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  error_message_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), error_message);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewProfileSessionResponse.error_message)
}

// bool empty_trace = 2;
inline void NewProfileSessionResponse::clear_empty_trace() {
  empty_trace_ = false;
}
inline bool NewProfileSessionResponse::empty_trace() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionResponse.empty_trace)
  return empty_trace_;
}
inline void NewProfileSessionResponse::set_empty_trace(bool value) {
  
  empty_trace_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionResponse.empty_trace)
}

// -------------------------------------------------------------------

// EnumProfileSessionsAndToolsRequest

// string repository_root = 1;
inline void EnumProfileSessionsAndToolsRequest::clear_repository_root() {
  repository_root_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& EnumProfileSessionsAndToolsRequest::repository_root() const {
  // @@protoc_insertion_point(field_get:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
  return repository_root_.GetNoArena();
}
inline void EnumProfileSessionsAndToolsRequest::set_repository_root(const std::string& value) {
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
}
inline void EnumProfileSessionsAndToolsRequest::set_repository_root(std::string&& value) {
  
  repository_root_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
}
inline void EnumProfileSessionsAndToolsRequest::set_repository_root(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
}
inline void EnumProfileSessionsAndToolsRequest::set_repository_root(const char* value, size_t size) {
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
}
inline std::string* EnumProfileSessionsAndToolsRequest::mutable_repository_root() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
  return repository_root_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* EnumProfileSessionsAndToolsRequest::release_repository_root() {
  // @@protoc_insertion_point(field_release:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
  
  return repository_root_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void EnumProfileSessionsAndToolsRequest::set_allocated_repository_root(std::string* repository_root) {
  if (repository_root != nullptr) {
    
  } else {
    
  }
  repository_root_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), repository_root);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
}

// -------------------------------------------------------------------

// ProfileSessionInfo

// string session_id = 1;
inline void ProfileSessionInfo::clear_session_id() {
  session_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileSessionInfo::session_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionInfo.session_id)
  return session_id_.GetNoArena();
}
inline void ProfileSessionInfo::set_session_id(const std::string& value) {
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionInfo.session_id)
}
inline void ProfileSessionInfo::set_session_id(std::string&& value) {
  
  session_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileSessionInfo.session_id)
}
inline void ProfileSessionInfo::set_session_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileSessionInfo.session_id)
}
inline void ProfileSessionInfo::set_session_id(const char* value, size_t size) {
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileSessionInfo.session_id)
}
inline std::string* ProfileSessionInfo::mutable_session_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionInfo.session_id)
  return session_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileSessionInfo::release_session_id() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionInfo.session_id)
  
  return session_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileSessionInfo::set_allocated_session_id(std::string* session_id) {
  if (session_id != nullptr) {
    
  } else {
    
  }
  session_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_id);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionInfo.session_id)
}

// repeated string available_tools = 2;
inline int ProfileSessionInfo::available_tools_size() const {
  return available_tools_.size();
}
inline void ProfileSessionInfo::clear_available_tools() {
  available_tools_.Clear();
}
inline const std::string& ProfileSessionInfo::available_tools(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionInfo.available_tools)
  return available_tools_.Get(index);
}
inline std::string* ProfileSessionInfo::mutable_available_tools(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionInfo.available_tools)
  return available_tools_.Mutable(index);
}
inline void ProfileSessionInfo::set_available_tools(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionInfo.available_tools)
  available_tools_.Mutable(index)->assign(value);
}
inline void ProfileSessionInfo::set_available_tools(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionInfo.available_tools)
  available_tools_.Mutable(index)->assign(std::move(value));
}
inline void ProfileSessionInfo::set_available_tools(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  available_tools_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileSessionInfo.available_tools)
}
inline void ProfileSessionInfo::set_available_tools(int index, const char* value, size_t size) {
  available_tools_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileSessionInfo.available_tools)
}
inline std::string* ProfileSessionInfo::add_available_tools() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ProfileSessionInfo.available_tools)
  return available_tools_.Add();
}
inline void ProfileSessionInfo::add_available_tools(const std::string& value) {
  available_tools_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ProfileSessionInfo.available_tools)
}
inline void ProfileSessionInfo::add_available_tools(std::string&& value) {
  available_tools_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ProfileSessionInfo.available_tools)
}
inline void ProfileSessionInfo::add_available_tools(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  available_tools_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ProfileSessionInfo.available_tools)
}
inline void ProfileSessionInfo::add_available_tools(const char* value, size_t size) {
  available_tools_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ProfileSessionInfo.available_tools)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ProfileSessionInfo::available_tools() const {
  // @@protoc_insertion_point(field_list:tensorflow.ProfileSessionInfo.available_tools)
  return available_tools_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ProfileSessionInfo::mutable_available_tools() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ProfileSessionInfo.available_tools)
  return &available_tools_;
}

// -------------------------------------------------------------------

// EnumProfileSessionsAndToolsResponse

// string error_message = 1;
inline void EnumProfileSessionsAndToolsResponse::clear_error_message() {
  error_message_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& EnumProfileSessionsAndToolsResponse::error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
  return error_message_.GetNoArena();
}
inline void EnumProfileSessionsAndToolsResponse::set_error_message(const std::string& value) {
  
  error_message_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
}
inline void EnumProfileSessionsAndToolsResponse::set_error_message(std::string&& value) {
  
  error_message_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
}
inline void EnumProfileSessionsAndToolsResponse::set_error_message(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  error_message_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
}
inline void EnumProfileSessionsAndToolsResponse::set_error_message(const char* value, size_t size) {
  
  error_message_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
}
inline std::string* EnumProfileSessionsAndToolsResponse::mutable_error_message() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
  return error_message_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* EnumProfileSessionsAndToolsResponse::release_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
  
  return error_message_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void EnumProfileSessionsAndToolsResponse::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  error_message_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), error_message);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
}

// repeated .tensorflow.ProfileSessionInfo sessions = 2;
inline int EnumProfileSessionsAndToolsResponse::sessions_size() const {
  return sessions_.size();
}
inline void EnumProfileSessionsAndToolsResponse::clear_sessions() {
  sessions_.Clear();
}
inline ::tensorflow::ProfileSessionInfo* EnumProfileSessionsAndToolsResponse::mutable_sessions(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.EnumProfileSessionsAndToolsResponse.sessions)
  return sessions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileSessionInfo >*
EnumProfileSessionsAndToolsResponse::mutable_sessions() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.EnumProfileSessionsAndToolsResponse.sessions)
  return &sessions_;
}
inline const ::tensorflow::ProfileSessionInfo& EnumProfileSessionsAndToolsResponse::sessions(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.EnumProfileSessionsAndToolsResponse.sessions)
  return sessions_.Get(index);
}
inline ::tensorflow::ProfileSessionInfo* EnumProfileSessionsAndToolsResponse::add_sessions() {
  // @@protoc_insertion_point(field_add:tensorflow.EnumProfileSessionsAndToolsResponse.sessions)
  return sessions_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileSessionInfo >&
EnumProfileSessionsAndToolsResponse::sessions() const {
  // @@protoc_insertion_point(field_list:tensorflow.EnumProfileSessionsAndToolsResponse.sessions)
  return sessions_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProfileSessionDataRequest

// string repository_root = 1;
inline void ProfileSessionDataRequest::clear_repository_root() {
  repository_root_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileSessionDataRequest::repository_root() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataRequest.repository_root)
  return repository_root_.GetNoArena();
}
inline void ProfileSessionDataRequest::set_repository_root(const std::string& value) {
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataRequest.repository_root)
}
inline void ProfileSessionDataRequest::set_repository_root(std::string&& value) {
  
  repository_root_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileSessionDataRequest.repository_root)
}
inline void ProfileSessionDataRequest::set_repository_root(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileSessionDataRequest.repository_root)
}
inline void ProfileSessionDataRequest::set_repository_root(const char* value, size_t size) {
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileSessionDataRequest.repository_root)
}
inline std::string* ProfileSessionDataRequest::mutable_repository_root() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataRequest.repository_root)
  return repository_root_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileSessionDataRequest::release_repository_root() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataRequest.repository_root)
  
  return repository_root_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileSessionDataRequest::set_allocated_repository_root(std::string* repository_root) {
  if (repository_root != nullptr) {
    
  } else {
    
  }
  repository_root_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), repository_root);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataRequest.repository_root)
}

// string session_id = 2;
inline void ProfileSessionDataRequest::clear_session_id() {
  session_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileSessionDataRequest::session_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataRequest.session_id)
  return session_id_.GetNoArena();
}
inline void ProfileSessionDataRequest::set_session_id(const std::string& value) {
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataRequest.session_id)
}
inline void ProfileSessionDataRequest::set_session_id(std::string&& value) {
  
  session_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileSessionDataRequest.session_id)
}
inline void ProfileSessionDataRequest::set_session_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileSessionDataRequest.session_id)
}
inline void ProfileSessionDataRequest::set_session_id(const char* value, size_t size) {
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileSessionDataRequest.session_id)
}
inline std::string* ProfileSessionDataRequest::mutable_session_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataRequest.session_id)
  return session_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileSessionDataRequest::release_session_id() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataRequest.session_id)
  
  return session_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileSessionDataRequest::set_allocated_session_id(std::string* session_id) {
  if (session_id != nullptr) {
    
  } else {
    
  }
  session_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_id);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataRequest.session_id)
}

// string host_name = 5;
inline void ProfileSessionDataRequest::clear_host_name() {
  host_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileSessionDataRequest::host_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataRequest.host_name)
  return host_name_.GetNoArena();
}
inline void ProfileSessionDataRequest::set_host_name(const std::string& value) {
  
  host_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataRequest.host_name)
}
inline void ProfileSessionDataRequest::set_host_name(std::string&& value) {
  
  host_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileSessionDataRequest.host_name)
}
inline void ProfileSessionDataRequest::set_host_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  host_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileSessionDataRequest.host_name)
}
inline void ProfileSessionDataRequest::set_host_name(const char* value, size_t size) {
  
  host_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileSessionDataRequest.host_name)
}
inline std::string* ProfileSessionDataRequest::mutable_host_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataRequest.host_name)
  return host_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileSessionDataRequest::release_host_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataRequest.host_name)
  
  return host_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileSessionDataRequest::set_allocated_host_name(std::string* host_name) {
  if (host_name != nullptr) {
    
  } else {
    
  }
  host_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), host_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataRequest.host_name)
}

// string tool_name = 3;
inline void ProfileSessionDataRequest::clear_tool_name() {
  tool_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileSessionDataRequest::tool_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataRequest.tool_name)
  return tool_name_.GetNoArena();
}
inline void ProfileSessionDataRequest::set_tool_name(const std::string& value) {
  
  tool_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataRequest.tool_name)
}
inline void ProfileSessionDataRequest::set_tool_name(std::string&& value) {
  
  tool_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileSessionDataRequest.tool_name)
}
inline void ProfileSessionDataRequest::set_tool_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  tool_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileSessionDataRequest.tool_name)
}
inline void ProfileSessionDataRequest::set_tool_name(const char* value, size_t size) {
  
  tool_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileSessionDataRequest.tool_name)
}
inline std::string* ProfileSessionDataRequest::mutable_tool_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataRequest.tool_name)
  return tool_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileSessionDataRequest::release_tool_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataRequest.tool_name)
  
  return tool_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileSessionDataRequest::set_allocated_tool_name(std::string* tool_name) {
  if (tool_name != nullptr) {
    
  } else {
    
  }
  tool_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tool_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataRequest.tool_name)
}

// map<string, string> parameters = 4;
inline int ProfileSessionDataRequest::parameters_size() const {
  return parameters_.size();
}
inline void ProfileSessionDataRequest::clear_parameters() {
  parameters_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
ProfileSessionDataRequest::parameters() const {
  // @@protoc_insertion_point(field_map:tensorflow.ProfileSessionDataRequest.parameters)
  return parameters_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
ProfileSessionDataRequest::mutable_parameters() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.ProfileSessionDataRequest.parameters)
  return parameters_.MutableMap();
}

// -------------------------------------------------------------------

// ProfileSessionDataResponse

// string error_message = 1;
inline void ProfileSessionDataResponse::clear_error_message() {
  error_message_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileSessionDataResponse::error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataResponse.error_message)
  return error_message_.GetNoArena();
}
inline void ProfileSessionDataResponse::set_error_message(const std::string& value) {
  
  error_message_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataResponse.error_message)
}
inline void ProfileSessionDataResponse::set_error_message(std::string&& value) {
  
  error_message_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileSessionDataResponse.error_message)
}
inline void ProfileSessionDataResponse::set_error_message(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  error_message_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileSessionDataResponse.error_message)
}
inline void ProfileSessionDataResponse::set_error_message(const char* value, size_t size) {
  
  error_message_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileSessionDataResponse.error_message)
}
inline std::string* ProfileSessionDataResponse::mutable_error_message() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataResponse.error_message)
  return error_message_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileSessionDataResponse::release_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataResponse.error_message)
  
  return error_message_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileSessionDataResponse::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  error_message_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), error_message);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataResponse.error_message)
}

// string output_format = 2;
inline void ProfileSessionDataResponse::clear_output_format() {
  output_format_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileSessionDataResponse::output_format() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataResponse.output_format)
  return output_format_.GetNoArena();
}
inline void ProfileSessionDataResponse::set_output_format(const std::string& value) {
  
  output_format_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataResponse.output_format)
}
inline void ProfileSessionDataResponse::set_output_format(std::string&& value) {
  
  output_format_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileSessionDataResponse.output_format)
}
inline void ProfileSessionDataResponse::set_output_format(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  output_format_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileSessionDataResponse.output_format)
}
inline void ProfileSessionDataResponse::set_output_format(const char* value, size_t size) {
  
  output_format_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileSessionDataResponse.output_format)
}
inline std::string* ProfileSessionDataResponse::mutable_output_format() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataResponse.output_format)
  return output_format_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileSessionDataResponse::release_output_format() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataResponse.output_format)
  
  return output_format_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileSessionDataResponse::set_allocated_output_format(std::string* output_format) {
  if (output_format != nullptr) {
    
  } else {
    
  }
  output_format_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), output_format);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataResponse.output_format)
}

// bytes output = 3;
inline void ProfileSessionDataResponse::clear_output() {
  output_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileSessionDataResponse::output() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataResponse.output)
  return output_.GetNoArena();
}
inline void ProfileSessionDataResponse::set_output(const std::string& value) {
  
  output_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataResponse.output)
}
inline void ProfileSessionDataResponse::set_output(std::string&& value) {
  
  output_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileSessionDataResponse.output)
}
inline void ProfileSessionDataResponse::set_output(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  output_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileSessionDataResponse.output)
}
inline void ProfileSessionDataResponse::set_output(const void* value, size_t size) {
  
  output_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileSessionDataResponse.output)
}
inline std::string* ProfileSessionDataResponse::mutable_output() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataResponse.output)
  return output_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileSessionDataResponse::release_output() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataResponse.output)
  
  return output_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileSessionDataResponse::set_allocated_output(std::string* output) {
  if (output != nullptr) {
    
  } else {
    
  }
  output_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), output);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataResponse.output)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto
