import random

from apps.models.analysis_models import ArrhythmiaDiagnosisEntity
from apps.models.ecg_analysis_modes import T<PERSON><PERSON><PERSON><PERSON>


def process(union_id):
    """
    白名单处理
    :param union_id: 用户ID
    :return: 诊断结果，心脏年龄
    """
    arrhythmia_diagnosis_entity = None
    ecg_age = None
    range_value = 2

    # 查询是否在白名单
    whitelist = TWhitelist.objects.filter(union_id=union_id).first()

    if whitelist:
        final_labels = whitelist.arrhythmia_diagnosis
        ecg_age = whitelist.ecg_age

        # 分析结果处理
        if final_labels:
            arrhythmia_diagnosis_entity = ArrhythmiaDiagnosisEntity()

            for final_label in final_labels.split(','):
                setattr(arrhythmia_diagnosis_entity, final_label, 1)

        if ecg_age:
            ecg_age = random.randint(ecg_age - range_value, ecg_age + range_value)

    return arrhythmia_diagnosis_entity, ecg_age
