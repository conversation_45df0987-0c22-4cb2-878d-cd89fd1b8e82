Metadata-Version: 2.1
Name: nolds
Version: 0.6.1
Summary: Nonlinear measures for dynamical systems (based on one-dimensional time series)
Home-page: https://github.com/CSchoel/nolds
Download-URL: https://github.com/CSchoel/nolds/tarball/0.6.1
Author: <PERSON>
Author-email: christopher.scho<PERSON><PERSON>@gmx.net
License: MIT
Keywords: nonlinear,dynamical system,chaos,lyapunov,hurst,hurst exponent,rescaled range,DFA,detrended fluctuation analysis,sample entropy,correlation dimension
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Topic :: Scientific/Engineering :: Bio-Informatics
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
License-File: LICENSE.txt
Requires-Dist: numpy <3.0,>1.0
Requires-Dist: future
Requires-Dist: setuptools
Provides-Extra: ransac
Requires-Dist: scikit-learn >=0.19 ; extra == 'ransac'
Provides-Extra: plots
Requires-Dist: matplotlib ; extra == 'plots'
Provides-Extra: qrandom
Requires-Dist: quantumrandom ; extra == 'qrandom'

NOnLinear measures for Dynamical Systems (nolds)
================================================

.. image:: https://github.com/CSchoel/nolds/actions/workflows/ci.yaml/badge.svg
    :target: https://github.com/CSchoel/nolds/actions/workflows/ci.yaml

.. image:: https://zenodo.org/badge/DOI/10.5281/zenodo.3814723.svg
   :target: https://doi.org/10.5281/zenodo.3814723

.. image:: https://api.codacy.com/project/badge/Grade/42c30d253b384e87a86b70c8fa0da6b2
    :target: https://www.codacy.com/app/christopher.schoelzel/nolds?utm_source=github.com&amp;utm_medium=referral&amp;utm_content=CSchoel/nolds&amp;utm_campaign=Badge_Grade

.. image:: https://codecov.io/gh/CSchoel/nolds/graph/badge.svg?token=Xgr82QKhFi 
    :target: https://codecov.io/gh/CSchoel/nolds


Nolds is a small numpy-based library that provides an implementation and a learning resource for nonlinear measures for dynamical systems based on one-dimensional time series.

Currently the following measures are implemented:

**sample entropy** (``sampen``)
    Measures the complexity of a time-series, based on approximate entropy
**correlation dimension** (``corr_dim``)
    A measure of the *fractal dimension* of a time series which is also related to complexity.
**Lyapunov exponent** (``lyap_r``, ``lyap_e``)
    Positive Lyapunov exponents indicate chaos and unpredictability.
    Nolds provides the algorithm of Rosenstein et al. (``lyap_r``) to estimate the largest Lyapunov exponent and the algorithm of Eckmann et al. (``lyap_e``) to estimate the whole spectrum of Lyapunov exponents.
**Hurst exponent** (``hurst_rs``)
	The Hurst exponent is a measure of the "long-term memory" of a time series.
	It can be used to determine whether the time series is more, less, or equally likely to increase if it has increased in previous steps.
	This property makes the Hurst exponent especially interesting for the analysis of stock data.
**detrended fluctuation analysis (DFA)** (``dfa``)
	DFA measures the Hurst parameter *H*, which is very similar to the Hurst exponent.
	The main difference is that DFA can be used for non-stationary processes (whose mean and/or variance change over time).
**Generalized Hurst Exponent** (``mfhurst_b``)
    The Generalized Hurst Exponent (GHE, H_q or H(q)) can (as the name implies) be seen as a generalization of the Hurst exponent for data series with multifractal properties.
    It's origins are however not directly related to Hurst's rescaled range approach, but to the definition of self-affine functions.

    .. warning::
        Nolds also supports the GHE implementation by Di Matteo and Aste (2003) as ``mfhurst_dm``.
        However, it is not recommended to use this for anything else than reproducing the results of the authors.
        Instead, use ``mfhurst_b``, which is more stable and mathematically sound.


Example
-------

::

	import nolds
	import numpy as np

	rwalk = np.cumsum(np.random.random(1000))
	h = nolds.dfa(rwalk)

Requirements
------------
Nolds supports Python 2 (>= 2.7) and 3 (>= 3.4) from one code source. It requires the package numpy_.

These are the only hard requirements, but some functions will need other packages:

* If you want to use the RANSAC algorithm for line fitting, you will also need the package sklearn_.
* For the true random numbers generated by ``nolds.qrandom`` you need the package quantumrandom_.
* The plotting functions in ``nolds.examples`` require the package matplotlib_.

.. _numpy: http://numpy.scipy.org/
.. _sklearn: http://scikit-learn.org/stable/
.. _quantumrandom: https://pypi.python.org/pypi/quantumrandom/1.9.0
.. _matplotlib: https://matplotlib.org/

Installation
------------
Nolds is available through PyPI and can be installed using pip:

``pip install nolds``

You can test your installation by running some sample code with:

``python -m nolds.examples lyapunov-logistic``

Alternatively, if you do not have matplotlib_ installed, you can run the unittests with:

``python -m unittest nolds.test_measures``

Documentation
-------------

Nolds is designed as a learning resource for the measures mentioned above.
Therefore the corresponding functions feature extensive documentation that not only explains the interface but also the algorithm used and points the user to additional reference code and papers.
The documentation can be found in the code, but it is also available as `HTML-Version <https://cschoel.github.io/nolds/>`_ and on `Read the Docs <http://nolds.readthedocs.io/>`_.

The relevant measures can be found in the file ``nolds/measures.py``.


How to cite nolds
-----------------

I am planning to publish nolds in a peer-reviewed journal, but as this is a side project of my PhD thesis this may take a while.
For the time being you can use the following `Zenodo reference`_:

    Schölzel, Christopher. (2019, June 16). Nonlinear measures for dynamical systems (Version 0.5.2). Zenodo. http://doi.org/10.5281/zenodo.3814723

You can also `export a BibTeX reference`_ from Zenodo.

.. _Zenodo reference: https://zenodo.org/record/3814723
.. _export a BibTeX reference: https://zenodo.org/record/3814723/export/hx


Contact information
-------------------

If you have any questions, suggestions or corrections, you can find my contact
information in `my GitHub profile`_ or on `my blog`_.

Please note that this is one of my side projects.
Depending on my current workload it may take days, weeks or even months until
I can find the time to check on mails and issues for nolds.

.. _my GitHub profile: https://github.com/CSchoel
.. _my blog: http://arbitrary-but-fixed.net/
