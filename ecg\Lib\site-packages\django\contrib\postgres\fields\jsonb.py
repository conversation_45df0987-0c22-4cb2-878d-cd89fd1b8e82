import warnings

from django.db.models import <PERSON><PERSON><PERSON><PERSON> as BuiltinJ<PERSON><PERSON><PERSON>
from django.db.models.fields.json import (
    KeyTextTransform as BuiltinKeyTextTransform,
    KeyTransform as BuiltinKeyTransform,
)
from django.utils.deprecation import RemovedInDjango40Warning

__all__ = ['JSO<PERSON>ield']


class JSONField(BuiltinJSONField):
    system_check_deprecated_details = {
        'msg': (
            'django.contrib.postgres.fields.JSONField is deprecated. Support '
            'for it (except in historical migrations) will be removed in '
            'Django 4.0.'
        ),
        'hint': 'Use django.db.models.JSONField instead.',
        'id': 'fields.W904',
    }


class KeyTransform(BuiltinKeyTransform):
    def __init__(self, *args, **kwargs):
        warnings.warn(
            'django.contrib.postgres.fields.jsonb.KeyTransform is deprecated '
            'in favor of django.db.models.fields.json.KeyTransform.',
            RemovedInDjango40Warning, stacklevel=2,
        )
        super().__init__(*args, **kwargs)


class KeyTextTransform(BuiltinKeyTextTransform):
    def __init__(self, *args, **kwargs):
        warnings.warn(
            'django.contrib.postgres.fields.jsonb.KeyTextTransform is '
            'deprecated in favor of '
            'django.db.models.fields.json.KeyTextTransform.',
            RemovedInDjango40Warning, stacklevel=2,
        )
        super().__init__(*args, **kwargs)
