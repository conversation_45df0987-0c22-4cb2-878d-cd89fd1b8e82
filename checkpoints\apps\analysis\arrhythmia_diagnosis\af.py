import traceback
from apps.utils.logger_helper import Logger


def process(waveform_info):
    try:
        waveform = waveform_info['waveform']
        rr_intervals = waveform['rr_intervals']
        r_peaks = waveform['r_peaks']
        p_end_positions = waveform['p_end_positions']

        rr_mean = waveform['rr_mean']
        rr_std = waveform['rr_std']
        rr_cv = waveform['rr_cv']
        rr_iqr = waveform['rr_iqr']

        if len(rr_intervals) < 6:
            return False

        # todo 测试使用p_peaks和p_end_positions的差异
        rp_diff = len(r_peaks) - len(p_end_positions)

        # AF诊断标准
        condition1 = rp_diff > 4
        condition2 = rr_std > 0.05
        condition3 = rr_cv > 0.05
        condition4 = rr_iqr > 0.02
        condition5 = rr_mean < 1.3

        is_af = (condition1 and condition2 and condition3 and condition4 and condition5)

        return bool(is_af)
    except Exception as e:
        Logger().error(f'AF诊断异常：\n{traceback.format_exc()}')
        return False
