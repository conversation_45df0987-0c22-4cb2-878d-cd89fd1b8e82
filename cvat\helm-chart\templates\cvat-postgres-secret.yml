{{- if .Values.postgresql.secret.create }}
apiVersion: v1
kind: Secret
metadata:
  name: "{{ tpl (.Values.postgresql.secret.name) . }}"
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cvat.labels" . | nindent 4 }}
type: generic
stringData:
  database: {{ .Values.postgresql.auth.database }}
  username: {{ .Values.postgresql.auth.username }}
  password: {{ .Values.postgresql.secret.password }}
  postgres-password: {{ .Values.postgresql.secret.postgres_password }}
  replication-password: {{ .Values.postgresql.secret.replication_password }}
{{- end }}
