/* Copyright 2016 Google Inc.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

#ifndef NSYNC_PUBLIC_NSYNC_H_
#define NSYNC_PUBLIC_NSYNC_H_

#include "nsync_mu.h"
#include "nsync_mu_wait.h"
#include "nsync_cv.h"
#include "nsync_note.h"
#include "nsync_counter.h"
#include "nsync_waiter.h"
#include "nsync_once.h"
#include "nsync_debug.h"

#endif /*NSYNC_PUBLIC_NSYNC_H_*/
