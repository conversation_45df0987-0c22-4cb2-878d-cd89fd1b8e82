/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// Linalg Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterLinalgPasses() {
  registerLinalgPasses();
}

MlirPass mlirCreateLinalgConvertElementwiseToLinalg() {
  return wrap(mlir::createConvertElementwiseToLinalgPass().release());
}
void mlirRegisterLinalgConvertElementwiseToLinalg() {
  registerConvertElementwiseToLinalgPass();
}


MlirPass mlirCreateLinalgLinalgBufferize() {
  return wrap(mlir::createLinalgBufferizePass().release());
}
void mlirRegisterLinalgLinalgBufferize() {
  registerLinalgBufferizePass();
}


MlirPass mlirCreateLinalgLinalgComprehensiveFuncBufferize() {
  return wrap(mlir::createLinalgComprehensiveFuncBufferizePass().release());
}
void mlirRegisterLinalgLinalgComprehensiveFuncBufferize() {
  registerLinalgComprehensiveFuncBufferizePass();
}


MlirPass mlirCreateLinalgLinalgDetensorize() {
  return wrap(mlir::createLinalgDetensorizePass().release());
}
void mlirRegisterLinalgLinalgDetensorize() {
  registerLinalgDetensorizePass();
}


MlirPass mlirCreateLinalgLinalgFoldReshapeOpsByLinearization() {
  return wrap(mlir::createFoldReshapeOpsByLinearizationPass().release());
}
void mlirRegisterLinalgLinalgFoldReshapeOpsByLinearization() {
  registerLinalgFoldReshapeOpsByLinearizationPass();
}


MlirPass mlirCreateLinalgLinalgFoldUnitExtentDims() {
  return wrap(mlir::createLinalgFoldUnitExtentDimsPass().release());
}
void mlirRegisterLinalgLinalgFoldUnitExtentDims() {
  registerLinalgFoldUnitExtentDimsPass();
}


MlirPass mlirCreateLinalgLinalgFusionOfTensorOps() {
  return wrap(mlir::createLinalgFusionOfTensorOpsPass().release());
}
void mlirRegisterLinalgLinalgFusionOfTensorOps() {
  registerLinalgFusionOfTensorOpsPass();
}


MlirPass mlirCreateLinalgLinalgGeneralization() {
  return wrap(mlir::createLinalgGeneralizationPass().release());
}
void mlirRegisterLinalgLinalgGeneralization() {
  registerLinalgGeneralizationPass();
}


MlirPass mlirCreateLinalgLinalgInlineScalarOperands() {
  return wrap(mlir::createLinalgInlineScalarOperandsPass().release());
}
void mlirRegisterLinalgLinalgInlineScalarOperands() {
  registerLinalgInlineScalarOperandsPass();
}


MlirPass mlirCreateLinalgLinalgLowerTiledLoopsToSCF() {
  return wrap(mlir::createConvertLinalgTiledLoopsToSCFPass().release());
}
void mlirRegisterLinalgLinalgLowerTiledLoopsToSCF() {
  registerLinalgLowerTiledLoopsToSCFPass();
}


MlirPass mlirCreateLinalgLinalgLowerToAffineLoops() {
  return wrap(mlir::createConvertLinalgToAffineLoopsPass().release());
}
void mlirRegisterLinalgLinalgLowerToAffineLoops() {
  registerLinalgLowerToAffineLoopsPass();
}


MlirPass mlirCreateLinalgLinalgLowerToLoops() {
  return wrap(mlir::createConvertLinalgToLoopsPass().release());
}
void mlirRegisterLinalgLinalgLowerToLoops() {
  registerLinalgLowerToLoopsPass();
}


MlirPass mlirCreateLinalgLinalgLowerToParallelLoops() {
  return wrap(mlir::createConvertLinalgToParallelLoopsPass().release());
}
void mlirRegisterLinalgLinalgLowerToParallelLoops() {
  registerLinalgLowerToParallelLoopsPass();
}


MlirPass mlirCreateLinalgLinalgPromotion() {
  return wrap(mlir::createLinalgPromotionPass().release());
}
void mlirRegisterLinalgLinalgPromotion() {
  registerLinalgPromotionPass();
}


MlirPass mlirCreateLinalgLinalgTiling() {
  return wrap(mlir::createLinalgTilingPass().release());
}
void mlirRegisterLinalgLinalgTiling() {
  registerLinalgTilingPass();
}


MlirPass mlirCreateLinalgLinalgTilingToParallelLoops() {
  return wrap(mlir::createLinalgTilingToParallelLoopsPass().release());
}
void mlirRegisterLinalgLinalgTilingToParallelLoops() {
  registerLinalgTilingToParallelLoopsPass();
}


MlirPass mlirCreateLinalgLinalgTilingToTiledLoops() {
  return wrap(mlir::createLinalgTilingToTiledLoopPass().release());
}
void mlirRegisterLinalgLinalgTilingToTiledLoops() {
  registerLinalgTilingToTiledLoopsPass();
}

