# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011-2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Malayalam (http://www.transifex.com/django/django/language/"
"ml/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ml\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr ""

msgid "python model class name"
msgstr "പൈത്തണ്‍ മോഡല്‍  ക്ളാസ്സിന്റെ പേര്"

msgid "content type"
msgstr "ഏതു തരം ഉള്ളടക്കം"

msgid "content types"
msgstr "ഏതൊക്കെ തരം ഉള്ളടക്കം"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "കണ്ടന്റ് ടൈപ്പ് %(ct_id)s വസ്തുവിന് അനുബന്ധമായ മോഡല്‍ ഇല്ല."

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "കണ്ടന്റ് ടൈപ്പ് %(ct_id)s വസ്തു %(obj_id)s നിലവിലില്ല"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s വസ്തുക്കള്‍ക്ക് get_absolute_url() രീതി ഇല്ല."
