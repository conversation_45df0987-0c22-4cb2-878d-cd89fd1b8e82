// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/xla/service/gpu/gpu_autotuning.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/compiler/xla/service/hlo.pb.h"
#include "tensorflow/compiler/xla/xla_data.pb.h"
#include "tensorflow/core/protobuf/autotuning.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto;
namespace xla {
namespace gpu {
class AlgorithmDenylist;
class AlgorithmDenylistDefaultTypeInternal;
extern AlgorithmDenylistDefaultTypeInternal _AlgorithmDenylist_default_instance_;
class AlgorithmDenylistEntry;
class AlgorithmDenylistEntryDefaultTypeInternal;
extern AlgorithmDenylistEntryDefaultTypeInternal _AlgorithmDenylistEntry_default_instance_;
class ConvInstructionLog;
class ConvInstructionLogDefaultTypeInternal;
extern ConvInstructionLogDefaultTypeInternal _ConvInstructionLog_default_instance_;
class DenylistedAlgorithm;
class DenylistedAlgorithmDefaultTypeInternal;
extern DenylistedAlgorithmDefaultTypeInternal _DenylistedAlgorithm_default_instance_;
}  // namespace gpu
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::gpu::AlgorithmDenylist* Arena::CreateMaybeMessage<::xla::gpu::AlgorithmDenylist>(Arena*);
template<> ::xla::gpu::AlgorithmDenylistEntry* Arena::CreateMaybeMessage<::xla::gpu::AlgorithmDenylistEntry>(Arena*);
template<> ::xla::gpu::ConvInstructionLog* Arena::CreateMaybeMessage<::xla::gpu::ConvInstructionLog>(Arena*);
template<> ::xla::gpu::DenylistedAlgorithm* Arena::CreateMaybeMessage<::xla::gpu::DenylistedAlgorithm>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {
namespace gpu {

// ===================================================================

class ConvInstructionLog :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.gpu.ConvInstructionLog) */ {
 public:
  ConvInstructionLog();
  virtual ~ConvInstructionLog();

  ConvInstructionLog(const ConvInstructionLog& from);
  ConvInstructionLog(ConvInstructionLog&& from) noexcept
    : ConvInstructionLog() {
    *this = ::std::move(from);
  }

  inline ConvInstructionLog& operator=(const ConvInstructionLog& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConvInstructionLog& operator=(ConvInstructionLog&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ConvInstructionLog& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConvInstructionLog* internal_default_instance() {
    return reinterpret_cast<const ConvInstructionLog*>(
               &_ConvInstructionLog_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ConvInstructionLog& a, ConvInstructionLog& b) {
    a.Swap(&b);
  }
  inline void Swap(ConvInstructionLog* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ConvInstructionLog* New() const final {
    return CreateMaybeMessage<ConvInstructionLog>(nullptr);
  }

  ConvInstructionLog* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ConvInstructionLog>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ConvInstructionLog& from);
  void MergeFrom(const ConvInstructionLog& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConvInstructionLog* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.gpu.ConvInstructionLog";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperandShapesFieldNumber = 2,
    kOperandAddressesFieldNumber = 4,
    kInstructionFieldNumber = 1,
    kResultAddressFieldNumber = 3,
  };
  // repeated .xla.ShapeProto operand_shapes = 2;
  int operand_shapes_size() const;
  void clear_operand_shapes();
  ::xla::ShapeProto* mutable_operand_shapes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
      mutable_operand_shapes();
  const ::xla::ShapeProto& operand_shapes(int index) const;
  ::xla::ShapeProto* add_operand_shapes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
      operand_shapes() const;

  // repeated uint64 operand_addresses = 4;
  int operand_addresses_size() const;
  void clear_operand_addresses();
  ::PROTOBUF_NAMESPACE_ID::uint64 operand_addresses(int index) const;
  void set_operand_addresses(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value);
  void add_operand_addresses(::PROTOBUF_NAMESPACE_ID::uint64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
      operand_addresses() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
      mutable_operand_addresses();

  // .xla.HloInstructionProto instruction = 1;
  bool has_instruction() const;
  void clear_instruction();
  const ::xla::HloInstructionProto& instruction() const;
  ::xla::HloInstructionProto* release_instruction();
  ::xla::HloInstructionProto* mutable_instruction();
  void set_allocated_instruction(::xla::HloInstructionProto* instruction);

  // uint64 result_address = 3;
  void clear_result_address();
  ::PROTOBUF_NAMESPACE_ID::uint64 result_address() const;
  void set_result_address(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:xla.gpu.ConvInstructionLog)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto > operand_shapes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 > operand_addresses_;
  mutable std::atomic<int> _operand_addresses_cached_byte_size_;
  ::xla::HloInstructionProto* instruction_;
  ::PROTOBUF_NAMESPACE_ID::uint64 result_address_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto;
};
// -------------------------------------------------------------------

class DenylistedAlgorithm :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.gpu.DenylistedAlgorithm) */ {
 public:
  DenylistedAlgorithm();
  virtual ~DenylistedAlgorithm();

  DenylistedAlgorithm(const DenylistedAlgorithm& from);
  DenylistedAlgorithm(DenylistedAlgorithm&& from) noexcept
    : DenylistedAlgorithm() {
    *this = ::std::move(from);
  }

  inline DenylistedAlgorithm& operator=(const DenylistedAlgorithm& from) {
    CopyFrom(from);
    return *this;
  }
  inline DenylistedAlgorithm& operator=(DenylistedAlgorithm&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DenylistedAlgorithm& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DenylistedAlgorithm* internal_default_instance() {
    return reinterpret_cast<const DenylistedAlgorithm*>(
               &_DenylistedAlgorithm_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DenylistedAlgorithm& a, DenylistedAlgorithm& b) {
    a.Swap(&b);
  }
  inline void Swap(DenylistedAlgorithm* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DenylistedAlgorithm* New() const final {
    return CreateMaybeMessage<DenylistedAlgorithm>(nullptr);
  }

  DenylistedAlgorithm* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DenylistedAlgorithm>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DenylistedAlgorithm& from);
  void MergeFrom(const DenylistedAlgorithm& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DenylistedAlgorithm* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.gpu.DenylistedAlgorithm";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kTensorOpsFieldNumber = 2,
  };
  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool tensor_ops = 2;
  void clear_tensor_ops();
  bool tensor_ops() const;
  void set_tensor_ops(bool value);

  // @@protoc_insertion_point(class_scope:xla.gpu.DenylistedAlgorithm)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  bool tensor_ops_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AlgorithmDenylistEntry :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.gpu.AlgorithmDenylistEntry) */ {
 public:
  AlgorithmDenylistEntry();
  virtual ~AlgorithmDenylistEntry();

  AlgorithmDenylistEntry(const AlgorithmDenylistEntry& from);
  AlgorithmDenylistEntry(AlgorithmDenylistEntry&& from) noexcept
    : AlgorithmDenylistEntry() {
    *this = ::std::move(from);
  }

  inline AlgorithmDenylistEntry& operator=(const AlgorithmDenylistEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlgorithmDenylistEntry& operator=(AlgorithmDenylistEntry&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AlgorithmDenylistEntry& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AlgorithmDenylistEntry* internal_default_instance() {
    return reinterpret_cast<const AlgorithmDenylistEntry*>(
               &_AlgorithmDenylistEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(AlgorithmDenylistEntry& a, AlgorithmDenylistEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(AlgorithmDenylistEntry* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AlgorithmDenylistEntry* New() const final {
    return CreateMaybeMessage<AlgorithmDenylistEntry>(nullptr);
  }

  AlgorithmDenylistEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AlgorithmDenylistEntry>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AlgorithmDenylistEntry& from);
  void MergeFrom(const AlgorithmDenylistEntry& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlgorithmDenylistEntry* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.gpu.AlgorithmDenylistEntry";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlgosFieldNumber = 4,
    kHloFieldNumber = 1,
    kBlasVersionFieldNumber = 5,
    kCcFieldNumber = 2,
    kCudnnVersionFieldNumber = 3,
  };
  // repeated .xla.gpu.DenylistedAlgorithm algos = 4;
  int algos_size() const;
  void clear_algos();
  ::xla::gpu::DenylistedAlgorithm* mutable_algos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::DenylistedAlgorithm >*
      mutable_algos();
  const ::xla::gpu::DenylistedAlgorithm& algos(int index) const;
  ::xla::gpu::DenylistedAlgorithm* add_algos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::DenylistedAlgorithm >&
      algos() const;

  // string hlo = 1;
  void clear_hlo();
  const std::string& hlo() const;
  void set_hlo(const std::string& value);
  void set_hlo(std::string&& value);
  void set_hlo(const char* value);
  void set_hlo(const char* value, size_t size);
  std::string* mutable_hlo();
  std::string* release_hlo();
  void set_allocated_hlo(std::string* hlo);

  // string blas_version = 5;
  void clear_blas_version();
  const std::string& blas_version() const;
  void set_blas_version(const std::string& value);
  void set_blas_version(std::string&& value);
  void set_blas_version(const char* value);
  void set_blas_version(const char* value, size_t size);
  std::string* mutable_blas_version();
  std::string* release_blas_version();
  void set_allocated_blas_version(std::string* blas_version);

  // .tensorflow.ComputeCapability cc = 2;
  bool has_cc() const;
  void clear_cc();
  const ::tensorflow::ComputeCapability& cc() const;
  ::tensorflow::ComputeCapability* release_cc();
  ::tensorflow::ComputeCapability* mutable_cc();
  void set_allocated_cc(::tensorflow::ComputeCapability* cc);

  // .tensorflow.CudnnVersion cudnn_version = 3;
  bool has_cudnn_version() const;
  void clear_cudnn_version();
  const ::tensorflow::CudnnVersion& cudnn_version() const;
  ::tensorflow::CudnnVersion* release_cudnn_version();
  ::tensorflow::CudnnVersion* mutable_cudnn_version();
  void set_allocated_cudnn_version(::tensorflow::CudnnVersion* cudnn_version);

  // @@protoc_insertion_point(class_scope:xla.gpu.AlgorithmDenylistEntry)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::DenylistedAlgorithm > algos_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr hlo_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr blas_version_;
  ::tensorflow::ComputeCapability* cc_;
  ::tensorflow::CudnnVersion* cudnn_version_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AlgorithmDenylist :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.gpu.AlgorithmDenylist) */ {
 public:
  AlgorithmDenylist();
  virtual ~AlgorithmDenylist();

  AlgorithmDenylist(const AlgorithmDenylist& from);
  AlgorithmDenylist(AlgorithmDenylist&& from) noexcept
    : AlgorithmDenylist() {
    *this = ::std::move(from);
  }

  inline AlgorithmDenylist& operator=(const AlgorithmDenylist& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlgorithmDenylist& operator=(AlgorithmDenylist&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AlgorithmDenylist& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AlgorithmDenylist* internal_default_instance() {
    return reinterpret_cast<const AlgorithmDenylist*>(
               &_AlgorithmDenylist_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(AlgorithmDenylist& a, AlgorithmDenylist& b) {
    a.Swap(&b);
  }
  inline void Swap(AlgorithmDenylist* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AlgorithmDenylist* New() const final {
    return CreateMaybeMessage<AlgorithmDenylist>(nullptr);
  }

  AlgorithmDenylist* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AlgorithmDenylist>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AlgorithmDenylist& from);
  void MergeFrom(const AlgorithmDenylist& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlgorithmDenylist* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.gpu.AlgorithmDenylist";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEntriesFieldNumber = 1,
  };
  // repeated .xla.gpu.AlgorithmDenylistEntry entries = 1;
  int entries_size() const;
  void clear_entries();
  ::xla::gpu::AlgorithmDenylistEntry* mutable_entries(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::AlgorithmDenylistEntry >*
      mutable_entries();
  const ::xla::gpu::AlgorithmDenylistEntry& entries(int index) const;
  ::xla::gpu::AlgorithmDenylistEntry* add_entries();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::AlgorithmDenylistEntry >&
      entries() const;

  // @@protoc_insertion_point(class_scope:xla.gpu.AlgorithmDenylist)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::AlgorithmDenylistEntry > entries_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ConvInstructionLog

// .xla.HloInstructionProto instruction = 1;
inline bool ConvInstructionLog::has_instruction() const {
  return this != internal_default_instance() && instruction_ != nullptr;
}
inline const ::xla::HloInstructionProto& ConvInstructionLog::instruction() const {
  const ::xla::HloInstructionProto* p = instruction_;
  // @@protoc_insertion_point(field_get:xla.gpu.ConvInstructionLog.instruction)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloInstructionProto*>(
      &::xla::_HloInstructionProto_default_instance_);
}
inline ::xla::HloInstructionProto* ConvInstructionLog::release_instruction() {
  // @@protoc_insertion_point(field_release:xla.gpu.ConvInstructionLog.instruction)
  
  ::xla::HloInstructionProto* temp = instruction_;
  instruction_ = nullptr;
  return temp;
}
inline ::xla::HloInstructionProto* ConvInstructionLog::mutable_instruction() {
  
  if (instruction_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloInstructionProto>(GetArenaNoVirtual());
    instruction_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.gpu.ConvInstructionLog.instruction)
  return instruction_;
}
inline void ConvInstructionLog::set_allocated_instruction(::xla::HloInstructionProto* instruction) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(instruction_);
  }
  if (instruction) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(instruction)->GetArena();
    if (message_arena != submessage_arena) {
      instruction = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, instruction, submessage_arena);
    }
    
  } else {
    
  }
  instruction_ = instruction;
  // @@protoc_insertion_point(field_set_allocated:xla.gpu.ConvInstructionLog.instruction)
}

// repeated .xla.ShapeProto operand_shapes = 2;
inline int ConvInstructionLog::operand_shapes_size() const {
  return operand_shapes_.size();
}
inline ::xla::ShapeProto* ConvInstructionLog::mutable_operand_shapes(int index) {
  // @@protoc_insertion_point(field_mutable:xla.gpu.ConvInstructionLog.operand_shapes)
  return operand_shapes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
ConvInstructionLog::mutable_operand_shapes() {
  // @@protoc_insertion_point(field_mutable_list:xla.gpu.ConvInstructionLog.operand_shapes)
  return &operand_shapes_;
}
inline const ::xla::ShapeProto& ConvInstructionLog::operand_shapes(int index) const {
  // @@protoc_insertion_point(field_get:xla.gpu.ConvInstructionLog.operand_shapes)
  return operand_shapes_.Get(index);
}
inline ::xla::ShapeProto* ConvInstructionLog::add_operand_shapes() {
  // @@protoc_insertion_point(field_add:xla.gpu.ConvInstructionLog.operand_shapes)
  return operand_shapes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
ConvInstructionLog::operand_shapes() const {
  // @@protoc_insertion_point(field_list:xla.gpu.ConvInstructionLog.operand_shapes)
  return operand_shapes_;
}

// uint64 result_address = 3;
inline void ConvInstructionLog::clear_result_address() {
  result_address_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 ConvInstructionLog::result_address() const {
  // @@protoc_insertion_point(field_get:xla.gpu.ConvInstructionLog.result_address)
  return result_address_;
}
inline void ConvInstructionLog::set_result_address(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  result_address_ = value;
  // @@protoc_insertion_point(field_set:xla.gpu.ConvInstructionLog.result_address)
}

// repeated uint64 operand_addresses = 4;
inline int ConvInstructionLog::operand_addresses_size() const {
  return operand_addresses_.size();
}
inline void ConvInstructionLog::clear_operand_addresses() {
  operand_addresses_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 ConvInstructionLog::operand_addresses(int index) const {
  // @@protoc_insertion_point(field_get:xla.gpu.ConvInstructionLog.operand_addresses)
  return operand_addresses_.Get(index);
}
inline void ConvInstructionLog::set_operand_addresses(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value) {
  operand_addresses_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.gpu.ConvInstructionLog.operand_addresses)
}
inline void ConvInstructionLog::add_operand_addresses(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  operand_addresses_.Add(value);
  // @@protoc_insertion_point(field_add:xla.gpu.ConvInstructionLog.operand_addresses)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
ConvInstructionLog::operand_addresses() const {
  // @@protoc_insertion_point(field_list:xla.gpu.ConvInstructionLog.operand_addresses)
  return operand_addresses_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
ConvInstructionLog::mutable_operand_addresses() {
  // @@protoc_insertion_point(field_mutable_list:xla.gpu.ConvInstructionLog.operand_addresses)
  return &operand_addresses_;
}

// -------------------------------------------------------------------

// DenylistedAlgorithm

// int64 id = 1;
inline void DenylistedAlgorithm::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DenylistedAlgorithm::id() const {
  // @@protoc_insertion_point(field_get:xla.gpu.DenylistedAlgorithm.id)
  return id_;
}
inline void DenylistedAlgorithm::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:xla.gpu.DenylistedAlgorithm.id)
}

// bool tensor_ops = 2;
inline void DenylistedAlgorithm::clear_tensor_ops() {
  tensor_ops_ = false;
}
inline bool DenylistedAlgorithm::tensor_ops() const {
  // @@protoc_insertion_point(field_get:xla.gpu.DenylistedAlgorithm.tensor_ops)
  return tensor_ops_;
}
inline void DenylistedAlgorithm::set_tensor_ops(bool value) {
  
  tensor_ops_ = value;
  // @@protoc_insertion_point(field_set:xla.gpu.DenylistedAlgorithm.tensor_ops)
}

// -------------------------------------------------------------------

// AlgorithmDenylistEntry

// string hlo = 1;
inline void AlgorithmDenylistEntry::clear_hlo() {
  hlo_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& AlgorithmDenylistEntry::hlo() const {
  // @@protoc_insertion_point(field_get:xla.gpu.AlgorithmDenylistEntry.hlo)
  return hlo_.GetNoArena();
}
inline void AlgorithmDenylistEntry::set_hlo(const std::string& value) {
  
  hlo_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:xla.gpu.AlgorithmDenylistEntry.hlo)
}
inline void AlgorithmDenylistEntry::set_hlo(std::string&& value) {
  
  hlo_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:xla.gpu.AlgorithmDenylistEntry.hlo)
}
inline void AlgorithmDenylistEntry::set_hlo(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  hlo_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:xla.gpu.AlgorithmDenylistEntry.hlo)
}
inline void AlgorithmDenylistEntry::set_hlo(const char* value, size_t size) {
  
  hlo_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:xla.gpu.AlgorithmDenylistEntry.hlo)
}
inline std::string* AlgorithmDenylistEntry::mutable_hlo() {
  
  // @@protoc_insertion_point(field_mutable:xla.gpu.AlgorithmDenylistEntry.hlo)
  return hlo_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AlgorithmDenylistEntry::release_hlo() {
  // @@protoc_insertion_point(field_release:xla.gpu.AlgorithmDenylistEntry.hlo)
  
  return hlo_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AlgorithmDenylistEntry::set_allocated_hlo(std::string* hlo) {
  if (hlo != nullptr) {
    
  } else {
    
  }
  hlo_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), hlo);
  // @@protoc_insertion_point(field_set_allocated:xla.gpu.AlgorithmDenylistEntry.hlo)
}

// .tensorflow.ComputeCapability cc = 2;
inline bool AlgorithmDenylistEntry::has_cc() const {
  return this != internal_default_instance() && cc_ != nullptr;
}
inline const ::tensorflow::ComputeCapability& AlgorithmDenylistEntry::cc() const {
  const ::tensorflow::ComputeCapability* p = cc_;
  // @@protoc_insertion_point(field_get:xla.gpu.AlgorithmDenylistEntry.cc)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ComputeCapability*>(
      &::tensorflow::_ComputeCapability_default_instance_);
}
inline ::tensorflow::ComputeCapability* AlgorithmDenylistEntry::release_cc() {
  // @@protoc_insertion_point(field_release:xla.gpu.AlgorithmDenylistEntry.cc)
  
  ::tensorflow::ComputeCapability* temp = cc_;
  cc_ = nullptr;
  return temp;
}
inline ::tensorflow::ComputeCapability* AlgorithmDenylistEntry::mutable_cc() {
  
  if (cc_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ComputeCapability>(GetArenaNoVirtual());
    cc_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.gpu.AlgorithmDenylistEntry.cc)
  return cc_;
}
inline void AlgorithmDenylistEntry::set_allocated_cc(::tensorflow::ComputeCapability* cc) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(cc_);
  }
  if (cc) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      cc = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cc, submessage_arena);
    }
    
  } else {
    
  }
  cc_ = cc;
  // @@protoc_insertion_point(field_set_allocated:xla.gpu.AlgorithmDenylistEntry.cc)
}

// .tensorflow.CudnnVersion cudnn_version = 3;
inline bool AlgorithmDenylistEntry::has_cudnn_version() const {
  return this != internal_default_instance() && cudnn_version_ != nullptr;
}
inline const ::tensorflow::CudnnVersion& AlgorithmDenylistEntry::cudnn_version() const {
  const ::tensorflow::CudnnVersion* p = cudnn_version_;
  // @@protoc_insertion_point(field_get:xla.gpu.AlgorithmDenylistEntry.cudnn_version)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::CudnnVersion*>(
      &::tensorflow::_CudnnVersion_default_instance_);
}
inline ::tensorflow::CudnnVersion* AlgorithmDenylistEntry::release_cudnn_version() {
  // @@protoc_insertion_point(field_release:xla.gpu.AlgorithmDenylistEntry.cudnn_version)
  
  ::tensorflow::CudnnVersion* temp = cudnn_version_;
  cudnn_version_ = nullptr;
  return temp;
}
inline ::tensorflow::CudnnVersion* AlgorithmDenylistEntry::mutable_cudnn_version() {
  
  if (cudnn_version_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CudnnVersion>(GetArenaNoVirtual());
    cudnn_version_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.gpu.AlgorithmDenylistEntry.cudnn_version)
  return cudnn_version_;
}
inline void AlgorithmDenylistEntry::set_allocated_cudnn_version(::tensorflow::CudnnVersion* cudnn_version) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(cudnn_version_);
  }
  if (cudnn_version) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      cudnn_version = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cudnn_version, submessage_arena);
    }
    
  } else {
    
  }
  cudnn_version_ = cudnn_version;
  // @@protoc_insertion_point(field_set_allocated:xla.gpu.AlgorithmDenylistEntry.cudnn_version)
}

// string blas_version = 5;
inline void AlgorithmDenylistEntry::clear_blas_version() {
  blas_version_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& AlgorithmDenylistEntry::blas_version() const {
  // @@protoc_insertion_point(field_get:xla.gpu.AlgorithmDenylistEntry.blas_version)
  return blas_version_.GetNoArena();
}
inline void AlgorithmDenylistEntry::set_blas_version(const std::string& value) {
  
  blas_version_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:xla.gpu.AlgorithmDenylistEntry.blas_version)
}
inline void AlgorithmDenylistEntry::set_blas_version(std::string&& value) {
  
  blas_version_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:xla.gpu.AlgorithmDenylistEntry.blas_version)
}
inline void AlgorithmDenylistEntry::set_blas_version(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  blas_version_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:xla.gpu.AlgorithmDenylistEntry.blas_version)
}
inline void AlgorithmDenylistEntry::set_blas_version(const char* value, size_t size) {
  
  blas_version_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:xla.gpu.AlgorithmDenylistEntry.blas_version)
}
inline std::string* AlgorithmDenylistEntry::mutable_blas_version() {
  
  // @@protoc_insertion_point(field_mutable:xla.gpu.AlgorithmDenylistEntry.blas_version)
  return blas_version_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AlgorithmDenylistEntry::release_blas_version() {
  // @@protoc_insertion_point(field_release:xla.gpu.AlgorithmDenylistEntry.blas_version)
  
  return blas_version_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AlgorithmDenylistEntry::set_allocated_blas_version(std::string* blas_version) {
  if (blas_version != nullptr) {
    
  } else {
    
  }
  blas_version_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), blas_version);
  // @@protoc_insertion_point(field_set_allocated:xla.gpu.AlgorithmDenylistEntry.blas_version)
}

// repeated .xla.gpu.DenylistedAlgorithm algos = 4;
inline int AlgorithmDenylistEntry::algos_size() const {
  return algos_.size();
}
inline void AlgorithmDenylistEntry::clear_algos() {
  algos_.Clear();
}
inline ::xla::gpu::DenylistedAlgorithm* AlgorithmDenylistEntry::mutable_algos(int index) {
  // @@protoc_insertion_point(field_mutable:xla.gpu.AlgorithmDenylistEntry.algos)
  return algos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::DenylistedAlgorithm >*
AlgorithmDenylistEntry::mutable_algos() {
  // @@protoc_insertion_point(field_mutable_list:xla.gpu.AlgorithmDenylistEntry.algos)
  return &algos_;
}
inline const ::xla::gpu::DenylistedAlgorithm& AlgorithmDenylistEntry::algos(int index) const {
  // @@protoc_insertion_point(field_get:xla.gpu.AlgorithmDenylistEntry.algos)
  return algos_.Get(index);
}
inline ::xla::gpu::DenylistedAlgorithm* AlgorithmDenylistEntry::add_algos() {
  // @@protoc_insertion_point(field_add:xla.gpu.AlgorithmDenylistEntry.algos)
  return algos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::DenylistedAlgorithm >&
AlgorithmDenylistEntry::algos() const {
  // @@protoc_insertion_point(field_list:xla.gpu.AlgorithmDenylistEntry.algos)
  return algos_;
}

// -------------------------------------------------------------------

// AlgorithmDenylist

// repeated .xla.gpu.AlgorithmDenylistEntry entries = 1;
inline int AlgorithmDenylist::entries_size() const {
  return entries_.size();
}
inline void AlgorithmDenylist::clear_entries() {
  entries_.Clear();
}
inline ::xla::gpu::AlgorithmDenylistEntry* AlgorithmDenylist::mutable_entries(int index) {
  // @@protoc_insertion_point(field_mutable:xla.gpu.AlgorithmDenylist.entries)
  return entries_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::AlgorithmDenylistEntry >*
AlgorithmDenylist::mutable_entries() {
  // @@protoc_insertion_point(field_mutable_list:xla.gpu.AlgorithmDenylist.entries)
  return &entries_;
}
inline const ::xla::gpu::AlgorithmDenylistEntry& AlgorithmDenylist::entries(int index) const {
  // @@protoc_insertion_point(field_get:xla.gpu.AlgorithmDenylist.entries)
  return entries_.Get(index);
}
inline ::xla::gpu::AlgorithmDenylistEntry* AlgorithmDenylist::add_entries() {
  // @@protoc_insertion_point(field_add:xla.gpu.AlgorithmDenylist.entries)
  return entries_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::AlgorithmDenylistEntry >&
AlgorithmDenylist::entries() const {
  // @@protoc_insertion_point(field_list:xla.gpu.AlgorithmDenylist.entries)
  return entries_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace gpu
}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fgpu_2fgpu_5fautotuning_2eproto
