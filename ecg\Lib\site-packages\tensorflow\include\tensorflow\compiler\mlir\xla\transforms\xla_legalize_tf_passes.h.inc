/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// LegalizeTF
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeTFBase : public ::mlir::FunctionPass {
public:
  using Base = LegalizeTFBase;

  LegalizeTFBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFBase(const LegalizeTFBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from TF dialect's or HLO dialect's control flow."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTF");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<chlo::HloClientDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<mhlo::MhloDialect>();

  registry.insert<shape::ShapeDialect>();

  registry.insert<StandardOpsDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> allow_partial_conversion_{*this, "allow-partial-conversion", ::llvm::cl::desc("Allow operations that can't be legalized."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> legalize_chlo_{*this, "legalize-chlo", ::llvm::cl::desc("Legalizes intermediate chlo ops to hlo"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> use_tf2xla_fallback_{*this, "use-tf2xla-fallback", ::llvm::cl::desc("Use TF2XLA fallback for legalization"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> device_type_{*this, "device-type", ::llvm::cl::desc("The device type used by TF2XLA fallback. Must be specified if use-tf2xla-fallback is true, otherwise not used"), ::llvm::cl::init("INVALID_DEVICE_TYPE")};
  ::mlir::Pass::Option<bool> prefer_tf2xla_{*this, "prefer-tf2xla", ::llvm::cl::desc("Prioritize tf2xla fallback legalization over MLIR legalization patterns"), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LegalizeTFControlFlow
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeTFControlFlowBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFControlFlowBase;

  LegalizeTFControlFlowBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFControlFlowBase(const LegalizeTFControlFlowBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-control-flow");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-control-flow"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from TF dialect's to HLO dialect's control flow."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFControlFlow");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFControlFlow"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LegalizeTfTypesPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeTfTypesPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LegalizeTfTypesPassBase;

  LegalizeTfTypesPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTfTypesPassBase(const LegalizeTfTypesPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-types");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-types"; }

  ::llvm::StringRef getDescription() const override { return "Replace TensorFlow types with types that are legal in the MHLO dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTfTypesPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTfTypesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// LegalizeTF Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createLegalizeTFPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTFControlFlow Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTFControlFlowPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createLegalizeTFControlFlowPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTfTypesPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTfTypesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::mhlo::CreateLegalizeTfTypesPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTf Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTfPasses() {
  registerLegalizeTFPass();
  registerLegalizeTFControlFlowPass();
  registerLegalizeTfTypesPassPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
