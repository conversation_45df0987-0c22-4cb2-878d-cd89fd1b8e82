"""
调试验证页面HTML结构
"""
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_driver():
    """设置Chrome浏览器驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def debug_verification_page():
    """调试验证页面"""
    driver = setup_driver()
    try:
        # 访问入手价页面
        url = "https://www.badmintoncn.com/cbo_eq/view_buy.php?eid=22974"
        logger.info(f"访问: {url}")
        driver.get(url)
        time.sleep(5)
        
        # 保存页面源码
        page_source = driver.page_source
        with open('debug_verification_page.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        logger.info("页面源码已保存到 debug_verification_page.html")
        
        # 获取页面文本
        body_text = driver.find_element(By.TAG_NAME, "body").text
        logger.info(f"页面完整文本:\n{body_text}")
        
        # 查找所有input元素
        inputs = driver.find_elements(By.TAG_NAME, "input")
        logger.info(f"找到 {len(inputs)} 个input元素:")
        for i, inp in enumerate(inputs):
            try:
                inp_type = inp.get_attribute("type")
                inp_name = inp.get_attribute("name")
                inp_value = inp.get_attribute("value")
                inp_placeholder = inp.get_attribute("placeholder")
                logger.info(f"  Input {i+1}: type={inp_type}, name={inp_name}, value={inp_value}, placeholder={inp_placeholder}")
            except:
                pass
        
        # 查找所有form元素
        forms = driver.find_elements(By.TAG_NAME, "form")
        logger.info(f"找到 {len(forms)} 个form元素:")
        for i, form in enumerate(forms):
            try:
                form_action = form.get_attribute("action")
                form_method = form.get_attribute("method")
                logger.info(f"  Form {i+1}: action={form_action}, method={form_method}")
                logger.info(f"  Form text: {form.text[:200]}...")
            except:
                pass
        
        # 查找所有table元素
        tables = driver.find_elements(By.TAG_NAME, "table")
        logger.info(f"找到 {len(tables)} 个table元素:")
        for i, table in enumerate(tables):
            try:
                logger.info(f"  Table {i+1} text: {table.text[:200]}...")
            except:
                pass
                
    except Exception as e:
        logger.error(f"调试失败: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_verification_page() 