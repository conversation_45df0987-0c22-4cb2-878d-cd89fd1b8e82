// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/tpu_embedding_output_layout.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto;
namespace tensorflow {
namespace tpu {
class TPUEmbeddingOutputLayout;
class TPUEmbeddingOutputLayoutDefaultTypeInternal;
extern TPUEmbeddingOutputLayoutDefaultTypeInternal _TPUEmbeddingOutputLayout_default_instance_;
class TPUEmbeddingOutputLayout_EmbeddingOutputTensor;
class TPUEmbeddingOutputLayout_EmbeddingOutputTensorDefaultTypeInternal;
extern TPUEmbeddingOutputLayout_EmbeddingOutputTensorDefaultTypeInternal _TPUEmbeddingOutputLayout_EmbeddingOutputTensor_default_instance_;
class TPUEmbeddingOutputLayout_FeatureDescriptor;
class TPUEmbeddingOutputLayout_FeatureDescriptorDefaultTypeInternal;
extern TPUEmbeddingOutputLayout_FeatureDescriptorDefaultTypeInternal _TPUEmbeddingOutputLayout_FeatureDescriptor_default_instance_;
class TPUEmbeddingOutputLayout_OutputLocation;
class TPUEmbeddingOutputLayout_OutputLocationDefaultTypeInternal;
extern TPUEmbeddingOutputLayout_OutputLocationDefaultTypeInternal _TPUEmbeddingOutputLayout_OutputLocation_default_instance_;
class TPUEmbeddingOutputLayout_TableDescriptor;
class TPUEmbeddingOutputLayout_TableDescriptorDefaultTypeInternal;
extern TPUEmbeddingOutputLayout_TableDescriptorDefaultTypeInternal _TPUEmbeddingOutputLayout_TableDescriptor_default_instance_;
class TPUEmbeddingOutputLayout_TwoDOutputTensor;
class TPUEmbeddingOutputLayout_TwoDOutputTensorDefaultTypeInternal;
extern TPUEmbeddingOutputLayout_TwoDOutputTensorDefaultTypeInternal _TPUEmbeddingOutputLayout_TwoDOutputTensor_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tpu {

// ===================================================================

class TPUEmbeddingOutputLayout_OutputLocation :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation) */ {
 public:
  TPUEmbeddingOutputLayout_OutputLocation();
  virtual ~TPUEmbeddingOutputLayout_OutputLocation();

  TPUEmbeddingOutputLayout_OutputLocation(const TPUEmbeddingOutputLayout_OutputLocation& from);
  TPUEmbeddingOutputLayout_OutputLocation(TPUEmbeddingOutputLayout_OutputLocation&& from) noexcept
    : TPUEmbeddingOutputLayout_OutputLocation() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout_OutputLocation& operator=(const TPUEmbeddingOutputLayout_OutputLocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingOutputLayout_OutputLocation& operator=(TPUEmbeddingOutputLayout_OutputLocation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUEmbeddingOutputLayout_OutputLocation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout_OutputLocation* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout_OutputLocation*>(
               &_TPUEmbeddingOutputLayout_OutputLocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TPUEmbeddingOutputLayout_OutputLocation& a, TPUEmbeddingOutputLayout_OutputLocation& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingOutputLayout_OutputLocation* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout_OutputLocation* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_OutputLocation>(nullptr);
  }

  TPUEmbeddingOutputLayout_OutputLocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_OutputLocation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout_OutputLocation& from);
  void MergeFrom(const TPUEmbeddingOutputLayout_OutputLocation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout_OutputLocation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorIndexFieldNumber = 1,
    kDim0OffsetFieldNumber = 2,
    kDim1OffsetFieldNumber = 3,
  };
  // int32 tensor_index = 1;
  void clear_tensor_index();
  ::PROTOBUF_NAMESPACE_ID::int32 tensor_index() const;
  void set_tensor_index(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 dim0_offset = 2;
  void clear_dim0_offset();
  ::PROTOBUF_NAMESPACE_ID::int32 dim0_offset() const;
  void set_dim0_offset(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 dim1_offset = 3;
  void clear_dim1_offset();
  ::PROTOBUF_NAMESPACE_ID::int32 dim1_offset() const;
  void set_dim1_offset(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int32 tensor_index_;
  ::PROTOBUF_NAMESPACE_ID::int32 dim0_offset_;
  ::PROTOBUF_NAMESPACE_ID::int32 dim1_offset_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto;
};
// -------------------------------------------------------------------

class TPUEmbeddingOutputLayout_FeatureDescriptor :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor) */ {
 public:
  TPUEmbeddingOutputLayout_FeatureDescriptor();
  virtual ~TPUEmbeddingOutputLayout_FeatureDescriptor();

  TPUEmbeddingOutputLayout_FeatureDescriptor(const TPUEmbeddingOutputLayout_FeatureDescriptor& from);
  TPUEmbeddingOutputLayout_FeatureDescriptor(TPUEmbeddingOutputLayout_FeatureDescriptor&& from) noexcept
    : TPUEmbeddingOutputLayout_FeatureDescriptor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout_FeatureDescriptor& operator=(const TPUEmbeddingOutputLayout_FeatureDescriptor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingOutputLayout_FeatureDescriptor& operator=(TPUEmbeddingOutputLayout_FeatureDescriptor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUEmbeddingOutputLayout_FeatureDescriptor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout_FeatureDescriptor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout_FeatureDescriptor*>(
               &_TPUEmbeddingOutputLayout_FeatureDescriptor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TPUEmbeddingOutputLayout_FeatureDescriptor& a, TPUEmbeddingOutputLayout_FeatureDescriptor& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingOutputLayout_FeatureDescriptor* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout_FeatureDescriptor* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_FeatureDescriptor>(nullptr);
  }

  TPUEmbeddingOutputLayout_FeatureDescriptor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_FeatureDescriptor>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout_FeatureDescriptor& from);
  void MergeFrom(const TPUEmbeddingOutputLayout_FeatureDescriptor& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout_FeatureDescriptor* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputLocationFieldNumber = 1,
  };
  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation output_location = 1;
  int output_location_size() const;
  void clear_output_location();
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* mutable_output_location(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation >*
      mutable_output_location();
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation& output_location(int index) const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* add_output_location();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation >&
      output_location() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation > output_location_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto;
};
// -------------------------------------------------------------------

class TPUEmbeddingOutputLayout_TableDescriptor :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor) */ {
 public:
  TPUEmbeddingOutputLayout_TableDescriptor();
  virtual ~TPUEmbeddingOutputLayout_TableDescriptor();

  TPUEmbeddingOutputLayout_TableDescriptor(const TPUEmbeddingOutputLayout_TableDescriptor& from);
  TPUEmbeddingOutputLayout_TableDescriptor(TPUEmbeddingOutputLayout_TableDescriptor&& from) noexcept
    : TPUEmbeddingOutputLayout_TableDescriptor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout_TableDescriptor& operator=(const TPUEmbeddingOutputLayout_TableDescriptor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingOutputLayout_TableDescriptor& operator=(TPUEmbeddingOutputLayout_TableDescriptor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUEmbeddingOutputLayout_TableDescriptor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout_TableDescriptor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout_TableDescriptor*>(
               &_TPUEmbeddingOutputLayout_TableDescriptor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TPUEmbeddingOutputLayout_TableDescriptor& a, TPUEmbeddingOutputLayout_TableDescriptor& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingOutputLayout_TableDescriptor* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout_TableDescriptor* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_TableDescriptor>(nullptr);
  }

  TPUEmbeddingOutputLayout_TableDescriptor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_TableDescriptor>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout_TableDescriptor& from);
  void MergeFrom(const TPUEmbeddingOutputLayout_TableDescriptor& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout_TableDescriptor* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeatureFieldNumber = 1,
  };
  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor feature = 1;
  int feature_size() const;
  void clear_feature();
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* mutable_feature(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor >*
      mutable_feature();
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor& feature(int index) const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* add_feature();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor >&
      feature() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor > feature_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto;
};
// -------------------------------------------------------------------

class TPUEmbeddingOutputLayout_TwoDOutputTensor :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor) */ {
 public:
  TPUEmbeddingOutputLayout_TwoDOutputTensor();
  virtual ~TPUEmbeddingOutputLayout_TwoDOutputTensor();

  TPUEmbeddingOutputLayout_TwoDOutputTensor(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from);
  TPUEmbeddingOutputLayout_TwoDOutputTensor(TPUEmbeddingOutputLayout_TwoDOutputTensor&& from) noexcept
    : TPUEmbeddingOutputLayout_TwoDOutputTensor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout_TwoDOutputTensor& operator=(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingOutputLayout_TwoDOutputTensor& operator=(TPUEmbeddingOutputLayout_TwoDOutputTensor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUEmbeddingOutputLayout_TwoDOutputTensor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout_TwoDOutputTensor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout_TwoDOutputTensor*>(
               &_TPUEmbeddingOutputLayout_TwoDOutputTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TPUEmbeddingOutputLayout_TwoDOutputTensor& a, TPUEmbeddingOutputLayout_TwoDOutputTensor& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingOutputLayout_TwoDOutputTensor* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout_TwoDOutputTensor* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_TwoDOutputTensor>(nullptr);
  }

  TPUEmbeddingOutputLayout_TwoDOutputTensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_TwoDOutputTensor>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from);
  void MergeFrom(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout_TwoDOutputTensor* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDim1SizeFieldNumber = 1,
    kDim0SizePerSampleFieldNumber = 2,
  };
  // int32 dim1_size = 1;
  void clear_dim1_size();
  ::PROTOBUF_NAMESPACE_ID::int32 dim1_size() const;
  void set_dim1_size(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 dim0_size_per_sample = 2;
  void clear_dim0_size_per_sample();
  ::PROTOBUF_NAMESPACE_ID::int32 dim0_size_per_sample() const;
  void set_dim0_size_per_sample(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int32 dim1_size_;
  ::PROTOBUF_NAMESPACE_ID::int32 dim0_size_per_sample_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto;
};
// -------------------------------------------------------------------

class TPUEmbeddingOutputLayout_EmbeddingOutputTensor :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor) */ {
 public:
  TPUEmbeddingOutputLayout_EmbeddingOutputTensor();
  virtual ~TPUEmbeddingOutputLayout_EmbeddingOutputTensor();

  TPUEmbeddingOutputLayout_EmbeddingOutputTensor(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from);
  TPUEmbeddingOutputLayout_EmbeddingOutputTensor(TPUEmbeddingOutputLayout_EmbeddingOutputTensor&& from) noexcept
    : TPUEmbeddingOutputLayout_EmbeddingOutputTensor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout_EmbeddingOutputTensor& operator=(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingOutputLayout_EmbeddingOutputTensor& operator=(TPUEmbeddingOutputLayout_EmbeddingOutputTensor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& default_instance();

  enum OutputFormatCase {
    kTwoD = 4,
    OUTPUT_FORMAT_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout_EmbeddingOutputTensor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout_EmbeddingOutputTensor*>(
               &_TPUEmbeddingOutputLayout_EmbeddingOutputTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TPUEmbeddingOutputLayout_EmbeddingOutputTensor& a, TPUEmbeddingOutputLayout_EmbeddingOutputTensor& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingOutputLayout_EmbeddingOutputTensor* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout_EmbeddingOutputTensor* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_EmbeddingOutputTensor>(nullptr);
  }

  TPUEmbeddingOutputLayout_EmbeddingOutputTensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_EmbeddingOutputTensor>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from);
  void MergeFrom(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout_EmbeddingOutputTensor* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTwoDFieldNumber = 4,
  };
  // .tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor two_d = 4;
  bool has_two_d() const;
  void clear_two_d();
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor& two_d() const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* release_two_d();
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* mutable_two_d();
  void set_allocated_two_d(::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* two_d);

  void clear_output_format();
  OutputFormatCase output_format_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
 private:
  class _Internal;
  void set_has_two_d();

  inline bool has_output_format() const;
  inline void clear_has_output_format();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  union OutputFormatUnion {
    OutputFormatUnion() {}
    ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* two_d_;
  } output_format_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto;
};
// -------------------------------------------------------------------

class TPUEmbeddingOutputLayout :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout) */ {
 public:
  TPUEmbeddingOutputLayout();
  virtual ~TPUEmbeddingOutputLayout();

  TPUEmbeddingOutputLayout(const TPUEmbeddingOutputLayout& from);
  TPUEmbeddingOutputLayout(TPUEmbeddingOutputLayout&& from) noexcept
    : TPUEmbeddingOutputLayout() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout& operator=(const TPUEmbeddingOutputLayout& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingOutputLayout& operator=(TPUEmbeddingOutputLayout&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUEmbeddingOutputLayout& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout*>(
               &_TPUEmbeddingOutputLayout_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(TPUEmbeddingOutputLayout& a, TPUEmbeddingOutputLayout& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingOutputLayout* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout>(nullptr);
  }

  TPUEmbeddingOutputLayout* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout& from);
  void MergeFrom(const TPUEmbeddingOutputLayout& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingOutputLayout";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TPUEmbeddingOutputLayout_OutputLocation OutputLocation;
  typedef TPUEmbeddingOutputLayout_FeatureDescriptor FeatureDescriptor;
  typedef TPUEmbeddingOutputLayout_TableDescriptor TableDescriptor;
  typedef TPUEmbeddingOutputLayout_TwoDOutputTensor TwoDOutputTensor;
  typedef TPUEmbeddingOutputLayout_EmbeddingOutputTensor EmbeddingOutputTensor;

  // accessors -------------------------------------------------------

  enum : int {
    kTableFieldNumber = 1,
    kOutputFieldNumber = 2,
  };
  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor table = 1;
  int table_size() const;
  void clear_table();
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* mutable_table(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor >*
      mutable_table();
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor& table(int index) const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* add_table();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor >&
      table() const;

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor output = 2;
  int output_size() const;
  void clear_output();
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* mutable_output(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor >*
      mutable_output();
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor& output(int index) const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* add_output();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor >&
      output() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor > table_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor > output_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TPUEmbeddingOutputLayout_OutputLocation

// int32 tensor_index = 1;
inline void TPUEmbeddingOutputLayout_OutputLocation::clear_tensor_index() {
  tensor_index_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUEmbeddingOutputLayout_OutputLocation::tensor_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.tensor_index)
  return tensor_index_;
}
inline void TPUEmbeddingOutputLayout_OutputLocation::set_tensor_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  tensor_index_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.tensor_index)
}

// int32 dim0_offset = 2;
inline void TPUEmbeddingOutputLayout_OutputLocation::clear_dim0_offset() {
  dim0_offset_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUEmbeddingOutputLayout_OutputLocation::dim0_offset() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.dim0_offset)
  return dim0_offset_;
}
inline void TPUEmbeddingOutputLayout_OutputLocation::set_dim0_offset(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  dim0_offset_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.dim0_offset)
}

// int32 dim1_offset = 3;
inline void TPUEmbeddingOutputLayout_OutputLocation::clear_dim1_offset() {
  dim1_offset_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUEmbeddingOutputLayout_OutputLocation::dim1_offset() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.dim1_offset)
  return dim1_offset_;
}
inline void TPUEmbeddingOutputLayout_OutputLocation::set_dim1_offset(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  dim1_offset_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.dim1_offset)
}

// -------------------------------------------------------------------

// TPUEmbeddingOutputLayout_FeatureDescriptor

// repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation output_location = 1;
inline int TPUEmbeddingOutputLayout_FeatureDescriptor::output_location_size() const {
  return output_location_.size();
}
inline void TPUEmbeddingOutputLayout_FeatureDescriptor::clear_output_location() {
  output_location_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* TPUEmbeddingOutputLayout_FeatureDescriptor::mutable_output_location(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor.output_location)
  return output_location_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation >*
TPUEmbeddingOutputLayout_FeatureDescriptor::mutable_output_location() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor.output_location)
  return &output_location_;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation& TPUEmbeddingOutputLayout_FeatureDescriptor::output_location(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor.output_location)
  return output_location_.Get(index);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* TPUEmbeddingOutputLayout_FeatureDescriptor::add_output_location() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor.output_location)
  return output_location_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation >&
TPUEmbeddingOutputLayout_FeatureDescriptor::output_location() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor.output_location)
  return output_location_;
}

// -------------------------------------------------------------------

// TPUEmbeddingOutputLayout_TableDescriptor

// repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor feature = 1;
inline int TPUEmbeddingOutputLayout_TableDescriptor::feature_size() const {
  return feature_.size();
}
inline void TPUEmbeddingOutputLayout_TableDescriptor::clear_feature() {
  feature_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* TPUEmbeddingOutputLayout_TableDescriptor::mutable_feature(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor.feature)
  return feature_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor >*
TPUEmbeddingOutputLayout_TableDescriptor::mutable_feature() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor.feature)
  return &feature_;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor& TPUEmbeddingOutputLayout_TableDescriptor::feature(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor.feature)
  return feature_.Get(index);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* TPUEmbeddingOutputLayout_TableDescriptor::add_feature() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor.feature)
  return feature_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor >&
TPUEmbeddingOutputLayout_TableDescriptor::feature() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor.feature)
  return feature_;
}

// -------------------------------------------------------------------

// TPUEmbeddingOutputLayout_TwoDOutputTensor

// int32 dim0_size_per_sample = 2;
inline void TPUEmbeddingOutputLayout_TwoDOutputTensor::clear_dim0_size_per_sample() {
  dim0_size_per_sample_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUEmbeddingOutputLayout_TwoDOutputTensor::dim0_size_per_sample() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor.dim0_size_per_sample)
  return dim0_size_per_sample_;
}
inline void TPUEmbeddingOutputLayout_TwoDOutputTensor::set_dim0_size_per_sample(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  dim0_size_per_sample_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor.dim0_size_per_sample)
}

// int32 dim1_size = 1;
inline void TPUEmbeddingOutputLayout_TwoDOutputTensor::clear_dim1_size() {
  dim1_size_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUEmbeddingOutputLayout_TwoDOutputTensor::dim1_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor.dim1_size)
  return dim1_size_;
}
inline void TPUEmbeddingOutputLayout_TwoDOutputTensor::set_dim1_size(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  dim1_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor.dim1_size)
}

// -------------------------------------------------------------------

// TPUEmbeddingOutputLayout_EmbeddingOutputTensor

// .tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor two_d = 4;
inline bool TPUEmbeddingOutputLayout_EmbeddingOutputTensor::has_two_d() const {
  return output_format_case() == kTwoD;
}
inline void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::set_has_two_d() {
  _oneof_case_[0] = kTwoD;
}
inline void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::clear_two_d() {
  if (has_two_d()) {
    delete output_format_.two_d_;
    clear_has_output_format();
  }
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* TPUEmbeddingOutputLayout_EmbeddingOutputTensor::release_two_d() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor.two_d)
  if (has_two_d()) {
    clear_has_output_format();
      ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* temp = output_format_.two_d_;
    output_format_.two_d_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor& TPUEmbeddingOutputLayout_EmbeddingOutputTensor::two_d() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor.two_d)
  return has_two_d()
      ? *output_format_.two_d_
      : *reinterpret_cast< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor*>(&::tensorflow::tpu::_TPUEmbeddingOutputLayout_TwoDOutputTensor_default_instance_);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* TPUEmbeddingOutputLayout_EmbeddingOutputTensor::mutable_two_d() {
  if (!has_two_d()) {
    clear_output_format();
    set_has_two_d();
    output_format_.two_d_ = CreateMaybeMessage< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor.two_d)
  return output_format_.two_d_;
}

inline bool TPUEmbeddingOutputLayout_EmbeddingOutputTensor::has_output_format() const {
  return output_format_case() != OUTPUT_FORMAT_NOT_SET;
}
inline void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::clear_has_output_format() {
  _oneof_case_[0] = OUTPUT_FORMAT_NOT_SET;
}
inline TPUEmbeddingOutputLayout_EmbeddingOutputTensor::OutputFormatCase TPUEmbeddingOutputLayout_EmbeddingOutputTensor::output_format_case() const {
  return TPUEmbeddingOutputLayout_EmbeddingOutputTensor::OutputFormatCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// TPUEmbeddingOutputLayout

// repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor table = 1;
inline int TPUEmbeddingOutputLayout::table_size() const {
  return table_.size();
}
inline void TPUEmbeddingOutputLayout::clear_table() {
  table_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* TPUEmbeddingOutputLayout::mutable_table(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingOutputLayout.table)
  return table_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor >*
TPUEmbeddingOutputLayout::mutable_table() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingOutputLayout.table)
  return &table_;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor& TPUEmbeddingOutputLayout::table(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.table)
  return table_.Get(index);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* TPUEmbeddingOutputLayout::add_table() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingOutputLayout.table)
  return table_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor >&
TPUEmbeddingOutputLayout::table() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingOutputLayout.table)
  return table_;
}

// repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor output = 2;
inline int TPUEmbeddingOutputLayout::output_size() const {
  return output_.size();
}
inline void TPUEmbeddingOutputLayout::clear_output() {
  output_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* TPUEmbeddingOutputLayout::mutable_output(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingOutputLayout.output)
  return output_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor >*
TPUEmbeddingOutputLayout::mutable_output() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingOutputLayout.output)
  return &output_;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor& TPUEmbeddingOutputLayout::output(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.output)
  return output_.Get(index);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* TPUEmbeddingOutputLayout::add_output() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingOutputLayout.output)
  return output_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor >&
TPUEmbeddingOutputLayout::output() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingOutputLayout.output)
  return output_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto
