Stack trace:
Frame         Function      Args
0007FFFFA110  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA110, 0007FFFF9010) msys-2.0.dll+0x1FE8E
0007FFFFA110  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3E8) msys-2.0.dll+0x67F9
0007FFFFA110  000210046832 (000210286019, 0007FFFF9FC8, 0007FFFFA110, 000000000000) msys-2.0.dll+0x6832
0007FFFFA110  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA110  000210068E24 (0007FFFFA120, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA3F0  00021006A225 (0007FFFFA120, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF93AC0000 ntdll.dll
7FFF91AB0000 KERNEL32.DLL
7FFF90EC0000 KERNELBASE.dll
7FFF93060000 USER32.dll
7FFF91820000 win32u.dll
000210040000 msys-2.0.dll
7FFF92A90000 GDI32.dll
7FFF91440000 gdi32full.dll
7FFF916B0000 msvcp_win.dll
7FFF90D70000 ucrtbase.dll
7FFF929D0000 advapi32.dll
7FFF939D0000 msvcrt.dll
7FFF92920000 sechost.dll
7FFF92C70000 RPCRT4.dll
7FFF901E0000 CRYPTBASE.DLL
7FFF91610000 bcryptPrimitives.dll
7FFF927C0000 IMM32.DLL
