Metadata-Version: 2.4
Name: bitsandbytes
Version: 0.45.5
Summary: k-bit optimizers and matrix multiplication routines.
Author-email: <PERSON> <<EMAIL>>
Maintainer-email: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
License: MIT License
        
        Copyright (c) Facebook, Inc. and its affiliates.
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.
        
Project-URL: homepage, https://github.com/bitsandbytes-foundation/bitsandbytes
Project-URL: changelog, https://github.com/bitsandbytes-foundation/bitsandbytes/blob/main/CHANGELOG.md
Project-URL: docs, https://huggingface.co/docs/bitsandbytes/main
Project-URL: issues, https://github.com/bitsandbytes-foundation/bitsandbytes/issues
Keywords: gpu,optimizers,optimization,8-bit,quantization,compression
Classifier: Development Status :: 4 - Beta
Classifier: License :: OSI Approved :: MIT License
Classifier: Environment :: GPU :: NVIDIA CUDA :: 11
Classifier: Environment :: GPU :: NVIDIA CUDA :: 12
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: C++
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
License-File: NOTICE.md
Requires-Dist: torch<3,>=2.0
Requires-Dist: numpy>=1.17
Provides-Extra: benchmark
Requires-Dist: pandas; extra == "benchmark"
Requires-Dist: matplotlib; extra == "benchmark"
Provides-Extra: docs
Requires-Dist: hf-doc-builder==0.5.0; extra == "docs"
Provides-Extra: dev
Requires-Dist: bitsandbytes[test]; extra == "dev"
Requires-Dist: build<2,>=1.0.0; extra == "dev"
Requires-Dist: ruff==0.9.6; extra == "dev"
Requires-Dist: pre-commit<4,>=3.5.0; extra == "dev"
Requires-Dist: wheel<1,>=0.42; extra == "dev"
Provides-Extra: test
Requires-Dist: einops~=0.8.0; extra == "test"
Requires-Dist: lion-pytorch==0.2.3; extra == "test"
Requires-Dist: pytest~=8.3; extra == "test"
Requires-Dist: scipy<2,>=1.10.1; python_version < "3.9" and extra == "test"
Requires-Dist: scipy<2,>=1.11.4; python_version >= "3.9" and extra == "test"
Requires-Dist: transformers<5,>=4.30.1; extra == "test"
Dynamic: license-file

# `bitsandbytes`

[![Downloads](https://static.pepy.tech/badge/bitsandbytes)](https://pepy.tech/project/bitsandbytes) [![Downloads](https://static.pepy.tech/badge/bitsandbytes/month)](https://pepy.tech/project/bitsandbytes) [![Downloads](https://static.pepy.tech/badge/bitsandbytes/week)](https://pepy.tech/project/bitsandbytes)

The `bitsandbytes` library is a lightweight Python wrapper around CUDA custom functions, in particular 8-bit optimizers, matrix multiplication (LLM.int8()), and 8 & 4-bit quantization functions.

The library includes quantization primitives for 8-bit & 4-bit operations, through `bitsandbytes.nn.Linear8bitLt` and `bitsandbytes.nn.Linear4bit` and 8-bit optimizers through `bitsandbytes.optim` module.

There are ongoing efforts to support further hardware backends, i.e. Intel CPU + GPU, AMD GPU, Apple Silicon, hopefully NPU.

**Please head to the official documentation page:**

**[https://huggingface.co/docs/bitsandbytes/main](https://huggingface.co/docs/bitsandbytes/main)**

## License

`bitsandbytes` is MIT licensed.

We thank Fabio Cannizzo for his work on [FastBinarySearch](https://github.com/fabiocannizzo/FastBinarySearch) which we use for CPU quantization.
