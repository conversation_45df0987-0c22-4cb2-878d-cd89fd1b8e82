#!/usr/bin/env python3
"""
🏸 羽毛球装备爬虫 (Badminton Equipment Crawler)

基于Selenium的智能羽毛球装备数据采集工具
专门用于从中羽在线网站获取详细的装备信息

主要功能:
- 自动获取装备链接
- 智能验证问题处理
- 全面的装备数据提取
- 入手价信息采集
- 反反爬虫技术

作者: 爬虫开发团队
版本: 2.1
更新时间: 2025-01-09
优化内容: 增强Windows兼容性和错误处理
"""

import time
import csv
import json
import re
import platform
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
from bs4 import BeautifulSoup
import random
from selenium.webdriver.common.keys import Keys
import os # Added for os.path.dirname

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BrowserBadmintonCrawler:
    def __init__(self, headless=False):  # 默认显示浏览器，便于调试
        self.base_url = "https://www.badmintoncn.com"
        self.equipment_data = []
        self.headless = headless
        self.driver = None
        self.platform = platform.system().lower()
        
        # 设置日志记录器
        self.logger = logging.getLogger(self.__class__.__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        
    def setup_driver(self):
        """设置Chrome浏览器驱动 - 增强Windows兼容性"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
            
            # 通用浏览器参数
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Windows特定优化
            if self.platform == 'windows':
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--disable-software-rasterizer')
                chrome_options.add_argument('--disable-extensions')
                chrome_options.add_argument('--disable-plugins')
                chrome_options.add_argument('--disable-images')  # 减少内存使用
                chrome_options.add_argument('--disable-javascript')  # 可选：禁用JS以提高稳定性
                chrome_options.add_argument('--window-size=1920,1080')
                # Windows下的User-Agent
                chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            else:
                # macOS/Linux的User-Agent
                chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 内存和性能优化（特别是Windows）
            chrome_options.add_argument('--memory-pressure-off')
            chrome_options.add_argument('--max_old_space_size=4096')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            
            # 尝试获取ChromeDriver - 增强错误处理
            try:
                # 使用webdriver_manager获取正确的chromedriver路径
                from webdriver_manager.chrome import ChromeDriverManager
                driver_path = ChromeDriverManager().install()
                # 确保路径指向chromedriver.exe，而不是THIRD_PARTY_NOTICES文件
                if "THIRD_PARTY_NOTICES" in driver_path:
                    # 尝试找到正确的chromedriver.exe
                    driver_dir = os.path.dirname(driver_path)
                    for root, dirs, files in os.walk(driver_dir):
                        for file in files:
                            if file.lower() == "chromedriver.exe":
                                driver_path = os.path.join(root, file)
                                break
                
                self.logger.info(f"✅ ChromeDriver路径: {driver_path}")
                service = Service(driver_path)
            except Exception as e:
                self.logger.error(f"❌ ChromeDriver下载失败: {e}")
                # 备用方案：尝试使用系统PATH中的chromedriver
                try:
                    service = Service()  # 使用系统默认路径
                    self.logger.info("🔄 使用系统默认ChromeDriver路径")
                except Exception as e2:
                    self.logger.error(f"❌ 系统ChromeDriver也不可用: {e2}")
                    return False
            
            # 创建WebDriver实例
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 设置超时
            self.driver.implicitly_wait(10)
            self.driver.set_page_load_timeout(30)
            
            # 移除webdriver属性（反检测）
            try:
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            except Exception:
                pass  # 某些情况下可能失败，但不影响主要功能
            
            self.logger.info(f"✅ Chrome浏览器启动成功 (平台: {self.platform})")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 启动浏览器失败: {e}")
            self.logger.error(f"   平台: {self.platform}")
            self.logger.error("   请确保已安装Chrome浏览器")
            if self.platform == 'windows':
                self.logger.error("   Windows用户请确保Chrome浏览器已添加到系统PATH")
            return False
    
    def safe_extract_field(self, extraction_func, field_name, default_value=""):
        """安全提取字段，失败时返回默认值"""
        try:
            result = extraction_func()
            if result is not None and str(result).strip():
                return result
            else:
                return default_value
        except Exception as e:
            self.logger.debug(f"⚠️ 提取字段 {field_name} 失败: {e}")
            return default_value

    def get_equipment_links_from_homepage(self):
        """从主页获取装备链接 - 增强错误处理"""
        try:
            self.logger.info("🌐 访问主页获取装备链接...")
            
            # 安全访问主页
            try:
                self.driver.get(self.base_url)
                
                # 等待页面加载完成
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except Exception as e:
                self.logger.error(f"❌ 访问主页失败: {e}")
                return []
            
            # 安全获取页面标题
            title = self.safe_extract_field(
                lambda: self.driver.title,
                'homepage_title'
            )
            if title:
                self.logger.info(f"页面标题: {title}")
            
            # 模拟人类行为 - 滚动页面
            try:
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
                time.sleep(2)
                self.driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(1)
            except Exception:
                pass  # 滚动失败不影响主要功能
            
            # 查找装备相关链接
            equipment_links = []
            
            # 查找所有包含view.php?eid=的链接
            def find_equipment_links():
                links = self.driver.find_elements(By.XPATH, "//a[contains(@href, 'view.php?eid=')]")
                self.logger.info(f"找到 {len(links)} 个装备链接")
                
                found_links = []
                for link in links:
                    try:
                        href = self.safe_extract_field(
                            lambda: link.get_attribute('href'),
                            'link_href'
                        )
                        text = self.safe_extract_field(
                            lambda: link.text.strip(),
                            'link_text'
                        )
                        
                        if href and 'eid=' in href:
                            # 提取设备ID
                            eid_match = re.search(r'eid=(\d+)', href)
                            if eid_match:
                                eid = eid_match.group(1)
                                found_links.append({
                                    'url': href,
                                    'text': text or f"装备_{eid}",
                                    'eid': eid
                                })
                    except Exception as e:
                        self.logger.debug(f"处理链接时出错: {e}")
                        continue
                
                return found_links
            
            equipment_links = self.safe_extract_field(find_equipment_links, 'equipment_links_search', [])
            
            # 如果没找到，尝试查找装备相关的导航链接
            if not equipment_links:
                self.logger.info("🔍 主页没有直接的装备链接，尝试查找装备页面入口...")
                
                equipment_nav_patterns = [
                    "//a[contains(@href, 'cbo_eq')]",
                    "//a[contains(text(), '装备')]",
                    "//a[contains(text(), '器材')]",
                    "//a[contains(text(), '球拍')]"
                ]
                
                def search_nav_links():
                    for pattern in equipment_nav_patterns:
                        try:
                            nav_links = self.driver.find_elements(By.XPATH, pattern)
                            if nav_links:
                                self.logger.info(f"找到 {len(nav_links)} 个装备相关导航链接")
                                
                                nav_link = nav_links[0]
                                nav_url = nav_link.get_attribute('href')
                                self.logger.info(f"尝试访问装备页面: {nav_url}")
                                
                                # 在新标签页中打开
                                self.driver.execute_script(f"window.open('{nav_url}', '_blank');")
                                self.driver.switch_to.window(self.driver.window_handles[-1])
                                time.sleep(3)
                                
                                # 在装备页面查找装备链接
                                equipment_page_links = self.driver.find_elements(By.XPATH, "//a[contains(@href, 'view.php?eid=')]")
                                
                                found_nav_links = []
                                for link in equipment_page_links:
                                    try:
                                        href = link.get_attribute('href')
                                        text = link.text.strip()
                                        
                                        if href and 'eid=' in href:
                                            eid_match = re.search(r'eid=(\d+)', href)
                                            if eid_match:
                                                eid = eid_match.group(1)
                                                found_nav_links.append({
                                                    'url': href,
                                                    'text': text or f"装备_{eid}",
                                                    'eid': eid
                                                })
                                    except Exception:
                                        continue
                                
                                # 关闭装备页面标签，回到主页
                                self.driver.close()
                                self.driver.switch_to.window(self.driver.window_handles[0])
                                
                                if found_nav_links:
                                    return found_nav_links
                                    
                        except Exception as e:
                            self.logger.debug(f"处理导航链接时出错: {e}")
                            continue
                    
                    return []
                
                nav_equipment_links = self.safe_extract_field(search_nav_links, 'nav_links_search', [])
                equipment_links.extend(nav_equipment_links)
            
            # 去重
            def deduplicate_links():
                unique_links = []
                seen_eids = set()
                for link in equipment_links:
                    if link['eid'] not in seen_eids:
                        seen_eids.add(link['eid'])
                        unique_links.append(link)
                return unique_links
            
            unique_links = self.safe_extract_field(deduplicate_links, 'deduplicate', [])
            
            self.logger.info(f"✅ 总共找到 {len(unique_links)} 个独特的装备链接")
            
            # 显示前几个链接
            for i, link in enumerate(unique_links[:5]):
                self.logger.info(f"  {i+1}. {link['text']} (eid: {link['eid']})")
            
            return unique_links
            
        except Exception as e:
            self.logger.error(f"❌ 获取主页装备链接失败: {e}")
            return []
    
    def extract_buy_price_info(self, equipment_id):
        """提取入手价信息 - 增强错误处理"""
        price_info = {}
        
        try:
            buy_price_url = f"https://www.badmintoncn.com/cbo_eq/view_buy.php?eid={equipment_id}"
            self.logger.info(f"💰 访问入手价页面: {buy_price_url}")
            
            # 在当前标签页打开入手价页面
            self.driver.get(buy_price_url)
            time.sleep(3)
            
            # 处理验证
            if "验证" in self.driver.title:
                self.logger.info("🔐 检测到验证页面，尝试解决...")
                if self.solve_verification(self.driver):
                    self.logger.info("✅ 入手价页面验证成功")
                    time.sleep(2)
                else:
                    self.logger.warning("❌ 入手价页面验证失败")
                    return price_info
            
            # 获取页面内容
            page_source = self.safe_extract_field(
                lambda: self.driver.page_source,
                'buy_price_page_source'
            )
            
            if page_source:
                def parse_price_page():
                    soup = BeautifulSoup(page_source, 'html.parser')
                    text_content = soup.get_text()
                    
                    # 提取入手价信息
                    patterns = {
                        'new_avg_price': [r'最近全新均价[：:\s]*[¥￥]?\s*(\d+)', r'全新均价[：:\s]*[¥￥]?\s*(\d+)'],
                        'used_avg_price': [r'最近二手均价[：:\s]*[¥￥]?\s*(\d+)', r'二手均价[：:\s]*[¥￥]?\s*(\d+)'],
                        'total_users': [r'总登记球友[：:\s]*(\d+)', r'登记球友[：:\s]*(\d+)']
                    }
                    
                    result = {}
                    for field, pattern_list in patterns.items():
                        for pattern in pattern_list:
                            matches = re.findall(pattern, text_content)
                            if matches:
                                result[field] = matches[0]
                                break
                    
                    return result
                
                extracted_info = self.safe_extract_field(parse_price_page, 'price_parsing')
                if extracted_info:
                    price_info.update(extracted_info)
            
        except Exception as e:
            self.logger.error(f"❌ 入手价信息提取失败: {str(e)}")
        
        return price_info
    
    def solve_verification(self, driver):
        """解决验证问题 - 增强错误处理"""
        try:
            time.sleep(2)
            
            # 安全获取验证问题文本
            verification_text = self.safe_extract_field(
                lambda: driver.find_element(By.TAG_NAME, "body").text.strip(),
                'verification_text'
            )
            
            if not verification_text or "问题" not in verification_text:
                return True  # 没有验证问题
            
            self.logger.info(f"🤖 验证问题: {verification_text}")
            
            answer = None
            
            # 数学计算问题
            if '=' in verification_text and any(op in verification_text for op in ['+', '-', '×', '*', '÷', '/']):
                def calculate_answer():
                    calculation_patterns = [
                        r'(\d+)\s*[+]\s*(\d+)',
                        r'(\d+)\s*[-]\s*(\d+)', 
                        r'(\d+)\s*[×*]\s*(\d+)',
                        r'(\d+)\s*[÷/]\s*(\d+)'
                    ]
                    
                    for pattern in calculation_patterns:
                        match = re.search(pattern, verification_text)
                        if match:
                            num1, num2 = int(match.group(1)), int(match.group(2))
                            
                            if '+' in verification_text:
                                return str(num1 + num2)
                            elif '-' in verification_text:
                                return str(num1 - num2)
                            elif any(op in verification_text for op in ['×', '*']):
                                return str(num1 * num2)
                            elif any(op in verification_text for op in ['÷', '/']):
                                return str(num1 // num2) if num2 != 0 else "0"
                    return None
                
                answer = self.safe_extract_field(calculate_answer, 'calculation')
                if answer:
                    self.logger.info(f"🧮 计算结果: {answer}")
            
            # 羽毛球知识问题
            elif "羽毛球有几根毛" in verification_text or "几根毛" in verification_text:
                answer = "16"
                self.logger.info("🏸 羽毛球知识: 羽毛球有16根毛")
            
            # 字母序列问题
            elif "ABC后3个大写字母" in verification_text:
                answer = "DEF"
                self.logger.info("🔤 字母序列: ABC后3个字母是 DEF")
            elif "XYZ前3个大写字母" in verification_text:
                answer = "UVW"
                self.logger.info("🔤 字母序列: XYZ前3个字母是 UVW")
            
            if answer:
                # 查找输入框并输入答案
                def submit_answer():
                    input_selectors = [
                        'input[type="text"]',
                        'input[name*="answer"]',
                        'input[id*="answer"]',
                        'input.form-control'
                    ]
                    
                    for selector in input_selectors:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            input_element = elements[0]
                            input_element.clear()
                            input_element.send_keys(answer)
                            self.logger.info(f"✍️ 输入验证答案: {answer}")
                            
                            input_element.send_keys(Keys.RETURN)
                            time.sleep(3)
                            return True
                    return False
                
                return self.safe_extract_field(submit_answer, 'answer_submission', False)
            
        except Exception as e:
            self.logger.warning(f"⚠️ 验证处理失败: {e}")
        
        return False
    
    def crawl_equipment_details(self, equipment_url):
        """爬取单个装备详情 - 增强错误处理"""
        equipment_data = {
            'url': equipment_url,
            'crawl_time': datetime.now().isoformat(),
            'title': '',
            'brand': '',
            'series': '',
            'msrp_price': '',
            'name': '',
            'description': '',
            'weight': '',
            'balance': '',
            'shaft_material': '',
            'shaft_diameter': '',
            'technology': '',
            'user_tags': '',
            'rating': '',
            'equipment_intro': '',
            'new_avg_price': '',
            'used_avg_price': '',
            'total_users': ''
        }
        
        try:
            self.logger.info(f"🏸 访问装备详情页: {equipment_url}")
            
            # 从URL中提取eid
            eid = self.safe_extract_field(
                lambda: re.search(r'eid=(\d+)', equipment_url).group(1) if re.search(r'eid=(\d+)', equipment_url) else None,
                'eid'
            )
            if eid:
                self.logger.info(f"📋 提取到eid: {eid}")
            
            # 在新标签页中打开装备详情页
            try:
                self.driver.execute_script(f"window.open('{equipment_url}', '_blank');")
                self.driver.switch_to.window(self.driver.window_handles[-1])
                time.sleep(3)
            except Exception as e:
                self.logger.error(f"❌ 打开新标签页失败: {e}")
                return equipment_data
            
            # 处理可能的验证
            try:
                if not self.solve_verification(self.driver):
                    self.logger.warning(f"⚠️ 验证处理失败，继续尝试提取数据")
            except Exception as e:
                self.logger.warning(f"⚠️ 验证处理异常: {e}")
            
            # 安全提取页面标题
            equipment_data['title'] = self.safe_extract_field(
                lambda: self.driver.title,
                'title'
            )
            if equipment_data['title']:
                self.logger.info(f"页面标题: {equipment_data['title']}")
            
            # 获取页面源码并解析
            page_source = self.safe_extract_field(
                lambda: self.driver.page_source,
                'page_source'
            )
            
            if not page_source:
                self.logger.error("❌ 无法获取页面源码")
                self._safe_close_tab()
                return equipment_data
            
            try:
                soup = BeautifulSoup(page_source, 'html.parser')
                clean_text = soup.get_text()
            except Exception as e:
                self.logger.error(f"❌ 页面解析失败: {e}")
                self._safe_close_tab()
                return equipment_data
            
            # 1. 从表格中提取结构化数据
            self._extract_table_data(soup, equipment_data)
            
            # 2. 提取产品名称
            self._extract_product_name(equipment_data)
            
            # 3. 提取描述信息
            self._extract_description(soup, clean_text, equipment_data)
            
            # 4. 提取技术参数
            self._extract_technical_params(clean_text, equipment_data)
            
            # 5. 提取价格信息
            self._extract_price_info(soup, clean_text, equipment_data)
            
            # 6. 提取用户评价和评分
            self._extract_user_ratings(soup, clean_text, equipment_data)
            
            # 7. 提取装备简介
            self._extract_equipment_intro(soup, clean_text, equipment_data)
            
            # 8. 提取入手价信息
            if eid:
                self._extract_buy_price_info_safely(eid, equipment_data)
            
            # 关闭当前标签页
            self._safe_close_tab()
            
            # 记录成功提取
            extracted_fields = [k for k, v in equipment_data.items() if v and v != '']
            self.logger.info(f"✅ 成功提取装备信息: {equipment_data.get('name', equipment_data.get('title', 'Unknown'))}")
            self.logger.info(f"📊 提取字段数: {len(extracted_fields)}/{len(equipment_data)}")
            
            return equipment_data
            
        except Exception as e:
            self.logger.error(f"❌ 爬取装备详情失败 {equipment_url}: {e}")
            self._safe_close_tab()
            return equipment_data
    
    def _safe_close_tab(self):
        """安全关闭当前标签页"""
        try:
            if len(self.driver.window_handles) > 1:
                self.driver.close()
                self.driver.switch_to.window(self.driver.window_handles[0])
        except Exception as e:
            self.logger.debug(f"关闭标签页时出错: {e}")
    
    def _extract_table_data(self, soup, equipment_data):
        """从表格中提取结构化数据"""
        def extract_from_tables():
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if key == '装备品牌':
                            equipment_data['brand'] = value
                        elif key == '装备系列':
                            equipment_data['series'] = value
                        elif key == '吊 牌 价':
                            price_match = re.search(r'(\d+)', value)
                            if price_match:
                                equipment_data['msrp_price'] = price_match.group(1)
        
        self.safe_extract_field(extract_from_tables, 'table_data')
    
    def _extract_product_name(self, equipment_data):
        """提取产品名称"""
        def extract_name():
            if equipment_data.get('title'):
                title = equipment_data['title']
                clean_name = title
                remove_patterns = [
                    r'羽毛球拍.*',
                    r'中羽在线.*',
                    r'badmintoncn\.com.*',
                    r'验证.*'
                ]
                for pattern in remove_patterns:
                    clean_name = re.sub(pattern, '', clean_name).strip()
                
                if clean_name and clean_name != title:
                    return clean_name
            return ''
        
        equipment_data['name'] = self.safe_extract_field(extract_name, 'product_name')
    
    def _extract_description(self, soup, clean_text, equipment_data):
        """提取描述信息"""
        def extract_desc():
            # 查找面包屑导航
            breadcrumb_patterns = [
                r'(首页\s*＞[^综]*?)综述介绍',
                r'(首页\s*＞[^参]*?)参\s*数',
                r'(首页\s*＞[^图]*?)图\s*库'
            ]
            
            for pattern in breadcrumb_patterns:
                breadcrumb_match = re.search(pattern, clean_text, re.DOTALL)
                if breadcrumb_match:
                    breadcrumb_raw = breadcrumb_match.group(1)
                    breadcrumb_lines = []
                    for line in breadcrumb_raw.split('\n'):
                        line = line.strip()
                        if line and '＞' in line:
                            breadcrumb_lines.append(line)
                    if breadcrumb_lines:
                        return '\n'.join(breadcrumb_lines)
            return ''
        
        equipment_data['description'] = self.safe_extract_field(extract_desc, 'description')
    
    def _extract_technical_params(self, clean_text, equipment_data):
        """提取技术参数"""
        enhanced_patterns = {
            'weight': [
                r'重量[：:\s]*([0-9]+(?:\.[0-9]+)?)\s*[gG克]',
                r'Weight[：:\s]*([0-9]+(?:\.[0-9]+)?)\s*[gG]'
            ],
            'balance': [
                r'平衡点[：:\s]*([0-9]+(?:\.[0-9]+)?)\s*[mM毫米]*',
                r'平衡[：:\s]*([0-9]+(?:\.[0-9]+)?)\s*[mM毫米]*'
            ],
            'shaft_material': [
                r'中管.*?([碳硼][^，。\n]*纤维[^，。\n]*)',
                r'杆.*?([碳硼][^，。\n]*纤维[^，。\n]*)'
            ],
            'shaft_diameter': [
                r'中管.*?([0-9\.]+mm)',
                r'杆.*?([0-9\.]+mm)'
            ],
            'technology': [
                r'(二次成型|三维编织|纳米|T[0-9]+|高弹性|抗扭|减震)[^，。\n]*'
            ]
        }
        
        for param, patterns in enhanced_patterns.items():
            def extract_param(param_patterns=patterns):
                for pattern in param_patterns:
                    matches = re.findall(pattern, clean_text, re.IGNORECASE)
                    if matches:
                        value = matches[0].strip()
                        if value and len(value) < 50:
                            return value
                return ''
            
            equipment_data[param] = self.safe_extract_field(extract_param, param)
    
    def _extract_price_info(self, soup, clean_text, equipment_data):
        """提取价格信息"""
        # 提取全新均价
        def extract_new_price():
            for pattern in [r'最近全新均价[：:\s]*[¥￥]?\s*(\d+)', r'全新均价[：:\s]*[¥￥]?\s*(\d+)']:
                matches = re.findall(pattern, clean_text)
                if matches:
                    return matches[0]
            return ''
        
        # 提取二手均价
        def extract_used_price():
            for pattern in [r'最近二手均价[：:\s]*[¥￥]?\s*(\d+)', r'二手均价[：:\s]*[¥￥]?\s*(\d+)']:
                matches = re.findall(pattern, clean_text)
                if matches:
                    return matches[0]
            return ''
        
        # 提取登记球友数
        def extract_total_users():
            for pattern in [r'总登记球友[：:\s]*(\d+)', r'登记球友[：:\s]*(\d+)']:
                matches = re.findall(pattern, clean_text)
                if matches:
                    return matches[0]
            return ''
        
        equipment_data['new_avg_price'] = self.safe_extract_field(extract_new_price, 'new_avg_price')
        equipment_data['used_avg_price'] = self.safe_extract_field(extract_used_price, 'used_avg_price')
        equipment_data['total_users'] = self.safe_extract_field(extract_total_users, 'total_users')
    
    def _extract_user_ratings(self, soup, clean_text, equipment_data):
        """提取用户评价和评分"""
        # 提取评分
        def extract_rating():
            rating_match = re.search(r'([0-9]+\.?[0-9]*)\s*中羽评分', clean_text)
            if rating_match:
                return rating_match.group(1)
            return ''
        
        # 提取用户标签
        def extract_tags():
            tag_elements = soup.find_all('div', class_='album')
            if tag_elements:
                tags = []
                for tag_elem in tag_elements:
                    tag_text = tag_elem.get_text(strip=True)
                    if tag_text and len(tag_text) < 50:
                        tags.append(tag_text)
                if tags:
                    return ', '.join(tags)
            return ''
        
        equipment_data['rating'] = self.safe_extract_field(extract_rating, 'rating')
        equipment_data['user_tags'] = self.safe_extract_field(extract_tags, 'user_tags')
    
    def _extract_equipment_intro(self, soup, clean_text, equipment_data):
        """提取装备简介"""
        def extract_intro():
            intro_match = re.search(r'装备简介([^装]+?)(?:装备|$)', clean_text, re.DOTALL)
            if intro_match:
                intro_content = intro_match.group(1).strip()
                intro_content = re.sub(r'\s+', ' ', intro_content)
                return intro_content[:500] if len(intro_content) > 500 else intro_content
            return ''
        
        equipment_data['equipment_intro'] = self.safe_extract_field(extract_intro, 'equipment_intro')
    
    def _extract_buy_price_info_safely(self, eid, equipment_data):
        """安全提取入手价信息"""
        try:
            self.logger.info(f"🔄 开始提取入手价信息 (eid: {eid})")
            price_info = self.extract_buy_price_info(eid)
            
            if price_info:
                # 安全更新价格信息
                for key, value in price_info.items():
                    if value and str(value).strip():
                        equipment_data[key] = value
                self.logger.info(f"✅ 入手价信息已合并")
            else:
                self.logger.warning(f"⚠️ 未获取到入手价信息")
                
        except Exception as e:
            self.logger.error(f"❌ 入手价信息提取失败: {str(e)}")

    def save_to_csv(self, data, filename=None):
        """保存数据到CSV文件 - 增强错误处理"""
        if not data:
            self.logger.warning("⚠️ 没有数据可保存")
            return None
        
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"browser_crawl_{timestamp}.csv"
            
            # 定义所有可能的字段
            fieldnames = [
                'url', 'crawl_time', 'title', 'brand', 'series', 'msrp_price',
                'name', 'description', 'weight', 'balance', 'shaft_material',
                'shaft_diameter', 'technology', 'user_tags', 'rating',
                'equipment_intro', 'new_avg_price', 'used_avg_price', 'total_users'
            ]
            
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for item in data:
                    # 确保所有字段都存在，缺失的设为空字符串
                    row = {}
                    for field in fieldnames:
                        row[field] = item.get(field, '')
                    writer.writerow(row)
            
            self.logger.info(f"💾 数据已保存到 {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"❌ 保存CSV文件失败: {e}")
            return None
    
    def run(self, max_pages=None):
        """运行爬虫 - 增强错误处理和稳定性"""
        try:
            self.logger.info("🚀 开始运行浏览器爬虫...")
            
            # 启动浏览器
            if not self.setup_driver():
                self.logger.error("❌ 浏览器启动失败，程序退出")
                return
            
            # 获取装备链接
            equipment_links = self.get_equipment_links_from_homepage()
            
            if not equipment_links:
                self.logger.error("❌ 未找到任何装备链接")
                self.quit()
                return
            
            # 限制爬取数量（如果指定了max_pages）
            if max_pages is not None:
                equipment_links = equipment_links[:max_pages]
                self.logger.info(f"📋 准备爬取 {len(equipment_links)} 个装备页面")
            else:
                self.logger.info(f"📋 准备爬取所有 {len(equipment_links)} 个装备页面")
            
            # 爬取装备详情
            successful_count = 0
            for i, link in enumerate(equipment_links):
                try:
                    self.logger.info(f"🔄 进度: {i+1}/{len(equipment_links)} - {link.get('text', 'Unknown')}")
                    
                    equipment_data = self.crawl_equipment_details(link['url'])
                    
                    if equipment_data and any(v for v in equipment_data.values() if v and v.strip()):
                        self.equipment_data.append(equipment_data)
                        successful_count += 1
                        self.logger.info(f"✅ 成功: {link.get('text', 'Unknown')}")
                    else:
                        self.logger.warning(f"❌ 失败: {link.get('text', 'Unknown')}")
                    
                    # 随机等待，避免请求过快
                    wait_time = random.uniform(2.0, 5.0)
                    self.logger.info(f"⏱️ 等待 {wait_time:.1f} 秒...")
                    time.sleep(wait_time)
                    
                except Exception as e:
                    self.logger.error(f"❌ 处理装备 {link.get('text', 'Unknown')} 时出错: {e}")
                    continue
            
            # 保存数据
            if self.equipment_data:
                filename = self.save_to_csv(self.equipment_data)
                if filename:
                    self.logger.info(f"🎉 爬虫完成！成功获取 {successful_count} 个装备信息")
                    self.logger.info(f"📊 数据文件: {filename}")
                    
                    # 显示样本数据
                    self.logger.info("\n📋 样本数据:")
                    for i, item in enumerate(self.equipment_data[:3]):
                        name = item.get('name') or item.get('title', 'Unknown')
                        brand = item.get('brand', '未知品牌')
                        self.logger.info(f"  {i+1}. {name} - {brand}")
            else:
                self.logger.warning("⚠️ 没有成功获取任何装备信息")
            
        except Exception as e:
            self.logger.error(f"❌ 爬虫运行异常: {e}")
        finally:
            self.quit()
    
    def quit(self):
        """安全退出"""
        try:
            if self.driver:
                self.driver.quit()
                self.logger.info("🔄 浏览器已关闭")
        except Exception as e:
            self.logger.debug(f"关闭浏览器时出错: {e}")


def main():
    """主函数 - 支持多平台运行"""
    try:
        # 检测运行平台
        current_platform = platform.system().lower()
        print(f"🖥️ 检测到运行平台: {current_platform}")
        
        if current_platform == 'windows':
            print("💡 Windows用户提示:")
            print("   - 请确保已安装Chrome浏览器")
            print("   - 如果出现驱动问题，请检查Chrome版本")
            print("   - 建议关闭杀毒软件的实时保护")
        
        # 创建爬虫实例（headless=False 可以看到浏览器操作过程）
        crawler = BrowserBadmintonCrawler(headless=False)
        
        # 运行爬虫，默认爬取5个装备
        crawler.run(max_pages=100)
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断程序")
    except Exception as e:
        print(f"❌ 程序运行错误: {e}")
        if platform.system().lower() == 'windows':
            print("💡 Windows用户故障排除:")
            print("   - 请确保Chrome浏览器已正确安装")
            print("   - 尝试以管理员身份运行")
            print("   - 检查防火墙和杀毒软件设置")


if __name__ == "__main__":
    main() 