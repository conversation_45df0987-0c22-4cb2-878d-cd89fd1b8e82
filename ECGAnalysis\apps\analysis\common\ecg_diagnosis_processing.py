import traceback
import numpy as np
from biosppy.signals import ecg
from scipy import stats
from scipy.fftpack import fft
from scipy.signal import find_peaks
from scipy.stats import iqr
import pywt  # 引入小波变换库
from apps.analysis.common import ecg_qrs_processing
from apps.utils.logger_helper import Logger
from apps.analysis.common import ecg_llm_processing
from scipy.signal import butter, filtfilt, welch


class ECGProcessor:
    def __init__(self, ecg_data, sampling_rate):
        """
        初始化ECG处理器

        Args:
            ecg_data: ECG信号数据
            sampling_rate: 采样率
        """
        self.ecg_data = ecg_data
        self.sampling_rate = sampling_rate
        # 在初始化时进行信号预处理，避免重复处理
        self.preprocessed_signal = self.preprocess_signal(self.ecg_data)

    def assess_signal_quality(self):
        # 计算信号的标准差、RMS、零交叉率
        std_val = np.std(self.ecg_data)
        rms_val = np.sqrt(np.mean(np.square(self.ecg_data)))
        zero_crossings = np.where(np.diff(np.sign(self.ecg_data)))[0]
        zcr = len(zero_crossings)

        # 对信号进行FFT变换，评估频谱中的噪声
        ecg_fft = fft(self.ecg_data)
        freqs = np.fft.fftfreq(len(ecg_fft), 1 / self.sampling_rate)

        # 计算高频成分的能量
        high_freq_energy = np.sum(np.abs(ecg_fft[(freqs > 40)]))

        # 设置阈值
        high_freq_energy_threshold = 5000  # 高频能量阈值
        std_threshold = 0.1  # 修改标准差阈值为0.1
        rms_threshold = 0.1  # 修改RMS阈值为0.1
        zcr_threshold = 600  # 根据经验或数据调整ZCR阈值为600（或移除这个判断）

        # 根据高频能量判断噪声水平
        if high_freq_energy > high_freq_energy_threshold:
            Logger().error(f"Signal quality: Poor - High frequency noise detected (Energy: {high_freq_energy})")
            return "Poor signal quality: High frequency noise"

        # 根据其他信号特征评估信号质量
        if std_val > std_threshold or rms_val > rms_threshold or zcr > zcr_threshold:
            Logger().error(f"Signal quality: Poor - Signal parameters (std: {std_val}, RMS: {rms_val}, ZCR: {zcr})")
            return "Poor signal quality: Abnormal signal parameters"

        # 信号质量良好
        return "Good signal quality"

    def preprocess(self):
        # 这里可以添加滤波、去噪等预处理步骤
        pass

    def detect_peaks_by_find(self, threshold=None, min_distance=None):
        # 使用find_peaks检测QRS波群
        peaks, _ = find_peaks(self.ecg_data, height=threshold, distance=min_distance)
        return peaks

    def detect_peaks_by_bios(self):
        """使用预处理后的信号进行QRS检测"""
        try:
            bios_peaks = ecg.ecg(signal=self.preprocessed_signal, sampling_rate=self.sampling_rate, show=False)
            return bios_peaks
        except Exception as e:
            raise BiosppyEcgError() from e

    def get_ecg_feature(self, bios_peaks):
        # 获取ECG特征，如QRS波群位置等
        qout = ecg.getQPositions(bios_peaks, show=False)
        sout = ecg.getSPositions(bios_peaks, show=False)
        pout = ecg.getPPositions(bios_peaks, show=False)
        tout = ecg.getTPositions(bios_peaks, show=False)
        q_indices_hamilton = qout['Q_positions']
        s_indices_hamilton = sout['S_positions']
        rpeaks_hamilton = bios_peaks['rpeaks']
        return qout, sout, pout, tout, q_indices_hamilton, s_indices_hamilton, rpeaks_hamilton

    def cal_rr(self, rpeaks):
        rr_intervals = np.diff(rpeaks) / self.sampling_rate
        return rr_intervals

    def cal_SNA(self, rr_intervals, bios_peaks=None):
        """计算窦性心律不齐（SNA）"""
        try:
            if rr_intervals is None:
                rr_intervals = self.get_rr_intervals(bios_peaks)

            if bios_peaks is None:
                bios_peaks = self.detect_peaks_by_bios()

            # 确保有足够的数据进行分析
            if rr_intervals is None or len(rr_intervals) < 6:
                Logger().warning("Insufficient RR intervals for SNA analysis")
                return {}, False

            # 使用预处理后的信号进行分析
            if bios_peaks is None:
                r_peaks = ecg.ecg(signal=self.preprocessed_signal, sampling_rate=self.sampling_rate, show=False)[
                    'rpeaks']
                rr_intervals = np.diff(r_peaks) / self.sampling_rate

            # 1. 分析RR间期特征
            mean_rr = np.mean(rr_intervals)
            std_rr = np.std(rr_intervals)
            cv_rr = float(std_rr / mean_rr) if mean_rr > 0 else 0.0

            # 2. 计算其他特征
            rr_diff = np.diff(rr_intervals)
            rmssd = np.sqrt(np.mean(np.square(rr_diff)))
            pnn50 = len(np.where(np.abs(rr_diff) > 0.05)[0]) / len(rr_diff) * 100 if len(rr_diff) > 0 else 0.0

            # 3. 计算相邻RR间期变化率
            rr_changes = np.abs(np.diff(rr_intervals) / rr_intervals[:-1] * 100)
            max_change = np.max(rr_changes) if len(rr_changes) > 0 else 0.0
            mean_change = np.mean(rr_changes) if len(rr_changes) > 0 else 0.0

            # 4. 计算Lorenz图特征
            rr_n = rr_intervals[:-1]
            rr_n1 = rr_intervals[1:]
            sd1 = np.std(np.subtract(rr_n, rr_n1)) / np.sqrt(2)
            sd2 = np.std(np.add(rr_n, rr_n1)) / np.sqrt(2)
            sd_ratio = sd1 / sd2 if sd2 != 0 else 0

            # 5. 计算连续变化趋势
            consecutive_changes = np.sum(np.abs(np.diff(np.sign(np.diff(rr_intervals)))))

            def advanced_scoring():
                try:
                    score = 0

                    # 1. CV_RR分析
                    if 0.06 <= cv_rr < 0.10:
                        score += 1
                    elif 0.10 <= cv_rr < 0.15:
                        score += 2
                    elif cv_rr >= 0.15:
                        score += 3

                    # 2. RR间期连续性分析
                    rr_diffs = np.abs(np.diff(rr_intervals))
                    consecutive_large_changes = np.sum(rr_diffs > 0.12)
                    if consecutive_large_changes >= 3:
                        score += 2

                    # 3. 频域分析
                    if len(rr_intervals) >= 5:
                        try:
                            freqs, psd = welch(rr_intervals, fs=1.0, nperseg=min(len(rr_intervals), 256))
                            lf_power = np.sum(psd[(freqs >= 0.04) & (freqs <= 0.15)])
                            hf_power = np.sum(psd[(freqs >= 0.15) & (freqs <= 0.4)])
                            lf_hf_ratio = lf_power / hf_power if hf_power > 0 else 0

                            if lf_hf_ratio > 2.5:
                                score += 2
                        except Exception as e:
                            Logger().warning(f"Error in frequency domain analysis: {str(e)}")

                    # 4. Poincaré分析
                    if sd_ratio >= 0.3 and sd_ratio <= 0.7:
                        score += 0
                    elif sd_ratio > 0.7:
                        score += 2

                    # 5. 时间域指标
                    if pnn50 >= 15 and rmssd >= 0.07:
                        score += 2

                    return score
                except Exception as e:
                    Logger().error(f"Error in advanced_scoring: {str(e)}")
                    return 0

            score = advanced_scoring()

            sna_patterns = {
                'mean_rr': float(mean_rr),
                'std_rr': float(std_rr),
                'cv_rr': float(cv_rr),
                'rmssd': float(rmssd),
                'pnn50': float(pnn50),
                'max_change': float(max_change),
                'mean_change': float(mean_change),
                'sd1': float(sd1),
                'sd2': float(sd2),
                'sd_ratio': float(sd_ratio),
                'consecutive_changes': float(consecutive_changes),
                'score': float(score)
            }

            # 判定标准
            is_sna = score >= 7

            # 排除条件
            if is_sna:
                if max_change > 50 or cv_rr > 0.25:
                    is_sna = False

                mean_hr = 60 / mean_rr
                if mean_hr > 100 or mean_hr < 50:
                    is_sna = False

            return sna_patterns, bool(is_sna)

        except Exception as e:
            Logger().error(f"Error in cal_SNA: {str(e)}")
            return {}, False

    def cal_SNT_SNB(self, rr_intervals):
        SNT, SNB = False, False
        hr = int(60 / np.mean(rr_intervals)) if len(rr_intervals) > 0 else 0
        if hr > 100:
            SNT = True
        elif hr < 60:
            SNB = True
        return SNT, SNB

    def cal_AF(self, rr_stats, std_threshold=0.05, cv_threshold=5, mean_threshold=1.3, iqr_threshold=0.02):
        """房颤检测函数"""
        try:
            # 使用已预处理的信号进行QRS检测
            bios_peaks = ecg.ecg(signal=self.preprocessed_signal, sampling_rate=self.sampling_rate, show=False)

            # 获取特征点
            qout, sout, pout, tout, q_indices_hamilton, s_indices_hamilton, rpeaks_hamilton = self.get_ecg_feature(
                bios_peaks)

            # 计算R波和P波的差异
            rp_diff = len(rpeaks_hamilton) - len(pout['P_end_positions'])

            # 房颤判断条件
            if rp_diff > 4:  # P波缺失条件
                # RR间期特征判断
                if (rr_stats['std'] > std_threshold and
                        rr_stats['cv'] > cv_threshold and
                        rr_stats['iqr'] > iqr_threshold and
                        rr_stats['mean'] < mean_threshold):
                    return True

            return False

        except Exception as e:
            Logger().error(f"Error in cal_AF: {str(e)}")
            return False

    def _analyze_p_wave_morphology(self, ecg_data, p_positions):
        """分析P波形态特征"""
        if len(p_positions) < 2:
            return True  # P波异常

        p_wave_heights = []
        p_wave_widths = []
        window = 20  # P波分析窗口大小

        for p_pos in p_positions:
            if p_pos - window >= 0 and p_pos + window < len(ecg_data):
                p_segment = ecg_data[p_pos - window:p_pos + window]
                p_wave_heights.append(np.max(p_segment) - np.min(p_segment))
                # 计算P波宽度（简化版）
                p_wave_widths.append(np.sum(p_segment > np.mean(p_segment)))

        # 判断P波的一致性
        height_cv = np.std(p_wave_heights) / np.mean(p_wave_heights) if p_wave_heights else float('inf')
        width_cv = np.std(p_wave_widths) / np.mean(p_wave_widths) if p_wave_widths else float('inf')

        return height_cv > 0.5 or width_cv > 0.5  # P波形态不规则

    def _bandpass_filter(self, ecg_data, lowcut=0.5, highcut=40, order=4):
        """带通滤波"""
        try:
            nyquist = 0.5 * self.sampling_rate
            low = lowcut / nyquist
            high = highcut / nyquist

            # 确保频率在有效范围内
            low = max(0.001, min(0.999, low))
            high = max(0.001, min(0.999, high))

            # 确保高通频率大于低通频率
            if low >= high:
                high = min(0.999, low + 0.001)

            b, a = butter(order, [low, high], btype='band')
            return filtfilt(b, a, ecg_data)
        except Exception as e:
            Logger().error(f"Error in _bandpass_filter: {str(e)}")
            return ecg_data

    def _remove_baseline_drift(self, ecg_data, cutoff=0.5, order=4):
        """去除基线漂移"""
        try:
            nyquist = 0.5 * self.sampling_rate
            high = cutoff / nyquist

            # 确保频率在有效范围内
            high = max(0.001, min(0.999, high))

            b, a = butter(order, high, btype='high')
            return filtfilt(b, a, ecg_data)
        except Exception as e:
            Logger().error(f"Error in _remove_baseline_drift: {str(e)}")
            return ecg_data

    def _wavelet_denoise(self, ecg_data, wavelet='sym8', level=5, threshold=0.1):
        """小波去噪"""
        try:
            coeffs = pywt.wavedec(ecg_data, wavelet, level=level)
            denoised_coeffs = [coeffs[0]] + [
                pywt.threshold(c, threshold, mode='soft') for c in coeffs[1:]
            ]
            return pywt.waverec(denoised_coeffs, wavelet)
        except Exception as e:
            Logger().error(f"Error in _wavelet_denoise: {str(e)}")
            return ecg_data

    def preprocess_signal(self, ecg_data):
        """预处理ECG信号
        包含带通滤波、基线漂移去除和小波去噪
        """
        try:
            # 带通滤波（调整频率范围）
            filtered_data = self._bandpass_filter(ecg_data, lowcut=0.5, highcut=40)
            # 去除基线漂移
            baseline_removed = self._remove_baseline_drift(filtered_data, cutoff=0.5)
            # 小波去噪
            denoised_data = self._wavelet_denoise(baseline_removed)
            return denoised_data
        except Exception as e:
            Logger().error(f"Error in preprocess_signal: {str(e)}")
            return ecg_data

    def get_rr_intervals(self, bios_peaks=None):
        """获取RR间期"""
        try:
            if bios_peaks is None:
                bios_peaks = self.detect_peaks_by_bios()
            if bios_peaks and 'rpeaks' in bios_peaks:
                return np.diff(bios_peaks['rpeaks']) / self.sampling_rate
            return None
        except Exception as e:
            Logger().error(f"Error in get_rr_intervals: {str(e)}")
            return None

    def cal_PVC(self, rr_intervals):
        """计算室性早搏（PVC）的检测逻辑"""
        try:
            if rr_intervals is None or len(rr_intervals) < 3:
                return {'has_pvc': False, 'count': 0, 'frequency': 0.0, 'patterns': []}

            # 检查RR间期数量条件
            if not (6 < len(rr_intervals) < 17):
                return {'has_pvc': False, 'count': 0, 'frequency': 0.0, 'patterns': []}

            # 使用已预处理的信号进行QRS检测
            try:
                bios_peaks = ecg.ecg(signal=self.preprocessed_signal, sampling_rate=self.sampling_rate, show=False)
            except Exception as e:
                Logger().error(f"QRS检测错误: {str(e)}")
                return {'has_pvc': False, 'count': 0, 'frequency': 0.0, 'patterns': []}

            qout, sout, pout, tout, q_indices_hamilton, s_indices_hamilton, rpeaks_hamilton = self.get_ecg_feature(
                bios_peaks)
            rpeaks = bios_peaks['rpeaks']

            median_rr = np.median(rr_intervals)
            window_size = int(0.2 * self.sampling_rate)  # 使用更大的窗口以确保捕获完整的QRS波形

            pvc_count = 0
            patterns = []
            consecutive_count = 0
            last_pvc_idx = -2

            for i in range(1, len(rr_intervals) - 1):
                try:
                    current_qrs = self.preprocessed_signal[rpeaks[i] - window_size:rpeaks[i] + window_size]
                    if len(current_qrs) != 2 * window_size:
                        continue

                    # RR间期特征
                    current_rr = rr_intervals[i]
                    next_rr = rr_intervals[i + 1]
                    is_premature = current_rr < 0.85 * median_rr  # 提前出现特征
                    has_compensation = next_rr > 1.1 * median_rr  # 代偿性间歇特征

                    # 振幅特征
                    current_r_peak = np.max(np.abs(current_qrs))
                    surrounding_beats = []
                    for j in range(max(0, i - 3), min(len(rpeaks), i + 4)):
                        if j != i:  # 排除当前心拍
                            beat = self.preprocessed_signal[rpeaks[j] - window_size:rpeaks[j] + window_size]
                            if len(beat) == 2 * window_size:
                                surrounding_beats.append(np.max(np.abs(beat)))

                    if len(surrounding_beats) >= 2:
                        # 计算R波幅值差异
                        sorted_amplitudes = sorted(surrounding_beats, reverse=True)
                        max_diff_ratio = (current_r_peak - sorted_amplitudes[0]) / sorted_amplitudes[0]
                        second_max_diff_ratio = (sorted_amplitudes[0] - sorted_amplitudes[1]) / sorted_amplitudes[1]
                        has_significant_amplitude_diff = max_diff_ratio > 0.4 or second_max_diff_ratio > 0.3
                        is_high_amplitude = current_r_peak > 1.3 * np.median(surrounding_beats)
                    else:
                        has_significant_amplitude_diff = False
                        is_high_amplitude = False

                    # 形态特征
                    peaks_count = len(find_peaks(current_qrs)[0])
                    valleys_count = len(find_peaks(-current_qrs)[0])
                    has_abnormal_peaks = (peaks_count + valleys_count) > 4

                    # 检查R波检测条件
                    if has_abnormal_peaks:
                        # 计算R波幅值
                        r_peak_amplitudes = [np.max(self.preprocessed_signal[rpeaks[j] - window_size:rpeaks[j] + window_size]) for j in range(len(rpeaks))]
                        
                        # 计算R波的均值和标准差
                        mean_amplitude = np.mean(r_peak_amplitudes)
                        std_amplitude = np.std(r_peak_amplitudes)
                        
                        # 设置动态阈值
                        threshold = mean_amplitude + std_amplitude  # 可以根据需要调整倍数
                        
                        # 添加调试信息
                        Logger().info(f"R波幅值均值: {mean_amplitude}, 标准差: {std_amplitude}, 动态阈值: {threshold}")
                        
                        # 检测异常R波
                        for j in range(len(r_peak_amplitudes)):
                            if np.abs(r_peak_amplitudes[j] - mean_amplitude) > threshold:
                                Logger().warning(f"异常R波检测: R波索引 {j}, 幅值 {r_peak_amplitudes[j]}")
                                # 可以在这里添加逻辑来处理异常R波

                    # 调整后的评分系统
                    score = sum([
                        is_premature * 2.5,  # RR间期提前的权重
                        has_compensation * 1.5,  # 代偿性间歇的权重
                        is_high_amplitude * 2.0,  # 振幅特征的权重
                        has_significant_amplitude_diff * 2.0,  # 振幅差异的权重
                        has_abnormal_peaks * 1.5  # 形态特征的权重
                    ])

                    # PVC判定（调整阈值以适应新的评分系统）
                    if score >= 4.5:  # 降低阈值以补偿移除的QRS宽度特征
                        pvc_count += 1
                        patterns.append({
                            'index': i,
                            'rr_interval': round(current_rr, 3),
                            'amplitude_ratio': round(current_r_peak / np.median(surrounding_beats), 2),
                            'r_wave_diff_ratio': round(max_diff_ratio if len(surrounding_beats) >= 2 else 0, 2),
                            'score': round(score, 1)
                        })

                        if i == last_pvc_idx + 1:
                            consecutive_count += 1
                        else:
                            consecutive_count = 1
                        last_pvc_idx = i

                except Exception as e:
                    Logger().warning(f"单个QRS分析错误: {str(e)}")
                    continue

            recording_duration = len(self.ecg_data) / self.sampling_rate
            pvc_frequency = pvc_count / (recording_duration / 60) if recording_duration > 0 else 0

            result = {
                'has_pvc': pvc_count > 0,
                'count': pvc_count,
                'frequency': round(pvc_frequency, 1),
                'patterns': patterns,
                'is_frequent': pvc_frequency > 6,
                'consecutive': {
                    'count': consecutive_count,
                    'run': consecutive_count >= 3
                }
            }

            return result

        except Exception as e:
            Logger().error(f"PVC检测错误: {str(e)}\n{traceback.format_exc()}")
            return {'has_pvc': False, 'count': 0, 'frequency': 0.0, 'patterns': []}


def med_cal(ecg_data, fs):
    '''
    计算逻辑，后续在优化
    '''
    try:
        ecg_processor = ECGProcessor(ecg_data, fs)

        try:
            bios_peaks = ecg_processor.detect_peaks_by_bios()
        except Exception as e:
            Logger().error(f"Error in detect_peaks_by_bios: {str(e)}")
            return None, 0.0, False

        try:
            qout, sout, pout, tout, q_indices_hamilton, s_indices_hamilton, rpeaks_hamilton = ecg_processor.get_ecg_feature(
                bios_peaks)
        except Exception as e:
            Logger().error(f"Error in get_ecg_feature: {str(e)}")
            return None, 0.0, False

        try:
            rr_intervals = ecg_processor.cal_rr(rpeaks_hamilton)
            if rr_intervals is None or len(rr_intervals) == 0:
                Logger().warning("No valid RR intervals detected")
                return None, 0.0, False
        except Exception as e:
            Logger().error(f"Error in cal_rr: {str(e)}")
            return None, 0.0, False

        try:
            sna_patterns, SNA = ecg_processor.cal_SNA(rr_intervals)

            # 确保 sna_patterns 是字典且包含 cv_rr
            if isinstance(sna_patterns, dict) and 'cv_rr' in sna_patterns:
                cv_rr = float(sna_patterns['cv_rr'])  # 确保 cv_rr 是浮点数
                cv_rr = round(cv_rr, 5)  # 对浮点数进行四舍五入
            else:
                cv_rr = 0.0  # 如果无法获取 cv_rr，返回默认值
                Logger().warning(f"Unable to get cv_rr from sna_patterns: {sna_patterns}")

            return rr_intervals, cv_rr, SNA

        except Exception as e:
            Logger().error(f"Error in cal_SNA: {str(e)}")
            return None, 0.0, False

    except Exception as e:
        Logger().error(f"Error in med_cal: {str(e)}")
        return None, 0.0, False


def calculate_rr_statistics(rr_intervals):
    try:
        if len(rr_intervals) == 0:
            return {'mean': np.nan, 'std': np.nan, 'cv': np.nan, 'iqr': np.nan}

        mean_rr = np.mean(rr_intervals)
        std_rr = np.std(rr_intervals)
        cv_rr = (std_rr / mean_rr) * 100 if mean_rr > 0 else np.nan
        iqr_rr = iqr(rr_intervals)
        return {'mean': mean_rr, 'std': std_rr, 'cv': cv_rr, 'iqr': iqr_rr}
    except Exception as e:
        print(f"Error in calculate_rr_statistics: {str(e)}")
        return {'mean': np.nan, 'std': np.nan, 'cv': np.nan, 'iqr': np.nan}


def cal_signalQuantity(ecg_data, fs):
    """
    @desc:信号质量
    interface下调用
    """
    ecg_quantity = 1  # 假设信号质量初始为良好

    try:
        result = med_cal(ecg_data, fs)  # 调用 med_cal 进行信号处理
        if result is None or any(res is None for res in result):  # 检查返回结果中是否存在 None
            ecg_quantity = -1  # 标记信号质量差
            return ecg_quantity

        rr_intervals, cv_rr, SNA = result

        # 检查 rr_intervals 是否为空或包含异常值
        if rr_intervals is None or len(rr_intervals) == 0:
            Logger().error("Error: RR intervals are empty or invalid.")
            ecg_quantity = -1
            return ecg_quantity

        # 判断 cv_rr 是否在正常范围内
        if cv_rr > 15 or cv_rr < 0.003:
            ecg_quantity = -1
    except Exception as e:
        Logger().error(f"Error in cal_signalQuantity: {str(e)}")
        ecg_quantity = -1
    finally:
        return ecg_quantity


def diagnosis_main(ecg_data, fs):
    """
    症状分析
    diagnosis.py 调用
    """
    # 添加PVC诊断项
    diag_dict = {'SNA': False, 'SNT': False, 'SNB': False, 'AF': False,
                 'SN': False, 'PVC': False, 'signal_quality': 'Good'}

    try:
        ecg_processor = ECGProcessor(ecg_data, fs)

        rr_intervals, cv_rr, SNA = med_cal(ecg_data, fs)
        if rr_intervals is None:
            Logger().error("Error: No valid RR intervals to process.")
            return diag_dict  # 直接返回而不是继续处理

        # 添加PVC检测
        pvc_result = ecg_processor.cal_PVC(rr_intervals)
        diag_dict['PVC'] = pvc_result['has_pvc']

        SNT, SNB = ecg_processor.cal_SNT_SNB(rr_intervals)
        rr_stats = calculate_rr_statistics(rr_intervals)
        AF = ecg_processor.cal_AF(rr_stats)

        SN = False

        if AF:
            diag_dict['AF'] = True

        if SNT:
            diag_dict['SNT'] = True

        if SNB:
            diag_dict['SNB'] = True  # 检测到窦性心动过缓 (SNB)，设置为 True

        if SNA:
            diag_dict['SNA'] = True  # 检测到窦性心律不齐 (SNA)，设置为 True

        # 判断是否为正常心律，cv_rr 的范围调整为 (0.05, 0.8)
        if not diag_dict['AF'] and not diag_dict['SNT'] and not diag_dict['SNB'] and not diag_dict['SNA'] and not \
                diag_dict['PVC']:
            if 0.05 < cv_rr < 0.8:
                SN = True  # cv_rr 符合范围时，标记为正常心律
            diag_dict['SN'] = SN  # 设置正常心律的标志

        # 更新调试信息，显示最后的结果
        print(
            f"After processing - SNT: {diag_dict['SNT']}, SNB: {diag_dict['SNB']}, AF: {diag_dict['AF']}, SNA: {diag_dict['SNA']}, SN: {diag_dict['SN']}, PVC: {diag_dict['PVC']}")

    except Exception as e:
        print(f"Error during diagnosis: {str(e)}")
        Logger().error(traceback.print_exc())

    return diag_dict


def diagnosis_llm_main(ecg_data, fs):
    '''
    症状分析+llm
    diagnosis.py 调用
    '''
    # 0. get base
    first_diag = ['正常心电图', '窦速', '窦缓', '窦性心律不齐', '房颤', '室性早搏', '噪音']
    diag_zh = {'SNA': '窦性心律不齐', 'SNT': '窦速', 'SNB': '窦缓', 'AF': '房颤', 'SN': '正常心电图', 'PVC': '室性早搏',
               'signal_quality': '信号质量差'}
    diag_llm_dict = {'SNA': False, 'SNT': False, 'SNB': False, 'AF': False, 'SN': False, 'PVC': False}
    diag_dict = diagnosis_main(ecg_data, fs)

    diag_base_list = []
    for k, v in diag_dict.items():
        if v and k in diag_zh:  # 确保键存在于diag_zh中
            diag_base_list.append(diag_zh[k])

    # 1. get avg
    rr_average_num, qrs_average_num, qt_average_num, st_average_num, pr_average_num, pp_average_num, qtc_average_num = ecg_qrs_processing.process_ecg_dc(
        ecg_data, fs)
    # 2.get cv_rr
    rr_intervals, cv_rr, SNA = med_cal(ecg_data, fs)

    hr = int(60 / np.mean(rr_intervals)) if len(rr_intervals) > 0 else 0

    rr_stats = calculate_rr_statistics(rr_intervals)
    # 3. llm pred
    prompt_intext = '当前心电图数据特征为心率为{}次/min,RR间期数量{}个,RR间期的平均时长为{}ms,RR间期的标准差为{},RR间期变异系数为{},RR间期的IQR为{}，qrs间期时长均值为{}秒。'.format(
        hr, len(rr_intervals), rr_average_num, np.std(rr_intervals), cv_rr, rr_stats['iqr'], qrs_average_num)
    llm_list = ecg_llm_processing.llm_api(prompt_intext, fs)

    if isinstance(llm_list, list):
        llm_pred = list(set(first_diag) & set(llm_list))
        if "正常心电图" in llm_pred:
            diag_llm_dict['SN'] = True
            return diag_llm_dict
        else:
            # 处理逻辑如果大模型未检测出房颤或PVC，医学诊断有则加入到大模型的诊断结论中
            if '房颤' not in diag_base_list and '房颤' in llm_list:
                diag_base_list.append('房颤')
            if '室性早搏' not in diag_base_list and '室性早搏' in llm_list:
                diag_base_list.append('室性早搏')

            for key, value in diag_zh.items():
                if value in diag_base_list:
                    diag_llm_dict[key] = True
            return diag_llm_dict
    return diag_dict


class is_diag_true:
    '''
    @desc:一级分类中的六个label
    '''

    def __init__(self, ecg_data, fs):
        try:
            # 医学统计模型+大模型
            self.diag_llm_dict = diagnosis_llm_main(ecg_data, fs)
            # 医学统计模型
            # self.diag_llm_dict = diagnosis_main(ecg_data, fs)
        except Exception:
            # 记录错误日志
            Logger().error(f'\n运行异常, \n{traceback.format_exc()}')
            self.diag_llm_dict = {'SNA': False, 'SNT': False, 'SNB': False, 'AF': False, 'SN': False, 'PVC': False}

    def is_sna(self):
        return 1 if self.diag_llm_dict['SNA'] else 0

    def is_snt(self):
        return 1 if self.diag_llm_dict['SNT'] else 0

    def is_snb(self):
        return 1 if self.diag_llm_dict['SNB'] else 0

    def is_af(self):
        return 1 if self.diag_llm_dict['AF'] else 0

    def is_sn(self):
        return 1 if self.diag_llm_dict['SN'] else 0

    def is_pvc(self):
        return 1 if self.diag_llm_dict['PVC'] else 0