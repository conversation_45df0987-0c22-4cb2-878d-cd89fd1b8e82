# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/pr_curve/plugin_data.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.tensorboard/plugins/pr_curve/plugin_data.proto\x12\x0btensorboard\"<\n\x11PrCurvePluginData\x12\x0f\n\x07version\x18\x01 \x01(\x05\x12\x16\n\x0enum_thresholds\x18\x02 \x01(\rb\x06proto3')



_PRCURVEPLUGINDATA = DESCRIPTOR.message_types_by_name['PrCurvePluginData']
PrCurvePluginData = _reflection.GeneratedProtocolMessageType('PrCurvePluginData', (_message.Message,), {
  'DESCRIPTOR' : _PRCURVEPLUGINDATA,
  '__module__' : 'tensorboard.plugins.pr_curve.plugin_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.PrCurvePluginData)
  })
_sym_db.RegisterMessage(PrCurvePluginData)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _PRCURVEPLUGINDATA._serialized_start=63
  _PRCURVEPLUGINDATA._serialized_end=123
# @@protoc_insertion_point(module_scope)
