import csv
import os
import pandas as pd

# 文件路径
ORIGINAL_CSV = r'D:\Project\single_conclusion_ecg.csv'
RESULTS_CSV = r'D:\ECG\0331标注平台数据\噪音和非噪音区分\合并结果.csv'
OUTPUT_CSV = r'D:\ECG\0331标注平台数据\噪音和非噪音区分\匹配结果.csv'

def sanitize_filename(filename):
    """
    将 es_key 转换为有效的文件名
    替换可能在文件名中无效的字符
    (与下载脚本中的同名函数保持一致)
    """
    return filename.replace('/', '_').replace('\\', '_')

def match_csv_files():
    """
    匹配两个CSV文件并创建新的匹配结果文件
    """
    print(f"读取原始数据文件: {ORIGINAL_CSV}")
    print(f"读取结果数据文件: {RESULTS_CSV}")
    
    # 检查文件是否存在
    if not os.path.exists(ORIGINAL_CSV):
        print(f"错误: 找不到原始数据文件 {ORIGINAL_CSV}")
        return
    if not os.path.exists(RESULTS_CSV):
        print(f"错误: 找不到结果数据文件 {RESULTS_CSV}")
        return
    
    try:
        # 使用pandas读取CSV文件
        original_df = pd.read_csv(ORIGINAL_CSV, encoding='utf-8-sig')
        results_df = pd.read_csv(RESULTS_CSV, encoding='utf-8-sig')
        
        print(f"原始数据文件包含 {len(original_df)} 行")
        print(f"结果数据文件包含 {len(results_df)} 行")
        
        # 检查列名
        if 'es_key' not in original_df.columns:
            print(f"错误: 原始数据文件缺少 'es_key' 列。实际列名: {original_df.columns.tolist()}")
            return
        
        if 'new_report_conclusion' not in original_df.columns:
            print(f"错误: 原始数据文件缺少 'new_report_conclusion' 列。实际列名: {original_df.columns.tolist()}")
            return
            
        if '数据来源' not in results_df.columns:
            print(f"错误: 结果数据文件缺少 '数据来源' 列。实际列名: {results_df.columns.tolist()}")
            return
        
        # 创建一个匹配表
        matched_data = []
        
        # 为原始数据创建转换后的文件名列，用于匹配
        original_df['sanitized_key'] = original_df['es_key'].apply(sanitize_filename)
        
        # 跟踪匹配情况
        match_count = 0
        missing_count = 0
        
        # 为了提高匹配效率，创建一个从sanitized_key到原始行的映射
        sanitized_to_original = dict(zip(original_df['sanitized_key'], range(len(original_df))))
        
        # 遍历结果数据
        for i, row in results_df.iterrows():
            source_key = row['数据来源']
            
            # 如果数据来源中包含文件扩展名，去掉
            if '.' in source_key:
                source_key = source_key.split('.')[0]
                
            # 如果数据来源末尾有 "_acc"，去掉
            if source_key.endswith('_acc'):
                source_key = source_key[:-4]  # 去掉末尾的 "_acc"
            
            # 在原始数据中查找匹配项
            if source_key in sanitized_to_original:
                original_idx = sanitized_to_original[source_key]
                original_row = original_df.iloc[original_idx]
                
                # 创建匹配记录
                matched_row = {
                    '原始es_key': original_row['es_key'],
                    '原始诊断结论': original_row['new_report_conclusion'],
                    '数据来源': source_key
                }
                
                # 添加结果数据中的所有其他列
                for col in results_df.columns:
                    if col != '数据来源':  # 避免重复
                        matched_row[col] = row[col]
                
                matched_data.append(matched_row)
                match_count += 1
            else:
                print(f"警告: 无法找到与 '{source_key}' 匹配的原始数据")
                missing_count += 1
        
        # 创建匹配结果的DataFrame
        if matched_data:
            matched_df = pd.DataFrame(matched_data)
            
            # 保存到CSV
            matched_df.to_csv(OUTPUT_CSV, index=False, encoding='utf-8-sig')
            print(f"匹配完成。成功匹配: {match_count}, 未匹配: {missing_count}")
            print(f"匹配结果已保存到: {OUTPUT_CSV}")
        else:
            print("没有找到匹配项")
            
    except Exception as e:
        print(f"处理过程中出错: {e}")

if __name__ == "__main__":
    match_csv_files() 