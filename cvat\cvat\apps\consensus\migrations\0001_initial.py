# Generated by Django 4.2.18 on 2025-02-10 11:29

import django.db.models.deletion
from django.db import migrations, models


def init_consensus_settings_in_existing_consensus_tasks(apps, schema_editor):
    Task = apps.get_model("engine", "Task")
    ConsensusSettings = apps.get_model("consensus", "ConsensusSettings")

    tasks_with_consensus = Task.objects.filter(
        segment__job__type="consensus_replica", consensus_settings__isnull=True
    ).distinct()
    ConsensusSettings.objects.bulk_create(
        [ConsensusSettings(task=t) for t in tasks_with_consensus],
        batch_size=10000,
    )


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("engine", "0088_consensus_jobs"),
    ]

    operations = [
        migrations.CreateModel(
            name="ConsensusSettings",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("quorum", models.FloatField(default=0.5)),
                ("iou_threshold", models.FloatField(default=0.4)),
                (
                    "task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="consensus_settings",
                        to="engine.task",
                    ),
                ),
            ],
        ),
        migrations.RunPython(
            init_consensus_settings_in_existing_consensus_tasks,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
