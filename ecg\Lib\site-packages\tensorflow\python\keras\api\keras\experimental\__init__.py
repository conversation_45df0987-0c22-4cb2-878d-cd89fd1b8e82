# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.keras.experimental namespace.
"""

from __future__ import print_function as _print_function

import sys as _sys

from tensorflow.python.keras.feature_column.sequence_feature_column import SequenceFeatures
from tensorflow.python.keras.layers.recurrent import PeepholeLSTMCell
from tensorflow.python.keras.optimizer_v2.learning_rate_schedule import CosineDecay
from tensorflow.python.keras.optimizer_v2.learning_rate_schedule import CosineDecayRestarts
from tensorflow.python.keras.premade.linear import LinearModel
from tensorflow.python.keras.premade.wide_deep import WideDeepModel
from tensorflow.python.keras.saving.saved_model_experimental import export_saved_model
from tensorflow.python.keras.saving.saved_model_experimental import load_from_saved_model

del _print_function

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.experimental", public_apis=None, deprecation=True,
      has_lite=False)
