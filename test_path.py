#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路径配置
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

# 强制重新导入模块
if 'match_and_merge_enhanced' in sys.modules:
    del sys.modules['match_and_merge_enhanced']

from match_and_merge_enhanced import EnhancedDataMatcher

def main():
    print("测试路径配置...")
    
    # 创建匹配器实例
    matcher = EnhancedDataMatcher()
    
    print(f"file1_path: {matcher.config['file1_path']}")
    print(f"file2_path: {matcher.config['file2_path']}")
    print(f"output_path: {matcher.config['output_path']}")
    
    # 检查文件是否存在
    file1_exists = os.path.exists(matcher.config['file1_path'])
    file2_exists = os.path.exists(matcher.config['file2_path'])
    
    print(f"file1存在: {file1_exists}")
    print(f"file2存在: {file2_exists}")
    
    if not file1_exists:
        print("file1不存在，检查可能的路径...")
        # 检查v3.0路径
        v3_path = r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\10s10步长v3.0\合并结果.csv"
        v3_exists = os.path.exists(v3_path)
        print(f"v3.0路径存在: {v3_exists} - {v3_path}")

if __name__ == "__main__":
    main()
