         3030618 function calls (3030383 primitive calls) in 28.920 seconds

   Ordered by: internal time

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
       34   22.031    0.648   22.031    0.648 {method 'recv_into' of '_socket.socket' objects}
       33    3.554    0.108    3.554    0.108 {built-in method time.sleep}
        2    2.466    1.233    2.466    1.233 {built-in method builtins.input}
       33    0.165    0.005   26.188    0.794 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:216(ecg_analysis)
   376000    0.077    0.000    0.140    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:150(_isna)
        1    0.062    0.062   26.334   26.334 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:544(process_single_file)
       33    0.059    0.002    0.059    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\encoder.py:306(iterencode)
   376000    0.057    0.000    0.196    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:67(isna)
   188000    0.048    0.000    0.155    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:288(notna)
   376000    0.044    0.000    0.044    0.000 {built-in method pandas._libs.missing.checknull}
        1    0.038    0.038    0.038    0.038 {method 'connect' of '_socket.socket' objects}
      526    0.033    0.000    0.033    0.000 {method 'write' of '_io.TextIOWrapper' objects}
   564000    0.030    0.000    0.030    0.000 {pandas._libs.lib.is_scalar}
   378052    0.021    0.000    0.021    0.000 {method 'append' of 'list' objects}
        1    0.019    0.019    0.019    0.019 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\decoder.py:343(raw_decode)
        1    0.017    0.017    0.020    0.020 {method 'read' of '_io.TextIOWrapper' objects}
208962/208896    0.015    0.000    0.017    0.000 {built-in method builtins.isinstance}
        1    0.013    0.013    0.013    0.013 {built-in method _socket.getaddrinfo}
   188496    0.012    0.000    0.012    0.000 {method 'strip' of 'str' objects}
   188000    0.011    0.000    0.011    0.000 {built-in method builtins.abs}
       68    0.009    0.000    0.009    0.000 {method 'sendall' of '_socket.socket' objects}
       86    0.007    0.000    0.007    0.000 {built-in method nt.stat}
      705    0.006    0.000    0.006    0.000 {method 'split' of 'str' objects}
    11414    0.005    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:670(__getitem__)
    12067    0.004    0.000    0.017    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:742(__iter__)
      526    0.004    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:288(__init__)
       68    0.003    0.000    0.020    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2494(getproxies_environment)
    11414    0.003    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:740(encodekey)
      102    0.003    0.000    0.003    0.000 {built-in method winreg.QueryValueEx}
        1    0.003    0.003    0.003    0.003 {built-in method _codecs.utf_8_decode}
       66    0.003    0.000    0.003    0.000 {built-in method builtins.min}
      606    0.002    0.000    0.002    0.000 {built-in method builtins.max}
      526    0.002    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1514(findCaller)
    13483    0.002    0.000    0.002    0.000 {method 'lower' of 'str' objects}
      562    0.002    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:180(split)
    11152    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:693(__iter__)
       68    0.002    0.000    0.002    0.000 {built-in method winreg.OpenKey}
    11414    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:734(check_str)
      102    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:227(_encode_invalid_chars)
      612    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:124(splitdrive)
    11583    0.001    0.000    0.001    0.000 {method 'upper' of 'str' objects}
       33    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:392(raw_decode)
      526    0.001    0.000    0.039    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1073(emit)
       33    0.001    0.000   26.189    0.794 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:197(profile_api_call)
     1052    0.001    0.000    0.001    0.000 {method 'match' of 're.Pattern' objects}
     1052    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:44(normcase)
      337    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:366(urlparse)
      526    0.001    0.000    0.060    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1565(_log)
      526    0.001    0.000    0.061    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1436(info)
      526    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1062(flush)
      526    0.001    0.000    0.062    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2074(info)
       34    0.001    0.000   22.045    0.648 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:481(getresponse)
      529    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:121(_splitext)
     3342    0.001    0.000    0.001    0.000 {built-in method builtins.hasattr}
       33    0.001    0.000    0.001    0.000 {built-in method builtins.sum}
       33    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:33(select_wait_for_socket)
      526    0.001    0.000    0.041    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1645(callHandlers)
       68    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:369(parse_url)
6574/6496    0.001    0.000    0.001    0.000 {built-in method builtins.len}
      271    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1208(putheader)
      526    0.001    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1550(makeRecord)
     2933    0.001    0.000    0.001    0.000 {method 'get' of 'dict' objects}
       34    0.001    0.000    0.065    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:365(request)
       34    0.001    0.000   22.116    0.650 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:594(urlopen)
      236    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:824(update)
      526    0.001    0.000    0.040    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:941(handle)
      329    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2084(debug)
       34    0.001    0.000   22.238    0.654 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:500(request)
       34    0.001    0.000   22.130    0.651 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:673(send)
      526    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:655(format)
       34    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:301(makefile)
      529    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:203(splitext)
     1591    0.001    0.000    0.001    0.000 {method 'rfind' of 'str' objects}
      776    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:110(_coerce_args)
       33    0.001    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:204(parse_headers)
       34    0.001    0.000   22.110    0.650 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:379(_make_request)
     1052    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:898(acquire)
       34    0.001    0.000    0.080    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:457(prepare_request)
       34    0.001    0.000   22.125    0.651 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:613(send)
     1678    0.001    0.000    0.001    0.000 {method 'replace' of 'str' objects}
        1    0.001    0.001    0.040    0.040 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:274(load)
       34    0.001    0.000   22.032    0.648 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:267(_read_status)
      101    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:81(RLock)
       33    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:471(_parse_headers)
      371    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:417(urlsplit)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:204(iterencode)
       66    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:218(_parsegen)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:95(_default_key_normalizer)
      813    0.000    0.000    0.001    0.000 {built-in method _abc._abc_instancecheck}
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1052(putrequest)
      526    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:435(_format)
      364    0.000    0.000   22.032    0.061 {method 'readline' of '_io.BufferedReader' objects}
      526    0.000    0.000    0.041    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1591(handle)
     2018    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:52(bounded_int)
      974    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:46(__setitem__)
      238    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:61(merge_setting)
      889    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1689(isEnabledFor)
       34    0.000    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:204(get_netrc_auth)
       34    0.000    0.000   22.039    0.648 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:300(begin)
      381    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:657(get)
      526    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:918(format)
1462/1461    0.000    0.000    0.000    0.000 {method 'encode' of 'str' objects}
      913    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:51(__getitem__)
     1190    0.000    0.000    0.000    0.000 {method 'acquire' of '_thread.RLock' objects}
      526    0.000    0.000    0.000    0.000 {method 'flush' of '_io.TextIOWrapper' objects}
      239    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:345(to_key_val_list)
       34    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:409(prepare_url)
      101    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1258(__init__)
      134    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:121(put)
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:476(readinto)
      561    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:214(basename)
      442    0.000    0.000    0.000    0.000 {method 'search' of 're.Pattern' objects}
       67    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:521(cookiejar_from_dict)
      526    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:633(usesTime)
      526    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:119(getLevelName)
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:337(extend)
     1052    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:905(release)
      264    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:462(get)
      526    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:160(<lambda>)
      271    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:351(putheader)
       34    0.000    0.000    0.060    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:494(prepare_body)
      526    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1306(current_thread)
      306    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:155(hostname)
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:658(__init__)
      297    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:293(header_source_parse)
       66    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:858(_raw_read)
       33    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:59(parsestr)
      526    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:427(usesTime)
     2615    0.000    0.000    0.000    0.000 {method 'decode' of 'bytes' objects}
     1052    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:796(filter)
       34    0.000    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:114(proxy_bypass)
      168    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:40(__init__)
     2839    0.000    0.000    0.000    0.000 {built-in method nt.fspath}
       32    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:455(get_disease_name)
       68    0.000    0.000    0.000    0.000 {method 'settimeout' of '_socket.socket' objects}
       68    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:289(expanduser)
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:573(__init__)
      340    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:195(_hostinfo)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:483(prepare_headers)
      168    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:341(notify)
       66    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:444(read)
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:139(__init__)
      916    0.000    0.000    0.000    0.000 {method 'find' of 'str' objects}
      940    0.000    0.000    0.000    0.000 {method 'startswith' of 'str' objects}
       34    0.000    0.000    0.027    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:750(merge_environment_settings)
     1186    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:58(<genexpr>)
       34    0.000    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:765(should_bypass_proxies)
       33    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:359(build_response)
      727    0.000    0.000    0.000    0.000 {built-in method time.time}
      526    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:364(getMessage)
      363    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1424(debug)
      813    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\abc.py:96(__instancecheck__)
      942    0.000    0.000    0.000    0.000 {method 'rstrip' of 'str' objects}
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:315(__init__)
      429    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:259(__getitem__)
      169    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1219(vals_sorted_by_key)
      396    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:78(readline)
      495    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:281(_sanitize_header)
      526    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:438(format)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1353(add_cookie_header)
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:183(dumps)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:153(get)
      526    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1031(name)
      132    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:564(get_content_type)
       33    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:910(read)
      103    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:109(__init__)
       66    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:124(extract_cookies_to_jar)
       66    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1596(make_cookies)
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:263(_remove_path_dot_segments)
       34    0.000    0.000    0.068    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:351(prepare)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:258(__init__)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:53(__init__)
       34    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:76(proxy_bypass_registry)
      297    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:302(add)
       34    0.000    0.000   22.040    0.648 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1300(getresponse)
      526    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:639(formatMessage)
       34    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:446(get_connection_with_tls_context)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:643(__init__)
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:100(__new__)
       33    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:41(parse)
      543    0.000    0.000    0.000    0.000 {built-in method __new__ of type object at 0x00007FFEAA3BB810}
      132    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:497(get_all)
       66    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1677(extract_cookies)
      630    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:34(_get_bothseps)
       34    0.000    0.000   22.031    0.648 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:655(readinto)
      101    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:877(__init__)
      406    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1045(_validate_header_part)
      272    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:676(items)
     2544    0.000    0.000    0.000    0.000 {built-in method builtins.ord}
       68    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:838(select_proxy)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:237(__init__)
        6    0.000    0.000    0.000    0.000 {built-in method nt.get_terminal_size}
      100    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:35(__init__)
      237    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:664(__contains__)
   109/71    0.000    0.000    0.000    0.000 {built-in method _abc._abc_subclasscheck}
      526    0.000    0.000    0.000    0.000 {built-in method sys._getframe}
      100    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:82(__init__)
       66    0.000    0.000    0.000    0.000 {method 'readlines' of '_io._IOBase' objects}
       70    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:303(_normalize_host)
      407    0.000    0.000    0.004    0.000 {method 'join' of 'bytes' objects}
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:255(get)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:732(close)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:305(connection_from_context)
       81    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:16(exists)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:101(push)
     1054    0.000    0.000    0.000    0.000 {built-in method _thread.get_ident}
      409    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\util.py:19(to_str)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:255(inner)
       34    0.000    0.000   22.239    0.654 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:626(post)
      271    0.000    0.000    0.000    0.000 {method 'fullmatch' of 're.Pattern' objects}
      271    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:400(<genexpr>)
      526    0.000    0.000    0.000    0.000 {built-in method nt.getpid}
       33    0.000    0.000    0.059    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\encoder.py:277(encode)
      170    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:57(__iter__)
       34    0.000    0.000    0.000    0.000 {method 'Close' of 'PyHKEY' objects}
      396    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:128(__next__)
      169    0.000    0.000    0.000    0.000 {built-in method builtins.sorted}
     1190    0.000    0.000    0.000    0.000 {method 'release' of '_thread.RLock' objects}
      309    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:130(_validate_timeout)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1018(get_auth_from_url)
       33    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:242(__init__)
      528    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\utils.py:51(_has_surrogates)
      495    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:311(header_fetch_parse)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:90(_urllib3_request_context)
       34    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:276(connection_from_host)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:40(assert_header_parsing)
       33    0.000    0.000    0.125    0.004 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:102(get_token)
      986    0.000    0.000    0.000    0.000 {method 'join' of 'str' objects}
      100    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:117(__exit__)
      169    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1223(deepvalues)
       34    0.000    0.000    0.000    0.000 {method 'close' of '_io.BufferedReader' objects}
      100    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:238(helper)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:868(__new__)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:331(putrequest)
      343    0.000    0.000    0.000    0.000 {built-in method builtins.getattr}
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:182(encode)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2662(getproxies_registry)
      132    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:29(_splitparam)
     1118    0.000    0.000    0.000    0.000 {method 'partition' of 'str' objects}
       34    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:258(_get_conn)
      305    0.000    0.000    0.000    0.000 {built-in method builtins.any}
       68    0.000    0.000    0.060    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:939(send)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\request.py:189(body_to_chunks)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:679(_init_length)
       35    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:106(_encode_params)
      542    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:353(<genexpr>)
      203    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1034(check_header_validity)
       99    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:890(content)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:140(get_cookie_header)
       66    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:801(_fp_read)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:120(__init__)
        1    0.000    0.000    0.000    0.000 {built-in method io.open}
      776    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:99(_noop)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:330(connection_from_pool_key)
      297    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:479(set_raw)
       77    0.000    0.000    0.000    0.000 {method 'clear' of 'dict' objects}
      168    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:246(__enter__)
      449    0.000    0.000    0.000    0.000 {method 'rpartition' of 'str' objects}
      102    0.000    0.000    0.000    0.000 {method 'subn' of 're.Pattern' objects}
      198    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:434(isclosed)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:546(request_url)
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:542(merge_cookies)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:334(__init__)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:572(prepare_content_length)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:781(get_adapter)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:514(_parse_content_type_header)
       34    0.000    0.000    0.054    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:998(_send_output)
      305    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:977(_output)
       66    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:1038(stream)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:95(__getitem__)
      169    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1750(__iter__)
      201    0.000    0.000    0.001    0.000 {built-in method builtins.next}
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:304(cert_verify)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:870(quote_from_bytes)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:207(register_hook)
      108    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:332(_idna_encode)
       33    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:947(json)
      132    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:735(_error_catcher)
       66    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:178(_call_parse)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:539(get_encoding_from_headers)
       33    0.000    0.000    0.059    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\__init__.py:276(dumps)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:768(__instancecheck__)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:474(urlunparse)
       99    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:720(__hash__)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:459(<listcomp>)
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:348(_get_timeout)
      609    0.000    0.000    0.000    0.000 {method 'items' of 'dict' objects}
      100    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:108(__enter__)
       34    0.000    0.000    0.026    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:826(get_environ_proxies)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:296(_put_conn)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:485(urlunsplit)
       33    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:379(decode)
      168    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:261(_is_owned)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:213(get_payload)
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:15(default_hooks)
       33    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\__init__.py:459(loads)
      442    0.000    0.000    0.000    0.000 {method 'setdefault' of 'dict' objects}
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:909(text)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:771(__subclasscheck__)
      330    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:291(__iter__)
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:188(clone)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:135(super_len)
      272    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:698(__init__)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:624(unquote)
       33    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:173(feed)
       66    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:816(generate)
       35    0.000    0.000    0.000    0.000 {method 'pop' of 'collections.OrderedDict' objects}
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:610(prepare_cookies)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:159(resolve_redirects)
       38    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:799(quote)
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:15(is_connection_dropped)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:446(_init_decoder)
       67    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:534(<listcomp>)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:122(pushlines)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:104(__init__)
       66    0.000    0.000    0.000    0.000 {built-in method builtins.all}
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:774(get_proxy)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:1079(closed)
      185    0.000    0.000    0.000    0.000 {method 'endswith' of 'str' objects}
       34    0.000    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2707(getproxies)
      168    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:249(__exit__)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:85(path_url)
      237    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\_internal_utils.py:25(to_native_string)
      337    0.000    0.000    0.000    0.000 {method 'lstrip' of 'str' objects}
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:660(requote_uri)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:379(_is_method_retryable)
      102    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:84(<listcomp>)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:374(_merge_pool_kwargs)
       32    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:501(get_multi_label_disease_name)
        1    0.000    0.000    0.000    0.000 {built-in method io.open_code}
       35    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:892(urlencode)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:588(prepare_auth)
       99    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:588(get_content_maintype)
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:358(update)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:244(__init__)
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:184(close)
       15    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:91(_path_join)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:802(__getitem__)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:251(put)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:636(unquote_unreserved)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1731(clear_expired_cookies)
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:76(copy)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:799(iter_content)
      168    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:245(_qsize)
      101    0.000    0.000    0.000    0.000 {method 'copy' of 'dict' objects}
      102    0.000    0.000    0.000    0.000 {method 'count' of 'bytes' objects}
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:299(is_connected)
        1    0.000    0.000    0.000    0.000 {function socket.close at 0x000001F185A20280}
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:630(prepare_hooks)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:197(_new_message)
       66    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:635(release_conn)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:219(__init__)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:411(close)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:245(read_timeout)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:70(close)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:393(prepare_method)
       32    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:376(<listcomp>)
       33    0.000    0.000    0.000    0.000 {method 'readinto' of '_io.BufferedReader' objects}
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:202(start_connect)
       68    0.000    0.000    0.000    0.000 {function SocketIO.close at 0x000001F185A20CA0}
       71    0.000    0.000    0.000    0.000 {method 'extend' of 'list' objects}
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:326(<listcomp>)
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:113(wait_for_read)
      102    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:211(<genexpr>)
       34    0.000    0.000    0.054    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1236(endheaders)
      136    0.000    0.000    0.000    0.000 {method 'groups' of 're.Match' objects}
      102    0.000    0.000    0.000    0.000 {method 'decode' of 'bytearray' objects}
       34    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:351(_encode_target)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:396(build_connection_pool_key_attributes)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:403(retries)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:9(is_fp_closed)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:183(_userinfo)
       33    0.000    0.000    0.000    0.000 {method 'tobytes' of 'memoryview' objects}
       32    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:495(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1372(_clear_cache)
        1    0.000    0.000    0.000    0.000 {built-in method marshal.loads}
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1294(_cookie_attrs)
      102    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:60(__len__)
       66    0.000    0.000    0.000    0.000 {method 'read' of '_io.StringIO' objects}
      272    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:493(<genexpr>)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:107(get_redirect_target)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:77(join)
      168    0.000    0.000    0.000    0.000 {method 'acquire' of '_thread.lock' objects}
   109/71    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\abc.py:100(__subclasscheck__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:493(_parse)
      134    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:248(_put)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:769(is_redirect)
       35    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:389(parent)
        1    0.000    0.000    0.000    0.000 {method 'read' of '_io.BufferedReader' objects}
      132    0.000    0.000    0.000    0.000 {method 'count' of 'str' objects}
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:486(_decref_socketios)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:177(__init__)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:706(<setcomp>)
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:538(find)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:387(is_retry)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:147(username)
      300    0.000    0.000    0.000    0.000 {method 'pop' of 'dict' objects}
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:377(_check_close)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:17(__init__)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:210(_pop_message)
      237    0.000    0.000    0.000    0.000 {method 'keys' of 'dict' objects}
      168    0.000    0.000    0.000    0.000 {method '__enter__' of '_thread.lock' objects}
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:16(<dictcomp>)
       66    0.000    0.000    0.000    0.000 {method 'extend' of 'collections.deque' objects}
       33    0.000    0.000    0.000    0.000 {method 'write' of '_io.StringIO' objects}
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:694(readable)
        6    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1337(add_argument)
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:164(host)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1183(_validate_method)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:423(flush)
      100    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:44(_debug)
      396    0.000    0.000    0.000    0.000 {method 'popleft' of 'collections.deque' objects}
      204    0.000    0.000    0.000    0.000 {method 'values' of 'collections.OrderedDict' objects}
       66    0.000    0.000    0.000    0.000 {built-in method builtins.issubclass}
       34    0.000    0.000    0.000    0.000 {method 'update' of 'dict' objects}
       66    0.000    0.000    0.000    0.000 {method 'write' of '_io.BytesIO' objects}
     10/3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:71(_compile)
       99    0.000    0.000    0.000    0.000 {built-in method builtins.hash}
       38    0.000    0.000    0.000    0.000 {method 'rstrip' of 'bytes' objects}
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:406(_close_conn)
       34    0.000    0.000    0.000    0.000 {method 'sort' of 'list' objects}
       33    0.000    0.000    0.000    0.000 {method 'getvalue' of '_io.BytesIO' objects}
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:451(items)
       34    0.000    0.000    0.000    0.000 <string>:1(__new__)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1498(find_spec)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1255(__init__)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:303(set_payload)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\_internal_utils.py:38(unicode_is_ascii)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1192(_validate_path)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\request.py:134(set_file_position)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:251(_get)
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:289(_compile)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:161(__init__)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:212(_expand_lang)
        2    0.000    0.000    0.000    0.000 {built-in method builtins.__build_class__}
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:110(__init__)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1287(_cookies_for_request)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:91(merge_hooks)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1034(get_data)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:724(<listcomp>)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:166(port)
       67    0.000    0.000    0.000    0.000 {method 'pop' of 'list' objects}
       32    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:496(<listcomp>)
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:701(__len__)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:181(is_multipart)
       68    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:743(set_environ)
      168    0.000    0.000    0.000    0.000 {method '__exit__' of '_thread.lock' objects}
      132    0.000    0.000    0.000    0.000 {method 'seek' of '_io.StringIO' objects}
        1    0.000    0.000    0.051    0.051 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:193(_new_conn)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\shutil.py:1312(get_terminal_size)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:22(dispatch_hook)
        1    0.000    0.000    0.051    0.051 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:27(create_connection)
       67    0.000    0.000    0.000    0.000 {built-in method time.perf_counter}
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:343(<genexpr>)
       66    0.000    0.000    0.000    0.000 {method 'values' of 'dict' objects}
       69    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:126(resolve_default_timeout)
      132    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:248(__len__)
      136    0.000    0.000    0.000    0.000 {method 'items' of 'collections.OrderedDict' objects}
       69    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:227(connect_timeout)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:465(_decode)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\util.py:7(to_bytes)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1478(_get_optional_kwargs)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1179(_encode_request)
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:579(translation)
       66    0.000    0.000    0.000    0.000 {method 'truncate' of '_io.StringIO' objects}
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:570(_metavar_formatter)
        1    0.000    0.000    0.013    0.013 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:901(getaddrinfo)
     10/3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:174(getwidth)
       68    0.000    0.000    0.000    0.000 {method 'update' of 'collections.OrderedDict' objects}
       34    0.000    0.000    0.000    0.000 {method '_checkClosed' of '_io._IOBase' objects}
       37    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1308(register)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1638(__init__)
        1    0.000    0.000    0.019    0.019 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:299(loads)
       63    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:164(__getitem__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:536(_compile_info)
      132    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:117(info)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:890(_find_spec)
        1    0.000    0.000    0.000    0.000 {method 'setsockopt' of '_socket.socket' objects}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:759(compile)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:914(get_code)
      110    0.000    0.000    0.000    0.000 {method 'isascii' of 'str' objects}
       68    0.000    0.000    0.000    0.000 {method 'end' of 're.Match' objects}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:435(_parse_sub)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:313(__call__)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\proxy.py:11(connection_requires_http_tunnel)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\__init__.py:43(normalize_encoding)
        1    0.000    0.000    0.019    0.019 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\decoder.py:332(decode)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\locale.py:384(normalize)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1398(_add_action)
       34    0.000    0.000    0.000    0.000 {method '_checkReadable' of '_io._IOBase' objects}
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:586(_format_args)
        1    0.000    0.000    0.002    0.002 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:87(parse_arguments)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:79(<listcomp>)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:339(_validate_conn)
       23    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:233(__next)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\locale.py:350(_replace_encoding)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1388(add_argument_group)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:816(__init__)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:578(add_headers)
        1    0.000    0.000    0.003    0.003 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:319(decode)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:276(_optimize_charset)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\weakref.py:517(__init__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:937(parse)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:87(get_new_headers)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1774(parse_known_args)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:125(__iter__)
        1    0.000    0.000    0.001    0.001 <frozen importlib._bootstrap>:986(_find_and_load)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:216(<genexpr>)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:222(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:218(_acquireLock)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:809(__init__)
       34    0.000    0.000    0.000    0.000 {built-in method time.monotonic}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1567(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1809(_parse_known_args)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:295(is_closed)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:581(iter_slices)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:631(__new__)
       33    0.000    0.000    0.000    0.000 {method 'pop' of 'set' objects}
        2    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:354(cache_from_source)
       66    0.000    0.000    0.000    0.000 {method 'append' of 'collections.deque' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:238(_new_conn)
        2    0.000    0.000    0.000    0.000 {method 'split' of 'bytes' objects}
       32    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:539(<listcomp>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:97(_intenum_converter)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\__init__.py:70(search_function)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:316(close)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1367(_get_spec)
       15    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:114(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:93(_set_socket_options)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:1149(cast)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:157(_get_module_lock)
        3    0.000    0.000    0.000    0.000 {built-in method _thread.allocate_lock}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:33(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:229(_new_pool)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:1(<module>)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:477(_init_module_attrs)
       41    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:392(__subclasshook__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:355(_escape)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:938(__and__)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2480(_get_formatter)
        1    0.000    0.000    0.051    0.051 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:278(connect)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:249(_compile_charset)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\idna.py:300(getregentry)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:172(append)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1589(_add_action)
       34    0.000    0.000    0.000    0.000 {function HTTPResponse.flush at 0x000001F185AF5550}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:650(_load_unlocked)
       26    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:160(__len__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\idna.py:147(encode)
       16    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:222(_verbose_message)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:134(__init__)
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:250(compile)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:689(spec_from_file_location)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1417(setLevel)
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:660(dgettext)
        1    0.000    0.000    0.001    0.001 <frozen importlib._bootstrap>:956(_find_and_load_unlocked)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:638(_compile_bytecode)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:391(_splitnetloc)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:853(quote_plus)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:254(get)
        6    0.000    0.000    0.000    0.000 {method 'fileno' of '_io.TextIOWrapper' objects}
        1    0.000    0.000    0.000    0.000 {built-in method builtins.__import__}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\_distutils_hack\__init__.py:102(find_spec)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:598(_code)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:202(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:492(_real_close)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:842(exec_module)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:82(_have_working_poll)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1514(_pop_action_class)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1518(_get_handler)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:79(_unpack_uint32)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:309(__init__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:224(__init__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:461(_get_literal_prefix)
        5    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:135(_path_stat)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:851(__init__)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1330(_path_importer_cache)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\types.py:171(__get__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1675(getEffectiveLevel)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:286(tell)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1747(_add_action)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:111(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:81(__init__)
        5    0.000    0.000    0.000    0.000 {built-in method builtins.setattr}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:103(release)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:549(module_from_spec)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:167(_path_isabs)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:102(__setitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1954(consume_positionals)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:423(_simple)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:579(format)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1312(_registry_get)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:58(__init__)
        2    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:127(_path_split)
        1    0.000    0.000    0.000    0.000 {built-in method _imp.is_builtin}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:227(_releaseLock)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:95(wait_for_socket)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:78(acquire)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:553(_classify_pyc)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:94(__new__)
       19    0.000    0.000    0.000    0.000 {method 'isalnum' of 'str' objects}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:921(fix_flags)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:925(close)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1200(_validate_host)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1527(_check_conflict)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:595(isstring)
        3    0.000    0.000    0.000    0.000 {built-in method _sre.compile}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:867(__exit__)
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:129(<genexpr>)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1493(_get_spec)
        2    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:376(cached)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:249(match)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:492(_get_charset_prefix)
        4    0.000    0.000    0.000    0.000 {method 'find' of 'bytearray' objects}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:147(__enter__)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1004(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:172(from_float)
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:735(gettext)
        3    0.000    0.000    0.000    0.000 {method 'index' of 'str' objects}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:484(_get_cached)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1399(find_spec)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:72(_check_methods)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1276(disable)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:997(raise_for_status)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:64(_relax_case)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:81(groups)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:496(close)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1031(__init__)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:176(cb)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:725(find_spec)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:1149(_normalize_host)
        1    0.000    0.000    0.000    0.000 {built-in method builtins.exec}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:800(find_spec)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:863(__enter__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:432(_generate_overlap_table)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:909(__init__)
        1    0.000    0.000    0.000    0.000 {method 'format' of 'str' objects}
       12    0.000    0.000    0.000    0.000 {built-in method builtins.callable}
        1    0.000    0.000    0.000    0.000 {built-in method builtins.locals}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:145(_path_is_mode_type)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:154(_path_isfile)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:516(_check_name_wrapper)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:586(_validate_timestamp_pyc)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:76(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:193(_checkLevel)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2018(getLogger)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2106(_match_arguments_partial)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:221(dirname)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:168(__setitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:574(<listcomp>)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:35(_new_module)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:151(__exit__)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:211(_call_with_frames_removed)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1075(path_stats)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:753(value)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:103(allowed_gai_family)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:886(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:399(_checknetloc)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1767(parse_args)
        1    0.000    0.000    0.000    0.000 {method 'flush' of '_io.BufferedReader' objects}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:342(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:406(__subclasshook__)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:453(_get_iscased)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1759(_get_positional_actions)
        3    0.000    0.000    0.000    0.000 {built-in method from_bytes}
        6    0.000    0.000    0.000    0.000 {built-in method _imp.release_lock}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:260(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:22(netrc)
        6    0.000    0.000    0.000    0.000 {method 'reverse' of 'list' objects}
        6    0.000    0.000    0.000    0.000 {built-in method _imp.acquire_lock}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:162(__delitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:862(_get_hostport)
        1    0.000    0.000    0.000    0.000 {built-in method nt._path_splitroot}
        1    0.000    0.000    0.000    0.000 {built-in method _imp.is_frozen}
        1    0.000    0.000    0.000    0.000 {built-in method _socket.getdefaulttimeout}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:143(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:242(_init)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:183(host)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:10(NetrcParseError)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_request_methods.py:51(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:604(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1760(<listcomp>)
        1    0.000    0.000    0.000    0.000 {built-in method atexit.register}
        1    0.000    0.000    0.000    0.000 {built-in method sys.audit}
        1    0.000    0.000    0.000    0.000 {method 'disable' of '_lsprof.Profiler' objects}
        1    0.000    0.000    0.000    0.000 {built-in method _imp._fix_co_filename}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1240(__init__)
        1    0.000    0.000    0.000    0.000 {built-in method builtins.iter}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:397(has_location)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:839(create_module)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1029(get_filename)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\_distutils_hack\__init__.py:109(<lambda>)


