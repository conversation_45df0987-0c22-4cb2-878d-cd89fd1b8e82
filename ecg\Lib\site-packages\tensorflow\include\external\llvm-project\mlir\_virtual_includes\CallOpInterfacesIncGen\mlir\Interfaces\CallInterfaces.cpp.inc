/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Interface Definitions                                                      *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

CallInterfaceCallable mlir::CallOpInterface::getCallableForCallee() {
      return getImpl()->getCallableForCallee(getImpl(), getOperation());
  }
Operation::operand_range mlir::CallOpInterface::getArgOperands() {
      return getImpl()->getArgOperands(getImpl(), getOperation());
  }
Region *mlir::CallableOpInterface::getCallableRegion() {
      return getImpl()->getCallableRegion(getImpl(), getOperation());
  }
ArrayRef<Type> mlir::CallableOpInterface::getCallableResults() {
      return getImpl()->getCallableResults(getImpl(), getOperation());
  }
