{{- if and .Values.traefik.enabled .Values.analytics.enabled }}

apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: forwardauth
  namespace: {{ .Release.Namespace }}
  annotations:
  labels:
    {{- include "cvat.labels" . | nindent 4 }}
spec:
  forwardAuth:
    address: http://{{ .Release.Name }}-backend-service:8080/analytics
    authRequestHeaders:
      - "<PERSON><PERSON>"
      - "Authorization"

{{- end }}
