#!/usr/bin/env python3
"""
最终修复版羽毛球装备爬虫 - 正确处理Session状态
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os
from urllib.parse import urljoin

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalBadmintonCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        self.base_url = "https://www.badmintoncn.com"
        self.equipment_types = {
            1: "羽毛球拍",
            2: "羽毛球鞋", 
            3: "运动包",
            4: "羽毛球线",
            5: "羽毛球",
            6: "运动服饰",
            7: "手胶"
        }
        
        self.verified = False  # 跟踪验证状态
        
        os.makedirs('output', exist_ok=True)
        logger.info("🏸 最终修复版爬虫初始化完成")

    def initialize_session(self):
        """改进的Session初始化 - 建立稳定连接"""
        try:
            logger.info("🌐 初始化Session...")
            
            # 1. 首先访问主页建立基础连接
            response = self.session.get(self.base_url, timeout=30)
            logger.info(f"主页访问状态码: {response.status_code}")
            
            if response.status_code == 200:
                logger.info("✅ 主页访问成功")
                
                # 2. 检查是否已经有Cookie
                cookies = self.session.cookies
                logger.info(f"当前Cookie数量: {len(cookies)}")
                
                # 3. 尝试访问装备页面测试连接状态
                time.sleep(2)
                test_url = f"{self.base_url}/cbo_eq/list.php"
                logger.info("🧪 测试装备页面连接...")
                
                test_response = self.session.get(test_url, timeout=30)
                logger.info(f"测试页面状态码: {test_response.status_code}")
                
                # 4. 解析测试页面
                test_soup = BeautifulSoup(test_response.text, 'html.parser')
                test_text = test_soup.get_text()
                
                # 检查是否需要验证
                verification_keywords = ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛']
                needs_verification = any(keyword in test_text for keyword in verification_keywords)
                
                if needs_verification:
                    logger.info("🔐 检测到需要验证，Session将在首次访问时处理")
                else:
                    # 检查是否有装备链接
                    equipment_links = [link.get('href') for link in test_soup.find_all('a', href=True) 
                                     if link.get('href') and 'view.php?eid=' in link.get('href')]
                    
                    if equipment_links:
                        logger.info(f"✅ 测试成功，找到 {len(equipment_links)} 个装备链接")
                        self.verified = True  # 标记为已验证状态
                    else:
                        logger.info("⚠️ 测试页面无装备链接，可能需要验证")
                
                time.sleep(3)  # 给服务器时间处理
                return True
            else:
                logger.warning(f"⚠️ 主页访问状态码异常: {response.status_code}")
                # 即使状态码异常也继续，有时内容仍然可用
                return True
                
        except Exception as e:
            logger.error(f"❌ Session初始化失败: {e}")
            return False

    def ask_ai_for_answer(self, question):
        """获取AI验证答案"""
        logger.info(f"🤖 AI验证问题: {question}")
        
        # 羽毛球有几根毛？
        if '羽毛球' in question and ('几根毛' in question or '多少毛' in question):
            return "16"
        
        # ZYZX小写
        if 'ZYZX' in question and ('小写' in question or '怎么写' in question):
            return "zyzx"
        
        # 中羽在线缩写
        if '中羽' in question and '缩写' in question:
            return "zyzx"
        
        # 数学计算
        try:
            # 清理问题文本
            math_text = re.sub(r'[=？?].*', '', question).strip()
            
            # 处理乘法符号
            math_text = math_text.replace('×', '*').replace('x', '*').replace('X', '*')
            
            # 验证是否为简单数学表达式
            if re.match(r'^\d+\s*[+\-*/]\s*\d+$', math_text):
                result = eval(math_text)
                logger.info(f"  🧮 计算结果: {math_text} = {result}")
                return str(result)
        except:
            pass
        
        logger.warning(f"⚠️ 未识别的验证问题: {question}")
        return None

    def handle_verification_once(self, url, max_retries=3):
        """改进的验证处理 - 支持多次重试"""
        for retry in range(max_retries):
            try:
                logger.info(f"🔍 访问URL (尝试 {retry+1}): {url}")
                response = self.session.get(url, timeout=30)
                
                # 如果状态码是200，直接检查内容
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    page_text = soup.get_text()
                    
                    # 检查是否有装备链接（成功页面的标志）
                    equipment_links = [link.get('href') for link in soup.find_all('a', href=True) 
                                     if link.get('href') and 'view.php?eid=' in link.get('href')]
                    
                    if equipment_links or '中羽在线' in soup.title.string if soup.title else False:
                        logger.info("✅ 成功获取页面内容")
                        return response.text
                
                # 解析页面内容（不管状态码）
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text()
                
                # 检查是否有验证表单
                verification_patterns = [
                    r'\d+[×*x]\d+=？', r'\d+[+]\d+=？', r'\d+[-]\d+=？',
                    r'羽毛球有几根毛', r'ZYZX.*?怎么写', r'中羽.*?缩写',
                ]
                
                has_verification = any(re.search(pattern, page_text, re.IGNORECASE) 
                                     for pattern in verification_patterns)
                
                verification_form = soup.find('form')
                
                if verification_form and has_verification:
                    logger.info(f"🔐 检测到验证页面，状态码: {response.status_code}")
                    if self.solve_verification(verification_form, page_text, url):
                        logger.info("✅ 验证提交成功，等待生效...")
                        time.sleep(5)  # 增加等待时间
                        
                        # 验证成功后，重新访问原URL
                        logger.info(f"🔄 验证后重新访问: {url}")
                        final_response = self.session.get(url, timeout=30)
                        logger.info(f"验证后访问状态码: {final_response.status_code}")
                        
                        # 检查重新访问是否成功
                        if final_response.status_code == 200:
                            final_soup = BeautifulSoup(final_response.text, 'html.parser')
                            final_text = final_soup.get_text()
                            
                            # 再次检查是否还有验证
                            final_has_verification = any(re.search(pattern, final_text, re.IGNORECASE) 
                                                       for pattern in verification_patterns)
                            
                            if not final_has_verification:
                                logger.info("✅ 验证后成功获取内容")
                                self.verified = True
                                return final_response.text
                            else:
                                logger.warning("⚠️ 验证后仍需要验证，可能需要重试")
                        
                        # 如果验证后仍然有问题，返回当前内容并标记已验证
                        logger.warning("⚠️ 验证后状态不理想，但继续尝试")
                        self.verified = True
                        return final_response.text if 'final_response' in locals() else response.text
                    else:
                        logger.warning(f"❌ 验证失败 (尝试 {retry+1})")
                        if retry < max_retries - 1:
                            time.sleep(3)
                            continue
                else:
                    # 无验证表单
                    if response.status_code == 200:
                        logger.info("✅ 直接访问成功")
                        return response.text
                    else:
                        logger.warning(f"⚠️ 状态码 {response.status_code}，但无验证表单")
                        # 尝试返回内容，可能仍然有用
                        return response.text
                        
            except Exception as e:
                logger.error(f"访问失败 (尝试 {retry+1}): {e}")
                if retry < max_retries - 1:
                    time.sleep(5)
                    continue
        
        logger.error(f"❌ {max_retries}次尝试后仍然失败")
        return None

    def solve_verification(self, form, page_text, original_url):
        """解决验证"""
        try:
            # 查找问题
            question_patterns = [
                r'(\d+[×*x]\d+)=？', r'(\d+[+]\d+)=？', r'(\d+[-]\d+)=？',
                r'羽毛球有几根毛', r'ZYZX.*?怎么写', r'中羽.*?缩写',
            ]
            
            question = None
            for pattern in question_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    question = matches[0]
                    break
            
            if not question:
                logger.warning("未找到验证问题")
                return False
            
            answer = self.ask_ai_for_answer(question)
            if not answer:
                logger.warning("无法获取验证答案")
                return False
            
            # 构建表单数据
            form_data = {}
            
            # 获取所有隐藏字段
            for inp in form.find_all('input', {'type': 'hidden'}):
                name = inp.get('name')
                value = inp.get('value', '')
                if name:
                    form_data[name] = value
            
            # 查找答案字段
            answer_fields = ['answer', 'verify', 'code', 'result', 'a']
            answer_field_found = False
            for field in answer_fields:
                input_field = form.find('input', {'name': field})
                if input_field:
                    form_data[field] = answer
                    answer_field_found = True
                    logger.info(f"  📝 答案字段: {field} = {answer}")
                    break
            
            if not answer_field_found:
                form_data['a'] = answer
                logger.info(f"  📝 默认答案字段: a = {answer}")
            
            # 提交验证
            action = form.get('action', original_url)
            submit_url = urljoin(self.base_url, action) if not action.startswith('http') else action
            
            logger.info(f"  📤 提交验证到: {submit_url}")
            response = self.session.post(submit_url, data=form_data, timeout=20)
            
            success = response.status_code in [200, 302]
            logger.info(f"  ✅ 验证结果: {'成功' if success else '失败'} (状态码: {response.status_code})")
            return success
                
        except Exception as e:
            logger.error(f"验证失败: {e}")
            return False

    def get_equipment_list(self, equipment_type_id=None):
        """改进的获取装备列表方法"""
        try:
            if equipment_type_id:
                list_url = f"{self.base_url}/cbo_eq/list.php?tid={equipment_type_id}"
                type_name = self.equipment_types.get(equipment_type_id, f"类型{equipment_type_id}")
                logger.info(f"🔍 获取{type_name}装备列表...")
            else:
                list_url = f"{self.base_url}/cbo_eq/list.php"
                logger.info("🔍 获取装备列表...")
            
            # 使用改进的验证处理方法
            html_content = self.handle_verification_once(list_url)
            if not html_content:
                logger.warning("未获取到列表页内容")
                
                # 尝试备用方法：直接访问（跳过验证处理）
                logger.info("🔄 尝试备用访问方法...")
                try:
                    backup_response = self.session.get(list_url, timeout=30)
                    if backup_response.status_code in [200, 400]:  # 400也可能有内容
                        html_content = backup_response.text
                        logger.info(f"备用方法状态码: {backup_response.status_code}")
                    else:
                        return []
                except:
                    return []
            
            soup = BeautifulSoup(html_content, 'html.parser')
            equipment_links = []
            
            # 查找装备链接
            all_links = soup.find_all('a', href=True)
            logger.debug(f"  页面总链接数: {len(all_links)}")
            
            for link in all_links:
                href = link.get('href')
                if href and 'view.php?eid=' in href:
                    if not href.startswith('http'):
                        href = urljoin(self.base_url, href)
                    equipment_links.append(href)
                    logger.debug(f"  找到装备链接: {href}")
            
            equipment_links = list(set(equipment_links))
            logger.info(f"  ✅ 找到 {len(equipment_links)} 个装备链接")
            
            # 显示前几个链接作为示例
            if equipment_links:
                logger.info(f"  示例链接: {equipment_links[0]}")
                if len(equipment_links) > 1:
                    logger.debug(f"  更多链接: {equipment_links[1:3]}")
            else:
                # 详细调试信息
                page_text = soup.get_text()
                
                # 检查常见的问题指示词
                verification_indicators = ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛']
                has_verification = any(indicator in page_text for indicator in verification_indicators)
                
                if has_verification:
                    logger.warning("  ⚠️ 页面包含验证内容")
                    # 显示验证相关内容
                    for indicator in verification_indicators:
                        if indicator in page_text:
                            logger.debug(f"  检测到验证指示词: {indicator}")
                else:
                    logger.warning("  ⚠️ 页面无验证但也无装备链接")
                    
                    # 检查页面标题
                    if soup.title:
                        logger.debug(f"  页面标题: {soup.title.string}")
                    
                    # 显示页面内容片段
                    logger.debug(f"  页面内容片段: {page_text[:500]}")
            
            return equipment_links
            
        except Exception as e:
            logger.error(f"获取装备列表失败: {e}")
            import traceback
            logger.debug(f"错误详情: {traceback.format_exc()}")
            return []

    def parse_equipment_detail(self, url):
        """解析装备详情"""
        try:
            eid_match = re.search(r'eid=(\d+)', url)
            equipment_id = eid_match.group(1) if eid_match else 'unknown'
            
            logger.info(f"  📋 解析装备详情 (ID: {equipment_id})")
            
            html_content = self.handle_verification_once(url)
            if not html_content:
                logger.warning(f"    未获取到详情页内容: {url}")
                return None
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 初始化数据
            equipment_data = {
                'equipment_id': equipment_id,
                'equipment_name': '',
                'equipment_type': '',
                'equipment_brand': '',
                'equipment_series': '',
                'equipment_description': '',
                'release_date': '',
                'equipment_introduction': '',
                'specifications': '',
                'frame_material': '',
                'shaft_material': '',
                'weight': '',
                'length': '',
                'grip_size': '',
                'shaft_stiffness': '',
                'string_tension': '',
                'balance_point': '',
                'new_avg_price': '',
                'used_avg_price': '',
                'total_registered_users': '',
                'detail_url': url,
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 提取标题
            if soup.title:
                title = soup.title.string.strip()
                equipment_name = title.replace('中羽在线 badmintoncn.com', '').strip()
                if equipment_name:
                    equipment_data['equipment_name'] = equipment_name[:100]
                    logger.info(f"    📝 装备名称: {equipment_name}")
            
            # 提取表格信息
            self.extract_table_info(soup, equipment_data)
            
            # 获取价格信息
            self.get_price_info(equipment_id, equipment_data)
            
            # 显示数据完整性
            filled = sum(1 for v in equipment_data.values() if v and v != '')
            total = len(equipment_data)
            logger.info(f"    📊 数据完整性: {filled/total*100:.1f}% ({filled}/{total})")
            
            return equipment_data
            
        except Exception as e:
            logger.error(f"解析装备详情失败: {e}")
            return None

    def extract_table_info(self, soup, equipment_data):
        """提取表格信息"""
        try:
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        # 字段映射
                        mappings = {
                            '装备类型': 'equipment_type', '类型': 'equipment_type',
                            '装备品牌': 'equipment_brand', '品牌': 'equipment_brand',
                            '装备系列': 'equipment_series', '系列': 'equipment_series',
                            '上市日期': 'release_date', '发布日期': 'release_date',
                            '拍框材质': 'frame_material', '框架材质': 'frame_material',
                            '拍杆材质': 'shaft_material', '中管材质': 'shaft_material',
                            '重量': 'weight', '拍重': 'weight', '拍身重量': 'weight',
                            '长度': 'length', '拍身长度': 'length',
                            '手柄尺寸': 'grip_size', '拍柄粗细': 'grip_size',
                            '中管韧度': 'shaft_stiffness', '硬度': 'shaft_stiffness',
                            '拉线磅数': 'string_tension', '穿线磅数': 'string_tension',
                            '平衡点': 'balance_point', '重心': 'balance_point',
                        }
                        
                        for keyword, field in mappings.items():
                            if keyword in key and not equipment_data[field]:
                                equipment_data[field] = value
                                logger.debug(f"    ✅ 映射: {keyword} -> {value}")
                                break
                        
                        # 规格参数
                        if key and value:
                            if equipment_data['specifications']:
                                equipment_data['specifications'] += f"; {key}: {value}"
                            else:
                                equipment_data['specifications'] = f"{key}: {value}"
                                
        except Exception as e:
            logger.error(f"提取表格信息失败: {e}")

    def get_price_info(self, equipment_id, equipment_data):
        """获取价格信息"""
        try:
            price_url = f"{self.base_url}/cbo_eq/view_buy.php?eid={equipment_id}"
            price_html = self.handle_verification_once(price_url)
            
            if not price_html:
                logger.debug("    ⚠️ 未获取到价格页面")
                return
                
            price_soup = BeautifulSoup(price_html, 'html.parser')
            price_text = price_soup.get_text()
            
            # 价格模式
            patterns = {
                'new_avg_price': [r'最近全新均价[：:]\s*(\d+)', r'全新均价[：:]\s*(\d+)'],
                'used_avg_price': [r'最近二手均价[：:]\s*(\d+)', r'二手均价[：:]\s*(\d+)'],
                'total_registered_users': [r'总登记球友[：:]\s*(\d+)', r'登记球友[：:]\s*(\d+)'],
            }
            
            for field, field_patterns in patterns.items():
                for pattern in field_patterns:
                    match = re.search(pattern, price_text)
                    if match:
                        equipment_data[field] = match.group(1)
                        logger.debug(f"    💰 {field}: {match.group(1)}")
                        break
                        
        except Exception as e:
            logger.error(f"获取价格信息失败: {e}")

    def crawl_by_type(self, equipment_type_id, max_items=5):
        """按类型爬取"""
        type_name = self.equipment_types.get(equipment_type_id, f"类型{equipment_type_id}")
        logger.info(f"\n📦 开始爬取 {type_name}")
        
        equipment_links = self.get_equipment_list(equipment_type_id)
        if not equipment_links:
            logger.warning(f"  ⚠️ {type_name} 未找到装备链接")
            return []
        
        data = []
        for i, link in enumerate(equipment_links[:max_items]):
            logger.info(f"  🔍 处理装备 ({i+1}/{min(max_items, len(equipment_links))})")
            
            equipment_data = self.parse_equipment_detail(link)
            if equipment_data:
                equipment_data['equipment_type'] = type_name
                data.append(equipment_data)
                logger.info(f"    ✅ 成功提取装备: {equipment_data['equipment_name']}")
            else:
                logger.warning(f"    ❌ 提取失败")
            
            time.sleep(2)  # 减少延时，Session已建立
        
        logger.info(f"  📊 {type_name} 完成: {len(data)}/{min(max_items, len(equipment_links))} 个装备")
        return data

    def crawl_all_types(self, max_items_per_type=3):
        """爬取所有类型"""
        logger.info("🚀 开始爬取所有装备类型...")
        
        # 首先初始化Session
        if not self.initialize_session():
            logger.error("❌ Session初始化失败，无法继续")
            return []
        
        all_data = []
        successful_types = 0
        
        # 策略1：按类型依次爬取
        for type_id in self.equipment_types.keys():
            type_name = self.equipment_types[type_id]
            logger.info(f"\n📦 开始爬取 {type_name} (类型ID: {type_id})")
            
            type_data = self.crawl_by_type(type_id, max_items_per_type)
            
            if type_data:
                all_data.extend(type_data)
                successful_types += 1
                logger.info(f"✅ {type_name} 爬取成功，获得 {len(type_data)} 条数据")
            else:
                logger.warning(f"⚠️ {type_name} 爬取失败")
            
            # 类型间延时
            if type_id < max(self.equipment_types.keys()):
                time.sleep(5)  # 增加延时
        
        logger.info(f"\n📊 第一轮结果: {successful_types}/{len(self.equipment_types)} 个类型成功")
        
        # 策略2：如果第一轮结果不理想，尝试无类型限制的通用方法
        if len(all_data) < 3:  # 如果数据太少
            logger.info("\n🔄 数据量较少，尝试通用方法...")
            
            # 尝试不指定类型的通用列表页
            general_data = self.try_general_crawl(max_items=max_items_per_type * 2)
            if general_data:
                all_data.extend(general_data)
                logger.info(f"✅ 通用方法获得额外 {len(general_data)} 条数据")
        
        # 去重
        unique_data = []
        seen_ids = set()
        for item in all_data:
            item_id = item.get('equipment_id', '')
            if item_id and item_id not in seen_ids:
                unique_data.append(item)
                seen_ids.add(item_id)
        
        logger.info(f"\n🎉 爬取完成！总共 {len(unique_data)} 条唯一数据")
        logger.info(f"成功类型数: {successful_types}/{len(self.equipment_types)}")
        
        return unique_data

    def try_general_crawl(self, max_items=6):
        """尝试通用爬取方法"""
        try:
            logger.info("🌐 尝试通用装备页面...")
            
            # 不指定类型，获取通用装备列表
            equipment_links = self.get_equipment_list()  # 不传type_id
            
            if not equipment_links:
                logger.warning("通用方法也未找到装备链接")
                return []
            
            logger.info(f"通用方法找到 {len(equipment_links)} 个装备链接")
            
            data = []
            for i, link in enumerate(equipment_links[:max_items]):
                logger.info(f"  🔍 通用方法处理装备 ({i+1}/{min(max_items, len(equipment_links))})")
                
                equipment_data = self.parse_equipment_detail(link)
                if equipment_data:
                    # 尝试从URL或其他信息推断类型
                    self.infer_equipment_type(equipment_data)
                    data.append(equipment_data)
                    logger.info(f"    ✅ 成功: {equipment_data['equipment_name']}")
                else:
                    logger.warning(f"    ❌ 失败")
                
                time.sleep(3)  # 较长延时
            
            return data
            
        except Exception as e:
            logger.error(f"通用爬取失败: {e}")
            return []

    def infer_equipment_type(self, equipment_data):
        """从装备数据推断类型"""
        try:
            name = equipment_data.get('equipment_name', '').lower()
            
            # 基于名称关键词推断类型
            type_keywords = {
                '羽毛球拍': ['拍', 'racket', 'racquet'],
                '羽毛球鞋': ['鞋', 'shoes', 'shoe'],
                '运动包': ['包', 'bag', '袋'],
                '羽毛球线': ['线', 'string', '弦'],
                '羽毛球': ['球', 'shuttle', 'cock'],
                '运动服饰': ['服', '衣', 'shirt', 'wear'],
                '手胶': ['胶', 'grip', '柄皮']
            }
            
            for eq_type, keywords in type_keywords.items():
                if any(keyword in name for keyword in keywords):
                    if not equipment_data.get('equipment_type'):
                        equipment_data['equipment_type'] = eq_type
                        logger.debug(f"    推断类型: {eq_type}")
                    break
                    
        except Exception as e:
            logger.debug(f"类型推断失败: {e}")

    def save_data(self, data):
        """保存数据"""
        if not data:
            logger.warning("没有数据需要保存")
            return
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # CSV
        csv_file = f"output/equipment_{timestamp}.csv"
        fieldnames = list(data[0].keys())
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        # JSON
        json_file = f"output/equipment_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📁 数据已保存: {csv_file}, {json_file}")
        return csv_file, json_file

    def analyze_data(self, data):
        """分析数据"""
        if not data:
            return
            
        logger.info("\n📊 数据分析:")
        logger.info(f"总数量: {len(data)}")
        
        # 类型分布
        types = {}
        brands = {}
        
        for item in data:
            eq_type = item.get('equipment_type', '未知')
            types[eq_type] = types.get(eq_type, 0) + 1
            
            brand = item.get('equipment_brand', '未知')
            brands[brand] = brands.get(brand, 0) + 1
        
        logger.info("\n类型分布:")
        for eq_type, count in sorted(types.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {eq_type}: {count}")
        
        logger.info("\n品牌分布:")
        for brand, count in sorted(brands.items(), key=lambda x: x[1], reverse=True)[:5]:
            logger.info(f"  {brand}: {count}")

def main():
    """主函数"""
    crawler = FinalBadmintonCrawler()
    
    # 爬取数据
    data = crawler.crawl_all_types(max_items_per_type=3)
    
    if data:
        # 保存和分析
        crawler.save_data(data)
        crawler.analyze_data(data)
    else:
        logger.error("未获取到数据")

if __name__ == "__main__":
    main() 