// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/model.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fmodel_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fmodel_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fmodel_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fmodel_2eproto;
namespace tensorflow {
namespace data {
namespace model {
class ModelProto;
class ModelProtoDefaultTypeInternal;
extern ModelProtoDefaultTypeInternal _ModelProto_default_instance_;
class ModelProto_Node;
class ModelProto_NodeDefaultTypeInternal;
extern ModelProto_NodeDefaultTypeInternal _ModelProto_Node_default_instance_;
class ModelProto_Node_Parameter;
class ModelProto_Node_ParameterDefaultTypeInternal;
extern ModelProto_Node_ParameterDefaultTypeInternal _ModelProto_Node_Parameter_default_instance_;
class ModelProto_OptimizationParams;
class ModelProto_OptimizationParamsDefaultTypeInternal;
extern ModelProto_OptimizationParamsDefaultTypeInternal _ModelProto_OptimizationParams_default_instance_;
}  // namespace model
}  // namespace data
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::data::model::ModelProto* Arena::CreateMaybeMessage<::tensorflow::data::model::ModelProto>(Arena*);
template<> ::tensorflow::data::model::ModelProto_Node* Arena::CreateMaybeMessage<::tensorflow::data::model::ModelProto_Node>(Arena*);
template<> ::tensorflow::data::model::ModelProto_Node_Parameter* Arena::CreateMaybeMessage<::tensorflow::data::model::ModelProto_Node_Parameter>(Arena*);
template<> ::tensorflow::data::model::ModelProto_OptimizationParams* Arena::CreateMaybeMessage<::tensorflow::data::model::ModelProto_OptimizationParams>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace data {
namespace model {

enum NodeClass : int {
  UNKNOWN = 0,
  INTERLEAVE_MANY = 1,
  ASYNC_INTERLEAVE_MANY = 2,
  KNOWN_RATIO = 3,
  ASYNC_KNOWN_RATIO = 4,
  UNKNOWN_RATIO = 5,
  NodeClass_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  NodeClass_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool NodeClass_IsValid(int value);
constexpr NodeClass NodeClass_MIN = UNKNOWN;
constexpr NodeClass NodeClass_MAX = UNKNOWN_RATIO;
constexpr int NodeClass_ARRAYSIZE = NodeClass_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* NodeClass_descriptor();
template<typename T>
inline const std::string& NodeClass_Name(T enum_t_value) {
  static_assert(::std::is_same<T, NodeClass>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function NodeClass_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    NodeClass_descriptor(), enum_t_value);
}
inline bool NodeClass_Parse(
    const std::string& name, NodeClass* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<NodeClass>(
    NodeClass_descriptor(), name, value);
}
enum AutotuneAlgorithm : int {
  HILL_CLIMB = 0,
  GRADIENT_DESCENT = 1,
  AutotuneAlgorithm_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  AutotuneAlgorithm_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool AutotuneAlgorithm_IsValid(int value);
constexpr AutotuneAlgorithm AutotuneAlgorithm_MIN = HILL_CLIMB;
constexpr AutotuneAlgorithm AutotuneAlgorithm_MAX = GRADIENT_DESCENT;
constexpr int AutotuneAlgorithm_ARRAYSIZE = AutotuneAlgorithm_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AutotuneAlgorithm_descriptor();
template<typename T>
inline const std::string& AutotuneAlgorithm_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AutotuneAlgorithm>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AutotuneAlgorithm_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AutotuneAlgorithm_descriptor(), enum_t_value);
}
inline bool AutotuneAlgorithm_Parse(
    const std::string& name, AutotuneAlgorithm* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AutotuneAlgorithm>(
    AutotuneAlgorithm_descriptor(), name, value);
}
// ===================================================================

class ModelProto_Node_Parameter :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.model.ModelProto.Node.Parameter) */ {
 public:
  ModelProto_Node_Parameter();
  virtual ~ModelProto_Node_Parameter();

  ModelProto_Node_Parameter(const ModelProto_Node_Parameter& from);
  ModelProto_Node_Parameter(ModelProto_Node_Parameter&& from) noexcept
    : ModelProto_Node_Parameter() {
    *this = ::std::move(from);
  }

  inline ModelProto_Node_Parameter& operator=(const ModelProto_Node_Parameter& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelProto_Node_Parameter& operator=(ModelProto_Node_Parameter&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ModelProto_Node_Parameter& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ModelProto_Node_Parameter* internal_default_instance() {
    return reinterpret_cast<const ModelProto_Node_Parameter*>(
               &_ModelProto_Node_Parameter_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ModelProto_Node_Parameter& a, ModelProto_Node_Parameter& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelProto_Node_Parameter* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelProto_Node_Parameter* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ModelProto_Node_Parameter* New() const final {
    return CreateMaybeMessage<ModelProto_Node_Parameter>(nullptr);
  }

  ModelProto_Node_Parameter* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ModelProto_Node_Parameter>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ModelProto_Node_Parameter& from);
  void MergeFrom(const ModelProto_Node_Parameter& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelProto_Node_Parameter* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.model.ModelProto.Node.Parameter";
  }
  protected:
  explicit ModelProto_Node_Parameter(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fmodel_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kValueFieldNumber = 2,
    kStateValueFieldNumber = 3,
    kMinFieldNumber = 4,
    kMaxFieldNumber = 5,
    kTunableFieldNumber = 6,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // double value = 2;
  void clear_value();
  double value() const;
  void set_value(double value);

  // double state_value = 3;
  void clear_state_value();
  double state_value() const;
  void set_state_value(double value);

  // double min = 4;
  void clear_min();
  double min() const;
  void set_min(double value);

  // double max = 5;
  void clear_max();
  double max() const;
  void set_max(double value);

  // bool tunable = 6;
  void clear_tunable();
  bool tunable() const;
  void set_tunable(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.data.model.ModelProto.Node.Parameter)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  double value_;
  double state_value_;
  double min_;
  double max_;
  bool tunable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ModelProto_Node :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.model.ModelProto.Node) */ {
 public:
  ModelProto_Node();
  virtual ~ModelProto_Node();

  ModelProto_Node(const ModelProto_Node& from);
  ModelProto_Node(ModelProto_Node&& from) noexcept
    : ModelProto_Node() {
    *this = ::std::move(from);
  }

  inline ModelProto_Node& operator=(const ModelProto_Node& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelProto_Node& operator=(ModelProto_Node&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ModelProto_Node& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ModelProto_Node* internal_default_instance() {
    return reinterpret_cast<const ModelProto_Node*>(
               &_ModelProto_Node_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ModelProto_Node& a, ModelProto_Node& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelProto_Node* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelProto_Node* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ModelProto_Node* New() const final {
    return CreateMaybeMessage<ModelProto_Node>(nullptr);
  }

  ModelProto_Node* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ModelProto_Node>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ModelProto_Node& from);
  void MergeFrom(const ModelProto_Node& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelProto_Node* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.model.ModelProto.Node";
  }
  protected:
  explicit ModelProto_Node(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fmodel_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef ModelProto_Node_Parameter Parameter;

  // accessors -------------------------------------------------------

  enum : int {
    kParametersFieldNumber = 11,
    kInputsFieldNumber = 14,
    kNameFieldNumber = 2,
    kIdFieldNumber = 1,
    kBufferedBytesFieldNumber = 4,
    kBufferedElementsFieldNumber = 5,
    kBytesConsumedFieldNumber = 6,
    kBytesProducedFieldNumber = 7,
    kNumElementsFieldNumber = 8,
    kProcessingTimeFieldNumber = 9,
    kAutotuneFieldNumber = 3,
    kRecordMetricsFieldNumber = 10,
    kNodeClassFieldNumber = 15,
    kInputProcessingTimeSumFieldNumber = 12,
    kInputProcessingTimeCountFieldNumber = 13,
    kRatioFieldNumber = 16,
    kMemoryRatioFieldNumber = 17,
  };
  // repeated .tensorflow.data.model.ModelProto.Node.Parameter parameters = 11;
  int parameters_size() const;
  void clear_parameters();
  ::tensorflow::data::model::ModelProto_Node_Parameter* mutable_parameters(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node_Parameter >*
      mutable_parameters();
  const ::tensorflow::data::model::ModelProto_Node_Parameter& parameters(int index) const;
  ::tensorflow::data::model::ModelProto_Node_Parameter* add_parameters();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node_Parameter >&
      parameters() const;

  // repeated .tensorflow.data.model.ModelProto.Node inputs = 14;
  int inputs_size() const;
  void clear_inputs();
  ::tensorflow::data::model::ModelProto_Node* mutable_inputs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node >*
      mutable_inputs();
  const ::tensorflow::data::model::ModelProto_Node& inputs(int index) const;
  ::tensorflow::data::model::ModelProto_Node* add_inputs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node >&
      inputs() const;

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 buffered_bytes = 4;
  void clear_buffered_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 buffered_bytes() const;
  void set_buffered_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 buffered_elements = 5;
  void clear_buffered_elements();
  ::PROTOBUF_NAMESPACE_ID::int64 buffered_elements() const;
  void set_buffered_elements(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 bytes_consumed = 6;
  void clear_bytes_consumed();
  ::PROTOBUF_NAMESPACE_ID::int64 bytes_consumed() const;
  void set_bytes_consumed(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 bytes_produced = 7;
  void clear_bytes_produced();
  ::PROTOBUF_NAMESPACE_ID::int64 bytes_produced() const;
  void set_bytes_produced(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 num_elements = 8;
  void clear_num_elements();
  ::PROTOBUF_NAMESPACE_ID::int64 num_elements() const;
  void set_num_elements(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 processing_time = 9;
  void clear_processing_time();
  ::PROTOBUF_NAMESPACE_ID::int64 processing_time() const;
  void set_processing_time(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool autotune = 3;
  void clear_autotune();
  bool autotune() const;
  void set_autotune(bool value);

  // bool record_metrics = 10;
  void clear_record_metrics();
  bool record_metrics() const;
  void set_record_metrics(bool value);

  // .tensorflow.data.model.NodeClass node_class = 15;
  void clear_node_class();
  ::tensorflow::data::model::NodeClass node_class() const;
  void set_node_class(::tensorflow::data::model::NodeClass value);

  // double input_processing_time_sum = 12;
  void clear_input_processing_time_sum();
  double input_processing_time_sum() const;
  void set_input_processing_time_sum(double value);

  // int64 input_processing_time_count = 13;
  void clear_input_processing_time_count();
  ::PROTOBUF_NAMESPACE_ID::int64 input_processing_time_count() const;
  void set_input_processing_time_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // double ratio = 16;
  void clear_ratio();
  double ratio() const;
  void set_ratio(double value);

  // double memory_ratio = 17;
  void clear_memory_ratio();
  double memory_ratio() const;
  void set_memory_ratio(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.data.model.ModelProto.Node)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node_Parameter > parameters_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node > inputs_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  ::PROTOBUF_NAMESPACE_ID::int64 buffered_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 buffered_elements_;
  ::PROTOBUF_NAMESPACE_ID::int64 bytes_consumed_;
  ::PROTOBUF_NAMESPACE_ID::int64 bytes_produced_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_elements_;
  ::PROTOBUF_NAMESPACE_ID::int64 processing_time_;
  bool autotune_;
  bool record_metrics_;
  int node_class_;
  double input_processing_time_sum_;
  ::PROTOBUF_NAMESPACE_ID::int64 input_processing_time_count_;
  double ratio_;
  double memory_ratio_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ModelProto_OptimizationParams :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.model.ModelProto.OptimizationParams) */ {
 public:
  ModelProto_OptimizationParams();
  virtual ~ModelProto_OptimizationParams();

  ModelProto_OptimizationParams(const ModelProto_OptimizationParams& from);
  ModelProto_OptimizationParams(ModelProto_OptimizationParams&& from) noexcept
    : ModelProto_OptimizationParams() {
    *this = ::std::move(from);
  }

  inline ModelProto_OptimizationParams& operator=(const ModelProto_OptimizationParams& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelProto_OptimizationParams& operator=(ModelProto_OptimizationParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ModelProto_OptimizationParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ModelProto_OptimizationParams* internal_default_instance() {
    return reinterpret_cast<const ModelProto_OptimizationParams*>(
               &_ModelProto_OptimizationParams_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ModelProto_OptimizationParams& a, ModelProto_OptimizationParams& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelProto_OptimizationParams* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelProto_OptimizationParams* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ModelProto_OptimizationParams* New() const final {
    return CreateMaybeMessage<ModelProto_OptimizationParams>(nullptr);
  }

  ModelProto_OptimizationParams* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ModelProto_OptimizationParams>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ModelProto_OptimizationParams& from);
  void MergeFrom(const ModelProto_OptimizationParams& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelProto_OptimizationParams* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.model.ModelProto.OptimizationParams";
  }
  protected:
  explicit ModelProto_OptimizationParams(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fmodel_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCpuBudgetFieldNumber = 2,
    kRamBudgetFieldNumber = 3,
    kModelInputTimeFieldNumber = 4,
    kAlgorithmFieldNumber = 1,
  };
  // int64 cpu_budget = 2;
  void clear_cpu_budget();
  ::PROTOBUF_NAMESPACE_ID::int64 cpu_budget() const;
  void set_cpu_budget(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 ram_budget = 3;
  void clear_ram_budget();
  ::PROTOBUF_NAMESPACE_ID::int64 ram_budget() const;
  void set_ram_budget(::PROTOBUF_NAMESPACE_ID::int64 value);

  // double model_input_time = 4;
  void clear_model_input_time();
  double model_input_time() const;
  void set_model_input_time(double value);

  // .tensorflow.data.model.AutotuneAlgorithm algorithm = 1;
  void clear_algorithm();
  ::tensorflow::data::model::AutotuneAlgorithm algorithm() const;
  void set_algorithm(::tensorflow::data::model::AutotuneAlgorithm value);

  // @@protoc_insertion_point(class_scope:tensorflow.data.model.ModelProto.OptimizationParams)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 cpu_budget_;
  ::PROTOBUF_NAMESPACE_ID::int64 ram_budget_;
  double model_input_time_;
  int algorithm_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ModelProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.model.ModelProto) */ {
 public:
  ModelProto();
  virtual ~ModelProto();

  ModelProto(const ModelProto& from);
  ModelProto(ModelProto&& from) noexcept
    : ModelProto() {
    *this = ::std::move(from);
  }

  inline ModelProto& operator=(const ModelProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelProto& operator=(ModelProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ModelProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ModelProto* internal_default_instance() {
    return reinterpret_cast<const ModelProto*>(
               &_ModelProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ModelProto& a, ModelProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ModelProto* New() const final {
    return CreateMaybeMessage<ModelProto>(nullptr);
  }

  ModelProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ModelProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ModelProto& from);
  void MergeFrom(const ModelProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.model.ModelProto";
  }
  protected:
  explicit ModelProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fmodel_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef ModelProto_Node Node;
  typedef ModelProto_OptimizationParams OptimizationParams;

  // accessors -------------------------------------------------------

  enum : int {
    kOutputFieldNumber = 1,
    kOptimizationParamsFieldNumber = 4,
    kIdCounterFieldNumber = 2,
    kCollectResourceUsageFieldNumber = 3,
  };
  // .tensorflow.data.model.ModelProto.Node output = 1;
  bool has_output() const;
  void clear_output();
  const ::tensorflow::data::model::ModelProto_Node& output() const;
  ::tensorflow::data::model::ModelProto_Node* release_output();
  ::tensorflow::data::model::ModelProto_Node* mutable_output();
  void set_allocated_output(::tensorflow::data::model::ModelProto_Node* output);
  void unsafe_arena_set_allocated_output(
      ::tensorflow::data::model::ModelProto_Node* output);
  ::tensorflow::data::model::ModelProto_Node* unsafe_arena_release_output();

  // .tensorflow.data.model.ModelProto.OptimizationParams optimization_params = 4;
  bool has_optimization_params() const;
  void clear_optimization_params();
  const ::tensorflow::data::model::ModelProto_OptimizationParams& optimization_params() const;
  ::tensorflow::data::model::ModelProto_OptimizationParams* release_optimization_params();
  ::tensorflow::data::model::ModelProto_OptimizationParams* mutable_optimization_params();
  void set_allocated_optimization_params(::tensorflow::data::model::ModelProto_OptimizationParams* optimization_params);
  void unsafe_arena_set_allocated_optimization_params(
      ::tensorflow::data::model::ModelProto_OptimizationParams* optimization_params);
  ::tensorflow::data::model::ModelProto_OptimizationParams* unsafe_arena_release_optimization_params();

  // int64 id_counter = 2;
  void clear_id_counter();
  ::PROTOBUF_NAMESPACE_ID::int64 id_counter() const;
  void set_id_counter(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool collect_resource_usage = 3;
  void clear_collect_resource_usage();
  bool collect_resource_usage() const;
  void set_collect_resource_usage(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.data.model.ModelProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::data::model::ModelProto_Node* output_;
  ::tensorflow::data::model::ModelProto_OptimizationParams* optimization_params_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_counter_;
  bool collect_resource_usage_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ModelProto_Node_Parameter

// string name = 1;
inline void ModelProto_Node_Parameter::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ModelProto_Node_Parameter::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.name)
  return name_.Get();
}
inline void ModelProto_Node_Parameter::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.name)
}
inline void ModelProto_Node_Parameter::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.data.model.ModelProto.Node.Parameter.name)
}
inline void ModelProto_Node_Parameter::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.data.model.ModelProto.Node.Parameter.name)
}
inline void ModelProto_Node_Parameter::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.data.model.ModelProto.Node.Parameter.name)
}
inline std::string* ModelProto_Node_Parameter::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.Node.Parameter.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ModelProto_Node_Parameter::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.data.model.ModelProto.Node.Parameter.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ModelProto_Node_Parameter::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.model.ModelProto.Node.Parameter.name)
}
inline std::string* ModelProto_Node_Parameter::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.data.model.ModelProto.Node.Parameter.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ModelProto_Node_Parameter::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.data.model.ModelProto.Node.Parameter.name)
}

// double value = 2;
inline void ModelProto_Node_Parameter::clear_value() {
  value_ = 0;
}
inline double ModelProto_Node_Parameter::value() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.value)
  return value_;
}
inline void ModelProto_Node_Parameter::set_value(double value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.value)
}

// double state_value = 3;
inline void ModelProto_Node_Parameter::clear_state_value() {
  state_value_ = 0;
}
inline double ModelProto_Node_Parameter::state_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.state_value)
  return state_value_;
}
inline void ModelProto_Node_Parameter::set_state_value(double value) {
  
  state_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.state_value)
}

// double min = 4;
inline void ModelProto_Node_Parameter::clear_min() {
  min_ = 0;
}
inline double ModelProto_Node_Parameter::min() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.min)
  return min_;
}
inline void ModelProto_Node_Parameter::set_min(double value) {
  
  min_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.min)
}

// double max = 5;
inline void ModelProto_Node_Parameter::clear_max() {
  max_ = 0;
}
inline double ModelProto_Node_Parameter::max() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.max)
  return max_;
}
inline void ModelProto_Node_Parameter::set_max(double value) {
  
  max_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.max)
}

// bool tunable = 6;
inline void ModelProto_Node_Parameter::clear_tunable() {
  tunable_ = false;
}
inline bool ModelProto_Node_Parameter::tunable() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.tunable)
  return tunable_;
}
inline void ModelProto_Node_Parameter::set_tunable(bool value) {
  
  tunable_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.tunable)
}

// -------------------------------------------------------------------

// ModelProto_Node

// int64 id = 1;
inline void ModelProto_Node::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto_Node::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.id)
  return id_;
}
inline void ModelProto_Node::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.id)
}

// string name = 2;
inline void ModelProto_Node::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ModelProto_Node::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.name)
  return name_.Get();
}
inline void ModelProto_Node::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.name)
}
inline void ModelProto_Node::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.data.model.ModelProto.Node.name)
}
inline void ModelProto_Node::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.data.model.ModelProto.Node.name)
}
inline void ModelProto_Node::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.data.model.ModelProto.Node.name)
}
inline std::string* ModelProto_Node::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.Node.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ModelProto_Node::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.data.model.ModelProto.Node.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ModelProto_Node::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.model.ModelProto.Node.name)
}
inline std::string* ModelProto_Node::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.data.model.ModelProto.Node.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ModelProto_Node::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.data.model.ModelProto.Node.name)
}

// bool autotune = 3;
inline void ModelProto_Node::clear_autotune() {
  autotune_ = false;
}
inline bool ModelProto_Node::autotune() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.autotune)
  return autotune_;
}
inline void ModelProto_Node::set_autotune(bool value) {
  
  autotune_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.autotune)
}

// int64 buffered_bytes = 4;
inline void ModelProto_Node::clear_buffered_bytes() {
  buffered_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto_Node::buffered_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.buffered_bytes)
  return buffered_bytes_;
}
inline void ModelProto_Node::set_buffered_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  buffered_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.buffered_bytes)
}

// int64 buffered_elements = 5;
inline void ModelProto_Node::clear_buffered_elements() {
  buffered_elements_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto_Node::buffered_elements() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.buffered_elements)
  return buffered_elements_;
}
inline void ModelProto_Node::set_buffered_elements(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  buffered_elements_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.buffered_elements)
}

// int64 bytes_consumed = 6;
inline void ModelProto_Node::clear_bytes_consumed() {
  bytes_consumed_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto_Node::bytes_consumed() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.bytes_consumed)
  return bytes_consumed_;
}
inline void ModelProto_Node::set_bytes_consumed(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  bytes_consumed_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.bytes_consumed)
}

// int64 bytes_produced = 7;
inline void ModelProto_Node::clear_bytes_produced() {
  bytes_produced_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto_Node::bytes_produced() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.bytes_produced)
  return bytes_produced_;
}
inline void ModelProto_Node::set_bytes_produced(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  bytes_produced_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.bytes_produced)
}

// int64 num_elements = 8;
inline void ModelProto_Node::clear_num_elements() {
  num_elements_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto_Node::num_elements() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.num_elements)
  return num_elements_;
}
inline void ModelProto_Node::set_num_elements(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_elements_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.num_elements)
}

// int64 processing_time = 9;
inline void ModelProto_Node::clear_processing_time() {
  processing_time_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto_Node::processing_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.processing_time)
  return processing_time_;
}
inline void ModelProto_Node::set_processing_time(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  processing_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.processing_time)
}

// bool record_metrics = 10;
inline void ModelProto_Node::clear_record_metrics() {
  record_metrics_ = false;
}
inline bool ModelProto_Node::record_metrics() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.record_metrics)
  return record_metrics_;
}
inline void ModelProto_Node::set_record_metrics(bool value) {
  
  record_metrics_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.record_metrics)
}

// repeated .tensorflow.data.model.ModelProto.Node.Parameter parameters = 11;
inline int ModelProto_Node::parameters_size() const {
  return parameters_.size();
}
inline void ModelProto_Node::clear_parameters() {
  parameters_.Clear();
}
inline ::tensorflow::data::model::ModelProto_Node_Parameter* ModelProto_Node::mutable_parameters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.Node.parameters)
  return parameters_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node_Parameter >*
ModelProto_Node::mutable_parameters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.model.ModelProto.Node.parameters)
  return &parameters_;
}
inline const ::tensorflow::data::model::ModelProto_Node_Parameter& ModelProto_Node::parameters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.parameters)
  return parameters_.Get(index);
}
inline ::tensorflow::data::model::ModelProto_Node_Parameter* ModelProto_Node::add_parameters() {
  // @@protoc_insertion_point(field_add:tensorflow.data.model.ModelProto.Node.parameters)
  return parameters_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node_Parameter >&
ModelProto_Node::parameters() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.model.ModelProto.Node.parameters)
  return parameters_;
}

// double input_processing_time_sum = 12;
inline void ModelProto_Node::clear_input_processing_time_sum() {
  input_processing_time_sum_ = 0;
}
inline double ModelProto_Node::input_processing_time_sum() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.input_processing_time_sum)
  return input_processing_time_sum_;
}
inline void ModelProto_Node::set_input_processing_time_sum(double value) {
  
  input_processing_time_sum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.input_processing_time_sum)
}

// int64 input_processing_time_count = 13;
inline void ModelProto_Node::clear_input_processing_time_count() {
  input_processing_time_count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto_Node::input_processing_time_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.input_processing_time_count)
  return input_processing_time_count_;
}
inline void ModelProto_Node::set_input_processing_time_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  input_processing_time_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.input_processing_time_count)
}

// repeated .tensorflow.data.model.ModelProto.Node inputs = 14;
inline int ModelProto_Node::inputs_size() const {
  return inputs_.size();
}
inline void ModelProto_Node::clear_inputs() {
  inputs_.Clear();
}
inline ::tensorflow::data::model::ModelProto_Node* ModelProto_Node::mutable_inputs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.Node.inputs)
  return inputs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node >*
ModelProto_Node::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.model.ModelProto.Node.inputs)
  return &inputs_;
}
inline const ::tensorflow::data::model::ModelProto_Node& ModelProto_Node::inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.inputs)
  return inputs_.Get(index);
}
inline ::tensorflow::data::model::ModelProto_Node* ModelProto_Node::add_inputs() {
  // @@protoc_insertion_point(field_add:tensorflow.data.model.ModelProto.Node.inputs)
  return inputs_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node >&
ModelProto_Node::inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.model.ModelProto.Node.inputs)
  return inputs_;
}

// .tensorflow.data.model.NodeClass node_class = 15;
inline void ModelProto_Node::clear_node_class() {
  node_class_ = 0;
}
inline ::tensorflow::data::model::NodeClass ModelProto_Node::node_class() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.node_class)
  return static_cast< ::tensorflow::data::model::NodeClass >(node_class_);
}
inline void ModelProto_Node::set_node_class(::tensorflow::data::model::NodeClass value) {
  
  node_class_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.node_class)
}

// double ratio = 16;
inline void ModelProto_Node::clear_ratio() {
  ratio_ = 0;
}
inline double ModelProto_Node::ratio() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.ratio)
  return ratio_;
}
inline void ModelProto_Node::set_ratio(double value) {
  
  ratio_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.ratio)
}

// double memory_ratio = 17;
inline void ModelProto_Node::clear_memory_ratio() {
  memory_ratio_ = 0;
}
inline double ModelProto_Node::memory_ratio() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.memory_ratio)
  return memory_ratio_;
}
inline void ModelProto_Node::set_memory_ratio(double value) {
  
  memory_ratio_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.memory_ratio)
}

// -------------------------------------------------------------------

// ModelProto_OptimizationParams

// .tensorflow.data.model.AutotuneAlgorithm algorithm = 1;
inline void ModelProto_OptimizationParams::clear_algorithm() {
  algorithm_ = 0;
}
inline ::tensorflow::data::model::AutotuneAlgorithm ModelProto_OptimizationParams::algorithm() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.OptimizationParams.algorithm)
  return static_cast< ::tensorflow::data::model::AutotuneAlgorithm >(algorithm_);
}
inline void ModelProto_OptimizationParams::set_algorithm(::tensorflow::data::model::AutotuneAlgorithm value) {
  
  algorithm_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.OptimizationParams.algorithm)
}

// int64 cpu_budget = 2;
inline void ModelProto_OptimizationParams::clear_cpu_budget() {
  cpu_budget_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto_OptimizationParams::cpu_budget() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.OptimizationParams.cpu_budget)
  return cpu_budget_;
}
inline void ModelProto_OptimizationParams::set_cpu_budget(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  cpu_budget_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.OptimizationParams.cpu_budget)
}

// int64 ram_budget = 3;
inline void ModelProto_OptimizationParams::clear_ram_budget() {
  ram_budget_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto_OptimizationParams::ram_budget() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.OptimizationParams.ram_budget)
  return ram_budget_;
}
inline void ModelProto_OptimizationParams::set_ram_budget(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  ram_budget_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.OptimizationParams.ram_budget)
}

// double model_input_time = 4;
inline void ModelProto_OptimizationParams::clear_model_input_time() {
  model_input_time_ = 0;
}
inline double ModelProto_OptimizationParams::model_input_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.OptimizationParams.model_input_time)
  return model_input_time_;
}
inline void ModelProto_OptimizationParams::set_model_input_time(double value) {
  
  model_input_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.OptimizationParams.model_input_time)
}

// -------------------------------------------------------------------

// ModelProto

// .tensorflow.data.model.ModelProto.Node output = 1;
inline bool ModelProto::has_output() const {
  return this != internal_default_instance() && output_ != nullptr;
}
inline void ModelProto::clear_output() {
  if (GetArenaNoVirtual() == nullptr && output_ != nullptr) {
    delete output_;
  }
  output_ = nullptr;
}
inline const ::tensorflow::data::model::ModelProto_Node& ModelProto::output() const {
  const ::tensorflow::data::model::ModelProto_Node* p = output_;
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.output)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::data::model::ModelProto_Node*>(
      &::tensorflow::data::model::_ModelProto_Node_default_instance_);
}
inline ::tensorflow::data::model::ModelProto_Node* ModelProto::release_output() {
  // @@protoc_insertion_point(field_release:tensorflow.data.model.ModelProto.output)
  
  ::tensorflow::data::model::ModelProto_Node* temp = output_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  output_ = nullptr;
  return temp;
}
inline ::tensorflow::data::model::ModelProto_Node* ModelProto::unsafe_arena_release_output() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.data.model.ModelProto.output)
  
  ::tensorflow::data::model::ModelProto_Node* temp = output_;
  output_ = nullptr;
  return temp;
}
inline ::tensorflow::data::model::ModelProto_Node* ModelProto::mutable_output() {
  
  if (output_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::data::model::ModelProto_Node>(GetArenaNoVirtual());
    output_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.output)
  return output_;
}
inline void ModelProto::set_allocated_output(::tensorflow::data::model::ModelProto_Node* output) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete output_;
  }
  if (output) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(output);
    if (message_arena != submessage_arena) {
      output = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, output, submessage_arena);
    }
    
  } else {
    
  }
  output_ = output;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.model.ModelProto.output)
}

// int64 id_counter = 2;
inline void ModelProto::clear_id_counter() {
  id_counter_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ModelProto::id_counter() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.id_counter)
  return id_counter_;
}
inline void ModelProto::set_id_counter(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_counter_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.id_counter)
}

// bool collect_resource_usage = 3;
inline void ModelProto::clear_collect_resource_usage() {
  collect_resource_usage_ = false;
}
inline bool ModelProto::collect_resource_usage() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.collect_resource_usage)
  return collect_resource_usage_;
}
inline void ModelProto::set_collect_resource_usage(bool value) {
  
  collect_resource_usage_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.collect_resource_usage)
}

// .tensorflow.data.model.ModelProto.OptimizationParams optimization_params = 4;
inline bool ModelProto::has_optimization_params() const {
  return this != internal_default_instance() && optimization_params_ != nullptr;
}
inline void ModelProto::clear_optimization_params() {
  if (GetArenaNoVirtual() == nullptr && optimization_params_ != nullptr) {
    delete optimization_params_;
  }
  optimization_params_ = nullptr;
}
inline const ::tensorflow::data::model::ModelProto_OptimizationParams& ModelProto::optimization_params() const {
  const ::tensorflow::data::model::ModelProto_OptimizationParams* p = optimization_params_;
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.optimization_params)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::data::model::ModelProto_OptimizationParams*>(
      &::tensorflow::data::model::_ModelProto_OptimizationParams_default_instance_);
}
inline ::tensorflow::data::model::ModelProto_OptimizationParams* ModelProto::release_optimization_params() {
  // @@protoc_insertion_point(field_release:tensorflow.data.model.ModelProto.optimization_params)
  
  ::tensorflow::data::model::ModelProto_OptimizationParams* temp = optimization_params_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  optimization_params_ = nullptr;
  return temp;
}
inline ::tensorflow::data::model::ModelProto_OptimizationParams* ModelProto::unsafe_arena_release_optimization_params() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.data.model.ModelProto.optimization_params)
  
  ::tensorflow::data::model::ModelProto_OptimizationParams* temp = optimization_params_;
  optimization_params_ = nullptr;
  return temp;
}
inline ::tensorflow::data::model::ModelProto_OptimizationParams* ModelProto::mutable_optimization_params() {
  
  if (optimization_params_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::data::model::ModelProto_OptimizationParams>(GetArenaNoVirtual());
    optimization_params_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.optimization_params)
  return optimization_params_;
}
inline void ModelProto::set_allocated_optimization_params(::tensorflow::data::model::ModelProto_OptimizationParams* optimization_params) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete optimization_params_;
  }
  if (optimization_params) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(optimization_params);
    if (message_arena != submessage_arena) {
      optimization_params = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optimization_params, submessage_arena);
    }
    
  } else {
    
  }
  optimization_params_ = optimization_params;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.model.ModelProto.optimization_params)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace data
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::data::model::NodeClass> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::model::NodeClass>() {
  return ::tensorflow::data::model::NodeClass_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::data::model::AutotuneAlgorithm> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::model::AutotuneAlgorithm>() {
  return ::tensorflow::data::model::AutotuneAlgorithm_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fmodel_2eproto
