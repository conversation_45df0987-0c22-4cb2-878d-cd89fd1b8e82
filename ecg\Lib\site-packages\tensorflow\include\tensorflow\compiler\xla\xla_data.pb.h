// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/xla/xla_data.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[34]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
namespace xla {
class ChannelHandle;
class ChannelHandleDefaultTypeInternal;
extern ChannelHandleDefaultTypeInternal _ChannelHandle_default_instance_;
class CholeskyOptions;
class CholeskyOptionsDefaultTypeInternal;
extern CholeskyOptionsDefaultTypeInternal _CholeskyOptions_default_instance_;
class ComputationStats;
class ComputationStatsDefaultTypeInternal;
extern ComputationStatsDefaultTypeInternal _ComputationStats_default_instance_;
class ConvolutionDimensionNumbers;
class ConvolutionDimensionNumbersDefaultTypeInternal;
extern ConvolutionDimensionNumbersDefaultTypeInternal _ConvolutionDimensionNumbers_default_instance_;
class CustomCallOutputOperandAliasing;
class CustomCallOutputOperandAliasingDefaultTypeInternal;
extern CustomCallOutputOperandAliasingDefaultTypeInternal _CustomCallOutputOperandAliasing_default_instance_;
class DeviceAssignmentProto;
class DeviceAssignmentProtoDefaultTypeInternal;
extern DeviceAssignmentProtoDefaultTypeInternal _DeviceAssignmentProto_default_instance_;
class DeviceAssignmentProto_ComputationDevice;
class DeviceAssignmentProto_ComputationDeviceDefaultTypeInternal;
extern DeviceAssignmentProto_ComputationDeviceDefaultTypeInternal _DeviceAssignmentProto_ComputationDevice_default_instance_;
class DeviceHandle;
class DeviceHandleDefaultTypeInternal;
extern DeviceHandleDefaultTypeInternal _DeviceHandle_default_instance_;
class DotDimensionNumbers;
class DotDimensionNumbersDefaultTypeInternal;
extern DotDimensionNumbersDefaultTypeInternal _DotDimensionNumbers_default_instance_;
class ExecutionHandle;
class ExecutionHandleDefaultTypeInternal;
extern ExecutionHandleDefaultTypeInternal _ExecutionHandle_default_instance_;
class ExecutionProfile;
class ExecutionProfileDefaultTypeInternal;
extern ExecutionProfileDefaultTypeInternal _ExecutionProfile_default_instance_;
class FrontendAttributes;
class FrontendAttributesDefaultTypeInternal;
extern FrontendAttributesDefaultTypeInternal _FrontendAttributes_default_instance_;
class FrontendAttributes_MapEntry_DoNotUse;
class FrontendAttributes_MapEntry_DoNotUseDefaultTypeInternal;
extern FrontendAttributes_MapEntry_DoNotUseDefaultTypeInternal _FrontendAttributes_MapEntry_DoNotUse_default_instance_;
class GatherDimensionNumbers;
class GatherDimensionNumbersDefaultTypeInternal;
extern GatherDimensionNumbersDefaultTypeInternal _GatherDimensionNumbers_default_instance_;
class GlobalDataHandle;
class GlobalDataHandleDefaultTypeInternal;
extern GlobalDataHandleDefaultTypeInternal _GlobalDataHandle_default_instance_;
class LayoutProto;
class LayoutProtoDefaultTypeInternal;
extern LayoutProtoDefaultTypeInternal _LayoutProto_default_instance_;
class LiteralProto;
class LiteralProtoDefaultTypeInternal;
extern LiteralProtoDefaultTypeInternal _LiteralProto_default_instance_;
class OpMetadata;
class OpMetadataDefaultTypeInternal;
extern OpMetadataDefaultTypeInternal _OpMetadata_default_instance_;
class OpSharding;
class OpShardingDefaultTypeInternal;
extern OpShardingDefaultTypeInternal _OpSharding_default_instance_;
class PaddingConfig;
class PaddingConfigDefaultTypeInternal;
extern PaddingConfigDefaultTypeInternal _PaddingConfig_default_instance_;
class PaddingConfig_PaddingConfigDimension;
class PaddingConfig_PaddingConfigDimensionDefaultTypeInternal;
extern PaddingConfig_PaddingConfigDimensionDefaultTypeInternal _PaddingConfig_PaddingConfigDimension_default_instance_;
class ParameterReplication;
class ParameterReplicationDefaultTypeInternal;
extern ParameterReplicationDefaultTypeInternal _ParameterReplication_default_instance_;
class PrecisionConfig;
class PrecisionConfigDefaultTypeInternal;
extern PrecisionConfigDefaultTypeInternal _PrecisionConfig_default_instance_;
class ProgramShapeProto;
class ProgramShapeProtoDefaultTypeInternal;
extern ProgramShapeProtoDefaultTypeInternal _ProgramShapeProto_default_instance_;
class ReplicaGroup;
class ReplicaGroupDefaultTypeInternal;
extern ReplicaGroupDefaultTypeInternal _ReplicaGroup_default_instance_;
class ScatterDimensionNumbers;
class ScatterDimensionNumbersDefaultTypeInternal;
extern ScatterDimensionNumbersDefaultTypeInternal _ScatterDimensionNumbers_default_instance_;
class ShapeProto;
class ShapeProtoDefaultTypeInternal;
extern ShapeProtoDefaultTypeInternal _ShapeProto_default_instance_;
class SourceTarget;
class SourceTargetDefaultTypeInternal;
extern SourceTargetDefaultTypeInternal _SourceTarget_default_instance_;
class TileProto;
class TileProtoDefaultTypeInternal;
extern TileProtoDefaultTypeInternal _TileProto_default_instance_;
class TriangularSolveOptions;
class TriangularSolveOptionsDefaultTypeInternal;
extern TriangularSolveOptionsDefaultTypeInternal _TriangularSolveOptions_default_instance_;
class WhileLoopBackendConfig;
class WhileLoopBackendConfigDefaultTypeInternal;
extern WhileLoopBackendConfigDefaultTypeInternal _WhileLoopBackendConfig_default_instance_;
class WhileLoopBackendConfig_KnownTripCount;
class WhileLoopBackendConfig_KnownTripCountDefaultTypeInternal;
extern WhileLoopBackendConfig_KnownTripCountDefaultTypeInternal _WhileLoopBackendConfig_KnownTripCount_default_instance_;
class Window;
class WindowDefaultTypeInternal;
extern WindowDefaultTypeInternal _Window_default_instance_;
class WindowDimension;
class WindowDimensionDefaultTypeInternal;
extern WindowDimensionDefaultTypeInternal _WindowDimension_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::ChannelHandle* Arena::CreateMaybeMessage<::xla::ChannelHandle>(Arena*);
template<> ::xla::CholeskyOptions* Arena::CreateMaybeMessage<::xla::CholeskyOptions>(Arena*);
template<> ::xla::ComputationStats* Arena::CreateMaybeMessage<::xla::ComputationStats>(Arena*);
template<> ::xla::ConvolutionDimensionNumbers* Arena::CreateMaybeMessage<::xla::ConvolutionDimensionNumbers>(Arena*);
template<> ::xla::CustomCallOutputOperandAliasing* Arena::CreateMaybeMessage<::xla::CustomCallOutputOperandAliasing>(Arena*);
template<> ::xla::DeviceAssignmentProto* Arena::CreateMaybeMessage<::xla::DeviceAssignmentProto>(Arena*);
template<> ::xla::DeviceAssignmentProto_ComputationDevice* Arena::CreateMaybeMessage<::xla::DeviceAssignmentProto_ComputationDevice>(Arena*);
template<> ::xla::DeviceHandle* Arena::CreateMaybeMessage<::xla::DeviceHandle>(Arena*);
template<> ::xla::DotDimensionNumbers* Arena::CreateMaybeMessage<::xla::DotDimensionNumbers>(Arena*);
template<> ::xla::ExecutionHandle* Arena::CreateMaybeMessage<::xla::ExecutionHandle>(Arena*);
template<> ::xla::ExecutionProfile* Arena::CreateMaybeMessage<::xla::ExecutionProfile>(Arena*);
template<> ::xla::FrontendAttributes* Arena::CreateMaybeMessage<::xla::FrontendAttributes>(Arena*);
template<> ::xla::FrontendAttributes_MapEntry_DoNotUse* Arena::CreateMaybeMessage<::xla::FrontendAttributes_MapEntry_DoNotUse>(Arena*);
template<> ::xla::GatherDimensionNumbers* Arena::CreateMaybeMessage<::xla::GatherDimensionNumbers>(Arena*);
template<> ::xla::GlobalDataHandle* Arena::CreateMaybeMessage<::xla::GlobalDataHandle>(Arena*);
template<> ::xla::LayoutProto* Arena::CreateMaybeMessage<::xla::LayoutProto>(Arena*);
template<> ::xla::LiteralProto* Arena::CreateMaybeMessage<::xla::LiteralProto>(Arena*);
template<> ::xla::OpMetadata* Arena::CreateMaybeMessage<::xla::OpMetadata>(Arena*);
template<> ::xla::OpSharding* Arena::CreateMaybeMessage<::xla::OpSharding>(Arena*);
template<> ::xla::PaddingConfig* Arena::CreateMaybeMessage<::xla::PaddingConfig>(Arena*);
template<> ::xla::PaddingConfig_PaddingConfigDimension* Arena::CreateMaybeMessage<::xla::PaddingConfig_PaddingConfigDimension>(Arena*);
template<> ::xla::ParameterReplication* Arena::CreateMaybeMessage<::xla::ParameterReplication>(Arena*);
template<> ::xla::PrecisionConfig* Arena::CreateMaybeMessage<::xla::PrecisionConfig>(Arena*);
template<> ::xla::ProgramShapeProto* Arena::CreateMaybeMessage<::xla::ProgramShapeProto>(Arena*);
template<> ::xla::ReplicaGroup* Arena::CreateMaybeMessage<::xla::ReplicaGroup>(Arena*);
template<> ::xla::ScatterDimensionNumbers* Arena::CreateMaybeMessage<::xla::ScatterDimensionNumbers>(Arena*);
template<> ::xla::ShapeProto* Arena::CreateMaybeMessage<::xla::ShapeProto>(Arena*);
template<> ::xla::SourceTarget* Arena::CreateMaybeMessage<::xla::SourceTarget>(Arena*);
template<> ::xla::TileProto* Arena::CreateMaybeMessage<::xla::TileProto>(Arena*);
template<> ::xla::TriangularSolveOptions* Arena::CreateMaybeMessage<::xla::TriangularSolveOptions>(Arena*);
template<> ::xla::WhileLoopBackendConfig* Arena::CreateMaybeMessage<::xla::WhileLoopBackendConfig>(Arena*);
template<> ::xla::WhileLoopBackendConfig_KnownTripCount* Arena::CreateMaybeMessage<::xla::WhileLoopBackendConfig_KnownTripCount>(Arena*);
template<> ::xla::Window* Arena::CreateMaybeMessage<::xla::Window>(Arena*);
template<> ::xla::WindowDimension* Arena::CreateMaybeMessage<::xla::WindowDimension>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

enum ChannelHandle_ChannelType : int {
  ChannelHandle_ChannelType_CHANNEL_TYPE_INVALID = 0,
  ChannelHandle_ChannelType_DEVICE_TO_DEVICE = 1,
  ChannelHandle_ChannelType_DEVICE_TO_HOST = 2,
  ChannelHandle_ChannelType_HOST_TO_DEVICE = 3,
  ChannelHandle_ChannelType_ChannelHandle_ChannelType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ChannelHandle_ChannelType_ChannelHandle_ChannelType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ChannelHandle_ChannelType_IsValid(int value);
constexpr ChannelHandle_ChannelType ChannelHandle_ChannelType_ChannelType_MIN = ChannelHandle_ChannelType_CHANNEL_TYPE_INVALID;
constexpr ChannelHandle_ChannelType ChannelHandle_ChannelType_ChannelType_MAX = ChannelHandle_ChannelType_HOST_TO_DEVICE;
constexpr int ChannelHandle_ChannelType_ChannelType_ARRAYSIZE = ChannelHandle_ChannelType_ChannelType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ChannelHandle_ChannelType_descriptor();
template<typename T>
inline const std::string& ChannelHandle_ChannelType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ChannelHandle_ChannelType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ChannelHandle_ChannelType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ChannelHandle_ChannelType_descriptor(), enum_t_value);
}
inline bool ChannelHandle_ChannelType_Parse(
    const std::string& name, ChannelHandle_ChannelType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ChannelHandle_ChannelType>(
    ChannelHandle_ChannelType_descriptor(), name, value);
}
enum TriangularSolveOptions_Transpose : int {
  TriangularSolveOptions_Transpose_TRANSPOSE_INVALID = 0,
  TriangularSolveOptions_Transpose_NO_TRANSPOSE = 1,
  TriangularSolveOptions_Transpose_TRANSPOSE = 2,
  TriangularSolveOptions_Transpose_ADJOINT = 3,
  TriangularSolveOptions_Transpose_TriangularSolveOptions_Transpose_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  TriangularSolveOptions_Transpose_TriangularSolveOptions_Transpose_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool TriangularSolveOptions_Transpose_IsValid(int value);
constexpr TriangularSolveOptions_Transpose TriangularSolveOptions_Transpose_Transpose_MIN = TriangularSolveOptions_Transpose_TRANSPOSE_INVALID;
constexpr TriangularSolveOptions_Transpose TriangularSolveOptions_Transpose_Transpose_MAX = TriangularSolveOptions_Transpose_ADJOINT;
constexpr int TriangularSolveOptions_Transpose_Transpose_ARRAYSIZE = TriangularSolveOptions_Transpose_Transpose_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TriangularSolveOptions_Transpose_descriptor();
template<typename T>
inline const std::string& TriangularSolveOptions_Transpose_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TriangularSolveOptions_Transpose>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TriangularSolveOptions_Transpose_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TriangularSolveOptions_Transpose_descriptor(), enum_t_value);
}
inline bool TriangularSolveOptions_Transpose_Parse(
    const std::string& name, TriangularSolveOptions_Transpose* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TriangularSolveOptions_Transpose>(
    TriangularSolveOptions_Transpose_descriptor(), name, value);
}
enum OpSharding_Type : int {
  OpSharding_Type_REPLICATED = 0,
  OpSharding_Type_MAXIMAL = 1,
  OpSharding_Type_TUPLE = 2,
  OpSharding_Type_OTHER = 3,
  OpSharding_Type_MANUAL = 4,
  OpSharding_Type_OpSharding_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  OpSharding_Type_OpSharding_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool OpSharding_Type_IsValid(int value);
constexpr OpSharding_Type OpSharding_Type_Type_MIN = OpSharding_Type_REPLICATED;
constexpr OpSharding_Type OpSharding_Type_Type_MAX = OpSharding_Type_MANUAL;
constexpr int OpSharding_Type_Type_ARRAYSIZE = OpSharding_Type_Type_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OpSharding_Type_descriptor();
template<typename T>
inline const std::string& OpSharding_Type_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OpSharding_Type>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OpSharding_Type_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OpSharding_Type_descriptor(), enum_t_value);
}
inline bool OpSharding_Type_Parse(
    const std::string& name, OpSharding_Type* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OpSharding_Type>(
    OpSharding_Type_descriptor(), name, value);
}
enum PrecisionConfig_Precision : int {
  PrecisionConfig_Precision_DEFAULT = 0,
  PrecisionConfig_Precision_HIGH = 1,
  PrecisionConfig_Precision_HIGHEST = 2,
  PrecisionConfig_Precision_PrecisionConfig_Precision_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  PrecisionConfig_Precision_PrecisionConfig_Precision_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool PrecisionConfig_Precision_IsValid(int value);
constexpr PrecisionConfig_Precision PrecisionConfig_Precision_Precision_MIN = PrecisionConfig_Precision_DEFAULT;
constexpr PrecisionConfig_Precision PrecisionConfig_Precision_Precision_MAX = PrecisionConfig_Precision_HIGHEST;
constexpr int PrecisionConfig_Precision_Precision_ARRAYSIZE = PrecisionConfig_Precision_Precision_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PrecisionConfig_Precision_descriptor();
template<typename T>
inline const std::string& PrecisionConfig_Precision_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PrecisionConfig_Precision>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PrecisionConfig_Precision_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PrecisionConfig_Precision_descriptor(), enum_t_value);
}
inline bool PrecisionConfig_Precision_Parse(
    const std::string& name, PrecisionConfig_Precision* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PrecisionConfig_Precision>(
    PrecisionConfig_Precision_descriptor(), name, value);
}
enum PrimitiveType : int {
  PRIMITIVE_TYPE_INVALID = 0,
  PRED = 1,
  S8 = 2,
  S16 = 3,
  S32 = 4,
  S64 = 5,
  U8 = 6,
  U16 = 7,
  U32 = 8,
  U64 = 9,
  F16 = 10,
  F32 = 11,
  BF16 = 16,
  F64 = 12,
  C64 = 15,
  C128 = 18,
  TUPLE = 13,
  OPAQUE_TYPE = 14,
  TOKEN = 17,
  PrimitiveType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  PrimitiveType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool PrimitiveType_IsValid(int value);
constexpr PrimitiveType PrimitiveType_MIN = PRIMITIVE_TYPE_INVALID;
constexpr PrimitiveType PrimitiveType_MAX = C128;
constexpr int PrimitiveType_ARRAYSIZE = PrimitiveType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PrimitiveType_descriptor();
template<typename T>
inline const std::string& PrimitiveType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PrimitiveType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PrimitiveType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PrimitiveType_descriptor(), enum_t_value);
}
inline bool PrimitiveType_Parse(
    const std::string& name, PrimitiveType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PrimitiveType>(
    PrimitiveType_descriptor(), name, value);
}
enum Format : int {
  INVALID_FORMAT = 0,
  DENSE = 1,
  Format_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  Format_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool Format_IsValid(int value);
constexpr Format Format_MIN = INVALID_FORMAT;
constexpr Format Format_MAX = DENSE;
constexpr int Format_ARRAYSIZE = Format_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Format_descriptor();
template<typename T>
inline const std::string& Format_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Format>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Format_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Format_descriptor(), enum_t_value);
}
inline bool Format_Parse(
    const std::string& name, Format* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Format>(
    Format_descriptor(), name, value);
}
enum ProfileType : int {
  INVALID = 0,
  WINDOW = 1,
  FLAG = 2,
  INTEGER = 3,
  ProfileType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ProfileType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ProfileType_IsValid(int value);
constexpr ProfileType ProfileType_MIN = INVALID;
constexpr ProfileType ProfileType_MAX = INTEGER;
constexpr int ProfileType_ARRAYSIZE = ProfileType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ProfileType_descriptor();
template<typename T>
inline const std::string& ProfileType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ProfileType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ProfileType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ProfileType_descriptor(), enum_t_value);
}
inline bool ProfileType_Parse(
    const std::string& name, ProfileType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ProfileType>(
    ProfileType_descriptor(), name, value);
}
enum PaddingType : int {
  PADDING_INVALID = 0,
  PADDING_VALID = 1,
  PADDING_SAME = 2,
  PaddingType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  PaddingType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool PaddingType_IsValid(int value);
constexpr PaddingType PaddingType_MIN = PADDING_INVALID;
constexpr PaddingType PaddingType_MAX = PADDING_SAME;
constexpr int PaddingType_ARRAYSIZE = PaddingType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PaddingType_descriptor();
template<typename T>
inline const std::string& PaddingType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PaddingType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PaddingType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PaddingType_descriptor(), enum_t_value);
}
inline bool PaddingType_Parse(
    const std::string& name, PaddingType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PaddingType>(
    PaddingType_descriptor(), name, value);
}
enum FftType : int {
  FFT = 0,
  IFFT = 1,
  RFFT = 2,
  IRFFT = 3,
  FftType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  FftType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool FftType_IsValid(int value);
constexpr FftType FftType_MIN = FFT;
constexpr FftType FftType_MAX = IRFFT;
constexpr int FftType_ARRAYSIZE = FftType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FftType_descriptor();
template<typename T>
inline const std::string& FftType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FftType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FftType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FftType_descriptor(), enum_t_value);
}
inline bool FftType_Parse(
    const std::string& name, FftType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FftType>(
    FftType_descriptor(), name, value);
}
enum RandomDistribution : int {
  RNG_INVALID = 0,
  RNG_UNIFORM = 1,
  RNG_NORMAL = 2,
  RandomDistribution_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  RandomDistribution_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool RandomDistribution_IsValid(int value);
constexpr RandomDistribution RandomDistribution_MIN = RNG_INVALID;
constexpr RandomDistribution RandomDistribution_MAX = RNG_NORMAL;
constexpr int RandomDistribution_ARRAYSIZE = RandomDistribution_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RandomDistribution_descriptor();
template<typename T>
inline const std::string& RandomDistribution_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RandomDistribution>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RandomDistribution_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RandomDistribution_descriptor(), enum_t_value);
}
inline bool RandomDistribution_Parse(
    const std::string& name, RandomDistribution* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RandomDistribution>(
    RandomDistribution_descriptor(), name, value);
}
enum RandomAlgorithm : int {
  RNG_DEFAULT = 0,
  RNG_THREE_FRY = 1,
  RNG_PHILOX = 2,
  RandomAlgorithm_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  RandomAlgorithm_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool RandomAlgorithm_IsValid(int value);
constexpr RandomAlgorithm RandomAlgorithm_MIN = RNG_DEFAULT;
constexpr RandomAlgorithm RandomAlgorithm_MAX = RNG_PHILOX;
constexpr int RandomAlgorithm_ARRAYSIZE = RandomAlgorithm_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RandomAlgorithm_descriptor();
template<typename T>
inline const std::string& RandomAlgorithm_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RandomAlgorithm>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RandomAlgorithm_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RandomAlgorithm_descriptor(), enum_t_value);
}
inline bool RandomAlgorithm_Parse(
    const std::string& name, RandomAlgorithm* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RandomAlgorithm>(
    RandomAlgorithm_descriptor(), name, value);
}
// ===================================================================

class PaddingConfig_PaddingConfigDimension :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.PaddingConfig.PaddingConfigDimension) */ {
 public:
  PaddingConfig_PaddingConfigDimension();
  virtual ~PaddingConfig_PaddingConfigDimension();

  PaddingConfig_PaddingConfigDimension(const PaddingConfig_PaddingConfigDimension& from);
  PaddingConfig_PaddingConfigDimension(PaddingConfig_PaddingConfigDimension&& from) noexcept
    : PaddingConfig_PaddingConfigDimension() {
    *this = ::std::move(from);
  }

  inline PaddingConfig_PaddingConfigDimension& operator=(const PaddingConfig_PaddingConfigDimension& from) {
    CopyFrom(from);
    return *this;
  }
  inline PaddingConfig_PaddingConfigDimension& operator=(PaddingConfig_PaddingConfigDimension&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PaddingConfig_PaddingConfigDimension& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PaddingConfig_PaddingConfigDimension* internal_default_instance() {
    return reinterpret_cast<const PaddingConfig_PaddingConfigDimension*>(
               &_PaddingConfig_PaddingConfigDimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(PaddingConfig_PaddingConfigDimension& a, PaddingConfig_PaddingConfigDimension& b) {
    a.Swap(&b);
  }
  inline void Swap(PaddingConfig_PaddingConfigDimension* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PaddingConfig_PaddingConfigDimension* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PaddingConfig_PaddingConfigDimension* New() const final {
    return CreateMaybeMessage<PaddingConfig_PaddingConfigDimension>(nullptr);
  }

  PaddingConfig_PaddingConfigDimension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PaddingConfig_PaddingConfigDimension>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PaddingConfig_PaddingConfigDimension& from);
  void MergeFrom(const PaddingConfig_PaddingConfigDimension& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PaddingConfig_PaddingConfigDimension* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.PaddingConfig.PaddingConfigDimension";
  }
  protected:
  explicit PaddingConfig_PaddingConfigDimension(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEdgePaddingLowFieldNumber = 1,
    kEdgePaddingHighFieldNumber = 2,
    kInteriorPaddingFieldNumber = 3,
  };
  // int64 edge_padding_low = 1;
  void clear_edge_padding_low();
  ::PROTOBUF_NAMESPACE_ID::int64 edge_padding_low() const;
  void set_edge_padding_low(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 edge_padding_high = 2;
  void clear_edge_padding_high();
  ::PROTOBUF_NAMESPACE_ID::int64 edge_padding_high() const;
  void set_edge_padding_high(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 interior_padding = 3;
  void clear_interior_padding();
  ::PROTOBUF_NAMESPACE_ID::int64 interior_padding() const;
  void set_interior_padding(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.PaddingConfig.PaddingConfigDimension)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 edge_padding_low_;
  ::PROTOBUF_NAMESPACE_ID::int64 edge_padding_high_;
  ::PROTOBUF_NAMESPACE_ID::int64 interior_padding_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class PaddingConfig :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.PaddingConfig) */ {
 public:
  PaddingConfig();
  virtual ~PaddingConfig();

  PaddingConfig(const PaddingConfig& from);
  PaddingConfig(PaddingConfig&& from) noexcept
    : PaddingConfig() {
    *this = ::std::move(from);
  }

  inline PaddingConfig& operator=(const PaddingConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline PaddingConfig& operator=(PaddingConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PaddingConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PaddingConfig* internal_default_instance() {
    return reinterpret_cast<const PaddingConfig*>(
               &_PaddingConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(PaddingConfig& a, PaddingConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(PaddingConfig* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PaddingConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PaddingConfig* New() const final {
    return CreateMaybeMessage<PaddingConfig>(nullptr);
  }

  PaddingConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PaddingConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PaddingConfig& from);
  void MergeFrom(const PaddingConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PaddingConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.PaddingConfig";
  }
  protected:
  explicit PaddingConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef PaddingConfig_PaddingConfigDimension PaddingConfigDimension;

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionsFieldNumber = 1,
  };
  // repeated .xla.PaddingConfig.PaddingConfigDimension dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  ::xla::PaddingConfig_PaddingConfigDimension* mutable_dimensions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::PaddingConfig_PaddingConfigDimension >*
      mutable_dimensions();
  const ::xla::PaddingConfig_PaddingConfigDimension& dimensions(int index) const;
  ::xla::PaddingConfig_PaddingConfigDimension* add_dimensions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::PaddingConfig_PaddingConfigDimension >&
      dimensions() const;

  // @@protoc_insertion_point(class_scope:xla.PaddingConfig)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::PaddingConfig_PaddingConfigDimension > dimensions_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class TileProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.TileProto) */ {
 public:
  TileProto();
  virtual ~TileProto();

  TileProto(const TileProto& from);
  TileProto(TileProto&& from) noexcept
    : TileProto() {
    *this = ::std::move(from);
  }

  inline TileProto& operator=(const TileProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TileProto& operator=(TileProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TileProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TileProto* internal_default_instance() {
    return reinterpret_cast<const TileProto*>(
               &_TileProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TileProto& a, TileProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TileProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TileProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TileProto* New() const final {
    return CreateMaybeMessage<TileProto>(nullptr);
  }

  TileProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TileProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TileProto& from);
  void MergeFrom(const TileProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TileProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.TileProto";
  }
  protected:
  explicit TileProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionsFieldNumber = 1,
  };
  // repeated int64 dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 dimensions(int index) const;
  void set_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_dimensions();

  // @@protoc_insertion_point(class_scope:xla.TileProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > dimensions_;
  mutable std::atomic<int> _dimensions_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class LayoutProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.LayoutProto) */ {
 public:
  LayoutProto();
  virtual ~LayoutProto();

  LayoutProto(const LayoutProto& from);
  LayoutProto(LayoutProto&& from) noexcept
    : LayoutProto() {
    *this = ::std::move(from);
  }

  inline LayoutProto& operator=(const LayoutProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline LayoutProto& operator=(LayoutProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LayoutProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LayoutProto* internal_default_instance() {
    return reinterpret_cast<const LayoutProto*>(
               &_LayoutProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(LayoutProto& a, LayoutProto& b) {
    a.Swap(&b);
  }
  inline void Swap(LayoutProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LayoutProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LayoutProto* New() const final {
    return CreateMaybeMessage<LayoutProto>(nullptr);
  }

  LayoutProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LayoutProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LayoutProto& from);
  void MergeFrom(const LayoutProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LayoutProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.LayoutProto";
  }
  protected:
  explicit LayoutProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMinorToMajorFieldNumber = 1,
    kTilesFieldNumber = 6,
    kFormatFieldNumber = 4,
    kElementSizeInBitsFieldNumber = 7,
    kMemorySpaceFieldNumber = 8,
  };
  // repeated int64 minor_to_major = 1;
  int minor_to_major_size() const;
  void clear_minor_to_major();
  ::PROTOBUF_NAMESPACE_ID::int64 minor_to_major(int index) const;
  void set_minor_to_major(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_minor_to_major(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      minor_to_major() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_minor_to_major();

  // repeated .xla.TileProto tiles = 6;
  int tiles_size() const;
  void clear_tiles();
  ::xla::TileProto* mutable_tiles(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::TileProto >*
      mutable_tiles();
  const ::xla::TileProto& tiles(int index) const;
  ::xla::TileProto* add_tiles();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::TileProto >&
      tiles() const;

  // .xla.Format format = 4;
  void clear_format();
  ::xla::Format format() const;
  void set_format(::xla::Format value);

  // int64 element_size_in_bits = 7;
  void clear_element_size_in_bits();
  ::PROTOBUF_NAMESPACE_ID::int64 element_size_in_bits() const;
  void set_element_size_in_bits(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 memory_space = 8;
  void clear_memory_space();
  ::PROTOBUF_NAMESPACE_ID::int64 memory_space() const;
  void set_memory_space(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.LayoutProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > minor_to_major_;
  mutable std::atomic<int> _minor_to_major_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::TileProto > tiles_;
  int format_;
  ::PROTOBUF_NAMESPACE_ID::int64 element_size_in_bits_;
  ::PROTOBUF_NAMESPACE_ID::int64 memory_space_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class ShapeProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ShapeProto) */ {
 public:
  ShapeProto();
  virtual ~ShapeProto();

  ShapeProto(const ShapeProto& from);
  ShapeProto(ShapeProto&& from) noexcept
    : ShapeProto() {
    *this = ::std::move(from);
  }

  inline ShapeProto& operator=(const ShapeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ShapeProto& operator=(ShapeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ShapeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ShapeProto* internal_default_instance() {
    return reinterpret_cast<const ShapeProto*>(
               &_ShapeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ShapeProto& a, ShapeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ShapeProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ShapeProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ShapeProto* New() const final {
    return CreateMaybeMessage<ShapeProto>(nullptr);
  }

  ShapeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ShapeProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ShapeProto& from);
  void MergeFrom(const ShapeProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ShapeProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ShapeProto";
  }
  protected:
  explicit ShapeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionsFieldNumber = 3,
    kTupleShapesFieldNumber = 4,
    kIsDynamicDimensionFieldNumber = 6,
    kLayoutFieldNumber = 5,
    kElementTypeFieldNumber = 2,
  };
  // repeated int64 dimensions = 3;
  int dimensions_size() const;
  void clear_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 dimensions(int index) const;
  void set_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_dimensions();

  // repeated .xla.ShapeProto tuple_shapes = 4;
  int tuple_shapes_size() const;
  void clear_tuple_shapes();
  ::xla::ShapeProto* mutable_tuple_shapes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
      mutable_tuple_shapes();
  const ::xla::ShapeProto& tuple_shapes(int index) const;
  ::xla::ShapeProto* add_tuple_shapes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
      tuple_shapes() const;

  // repeated bool is_dynamic_dimension = 6;
  int is_dynamic_dimension_size() const;
  void clear_is_dynamic_dimension();
  bool is_dynamic_dimension(int index) const;
  void set_is_dynamic_dimension(int index, bool value);
  void add_is_dynamic_dimension(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      is_dynamic_dimension() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_is_dynamic_dimension();

  // .xla.LayoutProto layout = 5;
  bool has_layout() const;
  void clear_layout();
  const ::xla::LayoutProto& layout() const;
  ::xla::LayoutProto* release_layout();
  ::xla::LayoutProto* mutable_layout();
  void set_allocated_layout(::xla::LayoutProto* layout);
  void unsafe_arena_set_allocated_layout(
      ::xla::LayoutProto* layout);
  ::xla::LayoutProto* unsafe_arena_release_layout();

  // .xla.PrimitiveType element_type = 2;
  void clear_element_type();
  ::xla::PrimitiveType element_type() const;
  void set_element_type(::xla::PrimitiveType value);

  // @@protoc_insertion_point(class_scope:xla.ShapeProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > dimensions_;
  mutable std::atomic<int> _dimensions_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto > tuple_shapes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > is_dynamic_dimension_;
  mutable std::atomic<int> _is_dynamic_dimension_cached_byte_size_;
  ::xla::LayoutProto* layout_;
  int element_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class ProgramShapeProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ProgramShapeProto) */ {
 public:
  ProgramShapeProto();
  virtual ~ProgramShapeProto();

  ProgramShapeProto(const ProgramShapeProto& from);
  ProgramShapeProto(ProgramShapeProto&& from) noexcept
    : ProgramShapeProto() {
    *this = ::std::move(from);
  }

  inline ProgramShapeProto& operator=(const ProgramShapeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProgramShapeProto& operator=(ProgramShapeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProgramShapeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProgramShapeProto* internal_default_instance() {
    return reinterpret_cast<const ProgramShapeProto*>(
               &_ProgramShapeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ProgramShapeProto& a, ProgramShapeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ProgramShapeProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProgramShapeProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProgramShapeProto* New() const final {
    return CreateMaybeMessage<ProgramShapeProto>(nullptr);
  }

  ProgramShapeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProgramShapeProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProgramShapeProto& from);
  void MergeFrom(const ProgramShapeProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProgramShapeProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ProgramShapeProto";
  }
  protected:
  explicit ProgramShapeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kParametersFieldNumber = 1,
    kParameterNamesFieldNumber = 3,
    kResultFieldNumber = 2,
  };
  // repeated .xla.ShapeProto parameters = 1;
  int parameters_size() const;
  void clear_parameters();
  ::xla::ShapeProto* mutable_parameters(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
      mutable_parameters();
  const ::xla::ShapeProto& parameters(int index) const;
  ::xla::ShapeProto* add_parameters();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
      parameters() const;

  // repeated string parameter_names = 3;
  int parameter_names_size() const;
  void clear_parameter_names();
  const std::string& parameter_names(int index) const;
  std::string* mutable_parameter_names(int index);
  void set_parameter_names(int index, const std::string& value);
  void set_parameter_names(int index, std::string&& value);
  void set_parameter_names(int index, const char* value);
  void set_parameter_names(int index, const char* value, size_t size);
  std::string* add_parameter_names();
  void add_parameter_names(const std::string& value);
  void add_parameter_names(std::string&& value);
  void add_parameter_names(const char* value);
  void add_parameter_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& parameter_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_parameter_names();

  // .xla.ShapeProto result = 2;
  bool has_result() const;
  void clear_result();
  const ::xla::ShapeProto& result() const;
  ::xla::ShapeProto* release_result();
  ::xla::ShapeProto* mutable_result();
  void set_allocated_result(::xla::ShapeProto* result);
  void unsafe_arena_set_allocated_result(
      ::xla::ShapeProto* result);
  ::xla::ShapeProto* unsafe_arena_release_result();

  // @@protoc_insertion_point(class_scope:xla.ProgramShapeProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto > parameters_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> parameter_names_;
  ::xla::ShapeProto* result_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class ComputationStats :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ComputationStats) */ {
 public:
  ComputationStats();
  virtual ~ComputationStats();

  ComputationStats(const ComputationStats& from);
  ComputationStats(ComputationStats&& from) noexcept
    : ComputationStats() {
    *this = ::std::move(from);
  }

  inline ComputationStats& operator=(const ComputationStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline ComputationStats& operator=(ComputationStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ComputationStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ComputationStats* internal_default_instance() {
    return reinterpret_cast<const ComputationStats*>(
               &_ComputationStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(ComputationStats& a, ComputationStats& b) {
    a.Swap(&b);
  }
  inline void Swap(ComputationStats* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ComputationStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ComputationStats* New() const final {
    return CreateMaybeMessage<ComputationStats>(nullptr);
  }

  ComputationStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ComputationStats>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ComputationStats& from);
  void MergeFrom(const ComputationStats& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ComputationStats* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ComputationStats";
  }
  protected:
  explicit ComputationStats(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFlopCountFieldNumber = 1,
    kTranscendentalCountFieldNumber = 2,
  };
  // double flop_count = 1;
  void clear_flop_count();
  double flop_count() const;
  void set_flop_count(double value);

  // double transcendental_count = 2;
  void clear_transcendental_count();
  double transcendental_count() const;
  void set_transcendental_count(double value);

  // @@protoc_insertion_point(class_scope:xla.ComputationStats)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double flop_count_;
  double transcendental_count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.OpMetadata) */ {
 public:
  OpMetadata();
  virtual ~OpMetadata();

  OpMetadata(const OpMetadata& from);
  OpMetadata(OpMetadata&& from) noexcept
    : OpMetadata() {
    *this = ::std::move(from);
  }

  inline OpMetadata& operator=(const OpMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpMetadata& operator=(OpMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpMetadata* internal_default_instance() {
    return reinterpret_cast<const OpMetadata*>(
               &_OpMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(OpMetadata& a, OpMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(OpMetadata* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpMetadata* New() const final {
    return CreateMaybeMessage<OpMetadata>(nullptr);
  }

  OpMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpMetadata& from);
  void MergeFrom(const OpMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.OpMetadata";
  }
  protected:
  explicit OpMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProfileTypeFieldNumber = 5,
    kOpTypeFieldNumber = 1,
    kOpNameFieldNumber = 2,
    kSourceFileFieldNumber = 3,
    kCreationPassIdFieldNumber = 6,
    kLogicalCreationPassIdFieldNumber = 7,
    kSizeOfGeneratedCodeInBytesFieldNumber = 8,
    kSizeOfMemoryWorkingSetInBytesFieldNumber = 9,
    kSourceLineFieldNumber = 4,
  };
  // repeated .xla.ProfileType profile_type = 5;
  int profile_type_size() const;
  void clear_profile_type();
  ::xla::ProfileType profile_type(int index) const;
  void set_profile_type(int index, ::xla::ProfileType value);
  void add_profile_type(::xla::ProfileType value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& profile_type() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_profile_type();

  // string op_type = 1;
  void clear_op_type();
  const std::string& op_type() const;
  void set_op_type(const std::string& value);
  void set_op_type(std::string&& value);
  void set_op_type(const char* value);
  void set_op_type(const char* value, size_t size);
  std::string* mutable_op_type();
  std::string* release_op_type();
  void set_allocated_op_type(std::string* op_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_op_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op_type(
      std::string* op_type);

  // string op_name = 2;
  void clear_op_name();
  const std::string& op_name() const;
  void set_op_name(const std::string& value);
  void set_op_name(std::string&& value);
  void set_op_name(const char* value);
  void set_op_name(const char* value, size_t size);
  std::string* mutable_op_name();
  std::string* release_op_name();
  void set_allocated_op_name(std::string* op_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_op_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op_name(
      std::string* op_name);

  // string source_file = 3;
  void clear_source_file();
  const std::string& source_file() const;
  void set_source_file(const std::string& value);
  void set_source_file(std::string&& value);
  void set_source_file(const char* value);
  void set_source_file(const char* value, size_t size);
  std::string* mutable_source_file();
  std::string* release_source_file();
  void set_allocated_source_file(std::string* source_file);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_source_file();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_source_file(
      std::string* source_file);

  // int64 creation_pass_id = 6;
  void clear_creation_pass_id();
  ::PROTOBUF_NAMESPACE_ID::int64 creation_pass_id() const;
  void set_creation_pass_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 logical_creation_pass_id = 7;
  void clear_logical_creation_pass_id();
  ::PROTOBUF_NAMESPACE_ID::int64 logical_creation_pass_id() const;
  void set_logical_creation_pass_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 size_of_generated_code_in_bytes = 8;
  void clear_size_of_generated_code_in_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 size_of_generated_code_in_bytes() const;
  void set_size_of_generated_code_in_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 size_of_memory_working_set_in_bytes = 9;
  void clear_size_of_memory_working_set_in_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 size_of_memory_working_set_in_bytes() const;
  void set_size_of_memory_working_set_in_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int32 source_line = 4;
  void clear_source_line();
  ::PROTOBUF_NAMESPACE_ID::int32 source_line() const;
  void set_source_line(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:xla.OpMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> profile_type_;
  mutable std::atomic<int> _profile_type_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr source_file_;
  ::PROTOBUF_NAMESPACE_ID::int64 creation_pass_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 logical_creation_pass_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 size_of_generated_code_in_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 size_of_memory_working_set_in_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int32 source_line_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class ExecutionProfile :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecutionProfile) */ {
 public:
  ExecutionProfile();
  virtual ~ExecutionProfile();

  ExecutionProfile(const ExecutionProfile& from);
  ExecutionProfile(ExecutionProfile&& from) noexcept
    : ExecutionProfile() {
    *this = ::std::move(from);
  }

  inline ExecutionProfile& operator=(const ExecutionProfile& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecutionProfile& operator=(ExecutionProfile&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecutionProfile& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecutionProfile* internal_default_instance() {
    return reinterpret_cast<const ExecutionProfile*>(
               &_ExecutionProfile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(ExecutionProfile& a, ExecutionProfile& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecutionProfile* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExecutionProfile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecutionProfile* New() const final {
    return CreateMaybeMessage<ExecutionProfile>(nullptr);
  }

  ExecutionProfile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecutionProfile>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecutionProfile& from);
  void MergeFrom(const ExecutionProfile& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecutionProfile* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecutionProfile";
  }
  protected:
  explicit ExecutionProfile(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCompileTimeMsFieldNumber = 2,
    kComputeCycleCountFieldNumber = 3,
    kComputeTimeNsFieldNumber = 4,
    kComputeAndTransferTimeNsFieldNumber = 5,
    kExecutableSizeInBytesFieldNumber = 6,
    kCompilationCacheHitFieldNumber = 1,
    kProfileCacheHitFieldNumber = 7,
  };
  // int64 compile_time_ms = 2;
  void clear_compile_time_ms();
  ::PROTOBUF_NAMESPACE_ID::int64 compile_time_ms() const;
  void set_compile_time_ms(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 compute_cycle_count = 3;
  void clear_compute_cycle_count();
  ::PROTOBUF_NAMESPACE_ID::int64 compute_cycle_count() const;
  void set_compute_cycle_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 compute_time_ns = 4;
  void clear_compute_time_ns();
  ::PROTOBUF_NAMESPACE_ID::int64 compute_time_ns() const;
  void set_compute_time_ns(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 compute_and_transfer_time_ns = 5;
  void clear_compute_and_transfer_time_ns();
  ::PROTOBUF_NAMESPACE_ID::int64 compute_and_transfer_time_ns() const;
  void set_compute_and_transfer_time_ns(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 executable_size_in_bytes = 6;
  void clear_executable_size_in_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 executable_size_in_bytes() const;
  void set_executable_size_in_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool compilation_cache_hit = 1;
  void clear_compilation_cache_hit();
  bool compilation_cache_hit() const;
  void set_compilation_cache_hit(bool value);

  // bool profile_cache_hit = 7;
  void clear_profile_cache_hit();
  bool profile_cache_hit() const;
  void set_profile_cache_hit(bool value);

  // @@protoc_insertion_point(class_scope:xla.ExecutionProfile)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 compile_time_ms_;
  ::PROTOBUF_NAMESPACE_ID::int64 compute_cycle_count_;
  ::PROTOBUF_NAMESPACE_ID::int64 compute_time_ns_;
  ::PROTOBUF_NAMESPACE_ID::int64 compute_and_transfer_time_ns_;
  ::PROTOBUF_NAMESPACE_ID::int64 executable_size_in_bytes_;
  bool compilation_cache_hit_;
  bool profile_cache_hit_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class ExecutionHandle :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecutionHandle) */ {
 public:
  ExecutionHandle();
  virtual ~ExecutionHandle();

  ExecutionHandle(const ExecutionHandle& from);
  ExecutionHandle(ExecutionHandle&& from) noexcept
    : ExecutionHandle() {
    *this = ::std::move(from);
  }

  inline ExecutionHandle& operator=(const ExecutionHandle& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecutionHandle& operator=(ExecutionHandle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecutionHandle& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecutionHandle* internal_default_instance() {
    return reinterpret_cast<const ExecutionHandle*>(
               &_ExecutionHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(ExecutionHandle& a, ExecutionHandle& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecutionHandle* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExecutionHandle* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecutionHandle* New() const final {
    return CreateMaybeMessage<ExecutionHandle>(nullptr);
  }

  ExecutionHandle* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecutionHandle>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecutionHandle& from);
  void MergeFrom(const ExecutionHandle& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecutionHandle* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecutionHandle";
  }
  protected:
  explicit ExecutionHandle(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleFieldNumber = 1,
  };
  // int64 handle = 1;
  void clear_handle();
  ::PROTOBUF_NAMESPACE_ID::int64 handle() const;
  void set_handle(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.ExecutionHandle)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class GlobalDataHandle :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.GlobalDataHandle) */ {
 public:
  GlobalDataHandle();
  virtual ~GlobalDataHandle();

  GlobalDataHandle(const GlobalDataHandle& from);
  GlobalDataHandle(GlobalDataHandle&& from) noexcept
    : GlobalDataHandle() {
    *this = ::std::move(from);
  }

  inline GlobalDataHandle& operator=(const GlobalDataHandle& from) {
    CopyFrom(from);
    return *this;
  }
  inline GlobalDataHandle& operator=(GlobalDataHandle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GlobalDataHandle& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GlobalDataHandle* internal_default_instance() {
    return reinterpret_cast<const GlobalDataHandle*>(
               &_GlobalDataHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GlobalDataHandle& a, GlobalDataHandle& b) {
    a.Swap(&b);
  }
  inline void Swap(GlobalDataHandle* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GlobalDataHandle* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GlobalDataHandle* New() const final {
    return CreateMaybeMessage<GlobalDataHandle>(nullptr);
  }

  GlobalDataHandle* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GlobalDataHandle>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GlobalDataHandle& from);
  void MergeFrom(const GlobalDataHandle& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GlobalDataHandle* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.GlobalDataHandle";
  }
  protected:
  explicit GlobalDataHandle(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleFieldNumber = 1,
  };
  // int64 handle = 1;
  void clear_handle();
  ::PROTOBUF_NAMESPACE_ID::int64 handle() const;
  void set_handle(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.GlobalDataHandle)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class DeviceHandle :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DeviceHandle) */ {
 public:
  DeviceHandle();
  virtual ~DeviceHandle();

  DeviceHandle(const DeviceHandle& from);
  DeviceHandle(DeviceHandle&& from) noexcept
    : DeviceHandle() {
    *this = ::std::move(from);
  }

  inline DeviceHandle& operator=(const DeviceHandle& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceHandle& operator=(DeviceHandle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeviceHandle& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceHandle* internal_default_instance() {
    return reinterpret_cast<const DeviceHandle*>(
               &_DeviceHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(DeviceHandle& a, DeviceHandle& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceHandle* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceHandle* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeviceHandle* New() const final {
    return CreateMaybeMessage<DeviceHandle>(nullptr);
  }

  DeviceHandle* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeviceHandle>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeviceHandle& from);
  void MergeFrom(const DeviceHandle& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceHandle* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DeviceHandle";
  }
  protected:
  explicit DeviceHandle(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleFieldNumber = 1,
    kDeviceCountFieldNumber = 2,
  };
  // int64 handle = 1;
  void clear_handle();
  ::PROTOBUF_NAMESPACE_ID::int64 handle() const;
  void set_handle(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 device_count = 2;
  void clear_device_count();
  ::PROTOBUF_NAMESPACE_ID::int64 device_count() const;
  void set_device_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.DeviceHandle)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 handle_;
  ::PROTOBUF_NAMESPACE_ID::int64 device_count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class ChannelHandle :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ChannelHandle) */ {
 public:
  ChannelHandle();
  virtual ~ChannelHandle();

  ChannelHandle(const ChannelHandle& from);
  ChannelHandle(ChannelHandle&& from) noexcept
    : ChannelHandle() {
    *this = ::std::move(from);
  }

  inline ChannelHandle& operator=(const ChannelHandle& from) {
    CopyFrom(from);
    return *this;
  }
  inline ChannelHandle& operator=(ChannelHandle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ChannelHandle& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ChannelHandle* internal_default_instance() {
    return reinterpret_cast<const ChannelHandle*>(
               &_ChannelHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(ChannelHandle& a, ChannelHandle& b) {
    a.Swap(&b);
  }
  inline void Swap(ChannelHandle* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ChannelHandle* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ChannelHandle* New() const final {
    return CreateMaybeMessage<ChannelHandle>(nullptr);
  }

  ChannelHandle* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ChannelHandle>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ChannelHandle& from);
  void MergeFrom(const ChannelHandle& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ChannelHandle* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ChannelHandle";
  }
  protected:
  explicit ChannelHandle(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef ChannelHandle_ChannelType ChannelType;
  static constexpr ChannelType CHANNEL_TYPE_INVALID =
    ChannelHandle_ChannelType_CHANNEL_TYPE_INVALID;
  static constexpr ChannelType DEVICE_TO_DEVICE =
    ChannelHandle_ChannelType_DEVICE_TO_DEVICE;
  static constexpr ChannelType DEVICE_TO_HOST =
    ChannelHandle_ChannelType_DEVICE_TO_HOST;
  static constexpr ChannelType HOST_TO_DEVICE =
    ChannelHandle_ChannelType_HOST_TO_DEVICE;
  static inline bool ChannelType_IsValid(int value) {
    return ChannelHandle_ChannelType_IsValid(value);
  }
  static constexpr ChannelType ChannelType_MIN =
    ChannelHandle_ChannelType_ChannelType_MIN;
  static constexpr ChannelType ChannelType_MAX =
    ChannelHandle_ChannelType_ChannelType_MAX;
  static constexpr int ChannelType_ARRAYSIZE =
    ChannelHandle_ChannelType_ChannelType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ChannelType_descriptor() {
    return ChannelHandle_ChannelType_descriptor();
  }
  template<typename T>
  static inline const std::string& ChannelType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ChannelType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ChannelType_Name.");
    return ChannelHandle_ChannelType_Name(enum_t_value);
  }
  static inline bool ChannelType_Parse(const std::string& name,
      ChannelType* value) {
    return ChannelHandle_ChannelType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kHandleFieldNumber = 1,
    kTypeFieldNumber = 2,
  };
  // int64 handle = 1;
  void clear_handle();
  ::PROTOBUF_NAMESPACE_ID::int64 handle() const;
  void set_handle(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .xla.ChannelHandle.ChannelType type = 2;
  void clear_type();
  ::xla::ChannelHandle_ChannelType type() const;
  void set_type(::xla::ChannelHandle_ChannelType value);

  // @@protoc_insertion_point(class_scope:xla.ChannelHandle)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 handle_;
  int type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class DeviceAssignmentProto_ComputationDevice :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DeviceAssignmentProto.ComputationDevice) */ {
 public:
  DeviceAssignmentProto_ComputationDevice();
  virtual ~DeviceAssignmentProto_ComputationDevice();

  DeviceAssignmentProto_ComputationDevice(const DeviceAssignmentProto_ComputationDevice& from);
  DeviceAssignmentProto_ComputationDevice(DeviceAssignmentProto_ComputationDevice&& from) noexcept
    : DeviceAssignmentProto_ComputationDevice() {
    *this = ::std::move(from);
  }

  inline DeviceAssignmentProto_ComputationDevice& operator=(const DeviceAssignmentProto_ComputationDevice& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceAssignmentProto_ComputationDevice& operator=(DeviceAssignmentProto_ComputationDevice&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeviceAssignmentProto_ComputationDevice& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceAssignmentProto_ComputationDevice* internal_default_instance() {
    return reinterpret_cast<const DeviceAssignmentProto_ComputationDevice*>(
               &_DeviceAssignmentProto_ComputationDevice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(DeviceAssignmentProto_ComputationDevice& a, DeviceAssignmentProto_ComputationDevice& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceAssignmentProto_ComputationDevice* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceAssignmentProto_ComputationDevice* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeviceAssignmentProto_ComputationDevice* New() const final {
    return CreateMaybeMessage<DeviceAssignmentProto_ComputationDevice>(nullptr);
  }

  DeviceAssignmentProto_ComputationDevice* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeviceAssignmentProto_ComputationDevice>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeviceAssignmentProto_ComputationDevice& from);
  void MergeFrom(const DeviceAssignmentProto_ComputationDevice& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceAssignmentProto_ComputationDevice* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DeviceAssignmentProto.ComputationDevice";
  }
  protected:
  explicit DeviceAssignmentProto_ComputationDevice(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kReplicaDeviceIdsFieldNumber = 1,
  };
  // repeated int32 replica_device_ids = 1;
  int replica_device_ids_size() const;
  void clear_replica_device_ids();
  ::PROTOBUF_NAMESPACE_ID::int32 replica_device_ids(int index) const;
  void set_replica_device_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_replica_device_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      replica_device_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_replica_device_ids();

  // @@protoc_insertion_point(class_scope:xla.DeviceAssignmentProto.ComputationDevice)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > replica_device_ids_;
  mutable std::atomic<int> _replica_device_ids_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class DeviceAssignmentProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DeviceAssignmentProto) */ {
 public:
  DeviceAssignmentProto();
  virtual ~DeviceAssignmentProto();

  DeviceAssignmentProto(const DeviceAssignmentProto& from);
  DeviceAssignmentProto(DeviceAssignmentProto&& from) noexcept
    : DeviceAssignmentProto() {
    *this = ::std::move(from);
  }

  inline DeviceAssignmentProto& operator=(const DeviceAssignmentProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceAssignmentProto& operator=(DeviceAssignmentProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeviceAssignmentProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceAssignmentProto* internal_default_instance() {
    return reinterpret_cast<const DeviceAssignmentProto*>(
               &_DeviceAssignmentProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(DeviceAssignmentProto& a, DeviceAssignmentProto& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceAssignmentProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceAssignmentProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeviceAssignmentProto* New() const final {
    return CreateMaybeMessage<DeviceAssignmentProto>(nullptr);
  }

  DeviceAssignmentProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeviceAssignmentProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeviceAssignmentProto& from);
  void MergeFrom(const DeviceAssignmentProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceAssignmentProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DeviceAssignmentProto";
  }
  protected:
  explicit DeviceAssignmentProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef DeviceAssignmentProto_ComputationDevice ComputationDevice;

  // accessors -------------------------------------------------------

  enum : int {
    kComputationDevicesFieldNumber = 3,
    kReplicaCountFieldNumber = 1,
    kComputationCountFieldNumber = 2,
  };
  // repeated .xla.DeviceAssignmentProto.ComputationDevice computation_devices = 3;
  int computation_devices_size() const;
  void clear_computation_devices();
  ::xla::DeviceAssignmentProto_ComputationDevice* mutable_computation_devices(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceAssignmentProto_ComputationDevice >*
      mutable_computation_devices();
  const ::xla::DeviceAssignmentProto_ComputationDevice& computation_devices(int index) const;
  ::xla::DeviceAssignmentProto_ComputationDevice* add_computation_devices();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceAssignmentProto_ComputationDevice >&
      computation_devices() const;

  // int32 replica_count = 1;
  void clear_replica_count();
  ::PROTOBUF_NAMESPACE_ID::int32 replica_count() const;
  void set_replica_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 computation_count = 2;
  void clear_computation_count();
  ::PROTOBUF_NAMESPACE_ID::int32 computation_count() const;
  void set_computation_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:xla.DeviceAssignmentProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceAssignmentProto_ComputationDevice > computation_devices_;
  ::PROTOBUF_NAMESPACE_ID::int32 replica_count_;
  ::PROTOBUF_NAMESPACE_ID::int32 computation_count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class LiteralProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.LiteralProto) */ {
 public:
  LiteralProto();
  virtual ~LiteralProto();

  LiteralProto(const LiteralProto& from);
  LiteralProto(LiteralProto&& from) noexcept
    : LiteralProto() {
    *this = ::std::move(from);
  }

  inline LiteralProto& operator=(const LiteralProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline LiteralProto& operator=(LiteralProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LiteralProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LiteralProto* internal_default_instance() {
    return reinterpret_cast<const LiteralProto*>(
               &_LiteralProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(LiteralProto& a, LiteralProto& b) {
    a.Swap(&b);
  }
  inline void Swap(LiteralProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LiteralProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LiteralProto* New() const final {
    return CreateMaybeMessage<LiteralProto>(nullptr);
  }

  LiteralProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LiteralProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LiteralProto& from);
  void MergeFrom(const LiteralProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LiteralProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.LiteralProto";
  }
  protected:
  explicit LiteralProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPredsFieldNumber = 2,
    kS32SFieldNumber = 4,
    kS64SFieldNumber = 5,
    kU32SFieldNumber = 6,
    kU64SFieldNumber = 7,
    kF32SFieldNumber = 8,
    kF64SFieldNumber = 9,
    kTupleLiteralsFieldNumber = 10,
    kC64SFieldNumber = 12,
    kSparseIndicesFieldNumber = 14,
    kC128SFieldNumber = 18,
    kU8SFieldNumber = 3,
    kF16SFieldNumber = 11,
    kBf16SFieldNumber = 13,
    kS8SFieldNumber = 15,
    kU16SFieldNumber = 16,
    kS16SFieldNumber = 17,
    kShapeFieldNumber = 1,
  };
  // repeated bool preds = 2;
  int preds_size() const;
  void clear_preds();
  bool preds(int index) const;
  void set_preds(int index, bool value);
  void add_preds(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      preds() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_preds();

  // repeated int32 s32s = 4;
  int s32s_size() const;
  void clear_s32s();
  ::PROTOBUF_NAMESPACE_ID::int32 s32s(int index) const;
  void set_s32s(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_s32s(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      s32s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_s32s();

  // repeated int64 s64s = 5;
  int s64s_size() const;
  void clear_s64s();
  ::PROTOBUF_NAMESPACE_ID::int64 s64s(int index) const;
  void set_s64s(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_s64s(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      s64s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_s64s();

  // repeated uint32 u32s = 6;
  int u32s_size() const;
  void clear_u32s();
  ::PROTOBUF_NAMESPACE_ID::uint32 u32s(int index) const;
  void set_u32s(int index, ::PROTOBUF_NAMESPACE_ID::uint32 value);
  void add_u32s(::PROTOBUF_NAMESPACE_ID::uint32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
      u32s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
      mutable_u32s();

  // repeated uint64 u64s = 7;
  int u64s_size() const;
  void clear_u64s();
  ::PROTOBUF_NAMESPACE_ID::uint64 u64s(int index) const;
  void set_u64s(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value);
  void add_u64s(::PROTOBUF_NAMESPACE_ID::uint64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
      u64s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
      mutable_u64s();

  // repeated float f32s = 8;
  int f32s_size() const;
  void clear_f32s();
  float f32s(int index) const;
  void set_f32s(int index, float value);
  void add_f32s(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      f32s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_f32s();

  // repeated double f64s = 9;
  int f64s_size() const;
  void clear_f64s();
  double f64s(int index) const;
  void set_f64s(int index, double value);
  void add_f64s(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      f64s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_f64s();

  // repeated .xla.LiteralProto tuple_literals = 10;
  int tuple_literals_size() const;
  void clear_tuple_literals();
  ::xla::LiteralProto* mutable_tuple_literals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LiteralProto >*
      mutable_tuple_literals();
  const ::xla::LiteralProto& tuple_literals(int index) const;
  ::xla::LiteralProto* add_tuple_literals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LiteralProto >&
      tuple_literals() const;

  // repeated float c64s = 12;
  int c64s_size() const;
  void clear_c64s();
  float c64s(int index) const;
  void set_c64s(int index, float value);
  void add_c64s(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      c64s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_c64s();

  // repeated int64 sparse_indices = 14;
  int sparse_indices_size() const;
  void clear_sparse_indices();
  ::PROTOBUF_NAMESPACE_ID::int64 sparse_indices(int index) const;
  void set_sparse_indices(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_sparse_indices(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      sparse_indices() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_sparse_indices();

  // repeated double c128s = 18;
  int c128s_size() const;
  void clear_c128s();
  double c128s(int index) const;
  void set_c128s(int index, double value);
  void add_c128s(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      c128s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_c128s();

  // bytes u8s = 3;
  void clear_u8s();
  const std::string& u8s() const;
  void set_u8s(const std::string& value);
  void set_u8s(std::string&& value);
  void set_u8s(const char* value);
  void set_u8s(const void* value, size_t size);
  std::string* mutable_u8s();
  std::string* release_u8s();
  void set_allocated_u8s(std::string* u8s);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_u8s();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_u8s(
      std::string* u8s);

  // bytes f16s = 11;
  void clear_f16s();
  const std::string& f16s() const;
  void set_f16s(const std::string& value);
  void set_f16s(std::string&& value);
  void set_f16s(const char* value);
  void set_f16s(const void* value, size_t size);
  std::string* mutable_f16s();
  std::string* release_f16s();
  void set_allocated_f16s(std::string* f16s);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_f16s();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_f16s(
      std::string* f16s);

  // bytes bf16s = 13;
  void clear_bf16s();
  const std::string& bf16s() const;
  void set_bf16s(const std::string& value);
  void set_bf16s(std::string&& value);
  void set_bf16s(const char* value);
  void set_bf16s(const void* value, size_t size);
  std::string* mutable_bf16s();
  std::string* release_bf16s();
  void set_allocated_bf16s(std::string* bf16s);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_bf16s();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_bf16s(
      std::string* bf16s);

  // bytes s8s = 15;
  void clear_s8s();
  const std::string& s8s() const;
  void set_s8s(const std::string& value);
  void set_s8s(std::string&& value);
  void set_s8s(const char* value);
  void set_s8s(const void* value, size_t size);
  std::string* mutable_s8s();
  std::string* release_s8s();
  void set_allocated_s8s(std::string* s8s);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_s8s();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_s8s(
      std::string* s8s);

  // bytes u16s = 16;
  void clear_u16s();
  const std::string& u16s() const;
  void set_u16s(const std::string& value);
  void set_u16s(std::string&& value);
  void set_u16s(const char* value);
  void set_u16s(const void* value, size_t size);
  std::string* mutable_u16s();
  std::string* release_u16s();
  void set_allocated_u16s(std::string* u16s);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_u16s();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_u16s(
      std::string* u16s);

  // bytes s16s = 17;
  void clear_s16s();
  const std::string& s16s() const;
  void set_s16s(const std::string& value);
  void set_s16s(std::string&& value);
  void set_s16s(const char* value);
  void set_s16s(const void* value, size_t size);
  std::string* mutable_s16s();
  std::string* release_s16s();
  void set_allocated_s16s(std::string* s16s);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_s16s();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_s16s(
      std::string* s16s);

  // .xla.ShapeProto shape = 1;
  bool has_shape() const;
  void clear_shape();
  const ::xla::ShapeProto& shape() const;
  ::xla::ShapeProto* release_shape();
  ::xla::ShapeProto* mutable_shape();
  void set_allocated_shape(::xla::ShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::xla::ShapeProto* shape);
  ::xla::ShapeProto* unsafe_arena_release_shape();

  // @@protoc_insertion_point(class_scope:xla.LiteralProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > preds_;
  mutable std::atomic<int> _preds_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > s32s_;
  mutable std::atomic<int> _s32s_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > s64s_;
  mutable std::atomic<int> _s64s_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 > u32s_;
  mutable std::atomic<int> _u32s_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 > u64s_;
  mutable std::atomic<int> _u64s_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > f32s_;
  mutable std::atomic<int> _f32s_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > f64s_;
  mutable std::atomic<int> _f64s_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LiteralProto > tuple_literals_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > c64s_;
  mutable std::atomic<int> _c64s_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > sparse_indices_;
  mutable std::atomic<int> _sparse_indices_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > c128s_;
  mutable std::atomic<int> _c128s_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr u8s_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr f16s_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bf16s_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr s8s_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr u16s_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr s16s_;
  ::xla::ShapeProto* shape_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class WindowDimension :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.WindowDimension) */ {
 public:
  WindowDimension();
  virtual ~WindowDimension();

  WindowDimension(const WindowDimension& from);
  WindowDimension(WindowDimension&& from) noexcept
    : WindowDimension() {
    *this = ::std::move(from);
  }

  inline WindowDimension& operator=(const WindowDimension& from) {
    CopyFrom(from);
    return *this;
  }
  inline WindowDimension& operator=(WindowDimension&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WindowDimension& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WindowDimension* internal_default_instance() {
    return reinterpret_cast<const WindowDimension*>(
               &_WindowDimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(WindowDimension& a, WindowDimension& b) {
    a.Swap(&b);
  }
  inline void Swap(WindowDimension* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WindowDimension* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WindowDimension* New() const final {
    return CreateMaybeMessage<WindowDimension>(nullptr);
  }

  WindowDimension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WindowDimension>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WindowDimension& from);
  void MergeFrom(const WindowDimension& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WindowDimension* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.WindowDimension";
  }
  protected:
  explicit WindowDimension(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSizeFieldNumber = 1,
    kStrideFieldNumber = 2,
    kPaddingLowFieldNumber = 3,
    kPaddingHighFieldNumber = 4,
    kWindowDilationFieldNumber = 5,
    kBaseDilationFieldNumber = 6,
    kWindowReversalFieldNumber = 7,
  };
  // int64 size = 1;
  void clear_size();
  ::PROTOBUF_NAMESPACE_ID::int64 size() const;
  void set_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 stride = 2;
  void clear_stride();
  ::PROTOBUF_NAMESPACE_ID::int64 stride() const;
  void set_stride(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 padding_low = 3;
  void clear_padding_low();
  ::PROTOBUF_NAMESPACE_ID::int64 padding_low() const;
  void set_padding_low(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 padding_high = 4;
  void clear_padding_high();
  ::PROTOBUF_NAMESPACE_ID::int64 padding_high() const;
  void set_padding_high(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 window_dilation = 5;
  void clear_window_dilation();
  ::PROTOBUF_NAMESPACE_ID::int64 window_dilation() const;
  void set_window_dilation(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 base_dilation = 6;
  void clear_base_dilation();
  ::PROTOBUF_NAMESPACE_ID::int64 base_dilation() const;
  void set_base_dilation(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool window_reversal = 7;
  void clear_window_reversal();
  bool window_reversal() const;
  void set_window_reversal(bool value);

  // @@protoc_insertion_point(class_scope:xla.WindowDimension)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 size_;
  ::PROTOBUF_NAMESPACE_ID::int64 stride_;
  ::PROTOBUF_NAMESPACE_ID::int64 padding_low_;
  ::PROTOBUF_NAMESPACE_ID::int64 padding_high_;
  ::PROTOBUF_NAMESPACE_ID::int64 window_dilation_;
  ::PROTOBUF_NAMESPACE_ID::int64 base_dilation_;
  bool window_reversal_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class Window :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.Window) */ {
 public:
  Window();
  virtual ~Window();

  Window(const Window& from);
  Window(Window&& from) noexcept
    : Window() {
    *this = ::std::move(from);
  }

  inline Window& operator=(const Window& from) {
    CopyFrom(from);
    return *this;
  }
  inline Window& operator=(Window&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Window& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Window* internal_default_instance() {
    return reinterpret_cast<const Window*>(
               &_Window_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(Window& a, Window& b) {
    a.Swap(&b);
  }
  inline void Swap(Window* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Window* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Window* New() const final {
    return CreateMaybeMessage<Window>(nullptr);
  }

  Window* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Window>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Window& from);
  void MergeFrom(const Window& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Window* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.Window";
  }
  protected:
  explicit Window(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionsFieldNumber = 1,
  };
  // repeated .xla.WindowDimension dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  ::xla::WindowDimension* mutable_dimensions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::WindowDimension >*
      mutable_dimensions();
  const ::xla::WindowDimension& dimensions(int index) const;
  ::xla::WindowDimension* add_dimensions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::WindowDimension >&
      dimensions() const;

  // @@protoc_insertion_point(class_scope:xla.Window)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::WindowDimension > dimensions_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class GatherDimensionNumbers :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.GatherDimensionNumbers) */ {
 public:
  GatherDimensionNumbers();
  virtual ~GatherDimensionNumbers();

  GatherDimensionNumbers(const GatherDimensionNumbers& from);
  GatherDimensionNumbers(GatherDimensionNumbers&& from) noexcept
    : GatherDimensionNumbers() {
    *this = ::std::move(from);
  }

  inline GatherDimensionNumbers& operator=(const GatherDimensionNumbers& from) {
    CopyFrom(from);
    return *this;
  }
  inline GatherDimensionNumbers& operator=(GatherDimensionNumbers&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GatherDimensionNumbers& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GatherDimensionNumbers* internal_default_instance() {
    return reinterpret_cast<const GatherDimensionNumbers*>(
               &_GatherDimensionNumbers_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(GatherDimensionNumbers& a, GatherDimensionNumbers& b) {
    a.Swap(&b);
  }
  inline void Swap(GatherDimensionNumbers* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GatherDimensionNumbers* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GatherDimensionNumbers* New() const final {
    return CreateMaybeMessage<GatherDimensionNumbers>(nullptr);
  }

  GatherDimensionNumbers* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GatherDimensionNumbers>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GatherDimensionNumbers& from);
  void MergeFrom(const GatherDimensionNumbers& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GatherDimensionNumbers* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.GatherDimensionNumbers";
  }
  protected:
  explicit GatherDimensionNumbers(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOffsetDimsFieldNumber = 1,
    kCollapsedSliceDimsFieldNumber = 2,
    kStartIndexMapFieldNumber = 3,
    kIndexVectorDimFieldNumber = 4,
  };
  // repeated int64 offset_dims = 1;
  int offset_dims_size() const;
  void clear_offset_dims();
  ::PROTOBUF_NAMESPACE_ID::int64 offset_dims(int index) const;
  void set_offset_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_offset_dims(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      offset_dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_offset_dims();

  // repeated int64 collapsed_slice_dims = 2;
  int collapsed_slice_dims_size() const;
  void clear_collapsed_slice_dims();
  ::PROTOBUF_NAMESPACE_ID::int64 collapsed_slice_dims(int index) const;
  void set_collapsed_slice_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_collapsed_slice_dims(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      collapsed_slice_dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_collapsed_slice_dims();

  // repeated int64 start_index_map = 3;
  int start_index_map_size() const;
  void clear_start_index_map();
  ::PROTOBUF_NAMESPACE_ID::int64 start_index_map(int index) const;
  void set_start_index_map(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_start_index_map(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      start_index_map() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_start_index_map();

  // int64 index_vector_dim = 4;
  void clear_index_vector_dim();
  ::PROTOBUF_NAMESPACE_ID::int64 index_vector_dim() const;
  void set_index_vector_dim(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.GatherDimensionNumbers)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > offset_dims_;
  mutable std::atomic<int> _offset_dims_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > collapsed_slice_dims_;
  mutable std::atomic<int> _collapsed_slice_dims_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > start_index_map_;
  mutable std::atomic<int> _start_index_map_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 index_vector_dim_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class ScatterDimensionNumbers :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ScatterDimensionNumbers) */ {
 public:
  ScatterDimensionNumbers();
  virtual ~ScatterDimensionNumbers();

  ScatterDimensionNumbers(const ScatterDimensionNumbers& from);
  ScatterDimensionNumbers(ScatterDimensionNumbers&& from) noexcept
    : ScatterDimensionNumbers() {
    *this = ::std::move(from);
  }

  inline ScatterDimensionNumbers& operator=(const ScatterDimensionNumbers& from) {
    CopyFrom(from);
    return *this;
  }
  inline ScatterDimensionNumbers& operator=(ScatterDimensionNumbers&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ScatterDimensionNumbers& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ScatterDimensionNumbers* internal_default_instance() {
    return reinterpret_cast<const ScatterDimensionNumbers*>(
               &_ScatterDimensionNumbers_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(ScatterDimensionNumbers& a, ScatterDimensionNumbers& b) {
    a.Swap(&b);
  }
  inline void Swap(ScatterDimensionNumbers* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ScatterDimensionNumbers* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ScatterDimensionNumbers* New() const final {
    return CreateMaybeMessage<ScatterDimensionNumbers>(nullptr);
  }

  ScatterDimensionNumbers* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ScatterDimensionNumbers>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ScatterDimensionNumbers& from);
  void MergeFrom(const ScatterDimensionNumbers& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ScatterDimensionNumbers* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ScatterDimensionNumbers";
  }
  protected:
  explicit ScatterDimensionNumbers(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUpdateWindowDimsFieldNumber = 1,
    kInsertedWindowDimsFieldNumber = 2,
    kScatterDimsToOperandDimsFieldNumber = 3,
    kIndexVectorDimFieldNumber = 4,
  };
  // repeated int64 update_window_dims = 1;
  int update_window_dims_size() const;
  void clear_update_window_dims();
  ::PROTOBUF_NAMESPACE_ID::int64 update_window_dims(int index) const;
  void set_update_window_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_update_window_dims(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      update_window_dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_update_window_dims();

  // repeated int64 inserted_window_dims = 2;
  int inserted_window_dims_size() const;
  void clear_inserted_window_dims();
  ::PROTOBUF_NAMESPACE_ID::int64 inserted_window_dims(int index) const;
  void set_inserted_window_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_inserted_window_dims(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      inserted_window_dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_inserted_window_dims();

  // repeated int64 scatter_dims_to_operand_dims = 3;
  int scatter_dims_to_operand_dims_size() const;
  void clear_scatter_dims_to_operand_dims();
  ::PROTOBUF_NAMESPACE_ID::int64 scatter_dims_to_operand_dims(int index) const;
  void set_scatter_dims_to_operand_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_scatter_dims_to_operand_dims(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      scatter_dims_to_operand_dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_scatter_dims_to_operand_dims();

  // int64 index_vector_dim = 4;
  void clear_index_vector_dim();
  ::PROTOBUF_NAMESPACE_ID::int64 index_vector_dim() const;
  void set_index_vector_dim(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.ScatterDimensionNumbers)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > update_window_dims_;
  mutable std::atomic<int> _update_window_dims_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > inserted_window_dims_;
  mutable std::atomic<int> _inserted_window_dims_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > scatter_dims_to_operand_dims_;
  mutable std::atomic<int> _scatter_dims_to_operand_dims_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 index_vector_dim_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class ConvolutionDimensionNumbers :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ConvolutionDimensionNumbers) */ {
 public:
  ConvolutionDimensionNumbers();
  virtual ~ConvolutionDimensionNumbers();

  ConvolutionDimensionNumbers(const ConvolutionDimensionNumbers& from);
  ConvolutionDimensionNumbers(ConvolutionDimensionNumbers&& from) noexcept
    : ConvolutionDimensionNumbers() {
    *this = ::std::move(from);
  }

  inline ConvolutionDimensionNumbers& operator=(const ConvolutionDimensionNumbers& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConvolutionDimensionNumbers& operator=(ConvolutionDimensionNumbers&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ConvolutionDimensionNumbers& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConvolutionDimensionNumbers* internal_default_instance() {
    return reinterpret_cast<const ConvolutionDimensionNumbers*>(
               &_ConvolutionDimensionNumbers_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(ConvolutionDimensionNumbers& a, ConvolutionDimensionNumbers& b) {
    a.Swap(&b);
  }
  inline void Swap(ConvolutionDimensionNumbers* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConvolutionDimensionNumbers* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ConvolutionDimensionNumbers* New() const final {
    return CreateMaybeMessage<ConvolutionDimensionNumbers>(nullptr);
  }

  ConvolutionDimensionNumbers* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ConvolutionDimensionNumbers>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ConvolutionDimensionNumbers& from);
  void MergeFrom(const ConvolutionDimensionNumbers& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConvolutionDimensionNumbers* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ConvolutionDimensionNumbers";
  }
  protected:
  explicit ConvolutionDimensionNumbers(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKernelSpatialDimensionsFieldNumber = 6,
    kInputSpatialDimensionsFieldNumber = 11,
    kOutputSpatialDimensionsFieldNumber = 12,
    kKernelInputFeatureDimensionFieldNumber = 3,
    kKernelOutputFeatureDimensionFieldNumber = 4,
    kInputBatchDimensionFieldNumber = 7,
    kInputFeatureDimensionFieldNumber = 8,
    kOutputBatchDimensionFieldNumber = 9,
    kOutputFeatureDimensionFieldNumber = 10,
  };
  // repeated int64 kernel_spatial_dimensions = 6;
  int kernel_spatial_dimensions_size() const;
  void clear_kernel_spatial_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 kernel_spatial_dimensions(int index) const;
  void set_kernel_spatial_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_kernel_spatial_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      kernel_spatial_dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_kernel_spatial_dimensions();

  // repeated int64 input_spatial_dimensions = 11;
  int input_spatial_dimensions_size() const;
  void clear_input_spatial_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 input_spatial_dimensions(int index) const;
  void set_input_spatial_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_input_spatial_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      input_spatial_dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_input_spatial_dimensions();

  // repeated int64 output_spatial_dimensions = 12;
  int output_spatial_dimensions_size() const;
  void clear_output_spatial_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 output_spatial_dimensions(int index) const;
  void set_output_spatial_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_output_spatial_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      output_spatial_dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_output_spatial_dimensions();

  // int64 kernel_input_feature_dimension = 3;
  void clear_kernel_input_feature_dimension();
  ::PROTOBUF_NAMESPACE_ID::int64 kernel_input_feature_dimension() const;
  void set_kernel_input_feature_dimension(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 kernel_output_feature_dimension = 4;
  void clear_kernel_output_feature_dimension();
  ::PROTOBUF_NAMESPACE_ID::int64 kernel_output_feature_dimension() const;
  void set_kernel_output_feature_dimension(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 input_batch_dimension = 7;
  void clear_input_batch_dimension();
  ::PROTOBUF_NAMESPACE_ID::int64 input_batch_dimension() const;
  void set_input_batch_dimension(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 input_feature_dimension = 8;
  void clear_input_feature_dimension();
  ::PROTOBUF_NAMESPACE_ID::int64 input_feature_dimension() const;
  void set_input_feature_dimension(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 output_batch_dimension = 9;
  void clear_output_batch_dimension();
  ::PROTOBUF_NAMESPACE_ID::int64 output_batch_dimension() const;
  void set_output_batch_dimension(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 output_feature_dimension = 10;
  void clear_output_feature_dimension();
  ::PROTOBUF_NAMESPACE_ID::int64 output_feature_dimension() const;
  void set_output_feature_dimension(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.ConvolutionDimensionNumbers)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > kernel_spatial_dimensions_;
  mutable std::atomic<int> _kernel_spatial_dimensions_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > input_spatial_dimensions_;
  mutable std::atomic<int> _input_spatial_dimensions_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > output_spatial_dimensions_;
  mutable std::atomic<int> _output_spatial_dimensions_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 kernel_input_feature_dimension_;
  ::PROTOBUF_NAMESPACE_ID::int64 kernel_output_feature_dimension_;
  ::PROTOBUF_NAMESPACE_ID::int64 input_batch_dimension_;
  ::PROTOBUF_NAMESPACE_ID::int64 input_feature_dimension_;
  ::PROTOBUF_NAMESPACE_ID::int64 output_batch_dimension_;
  ::PROTOBUF_NAMESPACE_ID::int64 output_feature_dimension_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class DotDimensionNumbers :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DotDimensionNumbers) */ {
 public:
  DotDimensionNumbers();
  virtual ~DotDimensionNumbers();

  DotDimensionNumbers(const DotDimensionNumbers& from);
  DotDimensionNumbers(DotDimensionNumbers&& from) noexcept
    : DotDimensionNumbers() {
    *this = ::std::move(from);
  }

  inline DotDimensionNumbers& operator=(const DotDimensionNumbers& from) {
    CopyFrom(from);
    return *this;
  }
  inline DotDimensionNumbers& operator=(DotDimensionNumbers&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DotDimensionNumbers& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DotDimensionNumbers* internal_default_instance() {
    return reinterpret_cast<const DotDimensionNumbers*>(
               &_DotDimensionNumbers_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(DotDimensionNumbers& a, DotDimensionNumbers& b) {
    a.Swap(&b);
  }
  inline void Swap(DotDimensionNumbers* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DotDimensionNumbers* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DotDimensionNumbers* New() const final {
    return CreateMaybeMessage<DotDimensionNumbers>(nullptr);
  }

  DotDimensionNumbers* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DotDimensionNumbers>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DotDimensionNumbers& from);
  void MergeFrom(const DotDimensionNumbers& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DotDimensionNumbers* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DotDimensionNumbers";
  }
  protected:
  explicit DotDimensionNumbers(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLhsContractingDimensionsFieldNumber = 1,
    kRhsContractingDimensionsFieldNumber = 2,
    kLhsBatchDimensionsFieldNumber = 3,
    kRhsBatchDimensionsFieldNumber = 4,
  };
  // repeated int64 lhs_contracting_dimensions = 1;
  int lhs_contracting_dimensions_size() const;
  void clear_lhs_contracting_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 lhs_contracting_dimensions(int index) const;
  void set_lhs_contracting_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_lhs_contracting_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      lhs_contracting_dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_lhs_contracting_dimensions();

  // repeated int64 rhs_contracting_dimensions = 2;
  int rhs_contracting_dimensions_size() const;
  void clear_rhs_contracting_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 rhs_contracting_dimensions(int index) const;
  void set_rhs_contracting_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_rhs_contracting_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      rhs_contracting_dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_rhs_contracting_dimensions();

  // repeated int64 lhs_batch_dimensions = 3;
  int lhs_batch_dimensions_size() const;
  void clear_lhs_batch_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 lhs_batch_dimensions(int index) const;
  void set_lhs_batch_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_lhs_batch_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      lhs_batch_dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_lhs_batch_dimensions();

  // repeated int64 rhs_batch_dimensions = 4;
  int rhs_batch_dimensions_size() const;
  void clear_rhs_batch_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 rhs_batch_dimensions(int index) const;
  void set_rhs_batch_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_rhs_batch_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      rhs_batch_dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_rhs_batch_dimensions();

  // @@protoc_insertion_point(class_scope:xla.DotDimensionNumbers)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > lhs_contracting_dimensions_;
  mutable std::atomic<int> _lhs_contracting_dimensions_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > rhs_contracting_dimensions_;
  mutable std::atomic<int> _rhs_contracting_dimensions_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > lhs_batch_dimensions_;
  mutable std::atomic<int> _lhs_batch_dimensions_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > rhs_batch_dimensions_;
  mutable std::atomic<int> _rhs_batch_dimensions_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class TriangularSolveOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.TriangularSolveOptions) */ {
 public:
  TriangularSolveOptions();
  virtual ~TriangularSolveOptions();

  TriangularSolveOptions(const TriangularSolveOptions& from);
  TriangularSolveOptions(TriangularSolveOptions&& from) noexcept
    : TriangularSolveOptions() {
    *this = ::std::move(from);
  }

  inline TriangularSolveOptions& operator=(const TriangularSolveOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline TriangularSolveOptions& operator=(TriangularSolveOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TriangularSolveOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TriangularSolveOptions* internal_default_instance() {
    return reinterpret_cast<const TriangularSolveOptions*>(
               &_TriangularSolveOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(TriangularSolveOptions& a, TriangularSolveOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(TriangularSolveOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TriangularSolveOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TriangularSolveOptions* New() const final {
    return CreateMaybeMessage<TriangularSolveOptions>(nullptr);
  }

  TriangularSolveOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TriangularSolveOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TriangularSolveOptions& from);
  void MergeFrom(const TriangularSolveOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TriangularSolveOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.TriangularSolveOptions";
  }
  protected:
  explicit TriangularSolveOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TriangularSolveOptions_Transpose Transpose;
  static constexpr Transpose TRANSPOSE_INVALID =
    TriangularSolveOptions_Transpose_TRANSPOSE_INVALID;
  static constexpr Transpose NO_TRANSPOSE =
    TriangularSolveOptions_Transpose_NO_TRANSPOSE;
  static constexpr Transpose TRANSPOSE =
    TriangularSolveOptions_Transpose_TRANSPOSE;
  static constexpr Transpose ADJOINT =
    TriangularSolveOptions_Transpose_ADJOINT;
  static inline bool Transpose_IsValid(int value) {
    return TriangularSolveOptions_Transpose_IsValid(value);
  }
  static constexpr Transpose Transpose_MIN =
    TriangularSolveOptions_Transpose_Transpose_MIN;
  static constexpr Transpose Transpose_MAX =
    TriangularSolveOptions_Transpose_Transpose_MAX;
  static constexpr int Transpose_ARRAYSIZE =
    TriangularSolveOptions_Transpose_Transpose_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Transpose_descriptor() {
    return TriangularSolveOptions_Transpose_descriptor();
  }
  template<typename T>
  static inline const std::string& Transpose_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Transpose>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Transpose_Name.");
    return TriangularSolveOptions_Transpose_Name(enum_t_value);
  }
  static inline bool Transpose_Parse(const std::string& name,
      Transpose* value) {
    return TriangularSolveOptions_Transpose_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kLeftSideFieldNumber = 1,
    kLowerFieldNumber = 2,
    kUnitDiagonalFieldNumber = 3,
    kTransposeAFieldNumber = 4,
  };
  // bool left_side = 1;
  void clear_left_side();
  bool left_side() const;
  void set_left_side(bool value);

  // bool lower = 2;
  void clear_lower();
  bool lower() const;
  void set_lower(bool value);

  // bool unit_diagonal = 3;
  void clear_unit_diagonal();
  bool unit_diagonal() const;
  void set_unit_diagonal(bool value);

  // .xla.TriangularSolveOptions.Transpose transpose_a = 4;
  void clear_transpose_a();
  ::xla::TriangularSolveOptions_Transpose transpose_a() const;
  void set_transpose_a(::xla::TriangularSolveOptions_Transpose value);

  // @@protoc_insertion_point(class_scope:xla.TriangularSolveOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool left_side_;
  bool lower_;
  bool unit_diagonal_;
  int transpose_a_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class CholeskyOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CholeskyOptions) */ {
 public:
  CholeskyOptions();
  virtual ~CholeskyOptions();

  CholeskyOptions(const CholeskyOptions& from);
  CholeskyOptions(CholeskyOptions&& from) noexcept
    : CholeskyOptions() {
    *this = ::std::move(from);
  }

  inline CholeskyOptions& operator=(const CholeskyOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline CholeskyOptions& operator=(CholeskyOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CholeskyOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CholeskyOptions* internal_default_instance() {
    return reinterpret_cast<const CholeskyOptions*>(
               &_CholeskyOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(CholeskyOptions& a, CholeskyOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(CholeskyOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CholeskyOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CholeskyOptions* New() const final {
    return CreateMaybeMessage<CholeskyOptions>(nullptr);
  }

  CholeskyOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CholeskyOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CholeskyOptions& from);
  void MergeFrom(const CholeskyOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CholeskyOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CholeskyOptions";
  }
  protected:
  explicit CholeskyOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLowerFieldNumber = 1,
  };
  // bool lower = 1;
  void clear_lower();
  bool lower() const;
  void set_lower(bool value);

  // @@protoc_insertion_point(class_scope:xla.CholeskyOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool lower_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class FrontendAttributes_MapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FrontendAttributes_MapEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FrontendAttributes_MapEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  FrontendAttributes_MapEntry_DoNotUse();
  FrontendAttributes_MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FrontendAttributes_MapEntry_DoNotUse& other);
  static const FrontendAttributes_MapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FrontendAttributes_MapEntry_DoNotUse*>(&_FrontendAttributes_MapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "xla.FrontendAttributes.MapEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "xla.FrontendAttributes.MapEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[24];
  }

  public:
};

// -------------------------------------------------------------------

class FrontendAttributes :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.FrontendAttributes) */ {
 public:
  FrontendAttributes();
  virtual ~FrontendAttributes();

  FrontendAttributes(const FrontendAttributes& from);
  FrontendAttributes(FrontendAttributes&& from) noexcept
    : FrontendAttributes() {
    *this = ::std::move(from);
  }

  inline FrontendAttributes& operator=(const FrontendAttributes& from) {
    CopyFrom(from);
    return *this;
  }
  inline FrontendAttributes& operator=(FrontendAttributes&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FrontendAttributes& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FrontendAttributes* internal_default_instance() {
    return reinterpret_cast<const FrontendAttributes*>(
               &_FrontendAttributes_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(FrontendAttributes& a, FrontendAttributes& b) {
    a.Swap(&b);
  }
  inline void Swap(FrontendAttributes* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FrontendAttributes* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FrontendAttributes* New() const final {
    return CreateMaybeMessage<FrontendAttributes>(nullptr);
  }

  FrontendAttributes* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FrontendAttributes>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FrontendAttributes& from);
  void MergeFrom(const FrontendAttributes& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FrontendAttributes* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.FrontendAttributes";
  }
  protected:
  explicit FrontendAttributes(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kMapFieldNumber = 1,
  };
  // map<string, string> map = 1;
  int map_size() const;
  void clear_map();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_map();

  // @@protoc_insertion_point(class_scope:xla.FrontendAttributes)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FrontendAttributes_MapEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > map_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpSharding :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.OpSharding) */ {
 public:
  OpSharding();
  virtual ~OpSharding();

  OpSharding(const OpSharding& from);
  OpSharding(OpSharding&& from) noexcept
    : OpSharding() {
    *this = ::std::move(from);
  }

  inline OpSharding& operator=(const OpSharding& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpSharding& operator=(OpSharding&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpSharding& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpSharding* internal_default_instance() {
    return reinterpret_cast<const OpSharding*>(
               &_OpSharding_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  friend void swap(OpSharding& a, OpSharding& b) {
    a.Swap(&b);
  }
  inline void Swap(OpSharding* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpSharding* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpSharding* New() const final {
    return CreateMaybeMessage<OpSharding>(nullptr);
  }

  OpSharding* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpSharding>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpSharding& from);
  void MergeFrom(const OpSharding& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpSharding* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.OpSharding";
  }
  protected:
  explicit OpSharding(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef OpSharding_Type Type;
  static constexpr Type REPLICATED =
    OpSharding_Type_REPLICATED;
  static constexpr Type MAXIMAL =
    OpSharding_Type_MAXIMAL;
  static constexpr Type TUPLE =
    OpSharding_Type_TUPLE;
  static constexpr Type OTHER =
    OpSharding_Type_OTHER;
  static constexpr Type MANUAL =
    OpSharding_Type_MANUAL;
  static inline bool Type_IsValid(int value) {
    return OpSharding_Type_IsValid(value);
  }
  static constexpr Type Type_MIN =
    OpSharding_Type_Type_MIN;
  static constexpr Type Type_MAX =
    OpSharding_Type_Type_MAX;
  static constexpr int Type_ARRAYSIZE =
    OpSharding_Type_Type_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Type_descriptor() {
    return OpSharding_Type_descriptor();
  }
  template<typename T>
  static inline const std::string& Type_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Type>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Type_Name.");
    return OpSharding_Type_Name(enum_t_value);
  }
  static inline bool Type_Parse(const std::string& name,
      Type* value) {
    return OpSharding_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTileAssignmentDimensionsFieldNumber = 3,
    kTileAssignmentDevicesFieldNumber = 4,
    kTupleShardingsFieldNumber = 5,
    kMetadataFieldNumber = 7,
    kTileShapeFieldNumber = 2,
    kTypeFieldNumber = 1,
    kReplicateOnLastTileDimFieldNumber = 6,
  };
  // repeated int64 tile_assignment_dimensions = 3;
  int tile_assignment_dimensions_size() const;
  void clear_tile_assignment_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 tile_assignment_dimensions(int index) const;
  void set_tile_assignment_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_tile_assignment_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      tile_assignment_dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_tile_assignment_dimensions();

  // repeated int64 tile_assignment_devices = 4;
  int tile_assignment_devices_size() const;
  void clear_tile_assignment_devices();
  ::PROTOBUF_NAMESPACE_ID::int64 tile_assignment_devices(int index) const;
  void set_tile_assignment_devices(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_tile_assignment_devices(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      tile_assignment_devices() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_tile_assignment_devices();

  // repeated .xla.OpSharding tuple_shardings = 5;
  int tuple_shardings_size() const;
  void clear_tuple_shardings();
  ::xla::OpSharding* mutable_tuple_shardings(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::OpSharding >*
      mutable_tuple_shardings();
  const ::xla::OpSharding& tuple_shardings(int index) const;
  ::xla::OpSharding* add_tuple_shardings();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::OpSharding >&
      tuple_shardings() const;

  // repeated .xla.OpMetadata metadata = 7;
  int metadata_size() const;
  void clear_metadata();
  ::xla::OpMetadata* mutable_metadata(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::OpMetadata >*
      mutable_metadata();
  const ::xla::OpMetadata& metadata(int index) const;
  ::xla::OpMetadata* add_metadata();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::OpMetadata >&
      metadata() const;

  // .xla.ShapeProto tile_shape = 2;
  bool has_tile_shape() const;
  void clear_tile_shape();
  const ::xla::ShapeProto& tile_shape() const;
  ::xla::ShapeProto* release_tile_shape();
  ::xla::ShapeProto* mutable_tile_shape();
  void set_allocated_tile_shape(::xla::ShapeProto* tile_shape);
  void unsafe_arena_set_allocated_tile_shape(
      ::xla::ShapeProto* tile_shape);
  ::xla::ShapeProto* unsafe_arena_release_tile_shape();

  // .xla.OpSharding.Type type = 1;
  void clear_type();
  ::xla::OpSharding_Type type() const;
  void set_type(::xla::OpSharding_Type value);

  // bool replicate_on_last_tile_dim = 6;
  void clear_replicate_on_last_tile_dim();
  bool replicate_on_last_tile_dim() const;
  void set_replicate_on_last_tile_dim(bool value);

  // @@protoc_insertion_point(class_scope:xla.OpSharding)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > tile_assignment_dimensions_;
  mutable std::atomic<int> _tile_assignment_dimensions_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > tile_assignment_devices_;
  mutable std::atomic<int> _tile_assignment_devices_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::OpSharding > tuple_shardings_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::OpMetadata > metadata_;
  ::xla::ShapeProto* tile_shape_;
  int type_;
  bool replicate_on_last_tile_dim_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class ReplicaGroup :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ReplicaGroup) */ {
 public:
  ReplicaGroup();
  virtual ~ReplicaGroup();

  ReplicaGroup(const ReplicaGroup& from);
  ReplicaGroup(ReplicaGroup&& from) noexcept
    : ReplicaGroup() {
    *this = ::std::move(from);
  }

  inline ReplicaGroup& operator=(const ReplicaGroup& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReplicaGroup& operator=(ReplicaGroup&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ReplicaGroup& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ReplicaGroup* internal_default_instance() {
    return reinterpret_cast<const ReplicaGroup*>(
               &_ReplicaGroup_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  friend void swap(ReplicaGroup& a, ReplicaGroup& b) {
    a.Swap(&b);
  }
  inline void Swap(ReplicaGroup* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReplicaGroup* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ReplicaGroup* New() const final {
    return CreateMaybeMessage<ReplicaGroup>(nullptr);
  }

  ReplicaGroup* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ReplicaGroup>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ReplicaGroup& from);
  void MergeFrom(const ReplicaGroup& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReplicaGroup* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ReplicaGroup";
  }
  protected:
  explicit ReplicaGroup(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kReplicaIdsFieldNumber = 1,
  };
  // repeated int64 replica_ids = 1;
  int replica_ids_size() const;
  void clear_replica_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 replica_ids(int index) const;
  void set_replica_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_replica_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      replica_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_replica_ids();

  // @@protoc_insertion_point(class_scope:xla.ReplicaGroup)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > replica_ids_;
  mutable std::atomic<int> _replica_ids_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class SourceTarget :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.SourceTarget) */ {
 public:
  SourceTarget();
  virtual ~SourceTarget();

  SourceTarget(const SourceTarget& from);
  SourceTarget(SourceTarget&& from) noexcept
    : SourceTarget() {
    *this = ::std::move(from);
  }

  inline SourceTarget& operator=(const SourceTarget& from) {
    CopyFrom(from);
    return *this;
  }
  inline SourceTarget& operator=(SourceTarget&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SourceTarget& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SourceTarget* internal_default_instance() {
    return reinterpret_cast<const SourceTarget*>(
               &_SourceTarget_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  friend void swap(SourceTarget& a, SourceTarget& b) {
    a.Swap(&b);
  }
  inline void Swap(SourceTarget* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SourceTarget* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SourceTarget* New() const final {
    return CreateMaybeMessage<SourceTarget>(nullptr);
  }

  SourceTarget* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SourceTarget>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SourceTarget& from);
  void MergeFrom(const SourceTarget& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SourceTarget* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.SourceTarget";
  }
  protected:
  explicit SourceTarget(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceFieldNumber = 1,
    kTargetFieldNumber = 2,
  };
  // int64 source = 1;
  void clear_source();
  ::PROTOBUF_NAMESPACE_ID::int64 source() const;
  void set_source(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 target = 2;
  void clear_target();
  ::PROTOBUF_NAMESPACE_ID::int64 target() const;
  void set_target(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.SourceTarget)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 source_;
  ::PROTOBUF_NAMESPACE_ID::int64 target_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class PrecisionConfig :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.PrecisionConfig) */ {
 public:
  PrecisionConfig();
  virtual ~PrecisionConfig();

  PrecisionConfig(const PrecisionConfig& from);
  PrecisionConfig(PrecisionConfig&& from) noexcept
    : PrecisionConfig() {
    *this = ::std::move(from);
  }

  inline PrecisionConfig& operator=(const PrecisionConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline PrecisionConfig& operator=(PrecisionConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PrecisionConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PrecisionConfig* internal_default_instance() {
    return reinterpret_cast<const PrecisionConfig*>(
               &_PrecisionConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    29;

  friend void swap(PrecisionConfig& a, PrecisionConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(PrecisionConfig* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PrecisionConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PrecisionConfig* New() const final {
    return CreateMaybeMessage<PrecisionConfig>(nullptr);
  }

  PrecisionConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PrecisionConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PrecisionConfig& from);
  void MergeFrom(const PrecisionConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PrecisionConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.PrecisionConfig";
  }
  protected:
  explicit PrecisionConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef PrecisionConfig_Precision Precision;
  static constexpr Precision DEFAULT =
    PrecisionConfig_Precision_DEFAULT;
  static constexpr Precision HIGH =
    PrecisionConfig_Precision_HIGH;
  static constexpr Precision HIGHEST =
    PrecisionConfig_Precision_HIGHEST;
  static inline bool Precision_IsValid(int value) {
    return PrecisionConfig_Precision_IsValid(value);
  }
  static constexpr Precision Precision_MIN =
    PrecisionConfig_Precision_Precision_MIN;
  static constexpr Precision Precision_MAX =
    PrecisionConfig_Precision_Precision_MAX;
  static constexpr int Precision_ARRAYSIZE =
    PrecisionConfig_Precision_Precision_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Precision_descriptor() {
    return PrecisionConfig_Precision_descriptor();
  }
  template<typename T>
  static inline const std::string& Precision_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Precision>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Precision_Name.");
    return PrecisionConfig_Precision_Name(enum_t_value);
  }
  static inline bool Precision_Parse(const std::string& name,
      Precision* value) {
    return PrecisionConfig_Precision_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kOperandPrecisionFieldNumber = 1,
  };
  // repeated .xla.PrecisionConfig.Precision operand_precision = 1;
  int operand_precision_size() const;
  void clear_operand_precision();
  ::xla::PrecisionConfig_Precision operand_precision(int index) const;
  void set_operand_precision(int index, ::xla::PrecisionConfig_Precision value);
  void add_operand_precision(::xla::PrecisionConfig_Precision value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& operand_precision() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_operand_precision();

  // @@protoc_insertion_point(class_scope:xla.PrecisionConfig)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> operand_precision_;
  mutable std::atomic<int> _operand_precision_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class ParameterReplication :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ParameterReplication) */ {
 public:
  ParameterReplication();
  virtual ~ParameterReplication();

  ParameterReplication(const ParameterReplication& from);
  ParameterReplication(ParameterReplication&& from) noexcept
    : ParameterReplication() {
    *this = ::std::move(from);
  }

  inline ParameterReplication& operator=(const ParameterReplication& from) {
    CopyFrom(from);
    return *this;
  }
  inline ParameterReplication& operator=(ParameterReplication&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ParameterReplication& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ParameterReplication* internal_default_instance() {
    return reinterpret_cast<const ParameterReplication*>(
               &_ParameterReplication_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    30;

  friend void swap(ParameterReplication& a, ParameterReplication& b) {
    a.Swap(&b);
  }
  inline void Swap(ParameterReplication* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ParameterReplication* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ParameterReplication* New() const final {
    return CreateMaybeMessage<ParameterReplication>(nullptr);
  }

  ParameterReplication* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ParameterReplication>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ParameterReplication& from);
  void MergeFrom(const ParameterReplication& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ParameterReplication* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ParameterReplication";
  }
  protected:
  explicit ParameterReplication(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kReplicatedAtLeafBuffersFieldNumber = 1,
  };
  // repeated bool replicated_at_leaf_buffers = 1;
  int replicated_at_leaf_buffers_size() const;
  void clear_replicated_at_leaf_buffers();
  bool replicated_at_leaf_buffers(int index) const;
  void set_replicated_at_leaf_buffers(int index, bool value);
  void add_replicated_at_leaf_buffers(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      replicated_at_leaf_buffers() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_replicated_at_leaf_buffers();

  // @@protoc_insertion_point(class_scope:xla.ParameterReplication)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > replicated_at_leaf_buffers_;
  mutable std::atomic<int> _replicated_at_leaf_buffers_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class WhileLoopBackendConfig_KnownTripCount :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.WhileLoopBackendConfig.KnownTripCount) */ {
 public:
  WhileLoopBackendConfig_KnownTripCount();
  virtual ~WhileLoopBackendConfig_KnownTripCount();

  WhileLoopBackendConfig_KnownTripCount(const WhileLoopBackendConfig_KnownTripCount& from);
  WhileLoopBackendConfig_KnownTripCount(WhileLoopBackendConfig_KnownTripCount&& from) noexcept
    : WhileLoopBackendConfig_KnownTripCount() {
    *this = ::std::move(from);
  }

  inline WhileLoopBackendConfig_KnownTripCount& operator=(const WhileLoopBackendConfig_KnownTripCount& from) {
    CopyFrom(from);
    return *this;
  }
  inline WhileLoopBackendConfig_KnownTripCount& operator=(WhileLoopBackendConfig_KnownTripCount&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WhileLoopBackendConfig_KnownTripCount& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WhileLoopBackendConfig_KnownTripCount* internal_default_instance() {
    return reinterpret_cast<const WhileLoopBackendConfig_KnownTripCount*>(
               &_WhileLoopBackendConfig_KnownTripCount_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    31;

  friend void swap(WhileLoopBackendConfig_KnownTripCount& a, WhileLoopBackendConfig_KnownTripCount& b) {
    a.Swap(&b);
  }
  inline void Swap(WhileLoopBackendConfig_KnownTripCount* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WhileLoopBackendConfig_KnownTripCount* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WhileLoopBackendConfig_KnownTripCount* New() const final {
    return CreateMaybeMessage<WhileLoopBackendConfig_KnownTripCount>(nullptr);
  }

  WhileLoopBackendConfig_KnownTripCount* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WhileLoopBackendConfig_KnownTripCount>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WhileLoopBackendConfig_KnownTripCount& from);
  void MergeFrom(const WhileLoopBackendConfig_KnownTripCount& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WhileLoopBackendConfig_KnownTripCount* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.WhileLoopBackendConfig.KnownTripCount";
  }
  protected:
  explicit WhileLoopBackendConfig_KnownTripCount(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNFieldNumber = 1,
  };
  // int64 n = 1;
  void clear_n();
  ::PROTOBUF_NAMESPACE_ID::int64 n() const;
  void set_n(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.WhileLoopBackendConfig.KnownTripCount)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 n_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class WhileLoopBackendConfig :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.WhileLoopBackendConfig) */ {
 public:
  WhileLoopBackendConfig();
  virtual ~WhileLoopBackendConfig();

  WhileLoopBackendConfig(const WhileLoopBackendConfig& from);
  WhileLoopBackendConfig(WhileLoopBackendConfig&& from) noexcept
    : WhileLoopBackendConfig() {
    *this = ::std::move(from);
  }

  inline WhileLoopBackendConfig& operator=(const WhileLoopBackendConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline WhileLoopBackendConfig& operator=(WhileLoopBackendConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WhileLoopBackendConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WhileLoopBackendConfig* internal_default_instance() {
    return reinterpret_cast<const WhileLoopBackendConfig*>(
               &_WhileLoopBackendConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    32;

  friend void swap(WhileLoopBackendConfig& a, WhileLoopBackendConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(WhileLoopBackendConfig* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WhileLoopBackendConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WhileLoopBackendConfig* New() const final {
    return CreateMaybeMessage<WhileLoopBackendConfig>(nullptr);
  }

  WhileLoopBackendConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WhileLoopBackendConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WhileLoopBackendConfig& from);
  void MergeFrom(const WhileLoopBackendConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WhileLoopBackendConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.WhileLoopBackendConfig";
  }
  protected:
  explicit WhileLoopBackendConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef WhileLoopBackendConfig_KnownTripCount KnownTripCount;

  // accessors -------------------------------------------------------

  enum : int {
    kKnownTripCountFieldNumber = 1,
  };
  // .xla.WhileLoopBackendConfig.KnownTripCount known_trip_count = 1;
  bool has_known_trip_count() const;
  void clear_known_trip_count();
  const ::xla::WhileLoopBackendConfig_KnownTripCount& known_trip_count() const;
  ::xla::WhileLoopBackendConfig_KnownTripCount* release_known_trip_count();
  ::xla::WhileLoopBackendConfig_KnownTripCount* mutable_known_trip_count();
  void set_allocated_known_trip_count(::xla::WhileLoopBackendConfig_KnownTripCount* known_trip_count);
  void unsafe_arena_set_allocated_known_trip_count(
      ::xla::WhileLoopBackendConfig_KnownTripCount* known_trip_count);
  ::xla::WhileLoopBackendConfig_KnownTripCount* unsafe_arena_release_known_trip_count();

  // @@protoc_insertion_point(class_scope:xla.WhileLoopBackendConfig)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::xla::WhileLoopBackendConfig_KnownTripCount* known_trip_count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// -------------------------------------------------------------------

class CustomCallOutputOperandAliasing :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CustomCallOutputOperandAliasing) */ {
 public:
  CustomCallOutputOperandAliasing();
  virtual ~CustomCallOutputOperandAliasing();

  CustomCallOutputOperandAliasing(const CustomCallOutputOperandAliasing& from);
  CustomCallOutputOperandAliasing(CustomCallOutputOperandAliasing&& from) noexcept
    : CustomCallOutputOperandAliasing() {
    *this = ::std::move(from);
  }

  inline CustomCallOutputOperandAliasing& operator=(const CustomCallOutputOperandAliasing& from) {
    CopyFrom(from);
    return *this;
  }
  inline CustomCallOutputOperandAliasing& operator=(CustomCallOutputOperandAliasing&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CustomCallOutputOperandAliasing& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CustomCallOutputOperandAliasing* internal_default_instance() {
    return reinterpret_cast<const CustomCallOutputOperandAliasing*>(
               &_CustomCallOutputOperandAliasing_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    33;

  friend void swap(CustomCallOutputOperandAliasing& a, CustomCallOutputOperandAliasing& b) {
    a.Swap(&b);
  }
  inline void Swap(CustomCallOutputOperandAliasing* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CustomCallOutputOperandAliasing* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CustomCallOutputOperandAliasing* New() const final {
    return CreateMaybeMessage<CustomCallOutputOperandAliasing>(nullptr);
  }

  CustomCallOutputOperandAliasing* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CustomCallOutputOperandAliasing>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CustomCallOutputOperandAliasing& from);
  void MergeFrom(const CustomCallOutputOperandAliasing& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CustomCallOutputOperandAliasing* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CustomCallOutputOperandAliasing";
  }
  protected:
  explicit CustomCallOutputOperandAliasing(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputShapeIndexFieldNumber = 1,
    kOperandShapeIndexFieldNumber = 3,
    kOperandIndexFieldNumber = 2,
  };
  // repeated int64 output_shape_index = 1;
  int output_shape_index_size() const;
  void clear_output_shape_index();
  ::PROTOBUF_NAMESPACE_ID::int64 output_shape_index(int index) const;
  void set_output_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_output_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      output_shape_index() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_output_shape_index();

  // repeated int64 operand_shape_index = 3;
  int operand_shape_index_size() const;
  void clear_operand_shape_index();
  ::PROTOBUF_NAMESPACE_ID::int64 operand_shape_index(int index) const;
  void set_operand_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_operand_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      operand_shape_index() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_operand_shape_index();

  // int64 operand_index = 2;
  void clear_operand_index();
  ::PROTOBUF_NAMESPACE_ID::int64 operand_index() const;
  void set_operand_index(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.CustomCallOutputOperandAliasing)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > output_shape_index_;
  mutable std::atomic<int> _output_shape_index_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > operand_shape_index_;
  mutable std::atomic<int> _operand_shape_index_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 operand_index_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PaddingConfig_PaddingConfigDimension

// int64 edge_padding_low = 1;
inline void PaddingConfig_PaddingConfigDimension::clear_edge_padding_low() {
  edge_padding_low_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 PaddingConfig_PaddingConfigDimension::edge_padding_low() const {
  // @@protoc_insertion_point(field_get:xla.PaddingConfig.PaddingConfigDimension.edge_padding_low)
  return edge_padding_low_;
}
inline void PaddingConfig_PaddingConfigDimension::set_edge_padding_low(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  edge_padding_low_ = value;
  // @@protoc_insertion_point(field_set:xla.PaddingConfig.PaddingConfigDimension.edge_padding_low)
}

// int64 edge_padding_high = 2;
inline void PaddingConfig_PaddingConfigDimension::clear_edge_padding_high() {
  edge_padding_high_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 PaddingConfig_PaddingConfigDimension::edge_padding_high() const {
  // @@protoc_insertion_point(field_get:xla.PaddingConfig.PaddingConfigDimension.edge_padding_high)
  return edge_padding_high_;
}
inline void PaddingConfig_PaddingConfigDimension::set_edge_padding_high(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  edge_padding_high_ = value;
  // @@protoc_insertion_point(field_set:xla.PaddingConfig.PaddingConfigDimension.edge_padding_high)
}

// int64 interior_padding = 3;
inline void PaddingConfig_PaddingConfigDimension::clear_interior_padding() {
  interior_padding_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 PaddingConfig_PaddingConfigDimension::interior_padding() const {
  // @@protoc_insertion_point(field_get:xla.PaddingConfig.PaddingConfigDimension.interior_padding)
  return interior_padding_;
}
inline void PaddingConfig_PaddingConfigDimension::set_interior_padding(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  interior_padding_ = value;
  // @@protoc_insertion_point(field_set:xla.PaddingConfig.PaddingConfigDimension.interior_padding)
}

// -------------------------------------------------------------------

// PaddingConfig

// repeated .xla.PaddingConfig.PaddingConfigDimension dimensions = 1;
inline int PaddingConfig::dimensions_size() const {
  return dimensions_.size();
}
inline void PaddingConfig::clear_dimensions() {
  dimensions_.Clear();
}
inline ::xla::PaddingConfig_PaddingConfigDimension* PaddingConfig::mutable_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:xla.PaddingConfig.dimensions)
  return dimensions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::PaddingConfig_PaddingConfigDimension >*
PaddingConfig::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.PaddingConfig.dimensions)
  return &dimensions_;
}
inline const ::xla::PaddingConfig_PaddingConfigDimension& PaddingConfig::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.PaddingConfig.dimensions)
  return dimensions_.Get(index);
}
inline ::xla::PaddingConfig_PaddingConfigDimension* PaddingConfig::add_dimensions() {
  // @@protoc_insertion_point(field_add:xla.PaddingConfig.dimensions)
  return dimensions_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::PaddingConfig_PaddingConfigDimension >&
PaddingConfig::dimensions() const {
  // @@protoc_insertion_point(field_list:xla.PaddingConfig.dimensions)
  return dimensions_;
}

// -------------------------------------------------------------------

// TileProto

// repeated int64 dimensions = 1;
inline int TileProto::dimensions_size() const {
  return dimensions_.size();
}
inline void TileProto::clear_dimensions() {
  dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TileProto::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.TileProto.dimensions)
  return dimensions_.Get(index);
}
inline void TileProto::set_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.TileProto.dimensions)
}
inline void TileProto::add_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.TileProto.dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
TileProto::dimensions() const {
  // @@protoc_insertion_point(field_list:xla.TileProto.dimensions)
  return dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
TileProto::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.TileProto.dimensions)
  return &dimensions_;
}

// -------------------------------------------------------------------

// LayoutProto

// .xla.Format format = 4;
inline void LayoutProto::clear_format() {
  format_ = 0;
}
inline ::xla::Format LayoutProto::format() const {
  // @@protoc_insertion_point(field_get:xla.LayoutProto.format)
  return static_cast< ::xla::Format >(format_);
}
inline void LayoutProto::set_format(::xla::Format value) {
  
  format_ = value;
  // @@protoc_insertion_point(field_set:xla.LayoutProto.format)
}

// repeated int64 minor_to_major = 1;
inline int LayoutProto::minor_to_major_size() const {
  return minor_to_major_.size();
}
inline void LayoutProto::clear_minor_to_major() {
  minor_to_major_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LayoutProto::minor_to_major(int index) const {
  // @@protoc_insertion_point(field_get:xla.LayoutProto.minor_to_major)
  return minor_to_major_.Get(index);
}
inline void LayoutProto::set_minor_to_major(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  minor_to_major_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LayoutProto.minor_to_major)
}
inline void LayoutProto::add_minor_to_major(::PROTOBUF_NAMESPACE_ID::int64 value) {
  minor_to_major_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LayoutProto.minor_to_major)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
LayoutProto::minor_to_major() const {
  // @@protoc_insertion_point(field_list:xla.LayoutProto.minor_to_major)
  return minor_to_major_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
LayoutProto::mutable_minor_to_major() {
  // @@protoc_insertion_point(field_mutable_list:xla.LayoutProto.minor_to_major)
  return &minor_to_major_;
}

// repeated .xla.TileProto tiles = 6;
inline int LayoutProto::tiles_size() const {
  return tiles_.size();
}
inline void LayoutProto::clear_tiles() {
  tiles_.Clear();
}
inline ::xla::TileProto* LayoutProto::mutable_tiles(int index) {
  // @@protoc_insertion_point(field_mutable:xla.LayoutProto.tiles)
  return tiles_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::TileProto >*
LayoutProto::mutable_tiles() {
  // @@protoc_insertion_point(field_mutable_list:xla.LayoutProto.tiles)
  return &tiles_;
}
inline const ::xla::TileProto& LayoutProto::tiles(int index) const {
  // @@protoc_insertion_point(field_get:xla.LayoutProto.tiles)
  return tiles_.Get(index);
}
inline ::xla::TileProto* LayoutProto::add_tiles() {
  // @@protoc_insertion_point(field_add:xla.LayoutProto.tiles)
  return tiles_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::TileProto >&
LayoutProto::tiles() const {
  // @@protoc_insertion_point(field_list:xla.LayoutProto.tiles)
  return tiles_;
}

// int64 element_size_in_bits = 7;
inline void LayoutProto::clear_element_size_in_bits() {
  element_size_in_bits_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LayoutProto::element_size_in_bits() const {
  // @@protoc_insertion_point(field_get:xla.LayoutProto.element_size_in_bits)
  return element_size_in_bits_;
}
inline void LayoutProto::set_element_size_in_bits(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  element_size_in_bits_ = value;
  // @@protoc_insertion_point(field_set:xla.LayoutProto.element_size_in_bits)
}

// int64 memory_space = 8;
inline void LayoutProto::clear_memory_space() {
  memory_space_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LayoutProto::memory_space() const {
  // @@protoc_insertion_point(field_get:xla.LayoutProto.memory_space)
  return memory_space_;
}
inline void LayoutProto::set_memory_space(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  memory_space_ = value;
  // @@protoc_insertion_point(field_set:xla.LayoutProto.memory_space)
}

// -------------------------------------------------------------------

// ShapeProto

// .xla.PrimitiveType element_type = 2;
inline void ShapeProto::clear_element_type() {
  element_type_ = 0;
}
inline ::xla::PrimitiveType ShapeProto::element_type() const {
  // @@protoc_insertion_point(field_get:xla.ShapeProto.element_type)
  return static_cast< ::xla::PrimitiveType >(element_type_);
}
inline void ShapeProto::set_element_type(::xla::PrimitiveType value) {
  
  element_type_ = value;
  // @@protoc_insertion_point(field_set:xla.ShapeProto.element_type)
}

// repeated int64 dimensions = 3;
inline int ShapeProto::dimensions_size() const {
  return dimensions_.size();
}
inline void ShapeProto::clear_dimensions() {
  dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ShapeProto::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.ShapeProto.dimensions)
  return dimensions_.Get(index);
}
inline void ShapeProto::set_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ShapeProto.dimensions)
}
inline void ShapeProto::add_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ShapeProto.dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ShapeProto::dimensions() const {
  // @@protoc_insertion_point(field_list:xla.ShapeProto.dimensions)
  return dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ShapeProto::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.ShapeProto.dimensions)
  return &dimensions_;
}

// repeated .xla.ShapeProto tuple_shapes = 4;
inline int ShapeProto::tuple_shapes_size() const {
  return tuple_shapes_.size();
}
inline void ShapeProto::clear_tuple_shapes() {
  tuple_shapes_.Clear();
}
inline ::xla::ShapeProto* ShapeProto::mutable_tuple_shapes(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ShapeProto.tuple_shapes)
  return tuple_shapes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
ShapeProto::mutable_tuple_shapes() {
  // @@protoc_insertion_point(field_mutable_list:xla.ShapeProto.tuple_shapes)
  return &tuple_shapes_;
}
inline const ::xla::ShapeProto& ShapeProto::tuple_shapes(int index) const {
  // @@protoc_insertion_point(field_get:xla.ShapeProto.tuple_shapes)
  return tuple_shapes_.Get(index);
}
inline ::xla::ShapeProto* ShapeProto::add_tuple_shapes() {
  // @@protoc_insertion_point(field_add:xla.ShapeProto.tuple_shapes)
  return tuple_shapes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
ShapeProto::tuple_shapes() const {
  // @@protoc_insertion_point(field_list:xla.ShapeProto.tuple_shapes)
  return tuple_shapes_;
}

// .xla.LayoutProto layout = 5;
inline bool ShapeProto::has_layout() const {
  return this != internal_default_instance() && layout_ != nullptr;
}
inline void ShapeProto::clear_layout() {
  if (GetArenaNoVirtual() == nullptr && layout_ != nullptr) {
    delete layout_;
  }
  layout_ = nullptr;
}
inline const ::xla::LayoutProto& ShapeProto::layout() const {
  const ::xla::LayoutProto* p = layout_;
  // @@protoc_insertion_point(field_get:xla.ShapeProto.layout)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LayoutProto*>(
      &::xla::_LayoutProto_default_instance_);
}
inline ::xla::LayoutProto* ShapeProto::release_layout() {
  // @@protoc_insertion_point(field_release:xla.ShapeProto.layout)
  
  ::xla::LayoutProto* temp = layout_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  layout_ = nullptr;
  return temp;
}
inline ::xla::LayoutProto* ShapeProto::unsafe_arena_release_layout() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.ShapeProto.layout)
  
  ::xla::LayoutProto* temp = layout_;
  layout_ = nullptr;
  return temp;
}
inline ::xla::LayoutProto* ShapeProto::mutable_layout() {
  
  if (layout_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LayoutProto>(GetArenaNoVirtual());
    layout_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ShapeProto.layout)
  return layout_;
}
inline void ShapeProto::set_allocated_layout(::xla::LayoutProto* layout) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete layout_;
  }
  if (layout) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(layout);
    if (message_arena != submessage_arena) {
      layout = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, layout, submessage_arena);
    }
    
  } else {
    
  }
  layout_ = layout;
  // @@protoc_insertion_point(field_set_allocated:xla.ShapeProto.layout)
}

// repeated bool is_dynamic_dimension = 6;
inline int ShapeProto::is_dynamic_dimension_size() const {
  return is_dynamic_dimension_.size();
}
inline void ShapeProto::clear_is_dynamic_dimension() {
  is_dynamic_dimension_.Clear();
}
inline bool ShapeProto::is_dynamic_dimension(int index) const {
  // @@protoc_insertion_point(field_get:xla.ShapeProto.is_dynamic_dimension)
  return is_dynamic_dimension_.Get(index);
}
inline void ShapeProto::set_is_dynamic_dimension(int index, bool value) {
  is_dynamic_dimension_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ShapeProto.is_dynamic_dimension)
}
inline void ShapeProto::add_is_dynamic_dimension(bool value) {
  is_dynamic_dimension_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ShapeProto.is_dynamic_dimension)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
ShapeProto::is_dynamic_dimension() const {
  // @@protoc_insertion_point(field_list:xla.ShapeProto.is_dynamic_dimension)
  return is_dynamic_dimension_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
ShapeProto::mutable_is_dynamic_dimension() {
  // @@protoc_insertion_point(field_mutable_list:xla.ShapeProto.is_dynamic_dimension)
  return &is_dynamic_dimension_;
}

// -------------------------------------------------------------------

// ProgramShapeProto

// repeated .xla.ShapeProto parameters = 1;
inline int ProgramShapeProto::parameters_size() const {
  return parameters_.size();
}
inline void ProgramShapeProto::clear_parameters() {
  parameters_.Clear();
}
inline ::xla::ShapeProto* ProgramShapeProto::mutable_parameters(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ProgramShapeProto.parameters)
  return parameters_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
ProgramShapeProto::mutable_parameters() {
  // @@protoc_insertion_point(field_mutable_list:xla.ProgramShapeProto.parameters)
  return &parameters_;
}
inline const ::xla::ShapeProto& ProgramShapeProto::parameters(int index) const {
  // @@protoc_insertion_point(field_get:xla.ProgramShapeProto.parameters)
  return parameters_.Get(index);
}
inline ::xla::ShapeProto* ProgramShapeProto::add_parameters() {
  // @@protoc_insertion_point(field_add:xla.ProgramShapeProto.parameters)
  return parameters_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
ProgramShapeProto::parameters() const {
  // @@protoc_insertion_point(field_list:xla.ProgramShapeProto.parameters)
  return parameters_;
}

// .xla.ShapeProto result = 2;
inline bool ProgramShapeProto::has_result() const {
  return this != internal_default_instance() && result_ != nullptr;
}
inline void ProgramShapeProto::clear_result() {
  if (GetArenaNoVirtual() == nullptr && result_ != nullptr) {
    delete result_;
  }
  result_ = nullptr;
}
inline const ::xla::ShapeProto& ProgramShapeProto::result() const {
  const ::xla::ShapeProto* p = result_;
  // @@protoc_insertion_point(field_get:xla.ProgramShapeProto.result)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* ProgramShapeProto::release_result() {
  // @@protoc_insertion_point(field_release:xla.ProgramShapeProto.result)
  
  ::xla::ShapeProto* temp = result_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  result_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* ProgramShapeProto::unsafe_arena_release_result() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.ProgramShapeProto.result)
  
  ::xla::ShapeProto* temp = result_;
  result_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* ProgramShapeProto::mutable_result() {
  
  if (result_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    result_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ProgramShapeProto.result)
  return result_;
}
inline void ProgramShapeProto::set_allocated_result(::xla::ShapeProto* result) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete result_;
  }
  if (result) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(result);
    if (message_arena != submessage_arena) {
      result = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, result, submessage_arena);
    }
    
  } else {
    
  }
  result_ = result;
  // @@protoc_insertion_point(field_set_allocated:xla.ProgramShapeProto.result)
}

// repeated string parameter_names = 3;
inline int ProgramShapeProto::parameter_names_size() const {
  return parameter_names_.size();
}
inline void ProgramShapeProto::clear_parameter_names() {
  parameter_names_.Clear();
}
inline const std::string& ProgramShapeProto::parameter_names(int index) const {
  // @@protoc_insertion_point(field_get:xla.ProgramShapeProto.parameter_names)
  return parameter_names_.Get(index);
}
inline std::string* ProgramShapeProto::mutable_parameter_names(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ProgramShapeProto.parameter_names)
  return parameter_names_.Mutable(index);
}
inline void ProgramShapeProto::set_parameter_names(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:xla.ProgramShapeProto.parameter_names)
  parameter_names_.Mutable(index)->assign(value);
}
inline void ProgramShapeProto::set_parameter_names(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:xla.ProgramShapeProto.parameter_names)
  parameter_names_.Mutable(index)->assign(std::move(value));
}
inline void ProgramShapeProto::set_parameter_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  parameter_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:xla.ProgramShapeProto.parameter_names)
}
inline void ProgramShapeProto::set_parameter_names(int index, const char* value, size_t size) {
  parameter_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:xla.ProgramShapeProto.parameter_names)
}
inline std::string* ProgramShapeProto::add_parameter_names() {
  // @@protoc_insertion_point(field_add_mutable:xla.ProgramShapeProto.parameter_names)
  return parameter_names_.Add();
}
inline void ProgramShapeProto::add_parameter_names(const std::string& value) {
  parameter_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:xla.ProgramShapeProto.parameter_names)
}
inline void ProgramShapeProto::add_parameter_names(std::string&& value) {
  parameter_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:xla.ProgramShapeProto.parameter_names)
}
inline void ProgramShapeProto::add_parameter_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  parameter_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:xla.ProgramShapeProto.parameter_names)
}
inline void ProgramShapeProto::add_parameter_names(const char* value, size_t size) {
  parameter_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:xla.ProgramShapeProto.parameter_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ProgramShapeProto::parameter_names() const {
  // @@protoc_insertion_point(field_list:xla.ProgramShapeProto.parameter_names)
  return parameter_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ProgramShapeProto::mutable_parameter_names() {
  // @@protoc_insertion_point(field_mutable_list:xla.ProgramShapeProto.parameter_names)
  return &parameter_names_;
}

// -------------------------------------------------------------------

// ComputationStats

// double flop_count = 1;
inline void ComputationStats::clear_flop_count() {
  flop_count_ = 0;
}
inline double ComputationStats::flop_count() const {
  // @@protoc_insertion_point(field_get:xla.ComputationStats.flop_count)
  return flop_count_;
}
inline void ComputationStats::set_flop_count(double value) {
  
  flop_count_ = value;
  // @@protoc_insertion_point(field_set:xla.ComputationStats.flop_count)
}

// double transcendental_count = 2;
inline void ComputationStats::clear_transcendental_count() {
  transcendental_count_ = 0;
}
inline double ComputationStats::transcendental_count() const {
  // @@protoc_insertion_point(field_get:xla.ComputationStats.transcendental_count)
  return transcendental_count_;
}
inline void ComputationStats::set_transcendental_count(double value) {
  
  transcendental_count_ = value;
  // @@protoc_insertion_point(field_set:xla.ComputationStats.transcendental_count)
}

// -------------------------------------------------------------------

// OpMetadata

// string op_type = 1;
inline void OpMetadata::clear_op_type() {
  op_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpMetadata::op_type() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.op_type)
  return op_type_.Get();
}
inline void OpMetadata::set_op_type(const std::string& value) {
  
  op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.OpMetadata.op_type)
}
inline void OpMetadata::set_op_type(std::string&& value) {
  
  op_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.OpMetadata.op_type)
}
inline void OpMetadata::set_op_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.OpMetadata.op_type)
}
inline void OpMetadata::set_op_type(const char* value,
    size_t size) {
  
  op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.OpMetadata.op_type)
}
inline std::string* OpMetadata::mutable_op_type() {
  
  // @@protoc_insertion_point(field_mutable:xla.OpMetadata.op_type)
  return op_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpMetadata::release_op_type() {
  // @@protoc_insertion_point(field_release:xla.OpMetadata.op_type)
  
  return op_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpMetadata::set_allocated_op_type(std::string* op_type) {
  if (op_type != nullptr) {
    
  } else {
    
  }
  op_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.OpMetadata.op_type)
}
inline std::string* OpMetadata::unsafe_arena_release_op_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.OpMetadata.op_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return op_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpMetadata::unsafe_arena_set_allocated_op_type(
    std::string* op_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (op_type != nullptr) {
    
  } else {
    
  }
  op_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      op_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.OpMetadata.op_type)
}

// string op_name = 2;
inline void OpMetadata::clear_op_name() {
  op_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpMetadata::op_name() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.op_name)
  return op_name_.Get();
}
inline void OpMetadata::set_op_name(const std::string& value) {
  
  op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.OpMetadata.op_name)
}
inline void OpMetadata::set_op_name(std::string&& value) {
  
  op_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.OpMetadata.op_name)
}
inline void OpMetadata::set_op_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.OpMetadata.op_name)
}
inline void OpMetadata::set_op_name(const char* value,
    size_t size) {
  
  op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.OpMetadata.op_name)
}
inline std::string* OpMetadata::mutable_op_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.OpMetadata.op_name)
  return op_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpMetadata::release_op_name() {
  // @@protoc_insertion_point(field_release:xla.OpMetadata.op_name)
  
  return op_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpMetadata::set_allocated_op_name(std::string* op_name) {
  if (op_name != nullptr) {
    
  } else {
    
  }
  op_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.OpMetadata.op_name)
}
inline std::string* OpMetadata::unsafe_arena_release_op_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.OpMetadata.op_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return op_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpMetadata::unsafe_arena_set_allocated_op_name(
    std::string* op_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (op_name != nullptr) {
    
  } else {
    
  }
  op_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      op_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.OpMetadata.op_name)
}

// string source_file = 3;
inline void OpMetadata::clear_source_file() {
  source_file_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpMetadata::source_file() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.source_file)
  return source_file_.Get();
}
inline void OpMetadata::set_source_file(const std::string& value) {
  
  source_file_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.OpMetadata.source_file)
}
inline void OpMetadata::set_source_file(std::string&& value) {
  
  source_file_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.OpMetadata.source_file)
}
inline void OpMetadata::set_source_file(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  source_file_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.OpMetadata.source_file)
}
inline void OpMetadata::set_source_file(const char* value,
    size_t size) {
  
  source_file_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.OpMetadata.source_file)
}
inline std::string* OpMetadata::mutable_source_file() {
  
  // @@protoc_insertion_point(field_mutable:xla.OpMetadata.source_file)
  return source_file_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpMetadata::release_source_file() {
  // @@protoc_insertion_point(field_release:xla.OpMetadata.source_file)
  
  return source_file_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpMetadata::set_allocated_source_file(std::string* source_file) {
  if (source_file != nullptr) {
    
  } else {
    
  }
  source_file_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), source_file,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.OpMetadata.source_file)
}
inline std::string* OpMetadata::unsafe_arena_release_source_file() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.OpMetadata.source_file)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return source_file_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpMetadata::unsafe_arena_set_allocated_source_file(
    std::string* source_file) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (source_file != nullptr) {
    
  } else {
    
  }
  source_file_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      source_file, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.OpMetadata.source_file)
}

// int32 source_line = 4;
inline void OpMetadata::clear_source_line() {
  source_line_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 OpMetadata::source_line() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.source_line)
  return source_line_;
}
inline void OpMetadata::set_source_line(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  source_line_ = value;
  // @@protoc_insertion_point(field_set:xla.OpMetadata.source_line)
}

// repeated .xla.ProfileType profile_type = 5;
inline int OpMetadata::profile_type_size() const {
  return profile_type_.size();
}
inline void OpMetadata::clear_profile_type() {
  profile_type_.Clear();
}
inline ::xla::ProfileType OpMetadata::profile_type(int index) const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.profile_type)
  return static_cast< ::xla::ProfileType >(profile_type_.Get(index));
}
inline void OpMetadata::set_profile_type(int index, ::xla::ProfileType value) {
  profile_type_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.OpMetadata.profile_type)
}
inline void OpMetadata::add_profile_type(::xla::ProfileType value) {
  profile_type_.Add(value);
  // @@protoc_insertion_point(field_add:xla.OpMetadata.profile_type)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
OpMetadata::profile_type() const {
  // @@protoc_insertion_point(field_list:xla.OpMetadata.profile_type)
  return profile_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
OpMetadata::mutable_profile_type() {
  // @@protoc_insertion_point(field_mutable_list:xla.OpMetadata.profile_type)
  return &profile_type_;
}

// int64 creation_pass_id = 6;
inline void OpMetadata::clear_creation_pass_id() {
  creation_pass_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpMetadata::creation_pass_id() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.creation_pass_id)
  return creation_pass_id_;
}
inline void OpMetadata::set_creation_pass_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  creation_pass_id_ = value;
  // @@protoc_insertion_point(field_set:xla.OpMetadata.creation_pass_id)
}

// int64 logical_creation_pass_id = 7;
inline void OpMetadata::clear_logical_creation_pass_id() {
  logical_creation_pass_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpMetadata::logical_creation_pass_id() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.logical_creation_pass_id)
  return logical_creation_pass_id_;
}
inline void OpMetadata::set_logical_creation_pass_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  logical_creation_pass_id_ = value;
  // @@protoc_insertion_point(field_set:xla.OpMetadata.logical_creation_pass_id)
}

// int64 size_of_generated_code_in_bytes = 8;
inline void OpMetadata::clear_size_of_generated_code_in_bytes() {
  size_of_generated_code_in_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpMetadata::size_of_generated_code_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.size_of_generated_code_in_bytes)
  return size_of_generated_code_in_bytes_;
}
inline void OpMetadata::set_size_of_generated_code_in_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  size_of_generated_code_in_bytes_ = value;
  // @@protoc_insertion_point(field_set:xla.OpMetadata.size_of_generated_code_in_bytes)
}

// int64 size_of_memory_working_set_in_bytes = 9;
inline void OpMetadata::clear_size_of_memory_working_set_in_bytes() {
  size_of_memory_working_set_in_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpMetadata::size_of_memory_working_set_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.OpMetadata.size_of_memory_working_set_in_bytes)
  return size_of_memory_working_set_in_bytes_;
}
inline void OpMetadata::set_size_of_memory_working_set_in_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  size_of_memory_working_set_in_bytes_ = value;
  // @@protoc_insertion_point(field_set:xla.OpMetadata.size_of_memory_working_set_in_bytes)
}

// -------------------------------------------------------------------

// ExecutionProfile

// bool compilation_cache_hit = 1;
inline void ExecutionProfile::clear_compilation_cache_hit() {
  compilation_cache_hit_ = false;
}
inline bool ExecutionProfile::compilation_cache_hit() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.compilation_cache_hit)
  return compilation_cache_hit_;
}
inline void ExecutionProfile::set_compilation_cache_hit(bool value) {
  
  compilation_cache_hit_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.compilation_cache_hit)
}

// int64 compile_time_ms = 2;
inline void ExecutionProfile::clear_compile_time_ms() {
  compile_time_ms_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecutionProfile::compile_time_ms() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.compile_time_ms)
  return compile_time_ms_;
}
inline void ExecutionProfile::set_compile_time_ms(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  compile_time_ms_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.compile_time_ms)
}

// int64 compute_cycle_count = 3;
inline void ExecutionProfile::clear_compute_cycle_count() {
  compute_cycle_count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecutionProfile::compute_cycle_count() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.compute_cycle_count)
  return compute_cycle_count_;
}
inline void ExecutionProfile::set_compute_cycle_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  compute_cycle_count_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.compute_cycle_count)
}

// int64 compute_time_ns = 4;
inline void ExecutionProfile::clear_compute_time_ns() {
  compute_time_ns_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecutionProfile::compute_time_ns() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.compute_time_ns)
  return compute_time_ns_;
}
inline void ExecutionProfile::set_compute_time_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  compute_time_ns_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.compute_time_ns)
}

// int64 compute_and_transfer_time_ns = 5;
inline void ExecutionProfile::clear_compute_and_transfer_time_ns() {
  compute_and_transfer_time_ns_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecutionProfile::compute_and_transfer_time_ns() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.compute_and_transfer_time_ns)
  return compute_and_transfer_time_ns_;
}
inline void ExecutionProfile::set_compute_and_transfer_time_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  compute_and_transfer_time_ns_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.compute_and_transfer_time_ns)
}

// int64 executable_size_in_bytes = 6;
inline void ExecutionProfile::clear_executable_size_in_bytes() {
  executable_size_in_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecutionProfile::executable_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.executable_size_in_bytes)
  return executable_size_in_bytes_;
}
inline void ExecutionProfile::set_executable_size_in_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  executable_size_in_bytes_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.executable_size_in_bytes)
}

// bool profile_cache_hit = 7;
inline void ExecutionProfile::clear_profile_cache_hit() {
  profile_cache_hit_ = false;
}
inline bool ExecutionProfile::profile_cache_hit() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionProfile.profile_cache_hit)
  return profile_cache_hit_;
}
inline void ExecutionProfile::set_profile_cache_hit(bool value) {
  
  profile_cache_hit_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionProfile.profile_cache_hit)
}

// -------------------------------------------------------------------

// ExecutionHandle

// int64 handle = 1;
inline void ExecutionHandle::clear_handle() {
  handle_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecutionHandle::handle() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionHandle.handle)
  return handle_;
}
inline void ExecutionHandle::set_handle(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionHandle.handle)
}

// -------------------------------------------------------------------

// GlobalDataHandle

// int64 handle = 1;
inline void GlobalDataHandle::clear_handle() {
  handle_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GlobalDataHandle::handle() const {
  // @@protoc_insertion_point(field_get:xla.GlobalDataHandle.handle)
  return handle_;
}
inline void GlobalDataHandle::set_handle(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:xla.GlobalDataHandle.handle)
}

// -------------------------------------------------------------------

// DeviceHandle

// int64 handle = 1;
inline void DeviceHandle::clear_handle() {
  handle_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceHandle::handle() const {
  // @@protoc_insertion_point(field_get:xla.DeviceHandle.handle)
  return handle_;
}
inline void DeviceHandle::set_handle(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:xla.DeviceHandle.handle)
}

// int64 device_count = 2;
inline void DeviceHandle::clear_device_count() {
  device_count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DeviceHandle::device_count() const {
  // @@protoc_insertion_point(field_get:xla.DeviceHandle.device_count)
  return device_count_;
}
inline void DeviceHandle::set_device_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  device_count_ = value;
  // @@protoc_insertion_point(field_set:xla.DeviceHandle.device_count)
}

// -------------------------------------------------------------------

// ChannelHandle

// int64 handle = 1;
inline void ChannelHandle::clear_handle() {
  handle_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ChannelHandle::handle() const {
  // @@protoc_insertion_point(field_get:xla.ChannelHandle.handle)
  return handle_;
}
inline void ChannelHandle::set_handle(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:xla.ChannelHandle.handle)
}

// .xla.ChannelHandle.ChannelType type = 2;
inline void ChannelHandle::clear_type() {
  type_ = 0;
}
inline ::xla::ChannelHandle_ChannelType ChannelHandle::type() const {
  // @@protoc_insertion_point(field_get:xla.ChannelHandle.type)
  return static_cast< ::xla::ChannelHandle_ChannelType >(type_);
}
inline void ChannelHandle::set_type(::xla::ChannelHandle_ChannelType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:xla.ChannelHandle.type)
}

// -------------------------------------------------------------------

// DeviceAssignmentProto_ComputationDevice

// repeated int32 replica_device_ids = 1;
inline int DeviceAssignmentProto_ComputationDevice::replica_device_ids_size() const {
  return replica_device_ids_.size();
}
inline void DeviceAssignmentProto_ComputationDevice::clear_replica_device_ids() {
  replica_device_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DeviceAssignmentProto_ComputationDevice::replica_device_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids)
  return replica_device_ids_.Get(index);
}
inline void DeviceAssignmentProto_ComputationDevice::set_replica_device_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  replica_device_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids)
}
inline void DeviceAssignmentProto_ComputationDevice::add_replica_device_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  replica_device_ids_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
DeviceAssignmentProto_ComputationDevice::replica_device_ids() const {
  // @@protoc_insertion_point(field_list:xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids)
  return replica_device_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
DeviceAssignmentProto_ComputationDevice::mutable_replica_device_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids)
  return &replica_device_ids_;
}

// -------------------------------------------------------------------

// DeviceAssignmentProto

// int32 replica_count = 1;
inline void DeviceAssignmentProto::clear_replica_count() {
  replica_count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DeviceAssignmentProto::replica_count() const {
  // @@protoc_insertion_point(field_get:xla.DeviceAssignmentProto.replica_count)
  return replica_count_;
}
inline void DeviceAssignmentProto::set_replica_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  replica_count_ = value;
  // @@protoc_insertion_point(field_set:xla.DeviceAssignmentProto.replica_count)
}

// int32 computation_count = 2;
inline void DeviceAssignmentProto::clear_computation_count() {
  computation_count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DeviceAssignmentProto::computation_count() const {
  // @@protoc_insertion_point(field_get:xla.DeviceAssignmentProto.computation_count)
  return computation_count_;
}
inline void DeviceAssignmentProto::set_computation_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  computation_count_ = value;
  // @@protoc_insertion_point(field_set:xla.DeviceAssignmentProto.computation_count)
}

// repeated .xla.DeviceAssignmentProto.ComputationDevice computation_devices = 3;
inline int DeviceAssignmentProto::computation_devices_size() const {
  return computation_devices_.size();
}
inline void DeviceAssignmentProto::clear_computation_devices() {
  computation_devices_.Clear();
}
inline ::xla::DeviceAssignmentProto_ComputationDevice* DeviceAssignmentProto::mutable_computation_devices(int index) {
  // @@protoc_insertion_point(field_mutable:xla.DeviceAssignmentProto.computation_devices)
  return computation_devices_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceAssignmentProto_ComputationDevice >*
DeviceAssignmentProto::mutable_computation_devices() {
  // @@protoc_insertion_point(field_mutable_list:xla.DeviceAssignmentProto.computation_devices)
  return &computation_devices_;
}
inline const ::xla::DeviceAssignmentProto_ComputationDevice& DeviceAssignmentProto::computation_devices(int index) const {
  // @@protoc_insertion_point(field_get:xla.DeviceAssignmentProto.computation_devices)
  return computation_devices_.Get(index);
}
inline ::xla::DeviceAssignmentProto_ComputationDevice* DeviceAssignmentProto::add_computation_devices() {
  // @@protoc_insertion_point(field_add:xla.DeviceAssignmentProto.computation_devices)
  return computation_devices_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceAssignmentProto_ComputationDevice >&
DeviceAssignmentProto::computation_devices() const {
  // @@protoc_insertion_point(field_list:xla.DeviceAssignmentProto.computation_devices)
  return computation_devices_;
}

// -------------------------------------------------------------------

// LiteralProto

// .xla.ShapeProto shape = 1;
inline bool LiteralProto::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline void LiteralProto::clear_shape() {
  if (GetArenaNoVirtual() == nullptr && shape_ != nullptr) {
    delete shape_;
  }
  shape_ = nullptr;
}
inline const ::xla::ShapeProto& LiteralProto::shape() const {
  const ::xla::ShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:xla.LiteralProto.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* LiteralProto::release_shape() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.shape)
  
  ::xla::ShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* LiteralProto::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.shape)
  
  ::xla::ShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* LiteralProto::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.shape)
  return shape_;
}
inline void LiteralProto::set_allocated_shape(::xla::ShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete shape_;
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(shape);
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.shape)
}

// repeated bool preds = 2;
inline int LiteralProto::preds_size() const {
  return preds_.size();
}
inline void LiteralProto::clear_preds() {
  preds_.Clear();
}
inline bool LiteralProto::preds(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.preds)
  return preds_.Get(index);
}
inline void LiteralProto::set_preds(int index, bool value) {
  preds_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.preds)
}
inline void LiteralProto::add_preds(bool value) {
  preds_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.preds)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
LiteralProto::preds() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.preds)
  return preds_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
LiteralProto::mutable_preds() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.preds)
  return &preds_;
}

// bytes s8s = 15;
inline void LiteralProto::clear_s8s() {
  s8s_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& LiteralProto::s8s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.s8s)
  return s8s_.Get();
}
inline void LiteralProto::set_s8s(const std::string& value) {
  
  s8s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.s8s)
}
inline void LiteralProto::set_s8s(std::string&& value) {
  
  s8s_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.s8s)
}
inline void LiteralProto::set_s8s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  s8s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.s8s)
}
inline void LiteralProto::set_s8s(const void* value,
    size_t size) {
  
  s8s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.s8s)
}
inline std::string* LiteralProto::mutable_s8s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.s8s)
  return s8s_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* LiteralProto::release_s8s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.s8s)
  
  return s8s_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_s8s(std::string* s8s) {
  if (s8s != nullptr) {
    
  } else {
    
  }
  s8s_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), s8s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.s8s)
}
inline std::string* LiteralProto::unsafe_arena_release_s8s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.s8s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return s8s_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_s8s(
    std::string* s8s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (s8s != nullptr) {
    
  } else {
    
  }
  s8s_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      s8s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.s8s)
}

// bytes u8s = 3;
inline void LiteralProto::clear_u8s() {
  u8s_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& LiteralProto::u8s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.u8s)
  return u8s_.Get();
}
inline void LiteralProto::set_u8s(const std::string& value) {
  
  u8s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.u8s)
}
inline void LiteralProto::set_u8s(std::string&& value) {
  
  u8s_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.u8s)
}
inline void LiteralProto::set_u8s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  u8s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.u8s)
}
inline void LiteralProto::set_u8s(const void* value,
    size_t size) {
  
  u8s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.u8s)
}
inline std::string* LiteralProto::mutable_u8s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.u8s)
  return u8s_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* LiteralProto::release_u8s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.u8s)
  
  return u8s_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_u8s(std::string* u8s) {
  if (u8s != nullptr) {
    
  } else {
    
  }
  u8s_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), u8s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.u8s)
}
inline std::string* LiteralProto::unsafe_arena_release_u8s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.u8s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return u8s_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_u8s(
    std::string* u8s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (u8s != nullptr) {
    
  } else {
    
  }
  u8s_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      u8s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.u8s)
}

// repeated int32 s32s = 4;
inline int LiteralProto::s32s_size() const {
  return s32s_.size();
}
inline void LiteralProto::clear_s32s() {
  s32s_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 LiteralProto::s32s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.s32s)
  return s32s_.Get(index);
}
inline void LiteralProto::set_s32s(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  s32s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.s32s)
}
inline void LiteralProto::add_s32s(::PROTOBUF_NAMESPACE_ID::int32 value) {
  s32s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.s32s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
LiteralProto::s32s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.s32s)
  return s32s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
LiteralProto::mutable_s32s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.s32s)
  return &s32s_;
}

// repeated int64 s64s = 5;
inline int LiteralProto::s64s_size() const {
  return s64s_.size();
}
inline void LiteralProto::clear_s64s() {
  s64s_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LiteralProto::s64s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.s64s)
  return s64s_.Get(index);
}
inline void LiteralProto::set_s64s(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  s64s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.s64s)
}
inline void LiteralProto::add_s64s(::PROTOBUF_NAMESPACE_ID::int64 value) {
  s64s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.s64s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
LiteralProto::s64s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.s64s)
  return s64s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
LiteralProto::mutable_s64s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.s64s)
  return &s64s_;
}

// repeated uint32 u32s = 6;
inline int LiteralProto::u32s_size() const {
  return u32s_.size();
}
inline void LiteralProto::clear_u32s() {
  u32s_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 LiteralProto::u32s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.u32s)
  return u32s_.Get(index);
}
inline void LiteralProto::set_u32s(int index, ::PROTOBUF_NAMESPACE_ID::uint32 value) {
  u32s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.u32s)
}
inline void LiteralProto::add_u32s(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  u32s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.u32s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
LiteralProto::u32s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.u32s)
  return u32s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
LiteralProto::mutable_u32s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.u32s)
  return &u32s_;
}

// repeated uint64 u64s = 7;
inline int LiteralProto::u64s_size() const {
  return u64s_.size();
}
inline void LiteralProto::clear_u64s() {
  u64s_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 LiteralProto::u64s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.u64s)
  return u64s_.Get(index);
}
inline void LiteralProto::set_u64s(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value) {
  u64s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.u64s)
}
inline void LiteralProto::add_u64s(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  u64s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.u64s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
LiteralProto::u64s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.u64s)
  return u64s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
LiteralProto::mutable_u64s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.u64s)
  return &u64s_;
}

// repeated float f32s = 8;
inline int LiteralProto::f32s_size() const {
  return f32s_.size();
}
inline void LiteralProto::clear_f32s() {
  f32s_.Clear();
}
inline float LiteralProto::f32s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.f32s)
  return f32s_.Get(index);
}
inline void LiteralProto::set_f32s(int index, float value) {
  f32s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.f32s)
}
inline void LiteralProto::add_f32s(float value) {
  f32s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.f32s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
LiteralProto::f32s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.f32s)
  return f32s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
LiteralProto::mutable_f32s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.f32s)
  return &f32s_;
}

// repeated double f64s = 9;
inline int LiteralProto::f64s_size() const {
  return f64s_.size();
}
inline void LiteralProto::clear_f64s() {
  f64s_.Clear();
}
inline double LiteralProto::f64s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.f64s)
  return f64s_.Get(index);
}
inline void LiteralProto::set_f64s(int index, double value) {
  f64s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.f64s)
}
inline void LiteralProto::add_f64s(double value) {
  f64s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.f64s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
LiteralProto::f64s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.f64s)
  return f64s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
LiteralProto::mutable_f64s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.f64s)
  return &f64s_;
}

// repeated float c64s = 12;
inline int LiteralProto::c64s_size() const {
  return c64s_.size();
}
inline void LiteralProto::clear_c64s() {
  c64s_.Clear();
}
inline float LiteralProto::c64s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.c64s)
  return c64s_.Get(index);
}
inline void LiteralProto::set_c64s(int index, float value) {
  c64s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.c64s)
}
inline void LiteralProto::add_c64s(float value) {
  c64s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.c64s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
LiteralProto::c64s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.c64s)
  return c64s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
LiteralProto::mutable_c64s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.c64s)
  return &c64s_;
}

// repeated double c128s = 18;
inline int LiteralProto::c128s_size() const {
  return c128s_.size();
}
inline void LiteralProto::clear_c128s() {
  c128s_.Clear();
}
inline double LiteralProto::c128s(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.c128s)
  return c128s_.Get(index);
}
inline void LiteralProto::set_c128s(int index, double value) {
  c128s_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.c128s)
}
inline void LiteralProto::add_c128s(double value) {
  c128s_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.c128s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
LiteralProto::c128s() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.c128s)
  return c128s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
LiteralProto::mutable_c128s() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.c128s)
  return &c128s_;
}

// repeated .xla.LiteralProto tuple_literals = 10;
inline int LiteralProto::tuple_literals_size() const {
  return tuple_literals_.size();
}
inline void LiteralProto::clear_tuple_literals() {
  tuple_literals_.Clear();
}
inline ::xla::LiteralProto* LiteralProto::mutable_tuple_literals(int index) {
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.tuple_literals)
  return tuple_literals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LiteralProto >*
LiteralProto::mutable_tuple_literals() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.tuple_literals)
  return &tuple_literals_;
}
inline const ::xla::LiteralProto& LiteralProto::tuple_literals(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.tuple_literals)
  return tuple_literals_.Get(index);
}
inline ::xla::LiteralProto* LiteralProto::add_tuple_literals() {
  // @@protoc_insertion_point(field_add:xla.LiteralProto.tuple_literals)
  return tuple_literals_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LiteralProto >&
LiteralProto::tuple_literals() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.tuple_literals)
  return tuple_literals_;
}

// bytes f16s = 11;
inline void LiteralProto::clear_f16s() {
  f16s_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& LiteralProto::f16s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.f16s)
  return f16s_.Get();
}
inline void LiteralProto::set_f16s(const std::string& value) {
  
  f16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.f16s)
}
inline void LiteralProto::set_f16s(std::string&& value) {
  
  f16s_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.f16s)
}
inline void LiteralProto::set_f16s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  f16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.f16s)
}
inline void LiteralProto::set_f16s(const void* value,
    size_t size) {
  
  f16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.f16s)
}
inline std::string* LiteralProto::mutable_f16s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.f16s)
  return f16s_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* LiteralProto::release_f16s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.f16s)
  
  return f16s_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_f16s(std::string* f16s) {
  if (f16s != nullptr) {
    
  } else {
    
  }
  f16s_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), f16s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.f16s)
}
inline std::string* LiteralProto::unsafe_arena_release_f16s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.f16s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return f16s_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_f16s(
    std::string* f16s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (f16s != nullptr) {
    
  } else {
    
  }
  f16s_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      f16s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.f16s)
}

// bytes bf16s = 13;
inline void LiteralProto::clear_bf16s() {
  bf16s_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& LiteralProto::bf16s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.bf16s)
  return bf16s_.Get();
}
inline void LiteralProto::set_bf16s(const std::string& value) {
  
  bf16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.bf16s)
}
inline void LiteralProto::set_bf16s(std::string&& value) {
  
  bf16s_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.bf16s)
}
inline void LiteralProto::set_bf16s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  bf16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.bf16s)
}
inline void LiteralProto::set_bf16s(const void* value,
    size_t size) {
  
  bf16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.bf16s)
}
inline std::string* LiteralProto::mutable_bf16s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.bf16s)
  return bf16s_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* LiteralProto::release_bf16s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.bf16s)
  
  return bf16s_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_bf16s(std::string* bf16s) {
  if (bf16s != nullptr) {
    
  } else {
    
  }
  bf16s_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bf16s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.bf16s)
}
inline std::string* LiteralProto::unsafe_arena_release_bf16s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.bf16s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return bf16s_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_bf16s(
    std::string* bf16s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (bf16s != nullptr) {
    
  } else {
    
  }
  bf16s_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      bf16s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.bf16s)
}

// bytes u16s = 16;
inline void LiteralProto::clear_u16s() {
  u16s_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& LiteralProto::u16s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.u16s)
  return u16s_.Get();
}
inline void LiteralProto::set_u16s(const std::string& value) {
  
  u16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.u16s)
}
inline void LiteralProto::set_u16s(std::string&& value) {
  
  u16s_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.u16s)
}
inline void LiteralProto::set_u16s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  u16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.u16s)
}
inline void LiteralProto::set_u16s(const void* value,
    size_t size) {
  
  u16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.u16s)
}
inline std::string* LiteralProto::mutable_u16s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.u16s)
  return u16s_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* LiteralProto::release_u16s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.u16s)
  
  return u16s_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_u16s(std::string* u16s) {
  if (u16s != nullptr) {
    
  } else {
    
  }
  u16s_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), u16s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.u16s)
}
inline std::string* LiteralProto::unsafe_arena_release_u16s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.u16s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return u16s_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_u16s(
    std::string* u16s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (u16s != nullptr) {
    
  } else {
    
  }
  u16s_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      u16s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.u16s)
}

// bytes s16s = 17;
inline void LiteralProto::clear_s16s() {
  s16s_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& LiteralProto::s16s() const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.s16s)
  return s16s_.Get();
}
inline void LiteralProto::set_s16s(const std::string& value) {
  
  s16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LiteralProto.s16s)
}
inline void LiteralProto::set_s16s(std::string&& value) {
  
  s16s_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LiteralProto.s16s)
}
inline void LiteralProto::set_s16s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  s16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LiteralProto.s16s)
}
inline void LiteralProto::set_s16s(const void* value,
    size_t size) {
  
  s16s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LiteralProto.s16s)
}
inline std::string* LiteralProto::mutable_s16s() {
  
  // @@protoc_insertion_point(field_mutable:xla.LiteralProto.s16s)
  return s16s_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* LiteralProto::release_s16s() {
  // @@protoc_insertion_point(field_release:xla.LiteralProto.s16s)
  
  return s16s_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LiteralProto::set_allocated_s16s(std::string* s16s) {
  if (s16s != nullptr) {
    
  } else {
    
  }
  s16s_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), s16s,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LiteralProto.s16s)
}
inline std::string* LiteralProto::unsafe_arena_release_s16s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LiteralProto.s16s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return s16s_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LiteralProto::unsafe_arena_set_allocated_s16s(
    std::string* s16s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (s16s != nullptr) {
    
  } else {
    
  }
  s16s_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      s16s, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LiteralProto.s16s)
}

// repeated int64 sparse_indices = 14;
inline int LiteralProto::sparse_indices_size() const {
  return sparse_indices_.size();
}
inline void LiteralProto::clear_sparse_indices() {
  sparse_indices_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LiteralProto::sparse_indices(int index) const {
  // @@protoc_insertion_point(field_get:xla.LiteralProto.sparse_indices)
  return sparse_indices_.Get(index);
}
inline void LiteralProto::set_sparse_indices(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  sparse_indices_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LiteralProto.sparse_indices)
}
inline void LiteralProto::add_sparse_indices(::PROTOBUF_NAMESPACE_ID::int64 value) {
  sparse_indices_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LiteralProto.sparse_indices)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
LiteralProto::sparse_indices() const {
  // @@protoc_insertion_point(field_list:xla.LiteralProto.sparse_indices)
  return sparse_indices_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
LiteralProto::mutable_sparse_indices() {
  // @@protoc_insertion_point(field_mutable_list:xla.LiteralProto.sparse_indices)
  return &sparse_indices_;
}

// -------------------------------------------------------------------

// WindowDimension

// int64 size = 1;
inline void WindowDimension::clear_size() {
  size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 WindowDimension::size() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.size)
  return size_;
}
inline void WindowDimension::set_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.size)
}

// int64 stride = 2;
inline void WindowDimension::clear_stride() {
  stride_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 WindowDimension::stride() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.stride)
  return stride_;
}
inline void WindowDimension::set_stride(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  stride_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.stride)
}

// int64 padding_low = 3;
inline void WindowDimension::clear_padding_low() {
  padding_low_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 WindowDimension::padding_low() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.padding_low)
  return padding_low_;
}
inline void WindowDimension::set_padding_low(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  padding_low_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.padding_low)
}

// int64 padding_high = 4;
inline void WindowDimension::clear_padding_high() {
  padding_high_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 WindowDimension::padding_high() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.padding_high)
  return padding_high_;
}
inline void WindowDimension::set_padding_high(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  padding_high_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.padding_high)
}

// int64 window_dilation = 5;
inline void WindowDimension::clear_window_dilation() {
  window_dilation_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 WindowDimension::window_dilation() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.window_dilation)
  return window_dilation_;
}
inline void WindowDimension::set_window_dilation(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  window_dilation_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.window_dilation)
}

// int64 base_dilation = 6;
inline void WindowDimension::clear_base_dilation() {
  base_dilation_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 WindowDimension::base_dilation() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.base_dilation)
  return base_dilation_;
}
inline void WindowDimension::set_base_dilation(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  base_dilation_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.base_dilation)
}

// bool window_reversal = 7;
inline void WindowDimension::clear_window_reversal() {
  window_reversal_ = false;
}
inline bool WindowDimension::window_reversal() const {
  // @@protoc_insertion_point(field_get:xla.WindowDimension.window_reversal)
  return window_reversal_;
}
inline void WindowDimension::set_window_reversal(bool value) {
  
  window_reversal_ = value;
  // @@protoc_insertion_point(field_set:xla.WindowDimension.window_reversal)
}

// -------------------------------------------------------------------

// Window

// repeated .xla.WindowDimension dimensions = 1;
inline int Window::dimensions_size() const {
  return dimensions_.size();
}
inline void Window::clear_dimensions() {
  dimensions_.Clear();
}
inline ::xla::WindowDimension* Window::mutable_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:xla.Window.dimensions)
  return dimensions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::WindowDimension >*
Window::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.Window.dimensions)
  return &dimensions_;
}
inline const ::xla::WindowDimension& Window::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.Window.dimensions)
  return dimensions_.Get(index);
}
inline ::xla::WindowDimension* Window::add_dimensions() {
  // @@protoc_insertion_point(field_add:xla.Window.dimensions)
  return dimensions_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::WindowDimension >&
Window::dimensions() const {
  // @@protoc_insertion_point(field_list:xla.Window.dimensions)
  return dimensions_;
}

// -------------------------------------------------------------------

// GatherDimensionNumbers

// repeated int64 offset_dims = 1;
inline int GatherDimensionNumbers::offset_dims_size() const {
  return offset_dims_.size();
}
inline void GatherDimensionNumbers::clear_offset_dims() {
  offset_dims_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GatherDimensionNumbers::offset_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.GatherDimensionNumbers.offset_dims)
  return offset_dims_.Get(index);
}
inline void GatherDimensionNumbers::set_offset_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  offset_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.GatherDimensionNumbers.offset_dims)
}
inline void GatherDimensionNumbers::add_offset_dims(::PROTOBUF_NAMESPACE_ID::int64 value) {
  offset_dims_.Add(value);
  // @@protoc_insertion_point(field_add:xla.GatherDimensionNumbers.offset_dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
GatherDimensionNumbers::offset_dims() const {
  // @@protoc_insertion_point(field_list:xla.GatherDimensionNumbers.offset_dims)
  return offset_dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
GatherDimensionNumbers::mutable_offset_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.GatherDimensionNumbers.offset_dims)
  return &offset_dims_;
}

// repeated int64 collapsed_slice_dims = 2;
inline int GatherDimensionNumbers::collapsed_slice_dims_size() const {
  return collapsed_slice_dims_.size();
}
inline void GatherDimensionNumbers::clear_collapsed_slice_dims() {
  collapsed_slice_dims_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GatherDimensionNumbers::collapsed_slice_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.GatherDimensionNumbers.collapsed_slice_dims)
  return collapsed_slice_dims_.Get(index);
}
inline void GatherDimensionNumbers::set_collapsed_slice_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  collapsed_slice_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.GatherDimensionNumbers.collapsed_slice_dims)
}
inline void GatherDimensionNumbers::add_collapsed_slice_dims(::PROTOBUF_NAMESPACE_ID::int64 value) {
  collapsed_slice_dims_.Add(value);
  // @@protoc_insertion_point(field_add:xla.GatherDimensionNumbers.collapsed_slice_dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
GatherDimensionNumbers::collapsed_slice_dims() const {
  // @@protoc_insertion_point(field_list:xla.GatherDimensionNumbers.collapsed_slice_dims)
  return collapsed_slice_dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
GatherDimensionNumbers::mutable_collapsed_slice_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.GatherDimensionNumbers.collapsed_slice_dims)
  return &collapsed_slice_dims_;
}

// repeated int64 start_index_map = 3;
inline int GatherDimensionNumbers::start_index_map_size() const {
  return start_index_map_.size();
}
inline void GatherDimensionNumbers::clear_start_index_map() {
  start_index_map_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GatherDimensionNumbers::start_index_map(int index) const {
  // @@protoc_insertion_point(field_get:xla.GatherDimensionNumbers.start_index_map)
  return start_index_map_.Get(index);
}
inline void GatherDimensionNumbers::set_start_index_map(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  start_index_map_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.GatherDimensionNumbers.start_index_map)
}
inline void GatherDimensionNumbers::add_start_index_map(::PROTOBUF_NAMESPACE_ID::int64 value) {
  start_index_map_.Add(value);
  // @@protoc_insertion_point(field_add:xla.GatherDimensionNumbers.start_index_map)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
GatherDimensionNumbers::start_index_map() const {
  // @@protoc_insertion_point(field_list:xla.GatherDimensionNumbers.start_index_map)
  return start_index_map_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
GatherDimensionNumbers::mutable_start_index_map() {
  // @@protoc_insertion_point(field_mutable_list:xla.GatherDimensionNumbers.start_index_map)
  return &start_index_map_;
}

// int64 index_vector_dim = 4;
inline void GatherDimensionNumbers::clear_index_vector_dim() {
  index_vector_dim_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GatherDimensionNumbers::index_vector_dim() const {
  // @@protoc_insertion_point(field_get:xla.GatherDimensionNumbers.index_vector_dim)
  return index_vector_dim_;
}
inline void GatherDimensionNumbers::set_index_vector_dim(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  index_vector_dim_ = value;
  // @@protoc_insertion_point(field_set:xla.GatherDimensionNumbers.index_vector_dim)
}

// -------------------------------------------------------------------

// ScatterDimensionNumbers

// repeated int64 update_window_dims = 1;
inline int ScatterDimensionNumbers::update_window_dims_size() const {
  return update_window_dims_.size();
}
inline void ScatterDimensionNumbers::clear_update_window_dims() {
  update_window_dims_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ScatterDimensionNumbers::update_window_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.ScatterDimensionNumbers.update_window_dims)
  return update_window_dims_.Get(index);
}
inline void ScatterDimensionNumbers::set_update_window_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  update_window_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ScatterDimensionNumbers.update_window_dims)
}
inline void ScatterDimensionNumbers::add_update_window_dims(::PROTOBUF_NAMESPACE_ID::int64 value) {
  update_window_dims_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ScatterDimensionNumbers.update_window_dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ScatterDimensionNumbers::update_window_dims() const {
  // @@protoc_insertion_point(field_list:xla.ScatterDimensionNumbers.update_window_dims)
  return update_window_dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ScatterDimensionNumbers::mutable_update_window_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.ScatterDimensionNumbers.update_window_dims)
  return &update_window_dims_;
}

// repeated int64 inserted_window_dims = 2;
inline int ScatterDimensionNumbers::inserted_window_dims_size() const {
  return inserted_window_dims_.size();
}
inline void ScatterDimensionNumbers::clear_inserted_window_dims() {
  inserted_window_dims_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ScatterDimensionNumbers::inserted_window_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.ScatterDimensionNumbers.inserted_window_dims)
  return inserted_window_dims_.Get(index);
}
inline void ScatterDimensionNumbers::set_inserted_window_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  inserted_window_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ScatterDimensionNumbers.inserted_window_dims)
}
inline void ScatterDimensionNumbers::add_inserted_window_dims(::PROTOBUF_NAMESPACE_ID::int64 value) {
  inserted_window_dims_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ScatterDimensionNumbers.inserted_window_dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ScatterDimensionNumbers::inserted_window_dims() const {
  // @@protoc_insertion_point(field_list:xla.ScatterDimensionNumbers.inserted_window_dims)
  return inserted_window_dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ScatterDimensionNumbers::mutable_inserted_window_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.ScatterDimensionNumbers.inserted_window_dims)
  return &inserted_window_dims_;
}

// repeated int64 scatter_dims_to_operand_dims = 3;
inline int ScatterDimensionNumbers::scatter_dims_to_operand_dims_size() const {
  return scatter_dims_to_operand_dims_.size();
}
inline void ScatterDimensionNumbers::clear_scatter_dims_to_operand_dims() {
  scatter_dims_to_operand_dims_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ScatterDimensionNumbers::scatter_dims_to_operand_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims)
  return scatter_dims_to_operand_dims_.Get(index);
}
inline void ScatterDimensionNumbers::set_scatter_dims_to_operand_dims(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  scatter_dims_to_operand_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims)
}
inline void ScatterDimensionNumbers::add_scatter_dims_to_operand_dims(::PROTOBUF_NAMESPACE_ID::int64 value) {
  scatter_dims_to_operand_dims_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ScatterDimensionNumbers::scatter_dims_to_operand_dims() const {
  // @@protoc_insertion_point(field_list:xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims)
  return scatter_dims_to_operand_dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ScatterDimensionNumbers::mutable_scatter_dims_to_operand_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims)
  return &scatter_dims_to_operand_dims_;
}

// int64 index_vector_dim = 4;
inline void ScatterDimensionNumbers::clear_index_vector_dim() {
  index_vector_dim_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ScatterDimensionNumbers::index_vector_dim() const {
  // @@protoc_insertion_point(field_get:xla.ScatterDimensionNumbers.index_vector_dim)
  return index_vector_dim_;
}
inline void ScatterDimensionNumbers::set_index_vector_dim(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  index_vector_dim_ = value;
  // @@protoc_insertion_point(field_set:xla.ScatterDimensionNumbers.index_vector_dim)
}

// -------------------------------------------------------------------

// ConvolutionDimensionNumbers

// int64 input_batch_dimension = 7;
inline void ConvolutionDimensionNumbers::clear_input_batch_dimension() {
  input_batch_dimension_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDimensionNumbers::input_batch_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.input_batch_dimension)
  return input_batch_dimension_;
}
inline void ConvolutionDimensionNumbers::set_input_batch_dimension(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  input_batch_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.input_batch_dimension)
}

// int64 input_feature_dimension = 8;
inline void ConvolutionDimensionNumbers::clear_input_feature_dimension() {
  input_feature_dimension_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDimensionNumbers::input_feature_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.input_feature_dimension)
  return input_feature_dimension_;
}
inline void ConvolutionDimensionNumbers::set_input_feature_dimension(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  input_feature_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.input_feature_dimension)
}

// repeated int64 input_spatial_dimensions = 11;
inline int ConvolutionDimensionNumbers::input_spatial_dimensions_size() const {
  return input_spatial_dimensions_.size();
}
inline void ConvolutionDimensionNumbers::clear_input_spatial_dimensions() {
  input_spatial_dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDimensionNumbers::input_spatial_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.input_spatial_dimensions)
  return input_spatial_dimensions_.Get(index);
}
inline void ConvolutionDimensionNumbers::set_input_spatial_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  input_spatial_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.input_spatial_dimensions)
}
inline void ConvolutionDimensionNumbers::add_input_spatial_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  input_spatial_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ConvolutionDimensionNumbers.input_spatial_dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ConvolutionDimensionNumbers::input_spatial_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.ConvolutionDimensionNumbers.input_spatial_dimensions)
  return input_spatial_dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ConvolutionDimensionNumbers::mutable_input_spatial_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.ConvolutionDimensionNumbers.input_spatial_dimensions)
  return &input_spatial_dimensions_;
}

// int64 kernel_input_feature_dimension = 3;
inline void ConvolutionDimensionNumbers::clear_kernel_input_feature_dimension() {
  kernel_input_feature_dimension_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDimensionNumbers::kernel_input_feature_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.kernel_input_feature_dimension)
  return kernel_input_feature_dimension_;
}
inline void ConvolutionDimensionNumbers::set_kernel_input_feature_dimension(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  kernel_input_feature_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.kernel_input_feature_dimension)
}

// int64 kernel_output_feature_dimension = 4;
inline void ConvolutionDimensionNumbers::clear_kernel_output_feature_dimension() {
  kernel_output_feature_dimension_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDimensionNumbers::kernel_output_feature_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.kernel_output_feature_dimension)
  return kernel_output_feature_dimension_;
}
inline void ConvolutionDimensionNumbers::set_kernel_output_feature_dimension(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  kernel_output_feature_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.kernel_output_feature_dimension)
}

// repeated int64 kernel_spatial_dimensions = 6;
inline int ConvolutionDimensionNumbers::kernel_spatial_dimensions_size() const {
  return kernel_spatial_dimensions_.size();
}
inline void ConvolutionDimensionNumbers::clear_kernel_spatial_dimensions() {
  kernel_spatial_dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDimensionNumbers::kernel_spatial_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions)
  return kernel_spatial_dimensions_.Get(index);
}
inline void ConvolutionDimensionNumbers::set_kernel_spatial_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  kernel_spatial_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions)
}
inline void ConvolutionDimensionNumbers::add_kernel_spatial_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  kernel_spatial_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ConvolutionDimensionNumbers::kernel_spatial_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions)
  return kernel_spatial_dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ConvolutionDimensionNumbers::mutable_kernel_spatial_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions)
  return &kernel_spatial_dimensions_;
}

// int64 output_batch_dimension = 9;
inline void ConvolutionDimensionNumbers::clear_output_batch_dimension() {
  output_batch_dimension_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDimensionNumbers::output_batch_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.output_batch_dimension)
  return output_batch_dimension_;
}
inline void ConvolutionDimensionNumbers::set_output_batch_dimension(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  output_batch_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.output_batch_dimension)
}

// int64 output_feature_dimension = 10;
inline void ConvolutionDimensionNumbers::clear_output_feature_dimension() {
  output_feature_dimension_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDimensionNumbers::output_feature_dimension() const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.output_feature_dimension)
  return output_feature_dimension_;
}
inline void ConvolutionDimensionNumbers::set_output_feature_dimension(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  output_feature_dimension_ = value;
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.output_feature_dimension)
}

// repeated int64 output_spatial_dimensions = 12;
inline int ConvolutionDimensionNumbers::output_spatial_dimensions_size() const {
  return output_spatial_dimensions_.size();
}
inline void ConvolutionDimensionNumbers::clear_output_spatial_dimensions() {
  output_spatial_dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDimensionNumbers::output_spatial_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.ConvolutionDimensionNumbers.output_spatial_dimensions)
  return output_spatial_dimensions_.Get(index);
}
inline void ConvolutionDimensionNumbers::set_output_spatial_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  output_spatial_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ConvolutionDimensionNumbers.output_spatial_dimensions)
}
inline void ConvolutionDimensionNumbers::add_output_spatial_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  output_spatial_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ConvolutionDimensionNumbers.output_spatial_dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ConvolutionDimensionNumbers::output_spatial_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.ConvolutionDimensionNumbers.output_spatial_dimensions)
  return output_spatial_dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ConvolutionDimensionNumbers::mutable_output_spatial_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.ConvolutionDimensionNumbers.output_spatial_dimensions)
  return &output_spatial_dimensions_;
}

// -------------------------------------------------------------------

// DotDimensionNumbers

// repeated int64 lhs_contracting_dimensions = 1;
inline int DotDimensionNumbers::lhs_contracting_dimensions_size() const {
  return lhs_contracting_dimensions_.size();
}
inline void DotDimensionNumbers::clear_lhs_contracting_dimensions() {
  lhs_contracting_dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DotDimensionNumbers::lhs_contracting_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.DotDimensionNumbers.lhs_contracting_dimensions)
  return lhs_contracting_dimensions_.Get(index);
}
inline void DotDimensionNumbers::set_lhs_contracting_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  lhs_contracting_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DotDimensionNumbers.lhs_contracting_dimensions)
}
inline void DotDimensionNumbers::add_lhs_contracting_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  lhs_contracting_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DotDimensionNumbers.lhs_contracting_dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
DotDimensionNumbers::lhs_contracting_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.DotDimensionNumbers.lhs_contracting_dimensions)
  return lhs_contracting_dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
DotDimensionNumbers::mutable_lhs_contracting_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.DotDimensionNumbers.lhs_contracting_dimensions)
  return &lhs_contracting_dimensions_;
}

// repeated int64 rhs_contracting_dimensions = 2;
inline int DotDimensionNumbers::rhs_contracting_dimensions_size() const {
  return rhs_contracting_dimensions_.size();
}
inline void DotDimensionNumbers::clear_rhs_contracting_dimensions() {
  rhs_contracting_dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DotDimensionNumbers::rhs_contracting_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.DotDimensionNumbers.rhs_contracting_dimensions)
  return rhs_contracting_dimensions_.Get(index);
}
inline void DotDimensionNumbers::set_rhs_contracting_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  rhs_contracting_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DotDimensionNumbers.rhs_contracting_dimensions)
}
inline void DotDimensionNumbers::add_rhs_contracting_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  rhs_contracting_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DotDimensionNumbers.rhs_contracting_dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
DotDimensionNumbers::rhs_contracting_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.DotDimensionNumbers.rhs_contracting_dimensions)
  return rhs_contracting_dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
DotDimensionNumbers::mutable_rhs_contracting_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.DotDimensionNumbers.rhs_contracting_dimensions)
  return &rhs_contracting_dimensions_;
}

// repeated int64 lhs_batch_dimensions = 3;
inline int DotDimensionNumbers::lhs_batch_dimensions_size() const {
  return lhs_batch_dimensions_.size();
}
inline void DotDimensionNumbers::clear_lhs_batch_dimensions() {
  lhs_batch_dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DotDimensionNumbers::lhs_batch_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.DotDimensionNumbers.lhs_batch_dimensions)
  return lhs_batch_dimensions_.Get(index);
}
inline void DotDimensionNumbers::set_lhs_batch_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  lhs_batch_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DotDimensionNumbers.lhs_batch_dimensions)
}
inline void DotDimensionNumbers::add_lhs_batch_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  lhs_batch_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DotDimensionNumbers.lhs_batch_dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
DotDimensionNumbers::lhs_batch_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.DotDimensionNumbers.lhs_batch_dimensions)
  return lhs_batch_dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
DotDimensionNumbers::mutable_lhs_batch_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.DotDimensionNumbers.lhs_batch_dimensions)
  return &lhs_batch_dimensions_;
}

// repeated int64 rhs_batch_dimensions = 4;
inline int DotDimensionNumbers::rhs_batch_dimensions_size() const {
  return rhs_batch_dimensions_.size();
}
inline void DotDimensionNumbers::clear_rhs_batch_dimensions() {
  rhs_batch_dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DotDimensionNumbers::rhs_batch_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.DotDimensionNumbers.rhs_batch_dimensions)
  return rhs_batch_dimensions_.Get(index);
}
inline void DotDimensionNumbers::set_rhs_batch_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  rhs_batch_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DotDimensionNumbers.rhs_batch_dimensions)
}
inline void DotDimensionNumbers::add_rhs_batch_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  rhs_batch_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DotDimensionNumbers.rhs_batch_dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
DotDimensionNumbers::rhs_batch_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.DotDimensionNumbers.rhs_batch_dimensions)
  return rhs_batch_dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
DotDimensionNumbers::mutable_rhs_batch_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.DotDimensionNumbers.rhs_batch_dimensions)
  return &rhs_batch_dimensions_;
}

// -------------------------------------------------------------------

// TriangularSolveOptions

// bool left_side = 1;
inline void TriangularSolveOptions::clear_left_side() {
  left_side_ = false;
}
inline bool TriangularSolveOptions::left_side() const {
  // @@protoc_insertion_point(field_get:xla.TriangularSolveOptions.left_side)
  return left_side_;
}
inline void TriangularSolveOptions::set_left_side(bool value) {
  
  left_side_ = value;
  // @@protoc_insertion_point(field_set:xla.TriangularSolveOptions.left_side)
}

// bool lower = 2;
inline void TriangularSolveOptions::clear_lower() {
  lower_ = false;
}
inline bool TriangularSolveOptions::lower() const {
  // @@protoc_insertion_point(field_get:xla.TriangularSolveOptions.lower)
  return lower_;
}
inline void TriangularSolveOptions::set_lower(bool value) {
  
  lower_ = value;
  // @@protoc_insertion_point(field_set:xla.TriangularSolveOptions.lower)
}

// bool unit_diagonal = 3;
inline void TriangularSolveOptions::clear_unit_diagonal() {
  unit_diagonal_ = false;
}
inline bool TriangularSolveOptions::unit_diagonal() const {
  // @@protoc_insertion_point(field_get:xla.TriangularSolveOptions.unit_diagonal)
  return unit_diagonal_;
}
inline void TriangularSolveOptions::set_unit_diagonal(bool value) {
  
  unit_diagonal_ = value;
  // @@protoc_insertion_point(field_set:xla.TriangularSolveOptions.unit_diagonal)
}

// .xla.TriangularSolveOptions.Transpose transpose_a = 4;
inline void TriangularSolveOptions::clear_transpose_a() {
  transpose_a_ = 0;
}
inline ::xla::TriangularSolveOptions_Transpose TriangularSolveOptions::transpose_a() const {
  // @@protoc_insertion_point(field_get:xla.TriangularSolveOptions.transpose_a)
  return static_cast< ::xla::TriangularSolveOptions_Transpose >(transpose_a_);
}
inline void TriangularSolveOptions::set_transpose_a(::xla::TriangularSolveOptions_Transpose value) {
  
  transpose_a_ = value;
  // @@protoc_insertion_point(field_set:xla.TriangularSolveOptions.transpose_a)
}

// -------------------------------------------------------------------

// CholeskyOptions

// bool lower = 1;
inline void CholeskyOptions::clear_lower() {
  lower_ = false;
}
inline bool CholeskyOptions::lower() const {
  // @@protoc_insertion_point(field_get:xla.CholeskyOptions.lower)
  return lower_;
}
inline void CholeskyOptions::set_lower(bool value) {
  
  lower_ = value;
  // @@protoc_insertion_point(field_set:xla.CholeskyOptions.lower)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FrontendAttributes

// map<string, string> map = 1;
inline int FrontendAttributes::map_size() const {
  return map_.size();
}
inline void FrontendAttributes::clear_map() {
  map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
FrontendAttributes::map() const {
  // @@protoc_insertion_point(field_map:xla.FrontendAttributes.map)
  return map_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
FrontendAttributes::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:xla.FrontendAttributes.map)
  return map_.MutableMap();
}

// -------------------------------------------------------------------

// OpSharding

// .xla.OpSharding.Type type = 1;
inline void OpSharding::clear_type() {
  type_ = 0;
}
inline ::xla::OpSharding_Type OpSharding::type() const {
  // @@protoc_insertion_point(field_get:xla.OpSharding.type)
  return static_cast< ::xla::OpSharding_Type >(type_);
}
inline void OpSharding::set_type(::xla::OpSharding_Type value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:xla.OpSharding.type)
}

// .xla.ShapeProto tile_shape = 2;
inline bool OpSharding::has_tile_shape() const {
  return this != internal_default_instance() && tile_shape_ != nullptr;
}
inline void OpSharding::clear_tile_shape() {
  if (GetArenaNoVirtual() == nullptr && tile_shape_ != nullptr) {
    delete tile_shape_;
  }
  tile_shape_ = nullptr;
}
inline const ::xla::ShapeProto& OpSharding::tile_shape() const {
  const ::xla::ShapeProto* p = tile_shape_;
  // @@protoc_insertion_point(field_get:xla.OpSharding.tile_shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* OpSharding::release_tile_shape() {
  // @@protoc_insertion_point(field_release:xla.OpSharding.tile_shape)
  
  ::xla::ShapeProto* temp = tile_shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  tile_shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* OpSharding::unsafe_arena_release_tile_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.OpSharding.tile_shape)
  
  ::xla::ShapeProto* temp = tile_shape_;
  tile_shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* OpSharding::mutable_tile_shape() {
  
  if (tile_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    tile_shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.OpSharding.tile_shape)
  return tile_shape_;
}
inline void OpSharding::set_allocated_tile_shape(::xla::ShapeProto* tile_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete tile_shape_;
  }
  if (tile_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(tile_shape);
    if (message_arena != submessage_arena) {
      tile_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tile_shape, submessage_arena);
    }
    
  } else {
    
  }
  tile_shape_ = tile_shape;
  // @@protoc_insertion_point(field_set_allocated:xla.OpSharding.tile_shape)
}

// repeated int64 tile_assignment_dimensions = 3;
inline int OpSharding::tile_assignment_dimensions_size() const {
  return tile_assignment_dimensions_.size();
}
inline void OpSharding::clear_tile_assignment_dimensions() {
  tile_assignment_dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpSharding::tile_assignment_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.OpSharding.tile_assignment_dimensions)
  return tile_assignment_dimensions_.Get(index);
}
inline void OpSharding::set_tile_assignment_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  tile_assignment_dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.OpSharding.tile_assignment_dimensions)
}
inline void OpSharding::add_tile_assignment_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  tile_assignment_dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.OpSharding.tile_assignment_dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
OpSharding::tile_assignment_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.OpSharding.tile_assignment_dimensions)
  return tile_assignment_dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
OpSharding::mutable_tile_assignment_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.OpSharding.tile_assignment_dimensions)
  return &tile_assignment_dimensions_;
}

// repeated int64 tile_assignment_devices = 4;
inline int OpSharding::tile_assignment_devices_size() const {
  return tile_assignment_devices_.size();
}
inline void OpSharding::clear_tile_assignment_devices() {
  tile_assignment_devices_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpSharding::tile_assignment_devices(int index) const {
  // @@protoc_insertion_point(field_get:xla.OpSharding.tile_assignment_devices)
  return tile_assignment_devices_.Get(index);
}
inline void OpSharding::set_tile_assignment_devices(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  tile_assignment_devices_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.OpSharding.tile_assignment_devices)
}
inline void OpSharding::add_tile_assignment_devices(::PROTOBUF_NAMESPACE_ID::int64 value) {
  tile_assignment_devices_.Add(value);
  // @@protoc_insertion_point(field_add:xla.OpSharding.tile_assignment_devices)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
OpSharding::tile_assignment_devices() const {
  // @@protoc_insertion_point(field_list:xla.OpSharding.tile_assignment_devices)
  return tile_assignment_devices_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
OpSharding::mutable_tile_assignment_devices() {
  // @@protoc_insertion_point(field_mutable_list:xla.OpSharding.tile_assignment_devices)
  return &tile_assignment_devices_;
}

// repeated .xla.OpSharding tuple_shardings = 5;
inline int OpSharding::tuple_shardings_size() const {
  return tuple_shardings_.size();
}
inline void OpSharding::clear_tuple_shardings() {
  tuple_shardings_.Clear();
}
inline ::xla::OpSharding* OpSharding::mutable_tuple_shardings(int index) {
  // @@protoc_insertion_point(field_mutable:xla.OpSharding.tuple_shardings)
  return tuple_shardings_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::OpSharding >*
OpSharding::mutable_tuple_shardings() {
  // @@protoc_insertion_point(field_mutable_list:xla.OpSharding.tuple_shardings)
  return &tuple_shardings_;
}
inline const ::xla::OpSharding& OpSharding::tuple_shardings(int index) const {
  // @@protoc_insertion_point(field_get:xla.OpSharding.tuple_shardings)
  return tuple_shardings_.Get(index);
}
inline ::xla::OpSharding* OpSharding::add_tuple_shardings() {
  // @@protoc_insertion_point(field_add:xla.OpSharding.tuple_shardings)
  return tuple_shardings_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::OpSharding >&
OpSharding::tuple_shardings() const {
  // @@protoc_insertion_point(field_list:xla.OpSharding.tuple_shardings)
  return tuple_shardings_;
}

// bool replicate_on_last_tile_dim = 6;
inline void OpSharding::clear_replicate_on_last_tile_dim() {
  replicate_on_last_tile_dim_ = false;
}
inline bool OpSharding::replicate_on_last_tile_dim() const {
  // @@protoc_insertion_point(field_get:xla.OpSharding.replicate_on_last_tile_dim)
  return replicate_on_last_tile_dim_;
}
inline void OpSharding::set_replicate_on_last_tile_dim(bool value) {
  
  replicate_on_last_tile_dim_ = value;
  // @@protoc_insertion_point(field_set:xla.OpSharding.replicate_on_last_tile_dim)
}

// repeated .xla.OpMetadata metadata = 7;
inline int OpSharding::metadata_size() const {
  return metadata_.size();
}
inline void OpSharding::clear_metadata() {
  metadata_.Clear();
}
inline ::xla::OpMetadata* OpSharding::mutable_metadata(int index) {
  // @@protoc_insertion_point(field_mutable:xla.OpSharding.metadata)
  return metadata_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::OpMetadata >*
OpSharding::mutable_metadata() {
  // @@protoc_insertion_point(field_mutable_list:xla.OpSharding.metadata)
  return &metadata_;
}
inline const ::xla::OpMetadata& OpSharding::metadata(int index) const {
  // @@protoc_insertion_point(field_get:xla.OpSharding.metadata)
  return metadata_.Get(index);
}
inline ::xla::OpMetadata* OpSharding::add_metadata() {
  // @@protoc_insertion_point(field_add:xla.OpSharding.metadata)
  return metadata_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::OpMetadata >&
OpSharding::metadata() const {
  // @@protoc_insertion_point(field_list:xla.OpSharding.metadata)
  return metadata_;
}

// -------------------------------------------------------------------

// ReplicaGroup

// repeated int64 replica_ids = 1;
inline int ReplicaGroup::replica_ids_size() const {
  return replica_ids_.size();
}
inline void ReplicaGroup::clear_replica_ids() {
  replica_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ReplicaGroup::replica_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.ReplicaGroup.replica_ids)
  return replica_ids_.Get(index);
}
inline void ReplicaGroup::set_replica_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  replica_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ReplicaGroup.replica_ids)
}
inline void ReplicaGroup::add_replica_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  replica_ids_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ReplicaGroup.replica_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ReplicaGroup::replica_ids() const {
  // @@protoc_insertion_point(field_list:xla.ReplicaGroup.replica_ids)
  return replica_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ReplicaGroup::mutable_replica_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.ReplicaGroup.replica_ids)
  return &replica_ids_;
}

// -------------------------------------------------------------------

// SourceTarget

// int64 source = 1;
inline void SourceTarget::clear_source() {
  source_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SourceTarget::source() const {
  // @@protoc_insertion_point(field_get:xla.SourceTarget.source)
  return source_;
}
inline void SourceTarget::set_source(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  source_ = value;
  // @@protoc_insertion_point(field_set:xla.SourceTarget.source)
}

// int64 target = 2;
inline void SourceTarget::clear_target() {
  target_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SourceTarget::target() const {
  // @@protoc_insertion_point(field_get:xla.SourceTarget.target)
  return target_;
}
inline void SourceTarget::set_target(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  target_ = value;
  // @@protoc_insertion_point(field_set:xla.SourceTarget.target)
}

// -------------------------------------------------------------------

// PrecisionConfig

// repeated .xla.PrecisionConfig.Precision operand_precision = 1;
inline int PrecisionConfig::operand_precision_size() const {
  return operand_precision_.size();
}
inline void PrecisionConfig::clear_operand_precision() {
  operand_precision_.Clear();
}
inline ::xla::PrecisionConfig_Precision PrecisionConfig::operand_precision(int index) const {
  // @@protoc_insertion_point(field_get:xla.PrecisionConfig.operand_precision)
  return static_cast< ::xla::PrecisionConfig_Precision >(operand_precision_.Get(index));
}
inline void PrecisionConfig::set_operand_precision(int index, ::xla::PrecisionConfig_Precision value) {
  operand_precision_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.PrecisionConfig.operand_precision)
}
inline void PrecisionConfig::add_operand_precision(::xla::PrecisionConfig_Precision value) {
  operand_precision_.Add(value);
  // @@protoc_insertion_point(field_add:xla.PrecisionConfig.operand_precision)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
PrecisionConfig::operand_precision() const {
  // @@protoc_insertion_point(field_list:xla.PrecisionConfig.operand_precision)
  return operand_precision_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
PrecisionConfig::mutable_operand_precision() {
  // @@protoc_insertion_point(field_mutable_list:xla.PrecisionConfig.operand_precision)
  return &operand_precision_;
}

// -------------------------------------------------------------------

// ParameterReplication

// repeated bool replicated_at_leaf_buffers = 1;
inline int ParameterReplication::replicated_at_leaf_buffers_size() const {
  return replicated_at_leaf_buffers_.size();
}
inline void ParameterReplication::clear_replicated_at_leaf_buffers() {
  replicated_at_leaf_buffers_.Clear();
}
inline bool ParameterReplication::replicated_at_leaf_buffers(int index) const {
  // @@protoc_insertion_point(field_get:xla.ParameterReplication.replicated_at_leaf_buffers)
  return replicated_at_leaf_buffers_.Get(index);
}
inline void ParameterReplication::set_replicated_at_leaf_buffers(int index, bool value) {
  replicated_at_leaf_buffers_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ParameterReplication.replicated_at_leaf_buffers)
}
inline void ParameterReplication::add_replicated_at_leaf_buffers(bool value) {
  replicated_at_leaf_buffers_.Add(value);
  // @@protoc_insertion_point(field_add:xla.ParameterReplication.replicated_at_leaf_buffers)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
ParameterReplication::replicated_at_leaf_buffers() const {
  // @@protoc_insertion_point(field_list:xla.ParameterReplication.replicated_at_leaf_buffers)
  return replicated_at_leaf_buffers_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
ParameterReplication::mutable_replicated_at_leaf_buffers() {
  // @@protoc_insertion_point(field_mutable_list:xla.ParameterReplication.replicated_at_leaf_buffers)
  return &replicated_at_leaf_buffers_;
}

// -------------------------------------------------------------------

// WhileLoopBackendConfig_KnownTripCount

// int64 n = 1;
inline void WhileLoopBackendConfig_KnownTripCount::clear_n() {
  n_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 WhileLoopBackendConfig_KnownTripCount::n() const {
  // @@protoc_insertion_point(field_get:xla.WhileLoopBackendConfig.KnownTripCount.n)
  return n_;
}
inline void WhileLoopBackendConfig_KnownTripCount::set_n(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  n_ = value;
  // @@protoc_insertion_point(field_set:xla.WhileLoopBackendConfig.KnownTripCount.n)
}

// -------------------------------------------------------------------

// WhileLoopBackendConfig

// .xla.WhileLoopBackendConfig.KnownTripCount known_trip_count = 1;
inline bool WhileLoopBackendConfig::has_known_trip_count() const {
  return this != internal_default_instance() && known_trip_count_ != nullptr;
}
inline void WhileLoopBackendConfig::clear_known_trip_count() {
  if (GetArenaNoVirtual() == nullptr && known_trip_count_ != nullptr) {
    delete known_trip_count_;
  }
  known_trip_count_ = nullptr;
}
inline const ::xla::WhileLoopBackendConfig_KnownTripCount& WhileLoopBackendConfig::known_trip_count() const {
  const ::xla::WhileLoopBackendConfig_KnownTripCount* p = known_trip_count_;
  // @@protoc_insertion_point(field_get:xla.WhileLoopBackendConfig.known_trip_count)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::WhileLoopBackendConfig_KnownTripCount*>(
      &::xla::_WhileLoopBackendConfig_KnownTripCount_default_instance_);
}
inline ::xla::WhileLoopBackendConfig_KnownTripCount* WhileLoopBackendConfig::release_known_trip_count() {
  // @@protoc_insertion_point(field_release:xla.WhileLoopBackendConfig.known_trip_count)
  
  ::xla::WhileLoopBackendConfig_KnownTripCount* temp = known_trip_count_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  known_trip_count_ = nullptr;
  return temp;
}
inline ::xla::WhileLoopBackendConfig_KnownTripCount* WhileLoopBackendConfig::unsafe_arena_release_known_trip_count() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.WhileLoopBackendConfig.known_trip_count)
  
  ::xla::WhileLoopBackendConfig_KnownTripCount* temp = known_trip_count_;
  known_trip_count_ = nullptr;
  return temp;
}
inline ::xla::WhileLoopBackendConfig_KnownTripCount* WhileLoopBackendConfig::mutable_known_trip_count() {
  
  if (known_trip_count_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::WhileLoopBackendConfig_KnownTripCount>(GetArenaNoVirtual());
    known_trip_count_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.WhileLoopBackendConfig.known_trip_count)
  return known_trip_count_;
}
inline void WhileLoopBackendConfig::set_allocated_known_trip_count(::xla::WhileLoopBackendConfig_KnownTripCount* known_trip_count) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete known_trip_count_;
  }
  if (known_trip_count) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(known_trip_count);
    if (message_arena != submessage_arena) {
      known_trip_count = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, known_trip_count, submessage_arena);
    }
    
  } else {
    
  }
  known_trip_count_ = known_trip_count;
  // @@protoc_insertion_point(field_set_allocated:xla.WhileLoopBackendConfig.known_trip_count)
}

// -------------------------------------------------------------------

// CustomCallOutputOperandAliasing

// repeated int64 output_shape_index = 1;
inline int CustomCallOutputOperandAliasing::output_shape_index_size() const {
  return output_shape_index_.size();
}
inline void CustomCallOutputOperandAliasing::clear_output_shape_index() {
  output_shape_index_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CustomCallOutputOperandAliasing::output_shape_index(int index) const {
  // @@protoc_insertion_point(field_get:xla.CustomCallOutputOperandAliasing.output_shape_index)
  return output_shape_index_.Get(index);
}
inline void CustomCallOutputOperandAliasing::set_output_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  output_shape_index_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.CustomCallOutputOperandAliasing.output_shape_index)
}
inline void CustomCallOutputOperandAliasing::add_output_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  output_shape_index_.Add(value);
  // @@protoc_insertion_point(field_add:xla.CustomCallOutputOperandAliasing.output_shape_index)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
CustomCallOutputOperandAliasing::output_shape_index() const {
  // @@protoc_insertion_point(field_list:xla.CustomCallOutputOperandAliasing.output_shape_index)
  return output_shape_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
CustomCallOutputOperandAliasing::mutable_output_shape_index() {
  // @@protoc_insertion_point(field_mutable_list:xla.CustomCallOutputOperandAliasing.output_shape_index)
  return &output_shape_index_;
}

// int64 operand_index = 2;
inline void CustomCallOutputOperandAliasing::clear_operand_index() {
  operand_index_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CustomCallOutputOperandAliasing::operand_index() const {
  // @@protoc_insertion_point(field_get:xla.CustomCallOutputOperandAliasing.operand_index)
  return operand_index_;
}
inline void CustomCallOutputOperandAliasing::set_operand_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  operand_index_ = value;
  // @@protoc_insertion_point(field_set:xla.CustomCallOutputOperandAliasing.operand_index)
}

// repeated int64 operand_shape_index = 3;
inline int CustomCallOutputOperandAliasing::operand_shape_index_size() const {
  return operand_shape_index_.size();
}
inline void CustomCallOutputOperandAliasing::clear_operand_shape_index() {
  operand_shape_index_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CustomCallOutputOperandAliasing::operand_shape_index(int index) const {
  // @@protoc_insertion_point(field_get:xla.CustomCallOutputOperandAliasing.operand_shape_index)
  return operand_shape_index_.Get(index);
}
inline void CustomCallOutputOperandAliasing::set_operand_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  operand_shape_index_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.CustomCallOutputOperandAliasing.operand_shape_index)
}
inline void CustomCallOutputOperandAliasing::add_operand_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  operand_shape_index_.Add(value);
  // @@protoc_insertion_point(field_add:xla.CustomCallOutputOperandAliasing.operand_shape_index)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
CustomCallOutputOperandAliasing::operand_shape_index() const {
  // @@protoc_insertion_point(field_list:xla.CustomCallOutputOperandAliasing.operand_shape_index)
  return operand_shape_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
CustomCallOutputOperandAliasing::mutable_operand_shape_index() {
  // @@protoc_insertion_point(field_mutable_list:xla.CustomCallOutputOperandAliasing.operand_shape_index)
  return &operand_shape_index_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::xla::ChannelHandle_ChannelType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::ChannelHandle_ChannelType>() {
  return ::xla::ChannelHandle_ChannelType_descriptor();
}
template <> struct is_proto_enum< ::xla::TriangularSolveOptions_Transpose> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::TriangularSolveOptions_Transpose>() {
  return ::xla::TriangularSolveOptions_Transpose_descriptor();
}
template <> struct is_proto_enum< ::xla::OpSharding_Type> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::OpSharding_Type>() {
  return ::xla::OpSharding_Type_descriptor();
}
template <> struct is_proto_enum< ::xla::PrecisionConfig_Precision> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::PrecisionConfig_Precision>() {
  return ::xla::PrecisionConfig_Precision_descriptor();
}
template <> struct is_proto_enum< ::xla::PrimitiveType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::PrimitiveType>() {
  return ::xla::PrimitiveType_descriptor();
}
template <> struct is_proto_enum< ::xla::Format> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::Format>() {
  return ::xla::Format_descriptor();
}
template <> struct is_proto_enum< ::xla::ProfileType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::ProfileType>() {
  return ::xla::ProfileType_descriptor();
}
template <> struct is_proto_enum< ::xla::PaddingType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::PaddingType>() {
  return ::xla::PaddingType_descriptor();
}
template <> struct is_proto_enum< ::xla::FftType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::FftType>() {
  return ::xla::FftType_descriptor();
}
template <> struct is_proto_enum< ::xla::RandomDistribution> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::RandomDistribution>() {
  return ::xla::RandomDistribution_descriptor();
}
template <> struct is_proto_enum< ::xla::RandomAlgorithm> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::RandomAlgorithm>() {
  return ::xla::RandomAlgorithm_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fxla_5fdata_2eproto
