import h5py
import json
import requests
import sys
import os
import pandas as pd
import logging
import cProfile  # Re-enabled
import pstats  # Re-enabled
from pstats import SortKey  # Re-enabled
import io  # Re-enabled
import time
import argparse

# 移除本地信号处理函数的引入
# from apps.signal_analysis.signal import get_available_signals

# 禁用TensorFlow的warning和error输出，避免Tkinter相关错误
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=全部打印, 1=不打印INFO, 2=不打印WARNING, 3=不打印ERROR

# 尝试禁用Tkinter相关功能
try:
    import matplotlib

    matplotlib.use('Agg')  # 使用非交互式后端，避免Tkinter错误
except ImportError:
    pass

# 修改日志格式,只输出消息内容
formatter = logging.Formatter('%(message)s')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')

# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录的路径
project_root = os.path.abspath(os.path.join(current_dir, '../../..'))
# 将项目根目录添加到 sys.path
sys.path.append(project_root)


# 创建全局会话
session = requests.Session()

# 设置连接池最大连接数
adapter = requests.adapters.HTTPAdapter(
    pool_connections=100,  # 连接池连接数
    pool_maxsize=100,  # 连接池最大连接数
    max_retries=3  # 最大重试次数
)
session.mount('http://', adapter)
session.mount('https://', adapter)

# 全局变量
TOKEN_EXPIRY_TIME = 3600  # token有效期(秒)
TOKEN_REFRESH_THRESHOLD = 3300  # token刷新阈值(秒)
token_obtain_time = 0  # token获取时间
token_value = None  # 全局token值
REQUEST_INTERVAL = 0.1  # 请求间隔(秒)
# MAX_ECG_POINTS = 5000  # 最大ECG数据点数 (用户要求不再截断)

ERROR_CODE_MAP = {
    401: "信号噪声过高",
    402: "算法内部错误",
    403: "心律失常诊断错误",
    4: "Token过期或无效",
    6: "数据格式或内容无效",  # 添加错误代码6的定义
    2: "API内部异常",  # 添加错误代码2的映射
    # ... 其他错误代码 ...
}


# def timing_decorator(func): # Commented out timing_decorator
#     """性能监控装饰器"""
#
#     @wraps(func)
#     def wrapper(*args, **kwargs):
#         start_time = time.time()
#         result = func(*args, **kwargs)
#         end_time = time.time()
#         logging.info(f"{func.__name__} took {end_time - start_time:.2f} seconds")
#         return result
#
#     return wrapper


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='ECG Analysis Tool')
    parser.add_argument('--mode', type=int, choices=[1, 2], help='处理模式：1=单文件，2=文件夹')
    parser.add_argument('--input', type=str, help='输入文件或文件夹路径')
    parser.add_argument('--output', type=str, help='输出文件夹路径')
    parser.add_argument('--debug', action='store_true', help='启用调试模式，输出更详细的日志')
    parser.add_argument('--sample-rate', type=int, default=500, help='采样率，默认为500Hz')
    return parser.parse_args()


# 在文件顶部导入部分添加


# 在 get_token 函数开始处添加以下代码
def get_token(force_refresh=False, refresh_token=False):
    """获取API的认证令牌，支持缓存和自动刷新

    Parameters:
        force_refresh (bool): 是否强制刷新获取新token
        refresh_token (bool): 如果为True，仅刷新token的过期时间，不重新获取
    """
    global token_value, token_obtain_time, session

    # 强制重新加载Configuration文件，确保使用最新Configuration
    # importlib.reload(global_settings) # Commented out reload

    current_time = time.time()

    # 如果仅刷新token的过期时间
    if refresh_token and token_value:
        token_obtain_time = current_time
        logging.info(f"刷新token的过期时间，token: {token_value[:10]}...")
        return token_value

    # 如果token存在且未过期且不强制刷新，直接返回
    if not force_refresh and token_value and (current_time - token_obtain_time) < TOKEN_REFRESH_THRESHOLD:
        logging.info(
            f"使用缓存的token: {token_value[:10]}... (还有 {TOKEN_REFRESH_THRESHOLD - (current_time - token_obtain_time):.1f} 秒过期)")
        return token_value

    try:
        # 直接使用本地URL，而不是从Configuration文件读取
        login_url = 'http://127.0.0.1:8000/api/login/'  # 修改为正式环境地址

        # 尝试从Configuration文件获取客户端ID和密钥
        try:
            client_id = global_settings.heartVoice['login']['clientId']
            client_secret = global_settings.heartVoice['login']['clientSecret']
        except (AttributeError, KeyError, NameError) as e:
            # 如果从Configuration文件获取失败，使用硬编码的备用值
            logging.warning(f"从配置文件获取客户端ID和密钥失败: {e}，使用硬编码的备用值")
            client_id = "znipr4p6"  # 使用日志中显示的客户端ID
            client_secret = "383cd1d7325046029af2d2f984395103"  # 如果您知道备用密钥，可以在这里添加

        params = {
            "clientId": client_id,
            "clientSecret": client_secret
        }

        logging.info(f"尝试获取token，URL: {login_url}")
        logging.info(f"认证参数: clientId={client_id}")

        # 重试机制
        max_retries = 3
        retry_count = 0
        retry_delay = 1

        while retry_count < max_retries:
            try:
                # 重置会话，避免之前的连接问题
                if retry_count > 0:
                    session = requests.Session()
                    adapter = requests.adapters.HTTPAdapter(
                        pool_connections=100,
                        pool_maxsize=100,
                        max_retries=3
                    )
                    session.mount('http://', adapter)
                    session.mount('https://', adapter)
                    logging.info(f"重试获取token (第{retry_count}次)")

                headers = {'Content-Type': 'application/x-www-form-urlencoded'}
                resp = session.post(login_url, data=params, headers=headers, timeout=10)
                logging.info(f"Token请求状态码: {resp.status_code}")

                resp.raise_for_status()
                resp_json = resp.json()

                if 'data' in resp_json and 'token' in resp_json['data']:
                    token_value = resp_json['data']['token']
                    token_obtain_time = current_time
                    logging.info(f"成功获取token: {token_value[:10]}...")
                    return token_value
                else:
                    logging.error(f"Token响应格式错误: {resp_json}")
                    retry_count += 1
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避

            except requests.exceptions.RequestException as e:
                logging.error(f"Token请求错误: {e}")
                retry_count += 1
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避

        logging.error("获取token失败，已达到最大重试次数")
        return None

    except Exception as e:
        logging.error(f"获取token时发生错误: {e}")
        return None


# @timing_decorator # Commented out
def profile_api_call(ecg_data, fs, token):
    """对单次API调用进行性能分析"""
    # profiler = cProfile.Profile()
    # profiler.enable()

    result, sna_features = ecg_analysis(ecg_data, fs, token)

    # profiler.disable()

    # 创建字符串流来捕获输出
    # s = io.StringIO()
    # stats = pstats.Stats(profiler, stream=s).sort_stats(SortKey.TIME)
    # stats.print_stats(10)  # 只打印前10个最耗时的函数

    # logging.info("Performance Analysis:\\n" + s.getvalue())
    return result, sna_features


# @timing_decorator # Commented out
def ecg_analysis(ecg_data, fs, token, retry_count=0):
    """调用心电分析API，支持自动重试和token刷新"""
    global session, token_value

    logging.debug(f"DEBUG: ecg_analysis called with fs={fs}")
    logging.debug(f"DEBUG: Original ecg_data type: {type(ecg_data)}, length: {len(ecg_data)}")
    if ecg_data and len(ecg_data) > 10:
        logging.debug(f"DEBUG: Original ecg_data samples (first 5): {ecg_data[:5]}, (last 5): {ecg_data[-5:]}")
    else:
        logging.debug(f"DEBUG: Original ecg_data: {ecg_data}")
    logging.debug(f"DEBUG: Token used: {token[:10]}...")

    # 增加请求间隔，避免触发API限制
    # 对于重试次数，增加更长的等待时间
    wait_time = REQUEST_INTERVAL * (retry_count + 1)
    logging.info(f"等待 {wait_time} 秒后发送请求...")
    time.sleep(wait_time)

    # 检查token是否需要刷新
    current_time = time.time()
    if current_time - token_obtain_time > TOKEN_REFRESH_THRESHOLD:
        logging.info("Token即将过期，尝试刷新")
        new_token = get_token(force_refresh=True)
        if new_token:
            token = new_token
            token_value = new_token

    # 使用本地地址测试本地功能
    analysis_url = 'http://127.0.0.1:8000/api/diagnose/arrhythmia/'
    headers = {
        "Content-Type": "application/json",
        "X-Auth-Token": token
    }

    # 限制ECG数据只取前5000个点 (用户要求不再截断)
    # if len(ecg_data) > MAX_ECG_POINTS:
    #     original_length = len(ecg_data)
    #     ecg_data = ecg_data[:MAX_ECG_POINTS]
    #     logging.info(f"ECG数据已截断：从{original_length}个点截取前{MAX_ECG_POINTS}个点")

    # 数据统计信息
    data_stats = {
        "min": min(ecg_data) if ecg_data else None,
        "max": max(ecg_data) if ecg_data else None,
        "mean": sum(ecg_data) / len(ecg_data) if ecg_data else None,
        "is_list": isinstance(ecg_data, list),
        "length": len(ecg_data) if hasattr(ecg_data, "__len__") else "N/A",
        "sample_first_5": str(ecg_data[:5]) if hasattr(ecg_data, "__getitem__") and len(ecg_data) >= 5 else "N/A"
    }
    logging.debug(f"ECG数据统计: {data_stats}")

    # 修改数据转换方式 - 不再使用str()转换整个列表
    # 而是确保数据是数字列表，然后由json模块正确处理

    # 确保所有数据都是合法的数字（非NaN、非无穷大）
    cleaned_ecg_data = []
    for value in ecg_data:
        if isinstance(value, (int, float)) and not pd.isna(value) and pd.notna(value):
            # 检查是否为无穷大值
            if abs(value) != float('inf'):
                cleaned_ecg_data.append(value)
            else:
                cleaned_ecg_data.append(0)  # 替换无穷大值为0
        else:
            cleaned_ecg_data.append(0)  # 替换非数值为0

    # 记录清理后的数据信息
    logging.debug(f"清理后的数据长度: {len(cleaned_ecg_data)}")
    if len(cleaned_ecg_data) != len(ecg_data):
        logging.warning(f"数据清理过程中移除了 {len(ecg_data) - len(cleaned_ecg_data)} 个无效值")

    logging.debug(f"DEBUG: Cleaned ecg_data type: {type(cleaned_ecg_data)}, length: {len(cleaned_ecg_data)}")
    if cleaned_ecg_data and len(cleaned_ecg_data) > 10:
        logging.debug(
            f"DEBUG: Cleaned ecg_data samples (first 5): {cleaned_ecg_data[:5]}, (last 5): {cleaned_ecg_data[-5:]}")
    else:
        logging.debug(f"DEBUG: Cleaned ecg_data: {cleaned_ecg_data}")

    # 使用JSON序列化，确保数据被正确格式化为JSON数组
    # 直接发送列表，让json模块负责序列化
    params = {
        "signal": cleaned_ecg_data,  # 直接传递列表，不转字符串
        "fs": int(fs),  # 确保fs是整数
        "adc_gain": 1,
        "adc_zero": 0,
        'union_id': 'test_user',
        'ecg_age_key': 1,  # 修改为1，启用心脏年龄计算
        'emotion_key': 1,  # 修改为1，启用情绪计算
        'health_metrics': 1  # 添加API需要的health_metrics参数
    }

    params_for_logging = params.copy()
    if 'signal' in params_for_logging:
        s_data = params_for_logging['signal']
        if s_data and len(s_data) > 10:
            params_for_logging['signal'] = f"list_len_{len(s_data)}_samples_({s_data[:5]}...{s_data[-5:]})"
        else:
            params_for_logging['signal'] = f"list_len_{len(s_data)}_data_({s_data})"
    logging.debug(f"DEBUG: API request params: {json.dumps(params_for_logging, ensure_ascii=False)}")

    try:
        logging.info("发送API请求...")
        logging.info(f"数据长度: {len(cleaned_ecg_data)}")
        logging.info(f"采样率: {fs}")
        logging.info(f"API地址: {analysis_url}")
        logging.info(f"使用token: {token[:10]}...")  # 只显示token的前10个字符

        # 设置超时，避免请求卡住
        resp = session.post(analysis_url, headers=headers, json=params, timeout=30)
        logging.debug(f"DEBUG: API response status code: {resp.status_code}")

        if resp.status_code == 401:
            # 401可能是token过期，尝试刷新token并重试
            if retry_count < 2:  # 最多重试2次
                logging.warning("Token可能已过期，尝试刷新并重试")
                new_token = get_token(force_refresh=True)
                if new_token:
                    return ecg_analysis(ecg_data, fs, new_token, retry_count + 1)
            logging.error("Token刷新后仍然失败")
            return None, None
        elif resp.status_code != 200:
            # 其他错误状态码
            logging.error(f"API响应错误: {resp.status_code}")

            # 如果是服务器错误，尝试重试
            if resp.status_code >= 500 and retry_count < 3:
                retry_delay = 1 * (2 ** retry_count)  # 指数退避
                logging.warning(f"服务器错误，{retry_delay}秒后重试 (第{retry_count + 1}次)")
                time.sleep(retry_delay)
                return ecg_analysis(ecg_data, fs, token, retry_count + 1)

            return None, None

        resp_json = resp.json()

        # 增加：记录完整的API响应，包括详细的错误信息
        if resp_json.get("code") != 0:
            logging.debug("DEBUG: ---- API Raw Error Response Start ----")
            logging.info(f"API详细错误响应: {json.dumps(resp_json, ensure_ascii=False, indent=2)}")
            logging.debug("DEBUG: ---- API Raw Error Response End ----")

        # 检查是否是错误代码4（token过期）
        if resp_json.get("code") == 4:
            if retry_count < 2:  # 最多重试2次
                logging.warning("Token已过期（错误代码4），尝试刷新token并重试")
                new_token = get_token(force_refresh=True)
                if new_token:
                    return ecg_analysis(ecg_data, fs, new_token, retry_count + 1)
            logging.error(f"Token刷新后仍然失败，详细错误信息: {resp_json}")
            return None, None

        if resp_json.get("code") == 0:
            # API调用成功，刷新token的过期时间
            get_token(refresh_token=True)
            logging.info("API调用成功，已刷新token的过期时间")

            data = resp_json.get("data", {})
            sna_feature = data.get('ArrhythmiaDiagnosis', {}).get('SNA', False)
            sna_features = data.get('SNA_Features', {})

            # 只输出关键诊断信息
            arrhythmia = data.get('ArrhythmiaDiagnosis', {})
            positive_diagnoses = [k for k, v in arrhythmia.items() if v == 1]
            if positive_diagnoses:
                logging.info(f"诊断结果: {', '.join(positive_diagnoses)}")

            metrics = data.get('HealthMetrics', {})
            logging.info(f"健康指标: 压力={metrics.get('Pressure', 0)}, "
                         f"HRV={metrics.get('HRV', 0)}, "
                         f"疲劳={metrics.get('Fatigue', 0)}, "
                         f"活力={metrics.get('Vitality', 0)}")

            pqrstc = data.get('PQRSTC', {})
            logging.info(
                f"心率: {pqrstc.get('HR', 0)} bpm, QRS: {pqrstc.get('QRS_duration', pqrstc.get('QRS_QRSDuration', 0))} ms, "
                f"QT: {pqrstc.get('QT', 0)} ms, QTc: {pqrstc.get('QTc', 0)} ms")

            # Signal quality信息
            is_noise = data.get('IsNoise', False)
            noise_message_from_api = data.get('NoiseMessage', 'N/A')
            signal_quantity_from_api = data.get('SignalQuantity', 'N/A')
            # 为了日志更清晰，获取API原始返回的IsNoise值，如果不存在则标记
            is_noise_raw_from_api = data.get('IsNoise')
            if is_noise_raw_from_api is None:
                is_noise_log_val = 'N/A (defaulted to False in script)'
            else:
                is_noise_log_val = is_noise_raw_from_api

            logging.info(
                f"信号状态: {'噪音信号' if is_noise else '正常信号'} "
                f"(API返回 - IsNoise: {is_noise_log_val}, "
                f"NoiseMessage: '{noise_message_from_api}', "
                f"SignalQuantity: {signal_quantity_from_api})"
            )

            return data, sna_features
        else:
            error_code = resp_json.get("code")
            error_message = resp_json.get("msg")
            detailed_error_message = ERROR_CODE_MAP.get(error_code, error_message)
            logging.error(f"API返回错误: {detailed_error_message} (code: {error_code})")

            # 如果是token相关错误，尝试刷新token
            if error_code in [401, 403, 4] and retry_count < 2:
                logging.warning("可能是token过期，尝试刷新token并重试")
                new_token = get_token(force_refresh=True)
                if new_token:
                    return ecg_analysis(ecg_data, fs, new_token, retry_count + 1)
            return None, None

    except requests.exceptions.RequestException as e:
        logging.error(f"API请求错误: {e}")

        # 对于连接超时错误，尝试重试
        if isinstance(e, (requests.exceptions.ConnectionError, requests.exceptions.Timeout)) and retry_count < 3:
            retry_delay = 1 * (2 ** retry_count)  # 指数退避
            logging.warning(f"连接错误，{retry_delay}秒后重试 (第{retry_count + 1}次)")
            time.sleep(retry_delay)

            # 重置会话
            session = requests.Session()
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=100,
                pool_maxsize=100,
                max_retries=3
            )
            session.mount('http://', adapter)
            session.mount('https://', adapter)

            return ecg_analysis(ecg_data, fs, token, retry_count + 1)

        return None, None


def get_disease_name(arrhythmia_diagnosis):
    """将识别到的疾病转换为文字"""
    disease_mapping = {
        'SN': '窦性心律',
        'SNA': '窦性心律不齐',
        'SNT': '窦性心动过速',
        'SNB': '窦性心动过缓',
        'PVC': '室性早搏',
        'PSC': '不确定的早搏类型',
        'PJC': '交界性早搏',
        'PAC': '房性早搏',
        'VT': '室性心动过速',
        'SVT': '室上性心动过速',
        'AFL': '心房扑动',
        'AF': '心房颤动',
        'WPW': 'WPW综合征',
        'VE': '室性逸搏',
        'JE': '交界性逸搏',
        'AE': '房性逸搏',
        'AVBI': '一度房室传导阻滞',
        'AVBII': '二度房室传导阻滞',
        'AVBIII': '三度房室传导阻滞',
        'IVB': '室内传导阻滞',
        'LBBB': '左束支传导阻滞',
        'RBBB': '右束支传导阻滞',
        'LAFB': '左前分支传导阻滞',
        'BRU': 'Brugada综合征',
        'LQT': 'QT间期延长',
        'bPVC': '成对室早',
        # 添加其他可能的多结论诊断映射
        'ISC': '心肌缺血',
        'LVH': '左心室肥大',
        'RVH': '右心室肥大',
        'LAH': '左心房肥大',
        'RAH': '右心房肥大',
        'MI': '心肌梗死'
    }
    # 新增逻辑：如果字典为空或所有值都为0，直接返回"Sinus Rhythm"
    if not arrhythmia_diagnosis or all(v == 0 for v in arrhythmia_diagnosis.values()):
        return '窦性心律'
    diagnoses = [k for k, v in arrhythmia_diagnosis.items() if v == 1]
    chinese_diagnoses = [disease_mapping.get(diagnosis, diagnosis) for diagnosis in diagnoses]
    return ', '.join(chinese_diagnoses)  # 返回逗号分隔的字符串


# 添加缺失的 get_multi_label_disease_name 函数定义
def get_multi_label_disease_name(multi_label_diagnosis_list):
    """将多结论诊断列表转换为文字列表"""
    disease_mapping = {
        'SN': '窦性心律',
        'SNA': '窦性心律不齐',
        'SNT': '窦性心动过速',
        'SNB': '窦性心动过缓',
        'PVC': '室性早搏',
        'PSC': '不确定的早搏类型',
        'PJC': '交界性早搏',
        'PAC': '房性早搏',
        'VT': '室性心动过速',
        'SVT': '室上性心动过速',
        'AFL': '心房扑动',
        'AF': '心房颤动',
        'WPW': 'WPW综合征',
        'VE': '室性逸搏',
        'JE': '交界性逸搏',
        'AE': '房性逸搏',
        'AVBI': '一度房室传导阻滞',
        'AVBII': '二度房室传导阻滞',
        'AVBIII': '三度房室传导阻滞',
        'IVB': '室内传导阻滞',
        'LBBB': '左束支传导阻滞',
        'RBBB': '右束支传导阻滞',
        'LAFB': '左前分支传导阻滞',
        'BRU': 'Brugada综合征',
        'LQT': 'QT间期延长',
        'bPVC': '成对室早',
        # 添加其他可能的多结论诊断映射
        'ISC': '心肌缺血',
        'LVH': '左心室肥大',
        'RVH': '右心室肥大',
        'LAH': '左心房肥大',
        'RAH': '右心房肥大',
        'MI': '心肌梗死'
    }
    # 遍历传入的列表 multi_label_diagnosis_list
    chinese_diagnoses = [disease_mapping.get(diagnosis, diagnosis) for diagnosis in multi_label_diagnosis_list]
    return chinese_diagnoses  # 返回中文列表


# @timing_decorator
def process_single_file(input_file, sampling_rate, token):
    """处理单个文件的函数 - 单个处理模式"""
    # 使用全局token变量
    global token_value

    # 强制使用500HzSampling rate
    # sampling_rate = 500 # 用户要求使用传入的Sampling rate
    logging.info(f"使用传入的采样率: {sampling_rate}Hz")

    results_list = []
    # 在函数末尾定义的列顺序，先在这里拿到，方便在错误处理中填充None
    cols = [
        'Row', 'ID', 'Status', 'First10sSkipped', 'Diseases', 'MultiLabelDiagnosis', 'ECGAge',
        'ArrhythmiaDiagnosis', 'PQRSTC', 'HealthMetrics',
        'SignalQuantity', 'IsNoise', 'NoiseMessage',
        'ErrorMessage'
    ]

    file_extension = os.path.splitext(input_file)[1].lower()

    if file_extension in ['.h5', '.hdf5']:
        logging.info(f"开始处理HDF5文件: {input_file}")
        detailed_error_count = 0  # Initialize counter
        try:
            h5_file = h5py.File(input_file, 'r')
            if 'ecg_data' not in h5_file:
                logging.error(f"HDF5 文件 {input_file} 中未找到 'ecg_data' 数据集")
                if 'h5_file' in locals() and h5_file:  # Ensure file is closed if opened
                    h5_file.close()
                return pd.DataFrame(columns=cols)

            dset = h5_file['ecg_data']
            num_rows = dset.shape[0]
            logging.info(f"HDF5文件 '{input_file}' 中的 'ecg_data' 数据集包含 {num_rows} 行数据.")

            # 尝试获取H5文件的字段名列表
            field_names = []
            if hasattr(dset, 'dtype') and hasattr(dset.dtype, 'names') and dset.dtype.names:
                field_names = list(dset.dtype.names)
                logging.info(f"HDF5文件中的字段: {', '.join(field_names)}")
            else:
                logging.warning("无法获取HDF5文件的字段结构")

            # 检查是否有id字段
            has_id_field = 'id' in field_names
            if has_id_field:
                logging.info("检测到id字段，将提取并添加到结果中")
            else:
                logging.warning("未检测到id字段，将使用行号作为ID")

            # 确定使用哪个字段作为心电数据源
            ecg_field = None
            for field_candidate in ['ecgII', 'ecgI', 'ecg', 'signal']:
                if field_candidate in field_names:
                    ecg_field = field_candidate
                    logging.info(f"将使用字段 '{ecg_field}' 作为心电数据源")
                    break

            if not ecg_field and field_names:
                # 如果没有找到预期的字段但有其他字段，使用第一个可用字段
                ecg_field = field_names[0]
                logging.warning(f"未找到预期的心电数据字段，将使用第一个可用字段 '{ecg_field}'")
            elif not ecg_field:
                logging.error("无法确定心电数据字段，HDF5文件可能格式不正确")
                if 'h5_file' in locals() and h5_file:
                    h5_file.close()
                return pd.DataFrame(columns=cols)

            for i in range(num_rows):
                row_label = f"HDF5 文件 '{os.path.basename(input_file)}', 行 {i + 1}"
                try:
                    h5_row_data = dset[i]

                    # 提取ID字段（如果存在）
                    record_id = None
                    if has_id_field:
                        record_id = h5_row_data['id']
                        if isinstance(record_id, bytes):
                            try:
                                record_id = record_id.decode('utf-8')
                            except UnicodeDecodeError:
                                record_id = str(record_id)
                        logging.info(f"{row_label}: 提取到ID: {record_id}")
                    else:
                        record_id = f"行{i + 1}"

                    # 检查字段是否存在于当前行
                    if ecg_field not in h5_row_data.dtype.names:
                        logging.warning(
                            f"{row_label}: 数据行中未找到 '{ecg_field}' 字段。可用的字段: {h5_row_data.dtype.names}")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': i + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': f"数据行中未找到 '{ecg_field}' 字段",
                            'First10sSkipped': None
                        })
                        results_list.append(error_row)
                        continue

                    ecg_data_raw = h5_row_data[ecg_field]
                    ecg_data_temp = []

                    # 针对HDF5文件中的bytes类型ECG数据进行处理
                    if isinstance(ecg_data_raw, bytes):
                        try:
                            # 将bytes解码为字符串
                            ecg_str = ecg_data_raw.decode('utf-8').strip()

                            # 尝试判断字符串格式：JSON数组、CSV格式或其他
                            if (ecg_str.startswith('[') and ecg_str.endswith(']')) or ',' in ecg_str:
                                # 看起来是JSON数组或CSV格式的字符串

                                # 移除方括号（如果有）
                                if ecg_str.startswith('[') and ecg_str.endswith(']'):
                                    ecg_str = ecg_str[1:-1]

                                # 分割字符串并尝试转换为浮点数
                                items = ecg_str.split(',')
                                for item in items:
                                    item = item.strip()
                                    if item:  # 确保不是空字符串
                                        try:
                                            value = float(item)
                                            ecg_data_temp.append(value)
                                        except ValueError:
                                            # 如果单个值转换失败，记录错误并跳过该值
                                            if detailed_error_count < 3:
                                                logging.warning(
                                                    f"DIAGNOSTIC ({row_label}): 无法将 '{item}' 转换为浮点数，已跳过")
                                                detailed_error_count += 1
                            else:
                                # 尝试作为单个数值处理
                                try:
                                    value = float(ecg_str)
                                    ecg_data_temp.append(value)
                                except ValueError:
                                    if detailed_error_count < 3:
                                        logging.error(
                                            f"DIAGNOSTIC ({row_label}): 无法将字符串 '{ecg_str[:100]}...' 转换为数值")
                                        detailed_error_count += 1

                            if ecg_data_temp:
                                logging.info(
                                    f"DIAGNOSTIC ({row_label}): 成功从bytes解析数据，获取 {len(ecg_data_temp)} 个数值")

                        except (UnicodeDecodeError, ValueError) as e:
                            # 如果处理失败，记录错误并设置为空列表
                            if detailed_error_count < 5:
                                logging.error(
                                    f"DIAGNOSTIC ({row_label}): bytes处理失败: {str(e)}, 内容: {str(ecg_data_raw)[:100]}...")
                                detailed_error_count += 1
                    # 处理字符串类型
                    elif isinstance(ecg_data_raw, str):
                        try:
                            ecg_str = ecg_data_raw.strip()

                            # 同样判断字符串格式
                            if (ecg_str.startswith('[') and ecg_str.endswith(']')) or ',' in ecg_str:
                                # 移除方括号（如果有）
                                if ecg_str.startswith('[') and ecg_str.endswith(']'):
                                    ecg_str = ecg_str[1:-1]

                                # 分割字符串并尝试转换为浮点数
                                items = ecg_str.split(',')
                                for item in items:
                                    item = item.strip()
                                    if item:  # 确保不是空字符串
                                        try:
                                            value = float(item)
                                            ecg_data_temp.append(value)
                                        except ValueError:
                                            if detailed_error_count < 3:
                                                logging.warning(
                                                    f"DIAGNOSTIC ({row_label}): 无法将 '{item}' 转换为浮点数，已跳过")
                                                detailed_error_count += 1
                            else:
                                # 尝试作为单个数值处理
                                try:
                                    value = float(ecg_str)
                                    ecg_data_temp.append(value)
                                except ValueError:
                                    if detailed_error_count < 3:
                                        logging.error(
                                            f"DIAGNOSTIC ({row_label}): 无法将字符串 '{ecg_str[:100]}...' 转换为数值")
                                        detailed_error_count += 1
                        except ValueError as e:
                            # 如果转换失败，记录更多信息
                            if detailed_error_count < 5:
                                logging.info(
                                    f"DIAGNOSTIC ({row_label}): 字符串转换失败: {str(e)}, 字符串内容: {ecg_data_raw[:100]}...")
                                detailed_error_count += 1
                    # 处理NumPy数组或可转为列表的对象
                    elif hasattr(ecg_data_raw, 'tolist'):
                        ecg_data_temp = ecg_data_raw.tolist()
                    # 处理已经是列表或元组的数据
                    elif isinstance(ecg_data_raw, (list, tuple)):
                        ecg_data_temp = list(ecg_data_raw)
                    else:  # 其他类型
                        # 尝试直接转为数值
                        try:
                            ecg_data_temp = [float(ecg_data_raw)]
                        except (ValueError, TypeError):
                            if detailed_error_count < 3:
                                logging.error(f"DIAGNOSTIC ({row_label}): 无法处理类型为 {type(ecg_data_raw)} 的数据")
                                detailed_error_count += 1
                            ecg_data_temp = []

                    # 最终处理：使用pandas函数确保所有值都是数值类型
                    if ecg_data_temp:
                        ecg_data_after_numeric = pd.to_numeric(ecg_data_temp, errors='coerce').tolist()
                        ecg_data = [x for x in ecg_data_after_numeric if pd.notna(x)]
                    else:
                        ecg_data = []

                    if not ecg_data:
                        if detailed_error_count < 5:  # Log details for the first 5 such errors
                            logging.info(f"DIAGNOSTIC ({row_label}): ---- Error Details Start ----")
                            logging.info(
                                f"DIAGNOSTIC ({row_label}): ecg_data_raw (type: {type(ecg_data_raw)}): {repr(ecg_data_raw)}")
                            if hasattr(ecg_data_raw, 'shape'):
                                logging.info(f"DIAGNOSTIC ({row_label}): ecg_data_raw.shape: {ecg_data_raw.shape}")
                            if hasattr(ecg_data_raw, 'dtype'):
                                logging.info(f"DIAGNOSTIC ({row_label}): ecg_data_raw.dtype: {ecg_data_raw.dtype}")
                            # Log ecg_data_temp which was the direct input to pd.to_numeric
                            logging.info(
                                f"DIAGNOSTIC ({row_label}): ecg_data_temp (input to pd.to_numeric, type: {type(ecg_data_temp)}): {repr(ecg_data_temp)}")
                            if ecg_data_after_numeric:
                                logging.info(
                                    f"DIAGNOSTIC ({row_label}): ecg_data_after_numeric (after pd.to_numeric, before dropna, type: {type(ecg_data_after_numeric)}): {repr(ecg_data_after_numeric)}")
                            logging.info(f"DIAGNOSTIC ({row_label}): ---- Error Details End ----")
                            detailed_error_count += 1

                        logging.warning(f"{row_label}: '{ecg_field}' 数据为空或所有值都无法转换为有效数值")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': i + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': f"'{ecg_field}' 数据为空或无效",
                            'First10sSkipped': None
                        })
                        results_list.append(error_row)
                        continue

                    min_required_samples = sampling_rate * 10
                    if len(ecg_data) < min_required_samples:
                        logging.warning(
                            f"{row_label}: 数据长度不足10秒 ({len(ecg_data)} < {min_required_samples})，跳过")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': i + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': f'数据长度不足10秒({len(ecg_data)}<{min_required_samples})',
                            'First10sSkipped': None
                        })
                        results_list.append(error_row)
                        continue

                    ecg_signal = ecg_data
                    logging.info(f"{row_label}: 准备发送 {len(ecg_signal)} 个样本点给 API")

                    # 使用最新的token_value进行API调用
                    current_token = token_value or token

                    # 调用API分析数据
                    result, _ = ecg_analysis(ecg_signal, sampling_rate, current_token)

                    if result:
                        arrhythmia_diagnosis = result.get('ArrhythmiaDiagnosis', {})
                        diseases = get_disease_name(arrhythmia_diagnosis)
                        multi_label_diagnosis = result.get('MultiLabelDiagnosis', [])
                        chinese_multi_label = get_multi_label_disease_name(multi_label_diagnosis)
                        signal_quantity = result.get('SignalQuantity')
                        is_noise = result.get('IsNoise', False)
                        noise_message = result.get('NoiseMessage', '')
                        first_10s_skipped = noise_message.startswith("前10秒信号可能为噪音")

                        # Log details (similar to CSV path)
                        logging.info(f"{row_label} 诊断结果:")
                        logging.info(f"  传统诊断结果: {diseases}")
                        logging.info(
                            f"  多结论诊断结果: {', '.join(chinese_multi_label) if chinese_multi_label else '无'}")
                        logging.info(f"  信号质量: {signal_quantity}, 是否为噪音: {'是' if is_noise else '否'}")
                        # ... (略)其他日志

                        new_row = {
                            'Row': i + 1,
                            'ID': record_id,
                            'ECGAge': result.get('ECGAge'),
                            'ArrhythmiaDiagnosis': result.get('ArrhythmiaDiagnosis', {}),
                            'MultiLabelDiagnosis': chinese_multi_label,
                            'HealthMetrics': result.get('HealthMetrics', {}),
                            'PQRSTC': result.get('PQRSTC', {}),
                            'SignalQuantity': signal_quantity,
                            'IsNoise': is_noise,
                            'NoiseMessage': noise_message,
                            'Status': '成功',
                            'Diseases': diseases,
                            'First10sSkipped': first_10s_skipped,
                            'ErrorMessage': ''
                        }
                    else:
                        logging.warning(f"{row_label}: API处理失败")
                        new_row = {col: None for col in cols}
                        new_row.update({
                            'Row': i + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': 'API调用失败或返回错误',
                            'First10sSkipped': None
                        })
                    results_list.append(new_row)

                except Exception as e:
                    logging.error(f"{row_label}: 处理时发生意外错误: {str(e)}")
                    error_row = {col: None for col in cols}
                    error_row.update({
                        'Row': i + 1,
                        'ID': has_id_field and h5_row_data.get('id', f"行{i + 1}") or f"行{i + 1}",
                        'Status': '失败',
                        'ErrorMessage': f'处理错误: {str(e)}',
                        'First10sSkipped': None
                    })
                    results_list.append(error_row)

            if 'h5_file' in locals() and h5_file:  # Ensure file is closed
                h5_file.close()
                logging.info(f"已关闭HDF5文件: {input_file}")

        except FileNotFoundError:
            logging.error(f"HDF5输入文件未找到: {input_file}")
            return pd.DataFrame(columns=cols)
        except Exception as e:
            logging.error(f"读取或处理HDF5文件 {input_file} 时发生严重错误: {str(e)}")
            if 'h5_file' in locals() and h5_file:  # Ensure file is closed in case of error after opening
                h5_file.close()
            return pd.DataFrame(columns=cols)

    elif file_extension == '.csv':
        logging.info(f"开始处理CSV文件: {input_file}")
        chunk_size = 1000  # Specific to CSV processing
        try:
            # 使用 chunksize 优化大文件内存使用
            for chunk_idx, chunk in enumerate(
                    pd.read_csv(input_file, encoding='utf-8', header=None, chunksize=chunk_size)):
                logging.info(f"处理文件块 {chunk_idx + 1}...")
                for idx, row in chunk.iterrows():
                    # Construct a unique row label for logging, incorporating chunk index and original row index
                    # The original 'idx' is 0-based within the chunk.
                    # To get a global row index, we need: chunk_idx * chunk_size + idx (if idx is 0-based for chunk)
                    # However, for user display, 'Row {idx + 1}' within the chunk is often simpler if chunk_size is large.
                    # Let's use a slightly more descriptive label that matches the HDF5 one for consistency.
                    original_row_index_in_file = chunk_idx * chunk_size + idx
                    row_label = f"CSV 文件 '{os.path.basename(input_file)}', 行 {original_row_index_in_file + 1}"

                    # 使用行号作为CSV文件的ID
                    record_id = f"行{original_row_index_in_file + 1}"

                    try:
                        # 数据预处理
                        ecg_data = pd.to_numeric(row, errors='coerce').dropna().tolist()
                        if not ecg_data:
                            logging.warning(f"{row_label}: 数据行为空或无法转换为数值")
                            error_row = {col: None for col in cols}  # 初始化所有列为None
                            error_row.update({
                                'Row': original_row_index_in_file + 1,  # Use original_row_index_in_file
                                'ID': record_id,
                                'Status': '失败',
                                'ErrorMessage': '数据行为空或无效',
                                'First10sSkipped': None
                            })
                            results_list.append(error_row)
                            continue

                        # 添加基础长度检查 (至少需要10秒数据)
                        min_required_samples = sampling_rate * 10
                        if len(ecg_data) < min_required_samples:
                            logging.warning(
                                f"{row_label}: 数据长度不足10秒 ({len(ecg_data)} < {min_required_samples})，跳过")
                            error_row = {col: None for col in cols}
                            error_row.update({
                                'Row': original_row_index_in_file + 1,  # Use original_row_index_in_file
                                'ID': record_id,
                                'Status': '失败',
                                'ErrorMessage': f'数据长度不足10秒({len(ecg_data)}<{min_required_samples})',
                                'First10sSkipped': None
                            })
                            results_list.append(error_row)
                            continue

                        # 直接使用整行数据作为信号
                        ecg_signal = ecg_data
                        logging.info(f"{row_label}: 准备发送 {len(ecg_signal)} 个样本点给 API")

                        # 使用最新的token_value进行API调用
                        current_token = token_value or token

                        # 调用API进行分析
                        result, _ = ecg_analysis(ecg_signal, sampling_rate, current_token)

                        # 处理API结果
                        if result:
                            arrhythmia_diagnosis = result.get('ArrhythmiaDiagnosis', {})
                            diseases = get_disease_name(arrhythmia_diagnosis)
                            multi_label_diagnosis = result.get('MultiLabelDiagnosis', [])
                            chinese_multi_label = get_multi_label_disease_name(multi_label_diagnosis)

                            # 更新日志输出
                            logging.info(f"{row_label} 诊断结果:")
                            logging.info(f"  传统诊断结果: {diseases}")
                            logging.info(f"  多结论诊断结果: {', '.join(chinese_multi_label)}")
                            logging.info(f"    ECG年龄: {result.get('ECGAge')}")
                            pqrstc = result.get('PQRSTC', {})
                            logging.info("  PQRSTC参数:")
                            logging.info(f"    心率(HR): {pqrstc.get('HR', 0)} bpm")
                            qrs_duration = pqrstc.get('QRS_duration', pqrstc.get('QRS_QRSDuration', 0))
                            logging.info(f"    QRS时长: {qrs_duration} ms")

                            signal_quantity = result.get('SignalQuantity')
                            is_noise = result.get('IsNoise', False)
                            noise_message = result.get('NoiseMessage', '')
                            first_10s_skipped = noise_message.startswith("前10秒信号可能为噪音")
                            logging.info("  信号质量信息:")
                            logging.info(f"    信号质量: {signal_quantity}")
                            logging.info(f"    是否噪音: {'是' if is_noise else '否'}")
                            if noise_message:
                                logging.info(f"    噪音信息: {noise_message}")
                            logging.info("-" * 50)

                            new_row = {
                                'Row': original_row_index_in_file + 1,  # Use original_row_index_in_file
                                'ID': record_id,
                                'ECGAge': result.get('ECGAge'),
                                'ArrhythmiaDiagnosis': result.get('ArrhythmiaDiagnosis', {}),
                                'MultiLabelDiagnosis': chinese_multi_label,
                                'HealthMetrics': result.get('HealthMetrics', {}),
                                'PQRSTC': result.get('PQRSTC', {}),
                                'SignalQuantity': signal_quantity,
                                'IsNoise': is_noise,
                                'NoiseMessage': noise_message,
                                'Status': '成功',
                                'Diseases': diseases,
                                'First10sSkipped': first_10s_skipped,
                                'ErrorMessage': ''
                            }
                        else:
                            logging.warning(f"{row_label}: API处理失败")
                            new_row = {col: None for col in cols}
                            new_row.update({
                                'Row': original_row_index_in_file + 1,  # Use original_row_index_in_file
                                'ID': record_id,
                                'Status': '失败',
                                'ErrorMessage': 'API调用失败或返回错误',
                                'First10sSkipped': None
                            })
                        results_list.append(new_row)

                    except Exception as e:
                        logging.error(f"{row_label}: 处理时发生意外错误: {str(e)}")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': original_row_index_in_file + 1,  # Use original_row_index_in_file
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': f'处理错误: {str(e)}',
                            'First10sSkipped': None
                        })
                        results_list.append(error_row)

        except FileNotFoundError:
            logging.error(f"CSV输入文件未找到: {input_file}")
            return pd.DataFrame(columns=cols)
        except pd.errors.EmptyDataError:
            logging.error(f"CSV输入文件为空: {input_file}")
            return pd.DataFrame(columns=cols)
        except Exception as e:
            logging.error(f"读取或处理CSV文件 {input_file} 时发生严重错误: {str(e)}")
            return pd.DataFrame(columns=cols)
    elif file_extension == '.json':
        logging.info(f"开始处理JSON文件: {input_file}")
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                json_data_list = json.load(f)  # Expecting a list of records

            if not isinstance(json_data_list, list):
                logging.error(f"JSON文件 {input_file} 内容不是预期的列表格式。实际类型: {type(json_data_list)}")
                # If it's a single record, wrap it in a list to process it
                if isinstance(json_data_list, dict):
                    logging.info(f"检测到JSON文件为单个记录对象，将作为单元素列表处理。")
                    json_data_list = [json_data_list]
                else:
                    return pd.DataFrame(columns=cols)

            for record_idx, record in enumerate(json_data_list):
                row_label = f"JSON 文件 '{os.path.basename(input_file)}', 记录 {record_idx + 1}"
                record_id = record.get("id", f"记录{record_idx + 1}")
                # study_uid = record.get("study_uid") # Optional: log or include if needed

                try:
                    ecg_data_str = record.get("ecg")
                    ecg_data = []

                    if isinstance(ecg_data_str, str) and ecg_data_str.startswith('[') and ecg_data_str.endswith(']'):
                        ecg_data_str_trimmed = ecg_data_str[1:-1]
                        data_points_str = ecg_data_str_trimmed.split(',')
                        for point_str in data_points_str:
                            try:
                                ecg_data.append(float(point_str.strip()))
                            except ValueError:
                                logging.warning(f"{row_label}: 无法将数据点 '{point_str}' 转换为浮点数，已跳过")
                    else:
                        logging.warning(f"{row_label}: 'ecg_data' 字段不是预期的字符串列表格式或不存在: {ecg_data_str}")

                    if not ecg_data:
                        logging.warning(f"{row_label}: ECG数据为空或无效")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': record_idx + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': 'ECG数据为空或无效'
                        })
                        results_list.append(error_row)
                        continue

                    min_required_samples = sampling_rate * 10
                    if len(ecg_data) < min_required_samples:
                        logging.warning(
                            f"{row_label}: 数据长度不足10秒 ({len(ecg_data)} < {min_required_samples})，跳过")
                        error_row = {col: None for col in cols}
                        error_row.update({
                            'Row': record_idx + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': f'数据长度不足10秒({len(ecg_data)}<{min_required_samples})'
                        })
                        results_list.append(error_row)
                        continue

                    ecg_signal = ecg_data
                    logging.info(f"{row_label}: 准备发送 {len(ecg_signal)} 个样本点给 API")

                    current_token = token_value or token
                    result, _ = ecg_analysis(ecg_signal, sampling_rate, current_token)

                    if result:
                        arrhythmia_diagnosis = result.get('ArrhythmiaDiagnosis', {})
                        diseases = get_disease_name(arrhythmia_diagnosis)
                        multi_label_diagnosis = result.get('MultiLabelDiagnosis', [])
                        chinese_multi_label = get_multi_label_disease_name(multi_label_diagnosis)
                        signal_quantity = result.get('SignalQuantity')
                        is_noise = result.get('IsNoise', False)
                        noise_message = result.get('NoiseMessage', '')
                        first_10s_skipped = noise_message.startswith("前10秒信号可能为噪音")

                        logging.info(f"{row_label} 诊断结果:")
                        logging.info(f"  传统诊断结果: {diseases}")
                        logging.info(
                            f"  多结论诊断结果: {', '.join(chinese_multi_label) if chinese_multi_label else '无'}")
                        # ... (add other relevant logging if needed)

                        new_row = {
                            'Row': record_idx + 1,
                            'ID': record_id,
                            'ECGAge': result.get('ECGAge'),
                            'ArrhythmiaDiagnosis': result.get('ArrhythmiaDiagnosis', {}),
                            'MultiLabelDiagnosis': chinese_multi_label,
                            'HealthMetrics': result.get('HealthMetrics', {}),
                            'PQRSTC': result.get('PQRSTC', {}),
                            'SignalQuantity': signal_quantity,
                            'IsNoise': is_noise,
                            'NoiseMessage': noise_message,
                            'Status': '成功',
                            'Diseases': diseases,
                            'First10sSkipped': first_10s_skipped,
                            'ErrorMessage': ''
                        }
                    else:
                        logging.warning(f"{row_label}: API处理失败")
                        new_row = {col: None for col in cols}
                        new_row.update({
                            'Row': record_idx + 1,
                            'ID': record_id,
                            'Status': '失败',
                            'ErrorMessage': 'API调用失败或返回错误'
                        })
                    results_list.append(new_row)

                except Exception as e:
                    logging.error(f"{row_label}: 处理单条JSON记录时发生意外错误: {str(e)}")
                    error_row = {col: None for col in cols}
                    error_row.update({
                        'Row': record_idx + 1,
                        'ID': record_id,
                        'Status': '失败',
                        'ErrorMessage': f'处理JSON记录错误: {str(e)}'
                    })
                    results_list.append(error_row)

        except FileNotFoundError:
            logging.error(f"JSON输入文件未找到: {input_file}")
            return pd.DataFrame(columns=cols)
        except json.JSONDecodeError as e:
            logging.error(f"解析JSON文件 {input_file} 失败: {str(e)}")
            return pd.DataFrame(columns=cols)
        except Exception as e:
            logging.error(f"读取或处理JSON文件 {input_file} 时发生严重错误: {str(e)}")
            return pd.DataFrame(columns=cols)

    else:
        logging.error(f"不支持的文件类型: {file_extension} (来自文件: {input_file})")
        return pd.DataFrame(columns=cols)

    # 转换为DataFrame并返回 (common to all paths that produce results_list)
    if not results_list:
        logging.warning("没有成功处理任何数据行")
        return pd.DataFrame(columns=cols)  # 确保返回带列名的空表

    df = pd.DataFrame(results_list)
    # 更新列顺序以包含新字段
    cols = [
        'Row', 'ID', 'Status', 'First10sSkipped', 'Diseases', 'MultiLabelDiagnosis', 'ECGAge',
        'ArrhythmiaDiagnosis', 'PQRSTC', 'HealthMetrics',
        # Signal quality相关列
        'SignalQuantity', 'IsNoise', 'NoiseMessage',
        'ErrorMessage'
    ]
    df = df.reindex(columns=cols)

    # 打印成功率统计
    total = len(df)
    success = len(df[df['Status'] == '成功'])
    noise_count = len(df[df['IsNoise'] == True])
    logging.info(f"\n处理统计:")
    logging.info(f"总数: {total}")
    logging.info(f"成功: {success}")
    logging.info(f"失败: {total - success}")
    logging.info(f"噪音信号数: {noise_count}")
    if total > 0:
        logging.info(f"成功率: {(success / total * 100):.2f}%")
        logging.info(f"噪音比例: {(noise_count / total * 100):.2f}%")
    else:
        logging.info("成功率: N/A")

    return df


def main():
    # 创建主性能分析器
    main_profiler = cProfile.Profile()  # Re-enabled
    main_profiler.enable()  # Re-enabled

    try:
        # 获取命令行参数
        args = parse_arguments()

        # 如果没有命令行参数，使用交互式输入
        if args.mode is None:
            mode = input("请选择处理模式（1: 单个文件，2: 整个文件夹）：")
            mode = int(mode)
        else:
            mode = args.mode

        # 根据是否启用调试模式设置日志级别
        if args.debug:
            logging.getLogger().setLevel(logging.DEBUG)
            logging.info("已启用调试模式")
        else:
            logging.getLogger().setLevel(logging.INFO)

        # 添加错误代码说明
        logging.info("错误代码说明:")
        for code, message in ERROR_CODE_MAP.items():
            logging.info(f"  - 代码 {code}: {message}")

        # 使用固定Sampling rate500Hz
        sampling_rate = 250
        logging.info(f"使用固定采样率: {sampling_rate} Hz")
        token = get_token(force_refresh=True)  # 强制刷新获取新token
        if not token:
            logging.error("无法获取到有效的token，程序结束")
            sys.exit(1)

        # 记录开始时间，用于定期检查token是否需要刷新
        process_start_time = time.time()

        if mode == 1:
            input_file = args.input or input("请输入要处理的CSV文件完整路径：").strip('"')
            if not os.path.exists(input_file):
                logging.error("文件不存在！")
                sys.exit(1)

            # 检测文件类型并输出相关信息
            file_extension = os.path.splitext(input_file)[1].lower()
            if file_extension in ['.h5', '.hdf5']:
                logging.info(f"检测到HDF5文件: {input_file}")
                # 尝试预览HDF5文件结构
                try:
                    with h5py.File(input_file, 'r') as h5f:
                        # 输出HDF5文件的顶级结构
                        logging.info("HDF5文件结构预览:")
                        for key in h5f.keys():
                            item = h5f[key]
                            if isinstance(item, h5py.Dataset):
                                logging.info(f"  - 数据集: {key}, 形状: {item.shape}, 类型: {item.dtype}")
                            elif isinstance(item, h5py.Group):
                                logging.info(f"  - 组: {key}")
                except Exception as e:
                    logging.error(f"预览HDF5文件结构时出错: {str(e)}")

            output_folder = os.path.dirname(input_file)
            file_name = os.path.basename(input_file)
            output_file_name = f"{os.path.splitext(file_name)[0]}_acc.csv"
            output_file_path = os.path.join(output_folder, output_file_name)

            results_df = process_single_file(input_file, sampling_rate, token)
            results_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            logging.info(f"分析结果已保存到: {output_file_path}")

        elif mode == 2:
            input_folder = args.input or input("请输入要处理的文件夹路径：").strip('"')
            output_folder = args.output or input("请输入结果保存的文件夹路径：").strip('"')

            if not os.path.exists(input_folder):
                logging.error("输入文件夹不存在！")
                sys.exit(1)

            if not os.path.exists(output_folder):
                os.makedirs(output_folder)

            supported_extensions = ('.csv', '.h5', '.hdf5', '.json')
            all_files = [f for f in os.listdir(input_folder) if f.lower().endswith(supported_extensions)]

            # 按文件类型分组统计
            file_type_count = {}
            for ext in supported_extensions:
                count = len([f for f in all_files if f.lower().endswith(ext)])
                if count > 0:
                    file_type_count[ext] = count

            logging.info(f"在文件夹 '{input_folder}' 中找到以下支持的文件：")
            for ext, count in file_type_count.items():
                logging.info(f"  - {ext} 文件: {count} 个")
            logging.info(f"总文件数: {len(all_files)}")

            if not all_files:
                logging.warning(f"在文件夹 '{input_folder}' 中没有找到支持的文件（{', '.join(supported_extensions)}）。")
                sys.exit(0)  # Graceful exit if no files to process

            # === 新增全局统计变量 ===
            total_count = 0
            success_count = 0
            fail_count = 0
            noise_count = 0

            for data_file in all_files:
                input_file_path = os.path.join(input_folder, data_file)
                output_file_name = f"{os.path.splitext(data_file)[0]}_acc.csv"
                output_file_path = os.path.join(output_folder, output_file_name)

                # 每个文件处理前检查token是否需要刷新
                current_time = time.time()
                if current_time - token_obtain_time > TOKEN_REFRESH_THRESHOLD:
                    logging.info("Token即将过期，尝试刷新")
                    new_token = get_token(force_refresh=True)
                    if new_token:
                        token = new_token

                logging.info(f"开始处理文件：{data_file} (保存到: {output_file_path})")
                results_df = process_single_file(input_file_path, sampling_rate, token)
                results_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
                logging.info(f"文件 {data_file} 的分析结果已保存到: {output_file_path}")

                # === 累加统计 ===
                file_total = len(results_df)
                file_success = len(results_df[results_df['Status'] == '成功'])
                file_fail = file_total - file_success
                file_noise = len(results_df[results_df['IsNoise'] == True])
                total_count += file_total
                success_count += file_success
                fail_count += file_fail
                noise_count += file_noise

            # === 所有文件处理完后统一输出统计 ===
            logging.info("\n文件夹整体处理统计:")
            logging.info(f"总数: {total_count}")
            logging.info(f"成功: {success_count}")
            logging.info(f"失败: {fail_count}")
            logging.info(f"噪音信号数: {noise_count}")
            if total_count > 0:
                logging.info(f"成功率: {(success_count / total_count * 100):.2f}%")
                logging.info(f"噪音比例: {(noise_count / total_count * 100):.2f}%")
            else:
                logging.info("成功率: N/A")

        else:
            logging.error("无效的选择！")
            sys.exit(1)

    finally:
        main_profiler.disable()  # Re-enabled

        # 创建Stats对象并提取摘要信息
        s = io.StringIO()
        stats = pstats.Stats(main_profiler, stream=s)
        stats.sort_stats(SortKey.TIME)
        # stats.print_stats() # Keep this commented if only summary is needed for logging

        # 从 stats 对象中提取所需信息
        # 注意: pstats 的内部结构可能变化，这里是基于常见属性的访问方式
        total_calls = stats.total_calls
        primitive_calls = stats.prim_calls
        total_tt = stats.total_tt

        logging.info(
            f"性能摘要: {total_calls} function calls ({primitive_calls} primitive calls) in {total_tt:.3f} seconds")

        # 保存完整的统计信息到文件 (可选)
        stats_file = "api_test_performance.txt"  # Re-enabled
        with open(stats_file, 'w', encoding='utf-8') as f:  # Re-enabled
            # 重新创建stats对象并将流指向文件
            stats_to_file = pstats.Stats(main_profiler, stream=f)  # Re-enabled
            stats_to_file.sort_stats(SortKey.TIME)  # Re-enabled
            stats_to_file.print_stats()  # Re-enabled

        logging.info(f"完整的性能分析结果已保存到: {stats_file}")  # Re-enabled
        logging.info("处理完成。")  # 更改为中文的完成消息


if __name__ == '__main__':
    main()