[unix_http_server]
file = /tmp/supervisord/supervisor.sock

[supervisorctl]
serverurl = unix:///tmp/supervisord/supervisor.sock


[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisord]
nodaemon=true
logfile=%(ENV_HOME)s/logs/supervisord.log ; supervisord log file
logfile_maxbytes=50MB       ; maximum size of logfile before rotation
logfile_backups=10          ; number of backed up logfiles
loglevel=debug              ; info, debug, warn, trace
pidfile=/tmp/supervisord/supervisord.pid ; pidfile location

[program:clamav_update]
startsecs=0
command=bash -c "if [ \"${CLAM_AV}\" = 'yes' ]; then /usr/bin/freshclam -d \
    -l %(ENV_HOME)s/logs/freshclam.log --foreground=true; fi"

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
startretries=5
numprocs=1
process_name=%(program_name)s-%(process_num)d

[fcgi-program:uvicorn]
socket=unix:///tmp/uvicorn.sock
command=%(ENV_HOME)s/wait_for_deps.sh
    python3 -m uvicorn
        --fd 0
        --forwarded-allow-ips='*'
        cvat.asgi:application
autorestart=true
environment=CVAT_EVENTS_LOCAL_DB_FILENAME="events_%(process_num)03d.db",CVAT_POSTGRES_APPLICATION_NAME="cvat:server"
numprocs=%(ENV_NUMPROCS)s
process_name=%(program_name)s-%(process_num)d

[program:smokescreen]
command=smokescreen --listen-ip=127.0.0.1 %(ENV_SMOKESCREEN_OPTS)s
