# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>, 2011
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# zod<PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-12-26 16:57+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Mexico) (http://www.transifex.com/django/django/"
"language/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es_MX\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Redirección"

msgid "site"
msgstr "sitio"

msgid "redirect from"
msgstr "redirigir desde"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Esta debería ser una ruta absoluta, excluyendo el nombre de dominio. Por "
"ejemplo: \"/events/search/\"."

msgid "redirect to"
msgstr "redirigir a"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with "
"“http://”."
msgstr ""
"Esto puede ser una ruta absoluta (como arriba) o una URL completa que "
"comience con \"http://\"."

msgid "redirect"
msgstr "redirigir"

msgid "redirects"
msgstr "redirecciona"
