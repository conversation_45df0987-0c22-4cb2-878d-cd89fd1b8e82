---
title: 'Shape mode (advanced)'
linkTitle: 'Shape mode'
weight: 3
description: 'Advanced operations available during annotation in shape mode.'
---

Basic operations in the mode were described in section
{{< ilink "/docs/manual/basics/shape-mode-basics" "shape mode (basics)" >}}.

**Occluded**
Occlusion is an attribute used if an object is occluded by another object or
isn't fully visible on the frame. Use `Q` shortcut to set the property
quickly.

![](/images/image065.jpg)

Example: the three cars on the figure below should be labeled as **occluded**.

![](/images/image054_mapillary_vistas.jpg)

If a frame contains too many objects and it is difficult to annotate them
due to many shapes placed mostly in the same place, it makes sense
to lock them. Shapes for locked objects are transparent, and it is easy to
annotate new objects. Besides, you can't change previously annotated objects
by accident. Shortcut: `L`.

![](/images/image066.jpg)
