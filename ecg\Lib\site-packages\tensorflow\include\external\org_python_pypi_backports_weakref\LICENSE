.. highlightlang:: none

.. _history-and-license:

*******************
History and License
*******************


History of the software
=======================

Python was created in the early 1990s by <PERSON> at Stichting
Mathematisch Centrum (CWI, see https://www.cwi.nl/) in the Netherlands as a
successor of a language called ABC.  <PERSON> remains Python's principal author,
although it includes many contributions from others.

In 1995, <PERSON> continued his work on Python at the Corporation for National
Research Initiatives (CNRI, see https://www.cnri.reston.va.us/) in Reston,
Virginia where he released several versions of the software.

In May 2000, <PERSON> and the Python core development team moved to BeOpen.com to
form the BeOpen PythonLabs team.  In October of the same year, the PythonLabs
team moved to Digital Creations (now Zope Corporation; see
http://www.zope.com/).  In 2001, the Python Software Foundation (PSF, see
https://www.python.org/psf/) was formed, a non-profit organization created
specifically to own Python-related Intellectual Property.  Zope Corporation is a
sponsoring member of the PSF.

All Python releases are Open Source (see https://opensource.org/ for the Open
Source Definition). Historically, most, but not all, Python releases have also
been GPL-compatible; the table below summarizes the various releases.

+----------------+--------------+-----------+------------+-----------------+
| Release        | Derived from | Year      | Owner      | GPL compatible? |
+================+==============+===========+============+=================+
| 0.9.0 thru 1.2 | n/a          | 1991-1995 | CWI        | yes             |
+----------------+--------------+-----------+------------+-----------------+
| 1.3 thru 1.5.2 | 1.2          | 1995-1999 | CNRI       | yes             |
+----------------+--------------+-----------+------------+-----------------+
| 1.6            | 1.5.2        | 2000      | CNRI       | no              |
+----------------+--------------+-----------+------------+-----------------+
| 2.0            | 1.6          | 2000      | BeOpen.com | no              |
+----------------+--------------+-----------+------------+-----------------+
| 1.6.1          | 1.6          | 2001      | CNRI       | no              |
+----------------+--------------+-----------+------------+-----------------+
| 2.1            | 2.0+1.6.1    | 2001      | PSF        | no              |
+----------------+--------------+-----------+------------+-----------------+
| 2.0.1          | 2.0+1.6.1    | 2001      | PSF        | yes             |
+----------------+--------------+-----------+------------+-----------------+
| 2.1.1          | 2.1+2.0.1    | 2001      | PSF        | yes             |
+----------------+--------------+-----------+------------+-----------------+
| 2.1.2          | 2.1.1        | 2002      | PSF        | yes             |
+----------------+--------------+-----------+------------+-----------------+
| 2.1.3          | 2.1.2        | 2002      | PSF        | yes             |
+----------------+--------------+-----------+------------+-----------------+
| 2.2 and above  | 2.1.1        | 2001-now  | PSF        | yes             |
+----------------+--------------+-----------+------------+-----------------+

.. note::

   GPL-compatible doesn't mean that we're distributing Python under the GPL.  All
   Python licenses, unlike the GPL, let you distribute a modified version without
   making your changes open source. The GPL-compatible licenses make it possible to
   combine Python with other software that is released under the GPL; the others
   don't.

Thanks to the many outside volunteers who have worked under Guido's direction to
make these releases possible.


Terms and conditions for accessing or otherwise using Python
============================================================


PSF LICENSE AGREEMENT FOR PYTHON |release|
------------------------------------------

.. parsed-literal::

   1. This LICENSE AGREEMENT is between the Python Software Foundation ("PSF"), and
      the Individual or Organization ("Licensee") accessing and otherwise using Python
      |release| software in source or binary form and its associated documentation.

   2. Subject to the terms and conditions of this License Agreement, PSF hereby
      grants Licensee a nonexclusive, royalty-free, world-wide license to reproduce,
      analyze, test, perform and/or display publicly, prepare derivative works,
      distribute, and otherwise use Python |release| alone or in any derivative
      version, provided, however, that PSF's License Agreement and PSF's notice of
      copyright, i.e., "Copyright © 2001-2019 Python Software Foundation; All Rights
      Reserved" are retained in Python |release| alone or in any derivative version
      prepared by Licensee.

   3. In the event Licensee prepares a derivative work that is based on or
      incorporates Python |release| or any part thereof, and wants to make the
      derivative work available to others as provided herein, then Licensee hereby
      agrees to include in any such work a brief summary of the changes made to Python
      |release|.

   4. PSF is making Python |release| available to Licensee on an "AS IS" basis.
      PSF MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED.  BY WAY OF
      EXAMPLE, BUT NOT LIMITATION, PSF MAKES NO AND DISCLAIMS ANY REPRESENTATION OR
      WARRANTY OF MERCHANTABILITY OR FITNESS FOR ANY PARTICULAR PURPOSE OR THAT THE
      USE OF PYTHON |release| WILL NOT INFRINGE ANY THIRD PARTY RIGHTS.

   5. PSF SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF PYTHON |release|
      FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS AS A RESULT OF
      MODIFYING, DISTRIBUTING, OR OTHERWISE USING PYTHON |release|, OR ANY DERIVATIVE
      THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.

   6. This License Agreement will automatically terminate upon a material breach of
      its terms and conditions.

   7. Nothing in this License Agreement shall be deemed to create any relationship
      of agency, partnership, or joint venture between PSF and Licensee.  This License
      Agreement does not grant permission to use PSF trademarks or trade name in a
      trademark sense to endorse or promote products or services of Licensee, or any
      third party.

   8. By copying, installing or otherwise using Python |release|, Licensee agrees
      to be bound by the terms and conditions of this License Agreement.


BEOPEN.COM LICENSE AGREEMENT FOR PYTHON 2.0
-------------------------------------------

BEOPEN PYTHON OPEN SOURCE LICENSE AGREEMENT VERSION 1

.. parsed-literal::

   1. This LICENSE AGREEMENT is between BeOpen.com ("BeOpen"), having an office at
      160 Saratoga Avenue, Santa Clara, CA 95051, and the Individual or Organization
      ("Licensee") accessing and otherwise using this software in source or binary
      form and its associated documentation ("the Software").

   2. Subject to the terms and conditions of this BeOpen Python License Agreement,
      BeOpen hereby grants Licensee a non-exclusive, royalty-free, world-wide license
      to reproduce, analyze, test, perform and/or display publicly, prepare derivative
      works, distribute, and otherwise use the Software alone or in any derivative
      version, provided, however, that the BeOpen Python License is retained in the
      Software, alone or in any derivative version prepared by Licensee.

   3. BeOpen is making the Software available to Licensee on an "AS IS" basis.
      BEOPEN MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED.  BY WAY OF
      EXAMPLE, BUT NOT LIMITATION, BEOPEN MAKES NO AND DISCLAIMS ANY REPRESENTATION OR
      WARRANTY OF MERCHANTABILITY OR FITNESS FOR ANY PARTICULAR PURPOSE OR THAT THE
      USE OF THE SOFTWARE WILL NOT INFRINGE ANY THIRD PARTY RIGHTS.

   4. BEOPEN SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF THE SOFTWARE FOR
      ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS AS A RESULT OF USING,
      MODIFYING OR DISTRIBUTING THE SOFTWARE, OR ANY DERIVATIVE THEREOF, EVEN IF
      ADVISED OF THE POSSIBILITY THEREOF.

   5. This License Agreement will automatically terminate upon a material breach of
      its terms and conditions.

   6. This License Agreement shall be governed by and interpreted in all respects
      by the law of the State of California, excluding conflict of law provisions.
      Nothing in this License Agreement shall be deemed to create any relationship of
      agency, partnership, or joint venture between BeOpen and Licensee.  This License
      Agreement does not grant permission to use BeOpen trademarks or trade names in a
      trademark sense to endorse or promote products or services of Licensee, or any
      third party.  As an exception, the "BeOpen Python" logos available at
      http://www.pythonlabs.com/logos.html may be used according to the permissions
      granted on that web page.

   7. By copying, installing or otherwise using the software, Licensee agrees to be
      bound by the terms and conditions of this License Agreement.


CNRI LICENSE AGREEMENT FOR PYTHON 1.6.1
---------------------------------------

.. parsed-literal::

   1. This LICENSE AGREEMENT is between the Corporation for National Research
      Initiatives, having an office at 1895 Preston White Drive, Reston, VA 20191
      ("CNRI"), and the Individual or Organization ("Licensee") accessing and
      otherwise using Python 1.6.1 software in source or binary form and its
      associated documentation.

   2. Subject to the terms and conditions of this License Agreement, CNRI hereby
      grants Licensee a nonexclusive, royalty-free, world-wide license to reproduce,
      analyze, test, perform and/or display publicly, prepare derivative works,
      distribute, and otherwise use Python 1.6.1 alone or in any derivative version,
      provided, however, that CNRI's License Agreement and CNRI's notice of copyright,
      i.e., "Copyright © 1995-2001 Corporation for National Research Initiatives; All
      Rights Reserved" are retained in Python 1.6.1 alone or in any derivative version
      prepared by Licensee.  Alternately, in lieu of CNRI's License Agreement,
      Licensee may substitute the following text (omitting the quotes): "Python 1.6.1
      is made available subject to the terms and conditions in CNRI's License
      Agreement.  This Agreement together with Python 1.6.1 may be located on the
      Internet using the following unique, persistent identifier (known as a handle):
      1895.22/1013.  This Agreement may also be obtained from a proxy server on the
      Internet using the following URL: http://hdl.handle.net/1895.22/1013."

   3. In the event Licensee prepares a derivative work that is based on or
      incorporates Python 1.6.1 or any part thereof, and wants to make the derivative
      work available to others as provided herein, then Licensee hereby agrees to
      include in any such work a brief summary of the changes made to Python 1.6.1.

   4. CNRI is making Python 1.6.1 available to Licensee on an "AS IS" basis.  CNRI
      MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED.  BY WAY OF EXAMPLE,
      BUT NOT LIMITATION, CNRI MAKES NO AND DISCLAIMS ANY REPRESENTATION OR WARRANTY
      OF MERCHANTABILITY OR FITNESS FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF
      PYTHON 1.6.1 WILL NOT INFRINGE ANY THIRD PARTY RIGHTS.

   5. CNRI SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF PYTHON 1.6.1 FOR
      ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS AS A RESULT OF
      MODIFYING, DISTRIBUTING, OR OTHERWISE USING PYTHON 1.6.1, OR ANY DERIVATIVE
      THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.

   6. This License Agreement will automatically terminate upon a material breach of
      its terms and conditions.

   7. This License Agreement shall be governed by the federal intellectual property
      law of the United States, including without limitation the federal copyright
      law, and, to the extent such U.S. federal law does not apply, by the law of the
      Commonwealth of Virginia, excluding Virginia's conflict of law provisions.
      Notwithstanding the foregoing, with regard to derivative works based on Python
      1.6.1 that incorporate non-separable material that was previously distributed
      under the GNU General Public License (GPL), the law of the Commonwealth of
      Virginia shall govern this License Agreement only as to issues arising under or
      with respect to Paragraphs 4, 5, and 7 of this License Agreement.  Nothing in
      this License Agreement shall be deemed to create any relationship of agency,
      partnership, or joint venture between CNRI and Licensee.  This License Agreement
      does not grant permission to use CNRI trademarks or trade name in a trademark
      sense to endorse or promote products or services of Licensee, or any third
      party.

   8. By clicking on the "ACCEPT" button where indicated, or by copying, installing
      or otherwise using Python 1.6.1, Licensee agrees to be bound by the terms and
      conditions of this License Agreement.


CWI LICENSE AGREEMENT FOR PYTHON 0.9.0 THROUGH 1.2
--------------------------------------------------

.. parsed-literal::

   Copyright © 1991 - 1995, Stichting Mathematisch Centrum Amsterdam, The
   Netherlands.  All rights reserved.

   Permission to use, copy, modify, and distribute this software and its
   documentation for any purpose and without fee is hereby granted, provided that
   the above copyright notice appear in all copies and that both that copyright
   notice and this permission notice appear in supporting documentation, and that
   the name of Stichting Mathematisch Centrum or CWI not be used in advertising or
   publicity pertaining to distribution of the software without specific, written
   prior permission.

   STICHTING MATHEMATISCH CENTRUM DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS
   SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
   EVENT SHALL STICHTING MATHEMATISCH CENTRUM BE LIABLE FOR ANY SPECIAL, INDIRECT
   OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
   DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS
   ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS
   SOFTWARE.


Licenses and Acknowledgements for Incorporated Software
=======================================================

This section is an incomplete, but growing list of licenses and acknowledgements
for third-party software incorporated in the Python distribution.


Mersenne Twister
----------------

The :mod:`_random` module includes code based on a download from
http://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/MT2002/emt19937ar.html. The following are
the verbatim comments from the original code::

   A C-program for MT19937, with initialization improved 2002/1/26.
   Coded by Takuji Nishimura and Makoto Matsumoto.

   Before using, initialize the state by using init_genrand(seed)
   or init_by_array(init_key, key_length).

   Copyright (C) 1997 - 2002, Makoto Matsumoto and Takuji Nishimura,
   All rights reserved.

   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions
   are met:

    1. Redistributions of source code must retain the above copyright
       notice, this list of conditions and the following disclaimer.

    2. Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.

    3. The names of its contributors may not be used to endorse or promote
       products derived from this software without specific prior written
       permission.

   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
   "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
   LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
   A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
   CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
   EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
   PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
   PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
   LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
   NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
   SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.


   Any feedback is very welcome.
   http://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/emt.html
   email: m-mat @ math.sci.hiroshima-u.ac.jp (remove space)


Sockets
-------

The :mod:`socket` module uses the functions, :func:`getaddrinfo`, and
:func:`getnameinfo`, which are coded in separate source files from the WIDE
Project, http://www.wide.ad.jp/. ::

   Copyright (C) 1995, 1996, 1997, and 1998 WIDE Project.
   All rights reserved.

   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions
   are met:
   1. Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
   2. Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.
   3. Neither the name of the project nor the names of its contributors
      may be used to endorse or promote products derived from this software
      without specific prior written permission.

   THIS SOFTWARE IS PROVIDED BY THE PROJECT AND CONTRIBUTORS ``AS IS'' AND
   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
   IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
   ARE DISCLAIMED.  IN NO EVENT SHALL THE PROJECT OR CONTRIBUTORS BE LIABLE
   FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
   DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
   OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
   HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
   LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
   OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
   SUCH DAMAGE.

Floating point exception control
--------------------------------

The source for the :mod:`fpectl` module includes the following notice::

     ---------------------------------------------------------------------
    /                       Copyright (c) 1996.                           \
   |          The Regents of the University of California.                 |
   |                        All rights reserved.                           |
   |                                                                       |
   |   Permission to use, copy, modify, and distribute this software for   |
   |   any purpose without fee is hereby granted, provided that this en-   |
   |   tire notice is included in all copies of any software which is or   |
   |   includes  a  copy  or  modification  of  this software and in all   |
   |   copies of the supporting documentation for such software.           |
   |                                                                       |
   |   This  work was produced at the University of California, Lawrence   |
   |   Livermore National Laboratory under  contract  no.  W-7405-ENG-48   |
   |   between  the  U.S.  Department  of  Energy and The Regents of the   |
   |   University of California for the operation of UC LLNL.              |
   |                                                                       |
   |                              DISCLAIMER                               |
   |                                                                       |
   |   This  software was prepared as an account of work sponsored by an   |
   |   agency of the United States Government. Neither the United States   |
   |   Government  nor the University of California nor any of their em-   |
   |   ployees, makes any warranty, express or implied, or  assumes  any   |
   |   liability  or  responsibility  for the accuracy, completeness, or   |
   |   usefulness of any information,  apparatus,  product,  or  process   |
   |   disclosed,   or  represents  that  its  use  would  not  infringe   |
   |   privately-owned rights. Reference herein to any specific  commer-   |
   |   cial  products,  process,  or  service  by trade name, trademark,   |
   |   manufacturer, or otherwise, does not  necessarily  constitute  or   |
   |   imply  its endorsement, recommendation, or favoring by the United   |
   |   States Government or the University of California. The views  and   |
   |   opinions  of authors expressed herein do not necessarily state or   |
   |   reflect those of the United States Government or  the  University   |
   |   of  California,  and shall not be used for advertising or product   |
    \  endorsement purposes.                                              /
     ---------------------------------------------------------------------


MD5 message digest algorithm
----------------------------

The source code for the :mod:`md5` module contains the following notice::

     Copyright (C) 1999, 2002 Aladdin Enterprises.  All rights reserved.

     This software is provided 'as-is', without any express or implied
     warranty.  In no event will the authors be held liable for any damages
     arising from the use of this software.

     Permission is granted to anyone to use this software for any purpose,
     including commercial applications, and to alter it and redistribute it
     freely, subject to the following restrictions:

     1. The origin of this software must not be misrepresented; you must not
        claim that you wrote the original software. If you use this software
        in a product, an acknowledgment in the product documentation would be
        appreciated but is not required.
     2. Altered source versions must be plainly marked as such, and must not be
        misrepresented as being the original software.
     3. This notice may not be removed or altered from any source distribution.

     L. Peter Deutsch
     <EMAIL>

     Independent implementation of MD5 (RFC 1321).

     This code implements the MD5 Algorithm defined in RFC 1321, whose
     text is available at
           http://www.ietf.org/rfc/rfc1321.txt
     The code is derived from the text of the RFC, including the test suite
     (section A.5) but excluding the rest of Appendix A.  It does not include
     any code or documentation that is identified in the RFC as being
     copyrighted.

     The original and principal author of md5.h is L. Peter Deutsch
     <<EMAIL>>.  Other authors are noted in the change history
     that follows (in reverse chronological order):

     2002-04-13 lpd Removed support for non-ANSI compilers; removed
           references to Ghostscript; clarified derivation from RFC 1321;
           now handles byte order either statically or dynamically.
     1999-11-04 lpd Edited comments slightly for automatic TOC extraction.
     1999-10-18 lpd Fixed typo in header comment (ansi2knr rather than md5);
           added conditionalization for C++ compilation from Martin
           Purschke <<EMAIL>>.
     1999-05-03 lpd Original version.


Asynchronous socket services
----------------------------

The :mod:`asynchat` and :mod:`asyncore` modules contain the following notice::

   Copyright 1996 by Sam Rushing

                           All Rights Reserved

   Permission to use, copy, modify, and distribute this software and
   its documentation for any purpose and without fee is hereby
   granted, provided that the above copyright notice appear in all
   copies and that both that copyright notice and this permission
   notice appear in supporting documentation, and that the name of Sam
   Rushing not be used in advertising or publicity pertaining to
   distribution of the software without specific, written prior
   permission.

   SAM RUSHING DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
   INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN
   NO EVENT SHALL SAM RUSHING BE LIABLE FOR ANY SPECIAL, INDIRECT OR
   CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS
   OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT,
   NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
   CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.


Cookie management
-----------------

The :mod:`Cookie` module contains the following notice::

   Copyright 2000 by Timothy O'Malley <<EMAIL>>

                  All Rights Reserved

   Permission to use, copy, modify, and distribute this software
   and its documentation for any purpose and without fee is hereby
   granted, provided that the above copyright notice appear in all
   copies and that both that copyright notice and this permission
   notice appear in supporting documentation, and that the name of
   Timothy O'Malley  not be used in advertising or publicity
   pertaining to distribution of the software without specific, written
   prior permission.

   Timothy O'Malley DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS
   SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
   AND FITNESS, IN NO EVENT SHALL Timothy O'Malley BE LIABLE FOR
   ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
   WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS
   ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
   PERFORMANCE OF THIS SOFTWARE.


Execution tracing
-----------------

The :mod:`trace` module contains the following notice::

   portions copyright 2001, Autonomous Zones Industries, Inc., all rights...
   err...  reserved and offered to the public under the terms of the
   Python 2.2 license.
   Author: Zooko O'Whielacronx
   http://zooko.com/
   mailto:<EMAIL>

   Copyright 2000, Mojam Media, Inc., all rights reserved.
   Author: Skip Montanaro

   Copyright 1999, Bioreason, Inc., all rights reserved.
   Author: Andrew Dalke

   Copyright 1995-1997, Automatrix, Inc., all rights reserved.
   Author: Skip Montanaro

   Copyright 1991-1995, Stichting Mathematisch Centrum, all rights reserved.


   Permission to use, copy, modify, and distribute this Python software and
   its associated documentation for any purpose without fee is hereby
   granted, provided that the above copyright notice appears in all copies,
   and that both that copyright notice and this permission notice appear in
   supporting documentation, and that the name of neither Automatrix,
   Bioreason or Mojam Media be used in advertising or publicity pertaining to
   distribution of the software without specific, written prior permission.


UUencode and UUdecode functions
-------------------------------

The :mod:`uu` module contains the following notice::

   Copyright 1994 by Lance Ellinghouse
   Cathedral City, California Republic, United States of America.
                          All Rights Reserved
   Permission to use, copy, modify, and distribute this software and its
   documentation for any purpose and without fee is hereby granted,
   provided that the above copyright notice appear in all copies and that
   both that copyright notice and this permission notice appear in
   supporting documentation, and that the name of Lance Ellinghouse
   not be used in advertising or publicity pertaining to distribution
   of the software without specific, written prior permission.
   LANCE ELLINGHOUSE DISCLAIMS ALL WARRANTIES WITH REGARD TO
   THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
   FITNESS, IN NO EVENT SHALL LANCE ELLINGHOUSE CENTRUM BE LIABLE
   FOR ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT
   OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

   Modified by Jack Jansen, CWI, July 1995:
   - Use binascii module to do the actual line-by-line conversion
     between ascii and binary. This results in a 1000-fold speedup. The C
     version is still 5 times faster, though.
   - Arguments more compliant with Python standard


XML Remote Procedure Calls
--------------------------

The :mod:`xmlrpclib` module contains the following notice::

       The XML-RPC client interface is

   Copyright (c) 1999-2002 by Secret Labs AB
   Copyright (c) 1999-2002 by Fredrik Lundh

   By obtaining, using, and/or copying this software and/or its
   associated documentation, you agree that you have read, understood,
   and will comply with the following terms and conditions:

   Permission to use, copy, modify, and distribute this software and
   its associated documentation for any purpose and without fee is
   hereby granted, provided that the above copyright notice appears in
   all copies, and that both that copyright notice and this permission
   notice appear in supporting documentation, and that the name of
   Secret Labs AB or the author not be used in advertising or publicity
   pertaining to distribution of the software without specific, written
   prior permission.

   SECRET LABS AB AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD
   TO THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANT-
   ABILITY AND FITNESS.  IN NO EVENT SHALL SECRET LABS AB OR THE AUTHOR
   BE LIABLE FOR ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY
   DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
   WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS
   ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE
   OF THIS SOFTWARE.


test_epoll
----------

The :mod:`test_epoll` contains the following notice::

  Copyright (c) 2001-2006 Twisted Matrix Laboratories.

  Permission is hereby granted, free of charge, to any person obtaining
  a copy of this software and associated documentation files (the
  "Software"), to deal in the Software without restriction, including
  without limitation the rights to use, copy, modify, merge, publish,
  distribute, sublicense, and/or sell copies of the Software, and to
  permit persons to whom the Software is furnished to do so, subject to
  the following conditions:

  The above copyright notice and this permission notice shall be
  included in all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
  LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
  OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
  WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Select kqueue
-------------

The :mod:`select` and contains the following notice for the kqueue interface::

  Copyright (c) 2000 Doug White, 2006 James Knight, 2007 Christian Heimes
  All rights reserved.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:
  1. Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.

  THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
  ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
  SUCH DAMAGE.


strtod and dtoa
---------------

The file :file:`Python/dtoa.c`, which supplies C functions dtoa and
strtod for conversion of C doubles to and from strings, is derived
from the file of the same name by David M. Gay, currently available
from http://www.netlib.org/fp/.  The original file, as retrieved on
March 16, 2009, contains the following copyright and licensing
notice::

   /****************************************************************
    *
    * The author of this software is David M. Gay.
    *
    * Copyright (c) 1991, 2000, 2001 by Lucent Technologies.
    *
    * Permission to use, copy, modify, and distribute this software for any
    * purpose without fee is hereby granted, provided that this entire notice
    * is included in all copies of any software which is or includes a copy
    * or modification of this software and in all copies of the supporting
    * documentation for such software.
    *
    * THIS SOFTWARE IS BEING PROVIDED "AS IS", WITHOUT ANY EXPRESS OR IMPLIED
    * WARRANTY.  IN PARTICULAR, NEITHER THE AUTHOR NOR LUCENT MAKES ANY
    * REPRESENTATION OR WARRANTY OF ANY KIND CONCERNING THE MERCHANTABILITY
    * OF THIS SOFTWARE OR ITS FITNESS FOR ANY PARTICULAR PURPOSE.
    *
    ***************************************************************/


OpenSSL
-------

The modules :mod:`hashlib`, :mod:`posix`, :mod:`ssl`, :mod:`crypt` use
the OpenSSL library for added performance if made available by the
operating system. Additionally, the Windows and Mac OS X installers for
Python may include a copy of the OpenSSL libraries, so we include a copy
of the OpenSSL license here::


  LICENSE ISSUES
  ==============

  The OpenSSL toolkit stays under a dual license, i.e. both the conditions of
  the OpenSSL License and the original SSLeay license apply to the toolkit.
  See below for the actual license texts. Actually both licenses are BSD-style
  Open Source licenses. In case of any license issues related to OpenSSL
  <NAME_EMAIL>.

  OpenSSL License
  ---------------

    /* ====================================================================
     * Copyright (c) 1998-2008 The OpenSSL Project.  All rights reserved.
     *
     * Redistribution and use in source and binary forms, with or without
     * modification, are permitted provided that the following conditions
     * are met:
     *
     * 1. Redistributions of source code must retain the above copyright
     *    notice, this list of conditions and the following disclaimer.
     *
     * 2. Redistributions in binary form must reproduce the above copyright
     *    notice, this list of conditions and the following disclaimer in
     *    the documentation and/or other materials provided with the
     *    distribution.
     *
     * 3. All advertising materials mentioning features or use of this
     *    software must display the following acknowledgment:
     *    "This product includes software developed by the OpenSSL Project
     *    for use in the OpenSSL Toolkit. (http://www.openssl.org/)"
     *
     * 4. The names "OpenSSL Toolkit" and "OpenSSL Project" must not be used to
     *    endorse or promote products derived from this software without
     *    prior written permission. For written permission, please contact
     *    <EMAIL>.
     *
     * 5. Products derived from this software may not be called "OpenSSL"
     *    nor may "OpenSSL" appear in their names without prior written
     *    permission of the OpenSSL Project.
     *
     * 6. Redistributions of any form whatsoever must retain the following
     *    acknowledgment:
     *    "This product includes software developed by the OpenSSL Project
     *    for use in the OpenSSL Toolkit (http://www.openssl.org/)"
     *
     * THIS SOFTWARE IS PROVIDED BY THE OpenSSL PROJECT ``AS IS'' AND ANY
     * EXPRESSED OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
     * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
     * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE OpenSSL PROJECT OR
     * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
     * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
     * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
     * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
     * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
     * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
     * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
     * OF THE POSSIBILITY OF SUCH DAMAGE.
     * ====================================================================
     *
     * This product includes cryptographic software written by Eric Young
     * (<EMAIL>).  This product includes software written by Tim
     * Hudson (<EMAIL>).
     *
     */

  Original SSLeay License
  -----------------------

    /* Copyright (C) 1995-1998 Eric Young (<EMAIL>)
     * All rights reserved.
     *
     * This package is an SSL implementation written
     * by Eric Young (<EMAIL>).
     * The implementation was written so as to conform with Netscapes SSL.
     *
     * This library is free for commercial and non-commercial use as long as
     * the following conditions are aheared to.  The following conditions
     * apply to all code found in this distribution, be it the RC4, RSA,
     * lhash, DES, etc., code; not just the SSL code.  The SSL documentation
     * included with this distribution is covered by the same copyright terms
     * except that the holder is Tim Hudson (<EMAIL>).
     *
     * Copyright remains Eric Young's, and as such any Copyright notices in
     * the code are not to be removed.
     * If this package is used in a product, Eric Young should be given attribution
     * as the author of the parts of the library used.
     * This can be in the form of a textual message at program startup or
     * in documentation (online or textual) provided with the package.
     *
     * Redistribution and use in source and binary forms, with or without
     * modification, are permitted provided that the following conditions
     * are met:
     * 1. Redistributions of source code must retain the copyright
     *    notice, this list of conditions and the following disclaimer.
     * 2. Redistributions in binary form must reproduce the above copyright
     *    notice, this list of conditions and the following disclaimer in the
     *    documentation and/or other materials provided with the distribution.
     * 3. All advertising materials mentioning features or use of this software
     *    must display the following acknowledgement:
     *    "This product includes cryptographic software written by
     *     Eric Young (<EMAIL>)"
     *    The word 'cryptographic' can be left out if the rouines from the library
     *    being used are not cryptographic related :-).
     * 4. If you include any Windows specific code (or a derivative thereof) from
     *    the apps directory (application code) you must include an acknowledgement:
     *    "This product includes software written by Tim Hudson (<EMAIL>)"
     *
     * THIS SOFTWARE IS PROVIDED BY ERIC YOUNG ``AS IS'' AND
     * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
     * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
     * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
     * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
     * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
     * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
     * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
     * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
     * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
     * SUCH DAMAGE.
     *
     * The licence and distribution terms for any publically available version or
     * derivative of this code cannot be changed.  i.e. this code cannot simply be
     * copied and put under another distribution licence
     * [including the GNU Public Licence.]
     */


expat
-----

The :mod:`pyexpat` extension is built using an included copy of the expat
sources unless the build is configured ``--with-system-expat``::

  Copyright (c) 1998, 1999, 2000 Thai Open Source Software Center Ltd
                                 and Clark Cooper

  Permission is hereby granted, free of charge, to any person obtaining
  a copy of this software and associated documentation files (the
  "Software"), to deal in the Software without restriction, including
  without limitation the rights to use, copy, modify, merge, publish,
  distribute, sublicense, and/or sell copies of the Software, and to
  permit persons to whom the Software is furnished to do so, subject to
  the following conditions:

  The above copyright notice and this permission notice shall be included
  in all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
  IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
  CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
  TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
  SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.


libffi
------

The :mod:`_ctypes` extension is built using an included copy of the libffi
sources unless the build is configured ``--with-system-libffi``::

   Copyright (c) 1996-2008  Red Hat, Inc and others.

   Permission is hereby granted, free of charge, to any person obtaining
   a copy of this software and associated documentation files (the
   ``Software''), to deal in the Software without restriction, including
   without limitation the rights to use, copy, modify, merge, publish,
   distribute, sublicense, and/or sell copies of the Software, and to
   permit persons to whom the Software is furnished to do so, subject to
   the following conditions:

   The above copyright notice and this permission notice shall be included
   in all copies or substantial portions of the Software.

   THE SOFTWARE IS PROVIDED ``AS IS'', WITHOUT WARRANTY OF ANY KIND,
   EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
   MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
   NONINFRINGEMENT.  IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
   HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
   WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
   DEALINGS IN THE SOFTWARE.


zlib
----

The :mod:`zlib` extension is built using an included copy of the zlib
sources if the zlib version found on the system is too old to be
used for the build::

  Copyright (C) 1995-2010 Jean-loup Gailly and Mark Adler

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.

  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.

  3. This notice may not be removed or altered from any source distribution.

  Jean-loup Gailly        Mark Adler
  <EMAIL>          <EMAIL>
