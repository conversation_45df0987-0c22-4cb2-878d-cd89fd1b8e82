// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/tf2xla/tf2xla.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
namespace tensorflow {
namespace tf2xla {
class Config;
class ConfigDefaultTypeInternal;
extern ConfigDefaultTypeInternal _Config_default_instance_;
class ConversionOptions;
class ConversionOptionsDefaultTypeInternal;
extern ConversionOptionsDefaultTypeInternal _ConversionOptions_default_instance_;
class Feed;
class FeedDefaultTypeInternal;
extern FeedDefaultTypeInternal _Feed_default_instance_;
class Fetch;
class FetchDefaultTypeInternal;
extern FetchDefaultTypeInternal _Fetch_default_instance_;
class TensorId;
class TensorIdDefaultTypeInternal;
extern TensorIdDefaultTypeInternal _TensorId_default_instance_;
class Variable;
class VariableDefaultTypeInternal;
extern VariableDefaultTypeInternal _Variable_default_instance_;
}  // namespace tf2xla
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tf2xla::Config* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Config>(Arena*);
template<> ::tensorflow::tf2xla::ConversionOptions* Arena::CreateMaybeMessage<::tensorflow::tf2xla::ConversionOptions>(Arena*);
template<> ::tensorflow::tf2xla::Feed* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Feed>(Arena*);
template<> ::tensorflow::tf2xla::Fetch* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Fetch>(Arena*);
template<> ::tensorflow::tf2xla::TensorId* Arena::CreateMaybeMessage<::tensorflow::tf2xla::TensorId>(Arena*);
template<> ::tensorflow::tf2xla::Variable* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Variable>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tf2xla {

// ===================================================================

class TensorId :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.TensorId) */ {
 public:
  TensorId();
  virtual ~TensorId();

  TensorId(const TensorId& from);
  TensorId(TensorId&& from) noexcept
    : TensorId() {
    *this = ::std::move(from);
  }

  inline TensorId& operator=(const TensorId& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorId& operator=(TensorId&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TensorId& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorId* internal_default_instance() {
    return reinterpret_cast<const TensorId*>(
               &_TensorId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TensorId& a, TensorId& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorId* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorId* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TensorId* New() const final {
    return CreateMaybeMessage<TensorId>(nullptr);
  }

  TensorId* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TensorId>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TensorId& from);
  void MergeFrom(const TensorId& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorId* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.TensorId";
  }
  protected:
  explicit TensorId(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeNameFieldNumber = 1,
    kOutputIndexFieldNumber = 2,
  };
  // string node_name = 1;
  void clear_node_name();
  const std::string& node_name() const;
  void set_node_name(const std::string& value);
  void set_node_name(std::string&& value);
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  std::string* mutable_node_name();
  std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_node_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node_name(
      std::string* node_name);

  // int64 output_index = 2;
  void clear_output_index();
  ::PROTOBUF_NAMESPACE_ID::int64 output_index() const;
  void set_output_index(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.TensorId)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 output_index_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// -------------------------------------------------------------------

class Feed :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Feed) */ {
 public:
  Feed();
  virtual ~Feed();

  Feed(const Feed& from);
  Feed(Feed&& from) noexcept
    : Feed() {
    *this = ::std::move(from);
  }

  inline Feed& operator=(const Feed& from) {
    CopyFrom(from);
    return *this;
  }
  inline Feed& operator=(Feed&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Feed& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Feed* internal_default_instance() {
    return reinterpret_cast<const Feed*>(
               &_Feed_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Feed& a, Feed& b) {
    a.Swap(&b);
  }
  inline void Swap(Feed* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Feed* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Feed* New() const final {
    return CreateMaybeMessage<Feed>(nullptr);
  }

  Feed* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Feed>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Feed& from);
  void MergeFrom(const Feed& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Feed* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.Feed";
  }
  protected:
  explicit Feed(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 3,
    kIdFieldNumber = 1,
    kShapeFieldNumber = 2,
    kTypeFieldNumber = 4,
  };
  // string name = 3;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .tensorflow.tf2xla.TensorId id = 1;
  bool has_id() const;
  void clear_id();
  const ::tensorflow::tf2xla::TensorId& id() const;
  ::tensorflow::tf2xla::TensorId* release_id();
  ::tensorflow::tf2xla::TensorId* mutable_id();
  void set_allocated_id(::tensorflow::tf2xla::TensorId* id);
  void unsafe_arena_set_allocated_id(
      ::tensorflow::tf2xla::TensorId* id);
  ::tensorflow::tf2xla::TensorId* unsafe_arena_release_id();

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 4;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Feed)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::tf2xla::TensorId* id_;
  ::tensorflow::TensorShapeProto* shape_;
  int type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// -------------------------------------------------------------------

class Fetch :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Fetch) */ {
 public:
  Fetch();
  virtual ~Fetch();

  Fetch(const Fetch& from);
  Fetch(Fetch&& from) noexcept
    : Fetch() {
    *this = ::std::move(from);
  }

  inline Fetch& operator=(const Fetch& from) {
    CopyFrom(from);
    return *this;
  }
  inline Fetch& operator=(Fetch&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Fetch& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Fetch* internal_default_instance() {
    return reinterpret_cast<const Fetch*>(
               &_Fetch_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Fetch& a, Fetch& b) {
    a.Swap(&b);
  }
  inline void Swap(Fetch* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Fetch* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Fetch* New() const final {
    return CreateMaybeMessage<Fetch>(nullptr);
  }

  Fetch* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Fetch>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Fetch& from);
  void MergeFrom(const Fetch& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Fetch* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.Fetch";
  }
  protected:
  explicit Fetch(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 2,
    kIdFieldNumber = 1,
    kShapeFieldNumber = 3,
    kTypeFieldNumber = 4,
  };
  // string name = 2;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .tensorflow.tf2xla.TensorId id = 1;
  bool has_id() const;
  void clear_id();
  const ::tensorflow::tf2xla::TensorId& id() const;
  ::tensorflow::tf2xla::TensorId* release_id();
  ::tensorflow::tf2xla::TensorId* mutable_id();
  void set_allocated_id(::tensorflow::tf2xla::TensorId* id);
  void unsafe_arena_set_allocated_id(
      ::tensorflow::tf2xla::TensorId* id);
  ::tensorflow::tf2xla::TensorId* unsafe_arena_release_id();

  // .tensorflow.TensorShapeProto shape = 3;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 4;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Fetch)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::tf2xla::TensorId* id_;
  ::tensorflow::TensorShapeProto* shape_;
  int type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// -------------------------------------------------------------------

class Variable :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Variable) */ {
 public:
  Variable();
  virtual ~Variable();

  Variable(const Variable& from);
  Variable(Variable&& from) noexcept
    : Variable() {
    *this = ::std::move(from);
  }

  inline Variable& operator=(const Variable& from) {
    CopyFrom(from);
    return *this;
  }
  inline Variable& operator=(Variable&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Variable& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Variable* internal_default_instance() {
    return reinterpret_cast<const Variable*>(
               &_Variable_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Variable& a, Variable& b) {
    a.Swap(&b);
  }
  inline void Swap(Variable* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Variable* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Variable* New() const final {
    return CreateMaybeMessage<Variable>(nullptr);
  }

  Variable* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Variable>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Variable& from);
  void MergeFrom(const Variable& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Variable* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.Variable";
  }
  protected:
  explicit Variable(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeNameFieldNumber = 1,
    kNameFieldNumber = 2,
    kShapeFieldNumber = 3,
    kTypeFieldNumber = 4,
    kReadonlyFieldNumber = 5,
  };
  // string node_name = 1;
  void clear_node_name();
  const std::string& node_name() const;
  void set_node_name(const std::string& value);
  void set_node_name(std::string&& value);
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  std::string* mutable_node_name();
  std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_node_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node_name(
      std::string* node_name);

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .tensorflow.TensorShapeProto shape = 3;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 4;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // bool readonly = 5;
  void clear_readonly();
  bool readonly() const;
  void set_readonly(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Variable)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::TensorShapeProto* shape_;
  int type_;
  bool readonly_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// -------------------------------------------------------------------

class ConversionOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.ConversionOptions) */ {
 public:
  ConversionOptions();
  virtual ~ConversionOptions();

  ConversionOptions(const ConversionOptions& from);
  ConversionOptions(ConversionOptions&& from) noexcept
    : ConversionOptions() {
    *this = ::std::move(from);
  }

  inline ConversionOptions& operator=(const ConversionOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConversionOptions& operator=(ConversionOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ConversionOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConversionOptions* internal_default_instance() {
    return reinterpret_cast<const ConversionOptions*>(
               &_ConversionOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ConversionOptions& a, ConversionOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(ConversionOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConversionOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ConversionOptions* New() const final {
    return CreateMaybeMessage<ConversionOptions>(nullptr);
  }

  ConversionOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ConversionOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ConversionOptions& from);
  void MergeFrom(const ConversionOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConversionOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.ConversionOptions";
  }
  protected:
  explicit ConversionOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCustomFakeQuantOpCallsFieldNumber = 1,
  };
  // bool custom_fake_quant_op_calls = 1;
  void clear_custom_fake_quant_op_calls();
  bool custom_fake_quant_op_calls() const;
  void set_custom_fake_quant_op_calls(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.ConversionOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool custom_fake_quant_op_calls_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// -------------------------------------------------------------------

class Config :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Config) */ {
 public:
  Config();
  virtual ~Config();

  Config(const Config& from);
  Config(Config&& from) noexcept
    : Config() {
    *this = ::std::move(from);
  }

  inline Config& operator=(const Config& from) {
    CopyFrom(from);
    return *this;
  }
  inline Config& operator=(Config&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Config& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Config* internal_default_instance() {
    return reinterpret_cast<const Config*>(
               &_Config_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Config& a, Config& b) {
    a.Swap(&b);
  }
  inline void Swap(Config* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Config* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Config* New() const final {
    return CreateMaybeMessage<Config>(nullptr);
  }

  Config* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Config>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Config& from);
  void MergeFrom(const Config& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Config* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.Config";
  }
  protected:
  explicit Config(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeedFieldNumber = 1,
    kFetchFieldNumber = 2,
    kVariableFieldNumber = 3,
    kConversionOptionsFieldNumber = 4,
  };
  // repeated .tensorflow.tf2xla.Feed feed = 1;
  int feed_size() const;
  void clear_feed();
  ::tensorflow::tf2xla::Feed* mutable_feed(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Feed >*
      mutable_feed();
  const ::tensorflow::tf2xla::Feed& feed(int index) const;
  ::tensorflow::tf2xla::Feed* add_feed();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Feed >&
      feed() const;

  // repeated .tensorflow.tf2xla.Fetch fetch = 2;
  int fetch_size() const;
  void clear_fetch();
  ::tensorflow::tf2xla::Fetch* mutable_fetch(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >*
      mutable_fetch();
  const ::tensorflow::tf2xla::Fetch& fetch(int index) const;
  ::tensorflow::tf2xla::Fetch* add_fetch();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >&
      fetch() const;

  // repeated .tensorflow.tf2xla.Variable variable = 3;
  int variable_size() const;
  void clear_variable();
  ::tensorflow::tf2xla::Variable* mutable_variable(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Variable >*
      mutable_variable();
  const ::tensorflow::tf2xla::Variable& variable(int index) const;
  ::tensorflow::tf2xla::Variable* add_variable();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Variable >&
      variable() const;

  // .tensorflow.tf2xla.ConversionOptions conversion_options = 4;
  bool has_conversion_options() const;
  void clear_conversion_options();
  const ::tensorflow::tf2xla::ConversionOptions& conversion_options() const;
  ::tensorflow::tf2xla::ConversionOptions* release_conversion_options();
  ::tensorflow::tf2xla::ConversionOptions* mutable_conversion_options();
  void set_allocated_conversion_options(::tensorflow::tf2xla::ConversionOptions* conversion_options);
  void unsafe_arena_set_allocated_conversion_options(
      ::tensorflow::tf2xla::ConversionOptions* conversion_options);
  ::tensorflow::tf2xla::ConversionOptions* unsafe_arena_release_conversion_options();

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Config)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Feed > feed_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Fetch > fetch_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Variable > variable_;
  ::tensorflow::tf2xla::ConversionOptions* conversion_options_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorId

// string node_name = 1;
inline void TensorId::clear_node_name() {
  node_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TensorId::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorId.node_name)
  return node_name_.Get();
}
inline void TensorId::set_node_name(const std::string& value) {
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.TensorId.node_name)
}
inline void TensorId::set_node_name(std::string&& value) {
  
  node_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.TensorId.node_name)
}
inline void TensorId::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.TensorId.node_name)
}
inline void TensorId::set_node_name(const char* value,
    size_t size) {
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.TensorId.node_name)
}
inline std::string* TensorId::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.TensorId.node_name)
  return node_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TensorId::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.TensorId.node_name)
  
  return node_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TensorId::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  node_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), node_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.TensorId.node_name)
}
inline std::string* TensorId::unsafe_arena_release_node_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.TensorId.node_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return node_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TensorId::unsafe_arena_set_allocated_node_name(
    std::string* node_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (node_name != nullptr) {
    
  } else {
    
  }
  node_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      node_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.TensorId.node_name)
}

// int64 output_index = 2;
inline void TensorId::clear_output_index() {
  output_index_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorId::output_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorId.output_index)
  return output_index_;
}
inline void TensorId::set_output_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  output_index_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.TensorId.output_index)
}

// -------------------------------------------------------------------

// Feed

// .tensorflow.tf2xla.TensorId id = 1;
inline bool Feed::has_id() const {
  return this != internal_default_instance() && id_ != nullptr;
}
inline void Feed::clear_id() {
  if (GetArenaNoVirtual() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
}
inline const ::tensorflow::tf2xla::TensorId& Feed::id() const {
  const ::tensorflow::tf2xla::TensorId* p = id_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.id)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tf2xla::TensorId*>(
      &::tensorflow::tf2xla::_TensorId_default_instance_);
}
inline ::tensorflow::tf2xla::TensorId* Feed::release_id() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Feed.id)
  
  ::tensorflow::tf2xla::TensorId* temp = id_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  id_ = nullptr;
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Feed::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Feed.id)
  
  ::tensorflow::tf2xla::TensorId* temp = id_;
  id_ = nullptr;
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Feed::mutable_id() {
  
  if (id_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tf2xla::TensorId>(GetArenaNoVirtual());
    id_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Feed.id)
  return id_;
}
inline void Feed::set_allocated_id(::tensorflow::tf2xla::TensorId* id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete id_;
  }
  if (id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(id);
    if (message_arena != submessage_arena) {
      id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  id_ = id;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Feed.id)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool Feed::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& Feed::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* Feed::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Feed.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Feed::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Feed.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Feed::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Feed.shape)
  return shape_;
}
inline void Feed::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Feed.shape)
}

// string name = 3;
inline void Feed::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& Feed::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.name)
  return name_.Get();
}
inline void Feed::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Feed.name)
}
inline void Feed::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.Feed.name)
}
inline void Feed::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.Feed.name)
}
inline void Feed::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.Feed.name)
}
inline std::string* Feed::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Feed.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* Feed::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Feed.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Feed::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Feed.name)
}
inline std::string* Feed::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Feed.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Feed::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Feed.name)
}

// .tensorflow.DataType type = 4;
inline void Feed::clear_type() {
  type_ = 0;
}
inline ::tensorflow::DataType Feed::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.type)
  return static_cast< ::tensorflow::DataType >(type_);
}
inline void Feed::set_type(::tensorflow::DataType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Feed.type)
}

// -------------------------------------------------------------------

// Fetch

// .tensorflow.tf2xla.TensorId id = 1;
inline bool Fetch::has_id() const {
  return this != internal_default_instance() && id_ != nullptr;
}
inline void Fetch::clear_id() {
  if (GetArenaNoVirtual() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
}
inline const ::tensorflow::tf2xla::TensorId& Fetch::id() const {
  const ::tensorflow::tf2xla::TensorId* p = id_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Fetch.id)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tf2xla::TensorId*>(
      &::tensorflow::tf2xla::_TensorId_default_instance_);
}
inline ::tensorflow::tf2xla::TensorId* Fetch::release_id() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Fetch.id)
  
  ::tensorflow::tf2xla::TensorId* temp = id_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  id_ = nullptr;
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Fetch::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Fetch.id)
  
  ::tensorflow::tf2xla::TensorId* temp = id_;
  id_ = nullptr;
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Fetch::mutable_id() {
  
  if (id_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tf2xla::TensorId>(GetArenaNoVirtual());
    id_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Fetch.id)
  return id_;
}
inline void Fetch::set_allocated_id(::tensorflow::tf2xla::TensorId* id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete id_;
  }
  if (id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(id);
    if (message_arena != submessage_arena) {
      id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  id_ = id;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Fetch.id)
}

// string name = 2;
inline void Fetch::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& Fetch::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Fetch.name)
  return name_.Get();
}
inline void Fetch::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Fetch.name)
}
inline void Fetch::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.Fetch.name)
}
inline void Fetch::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.Fetch.name)
}
inline void Fetch::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.Fetch.name)
}
inline std::string* Fetch::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Fetch.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* Fetch::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Fetch.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Fetch::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Fetch.name)
}
inline std::string* Fetch::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Fetch.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Fetch::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Fetch.name)
}

// .tensorflow.TensorShapeProto shape = 3;
inline bool Fetch::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& Fetch::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Fetch.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* Fetch::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Fetch.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Fetch::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Fetch.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Fetch::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Fetch.shape)
  return shape_;
}
inline void Fetch::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Fetch.shape)
}

// .tensorflow.DataType type = 4;
inline void Fetch::clear_type() {
  type_ = 0;
}
inline ::tensorflow::DataType Fetch::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Fetch.type)
  return static_cast< ::tensorflow::DataType >(type_);
}
inline void Fetch::set_type(::tensorflow::DataType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Fetch.type)
}

// -------------------------------------------------------------------

// Variable

// string node_name = 1;
inline void Variable::clear_node_name() {
  node_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& Variable::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.node_name)
  return node_name_.Get();
}
inline void Variable::set_node_name(const std::string& value) {
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.node_name)
}
inline void Variable::set_node_name(std::string&& value) {
  
  node_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.Variable.node_name)
}
inline void Variable::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.Variable.node_name)
}
inline void Variable::set_node_name(const char* value,
    size_t size) {
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.Variable.node_name)
}
inline std::string* Variable::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Variable.node_name)
  return node_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* Variable::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Variable.node_name)
  
  return node_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Variable::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  node_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), node_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Variable.node_name)
}
inline std::string* Variable::unsafe_arena_release_node_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Variable.node_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return node_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Variable::unsafe_arena_set_allocated_node_name(
    std::string* node_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (node_name != nullptr) {
    
  } else {
    
  }
  node_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      node_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Variable.node_name)
}

// string name = 2;
inline void Variable::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& Variable::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.name)
  return name_.Get();
}
inline void Variable::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.name)
}
inline void Variable::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tf2xla.Variable.name)
}
inline void Variable::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tf2xla.Variable.name)
}
inline void Variable::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tf2xla.Variable.name)
}
inline std::string* Variable::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Variable.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* Variable::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Variable.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void Variable::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Variable.name)
}
inline std::string* Variable::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Variable.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void Variable::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Variable.name)
}

// .tensorflow.TensorShapeProto shape = 3;
inline bool Variable::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& Variable::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* Variable::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Variable.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Variable::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Variable.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Variable::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Variable.shape)
  return shape_;
}
inline void Variable::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Variable.shape)
}

// .tensorflow.DataType type = 4;
inline void Variable::clear_type() {
  type_ = 0;
}
inline ::tensorflow::DataType Variable::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.type)
  return static_cast< ::tensorflow::DataType >(type_);
}
inline void Variable::set_type(::tensorflow::DataType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.type)
}

// bool readonly = 5;
inline void Variable::clear_readonly() {
  readonly_ = false;
}
inline bool Variable::readonly() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.readonly)
  return readonly_;
}
inline void Variable::set_readonly(bool value) {
  
  readonly_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.readonly)
}

// -------------------------------------------------------------------

// ConversionOptions

// bool custom_fake_quant_op_calls = 1;
inline void ConversionOptions::clear_custom_fake_quant_op_calls() {
  custom_fake_quant_op_calls_ = false;
}
inline bool ConversionOptions::custom_fake_quant_op_calls() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.ConversionOptions.custom_fake_quant_op_calls)
  return custom_fake_quant_op_calls_;
}
inline void ConversionOptions::set_custom_fake_quant_op_calls(bool value) {
  
  custom_fake_quant_op_calls_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.ConversionOptions.custom_fake_quant_op_calls)
}

// -------------------------------------------------------------------

// Config

// repeated .tensorflow.tf2xla.Feed feed = 1;
inline int Config::feed_size() const {
  return feed_.size();
}
inline void Config::clear_feed() {
  feed_.Clear();
}
inline ::tensorflow::tf2xla::Feed* Config::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Config.feed)
  return feed_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Feed >*
Config::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.Config.feed)
  return &feed_;
}
inline const ::tensorflow::tf2xla::Feed& Config::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Config.feed)
  return feed_.Get(index);
}
inline ::tensorflow::tf2xla::Feed* Config::add_feed() {
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.Config.feed)
  return feed_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Feed >&
Config::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.Config.feed)
  return feed_;
}

// repeated .tensorflow.tf2xla.Fetch fetch = 2;
inline int Config::fetch_size() const {
  return fetch_.size();
}
inline void Config::clear_fetch() {
  fetch_.Clear();
}
inline ::tensorflow::tf2xla::Fetch* Config::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Config.fetch)
  return fetch_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >*
Config::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.Config.fetch)
  return &fetch_;
}
inline const ::tensorflow::tf2xla::Fetch& Config::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Config.fetch)
  return fetch_.Get(index);
}
inline ::tensorflow::tf2xla::Fetch* Config::add_fetch() {
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.Config.fetch)
  return fetch_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >&
Config::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.Config.fetch)
  return fetch_;
}

// repeated .tensorflow.tf2xla.Variable variable = 3;
inline int Config::variable_size() const {
  return variable_.size();
}
inline void Config::clear_variable() {
  variable_.Clear();
}
inline ::tensorflow::tf2xla::Variable* Config::mutable_variable(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Config.variable)
  return variable_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Variable >*
Config::mutable_variable() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.Config.variable)
  return &variable_;
}
inline const ::tensorflow::tf2xla::Variable& Config::variable(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Config.variable)
  return variable_.Get(index);
}
inline ::tensorflow::tf2xla::Variable* Config::add_variable() {
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.Config.variable)
  return variable_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Variable >&
Config::variable() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.Config.variable)
  return variable_;
}

// .tensorflow.tf2xla.ConversionOptions conversion_options = 4;
inline bool Config::has_conversion_options() const {
  return this != internal_default_instance() && conversion_options_ != nullptr;
}
inline void Config::clear_conversion_options() {
  if (GetArenaNoVirtual() == nullptr && conversion_options_ != nullptr) {
    delete conversion_options_;
  }
  conversion_options_ = nullptr;
}
inline const ::tensorflow::tf2xla::ConversionOptions& Config::conversion_options() const {
  const ::tensorflow::tf2xla::ConversionOptions* p = conversion_options_;
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Config.conversion_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tf2xla::ConversionOptions*>(
      &::tensorflow::tf2xla::_ConversionOptions_default_instance_);
}
inline ::tensorflow::tf2xla::ConversionOptions* Config::release_conversion_options() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Config.conversion_options)
  
  ::tensorflow::tf2xla::ConversionOptions* temp = conversion_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  conversion_options_ = nullptr;
  return temp;
}
inline ::tensorflow::tf2xla::ConversionOptions* Config::unsafe_arena_release_conversion_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tf2xla.Config.conversion_options)
  
  ::tensorflow::tf2xla::ConversionOptions* temp = conversion_options_;
  conversion_options_ = nullptr;
  return temp;
}
inline ::tensorflow::tf2xla::ConversionOptions* Config::mutable_conversion_options() {
  
  if (conversion_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tf2xla::ConversionOptions>(GetArenaNoVirtual());
    conversion_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Config.conversion_options)
  return conversion_options_;
}
inline void Config::set_allocated_conversion_options(::tensorflow::tf2xla::ConversionOptions* conversion_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete conversion_options_;
  }
  if (conversion_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(conversion_options);
    if (message_arena != submessage_arena) {
      conversion_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, conversion_options, submessage_arena);
    }
    
  } else {
    
  }
  conversion_options_ = conversion_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Config.conversion_options)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tf2xla
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto
