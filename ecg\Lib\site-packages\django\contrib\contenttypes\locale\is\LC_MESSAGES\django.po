# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-11-20 05:05+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Icelandic (http://www.transifex.com/django/django/language/"
"is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

msgid "Content Types"
msgstr "Efnistög"

msgid "python model class name"
msgstr "python eininga klasa nafn"

msgid "content type"
msgstr "efnistag"

msgid "content types"
msgstr "efnistög"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Gerð innihalds %(ct_id)s hefur ekkert tengt módel"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Gerð innihalds %(ct_id)s hlutar %(obj_id)s er ekki til"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s hlutir hafa ekki get_absolute_url () aðferð"
