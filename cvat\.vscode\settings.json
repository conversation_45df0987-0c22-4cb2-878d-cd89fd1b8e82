{
    "eslint.probe": [
        "javascript",
        "typescript",
        "typescriptreact"
    ],
    "eslint.onIgnoredFiles": "warn",
    "eslint.workingDirectories": [
        {
            "directory": "${cwd}",
        },
        {
            "pattern":  "cvat-*"
        },
        {
            "directory": "tests",
            "!cwd": true
        }
    ],
    "npm.exclude": "**/.env/**",
    "files.trimTrailingWhitespace": true,
    "sqltools.connections": [
        {
            "previewLimit": 50,
            "driver": "SQLite",
            "name": "cvat",
            "database": "${workspaceFolder:cvat}/db.sqlite3"
        }
    ],
    "python.analysis.exclude": [
        // VS Code defaults
        "**/node_modules",
        "**/__pycache__",
        ".git",

        "cvat-cli/build",
        "cvat-sdk/build",
    ],
    "python.defaultInterpreterPath": "${workspaceFolder}/.env/",
    "python.testing.pytestArgs": [
        "--rootdir","${workspaceFolder}/tests/"
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
    "python.testing.pytestPath": "${workspaceFolder}/.env/bin/pytest",
    "python.testing.cwd": "${workspaceFolder}/tests",
    "cSpell.words": [
        "crowdsourcing"
    ]
}
