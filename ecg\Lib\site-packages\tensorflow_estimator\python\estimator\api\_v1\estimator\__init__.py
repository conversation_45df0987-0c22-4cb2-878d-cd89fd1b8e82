# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf_estimator.python.estimator.api._v1.estimator namespace
"""

import sys as _sys

from tensorflow_estimator.python.estimator.api._v1.estimator import experimental
from tensorflow_estimator.python.estimator.api._v1.estimator import export
from tensorflow_estimator.python.estimator.api._v1.estimator import inputs
from tensorflow_estimator.python.estimator.api._v1.estimator import tpu
from tensorflow_estimator.python.estimator.canned.baseline import BaselineClassifier # line: 403
from tensorflow_estimator.python.estimator.canned.baseline import BaselineEstimator # line: 511
from tensorflow_estimator.python.estimator.canned.baseline import BaselineRegressor # line: 626
from tensorflow_estimator.python.estimator.canned.dnn import DNNClassifier # line: 766
from tensorflow_estimator.python.estimator.canned.dnn import DNNEstimator # line: 969
from tensorflow_estimator.python.estimator.canned.dnn import DNNRegressor # line: 1179
from tensorflow_estimator.python.estimator.canned.dnn_linear_combined import DNNLinearCombinedClassifier # line: 593
from tensorflow_estimator.python.estimator.canned.dnn_linear_combined import DNNLinearCombinedEstimator # line: 851
from tensorflow_estimator.python.estimator.canned.dnn_linear_combined import DNNLinearCombinedRegressor # line: 1090
from tensorflow_estimator.python.estimator.canned.linear import LinearClassifier # line: 951
from tensorflow_estimator.python.estimator.canned.linear import LinearEstimator # line: 1129
from tensorflow_estimator.python.estimator.canned.linear import LinearRegressor # line: 1369
from tensorflow_estimator.python.estimator.canned.parsing_utils import classifier_parse_example_spec # line: 315
from tensorflow_estimator.python.estimator.canned.parsing_utils import regressor_parse_example_spec # line: 334
from tensorflow_estimator.python.estimator.estimator import Estimator # line: 67
from tensorflow_estimator.python.estimator.estimator import VocabInfo # line: 2173
from tensorflow_estimator.python.estimator.estimator import WarmStartSettings # line: 2176
from tensorflow_estimator.python.estimator.exporter import BestExporter # line: 164
from tensorflow_estimator.python.estimator.exporter import Exporter # line: 30
from tensorflow_estimator.python.estimator.exporter import FinalExporter # line: 368
from tensorflow_estimator.python.estimator.exporter import LatestExporter # line: 421
from tensorflow_estimator.python.estimator.extenders import add_metrics # line: 29
from tensorflow_estimator.python.estimator.head.base_head import Head # line: 43
from tensorflow_estimator.python.estimator.head.binary_class_head import BinaryClassHead # line: 33
from tensorflow_estimator.python.estimator.head.multi_class_head import MultiClassHead # line: 33
from tensorflow_estimator.python.estimator.head.multi_head import MultiHead # line: 52
from tensorflow_estimator.python.estimator.head.multi_label_head import MultiLabelHead # line: 34
from tensorflow_estimator.python.estimator.head.regression_head import LogisticRegressionHead # line: 499
from tensorflow_estimator.python.estimator.head.regression_head import PoissonRegressionHead # line: 409
from tensorflow_estimator.python.estimator.head.regression_head import RegressionHead # line: 33
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import CheckpointSaverHook # line: 40
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import CheckpointSaverListener # line: 39
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import FeedFnHook # line: 48
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import FinalOpsHook # line: 47
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import GlobalStepWaiterHook # line: 46
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import LoggingTensorHook # line: 37
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import NanLossDuringTrainingError # line: 42
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import NanTensorHook # line: 44
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import ProfilerHook # line: 49
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import SecondOrStepTimer # line: 36
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import StepCounterHook # line: 41
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import StopAtStepHook # line: 38
from tensorflow_estimator.python.estimator.hooks.basic_session_run_hooks import SummarySaverHook # line: 45
from tensorflow_estimator.python.estimator.hooks.session_run_hook import SessionRunArgs # line: 99
from tensorflow_estimator.python.estimator.hooks.session_run_hook import SessionRunContext # line: 100
from tensorflow_estimator.python.estimator.hooks.session_run_hook import SessionRunHook # line: 98
from tensorflow_estimator.python.estimator.hooks.session_run_hook import SessionRunValues # line: 101
from tensorflow_estimator.python.estimator.mode_keys import ModeKeys # line: 24
from tensorflow_estimator.python.estimator.model_fn import EstimatorSpec # line: 35
from tensorflow_estimator.python.estimator.run_config import RunConfig # line: 343
from tensorflow_estimator.python.estimator.training import EvalSpec # line: 200
from tensorflow_estimator.python.estimator.training import TrainSpec # line: 127
from tensorflow_estimator.python.estimator.training import train_and_evaluate # line: 296

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "estimator", public_apis=None, deprecation=True,
      has_lite=False)
