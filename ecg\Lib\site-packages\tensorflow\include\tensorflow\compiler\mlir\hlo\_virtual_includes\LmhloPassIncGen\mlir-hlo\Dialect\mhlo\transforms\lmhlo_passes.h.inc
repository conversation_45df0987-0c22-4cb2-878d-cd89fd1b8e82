/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// LegalizeTensorLoadOpPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeTensorLoadOpPassBase : public ::mlir::FunctionPass {
public:
  using Base = LegalizeTensorLoadOpPassBase;

  LegalizeTensorLoadOpPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTensorLoadOpPassBase(const LegalizeTensorLoadOpPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lhlo-legalize-tensor-load-op");
  }
  ::llvm::StringRef getArgument() const override { return "lhlo-legalize-tensor-load-op"; }

  ::llvm::StringRef getDescription() const override { return "Legalize tensor load ops that are inserted during mhlo to lmhlo conversion."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTensorLoadOpPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTensorLoadOpPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LhloFuseLinalgPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LhloFuseLinalgPassBase : public ::mlir::FunctionPass {
public:
  using Base = LhloFuseLinalgPassBase;

  LhloFuseLinalgPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LhloFuseLinalgPassBase(const LhloFuseLinalgPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lhlo-fuse-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "lhlo-fuse-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Greedily fuse linalg ops obtained after LHLO lowering."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LhloFuseLinalgPass");
  }
  ::llvm::StringRef getName() const override { return "LhloFuseLinalgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> use_parallel_loops_{*this, "use-parallel-loops", ::llvm::cl::desc("Tiles GenericOp consumer to parallel loops before linalg fusion"), ::llvm::cl::init(false)};
  ::mlir::Pass::ListOption<unsigned> tile_sizes_{*this, "tile-sizes", ::llvm::cl::desc("Faster memory space number to promote fusion buffers to"), llvm::cl::ZeroOrMore, llvm::cl::MiscFlags::CommaSeparated};
};

//===----------------------------------------------------------------------===//
// LhloFusionInlinerPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LhloFusionInlinerPassBase : public ::mlir::FunctionPass {
public:
  using Base = LhloFusionInlinerPassBase;

  LhloFusionInlinerPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LhloFusionInlinerPassBase(const LhloFusionInlinerPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lhlo-fusion-inliner");
  }
  ::llvm::StringRef getArgument() const override { return "lhlo-fusion-inliner"; }

  ::llvm::StringRef getDescription() const override { return "Inline the contents of its body to the parent region after its body has been lowered"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LhloFusionInlinerPass");
  }
  ::llvm::StringRef getName() const override { return "LhloFusionInlinerPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LhloFusionPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LhloFusionPassBase : public ::mlir::FunctionPass {
public:
  using Base = LhloFusionPassBase;

  LhloFusionPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LhloFusionPassBase(const LhloFusionPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lhlo-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "lhlo-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse lmhlo ops to kLoop/kInput fusion patterns."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LhloFusionPass");
  }
  ::llvm::StringRef getName() const override { return "LhloFusionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<int> max_num_arguments_per_kernel_{*this, "max-num-arguments-per-kernel", ::llvm::cl::desc("Maximum allowed number of arguments per fused kernel."), ::llvm::cl::init(64)};
};

//===----------------------------------------------------------------------===//
// LhloLegalizeToAffinePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LhloLegalizeToAffinePassBase : public ::mlir::FunctionPass {
public:
  using Base = LhloLegalizeToAffinePassBase;

  LhloLegalizeToAffinePassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LhloLegalizeToAffinePassBase(const LhloLegalizeToAffinePassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lhlo-legalize-to-affine");
  }
  ::llvm::StringRef getArgument() const override { return "lhlo-legalize-to-affine"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from LHLO dialect to affine dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LhloLegalizeToAffinePass");
  }
  ::llvm::StringRef getName() const override { return "LhloLegalizeToAffinePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LhloLegalizeToGpuPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LhloLegalizeToGpuPassBase : public ::mlir::FunctionPass {
public:
  using Base = LhloLegalizeToGpuPassBase;

  LhloLegalizeToGpuPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LhloLegalizeToGpuPassBase(const LhloLegalizeToGpuPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lhlo-legalize-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "lhlo-legalize-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from LHLO dialect to GPU dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LhloLegalizeToGpuPass");
  }
  ::llvm::StringRef getName() const override { return "LhloLegalizeToGpuPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LhloLegalizeToLinalgPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LhloLegalizeToLinalgPassBase : public ::mlir::FunctionPass {
public:
  using Base = LhloLegalizeToLinalgPassBase;

  LhloLegalizeToLinalgPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LhloLegalizeToLinalgPassBase(const LhloLegalizeToLinalgPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lhlo-legalize-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "lhlo-legalize-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from LHLO dialect to Linalg dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LhloLegalizeToLinalgPass");
  }
  ::llvm::StringRef getName() const override { return "LhloLegalizeToLinalgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LhloLegalizeToParallelLoopsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LhloLegalizeToParallelLoopsPassBase : public ::mlir::FunctionPass {
public:
  using Base = LhloLegalizeToParallelLoopsPassBase;

  LhloLegalizeToParallelLoopsPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LhloLegalizeToParallelLoopsPassBase(const LhloLegalizeToParallelLoopsPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lhlo-legalize-to-parallel-loops");
  }
  ::llvm::StringRef getArgument() const override { return "lhlo-legalize-to-parallel-loops"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from LHLO dialect to parallel loops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LhloLegalizeToParallelLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "LhloLegalizeToParallelLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// LegalizeTensorLoadOpPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTensorLoadOpPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeTensorLoadOpPass();
  });
}

//===----------------------------------------------------------------------===//
// LhloFuseLinalgPass Registration
//===----------------------------------------------------------------------===//

inline void registerLhloFuseLinalgPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLhloFuseLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// LhloFusionInlinerPass Registration
//===----------------------------------------------------------------------===//

inline void registerLhloFusionInlinerPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLhloFusionInlinerPass();
  });
}

//===----------------------------------------------------------------------===//
// LhloFusionPass Registration
//===----------------------------------------------------------------------===//

inline void registerLhloFusionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLhloFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// LhloLegalizeToAffinePass Registration
//===----------------------------------------------------------------------===//

inline void registerLhloLegalizeToAffinePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLhloLegalizeToAffinePass();
  });
}

//===----------------------------------------------------------------------===//
// LhloLegalizeToGpuPass Registration
//===----------------------------------------------------------------------===//

inline void registerLhloLegalizeToGpuPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeToGpuPass();
  });
}

//===----------------------------------------------------------------------===//
// LhloLegalizeToLinalgPass Registration
//===----------------------------------------------------------------------===//

inline void registerLhloLegalizeToLinalgPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeLhloToLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// LhloLegalizeToParallelLoopsPass Registration
//===----------------------------------------------------------------------===//

inline void registerLhloLegalizeToParallelLoopsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeLhloToParallelLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LMHLO Registration
//===----------------------------------------------------------------------===//

inline void registerLMHLOPasses() {
  registerLegalizeTensorLoadOpPassPass();
  registerLhloFuseLinalgPassPass();
  registerLhloFusionInlinerPassPass();
  registerLhloFusionPassPass();
  registerLhloLegalizeToAffinePassPass();
  registerLhloLegalizeToGpuPassPass();
  registerLhloLegalizeToLinalgPassPass();
  registerLhloLegalizeToParallelLoopsPassPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
