# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/core/lib/core/error_codes.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.core.protobuf import error_codes_pb2 as tensorflow_dot_core_dot_protobuf_dot_error__codes__pb2

from tensorflow.core.protobuf.error_codes_pb2 import *

DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/core/lib/core/error_codes.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n*tensorflow/core/lib/core/error_codes.proto\x1a*tensorflow/core/protobuf/error_codes.protoP\x00\x62\x06proto3')
  ,
  dependencies=[tensorflow_dot_core_dot_protobuf_dot_error__codes__pb2.DESCRIPTOR,],
  public_dependencies=[tensorflow_dot_core_dot_protobuf_dot_error__codes__pb2.DESCRIPTOR,])



_sym_db.RegisterFileDescriptor(DESCRIPTOR)


# @@protoc_insertion_point(module_scope)
