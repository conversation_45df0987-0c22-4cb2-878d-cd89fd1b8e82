# Generated by Django 4.2.16 on 2024-10-22 08:41

from django.conf import settings
from django.db import migrations, models


def set_has_analytics_access(apps, schema_editor):
    User = apps.get_model("auth", "User")
    for user in User.objects.all():
        is_admin = user.groups.filter(name=settings.IAM_ADMIN_ROLE).exists()
        user.profile.has_analytics_access = user.is_superuser or is_admin
        user.profile.save()


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0085_segment_chunks_updated_date"),
    ]

    operations = [
        migrations.AddField(
            model_name="profile",
            name="has_analytics_access",
            field=models.BooleanField(
                default=False,
                help_text="Designates whether the user can access analytics.",
                verbose_name="has access to analytics",
            ),
        ),
        migrations.RunPython(
            set_has_analytics_access,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
