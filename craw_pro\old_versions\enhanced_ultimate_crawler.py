#!/usr/bin/env python3
"""
增强版终极羽毛球装备爬虫 - 解决数据字段空白问题
专注于完整的数据提取和字段填充
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os
from urllib.parse import urljoin, urlparse
import random

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedUltimateCrawler:
    def __init__(self):
        self.session = requests.Session()
        
        # 完整的浏览器模拟Headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        })
        
        self.base_url = "https://www.badmintoncn.com"
        self.equipment_types = {
            1: "羽毛球拍",
            2: "羽毛球鞋", 
            3: "运动包",
            4: "羽毛球线",
            5: "羽毛球",
            6: "运动服饰",
            7: "手胶"
        }
        
        self.verification_solved = False
        
        os.makedirs('output', exist_ok=True)
        logger.info("🏸 增强版终极爬虫初始化完成")

    def human_like_delay(self, min_seconds=1, max_seconds=3):
        """人类化延时"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)

    def simulate_browser_session(self):
        """模拟真实浏览器会话建立过程"""
        try:
            logger.info("🌐 模拟浏览器会话建立...")
            
            # 1. 访问主页
            response = self.session.get(self.base_url, timeout=30)
            logger.info(f"主页状态码: {response.status_code}")
            
            if response.status_code != 200:
                logger.warning(f"主页访问异常: {response.status_code}")
                return False
            
            self.human_like_delay(2, 4)
            
            # 2. 访问装备页面
            equipment_url = f"{self.base_url}/cbo_eq/list.php"
            self.session.headers.update({
                'Referer': self.base_url,
                'Sec-Fetch-Site': 'same-origin'
            })
            
            response = self.session.get(equipment_url, timeout=30)
            logger.info(f"装备页面状态码: {response.status_code}")
            
            return response.text, response.status_code
            
        except Exception as e:
            logger.error(f"浏览器会话建立失败: {e}")
            return None, None

    def solve_verification_enhanced(self, html_content, page_url):
        """增强版验证解决"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            page_text = soup.get_text()
            
            # 检查是否需要验证
            verification_patterns = [
                r'(\d+[×*x]\d+)=？', r'(\d+[+]\d+)=？', r'(\d+[-]\d+)=？',
                r'羽毛球有几根毛', r'ZYZX.*?怎么写', r'中羽.*?缩写',
            ]
            
            question = None
            for pattern in verification_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    question = matches[0]
                    break
            
            if not question:
                logger.info("✅ 无需验证或已通过验证")
                return True
            
            logger.info(f"🔐 检测到验证问题: {question}")
            
            # 获取验证答案
            answer = self.get_verification_answer(question)
            if not answer:
                logger.error("❌ 无法获取验证答案")
                return False
            
            # 查找验证表单
            form = soup.find('form')
            if not form:
                logger.error("❌ 未找到验证表单")
                return False
            
            # 构建表单数据
            form_data = {}
            
            # 添加所有隐藏字段
            for input_tag in form.find_all('input'):
                input_name = input_tag.get('name')
                input_value = input_tag.get('value', '')
                input_type = input_tag.get('type', 'text')
                
                if input_name and input_type == 'hidden':
                    form_data[input_name] = input_value
            
            # 添加答案字段
            answer_fields = ['answer', 'verify', 'code', 'result', 'a']
            answer_added = False
            
            for field in answer_fields:
                if form.find('input', {'name': field}):
                    form_data[field] = answer
                    answer_added = True
                    logger.info(f"✅ 答案字段: {field} = {answer}")
                    break
            
            if not answer_added:
                form_data['a'] = answer
                logger.info(f"✅ 默认答案字段: a = {answer}")
            
            # 获取提交URL
            action = form.get('action', page_url)
            if not action.startswith('http'):
                submit_url = urljoin(self.base_url, action)
            else:
                submit_url = action
            
            # 提交验证
            post_headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': self.base_url,
                'Referer': page_url,
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
            }
            
            submit_response = self.session.post(
                submit_url, 
                data=form_data, 
                headers=post_headers,
                timeout=30,
                allow_redirects=True
            )
            
            logger.info(f"验证提交状态码: {submit_response.status_code}")
            
            if submit_response.status_code in [200, 302]:
                self.verification_solved = True
                logger.info("✅ 验证处理完成")
                self.human_like_delay(3, 5)
                return True
            else:
                logger.error(f"❌ 验证提交失败: {submit_response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"验证处理失败: {e}")
            return False

    def get_verification_answer(self, question):
        """获取验证答案"""
        logger.info(f"🤖 处理验证问题: {question}")
        
        # 羽毛球有几根毛？
        if '羽毛球' in question and ('几根毛' in question or '多少毛' in question):
            return "16"
        
        # ZYZX小写
        if 'ZYZX' in question and ('小写' in question or '怎么写' in question):
            return "zyzx"
        
        # 中羽在线缩写
        if '中羽' in question and '缩写' in question:
            return "zyzx"
        
        # 数学计算
        try:
            math_text = re.sub(r'[=？?].*', '', question).strip()
            math_text = math_text.replace('×', '*').replace('x', '*').replace('X', '*')
            
            if re.match(r'^\d+\s*[+\-*/]\s*\d+$', math_text):
                result = eval(math_text)
                logger.info(f"  🧮 计算结果: {math_text} = {result}")
                return str(result)
        except Exception as e:
            logger.debug(f"数学计算失败: {e}")
        
        logger.warning(f"⚠️ 未识别的验证问题: {question}")
        return None

    def get_equipment_with_session(self, url, max_retries=2):
        """使用建立好的Session获取装备数据"""
        for retry in range(max_retries):
            try:
                logger.info(f"🔍 获取装备数据 (尝试 {retry+1}): {url}")
                
                response = self.session.get(url, timeout=30)
                logger.info(f"页面状态码: {response.status_code}")
                
                if response.status_code in [200, 400]:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    page_text = soup.get_text()
                    
                    # 检查是否仍需验证
                    needs_verification = any(keyword in page_text for keyword in 
                                           ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛'])
                    
                    if needs_verification and not self.verification_solved:
                        logger.info("🔐 页面需要验证，开始处理...")
                        if self.solve_verification_enhanced(response.text, url):
                            self.human_like_delay(2, 4)
                            response = self.session.get(url, timeout=30)
                            soup = BeautifulSoup(response.text, 'html.parser')
                        else:
                            logger.warning("验证处理失败")
                            continue
                    
                    # 查找装备链接
                    equipment_links = []
                    for link in soup.find_all('a', href=True):
                        href = link.get('href')
                        if href and 'view.php?eid=' in href:
                            if not href.startswith('http'):
                                href = urljoin(self.base_url, href)
                            equipment_links.append(href)
                    
                    equipment_links = list(set(equipment_links))
                    
                    if equipment_links:
                        logger.info(f"✅ 找到 {len(equipment_links)} 个装备链接")
                        return equipment_links
                    else:
                        if retry < max_retries - 1:
                            self.human_like_delay(3, 5)
                            continue
                
            except Exception as e:
                logger.error(f"获取失败 (尝试 {retry+1}): {e}")
                if retry < max_retries - 1:
                    self.human_like_delay(3, 5)
                    continue
        
        return []

    def crawl_with_full_simulation(self, max_total_items=10):
        """完整模拟浏览器行为进行爬取"""
        logger.info("🚀 开始完整浏览器模拟爬取...")
        
        # 1. 建立浏览器会话
        initial_content, status_code = self.simulate_browser_session()
        if not initial_content:
            logger.error("❌ 无法建立浏览器会话")
            return []
        
        # 2. 如果初始页面需要验证，先解决
        if status_code == 400 or any(keyword in initial_content for keyword in 
                                   ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛']):
            logger.info("🔐 初始页面需要验证")
            equipment_url = f"{self.base_url}/cbo_eq/list.php"
            if not self.solve_verification_enhanced(initial_content, equipment_url):
                logger.error("❌ 初始验证失败")
                return []
        
        # 3. 收集所有装备链接
        all_equipment_links = []
        
        # 尝试通用装备页面
        general_links = self.get_equipment_with_session(f"{self.base_url}/cbo_eq/list.php")
        all_equipment_links.extend(general_links)
        
        # 如果通用页面成功，尝试分类页面
        if general_links:
            for type_id in [1, 2, 3]:  # 先尝试前3个类型
                type_url = f"{self.base_url}/cbo_eq/list.php?tid={type_id}"
                type_links = self.get_equipment_with_session(type_url)
                all_equipment_links.extend(type_links)
                
                self.human_like_delay(2, 4)
        
        # 去重
        all_equipment_links = list(set(all_equipment_links))
        logger.info(f"🔗 总共收集到 {len(all_equipment_links)} 个装备链接")
        
        if not all_equipment_links:
            logger.error("❌ 未找到任何装备链接")
            return []
        
        # 4. 解析装备详情
        equipment_data = []
        for i, link in enumerate(all_equipment_links[:max_total_items]):
            logger.info(f"📋 解析装备 ({i+1}/{min(max_total_items, len(all_equipment_links))})")
            
            detail_data = self.parse_equipment_detail_enhanced(link)
            if detail_data:
                equipment_data.append(detail_data)
                # 显示数据完整性
                filled = sum(1 for v in detail_data.values() if v and v != '')
                total = len(detail_data)
                logger.info(f"  ✅ 成功: {detail_data['equipment_name']} (完整性: {filled/total*100:.1f}%)")
            else:
                logger.warning(f"  ❌ 失败")
            
            self.human_like_delay(2, 4)
        
        logger.info(f"🎉 爬取完成！成功获取 {len(equipment_data)} 条装备数据")
        return equipment_data

    def parse_equipment_detail_enhanced(self, url):
        """增强版装备详情解析 - 解决数据空白问题"""
        try:
            response = self.session.get(url, timeout=30)
            
            if response.status_code not in [200, 400]:
                return None
            
            soup = BeautifulSoup(response.text, 'html.parser')
            page_text = soup.get_text()
            
            # 检查验证
            if any(keyword in page_text for keyword in ['验证', '问题', '计算']):
                logger.debug("  详情页需要验证，跳过")
                return None
            
            # 提取装备ID
            eid_match = re.search(r'eid=(\d+)', url)
            equipment_id = eid_match.group(1) if eid_match else 'unknown'
            
            # 初始化数据
            equipment_data = {
                'equipment_id': equipment_id,
                'equipment_name': '',
                'equipment_type': '',
                'equipment_brand': '',
                'equipment_series': '',
                'equipment_description': '',
                'release_date': '',
                'equipment_introduction': '',
                'specifications': '',
                'frame_material': '',
                'shaft_material': '',
                'weight': '',
                'length': '',
                'grip_size': '',
                'shaft_stiffness': '',
                'string_tension': '',
                'balance_point': '',
                'new_avg_price': '',
                'used_avg_price': '',
                'total_registered_users': '',
                'rating_score': '',
                'rating_distribution': '',
                'user_reviews_count': '',
                'detail_url': url,
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 1. 提取标题
            if soup.title:
                title = soup.title.string.strip()
                equipment_name = title.replace('中羽在线 badmintoncn.com', '').strip()
                if equipment_name:
                    equipment_data['equipment_name'] = equipment_name[:100]
            
            # 2. 增强版表格数据提取
            self.extract_enhanced_table_data(soup, equipment_data)
            
            # 3. 从页面文本中提取缺失信息
            self.extract_from_page_text(page_text, equipment_data)
            
            # 4. 提取评分和用户评价数据
            self.extract_rating_and_reviews(page_text, equipment_data)
            
            # 5. 尝试获取价格信息
            self.get_enhanced_price_info(equipment_id, equipment_data)
            
            # 6. 智能推断缺失字段
            self.infer_missing_fields(equipment_data)
            
            return equipment_data
            
        except Exception as e:
            logger.error(f"增强解析装备详情失败: {e}")
            return None

    def extract_enhanced_table_data(self, soup, equipment_data):
        """增强版表格数据提取"""
        try:
            tables = soup.find_all('table')
            logger.debug(f"  找到 {len(tables)} 个表格")
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        # 更全面的字段映射
                        field_mappings = {
                            # 基础信息
                            '装备类型': 'equipment_type', '类型': 'equipment_type',
                            '装备品牌': 'equipment_brand', '品牌': 'equipment_brand', '制造商': 'equipment_brand',
                            '装备系列': 'equipment_series', '系列': 'equipment_series', '产品系列': 'equipment_series',
                            '上市日期': 'release_date', '发布日期': 'release_date', '发售时间': 'release_date',
                            
                            # 羽毛球拍技术规格
                            '拍框材质': 'frame_material', '框架材质': 'frame_material', '拍头材质': 'frame_material',
                            '拍杆材质': 'shaft_material', '中管材质': 'shaft_material', '拍柄材质': 'shaft_material',
                            '重量': 'weight', '拍重': 'weight', '拍身重量': 'weight', '净重': 'weight',
                            '长度': 'length', '拍身长度': 'length', '总长度': 'length',
                            '手柄尺寸': 'grip_size', '拍柄粗细': 'grip_size', '握把大小': 'grip_size',
                            '中管韧度': 'shaft_stiffness', '硬度': 'shaft_stiffness', '中管硬度': 'shaft_stiffness',
                            '拉线磅数': 'string_tension', '穿线磅数': 'string_tension', '建议拉力': 'string_tension',
                            '平衡点': 'balance_point', '重心': 'balance_point', '平衡': 'balance_point',
                            
                            # 价格信息
                            '最近全新均价': 'new_avg_price', '全新均价': 'new_avg_price', '新品价格': 'new_avg_price',
                            '最近二手均价': 'used_avg_price', '二手均价': 'used_avg_price', '二手价格': 'used_avg_price',
                            '总登记球友': 'total_registered_users', '登记球友': 'total_registered_users', '用户数': 'total_registered_users',
                        }
                        
                        # 尝试映射字段
                        for keyword, field in field_mappings.items():
                            if keyword in key and not equipment_data[field]:
                                cleaned_value = self.clean_field_value(value, field)
                                if cleaned_value:
                                    equipment_data[field] = cleaned_value
                                    logger.debug(f"    ✅ 映射: {keyword} -> {cleaned_value}")
                                break
                        
                        # 收集所有规格参数
                        if key and value and key not in ['', ' ']:
                            if equipment_data['specifications']:
                                equipment_data['specifications'] += f"; {key}: {value}"
                            else:
                                equipment_data['specifications'] = f"{key}: {value}"
                                
        except Exception as e:
            logger.error(f"增强表格数据提取失败: {e}")

    def extract_from_page_text(self, page_text, equipment_data):
        """从页面文本中提取信息"""
        try:
            # 提取重量信息 (如: 3U, 4U, 85g, 90g等)
            if not equipment_data['weight']:
                weight_patterns = [
                    r'(\d+[Uu])',  # 3U, 4U
                    r'(\d+[gG])',  # 85g, 90g
                    r'重量[：:]\s*(\d+[gG]?)',
                    r'拍重[：:]\s*(\d+[gG]?)',
                ]
                for pattern in weight_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        equipment_data['weight'] = match.group(1)
                        logger.debug(f"    🔍 从文本提取重量: {match.group(1)}")
                        break
            
            # 提取平衡点信息 (如: 290mm, 头重, 平衡等)
            if not equipment_data['balance_point']:
                balance_patterns = [
                    r'平衡点[：:]\s*(\d+mm?)',
                    r'重心[：:]\s*(\d+mm?)',
                    r'(头重|头轻|平衡)',
                    r'(\d+mm).*?平衡',
                ]
                for pattern in balance_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        equipment_data['balance_point'] = match.group(1)
                        logger.debug(f"    🔍 从文本提取平衡点: {match.group(1)}")
                        break
            
            # 提取中管硬度 (如: 偏硬, 适中, 偏软等)
            if not equipment_data['shaft_stiffness']:
                stiffness_patterns = [
                    r'中管[：:]?\s*(偏?[硬软]|适中|弹性)',
                    r'硬度[：:]?\s*(偏?[硬软]|适中)',
                    r'韧度[：:]?\s*(偏?[硬软]|适中)',
                ]
                for pattern in stiffness_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        equipment_data['shaft_stiffness'] = match.group(1)
                        logger.debug(f"    🔍 从文本提取中管硬度: {match.group(1)}")
                        break
            
            # 提取拉线磅数 (如: 24-28磅, 26LBS等)
            if not equipment_data['string_tension']:
                tension_patterns = [
                    r'(\d+[-~]\d+).*?磅',
                    r'(\d+[-~]\d+).*?[Ll][Bb][Ss]',
                    r'拉线[：:]?\s*(\d+[-~]\d+)',
                    r'穿线[：:]?\s*(\d+[-~]\d+)',
                ]
                for pattern in tension_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        equipment_data['string_tension'] = match.group(1) + '磅'
                        logger.debug(f"    🔍 从文本提取拉线磅数: {match.group(1)}磅")
                        break
                        
        except Exception as e:
            logger.error(f"从页面文本提取信息失败: {e}")

    def extract_rating_and_reviews(self, page_text, equipment_data):
        """提取评分和用户评价数据"""
        try:
            # 提取中羽评分 (如: 8.7中羽评分, 9.5中羽评分)
            rating_pattern = r'(\d+\.?\d*)中羽评分'
            rating_match = re.search(rating_pattern, page_text)
            if rating_match:
                equipment_data['rating_score'] = rating_match.group(1)
                logger.debug(f"    ⭐ 提取评分: {rating_match.group(1)}")
            
            # 提取评分分布 (如: 5★47.7%4★40.9%3★8.7%2★1.8%1★0.9%)
            distribution_pattern = r'(5★\d+\.?\d*%.*?1★\d+\.?\d*%)'
            distribution_match = re.search(distribution_pattern, page_text)
            if distribution_match:
                equipment_data['rating_distribution'] = distribution_match.group(1)
                logger.debug(f"    📊 提取评分分布: {distribution_match.group(1)[:50]}...")
            
            # 统计用户评价数量
            review_count = len(re.findall(r'Ta说', page_text))
            if review_count > 0:
                equipment_data['user_reviews_count'] = str(review_count)
                logger.debug(f"    💬 用户评价数量: {review_count}")
                
        except Exception as e:
            logger.error(f"提取评分和评价失败: {e}")

    def get_enhanced_price_info(self, equipment_id, equipment_data):
        """增强版价格信息获取"""
        try:
            price_url = f"{self.base_url}/cbo_eq/view_buy.php?eid={equipment_id}"
            price_response = self.session.get(price_url, timeout=30)
            
            if price_response.status_code == 200:
                price_soup = BeautifulSoup(price_response.text, 'html.parser')
                price_text = price_soup.get_text()
                
                # 价格提取模式
                price_patterns = {
                    'new_avg_price': [
                        r'最近全新均价[：:]\s*(\d+)',
                        r'全新均价[：:]\s*(\d+)',
                        r'新品均价[：:]\s*(\d+)',
                    ],
                    'used_avg_price': [
                        r'最近二手均价[：:]\s*(\d+)',
                        r'二手均价[：:]\s*(\d+)',
                        r'二手价格[：:]\s*(\d+)',
                    ],
                    'total_registered_users': [
                        r'总登记球友[：:]\s*(\d+)',
                        r'登记球友[：:]\s*(\d+)',
                        r'用户数[：:]\s*(\d+)',
                    ],
                }
                
                for field, patterns in price_patterns.items():
                    if not equipment_data[field]:  # 只在字段为空时填充
                        for pattern in patterns:
                            match = re.search(pattern, price_text)
                            if match:
                                equipment_data[field] = match.group(1)
                                logger.debug(f"    💰 提取{field}: {match.group(1)}")
                                break
                                
        except Exception as e:
            logger.debug(f"获取价格信息失败: {e}")

    def clean_field_value(self, value, field_type):
        """清理字段值"""
        if not value or value.strip() == '':
            return ''
        
        cleaned = value.strip()
        
        # 特定字段的清理规则
        if field_type in ['weight']:
            # 保留数字和单位
            weight_match = re.search(r'(\d+[UuGg]?)', cleaned)
            if weight_match:
                return weight_match.group(1)
        elif field_type in ['string_tension']:
            # 提取磅数
            tension_match = re.search(r'(\d+[-~]\d*)', cleaned)
            if tension_match:
                return tension_match.group(1) + '磅'
        elif field_type in ['balance_point']:
            # 提取平衡点数值
            balance_match = re.search(r'(\d+mm?|头重|头轻|平衡)', cleaned)
            if balance_match:
                return balance_match.group(1)
        
        return cleaned[:50]  # 限制长度

    def infer_missing_fields(self, equipment_data):
        """智能推断缺失字段"""
        try:
            # 从装备名称推断类型和品牌
            name = equipment_data.get('equipment_name', '').lower()
            
            # 推断装备类型
            if not equipment_data['equipment_type']:
                type_keywords = {
                    '羽毛球拍': ['拍', 'racket', 'racquet'],
                    '羽毛球鞋': ['鞋', 'shoes', 'shoe'],
                    '运动包': ['包', 'bag', '袋'],
                    '羽毛球线': ['线', 'string', '弦'],
                    '羽毛球': ['球', 'shuttle', 'cock'],
                    '运动服饰': ['服', '衣', 'shirt', 'wear'],
                    '手胶': ['胶', 'grip', '柄皮']
                }
                
                for eq_type, keywords in type_keywords.items():
                    if any(keyword in name for keyword in keywords):
                        equipment_data['equipment_type'] = eq_type
                        logger.debug(f"    🤖 推断类型: {eq_type}")
                        break
            
            # 推断品牌
            if not equipment_data['equipment_brand']:
                brand_keywords = {
                    '尤尼克斯 YONEX': ['yonex', '尤尼克斯', 'yy'],
                    '李宁 Lining': ['lining', '李宁', 'li-ning'],
                    '胜利 VICTOR': ['victor', '胜利'],
                    '川崎 Kawasaki': ['kawasaki', '川崎'],
                    '波力 Bonny': ['bonny', '波力'],
                    '凯胜 Kason': ['kason', '凯胜'],
                }
                
                for brand, keywords in brand_keywords.items():
                    if any(keyword in name for keyword in keywords):
                        equipment_data['equipment_brand'] = brand
                        logger.debug(f"    🤖 推断品牌: {brand}")
                        break
                        
        except Exception as e:
            logger.debug(f"智能推断失败: {e}")

    def save_data(self, data):
        """保存数据"""
        if not data:
            logger.warning("没有数据需要保存")
            return
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # CSV文件
        csv_file = f"output/enhanced_equipment_{timestamp}.csv"
        fieldnames = list(data[0].keys())
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        # JSON文件
        json_file = f"output/enhanced_equipment_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📁 增强数据已保存:")
        logger.info(f"  CSV: {csv_file}")
        logger.info(f"  JSON: {json_file}")
        
        return csv_file, json_file

    def analyze_data_completeness(self, data):
        """分析数据完整性"""
        if not data:
            return
            
        logger.info("\n📊 数据完整性分析:")
        logger.info(f"总数量: {len(data)}")
        
        # 分析每个字段的填充率
        field_stats = {}
        for item in data:
            for field, value in item.items():
                if field not in field_stats:
                    field_stats[field] = {'filled': 0, 'empty': 0}
                
                if value and value != '':
                    field_stats[field]['filled'] += 1
                else:
                    field_stats[field]['empty'] += 1
        
        logger.info("\n字段完整性:")
        for field, stats in field_stats.items():
            total = stats['filled'] + stats['empty']
            fill_rate = (stats['filled'] / total) * 100 if total > 0 else 0
            logger.info(f"  {field}: {fill_rate:.1f}% ({stats['filled']}/{total})")
        
        # 统计分析
        types = {}
        brands = {}
        
        for item in data:
            eq_type = item.get('equipment_type', '未知')
            types[eq_type] = types.get(eq_type, 0) + 1
            
            brand = item.get('equipment_brand', '未知')
            brands[brand] = brands.get(brand, 0) + 1
        
        if types:
            logger.info("\n类型分布:")
            for eq_type, count in sorted(types.items(), key=lambda x: x[1], reverse=True):
                logger.info(f"  {eq_type}: {count}")
        
        if brands:
            logger.info("\n品牌分布 (前5):")
            for brand, count in sorted(brands.items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.info(f"  {brand}: {count}")

def main():
    """主函数"""
    crawler = EnhancedUltimateCrawler()
    
    # 执行爬取
    data = crawler.crawl_with_full_simulation(max_total_items=8)
    
    if data:
        # 保存和分析数据
        crawler.save_data(data)
        crawler.analyze_data_completeness(data)
        
        logger.info(f"\n🎊 增强版爬取成功完成！获取了 {len(data)} 条羽毛球装备数据")
    else:
        logger.error("❌ 未获取到任何数据")

if __name__ == "__main__":
    main() 