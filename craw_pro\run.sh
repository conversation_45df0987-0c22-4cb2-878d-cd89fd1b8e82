#!/bin/bash

# 🏸 羽毛球装备爬虫快速启动脚本
# Quick Start Script for Badminton Equipment Crawler

echo "🏸 羽毛球装备爬虫快速启动脚本"
echo "================================"

# 检查虚拟环境
if [ ! -d ".venv" ]; then
    echo "❌ 虚拟环境不存在，请先运行 python -m venv .venv"
    exit 1
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source .venv/bin/activate

# 检查依赖
echo "📦 检查依赖..."
pip install -r requirements.txt

# 创建必要目录
mkdir -p output logs data

echo ""
echo "🚀 选择运行模式:"
echo "1) 运行单个装备测试 (默认)"
echo "2) 爬取指定URL"
echo "3) 批量爬取装备"
echo "4) 查看帮助信息"
echo ""

read -p "请选择 (1-4): " choice

case $choice in
    1|"")
        echo "🔬 运行单个装备测试..."
        python main.py --test
        ;;
    2)
        read -p "请输入装备URL: " url
        if [ -n "$url" ]; then
            echo "🎯 爬取指定装备: $url"
            python main.py --url "$url"
        else
            echo "❌ URL不能为空"
        fi
        ;;
    3)
        read -p "请输入爬取数量限制 (默认5): " limit
        limit=${limit:-5}
        echo "📦 批量爬取 $limit 个装备..."
        python main.py --batch --limit $limit
        ;;
    4)
        echo "📖 显示帮助信息..."
        python main.py --help
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🎉 运行完成！"
echo "📁 输出文件位于: output/"
echo "📝 日志文件位于: logs/" 