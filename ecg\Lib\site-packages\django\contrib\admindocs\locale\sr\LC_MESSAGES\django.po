# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2019-06-27 19:32+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Serbian (http://www.transifex.com/django/django/language/"
"sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

msgid "Administrative Documentation"
msgstr "Административна документација"

msgid "Home"
msgstr "Почетна"

msgid "Documentation"
msgstr "Документација"

msgid "Bookmarklets"
msgstr "Букмарклети"

msgid "Documentation bookmarklets"
msgstr "Букмарклети документације"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""

msgid "Documentation for this page"
msgstr "Документација за ову страницу"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Води од било које странице до документаицје погледа који је генерисао ту "
"страницу."

msgid "Tags"
msgstr "Тагови"

msgid "List of all the template tags and their functions."
msgstr ""

msgid "Filters"
msgstr "Филтери"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""

msgid "Models"
msgstr "Модели"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

msgid "Views"
msgstr "Вјуеви"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

msgid "Please install docutils"
msgstr ""

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#, python-format
msgid "Model: %(name)s"
msgstr ""

msgid "Fields"
msgstr "Поља"

msgid "Field"
msgstr "Поље"

msgid "Type"
msgstr "Тип"

msgid "Description"
msgstr "Опис"

msgid "Methods with arguments"
msgstr "Метода са аргументима"

msgid "Method"
msgstr "Метод"

msgid "Arguments"
msgstr "Аргументи"

msgid "Back to Model documentation"
msgstr "Назад на документацију о Моделима"

msgid "Model documentation"
msgstr "Документација о Моделима"

msgid "Model groups"
msgstr "Групе модела"

msgid "Templates"
msgstr "Шаблони"

#, python-format
msgid "Template: %(name)s"
msgstr "Шаблон: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "Шаблон: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr ""

msgid "(does not exist)"
msgstr "(не постоји)"

msgid "Back to Documentation"
msgstr "Назада на документацију"

msgid "Template filters"
msgstr "Филтери шаблона"

msgid "Template filter documentation"
msgstr ""

msgid "Built-in filters"
msgstr "Уграђени филтери"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr ""

msgid "Template tag documentation"
msgstr ""

msgid "Built-in tags"
msgstr "Уграђене ознаке"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr "Види: %(name)s"

msgid "Context:"
msgstr "Контекст:"

msgid "Templates:"
msgstr "Шаблони:"

msgid "Back to View documentation"
msgstr "Назад на документацију о View-овима"

msgid "View documentation"
msgstr "View документација"

msgid "Jump to namespace"
msgstr ""

msgid "Empty namespace"
msgstr ""

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "tag:"
msgstr "таг:"

msgid "filter:"
msgstr "филтер:"

msgid "view:"
msgstr "вју:"

#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Модел %(model_name)r није пронађен у апликацији %(app_label)r"

msgid "model:"
msgstr "модел:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "повезани објекти класе `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "класе `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "сви повезани објекти %s"

#, python-format
msgid "number of %s"
msgstr "број повезаних објеката %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s не изгледа као „urlpattern“ објекат"
