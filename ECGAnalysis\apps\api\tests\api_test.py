import ast
import json
import requests
import sys
import os
import pandas as pd
import logging
import tensorflow as tf
import cProfile
import pstats
from pstats import SortKey
import io
import time
import argparse
from concurrent.futures import ThreadPoolExecutor
from functools import wraps
from apps.utils.logger_helper import Logger

# 修改日志格式,只输出消息内容
formatter = logging.Formatter('%(message)s')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')

# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录的路径
project_root = os.path.abspath(os.path.join(current_dir, '../../..'))
# 将项目根目录添加到 sys.path
sys.path.append(project_root)

import global_settings

# 创建全局会话
session = requests.Session()

# 设置连接池最大连接数
adapter = requests.adapters.HTTPAdapter(
    pool_connections=100,  # 连接池连接数
    pool_maxsize=100,  # 连接池最大连接数
    max_retries=3  # 最大重试次数
)
session.mount('http://', adapter)
session.mount('https://', adapter)

# 全局变量
TOKEN_EXPIRY_TIME = 3600  # token有效期(秒)
TOKEN_REFRESH_THRESHOLD = 3300  # token刷新阈值(秒)
token_obtain_time = 0  # token获取时间
token_value = None  # 全局token值
REQUEST_INTERVAL = 0.1  # 请求间隔(秒)

ERROR_CODE_MAP = {
    401: "信号噪声过高",
    402: "算法内部错误",
    403: "心律失常诊断错误",
    4: "Token过期或无效",
    # ... 其他错误代码 ...
}


def timing_decorator(func):
    """性能监控装饰器"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logging.info(f"{func.__name__} took {end_time - start_time:.2f} seconds")
        return result

    return wrapper


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='ECG Analysis Tool')
    parser.add_argument('--mode', type=int, choices=[1, 2], help='处理模式：1=单文件，2=文件夹')
    parser.add_argument('--input', type=str, help='输入文件或文件夹路径')
    parser.add_argument('--output', type=str, help='输出文件夹路径')
    return parser.parse_args()


def get_token(force_refresh=False, refresh_token=False):
    """获取API的认证令牌，支持缓存和自动刷新

    参数:
        force_refresh (bool): 是否强制刷新获取新token
        refresh_token (bool): 如果为True，仅刷新token的过期时间，不重新获取
    """
    global token_value, token_obtain_time, session

    current_time = time.time()

    # 如果仅刷新token的过期时间
    if refresh_token and token_value:
        token_obtain_time = current_time
        logging.info(f"刷新token的过期时间，token: {token_value[:10]}...")
        return token_value

    # 如果token存在且未过期且不强制刷新，直接返回
    if not force_refresh and token_value and (current_time - token_obtain_time) < TOKEN_REFRESH_THRESHOLD:
        return token_value

    try:
        login_url = global_settings.heartVoice['login']['url']
        client_id = global_settings.heartVoice['login']['clientId']
        client_secret = global_settings.heartVoice['login']['clientSecret']

        params = {
            "clientId": client_id,
            "clientSecret": client_secret
        }

        logging.info(f"尝试获取token，URL: {login_url}")
        logging.info(f"认证参数: clientId={client_id}")

        # 重试机制
        max_retries = 3
        retry_count = 0
        retry_delay = 1  # 初始延迟1秒

        while retry_count < max_retries:
            try:
                # 重置会话，避免之前的连接问题
                if retry_count > 0:
                    session = requests.Session()
                    adapter = requests.adapters.HTTPAdapter(
                        pool_connections=100,
                        pool_maxsize=100,
                        max_retries=3
                    )
                    session.mount('http://', adapter)
                    session.mount('https://', adapter)
                    logging.info(f"重试获取token (第{retry_count}次)")

                headers = {'Content-Type': 'application/x-www-form-urlencoded'}
                resp = session.post(login_url, data=params, headers=headers, timeout=10)
                logging.info(f"Token请求状态码: {resp.status_code}")

                resp.raise_for_status()
                resp_json = resp.json()

                if 'data' in resp_json and 'token' in resp_json['data']:
                    token_value = resp_json['data']['token']
                    token_obtain_time = current_time
                    logging.info(f"成功获取token: {token_value[:10]}...")
                    return token_value
                else:
                    logging.error(f"Token响应格式错误: {resp_json}")
                    retry_count += 1
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避

            except requests.exceptions.RequestException as e:
                logging.error(f"Token请求错误: {e}")
                retry_count += 1
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避

        logging.error("获取token失败，已达到最大重试次数")
        return None

    except Exception as e:
        logging.error(f"获取token时发生错误: {e}")
        return None


@timing_decorator
def profile_api_call(ecg_data, fs, token):
    """对单次API调用进行性能分析"""
    profiler = cProfile.Profile()
    profiler.enable()

    result, sna_features = ecg_analysis(ecg_data, fs, token)

    profiler.disable()

    # 创建字符串流来捕获输出
    s = io.StringIO()
    stats = pstats.Stats(profiler, stream=s).sort_stats(SortKey.TIME)
    stats.print_stats(10)  # 只打印前10个最耗时的函数

    logging.info("Performance Analysis:\n" + s.getvalue())
    return result, sna_features


def ecg_analysis(ecg_data, fs, token, retry_count=0):
    """调用心电分析API，支持自动重试和token刷新"""
    global session, token_value

    # 添加请求间隔，避免请求过于频繁
    time.sleep(REQUEST_INTERVAL)

    # 检查token是否需要刷新
    current_time = time.time()
    if current_time - token_obtain_time > TOKEN_REFRESH_THRESHOLD:
        logging.info("Token即将过期，尝试刷新")
        new_token = get_token(force_refresh=True)
        if new_token:
            token = new_token
            token_value = new_token

    analysis_url = 'http://minute.ecggpt.test.aiweihe.com/api/diagnose/arrhythmia/'
    headers = {
        "Content-Type": "application/json",
        "X-Auth-Token": token
    }

    # 直接使用列表数据
    signal_str = str(ecg_data)  # 如果已经是列表，直接转换为字符串

    params = {
        "signal": signal_str,
        "fs": int(fs),  # 确保fs是整数
        "adc_gain": 1,
        "adc_zero": 0,
        'union_id': 'test_user'
    }

    try:
        logging.info("发送API请求...")
        logging.info(f"数据长度: {len(ecg_data)}")
        logging.info(f"采样率: {fs}")

        # 设置超时，避免请求卡住
        resp = session.post(analysis_url, headers=headers, json=params, timeout=30)

        if resp.status_code == 401:
            # 401可能是token过期，尝试刷新token并重试
            if retry_count < 2:  # 最多重试2次
                logging.warning("Token可能已过期，尝试刷新并重试")
                new_token = get_token(force_refresh=True)
                if new_token:
                    return ecg_analysis(ecg_data, fs, new_token, retry_count + 1)
            logging.error("Token刷新后仍然失败")
            return None, None
        elif resp.status_code != 200:
            # 其他错误状态码
            logging.error(f"API响应错误: {resp.status_code}")

            # 如果是服务器错误，尝试重试
            if resp.status_code >= 500 and retry_count < 3:
                retry_delay = 1 * (2 ** retry_count)  # 指数退避
                logging.warning(f"服务器错误，{retry_delay}秒后重试 (第{retry_count + 1}次)")
                time.sleep(retry_delay)
                return ecg_analysis(ecg_data, fs, token, retry_count + 1)

            return None, None

        resp_json = resp.json()

        # 检查是否是错误代码4（token过期）
        if resp_json.get("code") == 4:
            if retry_count < 2:  # 最多重试2次
                logging.warning("Token已过期（错误代码4），尝试刷新token并重试")
                new_token = get_token(force_refresh=True)
                if new_token:
                    return ecg_analysis(ecg_data, fs, new_token, retry_count + 1)
            logging.error("Token刷新后仍然失败")
            return None, None

        if resp_json.get("code") == 0:
            # API调用成功，刷新token的过期时间
            get_token(refresh_token=True)
            logging.info("API调用成功，已刷新token的过期时间")

            data = resp_json.get("data", {})

            # 检查噪音情况
            signal_quantity = data.get('SignalQuantity', 1)  # 获取信号质量，默认为1（无噪音）
            if signal_quantity == -1:
                logging.warning("检测到噪音：信号质量差")

            sna_feature = data.get('ArrhythmiaDiagnosis', {}).get('SNA', False)
            sna_features = data.get('SNA_Features', {})

            # 只输出关键诊断信息
            arrhythmia = data.get('ArrhythmiaDiagnosis', {})
            positive_diagnoses = [k for k, v in arrhythmia.items() if v == 1]
            if positive_diagnoses:
                logging.info(f"诊断结果: {', '.join(positive_diagnoses)}")

            metrics = data.get('HealthMetrics', {})
            logging.info(f"健康指标: 压力={metrics.get('Pressure', 0)}, "
                         f"疲劳={metrics.get('Fatigue', 0)}, "
                         f"活力={metrics.get('Vitality', 0)}")

            pqrstc = data.get('PQRSTC', {})
            logging.info(f"心率: {pqrstc.get('HR', 0)} bpm")

            return data, sna_features
        else:
            error_code = resp_json.get("code")
            error_message = resp_json.get("msg")
            detailed_error_message = ERROR_CODE_MAP.get(error_code, error_message)
            logging.error(f"API返回错误: {detailed_error_message} (code: {error_code})")

            # 如果是token相关错误，尝试重试
            if error_code in [401, 403, 4] and retry_count < 2:
                logging.warning("可能是token过期，尝试刷新token并重试")
                new_token = get_token(force_refresh=True)
                if new_token:
                    return ecg_analysis(ecg_data, fs, new_token, retry_count + 1)
            return None, None

    except requests.exceptions.RequestException as e:
        logging.error(f"API请求错误: {e}")

        # 对于连接超时错误，尝试重试
        if isinstance(e, (requests.exceptions.ConnectionError, requests.exceptions.Timeout)) and retry_count < 3:
            retry_delay = 1 * (2 ** retry_count)  # 指数退避
            logging.warning(f"连接错误，{retry_delay}秒后重试 (第{retry_count + 1}次)")
            time.sleep(retry_delay)

            # 重置会话
            session = requests.Session()
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=100,
                pool_maxsize=100,
                max_retries=3
            )
            session.mount('http://', adapter)
            session.mount('https://', adapter)

            return ecg_analysis(ecg_data, fs, token, retry_count + 1)

        return None, None


def get_disease_name(arrhythmia_diagnosis):
    """将识别到的疾病转换为文字"""
    disease_mapping = {
        'SN': '窦性心律',
        'SNA': '窦性心律不齐',
        'SNT': '窦性心动过速',
        'SNB': '窦性心动过缓',
        'PVC': '室性早搏',
        'PSC': '不确定的早搏类型',
        'PJC': '交界性早搏',
        'PAC': '房性早搏',
        'VT': '室性心动过速',
        'SVT': '室上性心动过速',
        'AFL': '心房扑动',
        'AF': '心房颤动',
        'WPW': '应激综合征',
        'VE': '室性逸搏',
        'JE': '交界性逸搏',
        'AE': '房性逸搏',
        'AVBI': '一度房室传导阻滞',
        'AVBII': '二度房室传导阻滞',
        'AVBIII': '三度房室传导阻滞',
        'IVB': '室内传导阻滞',
        'LBBB': '左束支传导阻滞',
        'RBBB': '右束支传导阻滞',
        'LAFB': '左前分支传导阻滞',
        'BRU': 'Brugada综合征',
        'LQT': 'QT间期延长',
        'bPVC': '成对室早'
    }

    diseases = []
    for key, value in arrhythmia_diagnosis.items():
        if value == 1:
            diseases.append(disease_mapping.get(key, '未知疾病'))

    return ', '.join(diseases)


def get_multi_label_disease_name(multi_label_diagnosis):
    """将多结论诊断转换为中文"""
    disease_mapping = {
        'SN': '窦性心律',
        'SNA': '窦性心律不齐',
        'SNT': '窦性心动过速',
        'SNB': '窦性心动过缓',
        'PVC': '室性早搏',
        'PSC': '不确定的早搏类型',
        'PJC': '交界性早搏',
        'PAC': '房性早搏',
        'VT': '室性心动过速',
        'SVT': '室上性心动过速',
        'AFL': '心房扑动',
        'AF': '心房颤动',
        'WPW': 'WPW综合征',
        'VE': '室性逸搏',
        'JE': '交界性逸搏',
        'AE': '房性逸搏',
        'AVBI': '一度房室传导阻滞',
        'AVBII': '二度房室传导阻滞',
        'AVBIII': '三度房室传导阻滞',
        'IVB': '室内传导阻滞',
        'LBBB': '左束支传导阻滞',
        'RBBB': '右束支传导阻滞',
        'LAFB': '左前分支传导阻滞',
        'BRU': 'Brugada综合征',
        'LQT': 'QT间期延长',
        'bPVC': '成对室早',
        # 添加其他可能的多结论诊断映射
        'ISC': '心肌缺血',
        'LVH': '左心室肥大',
        'RVH': '右心室肥大',
        'LAH': '左心房肥大',
        'RAH': '右心房肥大',
        'MI': '心肌梗死'
    }

    # 将每个诊断结果转换为中文
    chinese_diagnoses = []
    for diagnosis in multi_label_diagnosis:
        chinese_diagnosis = disease_mapping.get(diagnosis, diagnosis)  # 如果找不到映射，保留原始值
        chinese_diagnoses.append(chinese_diagnosis)

    return chinese_diagnoses


@timing_decorator
def process_single_file(input_file, sampling_rate, token):
    """处理单个文件的函数 - 单个处理模式"""
    results_list = []
    chunk_size = 1

    try:
        for chunk in pd.read_csv(input_file, encoding='utf-8', header=None, chunksize=chunk_size):
            for idx, row in chunk.iterrows():
                try:
                    # 数据预处理
                    ecg_data = pd.to_numeric(row, errors='coerce').dropna().tolist()

                    # 数据验证
                    if len(ecg_data) < sampling_rate * 10:
                        logging.warning(f"Row {idx}: 数据长度不足 ({len(ecg_data)} < {sampling_rate * 10})")
                        continue

                    # 截取所需长度的数据
                    ecg_signal = ecg_data[:sampling_rate * 10]

                    # 单个处理
                    result, _ = ecg_analysis(ecg_signal, sampling_rate, token)

                    if result:
                        arrhythmia_diagnosis = result.get('ArrhythmiaDiagnosis', {})
                        diseases = get_disease_name(arrhythmia_diagnosis)
                        multi_label_diagnosis = result.get('MultiLabelDiagnosis', [])
                        chinese_multi_label = get_multi_label_disease_name(multi_label_diagnosis)
                        signal_quantity = result.get('SignalQuantity', 1)  # 获取信号质量

                        # 更新日志输出
                        logging.info(f"Row {idx + 1} 诊断结果:")
                        if signal_quantity != 1:
                            logging.info(f"  噪音状态: {'高噪音' if signal_quantity == 1 else '极高噪音'}")
                        logging.info(f"  传统诊断结果: {diseases}")
                        logging.info(f"  多结论诊断结果: {', '.join(chinese_multi_label)}")
                        logging.info(f"  详细诊断: {json.dumps(arrhythmia_diagnosis, ensure_ascii=False)}")
                        logging.info(f"  ECG年龄: {result.get('ECGAge')}")
                        logging.info("  健康指标:")
                        health_metrics = result.get('HealthMetrics', {})
                        for metric, value in health_metrics.items():
                            logging.info(f"    {metric}: {value}")
                        logging.info("-" * 50)

                        new_row = {
                            'Row': idx + 1,
                            'ECGAge': result.get('ECGAge'),
                            'ArrhythmiaDiagnosis': arrhythmia_diagnosis,
                            'MultiLabelDiagnosis': chinese_multi_label,
                            'HealthMetrics': health_metrics,
                            'PQRSTC': result.get('PQRSTC', {}),
                            'Status': '成功',
                            'Diseases': diseases,
                            'NoiseStatus': signal_quantity,  # 使用信号质量值作为噪音状态
                            'ErrorMessage': ''
                        }
                    else:
                        logging.warning(f"Row {idx + 1}: 处理失败")
                        new_row = {
                            'Row': idx + 1,
                            'ECGAge': None,
                            'ArrhythmiaDiagnosis': None,
                            'MultiLabelDiagnosis': None,
                            'HealthMetrics': None,
                            'PQRSTC': None,
                            'Status': '失败',
                            'Diseases': None,
                            'NoiseStatus': None,  # 添加噪音状态字段
                            'ErrorMessage': '接口调用失败'
                        }

                    results_list.append(new_row)

                except Exception as e:
                    logging.error(f"处理第 {idx + 1} 行数据时出错: {str(e)}")
                    results_list.append({
                        'Row': idx + 1,
                        'ECGAge': None,
                        'ArrhythmiaDiagnosis': None,
                        'MultiLabelDiagnosis': None,
                        'HealthMetrics': None,
                        'PQRSTC': None,
                        'Status': '失败',
                        'Diseases': None,
                        'NoiseStatus': None,  # 添加噪音状态字段
                        'ErrorMessage': f'处理错误: {str(e)}'
                    })

    except Exception as e:
        logging.error(f"处理文件时出错: {str(e)}")

    # 转换为DataFrame并返回
    df = pd.DataFrame(results_list)

    # 打印成功率统计
    total = len(df)
    success = len(df[df['Status'] == '成功'])
    logging.info(f"\n处理统计:")
    logging.info(f"总数: {total}")
    logging.info(f"成功: {success}")
    logging.info(f"失败: {total - success}")
    logging.info(f"成功率: {(success / total * 100):.2f}%")

    return df


def main():
    # 创建主性能分析器
    main_profiler = cProfile.Profile()
    main_profiler.enable()

    try:
        # 获取命令行参数
        args = parse_arguments()

        # 如果没有命令行参数，使用交互式输入
        if args.mode is None:
            mode = input("请选择处理模式（1: 单个文件，2: 整个文件夹）：")
            mode = int(mode)
        else:
            mode = args.mode

        sampling_rate = 250
        token = get_token(force_refresh=True)  # 强制刷新获取新token
        if not token:
            logging.error("无法获取到有效的token，程序结束")
            sys.exit(1)

        # 记录开始时间，用于定期检查token是否需要刷新
        process_start_time = time.time()

        if mode == 1:
            input_file = args.input or input("请输入要处理的CSV文件完整路径：").strip('"')
            if not os.path.exists(input_file):
                logging.error("文件不存在！")
                sys.exit(1)

            output_folder = os.path.dirname(input_file)
            file_name = os.path.basename(input_file)
            output_file_name = f"{os.path.splitext(file_name)[0]}_acc.csv"
            output_file_path = os.path.join(output_folder, output_file_name)

            results_df = process_single_file(input_file, sampling_rate, token)
            results_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            logging.info(f"分析结果已保存到: {output_file_path}")

        elif mode == 2:
            input_folder = args.input or input("请输入要处理的文件夹路径：").strip('"')
            output_folder = args.output or input("请输入结果保存的文件夹路径：").strip('"')

            if not os.path.exists(input_folder):
                logging.error("输入文件夹不存在！")
                sys.exit(1)

            if not os.path.exists(output_folder):
                os.makedirs(output_folder)

            csv_files = [f for f in os.listdir(input_folder) if f.endswith('.csv')]
            logging.info(f"找到以下CSV文件：{csv_files}")

            for csv_file in csv_files:
                input_file_path = os.path.join(input_folder, csv_file)
                output_file_name = f"{os.path.splitext(csv_file)[0]}_acc.csv"
                output_file_path = os.path.join(output_folder, output_file_name)

                logging.info(f"开始处理文件：{csv_file}")
                results_df = process_single_file(input_file_path, sampling_rate, token)
                results_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
                logging.info(f"文件 {csv_file} 的分析结果已保存到: {output_file_path}")

        else:
            logging.error("无效的选择！")
            sys.exit(1)

    finally:
        main_profiler.disable()

        stats_file = "api_test_performance.txt"
        with open(stats_file, 'w', encoding='utf-8') as f:
            stats = pstats.Stats(main_profiler, stream=f)
            stats.sort_stats(SortKey.TIME)
            stats.print_stats()

        logging.info(f"性能分析结果已保存到: {stats_file}")

        logging.info("\n=== 性能分析摘要 ===")
        stats = pstats.Stats(main_profiler)
        stats.sort_stats(SortKey.TIME)
        stats.print_stats(10)


if __name__ == '__main__':
    main()