
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorPasses();


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorSparseTensorConversion();
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorSparseTensorConversion();


/* Create SparseTensor Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateSparseTensorSparsification();
MLIR_CAPI_EXPORTED void mlirRegisterSparseTensorSparsification();



#ifdef __cplusplus
}
#endif
