from django.db import models


def get_data_ecg_detail_model(date_str):
    class TDataEcgDetail(models.Model):
        id = models.BigAutoField(primary_key=True)
        union_id = models.CharField(max_length=255, blank=True, null=True)
        check_date = models.DateTimeField(blank=True, null=True)
        data_ecg_id = models.BigIntegerField(blank=True, null=True)
        disease_code = models.CharField(max_length=255, blank=True, null=True)
        total_episodes = models.IntegerField(blank=True, null=True)
        total_duration = models.FloatField(blank=True, null=True)
        fastest_hr = models.IntegerField(blank=True, null=True)
        longest_duration = models.IntegerField(blank=True, null=True)
        longest_rr = models.IntegerField(blank=True, null=True)
        single_count = models.IntegerField(blank=True, null=True)
        pair_count = models.IntegerField(blank=True, null=True)
        bigeminy_count = models.IntegerField(blank=True, null=True)
        trigeminy_count = models.IntegerField(blank=True, null=True)
        run_count = models.IntegerField(blank=True, null=True)
        max_consecutive = models.IntegerField(blank=True, null=True)
        fastest_run_hr = models.IntegerField(blank=True, null=True)
        slowest_run_hr = models.IntegerField(blank=True, null=True)
        hr_total_count = models.IntegerField(blank=True, null=True)
        create_time = models.DateTimeField(blank=True, null=True)

        class Meta:
            managed = False
            db_table = f't_data_ecg_detail_{date_str}'

    return TDataEcgDetail
