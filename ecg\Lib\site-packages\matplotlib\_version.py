
# This file was generated by 'versioneer.py' (0.15) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json
import sys

version_json = '''
{
 "dirty": false,
 "error": null,
 "full-revisionid": "a1a5298b0d4a8d8230103d0fa8d369fca7fdeea0",
 "version": "3.2.2"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
