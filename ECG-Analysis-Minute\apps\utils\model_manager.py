"""
优化的模型管理器，解决内存泄漏和崩溃问题
"""
import os
import gc
import threading
import time
import psutil
import warnings
from typing import Dict, Any, Optional
from functools import lru_cache
import tensorflow as tf
from tensorflow.keras.models import load_model
import numpy as np
import pandas as pd
from apps.utils.logger_helper import Logger

# 禁用scikit-learn版本警告
warnings.filterwarnings("ignore", category=UserWarning, module="sklearn")

class ModelManager:
    """
    单例模式的模型管理器，负责模型缓存、内存管理和GPU资源优化
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ModelManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._models: Dict[str, Any] = {}
        self._model_lock = threading.Lock()
        self._last_cleanup = time.time()
        self._cleanup_interval = 300  # 5分钟清理一次
        self._max_memory_usage = 80  # 最大内存使用率80%
        
        # 配置TensorFlow GPU内存增长
        self._configure_tensorflow()
        
        Logger().info("ModelManager initialized with memory optimization")
    
    def _configure_tensorflow(self):
        """配置TensorFlow GPU内存管理"""
        try:
            # 获取GPU设备
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                for gpu in gpus:
                    # 启用内存增长，避免一次性占用所有GPU内存
                    tf.config.experimental.set_memory_growth(gpu, True)
                    
                    # 设置虚拟GPU内存限制（可选）
                    # tf.config.experimental.set_virtual_device_configuration(
                    #     gpu,
                    #     [tf.config.experimental.VirtualDeviceConfiguration(memory_limit=6144)]  # 6GB
                    # )
                Logger().info(f"Configured {len(gpus)} GPU(s) with memory growth enabled")
            else:
                Logger().info("No GPU devices found, using CPU")
                
            # 设置TensorFlow日志级别，减少输出
            tf.get_logger().setLevel('ERROR')
            os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
            
        except Exception as e:
            Logger().error(f"Failed to configure TensorFlow: {e}")
    
    def get_model(self, model_path: str, model_type: str = "default") -> Any:
        """
        获取模型，支持缓存和自动清理
        
        Args:
            model_path: 模型文件路径
            model_type: 模型类型标识
            
        Returns:
            加载的模型对象
        """
        model_key = f"{model_type}:{model_path}"
        
        # 检查是否需要清理内存
        self._check_and_cleanup()
        
        with self._model_lock:
            # 如果模型已缓存，直接返回
            if model_key in self._models:
                Logger().debug(f"Using cached model: {model_key}")
                return self._models[model_key]
            
            # 检查内存使用情况
            if self._get_memory_usage() > self._max_memory_usage:
                Logger().warning("High memory usage detected, clearing model cache")
                self._clear_cache()
                self._force_garbage_collection()
            
            # 加载新模型
            try:
                Logger().info(f"Loading model: {model_path}")
                
                if not os.path.exists(model_path):
                    raise FileNotFoundError(f"Model file not found: {model_path}")
                
                # 根据文件扩展名选择加载方式
                if model_path.endswith(('.h5', '.hdf5')):
                    model = load_model(model_path, compile=False)
                elif model_path.endswith('.keras'):
                    model = tf.keras.models.load_model(model_path, compile=False)
                else:
                    # 尝试作为SavedModel加载
                    model = tf.saved_model.load(model_path)
                
                # 缓存模型
                self._models[model_key] = model
                Logger().info(f"Model loaded and cached: {model_key}")
                
                return model
                
            except Exception as e:
                Logger().error(f"Failed to load model {model_path}: {e}")
                raise
    
    def predict_with_cached_model(self, model_path: str, input_data: np.ndarray, 
                                model_type: str = "default") -> np.ndarray:
        """
        使用缓存的模型进行预测
        
        Args:
            model_path: 模型路径
            input_data: 输入数据
            model_type: 模型类型
            
        Returns:
            预测结果
        """
        try:
            model = self.get_model(model_path, model_type)
            
            # 执行预测
            with tf.device('/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'):
                predictions = model.predict(input_data, verbose=0)
            
            # 预测后清理GPU内存
            if tf.config.list_physical_devices('GPU'):
                tf.keras.backend.clear_session()
                
            return predictions
            
        except Exception as e:
            Logger().error(f"Prediction failed: {e}")
            raise
    
    def _check_and_cleanup(self):
        """检查并执行定期清理"""
        current_time = time.time()
        if current_time - self._last_cleanup > self._cleanup_interval:
            self._periodic_cleanup()
            self._last_cleanup = current_time
    
    def _periodic_cleanup(self):
        """定期清理内存"""
        try:
            memory_usage = self._get_memory_usage()
            Logger().info(f"Current memory usage: {memory_usage:.1f}%")
            
            if memory_usage > self._max_memory_usage:
                Logger().warning("High memory usage, performing cleanup")
                self._clear_cache()
                self._force_garbage_collection()
                
                # 清理TensorFlow会话
                tf.keras.backend.clear_session()
                
                # 如果有GPU，清理GPU内存
                if tf.config.list_physical_devices('GPU'):
                    try:
                        # 这个方法在某些TensorFlow版本中可能不可用
                        tf.config.experimental.reset_memory_stats('GPU:0')
                    except:
                        pass
                
                new_memory_usage = self._get_memory_usage()
                Logger().info(f"Memory usage after cleanup: {new_memory_usage:.1f}%")
                
        except Exception as e:
            Logger().error(f"Cleanup failed: {e}")
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用率"""
        try:
            return psutil.virtual_memory().percent
        except:
            return 0.0
    
    def _clear_cache(self):
        """清空模型缓存"""
        with self._model_lock:
            Logger().info(f"Clearing {len(self._models)} cached models")
            self._models.clear()
    
    def _force_garbage_collection(self):
        """强制垃圾回收"""
        for _ in range(3):  # 多次调用确保彻底清理
            gc.collect()
        Logger().debug("Forced garbage collection completed")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cached_models': len(self._models),
            'memory_usage': self._get_memory_usage(),
            'last_cleanup': self._last_cleanup,
            'model_keys': list(self._models.keys())
        }
    
    def clear_all_cache(self):
        """清空所有缓存"""
        self._clear_cache()
        self._force_garbage_collection()
        tf.keras.backend.clear_session()
        Logger().info("All caches cleared")


# 全局模型管理器实例
model_manager = ModelManager()


def get_model_manager() -> ModelManager:
    """获取全局模型管理器实例"""
    return model_manager


# 装饰器：自动内存管理
def with_memory_management(func):
    """装饰器：为函数添加内存管理"""
    def wrapper(*args, **kwargs):
        try:
            # 执行前检查内存
            initial_memory = model_manager._get_memory_usage()
            if initial_memory > 85:  # 85%以上强制清理
                Logger().warning(f"High memory usage before function: {initial_memory:.1f}%")
                model_manager._force_garbage_collection()
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 执行后检查内存
            final_memory = model_manager._get_memory_usage()
            if final_memory > 90:  # 90%以上立即清理
                Logger().warning(f"Critical memory usage after function: {final_memory:.1f}%")
                model_manager._clear_cache()
                model_manager._force_garbage_collection()
                tf.keras.backend.clear_session()
            
            return result
            
        except Exception as e:
            Logger().error(f"Function execution failed: {e}")
            # 出错时也进行清理
            model_manager._force_garbage_collection()
            raise
    
    return wrapper
