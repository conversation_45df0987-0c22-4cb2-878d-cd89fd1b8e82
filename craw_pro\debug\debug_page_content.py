#!/usr/bin/env python3
"""
调试页面内容，查找中羽评分和装备简介
"""
import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import logging
from selenium.webdriver.common.keys import Keys

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_driver():
    """设置Chrome驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def solve_verification(driver):
    """解决验证问题"""
    try:
        time.sleep(2)
        body_text = driver.find_element(By.TAG_NAME, "body").text
        verification_text = body_text.strip()
        
        if "问题" not in verification_text:
            return True
            
        logger.info(f"🤖 验证问题: {verification_text}")
        
        answer = None
        
        # 羽毛球知识问题
        if "羽毛球有几根毛" in verification_text or "几根毛" in verification_text:
            answer = "16"
            logger.info("🏸 羽毛球知识: 羽毛球有16根毛")
        elif "ABC后3个大写字母" in verification_text:
            answer = "DEF"
            logger.info("🔤 字母序列: ABC后3个字母是 DEF")
        elif "XYZ前3个大写字母" in verification_text:
            answer = "UVW"
            logger.info("🔤 字母序列: XYZ前3个字母是 UVW")
        elif "羽毛球英文怎么写" in verification_text:
            answer = "badminton"
            logger.info("🏸 羽毛球英文: badminton")
        else:
            # 数学计算模式
            math_patterns = [
                r'(\d+)\s*[+＋]\s*(\d+)',
                r'(\d+)\s*[-－]\s*(\d+)',
                r'(\d+)\s*[×*]\s*(\d+)',
                r'(\d+)\s*[÷/]\s*(\d+)'
            ]
            
            for pattern in math_patterns:
                match = re.search(pattern, verification_text)
                if match:
                    num1, num2 = int(match.group(1)), int(match.group(2))
                    if '+' in pattern or '＋' in pattern:
                        answer = str(num1 + num2)
                    elif '-' in pattern or '－' in pattern:
                        answer = str(num1 - num2)
                    elif '×' in pattern or '*' in pattern:
                        answer = str(num1 * num2)
                    elif '÷' in pattern or '/' in pattern:
                        answer = str(num1 // num2)
                    logger.info(f"🧮 计算结果: {num1} ? {num2} = {answer}")
                    break
        
        if answer:
            input_element = driver.find_element(By.CSS_SELECTOR, 'input[name="a"]')
            input_element.clear()
            input_element.send_keys(answer)
            logger.info(f"✍️ 输入答案: {answer}")
            input_element.send_keys(Keys.RETURN)
            time.sleep(3)
            return True
            
    except Exception as e:
        logger.warning(f"⚠️ 验证处理失败: {e}")
    
    return False

def debug_page_content():
    """调试页面内容"""
    driver = setup_driver()
    
    try:
        # 访问装备页面
        url = "https://www.badmintoncn.com/cbo_eq/view.php?eid=22974"
        logger.info(f"访问: {url}")
        driver.get(url)
        time.sleep(5)
        
        # 处理验证
        if solve_verification(driver):
            logger.info("✅ 验证成功")
        
        # 获取页面源码
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        clean_text = soup.get_text()
        
        print("=" * 80)
        print("🔍 搜索中羽评分相关内容:")
        print("=" * 80)
        
        # 搜索评分相关内容
        rating_patterns = [
            r'中羽评分[^0-9]*([0-9\.]+)',
            r'评分[^0-9]*([0-9\.]+)',
            r'分数[^0-9]*([0-9\.]+)',
            r'([0-9\.]+)\s*分'
        ]
        
        lines = clean_text.split('\n')
        for i, line in enumerate(lines):
            line = line.strip()
            if '评分' in line or '分数' in line:
                print(f"行 {i}: {line}")
                
                # 检查前后几行
                for j in range(max(0, i-2), min(len(lines), i+3)):
                    if j != i:
                        context_line = lines[j].strip()
                        if context_line:
                            print(f"  上下文 {j}: {context_line}")
        
        # 查找所有表格
        print("\n" + "=" * 80)
        print("📊 分析所有表格:")
        print("=" * 80)
        
        tables = soup.find_all('table')
        for i, table in enumerate(tables):
            table_text = table.get_text()
            if '评分' in table_text or '简介' in table_text:
                print(f"\n表格 {i+1} (包含评分/简介):")
                rows = table.find_all('tr')
                for j, row in enumerate(rows):
                    cells = row.find_all(['td', 'th'])
                    if cells:
                        row_text = ' | '.join([cell.get_text(strip=True) for cell in cells])
                        print(f"  行 {j+1}: {row_text}")
        
        print("\n" + "=" * 80)
        print("📝 搜索装备简介相关内容:")
        print("=" * 80)
        
        # 搜索简介相关内容
        for i, line in enumerate(lines):
            line = line.strip()
            if '简介' in line or '介绍' in line:
                print(f"行 {i}: {line}")
                
                # 检查后续几行（简介内容）
                for j in range(i+1, min(len(lines), i+6)):
                    content_line = lines[j].strip()
                    if content_line and len(content_line) > 10:
                        print(f"  内容 {j}: {content_line}")
        
        # 查找特定的div或td元素
        print("\n" + "=" * 80)
        print("🔍 查找特定HTML结构:")
        print("=" * 80)
        
        # 查找包含"评分"的元素
        rating_elements = soup.find_all(text=lambda text: text and '评分' in text)
        for elem in rating_elements[:5]:
            parent = elem.parent
            print(f"评分元素: {elem.strip()}")
            print(f"父元素: {parent.name} - {parent.get_text(strip=True)[:100]}")
        
        # 查找包含"简介"的元素
        intro_elements = soup.find_all(text=lambda text: text and '简介' in text)
        for elem in intro_elements[:5]:
            parent = elem.parent
            print(f"简介元素: {elem.strip()}")
            print(f"父元素: {parent.name} - {parent.get_text(strip=True)[:100]}")
            
        # 保存完整HTML用于后续分析
        with open('debug_page_full.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        print(f"\n完整页面HTML已保存到: debug_page_full.html")
            
    except Exception as e:
        logger.error(f"调试失败: {e}")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_page_content() 