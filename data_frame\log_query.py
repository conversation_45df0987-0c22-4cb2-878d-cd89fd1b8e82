import mysql.connector
from mysql.connector import Error
import csv
import datetime
import gzip
import json
import requests
import traceback

# 数据库连接配置
db_config = {
    'host': '**************',
    'port': 3308,
    'user': 'ai',
    'password': 'z8^#g4r4mz',
    'database': 'ecg_analysis_test'
}

def test_db_connection(config):
    """
    测试数据库连接。

    Args:
        config (dict): 包含数据库连接信息的字典。

    Returns:
        bool: 如果连接成功返回 True，否则返回 False。
    """
    connection = None
    try:
        connection = mysql.connector.connect(**config)
        if connection.is_connected():
            print(f"成功连接到数据库: {config['database']}@{config['host']}:{config['port']}")
            return True
        else:
            print("连接已建立，但状态为非连接状态。")
            return False
    except Error as e:
        print(f"连接失败: {e}")
        return False
    finally:
        if connection and connection.is_connected():
            connection.close()
            print("数据库连接已关闭。")

def query_data(config, query):
    """
    连接到数据库并执行指定的查询。

    Args:
        config (dict): 包含数据库连接信息的字典。
        query (str): 要执行的 SQL 查询语句。

    Returns:
        list: 查询结果列表，如果出错则返回 None。
    """
    connection = None
    cursor = None
    results = None
    try:
        connection = mysql.connector.connect(**config)
        if connection.is_connected():
            print("数据库连接成功，准备执行查询...")
            cursor = connection.cursor()
            cursor.execute(query)
            results = cursor.fetchall()
            print(f"查询执行完毕，获取到 {len(results)} 条记录。")
            
            # 获取列名
            column_names = [desc[0] for desc in cursor.description]
            
            return results, column_names
        else:
            print("无法建立数据库连接以执行查询。")
            return None, None
    except Error as e:
        print(f"查询数据时出错: {e}")
        return None, None
    finally:
        if cursor:
            cursor.close()
            print("游标已关闭。")
        if connection and connection.is_connected():
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    if test_db_connection(db_config):
        
        # 直接查询指定 log_id 的记录
        print("\n------ 查询 log_id = 687649 的记录 ------")
        log_query = "SELECT * FROM t_analysis_log WHERE id = 687649;"
        print(f"将执行查询: {log_query}")
        log_data, log_columns = query_data(db_config, log_query)
        
        if log_data and len(log_data) > 0:
            print(f"找到 id = 687649 的日志记录")
            
            # 打印记录详情
            print("\n日志记录详情:")
            for i, col_name in enumerate(log_columns):
                print(f"{col_name}: {log_data[0][i]}")
            
            # 导出到CSV
            timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            csv_filename = f'log_id_687649_{timestamp}.csv'
            
            try:
                with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    # 写入表头
                    writer.writerow(log_columns)
                    # 写入数据
                    writer.writerows(log_data)
                print(f"日志记录已成功导出到文件: {csv_filename}")
                
                # 尝试获取实际日志内容
                print("\n------ 尝试获取日志文件内容 ------")
                analysis_info_path = log_data[0][2]  # 假设第三列是 analysis_info_path
                
                if analysis_info_path:
                    print(f"日志文件路径: {analysis_info_path}")
                    print("注意: 需要配置下载URL才能获取实际日志内容")
                    print("可参考 aiweihe_download.py 中的实现方式")
            except IOError as e:
                print(f"写入 CSV 文件时出错: {e}")
        else:
            print(f"未找到 id = 687649 的日志记录")
            
            # 查看表中的最大ID，帮助确认是否存在这条记录
            max_id_query = "SELECT MAX(id) FROM t_analysis_log;"
            print(f"将执行查询: {max_id_query}")
            max_id_data, _ = query_data(db_config, max_id_query)
            if max_id_data and max_id_data[0][0]:
                print(f"表中最大的 id 是: {max_id_data[0][0]}")
                
                if max_id_data[0][0] < 687649:
                    print(f"表中没有 id = 687649 的记录，因为最大 id 小于 687649")
                    
                    # 查找最近日期的几条记录
                    recent_query = "SELECT * FROM t_analysis_log ORDER BY create_date DESC LIMIT 5;"
                    print(f"将执行查询: {recent_query}")
                    recent_data, recent_columns = query_data(db_config, recent_query)
                    if recent_data:
                        print("\n最近的5条日志记录:")
                        for row in recent_data:
                            print("-" * 50)
                            for i, col_name in enumerate(recent_columns):
                                print(f"{col_name}: {row[i]}")
                                
                        # 导出最近的记录
                        timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
                        recent_csv = f'recent_logs_{timestamp}.csv'
                        
                        try:
                            with open(recent_csv, 'w', newline='', encoding='utf-8-sig') as csvfile:
                                writer = csv.writer(csvfile)
                                # 写入表头
                                writer.writerow(recent_columns)
                                # 写入数据
                                writer.writerows(recent_data)
                            print(f"最近的日志记录已导出到文件: {recent_csv}")
                        except IOError as e:
                            print(f"写入 CSV 文件时出错: {e}")
    else:
        print("数据库连接测试失败，无法执行查询。") 