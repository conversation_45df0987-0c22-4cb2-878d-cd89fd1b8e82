#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版数据匹配和合并工具
添加了更多实用功能和配置选项
"""

import pandas as pd
import numpy as np
import os
import logging
import argparse
import json
from typing import Tuple, Optional, Dict, List
from datetime import datetime

# 配置日志
def setup_logging(log_level='INFO'):
    """设置日志配置"""
    log_filename = f"match_and_merge_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


class EnhancedDataMatcher:
    """增强版数据匹配和合并类"""
    
    def __init__(self, config_file=None, **kwargs):
        self.logger = setup_logging(kwargs.get('log_level', 'INFO'))
        
        # 从配置文件或参数加载配置
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
        else:
            file1_path = kwargs.get('file1_path', r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\10s10步长v3.0\合并结果.csv")

            # 根据file1_path确定输出目录
            file1_dir = os.path.dirname(file1_path)
            default_output_path = os.path.join(file1_dir, "merged_result_with_disease.csv")

            self.config = {
                'file1_path': file1_path,
                'file2_path': kwargs.get('file2_path', r"D:\ECG\0723一分钟项目测试\标注平台数据\标注平台数据.xls"),
                'output_path': kwargs.get('output_path', default_output_path),
                'match_column': kwargs.get('match_column', None),  # 如果指定，使用指定列而不是最后一列
                'separator_chars': kwargs.get('separator_chars', ['_', '-', '']),  # 尝试的分隔符
                'case_sensitive': kwargs.get('case_sensitive', False),
                'remove_duplicates': kwargs.get('remove_duplicates', True),
                'backup_original': kwargs.get('backup_original', True),
                'strict_matching': kwargs.get('strict_matching', True)  # 严格匹配模式
            }
    
    def load_config(self, config_file):
        """从JSON配置文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            self.logger.info(f"已加载配置文件: {config_file}")
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise
    
    def save_config(self, config_file):
        """保存当前配置到文件"""
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            self.logger.info(f"配置已保存到: {config_file}")
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
    
    def load_data(self) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """加载数据文件"""
        df1, df2 = None, None
        
        # 加载文件1
        try:
            file1_path = self.config['file1_path']
            if os.path.exists(file1_path):
                if file1_path.endswith('.csv'):
                    try:
                        df1 = pd.read_csv(file1_path, encoding='utf-8')
                    except UnicodeDecodeError:
                        df1 = pd.read_csv(file1_path, encoding='gbk')
                elif file1_path.endswith(('.xls', '.xlsx')):
                    df1 = pd.read_excel(file1_path)
                
                self.logger.info(f"成功加载文件1: {file1_path}")
                self.logger.info(f"文件1形状: {df1.shape}")
                self.logger.info(f"文件1列名: {list(df1.columns)}")
                
                # 备份原始文件
                if self.config.get('backup_original', True):
                    backup_path = file1_path + '.backup'
                    if not os.path.exists(backup_path):
                        df1.to_csv(backup_path, index=False, encoding='utf-8-sig')
                        self.logger.info(f"已创建备份文件: {backup_path}")
            else:
                self.logger.error(f"文件1不存在: {file1_path}")
        except Exception as e:
            self.logger.error(f"加载文件1失败: {e}")
        
        # 加载文件2
        try:
            file2_path = self.config['file2_path']
            if os.path.exists(file2_path):
                if file2_path.endswith('.csv'):
                    try:
                        df2 = pd.read_csv(file2_path, encoding='utf-8')
                    except UnicodeDecodeError:
                        df2 = pd.read_csv(file2_path, encoding='gbk')
                elif file2_path.endswith(('.xls', '.xlsx')):
                    df2 = pd.read_excel(file2_path)
                
                self.logger.info(f"成功加载文件2: {file2_path}")
                self.logger.info(f"文件2形状: {df2.shape}")
                self.logger.info(f"文件2列名: {list(df2.columns)}")
            else:
                self.logger.error(f"文件2不存在: {file2_path}")
        except Exception as e:
            self.logger.error(f"加载文件2失败: {e}")
        
        return df1, df2
    
    def extract_key_components(self, key_string):
        """从复杂的键字符串中提取关键组件"""
        import re

        self.logger.debug(f"解析键字符串: {key_string}")

        # 提取客户ID (CUSTOMER后面的数字)
        customer_match = re.search(r'CUSTOMER(\d+)', key_string)
        customer_id = customer_match.group(1) if customer_match else ''

        # 提取完整的时间戳 (YYYYMMDDHHMMSS格式，通常是14位数字)
        # 针对不同的格式进行精确匹配
        datetime_patterns = [
            r'_(\d{14})_',      # _20250401012111_
            r'/(\d{14})$',      # /20250401012111 (文件2格式，以数字结尾)
            r'/(\d{12})$',      # /202504010121 (文件2格式，12位)
            r'_(\d{12})_',      # _202504010121_
            r'_(\d{8})_',       # _20250401_
            r'/(\d{8})$',       # /20250401 (文件2格式，8位)
        ]

        date_part = ''
        for pattern in datetime_patterns:
            date_match = re.search(pattern, key_string)
            if date_match:
                date_part = date_match.group(1)
                break

        # 提取导联信息
        lead_patterns = [
            r'[Ll][Ee][Aa][Dd]([IVX]+)',  # LEADI, leadI等
            r'_([IVX]+)_[Aa][Cc][Cc]',    # _I_acc, _II_acc等
            r'/(\d+[IVX]+)',              # /20250401063945_I等中的I
        ]

        lead_part = ''
        for pattern in lead_patterns:
            lead_match = re.search(pattern, key_string)
            if lead_match:
                lead_part = lead_match.group(1)
                # 标准化导联名称
                lead_part = lead_part.replace('LEAD', '').replace('lead', '')
                break

        self.logger.debug(f"解析结果: 客户ID={customer_id}, 日期={date_part}, 导联={lead_part}")
        return customer_id, date_part, lead_part

    def prepare_matching_keys(self, df1: pd.DataFrame, df2: pd.DataFrame) -> Dict[str, pd.Series]:
        """准备多种格式的匹配键"""
        # 获取文件1的匹配键
        if self.config.get('match_column'):
            if self.config['match_column'] in df1.columns:
                match_col = self.config['match_column']
            else:
                self.logger.warning(f"指定的匹配列 '{self.config['match_column']}' 不存在，使用最后一列")
                match_col = df1.columns[-1]
        else:
            match_col = df1.columns[-1]

        key1_raw = df1[match_col].astype(str).str.strip()
        if not self.config.get('case_sensitive', False):
            key1_raw = key1_raw.str.upper()

        self.logger.info(f"文件1匹配键列名: {match_col}")
        self.logger.info(f"文件1匹配键示例: {key1_raw.head().tolist()}")

        # 检查文件2必需的列
        required_cols = ['es_key', 'lead', 'disease_name']
        missing_cols = [col for col in required_cols if col not in df2.columns]

        if missing_cols:
            self.logger.error(f"文件2缺少必需的列: {missing_cols}")
            self.logger.info(f"文件2实际列名: {list(df2.columns)}")
            raise ValueError(f"文件2缺少必需的列: {missing_cols}")

        # 解析文件1的键组件
        key1_components = []
        for key in key1_raw:
            customer_id, date_part, lead_part = self.extract_key_components(key)
            key1_components.append((customer_id, date_part, lead_part))

        # 解析文件2的键组件
        key2_components = []
        for _, row in df2.iterrows():
            es_key = str(row['es_key']).strip()
            lead = str(row['lead']).strip()

            # 从es_key中提取客户ID和时间
            customer_id, date_part, _ = self.extract_key_components(es_key)

            # 标准化导联名称
            lead_normalized = lead.replace('LEAD', '').strip()

            key2_components.append((customer_id, date_part, lead_normalized))

        # 创建多种匹配策略
        matching_keys = {}

        # 策略1: 完全匹配 (客户ID + 日期 + 导联)
        key1_full = pd.Series([f"{comp[0]}_{comp[1]}_{comp[2]}" for comp in key1_components])
        key2_full = pd.Series([f"{comp[0]}_{comp[1]}_{comp[2]}" for comp in key2_components])
        matching_keys['strategy_full'] = key2_full

        # 策略2: 客户ID + 导联匹配 (忽略时间差异)
        key1_customer_lead = pd.Series([f"{comp[0]}_{comp[2]}" for comp in key1_components])
        key2_customer_lead = pd.Series([f"{comp[0]}_{comp[2]}" for comp in key2_components])
        matching_keys['strategy_customer_lead'] = key2_customer_lead

        # 策略3: 仅客户ID匹配
        key1_customer = pd.Series([comp[0] for comp in key1_components])
        key2_customer = pd.Series([comp[0] for comp in key2_components])
        matching_keys['strategy_customer_only'] = key2_customer

        # 记录匹配键示例
        self.logger.info(f"文件1解析后的键组件示例: {key1_components[:5]}")
        self.logger.info(f"文件2解析后的键组件示例: {key2_components[:5]}")

        for strategy_name, key2_series in matching_keys.items():
            self.logger.info(f"匹配策略 {strategy_name} 示例: {key2_series.head().tolist()}")

        # 返回所有匹配键
        result = {
            'key1_full': key1_full,
            'key1_customer_lead': key1_customer_lead,
            'key1_customer': key1_customer,
            **matching_keys
        }

        return result
    
    def perform_matching(self, df1: pd.DataFrame, df2: pd.DataFrame) -> pd.DataFrame:
        """执行增强的数据匹配"""
        try:
            # 准备匹配键
            all_keys = self.prepare_matching_keys(df1, df2)

            # 初始化结果列
            df1['disease_name'] = np.nan
            df1['match_strategy'] = ''

            strategy_stats = {}

            # 根据配置决定匹配策略
            if self.config.get('strict_matching', True):
                # 严格匹配模式：只使用完全匹配
                matching_strategies = [
                    ('strategy_full', 'key1_full', '严格匹配(客户ID+日期+导联)')
                ]
                self.logger.info("使用严格匹配模式：客户ID+日期+导联三项内容必须完全匹配")
            else:
                # 宽松匹配模式：使用多种策略
                matching_strategies = [
                    ('strategy_full', 'key1_full', '完全匹配(客户ID+日期+导联)'),
                    ('strategy_customer_lead', 'key1_customer_lead', '客户ID+导联匹配'),
                    ('strategy_customer_only', 'key1_customer', '仅客户ID匹配')
                ]
                self.logger.info("使用宽松匹配模式：尝试多种匹配策略")

            # 按优先级尝试每种匹配策略
            for strategy_name, key1_name, strategy_desc in matching_strategies:
                if strategy_name not in all_keys or key1_name not in all_keys:
                    continue

                key1 = all_keys[key1_name]
                key2 = all_keys[strategy_name]

                # 创建映射字典
                disease_mapping = dict(zip(key2, df2['disease_name']))

                # 添加调试信息
                self.logger.debug(f"策略 {strategy_desc} - 映射字典示例:")
                for i, (k2, disease) in enumerate(zip(key2.head(), df2['disease_name'].head())):
                    self.logger.debug(f"  {k2} -> {disease}")
                    if i >= 2:  # 只显示前3个
                        break

                # 对未匹配的记录进行匹配
                unmatched_mask = df1['disease_name'].isna()
                if unmatched_mask.any():
                    # 获取未匹配记录的索引
                    unmatched_indices = df1.index[unmatched_mask]

                    # 对每个未匹配的记录进行匹配
                    for idx in unmatched_indices:
                        key_value = key1.iloc[idx]  # 使用iloc确保索引正确
                        if key_value in disease_mapping:
                            matched_disease = disease_mapping[key_value]
                            df1.loc[idx, 'disease_name'] = matched_disease
                            df1.loc[idx, 'match_strategy'] = strategy_desc

                            # 记录匹配成功的数量
                            if strategy_desc not in strategy_stats:
                                strategy_stats[strategy_desc] = 0
                            strategy_stats[strategy_desc] += 1

                    # 统计本策略的匹配结果
                    if strategy_desc in strategy_stats:
                        newly_matched_count = strategy_stats[strategy_desc]
                        self.logger.info(f"匹配策略 '{strategy_desc}' 成功匹配: {newly_matched_count} 条记录")

                        # 显示详细的匹配示例进行验证
                        matched_indices = df1.index[df1['match_strategy'] == strategy_desc]
                        sample_matches = df1.loc[matched_indices[:5]]
                        for idx, row in sample_matches.iterrows():
                            original_key = df1.loc[idx, df1.columns[-3]]  # 数据来源列
                            matched_key = key1.iloc[idx]  # 解析后的匹配键
                            disease = row['disease_name']
                            self.logger.info(f"  匹配验证: 原始[{original_key}] -> 解析[{matched_key}] -> 疾病[{disease}]")

                            # 验证映射是否正确
                            if matched_key in disease_mapping:
                                expected_disease = disease_mapping[matched_key]
                                if expected_disease != disease:
                                    self.logger.error(f"  ❌ 映射错误! 期望[{expected_disease}] 实际[{disease}]")
                                else:
                                    self.logger.info(f"  ✅ 映射正确")
                            else:
                                self.logger.error(f"  ❌ 匹配键不存在于映射字典中")

            # 统计最终结果
            final_matched = df1['disease_name'].notna().sum()
            total_records = len(df1)
            match_rate = final_matched / total_records * 100

            self.logger.info(f"最终匹配统计:")
            self.logger.info(f"  总记录数: {total_records}")
            self.logger.info(f"  成功匹配: {final_matched}")
            self.logger.info(f"  未匹配: {total_records - final_matched}")
            self.logger.info(f"  匹配率: {match_rate:.2f}%")

            for strategy, count in strategy_stats.items():
                self.logger.info(f"  {strategy}: {count} 条")

            # 显示未匹配的键示例
            if final_matched < total_records:
                unmatched_mask = df1['disease_name'].isna()
                unmatched_original_keys = df1.loc[unmatched_mask, df1.columns[-2]].unique()[:5]
                self.logger.warning(f"未匹配的原始键示例: {unmatched_original_keys.tolist()}")

            return df1

        except Exception as e:
            self.logger.error(f"匹配过程中发生错误: {e}")
            raise
    
    def post_process(self, df: pd.DataFrame) -> pd.DataFrame:
        """后处理数据"""
        # 移除重复记录
        if self.config.get('remove_duplicates', True):
            original_count = len(df)
            df = df.drop_duplicates()
            removed_count = original_count - len(df)
            if removed_count > 0:
                self.logger.info(f"移除了 {removed_count} 条重复记录")
        
        return df
    
    def save_results(self, df: pd.DataFrame):
        """保存结果和报告"""
        try:
            output_path = self.config['output_path']

            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                self.logger.info(f"创建输出目录: {output_dir}")

            # 保存主要结果
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"合并结果已保存到: {output_path}")

            # 在同一目录下生成详细报告
            self.generate_detailed_report(df, output_dir)

        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
            raise
    
    def generate_detailed_report(self, df: pd.DataFrame, output_dir=None):
        """生成详细的分析报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_filename = f"matching_report_{timestamp}.txt"

        if output_dir:
            report_path = os.path.join(output_dir, report_filename)
        else:
            report_path = report_filename
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("数据匹配和合并详细报告\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # 基本统计
                total_count = len(df)
                matched_count = df['disease_name'].notna().sum()
                f.write("基本统计:\n")
                f.write(f"  总记录数: {total_count}\n")
                f.write(f"  成功匹配: {matched_count}\n")
                f.write(f"  未匹配: {total_count - matched_count}\n")
                f.write(f"  匹配率: {matched_count/total_count*100:.2f}%\n\n")
                
                # 匹配策略统计
                if 'match_strategy' in df.columns:
                    strategy_counts = df['match_strategy'].value_counts()
                    f.write("匹配策略统计:\n")
                    for strategy, count in strategy_counts.items():
                        if strategy:  # 排除空值
                            f.write(f"  {strategy}: {count} 条\n")
                    f.write("\n")
                
                # 疾病名称分布
                disease_counts = df['disease_name'].value_counts()
                f.write("疾病名称分布 (前20名):\n")
                for disease, count in disease_counts.head(20).items():
                    f.write(f"  {disease}: {count} 条\n")
                f.write("\n")
                
                # 配置信息
                f.write("使用的配置:\n")
                for key, value in self.config.items():
                    f.write(f"  {key}: {value}\n")
                
            self.logger.info(f"详细报告已保存到: {report_path}")
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
    
    def run(self) -> bool:
        """执行完整的匹配和合并流程"""
        self.logger.info("开始增强版数据匹配和合并流程")
        
        try:
            # 1. 加载数据
            df1, df2 = self.load_data()
            
            if df1 is None or df2 is None:
                self.logger.error("数据加载失败，无法继续")
                return False
            
            # 2. 执行匹配
            merged_df = self.perform_matching(df1, df2)
            
            # 3. 后处理
            merged_df = self.post_process(merged_df)
            
            # 4. 保存结果
            self.save_results(merged_df)
            
            self.logger.info("增强版数据匹配和合并流程完成")
            return True
            
        except Exception as e:
            self.logger.error(f"流程执行失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强版数据匹配和合并工具')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--file1', help='源文件1路径')
    parser.add_argument('--file2', help='源文件2路径')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'])
    parser.add_argument('--case-sensitive', action='store_true', help='区分大小写匹配')
    parser.add_argument('--no-backup', action='store_true', help='不创建备份文件')
    parser.add_argument('--loose-matching', action='store_true', help='使用宽松匹配模式（默认为严格匹配）')
    
    args = parser.parse_args()
    
    # 构建配置参数
    kwargs = {
        'log_level': args.log_level,
        'case_sensitive': args.case_sensitive,
        'backup_original': not args.no_backup,
        'strict_matching': not args.loose_matching  # 默认严格匹配，除非指定宽松匹配
    }
    
    if args.file1:
        kwargs['file1_path'] = args.file1
    if args.file2:
        kwargs['file2_path'] = args.file2
    if args.output:
        kwargs['output_path'] = args.output
    
    # 创建匹配器并运行
    matcher = EnhancedDataMatcher(config_file=args.config, **kwargs)
    success = matcher.run()
    
    if success:
        print("\n" + "=" * 60)
        print("数据匹配和合并成功完成！")
        print(f"结果文件: {matcher.config['output_path']}")
        print("详细报告和日志文件已生成")
    else:
        print("\n" + "=" * 60)
        print("数据匹配和合并失败，请查看日志了解详情")


if __name__ == "__main__":
    main()
