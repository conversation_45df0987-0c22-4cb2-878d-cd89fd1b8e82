import json
import os
import traceback
import requests
from qiniu import Auth
import pandas as pd
import glob
import urllib3
import concurrent.futures
from tqdm import tqdm

# 禁用因verify=False而产生的InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 直接在文件中定义七牛云配置，不再从global_settings导入
QINIU = {
    'test': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://test.upload.weiheyixue.com'
    },
    'prod': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://upload.weiheyixue.com'
    }
}

# --- 配置 ---
INPUT_DIR = os.path.join('qiniu_query_results', '平直线')
BASE_OUTPUT_DIR = 'qiniu_query_results'
ENVIRONMENT = 'prod'  # 根据需要修改为'test'
MAX_WORKERS = 50 # 并发下载的线程数，可根据网络情况调整
REQUEST_TIMEOUT = 10 # 请求超时时间（秒）

# --- 单一完整查询模式配置 ---
RUN_SINGLE_FULL_QUERY = False  # 设置为True以运行单一查询模式，否则运行批量模式
SINGLE_ES_KEY_TO_QUERY = "CUSTOMER18838092628219043846502/20250726213114" # 替换为您想查询的es_key
SINGLE_JSON_OUTPUT_PATH = "D:\Project\qiniu_query_results\低电压异常数据\肖总0726异常数据.json" # 完整数据输出文件名

# --- 指定文件模式配置 ---
RUN_SPECIFIC_FILES_MODE = False  # 设置为True以处理指定的CSV文件
SPECIFIC_FILES_TO_PROCESS = [
    "D:\\ECG\\0623运动状态\\肖总噪音数据\\异常状态es.csv"
]
USE_ORIGINAL_ES_KEY_AS_FILENAME = True  # 设置为True以使用原始es_key作为文件名

# --- 房颤数据Excel文件处理模式配置 ---
RUN_EXCEL_AF_MODE = True  # 设置为True以处理房颤数据Excel文件
EXCEL_AF_FILES = [
    r"D:\Project\qiniu_query_results\肖总呼吸波数据\t_data_ecg_20250729.xls",
    r"D:\Project\qiniu_query_results\肖总呼吸波数据\t_data_ecg_20250731.xls"
]
EXCEL_AF_OUTPUT_DIR = "qiniu_query_results/肖总呼吸波数据0731"  # 房颤数据输出根目录

# 从qiniu_helper.py复制过来的函数
def get_qiniu_data(file_path, environment):
    """获取七牛云数据，快速失败策略"""
    try:
        access_key = QINIU[environment]['access_key']
        secret_key = QINIU[environment]['secret_key']
        domain_prefix = QINIU[environment]['domain_prefix']  # 存储空间域名前缀
        # 构建鉴权对象
        q = Auth(access_key, secret_key)

        # 构建私有空间的下载链接
        private_url = q.private_download_url(domain_prefix + '/ecg/' + file_path)

        # 使用requests库下载文件，并禁用SSL验证、设置超时
        response = requests.get(private_url, verify=False, timeout=REQUEST_TIMEOUT)

        if response.status_code == 200:
            # 确保返回原始的完整JSON数据
            return json.loads(response.content)
        else:
            # 对于非200状态码，不打印警告以保持控制台清洁，直接返回None
            return None

    except requests.exceptions.RequestException:
        # 对于任何网络层面的异常（超时、连接错误等），静默失败，不打印信息
        return None

def query_and_save_full_json(es_key, output_path):
    """查询单个es_key的完整数据并保存为JSON文件。"""
    print(f"\n--- 单一查询模式 ---")
    print(f"正在查询es_key: {es_key}")
    
    data = get_qiniu_data(es_key, ENVIRONMENT)
    
    if data:
        # 输出数据信息，以便诊断
        data_type = type(data).__name__
        if isinstance(data, dict):
            keys = list(data.keys())
            print(f"[诊断] 获取到的数据类型: {data_type}，包含 {len(keys)} 个键")
            print(f"[诊断] 数据键列表: {', '.join(keys)}")
        else:
            print(f"[诊断] 获取到的数据类型: {data_type}，非字典类型")
        
        try:
            print(f"[诊断] 正在将完整数据保存到: {output_path}")
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"[成功] 已将完整数据保存到: {output_path}")
        except Exception as e:
            print(f"[错误] 保存文件失败: {e}")
    else:
        print(f"[失败] 未能获取到es_key '{es_key}' 的数据。")

def save_single_result(filename_base, data, output_dir):
    """
    保存单个查询结果中'ecg'和'ecgII'导联的数据到txt文件。
    使用CSV中的'id'列作为文件名。
    """
    os.makedirs(output_dir, exist_ok=True)

    # 检查data是否为有效字典
    if not isinstance(data, dict):
        print(f"  - 数据格式无效，无法保存 (关联文件名: {filename_base})")
        return None

    saved_files = []

    # 保存ECG I导联数据
    if 'ecg' in data:
        ecg_data = data['ecg']
        safe_filename = str(filename_base) + "_I.txt"
        filepath = os.path.join(output_dir, safe_filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                # 如果ecg_data是列表，每项占一行；否则直接写入
                if isinstance(ecg_data, list):
                    f.write('\n'.join(map(str, ecg_data)))
                else:
                    f.write(str(ecg_data))
            print(f"  - 已保存ECG(I)导联数据: {filepath}")
            saved_files.append(safe_filename)
        except Exception as e:
            print(f"  - 保存ECG(I)导联失败: {filepath}，原因: {e}")
    else:
        print(f"  - 数据中缺少'ecg'键，无法保存I导联数据")

    # 保存ECG II导联数据
    if 'ecgII' in data:
        ecgII_data = data['ecgII']
        safe_filename = str(filename_base) + "_II.txt"
        filepath = os.path.join(output_dir, safe_filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                # 如果ecgII_data是列表，每项占一行；否则直接写入
                if isinstance(ecgII_data, list):
                    f.write('\n'.join(map(str, ecgII_data)))
                else:
                    f.write(str(ecgII_data))
            print(f"  - 已保存ECG(II)导联数据: {filepath}")
            saved_files.append(safe_filename)
        except Exception as e:
            print(f"  - 保存ECG(II)导联失败: {filepath}，原因: {e}")
    else:
        print(f"  - 数据中缺少'ecgII'键，无法保存II导联数据")

    # 如果没有保存任何文件，返回None
    if not saved_files:
        print(f"  - 未找到任何ECG导联数据，无法保存 (关联文件名: {filename_base})")
        return None

    return saved_files[0] if len(saved_files) == 1 else saved_files

def save_full_json_result(filename_base, data, output_dir):
    """
    保存单个查询结果的完整JSON数据。
    使用与save_single_result相同的文件命名格式，但扩展名为.json。
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查data是否有效
    if not isinstance(data, dict):
        print(f"  - 数据格式无效，无法保存 (关联文件名: {filename_base}.json)")
        return None
    
    # 构建文件名 e.g., "ECG-01-01.json"
    safe_filename = str(filename_base) + ".json"
    filepath = os.path.join(output_dir, safe_filename)
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"  - 已保存完整JSON数据: {filepath}")
        return safe_filename
    except Exception as e:
        print(f"  - 保存失败: {filepath}，原因: {e}")
        return None

def save_full_json_with_original_name(es_key, data, output_dir):
    """
    保存单个查询结果的完整JSON数据，使用原始es_key作为文件名。
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查data是否有效
    if not isinstance(data, dict):
        print(f"  - 数据格式无效，无法保存 (es_key: {es_key})")
        return None
    
    # 将es_key处理成安全的文件名
    safe_filename = es_key.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_') + ".json"
    filepath = os.path.join(output_dir, safe_filename)
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"  - 已保存完整JSON数据: {filepath}")
        return safe_filename
    except Exception as e:
        print(f"  - 保存失败: {filepath}，原因: {e}")
        return None

def download_and_save_task(es_key, filename_base, output_dir, save_full_json=False, use_original_name=False):
    """
    单个任务单元：下载、提取、保存。为并发执行而设计。
    """
    data = get_qiniu_data(es_key, ENVIRONMENT)
    if data:
        if use_original_name:
            saved_filename = save_full_json_with_original_name(es_key, data, output_dir)
        elif save_full_json:
            saved_filename = save_full_json_result(filename_base, data, output_dir)
        else:
            saved_filename = save_single_result(filename_base, data, output_dir)
        if saved_filename:
            return {
                'original_es_key': es_key,
                'saved_filename': saved_filename
            }
    return None

def process_csv_file(csv_path, base_output_dir, folder_index, save_full_json=False, use_original_name=False):
    """处理单个CSV文件：读取es_key，并发下载数据，保存数据并创建映射文件"""
    print(f"\n--- 开始处理CSV文件: {os.path.basename(csv_path)} (文件夹编号: {folder_index:02d}) ---")

    # 1. 创建该CSV专属的输出目录
    csv_filename = os.path.basename(csv_path)
    output_dir_name = f"output_{os.path.splitext(csv_filename)[0]}"
    specific_output_dir = os.path.join(base_output_dir, output_dir_name)
    os.makedirs(specific_output_dir, exist_ok=True)
    print(f"数据将保存到: {specific_output_dir}")

    # 显示保存模式
    if use_original_name:
        print(f"保存模式: 原始es_key命名的完整JSON")
    else:
        print(f"保存模式: {'完整JSON' if save_full_json else 'ECG导联数据'}")

    # 2. 读取CSV中的es_key
    try:
        df = pd.read_csv(csv_path, low_memory=False)
        if 'es_key' not in df.columns:
            print(f"[错误] CSV文件 {csv_filename} 中未找到 'es_key' 列。")
            return
        # 筛选出需要处理的列并去除空值和重复项
        es_keys = df['es_key'].dropna().unique().tolist()
        print(f"发现 {len(es_keys)} 个唯一的es_key。")
    except Exception as e:
        print(f"[错误] 读取CSV文件 {csv_filename} 失败: {e}")
        return

    # 3. 并发下载并保存数据
    mapping_records = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 准备所有任务
        future_to_es_key = {}

        for i, es_key in enumerate(es_keys):
            if use_original_name:
                # 使用原始es_key作为标识，filename_base在这种模式下不使用
                future_to_es_key[executor.submit(
                    download_and_save_task,
                    es_key,
                    None,  # filename_base不使用
                    specific_output_dir,
                    save_full_json,
                    use_original_name
                )] = es_key
            else:
                # 使用序列号作为文件名
                future_to_es_key[executor.submit(
                    download_and_save_task,
                    es_key,
                    f"ECG-{folder_index:02d}-{i+1:02d}",
                    specific_output_dir,
                    save_full_json,
                    use_original_name
                )] = es_key

        # 使用tqdm显示进度
        for future in tqdm(concurrent.futures.as_completed(future_to_es_key), total=len(es_keys), desc=f"处理 {os.path.basename(csv_path)}"):
            try:
                result = future.result()
                if result:
                    mapping_records.append(result)
            except Exception as exc:
                es_key = future_to_es_key[future]
                print(f"\n[错误] 处理es_key '{es_key}' 时产生异常: {exc}")

    # 4. 创建并保存映射文件
    if mapping_records:
        manifest_path = os.path.join(specific_output_dir, '_manifest.csv')
        manifest_df = pd.DataFrame(mapping_records)
        manifest_df.to_csv(manifest_path, index=False, encoding='utf-8-sig')
        print(f"已创建映射文件: {manifest_path}")

    print(f"--- 完成处理: {csv_filename} ---")

def process_excel_file(excel_path, base_output_dir, folder_index):
    """处理单个Excel文件：读取es_key，并发下载数据，保存完整JSON数据并创建映射文件"""
    print(f"\n--- 开始处理Excel文件: {os.path.basename(excel_path)} (文件夹编号: {folder_index:02d}) ---")

    # 1. 创建该Excel专属的输出目录
    excel_filename = os.path.basename(excel_path)
    # 从文件名中提取用户标识，创建更友好的文件夹名
    if "CUSTOMER" in excel_filename:
        # 提取CUSTOMER后面的数字部分作为文件夹名
        import re
        customer_match = re.search(r'CUSTOMER(\d+)', excel_filename)
        if customer_match:
            customer_id = customer_match.group(1)
            output_dir_name = f"用户{customer_id}_房颤数据"
        else:
            output_dir_name = f"output_{os.path.splitext(excel_filename)[0]}"
    else:
        output_dir_name = f"output_{os.path.splitext(excel_filename)[0]}"

    specific_output_dir = os.path.join(base_output_dir, output_dir_name)
    os.makedirs(specific_output_dir, exist_ok=True)
    print(f"数据将保存到: {specific_output_dir}")
    print(f"保存模式: 原始es_key命名的完整JSON")

    # 2. 读取Excel中的es_key
    try:
        df = pd.read_excel(excel_path)
        if 'es_key' not in df.columns:
            print(f"[错误] Excel文件 {excel_filename} 中未找到 'es_key' 列。")
            print(f"可用列: {df.columns.tolist()}")
            return
        # 筛选出需要处理的列并去除空值和重复项
        es_keys = df['es_key'].dropna().unique().tolist()
        print(f"发现 {len(es_keys)} 个唯一的es_key。")
    except Exception as e:
        print(f"[错误] 读取Excel文件 {excel_filename} 失败: {e}")
        return

    # 3. 并发下载并保存数据
    mapping_records = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 准备所有任务 - 使用原始es_key作为文件名保存完整JSON
        future_to_es_key = {}

        for es_key in es_keys:
            future_to_es_key[executor.submit(
                download_and_save_task,
                es_key,
                None,  # filename_base不使用
                specific_output_dir,
                True,  # save_full_json=True
                True   # use_original_name=True
            )] = es_key

        # 使用tqdm显示进度
        for future in tqdm(concurrent.futures.as_completed(future_to_es_key), total=len(es_keys), desc=f"处理 {os.path.basename(excel_path)}"):
            try:
                result = future.result()
                if result:
                    mapping_records.append(result)
            except Exception as exc:
                es_key = future_to_es_key[future]
                print(f"\n[错误] 处理es_key '{es_key}' 时产生异常: {exc}")

    # 4. 创建并保存映射文件
    if mapping_records:
        manifest_path = os.path.join(specific_output_dir, '_manifest.csv')
        manifest_df = pd.DataFrame(mapping_records)
        manifest_df.to_csv(manifest_path, index=False, encoding='utf-8-sig')
        print(f"已创建映射文件: {manifest_path}")

    print(f"--- 完成处理: {excel_filename} ---")


if __name__ == "__main__":
    # 输出当前活动的模式
    print("\n=== 七牛云数据查询工具启动 ===")
    print(f"单一查询模式: {'启用' if RUN_SINGLE_FULL_QUERY else '禁用'}")
    print(f"指定文件模式: {'启用' if RUN_SPECIFIC_FILES_MODE else '禁用'}")
    print(f"Excel房颤数据模式: {'启用' if RUN_EXCEL_AF_MODE else '禁用'}")
    print(f"批量处理模式: {'启用' if not RUN_SINGLE_FULL_QUERY and not RUN_SPECIFIC_FILES_MODE and not RUN_EXCEL_AF_MODE else '禁用'}")
    print("=" * 30)

    if RUN_SINGLE_FULL_QUERY:
        print("\n执行单一查询模式...")
        query_and_save_full_json(SINGLE_ES_KEY_TO_QUERY, SINGLE_JSON_OUTPUT_PATH)
        print("\n单一查询模式执行完成。")
    elif RUN_SPECIFIC_FILES_MODE:
        # --- 指定文件模式 ---
        print(f"--- 指定文件模式 ---")
        for i, file_path in enumerate(SPECIFIC_FILES_TO_PROCESS, 1):
            if os.path.exists(file_path):
                # 从文件路径中提取文件名（不含路径和扩展名）
                file_name = os.path.splitext(os.path.basename(file_path))[0]
                print(f"\n处理指定文件 [{i}/{len(SPECIFIC_FILES_TO_PROCESS)}]: {file_name}")
                process_csv_file(file_path, BASE_OUTPUT_DIR, i, save_full_json=True, use_original_name=USE_ORIGINAL_ES_KEY_AS_FILENAME)
            else:
                print(f"\n[错误] 指定的文件不存在: {file_path}")
        print("\n所有指定文件处理完成！")
    elif RUN_EXCEL_AF_MODE:
        # --- Excel房颤数据处理模式 ---
        print(f"--- Excel房颤数据处理模式 ---")
        print(f"输出根目录: {EXCEL_AF_OUTPUT_DIR}")

        # 创建输出根目录
        os.makedirs(EXCEL_AF_OUTPUT_DIR, exist_ok=True)

        for i, excel_path in enumerate(EXCEL_AF_FILES, 1):
            if os.path.exists(excel_path):
                # 从文件路径中提取文件名（不含路径和扩展名）
                file_name = os.path.splitext(os.path.basename(excel_path))[0]
                print(f"\n处理Excel文件 [{i}/{len(EXCEL_AF_FILES)}]: {file_name}")
                process_excel_file(excel_path, EXCEL_AF_OUTPUT_DIR, i)
            else:
                print(f"\n[错误] 指定的Excel文件不存在: {excel_path}")
        print("\n所有Excel房颤数据文件处理完成！")
    else:
        # --- 批量处理模式 ---
        if not os.path.isdir(INPUT_DIR):
            print(f"[错误] 输入目录不存在: {INPUT_DIR}")
        else:
            # 使用glob查找所有CSV文件
            csv_files = glob.glob(os.path.join(INPUT_DIR, '*.csv'))

            if not csv_files:
                print(f"[警告] 在目录 {INPUT_DIR} 中未找到任何.csv文件。")
            else:
                print(f"找到 {len(csv_files)} 个CSV文件，准备开始处理...")
                for i, csv_file in enumerate(csv_files, 1):
                    process_csv_file(csv_file, BASE_OUTPUT_DIR, i)
                print("\n所有CSV文件处理完成！")
