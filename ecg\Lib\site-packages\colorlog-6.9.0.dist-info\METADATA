Metadata-Version: 2.1
Name: colorlog
Version: 6.9.0
Summary: Add colours to the output of Python's logging module.
Home-page: https://github.com/borntyping/python-colorlog
Author: <PERSON>
Author-email: <EMAIL>
License: MIT License
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Terminals
Classifier: Topic :: Utilities
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: colorama; sys_platform == "win32"
Provides-Extra: development
Requires-Dist: black; extra == "development"
Requires-Dist: flake8; extra == "development"
Requires-Dist: mypy; extra == "development"
Requires-Dist: pytest; extra == "development"
Requires-Dist: types-colorama; extra == "development"

# Log formatting with colors!

[![](https://img.shields.io/pypi/v/colorlog.svg)](https://pypi.org/project/colorlog/)
[![](https://img.shields.io/pypi/l/colorlog.svg)](https://pypi.org/project/colorlog/)

Add colours to the output of Python's `logging` module.

* [Source on GitHub](https://github.com/borntyping/python-colorlog)
* [Packages on PyPI](https://pypi.org/pypi/colorlog/)

## Status

colorlog currently requires Python 3.6 or higher. Older versions (below 5.x.x) 
support Python 2.6 and above.

* colorlog 6.x requires Python 3.6 or higher.
* colorlog 5.x is an interim version that will warn Python 2 users to downgrade.
* colorlog 4.x is the final version supporting Python 2.

[colorama] is included as a required dependency and initialised when using 
colorlog on Windows.

This library is over a decade old and supported a wide set of Python versions
for most of its life, which has made it a difficult library to add new features
to. colorlog 6 may break backwards compatibility so that newer features
can be added more easily, but may still not accept all changes or feature
requests. colorlog 4 might accept essential bugfixes but should not be
considered actively maintained and will not accept any major changes or new
features.

## Installation

Install from PyPI with:

```bash
pip install colorlog
```

Several Linux distributions provide official packages ([Debian], [Arch], [Fedora], 
[Gentoo], [OpenSuse] and [Ubuntu]), and others have user provided packages
([BSD ports], [Conda]).

## Usage

```python
import colorlog

handler = colorlog.StreamHandler()
handler.setFormatter(colorlog.ColoredFormatter(
	'%(log_color)s%(levelname)s:%(name)s:%(message)s'))

logger = colorlog.getLogger('example')
logger.addHandler(handler)
```

The `ColoredFormatter` class takes several arguments:

- `format`: The format string used to output the message (required).
- `datefmt`: An optional date format passed to the base class. See [`logging.Formatter`][Formatter].
- `reset`: Implicitly adds a color reset code to the message output, unless the output already ends with one. Defaults to `True`.
- `log_colors`: A mapping of record level names to color names. The defaults can be found in `colorlog.default_log_colors`, or the below example.
- `secondary_log_colors`: A mapping of names to `log_colors` style mappings, defining additional colors that can be used in format strings. See below for an example.
- `style`: Available on Python 3.2 and above. See [`logging.Formatter`][Formatter].

Color escape codes can be selected based on the log records level, by adding
parameters to the format string:

- `log_color`: Return the color associated with the records level.
- `<name>_log_color`: Return another color based on the records level if the formatter has secondary colors configured (see `secondary_log_colors` below).

Multiple escape codes can be used at once by joining them with commas when
configuring the color for a log level (but can't be used directly in the format
string). For example, `black,bg_white` would use the escape codes for black
text on a white background.

The following escape codes are made available for use in the format string:

- `{color}`, `fg_{color}`, `bg_{color}`: Foreground and background colors.
- `bold`, `bold_{color}`, `fg_bold_{color}`, `bg_bold_{color}`: Bold/bright colors.
- `thin`, `thin_{color}`, `fg_thin_{color}`: Thin colors (terminal dependent).
- `reset`: Clear all formatting (both foreground and background colors).

The available color names are:

- `black`
- `red`
- `green`
- `yellow`
- `blue`,
- `purple`
- `cyan`
- `white`

You can also use "bright" colors. These aren't standard ANSI codes, and
support for these varies wildly across different terminals.

- `light_black`
- `light_red`
- `light_green`
- `light_yellow`
- `light_blue`
- `light_purple`
- `light_cyan`
- `light_white`

## Examples

![Example output](docs/example.png)

The following code creates a `ColoredFormatter` for use in a logging setup,
using the default values for each argument.

```python
from colorlog import ColoredFormatter

formatter = ColoredFormatter(
	"%(log_color)s%(levelname)-8s%(reset)s %(blue)s%(message)s",
	datefmt=None,
	reset=True,
	log_colors={
		'DEBUG':    'cyan',
		'INFO':     'green',
		'WARNING':  'yellow',
		'ERROR':    'red',
		'CRITICAL': 'red,bg_white',
	},
	secondary_log_colors={},
	style='%'
)
```

### Using `secondary_log_colors`

Secondary log colors are a way to have more than one color that is selected
based on the log level. Each key in `secondary_log_colors` adds an attribute
that can be used in format strings (`message` becomes `message_log_color`), and
has a corresponding value that is identical in format to the `log_colors`
argument.

The following example highlights the level name using the default log colors,
and highlights the message in red for `error` and `critical` level log messages.

```python
from colorlog import ColoredFormatter

formatter = ColoredFormatter(
	"%(log_color)s%(levelname)-8s%(reset)s %(message_log_color)s%(message)s",
	secondary_log_colors={
		'message': {
			'ERROR':    'red',
			'CRITICAL': 'red'
		}
	}
)
```

### With [`dictConfig`][dictConfig]

```python
logging.config.dictConfig({
	'formatters': {
		'colored': {
			'()': 'colorlog.ColoredFormatter',
			'format': "%(log_color)s%(levelname)-8s%(reset)s %(blue)s%(message)s"
		}
	}
})
```

A full example dictionary can be found in `tests/test_colorlog.py`.

### With [`fileConfig`][fileConfig]

```ini
...

[formatters]
keys=color

[formatter_color]
class=colorlog.ColoredFormatter
format=%(log_color)s%(levelname)-8s%(reset)s %(bg_blue)s[%(name)s]%(reset)s %(message)s from fileConfig
datefmt=%m-%d %H:%M:%S
```

An instance of ColoredFormatter created with those arguments will then be used
by any handlers that are configured to use the `color` formatter.

A full example configuration can be found in `tests/test_config.ini`.

### With custom log levels

ColoredFormatter will work with custom log levels added with
[`logging.addLevelName`][addLevelName]:

```python
import logging, colorlog
TRACE = 5
logging.addLevelName(TRACE, 'TRACE')
formatter = colorlog.ColoredFormatter(log_colors={'TRACE': 'yellow'})
handler = logging.StreamHandler()
handler.setFormatter(formatter)
logger = logging.getLogger('example')
logger.addHandler(handler)
logger.setLevel('TRACE')
logger.log(TRACE, 'a message using a custom level')
```

## Tests

Tests similar to the above examples are found in `tests/test_colorlog.py`.

## Status

colorlog is in maintenance mode. I try and ensure bugfixes are published,
but compatibility with Python 2.6+ and Python 3+ makes this a difficult
codebase to add features to. Any changes that might break backwards
compatibility for existing users will not be considered.

## Alternatives

There are some more modern libraries for improving Python logging you may
find useful.

- [structlog]
- [jsonlog]

## Projects using colorlog

GitHub provides [a list of projects that depend on colorlog][dependents].

Some early adopters included [Errbot], [Pythran], and [zenlog].

## Licence

Copyright (c) 2012-2021 Sam Clements <<EMAIL>>

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
the Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

[dictConfig]: http://docs.python.org/3/library/logging.config.html#logging.config.dictConfig
[fileConfig]: http://docs.python.org/3/library/logging.config.html#logging.config.fileConfig
[addLevelName]: https://docs.python.org/3/library/logging.html#logging.addLevelName
[Formatter]: http://docs.python.org/3/library/logging.html#logging.Formatter
[tox]: http://tox.readthedocs.org/
[Arch]: https://archlinux.org/packages/extra/any/python-colorlog/
[BSD ports]: https://www.freshports.org/devel/py-colorlog/
[colorama]: https://pypi.python.org/pypi/colorama
[Conda]: https://anaconda.org/conda-forge/colorlog
[Debian]: [https://packages.debian.org/buster/python3-colorlog](https://packages.debian.org/buster/python3-colorlog)
[Errbot]: http://errbot.io/
[Fedora]: https://src.fedoraproject.org/rpms/python-colorlog
[Gentoo]: https://packages.gentoo.org/packages/dev-python/colorlog
[OpenSuse]: http://rpm.pbone.net/index.php3?stat=3&search=python-colorlog&srodzaj=3
[Pythran]: https://github.com/serge-sans-paille/pythran
[Ubuntu]: https://launchpad.net/python-colorlog
[zenlog]: https://github.com/ManufacturaInd/python-zenlog
[structlog]: https://www.structlog.org/en/stable/
[jsonlog]: https://github.com/borntyping/jsonlog
[dependents]: https://github.com/borntyping/python-colorlog/network/dependents?package_id=UGFja2FnZS01MDk3NDcyMQ%3D%3D
