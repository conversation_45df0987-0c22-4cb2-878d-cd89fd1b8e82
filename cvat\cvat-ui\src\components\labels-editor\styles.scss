// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

textarea.ant-input.cvat-raw-labels-viewer {
    border-color: $border-color-1;
    box-shadow: none;
    border-top: none;
    border-radius: 0 0 $border-radius-base $border-radius-base;
    min-height: 9em;
    font-family: $monospaced-fonts-stack;

    &:focus {
        border-color: $border-color-1;
        box-shadow: none;
    }

    &:hover {
        border-color: $border-color-1;
        box-shadow: none;
    }
}

.cvat-constructor-viewer {
    border: 1px solid $border-color-1;
    box-shadow: none;
    border-top: none;
    border-radius: 0 0 $border-radius-base $border-radius-base;
    padding: $grid-unit-size;
    display: flex;
    overflow-y: auto;
    min-height: 9em;
    flex-wrap: wrap;
    align-items: baseline;
}

.cvat-constructor-viewer-item {
    height: fit-content;
    display: flex;
    align-items: center;
    padding: 2px $grid-unit-size;
    border-radius: $border-radius-base;
    margin: 2px;
    margin-left: $grid-unit-size;
    user-select: none;
    border: 1px solid $border-color-1;
    opacity: 0.6;

    > span {
        margin-left: 5px;

        > span[role='img']:hover {
            filter: invert(0.2);
        }
    }

    &:hover {
        opacity: 1;
    }
}

.cvat-constructor-viewer-new-item,
.cvat-constructor-viewer-new-skeleton-item,
.cvat-constructor-viewer-new-from-model-item {
    height: fit-content;
    display: flex;
    align-items: center;
    padding: 2px $grid-unit-size;
    margin: 2px;
    margin-left: $grid-unit-size;
    user-select: none;
    opacity: 1;
    border-color: $border-color-1;
}

.cvat-label-constructor-creator,
.cvat-label-constructor-updater {
    > form:first-child {
        margin-top: $grid-unit-size;
    }
}

.cvat-attribute-constructor-form > div:first-child > div:nth-child(4) {
    display: contents;
}

.cvat-delete-attribute-button:hover > span[role='img'] {
    color: $danger-icon-color;
}

.cvat-new-attribute-button {
    width: 100%;
}

.cvat-change-task-label-color-button {
    width: 100%;

    .ant-badge-status-text {
        margin-left: $grid-unit-size * 2;
    }
}

.ant-badge.cvat-change-task-label-color-badge > .ant-badge-status-dot {
    width: $grid-unit-size * 2;
    height: $grid-unit-size * 2;
    border-radius: $border-radius-base;
}

.cvat-skeleton-configurator {
    position: relative;
    width: 100%;
    margin-top: $grid-unit-size;
    margin-bottom: $grid-unit-size;
    padding-top: $grid-unit-size;
    padding-bottom: $grid-unit-size;
    justify-content: center;

    > div:first-child {
        width: $grid-unit-size * 6;
        position: absolute;
        left: 1px;
    }

    > .ant-alert {
        width: 100%;
    }

    button {
        width: 40px;
        height: 40px;
    }
}

.cvat-skeleton-configurator-canvas {
    width: 100%;
    border: 1px solid $border-color-1;
    border-radius: $border-radius-base;
    height: auto;
}

.cvat-skeleton-configurator-svg {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
}

.cvat-skeleton-configurator-shape-buttons,
.cvat-skeleton-configurator-action-buttons {
    margin-top: $grid-unit-size;
}

.cvat-skeleton-configurator-svg-buttons {
    > button {
        margin-bottom: $grid-unit-size;
    }
}

.cvat-skeleton-configurator-shape-buttons {
    .ant-radio-group {
        text-align: center;
        width: $grid-unit-size * 5;
        margin-top: $grid-unit-size * 3;
        margin-bottom: $grid-unit-size * 3;

        label {
            padding: 0;
            width: $grid-unit-size * 5;
            height: $grid-unit-size * 5;
            line-height: $grid-unit-size * 5;
            margin-bottom: $grid-unit-size;

            .anticon-line {
                transform: rotate(45deg);
            }

            svg {
                width: $grid-unit-size * 2;
                height: $grid-unit-size * 2;
            }
        }
    }
}

.cvat-skeleton-canvas-wrapper {
    max-width: $grid-unit-size * 75;
    max-height: $grid-unit-size * 75;
    width: 100%;
    margin-left: $grid-unit-size * 6;
    position: relative;
}

.cvat-skeleton-configurator-text-label {
    font-size: 0.2em;
    font-weight: bold;
    pointer-events: none;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 100%);
}

.cvat-skeleton-configurator-context-menu {
    position: absolute;
    box-shadow: $box-shadow-base;
    z-index: 2;
    display: grid;
    background: $background-color-1;

    > button {
        line-height: 24px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        color: black;
    }
}

.cvat-label-constructor-pick-from-model {
    display: block;
    padding-top: $grid-unit-size;

    .ant-select {
        width: 73%;
        margin-top: $grid-unit-size;
        margin-right: $grid-unit-size * 2;
    }

    .ant-empty {
        button {
            margin-top: $grid-unit-size * 2;
        }
    }

    .cvat-label-constructor-pick-from-model-list {
        width: 100%;
        border-radius: 0 0 $border-radius-base $border-radius-base;
        display: flex;
        flex-wrap: wrap;

        .ant-empty {
            width: 100%;
            margin-top: $grid-unit-size;
        }

        > button {
            margin-top: $grid-unit-size;
            margin-right: $grid-unit-size;
        }
    }
}