import pymysql
import datetime
import json
import os

# 数据库连接配置
db_config = {
    'host': 'rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
    'user': 'ai',
    'password': 'wq$$4r%ixg',
    'database': 'backend_test',
    'port': 3306,
    'charset': 'utf8mb4'
}

def test_db_connection(config):
    """测试数据库连接"""
    connection = None
    try:
        connection = pymysql.connect(**config)
        print(f"成功连接到数据库: {config['database']}@{config['host']}:{config['port']}")
        return True
    except Exception as e:
        print(f"连接失败: {e}")
        return False
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭")

def query_by_id(target_date, record_id):
    """
    根据ID查询数据
    
    Args:
        target_date: 目标日期，格式为 datetime 对象
        record_id: 记录ID
    """
    connection = None
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 1. 构造表名
        table_date = target_date.strftime("%Y%m%d")
        table_name = f"t_data_ecg_{table_date}"
        
        print(f"尝试查询表: {table_name}")
        
        # 2. 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{db_config['database']}' 
            AND table_name = '{table_name}'
        """)
        if cursor.fetchone()[0] == 0:
            print(f"表 {table_name} 不存在")
            return None
        
        # 3. 根据ID查询数据
        print(f"\n------ 查询 ID = {record_id} 的记录 ------")
        
        cursor.execute(f"""
            SELECT * FROM {table_name}
            WHERE id = {record_id}
        """)
        record = cursor.fetchone()
        
        if not record:
            print(f"没有找到 ID = {record_id} 的记录")
            return None
        else:
            print(f"找到 ID = {record_id} 的记录")
            
            # 获取列名
            cursor.execute(f"SHOW COLUMNS FROM {table_name}")
            column_names = [column[0] for column in cursor.fetchall()]
            
            # 将记录转换为字典
            result = dict(zip(column_names, record))
            
            # 打印记录的基本信息（不包括大型数据字段）
            print("\n记录基本信息:")
            for key, value in result.items():
                if key not in ['ecg', 'ecgII', 'ecgIII', 'ecg_byte', 'ecg_summary', 'ecg_analysis', 
                               'ecg_analysisII', 'ecg_analysisIII']:
                    print(f"  {key}: {value}")
            
            return {"column_names": column_names, "record": result}
    except Exception as e:
        print(f"查询出错: {e}")
        return None
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭")

def save_record_to_json(data, record_id, date_str):
    """保存记录到JSON文件"""
    if not data or not data.get("record"):
        print("没有数据可以保存")
        return None
    
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    filename = f'ecg_record_{record_id}_{date_str}_{timestamp}.json'
    
    try:
        # 创建一个新字典，仅包含基本信息（不包括大型数据字段）
        record_summary = {}
        for key, value in data["record"].items():
            if key not in ['ecg', 'ecgII', 'ecgIII', 'ecg_byte', 'ecg_summary', 'ecg_analysis', 
                          'ecg_analysisII', 'ecg_analysisIII']:
                record_summary[key] = value
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(record_summary, f, ensure_ascii=False, indent=2, default=str)
        print(f"记录基本信息已成功导出到文件: {filename}")
        
        # 保存完整记录（包括大型数据字段）
        full_filename = f'ecg_record_full_{record_id}_{date_str}_{timestamp}.json'
        with open(full_filename, 'w', encoding='utf-8') as f:
            json.dump(data["record"], f, ensure_ascii=False, indent=2, default=str)
        print(f"完整记录已成功导出到文件: {full_filename}")
        
        return filename
    except Exception as e:
        print(f"保存JSON时出错: {e}")
        return None

if __name__ == "__main__":
    target_date = datetime.datetime(2025, 4, 1)  # 查询日期：2025年4月1日
    date_str = target_date.strftime("%Y%m%d")
    target_id = 1906972114197643266  # 目标ID
    
    if test_db_connection(db_config):
        print(f"\n------ 开始查询 ID = {target_id} 的数据 ------")
        
        # 查询特定ID的记录
        record_data = query_by_id(target_date, target_id)
        
        if record_data and record_data.get("record"):
            # 保存记录到JSON文件
            save_record_to_json(record_data, target_id, date_str)
        else:
            print(f"未找到 ID = {target_id} 的记录") 