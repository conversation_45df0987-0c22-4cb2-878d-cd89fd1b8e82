# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-15 17:49+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Serbian (http://www.transifex.com/django/django/language/"
"sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

msgid "Humanize"
msgstr "Улепшавање"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}-ти"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}-ти"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}-и"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}-и"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}-и"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}-и"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}-и"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}-и"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}-и"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}-и"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}-и"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s милион"
msgstr[1] "%(value)s милиона"
msgstr[2] "%(value)s милиона"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s милијарда"
msgstr[1] "%(value)s милијарде"
msgstr[2] "%(value)s милијарди"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s билион"
msgstr[1] "%(value)s билиона"
msgstr[2] "%(value)s билиона"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s билијарда"
msgstr[1] "%(value)s билијарде"
msgstr[2] "%(value)s билијарди"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s трилион"
msgstr[1] "%(value)s трилиона"
msgstr[2] "%(value)s трилиона"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s трилијарда"
msgstr[1] "%(value)s трилијарде"
msgstr[2] "%(value)s трилијарди"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)sквадрилион "
msgstr[1] "%(value)sквадрилиона "
msgstr[2] "%(value)sквадрилиона "

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)sквадрилијарда "
msgstr[1] "%(value)sквадрилијарде "
msgstr[2] "%(value)sквадрилијарди "

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)sквантилион "
msgstr[1] "%(value)sквантилиона "
msgstr[2] "%(value)sквантилиона "

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)sквантилијарда "
msgstr[1] "%(value)sквантилијарде "
msgstr[2] "%(value)sквантилијарди "

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s гугол"
msgstr[1] "%(value)s гугола"
msgstr[2] "%(value)s гугола"

msgid "one"
msgstr "један"

msgid "two"
msgstr "два"

msgid "three"
msgstr "три"

msgid "four"
msgstr "четири"

msgid "five"
msgstr "пет"

msgid "six"
msgstr "шест"

msgid "seven"
msgstr "седам"

msgid "eight"
msgstr "осам"

msgid "nine"
msgstr "девет"

msgid "today"
msgstr "данас"

msgid "tomorrow"
msgstr "сутра"

msgid "yesterday"
msgstr "јуче"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "пре %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "пре %(count)s сата"
msgstr[1] "пре %(count)s сата"
msgstr[2] "пре %(count)s сати"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "пре %(count)s минута"
msgstr[1] "пре %(count)s минута"
msgstr[2] "пре %(count)s минута"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "пре %(count)s секунде"
msgstr[1] "пре %(count)s секунде"
msgstr[2] "пре %(count)s секунди"

msgid "now"
msgstr "сада"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "%(count)s секунда од сад"
msgstr[1] "%(count)s секунде од сада"
msgstr[2] "%(count)s секунди од сада"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "%(count)s минут од сад"
msgstr[1] "%(count)s минута од сада"
msgstr[2] "%(count)s минута од сада"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "%(count)s сат од сад"
msgstr[1] "%(count)s сата од сада"
msgstr[2] "%(count)s сати од сада"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "%(delta)s од сад"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d годину"
msgstr[1] "%d године"
msgstr[2] "%d година"

#, python-format
msgctxt "naturaltime-past"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d месеца"
msgstr[1] "%d месеца"
msgstr[2] "%d месеци"

#, python-format
msgctxt "naturaltime-past"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d недеље"
msgstr[1] "%d недеље"
msgstr[2] "%d недеља"

#, python-format
msgctxt "naturaltime-past"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d дана"
msgstr[1] "%d дана"
msgstr[2] "%d дана"

#, python-format
msgctxt "naturaltime-past"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d сата"
msgstr[1] "%d сата"
msgstr[2] "%d сати"

#, python-format
msgctxt "naturaltime-past"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d минута"
msgstr[1] "%d минута"
msgstr[2] "%d минута"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d година"
msgstr[1] "%d године"
msgstr[2] "%d година"

#, python-format
msgctxt "naturaltime-future"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d месец"
msgstr[1] "%d месеца"
msgstr[2] "%d месеци"

#, python-format
msgctxt "naturaltime-future"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d недеља"
msgstr[1] "%d недеље"
msgstr[2] "%d недеља"

#, python-format
msgctxt "naturaltime-future"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d дан"
msgstr[1] "%d дана"
msgstr[2] "%d дана"

#, python-format
msgctxt "naturaltime-future"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d сат"
msgstr[1] "%d сата"
msgstr[2] "%d сати"

#, python-format
msgctxt "naturaltime-future"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d минут"
msgstr[1] "%d минута"
msgstr[2] "%d минута"
