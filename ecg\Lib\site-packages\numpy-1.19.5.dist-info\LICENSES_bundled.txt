The NumPy repository and source distributions bundle several libraries that are
compatibly licensed.  We list these here.

Name: Numpydoc
Files: doc/sphinxext/numpydoc/*
License: BSD-2-Clause
  For details, see doc/sphinxext/LICENSE.txt

Name: scipy-sphinx-theme
Files: doc/scipy-sphinx-theme/*
License: BSD-3-Clause AND PSF-2.0 AND Apache-2.0
  For details, see doc/scipy-sphinx-theme/LICENSE.txt

Name: lapack-lite
Files: numpy/linalg/lapack_lite/*
License: BSD-3-Clause
  For details, see numpy/linalg/lapack_lite/LICENSE.txt

Name: tempita
Files: tools/npy_tempita/*
License: MIT
  For details, see tools/npy_tempita/license.txt

Name: dragon4
Files: numpy/core/src/multiarray/dragon4.c
License: MIT
  For license text, see numpy/core/src/multiarray/dragon4.c
