// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-create-project-form-wrapper {
    text-align: center;
    padding-top: $grid-unit-size * 5;
    overflow-y: auto;
    height: 90%;
    position: fixed;
    width: 100%;

    > div > span {
        font-size: $grid-unit-size * 4;
    }
}

.cvat-create-project-content {
    margin-top: $grid-unit-size * 2;
    width: 100%;
    height: auto;
    border: 1px solid $border-color-1;
    border-radius: $border-radius-base;
    padding: $grid-unit-size * 2;
    background: $background-color-1;
    text-align: initial;

    > div:not(first-child) {
        margin-top: $grid-unit-size;
    }

    > div:nth-child(4) {
        display: flex;
        justify-content: flex-end;

        > button {
            width: $grid-unit-size * 15;
        }
    }
}
