Metadata-Version: 2.1
Name: biosppy
Version: 2.1.2
Summary: A toolbox for biosignal processing written in Python.
Home-page: https://github.com/scientisst/BioSPPy
Author: Instituto de Telecomunicacoes
Author-email: <EMAIL>
License: BSD 3-clause
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: Natural Language :: English
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Operating System :: OS Independent
Requires-Python: >3.5.2
Description-Content-Type: text/markdown
License-File: LICENSE
License-File: AUTHORS.md
Requires-Dist: bidict
Requires-Dist: h5py
Requires-Dist: matplotlib
Requires-Dist: numpy
Requires-Dist: scikit-learn
Requires-Dist: scipy
Requires-Dist: shortuuid
Requires-Dist: six
Requires-Dist: joblib
Requires-Dist: opencv-python
Requires-Dist: pywavelets
Provides-Extra: eda
Requires-Dist: cvxopt ; extra == 'eda'


```
🌀 New module for signal quality assessment 🌀
With the signals.quality module you can now evaluate the quality of your signals!
So far, the EDA and ECG quality are available, but more could be added soon. 
```

```
🫀 New module for heart rate variability 🫀
With the signals.hrv module you can now extract HRV features. Check it out! (PR #19)
```
```
✨ New plotting design ✨
New colors, new style and new features, check it out!
```
```
🎊 New module for feature extraction 🎊
With the 'features' module you can easily extract features from your biosignals!
(Check PR #4)
```


# BioSPPy - Biosignal Processing in Python

*A toolbox for biosignal processing written in Python.*

<a href="http://biosppy.readthedocs.org/">
<picture>
  <source media="(prefers-color-scheme: light)" srcset="docs/logo/logo_400.png">
  <source media="(prefers-color-scheme: dark)" srcset="docs/logo/logo_inverted_400.png">
  <img alt="Image" title="I know you're listening! - xkcd.com/525">
</picture>
</a>

The toolbox bundles together various signal processing and pattern recognition
methods geared towards the analysis of biosignals.

Highlights:

- Support for various biosignals: BVP, ECG, EDA, EEG, EMG, PCG, PPG, Respiration
- Signal analysis primitives: filtering, frequency analysis
- Clustering
- Biometrics

Documentation can be found at: <http://biosppy.readthedocs.org/>

## Installation

Installation can be easily done with `pip`:

```bash
$ pip install biosppy
```

Alternatively, you can install the latest version from the GitHub repository:

```bash
$ pip install git+https://github.com/scientisst/BioSPPy.git
```

## Simple Example

The code below loads an ECG signal from the `examples` folder, filters it,
performs R-peak detection, and computes the instantaneous heart rate.

```python
from biosppy import storage
from biosppy.signals import ecg

# load raw ECG signal
signal, mdata = storage.load_txt('./examples/ecg.txt')

# process it and plot
out = ecg.ecg(signal=signal, sampling_rate=1000., show=True)
```

This should produce a plot similar to the one below.

![ECG summary example](docs/images/ECG_summary.png)

## Dependencies

- bidict
- h5py
- matplotlib
- numpy
- scikit-learn
- scipy
- shortuuid
- six
- joblib

## Citing
Please use the following if you need to cite BioSPPy:

- Carreiras C, Alves AP, Lourenço A, Canento F, Silva H, Fred A, *et al.*
  **BioSPPy - Biosignal Processing in Python**, 2015-,
  https://github.com/PIA-Group/BioSPPy/ [Online; accessed ```<year>-<month>-<day>```].

```latex
@Misc{,
  author = {Carlos Carreiras and Ana Priscila Alves and Andr\'{e} Louren\c{c}o and Filipe Canento and Hugo Silva and Ana Fred and others},
  title = {{BioSPPy}: Biosignal Processing in {Python}},
  year = {2015--},
  url = "https://github.com/PIA-Group/BioSPPy/",
  note = {[Online; accessed <today>]}
}
```

## License

BioSPPy is released under the BSD 3-clause license. See LICENSE for more details.

## Disclaimer

This program is distributed in the hope it will be useful and provided
to you "as is", but WITHOUT ANY WARRANTY, without even the implied
warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. This
program is NOT intended for medical diagnosis. We expressly disclaim any
liability whatsoever for any direct, indirect, consequential, incidental
or special damages, including, without limitation, lost revenues, lost
profits, losses resulting from business interruption or loss of data,
regardless of the form of action or legal theory under which the
liability may be asserted, even if advised of the possibility of such
damages.
