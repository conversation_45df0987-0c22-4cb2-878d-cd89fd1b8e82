#!/usr/bin/env python3
"""
超级增强版羽毛球装备爬虫 - 完整数据提取
专门解决数据缺失问题，确保所有字段都被正确填充
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os
from urllib.parse import urljoin

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltraEnhancedCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 确保输出目录存在
        os.makedirs('output', exist_ok=True)
        
        # 基础URL
        self.base_url = "https://www.badmintoncn.com"
    
    def ask_ai_for_answer(self, question):
        """AI验证问题回答"""
        try:
            logger.info(f"AI验证问题: {question}")
            
            # 预处理常见问题
            question_lower = str(question).lower()
            
            # 直接回答的问题库
            direct_answers = {
                '羽毛球有几根毛': '16',
                '羽毛球几根毛': '16', 
                'zyzx小写怎么写': 'zyzx',
                'zyzx大写怎么写': 'ZYZX',
                'zyzx怎么写': 'zyzx',
                '中羽在线英文缩写': 'ZYZX',
                '中羽缩写': 'ZYZX',
            }
            
            for key, answer in direct_answers.items():
                if key in question_lower:
                    logger.info(f"直接回答: {answer}")
                    return answer
            
            # 数学计算
            math_result = self.calculate_math_expression(question)
            if math_result is not None:
                logger.info(f"数学计算结果: {math_result}")
                return str(math_result)
            
            # 调用AI API (如果需要)
            try:
                api_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer pat-20241215-QGKMa1gNWFqZCeaQ4nO5Wjrd5e1YdOZWGWH1GsQN35BUKi5m39sVhL4iGQXO1Pj5"
                }
                
                data = {
                    "model": "ep-20241215142258-fwxf9",
                    "messages": [
                        {"role": "system", "content": "你是验证问题回答助手，请直接给出简短准确答案。"},
                        {"role": "user", "content": f"问题：{question}"}
                    ],
                    "max_tokens": 10,
                    "temperature": 0.1
                }
                
                response = requests.post(api_url, headers=headers, json=data, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    answer = result['choices'][0]['message']['content'].strip()
                    
                    # 提取数字答案
                    numbers = re.findall(r'\d+', answer)
                    if numbers:
                        return numbers[0]
                    else:
                        return answer
                        
            except Exception as e:
                logger.warning(f"AI API调用失败: {e}")
            
            # 备用答案
            return self.get_fallback_answer(question)
                
        except Exception as e:
            logger.error(f"AI验证处理失败: {e}")
            return self.get_fallback_answer(question)
    
    def calculate_math_expression(self, question):
        """计算数学表达式"""
        try:
            # 数学表达式模式
            patterns = [
                (r'(\d+)\s*[×*]\s*(\d+)', lambda x, y: x * y),
                (r'(\d+)\s*[+]\s*(\d+)', lambda x, y: x + y),
                (r'(\d+)\s*[-]\s*(\d+)', lambda x, y: x - y),
                (r'(\d+)\s*[/÷]\s*(\d+)', lambda x, y: x // y if y != 0 else None),
            ]
            
            for pattern, operation in patterns:
                match = re.search(pattern, str(question))
                if match:
                    num1, num2 = int(match.group(1)), int(match.group(2))
                    result = operation(num1, num2)
                    if result is not None:
                        return result
            
            return None
            
        except Exception:
            return None
    
    def get_fallback_answer(self, question):
        """备用答案策略"""
        # 简单数学计算
        if '×' in question or '*' in question:
            parts = re.findall(r'\d+', question)
            if len(parts) >= 2:
                return str(int(parts[0]) * int(parts[1]))
        elif '+' in question:
            parts = re.findall(r'\d+', question)
            if len(parts) >= 2:
                return str(int(parts[0]) + int(parts[1]))
        elif '-' in question:
            parts = re.findall(r'\d+', question)
            if len(parts) >= 2:
                return str(int(parts[0]) - int(parts[1]))
        
        return "42"  # 默认答案
    
    def bypass_verification(self, url, max_retries=3):
        """绕过验证页面"""
        for attempt in range(max_retries):
            try:
                logger.debug(f"访问 (尝试 {attempt + 1}/{max_retries}): {url}")
                response = self.session.get(url, timeout=15)
                
                if response.status_code != 200:
                    logger.warning(f"状态码异常: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                        continue
                    else:
                        return None
                
                # 检查验证页面
                response_text = response.text
                verification_patterns = [
                    r'\d+[×*]\d+=？', r'\d+[+]\d+=？', r'\d+[-]\d+=？',
                    r'羽毛球有几根毛', r'ZYZX.*?怎么写', r'中羽.*?缩写',
                    r'验证.*?问题', r'请输入.*?答案',
                ]
                
                has_verification = any(re.search(pattern, response_text, re.IGNORECASE) 
                                     for pattern in verification_patterns)
                
                if has_verification:
                    logger.debug(f"检测到验证页面 (尝试 {attempt + 1})")
                    verified_content = self.handle_verification(response_text, url)
                    if verified_content:
                        logger.debug(f"✅ 验证成功 (尝试 {attempt + 1})")
                        return verified_content
                    else:
                        if attempt < max_retries - 1:
                            time.sleep(3)
                            continue
                else:
                    logger.debug(f"✅ 直接访问成功 (尝试 {attempt + 1})")
                    return response_text
                    
            except Exception as e:
                logger.error(f"访问失败 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue
        
        logger.error(f"所有访问尝试失败: {url}")
        return None
    
    def handle_verification(self, html_content, original_url):
        """处理验证页面"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找验证问题
            question_patterns = [
                r'(\d+[×*]\d+)=？', r'(\d+[+]\d+)=？', r'(\d+[-]\d+)=？',
                r'羽毛球有几根毛', r'ZYZX.*?怎么写', r'中羽.*?缩写',
            ]
            
            question = None
            full_text = soup.get_text()
            
            for pattern in question_patterns:
                matches = re.findall(pattern, full_text, re.IGNORECASE)
                if matches:
                    question = matches[0]
                    break
            
            if not question:
                logger.error("未找到验证问题")
                return None
            
            # 获取答案
            answer = self.ask_ai_for_answer(question)
            if not answer:
                return None
            
            # 查找表单
            form = soup.find('form')
            if not form:
                return None
            
            form_data = {}
            
            # 收集隐藏字段
            for inp in form.find_all('input', {'type': 'hidden'}):
                name = inp.get('name')
                value = inp.get('value', '')
                if name:
                    form_data[name] = value
            
            # 设置答案字段
            answer_fields = ['answer', 'verify', 'code', 'result', 'a']
            for field in answer_fields:
                if form.find('input', {'name': field}):
                    form_data[field] = answer
                    break
            else:
                form_data['a'] = answer
            
            # 提交按钮
            submit_btn = form.find('input', {'type': 'submit'})
            if submit_btn:
                submit_name = submit_btn.get('name')
                submit_value = submit_btn.get('value', '提交')
                if submit_name:
                    form_data[submit_name] = submit_value
            
            # 确定提交URL
            submit_url = form.get('action', original_url)
            if not submit_url.startswith('http'):
                submit_url = urljoin(self.base_url, submit_url)
            
            # 提交验证
            response = self.session.post(submit_url, data=form_data, timeout=15)
            
            if response.status_code == 200:
                return response.text
            else:
                return None
                
        except Exception as e:
            logger.error(f"验证处理失败: {e}")
            return None
    
    def get_equipment_list(self):
        """获取装备列表"""
        try:
            list_url = f"{self.base_url}/cbo_eq/list.php"
            html_content = self.bypass_verification(list_url)
            
            if not html_content:
                return []
            
            soup = BeautifulSoup(html_content, 'html.parser')
            equipment_links = []
            
            # 查找装备链接
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and 'view.php?eid=' in href:
                    if not href.startswith('http'):
                        href = urljoin(self.base_url, href)
                    equipment_links.append(href)
            
            # 去重
            equipment_links = list(set(equipment_links))
            logger.info(f"找到 {len(equipment_links)} 个装备链接")
            
            return equipment_links
            
        except Exception as e:
            logger.error(f"获取装备列表失败: {e}")
            return []
    
    def parse_equipment_detail(self, url):
        """超级增强版装备详情解析"""
        try:
            # 提取装备ID
            eid_match = re.search(r'eid=(\d+)', url)
            equipment_id = eid_match.group(1) if eid_match else 'unknown'
            
            logger.info(f"🔍 正在解析装备 {equipment_id}: {url}")
            
            # 获取主页面
            html_content = self.bypass_verification(url)
            if not html_content:
                return None
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 初始化数据结构
            equipment_data = {
                'equipment_id': equipment_id,
                'equipment_name': '',
                'equipment_type': '',
                'brand': '',
                'series': '',
                'description': '',
                'release_date': '',
                'introduction': '',
                'specifications': '',
                'frame_material': '',
                'shaft_material': '',
                'weight': '',
                'length': '',
                'grip_size': '',
                'shaft_stiffness': '',
                'string_tension': '',
                'balance_point': '',
                'purchase_price': '',
                'new_avg_price': '',
                'used_avg_price': '',
                'total_users': '',
                'review_count': '',
                'pro_players': '',
                'image_url': '',
                'detail_url': url,
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 1. 解析主页面基础信息
            self.parse_main_page_info(soup, html_content, equipment_data)
            
            # 2. 访问相关页面获取更多信息
            self.fetch_related_pages_info(equipment_id, equipment_data)
            
            # 3. 智能提取缺失信息
            self.extract_missing_technical_specs(soup, html_content, equipment_data)
            
            # 4. 清理和格式化数据
            self.clean_and_format_data(equipment_data)
            
            logger.info(f"✅ 解析完成: {equipment_data['equipment_name']} ({equipment_data['brand']})")
            return equipment_data
            
        except Exception as e:
            logger.error(f"解析装备详情失败 {url}: {e}")
            return None

    def parse_main_page_info(self, soup, html_content, equipment_data):
        """解析主页面信息"""
        try:
            # 提取标题
            title = soup.title.string if soup.title else ""
            equipment_data['equipment_name'] = self.clean_equipment_name(title)
            
            # 解析表格数据
            tables = soup.find_all('table')
            logger.debug(f"找到 {len(tables)} 个表格")
            
            # 基础信息提取
            for table in tables:
                self.extract_from_table(table, equipment_data)
            
            # 从页面文本中提取信息
            page_text = soup.get_text()
            self.extract_from_text(page_text, equipment_data)
            
            # 提取图片
            self.extract_equipment_image(soup, equipment_data)
            
            # 提取评分和评价信息
            self.extract_rating_info(html_content, equipment_data)
            
        except Exception as e:
            logger.error(f"解析主页面信息失败: {e}")

    def extract_from_table(self, table, equipment_data):
        """从表格中提取信息"""
        try:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    key = cells[0].get_text(strip=True)
                    value = cells[1].get_text(strip=True)
                    
                    # 映射关系
                    field_mapping = {
                        '装备类型': 'equipment_type',
                        '类型': 'equipment_type',
                        '装备品牌': 'brand',
                        '品牌': 'brand',
                        '装备系列': 'series',
                        '系列': 'series',
                        '上市日期': 'release_date',
                        '发布日期': 'release_date',
                        '拍框材质': 'frame_material',
                        '框架材质': 'frame_material',
                        '中管材质': 'shaft_material',
                        '拍杆材质': 'shaft_material',
                        '杆身材质': 'shaft_material',
                        '重量': 'weight',
                        '拍重': 'weight',
                        '球拍重量': 'weight',
                        '长度': 'length',
                        '球拍长度': 'length',
                        '手柄尺寸': 'grip_size',
                        '握把尺寸': 'grip_size',
                        '柄围': 'grip_size',
                        '中管弹性': 'shaft_stiffness',
                        '硬度': 'shaft_stiffness',
                        '中杆硬度': 'shaft_stiffness',
                        '拍杆硬度': 'shaft_stiffness',
                        '穿线磅数': 'string_tension',
                        '拉线磅数': 'string_tension',
                        '建议磅数': 'string_tension',
                        '平衡点': 'balance_point',
                        '重心': 'balance_point',
                    }
                    
                    for keyword, field in field_mapping.items():
                        if keyword in key and not equipment_data[field]:
                            equipment_data[field] = value
                            break
                    
                    # 收集所有规格信息
                    if key and value and key not in ['', ' ']:
                        if equipment_data['specifications']:
                            equipment_data['specifications'] += f"; {key}: {value}"
                        else:
                            equipment_data['specifications'] = f"{key}: {value}"
                            
        except Exception as e:
            logger.error(f"从表格提取信息失败: {e}")

    def extract_from_text(self, page_text, equipment_data):
        """从页面文本中提取信息"""
        try:
            # 技术参数正则模式
            patterns = {
                'weight': [r'重量.*?(\d+U)', r'(\d+)克', r'(\d+)g'],
                'shaft_stiffness': [r'中杆.*?(硬|软|适中)', r'韧度.*?(硬|软|适中)', r'(偏软|偏硬)'],
                'string_tension': [r'(\d+)磅', r'(\d+)LBS', r'拉线.*?(\d+)'],
                'balance_point': [r'平衡点.*?(\d+)mm', r'重心.*?(\d+)'],
                'frame_material': [r'拍框[：:]\s*([^，\n；]+)', r'框架材质[：:]\s*([^，\n；]+)'],
                'shaft_material': [r'中管[：:]\s*([^，\n；]+)', r'杆身材质[：:]\s*([^，\n；]+)'],
            }
            
            for field, field_patterns in patterns.items():
                if not equipment_data[field]:
                    for pattern in field_patterns:
                        match = re.search(pattern, page_text, re.IGNORECASE)
                        if match:
                            equipment_data[field] = match.group(1).strip()
                            break
                            
        except Exception as e:
            logger.error(f"从文本提取信息失败: {e}")

    def extract_equipment_image(self, soup, equipment_data):
        """提取装备图片"""
        try:
            # 优先级顺序查找图片
            img_selectors = [
                'img[src*="upload"]',
                'img[alt*="羽毛球"]',
                'img[src*="equipment"]',
                'img[src*="eq_"]',
                'img[src*=".jpg"]',
                'img[src*=".png"]',
            ]
            
            for selector in img_selectors:
                imgs = soup.select(selector)
                if imgs:
                    src = imgs[0].get('src')
                    if src:
                        if not src.startswith('http'):
                            src = urljoin(self.base_url, src)
                        equipment_data['image_url'] = src
                        break
                        
        except Exception as e:
            logger.error(f"提取图片失败: {e}")

    def extract_rating_info(self, html_content, equipment_data):
        """提取评分和评价信息"""
        try:
            # 提取评分信息
            rating_patterns = [
                r'(\d+\.\d+)中羽评分',
                r'评分[：:]\s*(\d+\.\d+)',
                r'(\d+)条评价',
                r'(\d+)人评价',
                r'评价.*?\((\d+)\)',
            ]
            
            for pattern in rating_patterns:
                match = re.search(pattern, html_content)
                if match:
                    if '评价' in pattern:
                        equipment_data['review_count'] = match.group(1)
                    break
            
            # 从评分统计中提取评价数量
            star_pattern = r'5★(\d+(?:\.\d+)?)%.*?4★(\d+(?:\.\d+)?)%'
            star_match = re.search(star_pattern, html_content)
            if star_match and not equipment_data['review_count']:
                # 如果能找到星级分布，说明有评价数据
                equipment_data['review_count'] = "有评价"
                
        except Exception as e:
            logger.error(f"提取评分信息失败: {e}")

    def fetch_related_pages_info(self, equipment_id, equipment_data):
        """访问相关页面获取更多信息"""
        try:
            # 1. 价格页面
            self.fetch_price_page_info(equipment_id, equipment_data)
            
            # 2. 球员页面
            self.fetch_player_page_info(equipment_id, equipment_data)
            
            # 3. 用户页面
            self.fetch_user_page_info(equipment_id, equipment_data)
            
        except Exception as e:
            logger.error(f"访问相关页面失败: {e}")

    def fetch_price_page_info(self, equipment_id, equipment_data):
        """获取价格页面信息"""
        try:
            price_url = f"{self.base_url}/cbo_eq/view_buy.php?eid={equipment_id}"
            price_html = self.bypass_verification(price_url)
            
            if price_html:
                price_soup = BeautifulSoup(price_html, 'html.parser')
                
                # 提取价格信息
                price_texts = price_soup.get_text()
                
                # 全新均价
                new_price_patterns = [
                    r'最近全新均价.*?(\d+)',
                    r'全新均价[：:].*?(\d+)',
                    r'新拍均价.*?(\d+)',
                ]
                
                for pattern in new_price_patterns:
                    match = re.search(pattern, price_texts)
                    if match and not equipment_data['new_avg_price']:
                        equipment_data['new_avg_price'] = match.group(1)
                        break
                
                # 二手均价
                used_price_patterns = [
                    r'最近二手均价.*?(\d+)',
                    r'二手均价[：:].*?(\d+)',
                    r'转让均价.*?(\d+)',
                ]
                
                for pattern in used_price_patterns:
                    match = re.search(pattern, price_texts)
                    if match and not equipment_data['used_avg_price']:
                        equipment_data['used_avg_price'] = match.group(1)
                        break
                
                # 总用户数
                user_patterns = [
                    r'总登记球友.*?(\d+)',
                    r'(\d+)人使用',
                    r'拥有者.*?(\d+)',
                ]
                
                for pattern in user_patterns:
                    match = re.search(pattern, price_texts)
                    if match and not equipment_data['total_users']:
                        equipment_data['total_users'] = match.group(1)
                        break
                
                # 从表格中提取最低价
                tables = price_soup.find_all('table')
                shop_prices = []
                
                for table in tables:
                    for cell in table.find_all(['td', 'th']):
                        text = cell.get_text(strip=True)
                        price_matches = re.findall(r'[¥￥](\d+)', text)
                        shop_prices.extend([int(p) for p in price_matches])
                
                if shop_prices and not equipment_data['purchase_price']:
                    equipment_data['purchase_price'] = str(min(shop_prices))
                    
        except Exception as e:
            logger.error(f"获取价格信息失败: {e}")

    def fetch_player_page_info(self, equipment_id, equipment_data):
        """获取球员页面信息"""
        try:
            player_url = f"{self.base_url}/cbo_eq/view_star.php?eid={equipment_id}"
            player_html = self.bypass_verification(player_url)
            
            if player_html:
                player_soup = BeautifulSoup(player_html, 'html.parser')
                players = []
                
                # 查找球员链接
                for link in player_soup.find_all('a'):
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    
                    if '/cbo_star/' in href and text and len(text) <= 6:
                        players.append(text)
                
                # 从页面文本中提取中文球员名
                page_text = player_soup.get_text()
                chinese_names = re.findall(r'[\u4e00-\u9fff]{2,4}', page_text)
                
                for name in chinese_names:
                    if (len(name) >= 2 and len(name) <= 4 and 
                        name not in players and 
                        not any(char in name for char in '页面搜索评分装备中羽在线')):
                        players.append(name)
                
                if players:
                    equipment_data['pro_players'] = '; '.join(list(set(players))[:5])
                    
        except Exception as e:
            logger.error(f"获取球员信息失败: {e}")

    def fetch_user_page_info(self, equipment_id, equipment_data):
        """获取用户页面信息"""
        try:
            user_url = f"{self.base_url}/cbo_eq/view_user.php?eid={equipment_id}"
            user_html = self.bypass_verification(user_url)
            
            if user_html:
                user_soup = BeautifulSoup(user_html, 'html.parser')
                user_text = user_soup.get_text()
                
                # 提取用户数量
                if not equipment_data['total_users']:
                    user_count_patterns = [
                        r'共(\d+)人',
                        r'总计(\d+)位',
                        r'(\d+)个用户',
                    ]
                    
                    for pattern in user_count_patterns:
                        match = re.search(pattern, user_text)
                        if match:
                            equipment_data['total_users'] = match.group(1)
                            break
                            
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")

    def extract_missing_technical_specs(self, soup, html_content, equipment_data):
        """智能提取缺失的技术规格"""
        try:
            # 如果主要技术参数仍然缺失，使用更多方法提取
            text_content = soup.get_text()
            
            # 重量提取（各种表示方法）
            if not equipment_data['weight']:
                weight_patterns = [
                    r'(\d+U)', r'重量.*?(\d+)', r'(\d+)克', r'(\d+)g',
                    r'Weight.*?(\d+)', r'拍重.*?(\d+)'
                ]
                for pattern in weight_patterns:
                    match = re.search(pattern, text_content, re.IGNORECASE)
                    if match:
                        equipment_data['weight'] = match.group(1)
                        break
            
            # 中管硬度提取
            if not equipment_data['shaft_stiffness']:
                stiffness_patterns = [
                    r'中杆(硬|软|适中)', r'硬度(硬|软|适中)', r'(偏软|偏硬|中等)',
                    r'Flexibility.*?(Stiff|Medium|Flexible)', r'杆身(硬|软)'
                ]
                for pattern in stiffness_patterns:
                    match = re.search(pattern, text_content, re.IGNORECASE)
                    if match:
                        equipment_data['shaft_stiffness'] = match.group(1)
                        break
            
            # 拉线磅数提取
            if not equipment_data['string_tension']:
                tension_patterns = [
                    r'(\d+)磅', r'(\d+)LBS', r'拉线.*?(\d+)', r'磅数.*?(\d+)',
                    r'Tension.*?(\d+)', r'建议.*?(\d+)磅'
                ]
                for pattern in tension_patterns:
                    match = re.search(pattern, text_content, re.IGNORECASE)
                    if match:
                        equipment_data['string_tension'] = match.group(1)
                        break
            
            # 材质信息提取
            material_keywords = {
                'frame_material': ['拍框', '框架', 'Frame'],
                'shaft_material': ['中管', '杆身', '拍杆', 'Shaft']
            }
            
            for field, keywords in material_keywords.items():
                if not equipment_data[field]:
                    for keyword in keywords:
                        pattern = f'{keyword}[：:材质]*\\s*([^，\\n；]+)'
                        match = re.search(pattern, text_content, re.IGNORECASE)
                        if match:
                            material = match.group(1).strip()
                            if len(material) > 2 and len(material) < 50:
                                equipment_data[field] = material
                                break
                        
        except Exception as e:
            logger.error(f"提取缺失技术规格失败: {e}")

    def clean_and_format_data(self, equipment_data):
        """清理和格式化数据"""
        try:
            # 清理装备名称
            if equipment_data['equipment_name']:
                name = equipment_data['equipment_name']
                name = name.replace('中羽在线 badmintoncn.com', '').strip()
                name = re.sub(r'\s+', ' ', name)
                equipment_data['equipment_name'] = name[:80] if len(name) > 80 else name
            
            # 清理品牌名称
            if equipment_data['brand']:
                brand = equipment_data['brand'].strip()
                brand = re.sub(r'\s+', ' ', brand)
                equipment_data['brand'] = brand
            
            # 格式化重量
            if equipment_data['weight']:
                weight = equipment_data['weight']
                if 'U' not in weight and weight.isdigit():
                    # 如果是纯数字，可能需要添加单位
                    if int(weight) < 10:
                        equipment_data['weight'] = f"{weight}U"
                    else:
                        equipment_data['weight'] = f"{weight}g"
            
            # 格式化拉线磅数
            if equipment_data['string_tension']:
                tension = equipment_data['string_tension']
                if tension.isdigit():
                    equipment_data['string_tension'] = f"{tension}磅"
            
            # 格式化平衡点
            if equipment_data['balance_point']:
                balance = equipment_data['balance_point']
                if balance.isdigit():
                    equipment_data['balance_point'] = f"{balance}mm"
            
            # 限制规格字段长度
            if len(equipment_data['specifications']) > 2000:
                equipment_data['specifications'] = equipment_data['specifications'][:2000] + '...'
                
        except Exception as e:
            logger.error(f"清理格式化数据失败: {e}")

    def clean_equipment_name(self, title):
        """清理装备名称"""
        if not title:
            return "未知装备"
        
        name = title.replace('中羽在线 badmintoncn.com', '').strip()
        name = re.sub(r'\s+', ' ', name)
        return name[:80] if len(name) > 80 else name
    
    def crawl_equipment_data(self, max_items=10):
        """爬取装备数据"""
        logger.info(f"🚀 开始爬取装备数据，最大数量: {max_items}")
        
        equipment_links = self.get_equipment_list()
        if not equipment_links:
            logger.error("未获取到装备链接")
            return []
        
        crawled_data = []
        for i, url in enumerate(equipment_links[:max_items]):
            logger.info(f"\n📦 正在爬取 ({i+1}/{min(len(equipment_links), max_items)}): {url}")
            
            equipment_data = self.parse_equipment_detail(url)
            if equipment_data:
                crawled_data.append(equipment_data)
                
                # 显示数据完整度
                filled_fields = sum(1 for v in equipment_data.values() if v)
                total_fields = len(equipment_data)
                completeness = (filled_fields / total_fields) * 100
                logger.info(f"✅ 数据完整度: {completeness:.1f}% ({filled_fields}/{total_fields})")
            else:
                logger.warning(f"❌ 解析失败: {url}")
            
            # 控制请求频率
            if i < len(equipment_links) - 1:
                time.sleep(3)
        
        logger.info(f"\n🎉 爬取完成！成功获取 {len(crawled_data)} 条装备数据")
        return crawled_data
    
    def save_to_csv(self, data, filename):
        """保存数据到CSV"""
        if not data:
            return
            
        fieldnames = [
            'equipment_id', 'equipment_name', 'equipment_type', 'brand', 'series',
            'description', 'release_date', 'introduction', 'specifications',
            'frame_material', 'shaft_material', 'weight', 'length', 'grip_size',
            'shaft_stiffness', 'string_tension', 'balance_point', 'purchase_price',
            'new_avg_price', 'used_avg_price', 'total_users', 'review_count',
            'pro_players', 'image_url', 'detail_url', 'crawl_time'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
            
        logger.info(f"📁 CSV数据已保存到: {filename}")
    
    def save_to_json(self, data, filename):
        """保存数据到JSON"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"📁 JSON数据已保存到: {filename}")
    
    def analyze_data_completeness(self, data):
        """分析数据完整性"""
        if not data:
            return
            
        logger.info("\n📊 数据完整性分析:")
        logger.info(f"总装备数量: {len(data)}")
        
        # 分析每个字段的填充率
        field_stats = {}
        important_fields = [
            'equipment_name', 'equipment_type', 'brand', 'series',
            'frame_material', 'shaft_material', 'weight', 'shaft_stiffness',
            'string_tension', 'balance_point', 'new_avg_price', 'total_users'
        ]
        
        for field in important_fields:
            filled_count = sum(1 for item in data if item.get(field))
            fill_rate = (filled_count / len(data)) * 100
            field_stats[field] = fill_rate
        
        logger.info("关键字段填充率:")
        for field, rate in sorted(field_stats.items(), key=lambda x: x[1], reverse=True):
            status = "✅" if rate >= 80 else "⚠️" if rate >= 50 else "❌"
            logger.info(f"  {status} {field}: {rate:.1f}% ({int(rate * len(data) / 100)}/{len(data)})")
        
        # 品牌统计
        brands = {}
        for item in data:
            brand = item.get('brand', '未知')
            brands[brand] = brands.get(brand, 0) + 1
        
        logger.info("\n品牌分布:")
        for brand, count in sorted(brands.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {brand}: {count}")

def main():
    """主函数"""
    logger.info("🚀 超级增强版爬虫准备启动...")
    
    crawler = UltraEnhancedCrawler()
    logger.info("✅ 爬虫初始化完成")
    
    # 这里只是一个测试框架，实际的爬取逻辑需要继续开发
    logger.info("🎯 程序框架就绪，等待进一步开发...")

if __name__ == "__main__":
    main() 