// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto;
namespace tensorflow {
namespace tfprof {
class AdvisorOptionsProto;
class AdvisorOptionsProtoDefaultTypeInternal;
extern AdvisorOptionsProtoDefaultTypeInternal _AdvisorOptionsProto_default_instance_;
class AdvisorOptionsProto_CheckerOption;
class AdvisorOptionsProto_CheckerOptionDefaultTypeInternal;
extern AdvisorOptionsProto_CheckerOptionDefaultTypeInternal _AdvisorOptionsProto_CheckerOption_default_instance_;
class AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse;
class AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUseDefaultTypeInternal;
extern AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUseDefaultTypeInternal _AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse_default_instance_;
class AdvisorOptionsProto_CheckersEntry_DoNotUse;
class AdvisorOptionsProto_CheckersEntry_DoNotUseDefaultTypeInternal;
extern AdvisorOptionsProto_CheckersEntry_DoNotUseDefaultTypeInternal _AdvisorOptionsProto_CheckersEntry_DoNotUse_default_instance_;
class OptionsProto;
class OptionsProtoDefaultTypeInternal;
extern OptionsProtoDefaultTypeInternal _OptionsProto_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tfprof::AdvisorOptionsProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto>(Arena*);
template<> ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption>(Arena*);
template<> ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::OptionsProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::OptionsProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tfprof {

// ===================================================================

class OptionsProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.OptionsProto) */ {
 public:
  OptionsProto();
  virtual ~OptionsProto();

  OptionsProto(const OptionsProto& from);
  OptionsProto(OptionsProto&& from) noexcept
    : OptionsProto() {
    *this = ::std::move(from);
  }

  inline OptionsProto& operator=(const OptionsProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OptionsProto& operator=(OptionsProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OptionsProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OptionsProto* internal_default_instance() {
    return reinterpret_cast<const OptionsProto*>(
               &_OptionsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(OptionsProto& a, OptionsProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OptionsProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OptionsProto* New() const final {
    return CreateMaybeMessage<OptionsProto>(nullptr);
  }

  OptionsProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OptionsProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OptionsProto& from);
  void MergeFrom(const OptionsProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptionsProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.OptionsProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAccountTypeRegexesFieldNumber = 8,
    kStartNameRegexesFieldNumber = 9,
    kTrimNameRegexesFieldNumber = 10,
    kShowNameRegexesFieldNumber = 11,
    kHideNameRegexesFieldNumber = 12,
    kSelectFieldNumber = 14,
    kOrderByFieldNumber = 7,
    kOutputFieldNumber = 15,
    kDumpToFileFieldNumber = 16,
    kMaxDepthFieldNumber = 1,
    kMinBytesFieldNumber = 2,
    kMinMicrosFieldNumber = 3,
    kMinParamsFieldNumber = 4,
    kMinFloatOpsFieldNumber = 5,
    kMinOccurrenceFieldNumber = 17,
    kStepFieldNumber = 18,
    kMinPeakBytesFieldNumber = 19,
    kMinResidualBytesFieldNumber = 20,
    kMinOutputBytesFieldNumber = 21,
    kMinAcceleratorMicrosFieldNumber = 22,
    kMinCpuMicrosFieldNumber = 23,
    kAccountDisplayedOpOnlyFieldNumber = 13,
  };
  // repeated string account_type_regexes = 8;
  int account_type_regexes_size() const;
  void clear_account_type_regexes();
  const std::string& account_type_regexes(int index) const;
  std::string* mutable_account_type_regexes(int index);
  void set_account_type_regexes(int index, const std::string& value);
  void set_account_type_regexes(int index, std::string&& value);
  void set_account_type_regexes(int index, const char* value);
  void set_account_type_regexes(int index, const char* value, size_t size);
  std::string* add_account_type_regexes();
  void add_account_type_regexes(const std::string& value);
  void add_account_type_regexes(std::string&& value);
  void add_account_type_regexes(const char* value);
  void add_account_type_regexes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& account_type_regexes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_account_type_regexes();

  // repeated string start_name_regexes = 9;
  int start_name_regexes_size() const;
  void clear_start_name_regexes();
  const std::string& start_name_regexes(int index) const;
  std::string* mutable_start_name_regexes(int index);
  void set_start_name_regexes(int index, const std::string& value);
  void set_start_name_regexes(int index, std::string&& value);
  void set_start_name_regexes(int index, const char* value);
  void set_start_name_regexes(int index, const char* value, size_t size);
  std::string* add_start_name_regexes();
  void add_start_name_regexes(const std::string& value);
  void add_start_name_regexes(std::string&& value);
  void add_start_name_regexes(const char* value);
  void add_start_name_regexes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& start_name_regexes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_start_name_regexes();

  // repeated string trim_name_regexes = 10;
  int trim_name_regexes_size() const;
  void clear_trim_name_regexes();
  const std::string& trim_name_regexes(int index) const;
  std::string* mutable_trim_name_regexes(int index);
  void set_trim_name_regexes(int index, const std::string& value);
  void set_trim_name_regexes(int index, std::string&& value);
  void set_trim_name_regexes(int index, const char* value);
  void set_trim_name_regexes(int index, const char* value, size_t size);
  std::string* add_trim_name_regexes();
  void add_trim_name_regexes(const std::string& value);
  void add_trim_name_regexes(std::string&& value);
  void add_trim_name_regexes(const char* value);
  void add_trim_name_regexes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& trim_name_regexes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_trim_name_regexes();

  // repeated string show_name_regexes = 11;
  int show_name_regexes_size() const;
  void clear_show_name_regexes();
  const std::string& show_name_regexes(int index) const;
  std::string* mutable_show_name_regexes(int index);
  void set_show_name_regexes(int index, const std::string& value);
  void set_show_name_regexes(int index, std::string&& value);
  void set_show_name_regexes(int index, const char* value);
  void set_show_name_regexes(int index, const char* value, size_t size);
  std::string* add_show_name_regexes();
  void add_show_name_regexes(const std::string& value);
  void add_show_name_regexes(std::string&& value);
  void add_show_name_regexes(const char* value);
  void add_show_name_regexes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& show_name_regexes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_show_name_regexes();

  // repeated string hide_name_regexes = 12;
  int hide_name_regexes_size() const;
  void clear_hide_name_regexes();
  const std::string& hide_name_regexes(int index) const;
  std::string* mutable_hide_name_regexes(int index);
  void set_hide_name_regexes(int index, const std::string& value);
  void set_hide_name_regexes(int index, std::string&& value);
  void set_hide_name_regexes(int index, const char* value);
  void set_hide_name_regexes(int index, const char* value, size_t size);
  std::string* add_hide_name_regexes();
  void add_hide_name_regexes(const std::string& value);
  void add_hide_name_regexes(std::string&& value);
  void add_hide_name_regexes(const char* value);
  void add_hide_name_regexes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& hide_name_regexes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_hide_name_regexes();

  // repeated string select = 14;
  int select_size() const;
  void clear_select();
  const std::string& select(int index) const;
  std::string* mutable_select(int index);
  void set_select(int index, const std::string& value);
  void set_select(int index, std::string&& value);
  void set_select(int index, const char* value);
  void set_select(int index, const char* value, size_t size);
  std::string* add_select();
  void add_select(const std::string& value);
  void add_select(std::string&& value);
  void add_select(const char* value);
  void add_select(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& select() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_select();

  // string order_by = 7;
  void clear_order_by();
  const std::string& order_by() const;
  void set_order_by(const std::string& value);
  void set_order_by(std::string&& value);
  void set_order_by(const char* value);
  void set_order_by(const char* value, size_t size);
  std::string* mutable_order_by();
  std::string* release_order_by();
  void set_allocated_order_by(std::string* order_by);

  // string output = 15;
  void clear_output();
  const std::string& output() const;
  void set_output(const std::string& value);
  void set_output(std::string&& value);
  void set_output(const char* value);
  void set_output(const char* value, size_t size);
  std::string* mutable_output();
  std::string* release_output();
  void set_allocated_output(std::string* output);

  // string dump_to_file = 16;
  void clear_dump_to_file();
  const std::string& dump_to_file() const;
  void set_dump_to_file(const std::string& value);
  void set_dump_to_file(std::string&& value);
  void set_dump_to_file(const char* value);
  void set_dump_to_file(const char* value, size_t size);
  std::string* mutable_dump_to_file();
  std::string* release_dump_to_file();
  void set_allocated_dump_to_file(std::string* dump_to_file);

  // int64 max_depth = 1;
  void clear_max_depth();
  ::PROTOBUF_NAMESPACE_ID::int64 max_depth() const;
  void set_max_depth(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 min_bytes = 2;
  void clear_min_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 min_bytes() const;
  void set_min_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 min_micros = 3;
  void clear_min_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 min_micros() const;
  void set_min_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 min_params = 4;
  void clear_min_params();
  ::PROTOBUF_NAMESPACE_ID::int64 min_params() const;
  void set_min_params(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 min_float_ops = 5;
  void clear_min_float_ops();
  ::PROTOBUF_NAMESPACE_ID::int64 min_float_ops() const;
  void set_min_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 min_occurrence = 17;
  void clear_min_occurrence();
  ::PROTOBUF_NAMESPACE_ID::int64 min_occurrence() const;
  void set_min_occurrence(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 step = 18;
  void clear_step();
  ::PROTOBUF_NAMESPACE_ID::int64 step() const;
  void set_step(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 min_peak_bytes = 19;
  void clear_min_peak_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 min_peak_bytes() const;
  void set_min_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 min_residual_bytes = 20;
  void clear_min_residual_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 min_residual_bytes() const;
  void set_min_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 min_output_bytes = 21;
  void clear_min_output_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 min_output_bytes() const;
  void set_min_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 min_accelerator_micros = 22;
  void clear_min_accelerator_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 min_accelerator_micros() const;
  void set_min_accelerator_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 min_cpu_micros = 23;
  void clear_min_cpu_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 min_cpu_micros() const;
  void set_min_cpu_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool account_displayed_op_only = 13;
  void clear_account_displayed_op_only();
  bool account_displayed_op_only() const;
  void set_account_displayed_op_only(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.OptionsProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> account_type_regexes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> start_name_regexes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> trim_name_regexes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> show_name_regexes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> hide_name_regexes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> select_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr order_by_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr output_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dump_to_file_;
  ::PROTOBUF_NAMESPACE_ID::int64 max_depth_;
  ::PROTOBUF_NAMESPACE_ID::int64 min_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 min_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 min_params_;
  ::PROTOBUF_NAMESPACE_ID::int64 min_float_ops_;
  ::PROTOBUF_NAMESPACE_ID::int64 min_occurrence_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_;
  ::PROTOBUF_NAMESPACE_ID::int64 min_peak_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 min_residual_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 min_output_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 min_accelerator_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 min_cpu_micros_;
  bool account_displayed_op_only_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto;
};
// -------------------------------------------------------------------

class AdvisorOptionsProto_CheckersEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdvisorOptionsProto_CheckersEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdvisorOptionsProto_CheckersEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  AdvisorOptionsProto_CheckersEntry_DoNotUse();
  AdvisorOptionsProto_CheckersEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const AdvisorOptionsProto_CheckersEntry_DoNotUse& other);
  static const AdvisorOptionsProto_CheckersEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const AdvisorOptionsProto_CheckersEntry_DoNotUse*>(&_AdvisorOptionsProto_CheckersEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.AdvisorOptionsProto.CheckersEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto.file_level_metadata[1];
  }

  public:
};

// -------------------------------------------------------------------

class AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse();
  AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse& other);
  static const AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse*>(&_AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto.file_level_metadata[2];
  }

  public:
};

// -------------------------------------------------------------------

class AdvisorOptionsProto_CheckerOption :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption) */ {
 public:
  AdvisorOptionsProto_CheckerOption();
  virtual ~AdvisorOptionsProto_CheckerOption();

  AdvisorOptionsProto_CheckerOption(const AdvisorOptionsProto_CheckerOption& from);
  AdvisorOptionsProto_CheckerOption(AdvisorOptionsProto_CheckerOption&& from) noexcept
    : AdvisorOptionsProto_CheckerOption() {
    *this = ::std::move(from);
  }

  inline AdvisorOptionsProto_CheckerOption& operator=(const AdvisorOptionsProto_CheckerOption& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdvisorOptionsProto_CheckerOption& operator=(AdvisorOptionsProto_CheckerOption&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AdvisorOptionsProto_CheckerOption& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdvisorOptionsProto_CheckerOption* internal_default_instance() {
    return reinterpret_cast<const AdvisorOptionsProto_CheckerOption*>(
               &_AdvisorOptionsProto_CheckerOption_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(AdvisorOptionsProto_CheckerOption& a, AdvisorOptionsProto_CheckerOption& b) {
    a.Swap(&b);
  }
  inline void Swap(AdvisorOptionsProto_CheckerOption* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AdvisorOptionsProto_CheckerOption* New() const final {
    return CreateMaybeMessage<AdvisorOptionsProto_CheckerOption>(nullptr);
  }

  AdvisorOptionsProto_CheckerOption* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AdvisorOptionsProto_CheckerOption>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AdvisorOptionsProto_CheckerOption& from);
  void MergeFrom(const AdvisorOptionsProto_CheckerOption& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdvisorOptionsProto_CheckerOption* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kOptionsFieldNumber = 1,
  };
  // map<string, string> options = 1;
  int options_size() const;
  void clear_options();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      options() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_options();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > options_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto;
};
// -------------------------------------------------------------------

class AdvisorOptionsProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdvisorOptionsProto) */ {
 public:
  AdvisorOptionsProto();
  virtual ~AdvisorOptionsProto();

  AdvisorOptionsProto(const AdvisorOptionsProto& from);
  AdvisorOptionsProto(AdvisorOptionsProto&& from) noexcept
    : AdvisorOptionsProto() {
    *this = ::std::move(from);
  }

  inline AdvisorOptionsProto& operator=(const AdvisorOptionsProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdvisorOptionsProto& operator=(AdvisorOptionsProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AdvisorOptionsProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdvisorOptionsProto* internal_default_instance() {
    return reinterpret_cast<const AdvisorOptionsProto*>(
               &_AdvisorOptionsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(AdvisorOptionsProto& a, AdvisorOptionsProto& b) {
    a.Swap(&b);
  }
  inline void Swap(AdvisorOptionsProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AdvisorOptionsProto* New() const final {
    return CreateMaybeMessage<AdvisorOptionsProto>(nullptr);
  }

  AdvisorOptionsProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AdvisorOptionsProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AdvisorOptionsProto& from);
  void MergeFrom(const AdvisorOptionsProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdvisorOptionsProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.AdvisorOptionsProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef AdvisorOptionsProto_CheckerOption CheckerOption;

  // accessors -------------------------------------------------------

  enum : int {
    kCheckersFieldNumber = 1,
  };
  // map<string, .tensorflow.tfprof.AdvisorOptionsProto.CheckerOption> checkers = 1;
  int checkers_size() const;
  void clear_checkers();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >&
      checkers() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >*
      mutable_checkers();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdvisorOptionsProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      AdvisorOptionsProto_CheckersEntry_DoNotUse,
      std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > checkers_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// OptionsProto

// int64 max_depth = 1;
inline void OptionsProto::clear_max_depth() {
  max_depth_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::max_depth() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.max_depth)
  return max_depth_;
}
inline void OptionsProto::set_max_depth(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  max_depth_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.max_depth)
}

// int64 min_bytes = 2;
inline void OptionsProto::clear_min_bytes() {
  min_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::min_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_bytes)
  return min_bytes_;
}
inline void OptionsProto::set_min_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  min_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_bytes)
}

// int64 min_peak_bytes = 19;
inline void OptionsProto::clear_min_peak_bytes() {
  min_peak_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::min_peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_peak_bytes)
  return min_peak_bytes_;
}
inline void OptionsProto::set_min_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  min_peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_peak_bytes)
}

// int64 min_residual_bytes = 20;
inline void OptionsProto::clear_min_residual_bytes() {
  min_residual_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::min_residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_residual_bytes)
  return min_residual_bytes_;
}
inline void OptionsProto::set_min_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  min_residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_residual_bytes)
}

// int64 min_output_bytes = 21;
inline void OptionsProto::clear_min_output_bytes() {
  min_output_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::min_output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_output_bytes)
  return min_output_bytes_;
}
inline void OptionsProto::set_min_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  min_output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_output_bytes)
}

// int64 min_micros = 3;
inline void OptionsProto::clear_min_micros() {
  min_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::min_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_micros)
  return min_micros_;
}
inline void OptionsProto::set_min_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  min_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_micros)
}

// int64 min_accelerator_micros = 22;
inline void OptionsProto::clear_min_accelerator_micros() {
  min_accelerator_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::min_accelerator_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_accelerator_micros)
  return min_accelerator_micros_;
}
inline void OptionsProto::set_min_accelerator_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  min_accelerator_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_accelerator_micros)
}

// int64 min_cpu_micros = 23;
inline void OptionsProto::clear_min_cpu_micros() {
  min_cpu_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::min_cpu_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_cpu_micros)
  return min_cpu_micros_;
}
inline void OptionsProto::set_min_cpu_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  min_cpu_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_cpu_micros)
}

// int64 min_params = 4;
inline void OptionsProto::clear_min_params() {
  min_params_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::min_params() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_params)
  return min_params_;
}
inline void OptionsProto::set_min_params(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  min_params_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_params)
}

// int64 min_float_ops = 5;
inline void OptionsProto::clear_min_float_ops() {
  min_float_ops_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::min_float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_float_ops)
  return min_float_ops_;
}
inline void OptionsProto::set_min_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  min_float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_float_ops)
}

// int64 min_occurrence = 17;
inline void OptionsProto::clear_min_occurrence() {
  min_occurrence_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::min_occurrence() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_occurrence)
  return min_occurrence_;
}
inline void OptionsProto::set_min_occurrence(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  min_occurrence_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_occurrence)
}

// int64 step = 18;
inline void OptionsProto::clear_step() {
  step_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptionsProto::step() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.step)
  return step_;
}
inline void OptionsProto::set_step(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.step)
}

// string order_by = 7;
inline void OptionsProto::clear_order_by() {
  order_by_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& OptionsProto::order_by() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.order_by)
  return order_by_.GetNoArena();
}
inline void OptionsProto::set_order_by(const std::string& value) {
  
  order_by_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.order_by)
}
inline void OptionsProto::set_order_by(std::string&& value) {
  
  order_by_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.OptionsProto.order_by)
}
inline void OptionsProto::set_order_by(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  order_by_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.order_by)
}
inline void OptionsProto::set_order_by(const char* value, size_t size) {
  
  order_by_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.order_by)
}
inline std::string* OptionsProto::mutable_order_by() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.order_by)
  return order_by_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OptionsProto::release_order_by() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OptionsProto.order_by)
  
  return order_by_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OptionsProto::set_allocated_order_by(std::string* order_by) {
  if (order_by != nullptr) {
    
  } else {
    
  }
  order_by_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), order_by);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OptionsProto.order_by)
}

// repeated string account_type_regexes = 8;
inline int OptionsProto::account_type_regexes_size() const {
  return account_type_regexes_.size();
}
inline void OptionsProto::clear_account_type_regexes() {
  account_type_regexes_.Clear();
}
inline const std::string& OptionsProto::account_type_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return account_type_regexes_.Get(index);
}
inline std::string* OptionsProto::mutable_account_type_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return account_type_regexes_.Mutable(index);
}
inline void OptionsProto::set_account_type_regexes(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.account_type_regexes)
  account_type_regexes_.Mutable(index)->assign(value);
}
inline void OptionsProto::set_account_type_regexes(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.account_type_regexes)
  account_type_regexes_.Mutable(index)->assign(std::move(value));
}
inline void OptionsProto::set_account_type_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  account_type_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::set_account_type_regexes(int index, const char* value, size_t size) {
  account_type_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline std::string* OptionsProto::add_account_type_regexes() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return account_type_regexes_.Add();
}
inline void OptionsProto::add_account_type_regexes(const std::string& value) {
  account_type_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::add_account_type_regexes(std::string&& value) {
  account_type_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::add_account_type_regexes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  account_type_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::add_account_type_regexes(const char* value, size_t size) {
  account_type_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::account_type_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return account_type_regexes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_account_type_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return &account_type_regexes_;
}

// repeated string start_name_regexes = 9;
inline int OptionsProto::start_name_regexes_size() const {
  return start_name_regexes_.size();
}
inline void OptionsProto::clear_start_name_regexes() {
  start_name_regexes_.Clear();
}
inline const std::string& OptionsProto::start_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return start_name_regexes_.Get(index);
}
inline std::string* OptionsProto::mutable_start_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return start_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_start_name_regexes(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.start_name_regexes)
  start_name_regexes_.Mutable(index)->assign(value);
}
inline void OptionsProto::set_start_name_regexes(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.start_name_regexes)
  start_name_regexes_.Mutable(index)->assign(std::move(value));
}
inline void OptionsProto::set_start_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  start_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::set_start_name_regexes(int index, const char* value, size_t size) {
  start_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline std::string* OptionsProto::add_start_name_regexes() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return start_name_regexes_.Add();
}
inline void OptionsProto::add_start_name_regexes(const std::string& value) {
  start_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::add_start_name_regexes(std::string&& value) {
  start_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::add_start_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  start_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::add_start_name_regexes(const char* value, size_t size) {
  start_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::start_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return start_name_regexes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_start_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return &start_name_regexes_;
}

// repeated string trim_name_regexes = 10;
inline int OptionsProto::trim_name_regexes_size() const {
  return trim_name_regexes_.size();
}
inline void OptionsProto::clear_trim_name_regexes() {
  trim_name_regexes_.Clear();
}
inline const std::string& OptionsProto::trim_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return trim_name_regexes_.Get(index);
}
inline std::string* OptionsProto::mutable_trim_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return trim_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_trim_name_regexes(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  trim_name_regexes_.Mutable(index)->assign(value);
}
inline void OptionsProto::set_trim_name_regexes(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  trim_name_regexes_.Mutable(index)->assign(std::move(value));
}
inline void OptionsProto::set_trim_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  trim_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::set_trim_name_regexes(int index, const char* value, size_t size) {
  trim_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline std::string* OptionsProto::add_trim_name_regexes() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return trim_name_regexes_.Add();
}
inline void OptionsProto::add_trim_name_regexes(const std::string& value) {
  trim_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::add_trim_name_regexes(std::string&& value) {
  trim_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::add_trim_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  trim_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::add_trim_name_regexes(const char* value, size_t size) {
  trim_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::trim_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return trim_name_regexes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_trim_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return &trim_name_regexes_;
}

// repeated string show_name_regexes = 11;
inline int OptionsProto::show_name_regexes_size() const {
  return show_name_regexes_.size();
}
inline void OptionsProto::clear_show_name_regexes() {
  show_name_regexes_.Clear();
}
inline const std::string& OptionsProto::show_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return show_name_regexes_.Get(index);
}
inline std::string* OptionsProto::mutable_show_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return show_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_show_name_regexes(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.show_name_regexes)
  show_name_regexes_.Mutable(index)->assign(value);
}
inline void OptionsProto::set_show_name_regexes(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.show_name_regexes)
  show_name_regexes_.Mutable(index)->assign(std::move(value));
}
inline void OptionsProto::set_show_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  show_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::set_show_name_regexes(int index, const char* value, size_t size) {
  show_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline std::string* OptionsProto::add_show_name_regexes() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return show_name_regexes_.Add();
}
inline void OptionsProto::add_show_name_regexes(const std::string& value) {
  show_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::add_show_name_regexes(std::string&& value) {
  show_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::add_show_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  show_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::add_show_name_regexes(const char* value, size_t size) {
  show_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::show_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return show_name_regexes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_show_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return &show_name_regexes_;
}

// repeated string hide_name_regexes = 12;
inline int OptionsProto::hide_name_regexes_size() const {
  return hide_name_regexes_.size();
}
inline void OptionsProto::clear_hide_name_regexes() {
  hide_name_regexes_.Clear();
}
inline const std::string& OptionsProto::hide_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return hide_name_regexes_.Get(index);
}
inline std::string* OptionsProto::mutable_hide_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return hide_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_hide_name_regexes(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  hide_name_regexes_.Mutable(index)->assign(value);
}
inline void OptionsProto::set_hide_name_regexes(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  hide_name_regexes_.Mutable(index)->assign(std::move(value));
}
inline void OptionsProto::set_hide_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  hide_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::set_hide_name_regexes(int index, const char* value, size_t size) {
  hide_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline std::string* OptionsProto::add_hide_name_regexes() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return hide_name_regexes_.Add();
}
inline void OptionsProto::add_hide_name_regexes(const std::string& value) {
  hide_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::add_hide_name_regexes(std::string&& value) {
  hide_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::add_hide_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  hide_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::add_hide_name_regexes(const char* value, size_t size) {
  hide_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::hide_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return hide_name_regexes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_hide_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return &hide_name_regexes_;
}

// bool account_displayed_op_only = 13;
inline void OptionsProto::clear_account_displayed_op_only() {
  account_displayed_op_only_ = false;
}
inline bool OptionsProto::account_displayed_op_only() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.account_displayed_op_only)
  return account_displayed_op_only_;
}
inline void OptionsProto::set_account_displayed_op_only(bool value) {
  
  account_displayed_op_only_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.account_displayed_op_only)
}

// repeated string select = 14;
inline int OptionsProto::select_size() const {
  return select_.size();
}
inline void OptionsProto::clear_select() {
  select_.Clear();
}
inline const std::string& OptionsProto::select(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.select)
  return select_.Get(index);
}
inline std::string* OptionsProto::mutable_select(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.select)
  return select_.Mutable(index);
}
inline void OptionsProto::set_select(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.select)
  select_.Mutable(index)->assign(value);
}
inline void OptionsProto::set_select(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.select)
  select_.Mutable(index)->assign(std::move(value));
}
inline void OptionsProto::set_select(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  select_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::set_select(int index, const char* value, size_t size) {
  select_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.select)
}
inline std::string* OptionsProto::add_select() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.select)
  return select_.Add();
}
inline void OptionsProto::add_select(const std::string& value) {
  select_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::add_select(std::string&& value) {
  select_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::add_select(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  select_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::add_select(const char* value, size_t size) {
  select_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.select)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::select() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.select)
  return select_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_select() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.select)
  return &select_;
}

// string output = 15;
inline void OptionsProto::clear_output() {
  output_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& OptionsProto::output() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.output)
  return output_.GetNoArena();
}
inline void OptionsProto::set_output(const std::string& value) {
  
  output_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.output)
}
inline void OptionsProto::set_output(std::string&& value) {
  
  output_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.OptionsProto.output)
}
inline void OptionsProto::set_output(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  output_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.output)
}
inline void OptionsProto::set_output(const char* value, size_t size) {
  
  output_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.output)
}
inline std::string* OptionsProto::mutable_output() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.output)
  return output_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OptionsProto::release_output() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OptionsProto.output)
  
  return output_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OptionsProto::set_allocated_output(std::string* output) {
  if (output != nullptr) {
    
  } else {
    
  }
  output_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), output);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OptionsProto.output)
}

// string dump_to_file = 16;
inline void OptionsProto::clear_dump_to_file() {
  dump_to_file_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& OptionsProto::dump_to_file() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.dump_to_file)
  return dump_to_file_.GetNoArena();
}
inline void OptionsProto::set_dump_to_file(const std::string& value) {
  
  dump_to_file_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.dump_to_file)
}
inline void OptionsProto::set_dump_to_file(std::string&& value) {
  
  dump_to_file_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.OptionsProto.dump_to_file)
}
inline void OptionsProto::set_dump_to_file(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  dump_to_file_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.dump_to_file)
}
inline void OptionsProto::set_dump_to_file(const char* value, size_t size) {
  
  dump_to_file_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.dump_to_file)
}
inline std::string* OptionsProto::mutable_dump_to_file() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.dump_to_file)
  return dump_to_file_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OptionsProto::release_dump_to_file() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OptionsProto.dump_to_file)
  
  return dump_to_file_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OptionsProto::set_allocated_dump_to_file(std::string* dump_to_file) {
  if (dump_to_file != nullptr) {
    
  } else {
    
  }
  dump_to_file_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), dump_to_file);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OptionsProto.dump_to_file)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// AdvisorOptionsProto_CheckerOption

// map<string, string> options = 1;
inline int AdvisorOptionsProto_CheckerOption::options_size() const {
  return options_.size();
}
inline void AdvisorOptionsProto_CheckerOption::clear_options() {
  options_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
AdvisorOptionsProto_CheckerOption::options() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.options)
  return options_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
AdvisorOptionsProto_CheckerOption::mutable_options() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.options)
  return options_.MutableMap();
}

// -------------------------------------------------------------------

// AdvisorOptionsProto

// map<string, .tensorflow.tfprof.AdvisorOptionsProto.CheckerOption> checkers = 1;
inline int AdvisorOptionsProto::checkers_size() const {
  return checkers_.size();
}
inline void AdvisorOptionsProto::clear_checkers() {
  checkers_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >&
AdvisorOptionsProto::checkers() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.AdvisorOptionsProto.checkers)
  return checkers_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >*
AdvisorOptionsProto::mutable_checkers() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.AdvisorOptionsProto.checkers)
  return checkers_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto
