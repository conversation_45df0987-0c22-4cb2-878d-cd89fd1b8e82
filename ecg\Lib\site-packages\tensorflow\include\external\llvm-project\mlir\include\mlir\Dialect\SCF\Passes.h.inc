/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// SCFBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFBufferizeBase : public ::mlir::FunctionPass {
public:
  using Base = SCFBufferizeBase;

  SCFBufferizeBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  SCFBufferizeBase(const SCFBufferizeBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "scf-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the scf dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFBufferize");
  }
  ::llvm::StringRef getName() const override { return "SCFBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFForLoopRangeFolding
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFForLoopRangeFoldingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopRangeFoldingBase;

  SCFForLoopRangeFoldingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopRangeFoldingBase(const SCFForLoopRangeFoldingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("for-loop-range-folding");
  }
  ::llvm::StringRef getArgument() const override { return "for-loop-range-folding"; }

  ::llvm::StringRef getDescription() const override { return "Fold add/mul ops into loop range"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopRangeFolding");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopRangeFolding"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFForLoopSpecialization
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFForLoopSpecializationBase : public ::mlir::FunctionPass {
public:
  using Base = SCFForLoopSpecializationBase;

  SCFForLoopSpecializationBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopSpecializationBase(const SCFForLoopSpecializationBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("for-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "for-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize `for` loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFParallelLoopFusion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFParallelLoopFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopFusionBase;

  SCFParallelLoopFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopFusionBase(const SCFParallelLoopFusionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("parallel-loop-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "parallel-loop-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse adjacent parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopFusion");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFParallelLoopSpecialization
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFParallelLoopSpecializationBase : public ::mlir::FunctionPass {
public:
  using Base = SCFParallelLoopSpecializationBase;

  SCFParallelLoopSpecializationBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopSpecializationBase(const SCFParallelLoopSpecializationBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("parallel-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "parallel-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize parallel loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFParallelLoopTiling
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFParallelLoopTilingBase : public ::mlir::FunctionPass {
public:
  using Base = SCFParallelLoopTilingBase;

  SCFParallelLoopTilingBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopTilingBase(const SCFParallelLoopTilingBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("parallel-loop-tiling");
  }
  ::llvm::StringRef getArgument() const override { return "parallel-loop-tiling"; }

  ::llvm::StringRef getDescription() const override { return "Tile parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopTiling");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  }

protected:
  ::mlir::Pass::ListOption<int64_t> tileSizes{*this, "parallel-loop-tile-sizes", ::llvm::cl::desc("Factors to tile parallel loops by"), llvm::cl::ZeroOrMore, llvm::cl::MiscFlags::CommaSeparated};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// SCFBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerSCFBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCFBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopRangeFolding Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopRangeFoldingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopRangeFoldingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopSpecialization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopSpecializationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopSpecializationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopFusion Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopSpecialization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopSpecializationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopSpecializationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopTiling Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopTilingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopTilingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCF Registration
//===----------------------------------------------------------------------===//

inline void registerSCFPasses() {
  registerSCFBufferizePass();
  registerSCFForLoopRangeFoldingPass();
  registerSCFForLoopSpecializationPass();
  registerSCFParallelLoopFusionPass();
  registerSCFParallelLoopSpecializationPass();
  registerSCFParallelLoopTilingPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
