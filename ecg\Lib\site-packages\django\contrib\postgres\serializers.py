from django.db.migrations.serializer import BaseSerializer


class RangeSerializer(BaseSerializer):
    def serialize(self):
        module = self.value.__class__.__module__
        # Ranges are implemented in psycopg2._range but the public import
        # location is psycopg2.extras.
        module = 'psycopg2.extras' if module == 'psycopg2._range' else module
        return '%s.%r' % (module, self.value), {'import %s' % module}
