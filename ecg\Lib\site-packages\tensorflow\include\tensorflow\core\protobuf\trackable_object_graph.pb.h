// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/trackable_object_graph.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
namespace tensorflow {
class TrackableObjectGraph;
class TrackableObjectGraphDefaultTypeInternal;
extern TrackableObjectGraphDefaultTypeInternal _TrackableObjectGraph_default_instance_;
class TrackableObjectGraph_TrackableObject;
class TrackableObjectGraph_TrackableObjectDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObjectDefaultTypeInternal _TrackableObjectGraph_TrackableObject_default_instance_;
class TrackableObjectGraph_TrackableObject_ObjectReference;
class TrackableObjectGraph_TrackableObject_ObjectReferenceDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObject_ObjectReferenceDefaultTypeInternal _TrackableObjectGraph_TrackableObject_ObjectReference_default_instance_;
class TrackableObjectGraph_TrackableObject_SerializedTensor;
class TrackableObjectGraph_TrackableObject_SerializedTensorDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObject_SerializedTensorDefaultTypeInternal _TrackableObjectGraph_TrackableObject_SerializedTensor_default_instance_;
class TrackableObjectGraph_TrackableObject_SlotVariableReference;
class TrackableObjectGraph_TrackableObject_SlotVariableReferenceDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObject_SlotVariableReferenceDefaultTypeInternal _TrackableObjectGraph_TrackableObject_SlotVariableReference_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::TrackableObjectGraph* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class TrackableObjectGraph_TrackableObject_ObjectReference :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference) */ {
 public:
  TrackableObjectGraph_TrackableObject_ObjectReference();
  virtual ~TrackableObjectGraph_TrackableObject_ObjectReference();

  TrackableObjectGraph_TrackableObject_ObjectReference(const TrackableObjectGraph_TrackableObject_ObjectReference& from);
  TrackableObjectGraph_TrackableObject_ObjectReference(TrackableObjectGraph_TrackableObject_ObjectReference&& from) noexcept
    : TrackableObjectGraph_TrackableObject_ObjectReference() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject_ObjectReference& operator=(const TrackableObjectGraph_TrackableObject_ObjectReference& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackableObjectGraph_TrackableObject_ObjectReference& operator=(TrackableObjectGraph_TrackableObject_ObjectReference&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TrackableObjectGraph_TrackableObject_ObjectReference& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrackableObjectGraph_TrackableObject_ObjectReference* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject_ObjectReference*>(
               &_TrackableObjectGraph_TrackableObject_ObjectReference_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TrackableObjectGraph_TrackableObject_ObjectReference& a, TrackableObjectGraph_TrackableObject_ObjectReference& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackableObjectGraph_TrackableObject_ObjectReference* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_ObjectReference* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TrackableObjectGraph_TrackableObject_ObjectReference* New() const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_ObjectReference>(nullptr);
  }

  TrackableObjectGraph_TrackableObject_ObjectReference* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_ObjectReference>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TrackableObjectGraph_TrackableObject_ObjectReference& from);
  void MergeFrom(const TrackableObjectGraph_TrackableObject_ObjectReference& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject_ObjectReference* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference";
  }
  protected:
  explicit TrackableObjectGraph_TrackableObject_ObjectReference(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocalNameFieldNumber = 2,
    kNodeIdFieldNumber = 1,
  };
  // string local_name = 2;
  void clear_local_name();
  const std::string& local_name() const;
  void set_local_name(const std::string& value);
  void set_local_name(std::string&& value);
  void set_local_name(const char* value);
  void set_local_name(const char* value, size_t size);
  std::string* mutable_local_name();
  std::string* release_local_name();
  void set_allocated_local_name(std::string* local_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_local_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_local_name(
      std::string* local_name);

  // int32 node_id = 1;
  void clear_node_id();
  ::PROTOBUF_NAMESPACE_ID::int32 node_id() const;
  void set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr local_name_;
  ::PROTOBUF_NAMESPACE_ID::int32 node_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TrackableObjectGraph_TrackableObject_SerializedTensor :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor) */ {
 public:
  TrackableObjectGraph_TrackableObject_SerializedTensor();
  virtual ~TrackableObjectGraph_TrackableObject_SerializedTensor();

  TrackableObjectGraph_TrackableObject_SerializedTensor(const TrackableObjectGraph_TrackableObject_SerializedTensor& from);
  TrackableObjectGraph_TrackableObject_SerializedTensor(TrackableObjectGraph_TrackableObject_SerializedTensor&& from) noexcept
    : TrackableObjectGraph_TrackableObject_SerializedTensor() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject_SerializedTensor& operator=(const TrackableObjectGraph_TrackableObject_SerializedTensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackableObjectGraph_TrackableObject_SerializedTensor& operator=(TrackableObjectGraph_TrackableObject_SerializedTensor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TrackableObjectGraph_TrackableObject_SerializedTensor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrackableObjectGraph_TrackableObject_SerializedTensor* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject_SerializedTensor*>(
               &_TrackableObjectGraph_TrackableObject_SerializedTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TrackableObjectGraph_TrackableObject_SerializedTensor& a, TrackableObjectGraph_TrackableObject_SerializedTensor& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackableObjectGraph_TrackableObject_SerializedTensor* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_SerializedTensor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TrackableObjectGraph_TrackableObject_SerializedTensor* New() const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_SerializedTensor>(nullptr);
  }

  TrackableObjectGraph_TrackableObject_SerializedTensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_SerializedTensor>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TrackableObjectGraph_TrackableObject_SerializedTensor& from);
  void MergeFrom(const TrackableObjectGraph_TrackableObject_SerializedTensor& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject_SerializedTensor* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor";
  }
  protected:
  explicit TrackableObjectGraph_TrackableObject_SerializedTensor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kFullNameFieldNumber = 2,
    kCheckpointKeyFieldNumber = 3,
    kOptionalRestoreFieldNumber = 4,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string full_name = 2;
  void clear_full_name();
  const std::string& full_name() const;
  void set_full_name(const std::string& value);
  void set_full_name(std::string&& value);
  void set_full_name(const char* value);
  void set_full_name(const char* value, size_t size);
  std::string* mutable_full_name();
  std::string* release_full_name();
  void set_allocated_full_name(std::string* full_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_full_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_full_name(
      std::string* full_name);

  // string checkpoint_key = 3;
  void clear_checkpoint_key();
  const std::string& checkpoint_key() const;
  void set_checkpoint_key(const std::string& value);
  void set_checkpoint_key(std::string&& value);
  void set_checkpoint_key(const char* value);
  void set_checkpoint_key(const char* value, size_t size);
  std::string* mutable_checkpoint_key();
  std::string* release_checkpoint_key();
  void set_allocated_checkpoint_key(std::string* checkpoint_key);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_checkpoint_key();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_checkpoint_key(
      std::string* checkpoint_key);

  // bool optional_restore = 4;
  void clear_optional_restore();
  bool optional_restore() const;
  void set_optional_restore(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr full_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr checkpoint_key_;
  bool optional_restore_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TrackableObjectGraph_TrackableObject_SlotVariableReference :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference) */ {
 public:
  TrackableObjectGraph_TrackableObject_SlotVariableReference();
  virtual ~TrackableObjectGraph_TrackableObject_SlotVariableReference();

  TrackableObjectGraph_TrackableObject_SlotVariableReference(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from);
  TrackableObjectGraph_TrackableObject_SlotVariableReference(TrackableObjectGraph_TrackableObject_SlotVariableReference&& from) noexcept
    : TrackableObjectGraph_TrackableObject_SlotVariableReference() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject_SlotVariableReference& operator=(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackableObjectGraph_TrackableObject_SlotVariableReference& operator=(TrackableObjectGraph_TrackableObject_SlotVariableReference&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TrackableObjectGraph_TrackableObject_SlotVariableReference& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrackableObjectGraph_TrackableObject_SlotVariableReference* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject_SlotVariableReference*>(
               &_TrackableObjectGraph_TrackableObject_SlotVariableReference_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TrackableObjectGraph_TrackableObject_SlotVariableReference& a, TrackableObjectGraph_TrackableObject_SlotVariableReference& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TrackableObjectGraph_TrackableObject_SlotVariableReference* New() const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_SlotVariableReference>(nullptr);
  }

  TrackableObjectGraph_TrackableObject_SlotVariableReference* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_SlotVariableReference>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from);
  void MergeFrom(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference";
  }
  protected:
  explicit TrackableObjectGraph_TrackableObject_SlotVariableReference(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSlotNameFieldNumber = 2,
    kOriginalVariableNodeIdFieldNumber = 1,
    kSlotVariableNodeIdFieldNumber = 3,
  };
  // string slot_name = 2;
  void clear_slot_name();
  const std::string& slot_name() const;
  void set_slot_name(const std::string& value);
  void set_slot_name(std::string&& value);
  void set_slot_name(const char* value);
  void set_slot_name(const char* value, size_t size);
  std::string* mutable_slot_name();
  std::string* release_slot_name();
  void set_allocated_slot_name(std::string* slot_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_slot_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_slot_name(
      std::string* slot_name);

  // int32 original_variable_node_id = 1;
  void clear_original_variable_node_id();
  ::PROTOBUF_NAMESPACE_ID::int32 original_variable_node_id() const;
  void set_original_variable_node_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 slot_variable_node_id = 3;
  void clear_slot_variable_node_id();
  ::PROTOBUF_NAMESPACE_ID::int32 slot_variable_node_id() const;
  void set_slot_variable_node_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr slot_name_;
  ::PROTOBUF_NAMESPACE_ID::int32 original_variable_node_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 slot_variable_node_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TrackableObjectGraph_TrackableObject :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject) */ {
 public:
  TrackableObjectGraph_TrackableObject();
  virtual ~TrackableObjectGraph_TrackableObject();

  TrackableObjectGraph_TrackableObject(const TrackableObjectGraph_TrackableObject& from);
  TrackableObjectGraph_TrackableObject(TrackableObjectGraph_TrackableObject&& from) noexcept
    : TrackableObjectGraph_TrackableObject() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject& operator=(const TrackableObjectGraph_TrackableObject& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackableObjectGraph_TrackableObject& operator=(TrackableObjectGraph_TrackableObject&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TrackableObjectGraph_TrackableObject& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrackableObjectGraph_TrackableObject* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject*>(
               &_TrackableObjectGraph_TrackableObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TrackableObjectGraph_TrackableObject& a, TrackableObjectGraph_TrackableObject& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackableObjectGraph_TrackableObject* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TrackableObjectGraph_TrackableObject* New() const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject>(nullptr);
  }

  TrackableObjectGraph_TrackableObject* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TrackableObjectGraph_TrackableObject& from);
  void MergeFrom(const TrackableObjectGraph_TrackableObject& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TrackableObjectGraph.TrackableObject";
  }
  protected:
  explicit TrackableObjectGraph_TrackableObject(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TrackableObjectGraph_TrackableObject_ObjectReference ObjectReference;
  typedef TrackableObjectGraph_TrackableObject_SerializedTensor SerializedTensor;
  typedef TrackableObjectGraph_TrackableObject_SlotVariableReference SlotVariableReference;

  // accessors -------------------------------------------------------

  enum : int {
    kChildrenFieldNumber = 1,
    kAttributesFieldNumber = 2,
    kSlotVariablesFieldNumber = 3,
  };
  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  int children_size() const;
  void clear_children();
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* mutable_children(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
      mutable_children();
  const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& children(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* add_children();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
      children() const;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor attributes = 2;
  int attributes_size() const;
  void clear_attributes();
  ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* mutable_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >*
      mutable_attributes();
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor& attributes(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* add_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >&
      attributes() const;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  int slot_variables_size() const;
  void clear_slot_variables();
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* mutable_slot_variables(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
      mutable_slot_variables();
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& slot_variables(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* add_slot_variables();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
      slot_variables() const;

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference > children_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor > attributes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference > slot_variables_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TrackableObjectGraph :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph) */ {
 public:
  TrackableObjectGraph();
  virtual ~TrackableObjectGraph();

  TrackableObjectGraph(const TrackableObjectGraph& from);
  TrackableObjectGraph(TrackableObjectGraph&& from) noexcept
    : TrackableObjectGraph() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph& operator=(const TrackableObjectGraph& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackableObjectGraph& operator=(TrackableObjectGraph&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TrackableObjectGraph& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrackableObjectGraph* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph*>(
               &_TrackableObjectGraph_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TrackableObjectGraph& a, TrackableObjectGraph& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackableObjectGraph* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackableObjectGraph* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TrackableObjectGraph* New() const final {
    return CreateMaybeMessage<TrackableObjectGraph>(nullptr);
  }

  TrackableObjectGraph* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TrackableObjectGraph>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TrackableObjectGraph& from);
  void MergeFrom(const TrackableObjectGraph& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TrackableObjectGraph";
  }
  protected:
  explicit TrackableObjectGraph(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TrackableObjectGraph_TrackableObject TrackableObject;

  // accessors -------------------------------------------------------

  enum : int {
    kNodesFieldNumber = 1,
  };
  // repeated .tensorflow.TrackableObjectGraph.TrackableObject nodes = 1;
  int nodes_size() const;
  void clear_nodes();
  ::tensorflow::TrackableObjectGraph_TrackableObject* mutable_nodes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >*
      mutable_nodes();
  const ::tensorflow::TrackableObjectGraph_TrackableObject& nodes(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject* add_nodes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >&
      nodes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject > nodes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TrackableObjectGraph_TrackableObject_ObjectReference

// int32 node_id = 1;
inline void TrackableObjectGraph_TrackableObject_ObjectReference::clear_node_id() {
  node_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TrackableObjectGraph_TrackableObject_ObjectReference::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.node_id)
  return node_id_;
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.node_id)
}

// string local_name = 2;
inline void TrackableObjectGraph_TrackableObject_ObjectReference::clear_local_name() {
  local_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TrackableObjectGraph_TrackableObject_ObjectReference::local_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  return local_name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_local_name(const std::string& value) {
  
  local_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_local_name(std::string&& value) {
  
  local_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_local_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  local_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_local_name(const char* value,
    size_t size) {
  
  local_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
inline std::string* TrackableObjectGraph_TrackableObject_ObjectReference::mutable_local_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  return local_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TrackableObjectGraph_TrackableObject_ObjectReference::release_local_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  
  return local_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_allocated_local_name(std::string* local_name) {
  if (local_name != nullptr) {
    
  } else {
    
  }
  local_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), local_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
inline std::string* TrackableObjectGraph_TrackableObject_ObjectReference::unsafe_arena_release_local_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return local_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::unsafe_arena_set_allocated_local_name(
    std::string* local_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (local_name != nullptr) {
    
  } else {
    
  }
  local_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      local_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}

// -------------------------------------------------------------------

// TrackableObjectGraph_TrackableObject_SerializedTensor

// string name = 1;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  return name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}

// string full_name = 2;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_full_name() {
  full_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::full_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  return full_name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_full_name(const std::string& value) {
  
  full_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_full_name(std::string&& value) {
  
  full_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_full_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  full_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_full_name(const char* value,
    size_t size) {
  
  full_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::mutable_full_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  return full_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::release_full_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  
  return full_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_allocated_full_name(std::string* full_name) {
  if (full_name != nullptr) {
    
  } else {
    
  }
  full_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), full_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_release_full_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return full_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_set_allocated_full_name(
    std::string* full_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (full_name != nullptr) {
    
  } else {
    
  }
  full_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      full_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}

// string checkpoint_key = 3;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_checkpoint_key() {
  checkpoint_key_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::checkpoint_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  return checkpoint_key_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_checkpoint_key(const std::string& value) {
  
  checkpoint_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_checkpoint_key(std::string&& value) {
  
  checkpoint_key_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_checkpoint_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  checkpoint_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_checkpoint_key(const char* value,
    size_t size) {
  
  checkpoint_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::mutable_checkpoint_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  return checkpoint_key_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::release_checkpoint_key() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  
  return checkpoint_key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_allocated_checkpoint_key(std::string* checkpoint_key) {
  if (checkpoint_key != nullptr) {
    
  } else {
    
  }
  checkpoint_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), checkpoint_key,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_release_checkpoint_key() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return checkpoint_key_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_set_allocated_checkpoint_key(
    std::string* checkpoint_key) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (checkpoint_key != nullptr) {
    
  } else {
    
  }
  checkpoint_key_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      checkpoint_key, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}

// bool optional_restore = 4;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_optional_restore() {
  optional_restore_ = false;
}
inline bool TrackableObjectGraph_TrackableObject_SerializedTensor::optional_restore() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.optional_restore)
  return optional_restore_;
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_optional_restore(bool value) {
  
  optional_restore_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.optional_restore)
}

// -------------------------------------------------------------------

// TrackableObjectGraph_TrackableObject_SlotVariableReference

// int32 original_variable_node_id = 1;
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::clear_original_variable_node_id() {
  original_variable_node_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TrackableObjectGraph_TrackableObject_SlotVariableReference::original_variable_node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.original_variable_node_id)
  return original_variable_node_id_;
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_original_variable_node_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  original_variable_node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.original_variable_node_id)
}

// string slot_name = 2;
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::clear_slot_name() {
  slot_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TrackableObjectGraph_TrackableObject_SlotVariableReference::slot_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  return slot_name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_name(const std::string& value) {
  
  slot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_name(std::string&& value) {
  
  slot_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  slot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_name(const char* value,
    size_t size) {
  
  slot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
inline std::string* TrackableObjectGraph_TrackableObject_SlotVariableReference::mutable_slot_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  return slot_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TrackableObjectGraph_TrackableObject_SlotVariableReference::release_slot_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  
  return slot_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_allocated_slot_name(std::string* slot_name) {
  if (slot_name != nullptr) {
    
  } else {
    
  }
  slot_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), slot_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
inline std::string* TrackableObjectGraph_TrackableObject_SlotVariableReference::unsafe_arena_release_slot_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return slot_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::unsafe_arena_set_allocated_slot_name(
    std::string* slot_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (slot_name != nullptr) {
    
  } else {
    
  }
  slot_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      slot_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}

// int32 slot_variable_node_id = 3;
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::clear_slot_variable_node_id() {
  slot_variable_node_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TrackableObjectGraph_TrackableObject_SlotVariableReference::slot_variable_node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_variable_node_id)
  return slot_variable_node_id_;
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_variable_node_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  slot_variable_node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_variable_node_id)
}

// -------------------------------------------------------------------

// TrackableObjectGraph_TrackableObject

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
inline int TrackableObjectGraph_TrackableObject::children_size() const {
  return children_.size();
}
inline void TrackableObjectGraph_TrackableObject::clear_children() {
  children_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* TrackableObjectGraph_TrackableObject::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return children_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
TrackableObjectGraph_TrackableObject::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return &children_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& TrackableObjectGraph_TrackableObject::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return children_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* TrackableObjectGraph_TrackableObject::add_children() {
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return children_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
TrackableObjectGraph_TrackableObject::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return children_;
}

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor attributes = 2;
inline int TrackableObjectGraph_TrackableObject::attributes_size() const {
  return attributes_.size();
}
inline void TrackableObjectGraph_TrackableObject::clear_attributes() {
  attributes_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* TrackableObjectGraph_TrackableObject::mutable_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >*
TrackableObjectGraph_TrackableObject::mutable_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return &attributes_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor& TrackableObjectGraph_TrackableObject::attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return attributes_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* TrackableObjectGraph_TrackableObject::add_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return attributes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >&
TrackableObjectGraph_TrackableObject::attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return attributes_;
}

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
inline int TrackableObjectGraph_TrackableObject::slot_variables_size() const {
  return slot_variables_.size();
}
inline void TrackableObjectGraph_TrackableObject::clear_slot_variables() {
  slot_variables_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* TrackableObjectGraph_TrackableObject::mutable_slot_variables(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return slot_variables_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
TrackableObjectGraph_TrackableObject::mutable_slot_variables() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return &slot_variables_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& TrackableObjectGraph_TrackableObject::slot_variables(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return slot_variables_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* TrackableObjectGraph_TrackableObject::add_slot_variables() {
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return slot_variables_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
TrackableObjectGraph_TrackableObject::slot_variables() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return slot_variables_;
}

// -------------------------------------------------------------------

// TrackableObjectGraph

// repeated .tensorflow.TrackableObjectGraph.TrackableObject nodes = 1;
inline int TrackableObjectGraph::nodes_size() const {
  return nodes_.size();
}
inline void TrackableObjectGraph::clear_nodes() {
  nodes_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject* TrackableObjectGraph::mutable_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.nodes)
  return nodes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >*
TrackableObjectGraph::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.nodes)
  return &nodes_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject& TrackableObjectGraph::nodes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.nodes)
  return nodes_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject* TrackableObjectGraph::add_nodes() {
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.nodes)
  return nodes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >&
TrackableObjectGraph::nodes() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.nodes)
  return nodes_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
