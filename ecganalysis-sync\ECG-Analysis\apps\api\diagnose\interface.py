import ast
import json
import traceback

import numpy as np
from django.utils.decorators import method_decorator
from django.views import View

import apps.analysis.arrhythmia_diagnosis.diagnosis as arrhythmia_diagnosis_diagnosis
import apps.analysis.cad_cardiomyopathy.diagnosis as cad_cardiomyopathy_diagnosis
import apps.analysis.health_metrics.diagnosis as health_metrics_diagnosis
import apps.analysis.pqrstc.diagnosis as pqrstc_diagnosis
from apps.analysis import whitelist
from apps.analysis.common.custom_exception import BiosppyEcgError
from apps.analysis.common.data_filter import filter_negative_values
from apps.analysis.diagnosis_filter.filter import apply_rules
from apps.analysis.ecg_age import diagnosis as ecg_age_diagnosis
from apps.api.diagnose.business import save_api_log, get_diagnosis_details
from apps.models.analysis_models import AnalysisEntity, ArrhythmiaDiagnosisEntity, HealthMetricsEntity, MotionInfoEntity
from apps.signal_analysis.available import get_available_signals
from apps.signal_analysis.waveform import get_waveform
from apps.utils.decorator import authentication
from apps.utils.get_response import GetResponse
from apps.utils.logger_helper import Logger
from apps.analysis.gravity.business import process as gravity_process
from apps.signal_analysis.available import identify_noise_type  # 导入identify_noise_type函数


@method_decorator(authentication, name="dispatch")
class ArrhythmiaView(View):
    """
    心率失常诊断
    """

    def post(self, request, *args, **kwargs):
        """
        POST请求接口
        :param request: 请求对象
        :return: 诊断结果
        """
        custom_id = kwargs.get('custom_id')  # 默认给一个整数ID，避免错误
        ecg_data = None
        union_id = None

        param_list = ['signal', 'fs', 'adc_gain', 'adc_zero', 'union_id', 'ecg_age_key', 'health_metrics']

        try:
            data = json.loads(request.body)

            # 验证参数是否存在
            missing_params = [param for param in param_list if param not in data]

            if len(missing_params) > 0:
                return GetResponse.get_response(code=6, data=f"所需参数 {','.join(missing_params)} 不存在")

            ecg_data_str = data['signal']  # ecg信号

            if isinstance(ecg_data_str, list):
                ecg_data = np.array(data['signal'])
            elif ecg_data_str.find('[') != -1 and ecg_data_str.find(']') != -1:
                ecg_data = np.array(ast.literal_eval(f"{ecg_data_str}"))
            else:
                ecg_data = np.array(ast.literal_eval(f"[{ecg_data_str}]"))  # 将心电信号转为nparray

            sampling_rate = data['fs']  # 采样率
            gain = data['adc_gain']  # 增益
            zero = data['adc_zero']  # 零点（基线）
            union_id = data['union_id']  # 用户ID
            ecg_age_key = data['ecg_age_key']  # 心脏年龄 0-不需要计算 1-需要计算
            health_metrics = data['health_metrics']  # 情绪指数 0-不需要计算 1-需要计算

            sampling_rate = int(sampling_rate)

            if len(ecg_data) < sampling_rate * 10:
                return GetResponse.get_response(code=6, data='心电信号不能小于10秒')

            if ecg_data is None or sampling_rate is None or gain is None or zero is None or gain <= 0:
                return GetResponse.get_response(code=5)

            if not isinstance(sampling_rate, int):
                return GetResponse.get_response(code=6, data='采样率请传递整数')

            ecg_data = (ecg_data - zero) / gain  # 计算实际电压（检测电压-基线）/ 增益

            # 分析实体对象
            analysis_entity = AnalysisEntity()

            # 获取可用信号
            signal_quality, normal_time_period = get_available_signals(ecg_data, sampling_rate)
            analysis_entity.SignalQuantity = signal_quality  # 信号质量 1-正常,-1-噪声
            signal_time_period = normal_time_period[0]  # 默认取第一个时间段
            analysis_entity.SignalTimePeriod = f'{signal_time_period[0]}-{signal_time_period[1]}'

            # 数据切割，只处理有效信号段
            ecg_data_processed = ecg_data[signal_time_period[0] * sampling_rate: signal_time_period[1] * sampling_rate]

            waveform_info = get_waveform(ecg_data_processed, sampling_rate)  # 波形分析
            if waveform_info is None:
                return GetResponse.get_response(code=7)

            # 设置白名单
            whitelist_arrhythmia_diagnosis, whitelist_ecg_age = None, None

            # 非噪音读取白名单信息
            if analysis_entity.SignalQuantity != -1:
                whitelist_arrhythmia_diagnosis, whitelist_ecg_age = whitelist.process(union_id)

            if ecg_age_key == 1:
                # 如果白名单中设置了年龄优先使用白名单年龄
                if whitelist_ecg_age:
                    analysis_entity.ECGAge = whitelist_ecg_age
                else:
                    analysis_entity.ECGAge = ecg_age_diagnosis.process(ecg_data_processed, sampling_rate)  # 心脏年龄

            analysis_entity.HeartFailureRisk = 0  # 心衰风险（0-1）
            analysis_entity.VentricularFibrillationRisk = 0  # 室颤风险（0-1）
            analysis_entity.SyncopeRisk = 0  # 晕厥风险（0-1）
            analysis_entity.SleepStage = 0  # 睡眠阶段 取值范围为（0，1，2，3，4），对应睡眠阶段（Wake，N1， N2， N3, REM）
            analysis_entity.OSARisk = 0  # 阻塞性睡眠呼吸暂停风险，取值范围为（0-1）, -1代表时长小于两分钟
            analysis_entity.RespiratoryRate = 0  # 呼吸次数/min 取值范围[10-25]

            pqrstc = pqrstc_diagnosis.process(waveform_info)  # ECG信号指标

            # 修改条件：对非噪声信号(0和1)执行完整诊断，仅对噪声信号(-1)执行基础诊断
            if analysis_entity.SignalQuantity != -1:  # 正常信号或异常信号处理
                if whitelist_arrhythmia_diagnosis:
                    analysis_entity.ArrhythmiaDiagnosis = whitelist_arrhythmia_diagnosis
                else:
                    analysis_entity.ArrhythmiaDiagnosis = arrhythmia_diagnosis_diagnosis.process(ecg_data_processed,
                                                                                                 sampling_rate,
                                                                                                 waveform_info)  # 心率失常诊断
            else:  # 噪声信号处理 (-1)
                # 检查是否为平直线+高频振荡噪声
                if hasattr(get_available_signals, 'noise_diagnosis'):
                    # 如果 noise_diagnosis 属性存在，说明已在 available.py 中确认为平直线+高频振荡噪声
                    return GetResponse.get_response(code=8)

                # 只有在不是平直线+高频振荡噪声的情况下才进行心律诊断
                new_diagnosis = ArrhythmiaDiagnosisEntity()
                # 根据心率设置不同的心律诊断
                hr = pqrstc.HR if pqrstc and hasattr(pqrstc, 'HR') else 75  # 默认值75
                if hr > 100:
                    # 窦性心动过速
                    new_diagnosis.SNT = 1
                elif hr < 60:
                    # 窦性心动过缓
                    new_diagnosis.SNB = 1
                else:
                    # 窦性心律（正常）
                    new_diagnosis.SN = 1
                analysis_entity.ArrhythmiaDiagnosis = new_diagnosis

            analysis_entity.CADCardiomyopathy = cad_cardiomyopathy_diagnosis.process(waveform_info)  # 心肌病冠心病诊断

            if health_metrics == 1:
                analysis_entity.HealthMetrics = health_metrics_diagnosis.process(waveform_info)  # 健康指标
            else:
                analysis_entity.HealthMetrics = HealthMetricsEntity()

            analysis_entity.PQRSTC = pqrstc

            analysis_entity.RRIntervals = ','.join(map(str, waveform_info.get('waveform', {}).get('rr_intervals', [])))
            analysis_entity.NNIntervals = ','.join(map(str, waveform_info.get('waveform', {}).get('nn_intervals', [])))

            # 处理加速度
            if 'gravity' in data and data['gravity']:
                gravity = data['gravity']
                analysis_entity.MotionInfo = gravity_process(union_id, data['gravity'])
                
                # 将运动强度为中(2)和高(3)的状态定义为噪音
                if analysis_entity.MotionInfo and analysis_entity.MotionInfo.motion_intensity in [2, 3]:
                    analysis_entity.SignalQuantity = -1
            else:
                gravity = None
                analysis_entity.MotionInfo = MotionInfoEntity()

            # 诊断详情设置
            analysis_entity.DiagnosisDetails = get_diagnosis_details(analysis_entity, waveform_info, sampling_rate)

            analysis_entity.ecg_id = save_api_log(ecg_data_str, gravity, sampling_rate, gain, zero, analysis_entity,
                                                  custom_id)  # ecg信号分析ID

            # 后处理，并准备最终返回数据
            final_data = self.after_process(analysis_entity)

            # 修正：如果信号因高运动强度被标记为噪音，确保返回原始运动状态
            if analysis_entity.MotionInfo and analysis_entity.MotionInfo.motion_intensity in [2, 3]:
                # 重新将正确的运动信息写入最终的返回字典中
                final_data['MotionInfo'] = {
                    'motion_intensity': analysis_entity.MotionInfo.motion_intensity,
                    'motion_proportion': analysis_entity.MotionInfo.motion_proportion,
                }
                # 同时，确保最终返回的信号质量为-1（噪音）
                final_data['SignalQuantity'] = -1

            return GetResponse.get_response(code=0, data=final_data)
        except BiosppyEcgError as e:
            # 错误处理
            Logger().error(f'\n客户ID: {union_id}, 心电信号：{ecg_data}\n{traceback.format_exc()}')
            return GetResponse.get_response(code=7)
        except Exception as e:
            # 错误处理
            Logger().error(f'\n客户ID: {union_id}, 心电信号：{ecg_data}\n{traceback.format_exc()}')
            return GetResponse.get_response(code=2)

    def after_process(self, analysis_entity):
        """
        后处理
        :param analysis_entity: 分析实体对象
        :return: 处理后的分析实体对象
        """

        # 规则过滤
        final_result = apply_rules(analysis_entity)

        if not any(value == 1 for value in vars(final_result).values()):
            final_result.SN = 1

        # 过滤负值并返回
        response_data = filter_negative_values(analysis_entity.to_entity_dict())

        return response_data
