# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Set of tools for real-time data augmentation on image data.
"""

from __future__ import print_function as _print_function

import sys as _sys

from tensorflow.python.keras.preprocessing.image import DirectoryIterator
from tensorflow.python.keras.preprocessing.image import ImageDataGenerator
from tensorflow.python.keras.preprocessing.image import Iterator
from tensorflow.python.keras.preprocessing.image import NumpyArrayIterator
from tensorflow.python.keras.preprocessing.image import apply_affine_transform
from tensorflow.python.keras.preprocessing.image import apply_brightness_shift
from tensorflow.python.keras.preprocessing.image import apply_channel_shift
from tensorflow.python.keras.preprocessing.image import array_to_img
from tensorflow.python.keras.preprocessing.image import img_to_array
from tensorflow.python.keras.preprocessing.image import load_img
from tensorflow.python.keras.preprocessing.image import random_brightness
from tensorflow.python.keras.preprocessing.image import random_channel_shift
from tensorflow.python.keras.preprocessing.image import random_rotation
from tensorflow.python.keras.preprocessing.image import random_shear
from tensorflow.python.keras.preprocessing.image import random_shift
from tensorflow.python.keras.preprocessing.image import random_zoom
from tensorflow.python.keras.preprocessing.image import save_img

del _print_function

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.preprocessing.image", public_apis=None, deprecation=True,
      has_lite=False)
