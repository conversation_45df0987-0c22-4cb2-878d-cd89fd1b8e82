import numpy as np
from apps.utils.logger_helper import Logger

# --- 房颤检测函数 ---

def detect_af_episodes(rr_intervals, r_peaks, sampling_rate, min_duration_sec=3, rr_std_thresh_factor=1.2,
                       window_size=8):
    """
    简化版基于RR间期标准差检测房颤/房扑发作
    """
    af_episodes = []
    if rr_intervals is None or len(rr_intervals) < window_size:
        return af_episodes

    try:
        # 计算 HRV 相关指标
        rr_diffs = np.abs(np.diff(rr_intervals))
        n_pairs = len(rr_diffs)
        high_af_probability = False

        if n_pairs > 0:
            # 计算 pNN50 和 RMSSD 指标
            n50 = sum(diff > 0.050 for diff in rr_diffs)
            pnn50 = (n50 / n_pairs) * 100
            rmssd = np.sqrt(np.mean(np.square(rr_diffs)))

            # 使用 HRV 指标判断房颤可能性
            high_af_probability = pnn50 > 40 and rmssd * 1000 > 100

            # 如果 HRV 指标显示高房颤可能性，添加一个覆盖事件
            if high_af_probability and r_peaks is not None and len(r_peaks) > 2:
                start_time = r_peaks[0] / sampling_rate
                end_time = r_peaks[-1] / sampling_rate
                duration = end_time - start_time

                if duration >= min_duration_sec:
                    longest_rr = np.max(rr_intervals) if len(rr_intervals) > 0 else 0
                    # 避免重复添加
                    if not any(ep.get('detection_method') == 'hrv_metrics' for ep in af_episodes):
                        af_episodes.append({
                            'start_time': start_time,
                            'end_time': end_time,
                            'duration': duration,
                            'longest_rr': longest_rr,
                            'detection_method': 'hrv_metrics'
                        })

        # 基于 RR 间期标准差的滑动窗口检测
        rr_diffs = np.abs(np.diff(rr_intervals))
        if len(rr_diffs) >= window_size:
            # 计算滑动窗口标准差
            rolling_std = np.sqrt(np.convolve(rr_diffs ** 2, np.ones(window_size) / window_size, mode='valid'))
            if len(rolling_std) > 0:
                median_rr_diff_std = np.median(rolling_std)
                threshold = median_rr_diff_std * rr_std_thresh_factor

                # 标记潜在房颤心跳
                is_potential_af_beat = np.zeros(len(rr_intervals) + 1, dtype=bool)

                for i in range(len(rr_intervals) - window_size + 1):
                    window_rr = rr_intervals[i: i + window_size]
                    window_std = np.std(window_rr)

                    if window_std > threshold:
                        for k in range(i, i + window_size + 1):
                            if k < len(is_potential_af_beat):
                                is_potential_af_beat[k] = True

                # 查找连续的房颤段
                start_idx = -1
                current_episode_rrs = []

                for i in range(len(r_peaks)):
                    is_af_beat = is_potential_af_beat[i]

                    if is_af_beat and start_idx == -1:  # 开始新段
                        start_idx = i
                        current_episode_rrs = []
                        if i < len(rr_intervals):
                            current_episode_rrs.append(rr_intervals[i])

                    elif is_af_beat:  # 继续当前段
                        if i < len(rr_intervals):
                            current_episode_rrs.append(rr_intervals[i])

                    elif not is_af_beat and start_idx != -1:  # 结束段
                        end_idx = i - 1
                        if start_idx <= end_idx:
                            start_time = r_peaks[start_idx] / sampling_rate
                            # 计算结束时间
                            end_time_rpeak_index = end_idx + 1
                            if end_time_rpeak_index < len(r_peaks):
                                end_time = r_peaks[end_time_rpeak_index] / sampling_rate
                            else:
                                # 如果是最后一个R峰，则基于平均RR估计结束时间
                                avg_rr = np.mean(current_episode_rrs) if current_episode_rrs else np.mean(rr_intervals)
                                end_time = (r_peaks[end_idx] / sampling_rate) + avg_rr

                            duration = end_time - start_time

                            if duration >= min_duration_sec:
                                longest_rr = np.max(current_episode_rrs) if current_episode_rrs else 0
                                af_episodes.append({
                                    'start_time': start_time,
                                    'end_time': end_time,
                                    'duration': duration,
                                    'longest_rr': longest_rr,
                                    'detection_method': 'rr_variability'
                                })
                        start_idx = -1
                        current_episode_rrs = []

                # 处理结束于最后一拍的段
                if start_idx != -1:
                    end_idx = len(r_peaks) - 1
                    if start_idx <= end_idx:
                        start_time = r_peaks[start_idx] / sampling_rate
                        # 计算结束时间
                        if end_idx < len(rr_intervals):
                             end_time = (r_peaks[end_idx] / sampling_rate) + rr_intervals[end_idx]
                        else:
                            # 如果是最后一个R峰，则基于平均RR估计结束时间
                            avg_rr = np.mean(current_episode_rrs) if current_episode_rrs else np.mean(rr_intervals)
                            end_time = (r_peaks[end_idx] / sampling_rate) + avg_rr

                        duration = end_time - start_time
                        if duration >= min_duration_sec:
                            longest_rr = np.max(current_episode_rrs) if current_episode_rrs else 0
                            af_episodes.append({
                                'start_time': start_time,
                                'end_time': end_time,
                                'duration': duration,
                                'longest_rr': longest_rr,
                                'detection_method': 'rr_variability'
                            })
    except Exception as e:
        Logger().error(f"房颤检测错误: {str(e)}")

    # 合并或去重发作？暂时保留所有检测到的
    return af_episodes

def quantify_af(af_episodes, total_duration_sec, hrv_data=None):
    """简化版房颤量化指标计算"""
    if total_duration_sec is None:
        total_duration_sec = 10.0

    # 初始化量化结果，使用建议的字段名
    result = {
        'af_total_episodes': len(af_episodes) if af_episodes else 0,
        'af_total_duration': 0,
        'af_percentage': 0,
        'af_longest_duration': 0,
        'af_longest_rr': 0,
        'af_probability': 0.0
        # 'af_fastest_hr': None # 暂未实现
    }

    # 没有检测到发作但有HRV数据时，从HRV估计房颤可能性
    # 并且只有在基于 HRV 检测到高可能性时才进行估计
    hrv_based_ep = next((ep for ep in af_episodes if ep.get('detection_method') == 'hrv_metrics'), None)
    if (not af_episodes or len(af_episodes) == 0 or hrv_based_ep) and hrv_data is not None:
        try:
            pnn50 = hrv_data.get('linear', {}).get('pnn50', 0)
            rmssd = hrv_data.get('linear', {}).get('rmssd', 0)
            sdnn = hrv_data.get('linear', {}).get('sdnn', 0)

            # 计算综合概率
            af_prob_pnn = min(1.0, pnn50 / 50.0) if pnn50 > 0 else 0
            af_prob_rmssd = min(1.0, rmssd / 150.0) if rmssd > 0 else 0
            af_prob_sdnn = min(1.0, sdnn / 100.0) if sdnn > 0 else 0
            af_probability = 0.4 * af_prob_pnn + 0.4 * af_prob_rmssd + 0.2 * af_prob_sdnn

            result['af_probability'] = af_probability

            # 高概率时创建假设性发作 (仅在没有其他发作时)
            if af_probability > 0.5 and len(af_episodes) == 0:
                result['af_total_duration'] = total_duration_sec * af_probability
                result['af_percentage'] = af_probability * 100
                result['af_longest_duration'] = total_duration_sec * af_probability
                result['af_total_episodes'] = 1 # 至少算一个
                return result # 如果只基于HRV估计，直接返回

        except Exception:
            pass

    # 存在检测到的发作时正常计算 (排除仅基于HRV的假设性发作)
    valid_episodes = [ep for ep in af_episodes if ep.get('detection_method') != 'hrv_metrics']
    if valid_episodes:
        try:
            result['af_total_episodes'] = len(valid_episodes)
            total_af_duration = sum(ep['duration'] for ep in valid_episodes)
            percentage = (total_af_duration / total_duration_sec) * 100 if total_duration_sec > 0 else 0
            longest_episode_duration = max(ep['duration'] for ep in valid_episodes) if valid_episodes else 0
            longest_rr_during_af = max(
                ep['longest_rr'] for ep in valid_episodes if 'longest_rr' in ep) if valid_episodes else 0

            # 更新结果字典
            result.update({
                'af_total_duration': total_af_duration,
                'af_percentage': percentage,
                'af_longest_duration': longest_episode_duration,
                'af_longest_rr': longest_rr_during_af,
                'af_probability': min(1.0, percentage / 100.0) if percentage > 0 else result['af_probability'] # 保留基于HRV的概率（如果更高）
            })
        except Exception as e:
            Logger().error(f"房颤量化错误: {str(e)}")

    # 如果没有有效发作，但HRV概率存在，则返回基于HRV的估计
    elif result['af_probability'] > 0:
        # 之前可能已经计算过基于HRV的估计，这里只需确保返回result
        pass
    # 否则返回初始化的result (全零)

    return result

def analyze_af(rr_intervals, r_peaks, sampling_rate, total_duration_sec, hrv_data):
    """执行房颤检测和量化"""
    episodes = detect_af_episodes(rr_intervals, r_peaks, sampling_rate)
    quantification = quantify_af(episodes, total_duration_sec, hrv_data)
    # 为了减小API响应体积，可以选择不返回详细的episodes列表
    # return {'quantification': quantification}
    return {
        'episodes': episodes,
        'quantification': quantification
    } 