/* Copyright 2016 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

syntax = "proto3";

package tensorflow;

import "tensorflow/core/protobuf/cluster.proto";
import "tensorflow/core/protobuf/config.proto";
import "tensorflow/core/protobuf/device_filters.proto";

option cc_enable_arenas = true;
option java_outer_classname = "ServerProtos";
option java_multiple_files = true;
option java_package = "org.tensorflow.distruntime";
option go_package = "github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto";

// Defines the configuration of a single TensorFlow server.
message ServerDef {
  // The cluster of which this server is a member.
  ClusterDef cluster = 1;

  // The name of the job of which this server is a member.
  //
  // NOTE(mrry): The `cluster` field must contain a `JobDef` with a `name` field
  // that matches this name.
  string job_name = 2;

  // The task index of this server in its job.
  //
  // NOTE: The `cluster` field must contain a `JobDef` with a matching `name`
  // and a mapping in its `tasks` field for this index.
  int32 task_index = 3;

  // The default configuration for sessions that run on this server.
  ConfigProto default_session_config = 4;

  // The protocol to be used by this server.
  //
  // Acceptable values include: "grpc", "grpc+verbs".
  string protocol = 5;

  // The server port. If not set, then we identify the port from the job_name.
  int32 port = 6;

  // Device filters for remote tasks in the cluster.
  // NOTE: This is an experimental feature and only effective in TensorFlow 2.x.
  ClusterDeviceFilters cluster_device_filters = 7;
}
