# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.keras.optimizers.schedules namespace.
"""

from __future__ import print_function as _print_function

import sys as _sys

from keras.optimizer_v2.learning_rate_schedule import CosineDecay
from keras.optimizer_v2.learning_rate_schedule import CosineDecayRestarts
from keras.optimizer_v2.learning_rate_schedule import ExponentialDecay
from keras.optimizer_v2.learning_rate_schedule import InverseTimeDecay
from keras.optimizer_v2.learning_rate_schedule import LearningRateSchedule
from keras.optimizer_v2.learning_rate_schedule import PiecewiseConstantDecay
from keras.optimizer_v2.learning_rate_schedule import PolynomialDecay
from keras.optimizer_v2.learning_rate_schedule import deserialize
from keras.optimizer_v2.learning_rate_schedule import serialize

del _print_function

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.optimizers.schedules", public_apis=None, deprecation=True,
      has_lite=False)
