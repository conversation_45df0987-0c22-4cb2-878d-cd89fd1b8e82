# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.keras.models namespace.
"""

from __future__ import print_function as _print_function

import sys as _sys

from keras.engine.sequential import Sequential
from keras.engine.training import Model
from keras.models import clone_model
from keras.saving.model_config import model_from_config
from keras.saving.model_config import model_from_json
from keras.saving.model_config import model_from_yaml
from keras.saving.save import load_model
from keras.saving.save import save_model

del _print_function

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.models", public_apis=None, deprecation=True,
      has_lite=False)
