#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的数据提取逻辑
"""

from browser_crawler import BrowserBadmintonCrawler
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_extraction():
    """测试增强的数据提取功能"""
    
    # 创建爬虫实例
    crawler = BrowserBadmintonCrawler()
    
    # 测试页面
    test_url = 'https://www.badmintoncn.com/cbo_eq/view.php?eid=22974'
    print(f'🏸 测试页面: {test_url}')
    
    try:
        # 设置浏览器
        crawler.setup_driver()
        print("✅ 浏览器启动成功")
        
        # 爬取单页数据
        result = crawler.crawl_equipment_details(test_url)
        
        if result:
            print('\n=== 📊 提取结果 ===')
            
            # 按类别展示结果
            basic_info = ['title', 'name', 'brand', 'series', 'keywords']
            tech_params = ['weight', 'balance_point', 'stiffness', 'string_tension', 'shaft_material', 'frame_material', 'shaft_diameter', 'frame_design', 'technology']
            price_info = ['msrp_price', 'price_range', 'min_market_price', 'max_market_price', 'avg_market_price']
            other_info = ['rating', 'user_tags', 'description', 'release_date']
            
            print('\n🏷️ 基本信息:')
            for key in basic_info:
                if key in result:
                    print(f'  {key}: {result[key]}')
            
            print('\n⚙️ 技术参数:')
            for key in tech_params:
                if key in result:
                    print(f'  {key}: {result[key]}')
            
            print('\n💰 价格信息:')
            for key in price_info:
                if key in result:
                    print(f'  {key}: {result[key]}')
            
            print('\n📝 其他信息:')
            for key in other_info:
                if key in result:
                    value = result[key]
                    if key == 'description' and len(str(value)) > 100:
                        value = str(value)[:100] + '...'
                    print(f'  {key}: {value}')
            
            # 保存到文件
            with open('test_single_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print('\n📄 结果已保存到 test_single_result.json')
            
            # 统计提取字段数量
            total_fields = len(result) - 2  # 排除 url 和 crawl_time
            print(f'\n📈 提取统计: 共提取 {total_fields} 个字段')
            
        else:
            print('❌ 提取失败')
            
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
    
    finally:
        # 关闭浏览器
        try:
            crawler.driver.quit()
            print("🔚 浏览器已关闭")
        except:
            pass

if __name__ == "__main__":
    test_enhanced_extraction() 