// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/variable.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fvariable_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fvariable_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fvariable_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fvariable_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fvariable_2eproto;
namespace tensorflow {
class SaveSliceInfoDef;
class SaveSliceInfoDefDefaultTypeInternal;
extern SaveSliceInfoDefDefaultTypeInternal _SaveSliceInfoDef_default_instance_;
class VariableDef;
class VariableDefDefaultTypeInternal;
extern VariableDefDefaultTypeInternal _VariableDef_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::SaveSliceInfoDef* Arena::CreateMaybeMessage<::tensorflow::SaveSliceInfoDef>(Arena*);
template<> ::tensorflow::VariableDef* Arena::CreateMaybeMessage<::tensorflow::VariableDef>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum VariableSynchronization : int {
  VARIABLE_SYNCHRONIZATION_AUTO = 0,
  VARIABLE_SYNCHRONIZATION_NONE = 1,
  VARIABLE_SYNCHRONIZATION_ON_WRITE = 2,
  VARIABLE_SYNCHRONIZATION_ON_READ = 3,
  VariableSynchronization_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  VariableSynchronization_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool VariableSynchronization_IsValid(int value);
constexpr VariableSynchronization VariableSynchronization_MIN = VARIABLE_SYNCHRONIZATION_AUTO;
constexpr VariableSynchronization VariableSynchronization_MAX = VARIABLE_SYNCHRONIZATION_ON_READ;
constexpr int VariableSynchronization_ARRAYSIZE = VariableSynchronization_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* VariableSynchronization_descriptor();
template<typename T>
inline const std::string& VariableSynchronization_Name(T enum_t_value) {
  static_assert(::std::is_same<T, VariableSynchronization>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function VariableSynchronization_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    VariableSynchronization_descriptor(), enum_t_value);
}
inline bool VariableSynchronization_Parse(
    const std::string& name, VariableSynchronization* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<VariableSynchronization>(
    VariableSynchronization_descriptor(), name, value);
}
enum VariableAggregation : int {
  VARIABLE_AGGREGATION_NONE = 0,
  VARIABLE_AGGREGATION_SUM = 1,
  VARIABLE_AGGREGATION_MEAN = 2,
  VARIABLE_AGGREGATION_ONLY_FIRST_REPLICA = 3,
  VariableAggregation_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  VariableAggregation_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool VariableAggregation_IsValid(int value);
constexpr VariableAggregation VariableAggregation_MIN = VARIABLE_AGGREGATION_NONE;
constexpr VariableAggregation VariableAggregation_MAX = VARIABLE_AGGREGATION_ONLY_FIRST_REPLICA;
constexpr int VariableAggregation_ARRAYSIZE = VariableAggregation_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* VariableAggregation_descriptor();
template<typename T>
inline const std::string& VariableAggregation_Name(T enum_t_value) {
  static_assert(::std::is_same<T, VariableAggregation>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function VariableAggregation_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    VariableAggregation_descriptor(), enum_t_value);
}
inline bool VariableAggregation_Parse(
    const std::string& name, VariableAggregation* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<VariableAggregation>(
    VariableAggregation_descriptor(), name, value);
}
// ===================================================================

class VariableDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.VariableDef) */ {
 public:
  VariableDef();
  virtual ~VariableDef();

  VariableDef(const VariableDef& from);
  VariableDef(VariableDef&& from) noexcept
    : VariableDef() {
    *this = ::std::move(from);
  }

  inline VariableDef& operator=(const VariableDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline VariableDef& operator=(VariableDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const VariableDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const VariableDef* internal_default_instance() {
    return reinterpret_cast<const VariableDef*>(
               &_VariableDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(VariableDef& a, VariableDef& b) {
    a.Swap(&b);
  }
  inline void Swap(VariableDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VariableDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline VariableDef* New() const final {
    return CreateMaybeMessage<VariableDef>(nullptr);
  }

  VariableDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<VariableDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const VariableDef& from);
  void MergeFrom(const VariableDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VariableDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.VariableDef";
  }
  protected:
  explicit VariableDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fvariable_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fvariable_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVariableNameFieldNumber = 1,
    kInitializerNameFieldNumber = 2,
    kSnapshotNameFieldNumber = 3,
    kInitialValueNameFieldNumber = 6,
    kSaveSliceInfoDefFieldNumber = 4,
    kIsResourceFieldNumber = 5,
    kTrainableFieldNumber = 7,
    kSynchronizationFieldNumber = 8,
    kAggregationFieldNumber = 9,
  };
  // string variable_name = 1;
  void clear_variable_name();
  const std::string& variable_name() const;
  void set_variable_name(const std::string& value);
  void set_variable_name(std::string&& value);
  void set_variable_name(const char* value);
  void set_variable_name(const char* value, size_t size);
  std::string* mutable_variable_name();
  std::string* release_variable_name();
  void set_allocated_variable_name(std::string* variable_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_variable_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_variable_name(
      std::string* variable_name);

  // string initializer_name = 2;
  void clear_initializer_name();
  const std::string& initializer_name() const;
  void set_initializer_name(const std::string& value);
  void set_initializer_name(std::string&& value);
  void set_initializer_name(const char* value);
  void set_initializer_name(const char* value, size_t size);
  std::string* mutable_initializer_name();
  std::string* release_initializer_name();
  void set_allocated_initializer_name(std::string* initializer_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_initializer_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_initializer_name(
      std::string* initializer_name);

  // string snapshot_name = 3;
  void clear_snapshot_name();
  const std::string& snapshot_name() const;
  void set_snapshot_name(const std::string& value);
  void set_snapshot_name(std::string&& value);
  void set_snapshot_name(const char* value);
  void set_snapshot_name(const char* value, size_t size);
  std::string* mutable_snapshot_name();
  std::string* release_snapshot_name();
  void set_allocated_snapshot_name(std::string* snapshot_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_snapshot_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_snapshot_name(
      std::string* snapshot_name);

  // string initial_value_name = 6;
  void clear_initial_value_name();
  const std::string& initial_value_name() const;
  void set_initial_value_name(const std::string& value);
  void set_initial_value_name(std::string&& value);
  void set_initial_value_name(const char* value);
  void set_initial_value_name(const char* value, size_t size);
  std::string* mutable_initial_value_name();
  std::string* release_initial_value_name();
  void set_allocated_initial_value_name(std::string* initial_value_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_initial_value_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_initial_value_name(
      std::string* initial_value_name);

  // .tensorflow.SaveSliceInfoDef save_slice_info_def = 4;
  bool has_save_slice_info_def() const;
  void clear_save_slice_info_def();
  const ::tensorflow::SaveSliceInfoDef& save_slice_info_def() const;
  ::tensorflow::SaveSliceInfoDef* release_save_slice_info_def();
  ::tensorflow::SaveSliceInfoDef* mutable_save_slice_info_def();
  void set_allocated_save_slice_info_def(::tensorflow::SaveSliceInfoDef* save_slice_info_def);
  void unsafe_arena_set_allocated_save_slice_info_def(
      ::tensorflow::SaveSliceInfoDef* save_slice_info_def);
  ::tensorflow::SaveSliceInfoDef* unsafe_arena_release_save_slice_info_def();

  // bool is_resource = 5;
  void clear_is_resource();
  bool is_resource() const;
  void set_is_resource(bool value);

  // bool trainable = 7;
  void clear_trainable();
  bool trainable() const;
  void set_trainable(bool value);

  // .tensorflow.VariableSynchronization synchronization = 8;
  void clear_synchronization();
  ::tensorflow::VariableSynchronization synchronization() const;
  void set_synchronization(::tensorflow::VariableSynchronization value);

  // .tensorflow.VariableAggregation aggregation = 9;
  void clear_aggregation();
  ::tensorflow::VariableAggregation aggregation() const;
  void set_aggregation(::tensorflow::VariableAggregation value);

  // @@protoc_insertion_point(class_scope:tensorflow.VariableDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr variable_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr initializer_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr snapshot_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr initial_value_name_;
  ::tensorflow::SaveSliceInfoDef* save_slice_info_def_;
  bool is_resource_;
  bool trainable_;
  int synchronization_;
  int aggregation_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fvariable_2eproto;
};
// -------------------------------------------------------------------

class SaveSliceInfoDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SaveSliceInfoDef) */ {
 public:
  SaveSliceInfoDef();
  virtual ~SaveSliceInfoDef();

  SaveSliceInfoDef(const SaveSliceInfoDef& from);
  SaveSliceInfoDef(SaveSliceInfoDef&& from) noexcept
    : SaveSliceInfoDef() {
    *this = ::std::move(from);
  }

  inline SaveSliceInfoDef& operator=(const SaveSliceInfoDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveSliceInfoDef& operator=(SaveSliceInfoDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SaveSliceInfoDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SaveSliceInfoDef* internal_default_instance() {
    return reinterpret_cast<const SaveSliceInfoDef*>(
               &_SaveSliceInfoDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SaveSliceInfoDef& a, SaveSliceInfoDef& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveSliceInfoDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveSliceInfoDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SaveSliceInfoDef* New() const final {
    return CreateMaybeMessage<SaveSliceInfoDef>(nullptr);
  }

  SaveSliceInfoDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SaveSliceInfoDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SaveSliceInfoDef& from);
  void MergeFrom(const SaveSliceInfoDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveSliceInfoDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SaveSliceInfoDef";
  }
  protected:
  explicit SaveSliceInfoDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fvariable_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fvariable_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFullShapeFieldNumber = 2,
    kVarOffsetFieldNumber = 3,
    kVarShapeFieldNumber = 4,
    kFullNameFieldNumber = 1,
  };
  // repeated int64 full_shape = 2;
  int full_shape_size() const;
  void clear_full_shape();
  ::PROTOBUF_NAMESPACE_ID::int64 full_shape(int index) const;
  void set_full_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_full_shape(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      full_shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_full_shape();

  // repeated int64 var_offset = 3;
  int var_offset_size() const;
  void clear_var_offset();
  ::PROTOBUF_NAMESPACE_ID::int64 var_offset(int index) const;
  void set_var_offset(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_var_offset(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      var_offset() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_var_offset();

  // repeated int64 var_shape = 4;
  int var_shape_size() const;
  void clear_var_shape();
  ::PROTOBUF_NAMESPACE_ID::int64 var_shape(int index) const;
  void set_var_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_var_shape(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      var_shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_var_shape();

  // string full_name = 1;
  void clear_full_name();
  const std::string& full_name() const;
  void set_full_name(const std::string& value);
  void set_full_name(std::string&& value);
  void set_full_name(const char* value);
  void set_full_name(const char* value, size_t size);
  std::string* mutable_full_name();
  std::string* release_full_name();
  void set_allocated_full_name(std::string* full_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_full_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_full_name(
      std::string* full_name);

  // @@protoc_insertion_point(class_scope:tensorflow.SaveSliceInfoDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > full_shape_;
  mutable std::atomic<int> _full_shape_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > var_offset_;
  mutable std::atomic<int> _var_offset_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > var_shape_;
  mutable std::atomic<int> _var_shape_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr full_name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fvariable_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// VariableDef

// string variable_name = 1;
inline void VariableDef::clear_variable_name() {
  variable_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& VariableDef::variable_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.variable_name)
  return variable_name_.Get();
}
inline void VariableDef::set_variable_name(const std::string& value) {
  
  variable_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.variable_name)
}
inline void VariableDef::set_variable_name(std::string&& value) {
  
  variable_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VariableDef.variable_name)
}
inline void VariableDef::set_variable_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  variable_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VariableDef.variable_name)
}
inline void VariableDef::set_variable_name(const char* value,
    size_t size) {
  
  variable_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VariableDef.variable_name)
}
inline std::string* VariableDef::mutable_variable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.variable_name)
  return variable_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* VariableDef::release_variable_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.variable_name)
  
  return variable_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VariableDef::set_allocated_variable_name(std::string* variable_name) {
  if (variable_name != nullptr) {
    
  } else {
    
  }
  variable_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), variable_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.variable_name)
}
inline std::string* VariableDef::unsafe_arena_release_variable_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariableDef.variable_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return variable_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VariableDef::unsafe_arena_set_allocated_variable_name(
    std::string* variable_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (variable_name != nullptr) {
    
  } else {
    
  }
  variable_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      variable_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariableDef.variable_name)
}

// string initial_value_name = 6;
inline void VariableDef::clear_initial_value_name() {
  initial_value_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& VariableDef::initial_value_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.initial_value_name)
  return initial_value_name_.Get();
}
inline void VariableDef::set_initial_value_name(const std::string& value) {
  
  initial_value_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.initial_value_name)
}
inline void VariableDef::set_initial_value_name(std::string&& value) {
  
  initial_value_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VariableDef.initial_value_name)
}
inline void VariableDef::set_initial_value_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  initial_value_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VariableDef.initial_value_name)
}
inline void VariableDef::set_initial_value_name(const char* value,
    size_t size) {
  
  initial_value_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VariableDef.initial_value_name)
}
inline std::string* VariableDef::mutable_initial_value_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.initial_value_name)
  return initial_value_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* VariableDef::release_initial_value_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.initial_value_name)
  
  return initial_value_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VariableDef::set_allocated_initial_value_name(std::string* initial_value_name) {
  if (initial_value_name != nullptr) {
    
  } else {
    
  }
  initial_value_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), initial_value_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.initial_value_name)
}
inline std::string* VariableDef::unsafe_arena_release_initial_value_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariableDef.initial_value_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return initial_value_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VariableDef::unsafe_arena_set_allocated_initial_value_name(
    std::string* initial_value_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (initial_value_name != nullptr) {
    
  } else {
    
  }
  initial_value_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      initial_value_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariableDef.initial_value_name)
}

// string initializer_name = 2;
inline void VariableDef::clear_initializer_name() {
  initializer_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& VariableDef::initializer_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.initializer_name)
  return initializer_name_.Get();
}
inline void VariableDef::set_initializer_name(const std::string& value) {
  
  initializer_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.initializer_name)
}
inline void VariableDef::set_initializer_name(std::string&& value) {
  
  initializer_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VariableDef.initializer_name)
}
inline void VariableDef::set_initializer_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  initializer_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VariableDef.initializer_name)
}
inline void VariableDef::set_initializer_name(const char* value,
    size_t size) {
  
  initializer_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VariableDef.initializer_name)
}
inline std::string* VariableDef::mutable_initializer_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.initializer_name)
  return initializer_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* VariableDef::release_initializer_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.initializer_name)
  
  return initializer_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VariableDef::set_allocated_initializer_name(std::string* initializer_name) {
  if (initializer_name != nullptr) {
    
  } else {
    
  }
  initializer_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), initializer_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.initializer_name)
}
inline std::string* VariableDef::unsafe_arena_release_initializer_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariableDef.initializer_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return initializer_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VariableDef::unsafe_arena_set_allocated_initializer_name(
    std::string* initializer_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (initializer_name != nullptr) {
    
  } else {
    
  }
  initializer_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      initializer_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariableDef.initializer_name)
}

// string snapshot_name = 3;
inline void VariableDef::clear_snapshot_name() {
  snapshot_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& VariableDef::snapshot_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.snapshot_name)
  return snapshot_name_.Get();
}
inline void VariableDef::set_snapshot_name(const std::string& value) {
  
  snapshot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.snapshot_name)
}
inline void VariableDef::set_snapshot_name(std::string&& value) {
  
  snapshot_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VariableDef.snapshot_name)
}
inline void VariableDef::set_snapshot_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  snapshot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VariableDef.snapshot_name)
}
inline void VariableDef::set_snapshot_name(const char* value,
    size_t size) {
  
  snapshot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VariableDef.snapshot_name)
}
inline std::string* VariableDef::mutable_snapshot_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.snapshot_name)
  return snapshot_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* VariableDef::release_snapshot_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.snapshot_name)
  
  return snapshot_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VariableDef::set_allocated_snapshot_name(std::string* snapshot_name) {
  if (snapshot_name != nullptr) {
    
  } else {
    
  }
  snapshot_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), snapshot_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.snapshot_name)
}
inline std::string* VariableDef::unsafe_arena_release_snapshot_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariableDef.snapshot_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return snapshot_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VariableDef::unsafe_arena_set_allocated_snapshot_name(
    std::string* snapshot_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (snapshot_name != nullptr) {
    
  } else {
    
  }
  snapshot_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      snapshot_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariableDef.snapshot_name)
}

// .tensorflow.SaveSliceInfoDef save_slice_info_def = 4;
inline bool VariableDef::has_save_slice_info_def() const {
  return this != internal_default_instance() && save_slice_info_def_ != nullptr;
}
inline void VariableDef::clear_save_slice_info_def() {
  if (GetArenaNoVirtual() == nullptr && save_slice_info_def_ != nullptr) {
    delete save_slice_info_def_;
  }
  save_slice_info_def_ = nullptr;
}
inline const ::tensorflow::SaveSliceInfoDef& VariableDef::save_slice_info_def() const {
  const ::tensorflow::SaveSliceInfoDef* p = save_slice_info_def_;
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.save_slice_info_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::SaveSliceInfoDef*>(
      &::tensorflow::_SaveSliceInfoDef_default_instance_);
}
inline ::tensorflow::SaveSliceInfoDef* VariableDef::release_save_slice_info_def() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.save_slice_info_def)
  
  ::tensorflow::SaveSliceInfoDef* temp = save_slice_info_def_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  save_slice_info_def_ = nullptr;
  return temp;
}
inline ::tensorflow::SaveSliceInfoDef* VariableDef::unsafe_arena_release_save_slice_info_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VariableDef.save_slice_info_def)
  
  ::tensorflow::SaveSliceInfoDef* temp = save_slice_info_def_;
  save_slice_info_def_ = nullptr;
  return temp;
}
inline ::tensorflow::SaveSliceInfoDef* VariableDef::mutable_save_slice_info_def() {
  
  if (save_slice_info_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SaveSliceInfoDef>(GetArenaNoVirtual());
    save_slice_info_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.save_slice_info_def)
  return save_slice_info_def_;
}
inline void VariableDef::set_allocated_save_slice_info_def(::tensorflow::SaveSliceInfoDef* save_slice_info_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete save_slice_info_def_;
  }
  if (save_slice_info_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(save_slice_info_def);
    if (message_arena != submessage_arena) {
      save_slice_info_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, save_slice_info_def, submessage_arena);
    }
    
  } else {
    
  }
  save_slice_info_def_ = save_slice_info_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.save_slice_info_def)
}

// bool is_resource = 5;
inline void VariableDef::clear_is_resource() {
  is_resource_ = false;
}
inline bool VariableDef::is_resource() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.is_resource)
  return is_resource_;
}
inline void VariableDef::set_is_resource(bool value) {
  
  is_resource_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.is_resource)
}

// bool trainable = 7;
inline void VariableDef::clear_trainable() {
  trainable_ = false;
}
inline bool VariableDef::trainable() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.trainable)
  return trainable_;
}
inline void VariableDef::set_trainable(bool value) {
  
  trainable_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.trainable)
}

// .tensorflow.VariableSynchronization synchronization = 8;
inline void VariableDef::clear_synchronization() {
  synchronization_ = 0;
}
inline ::tensorflow::VariableSynchronization VariableDef::synchronization() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.synchronization)
  return static_cast< ::tensorflow::VariableSynchronization >(synchronization_);
}
inline void VariableDef::set_synchronization(::tensorflow::VariableSynchronization value) {
  
  synchronization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.synchronization)
}

// .tensorflow.VariableAggregation aggregation = 9;
inline void VariableDef::clear_aggregation() {
  aggregation_ = 0;
}
inline ::tensorflow::VariableAggregation VariableDef::aggregation() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.aggregation)
  return static_cast< ::tensorflow::VariableAggregation >(aggregation_);
}
inline void VariableDef::set_aggregation(::tensorflow::VariableAggregation value) {
  
  aggregation_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.aggregation)
}

// -------------------------------------------------------------------

// SaveSliceInfoDef

// string full_name = 1;
inline void SaveSliceInfoDef::clear_full_name() {
  full_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SaveSliceInfoDef::full_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.full_name)
  return full_name_.Get();
}
inline void SaveSliceInfoDef::set_full_name(const std::string& value) {
  
  full_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.full_name)
}
inline void SaveSliceInfoDef::set_full_name(std::string&& value) {
  
  full_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SaveSliceInfoDef.full_name)
}
inline void SaveSliceInfoDef::set_full_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  full_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SaveSliceInfoDef.full_name)
}
inline void SaveSliceInfoDef::set_full_name(const char* value,
    size_t size) {
  
  full_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SaveSliceInfoDef.full_name)
}
inline std::string* SaveSliceInfoDef::mutable_full_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SaveSliceInfoDef.full_name)
  return full_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SaveSliceInfoDef::release_full_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SaveSliceInfoDef.full_name)
  
  return full_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SaveSliceInfoDef::set_allocated_full_name(std::string* full_name) {
  if (full_name != nullptr) {
    
  } else {
    
  }
  full_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), full_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SaveSliceInfoDef.full_name)
}
inline std::string* SaveSliceInfoDef::unsafe_arena_release_full_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SaveSliceInfoDef.full_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return full_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SaveSliceInfoDef::unsafe_arena_set_allocated_full_name(
    std::string* full_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (full_name != nullptr) {
    
  } else {
    
  }
  full_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      full_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SaveSliceInfoDef.full_name)
}

// repeated int64 full_shape = 2;
inline int SaveSliceInfoDef::full_shape_size() const {
  return full_shape_.size();
}
inline void SaveSliceInfoDef::clear_full_shape() {
  full_shape_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SaveSliceInfoDef::full_shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.full_shape)
  return full_shape_.Get(index);
}
inline void SaveSliceInfoDef::set_full_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  full_shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.full_shape)
}
inline void SaveSliceInfoDef::add_full_shape(::PROTOBUF_NAMESPACE_ID::int64 value) {
  full_shape_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.SaveSliceInfoDef.full_shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
SaveSliceInfoDef::full_shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.SaveSliceInfoDef.full_shape)
  return full_shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
SaveSliceInfoDef::mutable_full_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SaveSliceInfoDef.full_shape)
  return &full_shape_;
}

// repeated int64 var_offset = 3;
inline int SaveSliceInfoDef::var_offset_size() const {
  return var_offset_.size();
}
inline void SaveSliceInfoDef::clear_var_offset() {
  var_offset_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SaveSliceInfoDef::var_offset(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.var_offset)
  return var_offset_.Get(index);
}
inline void SaveSliceInfoDef::set_var_offset(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  var_offset_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.var_offset)
}
inline void SaveSliceInfoDef::add_var_offset(::PROTOBUF_NAMESPACE_ID::int64 value) {
  var_offset_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.SaveSliceInfoDef.var_offset)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
SaveSliceInfoDef::var_offset() const {
  // @@protoc_insertion_point(field_list:tensorflow.SaveSliceInfoDef.var_offset)
  return var_offset_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
SaveSliceInfoDef::mutable_var_offset() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SaveSliceInfoDef.var_offset)
  return &var_offset_;
}

// repeated int64 var_shape = 4;
inline int SaveSliceInfoDef::var_shape_size() const {
  return var_shape_.size();
}
inline void SaveSliceInfoDef::clear_var_shape() {
  var_shape_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SaveSliceInfoDef::var_shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.var_shape)
  return var_shape_.Get(index);
}
inline void SaveSliceInfoDef::set_var_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  var_shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.var_shape)
}
inline void SaveSliceInfoDef::add_var_shape(::PROTOBUF_NAMESPACE_ID::int64 value) {
  var_shape_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.SaveSliceInfoDef.var_shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
SaveSliceInfoDef::var_shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.SaveSliceInfoDef.var_shape)
  return var_shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
SaveSliceInfoDef::mutable_var_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SaveSliceInfoDef.var_shape)
  return &var_shape_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::VariableSynchronization> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::VariableSynchronization>() {
  return ::tensorflow::VariableSynchronization_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::VariableAggregation> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::VariableAggregation>() {
  return ::tensorflow::VariableAggregation_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fvariable_2eproto
