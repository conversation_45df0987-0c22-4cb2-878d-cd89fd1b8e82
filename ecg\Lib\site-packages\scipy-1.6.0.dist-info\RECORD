scipy-1.6.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scipy-1.6.0.dist-info/LICENSE.txt,sha256=CNTya81BTrkxFzgUx_BGzZbOq6aWm6p2h_8V04N0PYk,47874
scipy-1.6.0.dist-info/LICENSES_bundled.txt,sha256=QFtzzVKCh-6y-0MSe8hdGYWxQrAP5GWBZrHw8HYZx5U,11111
scipy-1.6.0.dist-info/METADATA,sha256=JBokJOHRqhtl8P7jDLHledYC6BYkVRC_Pn65hvOCDvo,2047
scipy-1.6.0.dist-info/RECORD,,
scipy-1.6.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.6.0.dist-info/WHEEL,sha256=jr7ubY0Lkz_yXH9FfFe9PTtLhGOsf62dZkNvTYrJINE,100
scipy-1.6.0.dist-info/top_level.txt,sha256=7wj5qJez-Vf-iL7K4uj9tRbdVCKVxpl7PqdN9UGMfuA,6
scipy/.libs/lib_arpack-.3A4GQ47HFAMPOPL6TOOKCN7VLD23IJMX.gfortran-win_amd64.dll,sha256=YMV7XNoPHKWekyqHNzysUyCe_hXAbpcUu5Ziuxr9rvU,1812493
scipy/.libs/lib_blas_su.E2ZZVNG6YR6T4QZ5JV2VPN3MEZJ3UUED.gfortran-win_amd64.dll,sha256=MTthBv8qYmRejeVAVGKz5HPwOlpwneiQxVi85LQooEA,150393
scipy/.libs/lib_dop-f2p.3HWIZKD75YUXVW7GYVDBE4FCDUO4NUKG.gfortran-win_amd64.dll,sha256=3riu1KzualgrrQmi6yYna5kMBeCA3ogICGObbYuuA_E,743473
scipy/.libs/lib_test_fo.JF5HTWMUPBXWGAYEBVEJU3OZAHTSVKCT.gfortran-win_amd64.dll,sha256=5d90Jn_-nAS4C8z5Ac5G_RgOJTJ0r2eNNfZ3cdqaDus,616262
scipy/.libs/libansari.R6EA3HQP5KZ6TAXU4Y4ZVTRPT7UVA53Z.gfortran-win_amd64.dll,sha256=62DRYyDh-8thUpZmYS2u04DwIverI066rs9EruVE8sk,138469
scipy/.libs/libbanded5x.R6BAIYYCA35YKAXJ7FAYR4IVGECTZQ7T.gfortran-win_amd64.dll,sha256=gipj2Msa9dSaGj7F8e7xY7p4Aa7u7byAAEz9pFRf8Nw,800133
scipy/.libs/libbispeu.7AH3PCQ2E2NGLC3AQD7FFAH73KGJTZCJ.gfortran-win_amd64.dll,sha256=KAj7zpncATX3h2wcXHGyxniJguFKP6OYaDfgGokQ59Q,1982740
scipy/.libs/libblkdta00.DTQDR3OHQ3J2USP7S6AMBOXPGRTFAVYX.gfortran-win_amd64.dll,sha256=IeS6USBCy33tRuEHoRYDAwTC_Zwvo5czBrU-fA_kbDE,790114
scipy/.libs/libchkder.G7WSOGIYYQO3UWFVEZ3PPXCXR53ADVPA.gfortran-win_amd64.dll,sha256=PhrkkT1IH91elM2Q8FcUMC5kGk11_oDSy0esVlaFYhw,294014
scipy/.libs/libcobyla2.25EVUSEBAW7VKISARB7LO3UGZPN2HXE3.gfortran-win_amd64.dll,sha256=WRbOo7qE8IDR9dplUF8Edxa1I-r-HxXGVzI69qgiqRk,768439
scipy/.libs/libd_odr.42NKV3LLJ3CEGTKQZ4ZPPAKB7ZGBNSQK.gfortran-win_amd64.dll,sha256=RjO9X0-jdjoSmIXahiaeXJ-4QrlCGz8JhkITABVftDE,1289117
scipy/.libs/libdcsrch.I2AOPDCXAPDRFNPWY55H5UE7XZSU5CVN.gfortran-win_amd64.dll,sha256=YhL6Jvlpsk8rMVuejDcOwz8W_lKu3WQ0Mdxo9ogMLZM,70258
scipy/.libs/libdet.DG2XNAW3X72BTIUI6M5LVYJKOYX4OWRC.gfortran-win_amd64.dll,sha256=SY08dl0HJSu_pgLpH7DYSHThVw-uNRK7gyWfe5An-h8,88590
scipy/.libs/libdfft.6W33T25OUJ5QS4LJ7RJOY2DK4EDTGCJB.gfortran-win_amd64.dll,sha256=eMAZ-UFeRehpy-CnPhlOdleODor8eB9KqWCIZJePc84,1751241
scipy/.libs/libdfitpack.VPRJUWUEP6U577QBCGTTCCFXB3222T4I.gfortran-win_amd64.dll,sha256=OeSIyd9BsPekZTpk5crbct8joeXZMY4w2Lk76evkDoQ,1986953
scipy/.libs/libdgamln.ISZADEYBRWDOW4SFMQIGKEGWTQ3AWMOF.gfortran-win_amd64.dll,sha256=vyF_BP2XN9n_mHms7TImO246W1py2-Vevwt53Yxk1rA,2718599
scipy/.libs/libdqag.ASJU4DPFRCFGQHUILEYL73HIQFCQW6IB.gfortran-win_amd64.dll,sha256=lPltuJ5iCmZAYvvdvjR9J5du-t4a0l9K4Yio8i-Hfrc,927493
scipy/.libs/libgetbreak.UT6BFT63RCRCLTZJQB5NIW7MUO2XV6GB.gfortran-win_amd64.dll,sha256=hILRNQ4IBsi6ZBz152QjyFQwmeFhcbPNJHGoryQxUYs,373584
scipy/.libs/liblbfgsb.PX342VLHSPSLICIJBXAVTUC5ZDU64A2X.gfortran-win_amd64.dll,sha256=nP-4TvWaqsN3J-nA7blX_YlYrKtajkAHCusYfPf6TI4,816917
scipy/.libs/liblsoda-f2.6RQQYRVPC7JIELFZ4GIC2VDVYI5IPBQ6.gfortran-win_amd64.dll,sha256=47HKXOZ4YgM2bgYYELtvXHtHNwe3jgU_ktjm-MvQuuY,791184
scipy/.libs/libmvndst.RDHNECQ4LTGAG44TTDYZZ5ZBHXLFUU6E.gfortran-win_amd64.dll,sha256=wvBpJNjrZhRTl2ke3mhnIid8rIEbwYQadKUUGII2Tjs,166812
scipy/.libs/libnnls.4HUTGAJQTI623WTX372VAIIWXRLC62YU.gfortran-win_amd64.dll,sha256=4YNp_OO--qKo6UtJvqutWDO697PiZNxZw8QNAm72X1M,638335
scipy/.libs/libopenblas.3HBPCJB5BPQGKWVZAVEBXNNJ2Q2G3TUP.gfortran-win_amd64.dll,sha256=9IwqhLReLlAjTjYzso2afyl1SfVJmlVbArncXa3Svkc,32862022
scipy/.libs/libslsqp_op.RGGN6ZOFD2K47X7YRNDYCM7JFP4AGLER.gfortran-win_amd64.dll,sha256=CG5l4UT0tAcSW80mwHfnj1TwVG5uNRAV7nkOE0l53jw,256535
scipy/.libs/libspecfun.LQCTHMCYNULEOOGKIO6AGREE6D6V37RU.gfortran-win_amd64.dll,sha256=7LyFmGD-NaVj6TtzZgEyJH8T-vnZLccBRAi3clzDtqY,1402857
scipy/.libs/libvode-f2p.V4OHW5TWZHBLK5SKC7W2P4NPONB4VEGU.gfortran-win_amd64.dll,sha256=a437Cc0GfJZ3szIbz32mJlgfbrd3cyvLJ9EI6uIC0g0,1018595
scipy/.libs/libwrap_dum.3IBXJNMLL6YZ5KSFGKX5YPBXBGEMZCKZ.gfortran-win_amd64.dll,sha256=w7KuDVMExxiVdPgbA6isPvS7r5e8Mi5ACUa72xFLHEI,67361
scipy/.libs/libwrap_dum.RJ7W7NSUU3XSBUNAI2DGMULQZWRNSGOP.gfortran-win_amd64.dll,sha256=IRMh1CmH0sPx03gHNs5fl5U5vYy4RsNPbwz-fmw6U5Y,173254
scipy/.libs/msvcp140.dll,sha256=UXzTqsIXejV8ymAy8HrXNg7oyiEqAt1uEwG_bPreIJQ,633152
scipy/HACKING.rst.txt,sha256=Edt9p4pDWU7wvQUHONToV9GCZQwPx9WMTi7-_JegSBk,14420
scipy/INSTALL.rst.txt,sha256=OXJgscZSerQrMiP0doEepJdFjg34bjUM8hPlgqmyPoA,7069
scipy/LICENSE.txt,sha256=CNTya81BTrkxFzgUx_BGzZbOq6aWm6p2h_8V04N0PYk,47874
scipy/LICENSES_bundled.txt,sha256=QFtzzVKCh-6y-0MSe8hdGYWxQrAP5GWBZrHw8HYZx5U,11111
scipy/__config__.py,sha256=zMhKXacfKg-C-UAbuXJi6gXyBr3Z3q-Fi7XXy1aMSTw,3048
scipy/__init__.py,sha256=9JsxSpO4SnRnEU2fa-1bI-Cf_iUljvT6H4ly5p2ufqI,5371
scipy/__pycache__/__config__.cpython-39.pyc,,
scipy/__pycache__/__init__.cpython-39.pyc,,
scipy/__pycache__/_distributor_init.cpython-39.pyc,,
scipy/__pycache__/conftest.cpython-39.pyc,,
scipy/__pycache__/setup.cpython-39.pyc,,
scipy/__pycache__/version.cpython-39.pyc,,
scipy/_build_utils/__init__.py,sha256=35uXO-X6DY8aR21ZmmEg4ZzxQUDdPUYgExXUYhzzDv8,710
scipy/_build_utils/__pycache__/__init__.cpython-39.pyc,,
scipy/_build_utils/__pycache__/_fortran.cpython-39.pyc,,
scipy/_build_utils/__pycache__/compiler_helper.cpython-39.pyc,,
scipy/_build_utils/__pycache__/setup.cpython-39.pyc,,
scipy/_build_utils/__pycache__/system_info.cpython-39.pyc,,
scipy/_build_utils/_fortran.py,sha256=IU_8OGxDKQNoJopbAXC9QAtuBqWg01G447kSZz_ChzA,14990
scipy/_build_utils/compiler_helper.py,sha256=SWaBsGOwzhHjGKhl8A1lL40ytMRg7jkFcDle8YxhgsU,4030
scipy/_build_utils/setup.py,sha256=7EusTwHZ2f5NE2Vo7eeo3TYc2S3ey_mLY2Okvc05JqA,351
scipy/_build_utils/system_info.py,sha256=kWZdClFeWahqeLUqYuNQNfYrOenI499eQoLAVFY7VgU,7153
scipy/_build_utils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_build_utils/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/_build_utils/tests/__pycache__/test_scipy_version.cpython-39.pyc,,
scipy/_build_utils/tests/test_scipy_version.py,sha256=seG6IZ4nMJprDw5UIS4boXz_xGKD3n0DuxQWdbrsGn0,604
scipy/_distributor_init.py,sha256=1MOfMZkDYHCX0V9Xns9IQtUtLbc0RBsqUfX-B6_tEjQ,2847
scipy/_lib/__init__.py,sha256=CXrH_YBpZ-HImHHrqXIhQt_vevp4P5NXClp7hnFMVLM,353
scipy/_lib/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/__pycache__/_bunch.cpython-39.pyc,,
scipy/_lib/__pycache__/_ccallback.cpython-39.pyc,,
scipy/_lib/__pycache__/_disjoint_set.cpython-39.pyc,,
scipy/_lib/__pycache__/_gcutils.cpython-39.pyc,,
scipy/_lib/__pycache__/_pep440.cpython-39.pyc,,
scipy/_lib/__pycache__/_testutils.cpython-39.pyc,,
scipy/_lib/__pycache__/_threadsafety.cpython-39.pyc,,
scipy/_lib/__pycache__/_tmpdirs.cpython-39.pyc,,
scipy/_lib/__pycache__/_util.cpython-39.pyc,,
scipy/_lib/__pycache__/decorator.cpython-39.pyc,,
scipy/_lib/__pycache__/deprecation.cpython-39.pyc,,
scipy/_lib/__pycache__/doccer.cpython-39.pyc,,
scipy/_lib/__pycache__/setup.cpython-39.pyc,,
scipy/_lib/__pycache__/uarray.cpython-39.pyc,,
scipy/_lib/_bunch.py,sha256=n1ibV-G3hFkMTkk5eZr40-VqMHIVtsT5vK1gmSW_2AQ,8162
scipy/_lib/_ccallback.py,sha256=-L8c-c8mps6kJifsx6DTmyr1RCl6Y3-3N8ViME29KLE,6221
scipy/_lib/_ccallback_c.cp39-win_amd64.pyd,sha256=lnUkBaOvG3BBK2QVBiUM8oSyH5enHNAQ9--re1CniI8,61952
scipy/_lib/_disjoint_set.py,sha256=1oQTcepXNfyVd4gPRfBvk0PJf6WKW0MSpu7Cwr4q6Mo,5582
scipy/_lib/_fpumode.cp39-win_amd64.pyd,sha256=Iu9sYgsH6wSs543bQrsuOGPxSbL6xxuGHWqpUcMYm9Y,10752
scipy/_lib/_gcutils.py,sha256=oC_Y4DDhzN1ZM4uBj5Sz6VvshAwVPc1oqChmq4J1bHs,2685
scipy/_lib/_pep440.py,sha256=0-FpblEJgX6r4Wu9tACEOW-lesf0si14oLzp8k5VgEo,14093
scipy/_lib/_test_ccallback.cp39-win_amd64.pyd,sha256=abeF4jq3gKhhry1tpE9tftVJs6oosKW0zfDuY2KsZWQ,18944
scipy/_lib/_test_deprecation_call.cp39-win_amd64.pyd,sha256=FkOYEcKxCFbotcCeutuN9ZxCD84MSv8iYTrBdrJWtnQ,21504
scipy/_lib/_test_deprecation_def.cp39-win_amd64.pyd,sha256=ZfOv5HPk51199-QbhDUROE6RG1sxHHhSO1lm6jgrS_I,23040
scipy/_lib/_testutils.py,sha256=Oznl9D1kRU0eNFaIe2L8pQ1pnxYaKt7iLH5H7m2okdo,4024
scipy/_lib/_threadsafety.py,sha256=8Qo2y3ArcLvdMPzRy5g3XxIpeGOBkH5DQBDZSLOKVzE,1463
scipy/_lib/_tmpdirs.py,sha256=z3IYpzACnWdN_BMjOvqYbkTvYyUbfbQvfehq7idENSo,2374
scipy/_lib/_uarray/LICENSE,sha256=yAw5tfzga6SJfhTgsKiLVEWDNNlR6xNhQC_60s-4Y7Q,1514
scipy/_lib/_uarray/__init__.py,sha256=ydfqR6cMzlrJ6kDAEBbAUIpQywjfdP4JJ4ugwNZZEjQ,4500
scipy/_lib/_uarray/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/_uarray/__pycache__/_backend.cpython-39.pyc,,
scipy/_lib/_uarray/__pycache__/setup.cpython-39.pyc,,
scipy/_lib/_uarray/_backend.py,sha256=bPMgRMMH0RYqNKSjCfpSy2GrLIq8ABaFu5ffT5JYIEM,11676
scipy/_lib/_uarray/_uarray.cp39-win_amd64.pyd,sha256=2I8fSrYsqF1IU85Rh4XRtSctLKud1DBhsRc9YCnWkzY,53760
scipy/_lib/_uarray/setup.py,sha256=ZQzpArtSwwvfjwNWTCmAckxH8swz1Xwp8UYesRS0QoU,915
scipy/_lib/_util.py,sha256=a2UGE5aD1Ppb160YWWi2B9y5hXkrLGcbjuIIJA91g3Q,16518
scipy/_lib/decorator.py,sha256=uUQqVuZEk8Zh6xpQ-E7wyicTAsccInxS0tXQ0YwbzgU,15067
scipy/_lib/deprecation.py,sha256=neuUwrHVCjmQIKi3iTIXlB4_V1giQg0r3cFl556kqes,3176
scipy/_lib/doccer.py,sha256=Ex-LtLN2_BKSX3hpyh6GdqGBvIil-hsQ5piH1hDLovE,8305
scipy/_lib/messagestream.cp39-win_amd64.pyd,sha256=FYV8mbnKGFsatlpLQT_M0W68qCmgO0-PsvIKvhP85QA,40448
scipy/_lib/setup.py,sha256=gdamXyV0q2M6hN3MYdbKyscngMNX6genvSh2GlWGUwg,2196
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test__gcutils.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test__pep440.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test__testutils.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test__threadsafety.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test__util.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_bunch.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_ccallback.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_deprecation.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_import_cycles.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_linear_assignment.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_tmpdirs.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_warnings.cpython-39.pyc,,
scipy/_lib/tests/test__gcutils.py,sha256=QaH4FTPYUOydiPDFlPIfh-Y5QVP7oogy1fZXPPfZMXg,3448
scipy/_lib/tests/test__pep440.py,sha256=u9hPoolK4AoIIS-Rq74Du5SJu5og2RxMwgaAvGgWvRo,2277
scipy/_lib/tests/test__testutils.py,sha256=P4WDJpUgy19wD9tknQSjIivuQvZF7YUBGSBWlur2QRA,800
scipy/_lib/tests/test__threadsafety.py,sha256=qSfCF5OG_5lbnSl-grmDN_QCU4QLe-fS3sqnwL04pf8,1322
scipy/_lib/tests/test__util.py,sha256=k6BXFmmzRG6Tjw7ZE7sXHb-JdO4bOsdBL2_MN5hNaK8,7937
scipy/_lib/tests/test_bunch.py,sha256=5qWRzh9tn_X_W-yOe0BmMEnGvnifyA-fTPFAqs0_XY4,6168
scipy/_lib/tests/test_ccallback.py,sha256=mvo9OeGktIqO-vfLLU1FPAfFwxPzX0wcYh_Lnwby7ik,5995
scipy/_lib/tests/test_deprecation.py,sha256=a_3r_9pFx1sxJXeFgiTSV9DXYnktc4fio1hR0ITPywA,364
scipy/_lib/tests/test_import_cycles.py,sha256=ZBZJCR2fM-6Nnd6SnqsoXxNAmPBKfEJiJ7eyuW9zRps,1276
scipy/_lib/tests/test_linear_assignment.py,sha256=eaO6p4JJYIYcoMyVM-I4zD3fixEB_8aHB8tfFMWzfPI,3420
scipy/_lib/tests/test_tmpdirs.py,sha256=jusM--qpUMscMAdbgNGkmCU23UGhytuqZM1gX76oWcE,1242
scipy/_lib/tests/test_warnings.py,sha256=BM7LzwH4p2KqbClqdBNVUn7iyHLBXRju4Dy052mtlSU,3761
scipy/_lib/uarray.py,sha256=22Madi1qOUUz9sHyDVcYmkSQEs8_4wNwRJlMqZke26A,773
scipy/cluster/__init__.py,sha256=Sco_jwP4eqTtUfElVvmub0n5Ue75puxGtCXYIbt7ZKI,871
scipy/cluster/__pycache__/__init__.cpython-39.pyc,,
scipy/cluster/__pycache__/hierarchy.cpython-39.pyc,,
scipy/cluster/__pycache__/setup.cpython-39.pyc,,
scipy/cluster/__pycache__/vq.cpython-39.pyc,,
scipy/cluster/_hierarchy.cp39-win_amd64.pyd,sha256=KJHGEKWeuiOnYQj4HuCBY8Ny1KRTTb6F90hOLgFmv7k,292352
scipy/cluster/_optimal_leaf_ordering.cp39-win_amd64.pyd,sha256=v0HnHG4YhmS4Jw50kpGYGMBCWMiZtGtpVMrN9f0ffRw,182784
scipy/cluster/_vq.cp39-win_amd64.pyd,sha256=3aI8X_E6E9y8hffPD3mztZ8-zxIYRUfxJL3AGGm0W38,79360
scipy/cluster/hierarchy.py,sha256=lz3A8ffl6ovE1CJtsV9-pvgeQgVCYXRuh87ey-km-AU,148018
scipy/cluster/setup.py,sha256=_7dlbk3ac89-fbgDB0tA1P9D95fU5bNrDSe0MC96oeM,797
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/cluster/tests/__pycache__/hierarchy_test_data.cpython-39.pyc,,
scipy/cluster/tests/__pycache__/test_disjoint_set.cpython-39.pyc,,
scipy/cluster/tests/__pycache__/test_hierarchy.cpython-39.pyc,,
scipy/cluster/tests/__pycache__/test_vq.cpython-39.pyc,,
scipy/cluster/tests/hierarchy_test_data.py,sha256=7syUYdIaDVr7hgvMliX0CW4386utjBJn1DOgX0USXls,6850
scipy/cluster/tests/test_disjoint_set.py,sha256=vpp0jubnol1S0hL60ufUwldzQjXhrKkJgXAAIgInumU,5474
scipy/cluster/tests/test_hierarchy.py,sha256=65eP-YS6pnjjAZYuAiVbC5v2nx6AKLpEVfde-BnZW6o,42687
scipy/cluster/tests/test_vq.py,sha256=ZcdlA9oi2TyiHvpSZTIl9HmgIDTnKDh_JGHA_ZREoJU,12417
scipy/cluster/vq.py,sha256=E0Az4fRjpA7jKs92Y_7b6clBzUF3786J9MxM40faBPU,27206
scipy/conftest.py,sha256=3JEHSN4-w98aV0RFhjgfId-ITCSXepmxJ-tJ-39GJWk,1723
scipy/constants/__init__.py,sha256=oWMVvAJ91N9264QUBCQ5uiSeGwqz6JRRyY5Q6xaxiDM,12116
scipy/constants/__pycache__/__init__.cpython-39.pyc,,
scipy/constants/__pycache__/codata.cpython-39.pyc,,
scipy/constants/__pycache__/constants.cpython-39.pyc,,
scipy/constants/__pycache__/setup.cpython-39.pyc,,
scipy/constants/codata.py,sha256=jjozmXJplWC8G0yiIvFSUr_Fq_ZhM8yl5pu1QoPSuSA,155762
scipy/constants/constants.py,sha256=Y2SAY2_wMBX59EDQWRn06UGdeyrPyqaA9HJIHXPWrB0,8219
scipy/constants/setup.py,sha256=h6PFNLpUXY9o5ma6cAoW_veRyx7bga1AxyL4S803CwY,348
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/constants/tests/__pycache__/test_codata.cpython-39.pyc,,
scipy/constants/tests/__pycache__/test_constants.cpython-39.pyc,,
scipy/constants/tests/test_codata.py,sha256=dJssAs0xasSF4Vso8qNp59LeZXw2-_kGm0NVEGiNMF8,1990
scipy/constants/tests/test_constants.py,sha256=PY1oy6bbM2zoPAPgUeBqVThnVRuu4lBt_uMmxm7Ct38,1632
scipy/fft/__init__.py,sha256=w3lXatwC-YwTUpwf0uhpz04fYg9R-jWBzdDF7JgrJg0,3237
scipy/fft/__pycache__/__init__.cpython-39.pyc,,
scipy/fft/__pycache__/_backend.cpython-39.pyc,,
scipy/fft/__pycache__/_basic.cpython-39.pyc,,
scipy/fft/__pycache__/_debug_backends.cpython-39.pyc,,
scipy/fft/__pycache__/_helper.cpython-39.pyc,,
scipy/fft/__pycache__/_realtransforms.cpython-39.pyc,,
scipy/fft/__pycache__/setup.cpython-39.pyc,,
scipy/fft/_backend.py,sha256=Oeh7J1G3RDiG9rbRfSkLOfeYzw9fQWizyZSsacq9olM,5785
scipy/fft/_basic.py,sha256=Mr2p2_QCUDqxBpCjM40UtQ-E6Q3lDea6LZr8673-R7Q,62596
scipy/fft/_debug_backends.py,sha256=RlvyunZNqaDDsI3-I6QH6GSBz_faT6EN4OONWsvMtR8,598
scipy/fft/_helper.py,sha256=yovIlq9IZ6CzWBy7lk6tWIeDPnn6Qfj7BVs0-o6cxQA,3347
scipy/fft/_pocketfft/LICENSE.md,sha256=wlSytf0wrjyJ02ugYXMFY7l2D8oE8bdGobLDFX2ix4k,1498
scipy/fft/_pocketfft/__init__.py,sha256=dROVDi9kRvkbSdynd3L09tp9_exzQ4QqG3xnNx78JeU,207
scipy/fft/_pocketfft/__pycache__/__init__.cpython-39.pyc,,
scipy/fft/_pocketfft/__pycache__/basic.cpython-39.pyc,,
scipy/fft/_pocketfft/__pycache__/helper.cpython-39.pyc,,
scipy/fft/_pocketfft/__pycache__/realtransforms.cpython-39.pyc,,
scipy/fft/_pocketfft/__pycache__/setup.cpython-39.pyc,,
scipy/fft/_pocketfft/basic.py,sha256=Wn-qvc2r1lfrU1df-rBVjhq1m0kGssOGCQWrGB2etG0,9845
scipy/fft/_pocketfft/helper.py,sha256=dlr4uOTGjDEtvC7hwjDnZIx7J1grxwFnKjE4yJ3CQHQ,5654
scipy/fft/_pocketfft/pypocketfft.cp39-win_amd64.pyd,sha256=FDB2-oaSSJP7DfAUGJ6v581H19j6miZAjs3kZchPqZs,558080
scipy/fft/_pocketfft/realtransforms.py,sha256=kWamCYbxJhjoiqh8gsGs6odioT2tZPssulqAAgCm5Wk,3305
scipy/fft/_pocketfft/setup.py,sha256=pDu_fngCu_NhDXP79-Kp_MHQBs1F3nRPizF__a4OMcY,1820
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_basic.cpython-39.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_real_transforms.cpython-39.pyc,,
scipy/fft/_pocketfft/tests/test_basic.py,sha256=CbqyIcNOB1D3tp_ibjGLe-UiwS43tEJ77ZBWls4uXU8,35830
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=qatlJVdjeGafCKXMJ1v1BOYmaC8FEQ5-PjrK2Am0V4E,16257
scipy/fft/_realtransforms.py,sha256=jW1aHX6nzsEK8qbe5q0KyOsxGdC2BophNrlkwJ77T4E,20891
scipy/fft/setup.py,sha256=9jGCIYE81HbeH1Dw7tpADjkOCYiYfWWzR4qydNnc4N0,382
scipy/fft/tests/__pycache__/mock_backend.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_backend.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_fft_function.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_helper.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_multithreading.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_numpy.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_real_transforms.cpython-39.pyc,,
scipy/fft/tests/mock_backend.py,sha256=wYsKQ4vAbVANgvX08XwiGFZiCPtJSqsskFEqj3DQ_SM,1683
scipy/fft/tests/test_backend.py,sha256=O9L2QFHA5MdNHUeHrVTigBnCI4eyxE1NWsqhX4nsva8,3841
scipy/fft/tests/test_fft_function.py,sha256=NTCRYpIE_9M6ylc-EcUVPWmCvoZTtiDedUqHo4lkWLk,1147
scipy/fft/tests/test_helper.py,sha256=a0ZQ-1x5Tq120k6c_GixiwtYVelB8SMzxA0TiRZAxE0,9846
scipy/fft/tests/test_multithreading.py,sha256=Ub0qD3_iSApPT9E71i0dvKnsKrctLiwMq95y3370POE,2132
scipy/fft/tests/test_numpy.py,sha256=usIV9fN3JIhQCk3dzvRQvQmhWPgg0r4_nOgVSFtSrIE,14464
scipy/fft/tests/test_real_transforms.py,sha256=j_k0uHvJRUEj4aN9QG8EalidfAZSuPZ9jhAwB7Xmjis,5186
scipy/fftpack/__init__.py,sha256=gx--1f8mRsmYX6xiLrpKgyAzi4FDZa9m-LJ4P36DvUk,3088
scipy/fftpack/__pycache__/__init__.cpython-39.pyc,,
scipy/fftpack/__pycache__/basic.cpython-39.pyc,,
scipy/fftpack/__pycache__/helper.cpython-39.pyc,,
scipy/fftpack/__pycache__/pseudo_diffs.cpython-39.pyc,,
scipy/fftpack/__pycache__/realtransforms.cpython-39.pyc,,
scipy/fftpack/__pycache__/setup.cpython-39.pyc,,
scipy/fftpack/basic.py,sha256=BuIdy8sie07Fgd7E4F-uC8DkOeIjM7nOGuWECrHWP4Q,12988
scipy/fftpack/convolve.cp39-win_amd64.pyd,sha256=ht_YfE_9eKdpzvEOrPQKsI1yRYfcSTejLxFHwrm_vzc,158208
scipy/fftpack/helper.py,sha256=IGxe23AzCNO8cfBobXfWc3WqaEoY61DAkL_02nhzO3U,3249
scipy/fftpack/pseudo_diffs.py,sha256=eCln0ZImNYr-wUWpOZ-SmKKIbhJsV8VBLmwT_C79RsQ,14200
scipy/fftpack/realtransforms.py,sha256=Aucz6vlCODRvSItBsyCOfeDqR1K6WyGLvCSVKREmkfU,18875
scipy/fftpack/setup.py,sha256=3_oa0OZEJUDyTV9A3lW8ucxfR8SxfzkJuC6y9Gfb1-w,449
scipy/fftpack/tests/Makefile,sha256=T1A-tl_FYYCxzSjdza6E0g8ByRAEudtInKdk0SVDI50,214
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/gen_fftw_ref.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/gendata.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/test_basic.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/test_helper.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/test_import.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/test_pseudo_diffs.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/test_real_transforms.cpython-39.pyc,,
scipy/fftpack/tests/fftw_dct.c,sha256=B-sCN3rbx_-OyUKNirswOGFnfyqBh4WCbVMD7lE6-YM,3863
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/gen_fftw_ref.py,sha256=l4ZGMv7otbbaLqGTCs6oj8a7d8lZ1lNZJAYGTINbxu8,1923
scipy/fftpack/tests/gendata.m,sha256=Bicu6oaHljL9WJt7OlLa00RxDtIAGcXOcQmfyks73VA,432
scipy/fftpack/tests/gendata.py,sha256=7_GuXK5eWP9rzCivlGJggL6ussYGMtZNsEPvPU-Ji00,163
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=bCEB8CllD1m6Nf4GbXabx8zMYo8v2CC_lso-u2EiQ3U,29868
scipy/fftpack/tests/test_helper.py,sha256=boWIzE8RzAdTkJczz9L0FLshKB4eMj4BXrKASr1tuLM,1699
scipy/fftpack/tests/test_import.py,sha256=TEccjIRhn0ytshwKFJ8f_NWe5su-7JTBaTbIEFZsk2I,1137
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=BzCmPQB2hjtkQ701SOguPDynwL-mmHMM9ZPWNVKMrEI,13445
scipy/fftpack/tests/test_real_transforms.py,sha256=NjJahVtaaCUtCq9RAsBxQ4Cvk7xP5zqKbc_QTdsdZuw,24109
scipy/integrate/__init__.py,sha256=MVqAD8evt5k__ttMMtZ07_ps-QYAyq7jgrTdqKxdEOw,3947
scipy/integrate/__pycache__/__init__.cpython-39.pyc,,
scipy/integrate/__pycache__/_bvp.cpython-39.pyc,,
scipy/integrate/__pycache__/_ode.cpython-39.pyc,,
scipy/integrate/__pycache__/_quad_vec.cpython-39.pyc,,
scipy/integrate/__pycache__/_quadrature.cpython-39.pyc,,
scipy/integrate/__pycache__/odepack.cpython-39.pyc,,
scipy/integrate/__pycache__/quadpack.cpython-39.pyc,,
scipy/integrate/__pycache__/setup.cpython-39.pyc,,
scipy/integrate/_bvp.py,sha256=yKRTwQfcFAKvkK8E06OzTMz084FFsuzmuVgYpQyBE7g,41051
scipy/integrate/_dop.cp39-win_amd64.pyd,sha256=0KKDylceiHws_Ub4g7tKtQEjH5jI_XvmBKDKtraS6_Y,47616
scipy/integrate/_ivp/__init__.py,sha256=gKFR_pPjr8fRLgAGY5sOzYKGUFu2nGX8x1RrXT-GZZc,256
scipy/integrate/_ivp/__pycache__/__init__.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/base.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/bdf.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/common.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/dop853_coefficients.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/ivp.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/lsoda.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/radau.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/rk.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/setup.cpython-39.pyc,,
scipy/integrate/_ivp/base.py,sha256=ZIieJBQnwwmNgjyvrnPpmIa1S5V8mJwwUzcASbeOwvU,9591
scipy/integrate/_ivp/bdf.py,sha256=Es8zgBiWttgJxDc7jvT0qGyg3_Oz84OsWm_KFm2Gevw,16890
scipy/integrate/_ivp/common.py,sha256=zlKdiIcehn4qoE5VcxASaVwOKS_vwWxUV4DRH_CGrtw,14702
scipy/integrate/_ivp/dop853_coefficients.py,sha256=OrYvW0Hu6X7sOh37FU58gNkgC77KVpYclewv_ARGMAE,7237
scipy/integrate/_ivp/ivp.py,sha256=lW7xqpBtXESVpOZn0cibZT_c6oR0QLcleUoONFI2bHg,27556
scipy/integrate/_ivp/lsoda.py,sha256=ZgOKN_iR9HW_ec6sLN9Soe5axGf7V3fkSEA-cLsNL34,8022
scipy/integrate/_ivp/radau.py,sha256=U-pRaGop47pgAQ33domgUthsm70-HqO6Q_HQSJWLsMw,19093
scipy/integrate/_ivp/rk.py,sha256=1sAMKTH5TWavpj9PCb6NmOo6C4pInY-uWEdnCN_6YSc,21378
scipy/integrate/_ivp/setup.py,sha256=zEcz_LuwENdC5avpxPD9xhCIcOnS1y6wem3tBvYUCGs,344
scipy/integrate/_ivp/tests/__pycache__/test_ivp.cpython-39.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_rk.cpython-39.pyc,,
scipy/integrate/_ivp/tests/test_ivp.py,sha256=pMMq5sPVXWzybz0djyQ9VByBc3e66rhqqGIABXnpTqE,32797
scipy/integrate/_ivp/tests/test_rk.py,sha256=K9UxZghBzSL2BzmgLndPJcWOWV4Nr530TGKWakpsoeM,1326
scipy/integrate/_ode.py,sha256=EXwndaBrnh8FXGBqNqM5dvezyAEOPlakJYwZ3eCwucI,48163
scipy/integrate/_odepack.cp39-win_amd64.pyd,sha256=dr-n52y9MADT2G6MTIRakGuW9N9P7SHGVCGYK3z9aK4,23040
scipy/integrate/_quad_vec.py,sha256=2IMuU8_6DikpPos3gNXpxB61SeNpArxh6MH6tohv5M8,20728
scipy/integrate/_quadpack.cp39-win_amd64.pyd,sha256=mP8fKE6s6dOzmopZZllZ4mJdnBK6ACmHVYs2Hb76ABc,33792
scipy/integrate/_quadrature.py,sha256=irEAJD32q55RUlbHjguEny_631JgMjDMoIK7BLGCXQI,32783
scipy/integrate/_test_multivariate.cp39-win_amd64.pyd,sha256=oUWm0omISuq64kxnR1KDALvoHwhqrSlKH_-SnBa9HTo,11776
scipy/integrate/_test_odeint_banded.cp39-win_amd64.pyd,sha256=Dqu8u6em1x2FbHWfK9J93S7lI5Gxdwww7Vt7LYiyrLc,41472
scipy/integrate/lsoda.cp39-win_amd64.pyd,sha256=J-pdjUPBP0nekbhLcI9SKRDkOiBQXlcMDOytBZTiJ0A,41472
scipy/integrate/odepack.py,sha256=J5kKveujEu348Im7JnTe5CWIUu-FYrYi99xr9cyib0s,10740
scipy/integrate/quadpack.py,sha256=eV7E3n8iXBxaFtjUGNYm5g9EyavAVNLQLkt-2rBQwxU,37367
scipy/integrate/setup.py,sha256=sptU19EXflDXHa6Sq0JrpSKfwHCwj7hl6o0ND4_J80k,4441
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test__quad_vec.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_banded_ode_solvers.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_bvp.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_integrate.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_odeint_jac.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_quadpack.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_quadrature.cpython-39.pyc,,
scipy/integrate/tests/_test_multivariate.c,sha256=WErONRd_TjR_LU47H9sbeypB64f0UinfkNb-L-bRRjo,2076
scipy/integrate/tests/banded5x5.f,sha256=MAFTUt9QraZ_sYyEXu64YsVAYNvGaGp5palgPdbJFLU,6668
scipy/integrate/tests/test__quad_vec.py,sha256=YgNbgGqiQk7URvopuGT_l3llOMCSPdwKtZ5tiIkmBuo,5451
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=kJWirYckJ7k4tfweg1ds-Tozp3GEhxTbuXfgSdeJw7k,6687
scipy/integrate/tests/test_bvp.py,sha256=ZgtHyZ2yhQMmrVefEAcBlmoMnzMKEz1eZ9D7W3zA40M,20159
scipy/integrate/tests/test_integrate.py,sha256=4fyFrtCpeTWb4sEsJ3PVQ5SzYt0_M7v1G_1lWdiOD6E,24375
scipy/integrate/tests/test_odeint_jac.py,sha256=VW63bDRP3uOg6uzm-3787qJl-UQ5Wsht3Ttc6YRybnE,1820
scipy/integrate/tests/test_quadpack.py,sha256=d3sdU27SunU_uwX2jpb8-ofMhCPZPKThET_frWARHE0,13819
scipy/integrate/tests/test_quadrature.py,sha256=_lilwPAe_3a1gWW120dY1tutTPN_qyypWrb0zW9RYnw,9866
scipy/integrate/vode.cp39-win_amd64.pyd,sha256=3KiX_iGsOdfojIHWcA8jFhgAXuntJ-Da7nbZ1wb2VxA,51712
scipy/interpolate/__init__.py,sha256=ito0BvfDTMlG1LoZHNgbKRF6MscGexuB3mtyYnfIs1g,3261
scipy/interpolate/__pycache__/__init__.cpython-39.pyc,,
scipy/interpolate/__pycache__/_bsplines.cpython-39.pyc,,
scipy/interpolate/__pycache__/_cubic.cpython-39.pyc,,
scipy/interpolate/__pycache__/_fitpack_impl.cpython-39.pyc,,
scipy/interpolate/__pycache__/_pade.cpython-39.pyc,,
scipy/interpolate/__pycache__/fitpack.cpython-39.pyc,,
scipy/interpolate/__pycache__/fitpack2.cpython-39.pyc,,
scipy/interpolate/__pycache__/interpnd_info.cpython-39.pyc,,
scipy/interpolate/__pycache__/interpolate.cpython-39.pyc,,
scipy/interpolate/__pycache__/ndgriddata.cpython-39.pyc,,
scipy/interpolate/__pycache__/polyint.cpython-39.pyc,,
scipy/interpolate/__pycache__/rbf.cpython-39.pyc,,
scipy/interpolate/__pycache__/setup.cpython-39.pyc,,
scipy/interpolate/_bspl.cp39-win_amd64.pyd,sha256=Sw2wUtLq4A_J10SDdn_65RlVB_T2zBLJXwyvvXYn3cw,212992
scipy/interpolate/_bsplines.py,sha256=SYo0mtnnpCx0_jyft-J61m1KcfX_cBPmqlyjTwuKKwg,34564
scipy/interpolate/_cubic.py,sha256=BTpX9IXN95DJknJCcXoBshXQxq78_W84A-jvUrTzRJQ,33233
scipy/interpolate/_fitpack.cp39-win_amd64.pyd,sha256=_wUDxmTCIjDgI63hdDQyg6HtLnsDEg2ej3wn09Y9Dvw,37888
scipy/interpolate/_fitpack_impl.py,sha256=jBzXbaHMvHPlt2kC1vk7_HzkmBUclMoJaNk8rtg_3OM,46842
scipy/interpolate/_pade.py,sha256=N6uqJcgqK_W_meETM6Jw4bYHLvelxiY5gtfcWkjmBZQ,1798
scipy/interpolate/_ppoly.cp39-win_amd64.pyd,sha256=iXBBmyPW_oX1tEaZrWkPrnfJjlPtc7LwdktmvAY6CHo,276480
scipy/interpolate/dfitpack.cp39-win_amd64.pyd,sha256=gO4T-_-mbMtGypQPbkP51DQ0NBY4YFEZUrZS45aRIHg,149504
scipy/interpolate/fitpack.py,sha256=pDUirnc4g9jnMj_WH0BdlLRJxQQJJexgDOhFk5D8r0Q,26807
scipy/interpolate/fitpack2.py,sha256=HZN3Tv4zQ_25EL-HlRWyPLIkFKAakcxu5XQip0dJdm4,73106
scipy/interpolate/interpnd.cp39-win_amd64.pyd,sha256=lA6IOYncnBf1YZ7dmElxOUkA2mJvcPOPyLA0AcAgncI,270848
scipy/interpolate/interpnd_info.py,sha256=HrtWWYsL5bPpRwAU6fZLr14pQYZfpNNBNDiEAC4si-w,893
scipy/interpolate/interpolate.py,sha256=iIDLm_gSUxZaR148D83dFo7bz91H_ytaKEW1lykICpQ,99288
scipy/interpolate/ndgriddata.py,sha256=NQn36EYkqg78Q8F4bo3J2Jwtw3sCd7hdSAkCfTkmrAo,8940
scipy/interpolate/polyint.py,sha256=z6648u5QsSX1hfmTYk51d4fioELwqFGjXyZLqgXeGuI,24681
scipy/interpolate/rbf.py,sha256=KHFQcKhA7LR32hgyW8a13t9B3j9njm6tPfWgTR05iHg,11446
scipy/interpolate/setup.py,sha256=Coe_4cD9JUkOFpEp49_ut68tYuLx7TEcuSUXUJWgEnY,2188
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_bsplines.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack2.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_gil.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_interpnd.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_interpolate.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_ndgriddata.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_pade.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_polyint.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_rbf.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_regression.cpython-39.pyc,,
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/test_bsplines.py,sha256=au2seJqj1XDxsihvF6IvE2R5ZFZY575XiCMRUoVRgi4,43800
scipy/interpolate/tests/test_fitpack.py,sha256=cnsKiowWYFiH_PZ6N4Kr65vUAxgnkFnRrkiPyAyRq_M,16204
scipy/interpolate/tests/test_fitpack2.py,sha256=kiiKTlRqQpvVgKrqHuGQFlqJkYJgo1JRZHOF-A3cB6g,46069
scipy/interpolate/tests/test_gil.py,sha256=pONn82voyueDzXlqHvZV8jPd9IGEnCahdhA9GSNyu9w,1882
scipy/interpolate/tests/test_interpnd.py,sha256=_rt3ucB4L-olb8ijdUR_owM28fGBZmTCjS0GHo61img,13650
scipy/interpolate/tests/test_interpolate.py,sha256=_6KDo_v8hjboX66HdvB4ZtD8LCOMASjGZnRXcXG7spQ,108969
scipy/interpolate/tests/test_ndgriddata.py,sha256=sUDuiDA42Ll2PDdZnRIDGX_mDwYn6zDal0l14CyVU5I,7578
scipy/interpolate/tests/test_pade.py,sha256=x5VyACjEgqIsz5e5vIOoCaIVb-ToZsFw6baxLQjRFZQ,3786
scipy/interpolate/tests/test_polyint.py,sha256=KlxZT8KcR6XjZ-rFpqFnI9-dBYyb-MwuKbTaI3Y4qaE,26780
scipy/interpolate/tests/test_rbf.py,sha256=jE1Lyum9fLzgtg10_BVifECuRKqWc1QUOIpkP9iUO5E,6546
scipy/interpolate/tests/test_regression.py,sha256=gmQfpGgU8FrDbSMK8eFIMbjjlokvzop5GDFOmWiYhiQ,418
scipy/io/__init__.py,sha256=UNZD3iqcKFtw-qadV-DdpQObqIV4Oq8XraqiTtPTCMw,2570
scipy/io/__pycache__/__init__.cpython-39.pyc,,
scipy/io/__pycache__/_fortran.cpython-39.pyc,,
scipy/io/__pycache__/idl.cpython-39.pyc,,
scipy/io/__pycache__/mmio.cpython-39.pyc,,
scipy/io/__pycache__/netcdf.cpython-39.pyc,,
scipy/io/__pycache__/setup.cpython-39.pyc,,
scipy/io/__pycache__/wavfile.cpython-39.pyc,,
scipy/io/_fortran.py,sha256=0BZBmd7x8QponBtBWLPMADAu-QDDzKAJQLMrZKExELk,10875
scipy/io/_test_fortran.cp39-win_amd64.pyd,sha256=2DCWwOrQC6RLhDevR1aEeYC7KyY07SHl8Fm8owoOj50,35840
scipy/io/arff/__init__.py,sha256=5Q7enuZ-WEewiP22LqvgjICGL8EtAhH8cazbOxaXaVE,715
scipy/io/arff/__pycache__/__init__.cpython-39.pyc,,
scipy/io/arff/__pycache__/arffread.cpython-39.pyc,,
scipy/io/arff/__pycache__/setup.cpython-39.pyc,,
scipy/io/arff/arffread.py,sha256=HpbcMii-myw0MWzNZ4PZGdvZ2aordLB5L-7qj-ngdo8,26529
scipy/io/arff/setup.py,sha256=OfgtGHQrwaP3r0236nLI7HI2CTBkLJCQAZalqkCZsvg,344
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/io/arff/tests/__pycache__/test_arffread.cpython-39.pyc,,
scipy/io/arff/tests/data/iris.arff,sha256=fTS6VWSX6dwoM16mYoo30dvLoJChriDcLenHAy0ZkVM,7486
scipy/io/arff/tests/data/missing.arff,sha256=ga__Te95i1Yf-yu2kmYDBVTz0xpSTemz7jS74_OfI4I,120
scipy/io/arff/tests/data/nodata.arff,sha256=DBXdnIe28vrbf4C-ar7ZgeFIa0kGD4pDBJ4YP-z4QHQ,229
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=01mPSc-_OpcjXFy3EoIzKdHCmzWSag4oK1Ek2tUc6_U,286
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=bcMOl-E0I5uTT27E7bDTbW2mYOp9jS8Yrj0NfFjQdKU,292
scipy/io/arff/tests/data/test1.arff,sha256=nUFDXUbV3sIkur55rL4qvvBdqUTbzSRrTiIPwmtmG8I,191
scipy/io/arff/tests/data/test10.arff,sha256=va7cXiWX_AnHf-_yz25ychD8hOgf7-sEMJITGwQla30,199009
scipy/io/arff/tests/data/test11.arff,sha256=G-cbOUUxuc3859vVkRDNjcLRSnUu8-T-Y8n0dSpvweo,241
scipy/io/arff/tests/data/test2.arff,sha256=COGWCYV9peOGLqlYWhqG4ANT2UqlAtoVehbJLW6fxHw,300
scipy/io/arff/tests/data/test3.arff,sha256=jUTWGaZbzoeGBneCmKu6V6RwsRPp9_0sJaSCdBg6tyI,72
scipy/io/arff/tests/data/test4.arff,sha256=mtyuSFKUeiRR2o3mNlwvDCxWq4DsHEBHj_8IthNzp-M,238
scipy/io/arff/tests/data/test5.arff,sha256=2Q_prOBCfM_ggsGRavlOaJ_qnWPFf2akFXJFz0NtTIE,365
scipy/io/arff/tests/data/test6.arff,sha256=V8FNv-WUdurutFXKTOq8DADtNDrzfW65gyOlv-lquOU,195
scipy/io/arff/tests/data/test7.arff,sha256=rxsqdev8WeqC_nKJNwetjVYXA1-qCzWmaHlMvSaVRGk,559
scipy/io/arff/tests/data/test8.arff,sha256=c34srlkU8hkXYpdKXVozEutiPryR8bf_5qEmiGQBoG4,429
scipy/io/arff/tests/data/test9.arff,sha256=ZuXQQzprgmTXxENW7we3wBJTpByBlpakrvRgG8n7fUk,311
scipy/io/arff/tests/test_arffread.py,sha256=PQ3WdR0TvzEdS7Dt2JJr-GMkhS9G46WQmDFfaBdWR0A,13073
scipy/io/harwell_boeing/__init__.py,sha256=BOtucl-suzUbk4aZRk6c2UOZEE2h2axVg4eahUFUeRw,110
scipy/io/harwell_boeing/__pycache__/__init__.cpython-39.pyc,,
scipy/io/harwell_boeing/__pycache__/_fortran_format_parser.cpython-39.pyc,,
scipy/io/harwell_boeing/__pycache__/hb.cpython-39.pyc,,
scipy/io/harwell_boeing/__pycache__/setup.cpython-39.pyc,,
scipy/io/harwell_boeing/_fortran_format_parser.py,sha256=av7rHQWDN847z-wnjTSAmAKgETIie9ABmDkBMCh0haA,8962
scipy/io/harwell_boeing/hb.py,sha256=YMVfL9784lqR5UpgqYgeJ8rGet2qwV-9sR7SpCZFHYY,19254
scipy/io/harwell_boeing/setup.py,sha256=mv72jH4gzsk0QlRZ9smceOrVoLKUeg6PJa96idbux34,351
scipy/io/harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/harwell_boeing/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/io/harwell_boeing/tests/__pycache__/test_fortran_format.cpython-39.pyc,,
scipy/io/harwell_boeing/tests/__pycache__/test_hb.cpython-39.pyc,,
scipy/io/harwell_boeing/tests/test_fortran_format.py,sha256=Zu0__JWRvayTBfY6Cy6saT18TCj2uK4i3NNlxzQKhhs,2406
scipy/io/harwell_boeing/tests/test_hb.py,sha256=dRsdmzKlfX9ijk7rHxeqVdhqKlXeOMstTIK6h5ZOw9Q,2300
scipy/io/idl.py,sha256=cgwMRDv2EnWoT56X2RDpA1chgkBEXEkZu79iwnS4UqA,26513
scipy/io/matlab/__init__.py,sha256=2BLNR-78fK8fhhY16BC0T5wG-9qQO5pOCIp5FSxKEMA,442
scipy/io/matlab/__pycache__/__init__.cpython-39.pyc,,
scipy/io/matlab/__pycache__/byteordercodes.cpython-39.pyc,,
scipy/io/matlab/__pycache__/mio.cpython-39.pyc,,
scipy/io/matlab/__pycache__/mio4.cpython-39.pyc,,
scipy/io/matlab/__pycache__/mio5.cpython-39.pyc,,
scipy/io/matlab/__pycache__/mio5_params.cpython-39.pyc,,
scipy/io/matlab/__pycache__/miobase.cpython-39.pyc,,
scipy/io/matlab/__pycache__/setup.cpython-39.pyc,,
scipy/io/matlab/byteordercodes.py,sha256=eyfQGR_WsVGOfVP4x2Tlv7LSSTk16vRctQM0gtndoJU,1808
scipy/io/matlab/mio.py,sha256=XRS2Svzb9WCc7LtJ6bj-jfam3BLSupKf7KI6AFhEYKc,12104
scipy/io/matlab/mio4.py,sha256=WXNRpI73oRXJj190MGJQK8tt4fdFZSTi9Fi9VwKYzys,20257
scipy/io/matlab/mio5.py,sha256=NQ0-hluc3lwV1eah68YMFYx0EevNA-hA99TjivEsLt4,33364
scipy/io/matlab/mio5_params.py,sha256=wTWGhhRe_D9sGq4ZM4i_s9VKncbo4VC1bK-6I6hN2Kc,7011
scipy/io/matlab/mio5_utils.cp39-win_amd64.pyd,sha256=Sz_XbwtdlaQA3IhM6wLTk9v63GqLjng8UH4oHyg5EMM,145920
scipy/io/matlab/mio_utils.cp39-win_amd64.pyd,sha256=xVgzNG0qEkbcwNGTzApRgcjrI2IZSBKRk_d4o25k1eQ,36352
scipy/io/matlab/miobase.py,sha256=gbKHDM7mxu_NJnv7PwIaQk_BmaVQ1SCGSyOemxXZKvg,12110
scipy/io/matlab/setup.py,sha256=AY0sDHVbNqKCMwI70IzwqOCAvaZmT1CyDxaQQG9F9Gg,533
scipy/io/matlab/streams.cp39-win_amd64.pyd,sha256=DImvkXQILScCe_Oes4-wfVyDtpHz5cPcI3Fu_42uc9M,78336
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_byteordercodes.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio5_utils.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_funcs.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_utils.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_miobase.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_pathological.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_streams.cpython-39.pyc,,
scipy/io/matlab/tests/afunc.m,sha256=DCXqL9FVIu9pnE8Lpmap1b2f5qwX0AfhGEPSyb6sxno,66
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/gen_mat4files.m,sha256=fUnZkI5roi_iV00F64uCIwYrqCEO2PVfUmVr53g_6J8,1163
scipy/io/matlab/tests/gen_mat5files.m,sha256=Moztv9kjP-cMrXrqy-vk6A-T7h938cQQ4RQkpy-UTnc,2485
scipy/io/matlab/tests/save_matfile.m,sha256=GWpdggpYywjenn5ja2cGTvRBUbmyfVthi3nSksChHrw,200
scipy/io/matlab/tests/test_byteordercodes.py,sha256=uZKjrhWEvjnX306lhZzjbfBgG5jZHQT9hXorYl6jIFA,937
scipy/io/matlab/tests/test_mio.py,sha256=f7ncvNhZcrE6cYZr5cMZhXXHIizaJPJqig9uTOEWQVM,42134
scipy/io/matlab/tests/test_mio5_utils.py,sha256=_PfN7IdefV5uTtM1GOR4yGRylJR1ygrbGjJOjKsZ3Pc,5424
scipy/io/matlab/tests/test_mio_funcs.py,sha256=VGRvn6A8qFeoTULFkY2nbSm5jhQUFuNK3qfMDVDW_Cs,1381
scipy/io/matlab/tests/test_mio_utils.py,sha256=f9mIEbsOhCmjx-jNWsVwPbf52bUUm3Q5eAntAJ5N65I,1593
scipy/io/matlab/tests/test_miobase.py,sha256=7muHF8wHVln-sb7JN522eO4CP4tBsXHO5BWar__TZG4,1386
scipy/io/matlab/tests/test_pathological.py,sha256=kUAXyYvB0JviaQ-3-ERWsjUU84g7xOjMF8U_Gql3dM4,1059
scipy/io/matlab/tests/test_streams.py,sha256=Hbf_eJb3DLv5E0rynsAjr2zgA1R1ab2PN16DOK6NJFg,7326
scipy/io/mmio.py,sha256=N9eLTvPVxZWxIPMG1JenpTmiT5zuYfPsnEvigzkpPzI,29199
scipy/io/netcdf.py,sha256=2MztwpP3sTWISQqrejdWw30BcxpoAwSkHEUnyJJUVN8,39266
scipy/io/setup.py,sha256=UKuAC9Ga2Wxx584XNJKNHAh4xIRi1KjxQds6IULAY-c,573
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_fortran.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_idl.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_mmio.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_netcdf.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_paths.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_wavfile.cpython-39.pyc,,
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=KGdQC5WrTn8I5F6lwLFb3Z3E4yq66GbYp0QL7Cui0jY,7570
scipy/io/tests/test_idl.py,sha256=OJQwSAXe1exw_7VbBUImYR47dhxAPK-UDDUsimXkbAI,19580
scipy/io/tests/test_mmio.py,sha256=l5RdaNX3aUkGr1E2hEMA50V4Upo-1J3SjXlFNHGrGto,25734
scipy/io/tests/test_netcdf.py,sha256=MDyDDDD4uwN0JJl4WqCU-0LvOErANING3KC0UPfYrAU,19196
scipy/io/tests/test_paths.py,sha256=NlYtjGuUzFGjRTqfJ0vdr4RTPecBxjJu51YijTV6odE,3241
scipy/io/tests/test_wavfile.py,sha256=oaZm_4o7skoW8IPNh_X0Ycrrc1qTKDUHKtL_oDGTlfg,14298
scipy/io/wavfile.py,sha256=E7wguWOIh0vYSuggpDYJEeTEC9bWhF0lq8ZXVbfN2X4,26026
scipy/linalg.pxd,sha256=M28Y_hLKRSlomUNFNm0LbL9lhYINd7mgo7maa_WiHmw,48
scipy/linalg/__init__.py,sha256=YKFnozEvpAj6kXsc-gmBYzjPTP4f709labPpImMDr7w,7215
scipy/linalg/__pycache__/__init__.cpython-39.pyc,,
scipy/linalg/__pycache__/_cython_signature_generator.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_cossin.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_ldl.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_polar.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_qz.cpython-39.pyc,,
scipy/linalg/__pycache__/_expm_frechet.cpython-39.pyc,,
scipy/linalg/__pycache__/_generate_pyx.cpython-39.pyc,,
scipy/linalg/__pycache__/_interpolative_backend.cpython-39.pyc,,
scipy/linalg/__pycache__/_matfuncs_inv_ssq.cpython-39.pyc,,
scipy/linalg/__pycache__/_matfuncs_sqrtm.cpython-39.pyc,,
scipy/linalg/__pycache__/_procrustes.cpython-39.pyc,,
scipy/linalg/__pycache__/_sketches.cpython-39.pyc,,
scipy/linalg/__pycache__/_solvers.cpython-39.pyc,,
scipy/linalg/__pycache__/_testutils.cpython-39.pyc,,
scipy/linalg/__pycache__/basic.cpython-39.pyc,,
scipy/linalg/__pycache__/blas.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp_cholesky.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp_lu.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp_qr.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp_schur.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp_svd.cpython-39.pyc,,
scipy/linalg/__pycache__/flinalg.cpython-39.pyc,,
scipy/linalg/__pycache__/interpolative.cpython-39.pyc,,
scipy/linalg/__pycache__/lapack.cpython-39.pyc,,
scipy/linalg/__pycache__/matfuncs.cpython-39.pyc,,
scipy/linalg/__pycache__/misc.cpython-39.pyc,,
scipy/linalg/__pycache__/setup.cpython-39.pyc,,
scipy/linalg/__pycache__/special_matrices.cpython-39.pyc,,
scipy/linalg/_cython_signature_generator.py,sha256=jRpqG07rWddai7bnUDQ_vumAIi5DCvJJdVMkNg9GRv0,11466
scipy/linalg/_decomp_cossin.py,sha256=VKMkn6ovEDsUppz-PV96XD-TLDkorltQt8PnYR_LEA8,9111
scipy/linalg/_decomp_ldl.py,sha256=scNY5OZvnYw9aW8Bj6f-TbeZR3uM2SzFAxJ4NYHgT_U,12519
scipy/linalg/_decomp_polar.py,sha256=LSJRhudO7pdYy4sm-ikoObpIVNvNeCArObcCkOqc_Vo,3551
scipy/linalg/_decomp_qz.py,sha256=uUn_IM-fTmFJn7CWPqhPTS1G_RuNcLa6tKud78Bij_M,14501
scipy/linalg/_decomp_update.cp39-win_amd64.pyd,sha256=Gv81LCdy7SAggsg2RRhFMKG3GZny-YGFV9z9LdX0fOE,240640
scipy/linalg/_expm_frechet.py,sha256=c2hJsD-g1-Vt-Aj82s43JPIjzZZ0FqWv4rVw5tZ3hd0,12324
scipy/linalg/_fblas.cp39-win_amd64.pyd,sha256=qs72uaiMbgxD6hB7q3kQD_5UargoNqePaQ_EnMS6jLc,592896
scipy/linalg/_flapack.cp39-win_amd64.pyd,sha256=GMnh2_KSgv0VYJizhgP2ArKIzbn8Y4-l3Y3qef6KBJg,1902592
scipy/linalg/_flinalg.cp39-win_amd64.pyd,sha256=G4gDJXCgDGpw16oxGqXpOZGVIIDtH090Lend5UNiSXk,53760
scipy/linalg/_generate_pyx.py,sha256=TPQT24ZQlXXpdmoFDfFD58G4hzQctBIpz-jz0jSyeoE,25052
scipy/linalg/_interpolative.cp39-win_amd64.pyd,sha256=36ERA_tvj3MJ1Gad-Ev19Vkzr9hSQTzS8SCX4-c20rA,229888
scipy/linalg/_interpolative_backend.py,sha256=yycf_ceX0dgf7Usjvtaxmkm_cT-2jmEMBuWY6tJST2g,45192
scipy/linalg/_matfuncs_inv_ssq.py,sha256=3XE0kZ671OywPi3feFvdL_YYUPAgfa-5gjxx_qXUBDM,27982
scipy/linalg/_matfuncs_sqrtm.py,sha256=nf6SROnjF_d-JIHyv4NQ4hUyF-NnRf6aDx36Q4BK0_g,5662
scipy/linalg/_matfuncs_sqrtm_triu.cp39-win_amd64.pyd,sha256=EccIuYrJVTUee2BFx2H-BRONzfqcUCDI7wu_3GgHBmU,159744
scipy/linalg/_procrustes.py,sha256=XeeL9EcAAXsZsnK8hAUUnuERIUkvcoKUd3akrIBHCLI,2721
scipy/linalg/_sketches.py,sha256=Hu7KrtfkDftka4vKhyxVuodaXlJiQr6x9I7MWwtOB2Q,5860
scipy/linalg/_solve_toeplitz.cp39-win_amd64.pyd,sha256=IFoh4W3d3N_XH8aiP9bkKHxGNZnwtHNkkDgSGk0JoK0,183808
scipy/linalg/_solvers.py,sha256=blWzyoCd5hWLwS25BY3721Tfs8RxkBA1w7kcgCJj_K8,28238
scipy/linalg/_testutils.py,sha256=i0xrjLgjzIr_KV4qCu1ffAYoVTRUwYeWRxHOKOnqJo8,1748
scipy/linalg/basic.py,sha256=Xde4cXYgszMp9S_SDqecRwEoYCjac0bbKz9ngOF5cxg,64903
scipy/linalg/blas.py,sha256=xndYhzhFgbQnTpxl7Mm0Vq3SPhAK40N3Aah3ulMy8mA,11557
scipy/linalg/cython_blas.cp39-win_amd64.pyd,sha256=EYxtdzJYr3hJbF9BazauRdU2u3PqVNX0RhFFOcasl64,207872
scipy/linalg/cython_blas.pxd,sha256=KrK1unX8rOb2eA8VMCVcCgK1IAfHqGsmaa_uCxc-Fk8,14717
scipy/linalg/cython_lapack.cp39-win_amd64.pyd,sha256=qqe2n2bXOOSBVUVQ6w8SfpEfH_cTtZavl25BBePAG0M,626176
scipy/linalg/cython_lapack.pxd,sha256=SWzpIFM1nF1LTfw2fLEKjGxGJ6M9-S9rO_k9RlgLBqc,195600
scipy/linalg/decomp.py,sha256=bq_ZdvVa--F3ZWfxdqzNyd0AueUshhA31ZOGZ5-UIm0,60849
scipy/linalg/decomp_cholesky.py,sha256=tYK7U0kMXIhuYVHglXiuta-8wBls6wmyCpxRkbjH03Q,11767
scipy/linalg/decomp_lu.py,sha256=g-CIOqM4h3J3qvY5qbYl3YtEBYH-hhxGzLiOIEq3380,6748
scipy/linalg/decomp_qr.py,sha256=lFU6Lu5q4n0FvPD8MwETgB2Hivla0OxYlE56R73AZ2U,13557
scipy/linalg/decomp_schur.py,sha256=jYm4N1lr_Si7FPTvyavuELyu9F_va90KCQg74SsmiiI,10216
scipy/linalg/decomp_svd.py,sha256=2Cg4fGMkdte2SIR2q2uorB18gmiDWexEmJ68g71QSdU,14584
scipy/linalg/flinalg.py,sha256=tccfjlga3EQrmXIH8VG_qr7aiYiSHUiexPdQ7Lvwkmw,1662
scipy/linalg/interpolative.py,sha256=Q9sCy0JvD14XYCEf7VM6q_GnU7K2VcPfqs5djVtbUIY,30969
scipy/linalg/lapack.py,sha256=aDp85LjR_HlRl5mM0M83717JdplC5dhqcHxZOVXGjQo,16473
scipy/linalg/matfuncs.py,sha256=dXleDxECOCuUO_87VjadOvtR2Ve4bnUqBA68eQscQ-k,19751
scipy/linalg/misc.py,sha256=DeaYnXe1v2MySUNDpfbPLfeVOs0l_hQdwJUA8GydRKA,6422
scipy/linalg/setup.py,sha256=drrpDH6S2mNnnJzdSgVQVeH0znYoRlDnWsSjuf_vVfM,6006
scipy/linalg/special_matrices.py,sha256=rcRCqvoBo91TasWAkMM3d_qTPephJVfRcDxx2vUnYTk,39925
scipy/linalg/src/id_dist/doc/doc.tex,sha256=-CUbZ01Brg-SAGsIxLas21ZVfg5bO78KL4OBo1QgjY8,37261
scipy/linalg/src/lapack_deprecations/LICENSE,sha256=N-ZKSYiUrHw7BwAj42ielUqOz4ojuQlo0JpFXxtPezU,2266
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_basic.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_blas.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_build.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_cython_blas.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_cython_lapack.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cholesky.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cossin.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_ldl.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_polar.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_update.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_fblas.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_interpolative.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_lapack.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_matfuncs.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_matmul_toeplitz.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_procrustes.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_sketches.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_solve_toeplitz.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_solvers.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_special_matrices.cpython-39.pyc,,
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=oxfYwjfbvWDxh0PI79fbe5aWs3E7AxK50dONEioZz44,60874
scipy/linalg/tests/test_blas.py,sha256=TnuUdcvU23Fi4y4PME9y7ulBJF2hxQRhfdHd-3qJQiY,40278
scipy/linalg/tests/test_build.py,sha256=G23HtM1EjdMwTHiZvLe0dQYy7oUAa18Ull-S335h9lo,1747
scipy/linalg/tests/test_cython_blas.py,sha256=uhLfMoBr0j9FWv2wOVP8kMz-e6LsUede20mjjfz8Rsw,4233
scipy/linalg/tests/test_cython_lapack.py,sha256=RJ_1aQRF2Y7mpA5vQoobxbZwUnm4ccrpsRZlhz3q65E,582
scipy/linalg/tests/test_decomp.py,sha256=IZCG1R6TJRPvIc5idSsKfuNd0xiWiqBleSIN6mL1RCk,108373
scipy/linalg/tests/test_decomp_cholesky.py,sha256=-tkcouE_t2bha20mXtK-k8yVWjhbYp97SA-Mt3N6DCM,7297
scipy/linalg/tests/test_decomp_cossin.py,sha256=5PF6FGd-WisBFeWLJqKmgbbIdWedJ-skZ9NevCM5x1k,5772
scipy/linalg/tests/test_decomp_ldl.py,sha256=21j_UzfKO__LQsovXOiK81gZJLH4gLtHW_Vt0ll_OLA,5041
scipy/linalg/tests/test_decomp_polar.py,sha256=5x5vz9rJE2U2nvo0kx6xMX5Z9OcnqxayPZvAd4dwsUQ,2646
scipy/linalg/tests/test_decomp_update.py,sha256=9Sf0iJJD7GQryTdneHs7d61v5RnTwZd0OmD7IXrxWLM,68374
scipy/linalg/tests/test_fblas.py,sha256=qJdqvRDDs8MeiNk75jZ5Mv0QqgvnqJYl6CBqkhiUGmc,18733
scipy/linalg/tests/test_interpolative.py,sha256=ViopMtNYkKdte339jqTUOn9MlQ0xDBILqGrLcXXZmYE,10961
scipy/linalg/tests/test_lapack.py,sha256=gLZa44cdm-QpNyfCiCN6aU3PUGp9KRKl_PgVmsv0RoI,114974
scipy/linalg/tests/test_matfuncs.py,sha256=skWfrTYK-WSkRkRyHfxARyEnYlPSRKB7nTcRJkQpUyg,34740
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=Wd9T03zZRwX3M3ppkhYJiJbkWZ_xop4VKj57TjeozUs,3870
scipy/linalg/tests/test_procrustes.py,sha256=ljgOAIXRomkN0TSDkKpOeeh2cjpo8wW2BsZkWf-Ohwo,6757
scipy/linalg/tests/test_sketches.py,sha256=gbU4eXjpnGb3IU48eOm9Xuox9J4YQ6IBzd8-M1KUfpM,3968
scipy/linalg/tests/test_solve_toeplitz.py,sha256=ISKGB1yNwjTx-pR2ZVKBByigq6RpmxBF-b-Eftn22As,4039
scipy/linalg/tests/test_solvers.py,sha256=fyOFsN9XeW592T0-WtaH_1reJC0vO6agNMGyaE7V9Jc,31018
scipy/linalg/tests/test_special_matrices.py,sha256=kE_9VqEENQHKOQOFGsEP00_S8H78epshN5hlAhrFUwc,26815
scipy/misc/__init__.py,sha256=wXGYbwq5INBfB6LPIZn2jIJDKYgya0FmMY9lg07NTEA,762
scipy/misc/__pycache__/__init__.cpython-39.pyc,,
scipy/misc/__pycache__/common.cpython-39.pyc,,
scipy/misc/__pycache__/doccer.cpython-39.pyc,,
scipy/misc/__pycache__/setup.cpython-39.pyc,,
scipy/misc/ascent.dat,sha256=6KhJOUhEY6uAUa7cW0CqJiqzOpHWRYps0TxqHK1aAj0,527630
scipy/misc/common.py,sha256=7apKbt2szLuh8tM5lOoGf5cSLl5bcHPjd0UCJkWIoeg,9678
scipy/misc/doccer.py,sha256=e0t21hxEywoAH6pLMG1fAxU0zPRNQsgDISDKOS7psEE,1732
scipy/misc/ecg.dat,sha256=8grTNl-5t_hF0OXEi2_mcIE3fuRmw6Igt_afNciVi68,119035
scipy/misc/face.dat,sha256=nYsLTQgTE-K0hXSMdwRy5ale0XOBRog9hMcDBJPoKIY,1581821
scipy/misc/setup.py,sha256=Q30YymjfF2YrHc7vpvE710wTrel71KDkeQ4k9EAnYc0,375
scipy/misc/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/misc/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/misc/tests/__pycache__/test_common.cpython-39.pyc,,
scipy/misc/tests/__pycache__/test_doccer.cpython-39.pyc,,
scipy/misc/tests/test_common.py,sha256=xjPP5pTR5IP6G-HH2oPSrhOJJ2DwQUy1tzhsA58Gisg,527
scipy/misc/tests/test_doccer.py,sha256=2eubA8C8iW3gaxrCdWPy07YO01ds2HarEwzsBIFovBM,3746
scipy/mypy_requirements.txt,sha256=Bp6iSCLxfXOjQAaySc7ixCcG5-LdM1zkHu552Atq6Jw,80
scipy/ndimage/__init__.py,sha256=JirmG6wGtaNRjQG4QdyxeKhaCoCdmpT1DZ_iUdrv4uA,4762
scipy/ndimage/__pycache__/__init__.cpython-39.pyc,,
scipy/ndimage/__pycache__/_ni_docstrings.cpython-39.pyc,,
scipy/ndimage/__pycache__/_ni_support.cpython-39.pyc,,
scipy/ndimage/__pycache__/filters.cpython-39.pyc,,
scipy/ndimage/__pycache__/fourier.cpython-39.pyc,,
scipy/ndimage/__pycache__/interpolation.cpython-39.pyc,,
scipy/ndimage/__pycache__/measurements.cpython-39.pyc,,
scipy/ndimage/__pycache__/morphology.cpython-39.pyc,,
scipy/ndimage/__pycache__/setup.cpython-39.pyc,,
scipy/ndimage/_ctest.cp39-win_amd64.pyd,sha256=xrOOmvdf7q_XHRYEechCn9LPafzGIGAATyjynTKHvWc,13312
scipy/ndimage/_cytest.cp39-win_amd64.pyd,sha256=IpsEUWL0Oj02xPsvm_jPiuOEVtxH8wOfu9P_kvIaw5Q,46592
scipy/ndimage/_nd_image.cp39-win_amd64.pyd,sha256=tdxGt1kDy21-gyM9j5-SOnA8IIVnhOZIzBAGXAho1LM,124416
scipy/ndimage/_ni_docstrings.py,sha256=BeK_hpDa8t7embwIVgNmAxexEA6US7FrILm7FxZoAyI,8458
scipy/ndimage/_ni_label.cp39-win_amd64.pyd,sha256=Cq9LJKF2DE7931Smxpv8yAOulGLa74huonPIxHrR53Q,237568
scipy/ndimage/_ni_support.py,sha256=VE1nM-kdydfBvRHNv2aQUV4bgA7fAxGqiHQCIpBy06U,3825
scipy/ndimage/filters.py,sha256=uFwzkREjcuN9_7gNSSi0ikOMMfGIBwpyuj31NoXOlJ4,55540
scipy/ndimage/fourier.py,sha256=N9RGZMzixWkp1Ox86wZZ3Khd5uB7kL1UQ-Wn6TBFczE,11239
scipy/ndimage/interpolation.py,sha256=SFY7UOgwzjN4mgPvS4HjJtynvivjaPI-9wG3JaBMMWY,35240
scipy/ndimage/measurements.py,sha256=3qIB_tH0uofwQ2m3cKjzqwAFKiX2YkGC4NVzZy3tu04,51658
scipy/ndimage/morphology.py,sha256=jQObbmFwEt9rLxeZgrM4J5C3knMOxSe1d48y98TqLLM,85085
scipy/ndimage/setup.py,sha256=9v7th1QtpF_8NYhAPxIdbpJuA22iL_t0bZJABIleUEc,1474
scipy/ndimage/tests/__init__.py,sha256=dCKRxjZqg6VUtusFFWwV7jK4oujQqWsLiPlRw5yPrzI,313
scipy/ndimage/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_c_api.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_datatypes.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_filters.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_fourier.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_interpolation.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_measurements.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_morphology.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_splines.cpython-39.pyc,,
scipy/ndimage/tests/data/README.txt,sha256=wvMFLCAFtWPyN6gpa7JoAd2mt5wIwrrjhijeocXHHLw,278
scipy/ndimage/tests/data/label_inputs.txt,sha256=JPbEnncwUyhlAAv6grN8ysQW9w9M7ZSIn_NPopqU7z4,294
scipy/ndimage/tests/data/label_results.txt,sha256=Cf2_l7FCWNjIkyi-XU1MaGzmLnf2J7NK2SZ_10O-8d0,4309
scipy/ndimage/tests/data/label_strels.txt,sha256=AU2FUAg0WghfvnPDW6lhMB1kpNdfv3coCR8blcRNBJ8,252
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=EPNsGMHzZHqd9jETd9Pw3gOQvo43On-jH5_4CJzf0S0,3476
scipy/ndimage/tests/test_datatypes.py,sha256=UCYf_2mKXeZHxUsBRCAbadB1ojEnKimbuV499h0Jb7E,2742
scipy/ndimage/tests/test_filters.py,sha256=zshBbrfPbbyYu1ldk1e2L4Ss39h4TxS_9H7iXrWyHIE,82897
scipy/ndimage/tests/test_fourier.py,sha256=pGzWifN0-WQIMurWAbMXC5pefcUJH_xSasa4Hg_j8PA,6394
scipy/ndimage/tests/test_interpolation.py,sha256=uKnpwnCdqHPHVvoiswV_kUZgooMZKAFTA8W1b5z4vF8,54261
scipy/ndimage/tests/test_measurements.py,sha256=BRJATO30s2ZigFir4-oYBAP-vs3xAPX-NRv48njkNnA,46513
scipy/ndimage/tests/test_morphology.py,sha256=-jvd4PzIXwH8MPJBlRwIVODDTRs0Ktlb1tO7fRSo5bo,101706
scipy/ndimage/tests/test_splines.py,sha256=KXQaTR1Odj45IQB4pfn8zWpWq26G2vPuFQxgc9qDYRk,2207
scipy/odr/__init__.py,sha256=cOthjvj8cWv5uNs0vejWkWsKijosCV8HPmx3DM1WE4Y,4243
scipy/odr/__odrpack.cp39-win_amd64.pyd,sha256=TEmiB3tqbn0FysmYhoNRsODxgZL1r0t68Po2rkE8vzc,31232
scipy/odr/__pycache__/__init__.cpython-39.pyc,,
scipy/odr/__pycache__/_add_newdocs.cpython-39.pyc,,
scipy/odr/__pycache__/models.cpython-39.pyc,,
scipy/odr/__pycache__/odrpack.cpython-39.pyc,,
scipy/odr/__pycache__/setup.cpython-39.pyc,,
scipy/odr/_add_newdocs.py,sha256=zX9DJ9c4fJX-6RU9xYZEJVxlO72wmNxV6_aTKSQjoGk,1090
scipy/odr/models.py,sha256=fo1sls0luV6228JGQRJajuwqwI-2ehPrPM9yzmE9aPM,7660
scipy/odr/odrpack.py,sha256=Pwh___GR6zhv6DMilMDKetb4Thsr7vRvllpAd9YLzPc,42011
scipy/odr/setup.py,sha256=5RyyWc7ZD7mpNocz4VcQe3c2yGS-duhAfmqb1x5d3UU,1483
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/odr/tests/__pycache__/test_odr.cpython-39.pyc,,
scipy/odr/tests/test_odr.py,sha256=271_iPcBYKsd90hgqorfNAt1pl5kNGEjpRFWr_z4MKk,19522
scipy/optimize.pxd,sha256=kFYBK9tveJXql1KXuOkKGvj4Fu67GmuyRP5kMVkMbyk,39
scipy/optimize/__init__.py,sha256=TG1mfcgrnkdMHPAtA0qZK9ZVy7k923R2vDYOQuFtHQI,12466
scipy/optimize/__nnls.cp39-win_amd64.pyd,sha256=j_lUpQlzXP-iOTpk8UIbzwFWZDyVSIHZFoQDe2eLZYk,31744
scipy/optimize/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/__pycache__/_basinhopping.cpython-39.pyc,,
scipy/optimize/__pycache__/_constraints.cpython-39.pyc,,
scipy/optimize/__pycache__/_differentiable_functions.cpython-39.pyc,,
scipy/optimize/__pycache__/_differentialevolution.cpython-39.pyc,,
scipy/optimize/__pycache__/_dual_annealing.cpython-39.pyc,,
scipy/optimize/__pycache__/_hessian_update_strategy.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_doc.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_highs.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_ip.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_rs.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_simplex.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_util.cpython-39.pyc,,
scipy/optimize/__pycache__/_lsap.cpython-39.pyc,,
scipy/optimize/__pycache__/_minimize.cpython-39.pyc,,
scipy/optimize/__pycache__/_nnls.cpython-39.pyc,,
scipy/optimize/__pycache__/_numdiff.cpython-39.pyc,,
scipy/optimize/__pycache__/_qap.cpython-39.pyc,,
scipy/optimize/__pycache__/_remove_redundancy.cpython-39.pyc,,
scipy/optimize/__pycache__/_root.cpython-39.pyc,,
scipy/optimize/__pycache__/_root_scalar.cpython-39.pyc,,
scipy/optimize/__pycache__/_shgo.cpython-39.pyc,,
scipy/optimize/__pycache__/_spectral.cpython-39.pyc,,
scipy/optimize/__pycache__/_trustregion.cpython-39.pyc,,
scipy/optimize/__pycache__/_trustregion_dogleg.cpython-39.pyc,,
scipy/optimize/__pycache__/_trustregion_exact.cpython-39.pyc,,
scipy/optimize/__pycache__/_trustregion_krylov.cpython-39.pyc,,
scipy/optimize/__pycache__/_trustregion_ncg.cpython-39.pyc,,
scipy/optimize/__pycache__/_tstutils.cpython-39.pyc,,
scipy/optimize/__pycache__/cobyla.cpython-39.pyc,,
scipy/optimize/__pycache__/lbfgsb.cpython-39.pyc,,
scipy/optimize/__pycache__/linesearch.cpython-39.pyc,,
scipy/optimize/__pycache__/minpack.cpython-39.pyc,,
scipy/optimize/__pycache__/nonlin.cpython-39.pyc,,
scipy/optimize/__pycache__/optimize.cpython-39.pyc,,
scipy/optimize/__pycache__/setup.cpython-39.pyc,,
scipy/optimize/__pycache__/slsqp.cpython-39.pyc,,
scipy/optimize/__pycache__/tnc.cpython-39.pyc,,
scipy/optimize/__pycache__/zeros.cpython-39.pyc,,
scipy/optimize/_basinhopping.py,sha256=nq2-CbUqwUPQuLFD73Pc9sRLm7MsJMTh-5y2F4y-hbc,30136
scipy/optimize/_bglu_dense.cp39-win_amd64.pyd,sha256=DOyOn-d8iK1LCFbe6xOSG7XJo0TCQtB_IzNHo3gXjx4,215040
scipy/optimize/_cobyla.cp39-win_amd64.pyd,sha256=otX036cLD7ocv72yw1y2jTBlnM4rchp5vyB1bQjIY_Y,35840
scipy/optimize/_constraints.py,sha256=9JAPXODOkoOa3q1UPqC6gY0vkCDu9lMipaxnMpj9c9s,18696
scipy/optimize/_differentiable_functions.py,sha256=hb3inpZSrQ41H6_btfBx35wFXqzq2fppIJCxMOQIyP0,21572
scipy/optimize/_differentialevolution.py,sha256=yXSqH-n86aNszW4-ry-dFR0LIVrQfWIaenDQ0TundmE,57012
scipy/optimize/_dual_annealing.py,sha256=MiN87TOHHYqUIIwD7xiEZ-8i6ynBFObWLBoKlHTskPc,29699
scipy/optimize/_group_columns.cp39-win_amd64.pyd,sha256=ckVI3Zl7_70Fr3npm1VwF4yrmlEBHC2xI4pPXphr630,140800
scipy/optimize/_hessian_update_strategy.py,sha256=c2MWA2BJ0LKZgKksCNyOdbMBHhkBi6VorwHV8346Pws,15857
scipy/optimize/_highs/constants.cp39-win_amd64.pyd,sha256=6tNZNrtd7gq4MNUSA5hQWmnYlmUbkFCx0Rh1qh830QM,30720
scipy/optimize/_highs/cython/src/HConst.pxd,sha256=Fgug6_8Dkv9vfMrffLxZKkFePi7c7YU7ZalSoAMnsJ0,1930
scipy/optimize/_highs/cython/src/Highs.pxd,sha256=fThw_dGApc3zX3BY6D5w-3SVXfQD6RRtHgsdpH1XSF4,2122
scipy/optimize/_highs/cython/src/HighsIO.pxd,sha256=CAKB-HSRIQDSjU7J7kvWWjILjztFywnApFlYtK9ZlM0,446
scipy/optimize/_highs/cython/src/HighsInfo.pxd,sha256=OUw2PXdgtq32bw174MYK4YaTxuF2cVWvGkhUPlRyROM,636
scipy/optimize/_highs/cython/src/HighsLp.pxd,sha256=P5H6jRXq0GsBUPPt-nMzGRDzTCN0zkgV5tr1rgV5cP0,2500
scipy/optimize/_highs/cython/src/HighsLpUtils.pxd,sha256=5-fmpcJ-mk5QE7VbdlALbooS-b3a6IjYGzhvSncQT8k,312
scipy/optimize/_highs/cython/src/HighsMipSolver.pxd,sha256=0xZy2BXIzaRl-TvZvNNsFv_hjJ7120uX_WcQyiWraQw,1540
scipy/optimize/_highs/cython/src/HighsModelUtils.pxd,sha256=D3kLEYZHIvI9sQkRHLFxy4RdPd4Zv9cYl7D5gsjloFU,296
scipy/optimize/_highs/cython/src/HighsOptions.pxd,sha256=HaQbUB08E9hi-FQU4zhfV56KJOr0xBzPHmAwgDW6ylQ,3144
scipy/optimize/_highs/cython/src/HighsRuntimeOptions.pxd,sha256=zrpT5OypnQ_tofScvvB5_FZqJuYFDKp0o2h1jMEi7Ss,286
scipy/optimize/_highs/cython/src/HighsStatus.pxd,sha256=B2on3jx5Yh3yR50czTlemC_AlPl97_T2XO4oijbjP-o,348
scipy/optimize/_highs/cython/src/SimplexConst.pxd,sha256=xN-8W7RPYqvXGXq8sorQTvqkt_I0NsOS--ZnufqVC5o,4479
scipy/optimize/_highs/cython/src/highs_c_api.pxd,sha256=ut0M5I_pG2D6K3fUHzU9WD67Y1VMZuQOKhLLjv0umqo,358
scipy/optimize/_highs/highs_wrapper.cp39-win_amd64.pyd,sha256=YDiHsGcdX9qgIbIBgS8-bxE09s_nGYndHrgChPy2D34,1441792
scipy/optimize/_highs/mpswriter.cp39-win_amd64.pyd,sha256=Sp-Bjjq6fBEQ0aHu9oMPk02fZRoRKemQgRlbMQuBBTc,188416
scipy/optimize/_lbfgsb.cp39-win_amd64.pyd,sha256=Yeok_4eze44Y6l96k4Zr1fasx0XyfUtQdSxPk5fgYms,39936
scipy/optimize/_linprog.py,sha256=rdCoCKB5sjUE-1f6wNny1Dmg4JmS_dxXxmIIRxYcwv4,27752
scipy/optimize/_linprog_doc.py,sha256=XZyMG8FkkgXvS7v1z98k8KFi5dTmQqGXMaOopjqlWk8,53838
scipy/optimize/_linprog_highs.py,sha256=1NlIjHUkXqIFRaF8oqy7wreJGXIIH70k6mV9e8vxZwo,12628
scipy/optimize/_linprog_ip.py,sha256=Nh0JyPU56BdsZ_y6r5e2XdaHXuByCKDCaukShFg5XB4,45799
scipy/optimize/_linprog_rs.py,sha256=RlsBFk3NpMoyQVA9cRKRKutXI0WAwWrF2Ibka0UoB90,23222
scipy/optimize/_linprog_simplex.py,sha256=IprhdMLIQrelije91jJUKoNTrsYST_-_egDCtYlYSLg,24726
scipy/optimize/_linprog_util.py,sha256=I6cFRm7NiWxbdF4Ofw-nOctXznTRFKlKRRSetFiseUA,60900
scipy/optimize/_lsap.py,sha256=ty6_NaBJ5MsDyY79bxTNcPX74dwzivjy9_WVsxPbmk8,3992
scipy/optimize/_lsap_module.cp39-win_amd64.pyd,sha256=UPHGyEuK5lqzjPrnnjrQQ2VITTTMb6H5Wm-JE4SyXs0,25600
scipy/optimize/_lsq/__init__.py,sha256=Yk4FSVEqe1h-qPqVX7XSkQNBYDtZO2veTmMAebCxhIQ,172
scipy/optimize/_lsq/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/bvls.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/common.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/dogbox.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/least_squares.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/lsq_linear.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/setup.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/trf.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/trf_linear.cpython-39.pyc,,
scipy/optimize/_lsq/bvls.py,sha256=7u5B8LfUbv3ZRZ8DAZKuDTSNRfDEBmTsn25VZtMMsKk,5195
scipy/optimize/_lsq/common.py,sha256=dQEQhp-41jqT3QOyTROQgegB2P24j0D6XZeTejmKtAg,20616
scipy/optimize/_lsq/dogbox.py,sha256=97htRlr-Yt-********************************,11682
scipy/optimize/_lsq/givens_elimination.cp39-win_amd64.pyd,sha256=6kKbrild2yxbH2YhppOXh-IS_YmSL19JQuGllooObIQ,129024
scipy/optimize/_lsq/least_squares.py,sha256=i6FZDyclMc9ESJnyEBd2VarrR_6ELjYIGsVcTwKf6GA,39120
scipy/optimize/_lsq/lsq_linear.py,sha256=6frPayPHq5SksZ1JzCOnLrhc_JuxeO-EhWorzg_xz2c,12572
scipy/optimize/_lsq/setup.py,sha256=i13waS4xvN09zzeGndoCTHGeU5Nj-Q-YGQFQT-0DKaw,416
scipy/optimize/_lsq/trf.py,sha256=W0gc6j9vLIqZvDMOhM5BxubrWsd5PQpdncUJB88ol44,19479
scipy/optimize/_lsq/trf_linear.py,sha256=UxqHgzbiXjU56uuIGLcVXovDCYH6LOutNkXD1ZIpGVo,7580
scipy/optimize/_minimize.py,sha256=Q_jYzU_UzgQXxbe0n6g8ZtiSNVopyWjBfvgVyJu5Bmw,37802
scipy/optimize/_minpack.cp39-win_amd64.pyd,sha256=Ot52X99N0NRVO1ACu7t9bg5Oja_77X17AP1v9IZrdGc,29696
scipy/optimize/_nnls.py,sha256=2cbjC4d_0djvvTYglikogh-YFrR2VYHzUyrHrk1XSGM,2272
scipy/optimize/_numdiff.py,sha256=Ti-W1bYRKQ_WEtRgo7FIJ0APweQ3X_Kp-po-Q-1_RQI,27487
scipy/optimize/_qap.py,sha256=QciWNQVBmmp6IKzr1qdUc7UV-AIhnBtIdW9Z3Sdnlv0,27235
scipy/optimize/_remove_redundancy.py,sha256=-uZBXga6QNiB2U1SVOnaR_ArmBVWN_OX-ZaAVVwmTeY,18771
scipy/optimize/_root.py,sha256=qFkMIXu4dYH0cHalklpS9H_3TSAeNjpE0-V6vBaWRuo,26284
scipy/optimize/_root_scalar.py,sha256=BGkgM9Qh1ENKywDMBABGpcZedUXP_MVyPgymti9MRTE,14946
scipy/optimize/_shgo.py,sha256=lUAIgmj33MPiUYWvKsNPHNUfTmrTY8CEHOhmgnVxkHE,63029
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_shgo_lib/__pycache__/sobol_seq.cpython-39.pyc,,
scipy/optimize/_shgo_lib/__pycache__/triangulation.cpython-39.pyc,,
scipy/optimize/_shgo_lib/sobol_seq.py,sha256=uUneOGUJbSQY_3aI1XJenkBsNQN_AfdgIx_FwJXQCv8,12457
scipy/optimize/_shgo_lib/sobol_vec.gz,sha256=I_h0m4DMFm08kZs8ktPm5TgESf3f7g2cAA-6ZCjIx4I,295548
scipy/optimize/_shgo_lib/triangulation.py,sha256=NXh5mDGbYAnS2Lgq4CtsKjulrQRK-vVKOAgZZJCagR8,21494
scipy/optimize/_slsqp.cp39-win_amd64.pyd,sha256=BY_Nvq-p8BzmJeyVCZ2fN0kXIa2lOwyZfvHleOghSRM,40960
scipy/optimize/_spectral.py,sha256=hM8pNJJ5LEn1wh0AcnPDlnu8fNG_8svGCv3EN_CaSNo,7921
scipy/optimize/_trlib/__init__.py,sha256=cNGWE1VffijqhPtSaqwagtBJvjJK-XrJ6K80RURLd48,524
scipy/optimize/_trlib/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_trlib/__pycache__/setup.cpython-39.pyc,,
scipy/optimize/_trlib/_trlib.cp39-win_amd64.pyd,sha256=Dd0BTDc8KqPA2LBo2s0AtaqdSJyyM6Ne7J_fiYH1cEk,246272
scipy/optimize/_trlib/setup.py,sha256=POfruj8J9NSzi0gZwiAKt5eh0SlHjmp71kLlmVkItJ0,1177
scipy/optimize/_trustregion.py,sha256=fdA4zkCtPXp5WEZX4m0i6wc4MG2119QSBNA3jfxP0jw,9226
scipy/optimize/_trustregion_constr/__init__.py,sha256=c8J2wYGQZr9WpLIT4zE4MUgEj4YNbHEWYYYsFmxAeXI,180
scipy/optimize/_trustregion_constr/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/canonical_constraint.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/equality_constrained_sqp.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/minimize_trustregion_constr.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/projections.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/qp_subproblem.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/report.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/setup.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/tr_interior_point.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=k9t9tisI2ZbxU8IupIsO3YA1GY_zfzuLOUkE9pxx9MM,12550
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=5NiEruWnhYL2zhhgZsuLMn-yb5NOFs_bX3sm5giG7I8,8592
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=9JTm8Ns2ldOSOlk557AbYAF6lfUcklbptQDp8kLcdzg,24909
scipy/optimize/_trustregion_constr/projections.py,sha256=2V9GysEHMzuYcE93CpnK2Q5iwQQBIc1rbtOJJBIZUZQ,13105
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=qXjegiBYJ0een7ozCh843Dz0ltJqz6WqdzteUYrIQVU,22596
scipy/optimize/_trustregion_constr/report.py,sha256=czp-EQJCav83TwBPFmkcweNqJA1C71iRMUtKyXN19_k,2048
scipy/optimize/_trustregion_constr/setup.py,sha256=Zd6lYAfOORAcUpm_Su3gdzUXobnMn13rLJVgTbUwAg4,358
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_canonical_constraint.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_projections.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_qp_subproblem.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_report.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=zVPxZDa0WkG_tw9Fm_eo_JzsQ8rQrUJyQicq4J12Nd4,9869
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=P4GZxs_6RJnlb6OXJX-wnvFqzFeQAgs9qHvnHxjvD4o,8820
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=j7yGN0XWii9Zlk7Eo5WeNfj5rL2pdHT4zq2z7DhsP20,27721
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=l7VEwT9bWqSVQbFH_zckGTGUI_wY2e2QBLM6_6BgS1s,408
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=fXuyoZ5WmIwce2EA-Gdld7S2YrM7usImXWBNk3DnURw,13802
scipy/optimize/_trustregion_dogleg.py,sha256=bjqxzrBcAVOJN6uecx4Jjc8ryQvBhubp_mOPepkzhVA,4383
scipy/optimize/_trustregion_exact.py,sha256=0kYSMFgD9Izj0MzCVlzT3jgJYhUKOfONutZzBl6-99g,15426
scipy/optimize/_trustregion_krylov.py,sha256=KGdudJsoXXROXAc82aZ8ACojD3rimvyx5PYitbo4UzQ,3030
scipy/optimize/_trustregion_ncg.py,sha256=y7b7QjFBfnB1wDtbwnvKD9DYpz7y7NqVrJ9RhNPcipw,4580
scipy/optimize/_tstutils.py,sha256=cSz6uEW6lxVvOKj_rihQTLg46i_LNrLmDKUT5gQExxg,29479
scipy/optimize/_zeros.cp39-win_amd64.pyd,sha256=B8Lpojwe4RcKe4cC2LbaDhaC8ilIuaNFd5Hjunnmzo0,16896
scipy/optimize/cobyla.py,sha256=DLo-uO0t4kpV9xWLwhMmaPtQiC3Y636KX2dtBuFC_0U,9794
scipy/optimize/cython_optimize.pxd,sha256=UQVKui1CYlTt1HS9ydLPLdgKNWH_-phR8fy4Rq2eEno,428
scipy/optimize/cython_optimize/__init__.py,sha256=geDoC1goxZGZMpJ8f0zEHTTogZuZrTefL3aChOHHrio,4831
scipy/optimize/cython_optimize/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/cython_optimize/_zeros.cp39-win_amd64.pyd,sha256=R3SZcvrPeV4pY7sQ-QbFQ1tF-_fOycurtlRVzjkS8bE,65024
scipy/optimize/cython_optimize/_zeros.pxd,sha256=wTtD2hT2XHUhSHem5glipOQNY67vExQxzxncdQPtbJ4,1194
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=kGxXXz8Y8XwvaHpHgKNVnTX-7APgdgH3SWAcxxdtI08,1085
scipy/optimize/lbfgsb.py,sha256=eoqY-ZS0n96jzw5cDw4YhezbFzhx7l2wzfQg9LhL5ow,18623
scipy/optimize/lbfgsb_src/README,sha256=q7vAotiT7affj-8xYhiy0g9r0fQBE2caLUnvjqjgSv4,3416
scipy/optimize/linesearch.py,sha256=mdPxeB-FOFxiNX6hXBCch8c_XpI3NbWcykN58Dj-D3A,27195
scipy/optimize/minpack.py,sha256=kvecelszaCu0bXhDTzjvKDmjHFGrqWoWUjSE6_HjtDg,34808
scipy/optimize/minpack2.cp39-win_amd64.pyd,sha256=6uH74eEG7Ql8oDPfXN_ioOTx8nKPb-xUUr4jPym8P3A,35328
scipy/optimize/moduleTNC.cp39-win_amd64.pyd,sha256=qVTotlxIQDxMC1x_z0X_x_v2KS-Uohkm_IEgN37Wo-U,51712
scipy/optimize/nonlin.py,sha256=SZyRWdMMNo75_ZsCK4M26fY67ShcWHGvKytNmPvezzI,50704
scipy/optimize/optimize.py,sha256=YiV1gFiF3JoOf7MAU2iQEuOHkUDoeQvl_MhNC67cjJM,126177
scipy/optimize/setup.py,sha256=4a9HAaSSVVDCHkiN5qgUotC48gvGydU3DaGUSkhckUU,5098
scipy/optimize/slsqp.py,sha256=Lqjvu-Y3vUgWuFMrXexquPSrqbEAcL__mbj3aNQL6hQ,20805
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__basinhopping.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__differential_evolution.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__dual_annealing.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__linprog_clean_inputs.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__numdiff.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__remove_redundancy.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__root.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__shgo.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__spectral.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_cobyla.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_constraint_conversion.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_constraints.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_cython_optimize.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_differentiable_functions.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_hessian_update_strategy.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_hessinv.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_setulb.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_least_squares.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_linear_assignment.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_linesearch.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_linprog.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_common.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_linear.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_minimize_constrained.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_minpack.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_nnls.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_nonlin.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_optimize.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_quadratic_assignment.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_regression.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_slsqp.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_tnc.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_exact.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_krylov.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_zeros.cpython-39.pyc,,
scipy/optimize/tests/test__basinhopping.py,sha256=raFdZV_Gy1AUAnBq89l8EAjxrMLvbsp083yYIezio7U,16305
scipy/optimize/tests/test__differential_evolution.py,sha256=6wmXoTd7K9LF7zMAe6gOKzrBBdu6bYRAK2hSPnUNLek,49081
scipy/optimize/tests/test__dual_annealing.py,sha256=Q_k4T4R0G7Ix_JEEF9f5WEtMGKdHoFK9_ThFG4q8g4A,12820
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=MDCIrO33d3jVd6swt3Wi156x1wxWC-cxdjueqFRMJH8,11106
scipy/optimize/tests/test__numdiff.py,sha256=Fu06pg6PRglm_beGcCAck5yNY2yp8yuW9UOTh2oDmw0,29672
scipy/optimize/tests/test__remove_redundancy.py,sha256=fXRLOUXOEdFOH5YmkQx-R47vN6BzXDqbCcc-88gdq0Y,7535
scipy/optimize/tests/test__root.py,sha256=JVBauGITMQolI9ZCOFVFfy1rqTXPB43Hc_MD_FpSz3k,2629
scipy/optimize/tests/test__shgo.py,sha256=rH0XOo9drg5FeL44iZqK6ABwAo1vRUkFTJMqAdCYdhg,27121
scipy/optimize/tests/test__spectral.py,sha256=JR6rTBUdhOYdgOmRBCA6sJ4ks0JTBYZ7aI6u6aiVUag,6519
scipy/optimize/tests/test_cobyla.py,sha256=7Q01_hrPweHxorjt6zBjZWkyqf1UDortDcH3yiVHjcQ,3446
scipy/optimize/tests/test_constraint_conversion.py,sha256=mKrbDIHAMOBTqV0oyCLCorpkob1KxAKfzyNnNkFG9dw,11778
scipy/optimize/tests/test_constraints.py,sha256=K4by1MUfMneyKXBaJjFq236mXJIZBPkv1tDGtaEn7xg,6728
scipy/optimize/tests/test_cython_optimize.py,sha256=n-HccBWoUmmBWq_OsNrAVnt4QrdssIYm4PWG29Ocias,2638
scipy/optimize/tests/test_differentiable_functions.py,sha256=Bsd86mS4Osj9ue7HZBKh-Rracfij-OkCR9SWuAPN7HM,24917
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=OgAiMeViiFfWMTZtCgDOq51uQHCiAUuwI4XeYL0ATAk,10209
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=rpJbiCUfgJrjp-xVe4JiXjVNe6-l8-s8uPqzKROgmJQ,1137
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=w1a-RPnLnZRyaKUK4tWgFks7eO1pEgEmcWH038oepcc,3172
scipy/optimize/tests/test_least_squares.py,sha256=VsOQEuWBqDCg23xzkhvSikxSvqVBKlZpLz4RsdXHL9g,30600
scipy/optimize/tests/test_linear_assignment.py,sha256=j_Pptr3AxAc7xIC9RMbkBSEjbBW_is1czPSNXTEErqE,1352
scipy/optimize/tests/test_linesearch.py,sha256=tdwnEhBWXIVxSDTjOtuYqnuRalGc5G1uGOORJorj3Z4,10830
scipy/optimize/tests/test_linprog.py,sha256=7CVOVN_lSHixyRBtPo5DF_zrCB5wDIjuBkJLjCMqXWY,78324
scipy/optimize/tests/test_lsq_common.py,sha256=7C2pKF1Y-NeuIERwbE_k34nwg2ub7dU1fZWPWCnqrCY,9524
scipy/optimize/tests/test_lsq_linear.py,sha256=qRbdQ0desjCZhJQHixi0TpvMNUPxvcNS0qoYr6MOJI8,7237
scipy/optimize/tests/test_minimize_constrained.py,sha256=JgkcVp7MZkF-3yND2g8vRcLVgggqL3_BGQZ8yBKyct4,21585
scipy/optimize/tests/test_minpack.py,sha256=moG-XexLqkey4OcemGzw84lxiLzS2Li6Vct6WhzR7S8,32768
scipy/optimize/tests/test_nnls.py,sha256=xZxuU8waOVcfeUpn6HtpC4OcdBqsMUaNjzScZymRPZw,922
scipy/optimize/tests/test_nonlin.py,sha256=PrhJO_jI5EMQKmP7h9vJPcGR5r9Crb_0Uoci-gbL25Y,14970
scipy/optimize/tests/test_optimize.py,sha256=1PyqpqVhVVovDCL1myiCxqV3B6cz1_xuk1DxJlL8O8I,86624
scipy/optimize/tests/test_quadratic_assignment.py,sha256=1nyc9WjN62udgxna7smGjJa4UOWYw3c8GNywa7kF1Do,16317
scipy/optimize/tests/test_regression.py,sha256=AAY3ao7qZM-fSGubSUrBwl_z2HNKyzPaO56zkDgU8Lo,1085
scipy/optimize/tests/test_slsqp.py,sha256=qRVGBaQIrhKHnHgS0ZPZAdDiJMZvXkxENDFuK85E-x8,23210
scipy/optimize/tests/test_tnc.py,sha256=0qcLH8SRi1Lbg0qQw2u0Gpt3p7T5LokwJwaz3prWw-8,11197
scipy/optimize/tests/test_trustregion.py,sha256=t1BYmXc-5JKbc8HVWilasJMUY_Cnbr3cx8ZLnbYc-hU,4370
scipy/optimize/tests/test_trustregion_exact.py,sha256=orDAwGlfRZNjdc8zyV_lvHdjJHc_V_BYMYeEvvnjwss,12978
scipy/optimize/tests/test_trustregion_krylov.py,sha256=2d9U5BmYdRejeFZPGYJIKWVBY_tQRofL1Yf1CVj5jDk,6595
scipy/optimize/tests/test_zeros.py,sha256=WuE5YMKHwH7w17gck5A0IXjbNrbXy_0VCnRPOhId8eQ,27873
scipy/optimize/tnc.py,sha256=ZX2ORs9FIrNO5fo4oTnSyw3gyZob7NEWzpzJxnmWzdY,17353
scipy/optimize/zeros.py,sha256=FkvPq3M-cGe3adu4Yvnm3kCR8TdAlYOBPZDvDaFYbyA,50241
scipy/setup.py,sha256=I-PucsfvOidhC8ZGgZOuWBL4EJKYwzakgVINescDUAA,1175
scipy/signal/__init__.py,sha256=F0Z6TP3pzl-7ePOVJ_aVLdCAPtL1DaMz0u2ExPeNzsA,14759
scipy/signal/__pycache__/__init__.cpython-39.pyc,,
scipy/signal/__pycache__/_arraytools.cpython-39.pyc,,
scipy/signal/__pycache__/_max_len_seq.cpython-39.pyc,,
scipy/signal/__pycache__/_peak_finding.cpython-39.pyc,,
scipy/signal/__pycache__/_savitzky_golay.cpython-39.pyc,,
scipy/signal/__pycache__/_upfirdn.cpython-39.pyc,,
scipy/signal/__pycache__/bsplines.cpython-39.pyc,,
scipy/signal/__pycache__/filter_design.cpython-39.pyc,,
scipy/signal/__pycache__/fir_filter_design.cpython-39.pyc,,
scipy/signal/__pycache__/lti_conversion.cpython-39.pyc,,
scipy/signal/__pycache__/ltisys.cpython-39.pyc,,
scipy/signal/__pycache__/setup.cpython-39.pyc,,
scipy/signal/__pycache__/signaltools.cpython-39.pyc,,
scipy/signal/__pycache__/spectral.cpython-39.pyc,,
scipy/signal/__pycache__/waveforms.cpython-39.pyc,,
scipy/signal/__pycache__/wavelets.cpython-39.pyc,,
scipy/signal/_arraytools.py,sha256=qHqX1pgjguFawwag8J81ZEQMAa2J64FBUG7ihSGGBWQ,7489
scipy/signal/_max_len_seq.py,sha256=S5Ez5rJL_JGLXsq3kY7NUI8psweEt-_hEs5jOL_JNHg,4919
scipy/signal/_max_len_seq_inner.cp39-win_amd64.pyd,sha256=Ynwyh7BrhP-cKW93hGGP9_Nm1f2PdKI-PN7FlHdHdlo,130048
scipy/signal/_peak_finding.py,sha256=Ni_X68zYOSqX5zHSC1iPfJsl78R9TUHlf8Rp_UWTyvU,48475
scipy/signal/_peak_finding_utils.cp39-win_amd64.pyd,sha256=_fR7Hh4sbEVb-ZQROMnvlq0yehhPU0MDwmQrLXYT02k,177664
scipy/signal/_savitzky_golay.py,sha256=C-zdJtMrB0SWsAcJ8-tMpJJYbmsd3rGk9GIiLayAqj0,13192
scipy/signal/_sosfilt.cp39-win_amd64.pyd,sha256=wkWoPlCr3mo5WWwcWxvePVOQdnDzEQDNfBDuRyBse7U,196608
scipy/signal/_spectral.cp39-win_amd64.pyd,sha256=71SQO7IPdyuA_QOCrXkzjrDg_X5_JDrVmHTe8ZYvNkE,43008
scipy/signal/_upfirdn.py,sha256=ll1xFXo9rPsXoZaE4m_YAQONsZAuJ7bExdeZ2SqOeQc,7887
scipy/signal/_upfirdn_apply.cp39-win_amd64.pyd,sha256=aEeS404oXKABy5uOheGTiKJVX37jrt_ObUXGApT7QBY,232960
scipy/signal/bsplines.py,sha256=DzkTaIYVTb2GzLras3VWiHSfHqWC0wRRK0CegcYOoSo,19336
scipy/signal/filter_design.py,sha256=llnirW_iBPSAiqWK7ptMuW_QRO6jTagph_8tUTpBFgE,177545
scipy/signal/fir_filter_design.py,sha256=L9ErexYfIW7Nyrab-f8BwCVDKFXmoYw9d8EalKxaQLY,47827
scipy/signal/lti_conversion.py,sha256=lwMFiX83QxemcwmHuXuGvv7h7IwNpENGcF48dTOVGjI,15067
scipy/signal/ltisys.py,sha256=ivk6fV0YiKlFDA36hRRZN0n_Y1h09__Ng4aEVfSDHC8,128113
scipy/signal/setup.py,sha256=3uJ-mDjZiPKGAKLGhgLvgDGyfwMBKCpwLHwiGTxfHuI,1568
scipy/signal/signaltools.py,sha256=oWnD3wXGvZqioV6uhxxIDQq5m9amrkYFJFs-SxWA2og,151257
scipy/signal/sigtools.cp39-win_amd64.pyd,sha256=Ee7cC_rwWB1JjALIe1-v8AAySswA0_kDx0ymBMlHWGU,97792
scipy/signal/spectral.py,sha256=vkFoURoKGdMQS1W_mjhAlQseg1UJVmLoNqhIehOxxGU,73849
scipy/signal/spline.cp39-win_amd64.pyd,sha256=i8E2z6BoTUJQLNckNTwY1N0tD_rlyL0AwVPX12rC1tY,38400
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/signal/tests/__pycache__/mpsig.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_array_tools.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_bsplines.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_cont2discrete.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_dltisys.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_filter_design.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_fir_filter_design.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_ltisys.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_max_len_seq.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_peak_finding.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_result_type.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_savitzky_golay.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_signaltools.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_spectral.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_upfirdn.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_waveforms.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_wavelets.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_windows.cpython-39.pyc,,
scipy/signal/tests/mpsig.py,sha256=YgVx225S9MdKF-JcDBu3ao9qOQyNhpND53ACAbOerA4,3332
scipy/signal/tests/test_array_tools.py,sha256=4udO9R8h_th-JF4iF76GE3c9LCyoqsgxg9h8VVhIgus,3640
scipy/signal/tests/test_bsplines.py,sha256=xP-k8faOUaDUTlUJhwQr54LMQixkbz7nAuJTDj1rvCs,13029
scipy/signal/tests/test_cont2discrete.py,sha256=tOD1YD5pTo0EVrKzOWAjbP-JBLN5pxE9PSbJR3Op7dY,14837
scipy/signal/tests/test_dltisys.py,sha256=ygYZlXYxjE5Ol3rAB64vYI5dPE9pL5UN4PnKby33mqM,21622
scipy/signal/tests/test_filter_design.py,sha256=fVkFPpmOzdJJd18wH3B8zJU8qbVtyeu041BwkxvHaSc,172943
scipy/signal/tests/test_fir_filter_design.py,sha256=oU2lexetsZyLl3eYQBgABvAco4nwGAHLi-kjrQ8bjyQ,27327
scipy/signal/tests/test_ltisys.py,sha256=Q4evKEnUwHxXsWlTjBc4KdYTQaa_4AqwKTXyNcuK7wk,46839
scipy/signal/tests/test_max_len_seq.py,sha256=qZHeoZt8nbkznZlHWBSO-PUjeeyeezMUrNcMX5yf7Qw,3114
scipy/signal/tests/test_peak_finding.py,sha256=ypIV47urlqcANfiuKb0KXppVyiG1SVfKXVuu9uIHv-8,32573
scipy/signal/tests/test_result_type.py,sha256=X22XwfonYn9ylhB_T0Z025flaBFC4vLswdnc0l0jnm8,1642
scipy/signal/tests/test_savitzky_golay.py,sha256=qXJ_mxeXd7rJ5uj26-CQdl6EWr-HxvhT40xSA7ZAnWc,10203
scipy/signal/tests/test_signaltools.py,sha256=aLmh3jfpr5DhDvy0skqZL_O_ZiX3phP95hkyh51Pro4,130387
scipy/signal/tests/test_spectral.py,sha256=YdJMVn-NxyQTIfMbtnIstUqnSM-VYBnLyd8GdCfYSYY,53294
scipy/signal/tests/test_upfirdn.py,sha256=Gx1ctttnqNMx_jBlhfbECkGBKI10sfC2023ge1EyXOQ,10733
scipy/signal/tests/test_waveforms.py,sha256=Qz4cEO2VatKoT-Qe4dQZZhHbgvIOmwxuuMa6rNJPMEc,12006
scipy/signal/tests/test_wavelets.py,sha256=_06vkPJDDQznGP9rZ6L_mCUQSy3ts9xzih11hIfIYUY,5948
scipy/signal/tests/test_windows.py,sha256=WDh38TSoG-B8E5C-fRvwbAZZj_m0Tr6cmkGzJx9vTJc,36037
scipy/signal/waveforms.py,sha256=0vJFo6QkIy9h7WYuN5hIU9M1fXm-5hugEXOGMcg_os0,20287
scipy/signal/wavelets.py,sha256=si6wpErd30ecBrg6RDtAKaTPdd2Hkc-a2oHKu47OmHE,14046
scipy/signal/windows/__init__.py,sha256=1VOHPDi_UFYDn89isP-dM-j7fA5d_zZ48YwKa0c3l7I,1770
scipy/signal/windows/__pycache__/__init__.cpython-39.pyc,,
scipy/signal/windows/__pycache__/setup.cpython-39.pyc,,
scipy/signal/windows/__pycache__/windows.cpython-39.pyc,,
scipy/signal/windows/setup.py,sha256=XinKZEN3EnwKh59V1Y1qKh3uAZtCkHHAPsMDYpU4-lM,228
scipy/signal/windows/windows.py,sha256=yqfL-1-RgAh_5mdDP7sGQnP_FR7Ucb6KepWz6YKG5H4,75901
scipy/sparse/__init__.py,sha256=dAk4T9Vmwjk2yIwj9jZtIVbaIyz3gPhUsMxTYv2ZhAg,6847
scipy/sparse/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/__pycache__/_index.cpython-39.pyc,,
scipy/sparse/__pycache__/_matrix_io.cpython-39.pyc,,
scipy/sparse/__pycache__/base.cpython-39.pyc,,
scipy/sparse/__pycache__/bsr.cpython-39.pyc,,
scipy/sparse/__pycache__/compressed.cpython-39.pyc,,
scipy/sparse/__pycache__/construct.cpython-39.pyc,,
scipy/sparse/__pycache__/coo.cpython-39.pyc,,
scipy/sparse/__pycache__/csc.cpython-39.pyc,,
scipy/sparse/__pycache__/csr.cpython-39.pyc,,
scipy/sparse/__pycache__/data.cpython-39.pyc,,
scipy/sparse/__pycache__/dia.cpython-39.pyc,,
scipy/sparse/__pycache__/dok.cpython-39.pyc,,
scipy/sparse/__pycache__/extract.cpython-39.pyc,,
scipy/sparse/__pycache__/generate_sparsetools.cpython-39.pyc,,
scipy/sparse/__pycache__/lil.cpython-39.pyc,,
scipy/sparse/__pycache__/setup.cpython-39.pyc,,
scipy/sparse/__pycache__/sparsetools.cpython-39.pyc,,
scipy/sparse/__pycache__/spfuncs.cpython-39.pyc,,
scipy/sparse/__pycache__/sputils.cpython-39.pyc,,
scipy/sparse/_csparsetools.cp39-win_amd64.pyd,sha256=UZOHZgNDxnVq8iYDriBXl-2qzljtetukP5Arc80vyvE,463872
scipy/sparse/_index.py,sha256=z3GOtccPh2zk6iefcOTbduVamkN6tmVqjvj99OzqME8,12149
scipy/sparse/_matrix_io.py,sha256=YPbODNIOqR57Rf2TAzRMuDGtH77H5_xAPb7Hl3i-xDw,5333
scipy/sparse/_sparsetools.cp39-win_amd64.pyd,sha256=ckRVxwaSgyu_ub4vOhUP60v8q1OIv_JY_SyAX-qnuAk,2459648
scipy/sparse/base.py,sha256=JidjxXbn5A-nlAN61iZsYe1ckmOcz3yE_MtQSsSHhXI,42103
scipy/sparse/bsr.py,sha256=1IK_QLrhYHq8M936hAHxf-h-6WJ1YnvdO7rsyEQTQQ4,24906
scipy/sparse/compressed.py,sha256=SDAZIqLmAPa1HTBst7b05bYAw2ROzPM1dUocMU6BOoc,50551
scipy/sparse/construct.py,sha256=GnXqcLVCwbHkweZa40MwRZm6e8GkaX0hC3duSP4zJKI,26836
scipy/sparse/coo.py,sha256=LCsqHtdf_hnrrEqTlhfmkThEitkBf62uBsbMgt7aR00,22079
scipy/sparse/csc.py,sha256=q8rr5OrguE2_INKVdm04QNtmbYZfNJBgHa8WuQ7S76w,7889
scipy/sparse/csgraph/__init__.py,sha256=xYvQ3yEASYrBjeHX3Ft8Ym__zmj3Paz12Uh5z6ELsyU,7658
scipy/sparse/csgraph/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/csgraph/__pycache__/_laplacian.cpython-39.pyc,,
scipy/sparse/csgraph/__pycache__/_validation.cpython-39.pyc,,
scipy/sparse/csgraph/__pycache__/setup.cpython-39.pyc,,
scipy/sparse/csgraph/_flow.cp39-win_amd64.pyd,sha256=_oZBJV7sd_tGhp0uV2IKdXGcwLtC9BZWAlE-KSTTWFM,187904
scipy/sparse/csgraph/_laplacian.py,sha256=lMOqZ8rPm4_3fX0idssJ_U-JvbgC7DqzS_uJfLSoBJ8,4024
scipy/sparse/csgraph/_matching.cp39-win_amd64.pyd,sha256=2KRfUJuMA3AQxFRPCVz-Agg6f19siH1ymtdDno_KHeQ,215552
scipy/sparse/csgraph/_min_spanning_tree.cp39-win_amd64.pyd,sha256=mBiKYYxchTPzeAclDbjUk9zBUZ8_6B5YIptRy5m7Vz0,145920
scipy/sparse/csgraph/_reordering.cp39-win_amd64.pyd,sha256=bQbkX3OTpBVWSXwPkHLsB1ywzrsmugBcW9pruU7Qzcg,195072
scipy/sparse/csgraph/_shortest_path.cp39-win_amd64.pyd,sha256=J5ICrv9YfczleB_diPrGBvlGjgJ9Q8GV8nwv0EHHtIU,311808
scipy/sparse/csgraph/_tools.cp39-win_amd64.pyd,sha256=qo-oinx1rjw7-CySZJmIrzlAuHv2trLj9CBxc8VheMs,119296
scipy/sparse/csgraph/_traversal.cp39-win_amd64.pyd,sha256=WvHaavWKfunw4omTdew9176gZ7Ar_B9vVpSIN9y2vz4,113152
scipy/sparse/csgraph/_validation.py,sha256=QNT8OusAIavxH4C989ljtgnduh61H6RBzqk3xRIO8Ho,2327
scipy/sparse/csgraph/setup.py,sha256=hAWJsFo4-YNix-AKUqEkUROyUfe7l4c7I9D-V5XOPQc,1099
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_connected_components.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_conversions.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_flow.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_graph_laplacian.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_matching.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_reordering.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_shortest_path.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_spanning_tree.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_traversal.cpython-39.pyc,,
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=DKsvhuW2BgDvMAa-MJ4GlYvyIDIOVe58QjxUhQ5yfgQ,3199
scipy/sparse/csgraph/tests/test_conversions.py,sha256=Y48qwFRsE4tTxFYS_Bn8ndCkAwe8n0rovbaVYppCy34,1855
scipy/sparse/csgraph/tests/test_flow.py,sha256=labfcqPxNTI7HTwq7vvSuHfB1F2l89mOqe9_eyp9LXc,4688
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=niOY4XdSKvQL78daSgTskolNl9zangCEJ9tWtpsYgic,4322
scipy/sparse/csgraph/tests/test_matching.py,sha256=8jycUxPF5QktXeDOQCQasQQLNrCwvgvGaNov6bQv-hw,6593
scipy/sparse/csgraph/tests/test_reordering.py,sha256=by-44sshHL-yaYE23lDp1EqnG-72MRbExi_HYSMJEz8,2613
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=2s8vELSKUUfgzksAI_CyUUUm4cwKhh1NKv_-nnX6EEw,12026
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=N76Z0c9xb988h00y7RhFZl-F643MIru5YW5Yys1Rxfo,2115
scipy/sparse/csgraph/tests/test_traversal.py,sha256=bdZc-7WE4SPhyL2SLUdsKC-B_DNmscl4Z5bO9zNrh6k,2325
scipy/sparse/csr.py,sha256=VgDSpLeGWenyAn04kq99sxH9IUMxzICcL67CAMOwdfk,11609
scipy/sparse/data.py,sha256=y12D6iaBBl1QBxEbqv0gB-db2cJ7Gs15CE-_NRhUskU,12774
scipy/sparse/dia.py,sha256=lRrzNMZt9WUCZ8x4LYXNLtBoozmVKGQHbyqDDe9aGBU,14431
scipy/sparse/dok.py,sha256=1dXLgM3TPK05UZBO7KoTDthZFK6h8GduZjFAcKMg_qk,15691
scipy/sparse/extract.py,sha256=Yi2ENMh2b1PbhjGsc9dJsSHzz6eCbLWmADBWPmsobq8,4647
scipy/sparse/generate_sparsetools.py,sha256=e2q_gNOR0UmaSoifgjBwoXpYRxlZp4sdz1BiE4-GHvg,12574
scipy/sparse/lil.py,sha256=HvUwj4XkYJJRSYyfdpuXC427NrMH_QChlxLHFIdgS6E,18208
scipy/sparse/linalg/__init__.py,sha256=LJWI6jiGHLm7ktC2PKXWXzaYJ-sAdoKj6_seGo-vwWY,3365
scipy/sparse/linalg/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/_expm_multiply.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/_norm.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/_onenormest.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/interface.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/matfuncs.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/setup.cpython-39.pyc,,
scipy/sparse/linalg/_expm_multiply.py,sha256=68ZH6fG_rAmjqMccX7ZfAkmcPLM6gtidQOeOBsPPUQE,21989
scipy/sparse/linalg/_norm.py,sha256=jLcIWeaxGIp1td4YE5oduXxFASo-f_PILtg-orU8P8o,5675
scipy/sparse/linalg/_onenormest.py,sha256=keifZZ-XYKdm9kftXTGTJOTUljpR-gbozIY0y_dCB3g,15462
scipy/sparse/linalg/dsolve/SuperLU/License.txt,sha256=8M7fUlA7LUK4NBGgoW5v76w0bfrY_dxm9QBQFQEjRww,1681
scipy/sparse/linalg/dsolve/__init__.py,sha256=Uv0aFjY09J_4x2K64oWUB8n_fywWFZ5tp68VOkUt0T4,1887
scipy/sparse/linalg/dsolve/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/dsolve/__pycache__/_add_newdocs.cpython-39.pyc,,
scipy/sparse/linalg/dsolve/__pycache__/linsolve.cpython-39.pyc,,
scipy/sparse/linalg/dsolve/__pycache__/setup.cpython-39.pyc,,
scipy/sparse/linalg/dsolve/_add_newdocs.py,sha256=bkOjuw5cUx2i2PN9FTgpiieUH5krX8HV9LLx1YlAEAg,3787
scipy/sparse/linalg/dsolve/_superlu.cp39-win_amd64.pyd,sha256=5bvvgZnCaGQyR_CKEdNmZmMDRmqkPJLTzsN0gvMPzUM,291840
scipy/sparse/linalg/dsolve/linsolve.py,sha256=GhF3NwO3VKcecHhVUXFhsjH7FFo0rzYxaODHVe5wOtI,21172
scipy/sparse/linalg/dsolve/setup.py,sha256=eDcje3Nsg1bPg2UqnI__VR6E1PHOKsfjKZAAFvvG7Yw,1616
scipy/sparse/linalg/dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/dsolve/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/dsolve/tests/__pycache__/test_linsolve.cpython-39.pyc,,
scipy/sparse/linalg/dsolve/tests/test_linsolve.py,sha256=6cHJoOKtLlfmaLlzDL4MHAClNPvcaNERwNKW-tF0UHc,26420
scipy/sparse/linalg/eigen/__init__.py,sha256=k22v949nctxPiWcqZWtDUrA7vD9tGKD--iMiPnIWERM,373
scipy/sparse/linalg/eigen/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/eigen/__pycache__/setup.cpython-39.pyc,,
scipy/sparse/linalg/eigen/arpack/ARPACK/COPYING,sha256=CSZWb59AYXjRIU-Mx5bhZrEhPdfAXgxbRhqLisnlC74,1892
scipy/sparse/linalg/eigen/arpack/__init__.py,sha256=zDxf9LokyPitn3_0d-PUXoBCh6tWK0eUSvsAj6nkXI0,562
scipy/sparse/linalg/eigen/arpack/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/eigen/arpack/__pycache__/arpack.cpython-39.pyc,,
scipy/sparse/linalg/eigen/arpack/__pycache__/setup.cpython-39.pyc,,
scipy/sparse/linalg/eigen/arpack/_arpack.cp39-win_amd64.pyd,sha256=9ERSA2u7b3vcHJrPCe5JXKlLwAUjlSd6DFZUzHuArV4,145920
scipy/sparse/linalg/eigen/arpack/arpack.py,sha256=N5z_G1ZpAOPmNH2el62Lfny8eVNBWiAv_WfNU33EUeY,74656
scipy/sparse/linalg/eigen/arpack/setup.py,sha256=nF6Mpi71sVUB_rGWMn2AlIh4hMSAsJS-li-nN5M9KDg,1845
scipy/sparse/linalg/eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/eigen/arpack/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/eigen/arpack/tests/__pycache__/test_arpack.cpython-39.pyc,,
scipy/sparse/linalg/eigen/arpack/tests/test_arpack.py,sha256=259afsMtPwEcV5Yh8BmJV8hqmYn1hujfesNg346Sirc,34200
scipy/sparse/linalg/eigen/lobpcg/__init__.py,sha256=E5JEPRoVz-TaLrj_rPm5LP3jCwei4XD-RxbcxYwf5lM,420
scipy/sparse/linalg/eigen/lobpcg/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/eigen/lobpcg/__pycache__/lobpcg.cpython-39.pyc,,
scipy/sparse/linalg/eigen/lobpcg/__pycache__/setup.cpython-39.pyc,,
scipy/sparse/linalg/eigen/lobpcg/lobpcg.py,sha256=3hxIJpLiMBTvFEx7kGwJBeX-3Zg-U7RxdLaD2eeGJJE,26099
scipy/sparse/linalg/eigen/lobpcg/setup.py,sha256=FMeUMxkvblDzpeVv5V0JJRX5Fs9HDQea-ahPvDCiTcA,344
scipy/sparse/linalg/eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/eigen/lobpcg/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/eigen/lobpcg/tests/__pycache__/test_lobpcg.cpython-39.pyc,,
scipy/sparse/linalg/eigen/lobpcg/tests/test_lobpcg.py,sha256=_8OaYm2_yyDTPnugEXcDgZNpbE6GXEzX4tTuxXwpwCM,13190
scipy/sparse/linalg/eigen/setup.py,sha256=ZmSBXFjJUnRisy_fZvBlcQD6OtPRZP2VeVJP5LO3nZo,387
scipy/sparse/linalg/interface.py,sha256=01BdRoMC1_JTjGutu_wJ86vxEb1nj35ys3rw8rTv80U,25578
scipy/sparse/linalg/isolve/__init__.py,sha256=Ad2iqyDZrSep1WNETu0eTFY06S6T8qQvyYG4x3Hi0eM,378
scipy/sparse/linalg/isolve/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/isolve/__pycache__/_gcrotmk.cpython-39.pyc,,
scipy/sparse/linalg/isolve/__pycache__/iterative.cpython-39.pyc,,
scipy/sparse/linalg/isolve/__pycache__/lgmres.cpython-39.pyc,,
scipy/sparse/linalg/isolve/__pycache__/lsmr.cpython-39.pyc,,
scipy/sparse/linalg/isolve/__pycache__/lsqr.cpython-39.pyc,,
scipy/sparse/linalg/isolve/__pycache__/minres.cpython-39.pyc,,
scipy/sparse/linalg/isolve/__pycache__/setup.cpython-39.pyc,,
scipy/sparse/linalg/isolve/__pycache__/utils.cpython-39.pyc,,
scipy/sparse/linalg/isolve/_gcrotmk.py,sha256=LDNkzeNptmDYi6hmf0hvMDng7CUC5SxKKmlnKz3C7iI,15522
scipy/sparse/linalg/isolve/_iterative.cp39-win_amd64.pyd,sha256=bIBCd7msZqnOBEU6RTZdkYsz2TKRGs5GdI0B5mzTMbQ,117248
scipy/sparse/linalg/isolve/iterative.py,sha256=4Gu5_gLvqmL7pkvmMjrZ8skK0AwCduA7f9Mqt0EH5Ak,27902
scipy/sparse/linalg/isolve/lgmres.py,sha256=RbgO2GPx9UIszeREn8ANVnc1IC8TfsxKDS_9SpIg8wQ,8871
scipy/sparse/linalg/isolve/lsmr.py,sha256=oJQll3eM53nWrrdBfyBbtLUty7BQQITLRXIFOHNSz-Q,15427
scipy/sparse/linalg/isolve/lsqr.py,sha256=Gat5MvOEMZMMDU9QuS52htXjik57_dMAvIHRiIo1qUo,19995
scipy/sparse/linalg/isolve/minres.py,sha256=vwDWT_myC1pQVRHkGBuHQ1wk0DUo7j3u7DuvXOhfMH0,10908
scipy/sparse/linalg/isolve/setup.py,sha256=VC-MGJehE9Rpd-O8Wdyue93iqfC7sPxJ2v-wHtG8F_Q,1678
scipy/sparse/linalg/isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/isolve/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/isolve/tests/__pycache__/demo_lgmres.cpython-39.pyc,,
scipy/sparse/linalg/isolve/tests/__pycache__/test_gcrotmk.cpython-39.pyc,,
scipy/sparse/linalg/isolve/tests/__pycache__/test_iterative.cpython-39.pyc,,
scipy/sparse/linalg/isolve/tests/__pycache__/test_lgmres.cpython-39.pyc,,
scipy/sparse/linalg/isolve/tests/__pycache__/test_lsmr.cpython-39.pyc,,
scipy/sparse/linalg/isolve/tests/__pycache__/test_lsqr.cpython-39.pyc,,
scipy/sparse/linalg/isolve/tests/__pycache__/test_minres.cpython-39.pyc,,
scipy/sparse/linalg/isolve/tests/__pycache__/test_utils.cpython-39.pyc,,
scipy/sparse/linalg/isolve/tests/demo_lgmres.py,sha256=soqwOMRudGCpiqk2Pne2SeL42o9dHmgG5iAmN4KtSaw,1588
scipy/sparse/linalg/isolve/tests/test_gcrotmk.py,sha256=sF8XVKxU_5hYy6gqtObfYBQjDdxkP-Zf6NE3I_qagf4,5413
scipy/sparse/linalg/isolve/tests/test_iterative.py,sha256=KwMIsz_pfH9WPzkW4JWMM6wI4Dr6g5qwXs0Gc38E_7c,24343
scipy/sparse/linalg/isolve/tests/test_lgmres.py,sha256=fhFRgWxZEiDw-bcFYTEksPiWBJRUO7AZRtzhsTm32wU,7066
scipy/sparse/linalg/isolve/tests/test_lsmr.py,sha256=AyOiN18mZ2Y_eo8JOzLzt2sUTjdlPrxJ2T60rZ2AIyo,7085
scipy/sparse/linalg/isolve/tests/test_lsqr.py,sha256=yqvCMLCJp0WG780s6Q-elhs73RxrMUZbDQGQQaL0JZ8,4167
scipy/sparse/linalg/isolve/tests/test_minres.py,sha256=FYa2sXga8uIBCw-v2vTDSq8Ta6siIEQF3MBdOvJz4N0,2454
scipy/sparse/linalg/isolve/tests/test_utils.py,sha256=njDImhUzWXkr2x4ICNnfTvZfvMbdWSnAS0mpDOf90SQ,235
scipy/sparse/linalg/isolve/utils.py,sha256=SJBrwzAPI5SWVsq_MN-Zmwn2NrgnVomh-Lbr-jsPgys,3417
scipy/sparse/linalg/matfuncs.py,sha256=EwRbMyd2ds-RaYuc4OOAqbhgkXxasJ9pgmQibbUq2Pc,27178
scipy/sparse/linalg/setup.py,sha256=gyAUYbcDmDsAf0Yc8YUlhDIQpCQC_QOMCCwfFDt2rss,459
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_expm_multiply.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_interface.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_matfuncs.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_norm.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_onenormest.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_pydata_sparse.cpython-39.pyc,,
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=qzJshDMHAkkACb_bpknl0ohHnX2JVrp87wnS0kbRkRM,9585
scipy/sparse/linalg/tests/test_interface.py,sha256=_EOm6bhb2UBB_w07Z_JB0WBOXgPRQTU1HK7hlXsf4Qw,16568
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=NGg7OBCmnNdFeYe_-FpwI_MLWZPAp432LFC-DVPOxnA,21254
scipy/sparse/linalg/tests/test_norm.py,sha256=8A7DaNp9_jhBIEo_zFtWzTEGV-Xuar8Zq3SjwImu7To,5361
scipy/sparse/linalg/tests/test_onenormest.py,sha256=r-go8dkNfUfDhwo3Rqk2jVR7Z7fY3LPJ_l78eu9hkAw,9245
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=S-VT77WXHevayDm3kDyM60PeiZTiMyHbECJx4goysRo,5954
scipy/sparse/setup.py,sha256=QKmPgJ7SZp6KQO4TTUItw90ymF9niQgJYrVBka6RpBs,2231
scipy/sparse/sparsetools.py,sha256=7xnma49tJ1-ZTCenVgytkQPqz7AOiVwtE4ZwiTAdJ2s,711
scipy/sparse/spfuncs.py,sha256=cTCdzEMka5D-LPmAkyiv7eYntwqZuUeC3jg4sJC6enA,2756
scipy/sparse/sputils.py,sha256=QWmkPhaGVsn_TPkq00CjpAEOZbjpZ37u3-F2tyhk6HE,11305
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_base.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_construct.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_csc.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_csr.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_extract.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_matrix_io.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_sparsetools.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_spfuncs.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_sputils.cpython-39.pyc,,
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_base.py,sha256=ctHyA0LDRxpEbmwqc50z-5o3NrRx6V1GIjw-xczAwd0,177062
scipy/sparse/tests/test_construct.py,sha256=u2bqVoXVCZkDIDq5mzvyUwItKSPLnsxf6LiNyO1QDbo,21996
scipy/sparse/tests/test_csc.py,sha256=HGHj4PHm1rChguz2tMwNYtEu6kgF6DW65x7XSv0ZXX0,2061
scipy/sparse/tests/test_csr.py,sha256=EMUC2IZxuTb7B3D0ticdJjAxIqdA1CTy-jMgLs0qa0s,3517
scipy/sparse/tests/test_extract.py,sha256=1XJdEn3JHVAoxh6JCT6BlCZTIY4PvuIvmLiMsYhPfaU,1317
scipy/sparse/tests/test_matrix_io.py,sha256=noYtJloHSLEAibzBAKRmRs4Ep2hgYT3Up1J3wQyJpGA,2550
scipy/sparse/tests/test_sparsetools.py,sha256=BmBdYAwswym-rMDgDuaN7SOBLuFXgEiMHZm2WIckKgE,10149
scipy/sparse/tests/test_spfuncs.py,sha256=Q-apz9tRCfFbTdhRHoOz4Phw21eu0hM2krCTUdUqls0,3285
scipy/sparse/tests/test_sputils.py,sha256=v7u_FtnI1AYZmnFBLyiCCLV_9oBQBVJJuFE2iA8_i8c,6717
scipy/spatial/__init__.py,sha256=en31tYYSXrU-4CKpU-kWRF2A8pRt4VaAUALAWhacAek,3360
scipy/spatial/__pycache__/__init__.cpython-39.pyc,,
scipy/spatial/__pycache__/_geometric_slerp.cpython-39.pyc,,
scipy/spatial/__pycache__/_plotutils.cpython-39.pyc,,
scipy/spatial/__pycache__/_procrustes.cpython-39.pyc,,
scipy/spatial/__pycache__/_spherical_voronoi.cpython-39.pyc,,
scipy/spatial/__pycache__/distance.cpython-39.pyc,,
scipy/spatial/__pycache__/kdtree.cpython-39.pyc,,
scipy/spatial/__pycache__/setup.cpython-39.pyc,,
scipy/spatial/_distance_wrap.cp39-win_amd64.pyd,sha256=vrExk80zfjMLiUkKajThwwlco6UTxfCCnSrbHbbi6wg,113152
scipy/spatial/_geometric_slerp.py,sha256=ZRhiQw77VFJWQYXy48gTYA8m0cv03uYWa8iEkPqkZTg,7668
scipy/spatial/_hausdorff.cp39-win_amd64.pyd,sha256=JXgVPRb7OnJAnEhjCSFuFUu5Tw058tpChgfD-8-_nZ8,136704
scipy/spatial/_plotutils.py,sha256=hNFmbbN4B7eiyGLaCASJ7KE2bwYy2AydPMv18IJzEOM,6957
scipy/spatial/_procrustes.py,sha256=ZnB8P0CjTXJJCd702GxcQjHNnHCxmVVHm7vMh5OWi6A,4400
scipy/spatial/_spherical_voronoi.py,sha256=g-IdrW5PO2ESNsG9ASnrJjxystxeFEXMf2HHdRXVqnw,13695
scipy/spatial/_voronoi.cp39-win_amd64.pyd,sha256=-3tp5oLFfLdME7s1ngY0AXq20fPaIAcjmts1xwxTYV4,135680
scipy/spatial/_voronoi.pyi,sha256=O0O1J1x1rfhJwJmiBYohBD55WhL2124tVdFNNAxj-0M,136
scipy/spatial/ckdtree.cp39-win_amd64.pyd,sha256=9kWY6-lJRm9V-wdzMjnjD7N_iQ-EV5oflf3DUwvETAE,511488
scipy/spatial/ckdtree.pyi,sha256=NAmEHC6efvrr2kPEqd6EABC_FmLka7Ctzcu879vYufQ,19
scipy/spatial/distance.py,sha256=KIp3RTs8y9U--WBpAQgO-JjY7J4_cjxsr4NPxvjhhbY,87252
scipy/spatial/kdtree.py,sha256=4ezRn07v9ddKnoi6Pb_N9wJ4VxhY2ecAy1ac_4bQU08,33706
scipy/spatial/qhull.cp39-win_amd64.pyd,sha256=CVs5eAd4oIOqz2j7JL7RWIzfWxx4JNapqkxi5f0TJCo,823296
scipy/spatial/qhull_src/COPYING.txt,sha256=NNsMDE-TGGHXIFVcnNei4ijRKQuimvDy7oDEG7IDivs,1635
scipy/spatial/setup.py,sha256=X6c2ufdTm7PqeYPfh1oopFB-IPBS6VvnV2n_-qjRg-g,3369
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test__plotutils.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test__procrustes.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_distance.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_hausdorff.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_kdtree.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_qhull.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_slerp.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_spherical_voronoi.cpython-39.pyc,,
scipy/spatial/tests/data/cdist-X1.txt,sha256=ULnYAgX2_AwOVF-VE7XfnW5S0pzhx7UAoocxSnXMaWs,5750
scipy/spatial/tests/data/cdist-X2.txt,sha256=_IJVjXsp3pvd8NNPNTLmVbHOrzl_RiEXz7cb86NfvZ4,11500
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=k19QSfkqhMmByqNMzwWDmM6wf5dt6whdGyfAyUO3AW0,15000
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=5Z9SMsXrtmzeUwJlVmGkrPDC_Km7nVpZIbBl7p3Hdc0,50000
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Yerj1wqIzcdyULlha-q02WBNGyS2Q5o2wAr0XVEkzis,178801
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=NEd2b-DONqUMV9f8gJ2yod17C_5fXGHHZ38PeFsXkyw,3041
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=UCWZJeMkMajbpjeG0FW60b0q-4r1geAyguNY6Chx5bM,178801
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=8Iq7cF8oMJjpqd6qsDt_mKPQK0T8Ldot2P8C5rgbGIU,3041
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=l2kEAu0Pm3OsFJsQtHf9Qdy5jnnoOu1v3MooBISnjP0,178801
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=S4GY3z-rf_BGuHmsnColMvR8KwYDyE9lqEbYT_a3Qag,3041
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=hQzzoZrmw9OXAbqkxC8eTFXtJZrbFzMgcWMLbJlOv7U,178801
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=P92Tm6Ie8xg4jGSP7k7bmFRAP5MfxtVR_KacS73a6PI,3041
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=0Sx5yL8D8pyYDXTIBZAoTiSsRpG_eJz8uD2ttVrklhU,50000
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=3-UwBM7WZa4aCgmW_ZAdRSq8KYMq2gnkIUqU73Z0OLI,178801
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=rkQA2-_d7uByKmw003lFXbXNDjHrUGBplZ8nB_TU5pk,3041
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=IAYroplsdz6n7PZ-vIMIJ4FjG9jC1OSxc3-oVJdSFDM,3041
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=Zb42SoVEnlTj_N_ndnym3_d4RNZWeHm290hTtpp_zO8,3041
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=L7STTmlRX-z-YvksmiAxEe1UoTmDnQ_lnAjZH53Szp0,172738
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=-sZUikGMWskONojs6fJIMX8VEWpviYYg4u1vipY6Bak,2818
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=N5L5CxRT5yf_vq6pFjorJ09Sr-RcnrAlH-_F3kEsyUU,178801
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=DRgzqxRtvQVzFnpFAjNC9TDNgRtk2ZRkWPyAaeOx3q4,3041
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=jz7SGKU8GuJWASH2u428QL9c-G_-8nZvOFSOUlMdCyA,178801
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=37H01o6GibccR_hKIwwbWxGX0Tuxnb-4Qc6rmDxwwUI,178801
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=YmcI7LZ6i-Wg1wjAkLVX7fmxzCj621Pc5itO3PvCm_k,3041
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=IrtJmDQliv4lDZ_UUjkZNso3EZyu7pMACxMB-rvHUj0,3041
scipy/spatial/tests/data/random-bool-data.txt,sha256=MHAQdE4hPVzgu-csVVbm1DNJ80dP7XthJ1kb2In8ImM,6000
scipy/spatial/tests/data/random-double-data.txt,sha256=GA8hYrHsTBeS864GJf0X6JRTvGlbpM8P8sJairmfnBU,75000
scipy/spatial/tests/data/random-int-data.txt,sha256=xTUbCgoT4X8nll3kXu7S9lv-eJzZtwewwm5lFepxkdQ,10266
scipy/spatial/tests/data/random-uint-data.txt,sha256=8IPpXhwglxzinL5PcK-PEqleZRlNKdx3zCVMoDklyrY,8711
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=rkVhIL1mupGuqDrw1a5QFaODzZkdoaLMbGI_DbLLTzM,480
scipy/spatial/tests/test__plotutils.py,sha256=vmDDeXOe4N2XPMeyw8Zx1T8b8bl3Nw5ZwT9uXx21JkU,1943
scipy/spatial/tests/test__procrustes.py,sha256=fAh7SknZAJ8qQDULEFERbstd_R-TreyMezJ7LFJs_4M,4982
scipy/spatial/tests/test_distance.py,sha256=m8jqsmYNGwiJ7jQ9eEg5rXCLV-rLqptSzexFk6OKS2c,82409
scipy/spatial/tests/test_hausdorff.py,sha256=vjrvEHQkK4tXkAWjuvrids3qDql54YEqQTXNoW07JQs,6376
scipy/spatial/tests/test_kdtree.py,sha256=hFL4Cyorq7lyVbwkHXWitKobZ38YUlavpAWci4v8T5I,48107
scipy/spatial/tests/test_qhull.py,sha256=UZT3b6Sj_Vgu2jbnBq86-3AwtrE5V5E62toOxSHiDz0,42359
scipy/spatial/tests/test_slerp.py,sha256=o00jhmYbHSlGeKNc7kMFXem2YiK0fpZ-jx8ktLGsWNw,15442
scipy/spatial/tests/test_spherical_voronoi.py,sha256=2XM0vFhX_G_K1tTuZoV90BQCj5W1WVSzCU42cZZoYKs,14147
scipy/spatial/transform/__init__.py,sha256=cfJUMspQoZLcH83Nw6ksjKo5vPdQGzYoo2-wqKQJClg,626
scipy/spatial/transform/__pycache__/__init__.cpython-39.pyc,,
scipy/spatial/transform/__pycache__/_rotation_groups.cpython-39.pyc,,
scipy/spatial/transform/__pycache__/_rotation_spline.cpython-39.pyc,,
scipy/spatial/transform/__pycache__/setup.cpython-39.pyc,,
scipy/spatial/transform/_rotation_groups.py,sha256=XS-9K6xYnnwWywMMYMVznBYc1-0DPhADHQp_FIT3_f8,4422
scipy/spatial/transform/_rotation_spline.py,sha256=vNlYLWeOYnFrkxHVqVzGCvw0C9EIzY-W09kM-hYpQpk,14062
scipy/spatial/transform/rotation.cp39-win_amd64.pyd,sha256=rvUPCEIzrPdE6vvFs_gUhc2gEZvEnBJAzotNl0Gaoeg,470016
scipy/spatial/transform/setup.py,sha256=GIe_nAqVvNyZhqR097gqQb_I5EX3ywyjNTGYhue49dw,317
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation.cpython-39.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_groups.cpython-39.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_spline.cpython-39.pyc,,
scipy/spatial/transform/tests/test_rotation.py,sha256=CtKSfZ5hzo83F-WPpb4dCSIMNjysGYv438DfUo-y398,37474
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=V6DiLWvJsrdklhS-GlzcA9qEy0cTQpwaNR-7vkhBt1M,5560
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=DRNIQM5Da8xFtVnRQcI5VRJIo7DgtCeiHYn52zD0qMk,5035
scipy/special.pxd,sha256=h8GS4dlnM_hFchSEzjL74WPstvZWYXNMJRNAJMyFzM8,37
scipy/special/__init__.py,sha256=eeBNyjIJAPYiExNkgeKXLlGJqj_g13aVdh9dahgXRw8,27440
scipy/special/__pycache__/__init__.cpython-39.pyc,,
scipy/special/__pycache__/_basic.cpython-39.pyc,,
scipy/special/__pycache__/_ellip_harm.cpython-39.pyc,,
scipy/special/__pycache__/_generate_pyx.cpython-39.pyc,,
scipy/special/__pycache__/_lambertw.cpython-39.pyc,,
scipy/special/__pycache__/_logsumexp.cpython-39.pyc,,
scipy/special/__pycache__/_mptestutils.cpython-39.pyc,,
scipy/special/__pycache__/_spherical_bessel.cpython-39.pyc,,
scipy/special/__pycache__/_testutils.cpython-39.pyc,,
scipy/special/__pycache__/add_newdocs.cpython-39.pyc,,
scipy/special/__pycache__/basic.cpython-39.pyc,,
scipy/special/__pycache__/orthogonal.cpython-39.pyc,,
scipy/special/__pycache__/setup.cpython-39.pyc,,
scipy/special/__pycache__/sf_error.cpython-39.pyc,,
scipy/special/__pycache__/spfun_stats.cpython-39.pyc,,
scipy/special/_basic.py,sha256=awlSPdLjJ7xFWh47jhekw95pfeWWuCZl3Nq_A-y18hg,71076
scipy/special/_comb.cp39-win_amd64.pyd,sha256=KVyoI4Jf9KiEoiOsFX7Y6J5zBUh-iO_G6pHj1_uOTXA,30720
scipy/special/_ellip_harm.py,sha256=pTaaBnbFC-z7iZ6ol5C8aCRUfRrCrdppnYHzZid34xs,5245
scipy/special/_ellip_harm_2.cp39-win_amd64.pyd,sha256=N8ixMD5nLISGUIPaeBw_Ri32733zlZXx2qHoODE3IUo,65536
scipy/special/_generate_pyx.py,sha256=mhP_XGqKnxW9QZucabUJSZTrPExRpH_5TSdcgwgWvJY,51233
scipy/special/_lambertw.py,sha256=jTVD3eAuMXGxNjsegUflAWNoR106QSZNfYYgRKWqG9U,2967
scipy/special/_logsumexp.py,sha256=PunpcmoLI-Cx6pm6nAcTIRcdQI_k8YiACf0wX62wUdo,7951
scipy/special/_mptestutils.py,sha256=cxwiD8pq0jMpXMmi09gDSMo1meLdxxBoE_VKZgsDUwI,14477
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/__pycache__/__init__.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/expn_asy.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/gammainc_asy.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/gammainc_data.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/lambertw.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/loggamma.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/setup.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/struve_convergence.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/utils.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/wrightomega.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/zetac.cpython-39.pyc,,
scipy/special/_precompute/expn_asy.py,sha256=JWR6E06vAj23BC2koWgReoOEb3nU9mPZi_DP16XRERQ,1357
scipy/special/_precompute/gammainc_asy.py,sha256=AQRGtkmHtdr2H6Xx5zCe2ADFOpd4jIp079Koy3_i7ZQ,2530
scipy/special/_precompute/gammainc_data.py,sha256=WAxU3IMXD0rbHmcfjOiyYXGben1fWmGNzqXdozbq7Fk,4117
scipy/special/_precompute/lambertw.py,sha256=wK-3mBM0KVEgqJzHenH3foyLgKZkiHjAiz_BzAJzzT4,2025
scipy/special/_precompute/loggamma.py,sha256=vGpMkcN4T3pFmzK6SyX69sCmQr1ROhurBndcXVpvWSU,1118
scipy/special/_precompute/setup.py,sha256=XTBMG-svM_BMjA9vXjUBqBCUNN6CJq2bEyXy69_hDns,308
scipy/special/_precompute/struve_convergence.py,sha256=ThCwm32339H-CNp7TXTs0nY5jzltUwa6FoeGBKYL7wg,3480
scipy/special/_precompute/utils.py,sha256=PuqKUyxI16_PDlLy-ocRW633AHA9MFwfXL81JMyZk0g,945
scipy/special/_precompute/wrightomega.py,sha256=EXKt0s-nxyNVdPewL9ceJJ2xCQbGDNbGvjwut2OJZCU,979
scipy/special/_precompute/zetac.py,sha256=YpBc87PZ_cV6LpsEPHDj2zy2UH2z-9e2qe6aFW2_Ue4,615
scipy/special/_spherical_bessel.py,sha256=FhYKSRVSBPiC6xaqTJE7fe1wkzQfxmwQk_B9fIThmng,5178
scipy/special/_test_round.cp39-win_amd64.pyd,sha256=735ecoZaCzEcS294WVv3Sq0xtxCWgsw0BYB3YKOCBLA,134144
scipy/special/_test_round.pyi,sha256=ZuQzrI6GrBQIAQ8XuWeGhuE0_-IQMimSYpmYCj4TMHg,143
scipy/special/_testutils.py,sha256=5krDAFhevU3esE_xHz2RZ5hP4J-EaHS9G0xzYbSMT3U,12001
scipy/special/_ufuncs.cp39-win_amd64.pyd,sha256=5nvreEk7uzxZEiscfXTB_6YS4ehT526JDfmsBSm95jw,759296
scipy/special/_ufuncs.pyi,sha256=Uk4Ny5GRB3XrCv1JmZn67hXPkwWlD_xkdO9K_VJkO2k,8949
scipy/special/_ufuncs_cxx.cp39-win_amd64.pyd,sha256=k0cJ8jE4BFfHKkSZlzErUM3CPzR-PdVvGL-Vi52wbsk,104960
scipy/special/add_newdocs.py,sha256=tENOKKvfdZJLDanDWEVVVwh0M5xQWCiOyTPQcHMlY6g,239193
scipy/special/basic.py,sha256=lvac9Y-SZHot5BtWKIjD4g32t-Gcm8C66vI_zKBJ1ZY,235
scipy/special/cython_special.cp39-win_amd64.pyd,sha256=TkcW3VJK-Pr3kwBYWZhf1xsA4umFI6jFMtkQjnAMtb8,1268224
scipy/special/cython_special.pxd,sha256=xytsjNjos6hu7b7jklZlBmXN3ohE6Sp4VLK3XIjOX5M,13585
scipy/special/cython_special.pyi,sha256=BQVUCzV8lCylnmLCtnN0Yz_ttlqyzcLc-BZx2KPXPzM,58
scipy/special/orthogonal.py,sha256=nSvIPxUaefyDMBBoDfSOVe2T26amE-JCglkKQVMQQdg,62155
scipy/special/orthogonal.pyi,sha256=HFIstLPKIYG5QuaSOFY8xZXyYN-JNlZANe8SdUTEOaI,8068
scipy/special/setup.py,sha256=bvrf3xOPgdW1956spoMOBY_mepNwxR1Ai-wYum5D7zM,6489
scipy/special/sf_error.py,sha256=q_Rbfkws1ttgTQKYLt6zFTdY6DFX2HajJe_lXiNWC0c,375
scipy/special/specfun.cp39-win_amd64.pyd,sha256=IKkj7DEXgudXFG2W1RPVajbR-LiJy97eHQOdlXz4LTE,78848
scipy/special/spfun_stats.py,sha256=3gijBHoU3tEe_63NASLG5-I9zFlSqA4nWgpC5_Dwz_c,3433
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_basic.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_bdtr.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_boxcox.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_cdflib.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_cython_special.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_data.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_digamma.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_ellip_harm.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_erfinv.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_exponential_integrals.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_faddeeva.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_gamma.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_gammainc.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_hypergeometric.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_kolmogorov.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_lambertw.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_log_softmax.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_loggamma.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_logit.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_logsumexp.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_mpmath.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_nan_inputs.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_ndtr.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_orthogonal.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_orthogonal_eval.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_owens_t.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_pcf.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_pdtr.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_precompute_expn_asy.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_precompute_gammainc.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_precompute_utils.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_round.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_sf_error.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_sici.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_spence.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_spfun_stats.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_sph_harm.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_spherical_bessel.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_trig.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_wrightomega.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_zeta.cpython-39.pyc,,
scipy/special/tests/data/README,sha256=ZoIaxnVYMTrPcAaPpxBoqcfXlftCee7HqH7hc2SmVPE,35139
scipy/special/tests/data/boost.npz,sha256=qgrBAsBCke6GmHcEKyoCV3UIUPplS3z1f4Xjcf9Zack,1085678
scipy/special/tests/data/gsl.npz,sha256=8oHuiW1OU8rYzfn2jv1ER_-SikJFilrLkDutNAg5-OE,51433
scipy/special/tests/data/local.npz,sha256=Sutcy08g-o40UWw1-qFoQ2UMG0SzOX4AHMgKElEKT8c,179493
scipy/special/tests/test_basic.py,sha256=CtzEbYD7odoQ7KrdY0yTyuMqlQWABN61QkQ7XKfplCI,133093
scipy/special/tests/test_bdtr.py,sha256=CTe5stBrdXVIeS29UhiRf0TDAVDkKA0rq717LbxT3cU,3255
scipy/special/tests/test_boxcox.py,sha256=gUrGF7Ql1adxiPl_YxpsGunDfg-B_WpqI9Zghzool7o,2672
scipy/special/tests/test_cdflib.py,sha256=9bvzVg8Bs27odQCIeLEOu92dyGfkBNzwhIq1sqpSHuw,12411
scipy/special/tests/test_cython_special.py,sha256=cY_8JDEK80c-gEgbuxG-L7DcGLC1MlfcS93A0fWRqAI,17937
scipy/special/tests/test_data.py,sha256=_zSnaGKz481UjKrqK_BZrp8qeZi3ryO2kI_Rjwgkf30,26194
scipy/special/tests/test_digamma.py,sha256=NlaFqc08di2L5FXArct9r0GaP_ciAxTIx-bfk-kdaks,1394
scipy/special/tests/test_ellip_harm.py,sha256=51KiCpQjqmf2uLZEsty-Vmr0FhoABtvMUz4218WR_S0,9640
scipy/special/tests/test_erfinv.py,sha256=ERewQCbfnM63sv2cxuCsRtsDZVsjCBrZ_Mtm-CmJ0Tw,1908
scipy/special/tests/test_exponential_integrals.py,sha256=HJVegnvRPLkJCLqxp-Ucsgt31xwQMqODmH9dGesSLqU,1892
scipy/special/tests/test_faddeeva.py,sha256=yB7xaTIN1cDfaTC0U4Mfd2tBd6AJXl6JfG_XdoPEla4,2576
scipy/special/tests/test_gamma.py,sha256=hb-ZlA2ZNz6gUGvVtMBgXFl_w30HPmthuUEAmNcz0sw,258
scipy/special/tests/test_gammainc.py,sha256=5X1bSuBWhABeQCpVoGLik2gHlfnDcmESw3v6VAiRx_A,3831
scipy/special/tests/test_hypergeometric.py,sha256=-tltjjpZLezpI4CzD1YsCkJBdIaWBTul70Q0Wk_dpqI,3543
scipy/special/tests/test_kolmogorov.py,sha256=fy-A3sKYEUQgScm0yF6cjrNYE530Xs-1Dw4BXUaOOP0,18455
scipy/special/tests/test_lambertw.py,sha256=PJ5PtoJF7P8ZCX_a2-ckBhj6RPDcbi725sWD_KpGsCQ,4201
scipy/special/tests/test_log_softmax.py,sha256=JdiC5C1Fm16rNdQHVWRu-FGMVOv24DPWRnguDDd1zEY,3415
scipy/special/tests/test_loggamma.py,sha256=x6kuJf-bEnn5ECdkDSgvk3An_A-9UxVsZpqa49IwAq8,1992
scipy/special/tests/test_logit.py,sha256=isiEIQ41MuP5SRPieCvytYZ58PXG7DgbYin7LQ-c8ag,2719
scipy/special/tests/test_logsumexp.py,sha256=vcHdTDJQKvUfkO0I8VDRUQF4MhnF0dQi2pjDzRsggB0,6180
scipy/special/tests/test_mpmath.py,sha256=PPoIwiaNBUDOcqfCUl4c220jzQ5NbbXSi3VA87tPzwc,75105
scipy/special/tests/test_nan_inputs.py,sha256=1F3CRXp_DGmfUcJMr_61eW2yItjQQD9xzaIiDgbsXvI,1845
scipy/special/tests/test_ndtr.py,sha256=KhILFCVx93EQ9FpPQ48VJCv4Y171VY-A6G_QjqeL1uw,476
scipy/special/tests/test_orthogonal.py,sha256=9vVWOt3zqKi4JZSs5D0fakrlpelB8duPAXMoWo7ntNI,29205
scipy/special/tests/test_orthogonal_eval.py,sha256=QwLOD53aQ-29sNLZ0g9umf1x7cVaWsXw8NIFjl5FWs0,9012
scipy/special/tests/test_owens_t.py,sha256=SROqkWPG0p_fgm0c9NJftZrmGhvZAKAKRZcE7gATz0s,1323
scipy/special/tests/test_pcf.py,sha256=RNjEWZGFS99DOGZkkPJ8HNqLULko8UkX0nEWFYX26NE,664
scipy/special/tests/test_pdtr.py,sha256=qH6DTmyWyVMHyA_lyzLMXYdRVhST5LboFonLx8YN9FI,1300
scipy/special/tests/test_precompute_expn_asy.py,sha256=m4PlubkO-bgxsOQ_Zcj-7k4i1BKzSIZOKLktfdekgTc,607
scipy/special/tests/test_precompute_gammainc.py,sha256=jQ5VF0WX43oHOOX1FhrLFtbNivsGiBfyf_CvmeC9_C8,4580
scipy/special/tests/test_precompute_utils.py,sha256=gQR3U70QpRT2qSIxelcPNAza1J0H8MQYxrxZF5Pkxlw,1221
scipy/special/tests/test_round.py,sha256=u9Eg6iJOGwXM5DHkC7wAlvLyeyDGe1DH7vb0ChyCTis,406
scipy/special/tests/test_sf_error.py,sha256=qWQRmn3kqyQ5oVtH4s3VV7usjBqy8OzgHb_XqcJci8Y,2978
scipy/special/tests/test_sici.py,sha256=w4anBf8fiq2fmkwMSz3MX0uy35NLXVqfuW3Fwt2Nqek,1227
scipy/special/tests/test_spence.py,sha256=fChPw7xncNCTPMUGb0C8BC-lDKHWoEXSz8Rb4Wv8vNo,1099
scipy/special/tests/test_spfun_stats.py,sha256=4pog1aAtWYbtfWQG8PYI8TW4H9ypXsY98-XENCluwY8,2006
scipy/special/tests/test_sph_harm.py,sha256=PQehyslic3K2uwj8lV2g0Gh6JNVjpSYLCuVnihUlByQ,1116
scipy/special/tests/test_spherical_bessel.py,sha256=5f2tsw0DUbs_Q4A4-BNrrDA7NzFuKEGnSJ3nwnDNWqI,14284
scipy/special/tests/test_trig.py,sha256=WiZ-ryT7F8-kaACJKcXaA7PXSbuU4gIz_MK9Pv1gsTc,2097
scipy/special/tests/test_wrightomega.py,sha256=8afmPCC6IYN-SqbeBgqTyRgz0JfQdCs2vtxFcR_Bj9I,3550
scipy/special/tests/test_zeta.py,sha256=IoBUdssBRj7noPjW-xs9xGFFihZ7wvQpPJidgMOFCOs,1367
scipy/stats/__init__.py,sha256=9pHLeHe08uOW8aq3_BS6uOd-RjhWfCSeWP4ax-S5tPE,10390
scipy/stats/__pycache__/__init__.cpython-39.pyc,,
scipy/stats/__pycache__/_binned_statistic.cpython-39.pyc,,
scipy/stats/__pycache__/_constants.cpython-39.pyc,,
scipy/stats/__pycache__/_continuous_distns.cpython-39.pyc,,
scipy/stats/__pycache__/_discrete_distns.cpython-39.pyc,,
scipy/stats/__pycache__/_distn_infrastructure.cpython-39.pyc,,
scipy/stats/__pycache__/_distr_params.cpython-39.pyc,,
scipy/stats/__pycache__/_hypotests.cpython-39.pyc,,
scipy/stats/__pycache__/_ksstats.cpython-39.pyc,,
scipy/stats/__pycache__/_multivariate.cpython-39.pyc,,
scipy/stats/__pycache__/_rvs_sampling.cpython-39.pyc,,
scipy/stats/__pycache__/_stats_mstats_common.cpython-39.pyc,,
scipy/stats/__pycache__/_tukeylambda_stats.cpython-39.pyc,,
scipy/stats/__pycache__/_wilcoxon_data.cpython-39.pyc,,
scipy/stats/__pycache__/contingency.cpython-39.pyc,,
scipy/stats/__pycache__/distributions.cpython-39.pyc,,
scipy/stats/__pycache__/kde.cpython-39.pyc,,
scipy/stats/__pycache__/morestats.cpython-39.pyc,,
scipy/stats/__pycache__/mstats.cpython-39.pyc,,
scipy/stats/__pycache__/mstats_basic.cpython-39.pyc,,
scipy/stats/__pycache__/mstats_extras.cpython-39.pyc,,
scipy/stats/__pycache__/setup.cpython-39.pyc,,
scipy/stats/__pycache__/stats.cpython-39.pyc,,
scipy/stats/_binned_statistic.py,sha256=idkmEN3Chd7KGrWIAz-Apscy8Z3IhfOZP_wpK4XGCio,30577
scipy/stats/_constants.py,sha256=XHIivxfkXEp698UEzTFFIq6IFZ3YdnJa-L5X51hS14U,728
scipy/stats/_continuous_distns.py,sha256=laFbALaGUzpAyTTKJdObzI_K5K-2cnZeguaca826PBA,274176
scipy/stats/_discrete_distns.py,sha256=aKCFifgnrFYcmThOFpwl6gBmWmoh3pev5O9AK2_VzoY,36314
scipy/stats/_distn_infrastructure.py,sha256=Vwl0sHVUMyGHECelmnvtbogAygzZ4ckdQpRAXdd4EiU,133156
scipy/stats/_distr_params.py,sha256=ByFVbKMEbhesOqGCUZ2UQRpWpEXCr5OGpxUJQMe1YCc,4560
scipy/stats/_hypotests.py,sha256=Vt8zj2oQ67CBs91HTL0PEtIeN8paGI9SruZiT9_lXQ4,14224
scipy/stats/_ksstats.py,sha256=Xbb6mr_g8WPTbUenKYD2dECEugendFytOFx08buYKvw,20059
scipy/stats/_multivariate.py,sha256=OldHx-I7INkzQ-SWiss7FxmDNyBXPZuQ_PanXtO6fVY,154122
scipy/stats/_rvs_sampling.py,sha256=JbynZHXPKmI_rb2AJ8gY2qZN7lREo-8nT-BDwtSVNpI,7097
scipy/stats/_stats.cp39-win_amd64.pyd,sha256=froQ7Vs9f_SNfCoBBQ9J4gloKj13V78O4CSz2o5Oquw,400896
scipy/stats/_stats_mstats_common.py,sha256=gdlW8e_-G7Gr-33rNDUTnyzf8tsomZNe8bpJ935ijJo,15900
scipy/stats/_tukeylambda_stats.py,sha256=ZL8ifXFFS5O-5IAGVmx8nsErRwLWJK68585cvLC5khg,6869
scipy/stats/_wilcoxon_data.py,sha256=jbtY35yAkW9v6qqStpMp5FBSlWlQ4OFc_8SwBkWAmzQ,19867
scipy/stats/contingency.py,sha256=lzLhiOkgSUyE6_vpDtEdMQoKUn24itfi9MX9V5Lwkt8,9337
scipy/stats/distributions.py,sha256=LVT8iAQIQz4yaJrU6uwOPNidI_TMxwVMbP644awJ4rk,753
scipy/stats/kde.py,sha256=kPGIc9M1T57qZElji8lF-bumuHZ1JTtw76_4m7Rz8Tc,21621
scipy/stats/morestats.py,sha256=iiNa_tlP1MdRXxFKaSBZmqOp0m2i5TB8tB6WTU3oFms,120368
scipy/stats/mstats.py,sha256=NwSEGgFJdkq1oHjvpw0bbRhAZv3Vq9-kIvQfVNma5jM,2260
scipy/stats/mstats_basic.py,sha256=InIxCtYRJ_akDVpECdMYFZiolwC6usLoC1650gptIEU,97939
scipy/stats/mstats_extras.py,sha256=6w3nWZi7HkaJKe_rUWqQIWVOPDJzocg688p7noMR4eE,14890
scipy/stats/mvn.cp39-win_amd64.pyd,sha256=7R0kDzCCOgjZGWZ217XHzrduXfGjP3l2ViaaZj2XhDc,38400
scipy/stats/setup.py,sha256=lFDOjzQjRNdepqmTTYs1qdF3qBTYcFlkKlCPI1ybnfU,872
scipy/stats/statlib.cp39-win_amd64.pyd,sha256=pjsLnHJi1n7pR9NBjrY7AKHvAyeU116SE322MuYPESw,33792
scipy/stats/stats.py,sha256=nmgWVbfHRgX_uxHg7WSUgWnXquxVL1AqPCvicgYEDzE,285538
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/stats/tests/__pycache__/common_tests.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_binned_statistic.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_contingency.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_continuous_basic.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_discrete_basic.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_discrete_distns.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_distributions.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_fit.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_hypotests.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_kdeoth.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_morestats.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_mstats_basic.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_mstats_extras.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_multivariate.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_rank.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_stats.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_tukeylambda_stats.cpython-39.pyc,,
scipy/stats/tests/common_tests.py,sha256=zupv4i6YhafoEmgpQa7-3hDW-zcWwCR7Is4Qfyr67lg,11270
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=Qdd0i7H4cNhAABfFOZPuplhi_9SCquFpO-hNkyRcMD8,3063
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=x9wJ2g1qnzf4DK_w9F_WiOiDMDEg4td2z6uU77G07xM,1947
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=KdnJedRthF7XLA-w7XkIPIMTgzu89yBAMmZA2H4uQOQ,6055
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=nCPyxRk1dAoSPWiC7kG4dLaXs2GL3-KRXRt2NwgXoIA,46561
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=6yPHiQSk0KI4oURQOk99t-uEm-IZN-8eIPHb_y0mQ1U,451566
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=fI-HpgJF9cdGdBinclhVzOcWCCc5ZJZuXalUwirV-lc,6815
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=iJTaAWUFn7DPLTd9bQh_EMKEK1DPG0fnN8xk7BQlPRE,53799
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=riOkYT-LRgmJhPpCK32x7xYnD38gwnh_Eo1X8OK3eN8,523605
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=QtSS11d-vkVvqaIEeJ6oNwyET1CKoyQqjlfBl2sTOJA,7381
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=qrxQQ0I6gnhrefygKwT48x-bz-8laD8Vpn7c81nITRg,59228
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=qmELOQyNlH7CWOMt8PQ0Z_yxgg9Hxc4lqZOuHZxxWuc,577633
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=zD_RTRxfqJHVZTAAyddzLDDbhCzKSfwFGr3hwZ1nq30,2591
scipy/stats/tests/data/stable-cdf-sample-data.npy,sha256=TNsQ_TDpSBsbBFuuOlNLEq6pzuwEMFP9NcSMsGWQ_-w,27008
scipy/stats/tests/data/stable-pdf-sample-data.npy,sha256=WrMmYqZWuX3B7PHcoBElLrhnKULUfgU0adSJVjn77QE,27008
scipy/stats/tests/test_binned_statistic.py,sha256=0EwJ14EfLo_S2Q6luMxNweITm8YRm-aZfYE7GJ7dfZQ,16600
scipy/stats/tests/test_contingency.py,sha256=efUN5hIlW1HBkyBSDM2nUsFMD_B9zCrMQbozjpl2psk,5982
scipy/stats/tests/test_continuous_basic.py,sha256=SEl3AxbJg16lh6LSmTuRZI4b2T6cEXcvc2YJUnVfWk4,26459
scipy/stats/tests/test_discrete_basic.py,sha256=UeXc2lX9sQ_spSNqeR-sxcNsTbBwqP0E_CJa1I_lMms,9607
scipy/stats/tests/test_discrete_distns.py,sha256=PHa_wezAA-ylW6U_IgoGpqzVrMF3UL_I-nGjvq17zMo,2770
scipy/stats/tests/test_distributions.py,sha256=QtGG6yj5nkrG5kRs02CVJ7fVd4J1SM2FWFnkfGcYmLI,201926
scipy/stats/tests/test_fit.py,sha256=OM_pFrFSCL9ot_Odn8Zowsw1jR_tfbRxn4T8hqZ-5cw,3720
scipy/stats/tests/test_hypotests.py,sha256=0Fck8VK0G_t-ywWAjfpdTyZuGKJPe5F5HxijKGYRGGI,6617
scipy/stats/tests/test_kdeoth.py,sha256=Ho6caYzYbm0Uz73JmumE1Wp5Xj8KCLCK9WLrmFGMaPA,16453
scipy/stats/tests/test_morestats.py,sha256=GEUYnytVrUOxrNyozexFLl-2hU3MS2dvy8pZkOivjng,81716
scipy/stats/tests/test_mstats_basic.py,sha256=b8XB65h8WpwMAQoGSMq7lZDHYdVkzzO_BLYihxbRy_E,69271
scipy/stats/tests/test_mstats_extras.py,sha256=Y93pgTlWw9gs4N5tHqd6vAI3RrRR14qOMARqIlIET64,5398
scipy/stats/tests/test_multivariate.py,sha256=EU7M8LBt9MDDoKJY4ioM6GjsIS5WvN5UadBRzDwLdFg,81226
scipy/stats/tests/test_rank.py,sha256=QDFcLUwvkh_-emjIiVrCI6sHE5WfxjNu52aY6Lkqo3U,8291
scipy/stats/tests/test_stats.py,sha256=eFBAkUCIwrfopJxYMmryRtbHW2kVCm455AQho099rkc,237231
scipy/stats/tests/test_tukeylambda_stats.py,sha256=eF6_VaRX71mlU3QdQnJF60uo0LcSzog-BHWFfunt_uI,3232
scipy/version.py,sha256=YBCzM9_8Ju_oEA4Ut7RQ548gJ3lAhmTIjWW0iksiEfI,238
