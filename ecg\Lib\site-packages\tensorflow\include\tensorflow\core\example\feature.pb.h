// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/example/feature.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2ffeature_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2ffeature_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fexample_2ffeature_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[9]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto;
namespace tensorflow {
class BytesList;
class BytesListDefaultTypeInternal;
extern BytesListDefaultTypeInternal _BytesList_default_instance_;
class Feature;
class FeatureDefaultTypeInternal;
extern FeatureDefaultTypeInternal _Feature_default_instance_;
class FeatureList;
class FeatureListDefaultTypeInternal;
extern FeatureListDefaultTypeInternal _FeatureList_default_instance_;
class FeatureLists;
class FeatureListsDefaultTypeInternal;
extern FeatureListsDefaultTypeInternal _FeatureLists_default_instance_;
class FeatureLists_FeatureListEntry_DoNotUse;
class FeatureLists_FeatureListEntry_DoNotUseDefaultTypeInternal;
extern FeatureLists_FeatureListEntry_DoNotUseDefaultTypeInternal _FeatureLists_FeatureListEntry_DoNotUse_default_instance_;
class Features;
class FeaturesDefaultTypeInternal;
extern FeaturesDefaultTypeInternal _Features_default_instance_;
class Features_FeatureEntry_DoNotUse;
class Features_FeatureEntry_DoNotUseDefaultTypeInternal;
extern Features_FeatureEntry_DoNotUseDefaultTypeInternal _Features_FeatureEntry_DoNotUse_default_instance_;
class FloatList;
class FloatListDefaultTypeInternal;
extern FloatListDefaultTypeInternal _FloatList_default_instance_;
class Int64List;
class Int64ListDefaultTypeInternal;
extern Int64ListDefaultTypeInternal _Int64List_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::BytesList* Arena::CreateMaybeMessage<::tensorflow::BytesList>(Arena*);
template<> ::tensorflow::Feature* Arena::CreateMaybeMessage<::tensorflow::Feature>(Arena*);
template<> ::tensorflow::FeatureList* Arena::CreateMaybeMessage<::tensorflow::FeatureList>(Arena*);
template<> ::tensorflow::FeatureLists* Arena::CreateMaybeMessage<::tensorflow::FeatureLists>(Arena*);
template<> ::tensorflow::FeatureLists_FeatureListEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FeatureLists_FeatureListEntry_DoNotUse>(Arena*);
template<> ::tensorflow::Features* Arena::CreateMaybeMessage<::tensorflow::Features>(Arena*);
template<> ::tensorflow::Features_FeatureEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::Features_FeatureEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FloatList* Arena::CreateMaybeMessage<::tensorflow::FloatList>(Arena*);
template<> ::tensorflow::Int64List* Arena::CreateMaybeMessage<::tensorflow::Int64List>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class BytesList :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BytesList) */ {
 public:
  BytesList();
  virtual ~BytesList();

  BytesList(const BytesList& from);
  BytesList(BytesList&& from) noexcept
    : BytesList() {
    *this = ::std::move(from);
  }

  inline BytesList& operator=(const BytesList& from) {
    CopyFrom(from);
    return *this;
  }
  inline BytesList& operator=(BytesList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BytesList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BytesList* internal_default_instance() {
    return reinterpret_cast<const BytesList*>(
               &_BytesList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(BytesList& a, BytesList& b) {
    a.Swap(&b);
  }
  inline void Swap(BytesList* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BytesList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BytesList* New() const final {
    return CreateMaybeMessage<BytesList>(nullptr);
  }

  BytesList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BytesList>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BytesList& from);
  void MergeFrom(const BytesList& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BytesList* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BytesList";
  }
  protected:
  explicit BytesList(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated bytes value = 1;
  int value_size() const;
  void clear_value();
  const std::string& value(int index) const;
  std::string* mutable_value(int index);
  void set_value(int index, const std::string& value);
  void set_value(int index, std::string&& value);
  void set_value(int index, const char* value);
  void set_value(int index, const void* value, size_t size);
  std::string* add_value();
  void add_value(const std::string& value);
  void add_value(std::string&& value);
  void add_value(const char* value);
  void add_value(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.BytesList)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class FloatList :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FloatList) */ {
 public:
  FloatList();
  virtual ~FloatList();

  FloatList(const FloatList& from);
  FloatList(FloatList&& from) noexcept
    : FloatList() {
    *this = ::std::move(from);
  }

  inline FloatList& operator=(const FloatList& from) {
    CopyFrom(from);
    return *this;
  }
  inline FloatList& operator=(FloatList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FloatList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FloatList* internal_default_instance() {
    return reinterpret_cast<const FloatList*>(
               &_FloatList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(FloatList& a, FloatList& b) {
    a.Swap(&b);
  }
  inline void Swap(FloatList* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FloatList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FloatList* New() const final {
    return CreateMaybeMessage<FloatList>(nullptr);
  }

  FloatList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FloatList>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FloatList& from);
  void MergeFrom(const FloatList& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FloatList* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FloatList";
  }
  protected:
  explicit FloatList(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated float value = 1 [packed = true];
  int value_size() const;
  void clear_value();
  float value(int index) const;
  void set_value(int index, float value);
  void add_value(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.FloatList)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > value_;
  mutable std::atomic<int> _value_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class Int64List :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Int64List) */ {
 public:
  Int64List();
  virtual ~Int64List();

  Int64List(const Int64List& from);
  Int64List(Int64List&& from) noexcept
    : Int64List() {
    *this = ::std::move(from);
  }

  inline Int64List& operator=(const Int64List& from) {
    CopyFrom(from);
    return *this;
  }
  inline Int64List& operator=(Int64List&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Int64List& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Int64List* internal_default_instance() {
    return reinterpret_cast<const Int64List*>(
               &_Int64List_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Int64List& a, Int64List& b) {
    a.Swap(&b);
  }
  inline void Swap(Int64List* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Int64List* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Int64List* New() const final {
    return CreateMaybeMessage<Int64List>(nullptr);
  }

  Int64List* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Int64List>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Int64List& from);
  void MergeFrom(const Int64List& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Int64List* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Int64List";
  }
  protected:
  explicit Int64List(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated int64 value = 1 [packed = true];
  int value_size() const;
  void clear_value();
  ::PROTOBUF_NAMESPACE_ID::int64 value(int index) const;
  void set_value(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_value(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.Int64List)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > value_;
  mutable std::atomic<int> _value_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class Feature :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Feature) */ {
 public:
  Feature();
  virtual ~Feature();

  Feature(const Feature& from);
  Feature(Feature&& from) noexcept
    : Feature() {
    *this = ::std::move(from);
  }

  inline Feature& operator=(const Feature& from) {
    CopyFrom(from);
    return *this;
  }
  inline Feature& operator=(Feature&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Feature& default_instance();

  enum KindCase {
    kBytesList = 1,
    kFloatList = 2,
    kInt64List = 3,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Feature* internal_default_instance() {
    return reinterpret_cast<const Feature*>(
               &_Feature_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Feature& a, Feature& b) {
    a.Swap(&b);
  }
  inline void Swap(Feature* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Feature* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Feature* New() const final {
    return CreateMaybeMessage<Feature>(nullptr);
  }

  Feature* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Feature>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Feature& from);
  void MergeFrom(const Feature& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Feature* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Feature";
  }
  protected:
  explicit Feature(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBytesListFieldNumber = 1,
    kFloatListFieldNumber = 2,
    kInt64ListFieldNumber = 3,
  };
  // .tensorflow.BytesList bytes_list = 1;
  bool has_bytes_list() const;
  void clear_bytes_list();
  const ::tensorflow::BytesList& bytes_list() const;
  ::tensorflow::BytesList* release_bytes_list();
  ::tensorflow::BytesList* mutable_bytes_list();
  void set_allocated_bytes_list(::tensorflow::BytesList* bytes_list);
  void unsafe_arena_set_allocated_bytes_list(
      ::tensorflow::BytesList* bytes_list);
  ::tensorflow::BytesList* unsafe_arena_release_bytes_list();

  // .tensorflow.FloatList float_list = 2;
  bool has_float_list() const;
  void clear_float_list();
  const ::tensorflow::FloatList& float_list() const;
  ::tensorflow::FloatList* release_float_list();
  ::tensorflow::FloatList* mutable_float_list();
  void set_allocated_float_list(::tensorflow::FloatList* float_list);
  void unsafe_arena_set_allocated_float_list(
      ::tensorflow::FloatList* float_list);
  ::tensorflow::FloatList* unsafe_arena_release_float_list();

  // .tensorflow.Int64List int64_list = 3;
  bool has_int64_list() const;
  void clear_int64_list();
  const ::tensorflow::Int64List& int64_list() const;
  ::tensorflow::Int64List* release_int64_list();
  ::tensorflow::Int64List* mutable_int64_list();
  void set_allocated_int64_list(::tensorflow::Int64List* int64_list);
  void unsafe_arena_set_allocated_int64_list(
      ::tensorflow::Int64List* int64_list);
  ::tensorflow::Int64List* unsafe_arena_release_int64_list();

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.Feature)
 private:
  class _Internal;
  void set_has_bytes_list();
  void set_has_float_list();
  void set_has_int64_list();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union KindUnion {
    KindUnion() {}
    ::tensorflow::BytesList* bytes_list_;
    ::tensorflow::FloatList* float_list_;
    ::tensorflow::Int64List* int64_list_;
  } kind_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class Features_FeatureEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Features_FeatureEntry_DoNotUse, 
    std::string, ::tensorflow::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Features_FeatureEntry_DoNotUse, 
    std::string, ::tensorflow::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  Features_FeatureEntry_DoNotUse();
  Features_FeatureEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const Features_FeatureEntry_DoNotUse& other);
  static const Features_FeatureEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Features_FeatureEntry_DoNotUse*>(&_Features_FeatureEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.Features.FeatureEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto.file_level_metadata[4];
  }

  public:
};

// -------------------------------------------------------------------

class Features :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Features) */ {
 public:
  Features();
  virtual ~Features();

  Features(const Features& from);
  Features(Features&& from) noexcept
    : Features() {
    *this = ::std::move(from);
  }

  inline Features& operator=(const Features& from) {
    CopyFrom(from);
    return *this;
  }
  inline Features& operator=(Features&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Features& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Features* internal_default_instance() {
    return reinterpret_cast<const Features*>(
               &_Features_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Features& a, Features& b) {
    a.Swap(&b);
  }
  inline void Swap(Features* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Features* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Features* New() const final {
    return CreateMaybeMessage<Features>(nullptr);
  }

  Features* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Features>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Features& from);
  void MergeFrom(const Features& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Features* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Features";
  }
  protected:
  explicit Features(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFeatureFieldNumber = 1,
  };
  // map<string, .tensorflow.Feature> feature = 1;
  int feature_size() const;
  void clear_feature();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >&
      feature() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >*
      mutable_feature();

  // @@protoc_insertion_point(class_scope:tensorflow.Features)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      Features_FeatureEntry_DoNotUse,
      std::string, ::tensorflow::Feature,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > feature_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class FeatureList :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FeatureList) */ {
 public:
  FeatureList();
  virtual ~FeatureList();

  FeatureList(const FeatureList& from);
  FeatureList(FeatureList&& from) noexcept
    : FeatureList() {
    *this = ::std::move(from);
  }

  inline FeatureList& operator=(const FeatureList& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureList& operator=(FeatureList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FeatureList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureList* internal_default_instance() {
    return reinterpret_cast<const FeatureList*>(
               &_FeatureList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(FeatureList& a, FeatureList& b) {
    a.Swap(&b);
  }
  inline void Swap(FeatureList* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FeatureList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FeatureList* New() const final {
    return CreateMaybeMessage<FeatureList>(nullptr);
  }

  FeatureList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FeatureList>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FeatureList& from);
  void MergeFrom(const FeatureList& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureList* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FeatureList";
  }
  protected:
  explicit FeatureList(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeatureFieldNumber = 1,
  };
  // repeated .tensorflow.Feature feature = 1;
  int feature_size() const;
  void clear_feature();
  ::tensorflow::Feature* mutable_feature(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Feature >*
      mutable_feature();
  const ::tensorflow::Feature& feature(int index) const;
  ::tensorflow::Feature* add_feature();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Feature >&
      feature() const;

  // @@protoc_insertion_point(class_scope:tensorflow.FeatureList)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Feature > feature_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class FeatureLists_FeatureListEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureLists_FeatureListEntry_DoNotUse, 
    std::string, ::tensorflow::FeatureList,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureLists_FeatureListEntry_DoNotUse, 
    std::string, ::tensorflow::FeatureList,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  FeatureLists_FeatureListEntry_DoNotUse();
  FeatureLists_FeatureListEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FeatureLists_FeatureListEntry_DoNotUse& other);
  static const FeatureLists_FeatureListEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FeatureLists_FeatureListEntry_DoNotUse*>(&_FeatureLists_FeatureListEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FeatureLists.FeatureListEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto.file_level_metadata[7];
  }

  public:
};

// -------------------------------------------------------------------

class FeatureLists :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FeatureLists) */ {
 public:
  FeatureLists();
  virtual ~FeatureLists();

  FeatureLists(const FeatureLists& from);
  FeatureLists(FeatureLists&& from) noexcept
    : FeatureLists() {
    *this = ::std::move(from);
  }

  inline FeatureLists& operator=(const FeatureLists& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureLists& operator=(FeatureLists&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FeatureLists& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureLists* internal_default_instance() {
    return reinterpret_cast<const FeatureLists*>(
               &_FeatureLists_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(FeatureLists& a, FeatureLists& b) {
    a.Swap(&b);
  }
  inline void Swap(FeatureLists* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FeatureLists* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FeatureLists* New() const final {
    return CreateMaybeMessage<FeatureLists>(nullptr);
  }

  FeatureLists* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FeatureLists>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FeatureLists& from);
  void MergeFrom(const FeatureLists& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureLists* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FeatureLists";
  }
  protected:
  explicit FeatureLists(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFeatureListFieldNumber = 1,
  };
  // map<string, .tensorflow.FeatureList> feature_list = 1;
  int feature_list_size() const;
  void clear_feature_list();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >&
      feature_list() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >*
      mutable_feature_list();

  // @@protoc_insertion_point(class_scope:tensorflow.FeatureLists)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FeatureLists_FeatureListEntry_DoNotUse,
      std::string, ::tensorflow::FeatureList,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > feature_list_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// BytesList

// repeated bytes value = 1;
inline int BytesList::value_size() const {
  return value_.size();
}
inline void BytesList::clear_value() {
  value_.Clear();
}
inline const std::string& BytesList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BytesList.value)
  return value_.Get(index);
}
inline std::string* BytesList::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BytesList.value)
  return value_.Mutable(index);
}
inline void BytesList::set_value(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BytesList.value)
  value_.Mutable(index)->assign(value);
}
inline void BytesList::set_value(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BytesList.value)
  value_.Mutable(index)->assign(std::move(value));
}
inline void BytesList::set_value(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.BytesList.value)
}
inline void BytesList::set_value(int index, const void* value, size_t size) {
  value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BytesList.value)
}
inline std::string* BytesList::add_value() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.BytesList.value)
  return value_.Add();
}
inline void BytesList::add_value(const std::string& value) {
  value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.BytesList.value)
}
inline void BytesList::add_value(std::string&& value) {
  value_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.BytesList.value)
}
inline void BytesList::add_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.BytesList.value)
}
inline void BytesList::add_value(const void* value, size_t size) {
  value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.BytesList.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
BytesList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.BytesList.value)
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
BytesList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BytesList.value)
  return &value_;
}

// -------------------------------------------------------------------

// FloatList

// repeated float value = 1 [packed = true];
inline int FloatList::value_size() const {
  return value_.size();
}
inline void FloatList::clear_value() {
  value_.Clear();
}
inline float FloatList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FloatList.value)
  return value_.Get(index);
}
inline void FloatList::set_value(int index, float value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.FloatList.value)
}
inline void FloatList::add_value(float value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.FloatList.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
FloatList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.FloatList.value)
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
FloatList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FloatList.value)
  return &value_;
}

// -------------------------------------------------------------------

// Int64List

// repeated int64 value = 1 [packed = true];
inline int Int64List::value_size() const {
  return value_.size();
}
inline void Int64List::clear_value() {
  value_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Int64List::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Int64List.value)
  return value_.Get(index);
}
inline void Int64List::set_value(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.Int64List.value)
}
inline void Int64List::add_value(::PROTOBUF_NAMESPACE_ID::int64 value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.Int64List.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
Int64List::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.Int64List.value)
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
Int64List::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Int64List.value)
  return &value_;
}

// -------------------------------------------------------------------

// Feature

// .tensorflow.BytesList bytes_list = 1;
inline bool Feature::has_bytes_list() const {
  return kind_case() == kBytesList;
}
inline void Feature::set_has_bytes_list() {
  _oneof_case_[0] = kBytesList;
}
inline void Feature::clear_bytes_list() {
  if (has_bytes_list()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.bytes_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::BytesList* Feature::release_bytes_list() {
  // @@protoc_insertion_point(field_release:tensorflow.Feature.bytes_list)
  if (has_bytes_list()) {
    clear_has_kind();
      ::tensorflow::BytesList* temp = kind_.bytes_list_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.bytes_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::BytesList& Feature::bytes_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.Feature.bytes_list)
  return has_bytes_list()
      ? *kind_.bytes_list_
      : *reinterpret_cast< ::tensorflow::BytesList*>(&::tensorflow::_BytesList_default_instance_);
}
inline ::tensorflow::BytesList* Feature::unsafe_arena_release_bytes_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Feature.bytes_list)
  if (has_bytes_list()) {
    clear_has_kind();
    ::tensorflow::BytesList* temp = kind_.bytes_list_;
    kind_.bytes_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Feature::unsafe_arena_set_allocated_bytes_list(::tensorflow::BytesList* bytes_list) {
  clear_kind();
  if (bytes_list) {
    set_has_bytes_list();
    kind_.bytes_list_ = bytes_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Feature.bytes_list)
}
inline ::tensorflow::BytesList* Feature::mutable_bytes_list() {
  if (!has_bytes_list()) {
    clear_kind();
    set_has_bytes_list();
    kind_.bytes_list_ = CreateMaybeMessage< ::tensorflow::BytesList >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Feature.bytes_list)
  return kind_.bytes_list_;
}

// .tensorflow.FloatList float_list = 2;
inline bool Feature::has_float_list() const {
  return kind_case() == kFloatList;
}
inline void Feature::set_has_float_list() {
  _oneof_case_[0] = kFloatList;
}
inline void Feature::clear_float_list() {
  if (has_float_list()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.float_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::FloatList* Feature::release_float_list() {
  // @@protoc_insertion_point(field_release:tensorflow.Feature.float_list)
  if (has_float_list()) {
    clear_has_kind();
      ::tensorflow::FloatList* temp = kind_.float_list_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.float_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::FloatList& Feature::float_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.Feature.float_list)
  return has_float_list()
      ? *kind_.float_list_
      : *reinterpret_cast< ::tensorflow::FloatList*>(&::tensorflow::_FloatList_default_instance_);
}
inline ::tensorflow::FloatList* Feature::unsafe_arena_release_float_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Feature.float_list)
  if (has_float_list()) {
    clear_has_kind();
    ::tensorflow::FloatList* temp = kind_.float_list_;
    kind_.float_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Feature::unsafe_arena_set_allocated_float_list(::tensorflow::FloatList* float_list) {
  clear_kind();
  if (float_list) {
    set_has_float_list();
    kind_.float_list_ = float_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Feature.float_list)
}
inline ::tensorflow::FloatList* Feature::mutable_float_list() {
  if (!has_float_list()) {
    clear_kind();
    set_has_float_list();
    kind_.float_list_ = CreateMaybeMessage< ::tensorflow::FloatList >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Feature.float_list)
  return kind_.float_list_;
}

// .tensorflow.Int64List int64_list = 3;
inline bool Feature::has_int64_list() const {
  return kind_case() == kInt64List;
}
inline void Feature::set_has_int64_list() {
  _oneof_case_[0] = kInt64List;
}
inline void Feature::clear_int64_list() {
  if (has_int64_list()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.int64_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::Int64List* Feature::release_int64_list() {
  // @@protoc_insertion_point(field_release:tensorflow.Feature.int64_list)
  if (has_int64_list()) {
    clear_has_kind();
      ::tensorflow::Int64List* temp = kind_.int64_list_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.int64_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::Int64List& Feature::int64_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.Feature.int64_list)
  return has_int64_list()
      ? *kind_.int64_list_
      : *reinterpret_cast< ::tensorflow::Int64List*>(&::tensorflow::_Int64List_default_instance_);
}
inline ::tensorflow::Int64List* Feature::unsafe_arena_release_int64_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Feature.int64_list)
  if (has_int64_list()) {
    clear_has_kind();
    ::tensorflow::Int64List* temp = kind_.int64_list_;
    kind_.int64_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Feature::unsafe_arena_set_allocated_int64_list(::tensorflow::Int64List* int64_list) {
  clear_kind();
  if (int64_list) {
    set_has_int64_list();
    kind_.int64_list_ = int64_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Feature.int64_list)
}
inline ::tensorflow::Int64List* Feature::mutable_int64_list() {
  if (!has_int64_list()) {
    clear_kind();
    set_has_int64_list();
    kind_.int64_list_ = CreateMaybeMessage< ::tensorflow::Int64List >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Feature.int64_list)
  return kind_.int64_list_;
}

inline bool Feature::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void Feature::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline Feature::KindCase Feature::kind_case() const {
  return Feature::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Features

// map<string, .tensorflow.Feature> feature = 1;
inline int Features::feature_size() const {
  return feature_.size();
}
inline void Features::clear_feature() {
  feature_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >&
Features::feature() const {
  // @@protoc_insertion_point(field_map:tensorflow.Features.feature)
  return feature_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >*
Features::mutable_feature() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.Features.feature)
  return feature_.MutableMap();
}

// -------------------------------------------------------------------

// FeatureList

// repeated .tensorflow.Feature feature = 1;
inline int FeatureList::feature_size() const {
  return feature_.size();
}
inline void FeatureList::clear_feature() {
  feature_.Clear();
}
inline ::tensorflow::Feature* FeatureList::mutable_feature(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FeatureList.feature)
  return feature_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Feature >*
FeatureList::mutable_feature() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FeatureList.feature)
  return &feature_;
}
inline const ::tensorflow::Feature& FeatureList::feature(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FeatureList.feature)
  return feature_.Get(index);
}
inline ::tensorflow::Feature* FeatureList::add_feature() {
  // @@protoc_insertion_point(field_add:tensorflow.FeatureList.feature)
  return feature_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Feature >&
FeatureList::feature() const {
  // @@protoc_insertion_point(field_list:tensorflow.FeatureList.feature)
  return feature_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FeatureLists

// map<string, .tensorflow.FeatureList> feature_list = 1;
inline int FeatureLists::feature_list_size() const {
  return feature_list_.size();
}
inline void FeatureLists::clear_feature_list() {
  feature_list_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >&
FeatureLists::feature_list() const {
  // @@protoc_insertion_point(field_map:tensorflow.FeatureLists.feature_list)
  return feature_list_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >*
FeatureLists::mutable_feature_list() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FeatureLists.feature_list)
  return feature_list_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2ffeature_2eproto
