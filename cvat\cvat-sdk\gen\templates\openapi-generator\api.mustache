{{>partial_header}}

from __future__ import annotations

import typing
import urllib3

import re  # noqa: F401

from {{packageName}}.api_client import ApiClient, Endpoint as _Endpoint
from {{packageName}}.model_utils import (  # noqa: F401
    date,
    datetime,
    file_type,
    none_type,
)
{{#imports}}
{{{import}}}
{{/imports}}

from typing import TYPE_CHECKING
if TYPE_CHECKING:
    # Enable introspection. Can't work normally due to cyclic imports
    from {{packageName}}.apis import *
    from {{packageName}}.models import *


class {{classname}}(object):
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client
{{#operations}}
{{#operation}}
        self.{{>operation_name}}_endpoint = _Endpoint(
            settings={
                'response_schema': {{#returnType}}({{{.}}},){{/returnType}}{{^returnType}}None{{/returnType}},
{{#authMethods}}
{{#-first}}
                'auth': [
{{/-first}}
                    '{{name}}'{{^-last}},{{/-last}}
{{#-last}}
                ],
{{/-last}}
{{/authMethods}}
{{^authMethods}}
                'auth': [],
{{/authMethods}}
                'endpoint_path': '{{{path}}}',
                'operation_id': '{{>operation_name}}',
                'http_method': '{{httpMethod}}',
{{#servers}}
{{#-first}}
                'servers': [
{{/-first}}
                    {
                        'url': "{{{url}}}",
                        'description': "{{{description}}}{{^description}}No description provided{{/description}}",
                        {{#variables}}
                        {{#-first}}
                        'variables': {
                        {{/-first}}
                            '{{{name}}}': {
                                'description': "{{{description}}}{{^description}}No description provided{{/description}}",
                                'default_value': "{{{defaultValue}}}",
                                {{#enumValues}}
                                {{#-first}}
                                'enum_values': [
                                {{/-first}}
                                    "{{{.}}}"{{^-last}},{{/-last}}
                                {{#-last}}
                                ]
                                {{/-last}}
                                {{/enumValues}}
                                }{{^-last}},{{/-last}}
                        {{#-last}}
                            }
                        {{/-last}}
                        {{/variables}}
                    },
{{#-last}}
                ]
{{/-last}}
{{/servers}}
{{^servers}}
                'servers': None,
{{/servers}}
            },
            params_map={
                'all': [
{{#allParams}}
                    '{{paramName}}',
{{/allParams}}
                ],
{{#requiredParams}}
{{#-first}}
                'required': [
{{/-first}}
                    '{{paramName}}',
{{#-last}}
                ],
{{/-last}}
{{/requiredParams}}
{{^requiredParams}}
                'required': [],
{{/requiredParams}}
                'nullable': [
{{#allParams}}
{{#isNullable}}
                    '{{paramName}}',
{{/isNullable}}
{{/allParams}}
                ],
                'enum': [
{{#allParams}}
{{#isEnum}}
                    '{{paramName}}',
{{/isEnum}}
{{/allParams}}
                ],
                'validation': [
{{#allParams}}
{{#hasValidation}}
                    '{{paramName}}',
{{/hasValidation}}
{{/allParams}}
                ]
            },
            root_map={
                'validations': {
{{#allParams}}
{{#hasValidation}}
                    ('{{paramName}}',): {
{{#maxLength}}
                        'max_length': {{.}},{{/maxLength}}{{#minLength}}
                        'min_length': {{.}},{{/minLength}}{{#maxItems}}
                        'max_items': {{.}},{{/maxItems}}{{#minItems}}
                        'min_items': {{.}},{{/minItems}}{{#maximum}}
                        {{#exclusiveMaximum}}'exclusive_maximum'{{/exclusiveMaximum}}{{^exclusiveMaximum}}'inclusive_maximum'{{/exclusiveMaximum}}: {{maximum}},{{/maximum}}{{#minimum}}
                        {{#exclusiveMinimum}}'exclusive_minimum'{{/exclusiveMinimum}}{{^exclusiveMinimum}}'inclusive_minimum'{{/exclusiveMinimum}}: {{minimum}},{{/minimum}}{{#pattern}}
                        'regex': {
                            'pattern': r'{{{vendorExtensions.x-regex}}}',  # noqa: E501{{#vendorExtensions.x-modifiers}}
                            {{#-first}}'flags': (re.{{.}}{{/-first}}{{^-first}}          re.{{.}}{{/-first}}{{^-last}} | {{/-last}}{{#-last}}){{/-last}}{{/vendorExtensions.x-modifiers}}
                        },{{/pattern}}
                    },
{{/hasValidation}}
{{/allParams}}
                },
                'allowed_values': {
{{#allParams}}
{{#isEnum}}
                    ('{{paramName}}',): {
{{#isNullable}}
                        'None': None,{{/isNullable}}{{#allowableValues}}{{#enumVars}}
                        "{{name}}": {{{value}}}{{^-last}},{{/-last}}{{/enumVars}}{{/allowableValues}}
                    },
{{/isEnum}}
{{/allParams}}
                },
                'openapi_types': {
{{#allParams}}
                    '{{paramName}}':
                        ({{{dataType}}},),
{{/allParams}}
                },
                'attribute_map': {
{{#allParams}}
{{^isBodyParam}}
                    '{{paramName}}': '{{baseName}}',
{{/isBodyParam}}
{{/allParams}}
                },
                'location_map': {
{{#allParams}}
                    '{{paramName}}': '{{#isFormParam}}form{{/isFormParam}}{{#isQueryParam}}query{{/isQueryParam}}{{#isPathParam}}path{{/isPathParam}}{{#isHeaderParam}}header{{/isHeaderParam}}{{#isCookieParam}}cookie{{/isCookieParam}}{{#isBodyParam}}body{{/isBodyParam}}',
{{/allParams}}
                },
                'collection_format_map': {
{{#allParams}}
{{#collectionFormat}}
                    '{{paramName}}': '{{collectionFormat}}',
{{/collectionFormat}}
{{/allParams}}
                }
            },
            headers_map={
{{#hasProduces}}
                'accept': [
{{#produces}}
                    '{{{mediaType}}}'{{^-last}},{{/-last}}
{{/produces}}
                ],
{{/hasProduces}}
{{^hasProduces}}
                'accept': [],
{{/hasProduces}}
{{#hasConsumes}}
                'content_type': [
{{#consumes}}
                    '{{{mediaType}}}'{{^-last}},{{/-last}}
{{/consumes}}
                ]
{{/hasConsumes}}
{{^hasConsumes}}
                'content_type': [],
{{/hasConsumes}}
            },
            api_client=api_client
        )
{{/operation}}
{{/operations}}

{{#operations}}
{{#operation}}
    def {{>operation_name}}(
        self,
{{#requiredParams}}
{{^defaultValue}}
        {{paramName}}: {{>model_templates/type_annotation_cleaned}},
{{/defaultValue}}
{{/requiredParams}}
{{#requiredParams}}
{{#defaultValue}}
        {{paramName}}: {{>model_templates/type_annotation_cleaned}} = {{{defaultValue}}},
{{/defaultValue}}
{{/requiredParams}}
        *,
        _parse_response: bool = True,
        _request_timeout: typing.Union[int, float, tuple] = None,
        _validate_inputs: bool = True,
        _validate_outputs: bool = True,
        _check_status: bool = True,
        _spec_property_naming: bool = False,
        _content_type: typing.Optional[str] = None,
        _host_index: typing.Optional[int] = None,
        _request_auths: typing.Optional[list] = None,
        _async_call: bool = False,
        **kwargs,
    ) -> tuple[typing.Optional[{{>return_type}}], urllib3.HTTPResponse]:
        """{{{summary}}}{{^summary}}{{>operation_name}}{{/summary}}  # noqa: E501

{{#notes}}
        {{{.}}}  # noqa: E501
{{/notes}}
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass _async_call=True

        >>> thread = api.{{>operation_name}}({{#requiredParams}}{{^defaultValue}}{{paramName}}, {{/defaultValue}}{{/requiredParams}}{{#requiredParams}}{{#defaultValue}}{{paramName}}={{{defaultValue}}}, {{/defaultValue}}{{/requiredParams}}_async_call=True)
        >>> result = thread.get()

{{#requiredParams}}
{{#-last}}
        Args:
{{/-last}}
{{/requiredParams}}
{{#requiredParams}}
{{^defaultValue}}
            {{paramName}} ({{dataType}}):{{#description}} {{{.}}}{{/description}}
{{/defaultValue}}
{{/requiredParams}}
{{#requiredParams}}
{{#defaultValue}}
            {{paramName}} ({{dataType}}):{{#description}} {{{.}}}.{{/description}} defaults to {{{defaultValue}}}, must be one of [{{{defaultValue}}}]
{{/defaultValue}}
{{/requiredParams}}

        Keyword Args:{{#optionalParams}}
            {{paramName}} ({{dataType}}):{{#description}} {{{.}}}.{{/description}} [optional]{{#defaultValue}} if omitted the server will use the default value of {{{.}}}{{/defaultValue}}{{/optionalParams}}
            _parse_response (bool): if False, the response data will not be parsed,
                None is returned for data.
                Default is True.
            _request_timeout (int/float/tuple): timeout setting for this request. If
                one number provided, it will be total request timeout. It can also
                be a pair (tuple) of (connection, read) timeouts.
                Default is None.
            _validate_inputs (bool): specifies if type checking
                should be done one the data sent to the server.
                Default is True.
            _validate_outputs (bool): specifies if type checking
                should be done one the data received from the server.
                Default is True.
            _check_status (bool): whether to check response status
                for being positive or not.
                Default is True
            _spec_property_naming (bool): True if the variable names in the input data
                are serialized names, as specified in the OpenAPI document.
                False if the variable names in the input data
                are pythonic names, e.g. snake case (default)
            _content_type (str/None): force body content-type.
                Default is None and content-type will be predicted by allowed
                content-types and body.
            _host_index (int/None): specifies the index of the server
                that we want to use.
                Default is read from the configuration.
            _request_auths (list): set to override the auth_settings for an a single
                request; this effectively ignores the authentication
                in the spec for a single request.
                Default is None
            _async_call (bool): execute request asynchronously

        Returns:
            ({{returnType}}{{^returnType}}None{{/returnType}}, HTTPResponse)
                If the method is called asynchronously, returns the request
                thread.
        """
        kwargs['_async_call'] = _async_call
        kwargs['_parse_response'] = _parse_response
        kwargs['_request_timeout'] = _request_timeout
        kwargs['_validate_inputs'] = _validate_inputs
        kwargs['_validate_outputs'] = _validate_outputs
        kwargs['_check_status'] = _check_status
        kwargs['_spec_property_naming'] = _spec_property_naming
        kwargs['_content_type'] = _content_type
        kwargs['_host_index'] = _host_index
        kwargs['_request_auths'] = _request_auths
{{#requiredParams}}
        kwargs['{{paramName}}'] = {{paramName}}
{{/requiredParams}}
        return self.{{>operation_name}}_endpoint.call_with_http_info(**kwargs)

{{/operation}}
{{/operations}}
