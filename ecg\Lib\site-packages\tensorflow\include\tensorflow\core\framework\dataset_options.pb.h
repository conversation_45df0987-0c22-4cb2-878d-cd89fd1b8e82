// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/dataset_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
namespace tensorflow {
namespace data {
class DistributeOptions;
class DistributeOptionsDefaultTypeInternal;
extern DistributeOptionsDefaultTypeInternal _DistributeOptions_default_instance_;
class OptimizationOptions;
class OptimizationOptionsDefaultTypeInternal;
extern OptimizationOptionsDefaultTypeInternal _OptimizationOptions_default_instance_;
class Options;
class OptionsDefaultTypeInternal;
extern OptionsDefaultTypeInternal _Options_default_instance_;
class ThreadingOptions;
class ThreadingOptionsDefaultTypeInternal;
extern ThreadingOptionsDefaultTypeInternal _ThreadingOptions_default_instance_;
}  // namespace data
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::data::DistributeOptions* Arena::CreateMaybeMessage<::tensorflow::data::DistributeOptions>(Arena*);
template<> ::tensorflow::data::OptimizationOptions* Arena::CreateMaybeMessage<::tensorflow::data::OptimizationOptions>(Arena*);
template<> ::tensorflow::data::Options* Arena::CreateMaybeMessage<::tensorflow::data::Options>(Arena*);
template<> ::tensorflow::data::ThreadingOptions* Arena::CreateMaybeMessage<::tensorflow::data::ThreadingOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace data {

enum AutoShardPolicy : int {
  AUTO = 0,
  FILE = 1,
  DATA = 2,
  HINT = 3,
  OFF = -1,
  AutoShardPolicy_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  AutoShardPolicy_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool AutoShardPolicy_IsValid(int value);
constexpr AutoShardPolicy AutoShardPolicy_MIN = OFF;
constexpr AutoShardPolicy AutoShardPolicy_MAX = HINT;
constexpr int AutoShardPolicy_ARRAYSIZE = AutoShardPolicy_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AutoShardPolicy_descriptor();
template<typename T>
inline const std::string& AutoShardPolicy_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AutoShardPolicy>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AutoShardPolicy_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AutoShardPolicy_descriptor(), enum_t_value);
}
inline bool AutoShardPolicy_Parse(
    const std::string& name, AutoShardPolicy* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AutoShardPolicy>(
    AutoShardPolicy_descriptor(), name, value);
}
enum ExternalStatePolicy : int {
  POLICY_WARN = 0,
  POLICY_IGNORE = 1,
  POLICY_FAIL = 2,
  ExternalStatePolicy_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ExternalStatePolicy_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ExternalStatePolicy_IsValid(int value);
constexpr ExternalStatePolicy ExternalStatePolicy_MIN = POLICY_WARN;
constexpr ExternalStatePolicy ExternalStatePolicy_MAX = POLICY_FAIL;
constexpr int ExternalStatePolicy_ARRAYSIZE = ExternalStatePolicy_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ExternalStatePolicy_descriptor();
template<typename T>
inline const std::string& ExternalStatePolicy_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ExternalStatePolicy>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ExternalStatePolicy_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ExternalStatePolicy_descriptor(), enum_t_value);
}
inline bool ExternalStatePolicy_Parse(
    const std::string& name, ExternalStatePolicy* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ExternalStatePolicy>(
    ExternalStatePolicy_descriptor(), name, value);
}
// ===================================================================

class DistributeOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.DistributeOptions) */ {
 public:
  DistributeOptions();
  virtual ~DistributeOptions();

  DistributeOptions(const DistributeOptions& from);
  DistributeOptions(DistributeOptions&& from) noexcept
    : DistributeOptions() {
    *this = ::std::move(from);
  }

  inline DistributeOptions& operator=(const DistributeOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline DistributeOptions& operator=(DistributeOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DistributeOptions& default_instance();

  enum OptionalNumDevicesCase {
    kNumDevices = 2,
    OPTIONAL_NUM_DEVICES_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DistributeOptions* internal_default_instance() {
    return reinterpret_cast<const DistributeOptions*>(
               &_DistributeOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DistributeOptions& a, DistributeOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(DistributeOptions* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DistributeOptions* New() const final {
    return CreateMaybeMessage<DistributeOptions>(nullptr);
  }

  DistributeOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DistributeOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DistributeOptions& from);
  void MergeFrom(const DistributeOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DistributeOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.DistributeOptions";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAutoShardPolicyFieldNumber = 1,
    kNumDevicesFieldNumber = 2,
  };
  // .tensorflow.data.AutoShardPolicy auto_shard_policy = 1;
  void clear_auto_shard_policy();
  ::tensorflow::data::AutoShardPolicy auto_shard_policy() const;
  void set_auto_shard_policy(::tensorflow::data::AutoShardPolicy value);

  // int32 num_devices = 2;
  private:
  bool has_num_devices() const;
  public:
  void clear_num_devices();
  ::PROTOBUF_NAMESPACE_ID::int32 num_devices() const;
  void set_num_devices(::PROTOBUF_NAMESPACE_ID::int32 value);

  void clear_optional_num_devices();
  OptionalNumDevicesCase optional_num_devices_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.data.DistributeOptions)
 private:
  class _Internal;
  void set_has_num_devices();

  inline bool has_optional_num_devices() const;
  inline void clear_has_optional_num_devices();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  int auto_shard_policy_;
  union OptionalNumDevicesUnion {
    OptionalNumDevicesUnion() {}
    ::PROTOBUF_NAMESPACE_ID::int32 num_devices_;
  } optional_num_devices_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
};
// -------------------------------------------------------------------

class OptimizationOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.OptimizationOptions) */ {
 public:
  OptimizationOptions();
  virtual ~OptimizationOptions();

  OptimizationOptions(const OptimizationOptions& from);
  OptimizationOptions(OptimizationOptions&& from) noexcept
    : OptimizationOptions() {
    *this = ::std::move(from);
  }

  inline OptimizationOptions& operator=(const OptimizationOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline OptimizationOptions& operator=(OptimizationOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OptimizationOptions& default_instance();

  enum OptionalApplyDefaultOptimizationsCase {
    kApplyDefaultOptimizations = 1,
    OPTIONAL_APPLY_DEFAULT_OPTIMIZATIONS_NOT_SET = 0,
  };

  enum OptionalAutotuneCase {
    kAutotune = 2,
    OPTIONAL_AUTOTUNE_NOT_SET = 0,
  };

  enum OptionalAutotuneBuffersCase {
    kAutotuneBuffers = 3,
    OPTIONAL_AUTOTUNE_BUFFERS_NOT_SET = 0,
  };

  enum OptionalAutotuneCpuBudgetCase {
    kAutotuneCpuBudget = 4,
    OPTIONAL_AUTOTUNE_CPU_BUDGET_NOT_SET = 0,
  };

  enum OptionalAutotuneRamBudgetCase {
    kAutotuneRamBudget = 5,
    OPTIONAL_AUTOTUNE_RAM_BUDGET_NOT_SET = 0,
  };

  enum OptionalFilterFusionCase {
    kFilterFusion = 6,
    OPTIONAL_FILTER_FUSION_NOT_SET = 0,
  };

  enum OptionalMapAndBatchFusionCase {
    kMapAndBatchFusion = 9,
    OPTIONAL_MAP_AND_BATCH_FUSION_NOT_SET = 0,
  };

  enum OptionalMapAndFilterFusionCase {
    kMapAndFilterFusion = 10,
    OPTIONAL_MAP_AND_FILTER_FUSION_NOT_SET = 0,
  };

  enum OptionalMapFusionCase {
    kMapFusion = 11,
    OPTIONAL_MAP_FUSION_NOT_SET = 0,
  };

  enum OptionalMapParallelizationCase {
    kMapParallelization = 12,
    OPTIONAL_MAP_PARALLELIZATION_NOT_SET = 0,
  };

  enum OptionalNoopEliminationCase {
    kNoopElimination = 14,
    OPTIONAL_NOOP_ELIMINATION_NOT_SET = 0,
  };

  enum OptionalParallelBatchCase {
    kParallelBatch = 15,
    OPTIONAL_PARALLEL_BATCH_NOT_SET = 0,
  };

  enum OptionalShuffleAndRepeatFusionCase {
    kShuffleAndRepeatFusion = 17,
    OPTIONAL_SHUFFLE_AND_REPEAT_FUSION_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OptimizationOptions* internal_default_instance() {
    return reinterpret_cast<const OptimizationOptions*>(
               &_OptimizationOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(OptimizationOptions& a, OptimizationOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(OptimizationOptions* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OptimizationOptions* New() const final {
    return CreateMaybeMessage<OptimizationOptions>(nullptr);
  }

  OptimizationOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OptimizationOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OptimizationOptions& from);
  void MergeFrom(const OptimizationOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptimizationOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.OptimizationOptions";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kApplyDefaultOptimizationsFieldNumber = 1,
    kAutotuneFieldNumber = 2,
    kAutotuneBuffersFieldNumber = 3,
    kAutotuneCpuBudgetFieldNumber = 4,
    kAutotuneRamBudgetFieldNumber = 5,
    kFilterFusionFieldNumber = 6,
    kMapAndBatchFusionFieldNumber = 9,
    kMapAndFilterFusionFieldNumber = 10,
    kMapFusionFieldNumber = 11,
    kMapParallelizationFieldNumber = 12,
    kNoopEliminationFieldNumber = 14,
    kParallelBatchFieldNumber = 15,
    kShuffleAndRepeatFusionFieldNumber = 17,
  };
  // bool apply_default_optimizations = 1;
  private:
  bool has_apply_default_optimizations() const;
  public:
  void clear_apply_default_optimizations();
  bool apply_default_optimizations() const;
  void set_apply_default_optimizations(bool value);

  // bool autotune = 2;
  private:
  bool has_autotune() const;
  public:
  void clear_autotune();
  bool autotune() const;
  void set_autotune(bool value);

  // bool autotune_buffers = 3;
  private:
  bool has_autotune_buffers() const;
  public:
  void clear_autotune_buffers();
  bool autotune_buffers() const;
  void set_autotune_buffers(bool value);

  // int32 autotune_cpu_budget = 4;
  private:
  bool has_autotune_cpu_budget() const;
  public:
  void clear_autotune_cpu_budget();
  ::PROTOBUF_NAMESPACE_ID::int32 autotune_cpu_budget() const;
  void set_autotune_cpu_budget(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int64 autotune_ram_budget = 5;
  private:
  bool has_autotune_ram_budget() const;
  public:
  void clear_autotune_ram_budget();
  ::PROTOBUF_NAMESPACE_ID::int64 autotune_ram_budget() const;
  void set_autotune_ram_budget(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool filter_fusion = 6;
  private:
  bool has_filter_fusion() const;
  public:
  void clear_filter_fusion();
  bool filter_fusion() const;
  void set_filter_fusion(bool value);

  // bool map_and_batch_fusion = 9;
  private:
  bool has_map_and_batch_fusion() const;
  public:
  void clear_map_and_batch_fusion();
  bool map_and_batch_fusion() const;
  void set_map_and_batch_fusion(bool value);

  // bool map_and_filter_fusion = 10;
  private:
  bool has_map_and_filter_fusion() const;
  public:
  void clear_map_and_filter_fusion();
  bool map_and_filter_fusion() const;
  void set_map_and_filter_fusion(bool value);

  // bool map_fusion = 11;
  private:
  bool has_map_fusion() const;
  public:
  void clear_map_fusion();
  bool map_fusion() const;
  void set_map_fusion(bool value);

  // bool map_parallelization = 12;
  private:
  bool has_map_parallelization() const;
  public:
  void clear_map_parallelization();
  bool map_parallelization() const;
  void set_map_parallelization(bool value);

  // bool noop_elimination = 14;
  private:
  bool has_noop_elimination() const;
  public:
  void clear_noop_elimination();
  bool noop_elimination() const;
  void set_noop_elimination(bool value);

  // bool parallel_batch = 15;
  private:
  bool has_parallel_batch() const;
  public:
  void clear_parallel_batch();
  bool parallel_batch() const;
  void set_parallel_batch(bool value);

  // bool shuffle_and_repeat_fusion = 17;
  private:
  bool has_shuffle_and_repeat_fusion() const;
  public:
  void clear_shuffle_and_repeat_fusion();
  bool shuffle_and_repeat_fusion() const;
  void set_shuffle_and_repeat_fusion(bool value);

  void clear_optional_apply_default_optimizations();
  OptionalApplyDefaultOptimizationsCase optional_apply_default_optimizations_case() const;
  void clear_optional_autotune();
  OptionalAutotuneCase optional_autotune_case() const;
  void clear_optional_autotune_buffers();
  OptionalAutotuneBuffersCase optional_autotune_buffers_case() const;
  void clear_optional_autotune_cpu_budget();
  OptionalAutotuneCpuBudgetCase optional_autotune_cpu_budget_case() const;
  void clear_optional_autotune_ram_budget();
  OptionalAutotuneRamBudgetCase optional_autotune_ram_budget_case() const;
  void clear_optional_filter_fusion();
  OptionalFilterFusionCase optional_filter_fusion_case() const;
  void clear_optional_map_and_batch_fusion();
  OptionalMapAndBatchFusionCase optional_map_and_batch_fusion_case() const;
  void clear_optional_map_and_filter_fusion();
  OptionalMapAndFilterFusionCase optional_map_and_filter_fusion_case() const;
  void clear_optional_map_fusion();
  OptionalMapFusionCase optional_map_fusion_case() const;
  void clear_optional_map_parallelization();
  OptionalMapParallelizationCase optional_map_parallelization_case() const;
  void clear_optional_noop_elimination();
  OptionalNoopEliminationCase optional_noop_elimination_case() const;
  void clear_optional_parallel_batch();
  OptionalParallelBatchCase optional_parallel_batch_case() const;
  void clear_optional_shuffle_and_repeat_fusion();
  OptionalShuffleAndRepeatFusionCase optional_shuffle_and_repeat_fusion_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.data.OptimizationOptions)
 private:
  class _Internal;
  void set_has_apply_default_optimizations();
  void set_has_autotune();
  void set_has_autotune_buffers();
  void set_has_autotune_cpu_budget();
  void set_has_autotune_ram_budget();
  void set_has_filter_fusion();
  void set_has_map_and_batch_fusion();
  void set_has_map_and_filter_fusion();
  void set_has_map_fusion();
  void set_has_map_parallelization();
  void set_has_noop_elimination();
  void set_has_parallel_batch();
  void set_has_shuffle_and_repeat_fusion();

  inline bool has_optional_apply_default_optimizations() const;
  inline void clear_has_optional_apply_default_optimizations();

  inline bool has_optional_autotune() const;
  inline void clear_has_optional_autotune();

  inline bool has_optional_autotune_buffers() const;
  inline void clear_has_optional_autotune_buffers();

  inline bool has_optional_autotune_cpu_budget() const;
  inline void clear_has_optional_autotune_cpu_budget();

  inline bool has_optional_autotune_ram_budget() const;
  inline void clear_has_optional_autotune_ram_budget();

  inline bool has_optional_filter_fusion() const;
  inline void clear_has_optional_filter_fusion();

  inline bool has_optional_map_and_batch_fusion() const;
  inline void clear_has_optional_map_and_batch_fusion();

  inline bool has_optional_map_and_filter_fusion() const;
  inline void clear_has_optional_map_and_filter_fusion();

  inline bool has_optional_map_fusion() const;
  inline void clear_has_optional_map_fusion();

  inline bool has_optional_map_parallelization() const;
  inline void clear_has_optional_map_parallelization();

  inline bool has_optional_noop_elimination() const;
  inline void clear_has_optional_noop_elimination();

  inline bool has_optional_parallel_batch() const;
  inline void clear_has_optional_parallel_batch();

  inline bool has_optional_shuffle_and_repeat_fusion() const;
  inline void clear_has_optional_shuffle_and_repeat_fusion();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  union OptionalApplyDefaultOptimizationsUnion {
    OptionalApplyDefaultOptimizationsUnion() {}
    bool apply_default_optimizations_;
  } optional_apply_default_optimizations_;
  union OptionalAutotuneUnion {
    OptionalAutotuneUnion() {}
    bool autotune_;
  } optional_autotune_;
  union OptionalAutotuneBuffersUnion {
    OptionalAutotuneBuffersUnion() {}
    bool autotune_buffers_;
  } optional_autotune_buffers_;
  union OptionalAutotuneCpuBudgetUnion {
    OptionalAutotuneCpuBudgetUnion() {}
    ::PROTOBUF_NAMESPACE_ID::int32 autotune_cpu_budget_;
  } optional_autotune_cpu_budget_;
  union OptionalAutotuneRamBudgetUnion {
    OptionalAutotuneRamBudgetUnion() {}
    ::PROTOBUF_NAMESPACE_ID::int64 autotune_ram_budget_;
  } optional_autotune_ram_budget_;
  union OptionalFilterFusionUnion {
    OptionalFilterFusionUnion() {}
    bool filter_fusion_;
  } optional_filter_fusion_;
  union OptionalMapAndBatchFusionUnion {
    OptionalMapAndBatchFusionUnion() {}
    bool map_and_batch_fusion_;
  } optional_map_and_batch_fusion_;
  union OptionalMapAndFilterFusionUnion {
    OptionalMapAndFilterFusionUnion() {}
    bool map_and_filter_fusion_;
  } optional_map_and_filter_fusion_;
  union OptionalMapFusionUnion {
    OptionalMapFusionUnion() {}
    bool map_fusion_;
  } optional_map_fusion_;
  union OptionalMapParallelizationUnion {
    OptionalMapParallelizationUnion() {}
    bool map_parallelization_;
  } optional_map_parallelization_;
  union OptionalNoopEliminationUnion {
    OptionalNoopEliminationUnion() {}
    bool noop_elimination_;
  } optional_noop_elimination_;
  union OptionalParallelBatchUnion {
    OptionalParallelBatchUnion() {}
    bool parallel_batch_;
  } optional_parallel_batch_;
  union OptionalShuffleAndRepeatFusionUnion {
    OptionalShuffleAndRepeatFusionUnion() {}
    bool shuffle_and_repeat_fusion_;
  } optional_shuffle_and_repeat_fusion_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[13];

  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
};
// -------------------------------------------------------------------

class ThreadingOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.ThreadingOptions) */ {
 public:
  ThreadingOptions();
  virtual ~ThreadingOptions();

  ThreadingOptions(const ThreadingOptions& from);
  ThreadingOptions(ThreadingOptions&& from) noexcept
    : ThreadingOptions() {
    *this = ::std::move(from);
  }

  inline ThreadingOptions& operator=(const ThreadingOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline ThreadingOptions& operator=(ThreadingOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ThreadingOptions& default_instance();

  enum OptionalMaxIntraOpParallelismCase {
    kMaxIntraOpParallelism = 1,
    OPTIONAL_MAX_INTRA_OP_PARALLELISM_NOT_SET = 0,
  };

  enum OptionalPrivateThreadpoolSizeCase {
    kPrivateThreadpoolSize = 2,
    OPTIONAL_PRIVATE_THREADPOOL_SIZE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ThreadingOptions* internal_default_instance() {
    return reinterpret_cast<const ThreadingOptions*>(
               &_ThreadingOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ThreadingOptions& a, ThreadingOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(ThreadingOptions* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ThreadingOptions* New() const final {
    return CreateMaybeMessage<ThreadingOptions>(nullptr);
  }

  ThreadingOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ThreadingOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ThreadingOptions& from);
  void MergeFrom(const ThreadingOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ThreadingOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.ThreadingOptions";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMaxIntraOpParallelismFieldNumber = 1,
    kPrivateThreadpoolSizeFieldNumber = 2,
  };
  // int32 max_intra_op_parallelism = 1;
  private:
  bool has_max_intra_op_parallelism() const;
  public:
  void clear_max_intra_op_parallelism();
  ::PROTOBUF_NAMESPACE_ID::int32 max_intra_op_parallelism() const;
  void set_max_intra_op_parallelism(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 private_threadpool_size = 2;
  private:
  bool has_private_threadpool_size() const;
  public:
  void clear_private_threadpool_size();
  ::PROTOBUF_NAMESPACE_ID::int32 private_threadpool_size() const;
  void set_private_threadpool_size(::PROTOBUF_NAMESPACE_ID::int32 value);

  void clear_optional_max_intra_op_parallelism();
  OptionalMaxIntraOpParallelismCase optional_max_intra_op_parallelism_case() const;
  void clear_optional_private_threadpool_size();
  OptionalPrivateThreadpoolSizeCase optional_private_threadpool_size_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.data.ThreadingOptions)
 private:
  class _Internal;
  void set_has_max_intra_op_parallelism();
  void set_has_private_threadpool_size();

  inline bool has_optional_max_intra_op_parallelism() const;
  inline void clear_has_optional_max_intra_op_parallelism();

  inline bool has_optional_private_threadpool_size() const;
  inline void clear_has_optional_private_threadpool_size();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  union OptionalMaxIntraOpParallelismUnion {
    OptionalMaxIntraOpParallelismUnion() {}
    ::PROTOBUF_NAMESPACE_ID::int32 max_intra_op_parallelism_;
  } optional_max_intra_op_parallelism_;
  union OptionalPrivateThreadpoolSizeUnion {
    OptionalPrivateThreadpoolSizeUnion() {}
    ::PROTOBUF_NAMESPACE_ID::int32 private_threadpool_size_;
  } optional_private_threadpool_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[2];

  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
};
// -------------------------------------------------------------------

class Options :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.Options) */ {
 public:
  Options();
  virtual ~Options();

  Options(const Options& from);
  Options(Options&& from) noexcept
    : Options() {
    *this = ::std::move(from);
  }

  inline Options& operator=(const Options& from) {
    CopyFrom(from);
    return *this;
  }
  inline Options& operator=(Options&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Options& default_instance();

  enum OptionalDeterministicCase {
    kDeterministic = 1,
    OPTIONAL_DETERMINISTIC_NOT_SET = 0,
  };

  enum OptionalSlackCase {
    kSlack = 4,
    OPTIONAL_SLACK_NOT_SET = 0,
  };

  enum OptionalExternalStatePolicyCase {
    kExternalStatePolicy = 6,
    OPTIONAL_EXTERNAL_STATE_POLICY_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Options* internal_default_instance() {
    return reinterpret_cast<const Options*>(
               &_Options_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Options& a, Options& b) {
    a.Swap(&b);
  }
  inline void Swap(Options* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Options* New() const final {
    return CreateMaybeMessage<Options>(nullptr);
  }

  Options* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Options>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Options& from);
  void MergeFrom(const Options& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Options* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.Options";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDistributeOptionsFieldNumber = 2,
    kOptimizationOptionsFieldNumber = 3,
    kThreadingOptionsFieldNumber = 5,
    kDeterministicFieldNumber = 1,
    kSlackFieldNumber = 4,
    kExternalStatePolicyFieldNumber = 6,
  };
  // .tensorflow.data.DistributeOptions distribute_options = 2;
  bool has_distribute_options() const;
  void clear_distribute_options();
  const ::tensorflow::data::DistributeOptions& distribute_options() const;
  ::tensorflow::data::DistributeOptions* release_distribute_options();
  ::tensorflow::data::DistributeOptions* mutable_distribute_options();
  void set_allocated_distribute_options(::tensorflow::data::DistributeOptions* distribute_options);

  // .tensorflow.data.OptimizationOptions optimization_options = 3;
  bool has_optimization_options() const;
  void clear_optimization_options();
  const ::tensorflow::data::OptimizationOptions& optimization_options() const;
  ::tensorflow::data::OptimizationOptions* release_optimization_options();
  ::tensorflow::data::OptimizationOptions* mutable_optimization_options();
  void set_allocated_optimization_options(::tensorflow::data::OptimizationOptions* optimization_options);

  // .tensorflow.data.ThreadingOptions threading_options = 5;
  bool has_threading_options() const;
  void clear_threading_options();
  const ::tensorflow::data::ThreadingOptions& threading_options() const;
  ::tensorflow::data::ThreadingOptions* release_threading_options();
  ::tensorflow::data::ThreadingOptions* mutable_threading_options();
  void set_allocated_threading_options(::tensorflow::data::ThreadingOptions* threading_options);

  // bool deterministic = 1;
  private:
  bool has_deterministic() const;
  public:
  void clear_deterministic();
  bool deterministic() const;
  void set_deterministic(bool value);

  // bool slack = 4;
  private:
  bool has_slack() const;
  public:
  void clear_slack();
  bool slack() const;
  void set_slack(bool value);

  // .tensorflow.data.ExternalStatePolicy external_state_policy = 6;
  private:
  bool has_external_state_policy() const;
  public:
  void clear_external_state_policy();
  ::tensorflow::data::ExternalStatePolicy external_state_policy() const;
  void set_external_state_policy(::tensorflow::data::ExternalStatePolicy value);

  void clear_optional_deterministic();
  OptionalDeterministicCase optional_deterministic_case() const;
  void clear_optional_slack();
  OptionalSlackCase optional_slack_case() const;
  void clear_optional_external_state_policy();
  OptionalExternalStatePolicyCase optional_external_state_policy_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.data.Options)
 private:
  class _Internal;
  void set_has_deterministic();
  void set_has_slack();
  void set_has_external_state_policy();

  inline bool has_optional_deterministic() const;
  inline void clear_has_optional_deterministic();

  inline bool has_optional_slack() const;
  inline void clear_has_optional_slack();

  inline bool has_optional_external_state_policy() const;
  inline void clear_has_optional_external_state_policy();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::data::DistributeOptions* distribute_options_;
  ::tensorflow::data::OptimizationOptions* optimization_options_;
  ::tensorflow::data::ThreadingOptions* threading_options_;
  union OptionalDeterministicUnion {
    OptionalDeterministicUnion() {}
    bool deterministic_;
  } optional_deterministic_;
  union OptionalSlackUnion {
    OptionalSlackUnion() {}
    bool slack_;
  } optional_slack_;
  union OptionalExternalStatePolicyUnion {
    OptionalExternalStatePolicyUnion() {}
    int external_state_policy_;
  } optional_external_state_policy_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[3];

  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DistributeOptions

// .tensorflow.data.AutoShardPolicy auto_shard_policy = 1;
inline void DistributeOptions::clear_auto_shard_policy() {
  auto_shard_policy_ = 0;
}
inline ::tensorflow::data::AutoShardPolicy DistributeOptions::auto_shard_policy() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.DistributeOptions.auto_shard_policy)
  return static_cast< ::tensorflow::data::AutoShardPolicy >(auto_shard_policy_);
}
inline void DistributeOptions::set_auto_shard_policy(::tensorflow::data::AutoShardPolicy value) {
  
  auto_shard_policy_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.DistributeOptions.auto_shard_policy)
}

// int32 num_devices = 2;
inline bool DistributeOptions::has_num_devices() const {
  return optional_num_devices_case() == kNumDevices;
}
inline void DistributeOptions::set_has_num_devices() {
  _oneof_case_[0] = kNumDevices;
}
inline void DistributeOptions::clear_num_devices() {
  if (has_num_devices()) {
    optional_num_devices_.num_devices_ = 0;
    clear_has_optional_num_devices();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DistributeOptions::num_devices() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.DistributeOptions.num_devices)
  if (has_num_devices()) {
    return optional_num_devices_.num_devices_;
  }
  return 0;
}
inline void DistributeOptions::set_num_devices(::PROTOBUF_NAMESPACE_ID::int32 value) {
  if (!has_num_devices()) {
    clear_optional_num_devices();
    set_has_num_devices();
  }
  optional_num_devices_.num_devices_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.DistributeOptions.num_devices)
}

inline bool DistributeOptions::has_optional_num_devices() const {
  return optional_num_devices_case() != OPTIONAL_NUM_DEVICES_NOT_SET;
}
inline void DistributeOptions::clear_has_optional_num_devices() {
  _oneof_case_[0] = OPTIONAL_NUM_DEVICES_NOT_SET;
}
inline DistributeOptions::OptionalNumDevicesCase DistributeOptions::optional_num_devices_case() const {
  return DistributeOptions::OptionalNumDevicesCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// OptimizationOptions

// bool apply_default_optimizations = 1;
inline bool OptimizationOptions::has_apply_default_optimizations() const {
  return optional_apply_default_optimizations_case() == kApplyDefaultOptimizations;
}
inline void OptimizationOptions::set_has_apply_default_optimizations() {
  _oneof_case_[0] = kApplyDefaultOptimizations;
}
inline void OptimizationOptions::clear_apply_default_optimizations() {
  if (has_apply_default_optimizations()) {
    optional_apply_default_optimizations_.apply_default_optimizations_ = false;
    clear_has_optional_apply_default_optimizations();
  }
}
inline bool OptimizationOptions::apply_default_optimizations() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.apply_default_optimizations)
  if (has_apply_default_optimizations()) {
    return optional_apply_default_optimizations_.apply_default_optimizations_;
  }
  return false;
}
inline void OptimizationOptions::set_apply_default_optimizations(bool value) {
  if (!has_apply_default_optimizations()) {
    clear_optional_apply_default_optimizations();
    set_has_apply_default_optimizations();
  }
  optional_apply_default_optimizations_.apply_default_optimizations_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.apply_default_optimizations)
}

// bool autotune = 2;
inline bool OptimizationOptions::has_autotune() const {
  return optional_autotune_case() == kAutotune;
}
inline void OptimizationOptions::set_has_autotune() {
  _oneof_case_[1] = kAutotune;
}
inline void OptimizationOptions::clear_autotune() {
  if (has_autotune()) {
    optional_autotune_.autotune_ = false;
    clear_has_optional_autotune();
  }
}
inline bool OptimizationOptions::autotune() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.autotune)
  if (has_autotune()) {
    return optional_autotune_.autotune_;
  }
  return false;
}
inline void OptimizationOptions::set_autotune(bool value) {
  if (!has_autotune()) {
    clear_optional_autotune();
    set_has_autotune();
  }
  optional_autotune_.autotune_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.autotune)
}

// bool autotune_buffers = 3;
inline bool OptimizationOptions::has_autotune_buffers() const {
  return optional_autotune_buffers_case() == kAutotuneBuffers;
}
inline void OptimizationOptions::set_has_autotune_buffers() {
  _oneof_case_[2] = kAutotuneBuffers;
}
inline void OptimizationOptions::clear_autotune_buffers() {
  if (has_autotune_buffers()) {
    optional_autotune_buffers_.autotune_buffers_ = false;
    clear_has_optional_autotune_buffers();
  }
}
inline bool OptimizationOptions::autotune_buffers() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.autotune_buffers)
  if (has_autotune_buffers()) {
    return optional_autotune_buffers_.autotune_buffers_;
  }
  return false;
}
inline void OptimizationOptions::set_autotune_buffers(bool value) {
  if (!has_autotune_buffers()) {
    clear_optional_autotune_buffers();
    set_has_autotune_buffers();
  }
  optional_autotune_buffers_.autotune_buffers_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.autotune_buffers)
}

// int32 autotune_cpu_budget = 4;
inline bool OptimizationOptions::has_autotune_cpu_budget() const {
  return optional_autotune_cpu_budget_case() == kAutotuneCpuBudget;
}
inline void OptimizationOptions::set_has_autotune_cpu_budget() {
  _oneof_case_[3] = kAutotuneCpuBudget;
}
inline void OptimizationOptions::clear_autotune_cpu_budget() {
  if (has_autotune_cpu_budget()) {
    optional_autotune_cpu_budget_.autotune_cpu_budget_ = 0;
    clear_has_optional_autotune_cpu_budget();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int32 OptimizationOptions::autotune_cpu_budget() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.autotune_cpu_budget)
  if (has_autotune_cpu_budget()) {
    return optional_autotune_cpu_budget_.autotune_cpu_budget_;
  }
  return 0;
}
inline void OptimizationOptions::set_autotune_cpu_budget(::PROTOBUF_NAMESPACE_ID::int32 value) {
  if (!has_autotune_cpu_budget()) {
    clear_optional_autotune_cpu_budget();
    set_has_autotune_cpu_budget();
  }
  optional_autotune_cpu_budget_.autotune_cpu_budget_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.autotune_cpu_budget)
}

// int64 autotune_ram_budget = 5;
inline bool OptimizationOptions::has_autotune_ram_budget() const {
  return optional_autotune_ram_budget_case() == kAutotuneRamBudget;
}
inline void OptimizationOptions::set_has_autotune_ram_budget() {
  _oneof_case_[4] = kAutotuneRamBudget;
}
inline void OptimizationOptions::clear_autotune_ram_budget() {
  if (has_autotune_ram_budget()) {
    optional_autotune_ram_budget_.autotune_ram_budget_ = PROTOBUF_LONGLONG(0);
    clear_has_optional_autotune_ram_budget();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OptimizationOptions::autotune_ram_budget() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.autotune_ram_budget)
  if (has_autotune_ram_budget()) {
    return optional_autotune_ram_budget_.autotune_ram_budget_;
  }
  return PROTOBUF_LONGLONG(0);
}
inline void OptimizationOptions::set_autotune_ram_budget(::PROTOBUF_NAMESPACE_ID::int64 value) {
  if (!has_autotune_ram_budget()) {
    clear_optional_autotune_ram_budget();
    set_has_autotune_ram_budget();
  }
  optional_autotune_ram_budget_.autotune_ram_budget_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.autotune_ram_budget)
}

// bool filter_fusion = 6;
inline bool OptimizationOptions::has_filter_fusion() const {
  return optional_filter_fusion_case() == kFilterFusion;
}
inline void OptimizationOptions::set_has_filter_fusion() {
  _oneof_case_[5] = kFilterFusion;
}
inline void OptimizationOptions::clear_filter_fusion() {
  if (has_filter_fusion()) {
    optional_filter_fusion_.filter_fusion_ = false;
    clear_has_optional_filter_fusion();
  }
}
inline bool OptimizationOptions::filter_fusion() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.filter_fusion)
  if (has_filter_fusion()) {
    return optional_filter_fusion_.filter_fusion_;
  }
  return false;
}
inline void OptimizationOptions::set_filter_fusion(bool value) {
  if (!has_filter_fusion()) {
    clear_optional_filter_fusion();
    set_has_filter_fusion();
  }
  optional_filter_fusion_.filter_fusion_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.filter_fusion)
}

// bool map_and_batch_fusion = 9;
inline bool OptimizationOptions::has_map_and_batch_fusion() const {
  return optional_map_and_batch_fusion_case() == kMapAndBatchFusion;
}
inline void OptimizationOptions::set_has_map_and_batch_fusion() {
  _oneof_case_[6] = kMapAndBatchFusion;
}
inline void OptimizationOptions::clear_map_and_batch_fusion() {
  if (has_map_and_batch_fusion()) {
    optional_map_and_batch_fusion_.map_and_batch_fusion_ = false;
    clear_has_optional_map_and_batch_fusion();
  }
}
inline bool OptimizationOptions::map_and_batch_fusion() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.map_and_batch_fusion)
  if (has_map_and_batch_fusion()) {
    return optional_map_and_batch_fusion_.map_and_batch_fusion_;
  }
  return false;
}
inline void OptimizationOptions::set_map_and_batch_fusion(bool value) {
  if (!has_map_and_batch_fusion()) {
    clear_optional_map_and_batch_fusion();
    set_has_map_and_batch_fusion();
  }
  optional_map_and_batch_fusion_.map_and_batch_fusion_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.map_and_batch_fusion)
}

// bool map_and_filter_fusion = 10;
inline bool OptimizationOptions::has_map_and_filter_fusion() const {
  return optional_map_and_filter_fusion_case() == kMapAndFilterFusion;
}
inline void OptimizationOptions::set_has_map_and_filter_fusion() {
  _oneof_case_[7] = kMapAndFilterFusion;
}
inline void OptimizationOptions::clear_map_and_filter_fusion() {
  if (has_map_and_filter_fusion()) {
    optional_map_and_filter_fusion_.map_and_filter_fusion_ = false;
    clear_has_optional_map_and_filter_fusion();
  }
}
inline bool OptimizationOptions::map_and_filter_fusion() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.map_and_filter_fusion)
  if (has_map_and_filter_fusion()) {
    return optional_map_and_filter_fusion_.map_and_filter_fusion_;
  }
  return false;
}
inline void OptimizationOptions::set_map_and_filter_fusion(bool value) {
  if (!has_map_and_filter_fusion()) {
    clear_optional_map_and_filter_fusion();
    set_has_map_and_filter_fusion();
  }
  optional_map_and_filter_fusion_.map_and_filter_fusion_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.map_and_filter_fusion)
}

// bool map_fusion = 11;
inline bool OptimizationOptions::has_map_fusion() const {
  return optional_map_fusion_case() == kMapFusion;
}
inline void OptimizationOptions::set_has_map_fusion() {
  _oneof_case_[8] = kMapFusion;
}
inline void OptimizationOptions::clear_map_fusion() {
  if (has_map_fusion()) {
    optional_map_fusion_.map_fusion_ = false;
    clear_has_optional_map_fusion();
  }
}
inline bool OptimizationOptions::map_fusion() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.map_fusion)
  if (has_map_fusion()) {
    return optional_map_fusion_.map_fusion_;
  }
  return false;
}
inline void OptimizationOptions::set_map_fusion(bool value) {
  if (!has_map_fusion()) {
    clear_optional_map_fusion();
    set_has_map_fusion();
  }
  optional_map_fusion_.map_fusion_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.map_fusion)
}

// bool map_parallelization = 12;
inline bool OptimizationOptions::has_map_parallelization() const {
  return optional_map_parallelization_case() == kMapParallelization;
}
inline void OptimizationOptions::set_has_map_parallelization() {
  _oneof_case_[9] = kMapParallelization;
}
inline void OptimizationOptions::clear_map_parallelization() {
  if (has_map_parallelization()) {
    optional_map_parallelization_.map_parallelization_ = false;
    clear_has_optional_map_parallelization();
  }
}
inline bool OptimizationOptions::map_parallelization() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.map_parallelization)
  if (has_map_parallelization()) {
    return optional_map_parallelization_.map_parallelization_;
  }
  return false;
}
inline void OptimizationOptions::set_map_parallelization(bool value) {
  if (!has_map_parallelization()) {
    clear_optional_map_parallelization();
    set_has_map_parallelization();
  }
  optional_map_parallelization_.map_parallelization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.map_parallelization)
}

// bool noop_elimination = 14;
inline bool OptimizationOptions::has_noop_elimination() const {
  return optional_noop_elimination_case() == kNoopElimination;
}
inline void OptimizationOptions::set_has_noop_elimination() {
  _oneof_case_[10] = kNoopElimination;
}
inline void OptimizationOptions::clear_noop_elimination() {
  if (has_noop_elimination()) {
    optional_noop_elimination_.noop_elimination_ = false;
    clear_has_optional_noop_elimination();
  }
}
inline bool OptimizationOptions::noop_elimination() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.noop_elimination)
  if (has_noop_elimination()) {
    return optional_noop_elimination_.noop_elimination_;
  }
  return false;
}
inline void OptimizationOptions::set_noop_elimination(bool value) {
  if (!has_noop_elimination()) {
    clear_optional_noop_elimination();
    set_has_noop_elimination();
  }
  optional_noop_elimination_.noop_elimination_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.noop_elimination)
}

// bool parallel_batch = 15;
inline bool OptimizationOptions::has_parallel_batch() const {
  return optional_parallel_batch_case() == kParallelBatch;
}
inline void OptimizationOptions::set_has_parallel_batch() {
  _oneof_case_[11] = kParallelBatch;
}
inline void OptimizationOptions::clear_parallel_batch() {
  if (has_parallel_batch()) {
    optional_parallel_batch_.parallel_batch_ = false;
    clear_has_optional_parallel_batch();
  }
}
inline bool OptimizationOptions::parallel_batch() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.parallel_batch)
  if (has_parallel_batch()) {
    return optional_parallel_batch_.parallel_batch_;
  }
  return false;
}
inline void OptimizationOptions::set_parallel_batch(bool value) {
  if (!has_parallel_batch()) {
    clear_optional_parallel_batch();
    set_has_parallel_batch();
  }
  optional_parallel_batch_.parallel_batch_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.parallel_batch)
}

// bool shuffle_and_repeat_fusion = 17;
inline bool OptimizationOptions::has_shuffle_and_repeat_fusion() const {
  return optional_shuffle_and_repeat_fusion_case() == kShuffleAndRepeatFusion;
}
inline void OptimizationOptions::set_has_shuffle_and_repeat_fusion() {
  _oneof_case_[12] = kShuffleAndRepeatFusion;
}
inline void OptimizationOptions::clear_shuffle_and_repeat_fusion() {
  if (has_shuffle_and_repeat_fusion()) {
    optional_shuffle_and_repeat_fusion_.shuffle_and_repeat_fusion_ = false;
    clear_has_optional_shuffle_and_repeat_fusion();
  }
}
inline bool OptimizationOptions::shuffle_and_repeat_fusion() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.OptimizationOptions.shuffle_and_repeat_fusion)
  if (has_shuffle_and_repeat_fusion()) {
    return optional_shuffle_and_repeat_fusion_.shuffle_and_repeat_fusion_;
  }
  return false;
}
inline void OptimizationOptions::set_shuffle_and_repeat_fusion(bool value) {
  if (!has_shuffle_and_repeat_fusion()) {
    clear_optional_shuffle_and_repeat_fusion();
    set_has_shuffle_and_repeat_fusion();
  }
  optional_shuffle_and_repeat_fusion_.shuffle_and_repeat_fusion_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.OptimizationOptions.shuffle_and_repeat_fusion)
}

inline bool OptimizationOptions::has_optional_apply_default_optimizations() const {
  return optional_apply_default_optimizations_case() != OPTIONAL_APPLY_DEFAULT_OPTIMIZATIONS_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_apply_default_optimizations() {
  _oneof_case_[0] = OPTIONAL_APPLY_DEFAULT_OPTIMIZATIONS_NOT_SET;
}
inline bool OptimizationOptions::has_optional_autotune() const {
  return optional_autotune_case() != OPTIONAL_AUTOTUNE_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_autotune() {
  _oneof_case_[1] = OPTIONAL_AUTOTUNE_NOT_SET;
}
inline bool OptimizationOptions::has_optional_autotune_buffers() const {
  return optional_autotune_buffers_case() != OPTIONAL_AUTOTUNE_BUFFERS_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_autotune_buffers() {
  _oneof_case_[2] = OPTIONAL_AUTOTUNE_BUFFERS_NOT_SET;
}
inline bool OptimizationOptions::has_optional_autotune_cpu_budget() const {
  return optional_autotune_cpu_budget_case() != OPTIONAL_AUTOTUNE_CPU_BUDGET_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_autotune_cpu_budget() {
  _oneof_case_[3] = OPTIONAL_AUTOTUNE_CPU_BUDGET_NOT_SET;
}
inline bool OptimizationOptions::has_optional_autotune_ram_budget() const {
  return optional_autotune_ram_budget_case() != OPTIONAL_AUTOTUNE_RAM_BUDGET_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_autotune_ram_budget() {
  _oneof_case_[4] = OPTIONAL_AUTOTUNE_RAM_BUDGET_NOT_SET;
}
inline bool OptimizationOptions::has_optional_filter_fusion() const {
  return optional_filter_fusion_case() != OPTIONAL_FILTER_FUSION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_filter_fusion() {
  _oneof_case_[5] = OPTIONAL_FILTER_FUSION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_map_and_batch_fusion() const {
  return optional_map_and_batch_fusion_case() != OPTIONAL_MAP_AND_BATCH_FUSION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_map_and_batch_fusion() {
  _oneof_case_[6] = OPTIONAL_MAP_AND_BATCH_FUSION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_map_and_filter_fusion() const {
  return optional_map_and_filter_fusion_case() != OPTIONAL_MAP_AND_FILTER_FUSION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_map_and_filter_fusion() {
  _oneof_case_[7] = OPTIONAL_MAP_AND_FILTER_FUSION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_map_fusion() const {
  return optional_map_fusion_case() != OPTIONAL_MAP_FUSION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_map_fusion() {
  _oneof_case_[8] = OPTIONAL_MAP_FUSION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_map_parallelization() const {
  return optional_map_parallelization_case() != OPTIONAL_MAP_PARALLELIZATION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_map_parallelization() {
  _oneof_case_[9] = OPTIONAL_MAP_PARALLELIZATION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_noop_elimination() const {
  return optional_noop_elimination_case() != OPTIONAL_NOOP_ELIMINATION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_noop_elimination() {
  _oneof_case_[10] = OPTIONAL_NOOP_ELIMINATION_NOT_SET;
}
inline bool OptimizationOptions::has_optional_parallel_batch() const {
  return optional_parallel_batch_case() != OPTIONAL_PARALLEL_BATCH_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_parallel_batch() {
  _oneof_case_[11] = OPTIONAL_PARALLEL_BATCH_NOT_SET;
}
inline bool OptimizationOptions::has_optional_shuffle_and_repeat_fusion() const {
  return optional_shuffle_and_repeat_fusion_case() != OPTIONAL_SHUFFLE_AND_REPEAT_FUSION_NOT_SET;
}
inline void OptimizationOptions::clear_has_optional_shuffle_and_repeat_fusion() {
  _oneof_case_[12] = OPTIONAL_SHUFFLE_AND_REPEAT_FUSION_NOT_SET;
}
inline OptimizationOptions::OptionalApplyDefaultOptimizationsCase OptimizationOptions::optional_apply_default_optimizations_case() const {
  return OptimizationOptions::OptionalApplyDefaultOptimizationsCase(_oneof_case_[0]);
}
inline OptimizationOptions::OptionalAutotuneCase OptimizationOptions::optional_autotune_case() const {
  return OptimizationOptions::OptionalAutotuneCase(_oneof_case_[1]);
}
inline OptimizationOptions::OptionalAutotuneBuffersCase OptimizationOptions::optional_autotune_buffers_case() const {
  return OptimizationOptions::OptionalAutotuneBuffersCase(_oneof_case_[2]);
}
inline OptimizationOptions::OptionalAutotuneCpuBudgetCase OptimizationOptions::optional_autotune_cpu_budget_case() const {
  return OptimizationOptions::OptionalAutotuneCpuBudgetCase(_oneof_case_[3]);
}
inline OptimizationOptions::OptionalAutotuneRamBudgetCase OptimizationOptions::optional_autotune_ram_budget_case() const {
  return OptimizationOptions::OptionalAutotuneRamBudgetCase(_oneof_case_[4]);
}
inline OptimizationOptions::OptionalFilterFusionCase OptimizationOptions::optional_filter_fusion_case() const {
  return OptimizationOptions::OptionalFilterFusionCase(_oneof_case_[5]);
}
inline OptimizationOptions::OptionalMapAndBatchFusionCase OptimizationOptions::optional_map_and_batch_fusion_case() const {
  return OptimizationOptions::OptionalMapAndBatchFusionCase(_oneof_case_[6]);
}
inline OptimizationOptions::OptionalMapAndFilterFusionCase OptimizationOptions::optional_map_and_filter_fusion_case() const {
  return OptimizationOptions::OptionalMapAndFilterFusionCase(_oneof_case_[7]);
}
inline OptimizationOptions::OptionalMapFusionCase OptimizationOptions::optional_map_fusion_case() const {
  return OptimizationOptions::OptionalMapFusionCase(_oneof_case_[8]);
}
inline OptimizationOptions::OptionalMapParallelizationCase OptimizationOptions::optional_map_parallelization_case() const {
  return OptimizationOptions::OptionalMapParallelizationCase(_oneof_case_[9]);
}
inline OptimizationOptions::OptionalNoopEliminationCase OptimizationOptions::optional_noop_elimination_case() const {
  return OptimizationOptions::OptionalNoopEliminationCase(_oneof_case_[10]);
}
inline OptimizationOptions::OptionalParallelBatchCase OptimizationOptions::optional_parallel_batch_case() const {
  return OptimizationOptions::OptionalParallelBatchCase(_oneof_case_[11]);
}
inline OptimizationOptions::OptionalShuffleAndRepeatFusionCase OptimizationOptions::optional_shuffle_and_repeat_fusion_case() const {
  return OptimizationOptions::OptionalShuffleAndRepeatFusionCase(_oneof_case_[12]);
}
// -------------------------------------------------------------------

// ThreadingOptions

// int32 max_intra_op_parallelism = 1;
inline bool ThreadingOptions::has_max_intra_op_parallelism() const {
  return optional_max_intra_op_parallelism_case() == kMaxIntraOpParallelism;
}
inline void ThreadingOptions::set_has_max_intra_op_parallelism() {
  _oneof_case_[0] = kMaxIntraOpParallelism;
}
inline void ThreadingOptions::clear_max_intra_op_parallelism() {
  if (has_max_intra_op_parallelism()) {
    optional_max_intra_op_parallelism_.max_intra_op_parallelism_ = 0;
    clear_has_optional_max_intra_op_parallelism();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ThreadingOptions::max_intra_op_parallelism() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.ThreadingOptions.max_intra_op_parallelism)
  if (has_max_intra_op_parallelism()) {
    return optional_max_intra_op_parallelism_.max_intra_op_parallelism_;
  }
  return 0;
}
inline void ThreadingOptions::set_max_intra_op_parallelism(::PROTOBUF_NAMESPACE_ID::int32 value) {
  if (!has_max_intra_op_parallelism()) {
    clear_optional_max_intra_op_parallelism();
    set_has_max_intra_op_parallelism();
  }
  optional_max_intra_op_parallelism_.max_intra_op_parallelism_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.ThreadingOptions.max_intra_op_parallelism)
}

// int32 private_threadpool_size = 2;
inline bool ThreadingOptions::has_private_threadpool_size() const {
  return optional_private_threadpool_size_case() == kPrivateThreadpoolSize;
}
inline void ThreadingOptions::set_has_private_threadpool_size() {
  _oneof_case_[1] = kPrivateThreadpoolSize;
}
inline void ThreadingOptions::clear_private_threadpool_size() {
  if (has_private_threadpool_size()) {
    optional_private_threadpool_size_.private_threadpool_size_ = 0;
    clear_has_optional_private_threadpool_size();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ThreadingOptions::private_threadpool_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.ThreadingOptions.private_threadpool_size)
  if (has_private_threadpool_size()) {
    return optional_private_threadpool_size_.private_threadpool_size_;
  }
  return 0;
}
inline void ThreadingOptions::set_private_threadpool_size(::PROTOBUF_NAMESPACE_ID::int32 value) {
  if (!has_private_threadpool_size()) {
    clear_optional_private_threadpool_size();
    set_has_private_threadpool_size();
  }
  optional_private_threadpool_size_.private_threadpool_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.ThreadingOptions.private_threadpool_size)
}

inline bool ThreadingOptions::has_optional_max_intra_op_parallelism() const {
  return optional_max_intra_op_parallelism_case() != OPTIONAL_MAX_INTRA_OP_PARALLELISM_NOT_SET;
}
inline void ThreadingOptions::clear_has_optional_max_intra_op_parallelism() {
  _oneof_case_[0] = OPTIONAL_MAX_INTRA_OP_PARALLELISM_NOT_SET;
}
inline bool ThreadingOptions::has_optional_private_threadpool_size() const {
  return optional_private_threadpool_size_case() != OPTIONAL_PRIVATE_THREADPOOL_SIZE_NOT_SET;
}
inline void ThreadingOptions::clear_has_optional_private_threadpool_size() {
  _oneof_case_[1] = OPTIONAL_PRIVATE_THREADPOOL_SIZE_NOT_SET;
}
inline ThreadingOptions::OptionalMaxIntraOpParallelismCase ThreadingOptions::optional_max_intra_op_parallelism_case() const {
  return ThreadingOptions::OptionalMaxIntraOpParallelismCase(_oneof_case_[0]);
}
inline ThreadingOptions::OptionalPrivateThreadpoolSizeCase ThreadingOptions::optional_private_threadpool_size_case() const {
  return ThreadingOptions::OptionalPrivateThreadpoolSizeCase(_oneof_case_[1]);
}
// -------------------------------------------------------------------

// Options

// bool deterministic = 1;
inline bool Options::has_deterministic() const {
  return optional_deterministic_case() == kDeterministic;
}
inline void Options::set_has_deterministic() {
  _oneof_case_[0] = kDeterministic;
}
inline void Options::clear_deterministic() {
  if (has_deterministic()) {
    optional_deterministic_.deterministic_ = false;
    clear_has_optional_deterministic();
  }
}
inline bool Options::deterministic() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.deterministic)
  if (has_deterministic()) {
    return optional_deterministic_.deterministic_;
  }
  return false;
}
inline void Options::set_deterministic(bool value) {
  if (!has_deterministic()) {
    clear_optional_deterministic();
    set_has_deterministic();
  }
  optional_deterministic_.deterministic_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.Options.deterministic)
}

// .tensorflow.data.DistributeOptions distribute_options = 2;
inline bool Options::has_distribute_options() const {
  return this != internal_default_instance() && distribute_options_ != nullptr;
}
inline void Options::clear_distribute_options() {
  if (GetArenaNoVirtual() == nullptr && distribute_options_ != nullptr) {
    delete distribute_options_;
  }
  distribute_options_ = nullptr;
}
inline const ::tensorflow::data::DistributeOptions& Options::distribute_options() const {
  const ::tensorflow::data::DistributeOptions* p = distribute_options_;
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.distribute_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::data::DistributeOptions*>(
      &::tensorflow::data::_DistributeOptions_default_instance_);
}
inline ::tensorflow::data::DistributeOptions* Options::release_distribute_options() {
  // @@protoc_insertion_point(field_release:tensorflow.data.Options.distribute_options)
  
  ::tensorflow::data::DistributeOptions* temp = distribute_options_;
  distribute_options_ = nullptr;
  return temp;
}
inline ::tensorflow::data::DistributeOptions* Options::mutable_distribute_options() {
  
  if (distribute_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::data::DistributeOptions>(GetArenaNoVirtual());
    distribute_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.data.Options.distribute_options)
  return distribute_options_;
}
inline void Options::set_allocated_distribute_options(::tensorflow::data::DistributeOptions* distribute_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete distribute_options_;
  }
  if (distribute_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      distribute_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, distribute_options, submessage_arena);
    }
    
  } else {
    
  }
  distribute_options_ = distribute_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.Options.distribute_options)
}

// .tensorflow.data.OptimizationOptions optimization_options = 3;
inline bool Options::has_optimization_options() const {
  return this != internal_default_instance() && optimization_options_ != nullptr;
}
inline void Options::clear_optimization_options() {
  if (GetArenaNoVirtual() == nullptr && optimization_options_ != nullptr) {
    delete optimization_options_;
  }
  optimization_options_ = nullptr;
}
inline const ::tensorflow::data::OptimizationOptions& Options::optimization_options() const {
  const ::tensorflow::data::OptimizationOptions* p = optimization_options_;
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.optimization_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::data::OptimizationOptions*>(
      &::tensorflow::data::_OptimizationOptions_default_instance_);
}
inline ::tensorflow::data::OptimizationOptions* Options::release_optimization_options() {
  // @@protoc_insertion_point(field_release:tensorflow.data.Options.optimization_options)
  
  ::tensorflow::data::OptimizationOptions* temp = optimization_options_;
  optimization_options_ = nullptr;
  return temp;
}
inline ::tensorflow::data::OptimizationOptions* Options::mutable_optimization_options() {
  
  if (optimization_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::data::OptimizationOptions>(GetArenaNoVirtual());
    optimization_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.data.Options.optimization_options)
  return optimization_options_;
}
inline void Options::set_allocated_optimization_options(::tensorflow::data::OptimizationOptions* optimization_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete optimization_options_;
  }
  if (optimization_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      optimization_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optimization_options, submessage_arena);
    }
    
  } else {
    
  }
  optimization_options_ = optimization_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.Options.optimization_options)
}

// bool slack = 4;
inline bool Options::has_slack() const {
  return optional_slack_case() == kSlack;
}
inline void Options::set_has_slack() {
  _oneof_case_[1] = kSlack;
}
inline void Options::clear_slack() {
  if (has_slack()) {
    optional_slack_.slack_ = false;
    clear_has_optional_slack();
  }
}
inline bool Options::slack() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.slack)
  if (has_slack()) {
    return optional_slack_.slack_;
  }
  return false;
}
inline void Options::set_slack(bool value) {
  if (!has_slack()) {
    clear_optional_slack();
    set_has_slack();
  }
  optional_slack_.slack_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.Options.slack)
}

// .tensorflow.data.ThreadingOptions threading_options = 5;
inline bool Options::has_threading_options() const {
  return this != internal_default_instance() && threading_options_ != nullptr;
}
inline void Options::clear_threading_options() {
  if (GetArenaNoVirtual() == nullptr && threading_options_ != nullptr) {
    delete threading_options_;
  }
  threading_options_ = nullptr;
}
inline const ::tensorflow::data::ThreadingOptions& Options::threading_options() const {
  const ::tensorflow::data::ThreadingOptions* p = threading_options_;
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.threading_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::data::ThreadingOptions*>(
      &::tensorflow::data::_ThreadingOptions_default_instance_);
}
inline ::tensorflow::data::ThreadingOptions* Options::release_threading_options() {
  // @@protoc_insertion_point(field_release:tensorflow.data.Options.threading_options)
  
  ::tensorflow::data::ThreadingOptions* temp = threading_options_;
  threading_options_ = nullptr;
  return temp;
}
inline ::tensorflow::data::ThreadingOptions* Options::mutable_threading_options() {
  
  if (threading_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::data::ThreadingOptions>(GetArenaNoVirtual());
    threading_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.data.Options.threading_options)
  return threading_options_;
}
inline void Options::set_allocated_threading_options(::tensorflow::data::ThreadingOptions* threading_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete threading_options_;
  }
  if (threading_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      threading_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, threading_options, submessage_arena);
    }
    
  } else {
    
  }
  threading_options_ = threading_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.Options.threading_options)
}

// .tensorflow.data.ExternalStatePolicy external_state_policy = 6;
inline bool Options::has_external_state_policy() const {
  return optional_external_state_policy_case() == kExternalStatePolicy;
}
inline void Options::set_has_external_state_policy() {
  _oneof_case_[2] = kExternalStatePolicy;
}
inline void Options::clear_external_state_policy() {
  if (has_external_state_policy()) {
    optional_external_state_policy_.external_state_policy_ = 0;
    clear_has_optional_external_state_policy();
  }
}
inline ::tensorflow::data::ExternalStatePolicy Options::external_state_policy() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.Options.external_state_policy)
  if (has_external_state_policy()) {
    return static_cast< ::tensorflow::data::ExternalStatePolicy >(optional_external_state_policy_.external_state_policy_);
  }
  return static_cast< ::tensorflow::data::ExternalStatePolicy >(0);
}
inline void Options::set_external_state_policy(::tensorflow::data::ExternalStatePolicy value) {
  if (!has_external_state_policy()) {
    clear_optional_external_state_policy();
    set_has_external_state_policy();
  }
  optional_external_state_policy_.external_state_policy_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.data.Options.external_state_policy)
}

inline bool Options::has_optional_deterministic() const {
  return optional_deterministic_case() != OPTIONAL_DETERMINISTIC_NOT_SET;
}
inline void Options::clear_has_optional_deterministic() {
  _oneof_case_[0] = OPTIONAL_DETERMINISTIC_NOT_SET;
}
inline bool Options::has_optional_slack() const {
  return optional_slack_case() != OPTIONAL_SLACK_NOT_SET;
}
inline void Options::clear_has_optional_slack() {
  _oneof_case_[1] = OPTIONAL_SLACK_NOT_SET;
}
inline bool Options::has_optional_external_state_policy() const {
  return optional_external_state_policy_case() != OPTIONAL_EXTERNAL_STATE_POLICY_NOT_SET;
}
inline void Options::clear_has_optional_external_state_policy() {
  _oneof_case_[2] = OPTIONAL_EXTERNAL_STATE_POLICY_NOT_SET;
}
inline Options::OptionalDeterministicCase Options::optional_deterministic_case() const {
  return Options::OptionalDeterministicCase(_oneof_case_[0]);
}
inline Options::OptionalSlackCase Options::optional_slack_case() const {
  return Options::OptionalSlackCase(_oneof_case_[1]);
}
inline Options::OptionalExternalStatePolicyCase Options::optional_external_state_policy_case() const {
  return Options::OptionalExternalStatePolicyCase(_oneof_case_[2]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace data
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::data::AutoShardPolicy> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::AutoShardPolicy>() {
  return ::tensorflow::data::AutoShardPolicy_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::data::ExternalStatePolicy> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::ExternalStatePolicy>() {
  return ::tensorflow::data::ExternalStatePolicy_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdataset_5foptions_2eproto
