# 阈值设置
import json
import random
import traceback
from datetime import datetime

import numpy as np
import pandas as pd

from apps.models.ecg_analysis_modes import TGravityInfo
from apps.utils.logger_helper import Logger

# 阈值设置
# x_mean_thresh = 0.2
x_std_thresh = 4.6
# y_mean_thresh = 4.1
y_std_thresh = 4
# z_mean_thresh = 0.2
z_std_thresh = 4.3


def process(union_id, data):
    try:
        info_data = json.loads(data.replace('\\', ''))

        x = [float(item['x']) for item in info_data]
        y = [float(item['y']) for item in info_data]
        z = [float(item['z']) for item in info_data]

        df_data = pd.DataFrame({
            'x': x,
            'y': y,
            'z': z
        })

        # 提取特征
        features = extract_features(df_data)

        # 状态判定
        status = motion_detection(features)

        gravity_info = TGravityInfo()
        gravity_info.union_id = union_id
        gravity_info.gravity = data
        gravity_info.motion_state = status
        gravity_info.create_date = datetime.now()
        gravity_info.save()

        return motion_detection(features)
    except Exception as e:
        Logger().error(f'加速度处理异常：{traceback.format_exc()}\n{data}')
        return None



def extract_features(data):
    """
    提取运动状态特征（均值和标准差）
    :param data:
    :return:
    """

    x_values = data['x']
    y_values = data['y']
    z_values = data['z']

    features = {
        'x_mean': np.mean(x_values),
        'x_std': np.std(x_values),
        'y_mean': np.mean(y_values),
        'y_std': np.std(y_values),
        'z_mean': np.mean(z_values),
        'z_std': np.std(z_values),
    }
    return features


def motion_detection(features):
    """
    根据标准差和均值判断运动状态
    :param features:
    :return: 1 静止 2 运动
    """
    # 判断条件：任意一个轴的均值和标准差超过阈值则判断为运动状态
    if (features['x_std'] > x_std_thresh or
            features['y_std'] > y_std_thresh or
            features['z_std'] > z_std_thresh):
        return 2
    else:
        return 1
