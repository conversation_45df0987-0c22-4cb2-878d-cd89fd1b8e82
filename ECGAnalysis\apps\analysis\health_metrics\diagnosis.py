from apps.analysis.health_metrics import rf_model_index
from apps.models.arrhythmia_models import HealthMetricsEntity


def process(ecg_data, sampling_rate):
    """
    健康指标
    :param ecg_data: ECG信号数据
    :param sampling_rate: 采样率
    :return:
    """
    health_metrics = HealthMetricsEntity()

    y_pred = rf_model_index.run_index_main(ecg_data, sampling_rate)

    health_metrics.Pressure = y_pred[0]
    health_metrics.HRV = 0
    health_metrics.Emotion = y_pred[1]
    health_metrics.Fatigue = y_pred[2]
    health_metrics.Vitality = y_pred[3]
    health_metrics.HeartAge = 0

    return health_metrics