# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: contrib/admin/actions.py:16
#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr ""

#: contrib/admin/actions.py:46
#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr ""

#: contrib/admin/actions.py:55 contrib/admin/options.py:1886
#, python-format
msgid "Cannot delete %(name)s"
msgstr ""

#: contrib/admin/actions.py:57 contrib/admin/options.py:1888
msgid "Are you sure?"
msgstr ""

#: contrib/admin/apps.py:13
msgid "Administration"
msgstr ""

#: contrib/admin/filters.py:108 contrib/admin/filters.py:213
#: contrib/admin/filters.py:249 contrib/admin/filters.py:284
#: contrib/admin/filters.py:403 contrib/admin/filters.py:468
msgid "All"
msgstr ""

#: contrib/admin/filters.py:250
msgid "Yes"
msgstr ""

#: contrib/admin/filters.py:251
msgid "No"
msgstr ""

#: contrib/admin/filters.py:262
msgid "Unknown"
msgstr ""

#: contrib/admin/filters.py:332
msgid "Any date"
msgstr ""

#: contrib/admin/filters.py:333
msgid "Today"
msgstr ""

#: contrib/admin/filters.py:337
msgid "Past 7 days"
msgstr ""

#: contrib/admin/filters.py:341
msgid "This month"
msgstr ""

#: contrib/admin/filters.py:345
msgid "This year"
msgstr ""

#: contrib/admin/filters.py:353
msgid "No date"
msgstr ""

#: contrib/admin/filters.py:354
msgid "Has date"
msgstr ""

#: contrib/admin/filters.py:469
msgid "Empty"
msgstr ""

#: contrib/admin/filters.py:470
msgid "Not empty"
msgstr ""

#: contrib/admin/forms.py:13
#, python-format
msgid ""
"Please enter the correct %(username)s and password for a staff account. Note "
"that both fields may be case-sensitive."
msgstr ""

#: contrib/admin/helpers.py:23
msgid "Action:"
msgstr ""

#: contrib/admin/helpers.py:329
#, python-format
msgid "Add another %(verbose_name)s"
msgstr ""

#: contrib/admin/helpers.py:332
msgid "Remove"
msgstr ""

#: contrib/admin/models.py:17
msgid "Addition"
msgstr ""

#: contrib/admin/models.py:18 contrib/admin/templates/admin/app_list.html:28
#: contrib/admin/templates/admin/edit_inline/stacked.html:12
#: contrib/admin/templates/admin/edit_inline/tabular.html:34
#: contrib/admin/templates/admin/widgets/related_widget_wrapper.html:11
msgid "Change"
msgstr ""

#: contrib/admin/models.py:19
msgid "Deletion"
msgstr ""

#: contrib/admin/models.py:41
msgid "action time"
msgstr ""

#: contrib/admin/models.py:48
msgid "user"
msgstr ""

#: contrib/admin/models.py:53
msgid "content type"
msgstr ""

#: contrib/admin/models.py:56
msgid "object id"
msgstr ""

#. Translators: 'repr' means representation (https://docs.python.org/library/functions.html#repr)
#: contrib/admin/models.py:58
msgid "object repr"
msgstr ""

#: contrib/admin/models.py:59
msgid "action flag"
msgstr ""

#: contrib/admin/models.py:61
msgid "change message"
msgstr ""

#: contrib/admin/models.py:66
msgid "log entry"
msgstr ""

#: contrib/admin/models.py:67
msgid "log entries"
msgstr ""

#: contrib/admin/models.py:76
#, python-format
msgid "Added “%(object)s”."
msgstr ""

#: contrib/admin/models.py:78
#, python-format
msgid "Changed “%(object)s” — %(changes)s"
msgstr ""

#: contrib/admin/models.py:83
#, python-format
msgid "Deleted “%(object)s.”"
msgstr ""

#: contrib/admin/models.py:85
msgid "LogEntry Object"
msgstr ""

#: contrib/admin/models.py:111
#, python-brace-format
msgid "Added {name} “{object}”."
msgstr ""

#: contrib/admin/models.py:113
msgid "Added."
msgstr ""

#: contrib/admin/models.py:117 contrib/admin/options.py:2112
msgid "and"
msgstr ""

#: contrib/admin/models.py:121
#, python-brace-format
msgid "Changed {fields} for {name} “{object}”."
msgstr ""

#: contrib/admin/models.py:125
#, python-brace-format
msgid "Changed {fields}."
msgstr ""

#: contrib/admin/models.py:129
#, python-brace-format
msgid "Deleted {name} “{object}”."
msgstr ""

#: contrib/admin/models.py:132
msgid "No fields changed."
msgstr ""

#: contrib/admin/options.py:203 contrib/admin/options.py:235
msgid "None"
msgstr ""

#: contrib/admin/options.py:281
msgid "Hold down “Control”, or “Command” on a Mac, to select more than one."
msgstr ""

#: contrib/admin/options.py:1221 contrib/admin/options.py:1245
#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr ""

#: contrib/admin/options.py:1223
msgid "You may edit it again below."
msgstr ""

#: contrib/admin/options.py:1235
#, python-brace-format
msgid ""
"The {name} “{obj}” was added successfully. You may add another {name} below."
msgstr ""

#: contrib/admin/options.py:1285
#, python-brace-format
msgid ""
"The {name} “{obj}” was changed successfully. You may edit it again below."
msgstr ""

#: contrib/admin/options.py:1295
#, python-brace-format
msgid "The {name} “{obj}” was added successfully. You may edit it again below."
msgstr ""

#: contrib/admin/options.py:1308
#, python-brace-format
msgid ""
"The {name} “{obj}” was changed successfully. You may add another {name} "
"below."
msgstr ""

#: contrib/admin/options.py:1320
#, python-brace-format
msgid "The {name} “{obj}” was changed successfully."
msgstr ""

#: contrib/admin/options.py:1397 contrib/admin/options.py:1727
msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr ""

#: contrib/admin/options.py:1416
msgid "No action selected."
msgstr ""

#: contrib/admin/options.py:1441
#, python-format
msgid "The %(name)s “%(obj)s” was deleted successfully."
msgstr ""

#: contrib/admin/options.py:1527
#, python-format
msgid "%(name)s with ID “%(key)s” doesn’t exist. Perhaps it was deleted?"
msgstr ""

#: contrib/admin/options.py:1622
#, python-format
msgid "Add %s"
msgstr ""

#: contrib/admin/options.py:1624
#, python-format
msgid "Change %s"
msgstr ""

#: contrib/admin/options.py:1626
#, python-format
msgid "View %s"
msgstr ""

#: contrib/admin/options.py:1705
msgid "Database error"
msgstr ""

#: contrib/admin/options.py:1774
#, python-format
msgid "%(count)s %(name)s was changed successfully."
msgid_plural "%(count)s %(name)s were changed successfully."
msgstr[0] ""
msgstr[1] ""

#: contrib/admin/options.py:1805
#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] ""
msgstr[1] ""

#: contrib/admin/options.py:1813
#, python-format
msgid "0 of %(cnt)s selected"
msgstr ""

#: contrib/admin/options.py:1932
#, python-format
msgid "Change history: %s"
msgstr ""

#. Translators: Model verbose name and instance representation,
#. suitable to be an item in a list.
#: contrib/admin/options.py:2105
#, python-format
msgid "%(class_name)s %(instance)s"
msgstr ""

#: contrib/admin/options.py:2114
#, python-format
msgid ""
"Deleting %(class_name)s %(instance)s would require deleting the following "
"protected related objects: %(related_objects)s"
msgstr ""

#: contrib/admin/sites.py:47 contrib/admin/templates/admin/base_site.html:3
msgid "Django site admin"
msgstr ""

#: contrib/admin/sites.py:50 contrib/admin/templates/admin/base_site.html:6
msgid "Django administration"
msgstr ""

#: contrib/admin/sites.py:53
msgid "Site administration"
msgstr ""

#: contrib/admin/sites.py:399 contrib/admin/templates/admin/login.html:63
#: contrib/admin/templates/registration/password_reset_complete.html:15
#: contrib/admin/tests.py:135
msgid "Log in"
msgstr ""

#: contrib/admin/sites.py:544
#, python-format
msgid "%(app)s administration"
msgstr ""

#: contrib/admin/templates/admin/404.html:4
#: contrib/admin/templates/admin/404.html:8
msgid "Page not found"
msgstr ""

#: contrib/admin/templates/admin/404.html:10
msgid "We’re sorry, but the requested page could not be found."
msgstr ""

#: contrib/admin/templates/admin/500.html:6
#: contrib/admin/templates/admin/app_index.html:9
#: contrib/admin/templates/admin/auth/user/change_password.html:10
#: contrib/admin/templates/admin/base.html:65
#: contrib/admin/templates/admin/change_form.html:18
#: contrib/admin/templates/admin/change_list.html:31
#: contrib/admin/templates/admin/delete_confirmation.html:14
#: contrib/admin/templates/admin/delete_selected_confirmation.html:14
#: contrib/admin/templates/admin/invalid_setup.html:6
#: contrib/admin/templates/admin/object_history.html:6
#: contrib/admin/templates/registration/logged_out.html:4
#: contrib/admin/templates/registration/password_change_done.html:6
#: contrib/admin/templates/registration/password_change_form.html:7
#: contrib/admin/templates/registration/password_reset_complete.html:6
#: contrib/admin/templates/registration/password_reset_confirm.html:7
#: contrib/admin/templates/registration/password_reset_done.html:6
#: contrib/admin/templates/registration/password_reset_form.html:7
msgid "Home"
msgstr ""

#: contrib/admin/templates/admin/500.html:7
msgid "Server error"
msgstr ""

#: contrib/admin/templates/admin/500.html:11
msgid "Server error (500)"
msgstr ""

#: contrib/admin/templates/admin/500.html:14
msgid "Server Error <em>(500)</em>"
msgstr ""

#: contrib/admin/templates/admin/500.html:15
msgid ""
"There’s been an error. It’s been reported to the site administrators via "
"email and should be fixed shortly. Thanks for your patience."
msgstr ""

#: contrib/admin/templates/admin/actions.html:8
msgid "Run the selected action"
msgstr ""

#: contrib/admin/templates/admin/actions.html:8
msgid "Go"
msgstr ""

#: contrib/admin/templates/admin/actions.html:16
msgid "Click here to select the objects across all pages"
msgstr ""

#: contrib/admin/templates/admin/actions.html:16
#, python-format
msgid "Select all %(total_count)s %(module_name)s"
msgstr ""

#: contrib/admin/templates/admin/actions.html:18
msgid "Clear selection"
msgstr ""

#: contrib/admin/templates/admin/app_list.html:8
#, python-format
msgid "Models in the %(name)s application"
msgstr ""

#: contrib/admin/templates/admin/app_list.html:19
#: contrib/admin/templates/admin/widgets/related_widget_wrapper.html:18
msgid "Add"
msgstr ""

#: contrib/admin/templates/admin/app_list.html:26
#: contrib/admin/templates/admin/edit_inline/stacked.html:12
#: contrib/admin/templates/admin/edit_inline/tabular.html:34
msgid "View"
msgstr ""

#: contrib/admin/templates/admin/app_list.html:39
msgid "You don’t have permission to view or edit anything."
msgstr ""

#: contrib/admin/templates/admin/auth/user/add_form.html:6
msgid ""
"First, enter a username and password. Then, you’ll be able to edit more user "
"options."
msgstr ""

#: contrib/admin/templates/admin/auth/user/add_form.html:8
msgid "Enter a username and password."
msgstr ""

#: contrib/admin/templates/admin/auth/user/change_password.html:14
#: contrib/admin/templates/admin/auth/user/change_password.html:52
#: contrib/admin/templates/admin/base.html:53
#: contrib/admin/templates/registration/password_change_done.html:3
#: contrib/admin/templates/registration/password_change_form.html:4
msgid "Change password"
msgstr ""

#: contrib/admin/templates/admin/auth/user/change_password.html:25
#: contrib/admin/templates/admin/change_form.html:43
#: contrib/admin/templates/admin/change_list.html:51
#: contrib/admin/templates/admin/login.html:23
#: contrib/admin/templates/registration/password_change_form.html:18
msgid "Please correct the error below."
msgstr ""

#: contrib/admin/templates/admin/auth/user/change_password.html:25
#: contrib/admin/templates/admin/change_form.html:43
#: contrib/admin/templates/admin/change_list.html:51
#: contrib/admin/templates/admin/login.html:23
#: contrib/admin/templates/registration/password_change_form.html:18
msgid "Please correct the errors below."
msgstr ""

#: contrib/admin/templates/admin/auth/user/change_password.html:29
#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""

#: contrib/admin/templates/admin/base.html:39
msgid "Welcome,"
msgstr ""

#: contrib/admin/templates/admin/base.html:44
msgid "View site"
msgstr ""

#: contrib/admin/templates/admin/base.html:49
#: contrib/admin/templates/registration/password_change_done.html:3
#: contrib/admin/templates/registration/password_change_form.html:4
msgid "Documentation"
msgstr ""

#: contrib/admin/templates/admin/base.html:55
#: contrib/admin/templates/registration/password_change_done.html:3
#: contrib/admin/templates/registration/password_change_form.html:4
msgid "Log out"
msgstr ""

#: contrib/admin/templates/admin/change_form.html:21
#: contrib/admin/templates/admin/change_list_object_tools.html:8
#, python-format
msgid "Add %(name)s"
msgstr ""

#: contrib/admin/templates/admin/change_form_object_tools.html:5
#: contrib/admin/templates/admin/object_history.html:10
msgid "History"
msgstr ""

#: contrib/admin/templates/admin/change_form_object_tools.html:7
#: contrib/admin/templates/admin/edit_inline/stacked.html:14
#: contrib/admin/templates/admin/edit_inline/tabular.html:36
msgid "View on site"
msgstr ""

#: contrib/admin/templates/admin/change_list.html:76
msgid "Filter"
msgstr ""

#: contrib/admin/templates/admin/change_list.html:78
msgid "Clear all filters"
msgstr ""

#: contrib/admin/templates/admin/change_list_results.html:17
msgid "Remove from sorting"
msgstr ""

#: contrib/admin/templates/admin/change_list_results.html:18
#, python-format
msgid "Sorting priority: %(priority_number)s"
msgstr ""

#: contrib/admin/templates/admin/change_list_results.html:19
msgid "Toggle sorting"
msgstr ""

#: contrib/admin/templates/admin/delete_confirmation.html:18
#: contrib/admin/templates/admin/submit_line.html:7
#: contrib/admin/templates/admin/widgets/related_widget_wrapper.html:25
msgid "Delete"
msgstr ""

#: contrib/admin/templates/admin/delete_confirmation.html:24
#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""

#: contrib/admin/templates/admin/delete_confirmation.html:31
#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would require deleting the "
"following protected related objects:"
msgstr ""

#: contrib/admin/templates/admin/delete_confirmation.html:38
#, python-format
msgid ""
"Are you sure you want to delete the %(object_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""

#: contrib/admin/templates/admin/delete_confirmation.html:40
#: contrib/admin/templates/admin/delete_selected_confirmation.html:39
msgid "Objects"
msgstr ""

#: contrib/admin/templates/admin/delete_confirmation.html:47
#: contrib/admin/templates/admin/delete_selected_confirmation.html:50
msgid "Yes, I’m sure"
msgstr ""

#: contrib/admin/templates/admin/delete_confirmation.html:48
#: contrib/admin/templates/admin/delete_selected_confirmation.html:51
msgid "No, take me back"
msgstr ""

#: contrib/admin/templates/admin/delete_selected_confirmation.html:17
msgid "Delete multiple objects"
msgstr ""

#: contrib/admin/templates/admin/delete_selected_confirmation.html:23
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""

#: contrib/admin/templates/admin/delete_selected_confirmation.html:30
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""

#: contrib/admin/templates/admin/delete_selected_confirmation.html:37
#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""

#: contrib/admin/templates/admin/edit_inline/tabular.html:20
msgid "Delete?"
msgstr ""

#: contrib/admin/templates/admin/filter.html:2
#, python-format
msgid " By %(filter_title)s "
msgstr ""

#: contrib/admin/templates/admin/includes/object_delete_summary.html:2
msgid "Summary"
msgstr ""

#: contrib/admin/templates/admin/index.html:23
msgid "Recent actions"
msgstr ""

#: contrib/admin/templates/admin/index.html:24
msgid "My actions"
msgstr ""

#: contrib/admin/templates/admin/index.html:28
msgid "None available"
msgstr ""

#: contrib/admin/templates/admin/index.html:42
msgid "Unknown content"
msgstr ""

#: contrib/admin/templates/admin/invalid_setup.html:12
msgid ""
"Something’s wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""

#: contrib/admin/templates/admin/login.html:39
#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""

#: contrib/admin/templates/admin/login.html:59
msgid "Forgotten your password or username?"
msgstr ""

#: contrib/admin/templates/admin/nav_sidebar.html:2
msgid "Toggle navigation"
msgstr ""

#: contrib/admin/templates/admin/object_history.html:22
msgid "Date/time"
msgstr ""

#: contrib/admin/templates/admin/object_history.html:23
msgid "User"
msgstr ""

#: contrib/admin/templates/admin/object_history.html:24
msgid "Action"
msgstr ""

#: contrib/admin/templates/admin/object_history.html:38
msgid ""
"This object doesn’t have a change history. It probably wasn’t added via this "
"admin site."
msgstr ""

#: contrib/admin/templates/admin/pagination.html:10
#: contrib/admin/templates/admin/search_form.html:9
msgid "Show all"
msgstr ""

#: contrib/admin/templates/admin/pagination.html:11
#: contrib/admin/templates/admin/submit_line.html:4
msgid "Save"
msgstr ""

#: contrib/admin/templates/admin/popup_response.html:3
msgid "Popup closing…"
msgstr ""

#: contrib/admin/templates/admin/search_form.html:7
msgid "Search"
msgstr ""

#: contrib/admin/templates/admin/search_form.html:9
#, python-format
msgid "%(counter)s result"
msgid_plural "%(counter)s results"
msgstr[0] ""
msgstr[1] ""

#: contrib/admin/templates/admin/search_form.html:9
#, python-format
msgid "%(full_result_count)s total"
msgstr ""

#: contrib/admin/templates/admin/submit_line.html:9
msgid "Save as new"
msgstr ""

#: contrib/admin/templates/admin/submit_line.html:10
msgid "Save and add another"
msgstr ""

#: contrib/admin/templates/admin/submit_line.html:11
msgid "Save and continue editing"
msgstr ""

#: contrib/admin/templates/admin/submit_line.html:11
msgid "Save and view"
msgstr ""

#: contrib/admin/templates/admin/submit_line.html:12
msgid "Close"
msgstr ""

#: contrib/admin/templates/admin/widgets/related_widget_wrapper.html:10
#, python-format
msgid "Change selected %(model)s"
msgstr ""

#: contrib/admin/templates/admin/widgets/related_widget_wrapper.html:17
#, python-format
msgid "Add another %(model)s"
msgstr ""

#: contrib/admin/templates/admin/widgets/related_widget_wrapper.html:24
#, python-format
msgid "Delete selected %(model)s"
msgstr ""

#: contrib/admin/templates/registration/logged_out.html:10
msgid "Thanks for spending some quality time with the Web site today."
msgstr ""

#: contrib/admin/templates/registration/logged_out.html:12
msgid "Log in again"
msgstr ""

#: contrib/admin/templates/registration/password_change_done.html:7
#: contrib/admin/templates/registration/password_change_form.html:8
msgid "Password change"
msgstr ""

#: contrib/admin/templates/registration/password_change_done.html:12
msgid "Your password was changed."
msgstr ""

#: contrib/admin/templates/registration/password_change_form.html:23
msgid ""
"Please enter your old password, for security’s sake, and then enter your new "
"password twice so we can verify you typed it in correctly."
msgstr ""

#: contrib/admin/templates/registration/password_change_form.html:51
#: contrib/admin/templates/registration/password_reset_confirm.html:31
msgid "Change my password"
msgstr ""

#: contrib/admin/templates/registration/password_reset_complete.html:7
#: contrib/admin/templates/registration/password_reset_done.html:7
#: contrib/admin/templates/registration/password_reset_form.html:8
msgid "Password reset"
msgstr ""

#: contrib/admin/templates/registration/password_reset_complete.html:13
msgid "Your password has been set.  You may go ahead and log in now."
msgstr ""

#: contrib/admin/templates/registration/password_reset_confirm.html:8
msgid "Password reset confirmation"
msgstr ""

#: contrib/admin/templates/registration/password_reset_confirm.html:16
msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr ""

#: contrib/admin/templates/registration/password_reset_confirm.html:23
msgid "New password:"
msgstr ""

#: contrib/admin/templates/registration/password_reset_confirm.html:28
msgid "Confirm password:"
msgstr ""

#: contrib/admin/templates/registration/password_reset_confirm.html:37
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr ""

#: contrib/admin/templates/registration/password_reset_done.html:13
msgid ""
"We’ve emailed you instructions for setting your password, if an account "
"exists with the email you entered. You should receive them shortly."
msgstr ""

#: contrib/admin/templates/registration/password_reset_done.html:15
msgid ""
"If you don’t receive an email, please make sure you’ve entered the address "
"you registered with, and check your spam folder."
msgstr ""

#: contrib/admin/templates/registration/password_reset_email.html:2
#, python-format
msgid ""
"You're receiving this email because you requested a password reset for your "
"user account at %(site_name)s."
msgstr ""

#: contrib/admin/templates/registration/password_reset_email.html:4
msgid "Please go to the following page and choose a new password:"
msgstr ""

#: contrib/admin/templates/registration/password_reset_email.html:8
msgid "Your username, in case you’ve forgotten:"
msgstr ""

#: contrib/admin/templates/registration/password_reset_email.html:10
msgid "Thanks for using our site!"
msgstr ""

#: contrib/admin/templates/registration/password_reset_email.html:12
#, python-format
msgid "The %(site_name)s team"
msgstr ""

#: contrib/admin/templates/registration/password_reset_form.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we’ll email "
"instructions for setting a new one."
msgstr ""

#: contrib/admin/templates/registration/password_reset_form.html:20
msgid "Email address:"
msgstr ""

#: contrib/admin/templates/registration/password_reset_form.html:23
msgid "Reset my password"
msgstr ""

#: contrib/admin/templatetags/admin_list.py:390
msgid "All dates"
msgstr ""

#: contrib/admin/views/main.py:102
#, python-format
msgid "Select %s"
msgstr ""

#: contrib/admin/views/main.py:104
#, python-format
msgid "Select %s to change"
msgstr ""

#: contrib/admin/views/main.py:106
#, python-format
msgid "Select %s to view"
msgstr ""

#: contrib/admin/widgets.py:87
msgid "Date:"
msgstr ""

#: contrib/admin/widgets.py:88
msgid "Time:"
msgstr ""

#: contrib/admin/widgets.py:150
msgid "Lookup"
msgstr ""

#: contrib/admin/widgets.py:340
msgid "Currently:"
msgstr ""

#: contrib/admin/widgets.py:341
msgid "Change:"
msgstr ""
