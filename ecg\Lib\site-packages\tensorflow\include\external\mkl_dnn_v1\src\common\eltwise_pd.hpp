/*******************************************************************************
* Copyright 2016-2021 Intel Corporation
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*******************************************************************************/

#ifndef COMMON_ELTWISE_PD_HPP
#define COMMON_ELTWISE_PD_HPP

#include "oneapi/dnnl/dnnl.h"

#include "c_types_map.hpp"
#include "primitive_desc.hpp"

namespace dnnl {
namespace impl {

struct eltwise_fwd_pd_t;

struct eltwise_pd_t : public primitive_desc_t {
    static constexpr auto base_pkind = primitive_kind::eltwise;

    eltwise_pd_t(const eltwise_desc_t *adesc, const primitive_attr_t *attr,
            const eltwise_fwd_pd_t *hint_fwd_pd)
        : primitive_desc_t(attr, base_pkind)
        , desc_(*adesc)
        , hint_fwd_pd_(hint_fwd_pd)
        , data_md_(desc_.data_desc) {}

    const eltwise_desc_t *desc() const { return &desc_; }
    const op_desc_t *op_desc() const override {
        return reinterpret_cast<const op_desc_t *>(this->desc());
    }

    status_t query(query_t what, int idx, void *result) const override {
        switch (what) {
            case query::prop_kind:
                *(prop_kind_t *)result = desc()->prop_kind;
                break;
            case query::eltwise_d:
                *(const eltwise_desc_t **)result = desc();
                break;
            default: return primitive_desc_t::query(what, idx, result);
        }
        return status::success;
    }

    /* common eltwise aux functions */

    dim_t MB() const { return data_desc().dims[0]; }
    dim_t C() const { return data_desc().dims[1]; }
    dim_t D() const { return ndims() >= 5 ? data_desc().dims[ndims() - 3] : 1; }
    dim_t H() const { return ndims() >= 4 ? data_desc().dims[ndims() - 2] : 1; }
    dim_t W() const { return ndims() >= 3 ? data_desc().dims[ndims() - 1] : 1; }

    int ndims() const { return data_desc().ndims; }

    bool is_fwd() const {
        return utils::one_of(desc_.prop_kind, prop_kind::forward_training,
                prop_kind::forward_inference);
    }

    bool has_zero_dim_memory() const {
        return memory_desc_wrapper(desc_.data_desc).has_zero_dim();
    }

    bool use_dst() const {
        using namespace alg_kind;
        return !is_fwd()
                && utils::one_of(desc_.alg_kind, eltwise_relu_use_dst_for_bwd,
                        eltwise_tanh_use_dst_for_bwd,
                        eltwise_elu_use_dst_for_bwd,
                        eltwise_sqrt_use_dst_for_bwd,
                        eltwise_logistic_use_dst_for_bwd,
                        eltwise_exp_use_dst_for_bwd,
                        eltwise_clip_v2_use_dst_for_bwd);
    }

protected:
    eltwise_desc_t desc_;
    const eltwise_fwd_pd_t *hint_fwd_pd_;

    memory_desc_t data_md_;

private:
    const memory_desc_t &data_desc() const { return desc_.data_desc; }
};

struct eltwise_fwd_pd_t : public eltwise_pd_t {
    typedef eltwise_fwd_pd_t base_class;
    typedef eltwise_fwd_pd_t hint_class;

    eltwise_fwd_pd_t(const eltwise_desc_t *adesc, const primitive_attr_t *attr,
            const eltwise_fwd_pd_t *hint_fwd_pd)
        : eltwise_pd_t(adesc, attr, hint_fwd_pd) {}

    arg_usage_t arg_usage(int arg) const override {
        if (arg == DNNL_ARG_SRC) return arg_usage_t::input;

        if (arg == DNNL_ARG_DST) return arg_usage_t::output;

        return primitive_desc_t::arg_usage(arg);
    }

    const memory_desc_t *arg_md(int arg) const override {
        switch (arg) {
            case DNNL_ARG_SRC: return src_md(0);
            case DNNL_ARG_DST: return dst_md(0);
            default: return eltwise_pd_t::arg_md(arg);
        }
    }

    // For compatibility with backward
    const memory_desc_t *data_md(int index = 0) const {
        return index == 0 ? &data_md_ : &glob_zero_md;
    }
    const memory_desc_t *src_md(int index = 0) const override {
        return data_md(index);
    }
    const memory_desc_t *dst_md(int index = 0) const override {
        return data_md(index);
    }

    int n_inputs() const override { return 1 + n_binary_po_inputs(); }
    int n_outputs() const override { return 1; }

    static bool eltwise_preserves_zero(
            alg_kind_t alg, float alpha, float beta) {
        using namespace alg_kind;
        using namespace utils;
        return one_of(alg, eltwise_relu, eltwise_tanh, eltwise_elu,
                       eltwise_square, eltwise_abs, eltwise_sqrt, eltwise_swish,
                       eltwise_bounded_relu, eltwise_gelu_tanh,
                       eltwise_gelu_erf, eltwise_round, eltwise_hardswish)
                || one_of(alg, eltwise_relu_use_dst_for_bwd,
                        eltwise_tanh_use_dst_for_bwd,
                        eltwise_elu_use_dst_for_bwd,
                        eltwise_sqrt_use_dst_for_bwd)
                || (one_of(alg, eltwise_clip, eltwise_clip_v2) && alpha <= 0
                        && beta >= 0)
                || (alg == eltwise_linear && beta == 0)
                || (alg == eltwise_pow && beta > 0);
    }

    static bool eltwise_preserves_zero(
            const post_ops_t::entry_t::eltwise_t &eltwise) {
        return eltwise_preserves_zero(eltwise.alg, eltwise.alpha, eltwise.beta);
    }

    bool is_zero_preserved() const {
        return eltwise_preserves_zero(desc_.alg_kind, desc_.alpha, desc_.beta);
    }
};

struct eltwise_bwd_pd_t : public eltwise_pd_t {
    typedef eltwise_bwd_pd_t base_class;
    typedef eltwise_fwd_pd_t hint_class;

    eltwise_bwd_pd_t(const eltwise_desc_t *adesc, const primitive_attr_t *attr,
            const eltwise_fwd_pd_t *hint_fwd_pd)
        : eltwise_pd_t(adesc, attr, hint_fwd_pd)
        , diff_data_md_(desc_.diff_data_desc) {}

    arg_usage_t arg_usage(int arg) const override {
        if (use_dst() ? arg == DNNL_ARG_DST : arg == DNNL_ARG_SRC)
            return arg_usage_t::input;

        if (arg == DNNL_ARG_DIFF_DST) return arg_usage_t::input;
        if (arg == DNNL_ARG_DIFF_SRC) return arg_usage_t::output;

        return primitive_desc_t::arg_usage(arg);
    }

    const memory_desc_t *arg_md(int arg) const override {
        switch (arg) {
            case DNNL_ARG_SRC: return src_md(0);
            case DNNL_ARG_DST: return dst_md(0);
            case DNNL_ARG_DIFF_SRC: return diff_src_md(0);
            case DNNL_ARG_DIFF_DST: return diff_dst_md(0);
            default: return eltwise_pd_t::arg_md(arg);
        }
    }

    // To avoid additional logic in implementations
    const memory_desc_t *data_md(int index = 0) const {
        return index == 0 ? &data_md_ : &glob_zero_md;
    }
    const memory_desc_t *src_md(int index = 0) const override {
        if (!use_dst()) return data_md(index);
        return &glob_zero_md;
    }
    const memory_desc_t *dst_md(int index = 0) const override {
        if (use_dst()) return data_md(index);
        return &glob_zero_md;
    }
    const memory_desc_t *diff_dst_md(int index = 0) const override {
        return index == 0 ? &diff_data_md_ : &glob_zero_md;
    }
    const memory_desc_t *diff_src_md(int index = 0) const override {
        return index == 0 ? &diff_data_md_ : &glob_zero_md;
    }

    int n_inputs() const override { return 2; }
    int n_outputs() const override { return 1; }

    static bool eltwise_preserves_zero(
            alg_kind_t alg, float alpha, float beta) {
        // Unlike forward counterpart, bwd works on two tensors (with same formats)
        // and if alg moves zero to non-zero, it's fine, because diff_dst will
        // still have zeros in padding and multiplication of zero and non-zero
        // gives desired result. However, it doesn't work in case of special fp
        // values which are NaN or infinity which give NaN when multiplying on
        // zero, so excluding all those algs from here.
        using namespace alg_kind;
        using namespace utils;
        return one_of(alg, eltwise_abs, eltwise_bounded_relu, eltwise_clip,
                       eltwise_clip_v2, eltwise_elu, eltwise_exp,
                       eltwise_gelu_erf, eltwise_gelu_tanh, eltwise_linear,
                       eltwise_logistic, eltwise_logsigmoid, eltwise_mish,
                       eltwise_relu, eltwise_soft_relu, eltwise_square,
                       eltwise_swish, eltwise_tanh)
                || one_of(alg, eltwise_elu_use_dst_for_bwd,
                        eltwise_exp_use_dst_for_bwd,
                        eltwise_logistic_use_dst_for_bwd,
                        eltwise_relu_use_dst_for_bwd,
                        eltwise_tanh_use_dst_for_bwd,
                        eltwise_clip_v2_use_dst_for_bwd)
                || (alg == eltwise_pow && beta >= 1);
    }

    bool is_zero_preserved() const {
        return eltwise_preserves_zero(desc_.alg_kind, desc_.alpha, desc_.beta);
    }

protected:
    memory_desc_t diff_data_md_;

    bool set_default_formats_common() {
        if (diff_data_md_.format_kind != format_kind::any) return true;

        return memory_desc_init_by_md_and_dt(
                       diff_data_md_, data_md_, diff_data_md_.data_type)
                == status::success;
    }
};

} // namespace impl
} // namespace dnnl

#endif

// vim: et ts=4 sw=4 cindent cino+=l0,\:4,N-s
