import ast
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from pyhrv import time_domain, frequency_domain
from apps.models.t_data_ecg_model import get_data_ecg_model
from apps.models.xh_report_model import HourlyStatisticsModel, HourlyStatisticsDetailModel
from apps.utils.sql_helper import table_exists, <PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>


def get_report_data(union_id, start_date, end_date):
    """
    获取报告数据
    :param union_id: 用户id
    :param start_date: 开始日期
    :param end_date: 结束日期
    :return: 报告数据
    """

    data_dates = get_data_dates(start_date, end_date)

    ecg_datas = []
    ecg_detail_datas = []

    for data_date in data_dates:

        ecg_datas_table_name = f't_data_ecg_{data_date}'
        # 检查表是否存在
        if not table_exists(ecg_datas_table_name):
            continue

        table_obj = get_data_ecg_model(data_date)  # 获取表对象
        ecg_data = table_obj.objects.filter(union_id=union_id).all()

        ecg_datas.extend(list(ecg_data))

        ecg_detail_datas_table_name = f't_data_ecg_detail_{data_date}'

        # 检查表是否存在
        if not table_exists(ecg_detail_datas_table_name):
            continue

        sql = f"""
            select a.* from {ecg_detail_datas_table_name} a
            left join {ecg_datas_table_name} b on b.id = a.data_ecg_id
            where a.union_id = '{union_id}'
            and b.dead = 1
        
        """

        datas = MysqlHelper('ecg_analysis').datas_query(sql)

        if datas:
            ecg_detail_datas.extend(list(datas))

    detail_columns = [
        'id',
        'union_id',
        'data_ecg_id',
        'check_date',
        'disease_code',
        'total_episodes',
        'total_duration',
        'fastest_hr',
        'longest_duration',
        'longest_rr',
        'single_count',
        'pair_count',
        'bigeminy_count',
        'trigeminy_count',
        'run_count',
        'max_consecutive',
        'fastest_run_hr',
        'slowest_run_hr',
        'hr_total_count',
        'create_time'
    ]

    return ecg_datas, pd.DataFrame(ecg_detail_datas, columns=detail_columns)


def get_report(report_model, datas, normal_data, hourly_statistics_list, detail_datas):
    """
    获取报告
    :param report_model: 报告模型
    :param datas: 原始数据
    :param normal_data: 正常数据
    :param hourly_statistics_list: 小时统计数据
    :param detail_datas: 详细数据 (新增)
    :return: 报告模型
    """
    record_total_duration = len(datas)  # 记录总时长（分钟）

    # 1. 设置报表心率
    report_model = report_heart_rate(report_model, normal_data, hourly_statistics_list)

    # 2. 设置房颤/房扑
    # 确保 detail_datas 是 DataFrame
    if not isinstance(detail_datas, pd.DataFrame):
        detail_datas = pd.DataFrame(detail_datas)
    # 筛选 AF/AFL 相关数据
    af_afl_detail_datas = detail_datas[
        detail_datas['disease_code'].isin(['AF', 'AFL'])] if 'disease_code' in detail_datas else pd.DataFrame()
    if not af_afl_detail_datas.empty:
        report_model = report_af_afl(report_model, record_total_duration, af_afl_detail_datas)

    # 3. 设置室性早搏
    # 筛选 PVC 相关数据
    pvc_detail_datas = detail_datas[
        detail_datas['disease_code'] == 'PVC'] if 'disease_code' in detail_datas else pd.DataFrame()
    if not pvc_detail_datas.empty:
        report_model = report_pvc(report_model, pvc_detail_datas)

    # 4. 设置HRV
    report_model = report_hrv(report_model, normal_data)

    # 5. 设置概述
    report_model = report_overview(report_model, normal_data)

    # 6. 设置结论
    report_model = report_conclusion(report_model)

    return report_model


def report_overview(report_model, normal_data):
    """
    概述
    :param report_model: 报告模型
    :param normal_data: 正常数据
    :return: 报告模型
    """
    normal_total_duration = len(normal_data)
    if normal_total_duration == 0:
        report_model.conclusion = "无有效心电数据。"
        return report_model

    # 计算心搏总数
    report_model.hr_total_count = int(normal_data['pqrstc_hr'].sum())

    return report_model


def report_af_afl(report_model, record_total_duration, detail_datas):
    """
    房颤/房扑
    :param report_model: 报告模型
    :param record_total_duration: 记录总时长（分钟）
    :param detail_datas: 详情数据
    :return: 报告模型
    """
    report_model.af_afl_total_episodes = detail_datas['total_episodes'].sum()  # 总阵数
    report_model.af_afl_total_duration = round(detail_datas['total_duration'].sum() / 60, 1)  # 总时长(分钟)
    report_model.af_afl_total_rate = round(report_model.af_afl_total_duration / record_total_duration * 100,
                                           2)  # 总占时比（%）

    # 最快心率
    hr_max_index = detail_datas['fastest_hr'].idxmax()
    hr_max_row = detail_datas.loc[hr_max_index]
    if hr_max_row['fastest_hr'] > 0:
        report_model.af_afl_hr_max = f"{int(hr_max_row['fastest_hr'])} @ {hr_max_row['check_date'].strftime('%Y-%m-%d %H:%M')}"  # 最快心率(次 / 分钟)

    # 最长持续(秒)
    longest_duration_index = detail_datas['longest_duration'].idxmax()
    longest_duration_row = detail_datas.loc[longest_duration_index]
    if longest_duration_row['longest_duration'] > 0:
        report_model.af_afl_longest_duration = f"{int(longest_duration_row['longest_duration'])} @ {longest_duration_row['check_date'].strftime('%Y-%m-%d %H:%M')}"

    # 最长RR(秒)
    longest_rr_index = detail_datas['longest_rr'].idxmax()
    longest_rr_row = detail_datas.loc[longest_rr_index]
    if longest_rr_row['longest_rr'] > 0:
        report_model.af_afl_longest_rr = f"{int(longest_rr_row['longest_rr'])}s @ {longest_rr_row['check_date'].strftime('%Y-%m-%d %H:%M')}"

    return report_model


def report_pvc(report_model, detail_datas):
    """
    室性早搏
    :param report_model: 报告模型
    :param detail_datas: 详情数据
    :return: 报告模型
    """
    # 计算室性早搏心搏总数
    report_model.pvc_single_count = detail_datas['single_count'].sum()  # 单发个数
    report_model.pvc_pair_count = detail_datas['pair_count'].sum()  # 成对阵数
    report_model.pvc_bigeminy_count = detail_datas['bigeminy_count'].sum()  # 二联律次数
    report_model.pvc_trigeminy_count = detail_datas['trigeminy_count'].sum()  # 三联律次数
    report_model.pvc_run_count = detail_datas['run_count'].sum()  # 连发阵数
    report_model.pvc_max_consecutive = detail_datas['single_count'].sum()  # 连发最多个数

    # 连发最快心率(次 / 分钟)
    fastest_run_hr_index = detail_datas['fastest_run_hr'].idxmax()
    if pd.isna(detail_datas['fastest_run_hr']).any():
        report_model.pvc_fastest_run_hr = ''
    else:
        fastest_run_hr_row = detail_datas.loc[fastest_run_hr_index]
        if fastest_run_hr_row['fastest_run_hr'] > 0:
            report_model.pvc_fastest_run_hr = f"{int(fastest_run_hr_row['fastest_run_hr'])} @ {fastest_run_hr_row['check_date'].strftime('%Y-%m-%d %H:%M')}"

    # 连发最慢心率(次 / 分钟)
    slowest_run_hr_index = detail_datas['slowest_run_hr'].idxmax()
    if pd.isna(detail_datas['slowest_run_hr']).any():
        report_model.pvc_slowest_run_hr = ''
    else:
        slowest_run_hr_row = detail_datas.loc[slowest_run_hr_index]
        if slowest_run_hr_row['slowest_run_hr'] > 0:
            report_model.pvc_slowest_run_hr = f"{int(slowest_run_hr_row['slowest_run_hr'])} @ {slowest_run_hr_row['check_date'].strftime('%Y-%m-%d %H:%M')}"

    report_model.pvc_hr_total_count = detail_datas['hr_total_count'].sum()  # 心搏总数

    return report_model


def report_conclusion(report_model):
    """
    结论
    :param report_model: 报告模型
    :return: 报告模型
    """

    # --- 结论生成逻辑 ---
    try:
        conclusion_parts = []

        # 1. 基础心律和平均心率
        if hasattr(report_model, 'hr_mean') and report_model.hr_mean is not None:
            hr_mean = int(report_model.hr_mean)
            if hr_mean < 60:
                conclusion_parts.append(f"全程基本心律为窦性心动过缓，平均心率为{hr_mean}次/分。")
            elif 60 <= hr_mean <= 100:
                conclusion_parts.append(f"全程基本心律为正常窦性心律，平均心率为{hr_mean}次/分。")
            else:
                conclusion_parts.append(f"全程基本心律为窦性心动过速，平均心率为{hr_mean}次/分。")
        else:
            conclusion_parts.append("窦性心律。")

        # 2. 心率极值
        if hasattr(report_model, 'nn_max') and report_model.nn_max and '@' in report_model.nn_max:
            parts = report_model.nn_max.split('@', 1)
            fastest_hr = int(parts[0].strip())
            fastest_time = parts[1].strip()
            if fastest_hr:
                conclusion_parts.append(f"最快心室率为{fastest_hr}次/分，发生于{fastest_time}。")

        if hasattr(report_model, 'nn_min') and report_model.nn_min and '@' in report_model.nn_min:
            parts = report_model.nn_min.split('@', 1)
            slowest_hr = int(parts[0].strip())
            slowest_time = parts[1].strip()
            if slowest_hr:
                conclusion_parts.append(f"最慢心室率为{slowest_hr}次/分，发生于{slowest_time}。")

        # 3. 最长RR间期
        if hasattr(report_model, 'longest_rr') and report_model.longest_rr:
            longest_rr = report_model.longest_rr
            if 's @' in longest_rr:
                parts = longest_rr.split('s @', 1)
                rr_value_str = parts[0].strip()
                rr_time = parts[1].strip()
                try:
                    rr_value = float(rr_value_str)
                except ValueError:
                    rr_value = None
            elif '@' in longest_rr:
                parts = longest_rr.split('@', 1)
                rr_str = parts[0].strip()
                rr_time = parts[1].strip()
                import re
                numbers = re.findall(r'\d+\.?\d*', rr_str)
                if numbers:
                    try:
                        rr_value = float(numbers[0])
                    except ValueError:
                        rr_value = None
                else:
                    rr_value = None
            else:
                rr_value = None
                rr_time = None

            if rr_value is not None:
                conclusion_parts.append(f"最长RR间期为{rr_value:.1f}秒，发生于{rr_time}。")

        # 4. 室性早搏 (使用 report_pvc 字段)
        pvc_single = getattr(report_model, 'pvc_single_count', 0) or 0
        pvc_pair = getattr(report_model, 'pvc_pair_count', 0) or 0
        pvc_run = getattr(report_model, 'pvc_run_count', 0) or 0

        if pvc_single > 0 or pvc_pair > 0 or pvc_run > 0:
            pvc_total_approx = pvc_single + pvc_pair * 2 + pvc_run * 3
            pvc_desc = f"共捕捉到室性早搏约{pvc_total_approx}个"
            details = []
            if pvc_single > 0: details.append(f"单发{pvc_single}个")
            if pvc_pair > 0: details.append(f"成对{pvc_pair}对")
            if pvc_run > 0: details.append(f"短阵{pvc_run}次")

            # 连发
            pvc_max_consecutive = getattr(report_model, 'pvc_max_consecutive', None)
            pvc_fastest_run_hr_str = getattr(report_model, 'pvc_fastest_run_hr', None)
            pvc_slowest_run_hr_str = getattr(report_model, 'pvc_slowest_run_hr', None)

            if pvc_max_consecutive and pvc_max_consecutive > 0:  # 连发最多个数
                pass

            fastest_run_hr, _ = parse_rate_time(pvc_fastest_run_hr_str)
            if fastest_run_hr:
                details.append(f"连发最快心率{fastest_run_hr}次/分")

            slowest_run_hr, _ = parse_rate_time(pvc_slowest_run_hr_str)
            if slowest_run_hr:
                details.append(f"连发最慢心率{slowest_run_hr}次/分")

            if details:
                pvc_desc += "，其中" + "，".join(details)
            conclusion_parts.append(pvc_desc + "。")

        # 5. 房颤/房扑
        af_afl_rate = getattr(report_model, 'af_afl_total_rate', 0) or 0
        af_afl_total_episodes = getattr(report_model, 'af_afl_total_episodes', 0) or 0

        if af_afl_rate > 0 or af_afl_total_episodes > 0:
            af_afl_total_rate = getattr(report_model, 'af_afl_total_rate', None)
            rate_to_report = af_afl_total_rate if af_afl_total_rate is not None else af_afl_rate

            af_desc = f"房颤/房扑占总时长的{rate_to_report:.2f}%"
            details = []
            if af_afl_total_episodes > 0:
                details.append(f"共{af_afl_total_episodes}次发作")

            # 获取并添加 AF/AFL 期间最快心率和最长持续时间
            af_afl_hr_max_str = getattr(report_model, 'af_afl_hr_max', None)
            af_afl_longest_duration_str = getattr(report_model, 'af_afl_longest_duration', None)

            fastest_af_hr, _ = parse_rate_time(af_afl_hr_max_str)
            if fastest_af_hr:
                details.append(f"期间最快心率{fastest_af_hr}次/分")

            # 解析最长持续时间 (格式: "秒数@时间")
            if af_afl_longest_duration_str and '@' in af_afl_longest_duration_str:
                try:
                    duration_part = af_afl_longest_duration_str.split('@', 1)[0].strip()
                    longest_dur = int(duration_part)
                    if longest_dur > 0:
                        details.append(f"最长持续{longest_dur}秒")
                except ValueError:
                    pass

            if details:
                af_desc += "，" + "，".join(details)

            conclusion_parts.append(af_desc + "。")

        # 组合结论
        if conclusion_parts:
            has_normal_ecg = len(conclusion_parts) == 1 and "正常窦性心律" in conclusion_parts[0]

            if has_normal_ecg:
                report_model.conclusion = "报告时段内心电图未见明显异常。"
            else:
                report_model.conclusion = "\n".join(conclusion_parts)
        else:
            report_model.conclusion = "报告时段内心电图未见明显异常。"

    except Exception as e:
        report_model.conclusion = "窦性心律。"

    return report_model


def report_heart_rate(report_model, normal_data, hourly_statistics_list):
    """
    心率
    :param report_model: 报告模型
    :param datas: 原始数据
    :param normal_data: 正常数据
    :param hourly_statistics_list: 小时统计数据
    :return: 报告模型
    """
    report_model.hr_mean = int(normal_data['pqrstc_hr'].mean())

    # 获取最大和最小nn值
    max_heart_rate = 0
    min_heart_rate = float('inf')
    max_check_date = None
    min_check_date = None

    for _, row in normal_data.iterrows():
        # 获取 check_date
        check_date = row['check_date'].strftime('%Y-%m-%d %H:%M')

        if row['nnintervals']:
            # 分割字符串获取 NNI 列表
            nni_list = row['nnintervals'].split(',')
            # 转换为浮点数
            nni_floats = [float(nni) for nni in nni_list if nni.strip() != '']
            # 计算对应的心率（转换为秒）
            heart_rates = [60 / nni for nni in nni_floats]

            # 更新最大和最小心率及其对应的 check_date
            current_max = max(heart_rates)
            current_min = min(heart_rates)

            if current_max > max_heart_rate:
                max_heart_rate = current_max
                max_check_date = check_date

            if current_min < min_heart_rate:
                min_heart_rate = current_min
                min_check_date = check_date

    report_model.nn_max = f"{int(max_heart_rate)} @ {max_check_date}"
    report_model.nn_min = f"{int(min_heart_rate)} @ {min_check_date}"

    # 获取小时最大最小值最长RR
    hourly_hr_max = 0
    hourly_hr_max_date = ''
    hourly_hr_min = float('inf')
    hourly_hr_min_date = ''

    for hourly_statistics in hourly_statistics_list:
        for hourly_statistics_detail in hourly_statistics.hourly_details:
            if hourly_statistics_detail.hr_mean == 0:
                continue

            if hourly_statistics_detail.hr_mean > hourly_hr_max:
                hourly_hr_max = hourly_statistics_detail.hr_max
                hourly_hr_max_date = f'{hourly_statistics.date_str} {hourly_statistics_detail.hour}'

            if hourly_statistics_detail.hr_mean < hourly_hr_min:
                hourly_hr_min = hourly_statistics_detail.hr_min
                hourly_hr_min_date = f'{hourly_statistics.date_str} {hourly_statistics_detail.hour}'

    report_model.hourly_mean_max = f"{hourly_hr_max} @ {hourly_hr_max_date}"
    report_model.hourly_mean_min = f"{hourly_hr_min} @ {hourly_hr_min_date}"

    rr_max_index = normal_data['pqrstc_rr_max'].idxmax()
    rr_max_row = normal_data.loc[rr_max_index]
    report_model.longest_rr = f"{round(rr_max_row['pqrstc_rr_max'], 1)}s @ {rr_max_row['check_date'].strftime('%Y-%m-%d %H:%M')}"

    return report_model


def report_hrv(report_model, normal_data):
    """
    HRV
    :param report_model: 报告模型
    :param normal_data: 正常数据
    :return: 报告模型
    """

    rr_intervals_data = normal_data.loc[:, 'rrintervals'].apply(lambda x: x if x != '' else pd.NA).dropna()
    rr_intervals = np.array(ast.literal_eval(', '.join(rr_intervals_data)))

    nn_intervals_data = normal_data.loc[:, 'nnintervals'].apply(lambda x: x if x != '' else pd.NA).dropna()
    nn_intervals = np.array(ast.literal_eval(', '.join(nn_intervals_data)))

    report_model.nn_rr_ratio = round(len(nn_intervals) / len(rr_intervals) * 100, 1)
    report_model.sdnn = round(time_domain.sdnn(nn_intervals)['sdnn'], 1)
    report_model.sdann = round(time_domain.sdann(nn_intervals)['sdann'], 1)
    report_model.sdnnidx = round(time_domain.sdnn_index(nn_intervals)['sdnn_index'], 1)
    report_model.rmssd = round(time_domain.rmssd(nn_intervals)['rmssd'], 1)
    report_model.pnn20 = round(time_domain.nn20(nn_intervals)['pnn20'], 1)
    report_model.pnn50 = round(time_domain.nn50(nn_intervals)['pnn50'], 1)

    # 计算功率谱密度(PSD)使用Welch方法
    results = frequency_domain.welch_psd(nni=nn_intervals, nfft=4096, show=False, show_param=False, legend=False)

    # 获取 VLF、LF 和 HF 的绝对功率
    vlf = results['fft_abs'][0]  # VLF 功率
    lf = results['fft_abs'][1]  # LF 功率
    hf = results['fft_abs'][2]  # HF 功率

    report_model.total = round(results['fft_total'], 1)
    report_model.lf = round(lf, 1)
    report_model.hf = round(hf, 1)
    report_model.vlf = round(vlf, 1)
    report_model.lf_hf = round(results['fft_ratio'], 1)

    return report_model


def get_hourly_statistics(start_date, end_date, normal_data, detail_datas):
    """
    获取小时统计数据
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param normal_data: 正常数据
    :param detail_datas: 详细数据
    :return: 小时统计数据
    """
    # 将 check_time 转换为 datetime 类型
    normal_data.loc[:, 'check_date'] = pd.to_datetime(normal_data['check_date'], format='%Y%m%d')

    # 提取日期和小时
    normal_data.loc[:, 'date'] = normal_data['check_date'].dt.date
    normal_data.loc[:, 'hour'] = normal_data['check_date'].dt.hour

    # 获取唯一的日期列表
    data_dates = get_data_dates(start_date, end_date)

    hourly_statistics_list = []

    # 定义一天中的所有小时
    all_hours = list(range(24))

    for data_date in data_dates:
        # 为当前日期创建一个包含所有24小时的 DataFrame
        d = datetime.strptime(data_date, '%Y%m%d').date()

        # 为当前日期创建一个包含所有24小时的 DataFrame
        date_str = d.strftime('%Y-%m-%d')

        hourly_statistics = HourlyStatisticsModel()

        hourly_statistics.date_str = date_str

        # 筛选当前日期的数据
        date_data = normal_data[normal_data['date'] == d]

        hourky_infos = []

        # 按小时筛选数据
        for hour in all_hours:
            # 筛选当前小时的数据
            hour_data = date_data[date_data['hour'] == hour]

            if len(hour_data) == 0:
                hourky_infos.append(get_hourly_empty(hour))
            else:
                hourky_infos.append(get_hourly_data(hour_data, hour, detail_datas))

        # 添加固定的统计行
        hourky_infos.append(get_summary_data(hourky_infos, date_str))

        hourly_statistics.hourly_details = hourky_infos

        hourly_statistics_list.append(hourly_statistics)

    return hourly_statistics_list


def get_hourly_empty(hour):
    """
    获取空的小时统计数据
    :param hour: 小时
    :return: 空的小时统计数据
    """

    hourly_statistics_detail = HourlyStatisticsDetailModel()

    hourly_statistics_detail.hour = f"{hour:02d} : 00"
    hourly_statistics_detail.hr_total_count = 0  # 心搏总数
    hourly_statistics_detail.sdnn = 0  # sdnn
    hourly_statistics_detail.hr_mean = 0  # 平均心率
    hourly_statistics_detail.hr_max = 0  # 最快心率
    hourly_statistics_detail.hr_min = 0  # 最慢心率
    hourly_statistics_detail.pvc_single_count = 0  # 室性早搏单发个数
    hourly_statistics_detail.pvc_pair_count = 0  # 室性早搏成对阵数
    hourly_statistics_detail.pvc_run_count = 0  # 室性早搏连发阵数
    hourly_statistics_detail.pvc_total_count = 0  # 室性早搏总个数
    hourly_statistics_detail.svpc_single_count = 0  # 室上性早搏单发个数
    hourly_statistics_detail.svpc_pair_count = 0  # 室上性早搏成对阵数
    hourly_statistics_detail.svpc_run_count = 0  # 室上性早搏连发阵数
    hourly_statistics_detail.svpc_total_count = 0  # 室上性早搏总个数
    hourly_statistics_detail.sa_count = 0  # 停搏个数

    return hourly_statistics_detail


def get_hourly_data(hour_data, hour, detail_datas):
    """
    获取小时统计数据
    :param hour_data: 小时数据
    :param hour: 小时
    :param detail_datas: 详细数据
    :return: 小时统计数据
    """
    data_ecg_ids = hour_data['id']

    hourly_statistics_detail = HourlyStatisticsDetailModel()

    hourly_statistics_detail.hour = f"{hour:02d} : 00"
    hourly_statistics_detail.hr_total_count = int(hour_data['pqrstc_hr'].sum())  # 心搏总数

    # 计算sdnn
    nn_intervals_data = hour_data.loc[:, 'nnintervals'].apply(lambda x: x if x != '' else pd.NA).dropna()

    if len(nn_intervals_data) > 0:
        nn_intervals = np.array(ast.literal_eval(', '.join(nn_intervals_data)))
        hourly_statistics_detail.sdnn = round(time_domain.sdnn(nn_intervals)['sdnn'], 1)  # sdnn

    hourly_statistics_detail.hr_mean = int(hour_data['pqrstc_hr'].mean())  # 平均心率
    hourly_statistics_detail.hr_max = int(hour_data['pqrstc_hr'].max())  # 最快心率
    hourly_statistics_detail.hr_min = int(hour_data['pqrstc_hr'].min())  # 最慢心率

    detail_data = detail_datas[detail_datas['data_ecg_id'].isin(data_ecg_ids)]
    if len(detail_data) > 0:
        pvc_data_detail = detail_data[detail_data['disease_code'] == 'PVC']

        if len(pvc_data_detail) == 0:
            hourly_statistics_detail.pvc_single_count = 0  # 室性早搏单发个数
            hourly_statistics_detail.pvc_pair_count = 0  # 室性早搏成对阵数
            hourly_statistics_detail.pvc_run_count = 0  # 室性早搏连发阵数
            hourly_statistics_detail.pvc_total_count = 0  # 室性早搏总个数
        else:
            hourly_statistics_detail.pvc_single_count = int(pvc_data_detail['single_count'].sum())  # 室性早搏单发个数
            hourly_statistics_detail.pvc_pair_count = int(pvc_data_detail['pair_count'].sum())  # 室性早搏成对阵数
            hourly_statistics_detail.pvc_run_count = int(pvc_data_detail['run_count'].sum())  # 室性早搏连发阵数
            hourly_statistics_detail.pvc_total_count = (hourly_statistics_detail.pvc_single_count +
                                                        hourly_statistics_detail.pvc_pair_count +
                                                        hourly_statistics_detail.pvc_run_count)  # 室性早搏总个数

        # 计算室上性早搏数据
        svpc_data_detail = detail_data[detail_data['disease_code'] == 'SVPC']

        if len(svpc_data_detail) == 0:
            hourly_statistics_detail.svpc_single_count = 0  # 室上性早搏单发个数
            hourly_statistics_detail.svpc_pair_count = 0  # 室上性早搏成对阵数
            hourly_statistics_detail.svpc_run_count = 0  # 室上性早搏连发阵数
            hourly_statistics_detail.svpc_total_count = 0  # 室上性早搏总个数
        else:
            hourly_statistics_detail.svpc_single_count = int(svpc_data_detail['single_count'].sum())  # 室上性早搏单发个数
            hourly_statistics_detail.svpc_pair_count = int(svpc_data_detail['pair_count'].sum())  # 室上性早搏成对阵数
            hourly_statistics_detail.svpc_run_count = int(svpc_data_detail['run_count'].sum())  # 室上性早搏连发阵数
            hourly_statistics_detail.svpc_total_count = (hourly_statistics_detail.svpc_single_count +
                                                         hourly_statistics_detail.svpc_pair_count +
                                                         hourly_statistics_detail.svpc_run_count)

        hourly_statistics_detail.sa_count = 0  # 停搏个数

    return hourly_statistics_detail


def get_data_dates(start_date, end_date):
    """
    获取数据日期集合
    :param start_date: 开始日期
    :param end_date: 结束日期
    :return: 日期集合
    """
    # 将字符串日期转换为datetime对象
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")

    # 用于存储表名的列表
    data_dates = []

    # 遍历日期范围，生成表名
    current_date = start
    while current_date <= end:
        data_dates.append(current_date.strftime('%Y%m%d'))

        # 日期加一天
        current_date += timedelta(days=1)

    return data_dates


def get_summary_data(hourky_infos, date_str):
    """
    返回汇总数据
    :param hourky_infos: 小时统计数据
    :param date_str: 日期字符串
    :return: 汇总数据
    """

    total_number = len([model for model in hourky_infos if model.hr_total_count > 0])  # 总数量

    hourly_statistics_detail = HourlyStatisticsDetailModel()
    hourly_statistics_detail.hour = date_str

    if total_number > 0:
        hourly_statistics_detail.hr_total_count = sum(hourky_inf.hr_total_count for hourky_inf in hourky_infos)  # 心搏总数
        hourly_statistics_detail.sdnn = ''  # sdnn
        hourly_statistics_detail.hr_mean = int(
            sum(hourky_inf.hr_mean for hourky_inf in hourky_infos) / total_number)  # 平均心率
        hourly_statistics_detail.hr_max = int(
            sum(hourky_inf.hr_max for hourky_inf in hourky_infos) / total_number)  # 最快心率
        hourly_statistics_detail.hr_min = int(
            sum(hourky_inf.hr_min for hourky_inf in hourky_infos) / total_number)  # 最慢心率
        hourly_statistics_detail.pvc_single_count = sum(
            hourky_inf.pvc_single_count for hourky_inf in hourky_infos)  # 室性早搏单发个数
        hourly_statistics_detail.pvc_pair_count = sum(
            hourky_inf.pvc_pair_count for hourky_inf in hourky_infos)  # 室性早搏成对阵数
        hourly_statistics_detail.pvc_run_count = sum(
            hourky_inf.pvc_run_count for hourky_inf in hourky_infos)  # 室性早搏连发阵数
        hourly_statistics_detail.pvc_total_count = sum(
            hourky_inf.pvc_total_count for hourky_inf in hourky_infos)  # 室性早搏总个数
        hourly_statistics_detail.svpc_single_count = sum(
            hourky_inf.svpc_single_count for hourky_inf in hourky_infos)  # 室上性早搏单发个数
        hourly_statistics_detail.svpc_pair_count = sum(
            hourky_inf.svpc_pair_count for hourky_inf in hourky_infos)  # 室上性早搏成对阵数
        hourly_statistics_detail.svpc_run_count = sum(
            hourky_inf.svpc_run_count for hourky_inf in hourky_infos)  # 室上性早搏连发阵数
        hourly_statistics_detail.svpc_total_count = sum(
            hourky_inf.svpc_total_count for hourky_inf in hourky_infos)  # 室上性早搏总个数
        hourly_statistics_detail.sa_count = sum(hourky_inf.sa_count for hourky_inf in hourky_infos)  # 停搏个数

    return hourly_statistics_detail


def parse_rate_time(rate_time_str):
    """
    解析心率时间字符串
    :param rate_time_str: 心率时间字符串，格式为"心率次/分@时间"
    :return: 心率（次/分）和时间（格式化）
    """
    if rate_time_str:
        parts = rate_time_str.split('@', 1)
        if len(parts) == 2:
            rate = float(parts[0].strip())
            time = parts[1].strip()
            return rate, time
    return None, None
