import traceback

from apps.utils.logger_helper import Logger


def param_extraction(request, key, request_type=None):
    """
    提取参数
    :param request: 请求对象
    :param key: 关键字
    :param request_type: 请求类型
    :return: 请求对象中的值
    """
    try:
        if key is None:
            pass
        else:
            if request_type == 'GET':
                value = request.GET.get(key)
                return value
            elif request_type == 'POST':
                # 表单取值
                value = request.POST.get(key)
                return value
            else:
                return None

    except Exception:
        Logger().error(f'提取参数异常：{traceback.format_exc()}')
        return None
