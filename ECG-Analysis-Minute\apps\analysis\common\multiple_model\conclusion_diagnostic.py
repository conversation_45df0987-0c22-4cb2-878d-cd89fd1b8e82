import os

import tensorflow as tf
import numpy as np
from apps.analysis.common.ecg_signal_processing import resample
from global_settings import conclusion_diagnostic_dict


class ECGDiagnostic:
    """
    多结论诊断类
    用于诊断心电图信号中的多结论问题。
    该类加载预训练的模型，对输入的心电图信号进行预处理和诊断。
    输入的心电图信号应为一维数组。
    输出的诊断结果为包含多个结论的列表，每个结论对应一个标签。
    标签的定义可以在全局设置文件中找到。
    """
    def __init__(self, sampling_rate, initial_threshold=0.9, min_threshold=0.5, step=0.1):
        """
        初始化 ECG 诊断类
        :param sampling_rate: 采样率
        :param initial_threshold: 动态阈值的起始值
        :param min_threshold: 动态阈值的最小值
        :param step: 动态阈值的步长
        """
        current_file_path = os.path.abspath(__file__)
        current_dir = os.path.dirname(current_file_path)
        model_path = os.path.join(current_dir, 'model', 'tf_model_multi_label.keras')
        self.model = tf.keras.models.load_model(model_path)
        self.sampling_rate = sampling_rate
        self.initial_threshold = initial_threshold
        self.min_threshold = min_threshold
        self.step = step
        self.conclusion_diagnostic_dict = {i: value for i, value in enumerate(conclusion_diagnostic_dict.values())}

    def preprocess_ecg_data(self, ecg_data):
        """
        数据预处理
        :param ecg_data: ECG信号数据
        :return: 预处理后的数据
        """
        ecg_data = np.expand_dims(ecg_data, axis=-1)
        return ecg_data

    def adjust_threshold(self, predict):
        """
        动态阈值调整
        :param predict: 模型预测结果
        :return: 诊断结果
        """
        values = list(self.conclusion_diagnostic_dict.values())
        threshold = self.initial_threshold
        while threshold >= self.min_threshold:
            predicted_labels = [values[i] for i, score in enumerate(predict) if score >= threshold]
            if predicted_labels:
                return predicted_labels
            threshold -= self.step
        return []

    def diagnose(self, ecg_data_list):
        """
        诊断方法
        :param ecg_data_list: ECG信号数据
        :return: 诊断结果列表
        """
        # 重采样并预处理数据
        ecg_data_list = [self.preprocess_ecg_data(resample(ecg_data, self.sampling_rate)) for ecg_data in ecg_data_list]
        ecg_data_array = np.array(ecg_data_list)

        # 模型预测
        predict = self.model.predict(ecg_data_array)

        predicted_labels_list = [self.adjust_threshold(predict_item) for predict_item in predict]

        return predicted_labels_list


def diagnostic(ecg_data, sampling_rate):
    """
    多结论诊断
    :param ecg_data: ECG信号数据
    :param sampling_rate: 采样率
    :return: 诊断结果集合
    """
    ecg_data = resample(ecg_data, sampling_rate)  # 重采样

    # 获取当前文件的绝对路径
    current_file_path = os.path.abspath(__file__)
    # 获取当前文件所在的目录
    current_dir = os.path.dirname(current_file_path)
    # 加载模型
    model_path = os.path.join(current_dir, 'model', 'tf_model_multi_label.keras')
    model = tf.keras.models.load_model(model_path)
    # 转换为模型需求格式
    predict = model.predict(np.expand_dims([ecg_data], axis=2))

    # 动态阈值参数
    initial_threshold = 0.9  # 起始阈值
    min_threshold = 0.5  # 最低阈值
    step = 0.1  # 每次下降的步长

    # 根据字典顺序获得结果对应的标签
    values = list(conclusion_diagnostic_dict.values())

    # 初始化输出
    predicted_labels = []

    # 动态调整阈值以找到符合条件的标签
    threshold = initial_threshold
    while threshold >= min_threshold:
        predicted_labels = [values[i] for i, score in enumerate(predict[0]) if score >= threshold]
        # 如果找到了符合条件的标签，则停止降低阈值
        if predicted_labels:
            break
        # 否则继续降低阈值
        threshold -= step

    return predicted_labels

