# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: Primož Verdnik <<EMAIL>>\n"
"Language-Team: Slovenian (http://www.transifex.com/django/django/language/"
"sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3);\n"

msgid "Content Types"
msgstr "vrste vsebine"

msgid "python model class name"
msgstr "ime razreda modela python"

msgid "content type"
msgstr "vrsta vsebine"

msgid "content types"
msgstr "vrste vsebine"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Predmet vrste vsebine %(ct_id)s nima določenega povezanega modela"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Predmet %(obj_id)s vrste %(ct_id)s ne obstaja"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "Predmeti vrste %(ct_name)s nimajo določila get_absolute_url()"
