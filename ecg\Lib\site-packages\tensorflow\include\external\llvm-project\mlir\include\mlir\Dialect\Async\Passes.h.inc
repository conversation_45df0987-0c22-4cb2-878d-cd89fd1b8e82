/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// AsyncParallelFor
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AsyncParallelForBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncParallelForBase;

  AsyncParallelForBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncParallelForBase(const AsyncParallelForBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-parallel-for");
  }
  ::llvm::StringRef getArgument() const override { return "async-parallel-for"; }

  ::llvm::StringRef getDescription() const override { return "Convert scf.parallel operations to multiple async regions executed concurrently for non-overlapping iteration ranges"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncParallelFor");
  }
  ::llvm::StringRef getName() const override { return "AsyncParallelFor"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
  ::mlir::Pass::Option<int32_t> numConcurrentAsyncExecute{*this, "num-concurrent-async-execute", ::llvm::cl::desc("The number of async.execute operations that will be used for concurrent loop execution."), ::llvm::cl::init(4)};
};

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCounting
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AsyncRuntimeRefCountingBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimeRefCountingBase;

  AsyncRuntimeRefCountingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimeRefCountingBase(const AsyncRuntimeRefCountingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-ref-counting");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-ref-counting"; }

  ::llvm::StringRef getDescription() const override { return "Automatic reference counting for Async runtime operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimeRefCounting");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimeRefCounting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCountingOpt
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AsyncRuntimeRefCountingOptBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimeRefCountingOptBase;

  AsyncRuntimeRefCountingOptBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimeRefCountingOptBase(const AsyncRuntimeRefCountingOptBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-ref-counting-opt");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-ref-counting-opt"; }

  ::llvm::StringRef getDescription() const override { return "Optimize automatic reference counting operations for theAsync runtime by removing redundant operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimeRefCountingOpt");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimeRefCountingOpt"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// AsyncToAsyncRuntime
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AsyncToAsyncRuntimeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AsyncToAsyncRuntimeBase;

  AsyncToAsyncRuntimeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncToAsyncRuntimeBase(const AsyncToAsyncRuntimeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-to-async-runtime");
  }
  ::llvm::StringRef getArgument() const override { return "async-to-async-runtime"; }

  ::llvm::StringRef getDescription() const override { return "Lower high level async operations (e.g. async.execute) to theexplicit async.runtime and async.coro operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncToAsyncRuntime");
  }
  ::llvm::StringRef getName() const override { return "AsyncToAsyncRuntime"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AsyncParallelFor Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncParallelForPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncParallelForPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCounting Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncRuntimeRefCountingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimeRefCountingPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCountingOpt Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncRuntimeRefCountingOptPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimeRefCountingOptPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncToAsyncRuntime Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncToAsyncRuntimePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncToAsyncRuntimePass();
  });
}

//===----------------------------------------------------------------------===//
// Async Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncPasses() {
  registerAsyncParallelForPass();
  registerAsyncRuntimeRefCountingPass();
  registerAsyncRuntimeRefCountingOptPass();
  registerAsyncToAsyncRuntimePass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
