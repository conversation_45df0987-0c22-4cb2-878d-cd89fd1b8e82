server {
    root /usr/share/nginx/html;

    # Disable server signature to make it slightly harder for
    # attackers to find known vulnerabilities. See
    # https://datatracker.ietf.org/doc/html/rfc9110#name-server
    server_tokens off;
    
    gzip on;
    gzip_comp_level 6;
    gzip_http_version 1.1;
    gzip_types
        text/html
        application/javascript
        application/x-javascript
        text/javascript
        application/wasm
        image/x-icon;

    location / {
        # Any route that doesn't exist on the server (e.g. /devices)
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Cross-Origin-Opener-Policy "same-origin";
        add_header Cross-Origin-Embedder-Policy "credentialless";
        add_header Expires 0;
        add_header X-Frame-Options "deny";
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header X-Content-Type-Options "nosniff" always; 
    }

    location /assets {
        expires 1y;
        add_header Cache-Control "public";
        add_header Cross-Origin-Embedder-Policy "require-corp";

        access_log off;
    }
}
