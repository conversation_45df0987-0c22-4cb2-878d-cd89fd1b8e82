// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/bfc_memory_map.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
namespace tensorflow {
class BinSummary;
class BinSummaryDefaultTypeInternal;
extern BinSummaryDefaultTypeInternal _BinSummary_default_instance_;
class MemAllocatorStats;
class MemAllocatorStatsDefaultTypeInternal;
extern MemAllocatorStatsDefaultTypeInternal _MemAllocatorStats_default_instance_;
class MemChunk;
class MemChunkDefaultTypeInternal;
extern MemChunkDefaultTypeInternal _MemChunk_default_instance_;
class MemoryDump;
class MemoryDumpDefaultTypeInternal;
extern MemoryDumpDefaultTypeInternal _MemoryDump_default_instance_;
class SnapShot;
class SnapShotDefaultTypeInternal;
extern SnapShotDefaultTypeInternal _SnapShot_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::BinSummary* Arena::CreateMaybeMessage<::tensorflow::BinSummary>(Arena*);
template<> ::tensorflow::MemAllocatorStats* Arena::CreateMaybeMessage<::tensorflow::MemAllocatorStats>(Arena*);
template<> ::tensorflow::MemChunk* Arena::CreateMaybeMessage<::tensorflow::MemChunk>(Arena*);
template<> ::tensorflow::MemoryDump* Arena::CreateMaybeMessage<::tensorflow::MemoryDump>(Arena*);
template<> ::tensorflow::SnapShot* Arena::CreateMaybeMessage<::tensorflow::SnapShot>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class MemAllocatorStats :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemAllocatorStats) */ {
 public:
  MemAllocatorStats();
  virtual ~MemAllocatorStats();

  MemAllocatorStats(const MemAllocatorStats& from);
  MemAllocatorStats(MemAllocatorStats&& from) noexcept
    : MemAllocatorStats() {
    *this = ::std::move(from);
  }

  inline MemAllocatorStats& operator=(const MemAllocatorStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemAllocatorStats& operator=(MemAllocatorStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemAllocatorStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemAllocatorStats* internal_default_instance() {
    return reinterpret_cast<const MemAllocatorStats*>(
               &_MemAllocatorStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MemAllocatorStats& a, MemAllocatorStats& b) {
    a.Swap(&b);
  }
  inline void Swap(MemAllocatorStats* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemAllocatorStats* New() const final {
    return CreateMaybeMessage<MemAllocatorStats>(nullptr);
  }

  MemAllocatorStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemAllocatorStats>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemAllocatorStats& from);
  void MergeFrom(const MemAllocatorStats& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemAllocatorStats* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemAllocatorStats";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNumAllocsFieldNumber = 1,
    kBytesInUseFieldNumber = 2,
    kPeakBytesInUseFieldNumber = 3,
    kLargestAllocSizeFieldNumber = 4,
    kFragmentationMetricFieldNumber = 5,
  };
  // int64 num_allocs = 1;
  void clear_num_allocs();
  ::PROTOBUF_NAMESPACE_ID::int64 num_allocs() const;
  void set_num_allocs(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 bytes_in_use = 2;
  void clear_bytes_in_use();
  ::PROTOBUF_NAMESPACE_ID::int64 bytes_in_use() const;
  void set_bytes_in_use(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 peak_bytes_in_use = 3;
  void clear_peak_bytes_in_use();
  ::PROTOBUF_NAMESPACE_ID::int64 peak_bytes_in_use() const;
  void set_peak_bytes_in_use(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 largest_alloc_size = 4;
  void clear_largest_alloc_size();
  ::PROTOBUF_NAMESPACE_ID::int64 largest_alloc_size() const;
  void set_largest_alloc_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // float fragmentation_metric = 5;
  void clear_fragmentation_metric();
  float fragmentation_metric() const;
  void set_fragmentation_metric(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemAllocatorStats)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_allocs_;
  ::PROTOBUF_NAMESPACE_ID::int64 bytes_in_use_;
  ::PROTOBUF_NAMESPACE_ID::int64 peak_bytes_in_use_;
  ::PROTOBUF_NAMESPACE_ID::int64 largest_alloc_size_;
  float fragmentation_metric_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
};
// -------------------------------------------------------------------

class MemChunk :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemChunk) */ {
 public:
  MemChunk();
  virtual ~MemChunk();

  MemChunk(const MemChunk& from);
  MemChunk(MemChunk&& from) noexcept
    : MemChunk() {
    *this = ::std::move(from);
  }

  inline MemChunk& operator=(const MemChunk& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemChunk& operator=(MemChunk&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemChunk& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemChunk* internal_default_instance() {
    return reinterpret_cast<const MemChunk*>(
               &_MemChunk_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MemChunk& a, MemChunk& b) {
    a.Swap(&b);
  }
  inline void Swap(MemChunk* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemChunk* New() const final {
    return CreateMaybeMessage<MemChunk>(nullptr);
  }

  MemChunk* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemChunk>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemChunk& from);
  void MergeFrom(const MemChunk& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemChunk* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemChunk";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpNameFieldNumber = 5,
    kAddressFieldNumber = 1,
    kSizeFieldNumber = 2,
    kRequestedSizeFieldNumber = 3,
    kFreedAtCountFieldNumber = 6,
    kBinFieldNumber = 4,
    kInUseFieldNumber = 8,
    kActionCountFieldNumber = 7,
    kStepIdFieldNumber = 9,
  };
  // string op_name = 5;
  void clear_op_name();
  const std::string& op_name() const;
  void set_op_name(const std::string& value);
  void set_op_name(std::string&& value);
  void set_op_name(const char* value);
  void set_op_name(const char* value, size_t size);
  std::string* mutable_op_name();
  std::string* release_op_name();
  void set_allocated_op_name(std::string* op_name);

  // uint64 address = 1;
  void clear_address();
  ::PROTOBUF_NAMESPACE_ID::uint64 address() const;
  void set_address(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int64 size = 2;
  void clear_size();
  ::PROTOBUF_NAMESPACE_ID::int64 size() const;
  void set_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 requested_size = 3;
  void clear_requested_size();
  ::PROTOBUF_NAMESPACE_ID::int64 requested_size() const;
  void set_requested_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // uint64 freed_at_count = 6;
  void clear_freed_at_count();
  ::PROTOBUF_NAMESPACE_ID::uint64 freed_at_count() const;
  void set_freed_at_count(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int32 bin = 4;
  void clear_bin();
  ::PROTOBUF_NAMESPACE_ID::int32 bin() const;
  void set_bin(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool in_use = 8;
  void clear_in_use();
  bool in_use() const;
  void set_in_use(bool value);

  // uint64 action_count = 7;
  void clear_action_count();
  ::PROTOBUF_NAMESPACE_ID::uint64 action_count() const;
  void set_action_count(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // uint64 step_id = 9;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemChunk)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_name_;
  ::PROTOBUF_NAMESPACE_ID::uint64 address_;
  ::PROTOBUF_NAMESPACE_ID::int64 size_;
  ::PROTOBUF_NAMESPACE_ID::int64 requested_size_;
  ::PROTOBUF_NAMESPACE_ID::uint64 freed_at_count_;
  ::PROTOBUF_NAMESPACE_ID::int32 bin_;
  bool in_use_;
  ::PROTOBUF_NAMESPACE_ID::uint64 action_count_;
  ::PROTOBUF_NAMESPACE_ID::uint64 step_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
};
// -------------------------------------------------------------------

class BinSummary :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BinSummary) */ {
 public:
  BinSummary();
  virtual ~BinSummary();

  BinSummary(const BinSummary& from);
  BinSummary(BinSummary&& from) noexcept
    : BinSummary() {
    *this = ::std::move(from);
  }

  inline BinSummary& operator=(const BinSummary& from) {
    CopyFrom(from);
    return *this;
  }
  inline BinSummary& operator=(BinSummary&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BinSummary& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BinSummary* internal_default_instance() {
    return reinterpret_cast<const BinSummary*>(
               &_BinSummary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(BinSummary& a, BinSummary& b) {
    a.Swap(&b);
  }
  inline void Swap(BinSummary* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BinSummary* New() const final {
    return CreateMaybeMessage<BinSummary>(nullptr);
  }

  BinSummary* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BinSummary>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BinSummary& from);
  void MergeFrom(const BinSummary& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BinSummary* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BinSummary";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTotalBytesInUseFieldNumber = 2,
    kTotalBytesInBinFieldNumber = 3,
    kTotalChunksInUseFieldNumber = 4,
    kTotalChunksInBinFieldNumber = 5,
    kBinFieldNumber = 1,
  };
  // int64 total_bytes_in_use = 2;
  void clear_total_bytes_in_use();
  ::PROTOBUF_NAMESPACE_ID::int64 total_bytes_in_use() const;
  void set_total_bytes_in_use(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_bytes_in_bin = 3;
  void clear_total_bytes_in_bin();
  ::PROTOBUF_NAMESPACE_ID::int64 total_bytes_in_bin() const;
  void set_total_bytes_in_bin(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_chunks_in_use = 4;
  void clear_total_chunks_in_use();
  ::PROTOBUF_NAMESPACE_ID::int64 total_chunks_in_use() const;
  void set_total_chunks_in_use(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_chunks_in_bin = 5;
  void clear_total_chunks_in_bin();
  ::PROTOBUF_NAMESPACE_ID::int64 total_chunks_in_bin() const;
  void set_total_chunks_in_bin(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int32 bin = 1;
  void clear_bin();
  ::PROTOBUF_NAMESPACE_ID::int32 bin() const;
  void set_bin(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.BinSummary)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_bytes_in_use_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_bytes_in_bin_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_chunks_in_use_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_chunks_in_bin_;
  ::PROTOBUF_NAMESPACE_ID::int32 bin_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
};
// -------------------------------------------------------------------

class SnapShot :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SnapShot) */ {
 public:
  SnapShot();
  virtual ~SnapShot();

  SnapShot(const SnapShot& from);
  SnapShot(SnapShot&& from) noexcept
    : SnapShot() {
    *this = ::std::move(from);
  }

  inline SnapShot& operator=(const SnapShot& from) {
    CopyFrom(from);
    return *this;
  }
  inline SnapShot& operator=(SnapShot&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SnapShot& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SnapShot* internal_default_instance() {
    return reinterpret_cast<const SnapShot*>(
               &_SnapShot_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SnapShot& a, SnapShot& b) {
    a.Swap(&b);
  }
  inline void Swap(SnapShot* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SnapShot* New() const final {
    return CreateMaybeMessage<SnapShot>(nullptr);
  }

  SnapShot* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SnapShot>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SnapShot& from);
  void MergeFrom(const SnapShot& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SnapShot* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SnapShot";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kActionCountFieldNumber = 1,
    kSizeFieldNumber = 2,
  };
  // uint64 action_count = 1;
  void clear_action_count();
  ::PROTOBUF_NAMESPACE_ID::uint64 action_count() const;
  void set_action_count(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int64 size = 2;
  void clear_size();
  ::PROTOBUF_NAMESPACE_ID::int64 size() const;
  void set_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.SnapShot)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::uint64 action_count_;
  ::PROTOBUF_NAMESPACE_ID::int64 size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
};
// -------------------------------------------------------------------

class MemoryDump :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryDump) */ {
 public:
  MemoryDump();
  virtual ~MemoryDump();

  MemoryDump(const MemoryDump& from);
  MemoryDump(MemoryDump&& from) noexcept
    : MemoryDump() {
    *this = ::std::move(from);
  }

  inline MemoryDump& operator=(const MemoryDump& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryDump& operator=(MemoryDump&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemoryDump& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryDump* internal_default_instance() {
    return reinterpret_cast<const MemoryDump*>(
               &_MemoryDump_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(MemoryDump& a, MemoryDump& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryDump* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemoryDump* New() const final {
    return CreateMaybeMessage<MemoryDump>(nullptr);
  }

  MemoryDump* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemoryDump>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemoryDump& from);
  void MergeFrom(const MemoryDump& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryDump* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryDump";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBinSummaryFieldNumber = 2,
    kChunkFieldNumber = 3,
    kSnapShotFieldNumber = 4,
    kAllocatorNameFieldNumber = 1,
    kStatsFieldNumber = 5,
  };
  // repeated .tensorflow.BinSummary bin_summary = 2;
  int bin_summary_size() const;
  void clear_bin_summary();
  ::tensorflow::BinSummary* mutable_bin_summary(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BinSummary >*
      mutable_bin_summary();
  const ::tensorflow::BinSummary& bin_summary(int index) const;
  ::tensorflow::BinSummary* add_bin_summary();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BinSummary >&
      bin_summary() const;

  // repeated .tensorflow.MemChunk chunk = 3;
  int chunk_size() const;
  void clear_chunk();
  ::tensorflow::MemChunk* mutable_chunk(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemChunk >*
      mutable_chunk();
  const ::tensorflow::MemChunk& chunk(int index) const;
  ::tensorflow::MemChunk* add_chunk();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemChunk >&
      chunk() const;

  // repeated .tensorflow.SnapShot snap_shot = 4;
  int snap_shot_size() const;
  void clear_snap_shot();
  ::tensorflow::SnapShot* mutable_snap_shot(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SnapShot >*
      mutable_snap_shot();
  const ::tensorflow::SnapShot& snap_shot(int index) const;
  ::tensorflow::SnapShot* add_snap_shot();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SnapShot >&
      snap_shot() const;

  // string allocator_name = 1;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  void set_allocator_name(const std::string& value);
  void set_allocator_name(std::string&& value);
  void set_allocator_name(const char* value);
  void set_allocator_name(const char* value, size_t size);
  std::string* mutable_allocator_name();
  std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);

  // .tensorflow.MemAllocatorStats stats = 5;
  bool has_stats() const;
  void clear_stats();
  const ::tensorflow::MemAllocatorStats& stats() const;
  ::tensorflow::MemAllocatorStats* release_stats();
  ::tensorflow::MemAllocatorStats* mutable_stats();
  void set_allocated_stats(::tensorflow::MemAllocatorStats* stats);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryDump)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BinSummary > bin_summary_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemChunk > chunk_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SnapShot > snap_shot_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
  ::tensorflow::MemAllocatorStats* stats_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MemAllocatorStats

// int64 num_allocs = 1;
inline void MemAllocatorStats::clear_num_allocs() {
  num_allocs_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemAllocatorStats::num_allocs() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemAllocatorStats.num_allocs)
  return num_allocs_;
}
inline void MemAllocatorStats::set_num_allocs(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_allocs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemAllocatorStats.num_allocs)
}

// int64 bytes_in_use = 2;
inline void MemAllocatorStats::clear_bytes_in_use() {
  bytes_in_use_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemAllocatorStats::bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemAllocatorStats.bytes_in_use)
  return bytes_in_use_;
}
inline void MemAllocatorStats::set_bytes_in_use(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  bytes_in_use_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemAllocatorStats.bytes_in_use)
}

// int64 peak_bytes_in_use = 3;
inline void MemAllocatorStats::clear_peak_bytes_in_use() {
  peak_bytes_in_use_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemAllocatorStats::peak_bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemAllocatorStats.peak_bytes_in_use)
  return peak_bytes_in_use_;
}
inline void MemAllocatorStats::set_peak_bytes_in_use(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  peak_bytes_in_use_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemAllocatorStats.peak_bytes_in_use)
}

// int64 largest_alloc_size = 4;
inline void MemAllocatorStats::clear_largest_alloc_size() {
  largest_alloc_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemAllocatorStats::largest_alloc_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemAllocatorStats.largest_alloc_size)
  return largest_alloc_size_;
}
inline void MemAllocatorStats::set_largest_alloc_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  largest_alloc_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemAllocatorStats.largest_alloc_size)
}

// float fragmentation_metric = 5;
inline void MemAllocatorStats::clear_fragmentation_metric() {
  fragmentation_metric_ = 0;
}
inline float MemAllocatorStats::fragmentation_metric() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemAllocatorStats.fragmentation_metric)
  return fragmentation_metric_;
}
inline void MemAllocatorStats::set_fragmentation_metric(float value) {
  
  fragmentation_metric_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemAllocatorStats.fragmentation_metric)
}

// -------------------------------------------------------------------

// MemChunk

// uint64 address = 1;
inline void MemChunk::clear_address() {
  address_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 MemChunk::address() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.address)
  return address_;
}
inline void MemChunk::set_address(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  address_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.address)
}

// int64 size = 2;
inline void MemChunk::clear_size() {
  size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemChunk::size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.size)
  return size_;
}
inline void MemChunk::set_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.size)
}

// int64 requested_size = 3;
inline void MemChunk::clear_requested_size() {
  requested_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemChunk::requested_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.requested_size)
  return requested_size_;
}
inline void MemChunk::set_requested_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  requested_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.requested_size)
}

// int32 bin = 4;
inline void MemChunk::clear_bin() {
  bin_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MemChunk::bin() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.bin)
  return bin_;
}
inline void MemChunk::set_bin(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  bin_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.bin)
}

// string op_name = 5;
inline void MemChunk::clear_op_name() {
  op_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& MemChunk::op_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.op_name)
  return op_name_.GetNoArena();
}
inline void MemChunk::set_op_name(const std::string& value) {
  
  op_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.op_name)
}
inline void MemChunk::set_op_name(std::string&& value) {
  
  op_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MemChunk.op_name)
}
inline void MemChunk::set_op_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.MemChunk.op_name)
}
inline void MemChunk::set_op_name(const char* value, size_t size) {
  
  op_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MemChunk.op_name)
}
inline std::string* MemChunk::mutable_op_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MemChunk.op_name)
  return op_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* MemChunk::release_op_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemChunk.op_name)
  
  return op_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void MemChunk::set_allocated_op_name(std::string* op_name) {
  if (op_name != nullptr) {
    
  } else {
    
  }
  op_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemChunk.op_name)
}

// uint64 freed_at_count = 6;
inline void MemChunk::clear_freed_at_count() {
  freed_at_count_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 MemChunk::freed_at_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.freed_at_count)
  return freed_at_count_;
}
inline void MemChunk::set_freed_at_count(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  freed_at_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.freed_at_count)
}

// uint64 action_count = 7;
inline void MemChunk::clear_action_count() {
  action_count_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 MemChunk::action_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.action_count)
  return action_count_;
}
inline void MemChunk::set_action_count(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  action_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.action_count)
}

// bool in_use = 8;
inline void MemChunk::clear_in_use() {
  in_use_ = false;
}
inline bool MemChunk::in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.in_use)
  return in_use_;
}
inline void MemChunk::set_in_use(bool value) {
  
  in_use_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.in_use)
}

// uint64 step_id = 9;
inline void MemChunk::clear_step_id() {
  step_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 MemChunk::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.step_id)
  return step_id_;
}
inline void MemChunk::set_step_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.step_id)
}

// -------------------------------------------------------------------

// BinSummary

// int32 bin = 1;
inline void BinSummary::clear_bin() {
  bin_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 BinSummary::bin() const {
  // @@protoc_insertion_point(field_get:tensorflow.BinSummary.bin)
  return bin_;
}
inline void BinSummary::set_bin(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  bin_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BinSummary.bin)
}

// int64 total_bytes_in_use = 2;
inline void BinSummary::clear_total_bytes_in_use() {
  total_bytes_in_use_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BinSummary::total_bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.BinSummary.total_bytes_in_use)
  return total_bytes_in_use_;
}
inline void BinSummary::set_total_bytes_in_use(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_bytes_in_use_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BinSummary.total_bytes_in_use)
}

// int64 total_bytes_in_bin = 3;
inline void BinSummary::clear_total_bytes_in_bin() {
  total_bytes_in_bin_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BinSummary::total_bytes_in_bin() const {
  // @@protoc_insertion_point(field_get:tensorflow.BinSummary.total_bytes_in_bin)
  return total_bytes_in_bin_;
}
inline void BinSummary::set_total_bytes_in_bin(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_bytes_in_bin_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BinSummary.total_bytes_in_bin)
}

// int64 total_chunks_in_use = 4;
inline void BinSummary::clear_total_chunks_in_use() {
  total_chunks_in_use_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BinSummary::total_chunks_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.BinSummary.total_chunks_in_use)
  return total_chunks_in_use_;
}
inline void BinSummary::set_total_chunks_in_use(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_chunks_in_use_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BinSummary.total_chunks_in_use)
}

// int64 total_chunks_in_bin = 5;
inline void BinSummary::clear_total_chunks_in_bin() {
  total_chunks_in_bin_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BinSummary::total_chunks_in_bin() const {
  // @@protoc_insertion_point(field_get:tensorflow.BinSummary.total_chunks_in_bin)
  return total_chunks_in_bin_;
}
inline void BinSummary::set_total_chunks_in_bin(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_chunks_in_bin_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.BinSummary.total_chunks_in_bin)
}

// -------------------------------------------------------------------

// SnapShot

// uint64 action_count = 1;
inline void SnapShot::clear_action_count() {
  action_count_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SnapShot::action_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.SnapShot.action_count)
  return action_count_;
}
inline void SnapShot::set_action_count(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  action_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SnapShot.action_count)
}

// int64 size = 2;
inline void SnapShot::clear_size() {
  size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SnapShot::size() const {
  // @@protoc_insertion_point(field_get:tensorflow.SnapShot.size)
  return size_;
}
inline void SnapShot::set_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SnapShot.size)
}

// -------------------------------------------------------------------

// MemoryDump

// string allocator_name = 1;
inline void MemoryDump::clear_allocator_name() {
  allocator_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& MemoryDump::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryDump.allocator_name)
  return allocator_name_.GetNoArena();
}
inline void MemoryDump::set_allocator_name(const std::string& value) {
  
  allocator_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryDump.allocator_name)
}
inline void MemoryDump::set_allocator_name(std::string&& value) {
  
  allocator_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MemoryDump.allocator_name)
}
inline void MemoryDump::set_allocator_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  allocator_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.MemoryDump.allocator_name)
}
inline void MemoryDump::set_allocator_name(const char* value, size_t size) {
  
  allocator_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MemoryDump.allocator_name)
}
inline std::string* MemoryDump::mutable_allocator_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryDump.allocator_name)
  return allocator_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* MemoryDump::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryDump.allocator_name)
  
  return allocator_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void MemoryDump::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  allocator_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), allocator_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryDump.allocator_name)
}

// repeated .tensorflow.BinSummary bin_summary = 2;
inline int MemoryDump::bin_summary_size() const {
  return bin_summary_.size();
}
inline void MemoryDump::clear_bin_summary() {
  bin_summary_.Clear();
}
inline ::tensorflow::BinSummary* MemoryDump::mutable_bin_summary(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryDump.bin_summary)
  return bin_summary_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BinSummary >*
MemoryDump::mutable_bin_summary() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryDump.bin_summary)
  return &bin_summary_;
}
inline const ::tensorflow::BinSummary& MemoryDump::bin_summary(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryDump.bin_summary)
  return bin_summary_.Get(index);
}
inline ::tensorflow::BinSummary* MemoryDump::add_bin_summary() {
  // @@protoc_insertion_point(field_add:tensorflow.MemoryDump.bin_summary)
  return bin_summary_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BinSummary >&
MemoryDump::bin_summary() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryDump.bin_summary)
  return bin_summary_;
}

// repeated .tensorflow.MemChunk chunk = 3;
inline int MemoryDump::chunk_size() const {
  return chunk_.size();
}
inline void MemoryDump::clear_chunk() {
  chunk_.Clear();
}
inline ::tensorflow::MemChunk* MemoryDump::mutable_chunk(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryDump.chunk)
  return chunk_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemChunk >*
MemoryDump::mutable_chunk() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryDump.chunk)
  return &chunk_;
}
inline const ::tensorflow::MemChunk& MemoryDump::chunk(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryDump.chunk)
  return chunk_.Get(index);
}
inline ::tensorflow::MemChunk* MemoryDump::add_chunk() {
  // @@protoc_insertion_point(field_add:tensorflow.MemoryDump.chunk)
  return chunk_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemChunk >&
MemoryDump::chunk() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryDump.chunk)
  return chunk_;
}

// repeated .tensorflow.SnapShot snap_shot = 4;
inline int MemoryDump::snap_shot_size() const {
  return snap_shot_.size();
}
inline void MemoryDump::clear_snap_shot() {
  snap_shot_.Clear();
}
inline ::tensorflow::SnapShot* MemoryDump::mutable_snap_shot(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryDump.snap_shot)
  return snap_shot_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SnapShot >*
MemoryDump::mutable_snap_shot() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryDump.snap_shot)
  return &snap_shot_;
}
inline const ::tensorflow::SnapShot& MemoryDump::snap_shot(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryDump.snap_shot)
  return snap_shot_.Get(index);
}
inline ::tensorflow::SnapShot* MemoryDump::add_snap_shot() {
  // @@protoc_insertion_point(field_add:tensorflow.MemoryDump.snap_shot)
  return snap_shot_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SnapShot >&
MemoryDump::snap_shot() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryDump.snap_shot)
  return snap_shot_;
}

// .tensorflow.MemAllocatorStats stats = 5;
inline bool MemoryDump::has_stats() const {
  return this != internal_default_instance() && stats_ != nullptr;
}
inline void MemoryDump::clear_stats() {
  if (GetArenaNoVirtual() == nullptr && stats_ != nullptr) {
    delete stats_;
  }
  stats_ = nullptr;
}
inline const ::tensorflow::MemAllocatorStats& MemoryDump::stats() const {
  const ::tensorflow::MemAllocatorStats* p = stats_;
  // @@protoc_insertion_point(field_get:tensorflow.MemoryDump.stats)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::MemAllocatorStats*>(
      &::tensorflow::_MemAllocatorStats_default_instance_);
}
inline ::tensorflow::MemAllocatorStats* MemoryDump::release_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryDump.stats)
  
  ::tensorflow::MemAllocatorStats* temp = stats_;
  stats_ = nullptr;
  return temp;
}
inline ::tensorflow::MemAllocatorStats* MemoryDump::mutable_stats() {
  
  if (stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::MemAllocatorStats>(GetArenaNoVirtual());
    stats_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryDump.stats)
  return stats_;
}
inline void MemoryDump::set_allocated_stats(::tensorflow::MemAllocatorStats* stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete stats_;
  }
  if (stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, stats, submessage_arena);
    }
    
  } else {
    
  }
  stats_ = stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryDump.stats)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto
