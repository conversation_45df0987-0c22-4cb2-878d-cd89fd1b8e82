# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/compiler/xla/xla_data.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/compiler/xla/xla_data.proto',
  package='xla',
  syntax='proto3',
  serialized_options=_b('\370\001\001'),
  serialized_pb=_b('\n&tensorflow/compiler/xla/xla_data.proto\x12\x03xla\"\xb7\x01\n\rPaddingConfig\x12=\n\ndimensions\x18\x01 \x03(\x0b\x32).xla.PaddingConfig.PaddingConfigDimension\x1ag\n\x16PaddingConfigDimension\x12\x18\n\x10\x65\x64ge_padding_low\x18\x01 \x01(\x03\x12\x19\n\x11\x65\x64ge_padding_high\x18\x02 \x01(\x03\x12\x18\n\x10interior_padding\x18\x03 \x01(\x03\"\x1f\n\tTileProto\x12\x12\n\ndimensions\x18\x01 \x03(\x03\"\xde\x01\n\x0bLayoutProto\x12\x1b\n\x06\x66ormat\x18\x04 \x01(\x0e\x32\x0b.xla.Format\x12\x16\n\x0eminor_to_major\x18\x01 \x03(\x03\x12\x1d\n\x05tiles\x18\x06 \x03(\x0b\x32\x0e.xla.TileProto\x12\x1c\n\x14\x65lement_size_in_bits\x18\x07 \x01(\x03\x12\x14\n\x0cmemory_space\x18\x08 \x01(\x03J\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04J\x04\x08\x05\x10\x06R\x11padded_dimensionsR\rpadding_valueR\x13max_sparse_elements\"\xbd\x01\n\nShapeProto\x12(\n\x0c\x65lement_type\x18\x02 \x01(\x0e\x32\x12.xla.PrimitiveType\x12\x12\n\ndimensions\x18\x03 \x03(\x03\x12%\n\x0ctuple_shapes\x18\x04 \x03(\x0b\x32\x0f.xla.ShapeProto\x12 \n\x06layout\x18\x05 \x01(\x0b\x32\x10.xla.LayoutProto\x12\x1c\n\x14is_dynamic_dimension\x18\x06 \x03(\x08J\x04\x08\x01\x10\x02R\x04rank\"r\n\x11ProgramShapeProto\x12#\n\nparameters\x18\x01 \x03(\x0b\x32\x0f.xla.ShapeProto\x12\x1f\n\x06result\x18\x02 \x01(\x0b\x32\x0f.xla.ShapeProto\x12\x17\n\x0fparameter_names\x18\x03 \x03(\t\"D\n\x10\x43omputationStats\x12\x12\n\nflop_count\x18\x01 \x01(\x01\x12\x1c\n\x14transcendental_count\x18\x02 \x01(\x01\"\x92\x02\n\nOpMetadata\x12\x0f\n\x07op_type\x18\x01 \x01(\t\x12\x0f\n\x07op_name\x18\x02 \x01(\t\x12\x13\n\x0bsource_file\x18\x03 \x01(\t\x12\x13\n\x0bsource_line\x18\x04 \x01(\x05\x12&\n\x0cprofile_type\x18\x05 \x03(\x0e\x32\x10.xla.ProfileType\x12\x18\n\x10\x63reation_pass_id\x18\x06 \x01(\x03\x12 \n\x18logical_creation_pass_id\x18\x07 \x01(\x03\x12\'\n\x1fsize_of_generated_code_in_bytes\x18\x08 \x01(\x03\x12+\n#size_of_memory_working_set_in_bytes\x18\t \x01(\x03\"\xe3\x01\n\x10\x45xecutionProfile\x12\x1d\n\x15\x63ompilation_cache_hit\x18\x01 \x01(\x08\x12\x17\n\x0f\x63ompile_time_ms\x18\x02 \x01(\x03\x12\x1b\n\x13\x63ompute_cycle_count\x18\x03 \x01(\x03\x12\x17\n\x0f\x63ompute_time_ns\x18\x04 \x01(\x03\x12$\n\x1c\x63ompute_and_transfer_time_ns\x18\x05 \x01(\x03\x12 \n\x18\x65xecutable_size_in_bytes\x18\x06 \x01(\x03\x12\x19\n\x11profile_cache_hit\x18\x07 \x01(\x08\"!\n\x0f\x45xecutionHandle\x12\x0e\n\x06handle\x18\x01 \x01(\x03\"\"\n\x10GlobalDataHandle\x12\x0e\n\x06handle\x18\x01 \x01(\x03\"4\n\x0c\x44\x65viceHandle\x12\x0e\n\x06handle\x18\x01 \x01(\x03\x12\x14\n\x0c\x64\x65vice_count\x18\x02 \x01(\x03\"\xb4\x01\n\rChannelHandle\x12\x0e\n\x06handle\x18\x01 \x01(\x03\x12,\n\x04type\x18\x02 \x01(\x0e\x32\x1e.xla.ChannelHandle.ChannelType\"e\n\x0b\x43hannelType\x12\x18\n\x14\x43HANNEL_TYPE_INVALID\x10\x00\x12\x14\n\x10\x44\x45VICE_TO_DEVICE\x10\x01\x12\x12\n\x0e\x44\x45VICE_TO_HOST\x10\x02\x12\x12\n\x0eHOST_TO_DEVICE\x10\x03\"\xc5\x01\n\x15\x44\x65viceAssignmentProto\x12\x15\n\rreplica_count\x18\x01 \x01(\x05\x12\x19\n\x11\x63omputation_count\x18\x02 \x01(\x05\x12I\n\x13\x63omputation_devices\x18\x03 \x03(\x0b\x32,.xla.DeviceAssignmentProto.ComputationDevice\x1a/\n\x11\x43omputationDevice\x12\x1a\n\x12replica_device_ids\x18\x01 \x03(\x05\"\xc4\x02\n\x0cLiteralProto\x12\x1e\n\x05shape\x18\x01 \x01(\x0b\x32\x0f.xla.ShapeProto\x12\r\n\x05preds\x18\x02 \x03(\x08\x12\x0b\n\x03s8s\x18\x0f \x01(\x0c\x12\x0b\n\x03u8s\x18\x03 \x01(\x0c\x12\x0c\n\x04s32s\x18\x04 \x03(\x05\x12\x0c\n\x04s64s\x18\x05 \x03(\x03\x12\x0c\n\x04u32s\x18\x06 \x03(\r\x12\x0c\n\x04u64s\x18\x07 \x03(\x04\x12\x0c\n\x04\x66\x33\x32s\x18\x08 \x03(\x02\x12\x0c\n\x04\x66\x36\x34s\x18\t \x03(\x01\x12\x0c\n\x04\x63\x36\x34s\x18\x0c \x03(\x02\x12\r\n\x05\x63\x31\x32\x38s\x18\x12 \x03(\x01\x12)\n\x0etuple_literals\x18\n \x03(\x0b\x32\x11.xla.LiteralProto\x12\x0c\n\x04\x66\x31\x36s\x18\x0b \x01(\x0c\x12\r\n\x05\x62\x66\x31\x36s\x18\r \x01(\x0c\x12\x0c\n\x04u16s\x18\x10 \x01(\x0c\x12\x0c\n\x04s16s\x18\x11 \x01(\x0c\x12\x16\n\x0esparse_indices\x18\x0e \x03(\x03\"\xa3\x01\n\x0fWindowDimension\x12\x0c\n\x04size\x18\x01 \x01(\x03\x12\x0e\n\x06stride\x18\x02 \x01(\x03\x12\x13\n\x0bpadding_low\x18\x03 \x01(\x03\x12\x14\n\x0cpadding_high\x18\x04 \x01(\x03\x12\x17\n\x0fwindow_dilation\x18\x05 \x01(\x03\x12\x15\n\rbase_dilation\x18\x06 \x01(\x03\x12\x17\n\x0fwindow_reversal\x18\x07 \x01(\x08\"2\n\x06Window\x12(\n\ndimensions\x18\x01 \x03(\x0b\x32\x14.xla.WindowDimension\"~\n\x16GatherDimensionNumbers\x12\x13\n\x0boffset_dims\x18\x01 \x03(\x03\x12\x1c\n\x14\x63ollapsed_slice_dims\x18\x02 \x03(\x03\x12\x17\n\x0fstart_index_map\x18\x03 \x03(\x03\x12\x18\n\x10index_vector_dim\x18\x04 \x01(\x03\"\x93\x01\n\x17ScatterDimensionNumbers\x12\x1a\n\x12update_window_dims\x18\x01 \x03(\x03\x12\x1c\n\x14inserted_window_dims\x18\x02 \x03(\x03\x12$\n\x1cscatter_dims_to_operand_dims\x18\x03 \x03(\x03\x12\x18\n\x10index_vector_dim\x18\x04 \x01(\x03\"\xd8\x02\n\x1b\x43onvolutionDimensionNumbers\x12\x1d\n\x15input_batch_dimension\x18\x07 \x01(\x03\x12\x1f\n\x17input_feature_dimension\x18\x08 \x01(\x03\x12 \n\x18input_spatial_dimensions\x18\x0b \x03(\x03\x12&\n\x1ekernel_input_feature_dimension\x18\x03 \x01(\x03\x12\'\n\x1fkernel_output_feature_dimension\x18\x04 \x01(\x03\x12!\n\x19kernel_spatial_dimensions\x18\x06 \x03(\x03\x12\x1e\n\x16output_batch_dimension\x18\t \x01(\x03\x12 \n\x18output_feature_dimension\x18\n \x01(\x03\x12!\n\x19output_spatial_dimensions\x18\x0c \x03(\x03\"\x99\x01\n\x13\x44otDimensionNumbers\x12\"\n\x1alhs_contracting_dimensions\x18\x01 \x03(\x03\x12\"\n\x1arhs_contracting_dimensions\x18\x02 \x03(\x03\x12\x1c\n\x14lhs_batch_dimensions\x18\x03 \x03(\x03\x12\x1c\n\x14rhs_batch_dimensions\x18\x04 \x03(\x03\"\xdf\x01\n\x16TriangularSolveOptions\x12\x11\n\tleft_side\x18\x01 \x01(\x08\x12\r\n\x05lower\x18\x02 \x01(\x08\x12\x15\n\runit_diagonal\x18\x03 \x01(\x08\x12:\n\x0btranspose_a\x18\x04 \x01(\x0e\x32%.xla.TriangularSolveOptions.Transpose\"P\n\tTranspose\x12\x15\n\x11TRANSPOSE_INVALID\x10\x00\x12\x10\n\x0cNO_TRANSPOSE\x10\x01\x12\r\n\tTRANSPOSE\x10\x02\x12\x0b\n\x07\x41\x44JOINT\x10\x03\" \n\x0f\x43holeskyOptions\x12\r\n\x05lower\x18\x01 \x01(\x08\"o\n\x12\x46rontendAttributes\x12-\n\x03map\x18\x01 \x03(\x0b\x32 .xla.FrontendAttributes.MapEntry\x1a*\n\x08MapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xd2\x02\n\nOpSharding\x12\"\n\x04type\x18\x01 \x01(\x0e\x32\x14.xla.OpSharding.Type\x12#\n\ntile_shape\x18\x02 \x01(\x0b\x32\x0f.xla.ShapeProto\x12\"\n\x1atile_assignment_dimensions\x18\x03 \x03(\x03\x12\x1f\n\x17tile_assignment_devices\x18\x04 \x03(\x03\x12(\n\x0ftuple_shardings\x18\x05 \x03(\x0b\x32\x0f.xla.OpSharding\x12\"\n\x1areplicate_on_last_tile_dim\x18\x06 \x01(\x08\x12!\n\x08metadata\x18\x07 \x03(\x0b\x32\x0f.xla.OpMetadata\"E\n\x04Type\x12\x0e\n\nREPLICATED\x10\x00\x12\x0b\n\x07MAXIMAL\x10\x01\x12\t\n\x05TUPLE\x10\x02\x12\t\n\x05OTHER\x10\x03\x12\n\n\x06MANUAL\x10\x04\"#\n\x0cReplicaGroup\x12\x13\n\x0breplica_ids\x18\x01 \x03(\x03\".\n\x0cSourceTarget\x12\x0e\n\x06source\x18\x01 \x01(\x03\x12\x0e\n\x06target\x18\x02 \x01(\x03\"}\n\x0fPrecisionConfig\x12\x39\n\x11operand_precision\x18\x01 \x03(\x0e\x32\x1e.xla.PrecisionConfig.Precision\"/\n\tPrecision\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x08\n\x04HIGH\x10\x01\x12\x0b\n\x07HIGHEST\x10\x02\":\n\x14ParameterReplication\x12\"\n\x1areplicated_at_leaf_buffers\x18\x01 \x03(\x08\"{\n\x16WhileLoopBackendConfig\x12\x44\n\x10known_trip_count\x18\x01 \x01(\x0b\x32*.xla.WhileLoopBackendConfig.KnownTripCount\x1a\x1b\n\x0eKnownTripCount\x12\t\n\x01n\x18\x01 \x01(\x03\"q\n\x1f\x43ustomCallOutputOperandAliasing\x12\x1a\n\x12output_shape_index\x18\x01 \x03(\x03\x12\x15\n\roperand_index\x18\x02 \x01(\x03\x12\x1b\n\x13operand_shape_index\x18\x03 \x03(\x03*\xda\x01\n\rPrimitiveType\x12\x1a\n\x16PRIMITIVE_TYPE_INVALID\x10\x00\x12\x08\n\x04PRED\x10\x01\x12\x06\n\x02S8\x10\x02\x12\x07\n\x03S16\x10\x03\x12\x07\n\x03S32\x10\x04\x12\x07\n\x03S64\x10\x05\x12\x06\n\x02U8\x10\x06\x12\x07\n\x03U16\x10\x07\x12\x07\n\x03U32\x10\x08\x12\x07\n\x03U64\x10\t\x12\x07\n\x03\x46\x31\x36\x10\n\x12\x07\n\x03\x46\x33\x32\x10\x0b\x12\x08\n\x04\x42\x46\x31\x36\x10\x10\x12\x07\n\x03\x46\x36\x34\x10\x0c\x12\x07\n\x03\x43\x36\x34\x10\x0f\x12\x08\n\x04\x43\x31\x32\x38\x10\x12\x12\t\n\x05TUPLE\x10\r\x12\x0f\n\x0bOPAQUE_TYPE\x10\x0e\x12\t\n\x05TOKEN\x10\x11*5\n\x06\x46ormat\x12\x12\n\x0eINVALID_FORMAT\x10\x00\x12\t\n\x05\x44\x45NSE\x10\x01\"\x04\x08\x02\x10\x02*\x06SPARSE*=\n\x0bProfileType\x12\x0b\n\x07INVALID\x10\x00\x12\n\n\x06WINDOW\x10\x01\x12\x08\n\x04\x46LAG\x10\x02\x12\x0b\n\x07INTEGER\x10\x03*G\n\x0bPaddingType\x12\x13\n\x0fPADDING_INVALID\x10\x00\x12\x11\n\rPADDING_VALID\x10\x01\x12\x10\n\x0cPADDING_SAME\x10\x02*1\n\x07\x46\x66tType\x12\x07\n\x03\x46\x46T\x10\x00\x12\x08\n\x04IFFT\x10\x01\x12\x08\n\x04RFFT\x10\x02\x12\t\n\x05IRFFT\x10\x03*F\n\x12RandomDistribution\x12\x0f\n\x0bRNG_INVALID\x10\x00\x12\x0f\n\x0bRNG_UNIFORM\x10\x01\x12\x0e\n\nRNG_NORMAL\x10\x02*E\n\x0fRandomAlgorithm\x12\x0f\n\x0bRNG_DEFAULT\x10\x00\x12\x11\n\rRNG_THREE_FRY\x10\x01\x12\x0e\n\nRNG_PHILOX\x10\x02\x42\x03\xf8\x01\x01\x62\x06proto3')
)

_PRIMITIVETYPE = _descriptor.EnumDescriptor(
  name='PrimitiveType',
  full_name='xla.PrimitiveType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PRIMITIVE_TYPE_INVALID', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PRED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='S8', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='S16', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='S32', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='S64', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='U8', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='U16', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='U32', index=8, number=8,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='U64', index=9, number=9,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='F16', index=10, number=10,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='F32', index=11, number=11,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BF16', index=12, number=16,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='F64', index=13, number=12,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='C64', index=14, number=15,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='C128', index=15, number=18,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TUPLE', index=16, number=13,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OPAQUE_TYPE', index=17, number=14,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TOKEN', index=18, number=17,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4437,
  serialized_end=4655,
)
_sym_db.RegisterEnumDescriptor(_PRIMITIVETYPE)

PrimitiveType = enum_type_wrapper.EnumTypeWrapper(_PRIMITIVETYPE)
_FORMAT = _descriptor.EnumDescriptor(
  name='Format',
  full_name='xla.Format',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INVALID_FORMAT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DENSE', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4657,
  serialized_end=4710,
)
_sym_db.RegisterEnumDescriptor(_FORMAT)

Format = enum_type_wrapper.EnumTypeWrapper(_FORMAT)
_PROFILETYPE = _descriptor.EnumDescriptor(
  name='ProfileType',
  full_name='xla.ProfileType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INVALID', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WINDOW', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FLAG', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INTEGER', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4712,
  serialized_end=4773,
)
_sym_db.RegisterEnumDescriptor(_PROFILETYPE)

ProfileType = enum_type_wrapper.EnumTypeWrapper(_PROFILETYPE)
_PADDINGTYPE = _descriptor.EnumDescriptor(
  name='PaddingType',
  full_name='xla.PaddingType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PADDING_INVALID', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PADDING_VALID', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PADDING_SAME', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4775,
  serialized_end=4846,
)
_sym_db.RegisterEnumDescriptor(_PADDINGTYPE)

PaddingType = enum_type_wrapper.EnumTypeWrapper(_PADDINGTYPE)
_FFTTYPE = _descriptor.EnumDescriptor(
  name='FftType',
  full_name='xla.FftType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FFT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IFFT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RFFT', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IRFFT', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4848,
  serialized_end=4897,
)
_sym_db.RegisterEnumDescriptor(_FFTTYPE)

FftType = enum_type_wrapper.EnumTypeWrapper(_FFTTYPE)
_RANDOMDISTRIBUTION = _descriptor.EnumDescriptor(
  name='RandomDistribution',
  full_name='xla.RandomDistribution',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='RNG_INVALID', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RNG_UNIFORM', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RNG_NORMAL', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4899,
  serialized_end=4969,
)
_sym_db.RegisterEnumDescriptor(_RANDOMDISTRIBUTION)

RandomDistribution = enum_type_wrapper.EnumTypeWrapper(_RANDOMDISTRIBUTION)
_RANDOMALGORITHM = _descriptor.EnumDescriptor(
  name='RandomAlgorithm',
  full_name='xla.RandomAlgorithm',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='RNG_DEFAULT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RNG_THREE_FRY', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RNG_PHILOX', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4971,
  serialized_end=5040,
)
_sym_db.RegisterEnumDescriptor(_RANDOMALGORITHM)

RandomAlgorithm = enum_type_wrapper.EnumTypeWrapper(_RANDOMALGORITHM)
PRIMITIVE_TYPE_INVALID = 0
PRED = 1
S8 = 2
S16 = 3
S32 = 4
S64 = 5
U8 = 6
U16 = 7
U32 = 8
U64 = 9
F16 = 10
F32 = 11
BF16 = 16
F64 = 12
C64 = 15
C128 = 18
TUPLE = 13
OPAQUE_TYPE = 14
TOKEN = 17
INVALID_FORMAT = 0
DENSE = 1
INVALID = 0
WINDOW = 1
FLAG = 2
INTEGER = 3
PADDING_INVALID = 0
PADDING_VALID = 1
PADDING_SAME = 2
FFT = 0
IFFT = 1
RFFT = 2
IRFFT = 3
RNG_INVALID = 0
RNG_UNIFORM = 1
RNG_NORMAL = 2
RNG_DEFAULT = 0
RNG_THREE_FRY = 1
RNG_PHILOX = 2


_CHANNELHANDLE_CHANNELTYPE = _descriptor.EnumDescriptor(
  name='ChannelType',
  full_name='xla.ChannelHandle.ChannelType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CHANNEL_TYPE_INVALID', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEVICE_TO_DEVICE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEVICE_TO_HOST', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HOST_TO_DEVICE', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1581,
  serialized_end=1682,
)
_sym_db.RegisterEnumDescriptor(_CHANNELHANDLE_CHANNELTYPE)

_TRIANGULARSOLVEOPTIONS_TRANSPOSE = _descriptor.EnumDescriptor(
  name='Transpose',
  full_name='xla.TriangularSolveOptions.Transpose',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TRANSPOSE_INVALID', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NO_TRANSPOSE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRANSPOSE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ADJOINT', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3354,
  serialized_end=3434,
)
_sym_db.RegisterEnumDescriptor(_TRIANGULARSOLVEOPTIONS_TRANSPOSE)

_OPSHARDING_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='xla.OpSharding.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='REPLICATED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MAXIMAL', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TUPLE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OTHER', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MANUAL', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3853,
  serialized_end=3922,
)
_sym_db.RegisterEnumDescriptor(_OPSHARDING_TYPE)

_PRECISIONCONFIG_PRECISION = _descriptor.EnumDescriptor(
  name='Precision',
  full_name='xla.PrecisionConfig.Precision',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DEFAULT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HIGH', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HIGHEST', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4087,
  serialized_end=4134,
)
_sym_db.RegisterEnumDescriptor(_PRECISIONCONFIG_PRECISION)


_PADDINGCONFIG_PADDINGCONFIGDIMENSION = _descriptor.Descriptor(
  name='PaddingConfigDimension',
  full_name='xla.PaddingConfig.PaddingConfigDimension',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='edge_padding_low', full_name='xla.PaddingConfig.PaddingConfigDimension.edge_padding_low', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='edge_padding_high', full_name='xla.PaddingConfig.PaddingConfigDimension.edge_padding_high', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interior_padding', full_name='xla.PaddingConfig.PaddingConfigDimension.interior_padding', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=128,
  serialized_end=231,
)

_PADDINGCONFIG = _descriptor.Descriptor(
  name='PaddingConfig',
  full_name='xla.PaddingConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='xla.PaddingConfig.dimensions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_PADDINGCONFIG_PADDINGCONFIGDIMENSION, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=48,
  serialized_end=231,
)


_TILEPROTO = _descriptor.Descriptor(
  name='TileProto',
  full_name='xla.TileProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='xla.TileProto.dimensions', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=233,
  serialized_end=264,
)


_LAYOUTPROTO = _descriptor.Descriptor(
  name='LayoutProto',
  full_name='xla.LayoutProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='format', full_name='xla.LayoutProto.format', index=0,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='minor_to_major', full_name='xla.LayoutProto.minor_to_major', index=1,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tiles', full_name='xla.LayoutProto.tiles', index=2,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='element_size_in_bits', full_name='xla.LayoutProto.element_size_in_bits', index=3,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='memory_space', full_name='xla.LayoutProto.memory_space', index=4,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=267,
  serialized_end=489,
)


_SHAPEPROTO = _descriptor.Descriptor(
  name='ShapeProto',
  full_name='xla.ShapeProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='element_type', full_name='xla.ShapeProto.element_type', index=0,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='xla.ShapeProto.dimensions', index=1,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tuple_shapes', full_name='xla.ShapeProto.tuple_shapes', index=2,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='layout', full_name='xla.ShapeProto.layout', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_dynamic_dimension', full_name='xla.ShapeProto.is_dynamic_dimension', index=4,
      number=6, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=492,
  serialized_end=681,
)


_PROGRAMSHAPEPROTO = _descriptor.Descriptor(
  name='ProgramShapeProto',
  full_name='xla.ProgramShapeProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='parameters', full_name='xla.ProgramShapeProto.parameters', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='xla.ProgramShapeProto.result', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parameter_names', full_name='xla.ProgramShapeProto.parameter_names', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=683,
  serialized_end=797,
)


_COMPUTATIONSTATS = _descriptor.Descriptor(
  name='ComputationStats',
  full_name='xla.ComputationStats',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='flop_count', full_name='xla.ComputationStats.flop_count', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transcendental_count', full_name='xla.ComputationStats.transcendental_count', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=799,
  serialized_end=867,
)


_OPMETADATA = _descriptor.Descriptor(
  name='OpMetadata',
  full_name='xla.OpMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='op_type', full_name='xla.OpMetadata.op_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='op_name', full_name='xla.OpMetadata.op_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_file', full_name='xla.OpMetadata.source_file', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_line', full_name='xla.OpMetadata.source_line', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='profile_type', full_name='xla.OpMetadata.profile_type', index=4,
      number=5, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='creation_pass_id', full_name='xla.OpMetadata.creation_pass_id', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logical_creation_pass_id', full_name='xla.OpMetadata.logical_creation_pass_id', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='size_of_generated_code_in_bytes', full_name='xla.OpMetadata.size_of_generated_code_in_bytes', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='size_of_memory_working_set_in_bytes', full_name='xla.OpMetadata.size_of_memory_working_set_in_bytes', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=870,
  serialized_end=1144,
)


_EXECUTIONPROFILE = _descriptor.Descriptor(
  name='ExecutionProfile',
  full_name='xla.ExecutionProfile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='compilation_cache_hit', full_name='xla.ExecutionProfile.compilation_cache_hit', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compile_time_ms', full_name='xla.ExecutionProfile.compile_time_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compute_cycle_count', full_name='xla.ExecutionProfile.compute_cycle_count', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compute_time_ns', full_name='xla.ExecutionProfile.compute_time_ns', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compute_and_transfer_time_ns', full_name='xla.ExecutionProfile.compute_and_transfer_time_ns', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='executable_size_in_bytes', full_name='xla.ExecutionProfile.executable_size_in_bytes', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='profile_cache_hit', full_name='xla.ExecutionProfile.profile_cache_hit', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1147,
  serialized_end=1374,
)


_EXECUTIONHANDLE = _descriptor.Descriptor(
  name='ExecutionHandle',
  full_name='xla.ExecutionHandle',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handle', full_name='xla.ExecutionHandle.handle', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1376,
  serialized_end=1409,
)


_GLOBALDATAHANDLE = _descriptor.Descriptor(
  name='GlobalDataHandle',
  full_name='xla.GlobalDataHandle',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handle', full_name='xla.GlobalDataHandle.handle', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1411,
  serialized_end=1445,
)


_DEVICEHANDLE = _descriptor.Descriptor(
  name='DeviceHandle',
  full_name='xla.DeviceHandle',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handle', full_name='xla.DeviceHandle.handle', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_count', full_name='xla.DeviceHandle.device_count', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1447,
  serialized_end=1499,
)


_CHANNELHANDLE = _descriptor.Descriptor(
  name='ChannelHandle',
  full_name='xla.ChannelHandle',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handle', full_name='xla.ChannelHandle.handle', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='xla.ChannelHandle.type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _CHANNELHANDLE_CHANNELTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1502,
  serialized_end=1682,
)


_DEVICEASSIGNMENTPROTO_COMPUTATIONDEVICE = _descriptor.Descriptor(
  name='ComputationDevice',
  full_name='xla.DeviceAssignmentProto.ComputationDevice',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='replica_device_ids', full_name='xla.DeviceAssignmentProto.ComputationDevice.replica_device_ids', index=0,
      number=1, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1835,
  serialized_end=1882,
)

_DEVICEASSIGNMENTPROTO = _descriptor.Descriptor(
  name='DeviceAssignmentProto',
  full_name='xla.DeviceAssignmentProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='replica_count', full_name='xla.DeviceAssignmentProto.replica_count', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='computation_count', full_name='xla.DeviceAssignmentProto.computation_count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='computation_devices', full_name='xla.DeviceAssignmentProto.computation_devices', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_DEVICEASSIGNMENTPROTO_COMPUTATIONDEVICE, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1685,
  serialized_end=1882,
)


_LITERALPROTO = _descriptor.Descriptor(
  name='LiteralProto',
  full_name='xla.LiteralProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='shape', full_name='xla.LiteralProto.shape', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='preds', full_name='xla.LiteralProto.preds', index=1,
      number=2, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s8s', full_name='xla.LiteralProto.s8s', index=2,
      number=15, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='u8s', full_name='xla.LiteralProto.u8s', index=3,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s32s', full_name='xla.LiteralProto.s32s', index=4,
      number=4, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s64s', full_name='xla.LiteralProto.s64s', index=5,
      number=5, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='u32s', full_name='xla.LiteralProto.u32s', index=6,
      number=6, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='u64s', full_name='xla.LiteralProto.u64s', index=7,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='f32s', full_name='xla.LiteralProto.f32s', index=8,
      number=8, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='f64s', full_name='xla.LiteralProto.f64s', index=9,
      number=9, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='c64s', full_name='xla.LiteralProto.c64s', index=10,
      number=12, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='c128s', full_name='xla.LiteralProto.c128s', index=11,
      number=18, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tuple_literals', full_name='xla.LiteralProto.tuple_literals', index=12,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='f16s', full_name='xla.LiteralProto.f16s', index=13,
      number=11, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bf16s', full_name='xla.LiteralProto.bf16s', index=14,
      number=13, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='u16s', full_name='xla.LiteralProto.u16s', index=15,
      number=16, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s16s', full_name='xla.LiteralProto.s16s', index=16,
      number=17, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sparse_indices', full_name='xla.LiteralProto.sparse_indices', index=17,
      number=14, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1885,
  serialized_end=2209,
)


_WINDOWDIMENSION = _descriptor.Descriptor(
  name='WindowDimension',
  full_name='xla.WindowDimension',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='size', full_name='xla.WindowDimension.size', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stride', full_name='xla.WindowDimension.stride', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='padding_low', full_name='xla.WindowDimension.padding_low', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='padding_high', full_name='xla.WindowDimension.padding_high', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='window_dilation', full_name='xla.WindowDimension.window_dilation', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='base_dilation', full_name='xla.WindowDimension.base_dilation', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='window_reversal', full_name='xla.WindowDimension.window_reversal', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2212,
  serialized_end=2375,
)


_WINDOW = _descriptor.Descriptor(
  name='Window',
  full_name='xla.Window',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='xla.Window.dimensions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2377,
  serialized_end=2427,
)


_GATHERDIMENSIONNUMBERS = _descriptor.Descriptor(
  name='GatherDimensionNumbers',
  full_name='xla.GatherDimensionNumbers',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='offset_dims', full_name='xla.GatherDimensionNumbers.offset_dims', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='collapsed_slice_dims', full_name='xla.GatherDimensionNumbers.collapsed_slice_dims', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_index_map', full_name='xla.GatherDimensionNumbers.start_index_map', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='index_vector_dim', full_name='xla.GatherDimensionNumbers.index_vector_dim', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2429,
  serialized_end=2555,
)


_SCATTERDIMENSIONNUMBERS = _descriptor.Descriptor(
  name='ScatterDimensionNumbers',
  full_name='xla.ScatterDimensionNumbers',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='update_window_dims', full_name='xla.ScatterDimensionNumbers.update_window_dims', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inserted_window_dims', full_name='xla.ScatterDimensionNumbers.inserted_window_dims', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scatter_dims_to_operand_dims', full_name='xla.ScatterDimensionNumbers.scatter_dims_to_operand_dims', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='index_vector_dim', full_name='xla.ScatterDimensionNumbers.index_vector_dim', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2558,
  serialized_end=2705,
)


_CONVOLUTIONDIMENSIONNUMBERS = _descriptor.Descriptor(
  name='ConvolutionDimensionNumbers',
  full_name='xla.ConvolutionDimensionNumbers',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='input_batch_dimension', full_name='xla.ConvolutionDimensionNumbers.input_batch_dimension', index=0,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='input_feature_dimension', full_name='xla.ConvolutionDimensionNumbers.input_feature_dimension', index=1,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='input_spatial_dimensions', full_name='xla.ConvolutionDimensionNumbers.input_spatial_dimensions', index=2,
      number=11, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kernel_input_feature_dimension', full_name='xla.ConvolutionDimensionNumbers.kernel_input_feature_dimension', index=3,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kernel_output_feature_dimension', full_name='xla.ConvolutionDimensionNumbers.kernel_output_feature_dimension', index=4,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kernel_spatial_dimensions', full_name='xla.ConvolutionDimensionNumbers.kernel_spatial_dimensions', index=5,
      number=6, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_batch_dimension', full_name='xla.ConvolutionDimensionNumbers.output_batch_dimension', index=6,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_feature_dimension', full_name='xla.ConvolutionDimensionNumbers.output_feature_dimension', index=7,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_spatial_dimensions', full_name='xla.ConvolutionDimensionNumbers.output_spatial_dimensions', index=8,
      number=12, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2708,
  serialized_end=3052,
)


_DOTDIMENSIONNUMBERS = _descriptor.Descriptor(
  name='DotDimensionNumbers',
  full_name='xla.DotDimensionNumbers',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lhs_contracting_dimensions', full_name='xla.DotDimensionNumbers.lhs_contracting_dimensions', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rhs_contracting_dimensions', full_name='xla.DotDimensionNumbers.rhs_contracting_dimensions', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lhs_batch_dimensions', full_name='xla.DotDimensionNumbers.lhs_batch_dimensions', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rhs_batch_dimensions', full_name='xla.DotDimensionNumbers.rhs_batch_dimensions', index=3,
      number=4, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3055,
  serialized_end=3208,
)


_TRIANGULARSOLVEOPTIONS = _descriptor.Descriptor(
  name='TriangularSolveOptions',
  full_name='xla.TriangularSolveOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='left_side', full_name='xla.TriangularSolveOptions.left_side', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lower', full_name='xla.TriangularSolveOptions.lower', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diagonal', full_name='xla.TriangularSolveOptions.unit_diagonal', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transpose_a', full_name='xla.TriangularSolveOptions.transpose_a', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TRIANGULARSOLVEOPTIONS_TRANSPOSE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3211,
  serialized_end=3434,
)


_CHOLESKYOPTIONS = _descriptor.Descriptor(
  name='CholeskyOptions',
  full_name='xla.CholeskyOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lower', full_name='xla.CholeskyOptions.lower', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3436,
  serialized_end=3468,
)


_FRONTENDATTRIBUTES_MAPENTRY = _descriptor.Descriptor(
  name='MapEntry',
  full_name='xla.FrontendAttributes.MapEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='xla.FrontendAttributes.MapEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='xla.FrontendAttributes.MapEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3539,
  serialized_end=3581,
)

_FRONTENDATTRIBUTES = _descriptor.Descriptor(
  name='FrontendAttributes',
  full_name='xla.FrontendAttributes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='map', full_name='xla.FrontendAttributes.map', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_FRONTENDATTRIBUTES_MAPENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3470,
  serialized_end=3581,
)


_OPSHARDING = _descriptor.Descriptor(
  name='OpSharding',
  full_name='xla.OpSharding',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='xla.OpSharding.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tile_shape', full_name='xla.OpSharding.tile_shape', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tile_assignment_dimensions', full_name='xla.OpSharding.tile_assignment_dimensions', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tile_assignment_devices', full_name='xla.OpSharding.tile_assignment_devices', index=3,
      number=4, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tuple_shardings', full_name='xla.OpSharding.tuple_shardings', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='replicate_on_last_tile_dim', full_name='xla.OpSharding.replicate_on_last_tile_dim', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='metadata', full_name='xla.OpSharding.metadata', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _OPSHARDING_TYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3584,
  serialized_end=3922,
)


_REPLICAGROUP = _descriptor.Descriptor(
  name='ReplicaGroup',
  full_name='xla.ReplicaGroup',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='replica_ids', full_name='xla.ReplicaGroup.replica_ids', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3924,
  serialized_end=3959,
)


_SOURCETARGET = _descriptor.Descriptor(
  name='SourceTarget',
  full_name='xla.SourceTarget',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source', full_name='xla.SourceTarget.source', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target', full_name='xla.SourceTarget.target', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3961,
  serialized_end=4007,
)


_PRECISIONCONFIG = _descriptor.Descriptor(
  name='PrecisionConfig',
  full_name='xla.PrecisionConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='operand_precision', full_name='xla.PrecisionConfig.operand_precision', index=0,
      number=1, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _PRECISIONCONFIG_PRECISION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4009,
  serialized_end=4134,
)


_PARAMETERREPLICATION = _descriptor.Descriptor(
  name='ParameterReplication',
  full_name='xla.ParameterReplication',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='replicated_at_leaf_buffers', full_name='xla.ParameterReplication.replicated_at_leaf_buffers', index=0,
      number=1, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4136,
  serialized_end=4194,
)


_WHILELOOPBACKENDCONFIG_KNOWNTRIPCOUNT = _descriptor.Descriptor(
  name='KnownTripCount',
  full_name='xla.WhileLoopBackendConfig.KnownTripCount',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='n', full_name='xla.WhileLoopBackendConfig.KnownTripCount.n', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4292,
  serialized_end=4319,
)

_WHILELOOPBACKENDCONFIG = _descriptor.Descriptor(
  name='WhileLoopBackendConfig',
  full_name='xla.WhileLoopBackendConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='known_trip_count', full_name='xla.WhileLoopBackendConfig.known_trip_count', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_WHILELOOPBACKENDCONFIG_KNOWNTRIPCOUNT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4196,
  serialized_end=4319,
)


_CUSTOMCALLOUTPUTOPERANDALIASING = _descriptor.Descriptor(
  name='CustomCallOutputOperandAliasing',
  full_name='xla.CustomCallOutputOperandAliasing',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='output_shape_index', full_name='xla.CustomCallOutputOperandAliasing.output_shape_index', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operand_index', full_name='xla.CustomCallOutputOperandAliasing.operand_index', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operand_shape_index', full_name='xla.CustomCallOutputOperandAliasing.operand_shape_index', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4321,
  serialized_end=4434,
)

_PADDINGCONFIG_PADDINGCONFIGDIMENSION.containing_type = _PADDINGCONFIG
_PADDINGCONFIG.fields_by_name['dimensions'].message_type = _PADDINGCONFIG_PADDINGCONFIGDIMENSION
_LAYOUTPROTO.fields_by_name['format'].enum_type = _FORMAT
_LAYOUTPROTO.fields_by_name['tiles'].message_type = _TILEPROTO
_SHAPEPROTO.fields_by_name['element_type'].enum_type = _PRIMITIVETYPE
_SHAPEPROTO.fields_by_name['tuple_shapes'].message_type = _SHAPEPROTO
_SHAPEPROTO.fields_by_name['layout'].message_type = _LAYOUTPROTO
_PROGRAMSHAPEPROTO.fields_by_name['parameters'].message_type = _SHAPEPROTO
_PROGRAMSHAPEPROTO.fields_by_name['result'].message_type = _SHAPEPROTO
_OPMETADATA.fields_by_name['profile_type'].enum_type = _PROFILETYPE
_CHANNELHANDLE.fields_by_name['type'].enum_type = _CHANNELHANDLE_CHANNELTYPE
_CHANNELHANDLE_CHANNELTYPE.containing_type = _CHANNELHANDLE
_DEVICEASSIGNMENTPROTO_COMPUTATIONDEVICE.containing_type = _DEVICEASSIGNMENTPROTO
_DEVICEASSIGNMENTPROTO.fields_by_name['computation_devices'].message_type = _DEVICEASSIGNMENTPROTO_COMPUTATIONDEVICE
_LITERALPROTO.fields_by_name['shape'].message_type = _SHAPEPROTO
_LITERALPROTO.fields_by_name['tuple_literals'].message_type = _LITERALPROTO
_WINDOW.fields_by_name['dimensions'].message_type = _WINDOWDIMENSION
_TRIANGULARSOLVEOPTIONS.fields_by_name['transpose_a'].enum_type = _TRIANGULARSOLVEOPTIONS_TRANSPOSE
_TRIANGULARSOLVEOPTIONS_TRANSPOSE.containing_type = _TRIANGULARSOLVEOPTIONS
_FRONTENDATTRIBUTES_MAPENTRY.containing_type = _FRONTENDATTRIBUTES
_FRONTENDATTRIBUTES.fields_by_name['map'].message_type = _FRONTENDATTRIBUTES_MAPENTRY
_OPSHARDING.fields_by_name['type'].enum_type = _OPSHARDING_TYPE
_OPSHARDING.fields_by_name['tile_shape'].message_type = _SHAPEPROTO
_OPSHARDING.fields_by_name['tuple_shardings'].message_type = _OPSHARDING
_OPSHARDING.fields_by_name['metadata'].message_type = _OPMETADATA
_OPSHARDING_TYPE.containing_type = _OPSHARDING
_PRECISIONCONFIG.fields_by_name['operand_precision'].enum_type = _PRECISIONCONFIG_PRECISION
_PRECISIONCONFIG_PRECISION.containing_type = _PRECISIONCONFIG
_WHILELOOPBACKENDCONFIG_KNOWNTRIPCOUNT.containing_type = _WHILELOOPBACKENDCONFIG
_WHILELOOPBACKENDCONFIG.fields_by_name['known_trip_count'].message_type = _WHILELOOPBACKENDCONFIG_KNOWNTRIPCOUNT
DESCRIPTOR.message_types_by_name['PaddingConfig'] = _PADDINGCONFIG
DESCRIPTOR.message_types_by_name['TileProto'] = _TILEPROTO
DESCRIPTOR.message_types_by_name['LayoutProto'] = _LAYOUTPROTO
DESCRIPTOR.message_types_by_name['ShapeProto'] = _SHAPEPROTO
DESCRIPTOR.message_types_by_name['ProgramShapeProto'] = _PROGRAMSHAPEPROTO
DESCRIPTOR.message_types_by_name['ComputationStats'] = _COMPUTATIONSTATS
DESCRIPTOR.message_types_by_name['OpMetadata'] = _OPMETADATA
DESCRIPTOR.message_types_by_name['ExecutionProfile'] = _EXECUTIONPROFILE
DESCRIPTOR.message_types_by_name['ExecutionHandle'] = _EXECUTIONHANDLE
DESCRIPTOR.message_types_by_name['GlobalDataHandle'] = _GLOBALDATAHANDLE
DESCRIPTOR.message_types_by_name['DeviceHandle'] = _DEVICEHANDLE
DESCRIPTOR.message_types_by_name['ChannelHandle'] = _CHANNELHANDLE
DESCRIPTOR.message_types_by_name['DeviceAssignmentProto'] = _DEVICEASSIGNMENTPROTO
DESCRIPTOR.message_types_by_name['LiteralProto'] = _LITERALPROTO
DESCRIPTOR.message_types_by_name['WindowDimension'] = _WINDOWDIMENSION
DESCRIPTOR.message_types_by_name['Window'] = _WINDOW
DESCRIPTOR.message_types_by_name['GatherDimensionNumbers'] = _GATHERDIMENSIONNUMBERS
DESCRIPTOR.message_types_by_name['ScatterDimensionNumbers'] = _SCATTERDIMENSIONNUMBERS
DESCRIPTOR.message_types_by_name['ConvolutionDimensionNumbers'] = _CONVOLUTIONDIMENSIONNUMBERS
DESCRIPTOR.message_types_by_name['DotDimensionNumbers'] = _DOTDIMENSIONNUMBERS
DESCRIPTOR.message_types_by_name['TriangularSolveOptions'] = _TRIANGULARSOLVEOPTIONS
DESCRIPTOR.message_types_by_name['CholeskyOptions'] = _CHOLESKYOPTIONS
DESCRIPTOR.message_types_by_name['FrontendAttributes'] = _FRONTENDATTRIBUTES
DESCRIPTOR.message_types_by_name['OpSharding'] = _OPSHARDING
DESCRIPTOR.message_types_by_name['ReplicaGroup'] = _REPLICAGROUP
DESCRIPTOR.message_types_by_name['SourceTarget'] = _SOURCETARGET
DESCRIPTOR.message_types_by_name['PrecisionConfig'] = _PRECISIONCONFIG
DESCRIPTOR.message_types_by_name['ParameterReplication'] = _PARAMETERREPLICATION
DESCRIPTOR.message_types_by_name['WhileLoopBackendConfig'] = _WHILELOOPBACKENDCONFIG
DESCRIPTOR.message_types_by_name['CustomCallOutputOperandAliasing'] = _CUSTOMCALLOUTPUTOPERANDALIASING
DESCRIPTOR.enum_types_by_name['PrimitiveType'] = _PRIMITIVETYPE
DESCRIPTOR.enum_types_by_name['Format'] = _FORMAT
DESCRIPTOR.enum_types_by_name['ProfileType'] = _PROFILETYPE
DESCRIPTOR.enum_types_by_name['PaddingType'] = _PADDINGTYPE
DESCRIPTOR.enum_types_by_name['FftType'] = _FFTTYPE
DESCRIPTOR.enum_types_by_name['RandomDistribution'] = _RANDOMDISTRIBUTION
DESCRIPTOR.enum_types_by_name['RandomAlgorithm'] = _RANDOMALGORITHM
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PaddingConfig = _reflection.GeneratedProtocolMessageType('PaddingConfig', (_message.Message,), {

  'PaddingConfigDimension' : _reflection.GeneratedProtocolMessageType('PaddingConfigDimension', (_message.Message,), {
    'DESCRIPTOR' : _PADDINGCONFIG_PADDINGCONFIGDIMENSION,
    '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
    # @@protoc_insertion_point(class_scope:xla.PaddingConfig.PaddingConfigDimension)
    })
  ,
  'DESCRIPTOR' : _PADDINGCONFIG,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.PaddingConfig)
  })
_sym_db.RegisterMessage(PaddingConfig)
_sym_db.RegisterMessage(PaddingConfig.PaddingConfigDimension)

TileProto = _reflection.GeneratedProtocolMessageType('TileProto', (_message.Message,), {
  'DESCRIPTOR' : _TILEPROTO,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.TileProto)
  })
_sym_db.RegisterMessage(TileProto)

LayoutProto = _reflection.GeneratedProtocolMessageType('LayoutProto', (_message.Message,), {
  'DESCRIPTOR' : _LAYOUTPROTO,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.LayoutProto)
  })
_sym_db.RegisterMessage(LayoutProto)

ShapeProto = _reflection.GeneratedProtocolMessageType('ShapeProto', (_message.Message,), {
  'DESCRIPTOR' : _SHAPEPROTO,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.ShapeProto)
  })
_sym_db.RegisterMessage(ShapeProto)

ProgramShapeProto = _reflection.GeneratedProtocolMessageType('ProgramShapeProto', (_message.Message,), {
  'DESCRIPTOR' : _PROGRAMSHAPEPROTO,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.ProgramShapeProto)
  })
_sym_db.RegisterMessage(ProgramShapeProto)

ComputationStats = _reflection.GeneratedProtocolMessageType('ComputationStats', (_message.Message,), {
  'DESCRIPTOR' : _COMPUTATIONSTATS,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.ComputationStats)
  })
_sym_db.RegisterMessage(ComputationStats)

OpMetadata = _reflection.GeneratedProtocolMessageType('OpMetadata', (_message.Message,), {
  'DESCRIPTOR' : _OPMETADATA,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.OpMetadata)
  })
_sym_db.RegisterMessage(OpMetadata)

ExecutionProfile = _reflection.GeneratedProtocolMessageType('ExecutionProfile', (_message.Message,), {
  'DESCRIPTOR' : _EXECUTIONPROFILE,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.ExecutionProfile)
  })
_sym_db.RegisterMessage(ExecutionProfile)

ExecutionHandle = _reflection.GeneratedProtocolMessageType('ExecutionHandle', (_message.Message,), {
  'DESCRIPTOR' : _EXECUTIONHANDLE,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.ExecutionHandle)
  })
_sym_db.RegisterMessage(ExecutionHandle)

GlobalDataHandle = _reflection.GeneratedProtocolMessageType('GlobalDataHandle', (_message.Message,), {
  'DESCRIPTOR' : _GLOBALDATAHANDLE,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.GlobalDataHandle)
  })
_sym_db.RegisterMessage(GlobalDataHandle)

DeviceHandle = _reflection.GeneratedProtocolMessageType('DeviceHandle', (_message.Message,), {
  'DESCRIPTOR' : _DEVICEHANDLE,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.DeviceHandle)
  })
_sym_db.RegisterMessage(DeviceHandle)

ChannelHandle = _reflection.GeneratedProtocolMessageType('ChannelHandle', (_message.Message,), {
  'DESCRIPTOR' : _CHANNELHANDLE,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.ChannelHandle)
  })
_sym_db.RegisterMessage(ChannelHandle)

DeviceAssignmentProto = _reflection.GeneratedProtocolMessageType('DeviceAssignmentProto', (_message.Message,), {

  'ComputationDevice' : _reflection.GeneratedProtocolMessageType('ComputationDevice', (_message.Message,), {
    'DESCRIPTOR' : _DEVICEASSIGNMENTPROTO_COMPUTATIONDEVICE,
    '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
    # @@protoc_insertion_point(class_scope:xla.DeviceAssignmentProto.ComputationDevice)
    })
  ,
  'DESCRIPTOR' : _DEVICEASSIGNMENTPROTO,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.DeviceAssignmentProto)
  })
_sym_db.RegisterMessage(DeviceAssignmentProto)
_sym_db.RegisterMessage(DeviceAssignmentProto.ComputationDevice)

LiteralProto = _reflection.GeneratedProtocolMessageType('LiteralProto', (_message.Message,), {
  'DESCRIPTOR' : _LITERALPROTO,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.LiteralProto)
  })
_sym_db.RegisterMessage(LiteralProto)

WindowDimension = _reflection.GeneratedProtocolMessageType('WindowDimension', (_message.Message,), {
  'DESCRIPTOR' : _WINDOWDIMENSION,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.WindowDimension)
  })
_sym_db.RegisterMessage(WindowDimension)

Window = _reflection.GeneratedProtocolMessageType('Window', (_message.Message,), {
  'DESCRIPTOR' : _WINDOW,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.Window)
  })
_sym_db.RegisterMessage(Window)

GatherDimensionNumbers = _reflection.GeneratedProtocolMessageType('GatherDimensionNumbers', (_message.Message,), {
  'DESCRIPTOR' : _GATHERDIMENSIONNUMBERS,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.GatherDimensionNumbers)
  })
_sym_db.RegisterMessage(GatherDimensionNumbers)

ScatterDimensionNumbers = _reflection.GeneratedProtocolMessageType('ScatterDimensionNumbers', (_message.Message,), {
  'DESCRIPTOR' : _SCATTERDIMENSIONNUMBERS,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.ScatterDimensionNumbers)
  })
_sym_db.RegisterMessage(ScatterDimensionNumbers)

ConvolutionDimensionNumbers = _reflection.GeneratedProtocolMessageType('ConvolutionDimensionNumbers', (_message.Message,), {
  'DESCRIPTOR' : _CONVOLUTIONDIMENSIONNUMBERS,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.ConvolutionDimensionNumbers)
  })
_sym_db.RegisterMessage(ConvolutionDimensionNumbers)

DotDimensionNumbers = _reflection.GeneratedProtocolMessageType('DotDimensionNumbers', (_message.Message,), {
  'DESCRIPTOR' : _DOTDIMENSIONNUMBERS,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.DotDimensionNumbers)
  })
_sym_db.RegisterMessage(DotDimensionNumbers)

TriangularSolveOptions = _reflection.GeneratedProtocolMessageType('TriangularSolveOptions', (_message.Message,), {
  'DESCRIPTOR' : _TRIANGULARSOLVEOPTIONS,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.TriangularSolveOptions)
  })
_sym_db.RegisterMessage(TriangularSolveOptions)

CholeskyOptions = _reflection.GeneratedProtocolMessageType('CholeskyOptions', (_message.Message,), {
  'DESCRIPTOR' : _CHOLESKYOPTIONS,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.CholeskyOptions)
  })
_sym_db.RegisterMessage(CholeskyOptions)

FrontendAttributes = _reflection.GeneratedProtocolMessageType('FrontendAttributes', (_message.Message,), {

  'MapEntry' : _reflection.GeneratedProtocolMessageType('MapEntry', (_message.Message,), {
    'DESCRIPTOR' : _FRONTENDATTRIBUTES_MAPENTRY,
    '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
    # @@protoc_insertion_point(class_scope:xla.FrontendAttributes.MapEntry)
    })
  ,
  'DESCRIPTOR' : _FRONTENDATTRIBUTES,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.FrontendAttributes)
  })
_sym_db.RegisterMessage(FrontendAttributes)
_sym_db.RegisterMessage(FrontendAttributes.MapEntry)

OpSharding = _reflection.GeneratedProtocolMessageType('OpSharding', (_message.Message,), {
  'DESCRIPTOR' : _OPSHARDING,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.OpSharding)
  })
_sym_db.RegisterMessage(OpSharding)

ReplicaGroup = _reflection.GeneratedProtocolMessageType('ReplicaGroup', (_message.Message,), {
  'DESCRIPTOR' : _REPLICAGROUP,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.ReplicaGroup)
  })
_sym_db.RegisterMessage(ReplicaGroup)

SourceTarget = _reflection.GeneratedProtocolMessageType('SourceTarget', (_message.Message,), {
  'DESCRIPTOR' : _SOURCETARGET,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.SourceTarget)
  })
_sym_db.RegisterMessage(SourceTarget)

PrecisionConfig = _reflection.GeneratedProtocolMessageType('PrecisionConfig', (_message.Message,), {
  'DESCRIPTOR' : _PRECISIONCONFIG,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.PrecisionConfig)
  })
_sym_db.RegisterMessage(PrecisionConfig)

ParameterReplication = _reflection.GeneratedProtocolMessageType('ParameterReplication', (_message.Message,), {
  'DESCRIPTOR' : _PARAMETERREPLICATION,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.ParameterReplication)
  })
_sym_db.RegisterMessage(ParameterReplication)

WhileLoopBackendConfig = _reflection.GeneratedProtocolMessageType('WhileLoopBackendConfig', (_message.Message,), {

  'KnownTripCount' : _reflection.GeneratedProtocolMessageType('KnownTripCount', (_message.Message,), {
    'DESCRIPTOR' : _WHILELOOPBACKENDCONFIG_KNOWNTRIPCOUNT,
    '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
    # @@protoc_insertion_point(class_scope:xla.WhileLoopBackendConfig.KnownTripCount)
    })
  ,
  'DESCRIPTOR' : _WHILELOOPBACKENDCONFIG,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.WhileLoopBackendConfig)
  })
_sym_db.RegisterMessage(WhileLoopBackendConfig)
_sym_db.RegisterMessage(WhileLoopBackendConfig.KnownTripCount)

CustomCallOutputOperandAliasing = _reflection.GeneratedProtocolMessageType('CustomCallOutputOperandAliasing', (_message.Message,), {
  'DESCRIPTOR' : _CUSTOMCALLOUTPUTOPERANDALIASING,
  '__module__' : 'tensorflow.compiler.xla.xla_data_pb2'
  # @@protoc_insertion_point(class_scope:xla.CustomCallOutputOperandAliasing)
  })
_sym_db.RegisterMessage(CustomCallOutputOperandAliasing)


DESCRIPTOR._options = None
_FRONTENDATTRIBUTES_MAPENTRY._options = None
# @@protoc_insertion_point(module_scope)
