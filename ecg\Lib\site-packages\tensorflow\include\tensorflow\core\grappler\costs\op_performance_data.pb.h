// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/grappler/costs/op_performance_data.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/protobuf/device_properties.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[9]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
namespace tensorflow {
class LogNormalDistribution;
class LogNormalDistributionDefaultTypeInternal;
extern LogNormalDistributionDefaultTypeInternal _LogNormalDistribution_default_instance_;
class NormalDistribution;
class NormalDistributionDefaultTypeInternal;
extern NormalDistributionDefaultTypeInternal _NormalDistribution_default_instance_;
class OpInfo;
class OpInfoDefaultTypeInternal;
extern OpInfoDefaultTypeInternal _OpInfo_default_instance_;
class OpInfo_AttrEntry_DoNotUse;
class OpInfo_AttrEntry_DoNotUseDefaultTypeInternal;
extern OpInfo_AttrEntry_DoNotUseDefaultTypeInternal _OpInfo_AttrEntry_DoNotUse_default_instance_;
class OpInfo_TensorProperties;
class OpInfo_TensorPropertiesDefaultTypeInternal;
extern OpInfo_TensorPropertiesDefaultTypeInternal _OpInfo_TensorProperties_default_instance_;
class OpPerformance;
class OpPerformanceDefaultTypeInternal;
extern OpPerformanceDefaultTypeInternal _OpPerformance_default_instance_;
class OpPerformanceList;
class OpPerformanceListDefaultTypeInternal;
extern OpPerformanceListDefaultTypeInternal _OpPerformanceList_default_instance_;
class OpPerformance_OpMemory;
class OpPerformance_OpMemoryDefaultTypeInternal;
extern OpPerformance_OpMemoryDefaultTypeInternal _OpPerformance_OpMemory_default_instance_;
class SessionInfo;
class SessionInfoDefaultTypeInternal;
extern SessionInfoDefaultTypeInternal _SessionInfo_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::LogNormalDistribution* Arena::CreateMaybeMessage<::tensorflow::LogNormalDistribution>(Arena*);
template<> ::tensorflow::NormalDistribution* Arena::CreateMaybeMessage<::tensorflow::NormalDistribution>(Arena*);
template<> ::tensorflow::OpInfo* Arena::CreateMaybeMessage<::tensorflow::OpInfo>(Arena*);
template<> ::tensorflow::OpInfo_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::OpInfo_AttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::OpInfo_TensorProperties* Arena::CreateMaybeMessage<::tensorflow::OpInfo_TensorProperties>(Arena*);
template<> ::tensorflow::OpPerformance* Arena::CreateMaybeMessage<::tensorflow::OpPerformance>(Arena*);
template<> ::tensorflow::OpPerformanceList* Arena::CreateMaybeMessage<::tensorflow::OpPerformanceList>(Arena*);
template<> ::tensorflow::OpPerformance_OpMemory* Arena::CreateMaybeMessage<::tensorflow::OpPerformance_OpMemory>(Arena*);
template<> ::tensorflow::SessionInfo* Arena::CreateMaybeMessage<::tensorflow::SessionInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class SessionInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SessionInfo) */ {
 public:
  SessionInfo();
  virtual ~SessionInfo();

  SessionInfo(const SessionInfo& from);
  SessionInfo(SessionInfo&& from) noexcept
    : SessionInfo() {
    *this = ::std::move(from);
  }

  inline SessionInfo& operator=(const SessionInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline SessionInfo& operator=(SessionInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SessionInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SessionInfo* internal_default_instance() {
    return reinterpret_cast<const SessionInfo*>(
               &_SessionInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SessionInfo& a, SessionInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(SessionInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SessionInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SessionInfo* New() const final {
    return CreateMaybeMessage<SessionInfo>(nullptr);
  }

  SessionInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SessionInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SessionInfo& from);
  void MergeFrom(const SessionInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SessionInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SessionInfo";
  }
  protected:
  explicit SessionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIntraOpParallelismFieldNumber = 1,
  };
  // int64 intra_op_parallelism = 1;
  void clear_intra_op_parallelism();
  ::PROTOBUF_NAMESPACE_ID::int64 intra_op_parallelism() const;
  void set_intra_op_parallelism(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.SessionInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 intra_op_parallelism_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpInfo_AttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<OpInfo_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<OpInfo_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  OpInfo_AttrEntry_DoNotUse();
  OpInfo_AttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const OpInfo_AttrEntry_DoNotUse& other);
  static const OpInfo_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const OpInfo_AttrEntry_DoNotUse*>(&_OpInfo_AttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.OpInfo.AttrEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto.file_level_metadata[1];
  }

  public:
};

// -------------------------------------------------------------------

class OpInfo_TensorProperties :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpInfo.TensorProperties) */ {
 public:
  OpInfo_TensorProperties();
  virtual ~OpInfo_TensorProperties();

  OpInfo_TensorProperties(const OpInfo_TensorProperties& from);
  OpInfo_TensorProperties(OpInfo_TensorProperties&& from) noexcept
    : OpInfo_TensorProperties() {
    *this = ::std::move(from);
  }

  inline OpInfo_TensorProperties& operator=(const OpInfo_TensorProperties& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpInfo_TensorProperties& operator=(OpInfo_TensorProperties&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpInfo_TensorProperties& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpInfo_TensorProperties* internal_default_instance() {
    return reinterpret_cast<const OpInfo_TensorProperties*>(
               &_OpInfo_TensorProperties_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(OpInfo_TensorProperties& a, OpInfo_TensorProperties& b) {
    a.Swap(&b);
  }
  inline void Swap(OpInfo_TensorProperties* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpInfo_TensorProperties* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpInfo_TensorProperties* New() const final {
    return CreateMaybeMessage<OpInfo_TensorProperties>(nullptr);
  }

  OpInfo_TensorProperties* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpInfo_TensorProperties>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpInfo_TensorProperties& from);
  void MergeFrom(const OpInfo_TensorProperties& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpInfo_TensorProperties* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpInfo.TensorProperties";
  }
  protected:
  explicit OpInfo_TensorProperties(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kValueFieldNumber = 3,
    kDtypeFieldNumber = 1,
  };
  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.TensorProto value = 3;
  bool has_value() const;
  void clear_value();
  const ::tensorflow::TensorProto& value() const;
  ::tensorflow::TensorProto* release_value();
  ::tensorflow::TensorProto* mutable_value();
  void set_allocated_value(::tensorflow::TensorProto* value);
  void unsafe_arena_set_allocated_value(
      ::tensorflow::TensorProto* value);
  ::tensorflow::TensorProto* unsafe_arena_release_value();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpInfo.TensorProperties)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorShapeProto* shape_;
  ::tensorflow::TensorProto* value_;
  int dtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpInfo) */ {
 public:
  OpInfo();
  virtual ~OpInfo();

  OpInfo(const OpInfo& from);
  OpInfo(OpInfo&& from) noexcept
    : OpInfo() {
    *this = ::std::move(from);
  }

  inline OpInfo& operator=(const OpInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpInfo& operator=(OpInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpInfo* internal_default_instance() {
    return reinterpret_cast<const OpInfo*>(
               &_OpInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(OpInfo& a, OpInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(OpInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpInfo* New() const final {
    return CreateMaybeMessage<OpInfo>(nullptr);
  }

  OpInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpInfo& from);
  void MergeFrom(const OpInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpInfo";
  }
  protected:
  explicit OpInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef OpInfo_TensorProperties TensorProperties;

  // accessors -------------------------------------------------------

  enum : int {
    kAttrFieldNumber = 2,
    kInputsFieldNumber = 3,
    kOutputsFieldNumber = 5,
    kOpFieldNumber = 1,
    kDeviceFieldNumber = 4,
    kSessionInfoFieldNumber = 6,
  };
  // map<string, .tensorflow.AttrValue> attr = 2;
  int attr_size() const;
  void clear_attr();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // repeated .tensorflow.OpInfo.TensorProperties inputs = 3;
  int inputs_size() const;
  void clear_inputs();
  ::tensorflow::OpInfo_TensorProperties* mutable_inputs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
      mutable_inputs();
  const ::tensorflow::OpInfo_TensorProperties& inputs(int index) const;
  ::tensorflow::OpInfo_TensorProperties* add_inputs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
      inputs() const;

  // repeated .tensorflow.OpInfo.TensorProperties outputs = 5;
  int outputs_size() const;
  void clear_outputs();
  ::tensorflow::OpInfo_TensorProperties* mutable_outputs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
      mutable_outputs();
  const ::tensorflow::OpInfo_TensorProperties& outputs(int index) const;
  ::tensorflow::OpInfo_TensorProperties* add_outputs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
      outputs() const;

  // string op = 1;
  void clear_op();
  const std::string& op() const;
  void set_op(const std::string& value);
  void set_op(std::string&& value);
  void set_op(const char* value);
  void set_op(const char* value, size_t size);
  std::string* mutable_op();
  std::string* release_op();
  void set_allocated_op(std::string* op);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_op();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op(
      std::string* op);

  // .tensorflow.DeviceProperties device = 4;
  bool has_device() const;
  void clear_device();
  const ::tensorflow::DeviceProperties& device() const;
  ::tensorflow::DeviceProperties* release_device();
  ::tensorflow::DeviceProperties* mutable_device();
  void set_allocated_device(::tensorflow::DeviceProperties* device);
  void unsafe_arena_set_allocated_device(
      ::tensorflow::DeviceProperties* device);
  ::tensorflow::DeviceProperties* unsafe_arena_release_device();

  // .tensorflow.SessionInfo session_info = 6;
  bool has_session_info() const;
  void clear_session_info();
  const ::tensorflow::SessionInfo& session_info() const;
  ::tensorflow::SessionInfo* release_session_info();
  ::tensorflow::SessionInfo* mutable_session_info();
  void set_allocated_session_info(::tensorflow::SessionInfo* session_info);
  void unsafe_arena_set_allocated_session_info(
      ::tensorflow::SessionInfo* session_info);
  ::tensorflow::SessionInfo* unsafe_arena_release_session_info();

  // @@protoc_insertion_point(class_scope:tensorflow.OpInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      OpInfo_AttrEntry_DoNotUse,
      std::string, ::tensorflow::AttrValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attr_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties > inputs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties > outputs_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_;
  ::tensorflow::DeviceProperties* device_;
  ::tensorflow::SessionInfo* session_info_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class NormalDistribution :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NormalDistribution) */ {
 public:
  NormalDistribution();
  virtual ~NormalDistribution();

  NormalDistribution(const NormalDistribution& from);
  NormalDistribution(NormalDistribution&& from) noexcept
    : NormalDistribution() {
    *this = ::std::move(from);
  }

  inline NormalDistribution& operator=(const NormalDistribution& from) {
    CopyFrom(from);
    return *this;
  }
  inline NormalDistribution& operator=(NormalDistribution&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NormalDistribution& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NormalDistribution* internal_default_instance() {
    return reinterpret_cast<const NormalDistribution*>(
               &_NormalDistribution_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(NormalDistribution& a, NormalDistribution& b) {
    a.Swap(&b);
  }
  inline void Swap(NormalDistribution* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NormalDistribution* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NormalDistribution* New() const final {
    return CreateMaybeMessage<NormalDistribution>(nullptr);
  }

  NormalDistribution* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NormalDistribution>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NormalDistribution& from);
  void MergeFrom(const NormalDistribution& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NormalDistribution* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NormalDistribution";
  }
  protected:
  explicit NormalDistribution(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMuFieldNumber = 1,
    kSigmaFieldNumber = 2,
  };
  // double mu = 1;
  void clear_mu();
  double mu() const;
  void set_mu(double value);

  // double sigma = 2;
  void clear_sigma();
  double sigma() const;
  void set_sigma(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.NormalDistribution)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double mu_;
  double sigma_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class LogNormalDistribution :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LogNormalDistribution) */ {
 public:
  LogNormalDistribution();
  virtual ~LogNormalDistribution();

  LogNormalDistribution(const LogNormalDistribution& from);
  LogNormalDistribution(LogNormalDistribution&& from) noexcept
    : LogNormalDistribution() {
    *this = ::std::move(from);
  }

  inline LogNormalDistribution& operator=(const LogNormalDistribution& from) {
    CopyFrom(from);
    return *this;
  }
  inline LogNormalDistribution& operator=(LogNormalDistribution&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LogNormalDistribution& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LogNormalDistribution* internal_default_instance() {
    return reinterpret_cast<const LogNormalDistribution*>(
               &_LogNormalDistribution_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(LogNormalDistribution& a, LogNormalDistribution& b) {
    a.Swap(&b);
  }
  inline void Swap(LogNormalDistribution* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LogNormalDistribution* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LogNormalDistribution* New() const final {
    return CreateMaybeMessage<LogNormalDistribution>(nullptr);
  }

  LogNormalDistribution* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LogNormalDistribution>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LogNormalDistribution& from);
  void MergeFrom(const LogNormalDistribution& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LogNormalDistribution* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LogNormalDistribution";
  }
  protected:
  explicit LogNormalDistribution(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMuFieldNumber = 1,
    kSigmaFieldNumber = 2,
  };
  // double mu = 1;
  void clear_mu();
  double mu() const;
  void set_mu(double value);

  // double sigma = 2;
  void clear_sigma();
  double sigma() const;
  void set_sigma(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.LogNormalDistribution)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double mu_;
  double sigma_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpPerformance_OpMemory :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpPerformance.OpMemory) */ {
 public:
  OpPerformance_OpMemory();
  virtual ~OpPerformance_OpMemory();

  OpPerformance_OpMemory(const OpPerformance_OpMemory& from);
  OpPerformance_OpMemory(OpPerformance_OpMemory&& from) noexcept
    : OpPerformance_OpMemory() {
    *this = ::std::move(from);
  }

  inline OpPerformance_OpMemory& operator=(const OpPerformance_OpMemory& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpPerformance_OpMemory& operator=(OpPerformance_OpMemory&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpPerformance_OpMemory& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpPerformance_OpMemory* internal_default_instance() {
    return reinterpret_cast<const OpPerformance_OpMemory*>(
               &_OpPerformance_OpMemory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(OpPerformance_OpMemory& a, OpPerformance_OpMemory& b) {
    a.Swap(&b);
  }
  inline void Swap(OpPerformance_OpMemory* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpPerformance_OpMemory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpPerformance_OpMemory* New() const final {
    return CreateMaybeMessage<OpPerformance_OpMemory>(nullptr);
  }

  OpPerformance_OpMemory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpPerformance_OpMemory>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpPerformance_OpMemory& from);
  void MergeFrom(const OpPerformance_OpMemory& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpPerformance_OpMemory* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpPerformance.OpMemory";
  }
  protected:
  explicit OpPerformance_OpMemory(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputMemoryFieldNumber = 1,
    kTempMemoryFieldNumber = 2,
    kDeviceTempMemoryFieldNumber = 3,
    kPersistentMemoryFieldNumber = 4,
    kDevicePersistentMemoryFieldNumber = 5,
  };
  // repeated int64 output_memory = 1;
  int output_memory_size() const;
  void clear_output_memory();
  ::PROTOBUF_NAMESPACE_ID::int64 output_memory(int index) const;
  void set_output_memory(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_output_memory(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      output_memory() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_output_memory();

  // int64 temp_memory = 2;
  void clear_temp_memory();
  ::PROTOBUF_NAMESPACE_ID::int64 temp_memory() const;
  void set_temp_memory(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 device_temp_memory = 3 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_temp_memory();
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int64 device_temp_memory() const;
  PROTOBUF_DEPRECATED void set_device_temp_memory(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 persistent_memory = 4;
  void clear_persistent_memory();
  ::PROTOBUF_NAMESPACE_ID::int64 persistent_memory() const;
  void set_persistent_memory(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 device_persistent_memory = 5 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_persistent_memory();
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int64 device_persistent_memory() const;
  PROTOBUF_DEPRECATED void set_device_persistent_memory(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpPerformance.OpMemory)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > output_memory_;
  mutable std::atomic<int> _output_memory_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 temp_memory_;
  ::PROTOBUF_NAMESPACE_ID::int64 device_temp_memory_;
  ::PROTOBUF_NAMESPACE_ID::int64 persistent_memory_;
  ::PROTOBUF_NAMESPACE_ID::int64 device_persistent_memory_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpPerformance :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpPerformance) */ {
 public:
  OpPerformance();
  virtual ~OpPerformance();

  OpPerformance(const OpPerformance& from);
  OpPerformance(OpPerformance&& from) noexcept
    : OpPerformance() {
    *this = ::std::move(from);
  }

  inline OpPerformance& operator=(const OpPerformance& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpPerformance& operator=(OpPerformance&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpPerformance& default_instance();

  enum ExecutionTimeCase {
    kExecutionTimeNormal = 10,
    kExecutionTimeLogNormal = 11,
    EXECUTION_TIME_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpPerformance* internal_default_instance() {
    return reinterpret_cast<const OpPerformance*>(
               &_OpPerformance_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(OpPerformance& a, OpPerformance& b) {
    a.Swap(&b);
  }
  inline void Swap(OpPerformance* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpPerformance* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpPerformance* New() const final {
    return CreateMaybeMessage<OpPerformance>(nullptr);
  }

  OpPerformance* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpPerformance>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpPerformance& from);
  void MergeFrom(const OpPerformance& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpPerformance* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpPerformance";
  }
  protected:
  explicit OpPerformance(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef OpPerformance_OpMemory OpMemory;

  // accessors -------------------------------------------------------

  enum : int {
    kNodeFieldNumber = 5,
    kOpFieldNumber = 1,
    kOpMemoryFieldNumber = 9,
    kSessionInfoFieldNumber = 12,
    kTemporaryMemorySizeFieldNumber = 2,
    kComputeCostFieldNumber = 3,
    kComputeEfficiencyFieldNumber = 4,
    kComputeTimeFieldNumber = 6,
    kMemoryTimeFieldNumber = 7,
    kMemoryEfficiencyFieldNumber = 8,
    kExecutionTimeNormalFieldNumber = 10,
    kExecutionTimeLogNormalFieldNumber = 11,
  };
  // string node = 5;
  void clear_node();
  const std::string& node() const;
  void set_node(const std::string& value);
  void set_node(std::string&& value);
  void set_node(const char* value);
  void set_node(const char* value, size_t size);
  std::string* mutable_node();
  std::string* release_node();
  void set_allocated_node(std::string* node);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_node();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node(
      std::string* node);

  // .tensorflow.OpInfo op = 1;
  bool has_op() const;
  void clear_op();
  const ::tensorflow::OpInfo& op() const;
  ::tensorflow::OpInfo* release_op();
  ::tensorflow::OpInfo* mutable_op();
  void set_allocated_op(::tensorflow::OpInfo* op);
  void unsafe_arena_set_allocated_op(
      ::tensorflow::OpInfo* op);
  ::tensorflow::OpInfo* unsafe_arena_release_op();

  // .tensorflow.OpPerformance.OpMemory op_memory = 9;
  bool has_op_memory() const;
  void clear_op_memory();
  const ::tensorflow::OpPerformance_OpMemory& op_memory() const;
  ::tensorflow::OpPerformance_OpMemory* release_op_memory();
  ::tensorflow::OpPerformance_OpMemory* mutable_op_memory();
  void set_allocated_op_memory(::tensorflow::OpPerformance_OpMemory* op_memory);
  void unsafe_arena_set_allocated_op_memory(
      ::tensorflow::OpPerformance_OpMemory* op_memory);
  ::tensorflow::OpPerformance_OpMemory* unsafe_arena_release_op_memory();

  // .tensorflow.SessionInfo session_info = 12 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_session_info() const;
  PROTOBUF_DEPRECATED void clear_session_info();
  PROTOBUF_DEPRECATED const ::tensorflow::SessionInfo& session_info() const;
  PROTOBUF_DEPRECATED ::tensorflow::SessionInfo* release_session_info();
  PROTOBUF_DEPRECATED ::tensorflow::SessionInfo* mutable_session_info();
  PROTOBUF_DEPRECATED void set_allocated_session_info(::tensorflow::SessionInfo* session_info);
  PROTOBUF_DEPRECATED void unsafe_arena_set_allocated_session_info(
      ::tensorflow::SessionInfo* session_info);
  PROTOBUF_DEPRECATED ::tensorflow::SessionInfo* unsafe_arena_release_session_info();

  // int64 temporary_memory_size = 2;
  void clear_temporary_memory_size();
  ::PROTOBUF_NAMESPACE_ID::int64 temporary_memory_size() const;
  void set_temporary_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 compute_cost = 3;
  void clear_compute_cost();
  ::PROTOBUF_NAMESPACE_ID::int64 compute_cost() const;
  void set_compute_cost(::PROTOBUF_NAMESPACE_ID::int64 value);

  // double compute_efficiency = 4;
  void clear_compute_efficiency();
  double compute_efficiency() const;
  void set_compute_efficiency(double value);

  // int64 compute_time = 6;
  void clear_compute_time();
  ::PROTOBUF_NAMESPACE_ID::int64 compute_time() const;
  void set_compute_time(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 memory_time = 7;
  void clear_memory_time();
  ::PROTOBUF_NAMESPACE_ID::int64 memory_time() const;
  void set_memory_time(::PROTOBUF_NAMESPACE_ID::int64 value);

  // double memory_efficiency = 8;
  void clear_memory_efficiency();
  double memory_efficiency() const;
  void set_memory_efficiency(double value);

  // .tensorflow.NormalDistribution execution_time_normal = 10;
  bool has_execution_time_normal() const;
  void clear_execution_time_normal();
  const ::tensorflow::NormalDistribution& execution_time_normal() const;
  ::tensorflow::NormalDistribution* release_execution_time_normal();
  ::tensorflow::NormalDistribution* mutable_execution_time_normal();
  void set_allocated_execution_time_normal(::tensorflow::NormalDistribution* execution_time_normal);
  void unsafe_arena_set_allocated_execution_time_normal(
      ::tensorflow::NormalDistribution* execution_time_normal);
  ::tensorflow::NormalDistribution* unsafe_arena_release_execution_time_normal();

  // .tensorflow.LogNormalDistribution execution_time_log_normal = 11;
  bool has_execution_time_log_normal() const;
  void clear_execution_time_log_normal();
  const ::tensorflow::LogNormalDistribution& execution_time_log_normal() const;
  ::tensorflow::LogNormalDistribution* release_execution_time_log_normal();
  ::tensorflow::LogNormalDistribution* mutable_execution_time_log_normal();
  void set_allocated_execution_time_log_normal(::tensorflow::LogNormalDistribution* execution_time_log_normal);
  void unsafe_arena_set_allocated_execution_time_log_normal(
      ::tensorflow::LogNormalDistribution* execution_time_log_normal);
  ::tensorflow::LogNormalDistribution* unsafe_arena_release_execution_time_log_normal();

  void clear_execution_time();
  ExecutionTimeCase execution_time_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.OpPerformance)
 private:
  class _Internal;
  void set_has_execution_time_normal();
  void set_has_execution_time_log_normal();

  inline bool has_execution_time() const;
  inline void clear_has_execution_time();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_;
  ::tensorflow::OpInfo* op_;
  ::tensorflow::OpPerformance_OpMemory* op_memory_;
  ::tensorflow::SessionInfo* session_info_;
  ::PROTOBUF_NAMESPACE_ID::int64 temporary_memory_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 compute_cost_;
  double compute_efficiency_;
  ::PROTOBUF_NAMESPACE_ID::int64 compute_time_;
  ::PROTOBUF_NAMESPACE_ID::int64 memory_time_;
  double memory_efficiency_;
  union ExecutionTimeUnion {
    ExecutionTimeUnion() {}
    ::tensorflow::NormalDistribution* execution_time_normal_;
    ::tensorflow::LogNormalDistribution* execution_time_log_normal_;
  } execution_time_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// -------------------------------------------------------------------

class OpPerformanceList :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpPerformanceList) */ {
 public:
  OpPerformanceList();
  virtual ~OpPerformanceList();

  OpPerformanceList(const OpPerformanceList& from);
  OpPerformanceList(OpPerformanceList&& from) noexcept
    : OpPerformanceList() {
    *this = ::std::move(from);
  }

  inline OpPerformanceList& operator=(const OpPerformanceList& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpPerformanceList& operator=(OpPerformanceList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpPerformanceList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpPerformanceList* internal_default_instance() {
    return reinterpret_cast<const OpPerformanceList*>(
               &_OpPerformanceList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(OpPerformanceList& a, OpPerformanceList& b) {
    a.Swap(&b);
  }
  inline void Swap(OpPerformanceList* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpPerformanceList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpPerformanceList* New() const final {
    return CreateMaybeMessage<OpPerformanceList>(nullptr);
  }

  OpPerformanceList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpPerformanceList>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpPerformanceList& from);
  void MergeFrom(const OpPerformanceList& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpPerformanceList* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpPerformanceList";
  }
  protected:
  explicit OpPerformanceList(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpPerformanceFieldNumber = 1,
  };
  // repeated .tensorflow.OpPerformance op_performance = 1;
  int op_performance_size() const;
  void clear_op_performance();
  ::tensorflow::OpPerformance* mutable_op_performance(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpPerformance >*
      mutable_op_performance();
  const ::tensorflow::OpPerformance& op_performance(int index) const;
  ::tensorflow::OpPerformance* add_op_performance();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpPerformance >&
      op_performance() const;

  // @@protoc_insertion_point(class_scope:tensorflow.OpPerformanceList)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpPerformance > op_performance_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SessionInfo

// int64 intra_op_parallelism = 1;
inline void SessionInfo::clear_intra_op_parallelism() {
  intra_op_parallelism_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SessionInfo::intra_op_parallelism() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionInfo.intra_op_parallelism)
  return intra_op_parallelism_;
}
inline void SessionInfo::set_intra_op_parallelism(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  intra_op_parallelism_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SessionInfo.intra_op_parallelism)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// OpInfo_TensorProperties

// .tensorflow.DataType dtype = 1;
inline void OpInfo_TensorProperties::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType OpInfo_TensorProperties::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.TensorProperties.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void OpInfo_TensorProperties::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpInfo.TensorProperties.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool OpInfo_TensorProperties::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& OpInfo_TensorProperties::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.TensorProperties.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* OpInfo_TensorProperties::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.TensorProperties.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* OpInfo_TensorProperties::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpInfo.TensorProperties.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* OpInfo_TensorProperties::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.TensorProperties.shape)
  return shape_;
}
inline void OpInfo_TensorProperties::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.TensorProperties.shape)
}

// .tensorflow.TensorProto value = 3;
inline bool OpInfo_TensorProperties::has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline const ::tensorflow::TensorProto& OpInfo_TensorProperties::value() const {
  const ::tensorflow::TensorProto* p = value_;
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.TensorProperties.value)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* OpInfo_TensorProperties::release_value() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.TensorProperties.value)
  
  ::tensorflow::TensorProto* temp = value_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  value_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* OpInfo_TensorProperties::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpInfo.TensorProperties.value)
  
  ::tensorflow::TensorProto* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* OpInfo_TensorProperties::mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.TensorProperties.value)
  return value_;
}
inline void OpInfo_TensorProperties::set_allocated_value(::tensorflow::TensorProto* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.TensorProperties.value)
}

// -------------------------------------------------------------------

// OpInfo

// string op = 1;
inline void OpInfo::clear_op() {
  op_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpInfo::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.op)
  return op_.Get();
}
inline void OpInfo::set_op(const std::string& value) {
  
  op_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpInfo.op)
}
inline void OpInfo::set_op(std::string&& value) {
  
  op_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpInfo.op)
}
inline void OpInfo::set_op(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpInfo.op)
}
inline void OpInfo::set_op(const char* value,
    size_t size) {
  
  op_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpInfo.op)
}
inline std::string* OpInfo::mutable_op() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.op)
  return op_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpInfo::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.op)
  
  return op_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpInfo::set_allocated_op(std::string* op) {
  if (op != nullptr) {
    
  } else {
    
  }
  op_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.op)
}
inline std::string* OpInfo::unsafe_arena_release_op() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpInfo.op)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return op_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpInfo::unsafe_arena_set_allocated_op(
    std::string* op) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (op != nullptr) {
    
  } else {
    
  }
  op_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      op, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpInfo.op)
}

// map<string, .tensorflow.AttrValue> attr = 2;
inline int OpInfo::attr_size() const {
  return attr_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
OpInfo::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.OpInfo.attr)
  return attr_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
OpInfo::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.OpInfo.attr)
  return attr_.MutableMap();
}

// repeated .tensorflow.OpInfo.TensorProperties inputs = 3;
inline int OpInfo::inputs_size() const {
  return inputs_.size();
}
inline void OpInfo::clear_inputs() {
  inputs_.Clear();
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::mutable_inputs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.inputs)
  return inputs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
OpInfo::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpInfo.inputs)
  return &inputs_;
}
inline const ::tensorflow::OpInfo_TensorProperties& OpInfo::inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.inputs)
  return inputs_.Get(index);
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::add_inputs() {
  // @@protoc_insertion_point(field_add:tensorflow.OpInfo.inputs)
  return inputs_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
OpInfo::inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpInfo.inputs)
  return inputs_;
}

// repeated .tensorflow.OpInfo.TensorProperties outputs = 5;
inline int OpInfo::outputs_size() const {
  return outputs_.size();
}
inline void OpInfo::clear_outputs() {
  outputs_.Clear();
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::mutable_outputs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.outputs)
  return outputs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >*
OpInfo::mutable_outputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpInfo.outputs)
  return &outputs_;
}
inline const ::tensorflow::OpInfo_TensorProperties& OpInfo::outputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.outputs)
  return outputs_.Get(index);
}
inline ::tensorflow::OpInfo_TensorProperties* OpInfo::add_outputs() {
  // @@protoc_insertion_point(field_add:tensorflow.OpInfo.outputs)
  return outputs_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpInfo_TensorProperties >&
OpInfo::outputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpInfo.outputs)
  return outputs_;
}

// .tensorflow.DeviceProperties device = 4;
inline bool OpInfo::has_device() const {
  return this != internal_default_instance() && device_ != nullptr;
}
inline const ::tensorflow::DeviceProperties& OpInfo::device() const {
  const ::tensorflow::DeviceProperties* p = device_;
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.device)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::DeviceProperties*>(
      &::tensorflow::_DeviceProperties_default_instance_);
}
inline ::tensorflow::DeviceProperties* OpInfo::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.device)
  
  ::tensorflow::DeviceProperties* temp = device_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  device_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceProperties* OpInfo::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpInfo.device)
  
  ::tensorflow::DeviceProperties* temp = device_;
  device_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceProperties* OpInfo::mutable_device() {
  
  if (device_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceProperties>(GetArenaNoVirtual());
    device_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.device)
  return device_;
}
inline void OpInfo::set_allocated_device(::tensorflow::DeviceProperties* device) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_);
  }
  if (device) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device)->GetArena();
    if (message_arena != submessage_arena) {
      device = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device, submessage_arena);
    }
    
  } else {
    
  }
  device_ = device;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.device)
}

// .tensorflow.SessionInfo session_info = 6;
inline bool OpInfo::has_session_info() const {
  return this != internal_default_instance() && session_info_ != nullptr;
}
inline void OpInfo::clear_session_info() {
  if (GetArenaNoVirtual() == nullptr && session_info_ != nullptr) {
    delete session_info_;
  }
  session_info_ = nullptr;
}
inline const ::tensorflow::SessionInfo& OpInfo::session_info() const {
  const ::tensorflow::SessionInfo* p = session_info_;
  // @@protoc_insertion_point(field_get:tensorflow.OpInfo.session_info)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::SessionInfo*>(
      &::tensorflow::_SessionInfo_default_instance_);
}
inline ::tensorflow::SessionInfo* OpInfo::release_session_info() {
  // @@protoc_insertion_point(field_release:tensorflow.OpInfo.session_info)
  
  ::tensorflow::SessionInfo* temp = session_info_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  session_info_ = nullptr;
  return temp;
}
inline ::tensorflow::SessionInfo* OpInfo::unsafe_arena_release_session_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpInfo.session_info)
  
  ::tensorflow::SessionInfo* temp = session_info_;
  session_info_ = nullptr;
  return temp;
}
inline ::tensorflow::SessionInfo* OpInfo::mutable_session_info() {
  
  if (session_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SessionInfo>(GetArenaNoVirtual());
    session_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpInfo.session_info)
  return session_info_;
}
inline void OpInfo::set_allocated_session_info(::tensorflow::SessionInfo* session_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete session_info_;
  }
  if (session_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(session_info);
    if (message_arena != submessage_arena) {
      session_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, session_info, submessage_arena);
    }
    
  } else {
    
  }
  session_info_ = session_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpInfo.session_info)
}

// -------------------------------------------------------------------

// NormalDistribution

// double mu = 1;
inline void NormalDistribution::clear_mu() {
  mu_ = 0;
}
inline double NormalDistribution::mu() const {
  // @@protoc_insertion_point(field_get:tensorflow.NormalDistribution.mu)
  return mu_;
}
inline void NormalDistribution::set_mu(double value) {
  
  mu_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NormalDistribution.mu)
}

// double sigma = 2;
inline void NormalDistribution::clear_sigma() {
  sigma_ = 0;
}
inline double NormalDistribution::sigma() const {
  // @@protoc_insertion_point(field_get:tensorflow.NormalDistribution.sigma)
  return sigma_;
}
inline void NormalDistribution::set_sigma(double value) {
  
  sigma_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NormalDistribution.sigma)
}

// -------------------------------------------------------------------

// LogNormalDistribution

// double mu = 1;
inline void LogNormalDistribution::clear_mu() {
  mu_ = 0;
}
inline double LogNormalDistribution::mu() const {
  // @@protoc_insertion_point(field_get:tensorflow.LogNormalDistribution.mu)
  return mu_;
}
inline void LogNormalDistribution::set_mu(double value) {
  
  mu_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LogNormalDistribution.mu)
}

// double sigma = 2;
inline void LogNormalDistribution::clear_sigma() {
  sigma_ = 0;
}
inline double LogNormalDistribution::sigma() const {
  // @@protoc_insertion_point(field_get:tensorflow.LogNormalDistribution.sigma)
  return sigma_;
}
inline void LogNormalDistribution::set_sigma(double value) {
  
  sigma_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LogNormalDistribution.sigma)
}

// -------------------------------------------------------------------

// OpPerformance_OpMemory

// repeated int64 output_memory = 1;
inline int OpPerformance_OpMemory::output_memory_size() const {
  return output_memory_.size();
}
inline void OpPerformance_OpMemory::clear_output_memory() {
  output_memory_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpPerformance_OpMemory::output_memory(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.output_memory)
  return output_memory_.Get(index);
}
inline void OpPerformance_OpMemory::set_output_memory(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  output_memory_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.output_memory)
}
inline void OpPerformance_OpMemory::add_output_memory(::PROTOBUF_NAMESPACE_ID::int64 value) {
  output_memory_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.OpPerformance.OpMemory.output_memory)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
OpPerformance_OpMemory::output_memory() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpPerformance.OpMemory.output_memory)
  return output_memory_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
OpPerformance_OpMemory::mutable_output_memory() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpPerformance.OpMemory.output_memory)
  return &output_memory_;
}

// int64 temp_memory = 2;
inline void OpPerformance_OpMemory::clear_temp_memory() {
  temp_memory_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpPerformance_OpMemory::temp_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.temp_memory)
  return temp_memory_;
}
inline void OpPerformance_OpMemory::set_temp_memory(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  temp_memory_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.temp_memory)
}

// int64 persistent_memory = 4;
inline void OpPerformance_OpMemory::clear_persistent_memory() {
  persistent_memory_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpPerformance_OpMemory::persistent_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.persistent_memory)
  return persistent_memory_;
}
inline void OpPerformance_OpMemory::set_persistent_memory(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  persistent_memory_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.persistent_memory)
}

// int64 device_temp_memory = 3 [deprecated = true];
inline void OpPerformance_OpMemory::clear_device_temp_memory() {
  device_temp_memory_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpPerformance_OpMemory::device_temp_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.device_temp_memory)
  return device_temp_memory_;
}
inline void OpPerformance_OpMemory::set_device_temp_memory(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  device_temp_memory_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.device_temp_memory)
}

// int64 device_persistent_memory = 5 [deprecated = true];
inline void OpPerformance_OpMemory::clear_device_persistent_memory() {
  device_persistent_memory_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpPerformance_OpMemory::device_persistent_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.OpMemory.device_persistent_memory)
  return device_persistent_memory_;
}
inline void OpPerformance_OpMemory::set_device_persistent_memory(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  device_persistent_memory_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.OpMemory.device_persistent_memory)
}

// -------------------------------------------------------------------

// OpPerformance

// .tensorflow.OpInfo op = 1;
inline bool OpPerformance::has_op() const {
  return this != internal_default_instance() && op_ != nullptr;
}
inline void OpPerformance::clear_op() {
  if (GetArenaNoVirtual() == nullptr && op_ != nullptr) {
    delete op_;
  }
  op_ = nullptr;
}
inline const ::tensorflow::OpInfo& OpPerformance::op() const {
  const ::tensorflow::OpInfo* p = op_;
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.op)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::OpInfo*>(
      &::tensorflow::_OpInfo_default_instance_);
}
inline ::tensorflow::OpInfo* OpPerformance::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.op)
  
  ::tensorflow::OpInfo* temp = op_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  op_ = nullptr;
  return temp;
}
inline ::tensorflow::OpInfo* OpPerformance::unsafe_arena_release_op() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.op)
  
  ::tensorflow::OpInfo* temp = op_;
  op_ = nullptr;
  return temp;
}
inline ::tensorflow::OpInfo* OpPerformance::mutable_op() {
  
  if (op_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OpInfo>(GetArenaNoVirtual());
    op_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.op)
  return op_;
}
inline void OpPerformance::set_allocated_op(::tensorflow::OpInfo* op) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete op_;
  }
  if (op) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(op);
    if (message_arena != submessage_arena) {
      op = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, op, submessage_arena);
    }
    
  } else {
    
  }
  op_ = op;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.op)
}

// .tensorflow.SessionInfo session_info = 12 [deprecated = true];
inline bool OpPerformance::has_session_info() const {
  return this != internal_default_instance() && session_info_ != nullptr;
}
inline void OpPerformance::clear_session_info() {
  if (GetArenaNoVirtual() == nullptr && session_info_ != nullptr) {
    delete session_info_;
  }
  session_info_ = nullptr;
}
inline const ::tensorflow::SessionInfo& OpPerformance::session_info() const {
  const ::tensorflow::SessionInfo* p = session_info_;
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.session_info)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::SessionInfo*>(
      &::tensorflow::_SessionInfo_default_instance_);
}
inline ::tensorflow::SessionInfo* OpPerformance::release_session_info() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.session_info)
  
  ::tensorflow::SessionInfo* temp = session_info_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  session_info_ = nullptr;
  return temp;
}
inline ::tensorflow::SessionInfo* OpPerformance::unsafe_arena_release_session_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.session_info)
  
  ::tensorflow::SessionInfo* temp = session_info_;
  session_info_ = nullptr;
  return temp;
}
inline ::tensorflow::SessionInfo* OpPerformance::mutable_session_info() {
  
  if (session_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SessionInfo>(GetArenaNoVirtual());
    session_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.session_info)
  return session_info_;
}
inline void OpPerformance::set_allocated_session_info(::tensorflow::SessionInfo* session_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete session_info_;
  }
  if (session_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(session_info);
    if (message_arena != submessage_arena) {
      session_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, session_info, submessage_arena);
    }
    
  } else {
    
  }
  session_info_ = session_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.session_info)
}

// string node = 5;
inline void OpPerformance::clear_node() {
  node_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpPerformance::node() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.node)
  return node_.Get();
}
inline void OpPerformance::set_node(const std::string& value) {
  
  node_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.node)
}
inline void OpPerformance::set_node(std::string&& value) {
  
  node_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpPerformance.node)
}
inline void OpPerformance::set_node(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  node_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpPerformance.node)
}
inline void OpPerformance::set_node(const char* value,
    size_t size) {
  
  node_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpPerformance.node)
}
inline std::string* OpPerformance::mutable_node() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.node)
  return node_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpPerformance::release_node() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.node)
  
  return node_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpPerformance::set_allocated_node(std::string* node) {
  if (node != nullptr) {
    
  } else {
    
  }
  node_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), node,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.node)
}
inline std::string* OpPerformance::unsafe_arena_release_node() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.node)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return node_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpPerformance::unsafe_arena_set_allocated_node(
    std::string* node) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (node != nullptr) {
    
  } else {
    
  }
  node_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      node, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.node)
}

// int64 temporary_memory_size = 2;
inline void OpPerformance::clear_temporary_memory_size() {
  temporary_memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpPerformance::temporary_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.temporary_memory_size)
  return temporary_memory_size_;
}
inline void OpPerformance::set_temporary_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  temporary_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.temporary_memory_size)
}

// int64 compute_cost = 3;
inline void OpPerformance::clear_compute_cost() {
  compute_cost_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpPerformance::compute_cost() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.compute_cost)
  return compute_cost_;
}
inline void OpPerformance::set_compute_cost(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  compute_cost_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.compute_cost)
}

// int64 compute_time = 6;
inline void OpPerformance::clear_compute_time() {
  compute_time_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpPerformance::compute_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.compute_time)
  return compute_time_;
}
inline void OpPerformance::set_compute_time(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  compute_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.compute_time)
}

// int64 memory_time = 7;
inline void OpPerformance::clear_memory_time() {
  memory_time_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpPerformance::memory_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.memory_time)
  return memory_time_;
}
inline void OpPerformance::set_memory_time(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  memory_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.memory_time)
}

// double compute_efficiency = 4;
inline void OpPerformance::clear_compute_efficiency() {
  compute_efficiency_ = 0;
}
inline double OpPerformance::compute_efficiency() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.compute_efficiency)
  return compute_efficiency_;
}
inline void OpPerformance::set_compute_efficiency(double value) {
  
  compute_efficiency_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.compute_efficiency)
}

// double memory_efficiency = 8;
inline void OpPerformance::clear_memory_efficiency() {
  memory_efficiency_ = 0;
}
inline double OpPerformance::memory_efficiency() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.memory_efficiency)
  return memory_efficiency_;
}
inline void OpPerformance::set_memory_efficiency(double value) {
  
  memory_efficiency_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpPerformance.memory_efficiency)
}

// .tensorflow.NormalDistribution execution_time_normal = 10;
inline bool OpPerformance::has_execution_time_normal() const {
  return execution_time_case() == kExecutionTimeNormal;
}
inline void OpPerformance::set_has_execution_time_normal() {
  _oneof_case_[0] = kExecutionTimeNormal;
}
inline void OpPerformance::clear_execution_time_normal() {
  if (has_execution_time_normal()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete execution_time_.execution_time_normal_;
    }
    clear_has_execution_time();
  }
}
inline ::tensorflow::NormalDistribution* OpPerformance::release_execution_time_normal() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.execution_time_normal)
  if (has_execution_time_normal()) {
    clear_has_execution_time();
      ::tensorflow::NormalDistribution* temp = execution_time_.execution_time_normal_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    execution_time_.execution_time_normal_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::NormalDistribution& OpPerformance::execution_time_normal() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.execution_time_normal)
  return has_execution_time_normal()
      ? *execution_time_.execution_time_normal_
      : *reinterpret_cast< ::tensorflow::NormalDistribution*>(&::tensorflow::_NormalDistribution_default_instance_);
}
inline ::tensorflow::NormalDistribution* OpPerformance::unsafe_arena_release_execution_time_normal() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.execution_time_normal)
  if (has_execution_time_normal()) {
    clear_has_execution_time();
    ::tensorflow::NormalDistribution* temp = execution_time_.execution_time_normal_;
    execution_time_.execution_time_normal_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OpPerformance::unsafe_arena_set_allocated_execution_time_normal(::tensorflow::NormalDistribution* execution_time_normal) {
  clear_execution_time();
  if (execution_time_normal) {
    set_has_execution_time_normal();
    execution_time_.execution_time_normal_ = execution_time_normal;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.execution_time_normal)
}
inline ::tensorflow::NormalDistribution* OpPerformance::mutable_execution_time_normal() {
  if (!has_execution_time_normal()) {
    clear_execution_time();
    set_has_execution_time_normal();
    execution_time_.execution_time_normal_ = CreateMaybeMessage< ::tensorflow::NormalDistribution >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.execution_time_normal)
  return execution_time_.execution_time_normal_;
}

// .tensorflow.LogNormalDistribution execution_time_log_normal = 11;
inline bool OpPerformance::has_execution_time_log_normal() const {
  return execution_time_case() == kExecutionTimeLogNormal;
}
inline void OpPerformance::set_has_execution_time_log_normal() {
  _oneof_case_[0] = kExecutionTimeLogNormal;
}
inline void OpPerformance::clear_execution_time_log_normal() {
  if (has_execution_time_log_normal()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete execution_time_.execution_time_log_normal_;
    }
    clear_has_execution_time();
  }
}
inline ::tensorflow::LogNormalDistribution* OpPerformance::release_execution_time_log_normal() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.execution_time_log_normal)
  if (has_execution_time_log_normal()) {
    clear_has_execution_time();
      ::tensorflow::LogNormalDistribution* temp = execution_time_.execution_time_log_normal_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    execution_time_.execution_time_log_normal_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::LogNormalDistribution& OpPerformance::execution_time_log_normal() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.execution_time_log_normal)
  return has_execution_time_log_normal()
      ? *execution_time_.execution_time_log_normal_
      : *reinterpret_cast< ::tensorflow::LogNormalDistribution*>(&::tensorflow::_LogNormalDistribution_default_instance_);
}
inline ::tensorflow::LogNormalDistribution* OpPerformance::unsafe_arena_release_execution_time_log_normal() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.execution_time_log_normal)
  if (has_execution_time_log_normal()) {
    clear_has_execution_time();
    ::tensorflow::LogNormalDistribution* temp = execution_time_.execution_time_log_normal_;
    execution_time_.execution_time_log_normal_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OpPerformance::unsafe_arena_set_allocated_execution_time_log_normal(::tensorflow::LogNormalDistribution* execution_time_log_normal) {
  clear_execution_time();
  if (execution_time_log_normal) {
    set_has_execution_time_log_normal();
    execution_time_.execution_time_log_normal_ = execution_time_log_normal;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpPerformance.execution_time_log_normal)
}
inline ::tensorflow::LogNormalDistribution* OpPerformance::mutable_execution_time_log_normal() {
  if (!has_execution_time_log_normal()) {
    clear_execution_time();
    set_has_execution_time_log_normal();
    execution_time_.execution_time_log_normal_ = CreateMaybeMessage< ::tensorflow::LogNormalDistribution >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.execution_time_log_normal)
  return execution_time_.execution_time_log_normal_;
}

// .tensorflow.OpPerformance.OpMemory op_memory = 9;
inline bool OpPerformance::has_op_memory() const {
  return this != internal_default_instance() && op_memory_ != nullptr;
}
inline void OpPerformance::clear_op_memory() {
  if (GetArenaNoVirtual() == nullptr && op_memory_ != nullptr) {
    delete op_memory_;
  }
  op_memory_ = nullptr;
}
inline const ::tensorflow::OpPerformance_OpMemory& OpPerformance::op_memory() const {
  const ::tensorflow::OpPerformance_OpMemory* p = op_memory_;
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformance.op_memory)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::OpPerformance_OpMemory*>(
      &::tensorflow::_OpPerformance_OpMemory_default_instance_);
}
inline ::tensorflow::OpPerformance_OpMemory* OpPerformance::release_op_memory() {
  // @@protoc_insertion_point(field_release:tensorflow.OpPerformance.op_memory)
  
  ::tensorflow::OpPerformance_OpMemory* temp = op_memory_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  op_memory_ = nullptr;
  return temp;
}
inline ::tensorflow::OpPerformance_OpMemory* OpPerformance::unsafe_arena_release_op_memory() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpPerformance.op_memory)
  
  ::tensorflow::OpPerformance_OpMemory* temp = op_memory_;
  op_memory_ = nullptr;
  return temp;
}
inline ::tensorflow::OpPerformance_OpMemory* OpPerformance::mutable_op_memory() {
  
  if (op_memory_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OpPerformance_OpMemory>(GetArenaNoVirtual());
    op_memory_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformance.op_memory)
  return op_memory_;
}
inline void OpPerformance::set_allocated_op_memory(::tensorflow::OpPerformance_OpMemory* op_memory) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete op_memory_;
  }
  if (op_memory) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(op_memory);
    if (message_arena != submessage_arena) {
      op_memory = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, op_memory, submessage_arena);
    }
    
  } else {
    
  }
  op_memory_ = op_memory;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpPerformance.op_memory)
}

inline bool OpPerformance::has_execution_time() const {
  return execution_time_case() != EXECUTION_TIME_NOT_SET;
}
inline void OpPerformance::clear_has_execution_time() {
  _oneof_case_[0] = EXECUTION_TIME_NOT_SET;
}
inline OpPerformance::ExecutionTimeCase OpPerformance::execution_time_case() const {
  return OpPerformance::ExecutionTimeCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// OpPerformanceList

// repeated .tensorflow.OpPerformance op_performance = 1;
inline int OpPerformanceList::op_performance_size() const {
  return op_performance_.size();
}
inline void OpPerformanceList::clear_op_performance() {
  op_performance_.Clear();
}
inline ::tensorflow::OpPerformance* OpPerformanceList::mutable_op_performance(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpPerformanceList.op_performance)
  return op_performance_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpPerformance >*
OpPerformanceList::mutable_op_performance() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpPerformanceList.op_performance)
  return &op_performance_;
}
inline const ::tensorflow::OpPerformance& OpPerformanceList::op_performance(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpPerformanceList.op_performance)
  return op_performance_.Get(index);
}
inline ::tensorflow::OpPerformance* OpPerformanceList::add_op_performance() {
  // @@protoc_insertion_point(field_add:tensorflow.OpPerformanceList.op_performance)
  return op_performance_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpPerformance >&
OpPerformanceList::op_performance() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpPerformanceList.op_performance)
  return op_performance_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fgrappler_2fcosts_2fop_5fperformance_5fdata_2eproto
