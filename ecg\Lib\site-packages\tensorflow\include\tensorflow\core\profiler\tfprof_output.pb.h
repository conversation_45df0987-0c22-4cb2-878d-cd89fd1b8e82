// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_output.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[7]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
namespace tensorflow {
namespace tfprof {
class AdviceProto;
class AdviceProtoDefaultTypeInternal;
extern AdviceProtoDefaultTypeInternal _AdviceProto_default_instance_;
class AdviceProto_Checker;
class AdviceProto_CheckerDefaultTypeInternal;
extern AdviceProto_CheckerDefaultTypeInternal _AdviceProto_Checker_default_instance_;
class AdviceProto_CheckersEntry_DoNotUse;
class AdviceProto_CheckersEntry_DoNotUseDefaultTypeInternal;
extern AdviceProto_CheckersEntry_DoNotUseDefaultTypeInternal _AdviceProto_CheckersEntry_DoNotUse_default_instance_;
class GraphNodeProto;
class GraphNodeProtoDefaultTypeInternal;
extern GraphNodeProtoDefaultTypeInternal _GraphNodeProto_default_instance_;
class GraphNodeProto_InputShapesEntry_DoNotUse;
class GraphNodeProto_InputShapesEntry_DoNotUseDefaultTypeInternal;
extern GraphNodeProto_InputShapesEntry_DoNotUseDefaultTypeInternal _GraphNodeProto_InputShapesEntry_DoNotUse_default_instance_;
class MultiGraphNodeProto;
class MultiGraphNodeProtoDefaultTypeInternal;
extern MultiGraphNodeProtoDefaultTypeInternal _MultiGraphNodeProto_default_instance_;
class TFProfTensorProto;
class TFProfTensorProtoDefaultTypeInternal;
extern TFProfTensorProtoDefaultTypeInternal _TFProfTensorProto_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tfprof::AdviceProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdviceProto>(Arena*);
template<> ::tensorflow::tfprof::AdviceProto_Checker* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdviceProto_Checker>(Arena*);
template<> ::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::GraphNodeProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::GraphNodeProto>(Arena*);
template<> ::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::MultiGraphNodeProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::MultiGraphNodeProto>(Arena*);
template<> ::tensorflow::tfprof::TFProfTensorProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::TFProfTensorProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tfprof {

// ===================================================================

class TFProfTensorProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.TFProfTensorProto) */ {
 public:
  TFProfTensorProto();
  virtual ~TFProfTensorProto();

  TFProfTensorProto(const TFProfTensorProto& from);
  TFProfTensorProto(TFProfTensorProto&& from) noexcept
    : TFProfTensorProto() {
    *this = ::std::move(from);
  }

  inline TFProfTensorProto& operator=(const TFProfTensorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TFProfTensorProto& operator=(TFProfTensorProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TFProfTensorProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TFProfTensorProto* internal_default_instance() {
    return reinterpret_cast<const TFProfTensorProto*>(
               &_TFProfTensorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TFProfTensorProto& a, TFProfTensorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TFProfTensorProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TFProfTensorProto* New() const final {
    return CreateMaybeMessage<TFProfTensorProto>(nullptr);
  }

  TFProfTensorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TFProfTensorProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TFProfTensorProto& from);
  void MergeFrom(const TFProfTensorProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TFProfTensorProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.TFProfTensorProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueDoubleFieldNumber = 2,
    kValueInt64FieldNumber = 3,
    kValueStrFieldNumber = 4,
    kDtypeFieldNumber = 1,
  };
  // repeated double value_double = 2;
  int value_double_size() const;
  void clear_value_double();
  double value_double(int index) const;
  void set_value_double(int index, double value);
  void add_value_double(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      value_double() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_value_double();

  // repeated int64 value_int64 = 3;
  int value_int64_size() const;
  void clear_value_int64();
  ::PROTOBUF_NAMESPACE_ID::int64 value_int64(int index) const;
  void set_value_int64(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_value_int64(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      value_int64() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_value_int64();

  // repeated string value_str = 4;
  int value_str_size() const;
  void clear_value_str();
  const std::string& value_str(int index) const;
  std::string* mutable_value_str(int index);
  void set_value_str(int index, const std::string& value);
  void set_value_str(int index, std::string&& value);
  void set_value_str(int index, const char* value);
  void set_value_str(int index, const char* value, size_t size);
  std::string* add_value_str();
  void add_value_str(const std::string& value);
  void add_value_str(std::string&& value);
  void add_value_str(const char* value);
  void add_value_str(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& value_str() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_value_str();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.TFProfTensorProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > value_double_;
  mutable std::atomic<int> _value_double_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > value_int64_;
  mutable std::atomic<int> _value_int64_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> value_str_;
  int dtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};
// -------------------------------------------------------------------

class GraphNodeProto_InputShapesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphNodeProto_InputShapesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TensorShapeProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphNodeProto_InputShapesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TensorShapeProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  GraphNodeProto_InputShapesEntry_DoNotUse();
  GraphNodeProto_InputShapesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GraphNodeProto_InputShapesEntry_DoNotUse& other);
  static const GraphNodeProto_InputShapesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GraphNodeProto_InputShapesEntry_DoNotUse*>(&_GraphNodeProto_InputShapesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto.file_level_metadata[1];
  }

  public:
};

// -------------------------------------------------------------------

class GraphNodeProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.GraphNodeProto) */ {
 public:
  GraphNodeProto();
  virtual ~GraphNodeProto();

  GraphNodeProto(const GraphNodeProto& from);
  GraphNodeProto(GraphNodeProto&& from) noexcept
    : GraphNodeProto() {
    *this = ::std::move(from);
  }

  inline GraphNodeProto& operator=(const GraphNodeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphNodeProto& operator=(GraphNodeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphNodeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphNodeProto* internal_default_instance() {
    return reinterpret_cast<const GraphNodeProto*>(
               &_GraphNodeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GraphNodeProto& a, GraphNodeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphNodeProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphNodeProto* New() const final {
    return CreateMaybeMessage<GraphNodeProto>(nullptr);
  }

  GraphNodeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphNodeProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphNodeProto& from);
  void MergeFrom(const GraphNodeProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphNodeProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.GraphNodeProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kDevicesFieldNumber = 10,
    kShapesFieldNumber = 11,
    kChildrenFieldNumber = 12,
    kInputShapesFieldNumber = 16,
    kNameFieldNumber = 1,
    kTensorValueFieldNumber = 15,
    kExecMicrosFieldNumber = 2,
    kRequestedBytesFieldNumber = 3,
    kParametersFieldNumber = 4,
    kTotalExecMicrosFieldNumber = 6,
    kTotalRequestedBytesFieldNumber = 7,
    kTotalParametersFieldNumber = 8,
    kFloatOpsFieldNumber = 13,
    kTotalFloatOpsFieldNumber = 14,
    kAcceleratorExecMicrosFieldNumber = 17,
    kCpuExecMicrosFieldNumber = 18,
    kTotalAcceleratorExecMicrosFieldNumber = 19,
    kTotalCpuExecMicrosFieldNumber = 20,
    kRunCountFieldNumber = 21,
    kTotalRunCountFieldNumber = 22,
    kTotalDefinitionCountFieldNumber = 23,
    kPeakBytesFieldNumber = 24,
    kResidualBytesFieldNumber = 25,
    kOutputBytesFieldNumber = 26,
    kTotalPeakBytesFieldNumber = 27,
    kTotalResidualBytesFieldNumber = 28,
    kTotalOutputBytesFieldNumber = 29,
  };
  // repeated string devices = 10;
  int devices_size() const;
  void clear_devices();
  const std::string& devices(int index) const;
  std::string* mutable_devices(int index);
  void set_devices(int index, const std::string& value);
  void set_devices(int index, std::string&& value);
  void set_devices(int index, const char* value);
  void set_devices(int index, const char* value, size_t size);
  std::string* add_devices();
  void add_devices(const std::string& value);
  void add_devices(std::string&& value);
  void add_devices(const char* value);
  void add_devices(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& devices() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_devices();

  // repeated .tensorflow.TensorShapeProto shapes = 11;
  int shapes_size() const;
  void clear_shapes();
  ::tensorflow::TensorShapeProto* mutable_shapes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_shapes();
  const ::tensorflow::TensorShapeProto& shapes(int index) const;
  ::tensorflow::TensorShapeProto* add_shapes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      shapes() const;

  // repeated .tensorflow.tfprof.GraphNodeProto children = 12;
  int children_size() const;
  void clear_children();
  ::tensorflow::tfprof::GraphNodeProto* mutable_children(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
      mutable_children();
  const ::tensorflow::tfprof::GraphNodeProto& children(int index) const;
  ::tensorflow::tfprof::GraphNodeProto* add_children();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
      children() const;

  // map<int32, .tensorflow.TensorShapeProto> input_shapes = 16;
  int input_shapes_size() const;
  void clear_input_shapes();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TensorShapeProto >&
      input_shapes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TensorShapeProto >*
      mutable_input_shapes();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // .tensorflow.tfprof.TFProfTensorProto tensor_value = 15;
  bool has_tensor_value() const;
  void clear_tensor_value();
  const ::tensorflow::tfprof::TFProfTensorProto& tensor_value() const;
  ::tensorflow::tfprof::TFProfTensorProto* release_tensor_value();
  ::tensorflow::tfprof::TFProfTensorProto* mutable_tensor_value();
  void set_allocated_tensor_value(::tensorflow::tfprof::TFProfTensorProto* tensor_value);

  // int64 exec_micros = 2;
  void clear_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 exec_micros() const;
  void set_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 requested_bytes = 3;
  void clear_requested_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 requested_bytes() const;
  void set_requested_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 parameters = 4;
  void clear_parameters();
  ::PROTOBUF_NAMESPACE_ID::int64 parameters() const;
  void set_parameters(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_exec_micros = 6;
  void clear_total_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 total_exec_micros() const;
  void set_total_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_requested_bytes = 7;
  void clear_total_requested_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 total_requested_bytes() const;
  void set_total_requested_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_parameters = 8;
  void clear_total_parameters();
  ::PROTOBUF_NAMESPACE_ID::int64 total_parameters() const;
  void set_total_parameters(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 float_ops = 13;
  void clear_float_ops();
  ::PROTOBUF_NAMESPACE_ID::int64 float_ops() const;
  void set_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_float_ops = 14;
  void clear_total_float_ops();
  ::PROTOBUF_NAMESPACE_ID::int64 total_float_ops() const;
  void set_total_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 accelerator_exec_micros = 17;
  void clear_accelerator_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 accelerator_exec_micros() const;
  void set_accelerator_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 cpu_exec_micros = 18;
  void clear_cpu_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 cpu_exec_micros() const;
  void set_cpu_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_accelerator_exec_micros = 19;
  void clear_total_accelerator_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 total_accelerator_exec_micros() const;
  void set_total_accelerator_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_cpu_exec_micros = 20;
  void clear_total_cpu_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 total_cpu_exec_micros() const;
  void set_total_cpu_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 run_count = 21;
  void clear_run_count();
  ::PROTOBUF_NAMESPACE_ID::int64 run_count() const;
  void set_run_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_run_count = 22;
  void clear_total_run_count();
  ::PROTOBUF_NAMESPACE_ID::int64 total_run_count() const;
  void set_total_run_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_definition_count = 23;
  void clear_total_definition_count();
  ::PROTOBUF_NAMESPACE_ID::int64 total_definition_count() const;
  void set_total_definition_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 peak_bytes = 24;
  void clear_peak_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 peak_bytes() const;
  void set_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 residual_bytes = 25;
  void clear_residual_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 residual_bytes() const;
  void set_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 output_bytes = 26;
  void clear_output_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 output_bytes() const;
  void set_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_peak_bytes = 27;
  void clear_total_peak_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 total_peak_bytes() const;
  void set_total_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_residual_bytes = 28;
  void clear_total_residual_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 total_residual_bytes() const;
  void set_total_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_output_bytes = 29;
  void clear_total_output_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 total_output_bytes() const;
  void set_total_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.GraphNodeProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> devices_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto > shapes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto > children_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GraphNodeProto_InputShapesEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TensorShapeProto,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > input_shapes_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::tfprof::TFProfTensorProto* tensor_value_;
  ::PROTOBUF_NAMESPACE_ID::int64 exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 requested_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 parameters_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_requested_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_parameters_;
  ::PROTOBUF_NAMESPACE_ID::int64 float_ops_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_float_ops_;
  ::PROTOBUF_NAMESPACE_ID::int64 accelerator_exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 cpu_exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_accelerator_exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_cpu_exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 run_count_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_run_count_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_definition_count_;
  ::PROTOBUF_NAMESPACE_ID::int64 peak_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 residual_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 output_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_peak_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_residual_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_output_bytes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};
// -------------------------------------------------------------------

class MultiGraphNodeProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.MultiGraphNodeProto) */ {
 public:
  MultiGraphNodeProto();
  virtual ~MultiGraphNodeProto();

  MultiGraphNodeProto(const MultiGraphNodeProto& from);
  MultiGraphNodeProto(MultiGraphNodeProto&& from) noexcept
    : MultiGraphNodeProto() {
    *this = ::std::move(from);
  }

  inline MultiGraphNodeProto& operator=(const MultiGraphNodeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline MultiGraphNodeProto& operator=(MultiGraphNodeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MultiGraphNodeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MultiGraphNodeProto* internal_default_instance() {
    return reinterpret_cast<const MultiGraphNodeProto*>(
               &_MultiGraphNodeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MultiGraphNodeProto& a, MultiGraphNodeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(MultiGraphNodeProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MultiGraphNodeProto* New() const final {
    return CreateMaybeMessage<MultiGraphNodeProto>(nullptr);
  }

  MultiGraphNodeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MultiGraphNodeProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MultiGraphNodeProto& from);
  void MergeFrom(const MultiGraphNodeProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MultiGraphNodeProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.MultiGraphNodeProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGraphNodesFieldNumber = 10,
    kChildrenFieldNumber = 11,
    kNameFieldNumber = 1,
    kExecMicrosFieldNumber = 2,
    kRequestedBytesFieldNumber = 3,
    kParametersFieldNumber = 4,
    kFloatOpsFieldNumber = 5,
    kTotalExecMicrosFieldNumber = 6,
    kTotalRequestedBytesFieldNumber = 7,
    kTotalParametersFieldNumber = 8,
    kTotalFloatOpsFieldNumber = 9,
    kAcceleratorExecMicrosFieldNumber = 12,
    kCpuExecMicrosFieldNumber = 13,
    kTotalAcceleratorExecMicrosFieldNumber = 14,
    kTotalCpuExecMicrosFieldNumber = 15,
    kPeakBytesFieldNumber = 16,
    kResidualBytesFieldNumber = 17,
    kOutputBytesFieldNumber = 18,
    kTotalPeakBytesFieldNumber = 19,
    kTotalResidualBytesFieldNumber = 20,
    kTotalOutputBytesFieldNumber = 21,
  };
  // repeated .tensorflow.tfprof.GraphNodeProto graph_nodes = 10;
  int graph_nodes_size() const;
  void clear_graph_nodes();
  ::tensorflow::tfprof::GraphNodeProto* mutable_graph_nodes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
      mutable_graph_nodes();
  const ::tensorflow::tfprof::GraphNodeProto& graph_nodes(int index) const;
  ::tensorflow::tfprof::GraphNodeProto* add_graph_nodes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
      graph_nodes() const;

  // repeated .tensorflow.tfprof.MultiGraphNodeProto children = 11;
  int children_size() const;
  void clear_children();
  ::tensorflow::tfprof::MultiGraphNodeProto* mutable_children(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >*
      mutable_children();
  const ::tensorflow::tfprof::MultiGraphNodeProto& children(int index) const;
  ::tensorflow::tfprof::MultiGraphNodeProto* add_children();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >&
      children() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // int64 exec_micros = 2;
  void clear_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 exec_micros() const;
  void set_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 requested_bytes = 3;
  void clear_requested_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 requested_bytes() const;
  void set_requested_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 parameters = 4;
  void clear_parameters();
  ::PROTOBUF_NAMESPACE_ID::int64 parameters() const;
  void set_parameters(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 float_ops = 5;
  void clear_float_ops();
  ::PROTOBUF_NAMESPACE_ID::int64 float_ops() const;
  void set_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_exec_micros = 6;
  void clear_total_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 total_exec_micros() const;
  void set_total_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_requested_bytes = 7;
  void clear_total_requested_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 total_requested_bytes() const;
  void set_total_requested_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_parameters = 8;
  void clear_total_parameters();
  ::PROTOBUF_NAMESPACE_ID::int64 total_parameters() const;
  void set_total_parameters(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_float_ops = 9;
  void clear_total_float_ops();
  ::PROTOBUF_NAMESPACE_ID::int64 total_float_ops() const;
  void set_total_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 accelerator_exec_micros = 12;
  void clear_accelerator_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 accelerator_exec_micros() const;
  void set_accelerator_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 cpu_exec_micros = 13;
  void clear_cpu_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 cpu_exec_micros() const;
  void set_cpu_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_accelerator_exec_micros = 14;
  void clear_total_accelerator_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 total_accelerator_exec_micros() const;
  void set_total_accelerator_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_cpu_exec_micros = 15;
  void clear_total_cpu_exec_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 total_cpu_exec_micros() const;
  void set_total_cpu_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 peak_bytes = 16;
  void clear_peak_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 peak_bytes() const;
  void set_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 residual_bytes = 17;
  void clear_residual_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 residual_bytes() const;
  void set_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 output_bytes = 18;
  void clear_output_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 output_bytes() const;
  void set_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_peak_bytes = 19;
  void clear_total_peak_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 total_peak_bytes() const;
  void set_total_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_residual_bytes = 20;
  void clear_total_residual_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 total_residual_bytes() const;
  void set_total_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 total_output_bytes = 21;
  void clear_total_output_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 total_output_bytes() const;
  void set_total_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.MultiGraphNodeProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto > graph_nodes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto > children_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::int64 exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 requested_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 parameters_;
  ::PROTOBUF_NAMESPACE_ID::int64 float_ops_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_requested_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_parameters_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_float_ops_;
  ::PROTOBUF_NAMESPACE_ID::int64 accelerator_exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 cpu_exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_accelerator_exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_cpu_exec_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 peak_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 residual_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 output_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_peak_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_residual_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_output_bytes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};
// -------------------------------------------------------------------

class AdviceProto_CheckersEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdviceProto_CheckersEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::AdviceProto_Checker,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdviceProto_CheckersEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::AdviceProto_Checker,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  AdviceProto_CheckersEntry_DoNotUse();
  AdviceProto_CheckersEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const AdviceProto_CheckersEntry_DoNotUse& other);
  static const AdviceProto_CheckersEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const AdviceProto_CheckersEntry_DoNotUse*>(&_AdviceProto_CheckersEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.AdviceProto.CheckersEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto.file_level_metadata[4];
  }

  public:
};

// -------------------------------------------------------------------

class AdviceProto_Checker :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdviceProto.Checker) */ {
 public:
  AdviceProto_Checker();
  virtual ~AdviceProto_Checker();

  AdviceProto_Checker(const AdviceProto_Checker& from);
  AdviceProto_Checker(AdviceProto_Checker&& from) noexcept
    : AdviceProto_Checker() {
    *this = ::std::move(from);
  }

  inline AdviceProto_Checker& operator=(const AdviceProto_Checker& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdviceProto_Checker& operator=(AdviceProto_Checker&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AdviceProto_Checker& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdviceProto_Checker* internal_default_instance() {
    return reinterpret_cast<const AdviceProto_Checker*>(
               &_AdviceProto_Checker_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(AdviceProto_Checker& a, AdviceProto_Checker& b) {
    a.Swap(&b);
  }
  inline void Swap(AdviceProto_Checker* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AdviceProto_Checker* New() const final {
    return CreateMaybeMessage<AdviceProto_Checker>(nullptr);
  }

  AdviceProto_Checker* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AdviceProto_Checker>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AdviceProto_Checker& from);
  void MergeFrom(const AdviceProto_Checker& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdviceProto_Checker* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.AdviceProto.Checker";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kReportsFieldNumber = 2,
  };
  // repeated string reports = 2;
  int reports_size() const;
  void clear_reports();
  const std::string& reports(int index) const;
  std::string* mutable_reports(int index);
  void set_reports(int index, const std::string& value);
  void set_reports(int index, std::string&& value);
  void set_reports(int index, const char* value);
  void set_reports(int index, const char* value, size_t size);
  std::string* add_reports();
  void add_reports(const std::string& value);
  void add_reports(std::string&& value);
  void add_reports(const char* value);
  void add_reports(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& reports() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_reports();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdviceProto.Checker)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> reports_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};
// -------------------------------------------------------------------

class AdviceProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdviceProto) */ {
 public:
  AdviceProto();
  virtual ~AdviceProto();

  AdviceProto(const AdviceProto& from);
  AdviceProto(AdviceProto&& from) noexcept
    : AdviceProto() {
    *this = ::std::move(from);
  }

  inline AdviceProto& operator=(const AdviceProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdviceProto& operator=(AdviceProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AdviceProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdviceProto* internal_default_instance() {
    return reinterpret_cast<const AdviceProto*>(
               &_AdviceProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(AdviceProto& a, AdviceProto& b) {
    a.Swap(&b);
  }
  inline void Swap(AdviceProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AdviceProto* New() const final {
    return CreateMaybeMessage<AdviceProto>(nullptr);
  }

  AdviceProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AdviceProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AdviceProto& from);
  void MergeFrom(const AdviceProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdviceProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.AdviceProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef AdviceProto_Checker Checker;

  // accessors -------------------------------------------------------

  enum : int {
    kCheckersFieldNumber = 1,
  };
  // map<string, .tensorflow.tfprof.AdviceProto.Checker> checkers = 1;
  int checkers_size() const;
  void clear_checkers();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >&
      checkers() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >*
      mutable_checkers();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdviceProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      AdviceProto_CheckersEntry_DoNotUse,
      std::string, ::tensorflow::tfprof::AdviceProto_Checker,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > checkers_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TFProfTensorProto

// .tensorflow.DataType dtype = 1;
inline void TFProfTensorProto::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType TFProfTensorProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void TFProfTensorProto::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.dtype)
}

// repeated double value_double = 2;
inline int TFProfTensorProto::value_double_size() const {
  return value_double_.size();
}
inline void TFProfTensorProto::clear_value_double() {
  value_double_.Clear();
}
inline double TFProfTensorProto::value_double(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.value_double)
  return value_double_.Get(index);
}
inline void TFProfTensorProto::set_value_double(int index, double value) {
  value_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_double)
}
inline void TFProfTensorProto::add_value_double(double value) {
  value_double_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_double)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TFProfTensorProto::value_double() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.TFProfTensorProto.value_double)
  return value_double_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TFProfTensorProto::mutable_value_double() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.TFProfTensorProto.value_double)
  return &value_double_;
}

// repeated int64 value_int64 = 3;
inline int TFProfTensorProto::value_int64_size() const {
  return value_int64_.size();
}
inline void TFProfTensorProto::clear_value_int64() {
  value_int64_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TFProfTensorProto::value_int64(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.value_int64)
  return value_int64_.Get(index);
}
inline void TFProfTensorProto::set_value_int64(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  value_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_int64)
}
inline void TFProfTensorProto::add_value_int64(::PROTOBUF_NAMESPACE_ID::int64 value) {
  value_int64_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_int64)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
TFProfTensorProto::value_int64() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.TFProfTensorProto.value_int64)
  return value_int64_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
TFProfTensorProto::mutable_value_int64() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.TFProfTensorProto.value_int64)
  return &value_int64_;
}

// repeated string value_str = 4;
inline int TFProfTensorProto::value_str_size() const {
  return value_str_.size();
}
inline void TFProfTensorProto::clear_value_str() {
  value_str_.Clear();
}
inline const std::string& TFProfTensorProto::value_str(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.value_str)
  return value_str_.Get(index);
}
inline std::string* TFProfTensorProto::mutable_value_str(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.TFProfTensorProto.value_str)
  return value_str_.Mutable(index);
}
inline void TFProfTensorProto::set_value_str(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_str)
  value_str_.Mutable(index)->assign(value);
}
inline void TFProfTensorProto::set_value_str(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_str)
  value_str_.Mutable(index)->assign(std::move(value));
}
inline void TFProfTensorProto::set_value_str(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  value_str_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::set_value_str(int index, const char* value, size_t size) {
  value_str_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline std::string* TFProfTensorProto::add_value_str() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.TFProfTensorProto.value_str)
  return value_str_.Add();
}
inline void TFProfTensorProto::add_value_str(const std::string& value) {
  value_str_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::add_value_str(std::string&& value) {
  value_str_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::add_value_str(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  value_str_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::add_value_str(const char* value, size_t size) {
  value_str_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TFProfTensorProto::value_str() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.TFProfTensorProto.value_str)
  return value_str_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TFProfTensorProto::mutable_value_str() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.TFProfTensorProto.value_str)
  return &value_str_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GraphNodeProto

// string name = 1;
inline void GraphNodeProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& GraphNodeProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.name)
  return name_.GetNoArena();
}
inline void GraphNodeProto::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.name)
}
inline void GraphNodeProto::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.GraphNodeProto.name)
}
inline void GraphNodeProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.GraphNodeProto.name)
}
inline void GraphNodeProto::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.GraphNodeProto.name)
}
inline std::string* GraphNodeProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* GraphNodeProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.GraphNodeProto.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void GraphNodeProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.GraphNodeProto.name)
}

// .tensorflow.tfprof.TFProfTensorProto tensor_value = 15;
inline bool GraphNodeProto::has_tensor_value() const {
  return this != internal_default_instance() && tensor_value_ != nullptr;
}
inline void GraphNodeProto::clear_tensor_value() {
  if (GetArenaNoVirtual() == nullptr && tensor_value_ != nullptr) {
    delete tensor_value_;
  }
  tensor_value_ = nullptr;
}
inline const ::tensorflow::tfprof::TFProfTensorProto& GraphNodeProto::tensor_value() const {
  const ::tensorflow::tfprof::TFProfTensorProto* p = tensor_value_;
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.tensor_value)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tfprof::TFProfTensorProto*>(
      &::tensorflow::tfprof::_TFProfTensorProto_default_instance_);
}
inline ::tensorflow::tfprof::TFProfTensorProto* GraphNodeProto::release_tensor_value() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.GraphNodeProto.tensor_value)
  
  ::tensorflow::tfprof::TFProfTensorProto* temp = tensor_value_;
  tensor_value_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::TFProfTensorProto* GraphNodeProto::mutable_tensor_value() {
  
  if (tensor_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::TFProfTensorProto>(GetArenaNoVirtual());
    tensor_value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.tensor_value)
  return tensor_value_;
}
inline void GraphNodeProto::set_allocated_tensor_value(::tensorflow::tfprof::TFProfTensorProto* tensor_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete tensor_value_;
  }
  if (tensor_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      tensor_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_value, submessage_arena);
    }
    
  } else {
    
  }
  tensor_value_ = tensor_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.GraphNodeProto.tensor_value)
}

// int64 run_count = 21;
inline void GraphNodeProto::clear_run_count() {
  run_count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::run_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.run_count)
  return run_count_;
}
inline void GraphNodeProto::set_run_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  run_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.run_count)
}

// int64 exec_micros = 2;
inline void GraphNodeProto::clear_exec_micros() {
  exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.exec_micros)
  return exec_micros_;
}
inline void GraphNodeProto::set_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.exec_micros)
}

// int64 accelerator_exec_micros = 17;
inline void GraphNodeProto::clear_accelerator_exec_micros() {
  accelerator_exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.accelerator_exec_micros)
  return accelerator_exec_micros_;
}
inline void GraphNodeProto::set_accelerator_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  accelerator_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.accelerator_exec_micros)
}

// int64 cpu_exec_micros = 18;
inline void GraphNodeProto::clear_cpu_exec_micros() {
  cpu_exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.cpu_exec_micros)
  return cpu_exec_micros_;
}
inline void GraphNodeProto::set_cpu_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  cpu_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.cpu_exec_micros)
}

// int64 requested_bytes = 3;
inline void GraphNodeProto::clear_requested_bytes() {
  requested_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.requested_bytes)
  return requested_bytes_;
}
inline void GraphNodeProto::set_requested_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  requested_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.requested_bytes)
}

// int64 peak_bytes = 24;
inline void GraphNodeProto::clear_peak_bytes() {
  peak_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.peak_bytes)
  return peak_bytes_;
}
inline void GraphNodeProto::set_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.peak_bytes)
}

// int64 residual_bytes = 25;
inline void GraphNodeProto::clear_residual_bytes() {
  residual_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.residual_bytes)
  return residual_bytes_;
}
inline void GraphNodeProto::set_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.residual_bytes)
}

// int64 output_bytes = 26;
inline void GraphNodeProto::clear_output_bytes() {
  output_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.output_bytes)
  return output_bytes_;
}
inline void GraphNodeProto::set_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.output_bytes)
}

// int64 parameters = 4;
inline void GraphNodeProto::clear_parameters() {
  parameters_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.parameters)
  return parameters_;
}
inline void GraphNodeProto::set_parameters(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  parameters_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.parameters)
}

// int64 float_ops = 13;
inline void GraphNodeProto::clear_float_ops() {
  float_ops_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.float_ops)
  return float_ops_;
}
inline void GraphNodeProto::set_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.float_ops)
}

// repeated string devices = 10;
inline int GraphNodeProto::devices_size() const {
  return devices_.size();
}
inline void GraphNodeProto::clear_devices() {
  devices_.Clear();
}
inline const std::string& GraphNodeProto::devices(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.devices)
  return devices_.Get(index);
}
inline std::string* GraphNodeProto::mutable_devices(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.devices)
  return devices_.Mutable(index);
}
inline void GraphNodeProto::set_devices(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.devices)
  devices_.Mutable(index)->assign(value);
}
inline void GraphNodeProto::set_devices(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.devices)
  devices_.Mutable(index)->assign(std::move(value));
}
inline void GraphNodeProto::set_devices(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  devices_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::set_devices(int index, const char* value, size_t size) {
  devices_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.GraphNodeProto.devices)
}
inline std::string* GraphNodeProto::add_devices() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.GraphNodeProto.devices)
  return devices_.Add();
}
inline void GraphNodeProto::add_devices(const std::string& value) {
  devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::add_devices(std::string&& value) {
  devices_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::add_devices(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::add_devices(const char* value, size_t size) {
  devices_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.GraphNodeProto.devices)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
GraphNodeProto::devices() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.GraphNodeProto.devices)
  return devices_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
GraphNodeProto::mutable_devices() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.GraphNodeProto.devices)
  return &devices_;
}

// int64 total_definition_count = 23;
inline void GraphNodeProto::clear_total_definition_count() {
  total_definition_count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_definition_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_definition_count)
  return total_definition_count_;
}
inline void GraphNodeProto::set_total_definition_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_definition_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_definition_count)
}

// int64 total_run_count = 22;
inline void GraphNodeProto::clear_total_run_count() {
  total_run_count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_run_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_run_count)
  return total_run_count_;
}
inline void GraphNodeProto::set_total_run_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_run_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_run_count)
}

// int64 total_exec_micros = 6;
inline void GraphNodeProto::clear_total_exec_micros() {
  total_exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_exec_micros)
  return total_exec_micros_;
}
inline void GraphNodeProto::set_total_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_exec_micros)
}

// int64 total_accelerator_exec_micros = 19;
inline void GraphNodeProto::clear_total_accelerator_exec_micros() {
  total_accelerator_exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_accelerator_exec_micros)
  return total_accelerator_exec_micros_;
}
inline void GraphNodeProto::set_total_accelerator_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_accelerator_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_accelerator_exec_micros)
}

// int64 total_cpu_exec_micros = 20;
inline void GraphNodeProto::clear_total_cpu_exec_micros() {
  total_cpu_exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_cpu_exec_micros)
  return total_cpu_exec_micros_;
}
inline void GraphNodeProto::set_total_cpu_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_cpu_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_cpu_exec_micros)
}

// int64 total_requested_bytes = 7;
inline void GraphNodeProto::clear_total_requested_bytes() {
  total_requested_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_requested_bytes)
  return total_requested_bytes_;
}
inline void GraphNodeProto::set_total_requested_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_requested_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_requested_bytes)
}

// int64 total_peak_bytes = 27;
inline void GraphNodeProto::clear_total_peak_bytes() {
  total_peak_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_peak_bytes)
  return total_peak_bytes_;
}
inline void GraphNodeProto::set_total_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_peak_bytes)
}

// int64 total_residual_bytes = 28;
inline void GraphNodeProto::clear_total_residual_bytes() {
  total_residual_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_residual_bytes)
  return total_residual_bytes_;
}
inline void GraphNodeProto::set_total_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_residual_bytes)
}

// int64 total_output_bytes = 29;
inline void GraphNodeProto::clear_total_output_bytes() {
  total_output_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_output_bytes)
  return total_output_bytes_;
}
inline void GraphNodeProto::set_total_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_output_bytes)
}

// int64 total_parameters = 8;
inline void GraphNodeProto::clear_total_parameters() {
  total_parameters_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_parameters)
  return total_parameters_;
}
inline void GraphNodeProto::set_total_parameters(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_parameters_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_parameters)
}

// int64 total_float_ops = 14;
inline void GraphNodeProto::clear_total_float_ops() {
  total_float_ops_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphNodeProto::total_float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_float_ops)
  return total_float_ops_;
}
inline void GraphNodeProto::set_total_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_float_ops)
}

// repeated .tensorflow.TensorShapeProto shapes = 11;
inline int GraphNodeProto::shapes_size() const {
  return shapes_.size();
}
inline ::tensorflow::TensorShapeProto* GraphNodeProto::mutable_shapes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.shapes)
  return shapes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
GraphNodeProto::mutable_shapes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.GraphNodeProto.shapes)
  return &shapes_;
}
inline const ::tensorflow::TensorShapeProto& GraphNodeProto::shapes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.shapes)
  return shapes_.Get(index);
}
inline ::tensorflow::TensorShapeProto* GraphNodeProto::add_shapes() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.shapes)
  return shapes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
GraphNodeProto::shapes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.GraphNodeProto.shapes)
  return shapes_;
}

// map<int32, .tensorflow.TensorShapeProto> input_shapes = 16;
inline int GraphNodeProto::input_shapes_size() const {
  return input_shapes_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TensorShapeProto >&
GraphNodeProto::input_shapes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.GraphNodeProto.input_shapes)
  return input_shapes_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::TensorShapeProto >*
GraphNodeProto::mutable_input_shapes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.GraphNodeProto.input_shapes)
  return input_shapes_.MutableMap();
}

// repeated .tensorflow.tfprof.GraphNodeProto children = 12;
inline int GraphNodeProto::children_size() const {
  return children_.size();
}
inline void GraphNodeProto::clear_children() {
  children_.Clear();
}
inline ::tensorflow::tfprof::GraphNodeProto* GraphNodeProto::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.children)
  return children_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
GraphNodeProto::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.GraphNodeProto.children)
  return &children_;
}
inline const ::tensorflow::tfprof::GraphNodeProto& GraphNodeProto::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.children)
  return children_.Get(index);
}
inline ::tensorflow::tfprof::GraphNodeProto* GraphNodeProto::add_children() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.children)
  return children_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
GraphNodeProto::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.GraphNodeProto.children)
  return children_;
}

// -------------------------------------------------------------------

// MultiGraphNodeProto

// string name = 1;
inline void MultiGraphNodeProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& MultiGraphNodeProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.name)
  return name_.GetNoArena();
}
inline void MultiGraphNodeProto::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.name)
}
inline void MultiGraphNodeProto::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.MultiGraphNodeProto.name)
}
inline void MultiGraphNodeProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.MultiGraphNodeProto.name)
}
inline void MultiGraphNodeProto::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.MultiGraphNodeProto.name)
}
inline std::string* MultiGraphNodeProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.MultiGraphNodeProto.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* MultiGraphNodeProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.MultiGraphNodeProto.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void MultiGraphNodeProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.MultiGraphNodeProto.name)
}

// int64 exec_micros = 2;
inline void MultiGraphNodeProto::clear_exec_micros() {
  exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.exec_micros)
  return exec_micros_;
}
inline void MultiGraphNodeProto::set_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.exec_micros)
}

// int64 accelerator_exec_micros = 12;
inline void MultiGraphNodeProto::clear_accelerator_exec_micros() {
  accelerator_exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.accelerator_exec_micros)
  return accelerator_exec_micros_;
}
inline void MultiGraphNodeProto::set_accelerator_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  accelerator_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.accelerator_exec_micros)
}

// int64 cpu_exec_micros = 13;
inline void MultiGraphNodeProto::clear_cpu_exec_micros() {
  cpu_exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.cpu_exec_micros)
  return cpu_exec_micros_;
}
inline void MultiGraphNodeProto::set_cpu_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  cpu_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.cpu_exec_micros)
}

// int64 requested_bytes = 3;
inline void MultiGraphNodeProto::clear_requested_bytes() {
  requested_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.requested_bytes)
  return requested_bytes_;
}
inline void MultiGraphNodeProto::set_requested_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  requested_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.requested_bytes)
}

// int64 peak_bytes = 16;
inline void MultiGraphNodeProto::clear_peak_bytes() {
  peak_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.peak_bytes)
  return peak_bytes_;
}
inline void MultiGraphNodeProto::set_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.peak_bytes)
}

// int64 residual_bytes = 17;
inline void MultiGraphNodeProto::clear_residual_bytes() {
  residual_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.residual_bytes)
  return residual_bytes_;
}
inline void MultiGraphNodeProto::set_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.residual_bytes)
}

// int64 output_bytes = 18;
inline void MultiGraphNodeProto::clear_output_bytes() {
  output_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.output_bytes)
  return output_bytes_;
}
inline void MultiGraphNodeProto::set_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.output_bytes)
}

// int64 parameters = 4;
inline void MultiGraphNodeProto::clear_parameters() {
  parameters_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.parameters)
  return parameters_;
}
inline void MultiGraphNodeProto::set_parameters(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  parameters_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.parameters)
}

// int64 float_ops = 5;
inline void MultiGraphNodeProto::clear_float_ops() {
  float_ops_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.float_ops)
  return float_ops_;
}
inline void MultiGraphNodeProto::set_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.float_ops)
}

// int64 total_exec_micros = 6;
inline void MultiGraphNodeProto::clear_total_exec_micros() {
  total_exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::total_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_exec_micros)
  return total_exec_micros_;
}
inline void MultiGraphNodeProto::set_total_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_exec_micros)
}

// int64 total_accelerator_exec_micros = 14;
inline void MultiGraphNodeProto::clear_total_accelerator_exec_micros() {
  total_accelerator_exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::total_accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_accelerator_exec_micros)
  return total_accelerator_exec_micros_;
}
inline void MultiGraphNodeProto::set_total_accelerator_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_accelerator_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_accelerator_exec_micros)
}

// int64 total_cpu_exec_micros = 15;
inline void MultiGraphNodeProto::clear_total_cpu_exec_micros() {
  total_cpu_exec_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::total_cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_cpu_exec_micros)
  return total_cpu_exec_micros_;
}
inline void MultiGraphNodeProto::set_total_cpu_exec_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_cpu_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_cpu_exec_micros)
}

// int64 total_requested_bytes = 7;
inline void MultiGraphNodeProto::clear_total_requested_bytes() {
  total_requested_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::total_requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_requested_bytes)
  return total_requested_bytes_;
}
inline void MultiGraphNodeProto::set_total_requested_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_requested_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_requested_bytes)
}

// int64 total_peak_bytes = 19;
inline void MultiGraphNodeProto::clear_total_peak_bytes() {
  total_peak_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::total_peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_peak_bytes)
  return total_peak_bytes_;
}
inline void MultiGraphNodeProto::set_total_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_peak_bytes)
}

// int64 total_residual_bytes = 20;
inline void MultiGraphNodeProto::clear_total_residual_bytes() {
  total_residual_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::total_residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_residual_bytes)
  return total_residual_bytes_;
}
inline void MultiGraphNodeProto::set_total_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_residual_bytes)
}

// int64 total_output_bytes = 21;
inline void MultiGraphNodeProto::clear_total_output_bytes() {
  total_output_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::total_output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_output_bytes)
  return total_output_bytes_;
}
inline void MultiGraphNodeProto::set_total_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_output_bytes)
}

// int64 total_parameters = 8;
inline void MultiGraphNodeProto::clear_total_parameters() {
  total_parameters_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::total_parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_parameters)
  return total_parameters_;
}
inline void MultiGraphNodeProto::set_total_parameters(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_parameters_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_parameters)
}

// int64 total_float_ops = 9;
inline void MultiGraphNodeProto::clear_total_float_ops() {
  total_float_ops_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MultiGraphNodeProto::total_float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_float_ops)
  return total_float_ops_;
}
inline void MultiGraphNodeProto::set_total_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_float_ops)
}

// repeated .tensorflow.tfprof.GraphNodeProto graph_nodes = 10;
inline int MultiGraphNodeProto::graph_nodes_size() const {
  return graph_nodes_.size();
}
inline void MultiGraphNodeProto::clear_graph_nodes() {
  graph_nodes_.Clear();
}
inline ::tensorflow::tfprof::GraphNodeProto* MultiGraphNodeProto::mutable_graph_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return graph_nodes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
MultiGraphNodeProto::mutable_graph_nodes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return &graph_nodes_;
}
inline const ::tensorflow::tfprof::GraphNodeProto& MultiGraphNodeProto::graph_nodes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return graph_nodes_.Get(index);
}
inline ::tensorflow::tfprof::GraphNodeProto* MultiGraphNodeProto::add_graph_nodes() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return graph_nodes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
MultiGraphNodeProto::graph_nodes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return graph_nodes_;
}

// repeated .tensorflow.tfprof.MultiGraphNodeProto children = 11;
inline int MultiGraphNodeProto::children_size() const {
  return children_.size();
}
inline void MultiGraphNodeProto::clear_children() {
  children_.Clear();
}
inline ::tensorflow::tfprof::MultiGraphNodeProto* MultiGraphNodeProto::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.MultiGraphNodeProto.children)
  return children_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >*
MultiGraphNodeProto::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.MultiGraphNodeProto.children)
  return &children_;
}
inline const ::tensorflow::tfprof::MultiGraphNodeProto& MultiGraphNodeProto::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.children)
  return children_.Get(index);
}
inline ::tensorflow::tfprof::MultiGraphNodeProto* MultiGraphNodeProto::add_children() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.MultiGraphNodeProto.children)
  return children_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >&
MultiGraphNodeProto::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.MultiGraphNodeProto.children)
  return children_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// AdviceProto_Checker

// repeated string reports = 2;
inline int AdviceProto_Checker::reports_size() const {
  return reports_.size();
}
inline void AdviceProto_Checker::clear_reports() {
  reports_.Clear();
}
inline const std::string& AdviceProto_Checker::reports(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.AdviceProto.Checker.reports)
  return reports_.Get(index);
}
inline std::string* AdviceProto_Checker::mutable_reports(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.AdviceProto.Checker.reports)
  return reports_.Mutable(index);
}
inline void AdviceProto_Checker::set_reports(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.AdviceProto.Checker.reports)
  reports_.Mutable(index)->assign(value);
}
inline void AdviceProto_Checker::set_reports(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.AdviceProto.Checker.reports)
  reports_.Mutable(index)->assign(std::move(value));
}
inline void AdviceProto_Checker::set_reports(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  reports_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::set_reports(int index, const char* value, size_t size) {
  reports_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline std::string* AdviceProto_Checker::add_reports() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.AdviceProto.Checker.reports)
  return reports_.Add();
}
inline void AdviceProto_Checker::add_reports(const std::string& value) {
  reports_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::add_reports(std::string&& value) {
  reports_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::add_reports(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  reports_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::add_reports(const char* value, size_t size) {
  reports_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
AdviceProto_Checker::reports() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.AdviceProto.Checker.reports)
  return reports_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
AdviceProto_Checker::mutable_reports() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.AdviceProto.Checker.reports)
  return &reports_;
}

// -------------------------------------------------------------------

// AdviceProto

// map<string, .tensorflow.tfprof.AdviceProto.Checker> checkers = 1;
inline int AdviceProto::checkers_size() const {
  return checkers_.size();
}
inline void AdviceProto::clear_checkers() {
  checkers_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >&
AdviceProto::checkers() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.AdviceProto.checkers)
  return checkers_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >*
AdviceProto::mutable_checkers() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.AdviceProto.checkers)
  return checkers_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto
