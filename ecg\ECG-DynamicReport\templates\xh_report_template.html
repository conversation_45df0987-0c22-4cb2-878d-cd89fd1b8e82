<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态心电图报告</title>
    <style>
        body {
            font-family: "Noto Sans CJK SC", sans-serif;
        }
        .section {
            margin: 20px 0;
        }
        h1 {
            text-align: center;
        }
        .prompt{
            margin: 20px 0;
            border: 1px solid #000; /* 设置边框颜色和宽度 */
            height: 300px;
        }
        .prompt p {
            margin: 5px;
        }
        table{
            width: 100%;
        }
        
        /* 表格样式 */
        .full-bordered-table {
            width: 100%; /* 表格宽度 */
            border-collapse: collapse; /* 合并边框 */
            margin: 0;
            padding: 0;
        }
        
        /* 表格头部和单元格的边框 */
        .full-bordered-table td {
            border: 1px solid #000; /* 设置边框颜色和宽度 */
            padding: 5px; /* 单元格内边距 */
            text-align: left; /* 文本对齐方式 */
            vertical-align: top;
        }
        .full-bordered-table span:last-child {
            float: right;
        }
        .title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-top: 0;
        }
        .hourly-table {
          width: 100%; /* 表格宽度 */
          border-collapse: collapse; /* 合并边框 */
          margin: 0;
          padding: 0;
        }
        .hourly-table th, td {
            border: 1px solid #000; /* 设置边框颜色和宽度 */
            padding: 2px; /* 单元格内边距 */
        }
        .hourly-table td {
            text-align: center;
        }
    </style>
</head>
<body style="line-height: 1.5">
    <h1>动态心电图报告</h1>
    <div class="section">
        <table class="full-bordered-table">
            <colgroup>
                <col style="width: 33%;">
                <col style="width: 35%;">
                <col style="width: 32%;">
            </colgroup>
            <tr>
                <td colspan="3">
                    <div style="overflow: hidden;">
                        <div style="float: left; width: 50%;">记录日期：{{ report.record_date }}</div>
                        <div style="float: left; width: 50%;">记录时长：{{ report.record_duration }}</div>
                        <div style="clear: both;"></div>
                    </div>
                    <div style="overflow: hidden;">
                        <div style="float: left; width: 50%;">有效时长：{{ report.effective_duration }}</div>
                        <div style="float: left; width: 50%;">分析时间：{{ report.analysis_time }}</div>
                        <div style="clear: both;"></div>
                    </div>
                    <div style="overflow: hidden;">
                        <div style="float: left; width: 50%;">信号质量：{{ report.signal_quality }}</div>
                        <div style="float: left; width: 50%;"></div>
                        <div style="clear: both;"></div>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div class="title">概述</div>
                    <div>
                        <span>心搏总数：</span>
                        <span>{{ report.hr_total_count }}</span>
                    </div>
                    <div>
                        <span>室性心搏总数：</span>
                        <span>{{ report.pvc_hr_total_count }}</span></div>
                    <div>
                        <span>室上性心搏总数：</span>
                        <span></span></div>
                    <div>
                        <span>房颤/房扑占时比(%)：</span>
                        <span>{{ report.af_afl_total_rate }}</span></div>
                    <div>
                        <span>其他发现：</span>
                        <span></span>
                    </div>
                </td>
                <td>
                    <div class="title">心率（bpm）</div>
                    <div>
                        <span>平均：</span>
                        <span>{{ report.hr_mean }}</span>
                    </div>
                    <div>
                        <span>NN最快：</span>
                        <span>{{ report.nn_max }}</span></div>
                    <div>
                        <span>NN最慢：</span>
                        <span>{{ report.nn_min }}</span></div>
                    <div>
                        <span>小时平均最快：</span>
                        <span>{{ report.hourly_mean_max }}</span></div>
                    <div>
                        <span>小时平均最慢：</span>
                        <span>{{ report.hourly_mean_min }}</span>
                    </div>
                    <div>
                        <span>最长RR：</span>
                        <span>{{ report.longest_rr }}</span>
                    </div>
                    <div>
                        <span>停搏最少（>2.0秒）：</span>
                        <span></span>
                    </div>
                </td>
                <td>
                    <div class="title">房颤 / 房扑</div>
                    <div>
                        <span>总阵数：</span>
                        <span>{{ report.af_afl_total_episodes }}</span>
                    </div>
                    <div>
                        <span>总时长：</span>
                        <span>{{ report.af_afl_total_duration }} min</span></div>
                    <div>
                        <span>总占时比（%）：</span>
                        <span>{{ report.af_afl_total_rate }}</span></div>
                    <div>
                        <span>最快心率：</span>
                        <span>{{ report.af_afl_hr_max }}</span></div>
                    <div>
                        <span>最长持续：</span>
                        <span>{{ report.af_afl_longest_duration }}</span>
                    </div>
                    <div>
                        <span>最长RR：</span>
                        <span>{{ report.af_afl_longest_rr }}</span>
                    </div>
            </tr>
            <tr>
                <td>
                    <div class="title">室性早搏与节律</div>
                    <div>
                        <span>单（个数）：</span>
                        <span>{{ report.pvc_single_count }}</span>
                    </div>
                    <div>
                        <span>成对（阵数）：</span>
                        <span>{{ report.pvc_pair_count }}</span></div>
                    <div>
                        <span>二联（阵数）：</span>
                        <span>{{ report.pvc_bigeminy_count }}</span></div>
                    <div>
                        <span>三联（阵数）：</span>
                        <span>{{ report.pvc_trigeminy_count }}</span></div>
                    <div>
                        <span>连发（阵数）：</span>
                        <span>{{ report.pvc_run_count }}</span>
                    </div>
                    <div>
                        <span>连发最多（个数）：</span>
                        <span>{{ report.pvc_max_consecutive }}</span>
                    </div>
                    <div>
                        <span>连发最快心率：</span>
                        <span>{{ report.pvc_fastest_run_hr }}</span>
                    </div>
                    <div>
                        <span>连发最慢心率：</span>
                        <span>{{ report.pvc_slowest_run_hr }}</span>
                    </div>
                    <div>
                        <span>室性心搏总数：</span>
                        <span>{{ report.pvc_hr_total_count }}</span>
                    </div>
                </td>
                <td>
                    <div class="title">室上性早搏与节律</div>
                    <div>
                        <span>单（个数）：</span>
                        <span></span>
                    </div>
                    <div>
                        <span>成对（阵数）：</span>
                        <span></span></div>
                    <div>
                        <span>二联（阵数）：</span>
                        <span></span></div>
                    <div>
                        <span>三联（阵数）：</span>
                        <span></span></div>
                    <div>
                        <span>连发（阵数）：</span>
                        <span></span>
                    </div>
                    <div>
                        <span>连发最多（个数）：</span>
                        <span></span>
                    </div>
                    <div>
                        <span>连发最快心率：</span>
                        <span></span>
                    </div>
                    <div>
                        <span>连发最慢心率：</span>
                        <span></span>
                    </div>
                    <div>
                        <span>室上性心搏总数：</span>
                        <span></span>
                    </div>
                </td>
                <td>
                    <div class="title">心率变异</div>
                    <div>
                        <span>NN/RR（%）：</span>
                        <span>{{ report.nn_rr_ratio }}</span>
                    </div>
                    <div>
                        <span>时域心率变异</span>
                        <span></span>
                    </div>
                    <div>
                        <span>SDNN（ms）：</span>
                        <span>{{ report.sdnn }}</span></div>
                    <div>
                        <span>SDANN（ms）：</span>
                        <span>{{ report.sdann }}</span></div>
                    <div>
                        <span>SDNNIDX：</span>
                        <span>{{ report.sdnnidx }}</span>
                    </div>
                    <div>
                        <span>rMSSD（ms）：</span>
                        <span>{{ report.rmssd }}</span>
                    </div>
                    <div>
                        <span>pNN20（%）：</span>
                        <span>{{ report.pnn20 }}</span>
                    </div>
                    <div>
                        <span>pNN50（%）：</span>
                        <span>{{ report.pnn50 }}</span>
                    </div>
                    <div>
                        <span>频域心率变异</span>
                        <span></span>
                    </div>
                    <div>
                        <span>TOTAL：</span>
                        <span>{{ report.total }}</span>
                    </div>
                    <div>
                        <span>LF：{{ report.lf }} </span>
                        <span style="text-align: left">HF：{{ report.hf }}</span>
                    </div>
                    <div>
                        <span>VLF：{{ report.vlf }} </span>
                        <span style="text-align: left">LF/HF：{{ report.lf_hf }}</span>
                    </div>
            </tr>
        </table>
    </div>
    <div class="prompt">
        <p style="text-align: center">结论及诊断提示</p>
        <p class="preserve-newlines">
            {{ report.conclusion  }}
        </p>
    </div>
    <div class="section">
        {% for hourly_statistics in report.hourly_statistics_infos %}
            <p style="text-align: center">{{ hourly_statistics.date_str }}（每小时统计）</p>
            <table class="hourly-table">
                <thead>
                    <tr>
                        <th rowspan="2">小时</th>
                        <th rowspan="2">心搏总数</th>
                        <th rowspan="2">SDNN</th>
                        <th colspan="3">RR心率（bpm）</th>
                        <th colspan="4">室性节律（个数）</th>
                        <th colspan="4">室上性节律（个数）</th>
                        <th rowspan="2">停搏个数</th>
                    </tr>
                    <tr>
                        <!-- RR心率子标题 -->
                        <th>平均</th>
                        <th>最快</th>
                        <th>最慢</th>
                        <!-- 室性节律子标题 -->
                        <th>单个</th>
                        <th>成对</th>
                        <th>连发</th>
                        <th>总数</th>
                        <!-- 室上性节律子标题 -->
                        <th>单个</th>
                        <th>成对</th>
                        <th>连发</th>
                        <th>总数</th>
                    </tr>
                </thead>
                <tbody>
                    {% for hourly_detail in hourly_statistics.hourly_details %}
                    <tr>
                        <td>{{ hourly_detail.hour }}</td>
                        <td>{{ hourly_detail.hr_total_count }}</td>
                        <td>{{ hourly_detail.sdnn }}</td>
                        <!-- RR心率数据 -->
                        <td>{{ hourly_detail.hr_mean }}</td>
                        <td>{{ hourly_detail.hr_max }}</td>
                        <td>{{ hourly_detail.hr_min }}</td>
                        <!-- 室性节律数据 -->
                        <td>{{ hourly_detail.pvc_single_count }}</td>
                        <td>{{ hourly_detail.pvc_pair_count }}</td>
                        <td>{{ hourly_detail.pvc_run_count }}</td>
                        <td>{{ hourly_detail.pvc_total_count }}</td>
                        <!-- 室上性节律数据 -->
                        <td>{{ hourly_detail.svpc_single_count }}</td>
                        <td>{{ hourly_detail.svpc_pair_count }}</td>
                        <td>{{ hourly_detail.svpc_run_count }}</td>
                        <td>{{ hourly_detail.svpc_total_count }}</td>
                        <!-- 停搏数据 -->
                        <td>{{ hourly_detail.sa_count }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% endfor %}
    </div>
</body>
</html>