from apps.models.base_model import BaseEntity


class ArrhythmiaEntity(BaseEntity):
    SignalQuantity = None  # 信号质量 1表示信号质量可以，-1表示信号质量差
    RespiratoryRate = None  # 呼吸次数/min 取值范围[10-25]
    ArrhythmiaDiagnosis = None  # 心律失常诊断（0有 or 1没有）
    CADCardiomyopathy = None  # 心肌病冠心病诊断（0 or 1）
    ECGAge = None  # 心脏年龄
    HealthMetrics = None  # 健康指标
    HeartFailureRisk = None  # 心衰风险（0-1）
    VentricularFibrillationRisk = None  # 室颤风险（0-1）
    SyncopeRisk = None  # 晕厥风险（0-1）
    SleepStage = None  # 睡眠阶段 取值范围为（0，1，2，3，4），对应睡眠阶段（Wake，N1， N2， N3, REM）
    OSARisk = None  # 阻塞性睡眠呼吸暂停风险，取值范围为（0-1）, -1代表时长小于两分钟
    PQRSTC = None  # ECG信号指标
    ecg_id = None  # ecg信号分析ID

    def to_entity_dict(self):
        # 将当前实体的属性转换为字典，包括嵌套实体的字典
        return {
            'SignalQuantity': self.SignalQuantity,
            'RespiratoryRate': self.RespiratoryRate,
            'ArrhythmiaDiagnosis': self.ArrhythmiaDiagnosis.to_dict(),
            'CADCardiomyopathy': self.CADCardiomyopathy.to_dict(),
            'ECGAge': self.ECGAge,
            'HealthMetrics': self.HealthMetrics.to_dict(),
            'HeartFailureRisk': self.HeartFailureRisk,
            'VentricularFibrillationRisk': self.VentricularFibrillationRisk,
            'SyncopeRisk': self.SyncopeRisk,
            'SleepStage': self.SleepStage,
            'OSARisk': self.OSARisk,
            'PQRSTC': self.PQRSTC.to_dict(),
            'ecg_id': self.ecg_id,
        }


class ArrhythmiaDiagnosisEntity(BaseEntity):
    """
    心律失常诊断 0有，1没有
    """

    def __init__(self):
        self.SN = 0  # 窦性心律
        self.SNA = 0  # 窦性心律不齐
        self.SNT = 0  # 窦性心动过速
        self.SNB = 0  # 窦性心动过缓
        self.PVC = 0  # 室性早搏
        self.PSC = 0  # 不确定的早搏类型
        self.PJC = 0  # 交界性早搏
        self.PAC = 0  # 房性早搏
        self.VT = 0  # 室性心动过速
        self.SVT = 0  # 室上性心动过速
        self.AFL = 0  # 心房扑动
        self.AF = 0  # 心房颤动
        self.WPW = 0  # 应激综合征
        self.VE = 0  # 室性逸搏
        self.JE = 0  # 交界性逸搏
        self.AE = 0  # 房性逸搏
        self.AVBI = 0  # 一度房室传导阻滞
        self.AVBII = 0  # 二度房室传导阻滞
        self.AVBIII = 0  # 三度房室传导阻滞
        self.IVB = 0  # 室内传导阻滞
        self.LBBB = 0  # 左束支传导阻滞
        self.RBBB = 0  # 右束支传导阻滞
        self.LAFB = 0  # 左前分支传导阻滞
        self.BRU = 0  # Brugada综合征
        self.LQT = 0  # QT间期延长
        self.bPVC = 0  # 成对室早


class CADCardiomyopathyEntity(BaseEntity):
    """
    心肌病冠心病诊断 0有，1没有
    """

    def __init__(self):
        self.ISC = 0  # 心肌缺血
        self.LVH = 0  # 左心室肥大
        self.RVH = 0  # 右心室肥大
        self.LAH = 0  # 左心房肥大
        self.RAH = 0  # 右心房肥大
        self.MI = 0  # 心肌梗死


class HealthMetricsEntity(BaseEntity):
    """
    健康指标
    """

    def __init__(self):
        self.Pressure = 0  # 压力指数 (0-100)
        self.HRV = 0  # 心率变异性 （>0 ；正常参考值：20-89)
        self.Emotion = 0  # 情绪指数(0-100)
        self.Fatigue = 0  # 疲劳指数(0-100)
        self.Vitality = 0  # 活力指数(0-100)


class PQRSTCEntity(BaseEntity):
    """
    ECG信号指标
    """

    def __init__(self):
        self.HR = 0  # 心率，单位次/min
        self.QRS_duration = 0  # QRS间期，单位毫秒
        self.QT = 0  # QT间期，单位毫秒
        self.QTc = 0  # QTc间期，单位毫秒，单位毫秒
        self.P_duration = 0  # P波时长，单位毫秒
        self.PR_interval = 0  # PR间期，单位毫秒
        self.ST_duration = 0  # ST间期，单位毫秒
