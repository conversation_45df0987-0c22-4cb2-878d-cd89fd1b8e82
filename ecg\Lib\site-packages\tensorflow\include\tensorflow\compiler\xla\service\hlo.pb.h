// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/xla/service/hlo.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/compiler/xla/xla_data.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[25]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
namespace xla {
class BufferAllocationProto;
class BufferAllocationProtoDefaultTypeInternal;
extern BufferAllocationProtoDefaultTypeInternal _BufferAllocationProto_default_instance_;
class BufferAllocationProto_Assigned;
class BufferAllocationProto_AssignedDefaultTypeInternal;
extern BufferAllocationProto_AssignedDefaultTypeInternal _BufferAllocationProto_Assigned_default_instance_;
class BufferAssignmentProto;
class BufferAssignmentProtoDefaultTypeInternal;
extern BufferAssignmentProtoDefaultTypeInternal _BufferAssignmentProto_default_instance_;
class BufferAssignmentProto_BufferAlias;
class BufferAssignmentProto_BufferAliasDefaultTypeInternal;
extern BufferAssignmentProto_BufferAliasDefaultTypeInternal _BufferAssignmentProto_BufferAlias_default_instance_;
class CrossProgramPrefetch;
class CrossProgramPrefetchDefaultTypeInternal;
extern CrossProgramPrefetchDefaultTypeInternal _CrossProgramPrefetch_default_instance_;
class DynamicParameterBindingProto;
class DynamicParameterBindingProtoDefaultTypeInternal;
extern DynamicParameterBindingProtoDefaultTypeInternal _DynamicParameterBindingProto_default_instance_;
class DynamicParameterBindingProto_Binding;
class DynamicParameterBindingProto_BindingDefaultTypeInternal;
extern DynamicParameterBindingProto_BindingDefaultTypeInternal _DynamicParameterBindingProto_Binding_default_instance_;
class HeapSimulatorTrace;
class HeapSimulatorTraceDefaultTypeInternal;
extern HeapSimulatorTraceDefaultTypeInternal _HeapSimulatorTrace_default_instance_;
class HeapSimulatorTrace_Event;
class HeapSimulatorTrace_EventDefaultTypeInternal;
extern HeapSimulatorTrace_EventDefaultTypeInternal _HeapSimulatorTrace_Event_default_instance_;
class HloComputationProto;
class HloComputationProtoDefaultTypeInternal;
extern HloComputationProtoDefaultTypeInternal _HloComputationProto_default_instance_;
class HloInputOutputAliasProto;
class HloInputOutputAliasProtoDefaultTypeInternal;
extern HloInputOutputAliasProtoDefaultTypeInternal _HloInputOutputAliasProto_default_instance_;
class HloInputOutputAliasProto_AliasEntryProto;
class HloInputOutputAliasProto_AliasEntryProtoDefaultTypeInternal;
extern HloInputOutputAliasProto_AliasEntryProtoDefaultTypeInternal _HloInputOutputAliasProto_AliasEntryProto_default_instance_;
class HloInstructionProto;
class HloInstructionProtoDefaultTypeInternal;
extern HloInstructionProtoDefaultTypeInternal _HloInstructionProto_default_instance_;
class HloInstructionProto_SliceDimensions;
class HloInstructionProto_SliceDimensionsDefaultTypeInternal;
extern HloInstructionProto_SliceDimensionsDefaultTypeInternal _HloInstructionProto_SliceDimensions_default_instance_;
class HloModuleGroupProto;
class HloModuleGroupProtoDefaultTypeInternal;
extern HloModuleGroupProtoDefaultTypeInternal _HloModuleGroupProto_default_instance_;
class HloModuleMetadataProto;
class HloModuleMetadataProtoDefaultTypeInternal;
extern HloModuleMetadataProtoDefaultTypeInternal _HloModuleMetadataProto_default_instance_;
class HloModuleProto;
class HloModuleProtoDefaultTypeInternal;
extern HloModuleProtoDefaultTypeInternal _HloModuleProto_default_instance_;
class HloPassMetadata;
class HloPassMetadataDefaultTypeInternal;
extern HloPassMetadataDefaultTypeInternal _HloPassMetadata_default_instance_;
class HloProto;
class HloProtoDefaultTypeInternal;
extern HloProtoDefaultTypeInternal _HloProto_default_instance_;
class HloScheduleProto;
class HloScheduleProtoDefaultTypeInternal;
extern HloScheduleProtoDefaultTypeInternal _HloScheduleProto_default_instance_;
class HloScheduleProto_InstructionSequence;
class HloScheduleProto_InstructionSequenceDefaultTypeInternal;
extern HloScheduleProto_InstructionSequenceDefaultTypeInternal _HloScheduleProto_InstructionSequence_default_instance_;
class HloScheduleProto_SequencesEntry_DoNotUse;
class HloScheduleProto_SequencesEntry_DoNotUseDefaultTypeInternal;
extern HloScheduleProto_SequencesEntry_DoNotUseDefaultTypeInternal _HloScheduleProto_SequencesEntry_DoNotUse_default_instance_;
class HloSnapshot;
class HloSnapshotDefaultTypeInternal;
extern HloSnapshotDefaultTypeInternal _HloSnapshot_default_instance_;
class LogicalBufferProto;
class LogicalBufferProtoDefaultTypeInternal;
extern LogicalBufferProtoDefaultTypeInternal _LogicalBufferProto_default_instance_;
class LogicalBufferProto_Location;
class LogicalBufferProto_LocationDefaultTypeInternal;
extern LogicalBufferProto_LocationDefaultTypeInternal _LogicalBufferProto_Location_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::BufferAllocationProto* Arena::CreateMaybeMessage<::xla::BufferAllocationProto>(Arena*);
template<> ::xla::BufferAllocationProto_Assigned* Arena::CreateMaybeMessage<::xla::BufferAllocationProto_Assigned>(Arena*);
template<> ::xla::BufferAssignmentProto* Arena::CreateMaybeMessage<::xla::BufferAssignmentProto>(Arena*);
template<> ::xla::BufferAssignmentProto_BufferAlias* Arena::CreateMaybeMessage<::xla::BufferAssignmentProto_BufferAlias>(Arena*);
template<> ::xla::CrossProgramPrefetch* Arena::CreateMaybeMessage<::xla::CrossProgramPrefetch>(Arena*);
template<> ::xla::DynamicParameterBindingProto* Arena::CreateMaybeMessage<::xla::DynamicParameterBindingProto>(Arena*);
template<> ::xla::DynamicParameterBindingProto_Binding* Arena::CreateMaybeMessage<::xla::DynamicParameterBindingProto_Binding>(Arena*);
template<> ::xla::HeapSimulatorTrace* Arena::CreateMaybeMessage<::xla::HeapSimulatorTrace>(Arena*);
template<> ::xla::HeapSimulatorTrace_Event* Arena::CreateMaybeMessage<::xla::HeapSimulatorTrace_Event>(Arena*);
template<> ::xla::HloComputationProto* Arena::CreateMaybeMessage<::xla::HloComputationProto>(Arena*);
template<> ::xla::HloInputOutputAliasProto* Arena::CreateMaybeMessage<::xla::HloInputOutputAliasProto>(Arena*);
template<> ::xla::HloInputOutputAliasProto_AliasEntryProto* Arena::CreateMaybeMessage<::xla::HloInputOutputAliasProto_AliasEntryProto>(Arena*);
template<> ::xla::HloInstructionProto* Arena::CreateMaybeMessage<::xla::HloInstructionProto>(Arena*);
template<> ::xla::HloInstructionProto_SliceDimensions* Arena::CreateMaybeMessage<::xla::HloInstructionProto_SliceDimensions>(Arena*);
template<> ::xla::HloModuleGroupProto* Arena::CreateMaybeMessage<::xla::HloModuleGroupProto>(Arena*);
template<> ::xla::HloModuleMetadataProto* Arena::CreateMaybeMessage<::xla::HloModuleMetadataProto>(Arena*);
template<> ::xla::HloModuleProto* Arena::CreateMaybeMessage<::xla::HloModuleProto>(Arena*);
template<> ::xla::HloPassMetadata* Arena::CreateMaybeMessage<::xla::HloPassMetadata>(Arena*);
template<> ::xla::HloProto* Arena::CreateMaybeMessage<::xla::HloProto>(Arena*);
template<> ::xla::HloScheduleProto* Arena::CreateMaybeMessage<::xla::HloScheduleProto>(Arena*);
template<> ::xla::HloScheduleProto_InstructionSequence* Arena::CreateMaybeMessage<::xla::HloScheduleProto_InstructionSequence>(Arena*);
template<> ::xla::HloScheduleProto_SequencesEntry_DoNotUse* Arena::CreateMaybeMessage<::xla::HloScheduleProto_SequencesEntry_DoNotUse>(Arena*);
template<> ::xla::HloSnapshot* Arena::CreateMaybeMessage<::xla::HloSnapshot>(Arena*);
template<> ::xla::LogicalBufferProto* Arena::CreateMaybeMessage<::xla::LogicalBufferProto>(Arena*);
template<> ::xla::LogicalBufferProto_Location* Arena::CreateMaybeMessage<::xla::LogicalBufferProto_Location>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

enum HeapSimulatorTrace_Event_Kind : int {
  HeapSimulatorTrace_Event_Kind_ALLOC = 0,
  HeapSimulatorTrace_Event_Kind_FREE = 1,
  HeapSimulatorTrace_Event_Kind_SHARE_WITH = 2,
  HeapSimulatorTrace_Event_Kind_HeapSimulatorTrace_Event_Kind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  HeapSimulatorTrace_Event_Kind_HeapSimulatorTrace_Event_Kind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool HeapSimulatorTrace_Event_Kind_IsValid(int value);
constexpr HeapSimulatorTrace_Event_Kind HeapSimulatorTrace_Event_Kind_Kind_MIN = HeapSimulatorTrace_Event_Kind_ALLOC;
constexpr HeapSimulatorTrace_Event_Kind HeapSimulatorTrace_Event_Kind_Kind_MAX = HeapSimulatorTrace_Event_Kind_SHARE_WITH;
constexpr int HeapSimulatorTrace_Event_Kind_Kind_ARRAYSIZE = HeapSimulatorTrace_Event_Kind_Kind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HeapSimulatorTrace_Event_Kind_descriptor();
template<typename T>
inline const std::string& HeapSimulatorTrace_Event_Kind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HeapSimulatorTrace_Event_Kind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HeapSimulatorTrace_Event_Kind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HeapSimulatorTrace_Event_Kind_descriptor(), enum_t_value);
}
inline bool HeapSimulatorTrace_Event_Kind_Parse(
    const std::string& name, HeapSimulatorTrace_Event_Kind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HeapSimulatorTrace_Event_Kind>(
    HeapSimulatorTrace_Event_Kind_descriptor(), name, value);
}
enum CustomCallSchedule : int {
  SCHEDULE_NONE = 0,
  SCHEDULE_LATEST = 1,
  SCHEDULE_EARLIEST = 2,
  CustomCallSchedule_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  CustomCallSchedule_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool CustomCallSchedule_IsValid(int value);
constexpr CustomCallSchedule CustomCallSchedule_MIN = SCHEDULE_NONE;
constexpr CustomCallSchedule CustomCallSchedule_MAX = SCHEDULE_EARLIEST;
constexpr int CustomCallSchedule_ARRAYSIZE = CustomCallSchedule_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CustomCallSchedule_descriptor();
template<typename T>
inline const std::string& CustomCallSchedule_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CustomCallSchedule>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CustomCallSchedule_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CustomCallSchedule_descriptor(), enum_t_value);
}
inline bool CustomCallSchedule_Parse(
    const std::string& name, CustomCallSchedule* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CustomCallSchedule>(
    CustomCallSchedule_descriptor(), name, value);
}
enum Kind : int {
  UNDEFINED_ALIAS = 0,
  MAY_ALIAS = 1,
  MUST_ALIAS = 2,
  Kind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  Kind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool Kind_IsValid(int value);
constexpr Kind Kind_MIN = UNDEFINED_ALIAS;
constexpr Kind Kind_MAX = MUST_ALIAS;
constexpr int Kind_ARRAYSIZE = Kind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Kind_descriptor();
template<typename T>
inline const std::string& Kind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Kind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Kind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Kind_descriptor(), enum_t_value);
}
inline bool Kind_Parse(
    const std::string& name, Kind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Kind>(
    Kind_descriptor(), name, value);
}
// ===================================================================

class HloInstructionProto_SliceDimensions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloInstructionProto.SliceDimensions) */ {
 public:
  HloInstructionProto_SliceDimensions();
  virtual ~HloInstructionProto_SliceDimensions();

  HloInstructionProto_SliceDimensions(const HloInstructionProto_SliceDimensions& from);
  HloInstructionProto_SliceDimensions(HloInstructionProto_SliceDimensions&& from) noexcept
    : HloInstructionProto_SliceDimensions() {
    *this = ::std::move(from);
  }

  inline HloInstructionProto_SliceDimensions& operator=(const HloInstructionProto_SliceDimensions& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloInstructionProto_SliceDimensions& operator=(HloInstructionProto_SliceDimensions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloInstructionProto_SliceDimensions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloInstructionProto_SliceDimensions* internal_default_instance() {
    return reinterpret_cast<const HloInstructionProto_SliceDimensions*>(
               &_HloInstructionProto_SliceDimensions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(HloInstructionProto_SliceDimensions& a, HloInstructionProto_SliceDimensions& b) {
    a.Swap(&b);
  }
  inline void Swap(HloInstructionProto_SliceDimensions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloInstructionProto_SliceDimensions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloInstructionProto_SliceDimensions* New() const final {
    return CreateMaybeMessage<HloInstructionProto_SliceDimensions>(nullptr);
  }

  HloInstructionProto_SliceDimensions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloInstructionProto_SliceDimensions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloInstructionProto_SliceDimensions& from);
  void MergeFrom(const HloInstructionProto_SliceDimensions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloInstructionProto_SliceDimensions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloInstructionProto.SliceDimensions";
  }
  protected:
  explicit HloInstructionProto_SliceDimensions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStartFieldNumber = 1,
    kLimitFieldNumber = 2,
    kStrideFieldNumber = 3,
  };
  // int64 start = 1;
  void clear_start();
  ::PROTOBUF_NAMESPACE_ID::int64 start() const;
  void set_start(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 limit = 2;
  void clear_limit();
  ::PROTOBUF_NAMESPACE_ID::int64 limit() const;
  void set_limit(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 stride = 3;
  void clear_stride();
  ::PROTOBUF_NAMESPACE_ID::int64 stride() const;
  void set_stride(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.HloInstructionProto.SliceDimensions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 start_;
  ::PROTOBUF_NAMESPACE_ID::int64 limit_;
  ::PROTOBUF_NAMESPACE_ID::int64 stride_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloInstructionProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloInstructionProto) */ {
 public:
  HloInstructionProto();
  virtual ~HloInstructionProto();

  HloInstructionProto(const HloInstructionProto& from);
  HloInstructionProto(HloInstructionProto&& from) noexcept
    : HloInstructionProto() {
    *this = ::std::move(from);
  }

  inline HloInstructionProto& operator=(const HloInstructionProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloInstructionProto& operator=(HloInstructionProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloInstructionProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloInstructionProto* internal_default_instance() {
    return reinterpret_cast<const HloInstructionProto*>(
               &_HloInstructionProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(HloInstructionProto& a, HloInstructionProto& b) {
    a.Swap(&b);
  }
  inline void Swap(HloInstructionProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloInstructionProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloInstructionProto* New() const final {
    return CreateMaybeMessage<HloInstructionProto>(nullptr);
  }

  HloInstructionProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloInstructionProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloInstructionProto& from);
  void MergeFrom(const HloInstructionProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloInstructionProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloInstructionProto";
  }
  protected:
  explicit HloInstructionProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef HloInstructionProto_SliceDimensions SliceDimensions;

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionsFieldNumber = 14,
    kSliceDimensionsFieldNumber = 17,
    kDynamicSliceSizesFieldNumber = 20,
    kFftLengthFieldNumber = 32,
    kGatherSliceSizesFieldNumber = 34,
    kOperandIdsFieldNumber = 36,
    kControlPredecessorIdsFieldNumber = 37,
    kCalledComputationIdsFieldNumber = 38,
    kReplicaGroupsFieldNumber = 49,
    kSourceTargetPairsFieldNumber = 52,
    kOperandShapesWithLayoutFieldNumber = 57,
    kOuterDimensionPartitionsFieldNumber = 64,
    kCustomCallOutputOperandAliasingFieldNumber = 74,
    kNameFieldNumber = 1,
    kOpcodeFieldNumber = 2,
    kFusionKindFieldNumber = 11,
    kOutfeedConfigFieldNumber = 22,
    kInfeedConfigFieldNumber = 27,
    kCustomCallTargetFieldNumber = 28,
    kChannelNameFieldNumber = 41,
    kBackendConfigFieldNumber = 43,
    kComparisonDirectionFieldNumber = 63,
    kComparisonTypeFieldNumber = 72,
    kShapeFieldNumber = 3,
    kMetadataFieldNumber = 7,
    kLiteralFieldNumber = 8,
    kWindowFieldNumber = 15,
    kConvolutionDimensionNumbersFieldNumber = 16,
    kPaddingConfigFieldNumber = 21,
    kOutfeedShapeFieldNumber = 29,
    kDotDimensionNumbersFieldNumber = 30,
    kGatherDimensionNumbersFieldNumber = 33,
    kShardingFieldNumber = 40,
    kScatterDimensionNumbersFieldNumber = 48,
    kPrecisionConfigFieldNumber = 51,
    kDomainEntryShardingFieldNumber = 54,
    kDomainExitShardingFieldNumber = 55,
    kTriangularSolveOptionsFieldNumber = 59,
    kParameterReplicationFieldNumber = 61,
    kCholeskyOptionsFieldNumber = 62,
    kFrontendAttributesFieldNumber = 68,
    kParameterNumberFieldNumber = 9,
    kTupleIndexFieldNumber = 13,
    kExponentBitsFieldNumber = 18,
    kMantissaBitsFieldNumber = 19,
    kDistributionFieldNumber = 23,
    kEpsilonFieldNumber = 24,
    kFeatureIndexFieldNumber = 25,
    kChannelIdFieldNumber = 26,
    kIdFieldNumber = 35,
    kCostEstimateNsFieldNumber = 42,
    kAllReduceIdFieldNumber = 45,
    kFeatureGroupCountFieldNumber = 50,
    kFftTypeFieldNumber = 31,
    kUseGlobalDeviceIdsFieldNumber = 71,
    kIsHostTransferFieldNumber = 47,
    kIsStableFieldNumber = 60,
    kConstrainLayoutFieldNumber = 56,
    kBatchGroupCountFieldNumber = 58,
    kDeltaFieldNumber = 66,
    kCustomCallHasSideEffectFieldNumber = 65,
    kIndicesAreSortedFieldNumber = 67,
    kUniqueIndicesFieldNumber = 69,
    kIsCrossProgramPrefetchFieldNumber = 73,
    kRngAlgorithmFieldNumber = 70,
    kPaddingTypeFieldNumber = 75,
    kCustomCallScheduleFieldNumber = 76,
  };
  // repeated int64 dimensions = 14;
  int dimensions_size() const;
  void clear_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 dimensions(int index) const;
  void set_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_dimensions();

  // repeated .xla.HloInstructionProto.SliceDimensions slice_dimensions = 17;
  int slice_dimensions_size() const;
  void clear_slice_dimensions();
  ::xla::HloInstructionProto_SliceDimensions* mutable_slice_dimensions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto_SliceDimensions >*
      mutable_slice_dimensions();
  const ::xla::HloInstructionProto_SliceDimensions& slice_dimensions(int index) const;
  ::xla::HloInstructionProto_SliceDimensions* add_slice_dimensions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto_SliceDimensions >&
      slice_dimensions() const;

  // repeated int64 dynamic_slice_sizes = 20;
  int dynamic_slice_sizes_size() const;
  void clear_dynamic_slice_sizes();
  ::PROTOBUF_NAMESPACE_ID::int64 dynamic_slice_sizes(int index) const;
  void set_dynamic_slice_sizes(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_dynamic_slice_sizes(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      dynamic_slice_sizes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_dynamic_slice_sizes();

  // repeated int64 fft_length = 32;
  int fft_length_size() const;
  void clear_fft_length();
  ::PROTOBUF_NAMESPACE_ID::int64 fft_length(int index) const;
  void set_fft_length(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_fft_length(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      fft_length() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_fft_length();

  // repeated int64 gather_slice_sizes = 34;
  int gather_slice_sizes_size() const;
  void clear_gather_slice_sizes();
  ::PROTOBUF_NAMESPACE_ID::int64 gather_slice_sizes(int index) const;
  void set_gather_slice_sizes(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_gather_slice_sizes(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      gather_slice_sizes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_gather_slice_sizes();

  // repeated int64 operand_ids = 36;
  int operand_ids_size() const;
  void clear_operand_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 operand_ids(int index) const;
  void set_operand_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_operand_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      operand_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_operand_ids();

  // repeated int64 control_predecessor_ids = 37;
  int control_predecessor_ids_size() const;
  void clear_control_predecessor_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 control_predecessor_ids(int index) const;
  void set_control_predecessor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_control_predecessor_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      control_predecessor_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_control_predecessor_ids();

  // repeated int64 called_computation_ids = 38;
  int called_computation_ids_size() const;
  void clear_called_computation_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 called_computation_ids(int index) const;
  void set_called_computation_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_called_computation_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      called_computation_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_called_computation_ids();

  // repeated .xla.ReplicaGroup replica_groups = 49;
  int replica_groups_size() const;
  void clear_replica_groups();
  ::xla::ReplicaGroup* mutable_replica_groups(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ReplicaGroup >*
      mutable_replica_groups();
  const ::xla::ReplicaGroup& replica_groups(int index) const;
  ::xla::ReplicaGroup* add_replica_groups();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ReplicaGroup >&
      replica_groups() const;

  // repeated .xla.SourceTarget source_target_pairs = 52;
  int source_target_pairs_size() const;
  void clear_source_target_pairs();
  ::xla::SourceTarget* mutable_source_target_pairs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::SourceTarget >*
      mutable_source_target_pairs();
  const ::xla::SourceTarget& source_target_pairs(int index) const;
  ::xla::SourceTarget* add_source_target_pairs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::SourceTarget >&
      source_target_pairs() const;

  // repeated .xla.ShapeProto operand_shapes_with_layout = 57;
  int operand_shapes_with_layout_size() const;
  void clear_operand_shapes_with_layout();
  ::xla::ShapeProto* mutable_operand_shapes_with_layout(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
      mutable_operand_shapes_with_layout();
  const ::xla::ShapeProto& operand_shapes_with_layout(int index) const;
  ::xla::ShapeProto* add_operand_shapes_with_layout();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
      operand_shapes_with_layout() const;

  // repeated int64 outer_dimension_partitions = 64;
  int outer_dimension_partitions_size() const;
  void clear_outer_dimension_partitions();
  ::PROTOBUF_NAMESPACE_ID::int64 outer_dimension_partitions(int index) const;
  void set_outer_dimension_partitions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_outer_dimension_partitions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      outer_dimension_partitions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_outer_dimension_partitions();

  // repeated .xla.CustomCallOutputOperandAliasing custom_call_output_operand_aliasing = 74;
  int custom_call_output_operand_aliasing_size() const;
  void clear_custom_call_output_operand_aliasing();
  ::xla::CustomCallOutputOperandAliasing* mutable_custom_call_output_operand_aliasing(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::CustomCallOutputOperandAliasing >*
      mutable_custom_call_output_operand_aliasing();
  const ::xla::CustomCallOutputOperandAliasing& custom_call_output_operand_aliasing(int index) const;
  ::xla::CustomCallOutputOperandAliasing* add_custom_call_output_operand_aliasing();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::CustomCallOutputOperandAliasing >&
      custom_call_output_operand_aliasing() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string opcode = 2;
  void clear_opcode();
  const std::string& opcode() const;
  void set_opcode(const std::string& value);
  void set_opcode(std::string&& value);
  void set_opcode(const char* value);
  void set_opcode(const char* value, size_t size);
  std::string* mutable_opcode();
  std::string* release_opcode();
  void set_allocated_opcode(std::string* opcode);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_opcode();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_opcode(
      std::string* opcode);

  // string fusion_kind = 11;
  void clear_fusion_kind();
  const std::string& fusion_kind() const;
  void set_fusion_kind(const std::string& value);
  void set_fusion_kind(std::string&& value);
  void set_fusion_kind(const char* value);
  void set_fusion_kind(const char* value, size_t size);
  std::string* mutable_fusion_kind();
  std::string* release_fusion_kind();
  void set_allocated_fusion_kind(std::string* fusion_kind);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_fusion_kind();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_fusion_kind(
      std::string* fusion_kind);

  // bytes outfeed_config = 22;
  void clear_outfeed_config();
  const std::string& outfeed_config() const;
  void set_outfeed_config(const std::string& value);
  void set_outfeed_config(std::string&& value);
  void set_outfeed_config(const char* value);
  void set_outfeed_config(const void* value, size_t size);
  std::string* mutable_outfeed_config();
  std::string* release_outfeed_config();
  void set_allocated_outfeed_config(std::string* outfeed_config);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_outfeed_config();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_outfeed_config(
      std::string* outfeed_config);

  // bytes infeed_config = 27;
  void clear_infeed_config();
  const std::string& infeed_config() const;
  void set_infeed_config(const std::string& value);
  void set_infeed_config(std::string&& value);
  void set_infeed_config(const char* value);
  void set_infeed_config(const void* value, size_t size);
  std::string* mutable_infeed_config();
  std::string* release_infeed_config();
  void set_allocated_infeed_config(std::string* infeed_config);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_infeed_config();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_infeed_config(
      std::string* infeed_config);

  // string custom_call_target = 28;
  void clear_custom_call_target();
  const std::string& custom_call_target() const;
  void set_custom_call_target(const std::string& value);
  void set_custom_call_target(std::string&& value);
  void set_custom_call_target(const char* value);
  void set_custom_call_target(const char* value, size_t size);
  std::string* mutable_custom_call_target();
  std::string* release_custom_call_target();
  void set_allocated_custom_call_target(std::string* custom_call_target);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_custom_call_target();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_custom_call_target(
      std::string* custom_call_target);

  // string channel_name = 41;
  void clear_channel_name();
  const std::string& channel_name() const;
  void set_channel_name(const std::string& value);
  void set_channel_name(std::string&& value);
  void set_channel_name(const char* value);
  void set_channel_name(const char* value, size_t size);
  std::string* mutable_channel_name();
  std::string* release_channel_name();
  void set_allocated_channel_name(std::string* channel_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_channel_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_channel_name(
      std::string* channel_name);

  // bytes backend_config = 43;
  void clear_backend_config();
  const std::string& backend_config() const;
  void set_backend_config(const std::string& value);
  void set_backend_config(std::string&& value);
  void set_backend_config(const char* value);
  void set_backend_config(const void* value, size_t size);
  std::string* mutable_backend_config();
  std::string* release_backend_config();
  void set_allocated_backend_config(std::string* backend_config);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_backend_config();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_backend_config(
      std::string* backend_config);

  // string comparison_direction = 63;
  void clear_comparison_direction();
  const std::string& comparison_direction() const;
  void set_comparison_direction(const std::string& value);
  void set_comparison_direction(std::string&& value);
  void set_comparison_direction(const char* value);
  void set_comparison_direction(const char* value, size_t size);
  std::string* mutable_comparison_direction();
  std::string* release_comparison_direction();
  void set_allocated_comparison_direction(std::string* comparison_direction);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_comparison_direction();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_comparison_direction(
      std::string* comparison_direction);

  // string comparison_type = 72;
  void clear_comparison_type();
  const std::string& comparison_type() const;
  void set_comparison_type(const std::string& value);
  void set_comparison_type(std::string&& value);
  void set_comparison_type(const char* value);
  void set_comparison_type(const char* value, size_t size);
  std::string* mutable_comparison_type();
  std::string* release_comparison_type();
  void set_allocated_comparison_type(std::string* comparison_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_comparison_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_comparison_type(
      std::string* comparison_type);

  // .xla.ShapeProto shape = 3;
  bool has_shape() const;
  void clear_shape();
  const ::xla::ShapeProto& shape() const;
  ::xla::ShapeProto* release_shape();
  ::xla::ShapeProto* mutable_shape();
  void set_allocated_shape(::xla::ShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::xla::ShapeProto* shape);
  ::xla::ShapeProto* unsafe_arena_release_shape();

  // .xla.OpMetadata metadata = 7;
  bool has_metadata() const;
  void clear_metadata();
  const ::xla::OpMetadata& metadata() const;
  ::xla::OpMetadata* release_metadata();
  ::xla::OpMetadata* mutable_metadata();
  void set_allocated_metadata(::xla::OpMetadata* metadata);
  void unsafe_arena_set_allocated_metadata(
      ::xla::OpMetadata* metadata);
  ::xla::OpMetadata* unsafe_arena_release_metadata();

  // .xla.LiteralProto literal = 8;
  bool has_literal() const;
  void clear_literal();
  const ::xla::LiteralProto& literal() const;
  ::xla::LiteralProto* release_literal();
  ::xla::LiteralProto* mutable_literal();
  void set_allocated_literal(::xla::LiteralProto* literal);
  void unsafe_arena_set_allocated_literal(
      ::xla::LiteralProto* literal);
  ::xla::LiteralProto* unsafe_arena_release_literal();

  // .xla.Window window = 15;
  bool has_window() const;
  void clear_window();
  const ::xla::Window& window() const;
  ::xla::Window* release_window();
  ::xla::Window* mutable_window();
  void set_allocated_window(::xla::Window* window);
  void unsafe_arena_set_allocated_window(
      ::xla::Window* window);
  ::xla::Window* unsafe_arena_release_window();

  // .xla.ConvolutionDimensionNumbers convolution_dimension_numbers = 16;
  bool has_convolution_dimension_numbers() const;
  void clear_convolution_dimension_numbers();
  const ::xla::ConvolutionDimensionNumbers& convolution_dimension_numbers() const;
  ::xla::ConvolutionDimensionNumbers* release_convolution_dimension_numbers();
  ::xla::ConvolutionDimensionNumbers* mutable_convolution_dimension_numbers();
  void set_allocated_convolution_dimension_numbers(::xla::ConvolutionDimensionNumbers* convolution_dimension_numbers);
  void unsafe_arena_set_allocated_convolution_dimension_numbers(
      ::xla::ConvolutionDimensionNumbers* convolution_dimension_numbers);
  ::xla::ConvolutionDimensionNumbers* unsafe_arena_release_convolution_dimension_numbers();

  // .xla.PaddingConfig padding_config = 21;
  bool has_padding_config() const;
  void clear_padding_config();
  const ::xla::PaddingConfig& padding_config() const;
  ::xla::PaddingConfig* release_padding_config();
  ::xla::PaddingConfig* mutable_padding_config();
  void set_allocated_padding_config(::xla::PaddingConfig* padding_config);
  void unsafe_arena_set_allocated_padding_config(
      ::xla::PaddingConfig* padding_config);
  ::xla::PaddingConfig* unsafe_arena_release_padding_config();

  // .xla.ShapeProto outfeed_shape = 29;
  bool has_outfeed_shape() const;
  void clear_outfeed_shape();
  const ::xla::ShapeProto& outfeed_shape() const;
  ::xla::ShapeProto* release_outfeed_shape();
  ::xla::ShapeProto* mutable_outfeed_shape();
  void set_allocated_outfeed_shape(::xla::ShapeProto* outfeed_shape);
  void unsafe_arena_set_allocated_outfeed_shape(
      ::xla::ShapeProto* outfeed_shape);
  ::xla::ShapeProto* unsafe_arena_release_outfeed_shape();

  // .xla.DotDimensionNumbers dot_dimension_numbers = 30;
  bool has_dot_dimension_numbers() const;
  void clear_dot_dimension_numbers();
  const ::xla::DotDimensionNumbers& dot_dimension_numbers() const;
  ::xla::DotDimensionNumbers* release_dot_dimension_numbers();
  ::xla::DotDimensionNumbers* mutable_dot_dimension_numbers();
  void set_allocated_dot_dimension_numbers(::xla::DotDimensionNumbers* dot_dimension_numbers);
  void unsafe_arena_set_allocated_dot_dimension_numbers(
      ::xla::DotDimensionNumbers* dot_dimension_numbers);
  ::xla::DotDimensionNumbers* unsafe_arena_release_dot_dimension_numbers();

  // .xla.GatherDimensionNumbers gather_dimension_numbers = 33;
  bool has_gather_dimension_numbers() const;
  void clear_gather_dimension_numbers();
  const ::xla::GatherDimensionNumbers& gather_dimension_numbers() const;
  ::xla::GatherDimensionNumbers* release_gather_dimension_numbers();
  ::xla::GatherDimensionNumbers* mutable_gather_dimension_numbers();
  void set_allocated_gather_dimension_numbers(::xla::GatherDimensionNumbers* gather_dimension_numbers);
  void unsafe_arena_set_allocated_gather_dimension_numbers(
      ::xla::GatherDimensionNumbers* gather_dimension_numbers);
  ::xla::GatherDimensionNumbers* unsafe_arena_release_gather_dimension_numbers();

  // .xla.OpSharding sharding = 40;
  bool has_sharding() const;
  void clear_sharding();
  const ::xla::OpSharding& sharding() const;
  ::xla::OpSharding* release_sharding();
  ::xla::OpSharding* mutable_sharding();
  void set_allocated_sharding(::xla::OpSharding* sharding);
  void unsafe_arena_set_allocated_sharding(
      ::xla::OpSharding* sharding);
  ::xla::OpSharding* unsafe_arena_release_sharding();

  // .xla.ScatterDimensionNumbers scatter_dimension_numbers = 48;
  bool has_scatter_dimension_numbers() const;
  void clear_scatter_dimension_numbers();
  const ::xla::ScatterDimensionNumbers& scatter_dimension_numbers() const;
  ::xla::ScatterDimensionNumbers* release_scatter_dimension_numbers();
  ::xla::ScatterDimensionNumbers* mutable_scatter_dimension_numbers();
  void set_allocated_scatter_dimension_numbers(::xla::ScatterDimensionNumbers* scatter_dimension_numbers);
  void unsafe_arena_set_allocated_scatter_dimension_numbers(
      ::xla::ScatterDimensionNumbers* scatter_dimension_numbers);
  ::xla::ScatterDimensionNumbers* unsafe_arena_release_scatter_dimension_numbers();

  // .xla.PrecisionConfig precision_config = 51;
  bool has_precision_config() const;
  void clear_precision_config();
  const ::xla::PrecisionConfig& precision_config() const;
  ::xla::PrecisionConfig* release_precision_config();
  ::xla::PrecisionConfig* mutable_precision_config();
  void set_allocated_precision_config(::xla::PrecisionConfig* precision_config);
  void unsafe_arena_set_allocated_precision_config(
      ::xla::PrecisionConfig* precision_config);
  ::xla::PrecisionConfig* unsafe_arena_release_precision_config();

  // .xla.OpSharding domain_entry_sharding = 54;
  bool has_domain_entry_sharding() const;
  void clear_domain_entry_sharding();
  const ::xla::OpSharding& domain_entry_sharding() const;
  ::xla::OpSharding* release_domain_entry_sharding();
  ::xla::OpSharding* mutable_domain_entry_sharding();
  void set_allocated_domain_entry_sharding(::xla::OpSharding* domain_entry_sharding);
  void unsafe_arena_set_allocated_domain_entry_sharding(
      ::xla::OpSharding* domain_entry_sharding);
  ::xla::OpSharding* unsafe_arena_release_domain_entry_sharding();

  // .xla.OpSharding domain_exit_sharding = 55;
  bool has_domain_exit_sharding() const;
  void clear_domain_exit_sharding();
  const ::xla::OpSharding& domain_exit_sharding() const;
  ::xla::OpSharding* release_domain_exit_sharding();
  ::xla::OpSharding* mutable_domain_exit_sharding();
  void set_allocated_domain_exit_sharding(::xla::OpSharding* domain_exit_sharding);
  void unsafe_arena_set_allocated_domain_exit_sharding(
      ::xla::OpSharding* domain_exit_sharding);
  ::xla::OpSharding* unsafe_arena_release_domain_exit_sharding();

  // .xla.TriangularSolveOptions triangular_solve_options = 59;
  bool has_triangular_solve_options() const;
  void clear_triangular_solve_options();
  const ::xla::TriangularSolveOptions& triangular_solve_options() const;
  ::xla::TriangularSolveOptions* release_triangular_solve_options();
  ::xla::TriangularSolveOptions* mutable_triangular_solve_options();
  void set_allocated_triangular_solve_options(::xla::TriangularSolveOptions* triangular_solve_options);
  void unsafe_arena_set_allocated_triangular_solve_options(
      ::xla::TriangularSolveOptions* triangular_solve_options);
  ::xla::TriangularSolveOptions* unsafe_arena_release_triangular_solve_options();

  // .xla.ParameterReplication parameter_replication = 61;
  bool has_parameter_replication() const;
  void clear_parameter_replication();
  const ::xla::ParameterReplication& parameter_replication() const;
  ::xla::ParameterReplication* release_parameter_replication();
  ::xla::ParameterReplication* mutable_parameter_replication();
  void set_allocated_parameter_replication(::xla::ParameterReplication* parameter_replication);
  void unsafe_arena_set_allocated_parameter_replication(
      ::xla::ParameterReplication* parameter_replication);
  ::xla::ParameterReplication* unsafe_arena_release_parameter_replication();

  // .xla.CholeskyOptions cholesky_options = 62;
  bool has_cholesky_options() const;
  void clear_cholesky_options();
  const ::xla::CholeskyOptions& cholesky_options() const;
  ::xla::CholeskyOptions* release_cholesky_options();
  ::xla::CholeskyOptions* mutable_cholesky_options();
  void set_allocated_cholesky_options(::xla::CholeskyOptions* cholesky_options);
  void unsafe_arena_set_allocated_cholesky_options(
      ::xla::CholeskyOptions* cholesky_options);
  ::xla::CholeskyOptions* unsafe_arena_release_cholesky_options();

  // .xla.FrontendAttributes frontend_attributes = 68;
  bool has_frontend_attributes() const;
  void clear_frontend_attributes();
  const ::xla::FrontendAttributes& frontend_attributes() const;
  ::xla::FrontendAttributes* release_frontend_attributes();
  ::xla::FrontendAttributes* mutable_frontend_attributes();
  void set_allocated_frontend_attributes(::xla::FrontendAttributes* frontend_attributes);
  void unsafe_arena_set_allocated_frontend_attributes(
      ::xla::FrontendAttributes* frontend_attributes);
  ::xla::FrontendAttributes* unsafe_arena_release_frontend_attributes();

  // int64 parameter_number = 9;
  void clear_parameter_number();
  ::PROTOBUF_NAMESPACE_ID::int64 parameter_number() const;
  void set_parameter_number(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 tuple_index = 13;
  void clear_tuple_index();
  ::PROTOBUF_NAMESPACE_ID::int64 tuple_index() const;
  void set_tuple_index(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int32 exponent_bits = 18;
  void clear_exponent_bits();
  ::PROTOBUF_NAMESPACE_ID::int32 exponent_bits() const;
  void set_exponent_bits(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 mantissa_bits = 19;
  void clear_mantissa_bits();
  ::PROTOBUF_NAMESPACE_ID::int32 mantissa_bits() const;
  void set_mantissa_bits(::PROTOBUF_NAMESPACE_ID::int32 value);

  // .xla.RandomDistribution distribution = 23;
  void clear_distribution();
  ::xla::RandomDistribution distribution() const;
  void set_distribution(::xla::RandomDistribution value);

  // float epsilon = 24;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);

  // int64 feature_index = 25;
  void clear_feature_index();
  ::PROTOBUF_NAMESPACE_ID::int64 feature_index() const;
  void set_feature_index(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 channel_id = 26;
  void clear_channel_id();
  ::PROTOBUF_NAMESPACE_ID::int64 channel_id() const;
  void set_channel_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 id = 35;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 cost_estimate_ns = 42;
  void clear_cost_estimate_ns();
  ::PROTOBUF_NAMESPACE_ID::int64 cost_estimate_ns() const;
  void set_cost_estimate_ns(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 all_reduce_id = 45 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_all_reduce_id();
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int64 all_reduce_id() const;
  PROTOBUF_DEPRECATED void set_all_reduce_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 feature_group_count = 50;
  void clear_feature_group_count();
  ::PROTOBUF_NAMESPACE_ID::int64 feature_group_count() const;
  void set_feature_group_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .xla.FftType fft_type = 31;
  void clear_fft_type();
  ::xla::FftType fft_type() const;
  void set_fft_type(::xla::FftType value);

  // bool use_global_device_ids = 71;
  void clear_use_global_device_ids();
  bool use_global_device_ids() const;
  void set_use_global_device_ids(bool value);

  // bool is_host_transfer = 47;
  void clear_is_host_transfer();
  bool is_host_transfer() const;
  void set_is_host_transfer(bool value);

  // bool is_stable = 60;
  void clear_is_stable();
  bool is_stable() const;
  void set_is_stable(bool value);

  // bool constrain_layout = 56;
  void clear_constrain_layout();
  bool constrain_layout() const;
  void set_constrain_layout(bool value);

  // int64 batch_group_count = 58;
  void clear_batch_group_count();
  ::PROTOBUF_NAMESPACE_ID::int64 batch_group_count() const;
  void set_batch_group_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 delta = 66;
  void clear_delta();
  ::PROTOBUF_NAMESPACE_ID::int64 delta() const;
  void set_delta(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool custom_call_has_side_effect = 65;
  void clear_custom_call_has_side_effect();
  bool custom_call_has_side_effect() const;
  void set_custom_call_has_side_effect(bool value);

  // bool indices_are_sorted = 67;
  void clear_indices_are_sorted();
  bool indices_are_sorted() const;
  void set_indices_are_sorted(bool value);

  // bool unique_indices = 69;
  void clear_unique_indices();
  bool unique_indices() const;
  void set_unique_indices(bool value);

  // bool is_cross_program_prefetch = 73;
  void clear_is_cross_program_prefetch();
  bool is_cross_program_prefetch() const;
  void set_is_cross_program_prefetch(bool value);

  // .xla.RandomAlgorithm rng_algorithm = 70;
  void clear_rng_algorithm();
  ::xla::RandomAlgorithm rng_algorithm() const;
  void set_rng_algorithm(::xla::RandomAlgorithm value);

  // .xla.PaddingType padding_type = 75;
  void clear_padding_type();
  ::xla::PaddingType padding_type() const;
  void set_padding_type(::xla::PaddingType value);

  // .xla.CustomCallSchedule custom_call_schedule = 76;
  void clear_custom_call_schedule();
  ::xla::CustomCallSchedule custom_call_schedule() const;
  void set_custom_call_schedule(::xla::CustomCallSchedule value);

  // @@protoc_insertion_point(class_scope:xla.HloInstructionProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > dimensions_;
  mutable std::atomic<int> _dimensions_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto_SliceDimensions > slice_dimensions_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > dynamic_slice_sizes_;
  mutable std::atomic<int> _dynamic_slice_sizes_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > fft_length_;
  mutable std::atomic<int> _fft_length_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > gather_slice_sizes_;
  mutable std::atomic<int> _gather_slice_sizes_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > operand_ids_;
  mutable std::atomic<int> _operand_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > control_predecessor_ids_;
  mutable std::atomic<int> _control_predecessor_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > called_computation_ids_;
  mutable std::atomic<int> _called_computation_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ReplicaGroup > replica_groups_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::SourceTarget > source_target_pairs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto > operand_shapes_with_layout_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > outer_dimension_partitions_;
  mutable std::atomic<int> _outer_dimension_partitions_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::CustomCallOutputOperandAliasing > custom_call_output_operand_aliasing_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr opcode_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr fusion_kind_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr outfeed_config_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr infeed_config_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr custom_call_target_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr channel_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr backend_config_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr comparison_direction_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr comparison_type_;
  ::xla::ShapeProto* shape_;
  ::xla::OpMetadata* metadata_;
  ::xla::LiteralProto* literal_;
  ::xla::Window* window_;
  ::xla::ConvolutionDimensionNumbers* convolution_dimension_numbers_;
  ::xla::PaddingConfig* padding_config_;
  ::xla::ShapeProto* outfeed_shape_;
  ::xla::DotDimensionNumbers* dot_dimension_numbers_;
  ::xla::GatherDimensionNumbers* gather_dimension_numbers_;
  ::xla::OpSharding* sharding_;
  ::xla::ScatterDimensionNumbers* scatter_dimension_numbers_;
  ::xla::PrecisionConfig* precision_config_;
  ::xla::OpSharding* domain_entry_sharding_;
  ::xla::OpSharding* domain_exit_sharding_;
  ::xla::TriangularSolveOptions* triangular_solve_options_;
  ::xla::ParameterReplication* parameter_replication_;
  ::xla::CholeskyOptions* cholesky_options_;
  ::xla::FrontendAttributes* frontend_attributes_;
  ::PROTOBUF_NAMESPACE_ID::int64 parameter_number_;
  ::PROTOBUF_NAMESPACE_ID::int64 tuple_index_;
  ::PROTOBUF_NAMESPACE_ID::int32 exponent_bits_;
  ::PROTOBUF_NAMESPACE_ID::int32 mantissa_bits_;
  int distribution_;
  float epsilon_;
  ::PROTOBUF_NAMESPACE_ID::int64 feature_index_;
  ::PROTOBUF_NAMESPACE_ID::int64 channel_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  ::PROTOBUF_NAMESPACE_ID::int64 cost_estimate_ns_;
  ::PROTOBUF_NAMESPACE_ID::int64 all_reduce_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 feature_group_count_;
  int fft_type_;
  bool use_global_device_ids_;
  bool is_host_transfer_;
  bool is_stable_;
  bool constrain_layout_;
  ::PROTOBUF_NAMESPACE_ID::int64 batch_group_count_;
  ::PROTOBUF_NAMESPACE_ID::int64 delta_;
  bool custom_call_has_side_effect_;
  bool indices_are_sorted_;
  bool unique_indices_;
  bool is_cross_program_prefetch_;
  int rng_algorithm_;
  int padding_type_;
  int custom_call_schedule_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloComputationProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloComputationProto) */ {
 public:
  HloComputationProto();
  virtual ~HloComputationProto();

  HloComputationProto(const HloComputationProto& from);
  HloComputationProto(HloComputationProto&& from) noexcept
    : HloComputationProto() {
    *this = ::std::move(from);
  }

  inline HloComputationProto& operator=(const HloComputationProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloComputationProto& operator=(HloComputationProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloComputationProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloComputationProto* internal_default_instance() {
    return reinterpret_cast<const HloComputationProto*>(
               &_HloComputationProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(HloComputationProto& a, HloComputationProto& b) {
    a.Swap(&b);
  }
  inline void Swap(HloComputationProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloComputationProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloComputationProto* New() const final {
    return CreateMaybeMessage<HloComputationProto>(nullptr);
  }

  HloComputationProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloComputationProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloComputationProto& from);
  void MergeFrom(const HloComputationProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloComputationProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloComputationProto";
  }
  protected:
  explicit HloComputationProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstructionsFieldNumber = 2,
    kNameFieldNumber = 1,
    kProgramShapeFieldNumber = 4,
    kIdFieldNumber = 5,
    kRootIdFieldNumber = 6,
  };
  // repeated .xla.HloInstructionProto instructions = 2;
  int instructions_size() const;
  void clear_instructions();
  ::xla::HloInstructionProto* mutable_instructions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto >*
      mutable_instructions();
  const ::xla::HloInstructionProto& instructions(int index) const;
  ::xla::HloInstructionProto* add_instructions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto >&
      instructions() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .xla.ProgramShapeProto program_shape = 4;
  bool has_program_shape() const;
  void clear_program_shape();
  const ::xla::ProgramShapeProto& program_shape() const;
  ::xla::ProgramShapeProto* release_program_shape();
  ::xla::ProgramShapeProto* mutable_program_shape();
  void set_allocated_program_shape(::xla::ProgramShapeProto* program_shape);
  void unsafe_arena_set_allocated_program_shape(
      ::xla::ProgramShapeProto* program_shape);
  ::xla::ProgramShapeProto* unsafe_arena_release_program_shape();

  // int64 id = 5;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 root_id = 6;
  void clear_root_id();
  ::PROTOBUF_NAMESPACE_ID::int64 root_id() const;
  void set_root_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.HloComputationProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto > instructions_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::xla::ProgramShapeProto* program_shape_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  ::PROTOBUF_NAMESPACE_ID::int64 root_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloScheduleProto_InstructionSequence :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloScheduleProto.InstructionSequence) */ {
 public:
  HloScheduleProto_InstructionSequence();
  virtual ~HloScheduleProto_InstructionSequence();

  HloScheduleProto_InstructionSequence(const HloScheduleProto_InstructionSequence& from);
  HloScheduleProto_InstructionSequence(HloScheduleProto_InstructionSequence&& from) noexcept
    : HloScheduleProto_InstructionSequence() {
    *this = ::std::move(from);
  }

  inline HloScheduleProto_InstructionSequence& operator=(const HloScheduleProto_InstructionSequence& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloScheduleProto_InstructionSequence& operator=(HloScheduleProto_InstructionSequence&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloScheduleProto_InstructionSequence& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloScheduleProto_InstructionSequence* internal_default_instance() {
    return reinterpret_cast<const HloScheduleProto_InstructionSequence*>(
               &_HloScheduleProto_InstructionSequence_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(HloScheduleProto_InstructionSequence& a, HloScheduleProto_InstructionSequence& b) {
    a.Swap(&b);
  }
  inline void Swap(HloScheduleProto_InstructionSequence* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloScheduleProto_InstructionSequence* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloScheduleProto_InstructionSequence* New() const final {
    return CreateMaybeMessage<HloScheduleProto_InstructionSequence>(nullptr);
  }

  HloScheduleProto_InstructionSequence* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloScheduleProto_InstructionSequence>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloScheduleProto_InstructionSequence& from);
  void MergeFrom(const HloScheduleProto_InstructionSequence& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloScheduleProto_InstructionSequence* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloScheduleProto.InstructionSequence";
  }
  protected:
  explicit HloScheduleProto_InstructionSequence(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstructionIdsFieldNumber = 1,
  };
  // repeated int64 instruction_ids = 1;
  int instruction_ids_size() const;
  void clear_instruction_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 instruction_ids(int index) const;
  void set_instruction_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_instruction_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      instruction_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_instruction_ids();

  // @@protoc_insertion_point(class_scope:xla.HloScheduleProto.InstructionSequence)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > instruction_ids_;
  mutable std::atomic<int> _instruction_ids_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloScheduleProto_SequencesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HloScheduleProto_SequencesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::xla::HloScheduleProto_InstructionSequence,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HloScheduleProto_SequencesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::xla::HloScheduleProto_InstructionSequence,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  HloScheduleProto_SequencesEntry_DoNotUse();
  HloScheduleProto_SequencesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const HloScheduleProto_SequencesEntry_DoNotUse& other);
  static const HloScheduleProto_SequencesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const HloScheduleProto_SequencesEntry_DoNotUse*>(&_HloScheduleProto_SequencesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[4];
  }

  public:
};

// -------------------------------------------------------------------

class HloScheduleProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloScheduleProto) */ {
 public:
  HloScheduleProto();
  virtual ~HloScheduleProto();

  HloScheduleProto(const HloScheduleProto& from);
  HloScheduleProto(HloScheduleProto&& from) noexcept
    : HloScheduleProto() {
    *this = ::std::move(from);
  }

  inline HloScheduleProto& operator=(const HloScheduleProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloScheduleProto& operator=(HloScheduleProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloScheduleProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloScheduleProto* internal_default_instance() {
    return reinterpret_cast<const HloScheduleProto*>(
               &_HloScheduleProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(HloScheduleProto& a, HloScheduleProto& b) {
    a.Swap(&b);
  }
  inline void Swap(HloScheduleProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloScheduleProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloScheduleProto* New() const final {
    return CreateMaybeMessage<HloScheduleProto>(nullptr);
  }

  HloScheduleProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloScheduleProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloScheduleProto& from);
  void MergeFrom(const HloScheduleProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloScheduleProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloScheduleProto";
  }
  protected:
  explicit HloScheduleProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef HloScheduleProto_InstructionSequence InstructionSequence;

  // accessors -------------------------------------------------------

  enum : int {
    kSequencesFieldNumber = 1,
  };
  // map<int64, .xla.HloScheduleProto.InstructionSequence> sequences = 1;
  int sequences_size() const;
  void clear_sequences();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::xla::HloScheduleProto_InstructionSequence >&
      sequences() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::xla::HloScheduleProto_InstructionSequence >*
      mutable_sequences();

  // @@protoc_insertion_point(class_scope:xla.HloScheduleProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      HloScheduleProto_SequencesEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int64, ::xla::HloScheduleProto_InstructionSequence,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > sequences_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloInputOutputAliasProto_AliasEntryProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloInputOutputAliasProto.AliasEntryProto) */ {
 public:
  HloInputOutputAliasProto_AliasEntryProto();
  virtual ~HloInputOutputAliasProto_AliasEntryProto();

  HloInputOutputAliasProto_AliasEntryProto(const HloInputOutputAliasProto_AliasEntryProto& from);
  HloInputOutputAliasProto_AliasEntryProto(HloInputOutputAliasProto_AliasEntryProto&& from) noexcept
    : HloInputOutputAliasProto_AliasEntryProto() {
    *this = ::std::move(from);
  }

  inline HloInputOutputAliasProto_AliasEntryProto& operator=(const HloInputOutputAliasProto_AliasEntryProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloInputOutputAliasProto_AliasEntryProto& operator=(HloInputOutputAliasProto_AliasEntryProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloInputOutputAliasProto_AliasEntryProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloInputOutputAliasProto_AliasEntryProto* internal_default_instance() {
    return reinterpret_cast<const HloInputOutputAliasProto_AliasEntryProto*>(
               &_HloInputOutputAliasProto_AliasEntryProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(HloInputOutputAliasProto_AliasEntryProto& a, HloInputOutputAliasProto_AliasEntryProto& b) {
    a.Swap(&b);
  }
  inline void Swap(HloInputOutputAliasProto_AliasEntryProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloInputOutputAliasProto_AliasEntryProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloInputOutputAliasProto_AliasEntryProto* New() const final {
    return CreateMaybeMessage<HloInputOutputAliasProto_AliasEntryProto>(nullptr);
  }

  HloInputOutputAliasProto_AliasEntryProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloInputOutputAliasProto_AliasEntryProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloInputOutputAliasProto_AliasEntryProto& from);
  void MergeFrom(const HloInputOutputAliasProto_AliasEntryProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloInputOutputAliasProto_AliasEntryProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloInputOutputAliasProto.AliasEntryProto";
  }
  protected:
  explicit HloInputOutputAliasProto_AliasEntryProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputShapeIndexFieldNumber = 1,
    kParameterShapeIndexFieldNumber = 3,
    kParameterNumberFieldNumber = 2,
    kKindFieldNumber = 4,
  };
  // repeated int64 output_shape_index = 1;
  int output_shape_index_size() const;
  void clear_output_shape_index();
  ::PROTOBUF_NAMESPACE_ID::int64 output_shape_index(int index) const;
  void set_output_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_output_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      output_shape_index() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_output_shape_index();

  // repeated int64 parameter_shape_index = 3;
  int parameter_shape_index_size() const;
  void clear_parameter_shape_index();
  ::PROTOBUF_NAMESPACE_ID::int64 parameter_shape_index(int index) const;
  void set_parameter_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_parameter_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      parameter_shape_index() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_parameter_shape_index();

  // int64 parameter_number = 2;
  void clear_parameter_number();
  ::PROTOBUF_NAMESPACE_ID::int64 parameter_number() const;
  void set_parameter_number(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .xla.Kind kind = 4;
  void clear_kind();
  ::xla::Kind kind() const;
  void set_kind(::xla::Kind value);

  // @@protoc_insertion_point(class_scope:xla.HloInputOutputAliasProto.AliasEntryProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > output_shape_index_;
  mutable std::atomic<int> _output_shape_index_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > parameter_shape_index_;
  mutable std::atomic<int> _parameter_shape_index_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 parameter_number_;
  int kind_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloInputOutputAliasProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloInputOutputAliasProto) */ {
 public:
  HloInputOutputAliasProto();
  virtual ~HloInputOutputAliasProto();

  HloInputOutputAliasProto(const HloInputOutputAliasProto& from);
  HloInputOutputAliasProto(HloInputOutputAliasProto&& from) noexcept
    : HloInputOutputAliasProto() {
    *this = ::std::move(from);
  }

  inline HloInputOutputAliasProto& operator=(const HloInputOutputAliasProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloInputOutputAliasProto& operator=(HloInputOutputAliasProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloInputOutputAliasProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloInputOutputAliasProto* internal_default_instance() {
    return reinterpret_cast<const HloInputOutputAliasProto*>(
               &_HloInputOutputAliasProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(HloInputOutputAliasProto& a, HloInputOutputAliasProto& b) {
    a.Swap(&b);
  }
  inline void Swap(HloInputOutputAliasProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloInputOutputAliasProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloInputOutputAliasProto* New() const final {
    return CreateMaybeMessage<HloInputOutputAliasProto>(nullptr);
  }

  HloInputOutputAliasProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloInputOutputAliasProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloInputOutputAliasProto& from);
  void MergeFrom(const HloInputOutputAliasProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloInputOutputAliasProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloInputOutputAliasProto";
  }
  protected:
  explicit HloInputOutputAliasProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef HloInputOutputAliasProto_AliasEntryProto AliasEntryProto;

  // accessors -------------------------------------------------------

  enum : int {
    kEntriesFieldNumber = 1,
  };
  // repeated .xla.HloInputOutputAliasProto.AliasEntryProto entries = 1;
  int entries_size() const;
  void clear_entries();
  ::xla::HloInputOutputAliasProto_AliasEntryProto* mutable_entries(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInputOutputAliasProto_AliasEntryProto >*
      mutable_entries();
  const ::xla::HloInputOutputAliasProto_AliasEntryProto& entries(int index) const;
  ::xla::HloInputOutputAliasProto_AliasEntryProto* add_entries();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInputOutputAliasProto_AliasEntryProto >&
      entries() const;

  // @@protoc_insertion_point(class_scope:xla.HloInputOutputAliasProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInputOutputAliasProto_AliasEntryProto > entries_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class DynamicParameterBindingProto_Binding :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DynamicParameterBindingProto.Binding) */ {
 public:
  DynamicParameterBindingProto_Binding();
  virtual ~DynamicParameterBindingProto_Binding();

  DynamicParameterBindingProto_Binding(const DynamicParameterBindingProto_Binding& from);
  DynamicParameterBindingProto_Binding(DynamicParameterBindingProto_Binding&& from) noexcept
    : DynamicParameterBindingProto_Binding() {
    *this = ::std::move(from);
  }

  inline DynamicParameterBindingProto_Binding& operator=(const DynamicParameterBindingProto_Binding& from) {
    CopyFrom(from);
    return *this;
  }
  inline DynamicParameterBindingProto_Binding& operator=(DynamicParameterBindingProto_Binding&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DynamicParameterBindingProto_Binding& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DynamicParameterBindingProto_Binding* internal_default_instance() {
    return reinterpret_cast<const DynamicParameterBindingProto_Binding*>(
               &_DynamicParameterBindingProto_Binding_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(DynamicParameterBindingProto_Binding& a, DynamicParameterBindingProto_Binding& b) {
    a.Swap(&b);
  }
  inline void Swap(DynamicParameterBindingProto_Binding* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DynamicParameterBindingProto_Binding* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DynamicParameterBindingProto_Binding* New() const final {
    return CreateMaybeMessage<DynamicParameterBindingProto_Binding>(nullptr);
  }

  DynamicParameterBindingProto_Binding* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DynamicParameterBindingProto_Binding>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DynamicParameterBindingProto_Binding& from);
  void MergeFrom(const DynamicParameterBindingProto_Binding& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DynamicParameterBindingProto_Binding* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DynamicParameterBindingProto.Binding";
  }
  protected:
  explicit DynamicParameterBindingProto_Binding(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDynamicParamIndexFieldNumber = 2,
    kTargetParamIndexFieldNumber = 4,
    kDynamicParamNumFieldNumber = 1,
    kTargetParamNumFieldNumber = 3,
    kTargetParamDimNumFieldNumber = 5,
  };
  // repeated int64 dynamic_param_index = 2;
  int dynamic_param_index_size() const;
  void clear_dynamic_param_index();
  ::PROTOBUF_NAMESPACE_ID::int64 dynamic_param_index(int index) const;
  void set_dynamic_param_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_dynamic_param_index(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      dynamic_param_index() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_dynamic_param_index();

  // repeated int64 target_param_index = 4;
  int target_param_index_size() const;
  void clear_target_param_index();
  ::PROTOBUF_NAMESPACE_ID::int64 target_param_index(int index) const;
  void set_target_param_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_target_param_index(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      target_param_index() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_target_param_index();

  // int64 dynamic_param_num = 1;
  void clear_dynamic_param_num();
  ::PROTOBUF_NAMESPACE_ID::int64 dynamic_param_num() const;
  void set_dynamic_param_num(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 target_param_num = 3;
  void clear_target_param_num();
  ::PROTOBUF_NAMESPACE_ID::int64 target_param_num() const;
  void set_target_param_num(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 target_param_dim_num = 5;
  void clear_target_param_dim_num();
  ::PROTOBUF_NAMESPACE_ID::int64 target_param_dim_num() const;
  void set_target_param_dim_num(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.DynamicParameterBindingProto.Binding)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > dynamic_param_index_;
  mutable std::atomic<int> _dynamic_param_index_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > target_param_index_;
  mutable std::atomic<int> _target_param_index_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 dynamic_param_num_;
  ::PROTOBUF_NAMESPACE_ID::int64 target_param_num_;
  ::PROTOBUF_NAMESPACE_ID::int64 target_param_dim_num_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class DynamicParameterBindingProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DynamicParameterBindingProto) */ {
 public:
  DynamicParameterBindingProto();
  virtual ~DynamicParameterBindingProto();

  DynamicParameterBindingProto(const DynamicParameterBindingProto& from);
  DynamicParameterBindingProto(DynamicParameterBindingProto&& from) noexcept
    : DynamicParameterBindingProto() {
    *this = ::std::move(from);
  }

  inline DynamicParameterBindingProto& operator=(const DynamicParameterBindingProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline DynamicParameterBindingProto& operator=(DynamicParameterBindingProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DynamicParameterBindingProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DynamicParameterBindingProto* internal_default_instance() {
    return reinterpret_cast<const DynamicParameterBindingProto*>(
               &_DynamicParameterBindingProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(DynamicParameterBindingProto& a, DynamicParameterBindingProto& b) {
    a.Swap(&b);
  }
  inline void Swap(DynamicParameterBindingProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DynamicParameterBindingProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DynamicParameterBindingProto* New() const final {
    return CreateMaybeMessage<DynamicParameterBindingProto>(nullptr);
  }

  DynamicParameterBindingProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DynamicParameterBindingProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DynamicParameterBindingProto& from);
  void MergeFrom(const DynamicParameterBindingProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DynamicParameterBindingProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DynamicParameterBindingProto";
  }
  protected:
  explicit DynamicParameterBindingProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef DynamicParameterBindingProto_Binding Binding;

  // accessors -------------------------------------------------------

  enum : int {
    kEntriesFieldNumber = 1,
  };
  // repeated .xla.DynamicParameterBindingProto.Binding entries = 1;
  int entries_size() const;
  void clear_entries();
  ::xla::DynamicParameterBindingProto_Binding* mutable_entries(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DynamicParameterBindingProto_Binding >*
      mutable_entries();
  const ::xla::DynamicParameterBindingProto_Binding& entries(int index) const;
  ::xla::DynamicParameterBindingProto_Binding* add_entries();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DynamicParameterBindingProto_Binding >&
      entries() const;

  // @@protoc_insertion_point(class_scope:xla.DynamicParameterBindingProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DynamicParameterBindingProto_Binding > entries_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class CrossProgramPrefetch :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CrossProgramPrefetch) */ {
 public:
  CrossProgramPrefetch();
  virtual ~CrossProgramPrefetch();

  CrossProgramPrefetch(const CrossProgramPrefetch& from);
  CrossProgramPrefetch(CrossProgramPrefetch&& from) noexcept
    : CrossProgramPrefetch() {
    *this = ::std::move(from);
  }

  inline CrossProgramPrefetch& operator=(const CrossProgramPrefetch& from) {
    CopyFrom(from);
    return *this;
  }
  inline CrossProgramPrefetch& operator=(CrossProgramPrefetch&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CrossProgramPrefetch& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CrossProgramPrefetch* internal_default_instance() {
    return reinterpret_cast<const CrossProgramPrefetch*>(
               &_CrossProgramPrefetch_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(CrossProgramPrefetch& a, CrossProgramPrefetch& b) {
    a.Swap(&b);
  }
  inline void Swap(CrossProgramPrefetch* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CrossProgramPrefetch* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CrossProgramPrefetch* New() const final {
    return CreateMaybeMessage<CrossProgramPrefetch>(nullptr);
  }

  CrossProgramPrefetch* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CrossProgramPrefetch>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CrossProgramPrefetch& from);
  void MergeFrom(const CrossProgramPrefetch& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CrossProgramPrefetch* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CrossProgramPrefetch";
  }
  protected:
  explicit CrossProgramPrefetch(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIndexFieldNumber = 2,
    kParameterFieldNumber = 1,
  };
  // repeated int64 index = 2;
  int index_size() const;
  void clear_index();
  ::PROTOBUF_NAMESPACE_ID::int64 index(int index) const;
  void set_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_index(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      index() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_index();

  // int64 parameter = 1;
  void clear_parameter();
  ::PROTOBUF_NAMESPACE_ID::int64 parameter() const;
  void set_parameter(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.CrossProgramPrefetch)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > index_;
  mutable std::atomic<int> _index_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 parameter_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloModuleProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloModuleProto) */ {
 public:
  HloModuleProto();
  virtual ~HloModuleProto();

  HloModuleProto(const HloModuleProto& from);
  HloModuleProto(HloModuleProto&& from) noexcept
    : HloModuleProto() {
    *this = ::std::move(from);
  }

  inline HloModuleProto& operator=(const HloModuleProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloModuleProto& operator=(HloModuleProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloModuleProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloModuleProto* internal_default_instance() {
    return reinterpret_cast<const HloModuleProto*>(
               &_HloModuleProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(HloModuleProto& a, HloModuleProto& b) {
    a.Swap(&b);
  }
  inline void Swap(HloModuleProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloModuleProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloModuleProto* New() const final {
    return CreateMaybeMessage<HloModuleProto>(nullptr);
  }

  HloModuleProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloModuleProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloModuleProto& from);
  void MergeFrom(const HloModuleProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloModuleProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloModuleProto";
  }
  protected:
  explicit HloModuleProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComputationsFieldNumber = 3,
    kCrossProgramPrefetchesFieldNumber = 10,
    kNameFieldNumber = 1,
    kEntryComputationNameFieldNumber = 2,
    kHostProgramShapeFieldNumber = 4,
    kScheduleFieldNumber = 7,
    kInputOutputAliasFieldNumber = 8,
    kDynamicParameterBindingFieldNumber = 9,
    kIdFieldNumber = 5,
    kEntryComputationIdFieldNumber = 6,
    kIsDynamicFieldNumber = 11,
  };
  // repeated .xla.HloComputationProto computations = 3;
  int computations_size() const;
  void clear_computations();
  ::xla::HloComputationProto* mutable_computations(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloComputationProto >*
      mutable_computations();
  const ::xla::HloComputationProto& computations(int index) const;
  ::xla::HloComputationProto* add_computations();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloComputationProto >&
      computations() const;

  // repeated .xla.CrossProgramPrefetch cross_program_prefetches = 10;
  int cross_program_prefetches_size() const;
  void clear_cross_program_prefetches();
  ::xla::CrossProgramPrefetch* mutable_cross_program_prefetches(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::CrossProgramPrefetch >*
      mutable_cross_program_prefetches();
  const ::xla::CrossProgramPrefetch& cross_program_prefetches(int index) const;
  ::xla::CrossProgramPrefetch* add_cross_program_prefetches();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::CrossProgramPrefetch >&
      cross_program_prefetches() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string entry_computation_name = 2;
  void clear_entry_computation_name();
  const std::string& entry_computation_name() const;
  void set_entry_computation_name(const std::string& value);
  void set_entry_computation_name(std::string&& value);
  void set_entry_computation_name(const char* value);
  void set_entry_computation_name(const char* value, size_t size);
  std::string* mutable_entry_computation_name();
  std::string* release_entry_computation_name();
  void set_allocated_entry_computation_name(std::string* entry_computation_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_entry_computation_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_entry_computation_name(
      std::string* entry_computation_name);

  // .xla.ProgramShapeProto host_program_shape = 4;
  bool has_host_program_shape() const;
  void clear_host_program_shape();
  const ::xla::ProgramShapeProto& host_program_shape() const;
  ::xla::ProgramShapeProto* release_host_program_shape();
  ::xla::ProgramShapeProto* mutable_host_program_shape();
  void set_allocated_host_program_shape(::xla::ProgramShapeProto* host_program_shape);
  void unsafe_arena_set_allocated_host_program_shape(
      ::xla::ProgramShapeProto* host_program_shape);
  ::xla::ProgramShapeProto* unsafe_arena_release_host_program_shape();

  // .xla.HloScheduleProto schedule = 7;
  bool has_schedule() const;
  void clear_schedule();
  const ::xla::HloScheduleProto& schedule() const;
  ::xla::HloScheduleProto* release_schedule();
  ::xla::HloScheduleProto* mutable_schedule();
  void set_allocated_schedule(::xla::HloScheduleProto* schedule);
  void unsafe_arena_set_allocated_schedule(
      ::xla::HloScheduleProto* schedule);
  ::xla::HloScheduleProto* unsafe_arena_release_schedule();

  // .xla.HloInputOutputAliasProto input_output_alias = 8;
  bool has_input_output_alias() const;
  void clear_input_output_alias();
  const ::xla::HloInputOutputAliasProto& input_output_alias() const;
  ::xla::HloInputOutputAliasProto* release_input_output_alias();
  ::xla::HloInputOutputAliasProto* mutable_input_output_alias();
  void set_allocated_input_output_alias(::xla::HloInputOutputAliasProto* input_output_alias);
  void unsafe_arena_set_allocated_input_output_alias(
      ::xla::HloInputOutputAliasProto* input_output_alias);
  ::xla::HloInputOutputAliasProto* unsafe_arena_release_input_output_alias();

  // .xla.DynamicParameterBindingProto dynamic_parameter_binding = 9;
  bool has_dynamic_parameter_binding() const;
  void clear_dynamic_parameter_binding();
  const ::xla::DynamicParameterBindingProto& dynamic_parameter_binding() const;
  ::xla::DynamicParameterBindingProto* release_dynamic_parameter_binding();
  ::xla::DynamicParameterBindingProto* mutable_dynamic_parameter_binding();
  void set_allocated_dynamic_parameter_binding(::xla::DynamicParameterBindingProto* dynamic_parameter_binding);
  void unsafe_arena_set_allocated_dynamic_parameter_binding(
      ::xla::DynamicParameterBindingProto* dynamic_parameter_binding);
  ::xla::DynamicParameterBindingProto* unsafe_arena_release_dynamic_parameter_binding();

  // int64 id = 5;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 entry_computation_id = 6;
  void clear_entry_computation_id();
  ::PROTOBUF_NAMESPACE_ID::int64 entry_computation_id() const;
  void set_entry_computation_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool is_dynamic = 11;
  void clear_is_dynamic();
  bool is_dynamic() const;
  void set_is_dynamic(bool value);

  // @@protoc_insertion_point(class_scope:xla.HloModuleProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloComputationProto > computations_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::CrossProgramPrefetch > cross_program_prefetches_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr entry_computation_name_;
  ::xla::ProgramShapeProto* host_program_shape_;
  ::xla::HloScheduleProto* schedule_;
  ::xla::HloInputOutputAliasProto* input_output_alias_;
  ::xla::DynamicParameterBindingProto* dynamic_parameter_binding_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  ::PROTOBUF_NAMESPACE_ID::int64 entry_computation_id_;
  bool is_dynamic_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class LogicalBufferProto_Location :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.LogicalBufferProto.Location) */ {
 public:
  LogicalBufferProto_Location();
  virtual ~LogicalBufferProto_Location();

  LogicalBufferProto_Location(const LogicalBufferProto_Location& from);
  LogicalBufferProto_Location(LogicalBufferProto_Location&& from) noexcept
    : LogicalBufferProto_Location() {
    *this = ::std::move(from);
  }

  inline LogicalBufferProto_Location& operator=(const LogicalBufferProto_Location& from) {
    CopyFrom(from);
    return *this;
  }
  inline LogicalBufferProto_Location& operator=(LogicalBufferProto_Location&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LogicalBufferProto_Location& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LogicalBufferProto_Location* internal_default_instance() {
    return reinterpret_cast<const LogicalBufferProto_Location*>(
               &_LogicalBufferProto_Location_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(LogicalBufferProto_Location& a, LogicalBufferProto_Location& b) {
    a.Swap(&b);
  }
  inline void Swap(LogicalBufferProto_Location* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LogicalBufferProto_Location* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LogicalBufferProto_Location* New() const final {
    return CreateMaybeMessage<LogicalBufferProto_Location>(nullptr);
  }

  LogicalBufferProto_Location* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LogicalBufferProto_Location>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LogicalBufferProto_Location& from);
  void MergeFrom(const LogicalBufferProto_Location& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LogicalBufferProto_Location* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.LogicalBufferProto.Location";
  }
  protected:
  explicit LogicalBufferProto_Location(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeIndexFieldNumber = 3,
    kComputationNameFieldNumber = 1,
    kInstructionNameFieldNumber = 2,
  };
  // repeated int64 shape_index = 3;
  int shape_index_size() const;
  void clear_shape_index();
  ::PROTOBUF_NAMESPACE_ID::int64 shape_index(int index) const;
  void set_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      shape_index() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_shape_index();

  // string computation_name = 1;
  void clear_computation_name();
  const std::string& computation_name() const;
  void set_computation_name(const std::string& value);
  void set_computation_name(std::string&& value);
  void set_computation_name(const char* value);
  void set_computation_name(const char* value, size_t size);
  std::string* mutable_computation_name();
  std::string* release_computation_name();
  void set_allocated_computation_name(std::string* computation_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_computation_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_computation_name(
      std::string* computation_name);

  // string instruction_name = 2;
  void clear_instruction_name();
  const std::string& instruction_name() const;
  void set_instruction_name(const std::string& value);
  void set_instruction_name(std::string&& value);
  void set_instruction_name(const char* value);
  void set_instruction_name(const char* value, size_t size);
  std::string* mutable_instruction_name();
  std::string* release_instruction_name();
  void set_allocated_instruction_name(std::string* instruction_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_instruction_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_instruction_name(
      std::string* instruction_name);

  // @@protoc_insertion_point(class_scope:xla.LogicalBufferProto.Location)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > shape_index_;
  mutable std::atomic<int> _shape_index_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr computation_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr instruction_name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class LogicalBufferProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.LogicalBufferProto) */ {
 public:
  LogicalBufferProto();
  virtual ~LogicalBufferProto();

  LogicalBufferProto(const LogicalBufferProto& from);
  LogicalBufferProto(LogicalBufferProto&& from) noexcept
    : LogicalBufferProto() {
    *this = ::std::move(from);
  }

  inline LogicalBufferProto& operator=(const LogicalBufferProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline LogicalBufferProto& operator=(LogicalBufferProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LogicalBufferProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LogicalBufferProto* internal_default_instance() {
    return reinterpret_cast<const LogicalBufferProto*>(
               &_LogicalBufferProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(LogicalBufferProto& a, LogicalBufferProto& b) {
    a.Swap(&b);
  }
  inline void Swap(LogicalBufferProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LogicalBufferProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LogicalBufferProto* New() const final {
    return CreateMaybeMessage<LogicalBufferProto>(nullptr);
  }

  LogicalBufferProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LogicalBufferProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LogicalBufferProto& from);
  void MergeFrom(const LogicalBufferProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LogicalBufferProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.LogicalBufferProto";
  }
  protected:
  explicit LogicalBufferProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef LogicalBufferProto_Location Location;

  // accessors -------------------------------------------------------

  enum : int {
    kDefinedAtFieldNumber = 3,
    kIdFieldNumber = 1,
    kSizeFieldNumber = 2,
    kColorFieldNumber = 4,
  };
  // .xla.LogicalBufferProto.Location defined_at = 3;
  bool has_defined_at() const;
  void clear_defined_at();
  const ::xla::LogicalBufferProto_Location& defined_at() const;
  ::xla::LogicalBufferProto_Location* release_defined_at();
  ::xla::LogicalBufferProto_Location* mutable_defined_at();
  void set_allocated_defined_at(::xla::LogicalBufferProto_Location* defined_at);
  void unsafe_arena_set_allocated_defined_at(
      ::xla::LogicalBufferProto_Location* defined_at);
  ::xla::LogicalBufferProto_Location* unsafe_arena_release_defined_at();

  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 size = 2;
  void clear_size();
  ::PROTOBUF_NAMESPACE_ID::int64 size() const;
  void set_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 color = 4;
  void clear_color();
  ::PROTOBUF_NAMESPACE_ID::int64 color() const;
  void set_color(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.LogicalBufferProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::xla::LogicalBufferProto_Location* defined_at_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  ::PROTOBUF_NAMESPACE_ID::int64 size_;
  ::PROTOBUF_NAMESPACE_ID::int64 color_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class BufferAllocationProto_Assigned :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.BufferAllocationProto.Assigned) */ {
 public:
  BufferAllocationProto_Assigned();
  virtual ~BufferAllocationProto_Assigned();

  BufferAllocationProto_Assigned(const BufferAllocationProto_Assigned& from);
  BufferAllocationProto_Assigned(BufferAllocationProto_Assigned&& from) noexcept
    : BufferAllocationProto_Assigned() {
    *this = ::std::move(from);
  }

  inline BufferAllocationProto_Assigned& operator=(const BufferAllocationProto_Assigned& from) {
    CopyFrom(from);
    return *this;
  }
  inline BufferAllocationProto_Assigned& operator=(BufferAllocationProto_Assigned&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BufferAllocationProto_Assigned& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BufferAllocationProto_Assigned* internal_default_instance() {
    return reinterpret_cast<const BufferAllocationProto_Assigned*>(
               &_BufferAllocationProto_Assigned_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(BufferAllocationProto_Assigned& a, BufferAllocationProto_Assigned& b) {
    a.Swap(&b);
  }
  inline void Swap(BufferAllocationProto_Assigned* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BufferAllocationProto_Assigned* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BufferAllocationProto_Assigned* New() const final {
    return CreateMaybeMessage<BufferAllocationProto_Assigned>(nullptr);
  }

  BufferAllocationProto_Assigned* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BufferAllocationProto_Assigned>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BufferAllocationProto_Assigned& from);
  void MergeFrom(const BufferAllocationProto_Assigned& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BufferAllocationProto_Assigned* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.BufferAllocationProto.Assigned";
  }
  protected:
  explicit BufferAllocationProto_Assigned(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLogicalBufferIdFieldNumber = 1,
    kOffsetFieldNumber = 2,
    kSizeFieldNumber = 3,
  };
  // int64 logical_buffer_id = 1;
  void clear_logical_buffer_id();
  ::PROTOBUF_NAMESPACE_ID::int64 logical_buffer_id() const;
  void set_logical_buffer_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 offset = 2;
  void clear_offset();
  ::PROTOBUF_NAMESPACE_ID::int64 offset() const;
  void set_offset(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 size = 3;
  void clear_size();
  ::PROTOBUF_NAMESPACE_ID::int64 size() const;
  void set_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.BufferAllocationProto.Assigned)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 logical_buffer_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 offset_;
  ::PROTOBUF_NAMESPACE_ID::int64 size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class BufferAllocationProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.BufferAllocationProto) */ {
 public:
  BufferAllocationProto();
  virtual ~BufferAllocationProto();

  BufferAllocationProto(const BufferAllocationProto& from);
  BufferAllocationProto(BufferAllocationProto&& from) noexcept
    : BufferAllocationProto() {
    *this = ::std::move(from);
  }

  inline BufferAllocationProto& operator=(const BufferAllocationProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline BufferAllocationProto& operator=(BufferAllocationProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BufferAllocationProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BufferAllocationProto* internal_default_instance() {
    return reinterpret_cast<const BufferAllocationProto*>(
               &_BufferAllocationProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(BufferAllocationProto& a, BufferAllocationProto& b) {
    a.Swap(&b);
  }
  inline void Swap(BufferAllocationProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BufferAllocationProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BufferAllocationProto* New() const final {
    return CreateMaybeMessage<BufferAllocationProto>(nullptr);
  }

  BufferAllocationProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BufferAllocationProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BufferAllocationProto& from);
  void MergeFrom(const BufferAllocationProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BufferAllocationProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.BufferAllocationProto";
  }
  protected:
  explicit BufferAllocationProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef BufferAllocationProto_Assigned Assigned;

  // accessors -------------------------------------------------------

  enum : int {
    kAssignedFieldNumber = 9,
    kParameterShapeIndexFieldNumber = 10,
    kIndexFieldNumber = 1,
    kSizeFieldNumber = 2,
    kParameterNumberFieldNumber = 6,
    kMaybeLiveOutFieldNumber = 7,
    kIsThreadLocalFieldNumber = 3,
    kIsTupleFieldNumber = 11,
    kIsEntryComputationParameterFieldNumber = 5,
    kIsConstantFieldNumber = 12,
    kColorFieldNumber = 8,
  };
  // repeated .xla.BufferAllocationProto.Assigned assigned = 9;
  int assigned_size() const;
  void clear_assigned();
  ::xla::BufferAllocationProto_Assigned* mutable_assigned(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAllocationProto_Assigned >*
      mutable_assigned();
  const ::xla::BufferAllocationProto_Assigned& assigned(int index) const;
  ::xla::BufferAllocationProto_Assigned* add_assigned();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAllocationProto_Assigned >&
      assigned() const;

  // repeated int64 parameter_shape_index = 10;
  int parameter_shape_index_size() const;
  void clear_parameter_shape_index();
  ::PROTOBUF_NAMESPACE_ID::int64 parameter_shape_index(int index) const;
  void set_parameter_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_parameter_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      parameter_shape_index() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_parameter_shape_index();

  // int64 index = 1;
  void clear_index();
  ::PROTOBUF_NAMESPACE_ID::int64 index() const;
  void set_index(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 size = 2;
  void clear_size();
  ::PROTOBUF_NAMESPACE_ID::int64 size() const;
  void set_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 parameter_number = 6;
  void clear_parameter_number();
  ::PROTOBUF_NAMESPACE_ID::int64 parameter_number() const;
  void set_parameter_number(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool maybe_live_out = 7;
  void clear_maybe_live_out();
  bool maybe_live_out() const;
  void set_maybe_live_out(bool value);

  // bool is_thread_local = 3;
  void clear_is_thread_local();
  bool is_thread_local() const;
  void set_is_thread_local(bool value);

  // bool is_tuple = 11;
  void clear_is_tuple();
  bool is_tuple() const;
  void set_is_tuple(bool value);

  // bool is_entry_computation_parameter = 5;
  void clear_is_entry_computation_parameter();
  bool is_entry_computation_parameter() const;
  void set_is_entry_computation_parameter(bool value);

  // bool is_constant = 12;
  void clear_is_constant();
  bool is_constant() const;
  void set_is_constant(bool value);

  // int64 color = 8;
  void clear_color();
  ::PROTOBUF_NAMESPACE_ID::int64 color() const;
  void set_color(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.BufferAllocationProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAllocationProto_Assigned > assigned_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > parameter_shape_index_;
  mutable std::atomic<int> _parameter_shape_index_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 index_;
  ::PROTOBUF_NAMESPACE_ID::int64 size_;
  ::PROTOBUF_NAMESPACE_ID::int64 parameter_number_;
  bool maybe_live_out_;
  bool is_thread_local_;
  bool is_tuple_;
  bool is_entry_computation_parameter_;
  bool is_constant_;
  ::PROTOBUF_NAMESPACE_ID::int64 color_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HeapSimulatorTrace_Event :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HeapSimulatorTrace.Event) */ {
 public:
  HeapSimulatorTrace_Event();
  virtual ~HeapSimulatorTrace_Event();

  HeapSimulatorTrace_Event(const HeapSimulatorTrace_Event& from);
  HeapSimulatorTrace_Event(HeapSimulatorTrace_Event&& from) noexcept
    : HeapSimulatorTrace_Event() {
    *this = ::std::move(from);
  }

  inline HeapSimulatorTrace_Event& operator=(const HeapSimulatorTrace_Event& from) {
    CopyFrom(from);
    return *this;
  }
  inline HeapSimulatorTrace_Event& operator=(HeapSimulatorTrace_Event&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HeapSimulatorTrace_Event& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HeapSimulatorTrace_Event* internal_default_instance() {
    return reinterpret_cast<const HeapSimulatorTrace_Event*>(
               &_HeapSimulatorTrace_Event_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(HeapSimulatorTrace_Event& a, HeapSimulatorTrace_Event& b) {
    a.Swap(&b);
  }
  inline void Swap(HeapSimulatorTrace_Event* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HeapSimulatorTrace_Event* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HeapSimulatorTrace_Event* New() const final {
    return CreateMaybeMessage<HeapSimulatorTrace_Event>(nullptr);
  }

  HeapSimulatorTrace_Event* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HeapSimulatorTrace_Event>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HeapSimulatorTrace_Event& from);
  void MergeFrom(const HeapSimulatorTrace_Event& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HeapSimulatorTrace_Event* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HeapSimulatorTrace.Event";
  }
  protected:
  explicit HeapSimulatorTrace_Event(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef HeapSimulatorTrace_Event_Kind Kind;
  static constexpr Kind ALLOC =
    HeapSimulatorTrace_Event_Kind_ALLOC;
  static constexpr Kind FREE =
    HeapSimulatorTrace_Event_Kind_FREE;
  static constexpr Kind SHARE_WITH =
    HeapSimulatorTrace_Event_Kind_SHARE_WITH;
  static inline bool Kind_IsValid(int value) {
    return HeapSimulatorTrace_Event_Kind_IsValid(value);
  }
  static constexpr Kind Kind_MIN =
    HeapSimulatorTrace_Event_Kind_Kind_MIN;
  static constexpr Kind Kind_MAX =
    HeapSimulatorTrace_Event_Kind_Kind_MAX;
  static constexpr int Kind_ARRAYSIZE =
    HeapSimulatorTrace_Event_Kind_Kind_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Kind_descriptor() {
    return HeapSimulatorTrace_Event_Kind_descriptor();
  }
  template<typename T>
  static inline const std::string& Kind_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Kind>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Kind_Name.");
    return HeapSimulatorTrace_Event_Kind_Name(enum_t_value);
  }
  static inline bool Kind_Parse(const std::string& name,
      Kind* value) {
    return HeapSimulatorTrace_Event_Kind_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kComputationNameFieldNumber = 3,
    kInstructionNameFieldNumber = 4,
    kBufferIdFieldNumber = 2,
    kShareWithCanonicalIdFieldNumber = 5,
    kKindFieldNumber = 1,
  };
  // string computation_name = 3;
  void clear_computation_name();
  const std::string& computation_name() const;
  void set_computation_name(const std::string& value);
  void set_computation_name(std::string&& value);
  void set_computation_name(const char* value);
  void set_computation_name(const char* value, size_t size);
  std::string* mutable_computation_name();
  std::string* release_computation_name();
  void set_allocated_computation_name(std::string* computation_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_computation_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_computation_name(
      std::string* computation_name);

  // string instruction_name = 4;
  void clear_instruction_name();
  const std::string& instruction_name() const;
  void set_instruction_name(const std::string& value);
  void set_instruction_name(std::string&& value);
  void set_instruction_name(const char* value);
  void set_instruction_name(const char* value, size_t size);
  std::string* mutable_instruction_name();
  std::string* release_instruction_name();
  void set_allocated_instruction_name(std::string* instruction_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_instruction_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_instruction_name(
      std::string* instruction_name);

  // int64 buffer_id = 2;
  void clear_buffer_id();
  ::PROTOBUF_NAMESPACE_ID::int64 buffer_id() const;
  void set_buffer_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 share_with_canonical_id = 5;
  void clear_share_with_canonical_id();
  ::PROTOBUF_NAMESPACE_ID::int64 share_with_canonical_id() const;
  void set_share_with_canonical_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .xla.HeapSimulatorTrace.Event.Kind kind = 1;
  void clear_kind();
  ::xla::HeapSimulatorTrace_Event_Kind kind() const;
  void set_kind(::xla::HeapSimulatorTrace_Event_Kind value);

  // @@protoc_insertion_point(class_scope:xla.HeapSimulatorTrace.Event)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr computation_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr instruction_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 buffer_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 share_with_canonical_id_;
  int kind_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HeapSimulatorTrace :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HeapSimulatorTrace) */ {
 public:
  HeapSimulatorTrace();
  virtual ~HeapSimulatorTrace();

  HeapSimulatorTrace(const HeapSimulatorTrace& from);
  HeapSimulatorTrace(HeapSimulatorTrace&& from) noexcept
    : HeapSimulatorTrace() {
    *this = ::std::move(from);
  }

  inline HeapSimulatorTrace& operator=(const HeapSimulatorTrace& from) {
    CopyFrom(from);
    return *this;
  }
  inline HeapSimulatorTrace& operator=(HeapSimulatorTrace&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HeapSimulatorTrace& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HeapSimulatorTrace* internal_default_instance() {
    return reinterpret_cast<const HeapSimulatorTrace*>(
               &_HeapSimulatorTrace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(HeapSimulatorTrace& a, HeapSimulatorTrace& b) {
    a.Swap(&b);
  }
  inline void Swap(HeapSimulatorTrace* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HeapSimulatorTrace* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HeapSimulatorTrace* New() const final {
    return CreateMaybeMessage<HeapSimulatorTrace>(nullptr);
  }

  HeapSimulatorTrace* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HeapSimulatorTrace>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HeapSimulatorTrace& from);
  void MergeFrom(const HeapSimulatorTrace& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HeapSimulatorTrace* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HeapSimulatorTrace";
  }
  protected:
  explicit HeapSimulatorTrace(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef HeapSimulatorTrace_Event Event;

  // accessors -------------------------------------------------------

  enum : int {
    kEventsFieldNumber = 1,
    kBufferAllocationIndexFieldNumber = 3,
    kWholeModuleSimulationFieldNumber = 2,
  };
  // repeated .xla.HeapSimulatorTrace.Event events = 1;
  int events_size() const;
  void clear_events();
  ::xla::HeapSimulatorTrace_Event* mutable_events(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HeapSimulatorTrace_Event >*
      mutable_events();
  const ::xla::HeapSimulatorTrace_Event& events(int index) const;
  ::xla::HeapSimulatorTrace_Event* add_events();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HeapSimulatorTrace_Event >&
      events() const;

  // int64 buffer_allocation_index = 3;
  void clear_buffer_allocation_index();
  ::PROTOBUF_NAMESPACE_ID::int64 buffer_allocation_index() const;
  void set_buffer_allocation_index(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool whole_module_simulation = 2;
  void clear_whole_module_simulation();
  bool whole_module_simulation() const;
  void set_whole_module_simulation(bool value);

  // @@protoc_insertion_point(class_scope:xla.HeapSimulatorTrace)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HeapSimulatorTrace_Event > events_;
  ::PROTOBUF_NAMESPACE_ID::int64 buffer_allocation_index_;
  bool whole_module_simulation_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloModuleGroupProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloModuleGroupProto) */ {
 public:
  HloModuleGroupProto();
  virtual ~HloModuleGroupProto();

  HloModuleGroupProto(const HloModuleGroupProto& from);
  HloModuleGroupProto(HloModuleGroupProto&& from) noexcept
    : HloModuleGroupProto() {
    *this = ::std::move(from);
  }

  inline HloModuleGroupProto& operator=(const HloModuleGroupProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloModuleGroupProto& operator=(HloModuleGroupProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloModuleGroupProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloModuleGroupProto* internal_default_instance() {
    return reinterpret_cast<const HloModuleGroupProto*>(
               &_HloModuleGroupProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(HloModuleGroupProto& a, HloModuleGroupProto& b) {
    a.Swap(&b);
  }
  inline void Swap(HloModuleGroupProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloModuleGroupProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloModuleGroupProto* New() const final {
    return CreateMaybeMessage<HloModuleGroupProto>(nullptr);
  }

  HloModuleGroupProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloModuleGroupProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloModuleGroupProto& from);
  void MergeFrom(const HloModuleGroupProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloModuleGroupProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloModuleGroupProto";
  }
  protected:
  explicit HloModuleGroupProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHloModulesFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // repeated .xla.HloModuleProto hlo_modules = 2;
  int hlo_modules_size() const;
  void clear_hlo_modules();
  ::xla::HloModuleProto* mutable_hlo_modules(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloModuleProto >*
      mutable_hlo_modules();
  const ::xla::HloModuleProto& hlo_modules(int index) const;
  ::xla::HloModuleProto* add_hlo_modules();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloModuleProto >&
      hlo_modules() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // @@protoc_insertion_point(class_scope:xla.HloModuleGroupProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloModuleProto > hlo_modules_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class BufferAssignmentProto_BufferAlias :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.BufferAssignmentProto.BufferAlias) */ {
 public:
  BufferAssignmentProto_BufferAlias();
  virtual ~BufferAssignmentProto_BufferAlias();

  BufferAssignmentProto_BufferAlias(const BufferAssignmentProto_BufferAlias& from);
  BufferAssignmentProto_BufferAlias(BufferAssignmentProto_BufferAlias&& from) noexcept
    : BufferAssignmentProto_BufferAlias() {
    *this = ::std::move(from);
  }

  inline BufferAssignmentProto_BufferAlias& operator=(const BufferAssignmentProto_BufferAlias& from) {
    CopyFrom(from);
    return *this;
  }
  inline BufferAssignmentProto_BufferAlias& operator=(BufferAssignmentProto_BufferAlias&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BufferAssignmentProto_BufferAlias& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BufferAssignmentProto_BufferAlias* internal_default_instance() {
    return reinterpret_cast<const BufferAssignmentProto_BufferAlias*>(
               &_BufferAssignmentProto_BufferAlias_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(BufferAssignmentProto_BufferAlias& a, BufferAssignmentProto_BufferAlias& b) {
    a.Swap(&b);
  }
  inline void Swap(BufferAssignmentProto_BufferAlias* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BufferAssignmentProto_BufferAlias* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BufferAssignmentProto_BufferAlias* New() const final {
    return CreateMaybeMessage<BufferAssignmentProto_BufferAlias>(nullptr);
  }

  BufferAssignmentProto_BufferAlias* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BufferAssignmentProto_BufferAlias>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BufferAssignmentProto_BufferAlias& from);
  void MergeFrom(const BufferAssignmentProto_BufferAlias& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BufferAssignmentProto_BufferAlias* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.BufferAssignmentProto.BufferAlias";
  }
  protected:
  explicit BufferAssignmentProto_BufferAlias(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocationFieldNumber = 2,
    kSourceBufferIdFieldNumber = 1,
  };
  // .xla.LogicalBufferProto.Location location = 2;
  bool has_location() const;
  void clear_location();
  const ::xla::LogicalBufferProto_Location& location() const;
  ::xla::LogicalBufferProto_Location* release_location();
  ::xla::LogicalBufferProto_Location* mutable_location();
  void set_allocated_location(::xla::LogicalBufferProto_Location* location);
  void unsafe_arena_set_allocated_location(
      ::xla::LogicalBufferProto_Location* location);
  ::xla::LogicalBufferProto_Location* unsafe_arena_release_location();

  // int64 source_buffer_id = 1;
  void clear_source_buffer_id();
  ::PROTOBUF_NAMESPACE_ID::int64 source_buffer_id() const;
  void set_source_buffer_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.BufferAssignmentProto.BufferAlias)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::xla::LogicalBufferProto_Location* location_;
  ::PROTOBUF_NAMESPACE_ID::int64 source_buffer_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class BufferAssignmentProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.BufferAssignmentProto) */ {
 public:
  BufferAssignmentProto();
  virtual ~BufferAssignmentProto();

  BufferAssignmentProto(const BufferAssignmentProto& from);
  BufferAssignmentProto(BufferAssignmentProto&& from) noexcept
    : BufferAssignmentProto() {
    *this = ::std::move(from);
  }

  inline BufferAssignmentProto& operator=(const BufferAssignmentProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline BufferAssignmentProto& operator=(BufferAssignmentProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BufferAssignmentProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BufferAssignmentProto* internal_default_instance() {
    return reinterpret_cast<const BufferAssignmentProto*>(
               &_BufferAssignmentProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(BufferAssignmentProto& a, BufferAssignmentProto& b) {
    a.Swap(&b);
  }
  inline void Swap(BufferAssignmentProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BufferAssignmentProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BufferAssignmentProto* New() const final {
    return CreateMaybeMessage<BufferAssignmentProto>(nullptr);
  }

  BufferAssignmentProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BufferAssignmentProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BufferAssignmentProto& from);
  void MergeFrom(const BufferAssignmentProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BufferAssignmentProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.BufferAssignmentProto";
  }
  protected:
  explicit BufferAssignmentProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef BufferAssignmentProto_BufferAlias BufferAlias;

  // accessors -------------------------------------------------------

  enum : int {
    kLogicalBuffersFieldNumber = 1,
    kBufferAliasesFieldNumber = 2,
    kBufferAllocationsFieldNumber = 3,
    kHeapSimulatorTracesFieldNumber = 4,
  };
  // repeated .xla.LogicalBufferProto logical_buffers = 1;
  int logical_buffers_size() const;
  void clear_logical_buffers();
  ::xla::LogicalBufferProto* mutable_logical_buffers(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LogicalBufferProto >*
      mutable_logical_buffers();
  const ::xla::LogicalBufferProto& logical_buffers(int index) const;
  ::xla::LogicalBufferProto* add_logical_buffers();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LogicalBufferProto >&
      logical_buffers() const;

  // repeated .xla.BufferAssignmentProto.BufferAlias buffer_aliases = 2;
  int buffer_aliases_size() const;
  void clear_buffer_aliases();
  ::xla::BufferAssignmentProto_BufferAlias* mutable_buffer_aliases(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAssignmentProto_BufferAlias >*
      mutable_buffer_aliases();
  const ::xla::BufferAssignmentProto_BufferAlias& buffer_aliases(int index) const;
  ::xla::BufferAssignmentProto_BufferAlias* add_buffer_aliases();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAssignmentProto_BufferAlias >&
      buffer_aliases() const;

  // repeated .xla.BufferAllocationProto buffer_allocations = 3;
  int buffer_allocations_size() const;
  void clear_buffer_allocations();
  ::xla::BufferAllocationProto* mutable_buffer_allocations(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAllocationProto >*
      mutable_buffer_allocations();
  const ::xla::BufferAllocationProto& buffer_allocations(int index) const;
  ::xla::BufferAllocationProto* add_buffer_allocations();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAllocationProto >&
      buffer_allocations() const;

  // repeated .xla.HeapSimulatorTrace heap_simulator_traces = 4;
  int heap_simulator_traces_size() const;
  void clear_heap_simulator_traces();
  ::xla::HeapSimulatorTrace* mutable_heap_simulator_traces(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HeapSimulatorTrace >*
      mutable_heap_simulator_traces();
  const ::xla::HeapSimulatorTrace& heap_simulator_traces(int index) const;
  ::xla::HeapSimulatorTrace* add_heap_simulator_traces();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HeapSimulatorTrace >&
      heap_simulator_traces() const;

  // @@protoc_insertion_point(class_scope:xla.BufferAssignmentProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LogicalBufferProto > logical_buffers_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAssignmentProto_BufferAlias > buffer_aliases_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAllocationProto > buffer_allocations_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HeapSimulatorTrace > heap_simulator_traces_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloProto) */ {
 public:
  HloProto();
  virtual ~HloProto();

  HloProto(const HloProto& from);
  HloProto(HloProto&& from) noexcept
    : HloProto() {
    *this = ::std::move(from);
  }

  inline HloProto& operator=(const HloProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloProto& operator=(HloProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloProto* internal_default_instance() {
    return reinterpret_cast<const HloProto*>(
               &_HloProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(HloProto& a, HloProto& b) {
    a.Swap(&b);
  }
  inline void Swap(HloProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloProto* New() const final {
    return CreateMaybeMessage<HloProto>(nullptr);
  }

  HloProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloProto& from);
  void MergeFrom(const HloProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloProto";
  }
  protected:
  explicit HloProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHloModuleFieldNumber = 1,
    kBufferAssignmentFieldNumber = 3,
  };
  // .xla.HloModuleProto hlo_module = 1;
  bool has_hlo_module() const;
  void clear_hlo_module();
  const ::xla::HloModuleProto& hlo_module() const;
  ::xla::HloModuleProto* release_hlo_module();
  ::xla::HloModuleProto* mutable_hlo_module();
  void set_allocated_hlo_module(::xla::HloModuleProto* hlo_module);
  void unsafe_arena_set_allocated_hlo_module(
      ::xla::HloModuleProto* hlo_module);
  ::xla::HloModuleProto* unsafe_arena_release_hlo_module();

  // .xla.BufferAssignmentProto buffer_assignment = 3;
  bool has_buffer_assignment() const;
  void clear_buffer_assignment();
  const ::xla::BufferAssignmentProto& buffer_assignment() const;
  ::xla::BufferAssignmentProto* release_buffer_assignment();
  ::xla::BufferAssignmentProto* mutable_buffer_assignment();
  void set_allocated_buffer_assignment(::xla::BufferAssignmentProto* buffer_assignment);
  void unsafe_arena_set_allocated_buffer_assignment(
      ::xla::BufferAssignmentProto* buffer_assignment);
  ::xla::BufferAssignmentProto* unsafe_arena_release_buffer_assignment();

  // @@protoc_insertion_point(class_scope:xla.HloProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::xla::HloModuleProto* hlo_module_;
  ::xla::BufferAssignmentProto* buffer_assignment_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloSnapshot :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloSnapshot) */ {
 public:
  HloSnapshot();
  virtual ~HloSnapshot();

  HloSnapshot(const HloSnapshot& from);
  HloSnapshot(HloSnapshot&& from) noexcept
    : HloSnapshot() {
    *this = ::std::move(from);
  }

  inline HloSnapshot& operator=(const HloSnapshot& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloSnapshot& operator=(HloSnapshot&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloSnapshot& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloSnapshot* internal_default_instance() {
    return reinterpret_cast<const HloSnapshot*>(
               &_HloSnapshot_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(HloSnapshot& a, HloSnapshot& b) {
    a.Swap(&b);
  }
  inline void Swap(HloSnapshot* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloSnapshot* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloSnapshot* New() const final {
    return CreateMaybeMessage<HloSnapshot>(nullptr);
  }

  HloSnapshot* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloSnapshot>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloSnapshot& from);
  void MergeFrom(const HloSnapshot& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloSnapshot* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloSnapshot";
  }
  protected:
  explicit HloSnapshot(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArgumentsFieldNumber = 2,
    kExecutionPlatformFieldNumber = 4,
    kHloFieldNumber = 1,
    kResultFieldNumber = 3,
  };
  // repeated .xla.LiteralProto arguments = 2;
  int arguments_size() const;
  void clear_arguments();
  ::xla::LiteralProto* mutable_arguments(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LiteralProto >*
      mutable_arguments();
  const ::xla::LiteralProto& arguments(int index) const;
  ::xla::LiteralProto* add_arguments();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LiteralProto >&
      arguments() const;

  // string execution_platform = 4;
  void clear_execution_platform();
  const std::string& execution_platform() const;
  void set_execution_platform(const std::string& value);
  void set_execution_platform(std::string&& value);
  void set_execution_platform(const char* value);
  void set_execution_platform(const char* value, size_t size);
  std::string* mutable_execution_platform();
  std::string* release_execution_platform();
  void set_allocated_execution_platform(std::string* execution_platform);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_execution_platform();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_execution_platform(
      std::string* execution_platform);

  // .xla.HloProto hlo = 1;
  bool has_hlo() const;
  void clear_hlo();
  const ::xla::HloProto& hlo() const;
  ::xla::HloProto* release_hlo();
  ::xla::HloProto* mutable_hlo();
  void set_allocated_hlo(::xla::HloProto* hlo);
  void unsafe_arena_set_allocated_hlo(
      ::xla::HloProto* hlo);
  ::xla::HloProto* unsafe_arena_release_hlo();

  // .xla.LiteralProto result = 3;
  bool has_result() const;
  void clear_result();
  const ::xla::LiteralProto& result() const;
  ::xla::LiteralProto* release_result();
  ::xla::LiteralProto* mutable_result();
  void set_allocated_result(::xla::LiteralProto* result);
  void unsafe_arena_set_allocated_result(
      ::xla::LiteralProto* result);
  ::xla::LiteralProto* unsafe_arena_release_result();

  // @@protoc_insertion_point(class_scope:xla.HloSnapshot)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LiteralProto > arguments_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr execution_platform_;
  ::xla::HloProto* hlo_;
  ::xla::LiteralProto* result_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloModuleMetadataProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloModuleMetadataProto) */ {
 public:
  HloModuleMetadataProto();
  virtual ~HloModuleMetadataProto();

  HloModuleMetadataProto(const HloModuleMetadataProto& from);
  HloModuleMetadataProto(HloModuleMetadataProto&& from) noexcept
    : HloModuleMetadataProto() {
    *this = ::std::move(from);
  }

  inline HloModuleMetadataProto& operator=(const HloModuleMetadataProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloModuleMetadataProto& operator=(HloModuleMetadataProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloModuleMetadataProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloModuleMetadataProto* internal_default_instance() {
    return reinterpret_cast<const HloModuleMetadataProto*>(
               &_HloModuleMetadataProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(HloModuleMetadataProto& a, HloModuleMetadataProto& b) {
    a.Swap(&b);
  }
  inline void Swap(HloModuleMetadataProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloModuleMetadataProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloModuleMetadataProto* New() const final {
    return CreateMaybeMessage<HloModuleMetadataProto>(nullptr);
  }

  HloModuleMetadataProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloModuleMetadataProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloModuleMetadataProto& from);
  void MergeFrom(const HloModuleMetadataProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloModuleMetadataProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloModuleMetadataProto";
  }
  protected:
  explicit HloModuleMetadataProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPartitionedModuleIdsFieldNumber = 4,
    kPassMetadataFieldNumber = 5,
    kModuleGroupNameFieldNumber = 2,
    kCanonicalModuleIdFieldNumber = 1,
    kOriginalModuleIdFieldNumber = 3,
  };
  // repeated int64 partitioned_module_ids = 4;
  int partitioned_module_ids_size() const;
  void clear_partitioned_module_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 partitioned_module_ids(int index) const;
  void set_partitioned_module_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_partitioned_module_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      partitioned_module_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_partitioned_module_ids();

  // repeated .xla.HloPassMetadata pass_metadata = 5;
  int pass_metadata_size() const;
  void clear_pass_metadata();
  ::xla::HloPassMetadata* mutable_pass_metadata(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloPassMetadata >*
      mutable_pass_metadata();
  const ::xla::HloPassMetadata& pass_metadata(int index) const;
  ::xla::HloPassMetadata* add_pass_metadata();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloPassMetadata >&
      pass_metadata() const;

  // string module_group_name = 2;
  void clear_module_group_name();
  const std::string& module_group_name() const;
  void set_module_group_name(const std::string& value);
  void set_module_group_name(std::string&& value);
  void set_module_group_name(const char* value);
  void set_module_group_name(const char* value, size_t size);
  std::string* mutable_module_group_name();
  std::string* release_module_group_name();
  void set_allocated_module_group_name(std::string* module_group_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_module_group_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_module_group_name(
      std::string* module_group_name);

  // int64 canonical_module_id = 1;
  void clear_canonical_module_id();
  ::PROTOBUF_NAMESPACE_ID::int64 canonical_module_id() const;
  void set_canonical_module_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 original_module_id = 3;
  void clear_original_module_id();
  ::PROTOBUF_NAMESPACE_ID::int64 original_module_id() const;
  void set_original_module_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.HloModuleMetadataProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > partitioned_module_ids_;
  mutable std::atomic<int> _partitioned_module_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloPassMetadata > pass_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr module_group_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 canonical_module_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 original_module_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// -------------------------------------------------------------------

class HloPassMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloPassMetadata) */ {
 public:
  HloPassMetadata();
  virtual ~HloPassMetadata();

  HloPassMetadata(const HloPassMetadata& from);
  HloPassMetadata(HloPassMetadata&& from) noexcept
    : HloPassMetadata() {
    *this = ::std::move(from);
  }

  inline HloPassMetadata& operator=(const HloPassMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloPassMetadata& operator=(HloPassMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HloPassMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HloPassMetadata* internal_default_instance() {
    return reinterpret_cast<const HloPassMetadata*>(
               &_HloPassMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(HloPassMetadata& a, HloPassMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(HloPassMetadata* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloPassMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HloPassMetadata* New() const final {
    return CreateMaybeMessage<HloPassMetadata>(nullptr);
  }

  HloPassMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HloPassMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HloPassMetadata& from);
  void MergeFrom(const HloPassMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloPassMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloPassMetadata";
  }
  protected:
  explicit HloPassMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDumpFilenamesFieldNumber = 4,
    kModuleGroupModuleIdsFieldNumber = 7,
    kPassNameFieldNumber = 2,
    kPipelineNameFieldNumber = 3,
    kPassIdFieldNumber = 1,
    kModuleIdFieldNumber = 6,
    kStartTimestampUsecFieldNumber = 8,
    kEndTimestampUsecFieldNumber = 9,
    kModuleChangedFieldNumber = 5,
  };
  // repeated string dump_filenames = 4;
  int dump_filenames_size() const;
  void clear_dump_filenames();
  const std::string& dump_filenames(int index) const;
  std::string* mutable_dump_filenames(int index);
  void set_dump_filenames(int index, const std::string& value);
  void set_dump_filenames(int index, std::string&& value);
  void set_dump_filenames(int index, const char* value);
  void set_dump_filenames(int index, const char* value, size_t size);
  std::string* add_dump_filenames();
  void add_dump_filenames(const std::string& value);
  void add_dump_filenames(std::string&& value);
  void add_dump_filenames(const char* value);
  void add_dump_filenames(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& dump_filenames() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_dump_filenames();

  // repeated int64 module_group_module_ids = 7;
  int module_group_module_ids_size() const;
  void clear_module_group_module_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 module_group_module_ids(int index) const;
  void set_module_group_module_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_module_group_module_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      module_group_module_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_module_group_module_ids();

  // string pass_name = 2;
  void clear_pass_name();
  const std::string& pass_name() const;
  void set_pass_name(const std::string& value);
  void set_pass_name(std::string&& value);
  void set_pass_name(const char* value);
  void set_pass_name(const char* value, size_t size);
  std::string* mutable_pass_name();
  std::string* release_pass_name();
  void set_allocated_pass_name(std::string* pass_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_pass_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_pass_name(
      std::string* pass_name);

  // string pipeline_name = 3;
  void clear_pipeline_name();
  const std::string& pipeline_name() const;
  void set_pipeline_name(const std::string& value);
  void set_pipeline_name(std::string&& value);
  void set_pipeline_name(const char* value);
  void set_pipeline_name(const char* value, size_t size);
  std::string* mutable_pipeline_name();
  std::string* release_pipeline_name();
  void set_allocated_pipeline_name(std::string* pipeline_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_pipeline_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_pipeline_name(
      std::string* pipeline_name);

  // int64 pass_id = 1;
  void clear_pass_id();
  ::PROTOBUF_NAMESPACE_ID::int64 pass_id() const;
  void set_pass_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 module_id = 6;
  void clear_module_id();
  ::PROTOBUF_NAMESPACE_ID::int64 module_id() const;
  void set_module_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 start_timestamp_usec = 8;
  void clear_start_timestamp_usec();
  ::PROTOBUF_NAMESPACE_ID::int64 start_timestamp_usec() const;
  void set_start_timestamp_usec(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 end_timestamp_usec = 9;
  void clear_end_timestamp_usec();
  ::PROTOBUF_NAMESPACE_ID::int64 end_timestamp_usec() const;
  void set_end_timestamp_usec(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool module_changed = 5;
  void clear_module_changed();
  bool module_changed() const;
  void set_module_changed(bool value);

  // @@protoc_insertion_point(class_scope:xla.HloPassMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> dump_filenames_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > module_group_module_ids_;
  mutable std::atomic<int> _module_group_module_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pass_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pipeline_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 pass_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 module_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 start_timestamp_usec_;
  ::PROTOBUF_NAMESPACE_ID::int64 end_timestamp_usec_;
  bool module_changed_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// HloInstructionProto_SliceDimensions

// int64 start = 1;
inline void HloInstructionProto_SliceDimensions::clear_start() {
  start_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto_SliceDimensions::start() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.SliceDimensions.start)
  return start_;
}
inline void HloInstructionProto_SliceDimensions::set_start(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  start_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.SliceDimensions.start)
}

// int64 limit = 2;
inline void HloInstructionProto_SliceDimensions::clear_limit() {
  limit_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto_SliceDimensions::limit() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.SliceDimensions.limit)
  return limit_;
}
inline void HloInstructionProto_SliceDimensions::set_limit(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  limit_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.SliceDimensions.limit)
}

// int64 stride = 3;
inline void HloInstructionProto_SliceDimensions::clear_stride() {
  stride_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto_SliceDimensions::stride() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.SliceDimensions.stride)
  return stride_;
}
inline void HloInstructionProto_SliceDimensions::set_stride(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  stride_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.SliceDimensions.stride)
}

// -------------------------------------------------------------------

// HloInstructionProto

// string name = 1;
inline void HloInstructionProto::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloInstructionProto::name() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.name)
  return name_.Get();
}
inline void HloInstructionProto::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.name)
}
inline void HloInstructionProto::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloInstructionProto.name)
}
inline void HloInstructionProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloInstructionProto.name)
}
inline void HloInstructionProto::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloInstructionProto.name)
}
inline std::string* HloInstructionProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloInstructionProto::release_name() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloInstructionProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.name)
}
inline std::string* HloInstructionProto::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloInstructionProto::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloInstructionProto.name)
}

// string opcode = 2;
inline void HloInstructionProto::clear_opcode() {
  opcode_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloInstructionProto::opcode() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.opcode)
  return opcode_.Get();
}
inline void HloInstructionProto::set_opcode(const std::string& value) {
  
  opcode_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.opcode)
}
inline void HloInstructionProto::set_opcode(std::string&& value) {
  
  opcode_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloInstructionProto.opcode)
}
inline void HloInstructionProto::set_opcode(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  opcode_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloInstructionProto.opcode)
}
inline void HloInstructionProto::set_opcode(const char* value,
    size_t size) {
  
  opcode_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloInstructionProto.opcode)
}
inline std::string* HloInstructionProto::mutable_opcode() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.opcode)
  return opcode_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloInstructionProto::release_opcode() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.opcode)
  
  return opcode_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloInstructionProto::set_allocated_opcode(std::string* opcode) {
  if (opcode != nullptr) {
    
  } else {
    
  }
  opcode_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), opcode,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.opcode)
}
inline std::string* HloInstructionProto::unsafe_arena_release_opcode() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.opcode)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return opcode_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloInstructionProto::unsafe_arena_set_allocated_opcode(
    std::string* opcode) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (opcode != nullptr) {
    
  } else {
    
  }
  opcode_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      opcode, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloInstructionProto.opcode)
}

// .xla.ShapeProto shape = 3;
inline bool HloInstructionProto::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::xla::ShapeProto& HloInstructionProto::shape() const {
  const ::xla::ShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* HloInstructionProto::release_shape() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.shape)
  
  ::xla::ShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* HloInstructionProto::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.shape)
  
  ::xla::ShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* HloInstructionProto::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.shape)
  return shape_;
}
inline void HloInstructionProto::set_allocated_shape(::xla::ShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.shape)
}

// .xla.OpMetadata metadata = 7;
inline bool HloInstructionProto::has_metadata() const {
  return this != internal_default_instance() && metadata_ != nullptr;
}
inline const ::xla::OpMetadata& HloInstructionProto::metadata() const {
  const ::xla::OpMetadata* p = metadata_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.metadata)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::OpMetadata*>(
      &::xla::_OpMetadata_default_instance_);
}
inline ::xla::OpMetadata* HloInstructionProto::release_metadata() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.metadata)
  
  ::xla::OpMetadata* temp = metadata_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  metadata_ = nullptr;
  return temp;
}
inline ::xla::OpMetadata* HloInstructionProto::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.metadata)
  
  ::xla::OpMetadata* temp = metadata_;
  metadata_ = nullptr;
  return temp;
}
inline ::xla::OpMetadata* HloInstructionProto::mutable_metadata() {
  
  if (metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::OpMetadata>(GetArenaNoVirtual());
    metadata_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.metadata)
  return metadata_;
}
inline void HloInstructionProto::set_allocated_metadata(::xla::OpMetadata* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata_);
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata)->GetArena();
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.metadata)
}

// .xla.LiteralProto literal = 8;
inline bool HloInstructionProto::has_literal() const {
  return this != internal_default_instance() && literal_ != nullptr;
}
inline const ::xla::LiteralProto& HloInstructionProto::literal() const {
  const ::xla::LiteralProto* p = literal_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.literal)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LiteralProto*>(
      &::xla::_LiteralProto_default_instance_);
}
inline ::xla::LiteralProto* HloInstructionProto::release_literal() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.literal)
  
  ::xla::LiteralProto* temp = literal_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  literal_ = nullptr;
  return temp;
}
inline ::xla::LiteralProto* HloInstructionProto::unsafe_arena_release_literal() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.literal)
  
  ::xla::LiteralProto* temp = literal_;
  literal_ = nullptr;
  return temp;
}
inline ::xla::LiteralProto* HloInstructionProto::mutable_literal() {
  
  if (literal_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LiteralProto>(GetArenaNoVirtual());
    literal_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.literal)
  return literal_;
}
inline void HloInstructionProto::set_allocated_literal(::xla::LiteralProto* literal) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal_);
  }
  if (literal) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal)->GetArena();
    if (message_arena != submessage_arena) {
      literal = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, literal, submessage_arena);
    }
    
  } else {
    
  }
  literal_ = literal;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.literal)
}

// int64 parameter_number = 9;
inline void HloInstructionProto::clear_parameter_number() {
  parameter_number_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::parameter_number() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.parameter_number)
  return parameter_number_;
}
inline void HloInstructionProto::set_parameter_number(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  parameter_number_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.parameter_number)
}

// string fusion_kind = 11;
inline void HloInstructionProto::clear_fusion_kind() {
  fusion_kind_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloInstructionProto::fusion_kind() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.fusion_kind)
  return fusion_kind_.Get();
}
inline void HloInstructionProto::set_fusion_kind(const std::string& value) {
  
  fusion_kind_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.fusion_kind)
}
inline void HloInstructionProto::set_fusion_kind(std::string&& value) {
  
  fusion_kind_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloInstructionProto.fusion_kind)
}
inline void HloInstructionProto::set_fusion_kind(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  fusion_kind_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloInstructionProto.fusion_kind)
}
inline void HloInstructionProto::set_fusion_kind(const char* value,
    size_t size) {
  
  fusion_kind_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloInstructionProto.fusion_kind)
}
inline std::string* HloInstructionProto::mutable_fusion_kind() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.fusion_kind)
  return fusion_kind_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloInstructionProto::release_fusion_kind() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.fusion_kind)
  
  return fusion_kind_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloInstructionProto::set_allocated_fusion_kind(std::string* fusion_kind) {
  if (fusion_kind != nullptr) {
    
  } else {
    
  }
  fusion_kind_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), fusion_kind,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.fusion_kind)
}
inline std::string* HloInstructionProto::unsafe_arena_release_fusion_kind() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.fusion_kind)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return fusion_kind_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloInstructionProto::unsafe_arena_set_allocated_fusion_kind(
    std::string* fusion_kind) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (fusion_kind != nullptr) {
    
  } else {
    
  }
  fusion_kind_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      fusion_kind, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloInstructionProto.fusion_kind)
}

// int64 tuple_index = 13;
inline void HloInstructionProto::clear_tuple_index() {
  tuple_index_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::tuple_index() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.tuple_index)
  return tuple_index_;
}
inline void HloInstructionProto::set_tuple_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  tuple_index_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.tuple_index)
}

// repeated int64 dimensions = 14;
inline int HloInstructionProto::dimensions_size() const {
  return dimensions_.size();
}
inline void HloInstructionProto::clear_dimensions() {
  dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.dimensions)
  return dimensions_.Get(index);
}
inline void HloInstructionProto::set_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.dimensions)
}
inline void HloInstructionProto::add_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloInstructionProto::dimensions() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.dimensions)
  return dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloInstructionProto::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.dimensions)
  return &dimensions_;
}

// .xla.Window window = 15;
inline bool HloInstructionProto::has_window() const {
  return this != internal_default_instance() && window_ != nullptr;
}
inline const ::xla::Window& HloInstructionProto::window() const {
  const ::xla::Window* p = window_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.window)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::Window*>(
      &::xla::_Window_default_instance_);
}
inline ::xla::Window* HloInstructionProto::release_window() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.window)
  
  ::xla::Window* temp = window_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  window_ = nullptr;
  return temp;
}
inline ::xla::Window* HloInstructionProto::unsafe_arena_release_window() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.window)
  
  ::xla::Window* temp = window_;
  window_ = nullptr;
  return temp;
}
inline ::xla::Window* HloInstructionProto::mutable_window() {
  
  if (window_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::Window>(GetArenaNoVirtual());
    window_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.window)
  return window_;
}
inline void HloInstructionProto::set_allocated_window(::xla::Window* window) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(window_);
  }
  if (window) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(window)->GetArena();
    if (message_arena != submessage_arena) {
      window = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, window, submessage_arena);
    }
    
  } else {
    
  }
  window_ = window;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.window)
}

// .xla.ConvolutionDimensionNumbers convolution_dimension_numbers = 16;
inline bool HloInstructionProto::has_convolution_dimension_numbers() const {
  return this != internal_default_instance() && convolution_dimension_numbers_ != nullptr;
}
inline const ::xla::ConvolutionDimensionNumbers& HloInstructionProto::convolution_dimension_numbers() const {
  const ::xla::ConvolutionDimensionNumbers* p = convolution_dimension_numbers_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.convolution_dimension_numbers)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ConvolutionDimensionNumbers*>(
      &::xla::_ConvolutionDimensionNumbers_default_instance_);
}
inline ::xla::ConvolutionDimensionNumbers* HloInstructionProto::release_convolution_dimension_numbers() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.convolution_dimension_numbers)
  
  ::xla::ConvolutionDimensionNumbers* temp = convolution_dimension_numbers_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  convolution_dimension_numbers_ = nullptr;
  return temp;
}
inline ::xla::ConvolutionDimensionNumbers* HloInstructionProto::unsafe_arena_release_convolution_dimension_numbers() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.convolution_dimension_numbers)
  
  ::xla::ConvolutionDimensionNumbers* temp = convolution_dimension_numbers_;
  convolution_dimension_numbers_ = nullptr;
  return temp;
}
inline ::xla::ConvolutionDimensionNumbers* HloInstructionProto::mutable_convolution_dimension_numbers() {
  
  if (convolution_dimension_numbers_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ConvolutionDimensionNumbers>(GetArenaNoVirtual());
    convolution_dimension_numbers_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.convolution_dimension_numbers)
  return convolution_dimension_numbers_;
}
inline void HloInstructionProto::set_allocated_convolution_dimension_numbers(::xla::ConvolutionDimensionNumbers* convolution_dimension_numbers) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(convolution_dimension_numbers_);
  }
  if (convolution_dimension_numbers) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(convolution_dimension_numbers)->GetArena();
    if (message_arena != submessage_arena) {
      convolution_dimension_numbers = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, convolution_dimension_numbers, submessage_arena);
    }
    
  } else {
    
  }
  convolution_dimension_numbers_ = convolution_dimension_numbers;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.convolution_dimension_numbers)
}

// int64 feature_group_count = 50;
inline void HloInstructionProto::clear_feature_group_count() {
  feature_group_count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::feature_group_count() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.feature_group_count)
  return feature_group_count_;
}
inline void HloInstructionProto::set_feature_group_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  feature_group_count_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.feature_group_count)
}

// int64 batch_group_count = 58;
inline void HloInstructionProto::clear_batch_group_count() {
  batch_group_count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::batch_group_count() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.batch_group_count)
  return batch_group_count_;
}
inline void HloInstructionProto::set_batch_group_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  batch_group_count_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.batch_group_count)
}

// repeated .xla.HloInstructionProto.SliceDimensions slice_dimensions = 17;
inline int HloInstructionProto::slice_dimensions_size() const {
  return slice_dimensions_.size();
}
inline void HloInstructionProto::clear_slice_dimensions() {
  slice_dimensions_.Clear();
}
inline ::xla::HloInstructionProto_SliceDimensions* HloInstructionProto::mutable_slice_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.slice_dimensions)
  return slice_dimensions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto_SliceDimensions >*
HloInstructionProto::mutable_slice_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.slice_dimensions)
  return &slice_dimensions_;
}
inline const ::xla::HloInstructionProto_SliceDimensions& HloInstructionProto::slice_dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.slice_dimensions)
  return slice_dimensions_.Get(index);
}
inline ::xla::HloInstructionProto_SliceDimensions* HloInstructionProto::add_slice_dimensions() {
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.slice_dimensions)
  return slice_dimensions_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto_SliceDimensions >&
HloInstructionProto::slice_dimensions() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.slice_dimensions)
  return slice_dimensions_;
}

// int32 exponent_bits = 18;
inline void HloInstructionProto::clear_exponent_bits() {
  exponent_bits_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 HloInstructionProto::exponent_bits() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.exponent_bits)
  return exponent_bits_;
}
inline void HloInstructionProto::set_exponent_bits(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  exponent_bits_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.exponent_bits)
}

// int32 mantissa_bits = 19;
inline void HloInstructionProto::clear_mantissa_bits() {
  mantissa_bits_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 HloInstructionProto::mantissa_bits() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.mantissa_bits)
  return mantissa_bits_;
}
inline void HloInstructionProto::set_mantissa_bits(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  mantissa_bits_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.mantissa_bits)
}

// repeated int64 dynamic_slice_sizes = 20;
inline int HloInstructionProto::dynamic_slice_sizes_size() const {
  return dynamic_slice_sizes_.size();
}
inline void HloInstructionProto::clear_dynamic_slice_sizes() {
  dynamic_slice_sizes_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::dynamic_slice_sizes(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.dynamic_slice_sizes)
  return dynamic_slice_sizes_.Get(index);
}
inline void HloInstructionProto::set_dynamic_slice_sizes(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  dynamic_slice_sizes_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.dynamic_slice_sizes)
}
inline void HloInstructionProto::add_dynamic_slice_sizes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  dynamic_slice_sizes_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.dynamic_slice_sizes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloInstructionProto::dynamic_slice_sizes() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.dynamic_slice_sizes)
  return dynamic_slice_sizes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloInstructionProto::mutable_dynamic_slice_sizes() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.dynamic_slice_sizes)
  return &dynamic_slice_sizes_;
}

// .xla.PaddingConfig padding_config = 21;
inline bool HloInstructionProto::has_padding_config() const {
  return this != internal_default_instance() && padding_config_ != nullptr;
}
inline const ::xla::PaddingConfig& HloInstructionProto::padding_config() const {
  const ::xla::PaddingConfig* p = padding_config_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.padding_config)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::PaddingConfig*>(
      &::xla::_PaddingConfig_default_instance_);
}
inline ::xla::PaddingConfig* HloInstructionProto::release_padding_config() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.padding_config)
  
  ::xla::PaddingConfig* temp = padding_config_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  padding_config_ = nullptr;
  return temp;
}
inline ::xla::PaddingConfig* HloInstructionProto::unsafe_arena_release_padding_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.padding_config)
  
  ::xla::PaddingConfig* temp = padding_config_;
  padding_config_ = nullptr;
  return temp;
}
inline ::xla::PaddingConfig* HloInstructionProto::mutable_padding_config() {
  
  if (padding_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::PaddingConfig>(GetArenaNoVirtual());
    padding_config_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.padding_config)
  return padding_config_;
}
inline void HloInstructionProto::set_allocated_padding_config(::xla::PaddingConfig* padding_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(padding_config_);
  }
  if (padding_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(padding_config)->GetArena();
    if (message_arena != submessage_arena) {
      padding_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, padding_config, submessage_arena);
    }
    
  } else {
    
  }
  padding_config_ = padding_config;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.padding_config)
}

// bytes outfeed_config = 22;
inline void HloInstructionProto::clear_outfeed_config() {
  outfeed_config_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloInstructionProto::outfeed_config() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.outfeed_config)
  return outfeed_config_.Get();
}
inline void HloInstructionProto::set_outfeed_config(const std::string& value) {
  
  outfeed_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.outfeed_config)
}
inline void HloInstructionProto::set_outfeed_config(std::string&& value) {
  
  outfeed_config_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloInstructionProto.outfeed_config)
}
inline void HloInstructionProto::set_outfeed_config(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  outfeed_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloInstructionProto.outfeed_config)
}
inline void HloInstructionProto::set_outfeed_config(const void* value,
    size_t size) {
  
  outfeed_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloInstructionProto.outfeed_config)
}
inline std::string* HloInstructionProto::mutable_outfeed_config() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.outfeed_config)
  return outfeed_config_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloInstructionProto::release_outfeed_config() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.outfeed_config)
  
  return outfeed_config_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloInstructionProto::set_allocated_outfeed_config(std::string* outfeed_config) {
  if (outfeed_config != nullptr) {
    
  } else {
    
  }
  outfeed_config_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), outfeed_config,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.outfeed_config)
}
inline std::string* HloInstructionProto::unsafe_arena_release_outfeed_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.outfeed_config)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return outfeed_config_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloInstructionProto::unsafe_arena_set_allocated_outfeed_config(
    std::string* outfeed_config) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (outfeed_config != nullptr) {
    
  } else {
    
  }
  outfeed_config_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      outfeed_config, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloInstructionProto.outfeed_config)
}

// .xla.RandomDistribution distribution = 23;
inline void HloInstructionProto::clear_distribution() {
  distribution_ = 0;
}
inline ::xla::RandomDistribution HloInstructionProto::distribution() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.distribution)
  return static_cast< ::xla::RandomDistribution >(distribution_);
}
inline void HloInstructionProto::set_distribution(::xla::RandomDistribution value) {
  
  distribution_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.distribution)
}

// float epsilon = 24;
inline void HloInstructionProto::clear_epsilon() {
  epsilon_ = 0;
}
inline float HloInstructionProto::epsilon() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.epsilon)
  return epsilon_;
}
inline void HloInstructionProto::set_epsilon(float value) {
  
  epsilon_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.epsilon)
}

// int64 feature_index = 25;
inline void HloInstructionProto::clear_feature_index() {
  feature_index_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::feature_index() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.feature_index)
  return feature_index_;
}
inline void HloInstructionProto::set_feature_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  feature_index_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.feature_index)
}

// int64 channel_id = 26;
inline void HloInstructionProto::clear_channel_id() {
  channel_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::channel_id() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.channel_id)
  return channel_id_;
}
inline void HloInstructionProto::set_channel_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  channel_id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.channel_id)
}

// bytes infeed_config = 27;
inline void HloInstructionProto::clear_infeed_config() {
  infeed_config_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloInstructionProto::infeed_config() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.infeed_config)
  return infeed_config_.Get();
}
inline void HloInstructionProto::set_infeed_config(const std::string& value) {
  
  infeed_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.infeed_config)
}
inline void HloInstructionProto::set_infeed_config(std::string&& value) {
  
  infeed_config_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloInstructionProto.infeed_config)
}
inline void HloInstructionProto::set_infeed_config(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  infeed_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloInstructionProto.infeed_config)
}
inline void HloInstructionProto::set_infeed_config(const void* value,
    size_t size) {
  
  infeed_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloInstructionProto.infeed_config)
}
inline std::string* HloInstructionProto::mutable_infeed_config() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.infeed_config)
  return infeed_config_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloInstructionProto::release_infeed_config() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.infeed_config)
  
  return infeed_config_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloInstructionProto::set_allocated_infeed_config(std::string* infeed_config) {
  if (infeed_config != nullptr) {
    
  } else {
    
  }
  infeed_config_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), infeed_config,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.infeed_config)
}
inline std::string* HloInstructionProto::unsafe_arena_release_infeed_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.infeed_config)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return infeed_config_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloInstructionProto::unsafe_arena_set_allocated_infeed_config(
    std::string* infeed_config) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (infeed_config != nullptr) {
    
  } else {
    
  }
  infeed_config_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      infeed_config, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloInstructionProto.infeed_config)
}

// string custom_call_target = 28;
inline void HloInstructionProto::clear_custom_call_target() {
  custom_call_target_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloInstructionProto::custom_call_target() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.custom_call_target)
  return custom_call_target_.Get();
}
inline void HloInstructionProto::set_custom_call_target(const std::string& value) {
  
  custom_call_target_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.custom_call_target)
}
inline void HloInstructionProto::set_custom_call_target(std::string&& value) {
  
  custom_call_target_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloInstructionProto.custom_call_target)
}
inline void HloInstructionProto::set_custom_call_target(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  custom_call_target_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloInstructionProto.custom_call_target)
}
inline void HloInstructionProto::set_custom_call_target(const char* value,
    size_t size) {
  
  custom_call_target_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloInstructionProto.custom_call_target)
}
inline std::string* HloInstructionProto::mutable_custom_call_target() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.custom_call_target)
  return custom_call_target_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloInstructionProto::release_custom_call_target() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.custom_call_target)
  
  return custom_call_target_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloInstructionProto::set_allocated_custom_call_target(std::string* custom_call_target) {
  if (custom_call_target != nullptr) {
    
  } else {
    
  }
  custom_call_target_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), custom_call_target,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.custom_call_target)
}
inline std::string* HloInstructionProto::unsafe_arena_release_custom_call_target() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.custom_call_target)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return custom_call_target_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloInstructionProto::unsafe_arena_set_allocated_custom_call_target(
    std::string* custom_call_target) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (custom_call_target != nullptr) {
    
  } else {
    
  }
  custom_call_target_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      custom_call_target, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloInstructionProto.custom_call_target)
}

// .xla.ShapeProto outfeed_shape = 29;
inline bool HloInstructionProto::has_outfeed_shape() const {
  return this != internal_default_instance() && outfeed_shape_ != nullptr;
}
inline const ::xla::ShapeProto& HloInstructionProto::outfeed_shape() const {
  const ::xla::ShapeProto* p = outfeed_shape_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.outfeed_shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* HloInstructionProto::release_outfeed_shape() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.outfeed_shape)
  
  ::xla::ShapeProto* temp = outfeed_shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  outfeed_shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* HloInstructionProto::unsafe_arena_release_outfeed_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.outfeed_shape)
  
  ::xla::ShapeProto* temp = outfeed_shape_;
  outfeed_shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* HloInstructionProto::mutable_outfeed_shape() {
  
  if (outfeed_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    outfeed_shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.outfeed_shape)
  return outfeed_shape_;
}
inline void HloInstructionProto::set_allocated_outfeed_shape(::xla::ShapeProto* outfeed_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(outfeed_shape_);
  }
  if (outfeed_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(outfeed_shape)->GetArena();
    if (message_arena != submessage_arena) {
      outfeed_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, outfeed_shape, submessage_arena);
    }
    
  } else {
    
  }
  outfeed_shape_ = outfeed_shape;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.outfeed_shape)
}

// .xla.DotDimensionNumbers dot_dimension_numbers = 30;
inline bool HloInstructionProto::has_dot_dimension_numbers() const {
  return this != internal_default_instance() && dot_dimension_numbers_ != nullptr;
}
inline const ::xla::DotDimensionNumbers& HloInstructionProto::dot_dimension_numbers() const {
  const ::xla::DotDimensionNumbers* p = dot_dimension_numbers_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.dot_dimension_numbers)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::DotDimensionNumbers*>(
      &::xla::_DotDimensionNumbers_default_instance_);
}
inline ::xla::DotDimensionNumbers* HloInstructionProto::release_dot_dimension_numbers() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.dot_dimension_numbers)
  
  ::xla::DotDimensionNumbers* temp = dot_dimension_numbers_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  dot_dimension_numbers_ = nullptr;
  return temp;
}
inline ::xla::DotDimensionNumbers* HloInstructionProto::unsafe_arena_release_dot_dimension_numbers() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.dot_dimension_numbers)
  
  ::xla::DotDimensionNumbers* temp = dot_dimension_numbers_;
  dot_dimension_numbers_ = nullptr;
  return temp;
}
inline ::xla::DotDimensionNumbers* HloInstructionProto::mutable_dot_dimension_numbers() {
  
  if (dot_dimension_numbers_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DotDimensionNumbers>(GetArenaNoVirtual());
    dot_dimension_numbers_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.dot_dimension_numbers)
  return dot_dimension_numbers_;
}
inline void HloInstructionProto::set_allocated_dot_dimension_numbers(::xla::DotDimensionNumbers* dot_dimension_numbers) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(dot_dimension_numbers_);
  }
  if (dot_dimension_numbers) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(dot_dimension_numbers)->GetArena();
    if (message_arena != submessage_arena) {
      dot_dimension_numbers = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, dot_dimension_numbers, submessage_arena);
    }
    
  } else {
    
  }
  dot_dimension_numbers_ = dot_dimension_numbers;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.dot_dimension_numbers)
}

// .xla.FftType fft_type = 31;
inline void HloInstructionProto::clear_fft_type() {
  fft_type_ = 0;
}
inline ::xla::FftType HloInstructionProto::fft_type() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.fft_type)
  return static_cast< ::xla::FftType >(fft_type_);
}
inline void HloInstructionProto::set_fft_type(::xla::FftType value) {
  
  fft_type_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.fft_type)
}

// repeated int64 fft_length = 32;
inline int HloInstructionProto::fft_length_size() const {
  return fft_length_.size();
}
inline void HloInstructionProto::clear_fft_length() {
  fft_length_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::fft_length(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.fft_length)
  return fft_length_.Get(index);
}
inline void HloInstructionProto::set_fft_length(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  fft_length_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.fft_length)
}
inline void HloInstructionProto::add_fft_length(::PROTOBUF_NAMESPACE_ID::int64 value) {
  fft_length_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.fft_length)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloInstructionProto::fft_length() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.fft_length)
  return fft_length_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloInstructionProto::mutable_fft_length() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.fft_length)
  return &fft_length_;
}

// string comparison_direction = 63;
inline void HloInstructionProto::clear_comparison_direction() {
  comparison_direction_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloInstructionProto::comparison_direction() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.comparison_direction)
  return comparison_direction_.Get();
}
inline void HloInstructionProto::set_comparison_direction(const std::string& value) {
  
  comparison_direction_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.comparison_direction)
}
inline void HloInstructionProto::set_comparison_direction(std::string&& value) {
  
  comparison_direction_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloInstructionProto.comparison_direction)
}
inline void HloInstructionProto::set_comparison_direction(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  comparison_direction_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloInstructionProto.comparison_direction)
}
inline void HloInstructionProto::set_comparison_direction(const char* value,
    size_t size) {
  
  comparison_direction_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloInstructionProto.comparison_direction)
}
inline std::string* HloInstructionProto::mutable_comparison_direction() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.comparison_direction)
  return comparison_direction_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloInstructionProto::release_comparison_direction() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.comparison_direction)
  
  return comparison_direction_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloInstructionProto::set_allocated_comparison_direction(std::string* comparison_direction) {
  if (comparison_direction != nullptr) {
    
  } else {
    
  }
  comparison_direction_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), comparison_direction,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.comparison_direction)
}
inline std::string* HloInstructionProto::unsafe_arena_release_comparison_direction() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.comparison_direction)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return comparison_direction_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloInstructionProto::unsafe_arena_set_allocated_comparison_direction(
    std::string* comparison_direction) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (comparison_direction != nullptr) {
    
  } else {
    
  }
  comparison_direction_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      comparison_direction, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloInstructionProto.comparison_direction)
}

// .xla.GatherDimensionNumbers gather_dimension_numbers = 33;
inline bool HloInstructionProto::has_gather_dimension_numbers() const {
  return this != internal_default_instance() && gather_dimension_numbers_ != nullptr;
}
inline const ::xla::GatherDimensionNumbers& HloInstructionProto::gather_dimension_numbers() const {
  const ::xla::GatherDimensionNumbers* p = gather_dimension_numbers_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.gather_dimension_numbers)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::GatherDimensionNumbers*>(
      &::xla::_GatherDimensionNumbers_default_instance_);
}
inline ::xla::GatherDimensionNumbers* HloInstructionProto::release_gather_dimension_numbers() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.gather_dimension_numbers)
  
  ::xla::GatherDimensionNumbers* temp = gather_dimension_numbers_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  gather_dimension_numbers_ = nullptr;
  return temp;
}
inline ::xla::GatherDimensionNumbers* HloInstructionProto::unsafe_arena_release_gather_dimension_numbers() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.gather_dimension_numbers)
  
  ::xla::GatherDimensionNumbers* temp = gather_dimension_numbers_;
  gather_dimension_numbers_ = nullptr;
  return temp;
}
inline ::xla::GatherDimensionNumbers* HloInstructionProto::mutable_gather_dimension_numbers() {
  
  if (gather_dimension_numbers_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::GatherDimensionNumbers>(GetArenaNoVirtual());
    gather_dimension_numbers_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.gather_dimension_numbers)
  return gather_dimension_numbers_;
}
inline void HloInstructionProto::set_allocated_gather_dimension_numbers(::xla::GatherDimensionNumbers* gather_dimension_numbers) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(gather_dimension_numbers_);
  }
  if (gather_dimension_numbers) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(gather_dimension_numbers)->GetArena();
    if (message_arena != submessage_arena) {
      gather_dimension_numbers = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, gather_dimension_numbers, submessage_arena);
    }
    
  } else {
    
  }
  gather_dimension_numbers_ = gather_dimension_numbers;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.gather_dimension_numbers)
}

// repeated int64 gather_slice_sizes = 34;
inline int HloInstructionProto::gather_slice_sizes_size() const {
  return gather_slice_sizes_.size();
}
inline void HloInstructionProto::clear_gather_slice_sizes() {
  gather_slice_sizes_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::gather_slice_sizes(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.gather_slice_sizes)
  return gather_slice_sizes_.Get(index);
}
inline void HloInstructionProto::set_gather_slice_sizes(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  gather_slice_sizes_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.gather_slice_sizes)
}
inline void HloInstructionProto::add_gather_slice_sizes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  gather_slice_sizes_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.gather_slice_sizes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloInstructionProto::gather_slice_sizes() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.gather_slice_sizes)
  return gather_slice_sizes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloInstructionProto::mutable_gather_slice_sizes() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.gather_slice_sizes)
  return &gather_slice_sizes_;
}

// string channel_name = 41;
inline void HloInstructionProto::clear_channel_name() {
  channel_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloInstructionProto::channel_name() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.channel_name)
  return channel_name_.Get();
}
inline void HloInstructionProto::set_channel_name(const std::string& value) {
  
  channel_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.channel_name)
}
inline void HloInstructionProto::set_channel_name(std::string&& value) {
  
  channel_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloInstructionProto.channel_name)
}
inline void HloInstructionProto::set_channel_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  channel_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloInstructionProto.channel_name)
}
inline void HloInstructionProto::set_channel_name(const char* value,
    size_t size) {
  
  channel_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloInstructionProto.channel_name)
}
inline std::string* HloInstructionProto::mutable_channel_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.channel_name)
  return channel_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloInstructionProto::release_channel_name() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.channel_name)
  
  return channel_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloInstructionProto::set_allocated_channel_name(std::string* channel_name) {
  if (channel_name != nullptr) {
    
  } else {
    
  }
  channel_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), channel_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.channel_name)
}
inline std::string* HloInstructionProto::unsafe_arena_release_channel_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.channel_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return channel_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloInstructionProto::unsafe_arena_set_allocated_channel_name(
    std::string* channel_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (channel_name != nullptr) {
    
  } else {
    
  }
  channel_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      channel_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloInstructionProto.channel_name)
}

// int64 cost_estimate_ns = 42;
inline void HloInstructionProto::clear_cost_estimate_ns() {
  cost_estimate_ns_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::cost_estimate_ns() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.cost_estimate_ns)
  return cost_estimate_ns_;
}
inline void HloInstructionProto::set_cost_estimate_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  cost_estimate_ns_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.cost_estimate_ns)
}

// int64 id = 35;
inline void HloInstructionProto::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::id() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.id)
  return id_;
}
inline void HloInstructionProto::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.id)
}

// repeated int64 operand_ids = 36;
inline int HloInstructionProto::operand_ids_size() const {
  return operand_ids_.size();
}
inline void HloInstructionProto::clear_operand_ids() {
  operand_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::operand_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.operand_ids)
  return operand_ids_.Get(index);
}
inline void HloInstructionProto::set_operand_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  operand_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.operand_ids)
}
inline void HloInstructionProto::add_operand_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  operand_ids_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.operand_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloInstructionProto::operand_ids() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.operand_ids)
  return operand_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloInstructionProto::mutable_operand_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.operand_ids)
  return &operand_ids_;
}

// repeated int64 control_predecessor_ids = 37;
inline int HloInstructionProto::control_predecessor_ids_size() const {
  return control_predecessor_ids_.size();
}
inline void HloInstructionProto::clear_control_predecessor_ids() {
  control_predecessor_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::control_predecessor_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.control_predecessor_ids)
  return control_predecessor_ids_.Get(index);
}
inline void HloInstructionProto::set_control_predecessor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  control_predecessor_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.control_predecessor_ids)
}
inline void HloInstructionProto::add_control_predecessor_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  control_predecessor_ids_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.control_predecessor_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloInstructionProto::control_predecessor_ids() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.control_predecessor_ids)
  return control_predecessor_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloInstructionProto::mutable_control_predecessor_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.control_predecessor_ids)
  return &control_predecessor_ids_;
}

// repeated int64 called_computation_ids = 38;
inline int HloInstructionProto::called_computation_ids_size() const {
  return called_computation_ids_.size();
}
inline void HloInstructionProto::clear_called_computation_ids() {
  called_computation_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::called_computation_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.called_computation_ids)
  return called_computation_ids_.Get(index);
}
inline void HloInstructionProto::set_called_computation_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  called_computation_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.called_computation_ids)
}
inline void HloInstructionProto::add_called_computation_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  called_computation_ids_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.called_computation_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloInstructionProto::called_computation_ids() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.called_computation_ids)
  return called_computation_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloInstructionProto::mutable_called_computation_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.called_computation_ids)
  return &called_computation_ids_;
}

// .xla.OpSharding sharding = 40;
inline bool HloInstructionProto::has_sharding() const {
  return this != internal_default_instance() && sharding_ != nullptr;
}
inline const ::xla::OpSharding& HloInstructionProto::sharding() const {
  const ::xla::OpSharding* p = sharding_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.sharding)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::OpSharding*>(
      &::xla::_OpSharding_default_instance_);
}
inline ::xla::OpSharding* HloInstructionProto::release_sharding() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.sharding)
  
  ::xla::OpSharding* temp = sharding_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* HloInstructionProto::unsafe_arena_release_sharding() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.sharding)
  
  ::xla::OpSharding* temp = sharding_;
  sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* HloInstructionProto::mutable_sharding() {
  
  if (sharding_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::OpSharding>(GetArenaNoVirtual());
    sharding_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.sharding)
  return sharding_;
}
inline void HloInstructionProto::set_allocated_sharding(::xla::OpSharding* sharding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(sharding_);
  }
  if (sharding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(sharding)->GetArena();
    if (message_arena != submessage_arena) {
      sharding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sharding, submessage_arena);
    }
    
  } else {
    
  }
  sharding_ = sharding;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.sharding)
}

// bytes backend_config = 43;
inline void HloInstructionProto::clear_backend_config() {
  backend_config_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloInstructionProto::backend_config() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.backend_config)
  return backend_config_.Get();
}
inline void HloInstructionProto::set_backend_config(const std::string& value) {
  
  backend_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.backend_config)
}
inline void HloInstructionProto::set_backend_config(std::string&& value) {
  
  backend_config_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloInstructionProto.backend_config)
}
inline void HloInstructionProto::set_backend_config(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  backend_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloInstructionProto.backend_config)
}
inline void HloInstructionProto::set_backend_config(const void* value,
    size_t size) {
  
  backend_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloInstructionProto.backend_config)
}
inline std::string* HloInstructionProto::mutable_backend_config() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.backend_config)
  return backend_config_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloInstructionProto::release_backend_config() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.backend_config)
  
  return backend_config_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloInstructionProto::set_allocated_backend_config(std::string* backend_config) {
  if (backend_config != nullptr) {
    
  } else {
    
  }
  backend_config_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), backend_config,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.backend_config)
}
inline std::string* HloInstructionProto::unsafe_arena_release_backend_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.backend_config)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return backend_config_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloInstructionProto::unsafe_arena_set_allocated_backend_config(
    std::string* backend_config) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (backend_config != nullptr) {
    
  } else {
    
  }
  backend_config_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      backend_config, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloInstructionProto.backend_config)
}

// repeated .xla.ReplicaGroup replica_groups = 49;
inline int HloInstructionProto::replica_groups_size() const {
  return replica_groups_.size();
}
inline ::xla::ReplicaGroup* HloInstructionProto::mutable_replica_groups(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.replica_groups)
  return replica_groups_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ReplicaGroup >*
HloInstructionProto::mutable_replica_groups() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.replica_groups)
  return &replica_groups_;
}
inline const ::xla::ReplicaGroup& HloInstructionProto::replica_groups(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.replica_groups)
  return replica_groups_.Get(index);
}
inline ::xla::ReplicaGroup* HloInstructionProto::add_replica_groups() {
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.replica_groups)
  return replica_groups_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ReplicaGroup >&
HloInstructionProto::replica_groups() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.replica_groups)
  return replica_groups_;
}

// int64 all_reduce_id = 45 [deprecated = true];
inline void HloInstructionProto::clear_all_reduce_id() {
  all_reduce_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::all_reduce_id() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.all_reduce_id)
  return all_reduce_id_;
}
inline void HloInstructionProto::set_all_reduce_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  all_reduce_id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.all_reduce_id)
}

// bool use_global_device_ids = 71;
inline void HloInstructionProto::clear_use_global_device_ids() {
  use_global_device_ids_ = false;
}
inline bool HloInstructionProto::use_global_device_ids() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.use_global_device_ids)
  return use_global_device_ids_;
}
inline void HloInstructionProto::set_use_global_device_ids(bool value) {
  
  use_global_device_ids_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.use_global_device_ids)
}

// bool is_host_transfer = 47;
inline void HloInstructionProto::clear_is_host_transfer() {
  is_host_transfer_ = false;
}
inline bool HloInstructionProto::is_host_transfer() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.is_host_transfer)
  return is_host_transfer_;
}
inline void HloInstructionProto::set_is_host_transfer(bool value) {
  
  is_host_transfer_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.is_host_transfer)
}

// bool is_stable = 60;
inline void HloInstructionProto::clear_is_stable() {
  is_stable_ = false;
}
inline bool HloInstructionProto::is_stable() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.is_stable)
  return is_stable_;
}
inline void HloInstructionProto::set_is_stable(bool value) {
  
  is_stable_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.is_stable)
}

// .xla.ScatterDimensionNumbers scatter_dimension_numbers = 48;
inline bool HloInstructionProto::has_scatter_dimension_numbers() const {
  return this != internal_default_instance() && scatter_dimension_numbers_ != nullptr;
}
inline const ::xla::ScatterDimensionNumbers& HloInstructionProto::scatter_dimension_numbers() const {
  const ::xla::ScatterDimensionNumbers* p = scatter_dimension_numbers_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.scatter_dimension_numbers)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ScatterDimensionNumbers*>(
      &::xla::_ScatterDimensionNumbers_default_instance_);
}
inline ::xla::ScatterDimensionNumbers* HloInstructionProto::release_scatter_dimension_numbers() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.scatter_dimension_numbers)
  
  ::xla::ScatterDimensionNumbers* temp = scatter_dimension_numbers_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  scatter_dimension_numbers_ = nullptr;
  return temp;
}
inline ::xla::ScatterDimensionNumbers* HloInstructionProto::unsafe_arena_release_scatter_dimension_numbers() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.scatter_dimension_numbers)
  
  ::xla::ScatterDimensionNumbers* temp = scatter_dimension_numbers_;
  scatter_dimension_numbers_ = nullptr;
  return temp;
}
inline ::xla::ScatterDimensionNumbers* HloInstructionProto::mutable_scatter_dimension_numbers() {
  
  if (scatter_dimension_numbers_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ScatterDimensionNumbers>(GetArenaNoVirtual());
    scatter_dimension_numbers_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.scatter_dimension_numbers)
  return scatter_dimension_numbers_;
}
inline void HloInstructionProto::set_allocated_scatter_dimension_numbers(::xla::ScatterDimensionNumbers* scatter_dimension_numbers) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(scatter_dimension_numbers_);
  }
  if (scatter_dimension_numbers) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(scatter_dimension_numbers)->GetArena();
    if (message_arena != submessage_arena) {
      scatter_dimension_numbers = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, scatter_dimension_numbers, submessage_arena);
    }
    
  } else {
    
  }
  scatter_dimension_numbers_ = scatter_dimension_numbers;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.scatter_dimension_numbers)
}

// .xla.PrecisionConfig precision_config = 51;
inline bool HloInstructionProto::has_precision_config() const {
  return this != internal_default_instance() && precision_config_ != nullptr;
}
inline const ::xla::PrecisionConfig& HloInstructionProto::precision_config() const {
  const ::xla::PrecisionConfig* p = precision_config_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.precision_config)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::PrecisionConfig*>(
      &::xla::_PrecisionConfig_default_instance_);
}
inline ::xla::PrecisionConfig* HloInstructionProto::release_precision_config() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.precision_config)
  
  ::xla::PrecisionConfig* temp = precision_config_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  precision_config_ = nullptr;
  return temp;
}
inline ::xla::PrecisionConfig* HloInstructionProto::unsafe_arena_release_precision_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.precision_config)
  
  ::xla::PrecisionConfig* temp = precision_config_;
  precision_config_ = nullptr;
  return temp;
}
inline ::xla::PrecisionConfig* HloInstructionProto::mutable_precision_config() {
  
  if (precision_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::PrecisionConfig>(GetArenaNoVirtual());
    precision_config_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.precision_config)
  return precision_config_;
}
inline void HloInstructionProto::set_allocated_precision_config(::xla::PrecisionConfig* precision_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(precision_config_);
  }
  if (precision_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(precision_config)->GetArena();
    if (message_arena != submessage_arena) {
      precision_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, precision_config, submessage_arena);
    }
    
  } else {
    
  }
  precision_config_ = precision_config;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.precision_config)
}

// repeated .xla.SourceTarget source_target_pairs = 52;
inline int HloInstructionProto::source_target_pairs_size() const {
  return source_target_pairs_.size();
}
inline ::xla::SourceTarget* HloInstructionProto::mutable_source_target_pairs(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.source_target_pairs)
  return source_target_pairs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::SourceTarget >*
HloInstructionProto::mutable_source_target_pairs() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.source_target_pairs)
  return &source_target_pairs_;
}
inline const ::xla::SourceTarget& HloInstructionProto::source_target_pairs(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.source_target_pairs)
  return source_target_pairs_.Get(index);
}
inline ::xla::SourceTarget* HloInstructionProto::add_source_target_pairs() {
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.source_target_pairs)
  return source_target_pairs_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::SourceTarget >&
HloInstructionProto::source_target_pairs() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.source_target_pairs)
  return source_target_pairs_;
}

// .xla.OpSharding domain_entry_sharding = 54;
inline bool HloInstructionProto::has_domain_entry_sharding() const {
  return this != internal_default_instance() && domain_entry_sharding_ != nullptr;
}
inline const ::xla::OpSharding& HloInstructionProto::domain_entry_sharding() const {
  const ::xla::OpSharding* p = domain_entry_sharding_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.domain_entry_sharding)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::OpSharding*>(
      &::xla::_OpSharding_default_instance_);
}
inline ::xla::OpSharding* HloInstructionProto::release_domain_entry_sharding() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.domain_entry_sharding)
  
  ::xla::OpSharding* temp = domain_entry_sharding_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  domain_entry_sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* HloInstructionProto::unsafe_arena_release_domain_entry_sharding() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.domain_entry_sharding)
  
  ::xla::OpSharding* temp = domain_entry_sharding_;
  domain_entry_sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* HloInstructionProto::mutable_domain_entry_sharding() {
  
  if (domain_entry_sharding_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::OpSharding>(GetArenaNoVirtual());
    domain_entry_sharding_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.domain_entry_sharding)
  return domain_entry_sharding_;
}
inline void HloInstructionProto::set_allocated_domain_entry_sharding(::xla::OpSharding* domain_entry_sharding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(domain_entry_sharding_);
  }
  if (domain_entry_sharding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(domain_entry_sharding)->GetArena();
    if (message_arena != submessage_arena) {
      domain_entry_sharding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, domain_entry_sharding, submessage_arena);
    }
    
  } else {
    
  }
  domain_entry_sharding_ = domain_entry_sharding;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.domain_entry_sharding)
}

// .xla.OpSharding domain_exit_sharding = 55;
inline bool HloInstructionProto::has_domain_exit_sharding() const {
  return this != internal_default_instance() && domain_exit_sharding_ != nullptr;
}
inline const ::xla::OpSharding& HloInstructionProto::domain_exit_sharding() const {
  const ::xla::OpSharding* p = domain_exit_sharding_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.domain_exit_sharding)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::OpSharding*>(
      &::xla::_OpSharding_default_instance_);
}
inline ::xla::OpSharding* HloInstructionProto::release_domain_exit_sharding() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.domain_exit_sharding)
  
  ::xla::OpSharding* temp = domain_exit_sharding_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  domain_exit_sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* HloInstructionProto::unsafe_arena_release_domain_exit_sharding() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.domain_exit_sharding)
  
  ::xla::OpSharding* temp = domain_exit_sharding_;
  domain_exit_sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* HloInstructionProto::mutable_domain_exit_sharding() {
  
  if (domain_exit_sharding_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::OpSharding>(GetArenaNoVirtual());
    domain_exit_sharding_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.domain_exit_sharding)
  return domain_exit_sharding_;
}
inline void HloInstructionProto::set_allocated_domain_exit_sharding(::xla::OpSharding* domain_exit_sharding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(domain_exit_sharding_);
  }
  if (domain_exit_sharding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(domain_exit_sharding)->GetArena();
    if (message_arena != submessage_arena) {
      domain_exit_sharding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, domain_exit_sharding, submessage_arena);
    }
    
  } else {
    
  }
  domain_exit_sharding_ = domain_exit_sharding;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.domain_exit_sharding)
}

// bool constrain_layout = 56;
inline void HloInstructionProto::clear_constrain_layout() {
  constrain_layout_ = false;
}
inline bool HloInstructionProto::constrain_layout() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.constrain_layout)
  return constrain_layout_;
}
inline void HloInstructionProto::set_constrain_layout(bool value) {
  
  constrain_layout_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.constrain_layout)
}

// repeated .xla.ShapeProto operand_shapes_with_layout = 57;
inline int HloInstructionProto::operand_shapes_with_layout_size() const {
  return operand_shapes_with_layout_.size();
}
inline ::xla::ShapeProto* HloInstructionProto::mutable_operand_shapes_with_layout(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.operand_shapes_with_layout)
  return operand_shapes_with_layout_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
HloInstructionProto::mutable_operand_shapes_with_layout() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.operand_shapes_with_layout)
  return &operand_shapes_with_layout_;
}
inline const ::xla::ShapeProto& HloInstructionProto::operand_shapes_with_layout(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.operand_shapes_with_layout)
  return operand_shapes_with_layout_.Get(index);
}
inline ::xla::ShapeProto* HloInstructionProto::add_operand_shapes_with_layout() {
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.operand_shapes_with_layout)
  return operand_shapes_with_layout_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
HloInstructionProto::operand_shapes_with_layout() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.operand_shapes_with_layout)
  return operand_shapes_with_layout_;
}

// .xla.TriangularSolveOptions triangular_solve_options = 59;
inline bool HloInstructionProto::has_triangular_solve_options() const {
  return this != internal_default_instance() && triangular_solve_options_ != nullptr;
}
inline const ::xla::TriangularSolveOptions& HloInstructionProto::triangular_solve_options() const {
  const ::xla::TriangularSolveOptions* p = triangular_solve_options_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.triangular_solve_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::TriangularSolveOptions*>(
      &::xla::_TriangularSolveOptions_default_instance_);
}
inline ::xla::TriangularSolveOptions* HloInstructionProto::release_triangular_solve_options() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.triangular_solve_options)
  
  ::xla::TriangularSolveOptions* temp = triangular_solve_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  triangular_solve_options_ = nullptr;
  return temp;
}
inline ::xla::TriangularSolveOptions* HloInstructionProto::unsafe_arena_release_triangular_solve_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.triangular_solve_options)
  
  ::xla::TriangularSolveOptions* temp = triangular_solve_options_;
  triangular_solve_options_ = nullptr;
  return temp;
}
inline ::xla::TriangularSolveOptions* HloInstructionProto::mutable_triangular_solve_options() {
  
  if (triangular_solve_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::TriangularSolveOptions>(GetArenaNoVirtual());
    triangular_solve_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.triangular_solve_options)
  return triangular_solve_options_;
}
inline void HloInstructionProto::set_allocated_triangular_solve_options(::xla::TriangularSolveOptions* triangular_solve_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(triangular_solve_options_);
  }
  if (triangular_solve_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(triangular_solve_options)->GetArena();
    if (message_arena != submessage_arena) {
      triangular_solve_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, triangular_solve_options, submessage_arena);
    }
    
  } else {
    
  }
  triangular_solve_options_ = triangular_solve_options;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.triangular_solve_options)
}

// .xla.CholeskyOptions cholesky_options = 62;
inline bool HloInstructionProto::has_cholesky_options() const {
  return this != internal_default_instance() && cholesky_options_ != nullptr;
}
inline const ::xla::CholeskyOptions& HloInstructionProto::cholesky_options() const {
  const ::xla::CholeskyOptions* p = cholesky_options_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.cholesky_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::CholeskyOptions*>(
      &::xla::_CholeskyOptions_default_instance_);
}
inline ::xla::CholeskyOptions* HloInstructionProto::release_cholesky_options() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.cholesky_options)
  
  ::xla::CholeskyOptions* temp = cholesky_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  cholesky_options_ = nullptr;
  return temp;
}
inline ::xla::CholeskyOptions* HloInstructionProto::unsafe_arena_release_cholesky_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.cholesky_options)
  
  ::xla::CholeskyOptions* temp = cholesky_options_;
  cholesky_options_ = nullptr;
  return temp;
}
inline ::xla::CholeskyOptions* HloInstructionProto::mutable_cholesky_options() {
  
  if (cholesky_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::CholeskyOptions>(GetArenaNoVirtual());
    cholesky_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.cholesky_options)
  return cholesky_options_;
}
inline void HloInstructionProto::set_allocated_cholesky_options(::xla::CholeskyOptions* cholesky_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(cholesky_options_);
  }
  if (cholesky_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cholesky_options)->GetArena();
    if (message_arena != submessage_arena) {
      cholesky_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cholesky_options, submessage_arena);
    }
    
  } else {
    
  }
  cholesky_options_ = cholesky_options;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.cholesky_options)
}

// .xla.ParameterReplication parameter_replication = 61;
inline bool HloInstructionProto::has_parameter_replication() const {
  return this != internal_default_instance() && parameter_replication_ != nullptr;
}
inline const ::xla::ParameterReplication& HloInstructionProto::parameter_replication() const {
  const ::xla::ParameterReplication* p = parameter_replication_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.parameter_replication)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ParameterReplication*>(
      &::xla::_ParameterReplication_default_instance_);
}
inline ::xla::ParameterReplication* HloInstructionProto::release_parameter_replication() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.parameter_replication)
  
  ::xla::ParameterReplication* temp = parameter_replication_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  parameter_replication_ = nullptr;
  return temp;
}
inline ::xla::ParameterReplication* HloInstructionProto::unsafe_arena_release_parameter_replication() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.parameter_replication)
  
  ::xla::ParameterReplication* temp = parameter_replication_;
  parameter_replication_ = nullptr;
  return temp;
}
inline ::xla::ParameterReplication* HloInstructionProto::mutable_parameter_replication() {
  
  if (parameter_replication_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ParameterReplication>(GetArenaNoVirtual());
    parameter_replication_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.parameter_replication)
  return parameter_replication_;
}
inline void HloInstructionProto::set_allocated_parameter_replication(::xla::ParameterReplication* parameter_replication) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(parameter_replication_);
  }
  if (parameter_replication) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(parameter_replication)->GetArena();
    if (message_arena != submessage_arena) {
      parameter_replication = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, parameter_replication, submessage_arena);
    }
    
  } else {
    
  }
  parameter_replication_ = parameter_replication;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.parameter_replication)
}

// repeated int64 outer_dimension_partitions = 64;
inline int HloInstructionProto::outer_dimension_partitions_size() const {
  return outer_dimension_partitions_.size();
}
inline void HloInstructionProto::clear_outer_dimension_partitions() {
  outer_dimension_partitions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::outer_dimension_partitions(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.outer_dimension_partitions)
  return outer_dimension_partitions_.Get(index);
}
inline void HloInstructionProto::set_outer_dimension_partitions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  outer_dimension_partitions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.outer_dimension_partitions)
}
inline void HloInstructionProto::add_outer_dimension_partitions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  outer_dimension_partitions_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.outer_dimension_partitions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloInstructionProto::outer_dimension_partitions() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.outer_dimension_partitions)
  return outer_dimension_partitions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloInstructionProto::mutable_outer_dimension_partitions() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.outer_dimension_partitions)
  return &outer_dimension_partitions_;
}

// bool custom_call_has_side_effect = 65;
inline void HloInstructionProto::clear_custom_call_has_side_effect() {
  custom_call_has_side_effect_ = false;
}
inline bool HloInstructionProto::custom_call_has_side_effect() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.custom_call_has_side_effect)
  return custom_call_has_side_effect_;
}
inline void HloInstructionProto::set_custom_call_has_side_effect(bool value) {
  
  custom_call_has_side_effect_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.custom_call_has_side_effect)
}

// repeated .xla.CustomCallOutputOperandAliasing custom_call_output_operand_aliasing = 74;
inline int HloInstructionProto::custom_call_output_operand_aliasing_size() const {
  return custom_call_output_operand_aliasing_.size();
}
inline ::xla::CustomCallOutputOperandAliasing* HloInstructionProto::mutable_custom_call_output_operand_aliasing(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.custom_call_output_operand_aliasing)
  return custom_call_output_operand_aliasing_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::CustomCallOutputOperandAliasing >*
HloInstructionProto::mutable_custom_call_output_operand_aliasing() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInstructionProto.custom_call_output_operand_aliasing)
  return &custom_call_output_operand_aliasing_;
}
inline const ::xla::CustomCallOutputOperandAliasing& HloInstructionProto::custom_call_output_operand_aliasing(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.custom_call_output_operand_aliasing)
  return custom_call_output_operand_aliasing_.Get(index);
}
inline ::xla::CustomCallOutputOperandAliasing* HloInstructionProto::add_custom_call_output_operand_aliasing() {
  // @@protoc_insertion_point(field_add:xla.HloInstructionProto.custom_call_output_operand_aliasing)
  return custom_call_output_operand_aliasing_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::CustomCallOutputOperandAliasing >&
HloInstructionProto::custom_call_output_operand_aliasing() const {
  // @@protoc_insertion_point(field_list:xla.HloInstructionProto.custom_call_output_operand_aliasing)
  return custom_call_output_operand_aliasing_;
}

// .xla.CustomCallSchedule custom_call_schedule = 76;
inline void HloInstructionProto::clear_custom_call_schedule() {
  custom_call_schedule_ = 0;
}
inline ::xla::CustomCallSchedule HloInstructionProto::custom_call_schedule() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.custom_call_schedule)
  return static_cast< ::xla::CustomCallSchedule >(custom_call_schedule_);
}
inline void HloInstructionProto::set_custom_call_schedule(::xla::CustomCallSchedule value) {
  
  custom_call_schedule_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.custom_call_schedule)
}

// int64 delta = 66;
inline void HloInstructionProto::clear_delta() {
  delta_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInstructionProto::delta() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.delta)
  return delta_;
}
inline void HloInstructionProto::set_delta(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  delta_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.delta)
}

// bool indices_are_sorted = 67;
inline void HloInstructionProto::clear_indices_are_sorted() {
  indices_are_sorted_ = false;
}
inline bool HloInstructionProto::indices_are_sorted() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.indices_are_sorted)
  return indices_are_sorted_;
}
inline void HloInstructionProto::set_indices_are_sorted(bool value) {
  
  indices_are_sorted_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.indices_are_sorted)
}

// .xla.FrontendAttributes frontend_attributes = 68;
inline bool HloInstructionProto::has_frontend_attributes() const {
  return this != internal_default_instance() && frontend_attributes_ != nullptr;
}
inline const ::xla::FrontendAttributes& HloInstructionProto::frontend_attributes() const {
  const ::xla::FrontendAttributes* p = frontend_attributes_;
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.frontend_attributes)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::FrontendAttributes*>(
      &::xla::_FrontendAttributes_default_instance_);
}
inline ::xla::FrontendAttributes* HloInstructionProto::release_frontend_attributes() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.frontend_attributes)
  
  ::xla::FrontendAttributes* temp = frontend_attributes_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  frontend_attributes_ = nullptr;
  return temp;
}
inline ::xla::FrontendAttributes* HloInstructionProto::unsafe_arena_release_frontend_attributes() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.frontend_attributes)
  
  ::xla::FrontendAttributes* temp = frontend_attributes_;
  frontend_attributes_ = nullptr;
  return temp;
}
inline ::xla::FrontendAttributes* HloInstructionProto::mutable_frontend_attributes() {
  
  if (frontend_attributes_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::FrontendAttributes>(GetArenaNoVirtual());
    frontend_attributes_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.frontend_attributes)
  return frontend_attributes_;
}
inline void HloInstructionProto::set_allocated_frontend_attributes(::xla::FrontendAttributes* frontend_attributes) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(frontend_attributes_);
  }
  if (frontend_attributes) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(frontend_attributes)->GetArena();
    if (message_arena != submessage_arena) {
      frontend_attributes = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, frontend_attributes, submessage_arena);
    }
    
  } else {
    
  }
  frontend_attributes_ = frontend_attributes;
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.frontend_attributes)
}

// bool unique_indices = 69;
inline void HloInstructionProto::clear_unique_indices() {
  unique_indices_ = false;
}
inline bool HloInstructionProto::unique_indices() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.unique_indices)
  return unique_indices_;
}
inline void HloInstructionProto::set_unique_indices(bool value) {
  
  unique_indices_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.unique_indices)
}

// .xla.RandomAlgorithm rng_algorithm = 70;
inline void HloInstructionProto::clear_rng_algorithm() {
  rng_algorithm_ = 0;
}
inline ::xla::RandomAlgorithm HloInstructionProto::rng_algorithm() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.rng_algorithm)
  return static_cast< ::xla::RandomAlgorithm >(rng_algorithm_);
}
inline void HloInstructionProto::set_rng_algorithm(::xla::RandomAlgorithm value) {
  
  rng_algorithm_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.rng_algorithm)
}

// string comparison_type = 72;
inline void HloInstructionProto::clear_comparison_type() {
  comparison_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloInstructionProto::comparison_type() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.comparison_type)
  return comparison_type_.Get();
}
inline void HloInstructionProto::set_comparison_type(const std::string& value) {
  
  comparison_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.comparison_type)
}
inline void HloInstructionProto::set_comparison_type(std::string&& value) {
  
  comparison_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloInstructionProto.comparison_type)
}
inline void HloInstructionProto::set_comparison_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  comparison_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloInstructionProto.comparison_type)
}
inline void HloInstructionProto::set_comparison_type(const char* value,
    size_t size) {
  
  comparison_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloInstructionProto.comparison_type)
}
inline std::string* HloInstructionProto::mutable_comparison_type() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloInstructionProto.comparison_type)
  return comparison_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloInstructionProto::release_comparison_type() {
  // @@protoc_insertion_point(field_release:xla.HloInstructionProto.comparison_type)
  
  return comparison_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloInstructionProto::set_allocated_comparison_type(std::string* comparison_type) {
  if (comparison_type != nullptr) {
    
  } else {
    
  }
  comparison_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), comparison_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloInstructionProto.comparison_type)
}
inline std::string* HloInstructionProto::unsafe_arena_release_comparison_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloInstructionProto.comparison_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return comparison_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloInstructionProto::unsafe_arena_set_allocated_comparison_type(
    std::string* comparison_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (comparison_type != nullptr) {
    
  } else {
    
  }
  comparison_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      comparison_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloInstructionProto.comparison_type)
}

// bool is_cross_program_prefetch = 73;
inline void HloInstructionProto::clear_is_cross_program_prefetch() {
  is_cross_program_prefetch_ = false;
}
inline bool HloInstructionProto::is_cross_program_prefetch() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.is_cross_program_prefetch)
  return is_cross_program_prefetch_;
}
inline void HloInstructionProto::set_is_cross_program_prefetch(bool value) {
  
  is_cross_program_prefetch_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.is_cross_program_prefetch)
}

// .xla.PaddingType padding_type = 75;
inline void HloInstructionProto::clear_padding_type() {
  padding_type_ = 0;
}
inline ::xla::PaddingType HloInstructionProto::padding_type() const {
  // @@protoc_insertion_point(field_get:xla.HloInstructionProto.padding_type)
  return static_cast< ::xla::PaddingType >(padding_type_);
}
inline void HloInstructionProto::set_padding_type(::xla::PaddingType value) {
  
  padding_type_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInstructionProto.padding_type)
}

// -------------------------------------------------------------------

// HloComputationProto

// string name = 1;
inline void HloComputationProto::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloComputationProto::name() const {
  // @@protoc_insertion_point(field_get:xla.HloComputationProto.name)
  return name_.Get();
}
inline void HloComputationProto::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloComputationProto.name)
}
inline void HloComputationProto::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloComputationProto.name)
}
inline void HloComputationProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloComputationProto.name)
}
inline void HloComputationProto::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloComputationProto.name)
}
inline std::string* HloComputationProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloComputationProto.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloComputationProto::release_name() {
  // @@protoc_insertion_point(field_release:xla.HloComputationProto.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloComputationProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloComputationProto.name)
}
inline std::string* HloComputationProto::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloComputationProto.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloComputationProto::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloComputationProto.name)
}

// repeated .xla.HloInstructionProto instructions = 2;
inline int HloComputationProto::instructions_size() const {
  return instructions_.size();
}
inline void HloComputationProto::clear_instructions() {
  instructions_.Clear();
}
inline ::xla::HloInstructionProto* HloComputationProto::mutable_instructions(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloComputationProto.instructions)
  return instructions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto >*
HloComputationProto::mutable_instructions() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloComputationProto.instructions)
  return &instructions_;
}
inline const ::xla::HloInstructionProto& HloComputationProto::instructions(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloComputationProto.instructions)
  return instructions_.Get(index);
}
inline ::xla::HloInstructionProto* HloComputationProto::add_instructions() {
  // @@protoc_insertion_point(field_add:xla.HloComputationProto.instructions)
  return instructions_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto >&
HloComputationProto::instructions() const {
  // @@protoc_insertion_point(field_list:xla.HloComputationProto.instructions)
  return instructions_;
}

// .xla.ProgramShapeProto program_shape = 4;
inline bool HloComputationProto::has_program_shape() const {
  return this != internal_default_instance() && program_shape_ != nullptr;
}
inline const ::xla::ProgramShapeProto& HloComputationProto::program_shape() const {
  const ::xla::ProgramShapeProto* p = program_shape_;
  // @@protoc_insertion_point(field_get:xla.HloComputationProto.program_shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ProgramShapeProto*>(
      &::xla::_ProgramShapeProto_default_instance_);
}
inline ::xla::ProgramShapeProto* HloComputationProto::release_program_shape() {
  // @@protoc_insertion_point(field_release:xla.HloComputationProto.program_shape)
  
  ::xla::ProgramShapeProto* temp = program_shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  program_shape_ = nullptr;
  return temp;
}
inline ::xla::ProgramShapeProto* HloComputationProto::unsafe_arena_release_program_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloComputationProto.program_shape)
  
  ::xla::ProgramShapeProto* temp = program_shape_;
  program_shape_ = nullptr;
  return temp;
}
inline ::xla::ProgramShapeProto* HloComputationProto::mutable_program_shape() {
  
  if (program_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ProgramShapeProto>(GetArenaNoVirtual());
    program_shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloComputationProto.program_shape)
  return program_shape_;
}
inline void HloComputationProto::set_allocated_program_shape(::xla::ProgramShapeProto* program_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(program_shape_);
  }
  if (program_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(program_shape)->GetArena();
    if (message_arena != submessage_arena) {
      program_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, program_shape, submessage_arena);
    }
    
  } else {
    
  }
  program_shape_ = program_shape;
  // @@protoc_insertion_point(field_set_allocated:xla.HloComputationProto.program_shape)
}

// int64 id = 5;
inline void HloComputationProto::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloComputationProto::id() const {
  // @@protoc_insertion_point(field_get:xla.HloComputationProto.id)
  return id_;
}
inline void HloComputationProto::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloComputationProto.id)
}

// int64 root_id = 6;
inline void HloComputationProto::clear_root_id() {
  root_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloComputationProto::root_id() const {
  // @@protoc_insertion_point(field_get:xla.HloComputationProto.root_id)
  return root_id_;
}
inline void HloComputationProto::set_root_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  root_id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloComputationProto.root_id)
}

// -------------------------------------------------------------------

// HloScheduleProto_InstructionSequence

// repeated int64 instruction_ids = 1;
inline int HloScheduleProto_InstructionSequence::instruction_ids_size() const {
  return instruction_ids_.size();
}
inline void HloScheduleProto_InstructionSequence::clear_instruction_ids() {
  instruction_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloScheduleProto_InstructionSequence::instruction_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloScheduleProto.InstructionSequence.instruction_ids)
  return instruction_ids_.Get(index);
}
inline void HloScheduleProto_InstructionSequence::set_instruction_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  instruction_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloScheduleProto.InstructionSequence.instruction_ids)
}
inline void HloScheduleProto_InstructionSequence::add_instruction_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  instruction_ids_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloScheduleProto.InstructionSequence.instruction_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloScheduleProto_InstructionSequence::instruction_ids() const {
  // @@protoc_insertion_point(field_list:xla.HloScheduleProto.InstructionSequence.instruction_ids)
  return instruction_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloScheduleProto_InstructionSequence::mutable_instruction_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloScheduleProto.InstructionSequence.instruction_ids)
  return &instruction_ids_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// HloScheduleProto

// map<int64, .xla.HloScheduleProto.InstructionSequence> sequences = 1;
inline int HloScheduleProto::sequences_size() const {
  return sequences_.size();
}
inline void HloScheduleProto::clear_sequences() {
  sequences_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::xla::HloScheduleProto_InstructionSequence >&
HloScheduleProto::sequences() const {
  // @@protoc_insertion_point(field_map:xla.HloScheduleProto.sequences)
  return sequences_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::xla::HloScheduleProto_InstructionSequence >*
HloScheduleProto::mutable_sequences() {
  // @@protoc_insertion_point(field_mutable_map:xla.HloScheduleProto.sequences)
  return sequences_.MutableMap();
}

// -------------------------------------------------------------------

// HloInputOutputAliasProto_AliasEntryProto

// repeated int64 output_shape_index = 1;
inline int HloInputOutputAliasProto_AliasEntryProto::output_shape_index_size() const {
  return output_shape_index_.size();
}
inline void HloInputOutputAliasProto_AliasEntryProto::clear_output_shape_index() {
  output_shape_index_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInputOutputAliasProto_AliasEntryProto::output_shape_index(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInputOutputAliasProto.AliasEntryProto.output_shape_index)
  return output_shape_index_.Get(index);
}
inline void HloInputOutputAliasProto_AliasEntryProto::set_output_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  output_shape_index_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloInputOutputAliasProto.AliasEntryProto.output_shape_index)
}
inline void HloInputOutputAliasProto_AliasEntryProto::add_output_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  output_shape_index_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloInputOutputAliasProto.AliasEntryProto.output_shape_index)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloInputOutputAliasProto_AliasEntryProto::output_shape_index() const {
  // @@protoc_insertion_point(field_list:xla.HloInputOutputAliasProto.AliasEntryProto.output_shape_index)
  return output_shape_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloInputOutputAliasProto_AliasEntryProto::mutable_output_shape_index() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInputOutputAliasProto.AliasEntryProto.output_shape_index)
  return &output_shape_index_;
}

// int64 parameter_number = 2;
inline void HloInputOutputAliasProto_AliasEntryProto::clear_parameter_number() {
  parameter_number_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInputOutputAliasProto_AliasEntryProto::parameter_number() const {
  // @@protoc_insertion_point(field_get:xla.HloInputOutputAliasProto.AliasEntryProto.parameter_number)
  return parameter_number_;
}
inline void HloInputOutputAliasProto_AliasEntryProto::set_parameter_number(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  parameter_number_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInputOutputAliasProto.AliasEntryProto.parameter_number)
}

// repeated int64 parameter_shape_index = 3;
inline int HloInputOutputAliasProto_AliasEntryProto::parameter_shape_index_size() const {
  return parameter_shape_index_.size();
}
inline void HloInputOutputAliasProto_AliasEntryProto::clear_parameter_shape_index() {
  parameter_shape_index_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloInputOutputAliasProto_AliasEntryProto::parameter_shape_index(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInputOutputAliasProto.AliasEntryProto.parameter_shape_index)
  return parameter_shape_index_.Get(index);
}
inline void HloInputOutputAliasProto_AliasEntryProto::set_parameter_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  parameter_shape_index_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloInputOutputAliasProto.AliasEntryProto.parameter_shape_index)
}
inline void HloInputOutputAliasProto_AliasEntryProto::add_parameter_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  parameter_shape_index_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloInputOutputAliasProto.AliasEntryProto.parameter_shape_index)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloInputOutputAliasProto_AliasEntryProto::parameter_shape_index() const {
  // @@protoc_insertion_point(field_list:xla.HloInputOutputAliasProto.AliasEntryProto.parameter_shape_index)
  return parameter_shape_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloInputOutputAliasProto_AliasEntryProto::mutable_parameter_shape_index() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInputOutputAliasProto.AliasEntryProto.parameter_shape_index)
  return &parameter_shape_index_;
}

// .xla.Kind kind = 4;
inline void HloInputOutputAliasProto_AliasEntryProto::clear_kind() {
  kind_ = 0;
}
inline ::xla::Kind HloInputOutputAliasProto_AliasEntryProto::kind() const {
  // @@protoc_insertion_point(field_get:xla.HloInputOutputAliasProto.AliasEntryProto.kind)
  return static_cast< ::xla::Kind >(kind_);
}
inline void HloInputOutputAliasProto_AliasEntryProto::set_kind(::xla::Kind value) {
  
  kind_ = value;
  // @@protoc_insertion_point(field_set:xla.HloInputOutputAliasProto.AliasEntryProto.kind)
}

// -------------------------------------------------------------------

// HloInputOutputAliasProto

// repeated .xla.HloInputOutputAliasProto.AliasEntryProto entries = 1;
inline int HloInputOutputAliasProto::entries_size() const {
  return entries_.size();
}
inline void HloInputOutputAliasProto::clear_entries() {
  entries_.Clear();
}
inline ::xla::HloInputOutputAliasProto_AliasEntryProto* HloInputOutputAliasProto::mutable_entries(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloInputOutputAliasProto.entries)
  return entries_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInputOutputAliasProto_AliasEntryProto >*
HloInputOutputAliasProto::mutable_entries() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloInputOutputAliasProto.entries)
  return &entries_;
}
inline const ::xla::HloInputOutputAliasProto_AliasEntryProto& HloInputOutputAliasProto::entries(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloInputOutputAliasProto.entries)
  return entries_.Get(index);
}
inline ::xla::HloInputOutputAliasProto_AliasEntryProto* HloInputOutputAliasProto::add_entries() {
  // @@protoc_insertion_point(field_add:xla.HloInputOutputAliasProto.entries)
  return entries_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInputOutputAliasProto_AliasEntryProto >&
HloInputOutputAliasProto::entries() const {
  // @@protoc_insertion_point(field_list:xla.HloInputOutputAliasProto.entries)
  return entries_;
}

// -------------------------------------------------------------------

// DynamicParameterBindingProto_Binding

// int64 dynamic_param_num = 1;
inline void DynamicParameterBindingProto_Binding::clear_dynamic_param_num() {
  dynamic_param_num_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DynamicParameterBindingProto_Binding::dynamic_param_num() const {
  // @@protoc_insertion_point(field_get:xla.DynamicParameterBindingProto.Binding.dynamic_param_num)
  return dynamic_param_num_;
}
inline void DynamicParameterBindingProto_Binding::set_dynamic_param_num(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  dynamic_param_num_ = value;
  // @@protoc_insertion_point(field_set:xla.DynamicParameterBindingProto.Binding.dynamic_param_num)
}

// repeated int64 dynamic_param_index = 2;
inline int DynamicParameterBindingProto_Binding::dynamic_param_index_size() const {
  return dynamic_param_index_.size();
}
inline void DynamicParameterBindingProto_Binding::clear_dynamic_param_index() {
  dynamic_param_index_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DynamicParameterBindingProto_Binding::dynamic_param_index(int index) const {
  // @@protoc_insertion_point(field_get:xla.DynamicParameterBindingProto.Binding.dynamic_param_index)
  return dynamic_param_index_.Get(index);
}
inline void DynamicParameterBindingProto_Binding::set_dynamic_param_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  dynamic_param_index_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DynamicParameterBindingProto.Binding.dynamic_param_index)
}
inline void DynamicParameterBindingProto_Binding::add_dynamic_param_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  dynamic_param_index_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DynamicParameterBindingProto.Binding.dynamic_param_index)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
DynamicParameterBindingProto_Binding::dynamic_param_index() const {
  // @@protoc_insertion_point(field_list:xla.DynamicParameterBindingProto.Binding.dynamic_param_index)
  return dynamic_param_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
DynamicParameterBindingProto_Binding::mutable_dynamic_param_index() {
  // @@protoc_insertion_point(field_mutable_list:xla.DynamicParameterBindingProto.Binding.dynamic_param_index)
  return &dynamic_param_index_;
}

// int64 target_param_num = 3;
inline void DynamicParameterBindingProto_Binding::clear_target_param_num() {
  target_param_num_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DynamicParameterBindingProto_Binding::target_param_num() const {
  // @@protoc_insertion_point(field_get:xla.DynamicParameterBindingProto.Binding.target_param_num)
  return target_param_num_;
}
inline void DynamicParameterBindingProto_Binding::set_target_param_num(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  target_param_num_ = value;
  // @@protoc_insertion_point(field_set:xla.DynamicParameterBindingProto.Binding.target_param_num)
}

// repeated int64 target_param_index = 4;
inline int DynamicParameterBindingProto_Binding::target_param_index_size() const {
  return target_param_index_.size();
}
inline void DynamicParameterBindingProto_Binding::clear_target_param_index() {
  target_param_index_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DynamicParameterBindingProto_Binding::target_param_index(int index) const {
  // @@protoc_insertion_point(field_get:xla.DynamicParameterBindingProto.Binding.target_param_index)
  return target_param_index_.Get(index);
}
inline void DynamicParameterBindingProto_Binding::set_target_param_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  target_param_index_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.DynamicParameterBindingProto.Binding.target_param_index)
}
inline void DynamicParameterBindingProto_Binding::add_target_param_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  target_param_index_.Add(value);
  // @@protoc_insertion_point(field_add:xla.DynamicParameterBindingProto.Binding.target_param_index)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
DynamicParameterBindingProto_Binding::target_param_index() const {
  // @@protoc_insertion_point(field_list:xla.DynamicParameterBindingProto.Binding.target_param_index)
  return target_param_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
DynamicParameterBindingProto_Binding::mutable_target_param_index() {
  // @@protoc_insertion_point(field_mutable_list:xla.DynamicParameterBindingProto.Binding.target_param_index)
  return &target_param_index_;
}

// int64 target_param_dim_num = 5;
inline void DynamicParameterBindingProto_Binding::clear_target_param_dim_num() {
  target_param_dim_num_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DynamicParameterBindingProto_Binding::target_param_dim_num() const {
  // @@protoc_insertion_point(field_get:xla.DynamicParameterBindingProto.Binding.target_param_dim_num)
  return target_param_dim_num_;
}
inline void DynamicParameterBindingProto_Binding::set_target_param_dim_num(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  target_param_dim_num_ = value;
  // @@protoc_insertion_point(field_set:xla.DynamicParameterBindingProto.Binding.target_param_dim_num)
}

// -------------------------------------------------------------------

// DynamicParameterBindingProto

// repeated .xla.DynamicParameterBindingProto.Binding entries = 1;
inline int DynamicParameterBindingProto::entries_size() const {
  return entries_.size();
}
inline void DynamicParameterBindingProto::clear_entries() {
  entries_.Clear();
}
inline ::xla::DynamicParameterBindingProto_Binding* DynamicParameterBindingProto::mutable_entries(int index) {
  // @@protoc_insertion_point(field_mutable:xla.DynamicParameterBindingProto.entries)
  return entries_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DynamicParameterBindingProto_Binding >*
DynamicParameterBindingProto::mutable_entries() {
  // @@protoc_insertion_point(field_mutable_list:xla.DynamicParameterBindingProto.entries)
  return &entries_;
}
inline const ::xla::DynamicParameterBindingProto_Binding& DynamicParameterBindingProto::entries(int index) const {
  // @@protoc_insertion_point(field_get:xla.DynamicParameterBindingProto.entries)
  return entries_.Get(index);
}
inline ::xla::DynamicParameterBindingProto_Binding* DynamicParameterBindingProto::add_entries() {
  // @@protoc_insertion_point(field_add:xla.DynamicParameterBindingProto.entries)
  return entries_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DynamicParameterBindingProto_Binding >&
DynamicParameterBindingProto::entries() const {
  // @@protoc_insertion_point(field_list:xla.DynamicParameterBindingProto.entries)
  return entries_;
}

// -------------------------------------------------------------------

// CrossProgramPrefetch

// int64 parameter = 1;
inline void CrossProgramPrefetch::clear_parameter() {
  parameter_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CrossProgramPrefetch::parameter() const {
  // @@protoc_insertion_point(field_get:xla.CrossProgramPrefetch.parameter)
  return parameter_;
}
inline void CrossProgramPrefetch::set_parameter(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  parameter_ = value;
  // @@protoc_insertion_point(field_set:xla.CrossProgramPrefetch.parameter)
}

// repeated int64 index = 2;
inline int CrossProgramPrefetch::index_size() const {
  return index_.size();
}
inline void CrossProgramPrefetch::clear_index() {
  index_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CrossProgramPrefetch::index(int index) const {
  // @@protoc_insertion_point(field_get:xla.CrossProgramPrefetch.index)
  return index_.Get(index);
}
inline void CrossProgramPrefetch::set_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  index_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.CrossProgramPrefetch.index)
}
inline void CrossProgramPrefetch::add_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  index_.Add(value);
  // @@protoc_insertion_point(field_add:xla.CrossProgramPrefetch.index)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
CrossProgramPrefetch::index() const {
  // @@protoc_insertion_point(field_list:xla.CrossProgramPrefetch.index)
  return index_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
CrossProgramPrefetch::mutable_index() {
  // @@protoc_insertion_point(field_mutable_list:xla.CrossProgramPrefetch.index)
  return &index_;
}

// -------------------------------------------------------------------

// HloModuleProto

// string name = 1;
inline void HloModuleProto::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloModuleProto::name() const {
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.name)
  return name_.Get();
}
inline void HloModuleProto::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloModuleProto.name)
}
inline void HloModuleProto::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloModuleProto.name)
}
inline void HloModuleProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloModuleProto.name)
}
inline void HloModuleProto::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloModuleProto.name)
}
inline std::string* HloModuleProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloModuleProto.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloModuleProto::release_name() {
  // @@protoc_insertion_point(field_release:xla.HloModuleProto.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloModuleProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloModuleProto.name)
}
inline std::string* HloModuleProto::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloModuleProto.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloModuleProto::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloModuleProto.name)
}

// string entry_computation_name = 2;
inline void HloModuleProto::clear_entry_computation_name() {
  entry_computation_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloModuleProto::entry_computation_name() const {
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.entry_computation_name)
  return entry_computation_name_.Get();
}
inline void HloModuleProto::set_entry_computation_name(const std::string& value) {
  
  entry_computation_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloModuleProto.entry_computation_name)
}
inline void HloModuleProto::set_entry_computation_name(std::string&& value) {
  
  entry_computation_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloModuleProto.entry_computation_name)
}
inline void HloModuleProto::set_entry_computation_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  entry_computation_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloModuleProto.entry_computation_name)
}
inline void HloModuleProto::set_entry_computation_name(const char* value,
    size_t size) {
  
  entry_computation_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloModuleProto.entry_computation_name)
}
inline std::string* HloModuleProto::mutable_entry_computation_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloModuleProto.entry_computation_name)
  return entry_computation_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloModuleProto::release_entry_computation_name() {
  // @@protoc_insertion_point(field_release:xla.HloModuleProto.entry_computation_name)
  
  return entry_computation_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloModuleProto::set_allocated_entry_computation_name(std::string* entry_computation_name) {
  if (entry_computation_name != nullptr) {
    
  } else {
    
  }
  entry_computation_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), entry_computation_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloModuleProto.entry_computation_name)
}
inline std::string* HloModuleProto::unsafe_arena_release_entry_computation_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloModuleProto.entry_computation_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return entry_computation_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloModuleProto::unsafe_arena_set_allocated_entry_computation_name(
    std::string* entry_computation_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (entry_computation_name != nullptr) {
    
  } else {
    
  }
  entry_computation_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      entry_computation_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloModuleProto.entry_computation_name)
}

// int64 entry_computation_id = 6;
inline void HloModuleProto::clear_entry_computation_id() {
  entry_computation_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloModuleProto::entry_computation_id() const {
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.entry_computation_id)
  return entry_computation_id_;
}
inline void HloModuleProto::set_entry_computation_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  entry_computation_id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloModuleProto.entry_computation_id)
}

// repeated .xla.HloComputationProto computations = 3;
inline int HloModuleProto::computations_size() const {
  return computations_.size();
}
inline void HloModuleProto::clear_computations() {
  computations_.Clear();
}
inline ::xla::HloComputationProto* HloModuleProto::mutable_computations(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloModuleProto.computations)
  return computations_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloComputationProto >*
HloModuleProto::mutable_computations() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloModuleProto.computations)
  return &computations_;
}
inline const ::xla::HloComputationProto& HloModuleProto::computations(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.computations)
  return computations_.Get(index);
}
inline ::xla::HloComputationProto* HloModuleProto::add_computations() {
  // @@protoc_insertion_point(field_add:xla.HloModuleProto.computations)
  return computations_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloComputationProto >&
HloModuleProto::computations() const {
  // @@protoc_insertion_point(field_list:xla.HloModuleProto.computations)
  return computations_;
}

// .xla.ProgramShapeProto host_program_shape = 4;
inline bool HloModuleProto::has_host_program_shape() const {
  return this != internal_default_instance() && host_program_shape_ != nullptr;
}
inline const ::xla::ProgramShapeProto& HloModuleProto::host_program_shape() const {
  const ::xla::ProgramShapeProto* p = host_program_shape_;
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.host_program_shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ProgramShapeProto*>(
      &::xla::_ProgramShapeProto_default_instance_);
}
inline ::xla::ProgramShapeProto* HloModuleProto::release_host_program_shape() {
  // @@protoc_insertion_point(field_release:xla.HloModuleProto.host_program_shape)
  
  ::xla::ProgramShapeProto* temp = host_program_shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  host_program_shape_ = nullptr;
  return temp;
}
inline ::xla::ProgramShapeProto* HloModuleProto::unsafe_arena_release_host_program_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloModuleProto.host_program_shape)
  
  ::xla::ProgramShapeProto* temp = host_program_shape_;
  host_program_shape_ = nullptr;
  return temp;
}
inline ::xla::ProgramShapeProto* HloModuleProto::mutable_host_program_shape() {
  
  if (host_program_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ProgramShapeProto>(GetArenaNoVirtual());
    host_program_shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloModuleProto.host_program_shape)
  return host_program_shape_;
}
inline void HloModuleProto::set_allocated_host_program_shape(::xla::ProgramShapeProto* host_program_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(host_program_shape_);
  }
  if (host_program_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(host_program_shape)->GetArena();
    if (message_arena != submessage_arena) {
      host_program_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, host_program_shape, submessage_arena);
    }
    
  } else {
    
  }
  host_program_shape_ = host_program_shape;
  // @@protoc_insertion_point(field_set_allocated:xla.HloModuleProto.host_program_shape)
}

// int64 id = 5;
inline void HloModuleProto::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloModuleProto::id() const {
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.id)
  return id_;
}
inline void HloModuleProto::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloModuleProto.id)
}

// .xla.HloScheduleProto schedule = 7;
inline bool HloModuleProto::has_schedule() const {
  return this != internal_default_instance() && schedule_ != nullptr;
}
inline void HloModuleProto::clear_schedule() {
  if (GetArenaNoVirtual() == nullptr && schedule_ != nullptr) {
    delete schedule_;
  }
  schedule_ = nullptr;
}
inline const ::xla::HloScheduleProto& HloModuleProto::schedule() const {
  const ::xla::HloScheduleProto* p = schedule_;
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.schedule)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloScheduleProto*>(
      &::xla::_HloScheduleProto_default_instance_);
}
inline ::xla::HloScheduleProto* HloModuleProto::release_schedule() {
  // @@protoc_insertion_point(field_release:xla.HloModuleProto.schedule)
  
  ::xla::HloScheduleProto* temp = schedule_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  schedule_ = nullptr;
  return temp;
}
inline ::xla::HloScheduleProto* HloModuleProto::unsafe_arena_release_schedule() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloModuleProto.schedule)
  
  ::xla::HloScheduleProto* temp = schedule_;
  schedule_ = nullptr;
  return temp;
}
inline ::xla::HloScheduleProto* HloModuleProto::mutable_schedule() {
  
  if (schedule_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloScheduleProto>(GetArenaNoVirtual());
    schedule_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloModuleProto.schedule)
  return schedule_;
}
inline void HloModuleProto::set_allocated_schedule(::xla::HloScheduleProto* schedule) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete schedule_;
  }
  if (schedule) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(schedule);
    if (message_arena != submessage_arena) {
      schedule = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, schedule, submessage_arena);
    }
    
  } else {
    
  }
  schedule_ = schedule;
  // @@protoc_insertion_point(field_set_allocated:xla.HloModuleProto.schedule)
}

// .xla.HloInputOutputAliasProto input_output_alias = 8;
inline bool HloModuleProto::has_input_output_alias() const {
  return this != internal_default_instance() && input_output_alias_ != nullptr;
}
inline void HloModuleProto::clear_input_output_alias() {
  if (GetArenaNoVirtual() == nullptr && input_output_alias_ != nullptr) {
    delete input_output_alias_;
  }
  input_output_alias_ = nullptr;
}
inline const ::xla::HloInputOutputAliasProto& HloModuleProto::input_output_alias() const {
  const ::xla::HloInputOutputAliasProto* p = input_output_alias_;
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.input_output_alias)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloInputOutputAliasProto*>(
      &::xla::_HloInputOutputAliasProto_default_instance_);
}
inline ::xla::HloInputOutputAliasProto* HloModuleProto::release_input_output_alias() {
  // @@protoc_insertion_point(field_release:xla.HloModuleProto.input_output_alias)
  
  ::xla::HloInputOutputAliasProto* temp = input_output_alias_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  input_output_alias_ = nullptr;
  return temp;
}
inline ::xla::HloInputOutputAliasProto* HloModuleProto::unsafe_arena_release_input_output_alias() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloModuleProto.input_output_alias)
  
  ::xla::HloInputOutputAliasProto* temp = input_output_alias_;
  input_output_alias_ = nullptr;
  return temp;
}
inline ::xla::HloInputOutputAliasProto* HloModuleProto::mutable_input_output_alias() {
  
  if (input_output_alias_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloInputOutputAliasProto>(GetArenaNoVirtual());
    input_output_alias_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloModuleProto.input_output_alias)
  return input_output_alias_;
}
inline void HloModuleProto::set_allocated_input_output_alias(::xla::HloInputOutputAliasProto* input_output_alias) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete input_output_alias_;
  }
  if (input_output_alias) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(input_output_alias);
    if (message_arena != submessage_arena) {
      input_output_alias = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, input_output_alias, submessage_arena);
    }
    
  } else {
    
  }
  input_output_alias_ = input_output_alias;
  // @@protoc_insertion_point(field_set_allocated:xla.HloModuleProto.input_output_alias)
}

// .xla.DynamicParameterBindingProto dynamic_parameter_binding = 9;
inline bool HloModuleProto::has_dynamic_parameter_binding() const {
  return this != internal_default_instance() && dynamic_parameter_binding_ != nullptr;
}
inline void HloModuleProto::clear_dynamic_parameter_binding() {
  if (GetArenaNoVirtual() == nullptr && dynamic_parameter_binding_ != nullptr) {
    delete dynamic_parameter_binding_;
  }
  dynamic_parameter_binding_ = nullptr;
}
inline const ::xla::DynamicParameterBindingProto& HloModuleProto::dynamic_parameter_binding() const {
  const ::xla::DynamicParameterBindingProto* p = dynamic_parameter_binding_;
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.dynamic_parameter_binding)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::DynamicParameterBindingProto*>(
      &::xla::_DynamicParameterBindingProto_default_instance_);
}
inline ::xla::DynamicParameterBindingProto* HloModuleProto::release_dynamic_parameter_binding() {
  // @@protoc_insertion_point(field_release:xla.HloModuleProto.dynamic_parameter_binding)
  
  ::xla::DynamicParameterBindingProto* temp = dynamic_parameter_binding_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  dynamic_parameter_binding_ = nullptr;
  return temp;
}
inline ::xla::DynamicParameterBindingProto* HloModuleProto::unsafe_arena_release_dynamic_parameter_binding() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloModuleProto.dynamic_parameter_binding)
  
  ::xla::DynamicParameterBindingProto* temp = dynamic_parameter_binding_;
  dynamic_parameter_binding_ = nullptr;
  return temp;
}
inline ::xla::DynamicParameterBindingProto* HloModuleProto::mutable_dynamic_parameter_binding() {
  
  if (dynamic_parameter_binding_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DynamicParameterBindingProto>(GetArenaNoVirtual());
    dynamic_parameter_binding_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloModuleProto.dynamic_parameter_binding)
  return dynamic_parameter_binding_;
}
inline void HloModuleProto::set_allocated_dynamic_parameter_binding(::xla::DynamicParameterBindingProto* dynamic_parameter_binding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete dynamic_parameter_binding_;
  }
  if (dynamic_parameter_binding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(dynamic_parameter_binding);
    if (message_arena != submessage_arena) {
      dynamic_parameter_binding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, dynamic_parameter_binding, submessage_arena);
    }
    
  } else {
    
  }
  dynamic_parameter_binding_ = dynamic_parameter_binding;
  // @@protoc_insertion_point(field_set_allocated:xla.HloModuleProto.dynamic_parameter_binding)
}

// repeated .xla.CrossProgramPrefetch cross_program_prefetches = 10;
inline int HloModuleProto::cross_program_prefetches_size() const {
  return cross_program_prefetches_.size();
}
inline void HloModuleProto::clear_cross_program_prefetches() {
  cross_program_prefetches_.Clear();
}
inline ::xla::CrossProgramPrefetch* HloModuleProto::mutable_cross_program_prefetches(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloModuleProto.cross_program_prefetches)
  return cross_program_prefetches_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::CrossProgramPrefetch >*
HloModuleProto::mutable_cross_program_prefetches() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloModuleProto.cross_program_prefetches)
  return &cross_program_prefetches_;
}
inline const ::xla::CrossProgramPrefetch& HloModuleProto::cross_program_prefetches(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.cross_program_prefetches)
  return cross_program_prefetches_.Get(index);
}
inline ::xla::CrossProgramPrefetch* HloModuleProto::add_cross_program_prefetches() {
  // @@protoc_insertion_point(field_add:xla.HloModuleProto.cross_program_prefetches)
  return cross_program_prefetches_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::CrossProgramPrefetch >&
HloModuleProto::cross_program_prefetches() const {
  // @@protoc_insertion_point(field_list:xla.HloModuleProto.cross_program_prefetches)
  return cross_program_prefetches_;
}

// bool is_dynamic = 11;
inline void HloModuleProto::clear_is_dynamic() {
  is_dynamic_ = false;
}
inline bool HloModuleProto::is_dynamic() const {
  // @@protoc_insertion_point(field_get:xla.HloModuleProto.is_dynamic)
  return is_dynamic_;
}
inline void HloModuleProto::set_is_dynamic(bool value) {
  
  is_dynamic_ = value;
  // @@protoc_insertion_point(field_set:xla.HloModuleProto.is_dynamic)
}

// -------------------------------------------------------------------

// LogicalBufferProto_Location

// string computation_name = 1;
inline void LogicalBufferProto_Location::clear_computation_name() {
  computation_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& LogicalBufferProto_Location::computation_name() const {
  // @@protoc_insertion_point(field_get:xla.LogicalBufferProto.Location.computation_name)
  return computation_name_.Get();
}
inline void LogicalBufferProto_Location::set_computation_name(const std::string& value) {
  
  computation_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LogicalBufferProto.Location.computation_name)
}
inline void LogicalBufferProto_Location::set_computation_name(std::string&& value) {
  
  computation_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LogicalBufferProto.Location.computation_name)
}
inline void LogicalBufferProto_Location::set_computation_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  computation_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LogicalBufferProto.Location.computation_name)
}
inline void LogicalBufferProto_Location::set_computation_name(const char* value,
    size_t size) {
  
  computation_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LogicalBufferProto.Location.computation_name)
}
inline std::string* LogicalBufferProto_Location::mutable_computation_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.LogicalBufferProto.Location.computation_name)
  return computation_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* LogicalBufferProto_Location::release_computation_name() {
  // @@protoc_insertion_point(field_release:xla.LogicalBufferProto.Location.computation_name)
  
  return computation_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LogicalBufferProto_Location::set_allocated_computation_name(std::string* computation_name) {
  if (computation_name != nullptr) {
    
  } else {
    
  }
  computation_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), computation_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LogicalBufferProto.Location.computation_name)
}
inline std::string* LogicalBufferProto_Location::unsafe_arena_release_computation_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LogicalBufferProto.Location.computation_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return computation_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LogicalBufferProto_Location::unsafe_arena_set_allocated_computation_name(
    std::string* computation_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (computation_name != nullptr) {
    
  } else {
    
  }
  computation_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      computation_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LogicalBufferProto.Location.computation_name)
}

// string instruction_name = 2;
inline void LogicalBufferProto_Location::clear_instruction_name() {
  instruction_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& LogicalBufferProto_Location::instruction_name() const {
  // @@protoc_insertion_point(field_get:xla.LogicalBufferProto.Location.instruction_name)
  return instruction_name_.Get();
}
inline void LogicalBufferProto_Location::set_instruction_name(const std::string& value) {
  
  instruction_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.LogicalBufferProto.Location.instruction_name)
}
inline void LogicalBufferProto_Location::set_instruction_name(std::string&& value) {
  
  instruction_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.LogicalBufferProto.Location.instruction_name)
}
inline void LogicalBufferProto_Location::set_instruction_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  instruction_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.LogicalBufferProto.Location.instruction_name)
}
inline void LogicalBufferProto_Location::set_instruction_name(const char* value,
    size_t size) {
  
  instruction_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.LogicalBufferProto.Location.instruction_name)
}
inline std::string* LogicalBufferProto_Location::mutable_instruction_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.LogicalBufferProto.Location.instruction_name)
  return instruction_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* LogicalBufferProto_Location::release_instruction_name() {
  // @@protoc_insertion_point(field_release:xla.LogicalBufferProto.Location.instruction_name)
  
  return instruction_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void LogicalBufferProto_Location::set_allocated_instruction_name(std::string* instruction_name) {
  if (instruction_name != nullptr) {
    
  } else {
    
  }
  instruction_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), instruction_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.LogicalBufferProto.Location.instruction_name)
}
inline std::string* LogicalBufferProto_Location::unsafe_arena_release_instruction_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LogicalBufferProto.Location.instruction_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return instruction_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void LogicalBufferProto_Location::unsafe_arena_set_allocated_instruction_name(
    std::string* instruction_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (instruction_name != nullptr) {
    
  } else {
    
  }
  instruction_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      instruction_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.LogicalBufferProto.Location.instruction_name)
}

// repeated int64 shape_index = 3;
inline int LogicalBufferProto_Location::shape_index_size() const {
  return shape_index_.size();
}
inline void LogicalBufferProto_Location::clear_shape_index() {
  shape_index_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LogicalBufferProto_Location::shape_index(int index) const {
  // @@protoc_insertion_point(field_get:xla.LogicalBufferProto.Location.shape_index)
  return shape_index_.Get(index);
}
inline void LogicalBufferProto_Location::set_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  shape_index_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.LogicalBufferProto.Location.shape_index)
}
inline void LogicalBufferProto_Location::add_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  shape_index_.Add(value);
  // @@protoc_insertion_point(field_add:xla.LogicalBufferProto.Location.shape_index)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
LogicalBufferProto_Location::shape_index() const {
  // @@protoc_insertion_point(field_list:xla.LogicalBufferProto.Location.shape_index)
  return shape_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
LogicalBufferProto_Location::mutable_shape_index() {
  // @@protoc_insertion_point(field_mutable_list:xla.LogicalBufferProto.Location.shape_index)
  return &shape_index_;
}

// -------------------------------------------------------------------

// LogicalBufferProto

// int64 id = 1;
inline void LogicalBufferProto::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LogicalBufferProto::id() const {
  // @@protoc_insertion_point(field_get:xla.LogicalBufferProto.id)
  return id_;
}
inline void LogicalBufferProto::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:xla.LogicalBufferProto.id)
}

// int64 size = 2;
inline void LogicalBufferProto::clear_size() {
  size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LogicalBufferProto::size() const {
  // @@protoc_insertion_point(field_get:xla.LogicalBufferProto.size)
  return size_;
}
inline void LogicalBufferProto::set_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:xla.LogicalBufferProto.size)
}

// .xla.LogicalBufferProto.Location defined_at = 3;
inline bool LogicalBufferProto::has_defined_at() const {
  return this != internal_default_instance() && defined_at_ != nullptr;
}
inline void LogicalBufferProto::clear_defined_at() {
  if (GetArenaNoVirtual() == nullptr && defined_at_ != nullptr) {
    delete defined_at_;
  }
  defined_at_ = nullptr;
}
inline const ::xla::LogicalBufferProto_Location& LogicalBufferProto::defined_at() const {
  const ::xla::LogicalBufferProto_Location* p = defined_at_;
  // @@protoc_insertion_point(field_get:xla.LogicalBufferProto.defined_at)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LogicalBufferProto_Location*>(
      &::xla::_LogicalBufferProto_Location_default_instance_);
}
inline ::xla::LogicalBufferProto_Location* LogicalBufferProto::release_defined_at() {
  // @@protoc_insertion_point(field_release:xla.LogicalBufferProto.defined_at)
  
  ::xla::LogicalBufferProto_Location* temp = defined_at_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  defined_at_ = nullptr;
  return temp;
}
inline ::xla::LogicalBufferProto_Location* LogicalBufferProto::unsafe_arena_release_defined_at() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.LogicalBufferProto.defined_at)
  
  ::xla::LogicalBufferProto_Location* temp = defined_at_;
  defined_at_ = nullptr;
  return temp;
}
inline ::xla::LogicalBufferProto_Location* LogicalBufferProto::mutable_defined_at() {
  
  if (defined_at_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LogicalBufferProto_Location>(GetArenaNoVirtual());
    defined_at_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.LogicalBufferProto.defined_at)
  return defined_at_;
}
inline void LogicalBufferProto::set_allocated_defined_at(::xla::LogicalBufferProto_Location* defined_at) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete defined_at_;
  }
  if (defined_at) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(defined_at);
    if (message_arena != submessage_arena) {
      defined_at = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, defined_at, submessage_arena);
    }
    
  } else {
    
  }
  defined_at_ = defined_at;
  // @@protoc_insertion_point(field_set_allocated:xla.LogicalBufferProto.defined_at)
}

// int64 color = 4;
inline void LogicalBufferProto::clear_color() {
  color_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LogicalBufferProto::color() const {
  // @@protoc_insertion_point(field_get:xla.LogicalBufferProto.color)
  return color_;
}
inline void LogicalBufferProto::set_color(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  color_ = value;
  // @@protoc_insertion_point(field_set:xla.LogicalBufferProto.color)
}

// -------------------------------------------------------------------

// BufferAllocationProto_Assigned

// int64 logical_buffer_id = 1;
inline void BufferAllocationProto_Assigned::clear_logical_buffer_id() {
  logical_buffer_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BufferAllocationProto_Assigned::logical_buffer_id() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.Assigned.logical_buffer_id)
  return logical_buffer_id_;
}
inline void BufferAllocationProto_Assigned::set_logical_buffer_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  logical_buffer_id_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.Assigned.logical_buffer_id)
}

// int64 offset = 2;
inline void BufferAllocationProto_Assigned::clear_offset() {
  offset_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BufferAllocationProto_Assigned::offset() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.Assigned.offset)
  return offset_;
}
inline void BufferAllocationProto_Assigned::set_offset(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  offset_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.Assigned.offset)
}

// int64 size = 3;
inline void BufferAllocationProto_Assigned::clear_size() {
  size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BufferAllocationProto_Assigned::size() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.Assigned.size)
  return size_;
}
inline void BufferAllocationProto_Assigned::set_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.Assigned.size)
}

// -------------------------------------------------------------------

// BufferAllocationProto

// int64 index = 1;
inline void BufferAllocationProto::clear_index() {
  index_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BufferAllocationProto::index() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.index)
  return index_;
}
inline void BufferAllocationProto::set_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  index_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.index)
}

// int64 size = 2;
inline void BufferAllocationProto::clear_size() {
  size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BufferAllocationProto::size() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.size)
  return size_;
}
inline void BufferAllocationProto::set_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.size)
}

// bool is_thread_local = 3;
inline void BufferAllocationProto::clear_is_thread_local() {
  is_thread_local_ = false;
}
inline bool BufferAllocationProto::is_thread_local() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.is_thread_local)
  return is_thread_local_;
}
inline void BufferAllocationProto::set_is_thread_local(bool value) {
  
  is_thread_local_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.is_thread_local)
}

// bool is_tuple = 11;
inline void BufferAllocationProto::clear_is_tuple() {
  is_tuple_ = false;
}
inline bool BufferAllocationProto::is_tuple() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.is_tuple)
  return is_tuple_;
}
inline void BufferAllocationProto::set_is_tuple(bool value) {
  
  is_tuple_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.is_tuple)
}

// bool is_entry_computation_parameter = 5;
inline void BufferAllocationProto::clear_is_entry_computation_parameter() {
  is_entry_computation_parameter_ = false;
}
inline bool BufferAllocationProto::is_entry_computation_parameter() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.is_entry_computation_parameter)
  return is_entry_computation_parameter_;
}
inline void BufferAllocationProto::set_is_entry_computation_parameter(bool value) {
  
  is_entry_computation_parameter_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.is_entry_computation_parameter)
}

// bool is_constant = 12;
inline void BufferAllocationProto::clear_is_constant() {
  is_constant_ = false;
}
inline bool BufferAllocationProto::is_constant() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.is_constant)
  return is_constant_;
}
inline void BufferAllocationProto::set_is_constant(bool value) {
  
  is_constant_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.is_constant)
}

// int64 parameter_number = 6;
inline void BufferAllocationProto::clear_parameter_number() {
  parameter_number_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BufferAllocationProto::parameter_number() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.parameter_number)
  return parameter_number_;
}
inline void BufferAllocationProto::set_parameter_number(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  parameter_number_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.parameter_number)
}

// repeated int64 parameter_shape_index = 10;
inline int BufferAllocationProto::parameter_shape_index_size() const {
  return parameter_shape_index_.size();
}
inline void BufferAllocationProto::clear_parameter_shape_index() {
  parameter_shape_index_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BufferAllocationProto::parameter_shape_index(int index) const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.parameter_shape_index)
  return parameter_shape_index_.Get(index);
}
inline void BufferAllocationProto::set_parameter_shape_index(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  parameter_shape_index_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.parameter_shape_index)
}
inline void BufferAllocationProto::add_parameter_shape_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  parameter_shape_index_.Add(value);
  // @@protoc_insertion_point(field_add:xla.BufferAllocationProto.parameter_shape_index)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
BufferAllocationProto::parameter_shape_index() const {
  // @@protoc_insertion_point(field_list:xla.BufferAllocationProto.parameter_shape_index)
  return parameter_shape_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
BufferAllocationProto::mutable_parameter_shape_index() {
  // @@protoc_insertion_point(field_mutable_list:xla.BufferAllocationProto.parameter_shape_index)
  return &parameter_shape_index_;
}

// bool maybe_live_out = 7;
inline void BufferAllocationProto::clear_maybe_live_out() {
  maybe_live_out_ = false;
}
inline bool BufferAllocationProto::maybe_live_out() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.maybe_live_out)
  return maybe_live_out_;
}
inline void BufferAllocationProto::set_maybe_live_out(bool value) {
  
  maybe_live_out_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.maybe_live_out)
}

// int64 color = 8;
inline void BufferAllocationProto::clear_color() {
  color_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BufferAllocationProto::color() const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.color)
  return color_;
}
inline void BufferAllocationProto::set_color(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  color_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAllocationProto.color)
}

// repeated .xla.BufferAllocationProto.Assigned assigned = 9;
inline int BufferAllocationProto::assigned_size() const {
  return assigned_.size();
}
inline void BufferAllocationProto::clear_assigned() {
  assigned_.Clear();
}
inline ::xla::BufferAllocationProto_Assigned* BufferAllocationProto::mutable_assigned(int index) {
  // @@protoc_insertion_point(field_mutable:xla.BufferAllocationProto.assigned)
  return assigned_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAllocationProto_Assigned >*
BufferAllocationProto::mutable_assigned() {
  // @@protoc_insertion_point(field_mutable_list:xla.BufferAllocationProto.assigned)
  return &assigned_;
}
inline const ::xla::BufferAllocationProto_Assigned& BufferAllocationProto::assigned(int index) const {
  // @@protoc_insertion_point(field_get:xla.BufferAllocationProto.assigned)
  return assigned_.Get(index);
}
inline ::xla::BufferAllocationProto_Assigned* BufferAllocationProto::add_assigned() {
  // @@protoc_insertion_point(field_add:xla.BufferAllocationProto.assigned)
  return assigned_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAllocationProto_Assigned >&
BufferAllocationProto::assigned() const {
  // @@protoc_insertion_point(field_list:xla.BufferAllocationProto.assigned)
  return assigned_;
}

// -------------------------------------------------------------------

// HeapSimulatorTrace_Event

// .xla.HeapSimulatorTrace.Event.Kind kind = 1;
inline void HeapSimulatorTrace_Event::clear_kind() {
  kind_ = 0;
}
inline ::xla::HeapSimulatorTrace_Event_Kind HeapSimulatorTrace_Event::kind() const {
  // @@protoc_insertion_point(field_get:xla.HeapSimulatorTrace.Event.kind)
  return static_cast< ::xla::HeapSimulatorTrace_Event_Kind >(kind_);
}
inline void HeapSimulatorTrace_Event::set_kind(::xla::HeapSimulatorTrace_Event_Kind value) {
  
  kind_ = value;
  // @@protoc_insertion_point(field_set:xla.HeapSimulatorTrace.Event.kind)
}

// int64 buffer_id = 2;
inline void HeapSimulatorTrace_Event::clear_buffer_id() {
  buffer_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HeapSimulatorTrace_Event::buffer_id() const {
  // @@protoc_insertion_point(field_get:xla.HeapSimulatorTrace.Event.buffer_id)
  return buffer_id_;
}
inline void HeapSimulatorTrace_Event::set_buffer_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  buffer_id_ = value;
  // @@protoc_insertion_point(field_set:xla.HeapSimulatorTrace.Event.buffer_id)
}

// string computation_name = 3;
inline void HeapSimulatorTrace_Event::clear_computation_name() {
  computation_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HeapSimulatorTrace_Event::computation_name() const {
  // @@protoc_insertion_point(field_get:xla.HeapSimulatorTrace.Event.computation_name)
  return computation_name_.Get();
}
inline void HeapSimulatorTrace_Event::set_computation_name(const std::string& value) {
  
  computation_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HeapSimulatorTrace.Event.computation_name)
}
inline void HeapSimulatorTrace_Event::set_computation_name(std::string&& value) {
  
  computation_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HeapSimulatorTrace.Event.computation_name)
}
inline void HeapSimulatorTrace_Event::set_computation_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  computation_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HeapSimulatorTrace.Event.computation_name)
}
inline void HeapSimulatorTrace_Event::set_computation_name(const char* value,
    size_t size) {
  
  computation_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HeapSimulatorTrace.Event.computation_name)
}
inline std::string* HeapSimulatorTrace_Event::mutable_computation_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HeapSimulatorTrace.Event.computation_name)
  return computation_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HeapSimulatorTrace_Event::release_computation_name() {
  // @@protoc_insertion_point(field_release:xla.HeapSimulatorTrace.Event.computation_name)
  
  return computation_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HeapSimulatorTrace_Event::set_allocated_computation_name(std::string* computation_name) {
  if (computation_name != nullptr) {
    
  } else {
    
  }
  computation_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), computation_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HeapSimulatorTrace.Event.computation_name)
}
inline std::string* HeapSimulatorTrace_Event::unsafe_arena_release_computation_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HeapSimulatorTrace.Event.computation_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return computation_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HeapSimulatorTrace_Event::unsafe_arena_set_allocated_computation_name(
    std::string* computation_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (computation_name != nullptr) {
    
  } else {
    
  }
  computation_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      computation_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HeapSimulatorTrace.Event.computation_name)
}

// string instruction_name = 4;
inline void HeapSimulatorTrace_Event::clear_instruction_name() {
  instruction_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HeapSimulatorTrace_Event::instruction_name() const {
  // @@protoc_insertion_point(field_get:xla.HeapSimulatorTrace.Event.instruction_name)
  return instruction_name_.Get();
}
inline void HeapSimulatorTrace_Event::set_instruction_name(const std::string& value) {
  
  instruction_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HeapSimulatorTrace.Event.instruction_name)
}
inline void HeapSimulatorTrace_Event::set_instruction_name(std::string&& value) {
  
  instruction_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HeapSimulatorTrace.Event.instruction_name)
}
inline void HeapSimulatorTrace_Event::set_instruction_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  instruction_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HeapSimulatorTrace.Event.instruction_name)
}
inline void HeapSimulatorTrace_Event::set_instruction_name(const char* value,
    size_t size) {
  
  instruction_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HeapSimulatorTrace.Event.instruction_name)
}
inline std::string* HeapSimulatorTrace_Event::mutable_instruction_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HeapSimulatorTrace.Event.instruction_name)
  return instruction_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HeapSimulatorTrace_Event::release_instruction_name() {
  // @@protoc_insertion_point(field_release:xla.HeapSimulatorTrace.Event.instruction_name)
  
  return instruction_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HeapSimulatorTrace_Event::set_allocated_instruction_name(std::string* instruction_name) {
  if (instruction_name != nullptr) {
    
  } else {
    
  }
  instruction_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), instruction_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HeapSimulatorTrace.Event.instruction_name)
}
inline std::string* HeapSimulatorTrace_Event::unsafe_arena_release_instruction_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HeapSimulatorTrace.Event.instruction_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return instruction_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HeapSimulatorTrace_Event::unsafe_arena_set_allocated_instruction_name(
    std::string* instruction_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (instruction_name != nullptr) {
    
  } else {
    
  }
  instruction_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      instruction_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HeapSimulatorTrace.Event.instruction_name)
}

// int64 share_with_canonical_id = 5;
inline void HeapSimulatorTrace_Event::clear_share_with_canonical_id() {
  share_with_canonical_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HeapSimulatorTrace_Event::share_with_canonical_id() const {
  // @@protoc_insertion_point(field_get:xla.HeapSimulatorTrace.Event.share_with_canonical_id)
  return share_with_canonical_id_;
}
inline void HeapSimulatorTrace_Event::set_share_with_canonical_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  share_with_canonical_id_ = value;
  // @@protoc_insertion_point(field_set:xla.HeapSimulatorTrace.Event.share_with_canonical_id)
}

// -------------------------------------------------------------------

// HeapSimulatorTrace

// repeated .xla.HeapSimulatorTrace.Event events = 1;
inline int HeapSimulatorTrace::events_size() const {
  return events_.size();
}
inline void HeapSimulatorTrace::clear_events() {
  events_.Clear();
}
inline ::xla::HeapSimulatorTrace_Event* HeapSimulatorTrace::mutable_events(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HeapSimulatorTrace.events)
  return events_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HeapSimulatorTrace_Event >*
HeapSimulatorTrace::mutable_events() {
  // @@protoc_insertion_point(field_mutable_list:xla.HeapSimulatorTrace.events)
  return &events_;
}
inline const ::xla::HeapSimulatorTrace_Event& HeapSimulatorTrace::events(int index) const {
  // @@protoc_insertion_point(field_get:xla.HeapSimulatorTrace.events)
  return events_.Get(index);
}
inline ::xla::HeapSimulatorTrace_Event* HeapSimulatorTrace::add_events() {
  // @@protoc_insertion_point(field_add:xla.HeapSimulatorTrace.events)
  return events_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HeapSimulatorTrace_Event >&
HeapSimulatorTrace::events() const {
  // @@protoc_insertion_point(field_list:xla.HeapSimulatorTrace.events)
  return events_;
}

// bool whole_module_simulation = 2;
inline void HeapSimulatorTrace::clear_whole_module_simulation() {
  whole_module_simulation_ = false;
}
inline bool HeapSimulatorTrace::whole_module_simulation() const {
  // @@protoc_insertion_point(field_get:xla.HeapSimulatorTrace.whole_module_simulation)
  return whole_module_simulation_;
}
inline void HeapSimulatorTrace::set_whole_module_simulation(bool value) {
  
  whole_module_simulation_ = value;
  // @@protoc_insertion_point(field_set:xla.HeapSimulatorTrace.whole_module_simulation)
}

// int64 buffer_allocation_index = 3;
inline void HeapSimulatorTrace::clear_buffer_allocation_index() {
  buffer_allocation_index_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HeapSimulatorTrace::buffer_allocation_index() const {
  // @@protoc_insertion_point(field_get:xla.HeapSimulatorTrace.buffer_allocation_index)
  return buffer_allocation_index_;
}
inline void HeapSimulatorTrace::set_buffer_allocation_index(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  buffer_allocation_index_ = value;
  // @@protoc_insertion_point(field_set:xla.HeapSimulatorTrace.buffer_allocation_index)
}

// -------------------------------------------------------------------

// HloModuleGroupProto

// string name = 1;
inline void HloModuleGroupProto::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloModuleGroupProto::name() const {
  // @@protoc_insertion_point(field_get:xla.HloModuleGroupProto.name)
  return name_.Get();
}
inline void HloModuleGroupProto::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloModuleGroupProto.name)
}
inline void HloModuleGroupProto::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloModuleGroupProto.name)
}
inline void HloModuleGroupProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloModuleGroupProto.name)
}
inline void HloModuleGroupProto::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloModuleGroupProto.name)
}
inline std::string* HloModuleGroupProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloModuleGroupProto.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloModuleGroupProto::release_name() {
  // @@protoc_insertion_point(field_release:xla.HloModuleGroupProto.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloModuleGroupProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloModuleGroupProto.name)
}
inline std::string* HloModuleGroupProto::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloModuleGroupProto.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloModuleGroupProto::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloModuleGroupProto.name)
}

// repeated .xla.HloModuleProto hlo_modules = 2;
inline int HloModuleGroupProto::hlo_modules_size() const {
  return hlo_modules_.size();
}
inline void HloModuleGroupProto::clear_hlo_modules() {
  hlo_modules_.Clear();
}
inline ::xla::HloModuleProto* HloModuleGroupProto::mutable_hlo_modules(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloModuleGroupProto.hlo_modules)
  return hlo_modules_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloModuleProto >*
HloModuleGroupProto::mutable_hlo_modules() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloModuleGroupProto.hlo_modules)
  return &hlo_modules_;
}
inline const ::xla::HloModuleProto& HloModuleGroupProto::hlo_modules(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloModuleGroupProto.hlo_modules)
  return hlo_modules_.Get(index);
}
inline ::xla::HloModuleProto* HloModuleGroupProto::add_hlo_modules() {
  // @@protoc_insertion_point(field_add:xla.HloModuleGroupProto.hlo_modules)
  return hlo_modules_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloModuleProto >&
HloModuleGroupProto::hlo_modules() const {
  // @@protoc_insertion_point(field_list:xla.HloModuleGroupProto.hlo_modules)
  return hlo_modules_;
}

// -------------------------------------------------------------------

// BufferAssignmentProto_BufferAlias

// int64 source_buffer_id = 1;
inline void BufferAssignmentProto_BufferAlias::clear_source_buffer_id() {
  source_buffer_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BufferAssignmentProto_BufferAlias::source_buffer_id() const {
  // @@protoc_insertion_point(field_get:xla.BufferAssignmentProto.BufferAlias.source_buffer_id)
  return source_buffer_id_;
}
inline void BufferAssignmentProto_BufferAlias::set_source_buffer_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  source_buffer_id_ = value;
  // @@protoc_insertion_point(field_set:xla.BufferAssignmentProto.BufferAlias.source_buffer_id)
}

// .xla.LogicalBufferProto.Location location = 2;
inline bool BufferAssignmentProto_BufferAlias::has_location() const {
  return this != internal_default_instance() && location_ != nullptr;
}
inline void BufferAssignmentProto_BufferAlias::clear_location() {
  if (GetArenaNoVirtual() == nullptr && location_ != nullptr) {
    delete location_;
  }
  location_ = nullptr;
}
inline const ::xla::LogicalBufferProto_Location& BufferAssignmentProto_BufferAlias::location() const {
  const ::xla::LogicalBufferProto_Location* p = location_;
  // @@protoc_insertion_point(field_get:xla.BufferAssignmentProto.BufferAlias.location)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LogicalBufferProto_Location*>(
      &::xla::_LogicalBufferProto_Location_default_instance_);
}
inline ::xla::LogicalBufferProto_Location* BufferAssignmentProto_BufferAlias::release_location() {
  // @@protoc_insertion_point(field_release:xla.BufferAssignmentProto.BufferAlias.location)
  
  ::xla::LogicalBufferProto_Location* temp = location_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  location_ = nullptr;
  return temp;
}
inline ::xla::LogicalBufferProto_Location* BufferAssignmentProto_BufferAlias::unsafe_arena_release_location() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.BufferAssignmentProto.BufferAlias.location)
  
  ::xla::LogicalBufferProto_Location* temp = location_;
  location_ = nullptr;
  return temp;
}
inline ::xla::LogicalBufferProto_Location* BufferAssignmentProto_BufferAlias::mutable_location() {
  
  if (location_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LogicalBufferProto_Location>(GetArenaNoVirtual());
    location_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.BufferAssignmentProto.BufferAlias.location)
  return location_;
}
inline void BufferAssignmentProto_BufferAlias::set_allocated_location(::xla::LogicalBufferProto_Location* location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete location_;
  }
  if (location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(location);
    if (message_arena != submessage_arena) {
      location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, location, submessage_arena);
    }
    
  } else {
    
  }
  location_ = location;
  // @@protoc_insertion_point(field_set_allocated:xla.BufferAssignmentProto.BufferAlias.location)
}

// -------------------------------------------------------------------

// BufferAssignmentProto

// repeated .xla.LogicalBufferProto logical_buffers = 1;
inline int BufferAssignmentProto::logical_buffers_size() const {
  return logical_buffers_.size();
}
inline void BufferAssignmentProto::clear_logical_buffers() {
  logical_buffers_.Clear();
}
inline ::xla::LogicalBufferProto* BufferAssignmentProto::mutable_logical_buffers(int index) {
  // @@protoc_insertion_point(field_mutable:xla.BufferAssignmentProto.logical_buffers)
  return logical_buffers_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LogicalBufferProto >*
BufferAssignmentProto::mutable_logical_buffers() {
  // @@protoc_insertion_point(field_mutable_list:xla.BufferAssignmentProto.logical_buffers)
  return &logical_buffers_;
}
inline const ::xla::LogicalBufferProto& BufferAssignmentProto::logical_buffers(int index) const {
  // @@protoc_insertion_point(field_get:xla.BufferAssignmentProto.logical_buffers)
  return logical_buffers_.Get(index);
}
inline ::xla::LogicalBufferProto* BufferAssignmentProto::add_logical_buffers() {
  // @@protoc_insertion_point(field_add:xla.BufferAssignmentProto.logical_buffers)
  return logical_buffers_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LogicalBufferProto >&
BufferAssignmentProto::logical_buffers() const {
  // @@protoc_insertion_point(field_list:xla.BufferAssignmentProto.logical_buffers)
  return logical_buffers_;
}

// repeated .xla.BufferAssignmentProto.BufferAlias buffer_aliases = 2;
inline int BufferAssignmentProto::buffer_aliases_size() const {
  return buffer_aliases_.size();
}
inline void BufferAssignmentProto::clear_buffer_aliases() {
  buffer_aliases_.Clear();
}
inline ::xla::BufferAssignmentProto_BufferAlias* BufferAssignmentProto::mutable_buffer_aliases(int index) {
  // @@protoc_insertion_point(field_mutable:xla.BufferAssignmentProto.buffer_aliases)
  return buffer_aliases_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAssignmentProto_BufferAlias >*
BufferAssignmentProto::mutable_buffer_aliases() {
  // @@protoc_insertion_point(field_mutable_list:xla.BufferAssignmentProto.buffer_aliases)
  return &buffer_aliases_;
}
inline const ::xla::BufferAssignmentProto_BufferAlias& BufferAssignmentProto::buffer_aliases(int index) const {
  // @@protoc_insertion_point(field_get:xla.BufferAssignmentProto.buffer_aliases)
  return buffer_aliases_.Get(index);
}
inline ::xla::BufferAssignmentProto_BufferAlias* BufferAssignmentProto::add_buffer_aliases() {
  // @@protoc_insertion_point(field_add:xla.BufferAssignmentProto.buffer_aliases)
  return buffer_aliases_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAssignmentProto_BufferAlias >&
BufferAssignmentProto::buffer_aliases() const {
  // @@protoc_insertion_point(field_list:xla.BufferAssignmentProto.buffer_aliases)
  return buffer_aliases_;
}

// repeated .xla.BufferAllocationProto buffer_allocations = 3;
inline int BufferAssignmentProto::buffer_allocations_size() const {
  return buffer_allocations_.size();
}
inline void BufferAssignmentProto::clear_buffer_allocations() {
  buffer_allocations_.Clear();
}
inline ::xla::BufferAllocationProto* BufferAssignmentProto::mutable_buffer_allocations(int index) {
  // @@protoc_insertion_point(field_mutable:xla.BufferAssignmentProto.buffer_allocations)
  return buffer_allocations_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAllocationProto >*
BufferAssignmentProto::mutable_buffer_allocations() {
  // @@protoc_insertion_point(field_mutable_list:xla.BufferAssignmentProto.buffer_allocations)
  return &buffer_allocations_;
}
inline const ::xla::BufferAllocationProto& BufferAssignmentProto::buffer_allocations(int index) const {
  // @@protoc_insertion_point(field_get:xla.BufferAssignmentProto.buffer_allocations)
  return buffer_allocations_.Get(index);
}
inline ::xla::BufferAllocationProto* BufferAssignmentProto::add_buffer_allocations() {
  // @@protoc_insertion_point(field_add:xla.BufferAssignmentProto.buffer_allocations)
  return buffer_allocations_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::BufferAllocationProto >&
BufferAssignmentProto::buffer_allocations() const {
  // @@protoc_insertion_point(field_list:xla.BufferAssignmentProto.buffer_allocations)
  return buffer_allocations_;
}

// repeated .xla.HeapSimulatorTrace heap_simulator_traces = 4;
inline int BufferAssignmentProto::heap_simulator_traces_size() const {
  return heap_simulator_traces_.size();
}
inline void BufferAssignmentProto::clear_heap_simulator_traces() {
  heap_simulator_traces_.Clear();
}
inline ::xla::HeapSimulatorTrace* BufferAssignmentProto::mutable_heap_simulator_traces(int index) {
  // @@protoc_insertion_point(field_mutable:xla.BufferAssignmentProto.heap_simulator_traces)
  return heap_simulator_traces_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HeapSimulatorTrace >*
BufferAssignmentProto::mutable_heap_simulator_traces() {
  // @@protoc_insertion_point(field_mutable_list:xla.BufferAssignmentProto.heap_simulator_traces)
  return &heap_simulator_traces_;
}
inline const ::xla::HeapSimulatorTrace& BufferAssignmentProto::heap_simulator_traces(int index) const {
  // @@protoc_insertion_point(field_get:xla.BufferAssignmentProto.heap_simulator_traces)
  return heap_simulator_traces_.Get(index);
}
inline ::xla::HeapSimulatorTrace* BufferAssignmentProto::add_heap_simulator_traces() {
  // @@protoc_insertion_point(field_add:xla.BufferAssignmentProto.heap_simulator_traces)
  return heap_simulator_traces_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HeapSimulatorTrace >&
BufferAssignmentProto::heap_simulator_traces() const {
  // @@protoc_insertion_point(field_list:xla.BufferAssignmentProto.heap_simulator_traces)
  return heap_simulator_traces_;
}

// -------------------------------------------------------------------

// HloProto

// .xla.HloModuleProto hlo_module = 1;
inline bool HloProto::has_hlo_module() const {
  return this != internal_default_instance() && hlo_module_ != nullptr;
}
inline void HloProto::clear_hlo_module() {
  if (GetArenaNoVirtual() == nullptr && hlo_module_ != nullptr) {
    delete hlo_module_;
  }
  hlo_module_ = nullptr;
}
inline const ::xla::HloModuleProto& HloProto::hlo_module() const {
  const ::xla::HloModuleProto* p = hlo_module_;
  // @@protoc_insertion_point(field_get:xla.HloProto.hlo_module)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloModuleProto*>(
      &::xla::_HloModuleProto_default_instance_);
}
inline ::xla::HloModuleProto* HloProto::release_hlo_module() {
  // @@protoc_insertion_point(field_release:xla.HloProto.hlo_module)
  
  ::xla::HloModuleProto* temp = hlo_module_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  hlo_module_ = nullptr;
  return temp;
}
inline ::xla::HloModuleProto* HloProto::unsafe_arena_release_hlo_module() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloProto.hlo_module)
  
  ::xla::HloModuleProto* temp = hlo_module_;
  hlo_module_ = nullptr;
  return temp;
}
inline ::xla::HloModuleProto* HloProto::mutable_hlo_module() {
  
  if (hlo_module_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloModuleProto>(GetArenaNoVirtual());
    hlo_module_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloProto.hlo_module)
  return hlo_module_;
}
inline void HloProto::set_allocated_hlo_module(::xla::HloModuleProto* hlo_module) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete hlo_module_;
  }
  if (hlo_module) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(hlo_module);
    if (message_arena != submessage_arena) {
      hlo_module = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hlo_module, submessage_arena);
    }
    
  } else {
    
  }
  hlo_module_ = hlo_module;
  // @@protoc_insertion_point(field_set_allocated:xla.HloProto.hlo_module)
}

// .xla.BufferAssignmentProto buffer_assignment = 3;
inline bool HloProto::has_buffer_assignment() const {
  return this != internal_default_instance() && buffer_assignment_ != nullptr;
}
inline void HloProto::clear_buffer_assignment() {
  if (GetArenaNoVirtual() == nullptr && buffer_assignment_ != nullptr) {
    delete buffer_assignment_;
  }
  buffer_assignment_ = nullptr;
}
inline const ::xla::BufferAssignmentProto& HloProto::buffer_assignment() const {
  const ::xla::BufferAssignmentProto* p = buffer_assignment_;
  // @@protoc_insertion_point(field_get:xla.HloProto.buffer_assignment)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::BufferAssignmentProto*>(
      &::xla::_BufferAssignmentProto_default_instance_);
}
inline ::xla::BufferAssignmentProto* HloProto::release_buffer_assignment() {
  // @@protoc_insertion_point(field_release:xla.HloProto.buffer_assignment)
  
  ::xla::BufferAssignmentProto* temp = buffer_assignment_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  buffer_assignment_ = nullptr;
  return temp;
}
inline ::xla::BufferAssignmentProto* HloProto::unsafe_arena_release_buffer_assignment() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloProto.buffer_assignment)
  
  ::xla::BufferAssignmentProto* temp = buffer_assignment_;
  buffer_assignment_ = nullptr;
  return temp;
}
inline ::xla::BufferAssignmentProto* HloProto::mutable_buffer_assignment() {
  
  if (buffer_assignment_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::BufferAssignmentProto>(GetArenaNoVirtual());
    buffer_assignment_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloProto.buffer_assignment)
  return buffer_assignment_;
}
inline void HloProto::set_allocated_buffer_assignment(::xla::BufferAssignmentProto* buffer_assignment) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete buffer_assignment_;
  }
  if (buffer_assignment) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(buffer_assignment);
    if (message_arena != submessage_arena) {
      buffer_assignment = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, buffer_assignment, submessage_arena);
    }
    
  } else {
    
  }
  buffer_assignment_ = buffer_assignment;
  // @@protoc_insertion_point(field_set_allocated:xla.HloProto.buffer_assignment)
}

// -------------------------------------------------------------------

// HloSnapshot

// .xla.HloProto hlo = 1;
inline bool HloSnapshot::has_hlo() const {
  return this != internal_default_instance() && hlo_ != nullptr;
}
inline void HloSnapshot::clear_hlo() {
  if (GetArenaNoVirtual() == nullptr && hlo_ != nullptr) {
    delete hlo_;
  }
  hlo_ = nullptr;
}
inline const ::xla::HloProto& HloSnapshot::hlo() const {
  const ::xla::HloProto* p = hlo_;
  // @@protoc_insertion_point(field_get:xla.HloSnapshot.hlo)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloProto*>(
      &::xla::_HloProto_default_instance_);
}
inline ::xla::HloProto* HloSnapshot::release_hlo() {
  // @@protoc_insertion_point(field_release:xla.HloSnapshot.hlo)
  
  ::xla::HloProto* temp = hlo_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  hlo_ = nullptr;
  return temp;
}
inline ::xla::HloProto* HloSnapshot::unsafe_arena_release_hlo() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloSnapshot.hlo)
  
  ::xla::HloProto* temp = hlo_;
  hlo_ = nullptr;
  return temp;
}
inline ::xla::HloProto* HloSnapshot::mutable_hlo() {
  
  if (hlo_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloProto>(GetArenaNoVirtual());
    hlo_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloSnapshot.hlo)
  return hlo_;
}
inline void HloSnapshot::set_allocated_hlo(::xla::HloProto* hlo) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete hlo_;
  }
  if (hlo) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(hlo);
    if (message_arena != submessage_arena) {
      hlo = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hlo, submessage_arena);
    }
    
  } else {
    
  }
  hlo_ = hlo;
  // @@protoc_insertion_point(field_set_allocated:xla.HloSnapshot.hlo)
}

// repeated .xla.LiteralProto arguments = 2;
inline int HloSnapshot::arguments_size() const {
  return arguments_.size();
}
inline ::xla::LiteralProto* HloSnapshot::mutable_arguments(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloSnapshot.arguments)
  return arguments_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LiteralProto >*
HloSnapshot::mutable_arguments() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloSnapshot.arguments)
  return &arguments_;
}
inline const ::xla::LiteralProto& HloSnapshot::arguments(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloSnapshot.arguments)
  return arguments_.Get(index);
}
inline ::xla::LiteralProto* HloSnapshot::add_arguments() {
  // @@protoc_insertion_point(field_add:xla.HloSnapshot.arguments)
  return arguments_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LiteralProto >&
HloSnapshot::arguments() const {
  // @@protoc_insertion_point(field_list:xla.HloSnapshot.arguments)
  return arguments_;
}

// .xla.LiteralProto result = 3;
inline bool HloSnapshot::has_result() const {
  return this != internal_default_instance() && result_ != nullptr;
}
inline const ::xla::LiteralProto& HloSnapshot::result() const {
  const ::xla::LiteralProto* p = result_;
  // @@protoc_insertion_point(field_get:xla.HloSnapshot.result)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LiteralProto*>(
      &::xla::_LiteralProto_default_instance_);
}
inline ::xla::LiteralProto* HloSnapshot::release_result() {
  // @@protoc_insertion_point(field_release:xla.HloSnapshot.result)
  
  ::xla::LiteralProto* temp = result_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  result_ = nullptr;
  return temp;
}
inline ::xla::LiteralProto* HloSnapshot::unsafe_arena_release_result() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloSnapshot.result)
  
  ::xla::LiteralProto* temp = result_;
  result_ = nullptr;
  return temp;
}
inline ::xla::LiteralProto* HloSnapshot::mutable_result() {
  
  if (result_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LiteralProto>(GetArenaNoVirtual());
    result_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.HloSnapshot.result)
  return result_;
}
inline void HloSnapshot::set_allocated_result(::xla::LiteralProto* result) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(result_);
  }
  if (result) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(result)->GetArena();
    if (message_arena != submessage_arena) {
      result = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, result, submessage_arena);
    }
    
  } else {
    
  }
  result_ = result;
  // @@protoc_insertion_point(field_set_allocated:xla.HloSnapshot.result)
}

// string execution_platform = 4;
inline void HloSnapshot::clear_execution_platform() {
  execution_platform_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloSnapshot::execution_platform() const {
  // @@protoc_insertion_point(field_get:xla.HloSnapshot.execution_platform)
  return execution_platform_.Get();
}
inline void HloSnapshot::set_execution_platform(const std::string& value) {
  
  execution_platform_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloSnapshot.execution_platform)
}
inline void HloSnapshot::set_execution_platform(std::string&& value) {
  
  execution_platform_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloSnapshot.execution_platform)
}
inline void HloSnapshot::set_execution_platform(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  execution_platform_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloSnapshot.execution_platform)
}
inline void HloSnapshot::set_execution_platform(const char* value,
    size_t size) {
  
  execution_platform_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloSnapshot.execution_platform)
}
inline std::string* HloSnapshot::mutable_execution_platform() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloSnapshot.execution_platform)
  return execution_platform_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloSnapshot::release_execution_platform() {
  // @@protoc_insertion_point(field_release:xla.HloSnapshot.execution_platform)
  
  return execution_platform_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloSnapshot::set_allocated_execution_platform(std::string* execution_platform) {
  if (execution_platform != nullptr) {
    
  } else {
    
  }
  execution_platform_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), execution_platform,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloSnapshot.execution_platform)
}
inline std::string* HloSnapshot::unsafe_arena_release_execution_platform() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloSnapshot.execution_platform)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return execution_platform_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloSnapshot::unsafe_arena_set_allocated_execution_platform(
    std::string* execution_platform) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (execution_platform != nullptr) {
    
  } else {
    
  }
  execution_platform_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      execution_platform, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloSnapshot.execution_platform)
}

// -------------------------------------------------------------------

// HloModuleMetadataProto

// int64 canonical_module_id = 1;
inline void HloModuleMetadataProto::clear_canonical_module_id() {
  canonical_module_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloModuleMetadataProto::canonical_module_id() const {
  // @@protoc_insertion_point(field_get:xla.HloModuleMetadataProto.canonical_module_id)
  return canonical_module_id_;
}
inline void HloModuleMetadataProto::set_canonical_module_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  canonical_module_id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloModuleMetadataProto.canonical_module_id)
}

// string module_group_name = 2;
inline void HloModuleMetadataProto::clear_module_group_name() {
  module_group_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloModuleMetadataProto::module_group_name() const {
  // @@protoc_insertion_point(field_get:xla.HloModuleMetadataProto.module_group_name)
  return module_group_name_.Get();
}
inline void HloModuleMetadataProto::set_module_group_name(const std::string& value) {
  
  module_group_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloModuleMetadataProto.module_group_name)
}
inline void HloModuleMetadataProto::set_module_group_name(std::string&& value) {
  
  module_group_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloModuleMetadataProto.module_group_name)
}
inline void HloModuleMetadataProto::set_module_group_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  module_group_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloModuleMetadataProto.module_group_name)
}
inline void HloModuleMetadataProto::set_module_group_name(const char* value,
    size_t size) {
  
  module_group_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloModuleMetadataProto.module_group_name)
}
inline std::string* HloModuleMetadataProto::mutable_module_group_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloModuleMetadataProto.module_group_name)
  return module_group_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloModuleMetadataProto::release_module_group_name() {
  // @@protoc_insertion_point(field_release:xla.HloModuleMetadataProto.module_group_name)
  
  return module_group_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloModuleMetadataProto::set_allocated_module_group_name(std::string* module_group_name) {
  if (module_group_name != nullptr) {
    
  } else {
    
  }
  module_group_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), module_group_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloModuleMetadataProto.module_group_name)
}
inline std::string* HloModuleMetadataProto::unsafe_arena_release_module_group_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloModuleMetadataProto.module_group_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return module_group_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloModuleMetadataProto::unsafe_arena_set_allocated_module_group_name(
    std::string* module_group_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (module_group_name != nullptr) {
    
  } else {
    
  }
  module_group_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      module_group_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloModuleMetadataProto.module_group_name)
}

// int64 original_module_id = 3;
inline void HloModuleMetadataProto::clear_original_module_id() {
  original_module_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloModuleMetadataProto::original_module_id() const {
  // @@protoc_insertion_point(field_get:xla.HloModuleMetadataProto.original_module_id)
  return original_module_id_;
}
inline void HloModuleMetadataProto::set_original_module_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  original_module_id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloModuleMetadataProto.original_module_id)
}

// repeated int64 partitioned_module_ids = 4;
inline int HloModuleMetadataProto::partitioned_module_ids_size() const {
  return partitioned_module_ids_.size();
}
inline void HloModuleMetadataProto::clear_partitioned_module_ids() {
  partitioned_module_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloModuleMetadataProto::partitioned_module_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloModuleMetadataProto.partitioned_module_ids)
  return partitioned_module_ids_.Get(index);
}
inline void HloModuleMetadataProto::set_partitioned_module_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  partitioned_module_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloModuleMetadataProto.partitioned_module_ids)
}
inline void HloModuleMetadataProto::add_partitioned_module_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  partitioned_module_ids_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloModuleMetadataProto.partitioned_module_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloModuleMetadataProto::partitioned_module_ids() const {
  // @@protoc_insertion_point(field_list:xla.HloModuleMetadataProto.partitioned_module_ids)
  return partitioned_module_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloModuleMetadataProto::mutable_partitioned_module_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloModuleMetadataProto.partitioned_module_ids)
  return &partitioned_module_ids_;
}

// repeated .xla.HloPassMetadata pass_metadata = 5;
inline int HloModuleMetadataProto::pass_metadata_size() const {
  return pass_metadata_.size();
}
inline void HloModuleMetadataProto::clear_pass_metadata() {
  pass_metadata_.Clear();
}
inline ::xla::HloPassMetadata* HloModuleMetadataProto::mutable_pass_metadata(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloModuleMetadataProto.pass_metadata)
  return pass_metadata_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloPassMetadata >*
HloModuleMetadataProto::mutable_pass_metadata() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloModuleMetadataProto.pass_metadata)
  return &pass_metadata_;
}
inline const ::xla::HloPassMetadata& HloModuleMetadataProto::pass_metadata(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloModuleMetadataProto.pass_metadata)
  return pass_metadata_.Get(index);
}
inline ::xla::HloPassMetadata* HloModuleMetadataProto::add_pass_metadata() {
  // @@protoc_insertion_point(field_add:xla.HloModuleMetadataProto.pass_metadata)
  return pass_metadata_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloPassMetadata >&
HloModuleMetadataProto::pass_metadata() const {
  // @@protoc_insertion_point(field_list:xla.HloModuleMetadataProto.pass_metadata)
  return pass_metadata_;
}

// -------------------------------------------------------------------

// HloPassMetadata

// int64 pass_id = 1;
inline void HloPassMetadata::clear_pass_id() {
  pass_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloPassMetadata::pass_id() const {
  // @@protoc_insertion_point(field_get:xla.HloPassMetadata.pass_id)
  return pass_id_;
}
inline void HloPassMetadata::set_pass_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  pass_id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloPassMetadata.pass_id)
}

// string pass_name = 2;
inline void HloPassMetadata::clear_pass_name() {
  pass_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloPassMetadata::pass_name() const {
  // @@protoc_insertion_point(field_get:xla.HloPassMetadata.pass_name)
  return pass_name_.Get();
}
inline void HloPassMetadata::set_pass_name(const std::string& value) {
  
  pass_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloPassMetadata.pass_name)
}
inline void HloPassMetadata::set_pass_name(std::string&& value) {
  
  pass_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloPassMetadata.pass_name)
}
inline void HloPassMetadata::set_pass_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  pass_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloPassMetadata.pass_name)
}
inline void HloPassMetadata::set_pass_name(const char* value,
    size_t size) {
  
  pass_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloPassMetadata.pass_name)
}
inline std::string* HloPassMetadata::mutable_pass_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloPassMetadata.pass_name)
  return pass_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloPassMetadata::release_pass_name() {
  // @@protoc_insertion_point(field_release:xla.HloPassMetadata.pass_name)
  
  return pass_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloPassMetadata::set_allocated_pass_name(std::string* pass_name) {
  if (pass_name != nullptr) {
    
  } else {
    
  }
  pass_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), pass_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloPassMetadata.pass_name)
}
inline std::string* HloPassMetadata::unsafe_arena_release_pass_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloPassMetadata.pass_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return pass_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloPassMetadata::unsafe_arena_set_allocated_pass_name(
    std::string* pass_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (pass_name != nullptr) {
    
  } else {
    
  }
  pass_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      pass_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloPassMetadata.pass_name)
}

// string pipeline_name = 3;
inline void HloPassMetadata::clear_pipeline_name() {
  pipeline_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& HloPassMetadata::pipeline_name() const {
  // @@protoc_insertion_point(field_get:xla.HloPassMetadata.pipeline_name)
  return pipeline_name_.Get();
}
inline void HloPassMetadata::set_pipeline_name(const std::string& value) {
  
  pipeline_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:xla.HloPassMetadata.pipeline_name)
}
inline void HloPassMetadata::set_pipeline_name(std::string&& value) {
  
  pipeline_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:xla.HloPassMetadata.pipeline_name)
}
inline void HloPassMetadata::set_pipeline_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  pipeline_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:xla.HloPassMetadata.pipeline_name)
}
inline void HloPassMetadata::set_pipeline_name(const char* value,
    size_t size) {
  
  pipeline_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:xla.HloPassMetadata.pipeline_name)
}
inline std::string* HloPassMetadata::mutable_pipeline_name() {
  
  // @@protoc_insertion_point(field_mutable:xla.HloPassMetadata.pipeline_name)
  return pipeline_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* HloPassMetadata::release_pipeline_name() {
  // @@protoc_insertion_point(field_release:xla.HloPassMetadata.pipeline_name)
  
  return pipeline_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void HloPassMetadata::set_allocated_pipeline_name(std::string* pipeline_name) {
  if (pipeline_name != nullptr) {
    
  } else {
    
  }
  pipeline_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), pipeline_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:xla.HloPassMetadata.pipeline_name)
}
inline std::string* HloPassMetadata::unsafe_arena_release_pipeline_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.HloPassMetadata.pipeline_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return pipeline_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void HloPassMetadata::unsafe_arena_set_allocated_pipeline_name(
    std::string* pipeline_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (pipeline_name != nullptr) {
    
  } else {
    
  }
  pipeline_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      pipeline_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloPassMetadata.pipeline_name)
}

// repeated string dump_filenames = 4;
inline int HloPassMetadata::dump_filenames_size() const {
  return dump_filenames_.size();
}
inline void HloPassMetadata::clear_dump_filenames() {
  dump_filenames_.Clear();
}
inline const std::string& HloPassMetadata::dump_filenames(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloPassMetadata.dump_filenames)
  return dump_filenames_.Get(index);
}
inline std::string* HloPassMetadata::mutable_dump_filenames(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloPassMetadata.dump_filenames)
  return dump_filenames_.Mutable(index);
}
inline void HloPassMetadata::set_dump_filenames(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:xla.HloPassMetadata.dump_filenames)
  dump_filenames_.Mutable(index)->assign(value);
}
inline void HloPassMetadata::set_dump_filenames(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:xla.HloPassMetadata.dump_filenames)
  dump_filenames_.Mutable(index)->assign(std::move(value));
}
inline void HloPassMetadata::set_dump_filenames(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  dump_filenames_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:xla.HloPassMetadata.dump_filenames)
}
inline void HloPassMetadata::set_dump_filenames(int index, const char* value, size_t size) {
  dump_filenames_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:xla.HloPassMetadata.dump_filenames)
}
inline std::string* HloPassMetadata::add_dump_filenames() {
  // @@protoc_insertion_point(field_add_mutable:xla.HloPassMetadata.dump_filenames)
  return dump_filenames_.Add();
}
inline void HloPassMetadata::add_dump_filenames(const std::string& value) {
  dump_filenames_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:xla.HloPassMetadata.dump_filenames)
}
inline void HloPassMetadata::add_dump_filenames(std::string&& value) {
  dump_filenames_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:xla.HloPassMetadata.dump_filenames)
}
inline void HloPassMetadata::add_dump_filenames(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  dump_filenames_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:xla.HloPassMetadata.dump_filenames)
}
inline void HloPassMetadata::add_dump_filenames(const char* value, size_t size) {
  dump_filenames_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:xla.HloPassMetadata.dump_filenames)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
HloPassMetadata::dump_filenames() const {
  // @@protoc_insertion_point(field_list:xla.HloPassMetadata.dump_filenames)
  return dump_filenames_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
HloPassMetadata::mutable_dump_filenames() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloPassMetadata.dump_filenames)
  return &dump_filenames_;
}

// bool module_changed = 5;
inline void HloPassMetadata::clear_module_changed() {
  module_changed_ = false;
}
inline bool HloPassMetadata::module_changed() const {
  // @@protoc_insertion_point(field_get:xla.HloPassMetadata.module_changed)
  return module_changed_;
}
inline void HloPassMetadata::set_module_changed(bool value) {
  
  module_changed_ = value;
  // @@protoc_insertion_point(field_set:xla.HloPassMetadata.module_changed)
}

// int64 module_id = 6;
inline void HloPassMetadata::clear_module_id() {
  module_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloPassMetadata::module_id() const {
  // @@protoc_insertion_point(field_get:xla.HloPassMetadata.module_id)
  return module_id_;
}
inline void HloPassMetadata::set_module_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  module_id_ = value;
  // @@protoc_insertion_point(field_set:xla.HloPassMetadata.module_id)
}

// repeated int64 module_group_module_ids = 7;
inline int HloPassMetadata::module_group_module_ids_size() const {
  return module_group_module_ids_.size();
}
inline void HloPassMetadata::clear_module_group_module_ids() {
  module_group_module_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloPassMetadata::module_group_module_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloPassMetadata.module_group_module_ids)
  return module_group_module_ids_.Get(index);
}
inline void HloPassMetadata::set_module_group_module_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  module_group_module_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloPassMetadata.module_group_module_ids)
}
inline void HloPassMetadata::add_module_group_module_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  module_group_module_ids_.Add(value);
  // @@protoc_insertion_point(field_add:xla.HloPassMetadata.module_group_module_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
HloPassMetadata::module_group_module_ids() const {
  // @@protoc_insertion_point(field_list:xla.HloPassMetadata.module_group_module_ids)
  return module_group_module_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
HloPassMetadata::mutable_module_group_module_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloPassMetadata.module_group_module_ids)
  return &module_group_module_ids_;
}

// int64 start_timestamp_usec = 8;
inline void HloPassMetadata::clear_start_timestamp_usec() {
  start_timestamp_usec_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloPassMetadata::start_timestamp_usec() const {
  // @@protoc_insertion_point(field_get:xla.HloPassMetadata.start_timestamp_usec)
  return start_timestamp_usec_;
}
inline void HloPassMetadata::set_start_timestamp_usec(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  start_timestamp_usec_ = value;
  // @@protoc_insertion_point(field_set:xla.HloPassMetadata.start_timestamp_usec)
}

// int64 end_timestamp_usec = 9;
inline void HloPassMetadata::clear_end_timestamp_usec() {
  end_timestamp_usec_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 HloPassMetadata::end_timestamp_usec() const {
  // @@protoc_insertion_point(field_get:xla.HloPassMetadata.end_timestamp_usec)
  return end_timestamp_usec_;
}
inline void HloPassMetadata::set_end_timestamp_usec(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  end_timestamp_usec_ = value;
  // @@protoc_insertion_point(field_set:xla.HloPassMetadata.end_timestamp_usec)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::xla::HeapSimulatorTrace_Event_Kind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::HeapSimulatorTrace_Event_Kind>() {
  return ::xla::HeapSimulatorTrace_Event_Kind_descriptor();
}
template <> struct is_proto_enum< ::xla::CustomCallSchedule> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::CustomCallSchedule>() {
  return ::xla::CustomCallSchedule_descriptor();
}
template <> struct is_proto_enum< ::xla::Kind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::Kind>() {
  return ::xla::Kind_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fservice_2fhlo_2eproto
