import numpy as np
import traceback
from apps.utils.logger_helper import <PERSON><PERSON>


def detect_af_episodes(rr_intervals, r_peaks, sampling_rate, min_duration_sec=3, rr_std_thresh_factor=1.2,
                       window_size=8):
    """
    基于RR间期标准差检测房颤/房扑发作 。
    :param rr_intervals: RR间期列表 (秒)
    :param r_peaks: R峰索引列表
    :param sampling_rate: 采样率 (Hz)
    :param min_duration_sec: AF发作最小持续时间 (秒) - 降低为3秒，提高灵敏度
    :param rr_std_thresh_factor: RR间期标准差阈值因子 (乘以中位数RR的标准差) - 1.2
    :param window_size: 计算标准差的滑动窗口大小 - 8
    :return: AF发作列表 [{'start_time': float, 'end_time': float, 'duration': float, 'longest_rr': float}]
    """
    af_episodes = []
    if rr_intervals is None or len(rr_intervals) < window_size:
        return af_episodes

    try:

        rr_diffs = np.abs(np.diff(rr_intervals))
        n_pairs = len(rr_diffs)
        if n_pairs > 0:
            n50 = sum(diff > 0.050 for diff in rr_diffs)
            pnn50 = (n50 / n_pairs) * 100
            rmssd = np.sqrt(np.mean(np.square(rr_diffs)))

            high_af_probability = pnn50 > 40 and rmssd * 1000 > 100

            if high_af_probability:
                if r_peaks is not None and len(r_peaks) > 2:
                    start_time = r_peaks[0] / sampling_rate
                    end_time = r_peaks[-1] / sampling_rate
                    duration = end_time - start_time
                    if duration >= min_duration_sec:
                        longest_rr = np.max(rr_intervals) if len(rr_intervals) > 0 else 0
                        af_episodes.append({
                            'start_time': start_time,
                            'end_time': end_time,
                            'duration': duration,
                            'longest_rr': longest_rr,
                            'detection_method': 'hrv_metrics',
                            'episode_rrs': rr_intervals.copy()  # 存储发作期间的RR间期
                        })

        rr_diffs = np.abs(np.diff(rr_intervals))
        if len(rr_diffs) < window_size:
            return af_episodes

        rolling_std = np.sqrt(np.convolve(rr_diffs ** 2, np.ones(window_size) / window_size, mode='valid'))
        if len(rolling_std) == 0: return af_episodes

        median_rr_diff_std = np.median(rolling_std)
        threshold = median_rr_diff_std * rr_std_thresh_factor

        is_potential_af_beat = np.zeros(len(rr_intervals) + 1, dtype=bool)

        for i in range(len(rr_intervals) - window_size + 1):
            window_rr = rr_intervals[i: i + window_size]
            window_mean = np.mean(window_rr)
            window_std = np.std(window_rr)

            if window_std > threshold:
                for k in range(i, i + window_size + 1):
                    if k < len(is_potential_af_beat):
                        is_potential_af_beat[k] = True

        af_indices = np.where(is_potential_af_beat)[0]
        if not len(af_indices):
            return af_episodes

        start_idx = -1
        current_episode_rrs = []

        for i in range(len(r_peaks)):
            is_af_beat = is_potential_af_beat[i]

            if is_af_beat and start_idx == -1:
                start_idx = i
                current_episode_rrs = []
                if i < len(rr_intervals):
                    current_episode_rrs.append(rr_intervals[i])

            elif is_af_beat and start_idx != -1:
                if i < len(rr_intervals):
                    current_episode_rrs.append(rr_intervals[i])

            elif not is_af_beat and start_idx != -1:
                end_idx = i - 1
                if start_idx <= end_idx:
                    start_time = r_peaks[start_idx] / sampling_rate
                    end_time = r_peaks[end_idx + 1] / sampling_rate if end_idx + 1 < len(r_peaks) else r_peaks[
                                                                                                           end_idx] / sampling_rate + np.mean(
                        current_episode_rrs) if current_episode_rrs else 0  # 添加检查

                    duration = end_time - start_time

                    if duration >= min_duration_sec:
                        longest_rr = np.max(current_episode_rrs) if current_episode_rrs else 0
                        existing_hrv_episode = next(
                            (ep for ep in af_episodes if ep.get('detection_method') == 'hrv_metrics'), None)
                        if existing_hrv_episode and (
                                start_time >= existing_hrv_episode['start_time'] and end_time <= existing_hrv_episode[
                            'end_time']):
                            pass
                        else:
                            af_episodes.append({
                                'start_time': start_time,
                                'end_time': end_time,
                                'duration': duration,
                                'longest_rr': longest_rr,
                                'detection_method': 'rr_variability',
                                'episode_rrs': current_episode_rrs.copy()  # 存储发作期间的RR间期
                            })
                start_idx = -1
                current_episode_rrs = []

        if start_idx != -1:
            end_idx = len(r_peaks) - 1
            if start_idx <= end_idx:
                start_time = r_peaks[start_idx] / sampling_rate
                end_time = r_peaks[end_idx] / sampling_rate
                if end_idx < len(rr_intervals):
                    end_time += rr_intervals[end_idx]

                duration = end_time - start_time

                if duration >= min_duration_sec:
                    longest_rr = np.max(current_episode_rrs) if current_episode_rrs else 0
                    existing_hrv_episode = next(
                        (ep for ep in af_episodes if ep.get('detection_method') == 'hrv_metrics'), None)
                    if existing_hrv_episode and (
                            start_time >= existing_hrv_episode['start_time'] and end_time <= existing_hrv_episode[
                        'end_time']):
                        pass
                    else:
                        af_episodes.append({
                            'start_time': start_time,
                            'end_time': end_time,
                            'duration': duration,
                            'longest_rr': longest_rr,
                            'detection_method': 'rr_variability',
                            'episode_rrs': current_episode_rrs.copy()  # 存储发作期间的RR间期
                        })

    except Exception as e:
        Logger().error(f"Error in detect_af_episodes: {str(e)}\n{traceback.format_exc()}")  # 保留 Error

    return af_episodes


def quantify_af(af_episodes, total_duration_sec, hrv_data=None):
    """
    量化房颤/房扑指标
    :param af_episodes: 房颤发作列表
    :param total_duration_sec: 总记录时长(秒)
    :param hrv_data: 可选的 HRV 指标数据
    :return: 房颤量化指标字典
    """
    if total_duration_sec is None:
        total_duration_sec = 10.0  # 默认 10 秒

    # 初始化量化结果
    quantification = {
        'total_duration': 0,
        'percentage': 0,
        'longest_episode_duration': 0,
        'longest_rr_during_af': 0,
        'af_probability': 0.0,  # 房颤可能性估计
        'total_episodes': 0,  # 总阵数
        'fastest_hr': 0  # 最快心率
    }

    # 如果没有检测到发作但提供了 HRV 数据，尝试从 HRV 指标估计房颤可能性
    if (not af_episodes or len(af_episodes) == 0) and hrv_data is not None:
        try:
            # 从 HRV 数据中提取相关指标
            pnn50 = hrv_data.get('linear', {}).get('pnn50', 0)
            rmssd = hrv_data.get('linear', {}).get('rmssd', 0)
            sdnn = hrv_data.get('linear', {}).get('sdnn', 0)

            # 估计房颤可能性
            af_prob_pnn = min(1.0, pnn50 / 50.0) if pnn50 > 0 else 0  # pNN50 > 50% 时概率为 1
            af_prob_rmssd = min(1.0, rmssd / 150.0) if rmssd > 0 else 0  # RMSSD > 150ms 时概率为 1
            af_prob_sdnn = min(1.0, sdnn / 100.0) if sdnn > 0 else 0  # SDNN > 100ms 时概率为 1

            af_probability = 0.4 * af_prob_pnn + 0.4 * af_prob_rmssd + 0.2 * af_prob_sdnn

            quantification['af_probability'] = af_probability

            if af_probability > 0.5:
                quantification['total_duration'] = total_duration_sec * af_probability
                quantification['percentage'] = af_probability * 100
                quantification['longest_episode_duration'] = total_duration_sec * af_probability
                quantification['total_episodes'] = 1
            else:
                quantification['total_episodes'] = 0

            return quantification
        except Exception as e:
            Logger().error(f"从 HRV 指标估计房颤可能性时出错: {str(e)}")

            # 如果存在检测到的发作，正常计算量化指标
    if not af_episodes:
        return quantification

    try:
        total_af_duration = sum(ep['duration'] for ep in af_episodes)
        percentage = (total_af_duration / total_duration_sec) * 100 if total_duration_sec > 0 else 0
        longest_episode_duration = max(ep['duration'] for ep in af_episodes) if af_episodes else 0
        longest_rr_during_af = max(ep['longest_rr'] for ep in af_episodes if
                                   'longest_rr' in ep and ep['longest_rr'] is not None) if af_episodes else 0

        # 计算最快心率
        fastest_hr = None
        min_rr_during_af = float('inf')
        all_episode_rrs = []
        for ep in af_episodes:
            episode_rrs = ep.get('episode_rrs', [])
            if episode_rrs is not None and len(episode_rrs) > 0:
                all_episode_rrs.extend(episode_rrs)
                min_rr_in_episode = np.min(episode_rrs)
                if min_rr_in_episode > 0:  # 确保 RR 间隔有效
                    min_rr_during_af = min(min_rr_during_af, min_rr_in_episode)

        if min_rr_during_af != float('inf') and min_rr_during_af > 0:
            fastest_hr = 60 / min_rr_during_af
        else:
            pass

        quantification = {
            'total_duration': total_af_duration,
            'percentage': percentage,
            'longest_episode_duration': longest_episode_duration,
            'longest_rr_during_af': longest_rr_during_af,
            'af_probability': min(1.0, percentage / 100.0),  # 使用占比作为概率
            'total_episodes': len(af_episodes),  # 总阵数
            'fastest_hr': fastest_hr  # 最快心率
        }

        return quantification
    except Exception as e:
        Logger().error(f"Error in quantify_af: {str(e)}\n{traceback.format_exc()}")  # 保留 Error
        quantification['total_episodes'] = len(af_episodes)  # 至少返回阵数
        return quantification