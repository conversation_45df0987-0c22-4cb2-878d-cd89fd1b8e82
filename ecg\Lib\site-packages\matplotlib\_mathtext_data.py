"""
font data tables for truetype and afm computer modern fonts
"""

latex_to_bakoma = {
    '\\__sqrt__'                 : ('cmex10', 0x70),
    '\\bigcap'                   : ('cmex10', 0x5c),
    '\\bigcup'                   : ('cmex10', 0x5b),
    '\\bigodot'                  : ('cmex10', 0x4b),
    '\\bigoplus'                 : ('cmex10', 0x4d),
    '\\bigotimes'                : ('cmex10', 0x4f),
    '\\biguplus'                 : ('cmex10', 0x5d),
    '\\bigvee'                   : ('cmex10', 0x5f),
    '\\bigwedge'                 : ('cmex10', 0x5e),
    '\\coprod'                   : ('cmex10', 0x61),
    '\\int'                      : ('cmex10', 0x5a),
    '\\langle'                   : ('cmex10', 0xad),
    '\\leftangle'                : ('cmex10', 0xad),
    '\\leftbrace'                : ('cmex10', 0xa9),
    '\\oint'                     : ('cmex10', 0x49),
    '\\prod'                     : ('cmex10', 0x59),
    '\\rangle'                   : ('cmex10', 0xae),
    '\\rightangle'               : ('cmex10', 0xae),
    '\\rightbrace'               : ('cmex10', 0xaa),
    '\\sum'                      : ('cmex10', 0x58),
    '\\widehat'                  : ('cmex10', 0x62),
    '\\widetilde'                : ('cmex10', 0x65),
    '\\{'                        : ('cmex10', 0xa9),
    '\\}'                        : ('cmex10', 0xaa),
    '{'                          : ('cmex10', 0xa9),
    '}'                          : ('cmex10', 0xaa),

    ','                          : ('cmmi10', 0x3b),
    '.'                          : ('cmmi10', 0x3a),
    '/'                          : ('cmmi10', 0x3d),
    '<'                          : ('cmmi10', 0x3c),
    '>'                          : ('cmmi10', 0x3e),
    '\\alpha'                    : ('cmmi10', 0xae),
    '\\beta'                     : ('cmmi10', 0xaf),
    '\\chi'                      : ('cmmi10', 0xc2),
    '\\combiningrightarrowabove' : ('cmmi10', 0x7e),
    '\\delta'                    : ('cmmi10', 0xb1),
    '\\ell'                      : ('cmmi10', 0x60),
    '\\epsilon'                  : ('cmmi10', 0xb2),
    '\\eta'                      : ('cmmi10', 0xb4),
    '\\flat'                     : ('cmmi10', 0x5b),
    '\\frown'                    : ('cmmi10', 0x5f),
    '\\gamma'                    : ('cmmi10', 0xb0),
    '\\imath'                    : ('cmmi10', 0x7b),
    '\\iota'                     : ('cmmi10', 0xb6),
    '\\jmath'                    : ('cmmi10', 0x7c),
    '\\kappa'                    : ('cmmi10', 0x2219),
    '\\lambda'                   : ('cmmi10', 0xb8),
    '\\leftharpoondown'          : ('cmmi10', 0x29),
    '\\leftharpoonup'            : ('cmmi10', 0x28),
    '\\mu'                       : ('cmmi10', 0xb9),
    '\\natural'                  : ('cmmi10', 0x5c),
    '\\nu'                       : ('cmmi10', 0xba),
    '\\omega'                    : ('cmmi10', 0x21),
    '\\phi'                      : ('cmmi10', 0xc1),
    '\\pi'                       : ('cmmi10', 0xbc),
    '\\psi'                      : ('cmmi10', 0xc3),
    '\\rho'                      : ('cmmi10', 0xbd),
    '\\rightharpoondown'         : ('cmmi10', 0x2b),
    '\\rightharpoonup'           : ('cmmi10', 0x2a),
    '\\sharp'                    : ('cmmi10', 0x5d),
    '\\sigma'                    : ('cmmi10', 0xbe),
    '\\smile'                    : ('cmmi10', 0x5e),
    '\\tau'                      : ('cmmi10', 0xbf),
    '\\theta'                    : ('cmmi10', 0xb5),
    '\\triangleleft'             : ('cmmi10', 0x2f),
    '\\triangleright'            : ('cmmi10', 0x2e),
    '\\upsilon'                  : ('cmmi10', 0xc0),
    '\\varepsilon'               : ('cmmi10', 0x22),
    '\\varphi'                   : ('cmmi10', 0x27),
    '\\varrho'                   : ('cmmi10', 0x25),
    '\\varsigma'                 : ('cmmi10', 0x26),
    '\\vartheta'                 : ('cmmi10', 0x23),
    '\\wp'                       : ('cmmi10', 0x7d),
    '\\xi'                       : ('cmmi10', 0xbb),
    '\\zeta'                     : ('cmmi10', 0xb3),

    '!'                          : ('cmr10', 0x21),
    '%'                          : ('cmr10', 0x25),
    '&'                          : ('cmr10', 0x26),
    '('                          : ('cmr10', 0x28),
    ')'                          : ('cmr10', 0x29),
    '+'                          : ('cmr10', 0x2b),
    '0'                          : ('cmr10', 0x30),
    '1'                          : ('cmr10', 0x31),
    '2'                          : ('cmr10', 0x32),
    '3'                          : ('cmr10', 0x33),
    '4'                          : ('cmr10', 0x34),
    '5'                          : ('cmr10', 0x35),
    '6'                          : ('cmr10', 0x36),
    '7'                          : ('cmr10', 0x37),
    '8'                          : ('cmr10', 0x38),
    '9'                          : ('cmr10', 0x39),
    ':'                          : ('cmr10', 0x3a),
    ';'                          : ('cmr10', 0x3b),
    '='                          : ('cmr10', 0x3d),
    '?'                          : ('cmr10', 0x3f),
    '@'                          : ('cmr10', 0x40),
    '['                          : ('cmr10', 0x5b),
    '\\#'                        : ('cmr10', 0x23),
    '\\$'                        : ('cmr10', 0x24),
    '\\%'                        : ('cmr10', 0x25),
    '\\Delta'                    : ('cmr10', 0xa2),
    '\\Gamma'                    : ('cmr10', 0xa1),
    '\\Lambda'                   : ('cmr10', 0xa4),
    '\\Omega'                    : ('cmr10', 0xad),
    '\\Phi'                      : ('cmr10', 0xa9),
    '\\Pi'                       : ('cmr10', 0xa6),
    '\\Psi'                      : ('cmr10', 0xaa),
    '\\Sigma'                    : ('cmr10', 0xa7),
    '\\Theta'                    : ('cmr10', 0xa3),
    '\\Upsilon'                  : ('cmr10', 0xa8),
    '\\Xi'                       : ('cmr10', 0xa5),
    '\\circumflexaccent'         : ('cmr10', 0x5e),
    '\\combiningacuteaccent'     : ('cmr10', 0xb6),
    '\\combiningbreve'           : ('cmr10', 0xb8),
    '\\combiningdiaeresis'       : ('cmr10', 0xc4),
    '\\combiningdotabove'        : ('cmr10', 0x5f),
    '\\combininggraveaccent'     : ('cmr10', 0xb5),
    '\\combiningoverline'        : ('cmr10', 0xb9),
    '\\combiningtilde'           : ('cmr10', 0x7e),
    '\\leftbracket'              : ('cmr10', 0x5b),
    '\\leftparen'                : ('cmr10', 0x28),
    '\\rightbracket'             : ('cmr10', 0x5d),
    '\\rightparen'               : ('cmr10', 0x29),
    '\\widebar'                  : ('cmr10', 0xb9),
    ']'                          : ('cmr10', 0x5d),

    '*'                          : ('cmsy10', 0xa4),
    '-'                          : ('cmsy10', 0xa1),
    '\\Downarrow'                : ('cmsy10', 0x2b),
    '\\Im'                       : ('cmsy10', 0x3d),
    '\\Leftarrow'                : ('cmsy10', 0x28),
    '\\Leftrightarrow'           : ('cmsy10', 0x2c),
    '\\P'                        : ('cmsy10', 0x7b),
    '\\Re'                       : ('cmsy10', 0x3c),
    '\\Rightarrow'               : ('cmsy10', 0x29),
    '\\S'                        : ('cmsy10', 0x78),
    '\\Uparrow'                  : ('cmsy10', 0x2a),
    '\\Updownarrow'              : ('cmsy10', 0x6d),
    '\\Vert'                     : ('cmsy10', 0x6b),
    '\\aleph'                    : ('cmsy10', 0x40),
    '\\approx'                   : ('cmsy10', 0xbc),
    '\\ast'                      : ('cmsy10', 0xa4),
    '\\asymp'                    : ('cmsy10', 0xb3),
    '\\backslash'                : ('cmsy10', 0x6e),
    '\\bigcirc'                  : ('cmsy10', 0xb0),
    '\\bigtriangledown'          : ('cmsy10', 0x35),
    '\\bigtriangleup'            : ('cmsy10', 0x34),
    '\\bot'                      : ('cmsy10', 0x3f),
    '\\bullet'                   : ('cmsy10', 0xb2),
    '\\cap'                      : ('cmsy10', 0x5c),
    '\\cdot'                     : ('cmsy10', 0xa2),
    '\\circ'                     : ('cmsy10', 0xb1),
    '\\clubsuit'                 : ('cmsy10', 0x7c),
    '\\cup'                      : ('cmsy10', 0x5b),
    '\\dag'                      : ('cmsy10', 0x79),
    '\\dashv'                    : ('cmsy10', 0x61),
    '\\ddag'                     : ('cmsy10', 0x7a),
    '\\diamond'                  : ('cmsy10', 0xa6),
    '\\diamondsuit'              : ('cmsy10', 0x7d),
    '\\div'                      : ('cmsy10', 0xa5),
    '\\downarrow'                : ('cmsy10', 0x23),
    '\\emptyset'                 : ('cmsy10', 0x3b),
    '\\equiv'                    : ('cmsy10', 0xb4),
    '\\exists'                   : ('cmsy10', 0x39),
    '\\forall'                   : ('cmsy10', 0x38),
    '\\geq'                      : ('cmsy10', 0xb8),
    '\\gg'                       : ('cmsy10', 0xc0),
    '\\heartsuit'                : ('cmsy10', 0x7e),
    '\\in'                       : ('cmsy10', 0x32),
    '\\infty'                    : ('cmsy10', 0x31),
    '\\lbrace'                   : ('cmsy10', 0x66),
    '\\lceil'                    : ('cmsy10', 0x64),
    '\\leftarrow'                : ('cmsy10', 0xc3),
    '\\leftrightarrow'           : ('cmsy10', 0x24),
    '\\leq'                      : ('cmsy10', 0x2219),
    '\\lfloor'                   : ('cmsy10', 0x62),
    '\\ll'                       : ('cmsy10', 0xbf),
    '\\mid'                      : ('cmsy10', 0x6a),
    '\\mp'                       : ('cmsy10', 0xa8),
    '\\nabla'                    : ('cmsy10', 0x72),
    '\\nearrow'                  : ('cmsy10', 0x25),
    '\\neg'                      : ('cmsy10', 0x3a),
    '\\ni'                       : ('cmsy10', 0x33),
    '\\nwarrow'                  : ('cmsy10', 0x2d),
    '\\odot'                     : ('cmsy10', 0xaf),
    '\\ominus'                   : ('cmsy10', 0xaa),
    '\\oplus'                    : ('cmsy10', 0xa9),
    '\\oslash'                   : ('cmsy10', 0xae),
    '\\otimes'                   : ('cmsy10', 0xad),
    '\\pm'                       : ('cmsy10', 0xa7),
    '\\prec'                     : ('cmsy10', 0xc1),
    '\\preceq'                   : ('cmsy10', 0xb9),
    '\\prime'                    : ('cmsy10', 0x30),
    '\\propto'                   : ('cmsy10', 0x2f),
    '\\rbrace'                   : ('cmsy10', 0x67),
    '\\rceil'                    : ('cmsy10', 0x65),
    '\\rfloor'                   : ('cmsy10', 0x63),
    '\\rightarrow'               : ('cmsy10', 0x21),
    '\\searrow'                  : ('cmsy10', 0x26),
    '\\sim'                      : ('cmsy10', 0xbb),
    '\\simeq'                    : ('cmsy10', 0x27),
    '\\slash'                    : ('cmsy10', 0x36),
    '\\spadesuit'                : ('cmsy10', 0xc4),
    '\\sqcap'                    : ('cmsy10', 0x75),
    '\\sqcup'                    : ('cmsy10', 0x74),
    '\\sqsubseteq'               : ('cmsy10', 0x76),
    '\\sqsupseteq'               : ('cmsy10', 0x77),
    '\\subset'                   : ('cmsy10', 0xbd),
    '\\subseteq'                 : ('cmsy10', 0xb5),
    '\\succ'                     : ('cmsy10', 0xc2),
    '\\succeq'                   : ('cmsy10', 0xba),
    '\\supset'                   : ('cmsy10', 0xbe),
    '\\supseteq'                 : ('cmsy10', 0xb6),
    '\\swarrow'                  : ('cmsy10', 0x2e),
    '\\times'                    : ('cmsy10', 0xa3),
    '\\to'                       : ('cmsy10', 0x21),
    '\\top'                      : ('cmsy10', 0x3e),
    '\\uparrow'                  : ('cmsy10', 0x22),
    '\\updownarrow'              : ('cmsy10', 0x6c),
    '\\uplus'                    : ('cmsy10', 0x5d),
    '\\vdash'                    : ('cmsy10', 0x60),
    '\\vee'                      : ('cmsy10', 0x5f),
    '\\vert'                     : ('cmsy10', 0x6a),
    '\\wedge'                    : ('cmsy10', 0x5e),
    '\\wr'                       : ('cmsy10', 0x6f),
    '\\|'                        : ('cmsy10', 0x6b),
    '|'                          : ('cmsy10', 0x6a),

    '\\_'                        : ('cmtt10', 0x5f)
}

latex_to_cmex = {
    r'\__sqrt__'   : 112,
    r'\bigcap'     : 92,
    r'\bigcup'     : 91,
    r'\bigodot'    : 75,
    r'\bigoplus'   : 77,
    r'\bigotimes'  : 79,
    r'\biguplus'   : 93,
    r'\bigvee'     : 95,
    r'\bigwedge'   : 94,
    r'\coprod'     : 97,
    r'\int'        : 90,
    r'\leftangle'  : 173,
    r'\leftbrace'  : 169,
    r'\oint'       : 73,
    r'\prod'       : 89,
    r'\rightangle' : 174,
    r'\rightbrace' : 170,
    r'\sum'        : 88,
    r'\widehat'    : 98,
    r'\widetilde'  : 101,
}

latex_to_standard = {
    r'\cong'                     : ('psyr', 64),
    r'\Delta'                    : ('psyr', 68),
    r'\Phi'                      : ('psyr', 70),
    r'\Gamma'                    : ('psyr', 89),
    r'\alpha'                    : ('psyr', 97),
    r'\beta'                     : ('psyr', 98),
    r'\chi'                      : ('psyr', 99),
    r'\delta'                    : ('psyr', 100),
    r'\varepsilon'               : ('psyr', 101),
    r'\phi'                      : ('psyr', 102),
    r'\gamma'                    : ('psyr', 103),
    r'\eta'                      : ('psyr', 104),
    r'\iota'                     : ('psyr', 105),
    r'\varpsi'                   : ('psyr', 106),
    r'\kappa'                    : ('psyr', 108),
    r'\nu'                       : ('psyr', 110),
    r'\pi'                       : ('psyr', 112),
    r'\theta'                    : ('psyr', 113),
    r'\rho'                      : ('psyr', 114),
    r'\sigma'                    : ('psyr', 115),
    r'\tau'                      : ('psyr', 116),
    r'\upsilon'                  : ('psyr', 117),
    r'\varpi'                    : ('psyr', 118),
    r'\omega'                    : ('psyr', 119),
    r'\xi'                       : ('psyr', 120),
    r'\psi'                      : ('psyr', 121),
    r'\zeta'                     : ('psyr', 122),
    r'\sim'                      : ('psyr', 126),
    r'\leq'                      : ('psyr', 163),
    r'\infty'                    : ('psyr', 165),
    r'\clubsuit'                 : ('psyr', 167),
    r'\diamondsuit'              : ('psyr', 168),
    r'\heartsuit'                : ('psyr', 169),
    r'\spadesuit'                : ('psyr', 170),
    r'\leftrightarrow'           : ('psyr', 171),
    r'\leftarrow'                : ('psyr', 172),
    r'\uparrow'                  : ('psyr', 173),
    r'\rightarrow'               : ('psyr', 174),
    r'\downarrow'                : ('psyr', 175),
    r'\pm'                       : ('psyr', 176),
    r'\geq'                      : ('psyr', 179),
    r'\times'                    : ('psyr', 180),
    r'\propto'                   : ('psyr', 181),
    r'\partial'                  : ('psyr', 182),
    r'\bullet'                   : ('psyr', 183),
    r'\div'                      : ('psyr', 184),
    r'\neq'                      : ('psyr', 185),
    r'\equiv'                    : ('psyr', 186),
    r'\approx'                   : ('psyr', 187),
    r'\ldots'                    : ('psyr', 188),
    r'\aleph'                    : ('psyr', 192),
    r'\Im'                       : ('psyr', 193),
    r'\Re'                       : ('psyr', 194),
    r'\wp'                       : ('psyr', 195),
    r'\otimes'                   : ('psyr', 196),
    r'\oplus'                    : ('psyr', 197),
    r'\oslash'                   : ('psyr', 198),
    r'\cap'                      : ('psyr', 199),
    r'\cup'                      : ('psyr', 200),
    r'\supset'                   : ('psyr', 201),
    r'\supseteq'                 : ('psyr', 202),
    r'\subset'                   : ('psyr', 204),
    r'\subseteq'                 : ('psyr', 205),
    r'\in'                       : ('psyr', 206),
    r'\notin'                    : ('psyr', 207),
    r'\angle'                    : ('psyr', 208),
    r'\nabla'                    : ('psyr', 209),
    r'\textregistered'           : ('psyr', 210),
    r'\copyright'                : ('psyr', 211),
    r'\texttrademark'            : ('psyr', 212),
    r'\Pi'                       : ('psyr', 213),
    r'\prod'                     : ('psyr', 213),
    r'\surd'                     : ('psyr', 214),
    r'\__sqrt__'                 : ('psyr', 214),
    r'\cdot'                     : ('psyr', 215),
    r'\urcorner'                 : ('psyr', 216),
    r'\vee'                      : ('psyr', 217),
    r'\wedge'                    : ('psyr', 218),
    r'\Leftrightarrow'           : ('psyr', 219),
    r'\Leftarrow'                : ('psyr', 220),
    r'\Uparrow'                  : ('psyr', 221),
    r'\Rightarrow'               : ('psyr', 222),
    r'\Downarrow'                : ('psyr', 223),
    r'\Diamond'                  : ('psyr', 224),
    r'\Sigma'                    : ('psyr', 229),
    r'\sum'                      : ('psyr', 229),
    r'\forall'                   : ('psyr',  34),
    r'\exists'                   : ('psyr',  36),
    r'\lceil'                    : ('psyr', 233),
    r'\lbrace'                   : ('psyr', 123),
    r'\Psi'                      : ('psyr',  89),
    r'\bot'                      : ('psyr', 0o136),
    r'\Omega'                    : ('psyr', 0o127),
    r'\leftbracket'              : ('psyr', 0o133),
    r'\rightbracket'             : ('psyr', 0o135),
    r'\leftbrace'                : ('psyr', 123),
    r'\leftparen'                : ('psyr', 0o50),
    r'\prime'                    : ('psyr', 0o242),
    r'\sharp'                    : ('psyr', 0o43),
    r'\slash'                    : ('psyr', 0o57),
    r'\Lamda'                    : ('psyr', 0o114),
    r'\neg'                      : ('psyr', 0o330),
    r'\Upsilon'                  : ('psyr', 0o241),
    r'\rightbrace'               : ('psyr', 0o175),
    r'\rfloor'                   : ('psyr', 0o373),
    r'\lambda'                   : ('psyr', 0o154),
    r'\to'                       : ('psyr', 0o256),
    r'\Xi'                       : ('psyr', 0o130),
    r'\emptyset'                 : ('psyr', 0o306),
    r'\lfloor'                   : ('psyr', 0o353),
    r'\rightparen'               : ('psyr', 0o51),
    r'\rceil'                    : ('psyr', 0o371),
    r'\ni'                       : ('psyr', 0o47),
    r'\epsilon'                  : ('psyr', 0o145),
    r'\Theta'                    : ('psyr', 0o121),
    r'\langle'                   : ('psyr', 0o341),
    r'\leftangle'                : ('psyr', 0o341),
    r'\rangle'                   : ('psyr', 0o361),
    r'\rightangle'               : ('psyr', 0o361),
    r'\rbrace'                   : ('psyr', 0o175),
    r'\circ'                     : ('psyr', 0o260),
    r'\diamond'                  : ('psyr', 0o340),
    r'\mu'                       : ('psyr', 0o155),
    r'\mid'                      : ('psyr', 0o352),
    r'\imath'                    : ('pncri8a', 105),
    r'\%'                        : ('pncr8a',  37),
    r'\$'                        : ('pncr8a',  36),
    r'\{'                        : ('pncr8a', 123),
    r'\}'                        : ('pncr8a', 125),
    r'\backslash'                : ('pncr8a',  92),
    r'\ast'                      : ('pncr8a',  42),
    r'\#'                        : ('pncr8a',  35),

    r'\circumflexaccent'         : ('pncri8a',   124), # for \hat
    r'\combiningbreve'           : ('pncri8a',   81),  # for \breve
    r'\combininggraveaccent'     : ('pncri8a', 114), # for \grave
    r'\combiningacuteaccent'     : ('pncri8a', 63), # for \accute
    r'\combiningdiaeresis'       : ('pncri8a', 91), # for \ddot
    r'\combiningtilde'           : ('pncri8a', 75), # for \tilde
    r'\combiningrightarrowabove' : ('pncri8a', 110), # for \vec
    r'\combiningdotabove'        : ('pncri8a', 26), # for \dot
}

# Automatically generated.

type12uni = {
    'uni24C8'        : 9416,
    'aring'          : 229,
    'uni22A0'        : 8864,
    'uni2292'        : 8850,
    'quotedblright'  : 8221,
    'uni03D2'        : 978,
    'uni2215'        : 8725,
    'uni03D0'        : 976,
    'V'              : 86,
    'dollar'         : 36,
    'uni301E'        : 12318,
    'uni03D5'        : 981,
    'four'           : 52,
    'uni25A0'        : 9632,
    'uni013C'        : 316,
    'uni013B'        : 315,
    'uni013E'        : 318,
    'Yacute'         : 221,
    'uni25DE'        : 9694,
    'uni013F'        : 319,
    'uni255A'        : 9562,
    'uni2606'        : 9734,
    'uni0180'        : 384,
    'uni22B7'        : 8887,
    'uni044F'        : 1103,
    'uni22B5'        : 8885,
    'uni22B4'        : 8884,
    'uni22AE'        : 8878,
    'uni22B2'        : 8882,
    'uni22B1'        : 8881,
    'uni22B0'        : 8880,
    'uni25CD'        : 9677,
    'uni03CE'        : 974,
    'uni03CD'        : 973,
    'uni03CC'        : 972,
    'uni03CB'        : 971,
    'uni03CA'        : 970,
    'uni22B8'        : 8888,
    'uni22C9'        : 8905,
    'uni0449'        : 1097,
    'uni20DD'        : 8413,
    'uni20DC'        : 8412,
    'uni20DB'        : 8411,
    'uni2231'        : 8753,
    'uni25CF'        : 9679,
    'uni306E'        : 12398,
    'uni03D1'        : 977,
    'uni01A1'        : 417,
    'uni20D7'        : 8407,
    'uni03D6'        : 982,
    'uni2233'        : 8755,
    'uni20D2'        : 8402,
    'uni20D1'        : 8401,
    'uni20D0'        : 8400,
    'P'              : 80,
    'uni22BE'        : 8894,
    'uni22BD'        : 8893,
    'uni22BC'        : 8892,
    'uni22BB'        : 8891,
    'underscore'     : 95,
    'uni03C8'        : 968,
    'uni03C7'        : 967,
    'uni0328'        : 808,
    'uni03C5'        : 965,
    'uni03C4'        : 964,
    'uni03C3'        : 963,
    'uni03C2'        : 962,
    'uni03C1'        : 961,
    'uni03C0'        : 960,
    'uni2010'        : 8208,
    'uni0130'        : 304,
    'uni0133'        : 307,
    'uni0132'        : 306,
    'uni0135'        : 309,
    'uni0134'        : 308,
    'uni0137'        : 311,
    'uni0136'        : 310,
    'uni0139'        : 313,
    'uni0138'        : 312,
    'uni2244'        : 8772,
    'uni229A'        : 8858,
    'uni2571'        : 9585,
    'uni0278'        : 632,
    'uni2239'        : 8761,
    'p'              : 112,
    'uni3019'        : 12313,
    'uni25CB'        : 9675,
    'uni03DB'        : 987,
    'uni03DC'        : 988,
    'uni03DA'        : 986,
    'uni03DF'        : 991,
    'uni03DD'        : 989,
    'uni013D'        : 317,
    'uni220A'        : 8714,
    'uni220C'        : 8716,
    'uni220B'        : 8715,
    'uni220E'        : 8718,
    'uni220D'        : 8717,
    'uni220F'        : 8719,
    'uni22CC'        : 8908,
    'Otilde'         : 213,
    'uni25E5'        : 9701,
    'uni2736'        : 10038,
    'perthousand'    : 8240,
    'zero'           : 48,
    'uni279B'        : 10139,
    'dotlessi'       : 305,
    'uni2279'        : 8825,
    'Scaron'         : 352,
    'zcaron'         : 382,
    'uni21D8'        : 8664,
    'egrave'         : 232,
    'uni0271'        : 625,
    'uni01AA'        : 426,
    'uni2332'        : 9010,
    'section'        : 167,
    'uni25E4'        : 9700,
    'Icircumflex'    : 206,
    'ntilde'         : 241,
    'uni041E'        : 1054,
    'ampersand'      : 38,
    'uni041C'        : 1052,
    'uni041A'        : 1050,
    'uni22AB'        : 8875,
    'uni21DB'        : 8667,
    'dotaccent'      : 729,
    'uni0416'        : 1046,
    'uni0417'        : 1047,
    'uni0414'        : 1044,
    'uni0415'        : 1045,
    'uni0412'        : 1042,
    'uni0413'        : 1043,
    'degree'         : 176,
    'uni0411'        : 1041,
    'K'              : 75,
    'uni25EB'        : 9707,
    'uni25EF'        : 9711,
    'uni0418'        : 1048,
    'uni0419'        : 1049,
    'uni2263'        : 8803,
    'uni226E'        : 8814,
    'uni2251'        : 8785,
    'uni02C8'        : 712,
    'uni2262'        : 8802,
    'acircumflex'    : 226,
    'uni22B3'        : 8883,
    'uni2261'        : 8801,
    'uni2394'        : 9108,
    'Aring'          : 197,
    'uni2260'        : 8800,
    'uni2254'        : 8788,
    'uni0436'        : 1078,
    'uni2267'        : 8807,
    'k'              : 107,
    'uni22C8'        : 8904,
    'uni226A'        : 8810,
    'uni231F'        : 8991,
    'smalltilde'     : 732,
    'uni2201'        : 8705,
    'uni2200'        : 8704,
    'uni2203'        : 8707,
    'uni02BD'        : 701,
    'uni2205'        : 8709,
    'uni2204'        : 8708,
    'Agrave'         : 192,
    'uni2206'        : 8710,
    'uni2209'        : 8713,
    'uni2208'        : 8712,
    'uni226D'        : 8813,
    'uni2264'        : 8804,
    'uni263D'        : 9789,
    'uni2258'        : 8792,
    'uni02D3'        : 723,
    'uni02D2'        : 722,
    'uni02D1'        : 721,
    'uni02D0'        : 720,
    'uni25E1'        : 9697,
    'divide'         : 247,
    'uni02D5'        : 725,
    'uni02D4'        : 724,
    'ocircumflex'    : 244,
    'uni2524'        : 9508,
    'uni043A'        : 1082,
    'uni24CC'        : 9420,
    'asciitilde'     : 126,
    'uni22B9'        : 8889,
    'uni24D2'        : 9426,
    'uni211E'        : 8478,
    'uni211D'        : 8477,
    'uni24DD'        : 9437,
    'uni211A'        : 8474,
    'uni211C'        : 8476,
    'uni211B'        : 8475,
    'uni25C6'        : 9670,
    'uni017F'        : 383,
    'uni017A'        : 378,
    'uni017C'        : 380,
    'uni017B'        : 379,
    'uni0346'        : 838,
    'uni22F1'        : 8945,
    'uni22F0'        : 8944,
    'two'            : 50,
    'uni2298'        : 8856,
    'uni24D1'        : 9425,
    'E'              : 69,
    'uni025D'        : 605,
    'scaron'         : 353,
    'uni2322'        : 8994,
    'uni25E3'        : 9699,
    'uni22BF'        : 8895,
    'F'              : 70,
    'uni0440'        : 1088,
    'uni255E'        : 9566,
    'uni22BA'        : 8890,
    'uni0175'        : 373,
    'uni0174'        : 372,
    'uni0177'        : 375,
    'uni0176'        : 374,
    'bracketleft'    : 91,
    'uni0170'        : 368,
    'uni0173'        : 371,
    'uni0172'        : 370,
    'asciicircum'    : 94,
    'uni0179'        : 377,
    'uni2590'        : 9616,
    'uni25E2'        : 9698,
    'uni2119'        : 8473,
    'uni2118'        : 8472,
    'uni25CC'        : 9676,
    'f'              : 102,
    'ordmasculine'   : 186,
    'uni229B'        : 8859,
    'uni22A1'        : 8865,
    'uni2111'        : 8465,
    'uni2110'        : 8464,
    'uni2113'        : 8467,
    'uni2112'        : 8466,
    'mu'             : 181,
    'uni2281'        : 8833,
    'paragraph'      : 182,
    'nine'           : 57,
    'uni25EC'        : 9708,
    'v'              : 118,
    'uni040C'        : 1036,
    'uni0113'        : 275,
    'uni22D0'        : 8912,
    'uni21CC'        : 8652,
    'uni21CB'        : 8651,
    'uni21CA'        : 8650,
    'uni22A5'        : 8869,
    'uni21CF'        : 8655,
    'uni21CE'        : 8654,
    'uni21CD'        : 8653,
    'guilsinglleft'  : 8249,
    'backslash'      : 92,
    'uni2284'        : 8836,
    'uni224E'        : 8782,
    'uni224D'        : 8781,
    'uni224F'        : 8783,
    'uni224A'        : 8778,
    'uni2287'        : 8839,
    'uni224C'        : 8780,
    'uni224B'        : 8779,
    'uni21BD'        : 8637,
    'uni2286'        : 8838,
    'uni030F'        : 783,
    'uni030D'        : 781,
    'uni030E'        : 782,
    'uni030B'        : 779,
    'uni030C'        : 780,
    'uni030A'        : 778,
    'uni026E'        : 622,
    'uni026D'        : 621,
    'six'            : 54,
    'uni026A'        : 618,
    'uni026C'        : 620,
    'uni25C1'        : 9665,
    'uni20D6'        : 8406,
    'uni045B'        : 1115,
    'uni045C'        : 1116,
    'uni256B'        : 9579,
    'uni045A'        : 1114,
    'uni045F'        : 1119,
    'uni045E'        : 1118,
    'A'              : 65,
    'uni2569'        : 9577,
    'uni0458'        : 1112,
    'uni0459'        : 1113,
    'uni0452'        : 1106,
    'uni0453'        : 1107,
    'uni2562'        : 9570,
    'uni0451'        : 1105,
    'uni0456'        : 1110,
    'uni0457'        : 1111,
    'uni0454'        : 1108,
    'uni0455'        : 1109,
    'icircumflex'    : 238,
    'uni0307'        : 775,
    'uni0304'        : 772,
    'uni0305'        : 773,
    'uni0269'        : 617,
    'uni0268'        : 616,
    'uni0300'        : 768,
    'uni0301'        : 769,
    'uni0265'        : 613,
    'uni0264'        : 612,
    'uni0267'        : 615,
    'uni0266'        : 614,
    'uni0261'        : 609,
    'uni0260'        : 608,
    'uni0263'        : 611,
    'uni0262'        : 610,
    'a'              : 97,
    'uni2207'        : 8711,
    'uni2247'        : 8775,
    'uni2246'        : 8774,
    'uni2241'        : 8769,
    'uni2240'        : 8768,
    'uni2243'        : 8771,
    'uni2242'        : 8770,
    'uni2312'        : 8978,
    'ogonek'         : 731,
    'uni2249'        : 8777,
    'uni2248'        : 8776,
    'uni3030'        : 12336,
    'q'              : 113,
    'uni21C2'        : 8642,
    'uni21C1'        : 8641,
    'uni21C0'        : 8640,
    'uni21C7'        : 8647,
    'uni21C6'        : 8646,
    'uni21C5'        : 8645,
    'uni21C4'        : 8644,
    'uni225F'        : 8799,
    'uni212C'        : 8492,
    'uni21C8'        : 8648,
    'uni2467'        : 9319,
    'oacute'         : 243,
    'uni028F'        : 655,
    'uni028E'        : 654,
    'uni026F'        : 623,
    'uni028C'        : 652,
    'uni028B'        : 651,
    'uni028A'        : 650,
    'uni2510'        : 9488,
    'ograve'         : 242,
    'edieresis'      : 235,
    'uni22CE'        : 8910,
    'uni22CF'        : 8911,
    'uni219F'        : 8607,
    'comma'          : 44,
    'uni22CA'        : 8906,
    'uni0429'        : 1065,
    'uni03C6'        : 966,
    'uni0427'        : 1063,
    'uni0426'        : 1062,
    'uni0425'        : 1061,
    'uni0424'        : 1060,
    'uni0423'        : 1059,
    'uni0422'        : 1058,
    'uni0421'        : 1057,
    'uni0420'        : 1056,
    'uni2465'        : 9317,
    'uni24D0'        : 9424,
    'uni2464'        : 9316,
    'uni0430'        : 1072,
    'otilde'         : 245,
    'uni2661'        : 9825,
    'uni24D6'        : 9430,
    'uni2466'        : 9318,
    'uni24D5'        : 9429,
    'uni219A'        : 8602,
    'uni2518'        : 9496,
    'uni22B6'        : 8886,
    'uni2461'        : 9313,
    'uni24D4'        : 9428,
    'uni2460'        : 9312,
    'uni24EA'        : 9450,
    'guillemotright' : 187,
    'ecircumflex'    : 234,
    'greater'        : 62,
    'uni2011'        : 8209,
    'uacute'         : 250,
    'uni2462'        : 9314,
    'L'              : 76,
    'bullet'         : 8226,
    'uni02A4'        : 676,
    'uni02A7'        : 679,
    'cedilla'        : 184,
    'uni02A2'        : 674,
    'uni2015'        : 8213,
    'uni22C4'        : 8900,
    'uni22C5'        : 8901,
    'uni22AD'        : 8877,
    'uni22C7'        : 8903,
    'uni22C0'        : 8896,
    'uni2016'        : 8214,
    'uni22C2'        : 8898,
    'uni22C3'        : 8899,
    'uni24CF'        : 9423,
    'uni042F'        : 1071,
    'uni042E'        : 1070,
    'uni042D'        : 1069,
    'ydieresis'      : 255,
    'l'              : 108,
    'logicalnot'     : 172,
    'uni24CA'        : 9418,
    'uni0287'        : 647,
    'uni0286'        : 646,
    'uni0285'        : 645,
    'uni0284'        : 644,
    'uni0283'        : 643,
    'uni0282'        : 642,
    'uni0281'        : 641,
    'uni027C'        : 636,
    'uni2664'        : 9828,
    'exclamdown'     : 161,
    'uni25C4'        : 9668,
    'uni0289'        : 649,
    'uni0288'        : 648,
    'uni039A'        : 922,
    'endash'         : 8211,
    'uni2640'        : 9792,
    'uni20E4'        : 8420,
    'uni0473'        : 1139,
    'uni20E1'        : 8417,
    'uni2642'        : 9794,
    'uni03B8'        : 952,
    'uni03B9'        : 953,
    'agrave'         : 224,
    'uni03B4'        : 948,
    'uni03B5'        : 949,
    'uni03B6'        : 950,
    'uni03B7'        : 951,
    'uni03B0'        : 944,
    'uni03B1'        : 945,
    'uni03B2'        : 946,
    'uni03B3'        : 947,
    'uni2555'        : 9557,
    'Adieresis'      : 196,
    'germandbls'     : 223,
    'Odieresis'      : 214,
    'space'          : 32,
    'uni0126'        : 294,
    'uni0127'        : 295,
    'uni0124'        : 292,
    'uni0125'        : 293,
    'uni0122'        : 290,
    'uni0123'        : 291,
    'uni0120'        : 288,
    'uni0121'        : 289,
    'quoteright'     : 8217,
    'uni2560'        : 9568,
    'uni2556'        : 9558,
    'ucircumflex'    : 251,
    'uni2561'        : 9569,
    'uni2551'        : 9553,
    'uni25B2'        : 9650,
    'uni2550'        : 9552,
    'uni2563'        : 9571,
    'uni2553'        : 9555,
    'G'              : 71,
    'uni2564'        : 9572,
    'uni2552'        : 9554,
    'quoteleft'      : 8216,
    'uni2565'        : 9573,
    'uni2572'        : 9586,
    'uni2568'        : 9576,
    'uni2566'        : 9574,
    'W'              : 87,
    'uni214A'        : 8522,
    'uni012F'        : 303,
    'uni012D'        : 301,
    'uni012E'        : 302,
    'uni012B'        : 299,
    'uni012C'        : 300,
    'uni255C'        : 9564,
    'uni012A'        : 298,
    'uni2289'        : 8841,
    'Q'              : 81,
    'uni2320'        : 8992,
    'uni2321'        : 8993,
    'g'              : 103,
    'uni03BD'        : 957,
    'uni03BE'        : 958,
    'uni03BF'        : 959,
    'uni2282'        : 8834,
    'uni2285'        : 8837,
    'uni03BA'        : 954,
    'uni03BB'        : 955,
    'uni03BC'        : 956,
    'uni2128'        : 8488,
    'uni25B7'        : 9655,
    'w'              : 119,
    'uni0302'        : 770,
    'uni03DE'        : 990,
    'uni25DA'        : 9690,
    'uni0303'        : 771,
    'uni0463'        : 1123,
    'uni0462'        : 1122,
    'uni3018'        : 12312,
    'uni2514'        : 9492,
    'question'       : 63,
    'uni25B3'        : 9651,
    'uni24E1'        : 9441,
    'one'            : 49,
    'uni200A'        : 8202,
    'uni2278'        : 8824,
    'ring'           : 730,
    'uni0195'        : 405,
    'figuredash'     : 8210,
    'uni22EC'        : 8940,
    'uni0339'        : 825,
    'uni0338'        : 824,
    'uni0337'        : 823,
    'uni0336'        : 822,
    'uni0335'        : 821,
    'uni0333'        : 819,
    'uni0332'        : 818,
    'uni0331'        : 817,
    'uni0330'        : 816,
    'uni01C1'        : 449,
    'uni01C0'        : 448,
    'uni01C3'        : 451,
    'uni01C2'        : 450,
    'uni2353'        : 9043,
    'uni0308'        : 776,
    'uni2218'        : 8728,
    'uni2219'        : 8729,
    'uni2216'        : 8726,
    'uni2217'        : 8727,
    'uni2214'        : 8724,
    'uni0309'        : 777,
    'uni2609'        : 9737,
    'uni2213'        : 8723,
    'uni2210'        : 8720,
    'uni2211'        : 8721,
    'uni2245'        : 8773,
    'B'              : 66,
    'uni25D6'        : 9686,
    'iacute'         : 237,
    'uni02E6'        : 742,
    'uni02E7'        : 743,
    'uni02E8'        : 744,
    'uni02E9'        : 745,
    'uni221D'        : 8733,
    'uni221E'        : 8734,
    'Ydieresis'      : 376,
    'uni221C'        : 8732,
    'uni22D7'        : 8919,
    'uni221A'        : 8730,
    'R'              : 82,
    'uni24DC'        : 9436,
    'uni033F'        : 831,
    'uni033E'        : 830,
    'uni033C'        : 828,
    'uni033B'        : 827,
    'uni033A'        : 826,
    'b'              : 98,
    'uni228A'        : 8842,
    'uni22DB'        : 8923,
    'uni2554'        : 9556,
    'uni046B'        : 1131,
    'uni046A'        : 1130,
    'r'              : 114,
    'uni24DB'        : 9435,
    'Ccedilla'       : 199,
    'minus'          : 8722,
    'uni24DA'        : 9434,
    'uni03F0'        : 1008,
    'uni03F1'        : 1009,
    'uni20AC'        : 8364,
    'uni2276'        : 8822,
    'uni24C0'        : 9408,
    'uni0162'        : 354,
    'uni0163'        : 355,
    'uni011E'        : 286,
    'uni011D'        : 285,
    'uni011C'        : 284,
    'uni011B'        : 283,
    'uni0164'        : 356,
    'uni0165'        : 357,
    'Lslash'         : 321,
    'uni0168'        : 360,
    'uni0169'        : 361,
    'uni25C9'        : 9673,
    'uni02E5'        : 741,
    'uni21C3'        : 8643,
    'uni24C4'        : 9412,
    'uni24E2'        : 9442,
    'uni2277'        : 8823,
    'uni013A'        : 314,
    'uni2102'        : 8450,
    'Uacute'         : 218,
    'uni2317'        : 8983,
    'uni2107'        : 8455,
    'uni221F'        : 8735,
    'yacute'         : 253,
    'uni3012'        : 12306,
    'Ucircumflex'    : 219,
    'uni015D'        : 349,
    'quotedbl'       : 34,
    'uni25D9'        : 9689,
    'uni2280'        : 8832,
    'uni22AF'        : 8879,
    'onehalf'        : 189,
    'uni221B'        : 8731,
    'Thorn'          : 222,
    'uni2226'        : 8742,
    'M'              : 77,
    'uni25BA'        : 9658,
    'uni2463'        : 9315,
    'uni2336'        : 9014,
    'eight'          : 56,
    'uni2236'        : 8758,
    'multiply'       : 215,
    'uni210C'        : 8460,
    'uni210A'        : 8458,
    'uni21C9'        : 8649,
    'grave'          : 96,
    'uni210E'        : 8462,
    'uni0117'        : 279,
    'uni016C'        : 364,
    'uni0115'        : 277,
    'uni016A'        : 362,
    'uni016F'        : 367,
    'uni0112'        : 274,
    'uni016D'        : 365,
    'uni016E'        : 366,
    'Ocircumflex'    : 212,
    'uni2305'        : 8965,
    'm'              : 109,
    'uni24DF'        : 9439,
    'uni0119'        : 281,
    'uni0118'        : 280,
    'uni20A3'        : 8355,
    'uni20A4'        : 8356,
    'uni20A7'        : 8359,
    'uni2288'        : 8840,
    'uni24C3'        : 9411,
    'uni251C'        : 9500,
    'uni228D'        : 8845,
    'uni222F'        : 8751,
    'uni222E'        : 8750,
    'uni222D'        : 8749,
    'uni222C'        : 8748,
    'uni222B'        : 8747,
    'uni222A'        : 8746,
    'uni255B'        : 9563,
    'Ugrave'         : 217,
    'uni24DE'        : 9438,
    'guilsinglright' : 8250,
    'uni250A'        : 9482,
    'Ntilde'         : 209,
    'uni0279'        : 633,
    'questiondown'   : 191,
    'uni256C'        : 9580,
    'Atilde'         : 195,
    'uni0272'        : 626,
    'uni0273'        : 627,
    'uni0270'        : 624,
    'ccedilla'       : 231,
    'uni0276'        : 630,
    'uni0277'        : 631,
    'uni0274'        : 628,
    'uni0275'        : 629,
    'uni2252'        : 8786,
    'uni041F'        : 1055,
    'uni2250'        : 8784,
    'Z'              : 90,
    'uni2256'        : 8790,
    'uni2257'        : 8791,
    'copyright'      : 169,
    'uni2255'        : 8789,
    'uni043D'        : 1085,
    'uni043E'        : 1086,
    'uni043F'        : 1087,
    'yen'            : 165,
    'uni041D'        : 1053,
    'uni043B'        : 1083,
    'uni043C'        : 1084,
    'uni21B0'        : 8624,
    'uni21B1'        : 8625,
    'uni21B2'        : 8626,
    'uni21B3'        : 8627,
    'uni21B4'        : 8628,
    'uni21B5'        : 8629,
    'uni21B6'        : 8630,
    'uni21B7'        : 8631,
    'uni21B8'        : 8632,
    'Eacute'         : 201,
    'uni2311'        : 8977,
    'uni2310'        : 8976,
    'uni228F'        : 8847,
    'uni25DB'        : 9691,
    'uni21BA'        : 8634,
    'uni21BB'        : 8635,
    'uni21BC'        : 8636,
    'uni2017'        : 8215,
    'uni21BE'        : 8638,
    'uni21BF'        : 8639,
    'uni231C'        : 8988,
    'H'              : 72,
    'uni0293'        : 659,
    'uni2202'        : 8706,
    'uni22A4'        : 8868,
    'uni231E'        : 8990,
    'uni2232'        : 8754,
    'uni225B'        : 8795,
    'uni225C'        : 8796,
    'uni24D9'        : 9433,
    'uni225A'        : 8794,
    'uni0438'        : 1080,
    'uni0439'        : 1081,
    'uni225D'        : 8797,
    'uni225E'        : 8798,
    'uni0434'        : 1076,
    'X'              : 88,
    'uni007F'        : 127,
    'uni0437'        : 1079,
    'Idieresis'      : 207,
    'uni0431'        : 1073,
    'uni0432'        : 1074,
    'uni0433'        : 1075,
    'uni22AC'        : 8876,
    'uni22CD'        : 8909,
    'uni25A3'        : 9635,
    'bar'            : 124,
    'uni24BB'        : 9403,
    'uni037E'        : 894,
    'uni027B'        : 635,
    'h'              : 104,
    'uni027A'        : 634,
    'uni027F'        : 639,
    'uni027D'        : 637,
    'uni027E'        : 638,
    'uni2227'        : 8743,
    'uni2004'        : 8196,
    'uni2225'        : 8741,
    'uni2224'        : 8740,
    'uni2223'        : 8739,
    'uni2222'        : 8738,
    'uni2221'        : 8737,
    'uni2220'        : 8736,
    'x'              : 120,
    'uni2323'        : 8995,
    'uni2559'        : 9561,
    'uni2558'        : 9560,
    'uni2229'        : 8745,
    'uni2228'        : 8744,
    'udieresis'      : 252,
    'uni029D'        : 669,
    'ordfeminine'    : 170,
    'uni22CB'        : 8907,
    'uni233D'        : 9021,
    'uni0428'        : 1064,
    'uni24C6'        : 9414,
    'uni22DD'        : 8925,
    'uni24C7'        : 9415,
    'uni015C'        : 348,
    'uni015B'        : 347,
    'uni015A'        : 346,
    'uni22AA'        : 8874,
    'uni015F'        : 351,
    'uni015E'        : 350,
    'braceleft'      : 123,
    'uni24C5'        : 9413,
    'uni0410'        : 1040,
    'uni03AA'        : 938,
    'uni24C2'        : 9410,
    'uni03AC'        : 940,
    'uni03AB'        : 939,
    'macron'         : 175,
    'uni03AD'        : 941,
    'uni03AF'        : 943,
    'uni0294'        : 660,
    'uni0295'        : 661,
    'uni0296'        : 662,
    'uni0297'        : 663,
    'uni0290'        : 656,
    'uni0291'        : 657,
    'uni0292'        : 658,
    'atilde'         : 227,
    'Acircumflex'    : 194,
    'uni2370'        : 9072,
    'uni24C1'        : 9409,
    'uni0298'        : 664,
    'uni0299'        : 665,
    'Oslash'         : 216,
    'uni029E'        : 670,
    'C'              : 67,
    'quotedblleft'   : 8220,
    'uni029B'        : 667,
    'uni029C'        : 668,
    'uni03A9'        : 937,
    'uni03A8'        : 936,
    'S'              : 83,
    'uni24C9'        : 9417,
    'uni03A1'        : 929,
    'uni03A0'        : 928,
    'exclam'         : 33,
    'uni03A5'        : 933,
    'uni03A4'        : 932,
    'uni03A7'        : 935,
    'Zcaron'         : 381,
    'uni2133'        : 8499,
    'uni2132'        : 8498,
    'uni0159'        : 345,
    'uni0158'        : 344,
    'uni2137'        : 8503,
    'uni2005'        : 8197,
    'uni2135'        : 8501,
    'uni2134'        : 8500,
    'uni02BA'        : 698,
    'uni2033'        : 8243,
    'uni0151'        : 337,
    'uni0150'        : 336,
    'uni0157'        : 343,
    'equal'          : 61,
    'uni0155'        : 341,
    'uni0154'        : 340,
    's'              : 115,
    'uni233F'        : 9023,
    'eth'            : 240,
    'uni24BE'        : 9406,
    'uni21E9'        : 8681,
    'uni2060'        : 8288,
    'Egrave'         : 200,
    'uni255D'        : 9565,
    'uni24CD'        : 9421,
    'uni21E1'        : 8673,
    'uni21B9'        : 8633,
    'hyphen'         : 45,
    'uni01BE'        : 446,
    'uni01BB'        : 443,
    'period'         : 46,
    'igrave'         : 236,
    'uni01BA'        : 442,
    'uni2296'        : 8854,
    'uni2297'        : 8855,
    'uni2294'        : 8852,
    'uni2295'        : 8853,
    'colon'          : 58,
    'uni2293'        : 8851,
    'uni2290'        : 8848,
    'uni2291'        : 8849,
    'uni032D'        : 813,
    'uni032E'        : 814,
    'uni032F'        : 815,
    'uni032A'        : 810,
    'uni032B'        : 811,
    'uni032C'        : 812,
    'uni231D'        : 8989,
    'Ecircumflex'    : 202,
    'uni24D7'        : 9431,
    'uni25DD'        : 9693,
    'trademark'      : 8482,
    'Aacute'         : 193,
    'cent'           : 162,
    'uni0445'        : 1093,
    'uni266E'        : 9838,
    'uni266D'        : 9837,
    'uni266B'        : 9835,
    'uni03C9'        : 969,
    'uni2003'        : 8195,
    'uni2047'        : 8263,
    'lslash'         : 322,
    'uni03A6'        : 934,
    'uni2043'        : 8259,
    'uni250C'        : 9484,
    'uni2040'        : 8256,
    'uni255F'        : 9567,
    'uni24CB'        : 9419,
    'uni0472'        : 1138,
    'uni0446'        : 1094,
    'uni0474'        : 1140,
    'uni0475'        : 1141,
    'uni2508'        : 9480,
    'uni2660'        : 9824,
    'uni2506'        : 9478,
    'uni2502'        : 9474,
    'c'              : 99,
    'uni2500'        : 9472,
    'N'              : 78,
    'uni22A6'        : 8870,
    'uni21E7'        : 8679,
    'uni2130'        : 8496,
    'uni2002'        : 8194,
    'breve'          : 728,
    'uni0442'        : 1090,
    'Oacute'         : 211,
    'uni229F'        : 8863,
    'uni25C7'        : 9671,
    'uni229D'        : 8861,
    'uni229E'        : 8862,
    'guillemotleft'  : 171,
    'uni0329'        : 809,
    'uni24E5'        : 9445,
    'uni011F'        : 287,
    'uni0324'        : 804,
    'uni0325'        : 805,
    'uni0326'        : 806,
    'uni0327'        : 807,
    'uni0321'        : 801,
    'uni0322'        : 802,
    'n'              : 110,
    'uni2032'        : 8242,
    'uni2269'        : 8809,
    'uni2268'        : 8808,
    'uni0306'        : 774,
    'uni226B'        : 8811,
    'uni21EA'        : 8682,
    'uni0166'        : 358,
    'uni203B'        : 8251,
    'uni01B5'        : 437,
    'idieresis'      : 239,
    'uni02BC'        : 700,
    'uni01B0'        : 432,
    'braceright'     : 125,
    'seven'          : 55,
    'uni02BB'        : 699,
    'uni011A'        : 282,
    'uni29FB'        : 10747,
    'brokenbar'      : 166,
    'uni2036'        : 8246,
    'uni25C0'        : 9664,
    'uni0156'        : 342,
    'uni22D5'        : 8917,
    'uni0258'        : 600,
    'ugrave'         : 249,
    'uni22D6'        : 8918,
    'uni22D1'        : 8913,
    'uni2034'        : 8244,
    'uni22D3'        : 8915,
    'uni22D2'        : 8914,
    'uni203C'        : 8252,
    'uni223E'        : 8766,
    'uni02BF'        : 703,
    'uni22D9'        : 8921,
    'uni22D8'        : 8920,
    'uni25BD'        : 9661,
    'uni25BE'        : 9662,
    'uni25BF'        : 9663,
    'uni041B'        : 1051,
    'periodcentered' : 183,
    'uni25BC'        : 9660,
    'uni019E'        : 414,
    'uni019B'        : 411,
    'uni019A'        : 410,
    'uni2007'        : 8199,
    'uni0391'        : 913,
    'uni0390'        : 912,
    'uni0393'        : 915,
    'uni0392'        : 914,
    'uni0395'        : 917,
    'uni0394'        : 916,
    'uni0397'        : 919,
    'uni0396'        : 918,
    'uni0399'        : 921,
    'uni0398'        : 920,
    'uni25C8'        : 9672,
    'uni2468'        : 9320,
    'sterling'       : 163,
    'uni22EB'        : 8939,
    'uni039C'        : 924,
    'uni039B'        : 923,
    'uni039E'        : 926,
    'uni039D'        : 925,
    'uni039F'        : 927,
    'I'              : 73,
    'uni03E1'        : 993,
    'uni03E0'        : 992,
    'uni2319'        : 8985,
    'uni228B'        : 8843,
    'uni25B5'        : 9653,
    'uni25B6'        : 9654,
    'uni22EA'        : 8938,
    'uni24B9'        : 9401,
    'uni044E'        : 1102,
    'uni0199'        : 409,
    'uni2266'        : 8806,
    'Y'              : 89,
    'uni22A2'        : 8866,
    'Eth'            : 208,
    'uni266F'        : 9839,
    'emdash'         : 8212,
    'uni263B'        : 9787,
    'uni24BD'        : 9405,
    'uni22DE'        : 8926,
    'uni0360'        : 864,
    'uni2557'        : 9559,
    'uni22DF'        : 8927,
    'uni22DA'        : 8922,
    'uni22DC'        : 8924,
    'uni0361'        : 865,
    'i'              : 105,
    'uni24BF'        : 9407,
    'uni0362'        : 866,
    'uni263E'        : 9790,
    'uni028D'        : 653,
    'uni2259'        : 8793,
    'uni0323'        : 803,
    'uni2265'        : 8805,
    'daggerdbl'      : 8225,
    'y'              : 121,
    'uni010A'        : 266,
    'plusminus'      : 177,
    'less'           : 60,
    'uni21AE'        : 8622,
    'uni0315'        : 789,
    'uni230B'        : 8971,
    'uni21AF'        : 8623,
    'uni21AA'        : 8618,
    'uni21AC'        : 8620,
    'uni21AB'        : 8619,
    'uni01FB'        : 507,
    'uni01FC'        : 508,
    'uni223A'        : 8762,
    'uni01FA'        : 506,
    'uni01FF'        : 511,
    'uni01FD'        : 509,
    'uni01FE'        : 510,
    'uni2567'        : 9575,
    'uni25E0'        : 9696,
    'uni0104'        : 260,
    'uni0105'        : 261,
    'uni0106'        : 262,
    'uni0107'        : 263,
    'uni0100'        : 256,
    'uni0101'        : 257,
    'uni0102'        : 258,
    'uni0103'        : 259,
    'uni2038'        : 8248,
    'uni2009'        : 8201,
    'uni2008'        : 8200,
    'uni0108'        : 264,
    'uni0109'        : 265,
    'uni02A1'        : 673,
    'uni223B'        : 8763,
    'uni226C'        : 8812,
    'uni25AC'        : 9644,
    'uni24D3'        : 9427,
    'uni21E0'        : 8672,
    'uni21E3'        : 8675,
    'Udieresis'      : 220,
    'uni21E2'        : 8674,
    'D'              : 68,
    'uni21E5'        : 8677,
    'uni2621'        : 9761,
    'uni21D1'        : 8657,
    'uni203E'        : 8254,
    'uni22C6'        : 8902,
    'uni21E4'        : 8676,
    'uni010D'        : 269,
    'uni010E'        : 270,
    'uni010F'        : 271,
    'five'           : 53,
    'T'              : 84,
    'uni010B'        : 267,
    'uni010C'        : 268,
    'uni2605'        : 9733,
    'uni2663'        : 9827,
    'uni21E6'        : 8678,
    'uni24B6'        : 9398,
    'uni22C1'        : 8897,
    'oslash'         : 248,
    'acute'          : 180,
    'uni01F0'        : 496,
    'd'              : 100,
    'OE'             : 338,
    'uni22E3'        : 8931,
    'Igrave'         : 204,
    'uni2308'        : 8968,
    'uni2309'        : 8969,
    'uni21A9'        : 8617,
    't'              : 116,
    'uni2313'        : 8979,
    'uni03A3'        : 931,
    'uni21A4'        : 8612,
    'uni21A7'        : 8615,
    'uni21A6'        : 8614,
    'uni21A1'        : 8609,
    'uni21A0'        : 8608,
    'uni21A3'        : 8611,
    'uni21A2'        : 8610,
    'parenright'     : 41,
    'uni256A'        : 9578,
    'uni25DC'        : 9692,
    'uni24CE'        : 9422,
    'uni042C'        : 1068,
    'uni24E0'        : 9440,
    'uni042B'        : 1067,
    'uni0409'        : 1033,
    'uni0408'        : 1032,
    'uni24E7'        : 9447,
    'uni25B4'        : 9652,
    'uni042A'        : 1066,
    'uni228E'        : 8846,
    'uni0401'        : 1025,
    'adieresis'      : 228,
    'uni0403'        : 1027,
    'quotesingle'    : 39,
    'uni0405'        : 1029,
    'uni0404'        : 1028,
    'uni0407'        : 1031,
    'uni0406'        : 1030,
    'uni229C'        : 8860,
    'uni2306'        : 8966,
    'uni2253'        : 8787,
    'twodotenleader' : 8229,
    'uni2131'        : 8497,
    'uni21DA'        : 8666,
    'uni2234'        : 8756,
    'uni2235'        : 8757,
    'uni01A5'        : 421,
    'uni2237'        : 8759,
    'uni2230'        : 8752,
    'uni02CC'        : 716,
    'slash'          : 47,
    'uni01A0'        : 416,
    'ellipsis'       : 8230,
    'uni2299'        : 8857,
    'uni2238'        : 8760,
    'numbersign'     : 35,
    'uni21A8'        : 8616,
    'uni223D'        : 8765,
    'uni01AF'        : 431,
    'uni223F'        : 8767,
    'uni01AD'        : 429,
    'uni01AB'        : 427,
    'odieresis'      : 246,
    'uni223C'        : 8764,
    'uni227D'        : 8829,
    'uni0280'        : 640,
    'O'              : 79,
    'uni227E'        : 8830,
    'uni21A5'        : 8613,
    'uni22D4'        : 8916,
    'uni25D4'        : 9684,
    'uni227F'        : 8831,
    'uni0435'        : 1077,
    'uni2302'        : 8962,
    'uni2669'        : 9833,
    'uni24E3'        : 9443,
    'uni2720'        : 10016,
    'uni22A8'        : 8872,
    'uni22A9'        : 8873,
    'uni040A'        : 1034,
    'uni22A7'        : 8871,
    'oe'             : 339,
    'uni040B'        : 1035,
    'uni040E'        : 1038,
    'uni22A3'        : 8867,
    'o'              : 111,
    'uni040F'        : 1039,
    'Edieresis'      : 203,
    'uni25D5'        : 9685,
    'plus'           : 43,
    'uni044D'        : 1101,
    'uni263C'        : 9788,
    'uni22E6'        : 8934,
    'uni2283'        : 8835,
    'uni258C'        : 9612,
    'uni219E'        : 8606,
    'uni24E4'        : 9444,
    'uni2136'        : 8502,
    'dagger'         : 8224,
    'uni24B7'        : 9399,
    'uni219B'        : 8603,
    'uni22E5'        : 8933,
    'three'          : 51,
    'uni210B'        : 8459,
    'uni2534'        : 9524,
    'uni24B8'        : 9400,
    'uni230A'        : 8970,
    'hungarumlaut'   : 733,
    'parenleft'      : 40,
    'uni0148'        : 328,
    'uni0149'        : 329,
    'uni2124'        : 8484,
    'uni2125'        : 8485,
    'uni2126'        : 8486,
    'uni2127'        : 8487,
    'uni0140'        : 320,
    'uni2129'        : 8489,
    'uni25C5'        : 9669,
    'uni0143'        : 323,
    'uni0144'        : 324,
    'uni0145'        : 325,
    'uni0146'        : 326,
    'uni0147'        : 327,
    'uni210D'        : 8461,
    'fraction'       : 8260,
    'uni2031'        : 8241,
    'uni2196'        : 8598,
    'uni2035'        : 8245,
    'uni24E6'        : 9446,
    'uni016B'        : 363,
    'uni24BA'        : 9402,
    'uni266A'        : 9834,
    'uni0116'        : 278,
    'uni2115'        : 8469,
    'registered'     : 174,
    'J'              : 74,
    'uni25DF'        : 9695,
    'uni25CE'        : 9678,
    'uni273D'        : 10045,
    'dieresis'       : 168,
    'uni212B'        : 8491,
    'uni0114'        : 276,
    'uni212D'        : 8493,
    'uni212E'        : 8494,
    'uni212F'        : 8495,
    'uni014A'        : 330,
    'uni014B'        : 331,
    'uni014C'        : 332,
    'uni014D'        : 333,
    'uni014E'        : 334,
    'uni014F'        : 335,
    'uni025E'        : 606,
    'uni24E8'        : 9448,
    'uni0111'        : 273,
    'uni24E9'        : 9449,
    'Ograve'         : 210,
    'j'              : 106,
    'uni2195'        : 8597,
    'uni2194'        : 8596,
    'uni2197'        : 8599,
    'uni2037'        : 8247,
    'uni2191'        : 8593,
    'uni2190'        : 8592,
    'uni2193'        : 8595,
    'uni2192'        : 8594,
    'uni29FA'        : 10746,
    'uni2713'        : 10003,
    'z'              : 122,
    'uni2199'        : 8601,
    'uni2198'        : 8600,
    'uni2667'        : 9831,
    'ae'             : 230,
    'uni0448'        : 1096,
    'semicolon'      : 59,
    'uni2666'        : 9830,
    'uni038F'        : 911,
    'uni0444'        : 1092,
    'uni0447'        : 1095,
    'uni038E'        : 910,
    'uni0441'        : 1089,
    'uni038C'        : 908,
    'uni0443'        : 1091,
    'uni038A'        : 906,
    'uni0250'        : 592,
    'uni0251'        : 593,
    'uni0252'        : 594,
    'uni0253'        : 595,
    'uni0254'        : 596,
    'at'             : 64,
    'uni0256'        : 598,
    'uni0257'        : 599,
    'uni0167'        : 359,
    'uni0259'        : 601,
    'uni228C'        : 8844,
    'uni2662'        : 9826,
    'uni0319'        : 793,
    'uni0318'        : 792,
    'uni24BC'        : 9404,
    'uni0402'        : 1026,
    'uni22EF'        : 8943,
    'Iacute'         : 205,
    'uni22ED'        : 8941,
    'uni22EE'        : 8942,
    'uni0311'        : 785,
    'uni0310'        : 784,
    'uni21E8'        : 8680,
    'uni0312'        : 786,
    'percent'        : 37,
    'uni0317'        : 791,
    'uni0316'        : 790,
    'uni21D6'        : 8662,
    'uni21D7'        : 8663,
    'uni21D4'        : 8660,
    'uni21D5'        : 8661,
    'uni21D2'        : 8658,
    'uni21D3'        : 8659,
    'uni21D0'        : 8656,
    'uni2138'        : 8504,
    'uni2270'        : 8816,
    'uni2271'        : 8817,
    'uni2272'        : 8818,
    'uni2273'        : 8819,
    'uni2274'        : 8820,
    'uni2275'        : 8821,
    'bracketright'   : 93,
    'uni21D9'        : 8665,
    'uni21DF'        : 8671,
    'uni21DD'        : 8669,
    'uni21DE'        : 8670,
    'AE'             : 198,
    'uni03AE'        : 942,
    'uni227A'        : 8826,
    'uni227B'        : 8827,
    'uni227C'        : 8828,
    'asterisk'       : 42,
    'aacute'         : 225,
    'uni226F'        : 8815,
    'uni22E2'        : 8930,
    'uni0386'        : 902,
    'uni22E0'        : 8928,
    'uni22E1'        : 8929,
    'U'              : 85,
    'uni22E7'        : 8935,
    'uni22E4'        : 8932,
    'uni0387'        : 903,
    'uni031A'        : 794,
    'eacute'         : 233,
    'uni22E8'        : 8936,
    'uni22E9'        : 8937,
    'uni24D8'        : 9432,
    'uni025A'        : 602,
    'uni025B'        : 603,
    'uni025C'        : 604,
    'e'              : 101,
    'uni0128'        : 296,
    'uni025F'        : 607,
    'uni2665'        : 9829,
    'thorn'          : 254,
    'uni0129'        : 297,
    'uni253C'        : 9532,
    'uni25D7'        : 9687,
    'u'              : 117,
    'uni0388'        : 904,
    'uni0389'        : 905,
    'uni0255'        : 597,
    'uni0171'        : 369,
    'uni0384'        : 900,
    'uni0385'        : 901,
    'uni044A'        : 1098,
    'uni252C'        : 9516,
    'uni044C'        : 1100,
    'uni044B'        : 1099
}

uni2type1 = {v: k for k, v in type12uni.items()}

tex2uni = {
    'widehat'                  : 0x0302,
    'widetilde'                : 0x0303,
    'widebar'                  : 0x0305,
    'langle'                   : 0x27e8,
    'rangle'                   : 0x27e9,
    'perp'                     : 0x27c2,
    'neq'                      : 0x2260,
    'Join'                     : 0x2a1d,
    'leqslant'                 : 0x2a7d,
    'geqslant'                 : 0x2a7e,
    'lessapprox'               : 0x2a85,
    'gtrapprox'                : 0x2a86,
    'lesseqqgtr'               : 0x2a8b,
    'gtreqqless'               : 0x2a8c,
    'triangleeq'               : 0x225c,
    'eqslantless'              : 0x2a95,
    'eqslantgtr'               : 0x2a96,
    'backepsilon'              : 0x03f6,
    'precapprox'               : 0x2ab7,
    'succapprox'               : 0x2ab8,
    'fallingdotseq'            : 0x2252,
    'subseteqq'                : 0x2ac5,
    'supseteqq'                : 0x2ac6,
    'varpropto'                : 0x221d,
    'precnapprox'              : 0x2ab9,
    'succnapprox'              : 0x2aba,
    'subsetneqq'               : 0x2acb,
    'supsetneqq'               : 0x2acc,
    'lnapprox'                 : 0x2ab9,
    'gnapprox'                 : 0x2aba,
    'longleftarrow'            : 0x27f5,
    'longrightarrow'           : 0x27f6,
    'longleftrightarrow'       : 0x27f7,
    'Longleftarrow'            : 0x27f8,
    'Longrightarrow'           : 0x27f9,
    'Longleftrightarrow'       : 0x27fa,
    'longmapsto'               : 0x27fc,
    'leadsto'                  : 0x21dd,
    'dashleftarrow'            : 0x290e,
    'dashrightarrow'           : 0x290f,
    'circlearrowleft'          : 0x21ba,
    'circlearrowright'         : 0x21bb,
    'leftrightsquigarrow'      : 0x21ad,
    'leftsquigarrow'           : 0x219c,
    'rightsquigarrow'          : 0x219d,
    'Game'                     : 0x2141,
    'hbar'                     : 0x0127,
    'hslash'                   : 0x210f,
    'ldots'                    : 0x2026,
    'vdots'                    : 0x22ee,
    'doteqdot'                 : 0x2251,
    'doteq'                    : 8784,
    'partial'                  : 8706,
    'gg'                       : 8811,
    'asymp'                    : 8781,
    'blacktriangledown'        : 9662,
    'otimes'                   : 8855,
    'nearrow'                  : 8599,
    'varpi'                    : 982,
    'vee'                      : 8744,
    'vec'                      : 8407,
    'smile'                    : 8995,
    'succnsim'                 : 8937,
    'gimel'                    : 8503,
    'vert'                     : 124,
    '|'                        : 124,
    'varrho'                   : 1009,
    'P'                        : 182,
    'approxident'              : 8779,
    'Swarrow'                  : 8665,
    'textasciicircum'          : 94,
    'imageof'                  : 8887,
    'ntriangleleft'            : 8938,
    'nleq'                     : 8816,
    'div'                      : 247,
    'nparallel'                : 8742,
    'Leftarrow'                : 8656,
    'lll'                      : 8920,
    'oiint'                    : 8751,
    'ngeq'                     : 8817,
    'Theta'                    : 920,
    'origof'                   : 8886,
    'blacksquare'              : 9632,
    'solbar'                   : 9023,
    'neg'                      : 172,
    'sum'                      : 8721,
    'Vdash'                    : 8873,
    'coloneq'                  : 8788,
    'degree'                   : 176,
    'bowtie'                   : 8904,
    'blacktriangleright'       : 9654,
    'varsigma'                 : 962,
    'leq'                      : 8804,
    'ggg'                      : 8921,
    'lneqq'                    : 8808,
    'scurel'                   : 8881,
    'stareq'                   : 8795,
    'BbbN'                     : 8469,
    'nLeftarrow'               : 8653,
    'nLeftrightarrow'          : 8654,
    'k'                        : 808,
    'bot'                      : 8869,
    'BbbC'                     : 8450,
    'Lsh'                      : 8624,
    'leftleftarrows'           : 8647,
    'BbbZ'                     : 8484,
    'digamma'                  : 989,
    'BbbR'                     : 8477,
    'BbbP'                     : 8473,
    'BbbQ'                     : 8474,
    'vartriangleright'         : 8883,
    'succsim'                  : 8831,
    'wedge'                    : 8743,
    'lessgtr'                  : 8822,
    'veebar'                   : 8891,
    'mapsdown'                 : 8615,
    'Rsh'                      : 8625,
    'chi'                      : 967,
    'prec'                     : 8826,
    'nsubseteq'                : 8840,
    'therefore'                : 8756,
    'eqcirc'                   : 8790,
    'textexclamdown'           : 161,
    'nRightarrow'              : 8655,
    'flat'                     : 9837,
    'notin'                    : 8713,
    'llcorner'                 : 8990,
    'varepsilon'               : 949,
    'bigtriangleup'            : 9651,
    'aleph'                    : 8501,
    'dotminus'                 : 8760,
    'upsilon'                  : 965,
    'Lambda'                   : 923,
    'cap'                      : 8745,
    'barleftarrow'             : 8676,
    'mu'                       : 956,
    'boxplus'                  : 8862,
    'mp'                       : 8723,
    'circledast'               : 8859,
    'tau'                      : 964,
    'in'                       : 8712,
    'backslash'                : 92,
    'varnothing'               : 8709,
    'sharp'                    : 9839,
    'eqsim'                    : 8770,
    'gnsim'                    : 8935,
    'Searrow'                  : 8664,
    'updownarrows'             : 8645,
    'heartsuit'                : 9825,
    'trianglelefteq'           : 8884,
    'ddag'                     : 8225,
    'sqsubseteq'               : 8849,
    'mapsfrom'                 : 8612,
    'boxbar'                   : 9707,
    'sim'                      : 8764,
    'Nwarrow'                  : 8662,
    'nequiv'                   : 8802,
    'succ'                     : 8827,
    'vdash'                    : 8866,
    'Leftrightarrow'           : 8660,
    'parallel'                 : 8741,
    'invnot'                   : 8976,
    'natural'                  : 9838,
    'ss'                       : 223,
    'uparrow'                  : 8593,
    'nsim'                     : 8769,
    'hookrightarrow'           : 8618,
    'Equiv'                    : 8803,
    'approx'                   : 8776,
    'Vvdash'                   : 8874,
    'nsucc'                    : 8833,
    'leftrightharpoons'        : 8651,
    'Re'                       : 8476,
    'boxminus'                 : 8863,
    'equiv'                    : 8801,
    'Lleftarrow'               : 8666,
    'll'                       : 8810,
    'Cup'                      : 8915,
    'measeq'                   : 8798,
    'upharpoonleft'            : 8639,
    'lq'                       : 8216,
    'Upsilon'                  : 933,
    'subsetneq'                : 8842,
    'greater'                  : 62,
    'supsetneq'                : 8843,
    'Cap'                      : 8914,
    'L'                        : 321,
    'spadesuit'                : 9824,
    'lrcorner'                 : 8991,
    'not'                      : 824,
    'bar'                      : 772,
    'rightharpoonaccent'       : 8401,
    'boxdot'                   : 8865,
    'l'                        : 322,
    'leftharpoondown'          : 8637,
    'bigcup'                   : 8899,
    'iint'                     : 8748,
    'bigwedge'                 : 8896,
    'downharpoonleft'          : 8643,
    'textasciitilde'           : 126,
    'subset'                   : 8834,
    'leqq'                     : 8806,
    'mapsup'                   : 8613,
    'nvDash'                   : 8877,
    'looparrowleft'            : 8619,
    'nless'                    : 8814,
    'rightarrowbar'            : 8677,
    'Vert'                     : 8214,
    'downdownarrows'           : 8650,
    'uplus'                    : 8846,
    'simeq'                    : 8771,
    'napprox'                  : 8777,
    'ast'                      : 8727,
    'twoheaduparrow'           : 8607,
    'doublebarwedge'           : 8966,
    'Sigma'                    : 931,
    'leftharpoonaccent'        : 8400,
    'ntrianglelefteq'          : 8940,
    'nexists'                  : 8708,
    'times'                    : 215,
    'measuredangle'            : 8737,
    'bumpeq'                   : 8783,
    'carriagereturn'           : 8629,
    'adots'                    : 8944,
    'checkmark'                : 10003,
    'lambda'                   : 955,
    'xi'                       : 958,
    'rbrace'                   : 125,
    'rbrack'                   : 93,
    'Nearrow'                  : 8663,
    'maltese'                  : 10016,
    'clubsuit'                 : 9827,
    'top'                      : 8868,
    'overarc'                  : 785,
    'varphi'                   : 966,
    'Delta'                    : 916,
    'iota'                     : 953,
    'nleftarrow'               : 8602,
    'candra'                   : 784,
    'supset'                   : 8835,
    'triangleleft'             : 9665,
    'gtreqless'                : 8923,
    'ntrianglerighteq'         : 8941,
    'quad'                     : 8195,
    'Xi'                       : 926,
    'gtrdot'                   : 8919,
    'leftthreetimes'           : 8907,
    'minus'                    : 8722,
    'preccurlyeq'              : 8828,
    'nleftrightarrow'          : 8622,
    'lambdabar'                : 411,
    'blacktriangle'            : 9652,
    'kernelcontraction'        : 8763,
    'Phi'                      : 934,
    'angle'                    : 8736,
    'spadesuitopen'            : 9828,
    'eqless'                   : 8924,
    'mid'                      : 8739,
    'varkappa'                 : 1008,
    'Ldsh'                     : 8626,
    'updownarrow'              : 8597,
    'beta'                     : 946,
    'textquotedblleft'         : 8220,
    'rho'                      : 961,
    'alpha'                    : 945,
    'intercal'                 : 8890,
    'beth'                     : 8502,
    'grave'                    : 768,
    'acwopencirclearrow'       : 8634,
    'nmid'                     : 8740,
    'nsupset'                  : 8837,
    'sigma'                    : 963,
    'dot'                      : 775,
    'Rightarrow'               : 8658,
    'turnednot'                : 8985,
    'backsimeq'                : 8909,
    'leftarrowtail'            : 8610,
    'approxeq'                 : 8778,
    'curlyeqsucc'              : 8927,
    'rightarrowtail'           : 8611,
    'Psi'                      : 936,
    'copyright'                : 169,
    'yen'                      : 165,
    'vartriangleleft'          : 8882,
    'rasp'                     : 700,
    'triangleright'            : 9655,
    'precsim'                  : 8830,
    'infty'                    : 8734,
    'geq'                      : 8805,
    'updownarrowbar'           : 8616,
    'precnsim'                 : 8936,
    'H'                        : 779,
    'ulcorner'                 : 8988,
    'looparrowright'           : 8620,
    'ncong'                    : 8775,
    'downarrow'                : 8595,
    'circeq'                   : 8791,
    'subseteq'                 : 8838,
    'bigstar'                  : 9733,
    'prime'                    : 8242,
    'lceil'                    : 8968,
    'Rrightarrow'              : 8667,
    'oiiint'                   : 8752,
    'curlywedge'               : 8911,
    'vDash'                    : 8872,
    'lfloor'                   : 8970,
    'ddots'                    : 8945,
    'exists'                   : 8707,
    'underbar'                 : 817,
    'Pi'                       : 928,
    'leftrightarrows'          : 8646,
    'sphericalangle'           : 8738,
    'coprod'                   : 8720,
    'circledcirc'              : 8858,
    'gtrsim'                   : 8819,
    'gneqq'                    : 8809,
    'between'                  : 8812,
    'theta'                    : 952,
    'complement'               : 8705,
    'arceq'                    : 8792,
    'nVdash'                   : 8878,
    'S'                        : 167,
    'wr'                       : 8768,
    'wp'                       : 8472,
    'backcong'                 : 8780,
    'lasp'                     : 701,
    'c'                        : 807,
    'nabla'                    : 8711,
    'dotplus'                  : 8724,
    'eta'                      : 951,
    'forall'                   : 8704,
    'eth'                      : 240,
    'colon'                    : 58,
    'sqcup'                    : 8852,
    'rightrightarrows'         : 8649,
    'sqsupset'                 : 8848,
    'mapsto'                   : 8614,
    'bigtriangledown'          : 9661,
    'sqsupseteq'               : 8850,
    'propto'                   : 8733,
    'pi'                       : 960,
    'pm'                       : 177,
    'dots'                     : 0x2026,
    'nrightarrow'              : 8603,
    'textasciiacute'           : 180,
    'Doteq'                    : 8785,
    'breve'                    : 774,
    'sqcap'                    : 8851,
    'twoheadrightarrow'        : 8608,
    'kappa'                    : 954,
    'vartriangle'              : 9653,
    'diamondsuit'              : 9826,
    'pitchfork'                : 8916,
    'blacktriangleleft'        : 9664,
    'nprec'                    : 8832,
    'curvearrowright'          : 8631,
    'barwedge'                 : 8892,
    'multimap'                 : 8888,
    'textquestiondown'         : 191,
    'cong'                     : 8773,
    'rtimes'                   : 8906,
    'rightzigzagarrow'         : 8669,
    'rightarrow'               : 8594,
    'leftarrow'                : 8592,
    '__sqrt__'                 : 8730,
    'twoheaddownarrow'         : 8609,
    'oint'                     : 8750,
    'bigvee'                   : 8897,
    'eqdef'                    : 8797,
    'sterling'                 : 163,
    'phi'                      : 981,
    'Updownarrow'              : 8661,
    'backprime'                : 8245,
    'emdash'                   : 8212,
    'Gamma'                    : 915,
    'i'                        : 305,
    'rceil'                    : 8969,
    'leftharpoonup'            : 8636,
    'Im'                       : 8465,
    'curvearrowleft'           : 8630,
    'wedgeq'                   : 8793,
    'curlyeqprec'              : 8926,
    'questeq'                  : 8799,
    'less'                     : 60,
    'upuparrows'               : 8648,
    'tilde'                    : 771,
    'textasciigrave'           : 96,
    'smallsetminus'            : 8726,
    'ell'                      : 8467,
    'cup'                      : 8746,
    'danger'                   : 9761,
    'nVDash'                   : 8879,
    'cdotp'                    : 183,
    'cdots'                    : 8943,
    'hat'                      : 770,
    'eqgtr'                    : 8925,
    'psi'                      : 968,
    'frown'                    : 8994,
    'acute'                    : 769,
    'downzigzagarrow'          : 8623,
    'ntriangleright'           : 8939,
    'cupdot'                   : 8845,
    'circleddash'              : 8861,
    'oslash'                   : 8856,
    'mho'                      : 8487,
    'd'                        : 803,
    'sqsubset'                 : 8847,
    'cdot'                     : 8901,
    'Omega'                    : 937,
    'OE'                       : 338,
    'veeeq'                    : 8794,
    'Finv'                     : 8498,
    't'                        : 865,
    'leftrightarrow'           : 8596,
    'swarrow'                  : 8601,
    'rightthreetimes'          : 8908,
    'rightleftharpoons'        : 8652,
    'lesssim'                  : 8818,
    'searrow'                  : 8600,
    'because'                  : 8757,
    'gtrless'                  : 8823,
    'star'                     : 8902,
    'nsubset'                  : 8836,
    'zeta'                     : 950,
    'dddot'                    : 8411,
    'bigcirc'                  : 9675,
    'Supset'                   : 8913,
    'circ'                     : 8728,
    'slash'                    : 8725,
    'ocirc'                    : 778,
    'prod'                     : 8719,
    'twoheadleftarrow'         : 8606,
    'daleth'                   : 8504,
    'upharpoonright'           : 8638,
    'odot'                     : 8857,
    'Uparrow'                  : 8657,
    'O'                        : 216,
    'hookleftarrow'            : 8617,
    'trianglerighteq'          : 8885,
    'nsime'                    : 8772,
    'oe'                       : 339,
    'nwarrow'                  : 8598,
    'o'                        : 248,
    'ddddot'                   : 8412,
    'downharpoonright'         : 8642,
    'succcurlyeq'              : 8829,
    'gamma'                    : 947,
    'scrR'                     : 8475,
    'dag'                      : 8224,
    'thickspace'               : 8197,
    'frakZ'                    : 8488,
    'lessdot'                  : 8918,
    'triangledown'             : 9663,
    'ltimes'                   : 8905,
    'scrB'                     : 8492,
    'endash'                   : 8211,
    'scrE'                     : 8496,
    'scrF'                     : 8497,
    'scrH'                     : 8459,
    'scrI'                     : 8464,
    'rightharpoondown'         : 8641,
    'scrL'                     : 8466,
    'scrM'                     : 8499,
    'frakC'                    : 8493,
    'nsupseteq'                : 8841,
    'circledR'                 : 174,
    'circledS'                 : 9416,
    'ngtr'                     : 8815,
    'bigcap'                   : 8898,
    'scre'                     : 8495,
    'Downarrow'                : 8659,
    'scrg'                     : 8458,
    'overleftrightarrow'       : 8417,
    'scro'                     : 8500,
    'lnsim'                    : 8934,
    'eqcolon'                  : 8789,
    'curlyvee'                 : 8910,
    'urcorner'                 : 8989,
    'lbrace'                   : 123,
    'Bumpeq'                   : 8782,
    'delta'                    : 948,
    'boxtimes'                 : 8864,
    'overleftarrow'            : 8406,
    'prurel'                   : 8880,
    'clubsuitopen'             : 9831,
    'cwopencirclearrow'        : 8635,
    'geqq'                     : 8807,
    'rightleftarrows'          : 8644,
    'ac'                       : 8766,
    'ae'                       : 230,
    'int'                      : 8747,
    'rfloor'                   : 8971,
    'risingdotseq'             : 8787,
    'nvdash'                   : 8876,
    'diamond'                  : 8900,
    'ddot'                     : 776,
    'backsim'                  : 8765,
    'oplus'                    : 8853,
    'triangleq'                : 8796,
    'check'                    : 780,
    'ni'                       : 8715,
    'iiint'                    : 8749,
    'ne'                       : 8800,
    'lesseqgtr'                : 8922,
    'obar'                     : 9021,
    'supseteq'                 : 8839,
    'nu'                       : 957,
    'AA'                       : 197,
    'AE'                       : 198,
    'models'                   : 8871,
    'ominus'                   : 8854,
    'dashv'                    : 8867,
    'omega'                    : 969,
    'rq'                       : 8217,
    'Subset'                   : 8912,
    'rightharpoonup'           : 8640,
    'Rdsh'                     : 8627,
    'bullet'                   : 8729,
    'divideontimes'            : 8903,
    'lbrack'                   : 91,
    'textquotedblright'        : 8221,
    'Colon'                    : 8759,
    '%'                        : 37,
    '$'                        : 36,
    '{'                        : 123,
    '}'                        : 125,
    '_'                        : 95,
    '#'                        : 35,
    'imath'                    : 0x131,
    'circumflexaccent'         : 770,
    'combiningbreve'           : 774,
    'combiningoverline'        : 772,
    'combininggraveaccent'     : 768,
    'combiningacuteaccent'     : 769,
    'combiningdiaeresis'       : 776,
    'combiningtilde'           : 771,
    'combiningrightarrowabove' : 8407,
    'combiningdotabove'        : 775,
    'to'                       : 8594,
    'succeq'                   : 8829,
    'emptyset'                 : 8709,
    'leftparen'                : 40,
    'rightparen'               : 41,
    'bigoplus'                 : 10753,
    'leftangle'                : 10216,
    'rightangle'               : 10217,
    'leftbrace'                : 124,
    'rightbrace'               : 125,
    'jmath'                    : 567,
    'bigodot'                  : 10752,
    'preceq'                   : 8828,
    'biguplus'                 : 10756,
    'epsilon'                  : 949,
    'vartheta'                 : 977,
    'bigotimes'                : 10754,
    'guillemotleft'            : 171,
    'ring'                     : 730,
    'Thorn'                    : 222,
    'guilsinglright'           : 8250,
    'perthousand'              : 8240,
    'macron'                   : 175,
    'cent'                     : 162,
    'guillemotright'           : 187,
    'equal'                    : 61,
    'asterisk'                 : 42,
    'guilsinglleft'            : 8249,
    'plus'                     : 43,
    'thorn'                    : 254,
    'dagger'                   : 8224
}

# Each element is a 4-tuple of the form:
#   src_start, src_end, dst_font, dst_start
#
stix_virtual_fonts = {
    'bb':
        {
        'rm':
            [
            (0x0030, 0x0039, 'rm', 0x1d7d8), # 0-9
            (0x0041, 0x0042, 'rm', 0x1d538), # A-B
            (0x0043, 0x0043, 'rm', 0x2102),  # C
            (0x0044, 0x0047, 'rm', 0x1d53b), # D-G
            (0x0048, 0x0048, 'rm', 0x210d),  # H
            (0x0049, 0x004d, 'rm', 0x1d540), # I-M
            (0x004e, 0x004e, 'rm', 0x2115),  # N
            (0x004f, 0x004f, 'rm', 0x1d546), # O
            (0x0050, 0x0051, 'rm', 0x2119),  # P-Q
            (0x0052, 0x0052, 'rm', 0x211d),  # R
            (0x0053, 0x0059, 'rm', 0x1d54a), # S-Y
            (0x005a, 0x005a, 'rm', 0x2124),  # Z
            (0x0061, 0x007a, 'rm', 0x1d552), # a-z
            (0x0393, 0x0393, 'rm', 0x213e),  # \Gamma
            (0x03a0, 0x03a0, 'rm', 0x213f),  # \Pi
            (0x03a3, 0x03a3, 'rm', 0x2140),  # \Sigma
            (0x03b3, 0x03b3, 'rm', 0x213d),  # \gamma
            (0x03c0, 0x03c0, 'rm', 0x213c),  # \pi
            ],
        'it':
            [
            (0x0030, 0x0039, 'rm', 0x1d7d8), # 0-9
            (0x0041, 0x0042, 'it', 0xe154),  # A-B
            (0x0043, 0x0043, 'it', 0x2102),  # C
            (0x0044, 0x0044, 'it', 0x2145),  # D
            (0x0045, 0x0047, 'it', 0xe156),  # E-G
            (0x0048, 0x0048, 'it', 0x210d),  # H
            (0x0049, 0x004d, 'it', 0xe159),  # I-M
            (0x004e, 0x004e, 'it', 0x2115),  # N
            (0x004f, 0x004f, 'it', 0xe15e),  # O
            (0x0050, 0x0051, 'it', 0x2119),  # P-Q
            (0x0052, 0x0052, 'it', 0x211d),  # R
            (0x0053, 0x0059, 'it', 0xe15f),  # S-Y
            (0x005a, 0x005a, 'it', 0x2124),  # Z
            (0x0061, 0x0063, 'it', 0xe166),  # a-c
            (0x0064, 0x0065, 'it', 0x2146),  # d-e
            (0x0066, 0x0068, 'it', 0xe169),  # f-h
            (0x0069, 0x006a, 'it', 0x2148),  # i-j
            (0x006b, 0x007a, 'it', 0xe16c),  # k-z
            (0x0393, 0x0393, 'it', 0x213e),  # \Gamma (not in beta STIX fonts)
            (0x03a0, 0x03a0, 'it', 0x213f),  # \Pi
            (0x03a3, 0x03a3, 'it', 0x2140),  # \Sigma (not in beta STIX fonts)
            (0x03b3, 0x03b3, 'it', 0x213d),  # \gamma (not in beta STIX fonts)
            (0x03c0, 0x03c0, 'it', 0x213c),  # \pi
            ],
        'bf':
            [
            (0x0030, 0x0039, 'rm', 0x1d7d8), # 0-9
            (0x0041, 0x0042, 'bf', 0xe38a),  # A-B
            (0x0043, 0x0043, 'bf', 0x2102),  # C
            (0x0044, 0x0044, 'bf', 0x2145),  # D
            (0x0045, 0x0047, 'bf', 0xe38d),  # E-G
            (0x0048, 0x0048, 'bf', 0x210d),  # H
            (0x0049, 0x004d, 'bf', 0xe390),  # I-M
            (0x004e, 0x004e, 'bf', 0x2115),  # N
            (0x004f, 0x004f, 'bf', 0xe395),  # O
            (0x0050, 0x0051, 'bf', 0x2119),  # P-Q
            (0x0052, 0x0052, 'bf', 0x211d),  # R
            (0x0053, 0x0059, 'bf', 0xe396),  # S-Y
            (0x005a, 0x005a, 'bf', 0x2124),  # Z
            (0x0061, 0x0063, 'bf', 0xe39d),  # a-c
            (0x0064, 0x0065, 'bf', 0x2146),  # d-e
            (0x0066, 0x0068, 'bf', 0xe3a2),  # f-h
            (0x0069, 0x006a, 'bf', 0x2148),  # i-j
            (0x006b, 0x007a, 'bf', 0xe3a7),  # k-z
            (0x0393, 0x0393, 'bf', 0x213e),  # \Gamma
            (0x03a0, 0x03a0, 'bf', 0x213f),  # \Pi
            (0x03a3, 0x03a3, 'bf', 0x2140),  # \Sigma
            (0x03b3, 0x03b3, 'bf', 0x213d),  # \gamma
            (0x03c0, 0x03c0, 'bf', 0x213c),  # \pi
            ],
        },
    'cal':
        [
        (0x0041, 0x005a, 'it', 0xe22d), # A-Z
        ],
    'circled':
        {
        'rm':
            [
            (0x0030, 0x0030, 'rm', 0x24ea), # 0
            (0x0031, 0x0039, 'rm', 0x2460), # 1-9
            (0x0041, 0x005a, 'rm', 0x24b6), # A-Z
            (0x0061, 0x007a, 'rm', 0x24d0)  # a-z
            ],
        'it':
            [
            (0x0030, 0x0030, 'rm', 0x24ea), # 0
            (0x0031, 0x0039, 'rm', 0x2460), # 1-9
            (0x0041, 0x005a, 'it', 0x24b6), # A-Z
            (0x0061, 0x007a, 'it', 0x24d0)  # a-z
            ],
        'bf':
            [
            (0x0030, 0x0030, 'bf', 0x24ea), # 0
            (0x0031, 0x0039, 'bf', 0x2460), # 1-9
            (0x0041, 0x005a, 'bf', 0x24b6), # A-Z
            (0x0061, 0x007a, 'bf', 0x24d0)  # a-z
            ],
        },
    'frak':
        {
        'rm':
            [
            (0x0041, 0x0042, 'rm', 0x1d504), # A-B
            (0x0043, 0x0043, 'rm', 0x212d),  # C
            (0x0044, 0x0047, 'rm', 0x1d507), # D-G
            (0x0048, 0x0048, 'rm', 0x210c),  # H
            (0x0049, 0x0049, 'rm', 0x2111),  # I
            (0x004a, 0x0051, 'rm', 0x1d50d), # J-Q
            (0x0052, 0x0052, 'rm', 0x211c),  # R
            (0x0053, 0x0059, 'rm', 0x1d516), # S-Y
            (0x005a, 0x005a, 'rm', 0x2128),  # Z
            (0x0061, 0x007a, 'rm', 0x1d51e), # a-z
            ],
        'it':
            [
            (0x0041, 0x0042, 'rm', 0x1d504), # A-B
            (0x0043, 0x0043, 'rm', 0x212d),  # C
            (0x0044, 0x0047, 'rm', 0x1d507), # D-G
            (0x0048, 0x0048, 'rm', 0x210c),  # H
            (0x0049, 0x0049, 'rm', 0x2111),  # I
            (0x004a, 0x0051, 'rm', 0x1d50d), # J-Q
            (0x0052, 0x0052, 'rm', 0x211c),  # R
            (0x0053, 0x0059, 'rm', 0x1d516), # S-Y
            (0x005a, 0x005a, 'rm', 0x2128),  # Z
            (0x0061, 0x007a, 'rm', 0x1d51e), # a-z
            ],
        'bf':
            [
            (0x0041, 0x005a, 'bf', 0x1d56c), # A-Z
            (0x0061, 0x007a, 'bf', 0x1d586), # a-z
            ],
        },
    'scr':
        [
        (0x0041, 0x0041, 'it', 0x1d49c), # A
        (0x0042, 0x0042, 'it', 0x212c),  # B
        (0x0043, 0x0044, 'it', 0x1d49e), # C-D
        (0x0045, 0x0046, 'it', 0x2130),  # E-F
        (0x0047, 0x0047, 'it', 0x1d4a2), # G
        (0x0048, 0x0048, 'it', 0x210b),  # H
        (0x0049, 0x0049, 'it', 0x2110),  # I
        (0x004a, 0x004b, 'it', 0x1d4a5), # J-K
        (0x004c, 0x004c, 'it', 0x2112),  # L
        (0x004d, 0x004d, 'it', 0x2133),  # M
        (0x004e, 0x0051, 'it', 0x1d4a9), # N-Q
        (0x0052, 0x0052, 'it', 0x211b),  # R
        (0x0053, 0x005a, 'it', 0x1d4ae), # S-Z
        (0x0061, 0x0064, 'it', 0x1d4b6), # a-d
        (0x0065, 0x0065, 'it', 0x212f),  # e
        (0x0066, 0x0066, 'it', 0x1d4bb), # f
        (0x0067, 0x0067, 'it', 0x210a),  # g
        (0x0068, 0x006e, 'it', 0x1d4bd), # h-n
        (0x006f, 0x006f, 'it', 0x2134),  # o
        (0x0070, 0x007a, 'it', 0x1d4c5), # p-z
        ],
    'sf':
        {
        'rm':
            [
            (0x0030, 0x0039, 'rm', 0x1d7e2), # 0-9
            (0x0041, 0x005a, 'rm', 0x1d5a0), # A-Z
            (0x0061, 0x007a, 'rm', 0x1d5ba), # a-z
            (0x0391, 0x03a9, 'rm', 0xe17d),  # \Alpha-\Omega
            (0x03b1, 0x03c9, 'rm', 0xe196),  # \alpha-\omega
            (0x03d1, 0x03d1, 'rm', 0xe1b0),  # theta variant
            (0x03d5, 0x03d5, 'rm', 0xe1b1),  # phi variant
            (0x03d6, 0x03d6, 'rm', 0xe1b3),  # pi variant
            (0x03f1, 0x03f1, 'rm', 0xe1b2),  # rho variant
            (0x03f5, 0x03f5, 'rm', 0xe1af),  # lunate epsilon
            (0x2202, 0x2202, 'rm', 0xe17c),  # partial differential
            ],
        'it':
            [
            # These numerals are actually upright.  We don't actually
            # want italic numerals ever.
            (0x0030, 0x0039, 'rm', 0x1d7e2), # 0-9
            (0x0041, 0x005a, 'it', 0x1d608), # A-Z
            (0x0061, 0x007a, 'it', 0x1d622), # a-z
            (0x0391, 0x03a9, 'rm', 0xe17d),  # \Alpha-\Omega
            (0x03b1, 0x03c9, 'it', 0xe1d8),  # \alpha-\omega
            (0x03d1, 0x03d1, 'it', 0xe1f2),  # theta variant
            (0x03d5, 0x03d5, 'it', 0xe1f3),  # phi variant
            (0x03d6, 0x03d6, 'it', 0xe1f5),  # pi variant
            (0x03f1, 0x03f1, 'it', 0xe1f4),  # rho variant
            (0x03f5, 0x03f5, 'it', 0xe1f1),  # lunate epsilon
            ],
        'bf':
            [
            (0x0030, 0x0039, 'bf', 0x1d7ec), # 0-9
            (0x0041, 0x005a, 'bf', 0x1d5d4), # A-Z
            (0x0061, 0x007a, 'bf', 0x1d5ee), # a-z
            (0x0391, 0x03a9, 'bf', 0x1d756), # \Alpha-\Omega
            (0x03b1, 0x03c9, 'bf', 0x1d770), # \alpha-\omega
            (0x03d1, 0x03d1, 'bf', 0x1d78b), # theta variant
            (0x03d5, 0x03d5, 'bf', 0x1d78d), # phi variant
            (0x03d6, 0x03d6, 'bf', 0x1d78f), # pi variant
            (0x03f0, 0x03f0, 'bf', 0x1d78c), # kappa variant
            (0x03f1, 0x03f1, 'bf', 0x1d78e), # rho variant
            (0x03f5, 0x03f5, 'bf', 0x1d78a), # lunate epsilon
            (0x2202, 0x2202, 'bf', 0x1d789), # partial differential
            (0x2207, 0x2207, 'bf', 0x1d76f), # \Nabla
            ],
        },
    'tt':
        [
        (0x0030, 0x0039, 'rm', 0x1d7f6), # 0-9
        (0x0041, 0x005a, 'rm', 0x1d670), # A-Z
        (0x0061, 0x007a, 'rm', 0x1d68a)  # a-z
        ],
    }
