// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/full_type.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto;
namespace tensorflow {
class FullTypeDef;
class FullTypeDefDefaultTypeInternal;
extern FullTypeDefDefaultTypeInternal _FullTypeDef_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::FullTypeDef* Arena::CreateMaybeMessage<::tensorflow::FullTypeDef>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum FullTypeId : int {
  TFT_UNSET = 0,
  TFT_VAR = 1,
  TFT_ANY = 2,
  TFT_PRODUCT = 3,
  TFT_CALLABLE = 100,
  TFT_TENSOR = 1000,
  TFT_ARRAY = 1001,
  TFT_OPTIONAL = 1002,
  TFT_DATASET = 10102,
  TFT_BOOL = 200,
  TFT_UINT8 = 201,
  TFT_UINT16 = 202,
  TFT_UINT32 = 203,
  TFT_UINT64 = 204,
  TFT_INT8 = 205,
  TFT_INT16 = 206,
  TFT_INT32 = 207,
  TFT_INT64 = 208,
  TFT_HALF = 209,
  TFT_FLOAT = 210,
  TFT_DOUBLE = 211,
  TFT_BFLOAT16 = 215,
  TFT_COMPLEX64 = 212,
  TFT_COMPLEX128 = 213,
  TFT_STRING = 214,
  FullTypeId_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  FullTypeId_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool FullTypeId_IsValid(int value);
constexpr FullTypeId FullTypeId_MIN = TFT_UNSET;
constexpr FullTypeId FullTypeId_MAX = TFT_DATASET;
constexpr int FullTypeId_ARRAYSIZE = FullTypeId_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FullTypeId_descriptor();
template<typename T>
inline const std::string& FullTypeId_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FullTypeId>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FullTypeId_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FullTypeId_descriptor(), enum_t_value);
}
inline bool FullTypeId_Parse(
    const std::string& name, FullTypeId* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FullTypeId>(
    FullTypeId_descriptor(), name, value);
}
// ===================================================================

class FullTypeDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FullTypeDef) */ {
 public:
  FullTypeDef();
  virtual ~FullTypeDef();

  FullTypeDef(const FullTypeDef& from);
  FullTypeDef(FullTypeDef&& from) noexcept
    : FullTypeDef() {
    *this = ::std::move(from);
  }

  inline FullTypeDef& operator=(const FullTypeDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline FullTypeDef& operator=(FullTypeDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FullTypeDef& default_instance();

  enum AttrCase {
    kS = 3,
    ATTR_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FullTypeDef* internal_default_instance() {
    return reinterpret_cast<const FullTypeDef*>(
               &_FullTypeDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(FullTypeDef& a, FullTypeDef& b) {
    a.Swap(&b);
  }
  inline void Swap(FullTypeDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FullTypeDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FullTypeDef* New() const final {
    return CreateMaybeMessage<FullTypeDef>(nullptr);
  }

  FullTypeDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FullTypeDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FullTypeDef& from);
  void MergeFrom(const FullTypeDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FullTypeDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FullTypeDef";
  }
  protected:
  explicit FullTypeDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArgsFieldNumber = 2,
    kTypeIdFieldNumber = 1,
    kSFieldNumber = 3,
  };
  // repeated .tensorflow.FullTypeDef args = 2;
  int args_size() const;
  void clear_args();
  ::tensorflow::FullTypeDef* mutable_args(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FullTypeDef >*
      mutable_args();
  const ::tensorflow::FullTypeDef& args(int index) const;
  ::tensorflow::FullTypeDef* add_args();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FullTypeDef >&
      args() const;

  // .tensorflow.FullTypeId type_id = 1;
  void clear_type_id();
  ::tensorflow::FullTypeId type_id() const;
  void set_type_id(::tensorflow::FullTypeId value);

  // string s = 3;
  private:
  bool has_s() const;
  public:
  void clear_s();
  const std::string& s() const;
  void set_s(const std::string& value);
  void set_s(std::string&& value);
  void set_s(const char* value);
  void set_s(const char* value, size_t size);
  std::string* mutable_s();
  std::string* release_s();
  void set_allocated_s(std::string* s);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_s();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_s(
      std::string* s);

  void clear_attr();
  AttrCase attr_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.FullTypeDef)
 private:
  class _Internal;
  void set_has_s();

  inline bool has_attr() const;
  inline void clear_has_attr();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FullTypeDef > args_;
  int type_id_;
  union AttrUnion {
    AttrUnion() {}
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr s_;
  } attr_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// FullTypeDef

// .tensorflow.FullTypeId type_id = 1;
inline void FullTypeDef::clear_type_id() {
  type_id_ = 0;
}
inline ::tensorflow::FullTypeId FullTypeDef::type_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.FullTypeDef.type_id)
  return static_cast< ::tensorflow::FullTypeId >(type_id_);
}
inline void FullTypeDef::set_type_id(::tensorflow::FullTypeId value) {
  
  type_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.FullTypeDef.type_id)
}

// repeated .tensorflow.FullTypeDef args = 2;
inline int FullTypeDef::args_size() const {
  return args_.size();
}
inline void FullTypeDef::clear_args() {
  args_.Clear();
}
inline ::tensorflow::FullTypeDef* FullTypeDef::mutable_args(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FullTypeDef.args)
  return args_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FullTypeDef >*
FullTypeDef::mutable_args() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FullTypeDef.args)
  return &args_;
}
inline const ::tensorflow::FullTypeDef& FullTypeDef::args(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FullTypeDef.args)
  return args_.Get(index);
}
inline ::tensorflow::FullTypeDef* FullTypeDef::add_args() {
  // @@protoc_insertion_point(field_add:tensorflow.FullTypeDef.args)
  return args_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FullTypeDef >&
FullTypeDef::args() const {
  // @@protoc_insertion_point(field_list:tensorflow.FullTypeDef.args)
  return args_;
}

// string s = 3;
inline bool FullTypeDef::has_s() const {
  return attr_case() == kS;
}
inline void FullTypeDef::set_has_s() {
  _oneof_case_[0] = kS;
}
inline void FullTypeDef::clear_s() {
  if (has_s()) {
    attr_.s_.Destroy(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_attr();
  }
}
inline const std::string& FullTypeDef::s() const {
  // @@protoc_insertion_point(field_get:tensorflow.FullTypeDef.s)
  if (has_s()) {
    return attr_.s_.Get();
  }
  return *&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void FullTypeDef::set_s(const std::string& value) {
  if (!has_s()) {
    clear_attr();
    set_has_s();
    attr_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  attr_.s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.FullTypeDef.s)
}
inline void FullTypeDef::set_s(std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.FullTypeDef.s)
  if (!has_s()) {
    clear_attr();
    set_has_s();
    attr_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  attr_.s_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.FullTypeDef.s)
}
inline void FullTypeDef::set_s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  if (!has_s()) {
    clear_attr();
    set_has_s();
    attr_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  attr_.s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.FullTypeDef.s)
}
inline void FullTypeDef::set_s(const char* value,
                             size_t size) {
  if (!has_s()) {
    clear_attr();
    set_has_s();
    attr_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  attr_.s_.Set(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.FullTypeDef.s)
}
inline std::string* FullTypeDef::mutable_s() {
  if (!has_s()) {
    clear_attr();
    set_has_s();
    attr_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return attr_.s_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.FullTypeDef.s)
}
inline std::string* FullTypeDef::release_s() {
  // @@protoc_insertion_point(field_release:tensorflow.FullTypeDef.s)
  if (has_s()) {
    clear_has_attr();
    return attr_.s_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void FullTypeDef::set_allocated_s(std::string* s) {
  if (has_attr()) {
    clear_attr();
  }
  if (s != nullptr) {
    set_has_s();
    attr_.s_.UnsafeSetDefault(s);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FullTypeDef.s)
}
inline std::string* FullTypeDef::unsafe_arena_release_s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FullTypeDef.s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (has_s()) {
    clear_has_attr();
    return attr_.s_.UnsafeArenaRelease(
        &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void FullTypeDef::unsafe_arena_set_allocated_s(std::string* s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (!has_s()) {
    attr_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  clear_attr();
  if (s) {
    set_has_s();
    attr_.s_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), s, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FullTypeDef.s)
}

inline bool FullTypeDef::has_attr() const {
  return attr_case() != ATTR_NOT_SET;
}
inline void FullTypeDef::clear_has_attr() {
  _oneof_case_[0] = ATTR_NOT_SET;
}
inline FullTypeDef::AttrCase FullTypeDef::attr_case() const {
  return FullTypeDef::AttrCase(_oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::FullTypeId> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::FullTypeId>() {
  return ::tensorflow::FullTypeId_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto
