# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf_estimator.python.estimator.api._v1 namespace
"""

import sys as _sys

from tensorflow_estimator.python.estimator.api._v1 import estimator

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "", public_apis=None, deprecation=True,
      has_lite=False)
