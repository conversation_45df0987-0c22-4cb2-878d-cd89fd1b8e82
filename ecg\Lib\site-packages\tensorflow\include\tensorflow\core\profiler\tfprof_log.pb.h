// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_log.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/step_stats.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[24]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
namespace tensorflow {
namespace tfprof {
class CodeDef;
class CodeDefDefaultTypeInternal;
extern CodeDefDefaultTypeInternal _CodeDef_default_instance_;
class CodeDef_Trace;
class CodeDef_TraceDefaultTypeInternal;
extern CodeDef_TraceDefaultTypeInternal _CodeDef_Trace_default_instance_;
class ExecMemory;
class ExecMemoryDefaultTypeInternal;
extern ExecMemoryDefaultTypeInternal _ExecMemory_default_instance_;
class ExecMemory_OutputMemoryEntry_DoNotUse;
class ExecMemory_OutputMemoryEntry_DoNotUseDefaultTypeInternal;
extern ExecMemory_OutputMemoryEntry_DoNotUseDefaultTypeInternal _ExecMemory_OutputMemoryEntry_DoNotUse_default_instance_;
class ExecProfile;
class ExecProfileDefaultTypeInternal;
extern ExecProfileDefaultTypeInternal _ExecProfile_default_instance_;
class ExecProfile_AcceleratorExecsEntry_DoNotUse;
class ExecProfile_AcceleratorExecsEntry_DoNotUseDefaultTypeInternal;
extern ExecProfile_AcceleratorExecsEntry_DoNotUseDefaultTypeInternal _ExecProfile_AcceleratorExecsEntry_DoNotUse_default_instance_;
class ExecProfile_CpuExecsEntry_DoNotUse;
class ExecProfile_CpuExecsEntry_DoNotUseDefaultTypeInternal;
extern ExecProfile_CpuExecsEntry_DoNotUseDefaultTypeInternal _ExecProfile_CpuExecsEntry_DoNotUse_default_instance_;
class ExecTime;
class ExecTimeDefaultTypeInternal;
extern ExecTimeDefaultTypeInternal _ExecTime_default_instance_;
class Memory;
class MemoryDefaultTypeInternal;
extern MemoryDefaultTypeInternal _Memory_default_instance_;
class OpLogEntry;
class OpLogEntryDefaultTypeInternal;
extern OpLogEntryDefaultTypeInternal _OpLogEntry_default_instance_;
class OpLogProto;
class OpLogProtoDefaultTypeInternal;
extern OpLogProtoDefaultTypeInternal _OpLogProto_default_instance_;
class OpLogProto_IdToStringEntry_DoNotUse;
class OpLogProto_IdToStringEntry_DoNotUseDefaultTypeInternal;
extern OpLogProto_IdToStringEntry_DoNotUseDefaultTypeInternal _OpLogProto_IdToStringEntry_DoNotUse_default_instance_;
class ProfileNode;
class ProfileNodeDefaultTypeInternal;
extern ProfileNodeDefaultTypeInternal _ProfileNode_default_instance_;
class ProfileNode_AttrsEntry_DoNotUse;
class ProfileNode_AttrsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_AttrsEntry_DoNotUseDefaultTypeInternal _ProfileNode_AttrsEntry_DoNotUse_default_instance_;
class ProfileNode_ExecsEntry_DoNotUse;
class ProfileNode_ExecsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_ExecsEntry_DoNotUseDefaultTypeInternal _ProfileNode_ExecsEntry_DoNotUse_default_instance_;
class ProfileNode_InputShapesEntry_DoNotUse;
class ProfileNode_InputShapesEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_InputShapesEntry_DoNotUseDefaultTypeInternal _ProfileNode_InputShapesEntry_DoNotUse_default_instance_;
class ProfileNode_InputsEntry_DoNotUse;
class ProfileNode_InputsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_InputsEntry_DoNotUseDefaultTypeInternal _ProfileNode_InputsEntry_DoNotUse_default_instance_;
class ProfileNode_OutputShapesEntry_DoNotUse;
class ProfileNode_OutputShapesEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_OutputShapesEntry_DoNotUseDefaultTypeInternal _ProfileNode_OutputShapesEntry_DoNotUse_default_instance_;
class ProfileNode_OutputsEntry_DoNotUse;
class ProfileNode_OutputsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_OutputsEntry_DoNotUseDefaultTypeInternal _ProfileNode_OutputsEntry_DoNotUse_default_instance_;
class ProfileNode_SrcOutputIndexEntry_DoNotUse;
class ProfileNode_SrcOutputIndexEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_SrcOutputIndexEntry_DoNotUseDefaultTypeInternal _ProfileNode_SrcOutputIndexEntry_DoNotUse_default_instance_;
class ProfileProto;
class ProfileProtoDefaultTypeInternal;
extern ProfileProtoDefaultTypeInternal _ProfileProto_default_instance_;
class ProfileProto_IdToStringEntry_DoNotUse;
class ProfileProto_IdToStringEntry_DoNotUseDefaultTypeInternal;
extern ProfileProto_IdToStringEntry_DoNotUseDefaultTypeInternal _ProfileProto_IdToStringEntry_DoNotUse_default_instance_;
class ProfileProto_NodesEntry_DoNotUse;
class ProfileProto_NodesEntry_DoNotUseDefaultTypeInternal;
extern ProfileProto_NodesEntry_DoNotUseDefaultTypeInternal _ProfileProto_NodesEntry_DoNotUse_default_instance_;
class Tuple;
class TupleDefaultTypeInternal;
extern TupleDefaultTypeInternal _Tuple_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tfprof::CodeDef* Arena::CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(Arena*);
template<> ::tensorflow::tfprof::CodeDef_Trace* Arena::CreateMaybeMessage<::tensorflow::tfprof::CodeDef_Trace>(Arena*);
template<> ::tensorflow::tfprof::ExecMemory* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecMemory>(Arena*);
template<> ::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ExecProfile* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecProfile>(Arena*);
template<> ::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ExecTime* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecTime>(Arena*);
template<> ::tensorflow::tfprof::Memory* Arena::CreateMaybeMessage<::tensorflow::tfprof::Memory>(Arena*);
template<> ::tensorflow::tfprof::OpLogEntry* Arena::CreateMaybeMessage<::tensorflow::tfprof::OpLogEntry>(Arena*);
template<> ::tensorflow::tfprof::OpLogProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::OpLogProto>(Arena*);
template<> ::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileProto>(Arena*);
template<> ::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::Tuple* Arena::CreateMaybeMessage<::tensorflow::tfprof::Tuple>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tfprof {

// ===================================================================

class CodeDef_Trace :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.CodeDef.Trace) */ {
 public:
  CodeDef_Trace();
  virtual ~CodeDef_Trace();

  CodeDef_Trace(const CodeDef_Trace& from);
  CodeDef_Trace(CodeDef_Trace&& from) noexcept
    : CodeDef_Trace() {
    *this = ::std::move(from);
  }

  inline CodeDef_Trace& operator=(const CodeDef_Trace& from) {
    CopyFrom(from);
    return *this;
  }
  inline CodeDef_Trace& operator=(CodeDef_Trace&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CodeDef_Trace& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CodeDef_Trace* internal_default_instance() {
    return reinterpret_cast<const CodeDef_Trace*>(
               &_CodeDef_Trace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CodeDef_Trace& a, CodeDef_Trace& b) {
    a.Swap(&b);
  }
  inline void Swap(CodeDef_Trace* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CodeDef_Trace* New() const final {
    return CreateMaybeMessage<CodeDef_Trace>(nullptr);
  }

  CodeDef_Trace* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CodeDef_Trace>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CodeDef_Trace& from);
  void MergeFrom(const CodeDef_Trace& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CodeDef_Trace* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.CodeDef.Trace";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFileFieldNumber = 1,
    kFunctionFieldNumber = 3,
    kLineFieldNumber = 4,
    kLinenoFieldNumber = 2,
    kFuncStartLineFieldNumber = 5,
    kFileIdFieldNumber = 6,
    kFunctionIdFieldNumber = 7,
    kLineIdFieldNumber = 8,
  };
  // string file = 1 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_file();
  PROTOBUF_DEPRECATED const std::string& file() const;
  PROTOBUF_DEPRECATED void set_file(const std::string& value);
  PROTOBUF_DEPRECATED void set_file(std::string&& value);
  PROTOBUF_DEPRECATED void set_file(const char* value);
  PROTOBUF_DEPRECATED void set_file(const char* value, size_t size);
  PROTOBUF_DEPRECATED std::string* mutable_file();
  PROTOBUF_DEPRECATED std::string* release_file();
  PROTOBUF_DEPRECATED void set_allocated_file(std::string* file);

  // string function = 3 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_function();
  PROTOBUF_DEPRECATED const std::string& function() const;
  PROTOBUF_DEPRECATED void set_function(const std::string& value);
  PROTOBUF_DEPRECATED void set_function(std::string&& value);
  PROTOBUF_DEPRECATED void set_function(const char* value);
  PROTOBUF_DEPRECATED void set_function(const char* value, size_t size);
  PROTOBUF_DEPRECATED std::string* mutable_function();
  PROTOBUF_DEPRECATED std::string* release_function();
  PROTOBUF_DEPRECATED void set_allocated_function(std::string* function);

  // string line = 4 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_line();
  PROTOBUF_DEPRECATED const std::string& line() const;
  PROTOBUF_DEPRECATED void set_line(const std::string& value);
  PROTOBUF_DEPRECATED void set_line(std::string&& value);
  PROTOBUF_DEPRECATED void set_line(const char* value);
  PROTOBUF_DEPRECATED void set_line(const char* value, size_t size);
  PROTOBUF_DEPRECATED std::string* mutable_line();
  PROTOBUF_DEPRECATED std::string* release_line();
  PROTOBUF_DEPRECATED void set_allocated_line(std::string* line);

  // int32 lineno = 2;
  void clear_lineno();
  ::PROTOBUF_NAMESPACE_ID::int32 lineno() const;
  void set_lineno(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 func_start_line = 5;
  void clear_func_start_line();
  ::PROTOBUF_NAMESPACE_ID::int32 func_start_line() const;
  void set_func_start_line(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int64 file_id = 6;
  void clear_file_id();
  ::PROTOBUF_NAMESPACE_ID::int64 file_id() const;
  void set_file_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 function_id = 7;
  void clear_function_id();
  ::PROTOBUF_NAMESPACE_ID::int64 function_id() const;
  void set_function_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 line_id = 8;
  void clear_line_id();
  ::PROTOBUF_NAMESPACE_ID::int64 line_id() const;
  void set_line_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.CodeDef.Trace)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr function_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr line_;
  ::PROTOBUF_NAMESPACE_ID::int32 lineno_;
  ::PROTOBUF_NAMESPACE_ID::int32 func_start_line_;
  ::PROTOBUF_NAMESPACE_ID::int64 file_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 function_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 line_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class CodeDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.CodeDef) */ {
 public:
  CodeDef();
  virtual ~CodeDef();

  CodeDef(const CodeDef& from);
  CodeDef(CodeDef&& from) noexcept
    : CodeDef() {
    *this = ::std::move(from);
  }

  inline CodeDef& operator=(const CodeDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline CodeDef& operator=(CodeDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CodeDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CodeDef* internal_default_instance() {
    return reinterpret_cast<const CodeDef*>(
               &_CodeDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CodeDef& a, CodeDef& b) {
    a.Swap(&b);
  }
  inline void Swap(CodeDef* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CodeDef* New() const final {
    return CreateMaybeMessage<CodeDef>(nullptr);
  }

  CodeDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CodeDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CodeDef& from);
  void MergeFrom(const CodeDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CodeDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.CodeDef";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef CodeDef_Trace Trace;

  // accessors -------------------------------------------------------

  enum : int {
    kTracesFieldNumber = 1,
  };
  // repeated .tensorflow.tfprof.CodeDef.Trace traces = 1;
  int traces_size() const;
  void clear_traces();
  ::tensorflow::tfprof::CodeDef_Trace* mutable_traces(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >*
      mutable_traces();
  const ::tensorflow::tfprof::CodeDef_Trace& traces(int index) const;
  ::tensorflow::tfprof::CodeDef_Trace* add_traces();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >&
      traces() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.CodeDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace > traces_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class OpLogEntry :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.OpLogEntry) */ {
 public:
  OpLogEntry();
  virtual ~OpLogEntry();

  OpLogEntry(const OpLogEntry& from);
  OpLogEntry(OpLogEntry&& from) noexcept
    : OpLogEntry() {
    *this = ::std::move(from);
  }

  inline OpLogEntry& operator=(const OpLogEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpLogEntry& operator=(OpLogEntry&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpLogEntry& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpLogEntry* internal_default_instance() {
    return reinterpret_cast<const OpLogEntry*>(
               &_OpLogEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(OpLogEntry& a, OpLogEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(OpLogEntry* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpLogEntry* New() const final {
    return CreateMaybeMessage<OpLogEntry>(nullptr);
  }

  OpLogEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpLogEntry>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpLogEntry& from);
  void MergeFrom(const OpLogEntry& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpLogEntry* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.OpLogEntry";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypesFieldNumber = 3,
    kNameFieldNumber = 1,
    kCodeDefFieldNumber = 4,
    kFloatOpsFieldNumber = 2,
  };
  // repeated string types = 3;
  int types_size() const;
  void clear_types();
  const std::string& types(int index) const;
  std::string* mutable_types(int index);
  void set_types(int index, const std::string& value);
  void set_types(int index, std::string&& value);
  void set_types(int index, const char* value);
  void set_types(int index, const char* value, size_t size);
  std::string* add_types();
  void add_types(const std::string& value);
  void add_types(std::string&& value);
  void add_types(const char* value);
  void add_types(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& types() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_types();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // .tensorflow.tfprof.CodeDef code_def = 4;
  bool has_code_def() const;
  void clear_code_def();
  const ::tensorflow::tfprof::CodeDef& code_def() const;
  ::tensorflow::tfprof::CodeDef* release_code_def();
  ::tensorflow::tfprof::CodeDef* mutable_code_def();
  void set_allocated_code_def(::tensorflow::tfprof::CodeDef* code_def);

  // int64 float_ops = 2;
  void clear_float_ops();
  ::PROTOBUF_NAMESPACE_ID::int64 float_ops() const;
  void set_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.OpLogEntry)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> types_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::tfprof::CodeDef* code_def_;
  ::PROTOBUF_NAMESPACE_ID::int64 float_ops_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class OpLogProto_IdToStringEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<OpLogProto_IdToStringEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<OpLogProto_IdToStringEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  OpLogProto_IdToStringEntry_DoNotUse();
  OpLogProto_IdToStringEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const OpLogProto_IdToStringEntry_DoNotUse& other);
  static const OpLogProto_IdToStringEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const OpLogProto_IdToStringEntry_DoNotUse*>(&_OpLogProto_IdToStringEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.OpLogProto.IdToStringEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[3];
  }

  public:
};

// -------------------------------------------------------------------

class OpLogProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.OpLogProto) */ {
 public:
  OpLogProto();
  virtual ~OpLogProto();

  OpLogProto(const OpLogProto& from);
  OpLogProto(OpLogProto&& from) noexcept
    : OpLogProto() {
    *this = ::std::move(from);
  }

  inline OpLogProto& operator=(const OpLogProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpLogProto& operator=(OpLogProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpLogProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpLogProto* internal_default_instance() {
    return reinterpret_cast<const OpLogProto*>(
               &_OpLogProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(OpLogProto& a, OpLogProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OpLogProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpLogProto* New() const final {
    return CreateMaybeMessage<OpLogProto>(nullptr);
  }

  OpLogProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpLogProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpLogProto& from);
  void MergeFrom(const OpLogProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpLogProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.OpLogProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kLogEntriesFieldNumber = 1,
    kIdToStringFieldNumber = 2,
  };
  // repeated .tensorflow.tfprof.OpLogEntry log_entries = 1;
  int log_entries_size() const;
  void clear_log_entries();
  ::tensorflow::tfprof::OpLogEntry* mutable_log_entries(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >*
      mutable_log_entries();
  const ::tensorflow::tfprof::OpLogEntry& log_entries(int index) const;
  ::tensorflow::tfprof::OpLogEntry* add_log_entries();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >&
      log_entries() const;

  // map<int64, string> id_to_string = 2;
  int id_to_string_size() const;
  void clear_id_to_string();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >&
      id_to_string() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >*
      mutable_id_to_string();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.OpLogProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry > log_entries_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      OpLogProto_IdToStringEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int64, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > id_to_string_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class ProfileProto_NodesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileProto_NodesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ProfileNode,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileProto_NodesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ProfileNode,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileProto_NodesEntry_DoNotUse();
  ProfileProto_NodesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileProto_NodesEntry_DoNotUse& other);
  static const ProfileProto_NodesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileProto_NodesEntry_DoNotUse*>(&_ProfileProto_NodesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[5];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileProto_IdToStringEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileProto_IdToStringEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileProto_IdToStringEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  ProfileProto_IdToStringEntry_DoNotUse();
  ProfileProto_IdToStringEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileProto_IdToStringEntry_DoNotUse& other);
  static const ProfileProto_IdToStringEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileProto_IdToStringEntry_DoNotUse*>(&_ProfileProto_IdToStringEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.ProfileProto.IdToStringEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[6];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ProfileProto) */ {
 public:
  ProfileProto();
  virtual ~ProfileProto();

  ProfileProto(const ProfileProto& from);
  ProfileProto(ProfileProto&& from) noexcept
    : ProfileProto() {
    *this = ::std::move(from);
  }

  inline ProfileProto& operator=(const ProfileProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileProto& operator=(ProfileProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProfileProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProfileProto* internal_default_instance() {
    return reinterpret_cast<const ProfileProto*>(
               &_ProfileProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(ProfileProto& a, ProfileProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProfileProto* New() const final {
    return CreateMaybeMessage<ProfileProto>(nullptr);
  }

  ProfileProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProfileProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProfileProto& from);
  void MergeFrom(const ProfileProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.ProfileProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kNodesFieldNumber = 1,
    kStepsFieldNumber = 3,
    kIdToStringFieldNumber = 4,
    kHasTraceFieldNumber = 2,
    kMissAcceleratorStreamFieldNumber = 5,
  };
  // map<int64, .tensorflow.tfprof.ProfileNode> nodes = 1;
  int nodes_size() const;
  void clear_nodes();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ProfileNode >&
      nodes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ProfileNode >*
      mutable_nodes();

  // repeated int64 steps = 3;
  int steps_size() const;
  void clear_steps();
  ::PROTOBUF_NAMESPACE_ID::int64 steps(int index) const;
  void set_steps(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_steps(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      steps() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_steps();

  // map<int64, string> id_to_string = 4;
  int id_to_string_size() const;
  void clear_id_to_string();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >&
      id_to_string() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >*
      mutable_id_to_string();

  // bool has_trace = 2;
  void clear_has_trace();
  bool has_trace() const;
  void set_has_trace(bool value);

  // bool miss_accelerator_stream = 5;
  void clear_miss_accelerator_stream();
  bool miss_accelerator_stream() const;
  void set_miss_accelerator_stream(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileProto_NodesEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ProfileNode,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > nodes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > steps_;
  mutable std::atomic<int> _steps_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileProto_IdToStringEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int64, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > id_to_string_;
  bool has_trace_;
  bool miss_accelerator_stream_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class ProfileNode_InputsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_InputsEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_InputsEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    0 > SuperType;
  ProfileNode_InputsEntry_DoNotUse();
  ProfileNode_InputsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_InputsEntry_DoNotUse& other);
  static const ProfileNode_InputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_InputsEntry_DoNotUse*>(&_ProfileNode_InputsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[8];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileNode_InputShapesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_InputShapesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_InputShapesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileNode_InputShapesEntry_DoNotUse();
  ProfileNode_InputShapesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_InputShapesEntry_DoNotUse& other);
  static const ProfileNode_InputShapesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_InputShapesEntry_DoNotUse*>(&_ProfileNode_InputShapesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[9];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileNode_OutputsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_OutputsEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_OutputsEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    0 > SuperType;
  ProfileNode_OutputsEntry_DoNotUse();
  ProfileNode_OutputsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_OutputsEntry_DoNotUse& other);
  static const ProfileNode_OutputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_OutputsEntry_DoNotUse*>(&_ProfileNode_OutputsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[10];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileNode_OutputShapesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_OutputShapesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_OutputShapesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileNode_OutputShapesEntry_DoNotUse();
  ProfileNode_OutputShapesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_OutputShapesEntry_DoNotUse& other);
  static const ProfileNode_OutputShapesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_OutputShapesEntry_DoNotUse*>(&_ProfileNode_OutputShapesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[11];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileNode_SrcOutputIndexEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_SrcOutputIndexEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::PROTOBUF_NAMESPACE_ID::int32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_SrcOutputIndexEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::PROTOBUF_NAMESPACE_ID::int32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    0 > SuperType;
  ProfileNode_SrcOutputIndexEntry_DoNotUse();
  ProfileNode_SrcOutputIndexEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_SrcOutputIndexEntry_DoNotUse& other);
  static const ProfileNode_SrcOutputIndexEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_SrcOutputIndexEntry_DoNotUse*>(&_ProfileNode_SrcOutputIndexEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[12];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileNode_AttrsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_AttrsEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_AttrsEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileNode_AttrsEntry_DoNotUse();
  ProfileNode_AttrsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_AttrsEntry_DoNotUse& other);
  static const ProfileNode_AttrsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_AttrsEntry_DoNotUse*>(&_ProfileNode_AttrsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.ProfileNode.AttrsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[13];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileNode_ExecsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_ExecsEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ExecProfile,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_ExecsEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ExecProfile,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileNode_ExecsEntry_DoNotUse();
  ProfileNode_ExecsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_ExecsEntry_DoNotUse& other);
  static const ProfileNode_ExecsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_ExecsEntry_DoNotUse*>(&_ProfileNode_ExecsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[14];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileNode :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ProfileNode) */ {
 public:
  ProfileNode();
  virtual ~ProfileNode();

  ProfileNode(const ProfileNode& from);
  ProfileNode(ProfileNode&& from) noexcept
    : ProfileNode() {
    *this = ::std::move(from);
  }

  inline ProfileNode& operator=(const ProfileNode& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileNode& operator=(ProfileNode&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProfileNode& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProfileNode* internal_default_instance() {
    return reinterpret_cast<const ProfileNode*>(
               &_ProfileNode_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(ProfileNode& a, ProfileNode& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileNode* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProfileNode* New() const final {
    return CreateMaybeMessage<ProfileNode>(nullptr);
  }

  ProfileNode* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProfileNode>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProfileNode& from);
  void MergeFrom(const ProfileNode& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileNode* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.ProfileNode";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kInputsFieldNumber = 2,
    kOutputsFieldNumber = 3,
    kShapeFieldNumber = 4,
    kOpTypesFieldNumber = 5,
    kAttrsFieldNumber = 11,
    kExecsFieldNumber = 12,
    kSrcOutputIndexFieldNumber = 14,
    kOutputShapesFieldNumber = 15,
    kInputShapesFieldNumber = 16,
    kNameFieldNumber = 1,
    kCanonicalDeviceFieldNumber = 6,
    kHostDeviceFieldNumber = 7,
    kOpFieldNumber = 9,
    kTraceFieldNumber = 10,
    kFloatOpsFieldNumber = 8,
    kIdFieldNumber = 13,
  };
  // map<int32, int64> inputs = 2;
  int inputs_size() const;
  void clear_inputs();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64 >&
      inputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_inputs();

  // map<int32, int64> outputs = 3;
  int outputs_size() const;
  void clear_outputs();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64 >&
      outputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_outputs();

  // repeated int64 shape = 4;
  int shape_size() const;
  void clear_shape();
  ::PROTOBUF_NAMESPACE_ID::int64 shape(int index) const;
  void set_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_shape(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_shape();

  // repeated string op_types = 5;
  int op_types_size() const;
  void clear_op_types();
  const std::string& op_types(int index) const;
  std::string* mutable_op_types(int index);
  void set_op_types(int index, const std::string& value);
  void set_op_types(int index, std::string&& value);
  void set_op_types(int index, const char* value);
  void set_op_types(int index, const char* value, size_t size);
  std::string* add_op_types();
  void add_op_types(const std::string& value);
  void add_op_types(std::string&& value);
  void add_op_types(const char* value);
  void add_op_types(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& op_types() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_op_types();

  // map<string, .tensorflow.AttrValue> attrs = 11;
  int attrs_size() const;
  void clear_attrs();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attrs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attrs();

  // map<int64, .tensorflow.tfprof.ExecProfile> execs = 12;
  int execs_size() const;
  void clear_execs();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ExecProfile >&
      execs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ExecProfile >*
      mutable_execs();

  // map<int64, int32> src_output_index = 14;
  int src_output_index_size() const;
  void clear_src_output_index();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::PROTOBUF_NAMESPACE_ID::int32 >&
      src_output_index() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_src_output_index();

  // map<int32, .tensorflow.tfprof.Tuple> output_shapes = 15;
  int output_shapes_size() const;
  void clear_output_shapes();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple >&
      output_shapes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple >*
      mutable_output_shapes();

  // map<int32, .tensorflow.tfprof.Tuple> input_shapes = 16;
  int input_shapes_size() const;
  void clear_input_shapes();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple >&
      input_shapes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple >*
      mutable_input_shapes();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // string canonical_device = 6;
  void clear_canonical_device();
  const std::string& canonical_device() const;
  void set_canonical_device(const std::string& value);
  void set_canonical_device(std::string&& value);
  void set_canonical_device(const char* value);
  void set_canonical_device(const char* value, size_t size);
  std::string* mutable_canonical_device();
  std::string* release_canonical_device();
  void set_allocated_canonical_device(std::string* canonical_device);

  // string host_device = 7;
  void clear_host_device();
  const std::string& host_device() const;
  void set_host_device(const std::string& value);
  void set_host_device(std::string&& value);
  void set_host_device(const char* value);
  void set_host_device(const char* value, size_t size);
  std::string* mutable_host_device();
  std::string* release_host_device();
  void set_allocated_host_device(std::string* host_device);

  // string op = 9;
  void clear_op();
  const std::string& op() const;
  void set_op(const std::string& value);
  void set_op(std::string&& value);
  void set_op(const char* value);
  void set_op(const char* value, size_t size);
  std::string* mutable_op();
  std::string* release_op();
  void set_allocated_op(std::string* op);

  // .tensorflow.tfprof.CodeDef trace = 10;
  bool has_trace() const;
  void clear_trace();
  const ::tensorflow::tfprof::CodeDef& trace() const;
  ::tensorflow::tfprof::CodeDef* release_trace();
  ::tensorflow::tfprof::CodeDef* mutable_trace();
  void set_allocated_trace(::tensorflow::tfprof::CodeDef* trace);

  // int64 float_ops = 8;
  void clear_float_ops();
  ::PROTOBUF_NAMESPACE_ID::int64 float_ops() const;
  void set_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 id = 13;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileNode_InputsEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      0 > inputs_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileNode_OutputsEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      0 > outputs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > shape_;
  mutable std::atomic<int> _shape_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> op_types_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileNode_AttrsEntry_DoNotUse,
      std::string, ::tensorflow::AttrValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attrs_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileNode_ExecsEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ExecProfile,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > execs_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileNode_SrcOutputIndexEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int64, ::PROTOBUF_NAMESPACE_ID::int32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      0 > src_output_index_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileNode_OutputShapesEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > output_shapes_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileNode_InputShapesEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > input_shapes_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr canonical_device_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_device_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_;
  ::tensorflow::tfprof::CodeDef* trace_;
  ::PROTOBUF_NAMESPACE_ID::int64 float_ops_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class ExecProfile_AcceleratorExecsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecProfile_AcceleratorExecsEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::ExecTime,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecProfile_AcceleratorExecsEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::ExecTime,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ExecProfile_AcceleratorExecsEntry_DoNotUse();
  ExecProfile_AcceleratorExecsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ExecProfile_AcceleratorExecsEntry_DoNotUse& other);
  static const ExecProfile_AcceleratorExecsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExecProfile_AcceleratorExecsEntry_DoNotUse*>(&_ExecProfile_AcceleratorExecsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.ExecProfile.AcceleratorExecsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[16];
  }

  public:
};

// -------------------------------------------------------------------

class ExecProfile_CpuExecsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecProfile_CpuExecsEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::ExecTime,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecProfile_CpuExecsEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::ExecTime,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ExecProfile_CpuExecsEntry_DoNotUse();
  ExecProfile_CpuExecsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ExecProfile_CpuExecsEntry_DoNotUse& other);
  static const ExecProfile_CpuExecsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExecProfile_CpuExecsEntry_DoNotUse*>(&_ExecProfile_CpuExecsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.ExecProfile.CpuExecsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[17];
  }

  public:
};

// -------------------------------------------------------------------

class ExecProfile :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ExecProfile) */ {
 public:
  ExecProfile();
  virtual ~ExecProfile();

  ExecProfile(const ExecProfile& from);
  ExecProfile(ExecProfile&& from) noexcept
    : ExecProfile() {
    *this = ::std::move(from);
  }

  inline ExecProfile& operator=(const ExecProfile& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecProfile& operator=(ExecProfile&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecProfile& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecProfile* internal_default_instance() {
    return reinterpret_cast<const ExecProfile*>(
               &_ExecProfile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(ExecProfile& a, ExecProfile& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecProfile* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecProfile* New() const final {
    return CreateMaybeMessage<ExecProfile>(nullptr);
  }

  ExecProfile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecProfile>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecProfile& from);
  void MergeFrom(const ExecProfile& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecProfile* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.ExecProfile";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAcceleratorExecsFieldNumber = 4,
    kCpuExecsFieldNumber = 5,
    kDevicesFieldNumber = 6,
    kMemoryExecsFieldNumber = 7,
    kAllocationsFieldNumber = 11,
    kRunCountFieldNumber = 1,
    kAllStartMicrosFieldNumber = 2,
    kLatestEndMicrosFieldNumber = 3,
  };
  // map<string, .tensorflow.tfprof.ExecTime> accelerator_execs = 4;
  int accelerator_execs_size() const;
  void clear_accelerator_execs();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
      accelerator_execs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
      mutable_accelerator_execs();

  // map<string, .tensorflow.tfprof.ExecTime> cpu_execs = 5;
  int cpu_execs_size() const;
  void clear_cpu_execs();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
      cpu_execs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
      mutable_cpu_execs();

  // repeated string devices = 6;
  int devices_size() const;
  void clear_devices();
  const std::string& devices(int index) const;
  std::string* mutable_devices(int index);
  void set_devices(int index, const std::string& value);
  void set_devices(int index, std::string&& value);
  void set_devices(int index, const char* value);
  void set_devices(int index, const char* value, size_t size);
  std::string* add_devices();
  void add_devices(const std::string& value);
  void add_devices(std::string&& value);
  void add_devices(const char* value);
  void add_devices(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& devices() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_devices();

  // repeated .tensorflow.tfprof.ExecMemory memory_execs = 7;
  int memory_execs_size() const;
  void clear_memory_execs();
  ::tensorflow::tfprof::ExecMemory* mutable_memory_execs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >*
      mutable_memory_execs();
  const ::tensorflow::tfprof::ExecMemory& memory_execs(int index) const;
  ::tensorflow::tfprof::ExecMemory* add_memory_execs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >&
      memory_execs() const;

  // repeated .tensorflow.AllocationRecord allocations = 11;
  int allocations_size() const;
  void clear_allocations();
  ::tensorflow::AllocationRecord* mutable_allocations(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >*
      mutable_allocations();
  const ::tensorflow::AllocationRecord& allocations(int index) const;
  ::tensorflow::AllocationRecord* add_allocations();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >&
      allocations() const;

  // int64 run_count = 1;
  void clear_run_count();
  ::PROTOBUF_NAMESPACE_ID::int64 run_count() const;
  void set_run_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 all_start_micros = 2;
  void clear_all_start_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 all_start_micros() const;
  void set_all_start_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 latest_end_micros = 3;
  void clear_latest_end_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 latest_end_micros() const;
  void set_latest_end_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecProfile)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ExecProfile_AcceleratorExecsEntry_DoNotUse,
      std::string, ::tensorflow::tfprof::ExecTime,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > accelerator_execs_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ExecProfile_CpuExecsEntry_DoNotUse,
      std::string, ::tensorflow::tfprof::ExecTime,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > cpu_execs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> devices_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory > memory_execs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord > allocations_;
  ::PROTOBUF_NAMESPACE_ID::int64 run_count_;
  ::PROTOBUF_NAMESPACE_ID::int64 all_start_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 latest_end_micros_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class ExecTime :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ExecTime) */ {
 public:
  ExecTime();
  virtual ~ExecTime();

  ExecTime(const ExecTime& from);
  ExecTime(ExecTime&& from) noexcept
    : ExecTime() {
    *this = ::std::move(from);
  }

  inline ExecTime& operator=(const ExecTime& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecTime& operator=(ExecTime&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecTime& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecTime* internal_default_instance() {
    return reinterpret_cast<const ExecTime*>(
               &_ExecTime_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(ExecTime& a, ExecTime& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecTime* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecTime* New() const final {
    return CreateMaybeMessage<ExecTime>(nullptr);
  }

  ExecTime* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecTime>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecTime& from);
  void MergeFrom(const ExecTime& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecTime* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.ExecTime";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimesFieldNumber = 1,
  };
  // repeated .tensorflow.tfprof.Tuple times = 1;
  int times_size() const;
  void clear_times();
  ::tensorflow::tfprof::Tuple* mutable_times(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::Tuple >*
      mutable_times();
  const ::tensorflow::tfprof::Tuple& times(int index) const;
  ::tensorflow::tfprof::Tuple* add_times();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::Tuple >&
      times() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecTime)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::Tuple > times_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class ExecMemory_OutputMemoryEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecMemory_OutputMemoryEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Memory,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecMemory_OutputMemoryEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Memory,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ExecMemory_OutputMemoryEntry_DoNotUse();
  ExecMemory_OutputMemoryEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ExecMemory_OutputMemoryEntry_DoNotUse& other);
  static const ExecMemory_OutputMemoryEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExecMemory_OutputMemoryEntry_DoNotUse*>(&_ExecMemory_OutputMemoryEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[20];
  }

  public:
};

// -------------------------------------------------------------------

class ExecMemory :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ExecMemory) */ {
 public:
  ExecMemory();
  virtual ~ExecMemory();

  ExecMemory(const ExecMemory& from);
  ExecMemory(ExecMemory&& from) noexcept
    : ExecMemory() {
    *this = ::std::move(from);
  }

  inline ExecMemory& operator=(const ExecMemory& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecMemory& operator=(ExecMemory&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecMemory& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecMemory* internal_default_instance() {
    return reinterpret_cast<const ExecMemory*>(
               &_ExecMemory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(ExecMemory& a, ExecMemory& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecMemory* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecMemory* New() const final {
    return CreateMaybeMessage<ExecMemory>(nullptr);
  }

  ExecMemory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecMemory>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecMemory& from);
  void MergeFrom(const ExecMemory& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecMemory* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.ExecMemory";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kOutputMemoryFieldNumber = 11,
    kMemoryMicrosFieldNumber = 1,
    kHostTempBytesFieldNumber = 2,
    kHostPersistentBytesFieldNumber = 3,
    kAcceleratorTempBytesFieldNumber = 4,
    kAcceleratorPersistentBytesFieldNumber = 5,
    kRequestedBytesFieldNumber = 6,
    kPeakBytesFieldNumber = 7,
    kResidualBytesFieldNumber = 8,
    kOutputBytesFieldNumber = 9,
    kAllocatorBytesInUseFieldNumber = 10,
  };
  // map<int32, .tensorflow.tfprof.Memory> output_memory = 11;
  int output_memory_size() const;
  void clear_output_memory();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Memory >&
      output_memory() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Memory >*
      mutable_output_memory();

  // int64 memory_micros = 1;
  void clear_memory_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 memory_micros() const;
  void set_memory_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 host_temp_bytes = 2;
  void clear_host_temp_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 host_temp_bytes() const;
  void set_host_temp_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 host_persistent_bytes = 3;
  void clear_host_persistent_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 host_persistent_bytes() const;
  void set_host_persistent_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 accelerator_temp_bytes = 4;
  void clear_accelerator_temp_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 accelerator_temp_bytes() const;
  void set_accelerator_temp_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 accelerator_persistent_bytes = 5;
  void clear_accelerator_persistent_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 accelerator_persistent_bytes() const;
  void set_accelerator_persistent_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 requested_bytes = 6;
  void clear_requested_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 requested_bytes() const;
  void set_requested_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 peak_bytes = 7;
  void clear_peak_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 peak_bytes() const;
  void set_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 residual_bytes = 8;
  void clear_residual_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 residual_bytes() const;
  void set_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 output_bytes = 9;
  void clear_output_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 output_bytes() const;
  void set_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 allocator_bytes_in_use = 10;
  void clear_allocator_bytes_in_use();
  ::PROTOBUF_NAMESPACE_ID::int64 allocator_bytes_in_use() const;
  void set_allocator_bytes_in_use(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecMemory)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ExecMemory_OutputMemoryEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Memory,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > output_memory_;
  ::PROTOBUF_NAMESPACE_ID::int64 memory_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 host_temp_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 host_persistent_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 accelerator_temp_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 accelerator_persistent_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 requested_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 peak_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 residual_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 output_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 allocator_bytes_in_use_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class Tuple :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.Tuple) */ {
 public:
  Tuple();
  virtual ~Tuple();

  Tuple(const Tuple& from);
  Tuple(Tuple&& from) noexcept
    : Tuple() {
    *this = ::std::move(from);
  }

  inline Tuple& operator=(const Tuple& from) {
    CopyFrom(from);
    return *this;
  }
  inline Tuple& operator=(Tuple&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Tuple& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Tuple* internal_default_instance() {
    return reinterpret_cast<const Tuple*>(
               &_Tuple_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(Tuple& a, Tuple& b) {
    a.Swap(&b);
  }
  inline void Swap(Tuple* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Tuple* New() const final {
    return CreateMaybeMessage<Tuple>(nullptr);
  }

  Tuple* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Tuple>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Tuple& from);
  void MergeFrom(const Tuple& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Tuple* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.Tuple";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInt64ValuesFieldNumber = 1,
  };
  // repeated int64 int64_values = 1;
  int int64_values_size() const;
  void clear_int64_values();
  ::PROTOBUF_NAMESPACE_ID::int64 int64_values(int index) const;
  void set_int64_values(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_int64_values(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      int64_values() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_int64_values();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.Tuple)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > int64_values_;
  mutable std::atomic<int> _int64_values_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class Memory :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.Memory) */ {
 public:
  Memory();
  virtual ~Memory();

  Memory(const Memory& from);
  Memory(Memory&& from) noexcept
    : Memory() {
    *this = ::std::move(from);
  }

  inline Memory& operator=(const Memory& from) {
    CopyFrom(from);
    return *this;
  }
  inline Memory& operator=(Memory&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Memory& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Memory* internal_default_instance() {
    return reinterpret_cast<const Memory*>(
               &_Memory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(Memory& a, Memory& b) {
    a.Swap(&b);
  }
  inline void Swap(Memory* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Memory* New() const final {
    return CreateMaybeMessage<Memory>(nullptr);
  }

  Memory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Memory>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Memory& from);
  void MergeFrom(const Memory& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Memory* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.Memory";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBytesFieldNumber = 1,
    kPtrFieldNumber = 2,
  };
  // int64 bytes = 1;
  void clear_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 bytes() const;
  void set_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // uint64 ptr = 2;
  void clear_ptr();
  ::PROTOBUF_NAMESPACE_ID::uint64 ptr() const;
  void set_ptr(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.Memory)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 bytes_;
  ::PROTOBUF_NAMESPACE_ID::uint64 ptr_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CodeDef_Trace

// string file = 1 [deprecated = true];
inline void CodeDef_Trace::clear_file() {
  file_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& CodeDef_Trace::file() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.file)
  return file_.GetNoArena();
}
inline void CodeDef_Trace::set_file(const std::string& value) {
  
  file_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.file)
}
inline void CodeDef_Trace::set_file(std::string&& value) {
  
  file_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.CodeDef.Trace.file)
}
inline void CodeDef_Trace::set_file(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  file_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.CodeDef.Trace.file)
}
inline void CodeDef_Trace::set_file(const char* value, size_t size) {
  
  file_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.CodeDef.Trace.file)
}
inline std::string* CodeDef_Trace::mutable_file() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.Trace.file)
  return file_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* CodeDef_Trace::release_file() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.CodeDef.Trace.file)
  
  return file_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void CodeDef_Trace::set_allocated_file(std::string* file) {
  if (file != nullptr) {
    
  } else {
    
  }
  file_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), file);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.CodeDef.Trace.file)
}

// int64 file_id = 6;
inline void CodeDef_Trace::clear_file_id() {
  file_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CodeDef_Trace::file_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.file_id)
  return file_id_;
}
inline void CodeDef_Trace::set_file_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  file_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.file_id)
}

// int32 lineno = 2;
inline void CodeDef_Trace::clear_lineno() {
  lineno_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CodeDef_Trace::lineno() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.lineno)
  return lineno_;
}
inline void CodeDef_Trace::set_lineno(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  lineno_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.lineno)
}

// string function = 3 [deprecated = true];
inline void CodeDef_Trace::clear_function() {
  function_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& CodeDef_Trace::function() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.function)
  return function_.GetNoArena();
}
inline void CodeDef_Trace::set_function(const std::string& value) {
  
  function_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.function)
}
inline void CodeDef_Trace::set_function(std::string&& value) {
  
  function_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.CodeDef.Trace.function)
}
inline void CodeDef_Trace::set_function(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  function_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.CodeDef.Trace.function)
}
inline void CodeDef_Trace::set_function(const char* value, size_t size) {
  
  function_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.CodeDef.Trace.function)
}
inline std::string* CodeDef_Trace::mutable_function() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.Trace.function)
  return function_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* CodeDef_Trace::release_function() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.CodeDef.Trace.function)
  
  return function_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void CodeDef_Trace::set_allocated_function(std::string* function) {
  if (function != nullptr) {
    
  } else {
    
  }
  function_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), function);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.CodeDef.Trace.function)
}

// int64 function_id = 7;
inline void CodeDef_Trace::clear_function_id() {
  function_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CodeDef_Trace::function_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.function_id)
  return function_id_;
}
inline void CodeDef_Trace::set_function_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  function_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.function_id)
}

// string line = 4 [deprecated = true];
inline void CodeDef_Trace::clear_line() {
  line_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& CodeDef_Trace::line() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.line)
  return line_.GetNoArena();
}
inline void CodeDef_Trace::set_line(const std::string& value) {
  
  line_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.line)
}
inline void CodeDef_Trace::set_line(std::string&& value) {
  
  line_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.CodeDef.Trace.line)
}
inline void CodeDef_Trace::set_line(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  line_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.CodeDef.Trace.line)
}
inline void CodeDef_Trace::set_line(const char* value, size_t size) {
  
  line_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.CodeDef.Trace.line)
}
inline std::string* CodeDef_Trace::mutable_line() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.Trace.line)
  return line_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* CodeDef_Trace::release_line() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.CodeDef.Trace.line)
  
  return line_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void CodeDef_Trace::set_allocated_line(std::string* line) {
  if (line != nullptr) {
    
  } else {
    
  }
  line_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), line);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.CodeDef.Trace.line)
}

// int64 line_id = 8;
inline void CodeDef_Trace::clear_line_id() {
  line_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CodeDef_Trace::line_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.line_id)
  return line_id_;
}
inline void CodeDef_Trace::set_line_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  line_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.line_id)
}

// int32 func_start_line = 5;
inline void CodeDef_Trace::clear_func_start_line() {
  func_start_line_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CodeDef_Trace::func_start_line() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.func_start_line)
  return func_start_line_;
}
inline void CodeDef_Trace::set_func_start_line(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  func_start_line_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.func_start_line)
}

// -------------------------------------------------------------------

// CodeDef

// repeated .tensorflow.tfprof.CodeDef.Trace traces = 1;
inline int CodeDef::traces_size() const {
  return traces_.size();
}
inline void CodeDef::clear_traces() {
  traces_.Clear();
}
inline ::tensorflow::tfprof::CodeDef_Trace* CodeDef::mutable_traces(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.traces)
  return traces_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >*
CodeDef::mutable_traces() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.CodeDef.traces)
  return &traces_;
}
inline const ::tensorflow::tfprof::CodeDef_Trace& CodeDef::traces(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.traces)
  return traces_.Get(index);
}
inline ::tensorflow::tfprof::CodeDef_Trace* CodeDef::add_traces() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.CodeDef.traces)
  return traces_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >&
CodeDef::traces() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.CodeDef.traces)
  return traces_;
}

// -------------------------------------------------------------------

// OpLogEntry

// string name = 1;
inline void OpLogEntry::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& OpLogEntry::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.name)
  return name_.GetNoArena();
}
inline void OpLogEntry::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.name)
}
inline void OpLogEntry::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.OpLogEntry.name)
}
inline void OpLogEntry::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OpLogEntry.name)
}
inline void OpLogEntry::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OpLogEntry.name)
}
inline std::string* OpLogEntry::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogEntry.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* OpLogEntry::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OpLogEntry.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void OpLogEntry::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OpLogEntry.name)
}

// int64 float_ops = 2;
inline void OpLogEntry::clear_float_ops() {
  float_ops_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpLogEntry::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.float_ops)
  return float_ops_;
}
inline void OpLogEntry::set_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.float_ops)
}

// repeated string types = 3;
inline int OpLogEntry::types_size() const {
  return types_.size();
}
inline void OpLogEntry::clear_types() {
  types_.Clear();
}
inline const std::string& OpLogEntry::types(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.types)
  return types_.Get(index);
}
inline std::string* OpLogEntry::mutable_types(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogEntry.types)
  return types_.Mutable(index);
}
inline void OpLogEntry::set_types(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.types)
  types_.Mutable(index)->assign(value);
}
inline void OpLogEntry::set_types(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.types)
  types_.Mutable(index)->assign(std::move(value));
}
inline void OpLogEntry::set_types(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  types_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::set_types(int index, const char* value, size_t size) {
  types_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OpLogEntry.types)
}
inline std::string* OpLogEntry::add_types() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OpLogEntry.types)
  return types_.Add();
}
inline void OpLogEntry::add_types(const std::string& value) {
  types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::add_types(std::string&& value) {
  types_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::add_types(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::add_types(const char* value, size_t size) {
  types_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OpLogEntry.types)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OpLogEntry::types() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OpLogEntry.types)
  return types_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OpLogEntry::mutable_types() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OpLogEntry.types)
  return &types_;
}

// .tensorflow.tfprof.CodeDef code_def = 4;
inline bool OpLogEntry::has_code_def() const {
  return this != internal_default_instance() && code_def_ != nullptr;
}
inline void OpLogEntry::clear_code_def() {
  if (GetArenaNoVirtual() == nullptr && code_def_ != nullptr) {
    delete code_def_;
  }
  code_def_ = nullptr;
}
inline const ::tensorflow::tfprof::CodeDef& OpLogEntry::code_def() const {
  const ::tensorflow::tfprof::CodeDef* p = code_def_;
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.code_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tfprof::CodeDef*>(
      &::tensorflow::tfprof::_CodeDef_default_instance_);
}
inline ::tensorflow::tfprof::CodeDef* OpLogEntry::release_code_def() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OpLogEntry.code_def)
  
  ::tensorflow::tfprof::CodeDef* temp = code_def_;
  code_def_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* OpLogEntry::mutable_code_def() {
  
  if (code_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(GetArenaNoVirtual());
    code_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogEntry.code_def)
  return code_def_;
}
inline void OpLogEntry::set_allocated_code_def(::tensorflow::tfprof::CodeDef* code_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete code_def_;
  }
  if (code_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      code_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, code_def, submessage_arena);
    }
    
  } else {
    
  }
  code_def_ = code_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OpLogEntry.code_def)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// OpLogProto

// repeated .tensorflow.tfprof.OpLogEntry log_entries = 1;
inline int OpLogProto::log_entries_size() const {
  return log_entries_.size();
}
inline void OpLogProto::clear_log_entries() {
  log_entries_.Clear();
}
inline ::tensorflow::tfprof::OpLogEntry* OpLogProto::mutable_log_entries(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogProto.log_entries)
  return log_entries_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >*
OpLogProto::mutable_log_entries() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OpLogProto.log_entries)
  return &log_entries_;
}
inline const ::tensorflow::tfprof::OpLogEntry& OpLogProto::log_entries(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogProto.log_entries)
  return log_entries_.Get(index);
}
inline ::tensorflow::tfprof::OpLogEntry* OpLogProto::add_log_entries() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OpLogProto.log_entries)
  return log_entries_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >&
OpLogProto::log_entries() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OpLogProto.log_entries)
  return log_entries_;
}

// map<int64, string> id_to_string = 2;
inline int OpLogProto::id_to_string_size() const {
  return id_to_string_.size();
}
inline void OpLogProto::clear_id_to_string() {
  id_to_string_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >&
OpLogProto::id_to_string() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.OpLogProto.id_to_string)
  return id_to_string_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >*
OpLogProto::mutable_id_to_string() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.OpLogProto.id_to_string)
  return id_to_string_.MutableMap();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProfileProto

// map<int64, .tensorflow.tfprof.ProfileNode> nodes = 1;
inline int ProfileProto::nodes_size() const {
  return nodes_.size();
}
inline void ProfileProto::clear_nodes() {
  nodes_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ProfileNode >&
ProfileProto::nodes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileProto.nodes)
  return nodes_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ProfileNode >*
ProfileProto::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileProto.nodes)
  return nodes_.MutableMap();
}

// bool has_trace = 2;
inline void ProfileProto::clear_has_trace() {
  has_trace_ = false;
}
inline bool ProfileProto::has_trace() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileProto.has_trace)
  return has_trace_;
}
inline void ProfileProto::set_has_trace(bool value) {
  
  has_trace_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileProto.has_trace)
}

// bool miss_accelerator_stream = 5;
inline void ProfileProto::clear_miss_accelerator_stream() {
  miss_accelerator_stream_ = false;
}
inline bool ProfileProto::miss_accelerator_stream() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileProto.miss_accelerator_stream)
  return miss_accelerator_stream_;
}
inline void ProfileProto::set_miss_accelerator_stream(bool value) {
  
  miss_accelerator_stream_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileProto.miss_accelerator_stream)
}

// repeated int64 steps = 3;
inline int ProfileProto::steps_size() const {
  return steps_.size();
}
inline void ProfileProto::clear_steps() {
  steps_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ProfileProto::steps(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileProto.steps)
  return steps_.Get(index);
}
inline void ProfileProto::set_steps(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  steps_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileProto.steps)
}
inline void ProfileProto::add_steps(::PROTOBUF_NAMESPACE_ID::int64 value) {
  steps_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileProto.steps)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ProfileProto::steps() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ProfileProto.steps)
  return steps_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ProfileProto::mutable_steps() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ProfileProto.steps)
  return &steps_;
}

// map<int64, string> id_to_string = 4;
inline int ProfileProto::id_to_string_size() const {
  return id_to_string_.size();
}
inline void ProfileProto::clear_id_to_string() {
  id_to_string_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >&
ProfileProto::id_to_string() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileProto.id_to_string)
  return id_to_string_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, std::string >*
ProfileProto::mutable_id_to_string() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileProto.id_to_string)
  return id_to_string_.MutableMap();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProfileNode

// string name = 1;
inline void ProfileNode::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileNode::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.name)
  return name_.GetNoArena();
}
inline void ProfileNode::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.name)
}
inline void ProfileNode::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.ProfileNode.name)
}
inline void ProfileNode::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.name)
}
inline void ProfileNode::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.name)
}
inline std::string* ProfileNode::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileNode::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileNode::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.name)
}

// string op = 9;
inline void ProfileNode::clear_op() {
  op_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileNode::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.op)
  return op_.GetNoArena();
}
inline void ProfileNode::set_op(const std::string& value) {
  
  op_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.op)
}
inline void ProfileNode::set_op(std::string&& value) {
  
  op_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.ProfileNode.op)
}
inline void ProfileNode::set_op(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.op)
}
inline void ProfileNode::set_op(const char* value, size_t size) {
  
  op_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.op)
}
inline std::string* ProfileNode::mutable_op() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.op)
  return op_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileNode::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.op)
  
  return op_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileNode::set_allocated_op(std::string* op) {
  if (op != nullptr) {
    
  } else {
    
  }
  op_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.op)
}

// int64 id = 13;
inline void ProfileNode::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ProfileNode::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.id)
  return id_;
}
inline void ProfileNode::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.id)
}

// map<int32, int64> inputs = 2;
inline int ProfileNode::inputs_size() const {
  return inputs_.size();
}
inline void ProfileNode::clear_inputs() {
  inputs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64 >&
ProfileNode::inputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.inputs)
  return inputs_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64 >*
ProfileNode::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.inputs)
  return inputs_.MutableMap();
}

// map<int32, .tensorflow.tfprof.Tuple> input_shapes = 16;
inline int ProfileNode::input_shapes_size() const {
  return input_shapes_.size();
}
inline void ProfileNode::clear_input_shapes() {
  input_shapes_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple >&
ProfileNode::input_shapes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.input_shapes)
  return input_shapes_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple >*
ProfileNode::mutable_input_shapes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.input_shapes)
  return input_shapes_.MutableMap();
}

// map<int32, int64> outputs = 3;
inline int ProfileNode::outputs_size() const {
  return outputs_.size();
}
inline void ProfileNode::clear_outputs() {
  outputs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64 >&
ProfileNode::outputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.outputs)
  return outputs_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::int64 >*
ProfileNode::mutable_outputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.outputs)
  return outputs_.MutableMap();
}

// map<int32, .tensorflow.tfprof.Tuple> output_shapes = 15;
inline int ProfileNode::output_shapes_size() const {
  return output_shapes_.size();
}
inline void ProfileNode::clear_output_shapes() {
  output_shapes_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple >&
ProfileNode::output_shapes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.output_shapes)
  return output_shapes_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Tuple >*
ProfileNode::mutable_output_shapes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.output_shapes)
  return output_shapes_.MutableMap();
}

// map<int64, int32> src_output_index = 14;
inline int ProfileNode::src_output_index_size() const {
  return src_output_index_.size();
}
inline void ProfileNode::clear_src_output_index() {
  src_output_index_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::PROTOBUF_NAMESPACE_ID::int32 >&
ProfileNode::src_output_index() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.src_output_index)
  return src_output_index_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::PROTOBUF_NAMESPACE_ID::int32 >*
ProfileNode::mutable_src_output_index() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.src_output_index)
  return src_output_index_.MutableMap();
}

// repeated int64 shape = 4;
inline int ProfileNode::shape_size() const {
  return shape_.size();
}
inline void ProfileNode::clear_shape() {
  shape_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ProfileNode::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.shape)
  return shape_.Get(index);
}
inline void ProfileNode::set_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.shape)
}
inline void ProfileNode::add_shape(::PROTOBUF_NAMESPACE_ID::int64 value) {
  shape_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileNode.shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ProfileNode::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ProfileNode.shape)
  return shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ProfileNode::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ProfileNode.shape)
  return &shape_;
}

// repeated string op_types = 5;
inline int ProfileNode::op_types_size() const {
  return op_types_.size();
}
inline void ProfileNode::clear_op_types() {
  op_types_.Clear();
}
inline const std::string& ProfileNode::op_types(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.op_types)
  return op_types_.Get(index);
}
inline std::string* ProfileNode::mutable_op_types(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.op_types)
  return op_types_.Mutable(index);
}
inline void ProfileNode::set_op_types(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.op_types)
  op_types_.Mutable(index)->assign(value);
}
inline void ProfileNode::set_op_types(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.op_types)
  op_types_.Mutable(index)->assign(std::move(value));
}
inline void ProfileNode::set_op_types(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  op_types_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::set_op_types(int index, const char* value, size_t size) {
  op_types_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.op_types)
}
inline std::string* ProfileNode::add_op_types() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.ProfileNode.op_types)
  return op_types_.Add();
}
inline void ProfileNode::add_op_types(const std::string& value) {
  op_types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::add_op_types(std::string&& value) {
  op_types_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::add_op_types(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  op_types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::add_op_types(const char* value, size_t size) {
  op_types_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.ProfileNode.op_types)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ProfileNode::op_types() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ProfileNode.op_types)
  return op_types_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ProfileNode::mutable_op_types() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ProfileNode.op_types)
  return &op_types_;
}

// string canonical_device = 6;
inline void ProfileNode::clear_canonical_device() {
  canonical_device_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileNode::canonical_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.canonical_device)
  return canonical_device_.GetNoArena();
}
inline void ProfileNode::set_canonical_device(const std::string& value) {
  
  canonical_device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.canonical_device)
}
inline void ProfileNode::set_canonical_device(std::string&& value) {
  
  canonical_device_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.ProfileNode.canonical_device)
}
inline void ProfileNode::set_canonical_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  canonical_device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.canonical_device)
}
inline void ProfileNode::set_canonical_device(const char* value, size_t size) {
  
  canonical_device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.canonical_device)
}
inline std::string* ProfileNode::mutable_canonical_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.canonical_device)
  return canonical_device_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileNode::release_canonical_device() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.canonical_device)
  
  return canonical_device_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileNode::set_allocated_canonical_device(std::string* canonical_device) {
  if (canonical_device != nullptr) {
    
  } else {
    
  }
  canonical_device_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), canonical_device);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.canonical_device)
}

// string host_device = 7;
inline void ProfileNode::clear_host_device() {
  host_device_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileNode::host_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.host_device)
  return host_device_.GetNoArena();
}
inline void ProfileNode::set_host_device(const std::string& value) {
  
  host_device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.host_device)
}
inline void ProfileNode::set_host_device(std::string&& value) {
  
  host_device_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.ProfileNode.host_device)
}
inline void ProfileNode::set_host_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  host_device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.host_device)
}
inline void ProfileNode::set_host_device(const char* value, size_t size) {
  
  host_device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.host_device)
}
inline std::string* ProfileNode::mutable_host_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.host_device)
  return host_device_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileNode::release_host_device() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.host_device)
  
  return host_device_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileNode::set_allocated_host_device(std::string* host_device) {
  if (host_device != nullptr) {
    
  } else {
    
  }
  host_device_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), host_device);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.host_device)
}

// int64 float_ops = 8;
inline void ProfileNode::clear_float_ops() {
  float_ops_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ProfileNode::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.float_ops)
  return float_ops_;
}
inline void ProfileNode::set_float_ops(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.float_ops)
}

// .tensorflow.tfprof.CodeDef trace = 10;
inline bool ProfileNode::has_trace() const {
  return this != internal_default_instance() && trace_ != nullptr;
}
inline void ProfileNode::clear_trace() {
  if (GetArenaNoVirtual() == nullptr && trace_ != nullptr) {
    delete trace_;
  }
  trace_ = nullptr;
}
inline const ::tensorflow::tfprof::CodeDef& ProfileNode::trace() const {
  const ::tensorflow::tfprof::CodeDef* p = trace_;
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.trace)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tfprof::CodeDef*>(
      &::tensorflow::tfprof::_CodeDef_default_instance_);
}
inline ::tensorflow::tfprof::CodeDef* ProfileNode::release_trace() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.trace)
  
  ::tensorflow::tfprof::CodeDef* temp = trace_;
  trace_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* ProfileNode::mutable_trace() {
  
  if (trace_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(GetArenaNoVirtual());
    trace_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.trace)
  return trace_;
}
inline void ProfileNode::set_allocated_trace(::tensorflow::tfprof::CodeDef* trace) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete trace_;
  }
  if (trace) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      trace = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, trace, submessage_arena);
    }
    
  } else {
    
  }
  trace_ = trace;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.trace)
}

// map<string, .tensorflow.AttrValue> attrs = 11;
inline int ProfileNode::attrs_size() const {
  return attrs_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
ProfileNode::attrs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.attrs)
  return attrs_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
ProfileNode::mutable_attrs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.attrs)
  return attrs_.MutableMap();
}

// map<int64, .tensorflow.tfprof.ExecProfile> execs = 12;
inline int ProfileNode::execs_size() const {
  return execs_.size();
}
inline void ProfileNode::clear_execs() {
  execs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ExecProfile >&
ProfileNode::execs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.execs)
  return execs_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int64, ::tensorflow::tfprof::ExecProfile >*
ProfileNode::mutable_execs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.execs)
  return execs_.MutableMap();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ExecProfile

// int64 run_count = 1;
inline void ExecProfile::clear_run_count() {
  run_count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecProfile::run_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.run_count)
  return run_count_;
}
inline void ExecProfile::set_run_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  run_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.run_count)
}

// int64 all_start_micros = 2;
inline void ExecProfile::clear_all_start_micros() {
  all_start_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecProfile::all_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.all_start_micros)
  return all_start_micros_;
}
inline void ExecProfile::set_all_start_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  all_start_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.all_start_micros)
}

// int64 latest_end_micros = 3;
inline void ExecProfile::clear_latest_end_micros() {
  latest_end_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecProfile::latest_end_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.latest_end_micros)
  return latest_end_micros_;
}
inline void ExecProfile::set_latest_end_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  latest_end_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.latest_end_micros)
}

// map<string, .tensorflow.tfprof.ExecTime> accelerator_execs = 4;
inline int ExecProfile::accelerator_execs_size() const {
  return accelerator_execs_.size();
}
inline void ExecProfile::clear_accelerator_execs() {
  accelerator_execs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
ExecProfile::accelerator_execs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ExecProfile.accelerator_execs)
  return accelerator_execs_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
ExecProfile::mutable_accelerator_execs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ExecProfile.accelerator_execs)
  return accelerator_execs_.MutableMap();
}

// map<string, .tensorflow.tfprof.ExecTime> cpu_execs = 5;
inline int ExecProfile::cpu_execs_size() const {
  return cpu_execs_.size();
}
inline void ExecProfile::clear_cpu_execs() {
  cpu_execs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
ExecProfile::cpu_execs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ExecProfile.cpu_execs)
  return cpu_execs_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
ExecProfile::mutable_cpu_execs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ExecProfile.cpu_execs)
  return cpu_execs_.MutableMap();
}

// repeated .tensorflow.tfprof.ExecMemory memory_execs = 7;
inline int ExecProfile::memory_execs_size() const {
  return memory_execs_.size();
}
inline void ExecProfile::clear_memory_execs() {
  memory_execs_.Clear();
}
inline ::tensorflow::tfprof::ExecMemory* ExecProfile::mutable_memory_execs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecProfile.memory_execs)
  return memory_execs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >*
ExecProfile::mutable_memory_execs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecProfile.memory_execs)
  return &memory_execs_;
}
inline const ::tensorflow::tfprof::ExecMemory& ExecProfile::memory_execs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.memory_execs)
  return memory_execs_.Get(index);
}
inline ::tensorflow::tfprof::ExecMemory* ExecProfile::add_memory_execs() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.memory_execs)
  return memory_execs_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >&
ExecProfile::memory_execs() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecProfile.memory_execs)
  return memory_execs_;
}

// repeated .tensorflow.AllocationRecord allocations = 11;
inline int ExecProfile::allocations_size() const {
  return allocations_.size();
}
inline ::tensorflow::AllocationRecord* ExecProfile::mutable_allocations(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecProfile.allocations)
  return allocations_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >*
ExecProfile::mutable_allocations() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecProfile.allocations)
  return &allocations_;
}
inline const ::tensorflow::AllocationRecord& ExecProfile::allocations(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.allocations)
  return allocations_.Get(index);
}
inline ::tensorflow::AllocationRecord* ExecProfile::add_allocations() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.allocations)
  return allocations_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >&
ExecProfile::allocations() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecProfile.allocations)
  return allocations_;
}

// repeated string devices = 6;
inline int ExecProfile::devices_size() const {
  return devices_.size();
}
inline void ExecProfile::clear_devices() {
  devices_.Clear();
}
inline const std::string& ExecProfile::devices(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.devices)
  return devices_.Get(index);
}
inline std::string* ExecProfile::mutable_devices(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecProfile.devices)
  return devices_.Mutable(index);
}
inline void ExecProfile::set_devices(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.devices)
  devices_.Mutable(index)->assign(value);
}
inline void ExecProfile::set_devices(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.devices)
  devices_.Mutable(index)->assign(std::move(value));
}
inline void ExecProfile::set_devices(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  devices_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::set_devices(int index, const char* value, size_t size) {
  devices_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ExecProfile.devices)
}
inline std::string* ExecProfile::add_devices() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.ExecProfile.devices)
  return devices_.Add();
}
inline void ExecProfile::add_devices(const std::string& value) {
  devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::add_devices(std::string&& value) {
  devices_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::add_devices(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::add_devices(const char* value, size_t size) {
  devices_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.ExecProfile.devices)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ExecProfile::devices() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecProfile.devices)
  return devices_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ExecProfile::mutable_devices() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecProfile.devices)
  return &devices_;
}

// -------------------------------------------------------------------

// ExecTime

// repeated .tensorflow.tfprof.Tuple times = 1;
inline int ExecTime::times_size() const {
  return times_.size();
}
inline void ExecTime::clear_times() {
  times_.Clear();
}
inline ::tensorflow::tfprof::Tuple* ExecTime::mutable_times(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecTime.times)
  return times_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::Tuple >*
ExecTime::mutable_times() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecTime.times)
  return &times_;
}
inline const ::tensorflow::tfprof::Tuple& ExecTime::times(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecTime.times)
  return times_.Get(index);
}
inline ::tensorflow::tfprof::Tuple* ExecTime::add_times() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecTime.times)
  return times_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::Tuple >&
ExecTime::times() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecTime.times)
  return times_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ExecMemory

// int64 memory_micros = 1;
inline void ExecMemory::clear_memory_micros() {
  memory_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecMemory::memory_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.memory_micros)
  return memory_micros_;
}
inline void ExecMemory::set_memory_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  memory_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.memory_micros)
}

// int64 host_temp_bytes = 2;
inline void ExecMemory::clear_host_temp_bytes() {
  host_temp_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecMemory::host_temp_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.host_temp_bytes)
  return host_temp_bytes_;
}
inline void ExecMemory::set_host_temp_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  host_temp_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.host_temp_bytes)
}

// int64 host_persistent_bytes = 3;
inline void ExecMemory::clear_host_persistent_bytes() {
  host_persistent_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecMemory::host_persistent_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.host_persistent_bytes)
  return host_persistent_bytes_;
}
inline void ExecMemory::set_host_persistent_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  host_persistent_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.host_persistent_bytes)
}

// int64 accelerator_temp_bytes = 4;
inline void ExecMemory::clear_accelerator_temp_bytes() {
  accelerator_temp_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecMemory::accelerator_temp_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.accelerator_temp_bytes)
  return accelerator_temp_bytes_;
}
inline void ExecMemory::set_accelerator_temp_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  accelerator_temp_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.accelerator_temp_bytes)
}

// int64 accelerator_persistent_bytes = 5;
inline void ExecMemory::clear_accelerator_persistent_bytes() {
  accelerator_persistent_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecMemory::accelerator_persistent_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.accelerator_persistent_bytes)
  return accelerator_persistent_bytes_;
}
inline void ExecMemory::set_accelerator_persistent_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  accelerator_persistent_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.accelerator_persistent_bytes)
}

// int64 requested_bytes = 6;
inline void ExecMemory::clear_requested_bytes() {
  requested_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecMemory::requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.requested_bytes)
  return requested_bytes_;
}
inline void ExecMemory::set_requested_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  requested_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.requested_bytes)
}

// int64 peak_bytes = 7;
inline void ExecMemory::clear_peak_bytes() {
  peak_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecMemory::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.peak_bytes)
  return peak_bytes_;
}
inline void ExecMemory::set_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.peak_bytes)
}

// int64 residual_bytes = 8;
inline void ExecMemory::clear_residual_bytes() {
  residual_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecMemory::residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.residual_bytes)
  return residual_bytes_;
}
inline void ExecMemory::set_residual_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.residual_bytes)
}

// int64 output_bytes = 9;
inline void ExecMemory::clear_output_bytes() {
  output_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecMemory::output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.output_bytes)
  return output_bytes_;
}
inline void ExecMemory::set_output_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.output_bytes)
}

// int64 allocator_bytes_in_use = 10;
inline void ExecMemory::clear_allocator_bytes_in_use() {
  allocator_bytes_in_use_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExecMemory::allocator_bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.allocator_bytes_in_use)
  return allocator_bytes_in_use_;
}
inline void ExecMemory::set_allocator_bytes_in_use(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  allocator_bytes_in_use_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.allocator_bytes_in_use)
}

// map<int32, .tensorflow.tfprof.Memory> output_memory = 11;
inline int ExecMemory::output_memory_size() const {
  return output_memory_.size();
}
inline void ExecMemory::clear_output_memory() {
  output_memory_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Memory >&
ExecMemory::output_memory() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ExecMemory.output_memory)
  return output_memory_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::int32, ::tensorflow::tfprof::Memory >*
ExecMemory::mutable_output_memory() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ExecMemory.output_memory)
  return output_memory_.MutableMap();
}

// -------------------------------------------------------------------

// Tuple

// repeated int64 int64_values = 1;
inline int Tuple::int64_values_size() const {
  return int64_values_.size();
}
inline void Tuple::clear_int64_values() {
  int64_values_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Tuple::int64_values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.Tuple.int64_values)
  return int64_values_.Get(index);
}
inline void Tuple::set_int64_values(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  int64_values_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.Tuple.int64_values)
}
inline void Tuple::add_int64_values(::PROTOBUF_NAMESPACE_ID::int64 value) {
  int64_values_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.Tuple.int64_values)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
Tuple::int64_values() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.Tuple.int64_values)
  return int64_values_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
Tuple::mutable_int64_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.Tuple.int64_values)
  return &int64_values_;
}

// -------------------------------------------------------------------

// Memory

// int64 bytes = 1;
inline void Memory::clear_bytes() {
  bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Memory::bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.Memory.bytes)
  return bytes_;
}
inline void Memory::set_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.Memory.bytes)
}

// uint64 ptr = 2;
inline void Memory::clear_ptr() {
  ptr_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Memory::ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.Memory.ptr)
  return ptr_;
}
inline void Memory::set_ptr(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  ptr_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.Memory.ptr)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
