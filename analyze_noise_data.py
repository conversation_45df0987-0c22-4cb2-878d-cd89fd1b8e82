#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析肖总0726异常数据为什么没有被噪音识别检测到
"""

import json
import numpy as np
from scipy import signal
from scipy.signal import find_peaks
from scipy import stats
import matplotlib.pyplot as plt

def load_ecg_data(file_path):
    """加载ECG数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 解析ECG数据
    ecg_str = data['ecg']
    ecg_data = np.array([float(x) for x in ecg_str.strip('[]').split(',')])
    
    return {
        'ecg_data': ecg_data,
        'sample_rate': data['sampleRate'],
        'zero': data['zero'],
        'gain': data['gain'],
        'start_time': data['startTime'],
        'end_time': data['endTime']
    }

def is_noise_detailed(ecg_data, sampling_rate):
    """
    详细的噪音检测函数，返回每个检测步骤的结果
    """
    results = {
        'is_noise': False,
        'reasons': [],
        'details': {}
    }
    
    if len(ecg_data) == 0:
        results['is_noise'] = True
        results['reasons'].append('Empty signal')
        return results

    # 1. 信号功率与噪声功率的比值(SNR)检测
    signal_power = np.mean(np.square(ecg_data))
    fc = 40.0
    w = fc / (sampling_rate / 2)
    b, a = signal.butter(3, w, 'high')
    noise = signal.filtfilt(b, a, ecg_data)
    noise_power = np.mean(np.square(noise))

    if noise_power > 0:
        snr = 10 * np.log10(signal_power / noise_power)
    else:
        snr = 100

    results['details']['snr'] = snr
    results['details']['signal_power'] = signal_power
    results['details']['noise_power'] = noise_power
    
    if snr < 1.5:
        results['is_noise'] = True
        results['reasons'].append(f'Low SNR: {snr:.2f} dB < 1.5 dB')

    # 2. 基线漂移检测
    fc = 0.5
    w = fc / (sampling_rate / 2)
    b, a = signal.butter(2, w, 'low')
    baseline = signal.filtfilt(b, a, ecg_data)
    baseline_var = np.std(baseline)
    signal_std = np.std(ecg_data)
    
    results['details']['baseline_var'] = baseline_var
    results['details']['signal_std'] = signal_std
    results['details']['baseline_ratio'] = baseline_var / signal_std if signal_std > 0 else 0

    if baseline_var > 0.4 * signal_std:
        results['is_noise'] = True
        results['reasons'].append(f'Baseline drift: {baseline_var:.4f} > {0.4 * signal_std:.4f}')

    # 3. QRS波群检测
    w1, w2 = 5.0 / (sampling_rate / 2), 15.0 / (sampling_rate / 2)
    b, a = signal.butter(3, [w1, w2], 'band')
    filtered_ecg = signal.filtfilt(b, a, ecg_data)

    diff_ecg = np.diff(filtered_ecg)
    squared_ecg = diff_ecg ** 2

    window_size = int(0.15 * sampling_rate)
    if window_size % 2 == 0:
        window_size += 1

    integrated_ecg = np.convolve(squared_ecg, np.ones(window_size) / window_size, mode='same')

    threshold = 0.3 * np.max(integrated_ecg)
    min_distance = int(0.2 * sampling_rate)
    peaks, _ = find_peaks(integrated_ecg, height=threshold, distance=min_distance)

    results['details']['num_peaks'] = len(peaks)
    results['details']['peaks'] = peaks.tolist()
    
    if len(peaks) < 3:
        results['is_noise'] = True
        results['reasons'].append(f'Too few R peaks: {len(peaks)} < 3')

    # 4. RR间期分析
    if len(peaks) >= 2:
        rr_intervals = np.diff(peaks) / sampling_rate * 1000  # 毫秒
        valid_rr = rr_intervals[(rr_intervals > 200) & (rr_intervals < 3000)]
        
        results['details']['rr_intervals'] = rr_intervals.tolist()
        results['details']['valid_rr_count'] = len(valid_rr)
        
        if len(valid_rr) < 5:
            results['is_noise'] = True
            results['reasons'].append(f'Too few valid RR intervals: {len(valid_rr)} < 5')
        
        if len(valid_rr) > 0:
            rr_mean = np.mean(valid_rr)
            rr_std = np.std(valid_rr)
            rr_cv = rr_std / rr_mean if rr_mean > 0 else 0
            heart_rate = 60000 / rr_mean if rr_mean > 0 else 0
            
            results['details']['rr_mean'] = rr_mean
            results['details']['rr_std'] = rr_std
            results['details']['rr_cv'] = rr_cv
            results['details']['heart_rate'] = heart_rate
            
            # 检测正常RR间期范围
            normal_rr_ranges = (valid_rr >= 600) & (valid_rr <= 1000)
            consecutive_count = 0
            max_consecutive = 0
            for is_normal in normal_rr_ranges:
                if is_normal:
                    consecutive_count += 1
                    max_consecutive = max(max_consecutive, consecutive_count)
                else:
                    consecutive_count = 0
            
            results['details']['max_consecutive_normal'] = max_consecutive
            results['details']['normal_hr_range'] = 60 <= heart_rate <= 100
            
            # 如果存在至少5个连续的正常RR间期且心率在60-100之间，认为信号质量好
            if max_consecutive >= 5 and 60 <= heart_rate <= 100:
                results['details']['good_signal_detected'] = True

    # 5. 工频干扰检测
    f, Pxx = signal.welch(ecg_data, fs=sampling_rate, nperseg=min(len(ecg_data), sampling_rate * 2))
    power_line_freq = 50
    line_idx = np.argmin(np.abs(f - power_line_freq))

    line_power = np.sum(Pxx[max(0, line_idx - 1):min(len(Pxx), line_idx + 2)])
    total_power = np.sum(Pxx)
    
    results['details']['line_power_ratio'] = line_power / total_power if total_power > 0 else 0

    if total_power > 0 and line_power / total_power > 0.4:
        results['is_noise'] = True
        results['reasons'].append(f'Power line interference: {line_power/total_power:.3f} > 0.4')

    # 6. 信号饱和检测
    range_percent = 0.05
    signal_range = np.max(ecg_data) - np.min(ecg_data)
    if signal_range > 1e-6:
        high_threshold = np.max(ecg_data) - signal_range * range_percent
        low_threshold = np.min(ecg_data) + signal_range * range_percent

        high_samples = np.sum(ecg_data > high_threshold)
        low_samples = np.sum(ecg_data < low_threshold)
        saturation_percent = (high_samples + low_samples) / len(ecg_data)
        
        results['details']['saturation_percent'] = saturation_percent

        if saturation_percent > 0.15:
            results['is_noise'] = True
            results['reasons'].append(f'Signal saturation: {saturation_percent:.3f} > 0.15')

    # 如果没有检测到噪音，返回信号质量好
    if not results['is_noise']:
        results['reasons'].append('Signal quality is good')

    return results

def plot_signal_analysis(ecg_data, sampling_rate, results):
    """绘制信号分析图"""
    fig, axes = plt.subplots(4, 1, figsize=(15, 12))
    
    # 时间轴
    time = np.arange(len(ecg_data)) / sampling_rate
    
    # 1. 原始信号
    axes[0].plot(time, ecg_data, 'b-', linewidth=0.8)
    axes[0].set_title('Original ECG Signal')
    axes[0].set_ylabel('Amplitude')
    axes[0].grid(True, alpha=0.3)
    
    # 2. 滤波后的信号和检测到的R波
    w1, w2 = 5.0 / (sampling_rate / 2), 15.0 / (sampling_rate / 2)
    b, a = signal.butter(3, [w1, w2], 'band')
    filtered_ecg = signal.filtfilt(b, a, ecg_data)
    
    axes[1].plot(time, filtered_ecg, 'g-', linewidth=0.8)
    if 'peaks' in results['details']:
        peak_times = np.array(results['details']['peaks']) / sampling_rate
        peak_values = filtered_ecg[results['details']['peaks']]
        axes[1].plot(peak_times, peak_values, 'ro', markersize=6)
    axes[1].set_title(f'Filtered Signal with R-peaks (Found: {results["details"].get("num_peaks", 0)})')
    axes[1].set_ylabel('Amplitude')
    axes[1].grid(True, alpha=0.3)
    
    # 3. 频谱分析
    f, Pxx = signal.welch(ecg_data, fs=sampling_rate, nperseg=min(len(ecg_data), sampling_rate * 2))
    axes[2].semilogy(f, Pxx, 'r-', linewidth=1)
    axes[2].axvline(x=50, color='k', linestyle='--', alpha=0.7, label='50Hz')
    axes[2].set_title('Power Spectral Density')
    axes[2].set_xlabel('Frequency (Hz)')
    axes[2].set_ylabel('PSD')
    axes[2].grid(True, alpha=0.3)
    axes[2].legend()
    
    # 4. RR间期分析
    if 'rr_intervals' in results['details'] and len(results['details']['rr_intervals']) > 0:
        rr_intervals = results['details']['rr_intervals']
        axes[3].plot(rr_intervals, 'mo-', linewidth=1, markersize=4)
        axes[3].axhline(y=600, color='g', linestyle='--', alpha=0.7, label='Normal range')
        axes[3].axhline(y=1000, color='g', linestyle='--', alpha=0.7)
        axes[3].set_title('RR Intervals')
        axes[3].set_xlabel('Beat Number')
        axes[3].set_ylabel('RR Interval (ms)')
        axes[3].grid(True, alpha=0.3)
        axes[3].legend()
    else:
        axes[3].text(0.5, 0.5, 'No RR intervals detected', 
                    transform=axes[3].transAxes, ha='center', va='center')
        axes[3].set_title('RR Intervals - None detected')
    
    plt.tight_layout()
    plt.savefig('ecg_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    # 加载数据
    file_path = r'qiniu_query_results\低电压异常数据\肖总0726异常数据.json'
    data = load_ecg_data(file_path)
    
    print("=== ECG数据信息 ===")
    print(f"采样率: {data['sample_rate']} Hz")
    print(f"数据长度: {len(data['ecg_data'])} 样本点")
    print(f"时长: {len(data['ecg_data']) / data['sample_rate']:.2f} 秒")
    print(f"Zero: {data['zero']}")
    print(f"Gain: {data['gain']}")
    print(f"信号范围: {np.min(data['ecg_data']):.6f} ~ {np.max(data['ecg_data']):.6f}")
    print(f"信号标准差: {np.std(data['ecg_data']):.6f}")
    print()
    
    # 进行噪音检测分析
    results = is_noise_detailed(data['ecg_data'], data['sample_rate'])
    
    print("=== 噪音检测结果 ===")
    print(f"是否为噪音: {'是' if results['is_noise'] else '否'}")
    print(f"检测原因: {'; '.join(results['reasons'])}")
    print()
    
    print("=== 详细分析结果 ===")
    for key, value in results['details'].items():
        if isinstance(value, (list, np.ndarray)) and len(value) > 10:
            print(f"{key}: [数组长度: {len(value)}]")
        else:
            print(f"{key}: {value}")
    print()
    
    # 绘制分析图
    plot_signal_analysis(data['ecg_data'], data['sample_rate'], results)
    
    # 分析为什么没有被识别为噪音
    print("=== 分析总结 ===")
    if not results['is_noise']:
        print("该信号没有被识别为噪音的可能原因：")
        
        # 检查各项指标
        if results['details'].get('snr', 0) >= 1.5:
            print(f"✓ SNR正常: {results['details']['snr']:.2f} dB >= 1.5 dB")
        
        if results['details'].get('baseline_ratio', 0) <= 0.4:
            print(f"✓ 基线漂移正常: {results['details']['baseline_ratio']:.3f} <= 0.4")
        
        if results['details'].get('num_peaks', 0) >= 3:
            print(f"✓ R波检测正常: {results['details']['num_peaks']} >= 3")
        
        if results['details'].get('valid_rr_count', 0) >= 5:
            print(f"✓ 有效RR间期数量正常: {results['details']['valid_rr_count']} >= 5")
        
        if results['details'].get('line_power_ratio', 0) <= 0.4:
            print(f"✓ 工频干扰正常: {results['details']['line_power_ratio']:.3f} <= 0.4")
        
        if results['details'].get('saturation_percent', 0) <= 0.15:
            print(f"✓ 信号饱和正常: {results['details']['saturation_percent']:.3f} <= 0.15")
        
        print("\n建议改进措施：")
        print("1. 检查信号的实际质量，可能需要更严格的噪音检测标准")
        print("2. 考虑添加更多的噪音检测特征，如信号的形态学特征")
        print("3. 分析信号的时域和频域特征，寻找异常模式")
        print("4. 考虑使用机器学习方法进行噪音检测")

if __name__ == "__main__":
    main()
