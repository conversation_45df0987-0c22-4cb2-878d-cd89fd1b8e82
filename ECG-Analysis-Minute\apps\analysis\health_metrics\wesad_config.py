"""
WESAD HRV分类器配置文件
包含基于文献的HRV阈值和WESAD状态映射配置
"""

# 基于国际HRV标准的年龄分层阈值 (参考<PERSON> et al. 2010, <PERSON><PERSON><PERSON> & G<PERSON>berg 2017)
AGE_STRATIFIED_THRESHOLDS = {
    'young_adult': {  # 18-30岁
        'age_range': (18, 30),
        'sdnn': {'low': 25, 'normal': 55, 'high': 120},
        'rmssd': {'low': 20, 'normal': 50, 'high': 100},
        'pnn50': {'low': 5, 'normal': 20, 'high': 40},
        'lf_hf_ratio': {'low': 0.5, 'normal': 2.0, 'high': 4.0}
    },
    'middle_aged': {  # 31-50岁
        'age_range': (31, 50),
        'sdnn': {'low': 20, 'normal': 45, 'high': 100},
        'rmssd': {'low': 15, 'normal': 40, 'high': 80},
        'pnn50': {'low': 3, 'normal': 15, 'high': 30},
        'lf_hf_ratio': {'low': 0.5, 'normal': 2.5, 'high': 5.0}
    },
    'older_adult': {  # 51+岁
        'age_range': (51, 100),
        'sdnn': {'low': 15, 'normal': 35, 'high': 80},
        'rmssd': {'low': 10, 'normal': 30, 'high': 60},
        'pnn50': {'low': 2, 'normal': 10, 'high': 25},
        'lf_hf_ratio': {'low': 0.5, 'normal': 3.0, 'high': 6.0}
    }
}

# 性别差异调整因子 (参考Koenig & Thayer 2016)
GENDER_ADJUSTMENT = {
    'male': {
        'sdnn_factor': 1.0,
        'rmssd_factor': 0.9,  # 男性RMSSD通常略低
        'pnn50_factor': 0.9,
        'lf_hf_factor': 1.1   # 男性LF/HF比值通常略高
    },
    'female': {
        'sdnn_factor': 1.0,
        'rmssd_factor': 1.1,  # 女性RMSSD通常略高
        'pnn50_factor': 1.1,
        'lf_hf_factor': 0.9   # 女性LF/HF比值通常略低
    }
}

# WESAD数据集状态映射权重 (基于Kreibig 2010, Appelhans & Luecken 2006)
WESAD_STATE_MAPPING = {
    'baseline': {
        'description': '基线状态 - 平静放松',
        'stress_score_range': (20, 40),
        'fatigue_score_range': (20, 40),
        'vitality_score_range': (40, 60),
        'typical_hrv': {
            'sdnn_range': (30, 60),
            'rmssd_range': (25, 50),
            'lf_hf_range': (0.8, 2.0)
        }
    },
    'stress': {
        'description': '压力状态 - 心理压力任务',
        'stress_score_range': (70, 90),
        'fatigue_score_range': (50, 70),
        'vitality_score_range': (20, 40),
        'typical_hrv': {
            'sdnn_range': (15, 35),
            'rmssd_range': (10, 25),
            'lf_hf_range': (2.5, 6.0)
        }
    },
    'amusement': {
        'description': '娱乐状态 - 积极情绪',
        'stress_score_range': (10, 30),
        'fatigue_score_range': (10, 30),
        'vitality_score_range': (70, 90),
        'typical_hrv': {
            'sdnn_range': (40, 80),
            'rmssd_range': (35, 70),
            'lf_hf_range': (0.5, 1.5)
        }
    },
    'meditation': {
        'description': '冥想状态 - 深度放松',
        'stress_score_range': (5, 20),
        'fatigue_score_range': (15, 35),
        'vitality_score_range': (60, 80),
        'typical_hrv': {
            'sdnn_range': (45, 90),
            'rmssd_range': (40, 80),
            'lf_hf_range': (0.3, 1.0)
        }
    }
}

# 聚类中心定义 (标准化后的特征空间)
CLUSTER_CENTERS = {
    'high_stress': {
        'center': [-1.5, -1.2, -1.0, 1.5, 0.8],
        'description': '高压力状态',
        'characteristics': ['低SDNN', '低RMSSD', '低pNN50', '高LF/HF', '心率偏高']
    },
    'baseline': {
        'center': [0.0, 0.0, 0.0, 0.0, 0.0],
        'description': '基线状态',
        'characteristics': ['正常HRV指标', '平衡的自主神经活动']
    },
    'high_vitality': {
        'center': [1.2, 1.5, 1.0, -0.8, -0.5],
        'description': '高活力状态',
        'characteristics': ['高SDNN', '高RMSSD', '高pNN50', '低LF/HF', '心率适中']
    },
    'relaxation': {
        'center': [0.8, 1.8, 1.2, -1.2, -1.0],
        'description': '深度放松状态',
        'characteristics': ['良好HRV', '极高RMSSD', '高副交感活性', '低心率']
    }
}

# 风险分级阈值
RISK_THRESHOLDS = {
    'stress': {
        'low_risk': 30,      # < 30分为低风险
        'medium_risk': 70,   # 30-70分为中等风险
        'high_risk': 100     # > 70分为高风险
    },
    'fatigue': {
        'low_risk': 30,
        'medium_risk': 70,
        'high_risk': 100
    },
    'vitality': {
        'low_level': 40,     # < 40分为低活力
        'medium_level': 70,  # 40-70分为中等活力
        'high_level': 100    # > 70分为高活力
    }
}

# 置信度权重配置
CONFIDENCE_WEIGHTS = {
    'feature_completeness': 0.4,  # 特征完整性权重
    'signal_quality': 0.3,        # 信号质量权重
    'consistency': 0.3            # 结果一致性权重
}

# 融合权重配置 (WESAD vs 机器学习模型)
FUSION_WEIGHTS = {
    'stress': {
        'wesad_weight': 0.7,
        'ml_weight': 0.3,
        'reason': 'WESAD在压力检测方面更准确'
    },
    'fatigue': {
        'wesad_weight': 0.6,
        'ml_weight': 0.4,
        'reason': '疲劳检测需要结合多种方法'
    },
    'vitality': {
        'wesad_weight': 0.8,
        'ml_weight': 0.2,
        'reason': 'HRV特征在活力评估中更可靠'
    },
    'emotion': {
        'wesad_weight': 0.3,
        'ml_weight': 0.7,
        'reason': '情绪评估主要依赖机器学习模型'
    }
}

# 推荐建议模板
RECOMMENDATION_TEMPLATES = {
    'stress_management': {
        'low': [
            "保持当前良好的压力管理状态",
            "继续进行规律的放松活动",
            "维持健康的生活方式"
        ],
        'medium': [
            "适当增加放松时间，建议每天15-20分钟",
            "尝试深呼吸练习：4秒吸气-4秒屏息-6秒呼气",
            "保证每晚7-8小时充足睡眠",
            "进行适度有氧运动，如快走30分钟"
        ],
        'high': [
            "立即采取压力缓解措施",
            "考虑寻求专业心理咨询师帮助",
            "减少工作强度，增加休息时间",
            "学习正念冥想或渐进性肌肉放松",
            "避免咖啡因和酒精摄入"
        ]
    },
    'fatigue_management': {
        'low': [
            "保持当前良好的精力状态",
            "维持规律的作息时间",
            "继续健康的生活习惯"
        ],
        'medium': [
            "注意劳逸结合，每工作1小时休息10分钟",
            "保证7-8小时高质量睡眠",
            "适当补充B族维生素和镁元素",
            "减少屏幕时间，特别是睡前2小时"
        ],
        'high': [
            "建议充分休息，考虑请假调整",
            "检查是否存在睡眠呼吸暂停等睡眠障碍",
            "建议进行全面体检排除疾病因素",
            "调整工作强度和时间安排",
            "增加自然光照射时间"
        ]
    },
    'vitality_enhancement': {
        'low': [
            "增加有氧运动，建议每周150分钟中等强度运动",
            "改善饮食：增加蛋白质、减少精制糖",
            "保证充足睡眠和规律作息",
            "多参与社交活动和户外运动",
            "考虑补充维生素D和B12"
        ],
        'medium': [
            "保持规律运动习惯",
            "均衡饮食，多吃新鲜蔬果",
            "适当增加户外活动时间",
            "培养新的兴趣爱好"
        ],
        'high': [
            "保持当前良好状态",
            "继续健康的生活方式",
            "可以尝试更具挑战性的运动项目",
            "考虑帮助他人提升活力"
        ]
    }
}

# 特征重要性权重 (基于文献研究)
FEATURE_IMPORTANCE = {
    'stress_detection': {
        'sdnn': 0.25,
        'rmssd': 0.20,
        'pnn50': 0.15,
        'lf_hf_ratio': 0.25,
        'mean_hr': 0.15
    },
    'fatigue_detection': {
        'sdnn': 0.20,
        'rmssd': 0.25,
        'total_power': 0.20,
        'lf_hf_ratio': 0.15,
        'mean_hr': 0.20
    },
    'vitality_assessment': {
        'sdnn': 0.30,
        'rmssd': 0.25,
        'total_power': 0.20,
        'pnn50': 0.15,
        'mean_hr': 0.10
    }
}

# 数据质量评估标准
DATA_QUALITY_CRITERIA = {
    'minimum_duration': 120,      # 最少需要2分钟数据
    'minimum_beats': 100,         # 最少需要100个心跳
    'max_artifact_ratio': 0.1,    # 最大伪迹比例10%
    'min_valid_features': 3       # 最少需要3个有效特征
}
