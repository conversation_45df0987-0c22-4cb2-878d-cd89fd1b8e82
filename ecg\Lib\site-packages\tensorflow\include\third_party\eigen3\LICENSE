Eigen is primarily MPL2 licensed. See COPYING.MPL2 and these links:
  http://www.mozilla.org/MPL/2.0/
  http://www.mozilla.org/MPL/2.0/FAQ.html

Some files contain third-party code under BSD or LGPL licenses, whence
the other COPYING.* files here.

All the LGPL code is either LGPL 2.1-only, or LGPL 2.1-or-later.
For this reason, the COPYING.LGPL file contains the LGPL 2.1 text.

If you want to guarantee that the Eigen code that you are #including
is licensed under the MPL2 and possibly more permissive licenses (like
BSD), #define this preprocessor symbol: EIGEN_MPL2_ONLY 
For example, with most compilers, you could add this to your project
      CXXFLAGS: -DEIGEN_MPL2_ONLY 
This will cause a compilation error to be generated if you #include
any code that is LGPL licensed.

----------------------------------------------------------------------
Following applies to:
./test/mapstaticmethods.cpp
./test/schur_real.cpp
./test/prec_inverse_4x4.cpp
./test/smallvectors.cpp
./test/redux.cpp
./test/special_numbers.cpp
./test/adjoint.cpp
./test/resize.cpp
./test/mixingtypes.cpp
./test/product_trmv.cpp
./test/sparse_solvers.cpp
./test/cholesky.cpp
./test/geo_quaternion.cpp
./test/miscmatrices.cpp
./test/stddeque.cpp
./test/integer_types.cpp
./test/product_large.cpp
./test/eigensolver_generic.cpp
./test/householder.cpp
./test/geo_orthomethods.cpp
./test/array_for_matrix.cpp
./test/sparseLM.cpp
./test/upperbidiagonalization.cpp
./test/nomalloc.cpp
./test/packetmath.cpp
./test/jacobisvd.cpp
./test/geo_transformations.cpp
./test/swap.cpp
./test/eigensolver_selfadjoint.cpp
./test/inverse.cpp
./test/product_selfadjoint.cpp
./test/product_trsolve.cpp
./test/product_extra.cpp
./test/sparse_solver.h
./test/mapstride.cpp
./test/mapped_matrix.cpp
./test/geo_eulerangles.cpp
./test/eigen2support.cpp
./test/denseLM.cpp
./test/stdvector.cpp
./test/nesting_ops.cpp
./test/sparse_permutations.cpp
./test/zerosized.cpp
./test/exceptions.cpp
./test/vectorwiseop.cpp
./test/cwiseop.cpp
./test/basicstuff.cpp
./test/product_trmm.cpp
./test/linearstructure.cpp
./test/sparse_product.cpp
./test/stdvector_overload.cpp
./test/stable_norm.cpp
./test/umeyama.cpp
./test/unalignedcount.cpp
./test/triangular.cpp
./test/product_mmtr.cpp
./test/sparse_basic.cpp
./test/sparse_vector.cpp
./test/meta.cpp
./test/real_qz.cpp
./test/ref.cpp
./test/eigensolver_complex.cpp
./test/cholmod_support.cpp
./test/conjugate_gradient.cpp
./test/sparse.h
./test/simplicial_cholesky.cpp
./test/bicgstab.cpp
./test/dynalloc.cpp
./test/product_notemporary.cpp
./test/geo_hyperplane.cpp
./test/lu.cpp
./test/qr.cpp
./test/hessenberg.cpp
./test/sizeof.cpp
./test/main.h
./test/selfadjoint.cpp
./test/permutationmatrices.cpp
./test/superlu_support.cpp
./test/qtvector.cpp
./test/geo_homogeneous.cpp
./test/determinant.cpp
./test/array_reverse.cpp
./test/unalignedassert.cpp
./test/stdlist.cpp
./test/product_symm.cpp
./test/corners.cpp
./test/dontalign.cpp
./test/visitor.cpp
./test/geo_alignedbox.cpp
./test/diagonalmatrices.cpp
./test/product_small.cpp
./test/eigensolver_generalized_real.cpp
./test/umfpack_support.cpp
./test/first_aligned.cpp
./test/qr_fullpivoting.cpp
./test/array_replicate.cpp
./test/geo_parametrizedline.cpp
./test/eigen2/eigen2_unalignedassert.cpp
./test/eigen2/eigen2_prec_inverse_4x4.cpp
./test/eigen2/eigen2_alignedbox.cpp
./test/eigen2/eigen2_sparse_product.cpp
./test/eigen2/eigen2_meta.cpp
./test/eigen2/eigen2_nomalloc.cpp
./test/eigen2/eigen2_visitor.cpp
./test/eigen2/eigen2_packetmath.cpp
./test/eigen2/eigen2_svd.cpp
./test/eigen2/eigen2_mixingtypes.cpp
./test/eigen2/eigen2_qr.cpp
./test/eigen2/eigen2_cwiseop.cpp
./test/eigen2/eigen2_geometry_with_eigen2_prefix.cpp
./test/eigen2/eigen2_smallvectors.cpp
./test/eigen2/eigen2_commainitializer.cpp
./test/eigen2/eigen2_sparse_solvers.cpp
./test/eigen2/eigen2_hyperplane.cpp
./test/eigen2/eigen2_eigensolver.cpp
./test/eigen2/eigen2_linearstructure.cpp
./test/eigen2/eigen2_sizeof.cpp
./test/eigen2/eigen2_parametrizedline.cpp
./test/eigen2/eigen2_lu.cpp
./test/eigen2/eigen2_adjoint.cpp
./test/eigen2/eigen2_geometry.cpp
./test/eigen2/eigen2_stdvector.cpp
./test/eigen2/eigen2_newstdvector.cpp
./test/eigen2/eigen2_submatrices.cpp
./test/eigen2/sparse.h
./test/eigen2/eigen2_swap.cpp
./test/eigen2/eigen2_triangular.cpp
./test/eigen2/eigen2_basicstuff.cpp
./test/eigen2/gsl_helper.h
./test/eigen2/eigen2_dynalloc.cpp
./test/eigen2/eigen2_array.cpp
./test/eigen2/eigen2_map.cpp
./test/eigen2/main.h
./test/eigen2/eigen2_miscmatrices.cpp
./test/eigen2/eigen2_product_large.cpp
./test/eigen2/eigen2_first_aligned.cpp
./test/eigen2/eigen2_cholesky.cpp
./test/eigen2/eigen2_determinant.cpp
./test/eigen2/eigen2_sum.cpp
./test/eigen2/eigen2_inverse.cpp
./test/eigen2/eigen2_regression.cpp
./test/eigen2/eigen2_product_small.cpp
./test/eigen2/eigen2_qtvector.cpp
./test/eigen2/eigen2_sparse_vector.cpp
./test/eigen2/product.h
./test/eigen2/eigen2_sparse_basic.cpp
./test/eigen2/eigen2_bug_132.cpp
./test/array.cpp
./test/product_syrk.cpp
./test/commainitializer.cpp
./test/conservative_resize.cpp
./test/qr_colpivoting.cpp
./test/nullary.cpp
./test/bandmatrix.cpp
./test/pastix_support.cpp
./test/product.h
./test/block.cpp
./test/vectorization_logic.cpp
./test/jacobi.cpp
./test/diagonal.cpp
./test/schur_complex.cpp
./test/sizeoverflow.cpp
./bench/BenchTimer.h
./bench/benchFFT.cpp
./bench/eig33.cpp
./bench/spbench/spbenchsolver.h
./bench/spbench/spbenchstyle.h
./lapack/complex_double.cpp
./lapack/cholesky.cpp
./lapack/lapack_common.h
./lapack/eigenvalues.cpp
./lapack/single.cpp
./lapack/lu.cpp
./lapack/complex_single.cpp
./lapack/double.cpp
./demos/mix_eigen_and_c/binary_library.cpp
./demos/mix_eigen_and_c/binary_library.h
./demos/mix_eigen_and_c/example.c
./demos/mandelbrot/mandelbrot.cpp
./demos/mandelbrot/mandelbrot.h
./demos/opengl/icosphere.cpp
./demos/opengl/icosphere.h
./demos/opengl/camera.cpp
./demos/opengl/quaternion_demo.h
./demos/opengl/camera.h
./demos/opengl/trackball.h
./demos/opengl/gpuhelper.h
./demos/opengl/trackball.cpp
./demos/opengl/gpuhelper.cpp
./demos/opengl/quaternion_demo.cpp
./debug/gdb/printers.py
./unsupported/test/minres.cpp
./unsupported/test/openglsupport.cpp
./unsupported/test/jacobisvd.cpp
./unsupported/test/dgmres.cpp
./unsupported/test/matrix_square_root.cpp
./unsupported/test/bdcsvd.cpp
./unsupported/test/matrix_exponential.cpp
./unsupported/test/forward_adolc.cpp
./unsupported/test/polynomialsolver.cpp
./unsupported/test/matrix_function.cpp
./unsupported/test/sparse_extra.cpp
./unsupported/test/matrix_functions.h
./unsupported/test/svd_common.h
./unsupported/test/FFTW.cpp
./unsupported/test/alignedvector3.cpp
./unsupported/test/autodiff.cpp
./unsupported/test/gmres.cpp
./unsupported/test/BVH.cpp
./unsupported/test/levenberg_marquardt.cpp
./unsupported/test/matrix_power.cpp
./unsupported/test/kronecker_product.cpp
./unsupported/test/splines.cpp
./unsupported/test/polynomialutils.cpp
./unsupported/bench/bench_svd.cpp
./unsupported/Eigen/IterativeSolvers
./unsupported/Eigen/src/IterativeSolvers/DGMRES.h
./unsupported/Eigen/src/IterativeSolvers/IncompleteLU.h
./unsupported/Eigen/src/IterativeSolvers/GMRES.h
./unsupported/Eigen/src/IterativeSolvers/IncompleteCholesky.h
./unsupported/Eigen/src/IterativeSolvers/Scaling.h
./unsupported/Eigen/src/IterativeSolvers/MINRES.h
./unsupported/Eigen/src/SparseExtra/RandomSetter.h
./unsupported/Eigen/src/SparseExtra/MatrixMarketIterator.h
./unsupported/Eigen/src/SparseExtra/DynamicSparseMatrix.h
./unsupported/Eigen/src/SparseExtra/MarketIO.h
./unsupported/Eigen/src/SparseExtra/BlockOfDynamicSparseMatrix.h
./unsupported/Eigen/src/KroneckerProduct/KroneckerTensorProduct.h
./unsupported/Eigen/src/NonLinearOptimization/LevenbergMarquardt.h
./unsupported/Eigen/src/NonLinearOptimization/HybridNonLinearSolver.h
./unsupported/Eigen/src/BVH/BVAlgorithms.h
./unsupported/Eigen/src/BVH/KdBVH.h
./unsupported/Eigen/src/AutoDiff/AutoDiffScalar.h
./unsupported/Eigen/src/AutoDiff/AutoDiffJacobian.h
./unsupported/Eigen/src/AutoDiff/AutoDiffVector.h
./unsupported/Eigen/src/Splines/Spline.h
./unsupported/Eigen/src/Splines/SplineFitting.h
./unsupported/Eigen/src/Splines/SplineFwd.h
./unsupported/Eigen/src/SVD/JacobiSVD.h
./unsupported/Eigen/src/SVD/BDCSVD.h
./unsupported/Eigen/src/SVD/SVDBase.h
./unsupported/Eigen/src/MatrixFunctions/MatrixFunction.h
./unsupported/Eigen/src/MatrixFunctions/MatrixSquareRoot.h
./unsupported/Eigen/src/MatrixFunctions/MatrixLogarithm.h
./unsupported/Eigen/src/MatrixFunctions/StemFunction.h
./unsupported/Eigen/src/MatrixFunctions/MatrixPower.h
./unsupported/Eigen/src/MatrixFunctions/MatrixExponential.h
./unsupported/Eigen/src/MatrixFunctions/MatrixFunctionAtomic.h
./unsupported/Eigen/src/MoreVectorization/MathFunctions.h
./unsupported/Eigen/src/LevenbergMarquardt/LevenbergMarquardt.h
./unsupported/Eigen/src/FFT/ei_fftw_impl.h
./unsupported/Eigen/src/FFT/ei_kissfft_impl.h
./unsupported/Eigen/src/Polynomials/PolynomialSolver.h
./unsupported/Eigen/src/Polynomials/Companion.h
./unsupported/Eigen/src/Polynomials/PolynomialUtils.h
./unsupported/Eigen/src/NumericalDiff/NumericalDiff.h
./unsupported/Eigen/src/Skyline/SkylineProduct.h
./unsupported/Eigen/src/Skyline/SkylineMatrixBase.h
./unsupported/Eigen/src/Skyline/SkylineStorage.h
./unsupported/Eigen/src/Skyline/SkylineUtil.h
./unsupported/Eigen/src/Skyline/SkylineInplaceLU.h
./unsupported/Eigen/src/Skyline/SkylineMatrix.h
./unsupported/Eigen/SparseExtra
./unsupported/Eigen/AdolcForward
./unsupported/Eigen/KroneckerProduct
./unsupported/Eigen/NonLinearOptimization
./unsupported/Eigen/BVH
./unsupported/Eigen/OpenGLSupport
./unsupported/Eigen/ArpackSupport
./unsupported/Eigen/AutoDiff
./unsupported/Eigen/Splines
./unsupported/Eigen/MPRealSupport
./unsupported/Eigen/MatrixFunctions
./unsupported/Eigen/MoreVectorization
./unsupported/Eigen/LevenbergMarquardt
./unsupported/Eigen/AlignedVector3
./unsupported/Eigen/FFT
./unsupported/Eigen/Polynomials
./unsupported/Eigen/NumericalDiff
./unsupported/Eigen/Skyline
./COPYING.README
./COPYING.README
./LICENSE
./LICENSE
./LICENSE
./Eigen/Eigen2Support
./Eigen/src/Eigen2Support/VectorBlock.h
./Eigen/src/Eigen2Support/Cwise.h
./Eigen/src/Eigen2Support/Minor.h
./Eigen/src/Eigen2Support/Lazy.h
./Eigen/src/Eigen2Support/Memory.h
./Eigen/src/Eigen2Support/MathFunctions.h
./Eigen/src/Eigen2Support/Geometry/AlignedBox.h
./Eigen/src/Eigen2Support/Geometry/Hyperplane.h
./Eigen/src/Eigen2Support/Geometry/Quaternion.h
./Eigen/src/Eigen2Support/Geometry/Rotation2D.h
./Eigen/src/Eigen2Support/Geometry/ParametrizedLine.h
./Eigen/src/Eigen2Support/Geometry/RotationBase.h
./Eigen/src/Eigen2Support/Geometry/Translation.h
./Eigen/src/Eigen2Support/Geometry/Scaling.h
./Eigen/src/Eigen2Support/Geometry/AngleAxis.h
./Eigen/src/Eigen2Support/Geometry/Transform.h
./Eigen/src/Eigen2Support/TriangularSolver.h
./Eigen/src/Eigen2Support/LU.h
./Eigen/src/Eigen2Support/QR.h
./Eigen/src/Eigen2Support/SVD.h
./Eigen/src/Eigen2Support/Meta.h
./Eigen/src/Eigen2Support/Block.h
./Eigen/src/Eigen2Support/Macros.h
./Eigen/src/Eigen2Support/LeastSquares.h
./Eigen/src/Eigen2Support/CwiseOperators.h
./Eigen/src/Jacobi/Jacobi.h
./Eigen/src/misc/Kernel.h
./Eigen/src/misc/SparseSolve.h
./Eigen/src/misc/Solve.h
./Eigen/src/misc/Image.h
./Eigen/src/SparseCore/SparseColEtree.h
./Eigen/src/SparseCore/SparseTranspose.h
./Eigen/src/SparseCore/SparseUtil.h
./Eigen/src/SparseCore/SparseCwiseBinaryOp.h
./Eigen/src/SparseCore/SparseDiagonalProduct.h
./Eigen/src/SparseCore/SparseProduct.h
./Eigen/src/SparseCore/SparseDot.h
./Eigen/src/SparseCore/SparseCwiseUnaryOp.h
./Eigen/src/SparseCore/SparseSparseProductWithPruning.h
./Eigen/src/SparseCore/SparseBlock.h
./Eigen/src/SparseCore/SparseDenseProduct.h
./Eigen/src/SparseCore/CompressedStorage.h
./Eigen/src/SparseCore/SparseMatrixBase.h
./Eigen/src/SparseCore/MappedSparseMatrix.h
./Eigen/src/SparseCore/SparseTriangularView.h
./Eigen/src/SparseCore/SparseView.h
./Eigen/src/SparseCore/SparseFuzzy.h
./Eigen/src/SparseCore/TriangularSolver.h
./Eigen/src/SparseCore/SparseSelfAdjointView.h
./Eigen/src/SparseCore/SparseMatrix.h
./Eigen/src/SparseCore/SparseVector.h
./Eigen/src/SparseCore/AmbiVector.h
./Eigen/src/SparseCore/ConservativeSparseSparseProduct.h
./Eigen/src/SparseCore/SparseRedux.h
./Eigen/src/SparseCore/SparsePermutation.h
./Eigen/src/Eigenvalues/RealSchur.h
./Eigen/src/Eigenvalues/ComplexEigenSolver.h
./Eigen/src/Eigenvalues/GeneralizedEigenSolver.h
./Eigen/src/Eigenvalues/ComplexSchur.h
./Eigen/src/Eigenvalues/RealQZ.h
./Eigen/src/Eigenvalues/EigenSolver.h
./Eigen/src/Eigenvalues/HessenbergDecomposition.h
./Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h
./Eigen/src/Eigenvalues/Tridiagonalization.h
./Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h
./Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h
./Eigen/src/SuperLUSupport/SuperLUSupport.h
./Eigen/src/StlSupport/StdDeque.h
./Eigen/src/StlSupport/StdVector.h
./Eigen/src/StlSupport/StdList.h
./Eigen/src/StlSupport/details.h
./Eigen/src/SparseQR/SparseQR.h
./Eigen/src/LU/Inverse.h
./Eigen/src/LU/arch/Inverse_SSE.h
./Eigen/src/LU/Determinant.h
./Eigen/src/LU/PartialPivLU.h
./Eigen/src/LU/FullPivLU.h
./Eigen/src/UmfPackSupport/UmfPackSupport.h
./Eigen/src/OrderingMethods/Ordering.h
./Eigen/src/OrderingMethods/Eigen_Colamd.h
./Eigen/src/QR/HouseholderQR.h
./Eigen/src/QR/ColPivHouseholderQR.h
./Eigen/src/QR/FullPivHouseholderQR.h
./Eigen/src/SVD/JacobiSVD.h
./Eigen/src/SVD/UpperBidiagonalization.h
./Eigen/src/Geometry/OrthoMethods.h
./Eigen/src/Geometry/AlignedBox.h
./Eigen/src/Geometry/Hyperplane.h
./Eigen/src/Geometry/Quaternion.h
./Eigen/src/Geometry/EulerAngles.h
./Eigen/src/Geometry/Rotation2D.h
./Eigen/src/Geometry/ParametrizedLine.h
./Eigen/src/Geometry/RotationBase.h
./Eigen/src/Geometry/arch/Geometry_SSE.h
./Eigen/src/Geometry/Umeyama.h
./Eigen/src/Geometry/Homogeneous.h
./Eigen/src/Geometry/Translation.h
./Eigen/src/Geometry/Scaling.h
./Eigen/src/Geometry/AngleAxis.h
./Eigen/src/Geometry/Transform.h
./Eigen/src/plugins/BlockMethods.h
./Eigen/src/plugins/CommonCwiseUnaryOps.h
./Eigen/src/plugins/CommonCwiseBinaryOps.h
./Eigen/src/plugins/MatrixCwiseUnaryOps.h
./Eigen/src/plugins/MatrixCwiseBinaryOps.h
./Eigen/src/Householder/Householder.h
./Eigen/src/Householder/HouseholderSequence.h
./Eigen/src/Householder/BlockHouseholder.h
./Eigen/src/Core/VectorBlock.h
./Eigen/src/Core/Matrix.h
./Eigen/src/Core/Ref.h
./Eigen/src/Core/SelfAdjointView.h
./Eigen/src/Core/MathFunctions.h
./Eigen/src/Core/GlobalFunctions.h
./Eigen/src/Core/MapBase.h
./Eigen/src/Core/EigenBase.h
./Eigen/src/Core/GenericPacketMath.h
./Eigen/src/Core/NestByValue.h
./Eigen/src/Core/CwiseUnaryOp.h
./Eigen/src/Core/SolveTriangular.h
./Eigen/src/Core/Fuzzy.h
./Eigen/src/Core/Visitor.h
./Eigen/src/Core/Map.h
./Eigen/src/Core/NoAlias.h
./Eigen/src/Core/Diagonal.h
./Eigen/src/Core/StableNorm.h
./Eigen/src/Core/CoreIterators.h
./Eigen/src/Core/products/Parallelizer.h
./Eigen/src/Core/products/SelfadjointMatrixVector.h
./Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h
./Eigen/src/Core/products/TriangularSolverMatrix.h
./Eigen/src/Core/products/GeneralMatrixMatrix.h
./Eigen/src/Core/products/SelfadjointProduct.h
./Eigen/src/Core/products/CoeffBasedProduct.h
./Eigen/src/Core/products/TriangularMatrixVector.h
./Eigen/src/Core/products/SelfadjointMatrixMatrix.h
./Eigen/src/Core/products/TriangularSolverVector.h
./Eigen/src/Core/products/SelfadjointRank2Update.h
./Eigen/src/Core/products/GeneralBlockPanelKernel.h
./Eigen/src/Core/products/GeneralMatrixVector.h
./Eigen/src/Core/products/TriangularMatrixMatrix.h
./Eigen/src/Core/Reverse.h
./Eigen/src/Core/BooleanRedux.h
./Eigen/src/Core/Replicate.h
./Eigen/src/Core/arch/AltiVec/PacketMath.h
./Eigen/src/Core/arch/AltiVec/Complex.h
./Eigen/src/Core/arch/SSE/PacketMath.h
./Eigen/src/Core/arch/SSE/Complex.h
./Eigen/src/Core/arch/SSE/MathFunctions.h
./Eigen/src/Core/arch/NEON/PacketMath.h
./Eigen/src/Core/arch/NEON/Complex.h
./Eigen/src/Core/arch/Default/Settings.h
./Eigen/src/Core/CwiseUnaryView.h
./Eigen/src/Core/Array.h
./Eigen/src/Core/ArrayWrapper.h
./Eigen/src/Core/Swap.h
./Eigen/src/Core/Transpositions.h
./Eigen/src/Core/Random.h
./Eigen/src/Core/IO.h
./Eigen/src/Core/SelfCwiseBinaryOp.h
./Eigen/src/Core/VectorwiseOp.h
./Eigen/src/Core/Select.h
./Eigen/src/Core/ArrayBase.h
./Eigen/src/Core/DenseCoeffsBase.h
./Eigen/src/Core/DiagonalProduct.h
./Eigen/src/Core/Assign.h
./Eigen/src/Core/Redux.h
./Eigen/src/Core/ForceAlignedAccess.h
./Eigen/src/Core/BandMatrix.h
./Eigen/src/Core/PlainObjectBase.h
./Eigen/src/Core/DenseBase.h
./Eigen/src/Core/Flagged.h
./Eigen/src/Core/CwiseBinaryOp.h
./Eigen/src/Core/ProductBase.h
./Eigen/src/Core/TriangularMatrix.h
./Eigen/src/Core/Transpose.h
./Eigen/src/Core/DiagonalMatrix.h
./Eigen/src/Core/Dot.h
./Eigen/src/Core/Functors.h
./Eigen/src/Core/PermutationMatrix.h
./Eigen/src/Core/NumTraits.h
./Eigen/src/Core/MatrixBase.h
./Eigen/src/Core/DenseStorage.h
./Eigen/src/Core/util/Memory.h
./Eigen/src/Core/util/StaticAssert.h
./Eigen/src/Core/util/BlasUtil.h
./Eigen/src/Core/util/MatrixMapper.h
./Eigen/src/Core/util/XprHelper.h
./Eigen/src/Core/util/ForwardDeclarations.h
./Eigen/src/Core/util/Meta.h
./Eigen/src/Core/util/Macros.h
./Eigen/src/Core/util/Constants.h
./Eigen/src/Core/CwiseNullaryOp.h
./Eigen/src/Core/Block.h
./Eigen/src/Core/GeneralProduct.h
./Eigen/src/Core/CommaInitializer.h
./Eigen/src/Core/ReturnByValue.h
./Eigen/src/Core/Stride.h
./Eigen/src/SPQRSupport/SuiteSparseQRSupport.h
./Eigen/src/SparseLU/SparseLU_column_dfs.h
./Eigen/src/SparseLU/SparseLU_panel_dfs.h
./Eigen/src/SparseLU/SparseLU_relax_snode.h
./Eigen/src/SparseLU/SparseLU_panel_bmod.h
./Eigen/src/SparseLU/SparseLU_SupernodalMatrix.h
./Eigen/src/SparseLU/SparseLU_Utils.h
./Eigen/src/SparseLU/SparseLU_gemm_kernel.h
./Eigen/src/SparseLU/SparseLU_kernel_bmod.h
./Eigen/src/SparseLU/SparseLU_pivotL.h
./Eigen/src/SparseLU/SparseLU_Memory.h
./Eigen/src/SparseLU/SparseLU_heap_relax_snode.h
./Eigen/src/SparseLU/SparseLUImpl.h
./Eigen/src/SparseLU/SparseLU_copy_to_ucol.h
./Eigen/src/SparseLU/SparseLU_Structs.h
./Eigen/src/SparseLU/SparseLU.h
./Eigen/src/SparseLU/SparseLU_column_bmod.h
./Eigen/src/SparseLU/SparseLU_pruneL.h
./Eigen/src/IterativeLinearSolvers/IncompleteLUT.h
./Eigen/src/IterativeLinearSolvers/BasicPreconditioners.h
./Eigen/src/IterativeLinearSolvers/IterativeSolverBase.h
./Eigen/src/IterativeLinearSolvers/ConjugateGradient.h
./Eigen/src/IterativeLinearSolvers/BiCGSTAB.h
./Eigen/src/SparseCholesky/SimplicialCholesky.h
./Eigen/src/Cholesky/LDLT.h
./Eigen/src/Cholesky/LLT.h
./Eigen/src/CholmodSupport/CholmodSupport.h
./Eigen/src/PaStiXSupport/PaStiXSupport.h
./Eigen/src/MetisSupport/MetisSupport.h
./Eigen/StdVector
./Eigen/Core
./Eigen/OrderingMethods
./Eigen/SparseLU
./Eigen/StdList
./Eigen/StdDeque
./Eigen/SparseCholesky
./Eigen/SparseCore
./scripts/relicense.py
./scripts/relicense.py
./blas/BandTriangularSolver.h
./blas/PackedTriangularMatrixVector.h
./blas/complex_double.cpp
./blas/level2_real_impl.h
./blas/level1_cplx_impl.h
./blas/level1_impl.h
./blas/level1_real_impl.h
./blas/level3_impl.h
./blas/single.cpp
./blas/level2_cplx_impl.h
./blas/PackedSelfadjointProduct.h
./blas/Rank2Update.h
./blas/complex_single.cpp
./blas/PackedTriangularSolverVector.h
./blas/double.cpp
./blas/common.h
./blas/level2_impl.h
./blas/GeneralRank1Update.h

Mozilla Public License Version 2.0
==================================

1. Definitions
--------------

1.1. "Contributor"
    means each individual or legal entity that creates, contributes to
    the creation of, or owns Covered Software.

1.2. "Contributor Version"
    means the combination of the Contributions of others (if any) used
    by a Contributor and that particular Contributor's Contribution.

1.3. "Contribution"
    means Covered Software of a particular Contributor.

1.4. "Covered Software"
    means Source Code Form to which the initial Contributor has attached
    the notice in Exhibit A, the Executable Form of such Source Code
    Form, and Modifications of such Source Code Form, in each case
    including portions thereof.

1.5. "Incompatible With Secondary Licenses"
    means

    (a) that the initial Contributor has attached the notice described
        in Exhibit B to the Covered Software; or

    (b) that the Covered Software was made available under the terms of
        version 1.1 or earlier of the License, but not also under the
        terms of a Secondary License.

1.6. "Executable Form"
    means any form of the work other than Source Code Form.

1.7. "Larger Work"
    means a work that combines Covered Software with other material, in 
    a separate file or files, that is not Covered Software.

1.8. "License"
    means this document.

1.9. "Licensable"
    means having the right to grant, to the maximum extent possible,
    whether at the time of the initial grant or subsequently, any and
    all of the rights conveyed by this License.

1.10. "Modifications"
    means any of the following:

    (a) any file in Source Code Form that results from an addition to,
        deletion from, or modification of the contents of Covered
        Software; or

    (b) any new file in Source Code Form that contains any Covered
        Software.

1.11. "Patent Claims" of a Contributor
    means any patent claim(s), including without limitation, method,
    process, and apparatus claims, in any patent Licensable by such
    Contributor that would be infringed, but for the grant of the
    License, by the making, using, selling, offering for sale, having
    made, import, or transfer of either its Contributions or its
    Contributor Version.

1.12. "Secondary License"
    means either the GNU General Public License, Version 2.0, the GNU
    Lesser General Public License, Version 2.1, the GNU Affero General
    Public License, Version 3.0, or any later versions of those
    licenses.

1.13. "Source Code Form"
    means the form of the work preferred for making modifications.

1.14. "You" (or "Your")
    means an individual or a legal entity exercising rights under this
    License. For legal entities, "You" includes any entity that
    controls, is controlled by, or is under common control with You. For
    purposes of this definition, "control" means (a) the power, direct
    or indirect, to cause the direction or management of such entity,
    whether by contract or otherwise, or (b) ownership of more than
    fifty percent (50%) of the outstanding shares or beneficial
    ownership of such entity.

2. License Grants and Conditions
--------------------------------

2.1. Grants

Each Contributor hereby grants You a world-wide, royalty-free,
non-exclusive license:

(a) under intellectual property rights (other than patent or trademark)
    Licensable by such Contributor to use, reproduce, make available,
    modify, display, perform, distribute, and otherwise exploit its
    Contributions, either on an unmodified basis, with Modifications, or
    as part of a Larger Work; and

(b) under Patent Claims of such Contributor to make, use, sell, offer
    for sale, have made, import, and otherwise transfer either its
    Contributions or its Contributor Version.

2.2. Effective Date

The licenses granted in Section 2.1 with respect to any Contribution
become effective for each Contribution on the date the Contributor first
distributes such Contribution.

2.3. Limitations on Grant Scope

The licenses granted in this Section 2 are the only rights granted under
this License. No additional rights or licenses will be implied from the
distribution or licensing of Covered Software under this License.
Notwithstanding Section 2.1(b) above, no patent license is granted by a
Contributor:

(a) for any code that a Contributor has removed from Covered Software;
    or

(b) for infringements caused by: (i) Your and any other third party's
    modifications of Covered Software, or (ii) the combination of its
    Contributions with other software (except as part of its Contributor
    Version); or

(c) under Patent Claims infringed by Covered Software in the absence of
    its Contributions.

This License does not grant any rights in the trademarks, service marks,
or logos of any Contributor (except as may be necessary to comply with
the notice requirements in Section 3.4).

2.4. Subsequent Licenses

No Contributor makes additional grants as a result of Your choice to
distribute the Covered Software under a subsequent version of this
License (see Section 10.2) or under the terms of a Secondary License (if
permitted under the terms of Section 3.3).

2.5. Representation

Each Contributor represents that the Contributor believes its
Contributions are its original creation(s) or it has sufficient rights
to grant the rights to its Contributions conveyed by this License.

2.6. Fair Use

This License is not intended to limit any rights You have under
applicable copyright doctrines of fair use, fair dealing, or other
equivalents.

2.7. Conditions

Sections 3.1, 3.2, 3.3, and 3.4 are conditions of the licenses granted
in Section 2.1.

3. Responsibilities
-------------------

3.1. Distribution of Source Form

All distribution of Covered Software in Source Code Form, including any
Modifications that You create or to which You contribute, must be under
the terms of this License. You must inform recipients that the Source
Code Form of the Covered Software is governed by the terms of this
License, and how they can obtain a copy of this License. You may not
attempt to alter or restrict the recipients' rights in the Source Code
Form.

3.2. Distribution of Executable Form

If You distribute Covered Software in Executable Form then:

(a) such Covered Software must also be made available in Source Code
    Form, as described in Section 3.1, and You must inform recipients of
    the Executable Form how they can obtain a copy of such Source Code
    Form by reasonable means in a timely manner, at a charge no more
    than the cost of distribution to the recipient; and

(b) You may distribute such Executable Form under the terms of this
    License, or sublicense it under different terms, provided that the
    license for the Executable Form does not attempt to limit or alter
    the recipients' rights in the Source Code Form under this License.

3.3. Distribution of a Larger Work

You may create and distribute a Larger Work under terms of Your choice,
provided that You also comply with the requirements of this License for
the Covered Software. If the Larger Work is a combination of Covered
Software with a work governed by one or more Secondary Licenses, and the
Covered Software is not Incompatible With Secondary Licenses, this
License permits You to additionally distribute such Covered Software
under the terms of such Secondary License(s), so that the recipient of
the Larger Work may, at their option, further distribute the Covered
Software under the terms of either this License or such Secondary
License(s).

3.4. Notices

You may not remove or alter the substance of any license notices
(including copyright notices, patent notices, disclaimers of warranty,
or limitations of liability) contained within the Source Code Form of
the Covered Software, except that You may alter any license notices to
the extent required to remedy known factual inaccuracies.

3.5. Application of Additional Terms

You may choose to offer, and to charge a fee for, warranty, support,
indemnity or liability obligations to one or more recipients of Covered
Software. However, You may do so only on Your own behalf, and not on
behalf of any Contributor. You must make it absolutely clear that any
such warranty, support, indemnity, or liability obligation is offered by
You alone, and You hereby agree to indemnify every Contributor for any
liability incurred by such Contributor as a result of warranty, support,
indemnity or liability terms You offer. You may include additional
disclaimers of warranty and limitations of liability specific to any
jurisdiction.

4. Inability to Comply Due to Statute or Regulation
---------------------------------------------------

If it is impossible for You to comply with any of the terms of this
License with respect to some or all of the Covered Software due to
statute, judicial order, or regulation then You must: (a) comply with
the terms of this License to the maximum extent possible; and (b)
describe the limitations and the code they affect. Such description must
be placed in a text file included with all distributions of the Covered
Software under this License. Except to the extent prohibited by statute
or regulation, such description must be sufficiently detailed for a
recipient of ordinary skill to be able to understand it.

5. Termination
--------------

5.1. The rights granted under this License will terminate automatically
if You fail to comply with any of its terms. However, if You become
compliant, then the rights granted under this License from a particular
Contributor are reinstated (a) provisionally, unless and until such
Contributor explicitly and finally terminates Your grants, and (b) on an
ongoing basis, if such Contributor fails to notify You of the
non-compliance by some reasonable means prior to 60 days after You have
come back into compliance. Moreover, Your grants from a particular
Contributor are reinstated on an ongoing basis if such Contributor
notifies You of the non-compliance by some reasonable means, this is the
first time You have received notice of non-compliance with this License
from such Contributor, and You become compliant prior to 30 days after
Your receipt of the notice.

5.2. If You initiate litigation against any entity by asserting a patent
infringement claim (excluding declaratory judgment actions,
counter-claims, and cross-claims) alleging that a Contributor Version
directly or indirectly infringes any patent, then the rights granted to
You by any and all Contributors for the Covered Software under Section
2.1 of this License shall terminate.

5.3. In the event of termination under Sections 5.1 or 5.2 above, all
end user license agreements (excluding distributors and resellers) which
have been validly granted by You or Your distributors under this License
prior to termination shall survive termination.

************************************************************************
*                                                                      *
*  6. Disclaimer of Warranty                                           *
*  -------------------------                                           *
*                                                                      *
*  Covered Software is provided under this License on an "as is"       *
*  basis, without warranty of any kind, either expressed, implied, or  *
*  statutory, including, without limitation, warranties that the       *
*  Covered Software is free of defects, merchantable, fit for a        *
*  particular purpose or non-infringing. The entire risk as to the     *
*  quality and performance of the Covered Software is with You.        *
*  Should any Covered Software prove defective in any respect, You     *
*  (not any Contributor) assume the cost of any necessary servicing,   *
*  repair, or correction. This disclaimer of warranty constitutes an   *
*  essential part of this License. No use of any Covered Software is   *
*  authorized under this License except under this disclaimer.         *
*                                                                      *
************************************************************************

************************************************************************
*                                                                      *
*  7. Limitation of Liability                                          *
*  --------------------------                                          *
*                                                                      *
*  Under no circumstances and under no legal theory, whether tort      *
*  (including negligence), contract, or otherwise, shall any           *
*  Contributor, or anyone who distributes Covered Software as          *
*  permitted above, be liable to You for any direct, indirect,         *
*  special, incidental, or consequential damages of any character      *
*  including, without limitation, damages for lost profits, loss of    *
*  goodwill, work stoppage, computer failure or malfunction, or any    *
*  and all other commercial damages or losses, even if such party      *
*  shall have been informed of the possibility of such damages. This   *
*  limitation of liability shall not apply to liability for death or   *
*  personal injury resulting from such party's negligence to the       *
*  extent applicable law prohibits such limitation. Some               *
*  jurisdictions do not allow the exclusion or limitation of           *
*  incidental or consequential damages, so this exclusion and          *
*  limitation may not apply to You.                                    *
*                                                                      *
************************************************************************

8. Litigation
-------------

Any litigation relating to this License may be brought only in the
courts of a jurisdiction where the defendant maintains its principal
place of business and such litigation shall be governed by laws of that
jurisdiction, without reference to its conflict-of-law provisions.
Nothing in this Section shall prevent a party's ability to bring
cross-claims or counter-claims.

9. Miscellaneous
----------------

This License represents the complete agreement concerning the subject
matter hereof. If any provision of this License is held to be
unenforceable, such provision shall be reformed only to the extent
necessary to make it enforceable. Any law or regulation which provides
that the language of a contract shall be construed against the drafter
shall not be used to construe this License against a Contributor.

10. Versions of the License
---------------------------

10.1. New Versions

Mozilla Foundation is the license steward. Except as provided in Section
10.3, no one other than the license steward has the right to modify or
publish new versions of this License. Each version will be given a
distinguishing version number.

10.2. Effect of New Versions

You may distribute the Covered Software under the terms of the version
of the License under which You originally received the Covered Software,
or under the terms of any subsequent version published by the license
steward.

10.3. Modified Versions

If you create software not governed by this License, and you want to
create a new license for such software, you may create and use a
modified version of this License if you rename the license and remove
any references to the name of the license steward (except to note that
such modified license differs from this License).

10.4. Distributing Source Code Form that is Incompatible With Secondary
Licenses

If You choose to distribute Source Code Form that is Incompatible With
Secondary Licenses under the terms of this version of the License, the
notice described in Exhibit B of this License must be attached.

Exhibit A - Source Code Form License Notice
-------------------------------------------

  This Source Code Form is subject to the terms of the Mozilla Public
  License, v. 2.0. If a copy of the MPL was not distributed with this
  file, You can obtain one at http://mozilla.org/MPL/2.0/.

If it is not possible or desirable to put the notice in a particular
file, then You may include the notice in a location (such as a LICENSE
file in a relevant directory) where a recipient would be likely to look
for such a notice.

You may add additional accurate notices of copyright ownership.

Exhibit B - "Incompatible With Secondary Licenses" Notice
---------------------------------------------------------

  This Source Code Form is "Incompatible With Secondary Licenses", as
  defined by the Mozilla Public License, v. 2.0.

----------------------------------------------------------------------
Following applies to:
./doc/UsingIntelMKL.dox
./doc/UsingIntelMKL.dox
./Eigen/src/Eigenvalues/ComplexSchur_MKL.h
./Eigen/src/Eigenvalues/ComplexSchur_MKL.h
./Eigen/src/Eigenvalues/SelfAdjointEigenSolver_MKL.h
./Eigen/src/Eigenvalues/SelfAdjointEigenSolver_MKL.h
./Eigen/src/Eigenvalues/RealSchur_MKL.h
./Eigen/src/Eigenvalues/RealSchur_MKL.h
./Eigen/src/LU/arch/Inverse_SSE.h
./Eigen/src/LU/arch/Inverse_SSE.h
./Eigen/src/LU/PartialPivLU_MKL.h
./Eigen/src/LU/PartialPivLU_MKL.h
./Eigen/src/QR/HouseholderQR_MKL.h
./Eigen/src/QR/HouseholderQR_MKL.h
./Eigen/src/QR/ColPivHouseholderQR_MKL.h
./Eigen/src/QR/ColPivHouseholderQR_MKL.h
./Eigen/src/SVD/JacobiSVD_MKL.h
./Eigen/src/SVD/JacobiSVD_MKL.h
./Eigen/src/PardisoSupport/PardisoSupport.h
./Eigen/src/PardisoSupport/PardisoSupport.h
./Eigen/src/Core/Assign_MKL.h
./Eigen/src/Core/Assign_MKL.h
./Eigen/src/Core/products/SelfadjointMatrixVector_MKL.h
./Eigen/src/Core/products/SelfadjointMatrixVector_MKL.h
./Eigen/src/Core/products/GeneralMatrixVector_MKL.h
./Eigen/src/Core/products/GeneralMatrixVector_MKL.h
./Eigen/src/Core/products/SelfadjointMatrixMatrix_MKL.h
./Eigen/src/Core/products/SelfadjointMatrixMatrix_MKL.h
./Eigen/src/Core/products/TriangularMatrixMatrix_MKL.h
./Eigen/src/Core/products/TriangularMatrixMatrix_MKL.h
./Eigen/src/Core/products/GeneralMatrixMatrix_MKL.h
./Eigen/src/Core/products/GeneralMatrixMatrix_MKL.h
./Eigen/src/Core/products/TriangularMatrixVector_MKL.h
./Eigen/src/Core/products/TriangularMatrixVector_MKL.h
./Eigen/src/Core/products/GeneralMatrixMatrixTriangular_MKL.h
./Eigen/src/Core/products/GeneralMatrixMatrixTriangular_MKL.h
./Eigen/src/Core/products/TriangularSolverMatrix_MKL.h
./Eigen/src/Core/products/TriangularSolverMatrix_MKL.h
./Eigen/src/Core/util/MKL_support.h
./Eigen/src/Core/util/MKL_support.h
./Eigen/src/Cholesky/LLT_MKL.h
./Eigen/src/Cholesky/LLT_MKL.h

/*
 Copyright (c) 2011, Intel Corporation. All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:

 * Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.  *
   Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the
   distribution.  * Neither the name of Intel Corporation nor the
   names of its contributors may be used to endorse or promote
   products derived from this software without specific prior written
   permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */


----------------------------------------------------------------------
Following applies to:
./unsupported/Eigen/src/LevenbergMarquardt/LevenbergMarquardt.h
./unsupported/Eigen/src/LevenbergMarquardt/LMcovar.h
./unsupported/Eigen/src/LevenbergMarquardt/LMonestep.h
./unsupported/Eigen/src/LevenbergMarquardt/LMpar.h
./unsupported/Eigen/src/LevenbergMarquardt/LMqrsolv.h

Minpack Copyright Notice (1999) University of Chicago.  All rights
reserved

Redistribution and use in source and binary forms, with or
without modification, are permitted provided that the
following conditions are met:

1. Redistributions of source code must retain the above
copyright notice, this list of conditions and the following
disclaimer.

2. Redistributions in binary form must reproduce the above
copyright notice, this list of conditions and the following
disclaimer in the documentation and/or other materials
provided with the distribution.

3. The end-user documentation included with the
redistribution, if any, must include the following
acknowledgment:

   "This product includes software developed by the
   University of Chicago, as Operator of Argonne National
   Laboratory.

Alternately, this acknowledgment may appear in the software
itself, if and wherever such third-party acknowledgments
normally appear.

4. WARRANTY DISCLAIMER. THE SOFTWARE IS SUPPLIED "AS IS"
WITHOUT WARRANTY OF ANY KIND. THE COPYRIGHT HOLDER, THE
UNITED STATES, THE UNITED STATES DEPARTMENT OF ENERGY, AND
THEIR EMPLOYEES: (1) DISCLAIM ANY WARRANTIES, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO ANY IMPLIED WARRANTIES
OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE
OR NON-INFRINGEMENT, (2) DO NOT ASSUME ANY LEGAL LIABILITY
OR RESPONSIBILITY FOR THE ACCURACY, COMPLETENESS, OR
USEFULNESS OF THE SOFTWARE, (3) DO NOT REPRESENT THAT USE OF
THE SOFTWARE WOULD NOT INFRINGE PRIVATELY OWNED RIGHTS, (4)
DO NOT WARRANT THAT THE SOFTWARE WILL FUNCTION
UNINTERRUPTED, THAT IT IS ERROR-FREE OR THAT ANY ERRORS WILL
BE CORRECTED.

5. LIMITATION OF LIABILITY. IN NO EVENT WILL THE COPYRIGHT
HOLDER, THE UNITED STATES, THE UNITED STATES DEPARTMENT OF
ENERGY, OR THEIR EMPLOYEES: BE LIABLE FOR ANY INDIRECT,
INCIDENTAL, CONSEQUENTIAL, SPECIAL OR PUNITIVE DAMAGES OF
ANY KIND OR NATURE, INCLUDING BUT NOT LIMITED TO LOSS OF
PROFITS OR LOSS OF DATA, FOR ANY REASON WHATSOEVER, WHETHER
SUCH LIABILITY IS ASSERTED ON THE BASIS OF CONTRACT, TORT
(INCLUDING NEGLIGENCE OR STRICT LIABILITY), OR OTHERWISE,
EVEN IF ANY OF SAID PARTIES HAS BEEN WARNED OF THE
POSSIBILITY OF SUCH LOSS OR DAMAGES.
