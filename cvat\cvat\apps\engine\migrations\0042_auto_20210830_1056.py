# Generated by Django 3.1.13 on 2021-08-30 10:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0041_auto_20210827_0258"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="cloudstorage",
            name="credentials_type",
            field=models.CharField(
                choices=[
                    ("KEY_SECRET_KEY_PAIR", "KEY_SECRET_KEY_PAIR"),
                    ("ACCOUNT_NAME_TOKEN_PAIR", "ACCOUNT_NAME_TOKEN_PAIR"),
                    ("KEY_FILE_PATH", "KEY_FILE_PATH"),
                    ("ANONYMOUS_ACCESS", "ANONYMOUS_ACCESS"),
                ],
                max_length=29,
            ),
        ),
        migrations.CreateModel(
            name="Manifest",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("filename", models.CharField(default="manifest.jsonl", max_length=1024)),
                (
                    "cloud_storage",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="manifests",
                        to="engine.cloudstorage",
                    ),
                ),
            ],
        ),
    ]
