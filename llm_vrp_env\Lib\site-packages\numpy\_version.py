
# This file was generated by 'versioneer.py' (0.26) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2022-12-25T22:06:03-0500",
 "dirty": false,
 "error": null,
 "full-revisionid": "a28f4f2a472dc826d043abf4442ba258bbd6ade5",
 "version": "1.24.1"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
