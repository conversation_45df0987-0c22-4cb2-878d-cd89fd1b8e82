// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/op_def.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/full_type.pb.h"
#include "tensorflow/core/framework/resource_handle.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
namespace tensorflow {
class OpDef;
class OpDefDefaultTypeInternal;
extern OpDefDefaultTypeInternal _OpDef_default_instance_;
class OpDef_ArgDef;
class OpDef_ArgDefDefaultTypeInternal;
extern OpDef_ArgDefDefaultTypeInternal _OpDef_ArgDef_default_instance_;
class OpDef_AttrDef;
class OpDef_AttrDefDefaultTypeInternal;
extern OpDef_AttrDefDefaultTypeInternal _OpDef_AttrDef_default_instance_;
class OpDeprecation;
class OpDeprecationDefaultTypeInternal;
extern OpDeprecationDefaultTypeInternal _OpDeprecation_default_instance_;
class OpList;
class OpListDefaultTypeInternal;
extern OpListDefaultTypeInternal _OpList_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::OpDef* Arena::CreateMaybeMessage<::tensorflow::OpDef>(Arena*);
template<> ::tensorflow::OpDef_ArgDef* Arena::CreateMaybeMessage<::tensorflow::OpDef_ArgDef>(Arena*);
template<> ::tensorflow::OpDef_AttrDef* Arena::CreateMaybeMessage<::tensorflow::OpDef_AttrDef>(Arena*);
template<> ::tensorflow::OpDeprecation* Arena::CreateMaybeMessage<::tensorflow::OpDeprecation>(Arena*);
template<> ::tensorflow::OpList* Arena::CreateMaybeMessage<::tensorflow::OpList>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class OpDef_ArgDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDef.ArgDef) */ {
 public:
  OpDef_ArgDef();
  virtual ~OpDef_ArgDef();

  OpDef_ArgDef(const OpDef_ArgDef& from);
  OpDef_ArgDef(OpDef_ArgDef&& from) noexcept
    : OpDef_ArgDef() {
    *this = ::std::move(from);
  }

  inline OpDef_ArgDef& operator=(const OpDef_ArgDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpDef_ArgDef& operator=(OpDef_ArgDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpDef_ArgDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpDef_ArgDef* internal_default_instance() {
    return reinterpret_cast<const OpDef_ArgDef*>(
               &_OpDef_ArgDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(OpDef_ArgDef& a, OpDef_ArgDef& b) {
    a.Swap(&b);
  }
  inline void Swap(OpDef_ArgDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpDef_ArgDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpDef_ArgDef* New() const final {
    return CreateMaybeMessage<OpDef_ArgDef>(nullptr);
  }

  OpDef_ArgDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpDef_ArgDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpDef_ArgDef& from);
  void MergeFrom(const OpDef_ArgDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDef_ArgDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpDef.ArgDef";
  }
  protected:
  explicit OpDef_ArgDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleDataFieldNumber = 7,
    kNameFieldNumber = 1,
    kDescriptionFieldNumber = 2,
    kTypeAttrFieldNumber = 4,
    kNumberAttrFieldNumber = 5,
    kTypeListAttrFieldNumber = 6,
    kExperimentalFullTypeFieldNumber = 17,
    kTypeFieldNumber = 3,
    kIsRefFieldNumber = 16,
  };
  // repeated .tensorflow.ResourceHandleProto.DtypeAndShape handle_data = 7;
  int handle_data_size() const;
  void clear_handle_data();
  ::tensorflow::ResourceHandleProto_DtypeAndShape* mutable_handle_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >*
      mutable_handle_data();
  const ::tensorflow::ResourceHandleProto_DtypeAndShape& handle_data(int index) const;
  ::tensorflow::ResourceHandleProto_DtypeAndShape* add_handle_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >&
      handle_data() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string description = 2;
  void clear_description();
  const std::string& description() const;
  void set_description(const std::string& value);
  void set_description(std::string&& value);
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  std::string* mutable_description();
  std::string* release_description();
  void set_allocated_description(std::string* description);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_description();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description(
      std::string* description);

  // string type_attr = 4;
  void clear_type_attr();
  const std::string& type_attr() const;
  void set_type_attr(const std::string& value);
  void set_type_attr(std::string&& value);
  void set_type_attr(const char* value);
  void set_type_attr(const char* value, size_t size);
  std::string* mutable_type_attr();
  std::string* release_type_attr();
  void set_allocated_type_attr(std::string* type_attr);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_type_attr();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type_attr(
      std::string* type_attr);

  // string number_attr = 5;
  void clear_number_attr();
  const std::string& number_attr() const;
  void set_number_attr(const std::string& value);
  void set_number_attr(std::string&& value);
  void set_number_attr(const char* value);
  void set_number_attr(const char* value, size_t size);
  std::string* mutable_number_attr();
  std::string* release_number_attr();
  void set_allocated_number_attr(std::string* number_attr);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_number_attr();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_number_attr(
      std::string* number_attr);

  // string type_list_attr = 6;
  void clear_type_list_attr();
  const std::string& type_list_attr() const;
  void set_type_list_attr(const std::string& value);
  void set_type_list_attr(std::string&& value);
  void set_type_list_attr(const char* value);
  void set_type_list_attr(const char* value, size_t size);
  std::string* mutable_type_list_attr();
  std::string* release_type_list_attr();
  void set_allocated_type_list_attr(std::string* type_list_attr);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_type_list_attr();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type_list_attr(
      std::string* type_list_attr);

  // .tensorflow.FullTypeDef experimental_full_type = 17;
  bool has_experimental_full_type() const;
  void clear_experimental_full_type();
  const ::tensorflow::FullTypeDef& experimental_full_type() const;
  ::tensorflow::FullTypeDef* release_experimental_full_type();
  ::tensorflow::FullTypeDef* mutable_experimental_full_type();
  void set_allocated_experimental_full_type(::tensorflow::FullTypeDef* experimental_full_type);
  void unsafe_arena_set_allocated_experimental_full_type(
      ::tensorflow::FullTypeDef* experimental_full_type);
  ::tensorflow::FullTypeDef* unsafe_arena_release_experimental_full_type();

  // .tensorflow.DataType type = 3;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // bool is_ref = 16;
  void clear_is_ref();
  bool is_ref() const;
  void set_is_ref(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpDef.ArgDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape > handle_data_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_attr_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr number_attr_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_list_attr_;
  ::tensorflow::FullTypeDef* experimental_full_type_;
  int type_;
  bool is_ref_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
};
// -------------------------------------------------------------------

class OpDef_AttrDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDef.AttrDef) */ {
 public:
  OpDef_AttrDef();
  virtual ~OpDef_AttrDef();

  OpDef_AttrDef(const OpDef_AttrDef& from);
  OpDef_AttrDef(OpDef_AttrDef&& from) noexcept
    : OpDef_AttrDef() {
    *this = ::std::move(from);
  }

  inline OpDef_AttrDef& operator=(const OpDef_AttrDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpDef_AttrDef& operator=(OpDef_AttrDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpDef_AttrDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpDef_AttrDef* internal_default_instance() {
    return reinterpret_cast<const OpDef_AttrDef*>(
               &_OpDef_AttrDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(OpDef_AttrDef& a, OpDef_AttrDef& b) {
    a.Swap(&b);
  }
  inline void Swap(OpDef_AttrDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpDef_AttrDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpDef_AttrDef* New() const final {
    return CreateMaybeMessage<OpDef_AttrDef>(nullptr);
  }

  OpDef_AttrDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpDef_AttrDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpDef_AttrDef& from);
  void MergeFrom(const OpDef_AttrDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDef_AttrDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpDef.AttrDef";
  }
  protected:
  explicit OpDef_AttrDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kTypeFieldNumber = 2,
    kDescriptionFieldNumber = 4,
    kDefaultValueFieldNumber = 3,
    kAllowedValuesFieldNumber = 7,
    kMinimumFieldNumber = 6,
    kHasMinimumFieldNumber = 5,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string type = 2;
  void clear_type();
  const std::string& type() const;
  void set_type(const std::string& value);
  void set_type(std::string&& value);
  void set_type(const char* value);
  void set_type(const char* value, size_t size);
  std::string* mutable_type();
  std::string* release_type();
  void set_allocated_type(std::string* type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type(
      std::string* type);

  // string description = 4;
  void clear_description();
  const std::string& description() const;
  void set_description(const std::string& value);
  void set_description(std::string&& value);
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  std::string* mutable_description();
  std::string* release_description();
  void set_allocated_description(std::string* description);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_description();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description(
      std::string* description);

  // .tensorflow.AttrValue default_value = 3;
  bool has_default_value() const;
  void clear_default_value();
  const ::tensorflow::AttrValue& default_value() const;
  ::tensorflow::AttrValue* release_default_value();
  ::tensorflow::AttrValue* mutable_default_value();
  void set_allocated_default_value(::tensorflow::AttrValue* default_value);
  void unsafe_arena_set_allocated_default_value(
      ::tensorflow::AttrValue* default_value);
  ::tensorflow::AttrValue* unsafe_arena_release_default_value();

  // .tensorflow.AttrValue allowed_values = 7;
  bool has_allowed_values() const;
  void clear_allowed_values();
  const ::tensorflow::AttrValue& allowed_values() const;
  ::tensorflow::AttrValue* release_allowed_values();
  ::tensorflow::AttrValue* mutable_allowed_values();
  void set_allocated_allowed_values(::tensorflow::AttrValue* allowed_values);
  void unsafe_arena_set_allocated_allowed_values(
      ::tensorflow::AttrValue* allowed_values);
  ::tensorflow::AttrValue* unsafe_arena_release_allowed_values();

  // int64 minimum = 6;
  void clear_minimum();
  ::PROTOBUF_NAMESPACE_ID::int64 minimum() const;
  void set_minimum(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool has_minimum = 5;
  void clear_has_minimum();
  bool has_minimum() const;
  void set_has_minimum(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpDef.AttrDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::tensorflow::AttrValue* default_value_;
  ::tensorflow::AttrValue* allowed_values_;
  ::PROTOBUF_NAMESPACE_ID::int64 minimum_;
  bool has_minimum_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
};
// -------------------------------------------------------------------

class OpDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDef) */ {
 public:
  OpDef();
  virtual ~OpDef();

  OpDef(const OpDef& from);
  OpDef(OpDef&& from) noexcept
    : OpDef() {
    *this = ::std::move(from);
  }

  inline OpDef& operator=(const OpDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpDef& operator=(OpDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpDef* internal_default_instance() {
    return reinterpret_cast<const OpDef*>(
               &_OpDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(OpDef& a, OpDef& b) {
    a.Swap(&b);
  }
  inline void Swap(OpDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpDef* New() const final {
    return CreateMaybeMessage<OpDef>(nullptr);
  }

  OpDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpDef& from);
  void MergeFrom(const OpDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpDef";
  }
  protected:
  explicit OpDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef OpDef_ArgDef ArgDef;
  typedef OpDef_AttrDef AttrDef;

  // accessors -------------------------------------------------------

  enum : int {
    kInputArgFieldNumber = 2,
    kOutputArgFieldNumber = 3,
    kAttrFieldNumber = 4,
    kControlOutputFieldNumber = 20,
    kNameFieldNumber = 1,
    kSummaryFieldNumber = 5,
    kDescriptionFieldNumber = 6,
    kDeprecationFieldNumber = 8,
    kIsCommutativeFieldNumber = 18,
    kIsAggregateFieldNumber = 16,
    kIsStatefulFieldNumber = 17,
    kAllowsUninitializedInputFieldNumber = 19,
    kIsDistributedCommunicationFieldNumber = 21,
  };
  // repeated .tensorflow.OpDef.ArgDef input_arg = 2;
  int input_arg_size() const;
  void clear_input_arg();
  ::tensorflow::OpDef_ArgDef* mutable_input_arg(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
      mutable_input_arg();
  const ::tensorflow::OpDef_ArgDef& input_arg(int index) const;
  ::tensorflow::OpDef_ArgDef* add_input_arg();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
      input_arg() const;

  // repeated .tensorflow.OpDef.ArgDef output_arg = 3;
  int output_arg_size() const;
  void clear_output_arg();
  ::tensorflow::OpDef_ArgDef* mutable_output_arg(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
      mutable_output_arg();
  const ::tensorflow::OpDef_ArgDef& output_arg(int index) const;
  ::tensorflow::OpDef_ArgDef* add_output_arg();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
      output_arg() const;

  // repeated .tensorflow.OpDef.AttrDef attr = 4;
  int attr_size() const;
  void clear_attr();
  ::tensorflow::OpDef_AttrDef* mutable_attr(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >*
      mutable_attr();
  const ::tensorflow::OpDef_AttrDef& attr(int index) const;
  ::tensorflow::OpDef_AttrDef* add_attr();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >&
      attr() const;

  // repeated string control_output = 20;
  int control_output_size() const;
  void clear_control_output();
  const std::string& control_output(int index) const;
  std::string* mutable_control_output(int index);
  void set_control_output(int index, const std::string& value);
  void set_control_output(int index, std::string&& value);
  void set_control_output(int index, const char* value);
  void set_control_output(int index, const char* value, size_t size);
  std::string* add_control_output();
  void add_control_output(const std::string& value);
  void add_control_output(std::string&& value);
  void add_control_output(const char* value);
  void add_control_output(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& control_output() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_control_output();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string summary = 5;
  void clear_summary();
  const std::string& summary() const;
  void set_summary(const std::string& value);
  void set_summary(std::string&& value);
  void set_summary(const char* value);
  void set_summary(const char* value, size_t size);
  std::string* mutable_summary();
  std::string* release_summary();
  void set_allocated_summary(std::string* summary);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_summary();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_summary(
      std::string* summary);

  // string description = 6;
  void clear_description();
  const std::string& description() const;
  void set_description(const std::string& value);
  void set_description(std::string&& value);
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  std::string* mutable_description();
  std::string* release_description();
  void set_allocated_description(std::string* description);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_description();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_description(
      std::string* description);

  // .tensorflow.OpDeprecation deprecation = 8;
  bool has_deprecation() const;
  void clear_deprecation();
  const ::tensorflow::OpDeprecation& deprecation() const;
  ::tensorflow::OpDeprecation* release_deprecation();
  ::tensorflow::OpDeprecation* mutable_deprecation();
  void set_allocated_deprecation(::tensorflow::OpDeprecation* deprecation);
  void unsafe_arena_set_allocated_deprecation(
      ::tensorflow::OpDeprecation* deprecation);
  ::tensorflow::OpDeprecation* unsafe_arena_release_deprecation();

  // bool is_commutative = 18;
  void clear_is_commutative();
  bool is_commutative() const;
  void set_is_commutative(bool value);

  // bool is_aggregate = 16;
  void clear_is_aggregate();
  bool is_aggregate() const;
  void set_is_aggregate(bool value);

  // bool is_stateful = 17;
  void clear_is_stateful();
  bool is_stateful() const;
  void set_is_stateful(bool value);

  // bool allows_uninitialized_input = 19;
  void clear_allows_uninitialized_input();
  bool allows_uninitialized_input() const;
  void set_allows_uninitialized_input(bool value);

  // bool is_distributed_communication = 21;
  void clear_is_distributed_communication();
  bool is_distributed_communication() const;
  void set_is_distributed_communication(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef > input_arg_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef > output_arg_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_AttrDef > attr_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> control_output_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr summary_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::tensorflow::OpDeprecation* deprecation_;
  bool is_commutative_;
  bool is_aggregate_;
  bool is_stateful_;
  bool allows_uninitialized_input_;
  bool is_distributed_communication_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
};
// -------------------------------------------------------------------

class OpDeprecation :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDeprecation) */ {
 public:
  OpDeprecation();
  virtual ~OpDeprecation();

  OpDeprecation(const OpDeprecation& from);
  OpDeprecation(OpDeprecation&& from) noexcept
    : OpDeprecation() {
    *this = ::std::move(from);
  }

  inline OpDeprecation& operator=(const OpDeprecation& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpDeprecation& operator=(OpDeprecation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpDeprecation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpDeprecation* internal_default_instance() {
    return reinterpret_cast<const OpDeprecation*>(
               &_OpDeprecation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(OpDeprecation& a, OpDeprecation& b) {
    a.Swap(&b);
  }
  inline void Swap(OpDeprecation* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpDeprecation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpDeprecation* New() const final {
    return CreateMaybeMessage<OpDeprecation>(nullptr);
  }

  OpDeprecation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpDeprecation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpDeprecation& from);
  void MergeFrom(const OpDeprecation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDeprecation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpDeprecation";
  }
  protected:
  explicit OpDeprecation(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExplanationFieldNumber = 2,
    kVersionFieldNumber = 1,
  };
  // string explanation = 2;
  void clear_explanation();
  const std::string& explanation() const;
  void set_explanation(const std::string& value);
  void set_explanation(std::string&& value);
  void set_explanation(const char* value);
  void set_explanation(const char* value, size_t size);
  std::string* mutable_explanation();
  std::string* release_explanation();
  void set_allocated_explanation(std::string* explanation);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_explanation();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_explanation(
      std::string* explanation);

  // int32 version = 1;
  void clear_version();
  ::PROTOBUF_NAMESPACE_ID::int32 version() const;
  void set_version(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.OpDeprecation)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr explanation_;
  ::PROTOBUF_NAMESPACE_ID::int32 version_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
};
// -------------------------------------------------------------------

class OpList :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpList) */ {
 public:
  OpList();
  virtual ~OpList();

  OpList(const OpList& from);
  OpList(OpList&& from) noexcept
    : OpList() {
    *this = ::std::move(from);
  }

  inline OpList& operator=(const OpList& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpList& operator=(OpList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OpList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpList* internal_default_instance() {
    return reinterpret_cast<const OpList*>(
               &_OpList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(OpList& a, OpList& b) {
    a.Swap(&b);
  }
  inline void Swap(OpList* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OpList* New() const final {
    return CreateMaybeMessage<OpList>(nullptr);
  }

  OpList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OpList>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OpList& from);
  void MergeFrom(const OpList& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpList* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpList";
  }
  protected:
  explicit OpList(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpFieldNumber = 1,
  };
  // repeated .tensorflow.OpDef op = 1;
  int op_size() const;
  void clear_op();
  ::tensorflow::OpDef* mutable_op(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef >*
      mutable_op();
  const ::tensorflow::OpDef& op(int index) const;
  ::tensorflow::OpDef* add_op();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef >&
      op() const;

  // @@protoc_insertion_point(class_scope:tensorflow.OpList)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef > op_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// OpDef_ArgDef

// string name = 1;
inline void OpDef_ArgDef::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef_ArgDef::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.name)
  return name_.Get();
}
inline void OpDef_ArgDef::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.name)
}
inline void OpDef_ArgDef::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.ArgDef.name)
}
inline void OpDef_ArgDef::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.ArgDef.name)
}
inline void OpDef_ArgDef::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.ArgDef.name)
}
inline std::string* OpDef_ArgDef::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef_ArgDef::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_ArgDef::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.name)
}
inline std::string* OpDef_ArgDef::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.name)
}

// string description = 2;
inline void OpDef_ArgDef::clear_description() {
  description_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef_ArgDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.description)
  return description_.Get();
}
inline void OpDef_ArgDef::set_description(const std::string& value) {
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.description)
}
inline void OpDef_ArgDef::set_description(std::string&& value) {
  
  description_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.ArgDef.description)
}
inline void OpDef_ArgDef::set_description(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.ArgDef.description)
}
inline void OpDef_ArgDef::set_description(const char* value,
    size_t size) {
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.ArgDef.description)
}
inline std::string* OpDef_ArgDef::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.description)
  return description_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef_ArgDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.description)
  
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_ArgDef::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.description)
}
inline std::string* OpDef_ArgDef::unsafe_arena_release_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return description_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_description(
    std::string* description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (description != nullptr) {
    
  } else {
    
  }
  description_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.description)
}

// .tensorflow.DataType type = 3;
inline void OpDef_ArgDef::clear_type() {
  type_ = 0;
}
inline ::tensorflow::DataType OpDef_ArgDef::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.type)
  return static_cast< ::tensorflow::DataType >(type_);
}
inline void OpDef_ArgDef::set_type(::tensorflow::DataType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.type)
}

// string type_attr = 4;
inline void OpDef_ArgDef::clear_type_attr() {
  type_attr_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef_ArgDef::type_attr() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.type_attr)
  return type_attr_.Get();
}
inline void OpDef_ArgDef::set_type_attr(const std::string& value) {
  
  type_attr_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.type_attr)
}
inline void OpDef_ArgDef::set_type_attr(std::string&& value) {
  
  type_attr_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.ArgDef.type_attr)
}
inline void OpDef_ArgDef::set_type_attr(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  type_attr_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.ArgDef.type_attr)
}
inline void OpDef_ArgDef::set_type_attr(const char* value,
    size_t size) {
  
  type_attr_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.ArgDef.type_attr)
}
inline std::string* OpDef_ArgDef::mutable_type_attr() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.type_attr)
  return type_attr_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef_ArgDef::release_type_attr() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.type_attr)
  
  return type_attr_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_ArgDef::set_allocated_type_attr(std::string* type_attr) {
  if (type_attr != nullptr) {
    
  } else {
    
  }
  type_attr_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type_attr,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.type_attr)
}
inline std::string* OpDef_ArgDef::unsafe_arena_release_type_attr() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.type_attr)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return type_attr_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_type_attr(
    std::string* type_attr) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (type_attr != nullptr) {
    
  } else {
    
  }
  type_attr_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      type_attr, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.type_attr)
}

// string number_attr = 5;
inline void OpDef_ArgDef::clear_number_attr() {
  number_attr_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef_ArgDef::number_attr() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.number_attr)
  return number_attr_.Get();
}
inline void OpDef_ArgDef::set_number_attr(const std::string& value) {
  
  number_attr_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.number_attr)
}
inline void OpDef_ArgDef::set_number_attr(std::string&& value) {
  
  number_attr_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.ArgDef.number_attr)
}
inline void OpDef_ArgDef::set_number_attr(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  number_attr_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.ArgDef.number_attr)
}
inline void OpDef_ArgDef::set_number_attr(const char* value,
    size_t size) {
  
  number_attr_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.ArgDef.number_attr)
}
inline std::string* OpDef_ArgDef::mutable_number_attr() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.number_attr)
  return number_attr_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef_ArgDef::release_number_attr() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.number_attr)
  
  return number_attr_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_ArgDef::set_allocated_number_attr(std::string* number_attr) {
  if (number_attr != nullptr) {
    
  } else {
    
  }
  number_attr_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), number_attr,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.number_attr)
}
inline std::string* OpDef_ArgDef::unsafe_arena_release_number_attr() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.number_attr)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return number_attr_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_number_attr(
    std::string* number_attr) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (number_attr != nullptr) {
    
  } else {
    
  }
  number_attr_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      number_attr, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.number_attr)
}

// string type_list_attr = 6;
inline void OpDef_ArgDef::clear_type_list_attr() {
  type_list_attr_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef_ArgDef::type_list_attr() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.type_list_attr)
  return type_list_attr_.Get();
}
inline void OpDef_ArgDef::set_type_list_attr(const std::string& value) {
  
  type_list_attr_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.type_list_attr)
}
inline void OpDef_ArgDef::set_type_list_attr(std::string&& value) {
  
  type_list_attr_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.ArgDef.type_list_attr)
}
inline void OpDef_ArgDef::set_type_list_attr(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  type_list_attr_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.ArgDef.type_list_attr)
}
inline void OpDef_ArgDef::set_type_list_attr(const char* value,
    size_t size) {
  
  type_list_attr_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.ArgDef.type_list_attr)
}
inline std::string* OpDef_ArgDef::mutable_type_list_attr() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.type_list_attr)
  return type_list_attr_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef_ArgDef::release_type_list_attr() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.type_list_attr)
  
  return type_list_attr_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_ArgDef::set_allocated_type_list_attr(std::string* type_list_attr) {
  if (type_list_attr != nullptr) {
    
  } else {
    
  }
  type_list_attr_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type_list_attr,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.type_list_attr)
}
inline std::string* OpDef_ArgDef::unsafe_arena_release_type_list_attr() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.type_list_attr)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return type_list_attr_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_type_list_attr(
    std::string* type_list_attr) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (type_list_attr != nullptr) {
    
  } else {
    
  }
  type_list_attr_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      type_list_attr, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.type_list_attr)
}

// repeated .tensorflow.ResourceHandleProto.DtypeAndShape handle_data = 7;
inline int OpDef_ArgDef::handle_data_size() const {
  return handle_data_.size();
}
inline ::tensorflow::ResourceHandleProto_DtypeAndShape* OpDef_ArgDef::mutable_handle_data(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.handle_data)
  return handle_data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >*
OpDef_ArgDef::mutable_handle_data() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.ArgDef.handle_data)
  return &handle_data_;
}
inline const ::tensorflow::ResourceHandleProto_DtypeAndShape& OpDef_ArgDef::handle_data(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.handle_data)
  return handle_data_.Get(index);
}
inline ::tensorflow::ResourceHandleProto_DtypeAndShape* OpDef_ArgDef::add_handle_data() {
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.ArgDef.handle_data)
  return handle_data_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >&
OpDef_ArgDef::handle_data() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.ArgDef.handle_data)
  return handle_data_;
}

// bool is_ref = 16;
inline void OpDef_ArgDef::clear_is_ref() {
  is_ref_ = false;
}
inline bool OpDef_ArgDef::is_ref() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.is_ref)
  return is_ref_;
}
inline void OpDef_ArgDef::set_is_ref(bool value) {
  
  is_ref_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.is_ref)
}

// .tensorflow.FullTypeDef experimental_full_type = 17;
inline bool OpDef_ArgDef::has_experimental_full_type() const {
  return this != internal_default_instance() && experimental_full_type_ != nullptr;
}
inline const ::tensorflow::FullTypeDef& OpDef_ArgDef::experimental_full_type() const {
  const ::tensorflow::FullTypeDef* p = experimental_full_type_;
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.experimental_full_type)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::FullTypeDef*>(
      &::tensorflow::_FullTypeDef_default_instance_);
}
inline ::tensorflow::FullTypeDef* OpDef_ArgDef::release_experimental_full_type() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.experimental_full_type)
  
  ::tensorflow::FullTypeDef* temp = experimental_full_type_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  experimental_full_type_ = nullptr;
  return temp;
}
inline ::tensorflow::FullTypeDef* OpDef_ArgDef::unsafe_arena_release_experimental_full_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.ArgDef.experimental_full_type)
  
  ::tensorflow::FullTypeDef* temp = experimental_full_type_;
  experimental_full_type_ = nullptr;
  return temp;
}
inline ::tensorflow::FullTypeDef* OpDef_ArgDef::mutable_experimental_full_type() {
  
  if (experimental_full_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FullTypeDef>(GetArenaNoVirtual());
    experimental_full_type_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.experimental_full_type)
  return experimental_full_type_;
}
inline void OpDef_ArgDef::set_allocated_experimental_full_type(::tensorflow::FullTypeDef* experimental_full_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(experimental_full_type_);
  }
  if (experimental_full_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(experimental_full_type)->GetArena();
    if (message_arena != submessage_arena) {
      experimental_full_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, experimental_full_type, submessage_arena);
    }
    
  } else {
    
  }
  experimental_full_type_ = experimental_full_type;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.experimental_full_type)
}

// -------------------------------------------------------------------

// OpDef_AttrDef

// string name = 1;
inline void OpDef_AttrDef::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef_AttrDef::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.name)
  return name_.Get();
}
inline void OpDef_AttrDef::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.name)
}
inline void OpDef_AttrDef::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.AttrDef.name)
}
inline void OpDef_AttrDef::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.AttrDef.name)
}
inline void OpDef_AttrDef::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.AttrDef.name)
}
inline std::string* OpDef_AttrDef::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef_AttrDef::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_AttrDef::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.name)
}
inline std::string* OpDef_AttrDef::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.AttrDef.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_AttrDef::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.AttrDef.name)
}

// string type = 2;
inline void OpDef_AttrDef::clear_type() {
  type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef_AttrDef::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.type)
  return type_.Get();
}
inline void OpDef_AttrDef::set_type(const std::string& value) {
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.type)
}
inline void OpDef_AttrDef::set_type(std::string&& value) {
  
  type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.AttrDef.type)
}
inline void OpDef_AttrDef::set_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.AttrDef.type)
}
inline void OpDef_AttrDef::set_type(const char* value,
    size_t size) {
  
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.AttrDef.type)
}
inline std::string* OpDef_AttrDef::mutable_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.type)
  return type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef_AttrDef::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.type)
  
  return type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_AttrDef::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.type)
}
inline std::string* OpDef_AttrDef::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.AttrDef.type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_AttrDef::unsafe_arena_set_allocated_type(
    std::string* type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (type != nullptr) {
    
  } else {
    
  }
  type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.AttrDef.type)
}

// .tensorflow.AttrValue default_value = 3;
inline bool OpDef_AttrDef::has_default_value() const {
  return this != internal_default_instance() && default_value_ != nullptr;
}
inline const ::tensorflow::AttrValue& OpDef_AttrDef::default_value() const {
  const ::tensorflow::AttrValue* p = default_value_;
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.default_value)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::AttrValue*>(
      &::tensorflow::_AttrValue_default_instance_);
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::release_default_value() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.default_value)
  
  ::tensorflow::AttrValue* temp = default_value_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  default_value_ = nullptr;
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::unsafe_arena_release_default_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.AttrDef.default_value)
  
  ::tensorflow::AttrValue* temp = default_value_;
  default_value_ = nullptr;
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::mutable_default_value() {
  
  if (default_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::AttrValue>(GetArenaNoVirtual());
    default_value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.default_value)
  return default_value_;
}
inline void OpDef_AttrDef::set_allocated_default_value(::tensorflow::AttrValue* default_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(default_value_);
  }
  if (default_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(default_value)->GetArena();
    if (message_arena != submessage_arena) {
      default_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, default_value, submessage_arena);
    }
    
  } else {
    
  }
  default_value_ = default_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.default_value)
}

// string description = 4;
inline void OpDef_AttrDef::clear_description() {
  description_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef_AttrDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.description)
  return description_.Get();
}
inline void OpDef_AttrDef::set_description(const std::string& value) {
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.description)
}
inline void OpDef_AttrDef::set_description(std::string&& value) {
  
  description_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.AttrDef.description)
}
inline void OpDef_AttrDef::set_description(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.AttrDef.description)
}
inline void OpDef_AttrDef::set_description(const char* value,
    size_t size) {
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.AttrDef.description)
}
inline std::string* OpDef_AttrDef::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.description)
  return description_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef_AttrDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.description)
  
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef_AttrDef::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.description)
}
inline std::string* OpDef_AttrDef::unsafe_arena_release_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.AttrDef.description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return description_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef_AttrDef::unsafe_arena_set_allocated_description(
    std::string* description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (description != nullptr) {
    
  } else {
    
  }
  description_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.AttrDef.description)
}

// bool has_minimum = 5;
inline void OpDef_AttrDef::clear_has_minimum() {
  has_minimum_ = false;
}
inline bool OpDef_AttrDef::has_minimum() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.has_minimum)
  return has_minimum_;
}
inline void OpDef_AttrDef::set_has_minimum(bool value) {
  
  has_minimum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.has_minimum)
}

// int64 minimum = 6;
inline void OpDef_AttrDef::clear_minimum() {
  minimum_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 OpDef_AttrDef::minimum() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.minimum)
  return minimum_;
}
inline void OpDef_AttrDef::set_minimum(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  minimum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.minimum)
}

// .tensorflow.AttrValue allowed_values = 7;
inline bool OpDef_AttrDef::has_allowed_values() const {
  return this != internal_default_instance() && allowed_values_ != nullptr;
}
inline const ::tensorflow::AttrValue& OpDef_AttrDef::allowed_values() const {
  const ::tensorflow::AttrValue* p = allowed_values_;
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.allowed_values)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::AttrValue*>(
      &::tensorflow::_AttrValue_default_instance_);
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::release_allowed_values() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.allowed_values)
  
  ::tensorflow::AttrValue* temp = allowed_values_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  allowed_values_ = nullptr;
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::unsafe_arena_release_allowed_values() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.AttrDef.allowed_values)
  
  ::tensorflow::AttrValue* temp = allowed_values_;
  allowed_values_ = nullptr;
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::mutable_allowed_values() {
  
  if (allowed_values_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::AttrValue>(GetArenaNoVirtual());
    allowed_values_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.allowed_values)
  return allowed_values_;
}
inline void OpDef_AttrDef::set_allocated_allowed_values(::tensorflow::AttrValue* allowed_values) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(allowed_values_);
  }
  if (allowed_values) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(allowed_values)->GetArena();
    if (message_arena != submessage_arena) {
      allowed_values = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, allowed_values, submessage_arena);
    }
    
  } else {
    
  }
  allowed_values_ = allowed_values;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.allowed_values)
}

// -------------------------------------------------------------------

// OpDef

// string name = 1;
inline void OpDef::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.name)
  return name_.Get();
}
inline void OpDef::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.name)
}
inline void OpDef::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.name)
}
inline void OpDef::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.name)
}
inline void OpDef::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.name)
}
inline std::string* OpDef::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.name)
}
inline std::string* OpDef::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.name)
}

// repeated .tensorflow.OpDef.ArgDef input_arg = 2;
inline int OpDef::input_arg_size() const {
  return input_arg_.size();
}
inline void OpDef::clear_input_arg() {
  input_arg_.Clear();
}
inline ::tensorflow::OpDef_ArgDef* OpDef::mutable_input_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.input_arg)
  return input_arg_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
OpDef::mutable_input_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.input_arg)
  return &input_arg_;
}
inline const ::tensorflow::OpDef_ArgDef& OpDef::input_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.input_arg)
  return input_arg_.Get(index);
}
inline ::tensorflow::OpDef_ArgDef* OpDef::add_input_arg() {
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.input_arg)
  return input_arg_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
OpDef::input_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.input_arg)
  return input_arg_;
}

// repeated .tensorflow.OpDef.ArgDef output_arg = 3;
inline int OpDef::output_arg_size() const {
  return output_arg_.size();
}
inline void OpDef::clear_output_arg() {
  output_arg_.Clear();
}
inline ::tensorflow::OpDef_ArgDef* OpDef::mutable_output_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.output_arg)
  return output_arg_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
OpDef::mutable_output_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.output_arg)
  return &output_arg_;
}
inline const ::tensorflow::OpDef_ArgDef& OpDef::output_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.output_arg)
  return output_arg_.Get(index);
}
inline ::tensorflow::OpDef_ArgDef* OpDef::add_output_arg() {
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.output_arg)
  return output_arg_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
OpDef::output_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.output_arg)
  return output_arg_;
}

// repeated string control_output = 20;
inline int OpDef::control_output_size() const {
  return control_output_.size();
}
inline void OpDef::clear_control_output() {
  control_output_.Clear();
}
inline const std::string& OpDef::control_output(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.control_output)
  return control_output_.Get(index);
}
inline std::string* OpDef::mutable_control_output(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.control_output)
  return control_output_.Mutable(index);
}
inline void OpDef::set_control_output(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.control_output)
  control_output_.Mutable(index)->assign(value);
}
inline void OpDef::set_control_output(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.control_output)
  control_output_.Mutable(index)->assign(std::move(value));
}
inline void OpDef::set_control_output(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  control_output_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.control_output)
}
inline void OpDef::set_control_output(int index, const char* value, size_t size) {
  control_output_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.control_output)
}
inline std::string* OpDef::add_control_output() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.OpDef.control_output)
  return control_output_.Add();
}
inline void OpDef::add_control_output(const std::string& value) {
  control_output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.control_output)
}
inline void OpDef::add_control_output(std::string&& value) {
  control_output_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.control_output)
}
inline void OpDef::add_control_output(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  control_output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.OpDef.control_output)
}
inline void OpDef::add_control_output(const char* value, size_t size) {
  control_output_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.OpDef.control_output)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OpDef::control_output() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.control_output)
  return control_output_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OpDef::mutable_control_output() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.control_output)
  return &control_output_;
}

// repeated .tensorflow.OpDef.AttrDef attr = 4;
inline int OpDef::attr_size() const {
  return attr_.size();
}
inline void OpDef::clear_attr() {
  attr_.Clear();
}
inline ::tensorflow::OpDef_AttrDef* OpDef::mutable_attr(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.attr)
  return attr_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >*
OpDef::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.attr)
  return &attr_;
}
inline const ::tensorflow::OpDef_AttrDef& OpDef::attr(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.attr)
  return attr_.Get(index);
}
inline ::tensorflow::OpDef_AttrDef* OpDef::add_attr() {
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.attr)
  return attr_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >&
OpDef::attr() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.attr)
  return attr_;
}

// .tensorflow.OpDeprecation deprecation = 8;
inline bool OpDef::has_deprecation() const {
  return this != internal_default_instance() && deprecation_ != nullptr;
}
inline void OpDef::clear_deprecation() {
  if (GetArenaNoVirtual() == nullptr && deprecation_ != nullptr) {
    delete deprecation_;
  }
  deprecation_ = nullptr;
}
inline const ::tensorflow::OpDeprecation& OpDef::deprecation() const {
  const ::tensorflow::OpDeprecation* p = deprecation_;
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.deprecation)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::OpDeprecation*>(
      &::tensorflow::_OpDeprecation_default_instance_);
}
inline ::tensorflow::OpDeprecation* OpDef::release_deprecation() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.deprecation)
  
  ::tensorflow::OpDeprecation* temp = deprecation_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  deprecation_ = nullptr;
  return temp;
}
inline ::tensorflow::OpDeprecation* OpDef::unsafe_arena_release_deprecation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.deprecation)
  
  ::tensorflow::OpDeprecation* temp = deprecation_;
  deprecation_ = nullptr;
  return temp;
}
inline ::tensorflow::OpDeprecation* OpDef::mutable_deprecation() {
  
  if (deprecation_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OpDeprecation>(GetArenaNoVirtual());
    deprecation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.deprecation)
  return deprecation_;
}
inline void OpDef::set_allocated_deprecation(::tensorflow::OpDeprecation* deprecation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete deprecation_;
  }
  if (deprecation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(deprecation);
    if (message_arena != submessage_arena) {
      deprecation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, deprecation, submessage_arena);
    }
    
  } else {
    
  }
  deprecation_ = deprecation;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.deprecation)
}

// string summary = 5;
inline void OpDef::clear_summary() {
  summary_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef::summary() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.summary)
  return summary_.Get();
}
inline void OpDef::set_summary(const std::string& value) {
  
  summary_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.summary)
}
inline void OpDef::set_summary(std::string&& value) {
  
  summary_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.summary)
}
inline void OpDef::set_summary(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  summary_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.summary)
}
inline void OpDef::set_summary(const char* value,
    size_t size) {
  
  summary_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.summary)
}
inline std::string* OpDef::mutable_summary() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.summary)
  return summary_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef::release_summary() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.summary)
  
  return summary_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef::set_allocated_summary(std::string* summary) {
  if (summary != nullptr) {
    
  } else {
    
  }
  summary_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), summary,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.summary)
}
inline std::string* OpDef::unsafe_arena_release_summary() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.summary)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return summary_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef::unsafe_arena_set_allocated_summary(
    std::string* summary) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (summary != nullptr) {
    
  } else {
    
  }
  summary_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      summary, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.summary)
}

// string description = 6;
inline void OpDef::clear_description() {
  description_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.description)
  return description_.Get();
}
inline void OpDef::set_description(const std::string& value) {
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.description)
}
inline void OpDef::set_description(std::string&& value) {
  
  description_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDef.description)
}
inline void OpDef::set_description(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.description)
}
inline void OpDef::set_description(const char* value,
    size_t size) {
  
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.description)
}
inline std::string* OpDef::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.description)
  return description_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.description)
  
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDef::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.description)
}
inline std::string* OpDef::unsafe_arena_release_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDef.description)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return description_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDef::unsafe_arena_set_allocated_description(
    std::string* description) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (description != nullptr) {
    
  } else {
    
  }
  description_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      description, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.description)
}

// bool is_commutative = 18;
inline void OpDef::clear_is_commutative() {
  is_commutative_ = false;
}
inline bool OpDef::is_commutative() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_commutative)
  return is_commutative_;
}
inline void OpDef::set_is_commutative(bool value) {
  
  is_commutative_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_commutative)
}

// bool is_aggregate = 16;
inline void OpDef::clear_is_aggregate() {
  is_aggregate_ = false;
}
inline bool OpDef::is_aggregate() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_aggregate)
  return is_aggregate_;
}
inline void OpDef::set_is_aggregate(bool value) {
  
  is_aggregate_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_aggregate)
}

// bool is_stateful = 17;
inline void OpDef::clear_is_stateful() {
  is_stateful_ = false;
}
inline bool OpDef::is_stateful() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_stateful)
  return is_stateful_;
}
inline void OpDef::set_is_stateful(bool value) {
  
  is_stateful_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_stateful)
}

// bool allows_uninitialized_input = 19;
inline void OpDef::clear_allows_uninitialized_input() {
  allows_uninitialized_input_ = false;
}
inline bool OpDef::allows_uninitialized_input() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.allows_uninitialized_input)
  return allows_uninitialized_input_;
}
inline void OpDef::set_allows_uninitialized_input(bool value) {
  
  allows_uninitialized_input_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.allows_uninitialized_input)
}

// bool is_distributed_communication = 21;
inline void OpDef::clear_is_distributed_communication() {
  is_distributed_communication_ = false;
}
inline bool OpDef::is_distributed_communication() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_distributed_communication)
  return is_distributed_communication_;
}
inline void OpDef::set_is_distributed_communication(bool value) {
  
  is_distributed_communication_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_distributed_communication)
}

// -------------------------------------------------------------------

// OpDeprecation

// int32 version = 1;
inline void OpDeprecation::clear_version() {
  version_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 OpDeprecation::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDeprecation.version)
  return version_;
}
inline void OpDeprecation::set_version(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.OpDeprecation.version)
}

// string explanation = 2;
inline void OpDeprecation::clear_explanation() {
  explanation_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& OpDeprecation::explanation() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDeprecation.explanation)
  return explanation_.Get();
}
inline void OpDeprecation::set_explanation(const std::string& value) {
  
  explanation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.OpDeprecation.explanation)
}
inline void OpDeprecation::set_explanation(std::string&& value) {
  
  explanation_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.OpDeprecation.explanation)
}
inline void OpDeprecation::set_explanation(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  explanation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDeprecation.explanation)
}
inline void OpDeprecation::set_explanation(const char* value,
    size_t size) {
  
  explanation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDeprecation.explanation)
}
inline std::string* OpDeprecation::mutable_explanation() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDeprecation.explanation)
  return explanation_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* OpDeprecation::release_explanation() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDeprecation.explanation)
  
  return explanation_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void OpDeprecation::set_allocated_explanation(std::string* explanation) {
  if (explanation != nullptr) {
    
  } else {
    
  }
  explanation_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), explanation,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDeprecation.explanation)
}
inline std::string* OpDeprecation::unsafe_arena_release_explanation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.OpDeprecation.explanation)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return explanation_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void OpDeprecation::unsafe_arena_set_allocated_explanation(
    std::string* explanation) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (explanation != nullptr) {
    
  } else {
    
  }
  explanation_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      explanation, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDeprecation.explanation)
}

// -------------------------------------------------------------------

// OpList

// repeated .tensorflow.OpDef op = 1;
inline int OpList::op_size() const {
  return op_.size();
}
inline void OpList::clear_op() {
  op_.Clear();
}
inline ::tensorflow::OpDef* OpList::mutable_op(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpList.op)
  return op_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef >*
OpList::mutable_op() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpList.op)
  return &op_;
}
inline const ::tensorflow::OpDef& OpList::op(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpList.op)
  return op_.Get(index);
}
inline ::tensorflow::OpDef* OpList::add_op() {
  // @@protoc_insertion_point(field_add:tensorflow.OpList.op)
  return op_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef >&
OpList::op() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpList.op)
  return op_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
