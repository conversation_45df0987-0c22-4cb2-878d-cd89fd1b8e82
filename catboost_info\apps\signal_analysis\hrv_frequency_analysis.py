import numpy as np
from pyhrv import frequency_domain
import traceback
import re # 用于字符串解析
from apps.utils.logger_helper import Logger

# --- HRV频域分析函数 ---

def analyze_hrv_frequency(rr_intervals, sampling_rate=4):
    """简化版HRV频域分析"""
    if rr_intervals is None:
        rr_intervals = np.array([])
    if not isinstance(rr_intervals, np.ndarray):
        rr_intervals = np.array(rr_intervals)

    if len(rr_intervals) < 5:
        Logger().warning("Not enough RR intervals for frequency domain HRV analysis.")
        return {'lf': 0, 'hf': 0, 'lf_hf_ratio': 0, 'total_power': 0}

    try:
        # 转换为毫秒
        nni_ms = rr_intervals * 1000

        # 调用pyhrv计算频域指标
        results = frequency_domain.welch_psd(nni=nni_ms, show=False)

        # 提取结果
        lf, hf, total_power, lf_hf_ratio = 0, 0, 0, 0

        # 尝试从ReturnTuple中提取数据
        # 检查是否是 biosppy.utils.ReturnTuple 类型
        if str(type(results).__name__) == 'ReturnTuple' and 'biosppy.utils' in str(type(results)):
            Logger().debug("检测到 biosppy.utils.ReturnTuple 类型")
            
            # 检查是否有 as_dict 方法
            if hasattr(results, 'as_dict') and callable(getattr(results, 'as_dict')):
                Logger().debug("使用 as_dict() 方法")
                results_dict = results.as_dict()
                Logger().debug(f"转换后的字典键: {results_dict.keys() if hasattr(results_dict, 'keys') else 'None'}")
                
                # 尝试从字典中获取值
                if 'fft_abs' in results_dict:
                    fft_abs = results_dict['fft_abs']
                    Logger().debug(f"从字典获取 fft_abs: {fft_abs}")
                    if isinstance(fft_abs, (list, tuple)) and len(fft_abs) >= 2:
                        lf = fft_abs[0]  # LF 在第一个位置
                        hf = fft_abs[1]  # HF 在第二个位置
                
                if 'fft_total' in results_dict:
                    total_power = results_dict['fft_total']
            
            # 如果没有 as_dict 方法或者获取失败，直接尝试属性访问
            if (lf == 0 or hf == 0) and hasattr(results, 'fft_abs'):
                Logger().debug("直接使用属性访问 fft_abs")
                fft_abs = results.fft_abs
                Logger().debug(f"属性 fft_abs: {fft_abs}")
                if isinstance(fft_abs, (list, tuple)) and len(fft_abs) >= 2:
                    lf = fft_abs[0]  # LF 在第一个位置
                    hf = fft_abs[1]  # HF 在第二个位置
            
            if total_power == 0 and hasattr(results, 'fft_total'):
                Logger().debug("直接使用属性访问 fft_total")
                total_power = results.fft_total
            
            # 计算 LF/HF 比值
            if hf > 0:
                lf_hf_ratio = lf / hf
                Logger().debug(f"计算 LF/HF 比值: {lf_hf_ratio}")
            else:
                lf_hf_ratio = np.inf
                Logger().debug("HF 为 0，LF/HF 比值设为无穷大")
            
            Logger().debug(f"从 biosppy.utils.ReturnTuple 提取数据: LF={lf}, HF={hf}, 总功率={total_power}, LF/HF比={lf_hf_ratio}")
        
        # 如果不是 ReturnTuple 或者提取失败，尝试其他方法 (例如从字符串解析)
        elif (lf == 0 or hf == 0 or total_power == 0) and hasattr(results, '__str__'):
            # 从字符串表示中尝试提取
            result_str = str(results)
            Logger().debug(f"尝试从字符串中提取值: {result_str}")
            
            # 尝试从字符串中解析 fft_abs
            fft_abs_match = re.search(r'fft_abs=\(([^)]+)\)', result_str)
            if fft_abs_match:
                fft_abs_str = fft_abs_match.group(1)
                Logger().debug(f"找到 fft_abs 字符串: {fft_abs_str}")
                try:
                    fft_abs_values = [float(x.strip()) for x in fft_abs_str.split(',')]
                    if len(fft_abs_values) >= 2:
                        lf = fft_abs_values[0]  # LF 在第一个位置
                        hf = fft_abs_values[1]  # HF 在第二个位置
                        Logger().debug(f"从字符串解析 fft_abs: LF={lf}, HF={hf}")
                except Exception as e:
                    Logger().error(f"解析 fft_abs 字符串时出错: {str(e)}")
            
            # 尝试从字符串中解析 fft_total
            fft_total_match = re.search(r'fft_total=([^,\)]+)', result_str)
            if fft_total_match:
                try:
                    total_power = float(fft_total_match.group(1))
                    Logger().debug(f"从字符串解析 fft_total: {total_power}")
                except Exception as e:
                    Logger().error(f"解析 fft_total 字符串时出错: {str(e)}")
            
            # 重新计算 LF/HF 比值
            if hf > 0:
                lf_hf_ratio = lf / hf
            else:
                lf_hf_ratio = np.inf

        return {
            'lf': float(lf) if lf is not None else 0,
            'hf': float(hf) if hf is not None else 0,
            'lf_hf_ratio': float(lf_hf_ratio) if lf_hf_ratio is not None else 0,
            'total_power': float(total_power) if total_power is not None else 0
        }
    except ImportError:
         Logger().error("pyhrv.frequency_domain not available or Welch method failed. Frequency HRV calculation skipped.")
         return {'lf': 0, 'hf': 0, 'lf_hf_ratio': 0, 'total_power': 0}
    except Exception as e:
        Logger().error(f"HRV频域分析错误: {str(e)}")
        return {'lf': 0, 'hf': 0, 'lf_hf_ratio': 0, 'total_power': 0} 