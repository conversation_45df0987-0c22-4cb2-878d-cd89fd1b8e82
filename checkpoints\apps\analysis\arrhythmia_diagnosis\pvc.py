import traceback

import numpy as np

from apps.utils.logger_helper import <PERSON><PERSON>


def process(signal_segment, sampling_rate, waveform_info):
    """
    心电信号PVC检测
    :param signal_segment: 经过 gain/zero 调整并切割后的有效信号段
    :param waveform_info: *原始信号*计算出的完整波形信息 (来自 get_waveform)
    :param sampling_rate: 采样率
    :return: True or False
    """
    try:
        waveform = waveform_info.get('waveform', {})
        if not waveform:
             return False

        rr_intervals = waveform['rr_intervals']
        if len(rr_intervals) < 3:
            return False

        q_indices_hamilton = waveform['q_peaks']
        s_indices_hamilton = waveform['s_peaks']
        rpeaks = waveform['r_peaks']
        p_positions = waveform['p_peaks']

        median_rr = np.median(rr_intervals)
        window_size = int(0.2 * sampling_rate)

        # --- QRS 宽度计算逻辑，使用来自 waveform 的 Q/S 峰 ---
        qrs_widths = []
        for i in range(len(rpeaks)):
            q_idx = -1
            s_idx = -1
            if i < len(q_indices_hamilton):
                q_idx = q_indices_hamilton[i]
            if i < len(s_indices_hamilton):
                s_idx = s_indices_hamilton[i]

            if q_idx != -1 and s_idx != -1 and s_idx > q_idx:
                qrs_width = (s_idx - q_idx) / sampling_rate
                if 0.05 < qrs_width < 0.2:
                    qrs_widths.append(qrs_width)
            else:
                pass

        normal_qrs_width = np.median(qrs_widths) if qrs_widths else 0.08
        qrs_width_threshold = max(0.12, normal_qrs_width * 1.5)

        pvc_count = 0

        for i in range(1, len(rr_intervals) - 1):
            if rpeaks[i] - window_size < 0 or rpeaks[i] + window_size >= len(signal_segment):
                continue
            current_qrs = signal_segment[rpeaks[i] - window_size:rpeaks[i] + window_size]
            if len(current_qrs) != 2 * window_size:
                continue

            current_rr = rr_intervals[i]
            next_rr = rr_intervals[i + 1]

            is_premature = current_rr < 0.85 * median_rr
            has_compensation = next_rr > 1.15 * median_rr

            # 查找当前 R 波峰对应的 QRS 宽度
            is_wide_qrs = False
            qrs_width_current = -1
            q_idx_curr = -1
            s_idx_curr = -1
            if i < len(q_indices_hamilton):
                 q_idx_curr = q_indices_hamilton[i]
            if i < len(s_indices_hamilton):
                 s_idx_curr = s_indices_hamilton[i]

            if q_idx_curr != -1 and s_idx_curr != -1 and s_idx_curr > q_idx_curr:
                 qrs_width_current = (s_idx_curr - q_idx_curr) / sampling_rate
                 is_wide_qrs = qrs_width_current > qrs_width_threshold

            # 检查当前 QRS 前是否存在 P 波
            p_wave_present = False
            for p_pos in p_positions:
                if 0 < rpeaks[i] - p_pos < 0.25 * sampling_rate:
                    p_wave_present = True
                    break

            reference_beats = []
            for j in range(max(0, i - 5), min(len(rpeaks), i + 6)):
                if j != i and j - 1 >= 0 and rr_intervals[j - 1] >= 0.9 * median_rr:
                    if rpeaks[j] - window_size >= 0 and rpeaks[j] + window_size < len(signal_segment):
                        beat = signal_segment[rpeaks[j] - window_size:rpeaks[j] + window_size]
                        if len(beat) == 2 * window_size:
                            reference_beats.append(beat)
                    else:
                        pass

            morphology_different = True
            if len(reference_beats) >= 2:
                correlations = []
                try:
                    for ref_beat in reference_beats:
                        if np.std(current_qrs) > 1e-6 and np.std(ref_beat) > 1e-6:
                            corr = np.corrcoef(current_qrs, ref_beat)[0, 1]
                            correlations.append(corr)
                        else:
                             correlations.append(1.0)
                    avg_correlation = np.mean(correlations) if correlations else 1.0 # 处理空列表
                    morphology_different = avg_correlation < 0.7
                except ValueError as ve:
                     Logger().warning(f"PVC 形态学比较中计算相关性时出错: {ve}") # 记录警告而非终止
                     morphology_different = True # 出错时假设形态不同

            current_amplitude = np.max(np.abs(current_qrs))
            reference_amplitudes = [np.max(np.abs(beat)) for beat in reference_beats] if reference_beats else []

            is_high_amplitude = False
            if reference_amplitudes:
                median_ref_amp = np.median(reference_amplitudes)
                if median_ref_amp > 1e-6:
                   is_high_amplitude = current_amplitude > 1.4 * median_ref_amp

            pvc_score = 0
            pac_score = 0

            if is_premature: pvc_score += 1.5; pac_score += 1.5
            if has_compensation: pvc_score += 1.0; pac_score += 0.5
            if is_wide_qrs: pvc_score += 3.0
            else: pac_score += 2.0
            if p_wave_present: pac_score += 3.0
            else: pvc_score += 2.5
            if morphology_different: pvc_score += 2.0
            else: pac_score += 1.5
            if is_high_amplitude: pvc_score += 1.5
            else: pac_score += 1.0

            is_pvc = pvc_score > pac_score + 2.0 and pvc_score >= 6.0

            if is_pvc:
                pvc_count += 1

        return pvc_count > 0
    except Exception as e:
        Logger().error(f'PVC诊断异常：\n{traceback.format_exc()}')
        return False
