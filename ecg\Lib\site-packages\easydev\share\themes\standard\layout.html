{% extends "agogo/layout.html" %}



{% block header %}
    <div class="header-wrapper">
        <div class="header">
            {%- if logo %}
              <p class="logo">
                <a href="{{ pathto(master_doc) }}">
                <img class="logo" src="{{ pathto('_static/' + logo, 1) }}" alt="Logo"/>
                <p>{{ shorttitle|e }}</p>
                </a>
               </p>
            {%- endif %}

            {%- block headertitle %}
            <h1><a href="{{ pathto(master_doc) }}">{{ docstitle|e }}</a>
            </h1>
            <div class="rel">
              {%- for rellink in rellinks|reverse %}
                  <a href="{{ pathto(rellink[0]) }}" 
                    title="{{ rellink[1]|striptags|e }}"
                 {{ accesskey(rellink[2]) }}>{{ rellink[3] }}</a>
              {%- if not loop.last %}{{ reldelim2 }}{% endif %}
              {%- endfor %}
            </div>
            {%- endblock %}
       </div>
    </div>
{% endblock %}





{% block rootrellink %}
    {# Top of the main page #}
    <li><a href="http://www.cellnopt.org/software">Return to Thomas Cokelaer Front page</a> |&nbsp;</li>
    <li><a href="{{ pathto('contents') }}">{{project.split('.')[0]|title}} Documentation </a> &raquo;</li>
{% endblock %}


{% block extrahead %}
{% include 'google_head.html' %}
{% endblock %}


{% block footer %}
    <div class="footer-wrapper">
      <div class="footer">
      		<div class="left">
      {% trans path=pathto('copyright'), copyright=copyright|e %}&copy;
       <a href="{{ path }}">Copyright</a> {{ copyright }}.{% endtrans %}
      
     <br/> 
       {%- if last_updated %}
       	{% trans last_updated=last_updated|e %}Last updated on {{ last_updated }}.
       	{% endtrans %}
       {%- endif %}
       <br/> 
       {%- if show_sphinx %}
       {% trans sphinx_version=sphinx_version|e %}Created using <a href="http://sphinx.pocoo.org/">Sphinx</a> {{ sphinx_version }}.{% endtrans %}
       {%- endif %}
       <br/> 
   		</div>

       <div class="right">
          {%- for rellink in rellinks|reverse %}
          <a href="{{ pathto(rellink[0]) }}" title="{{ rellink[1]|striptags|e }}"
             {{ accesskey(rellink[2]) }}>{{ rellink[3] }}</a>
          {%- if not loop.last %}{{ reldelim2 }}{% endif %}
          {%- endfor %}
          <br/>
        </div>

        <div class="clearer"></div>
      </div>
    </div>
{% endblock %}


{% block content %}
    <div class="content-wrapper">
      <div class="content">
        <div class="document">

          {%- block document %}
            {{ super() }}
          {%- endblock %}
        </div>
        <div class="sidebar">

            <!-- <g:plusone size="tall"></g:plusone> -->
            <g:plusone size="standard" count='true'></g:plusone>

          	{%- block sidebarsearch %}
          		{{ super() }}
   	       	<hr />
   	       	<!-- Authors: <AUTHORS>
	          <hr />
          {%- if show_source and has_source and sourcename %}
            This page: <a href="{{ pathto('_sources/' + sourcename, true)|e }}">Show source.</a>
            <hr />


          {%- block sidebartoc %}
           		<h3><a href="index.html">{{ _('Table Of Contents') }}</a></h3>
          		{{ toctree() }}
          	{%- endblock %}
          	<hr />
          {%- endif %}

          {%- endblock %}
        </div>
        <div class="clearer"></div>
      </div>
    </div>
{% endblock %}



{# KEEP THIS as an example

	{% block relbar1 %}
	<div style="background-color: red; text-align: left; padding: 10px 10px 15px 15px">
	</div>
	{{ super() }}
	{% endblock %}
#}

{# put the sidebar before the body #}
{% block sidebar1 %}{{ sidebar() }}{% endblock %}
{% block sidebar2 %}{% endblock %}
