"""
Django应用配置
"""
from django.apps import AppConfig


class AppsConfig(AppConfig):
    """应用配置类"""
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps'
    verbose_name = 'ECG Analysis Application'
    
    def ready(self):
        """应用就绪时的初始化"""
        # 导入并初始化优化组件
        try:
            from apps.utils.startup_optimizer import initialize_optimizations
            initialize_optimizations()
        except Exception as e:
            # 如果初始化失败，记录错误但不阻止应用启动
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to initialize optimizations: {e}")
