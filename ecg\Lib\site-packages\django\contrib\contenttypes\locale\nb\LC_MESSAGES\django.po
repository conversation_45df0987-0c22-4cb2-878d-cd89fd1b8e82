# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2012
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-01-21 12:15+0000\n"
"Last-Translator: Jon <<EMAIL>>\n"
"Language-Team: Norwegian Bokmål (http://www.transifex.com/django/django/"
"language/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Innholdstyper"

msgid "python model class name"
msgstr "python-modellklassenavn"

msgid "content type"
msgstr "innholdstype"

msgid "content types"
msgstr "innholdstyper"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Innholdstype %(ct_id)s objekt har ingen assosiert model"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Innholdstype %(ct_id)s objekt %(obj_id)s finnes ikke"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s-objekter har ikke get_absolute_url()-metode"
