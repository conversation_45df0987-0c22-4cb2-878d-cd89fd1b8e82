PIL/BdfFontFile.py,sha256=eOiPya2ngPj43BlnsoBAhA-m5a8tn2-b-jpHFy9WIIQ,3602
PIL/BlpImagePlugin.py,sha256=Wxm4tVniWtVYzXeWQu5u4GOg13afYEMaYNYf5UrLcok,16044
PIL/BmpImagePlugin.py,sha256=6kAENJTRS8pAWqvmPlGf6z1Yluyc6EDE99-Pry4CaWA,18170
PIL/BufrStubImagePlugin.py,sha256=wlxSRFKTh1Pe1ihNhjwlU5jqfzQJml6S125-5O8unYg,1666
PIL/ContainerIO.py,sha256=BTz6Qlz-VyDmurXnWpQU-lAevLxgcsOcEGZP0CtvSKc,3302
PIL/CurImagePlugin.py,sha256=i5RavrbD_Dmm-sGTGi4GBebFysK-XvRr5mm8jMeu7qY,1816
PIL/DcxImagePlugin.py,sha256=NJLUi8N5yt1xojdPZmpozC6Xq39k1K9KxBJpMVbAXhA,2073
PIL/DdsImagePlugin.py,sha256=4HwLAwNsGAnO3EP4GJAqMd3yzyCv3u-YHO2wnRJsKFk,17105
PIL/EpsImagePlugin.py,sha256=K0BpmxPpc2PMvumpRbRQRJf8xVY8-McF9OxSmycjnWk,16397
PIL/ExifTags.py,sha256=LvHb4g-nwrIP0mPY7tGeNw41cLFihc0-VRmNTADvLCo,10134
PIL/FitsImagePlugin.py,sha256=bVgVGVU25fF012HWe2biVk5Sn8lHWfelFUs3Mu7auwc,2072
PIL/FliImagePlugin.py,sha256=8wuycJ6t6oxtPF3ZqoLufrQQgIvScRVKpsnPhPgkwKU,4696
PIL/FontFile.py,sha256=sX8ulECmeAnZbMEfuTpOhOra30E8zQVLXXzoFPa5JE4,3727
PIL/FpxImagePlugin.py,sha256=nIdCc6vAWAbUhC7zGp4I26HdbkIYvBplEHtDtRgEvtc,7238
PIL/FtexImagePlugin.py,sha256=fSdysRJTe3OKIkvTeDyPTN7U2tbZaTPfqKkaSEqNYlQ,3579
PIL/GbrImagePlugin.py,sha256=S6JTUcCgjYV6sRdVrc9ZiPTr5kcLuVtPlYU21kjApRU,3048
PIL/GdImageFile.py,sha256=1P2tIpjcQ3YiTG2uBbSIF_fuEWyL_qDkzPYJNMJEgcU,2739
PIL/GifImagePlugin.py,sha256=oqyE8h3bcwFRV0WjDHikUfgf6GbFeTPEkT3l4911wlE,38300
PIL/GimpGradientFile.py,sha256=Uxi1xSOmyfHi6RJhbyPD-Uuq6yvhirAT98RjvdOAeyY,3567
PIL/GimpPaletteFile.py,sha256=mPqJ60BA6-bX5fza39rSQ1zGUqMiAeqx-eRX3ZRrD1A,1437
PIL/GribStubImagePlugin.py,sha256=0tq7qbqox15DBxH-LSoqnAYmaPMQbu55-vJisJbxPEg,1660
PIL/Hdf5StubImagePlugin.py,sha256=TTWZfdfzIhKMwbIa3lDqcUki7wlT-GI3JL4eb8t9iwM,1663
PIL/IcnsImagePlugin.py,sha256=eypnvxilZLtobRfKdUo72gdJIhvp_cdIPf7u8Yf7UbA,12396
PIL/IcoImagePlugin.py,sha256=rNLuU-klG6m3gKl6C5GVEbzOp9XTrICAbb24LH-dTS8,11930
PIL/ImImagePlugin.py,sha256=SE_zqCsxsPCK6q_I6mkSivSrWX-NwQ3lwxa-6bdTnis,11275
PIL/Image.py,sha256=uF6xnLjp9j1NKumHpX7ZMJUAnt2-KgSF1YNjou4V0mg,138424
PIL/ImageChops.py,sha256=hZ8EPUPlQIzugsEedV8trkKX0jBCDGb6Cszma6ZeMZQ,8257
PIL/ImageCms.py,sha256=wt06KYXCqlLtPAbTct4X-h9jk-IGekkkSLQ98f_HymU,38188
PIL/ImageColor.py,sha256=4EYd7tfysbfBCAW2RUOCsO-aOOKaEFCyecJWwDUc1eo,9490
PIL/ImageDraw.py,sha256=S6YxXOOfA_ogQhPlrtAMCCE10pWKMvDmk5HFFVhoYIA,37500
PIL/ImageDraw2.py,sha256=xfb7_k7u6rcWUUAdyCnDwL1saBmvJ5YDEPDUNnPAGMU,5728
PIL/ImageEnhance.py,sha256=ZqE_R7iZaxDOuV9Y0taSakmoyP-s4xJ5OizSdBgh-0M,3329
PIL/ImageFile.py,sha256=MxBU7rbA0bnq6mJEW4V4YqJNuk8KnUwP5BkNArwg4V0,25140
PIL/ImageFilter.py,sha256=878DA8Cc_aXGHJyfIied7ev5B6WCIT6pq8ZkSv4ATI8,17711
PIL/ImageFont.py,sha256=bFtSgB2GvxYFUXcCyHA9DEli6t3Zdv3BrTACRwd7aVU,62104
PIL/ImageGrab.py,sha256=7UB7p8rUzvhm-6tOyZoHdMKLBoodecV3wU_YVDDuia4,5808
PIL/ImageMath.py,sha256=j8ABd7poA7HoGfQ7jZmTG3ehTIgBjLvSlcCdG_neLxY,7774
PIL/ImageMode.py,sha256=XMCH0yS-ZslCUKo5_hMOmDCr-_e08OZuPhTQGCbqS3Y,2866
PIL/ImageMorph.py,sha256=YflkGyX8XJJXR0cixXxnP52tRkvUrbXVIk4LXxb8GJs,8267
PIL/ImageOps.py,sha256=nQRzhUYsIR9I8T9RRugLZ3OeY2mypTZSQdjedrATGeo,23113
PIL/ImagePalette.py,sha256=BygMYy1Y_hgQOZJ8oKHi9YZX78VDWb2kUOexPJ0cEGk,8025
PIL/ImagePath.py,sha256=ZnnJuvQNtbKRhCmr61TEmEh1vVV5_90WMEPL8Opy5l8,391
PIL/ImageQt.py,sha256=kiIDndKoyfVKrbiG89QasmGaPsPWRA4AnQLaU9mfRuM,5942
PIL/ImageSequence.py,sha256=jyVU7FmpmrvkBASsZIN-fI21g9DUcCzmSmj0FxS4Fng,2278
PIL/ImageShow.py,sha256=N8THEfxU2L5AgctBLiDkHHFG8oWC2ZecfDIMr9n8WFU,8718
PIL/ImageStat.py,sha256=lnzEWMbFmxBu-kuWVCaOZRT_qH-AJBRVE0OL3toQg-M,3853
PIL/ImageTk.py,sha256=tFvNW2NHf84TKr7mxE0_bosk_IUo3I7Iw6PjvNQMyY4,8780
PIL/ImageTransform.py,sha256=rT7mRQ9FGlaYGT9B9q0A0GHFasJr8KGf9Jg0xcV9LY0,3276
PIL/ImageWin.py,sha256=rYZTiv3ahrz2f52dGeZPMEWkJ8riiT0kchFU5f7OVV0,7457
PIL/ImtImagePlugin.py,sha256=m_qBg5LhZjMFlQF4Q7xObtuVEJ86q6LZdo4rb2aYFIs,2715
PIL/IptcImagePlugin.py,sha256=a7nQ5VkZHUgrR22VUp2ACYrVM1RTPck5a3EqspEgDzw,6370
PIL/Jpeg2KImagePlugin.py,sha256=cHDBCGSS7Ja_SIw9LpWg3nskjK2aIAZH3SyzkbVZzr0,11971
PIL/JpegImagePlugin.py,sha256=BKpHWkErNvKZeCymF95Os-oAAWImLLBoCKmtD0MffUs,30446
PIL/JpegPresets.py,sha256=goMU8pUIumqW27y7OEfr5ABQZsKJ4x3pHvt7zQMdR-U,12619
PIL/McIdasImagePlugin.py,sha256=Qad1_T4XRAcgLoTSgn-LCHUXZ4Hcpqw7HijH3vdtpKA,1908
PIL/MicImagePlugin.py,sha256=KfxwPw4cfn5qzKLxjrSqkntAGrJNjXBFmwWLXmMathQ,2722
PIL/MpegImagePlugin.py,sha256=p5SyFAyD_KexbWRrvlShq7IElYkPp3k6ekwFwgyInD0,1940
PIL/MpoImagePlugin.py,sha256=wI-FXBhpODDCY_WIxQclnBuvSCG1He17DLVfPRNIfPE,6450
PIL/MspImagePlugin.py,sha256=6j3S2n8CjJtsOS7P_M70V1VDK_PqxoFIj3ulzEwJb7w,5843
PIL/PSDraw.py,sha256=XcnamR_C_wE94qVU9mVChdxPGTLsKfkQyyqZphWmuHY,6790
PIL/PaletteFile.py,sha256=nTk5yXjngne55qRAsTk8SIbbWorE_ljw_9qAZ8orCVk,1215
PIL/PalmImagePlugin.py,sha256=YoMpei-CYeeZEdGIIlPkaIeJVwE5cxSYLdHhpLxkOUk,9405
PIL/PcdImagePlugin.py,sha256=gWMlLgqH3pS2Fz2pR1d6SybFjsQEczMlxOPL7_GA8-g,1593
PIL/PcfFontFile.py,sha256=RkM5wUp3SgRpQhpsTBEtk8uuFrQPnBSYBryOmcoRphQ,7401
PIL/PcxImagePlugin.py,sha256=eSD1q4qCVuiewAb_dDm11G40dolheyTs4WQvXEMNLvA,6279
PIL/PdfImagePlugin.py,sha256=Crfss42DurUxdoi54XwLipZdtdM-EyJw_-F9vUVkSEk,9144
PIL/PdfParser.py,sha256=_Fxx8wlZ79tYKe29wU3lCj12CCbt6quBrwn3YfAkG8g,35481
PIL/PixarImagePlugin.py,sha256=OM899PfjdD_glWu3L6Bv87ib9lghZAwgx15wPyvw5G8,1757
PIL/PngImagePlugin.py,sha256=a9LIrhFTxBCOY3fYanhXm2LdTBdENZeK98WLe2A0avI,47941
PIL/PpmImagePlugin.py,sha256=xQLszo8L56etmb5nZrDLEoZ0nazLFjdIggxd1orDUp4,11703
PIL/PsdImagePlugin.py,sha256=x-_MidYhH4-XyXs2oiNg_EhYERSsIOnUX4_Yaoh4o30,8004
PIL/PyAccess.py,sha256=rZGXcdpQs5wzEPCKQ5lexIDuXNbhFcP1BU1IH8vlUSs,10273
PIL/QoiImagePlugin.py,sha256=HW0G6_WfAoLlW2Ga2vSzSG2tzfZoR4ty8CnEQpJN1wg,3776
PIL/SgiImagePlugin.py,sha256=Vx1vyrpk_h1S9eEU6cfd1OvRR7oRCr4IwhtQwX12fW8,6415
PIL/SpiderImagePlugin.py,sha256=X9_JONb350b0pY98RV-Iik9uBbxuLWCAltS-tIoa8zg,9785
PIL/SunImagePlugin.py,sha256=ToCpRbGs9Ileb2cC0tK-XatSc8U8EHEkVemY6bYYTm8,4579
PIL/TarIO.py,sha256=57Vykz4J7Tvey9X1o9Wb2-MCZ14ditGStpQtbpaG_d4,1812
PIL/TgaImagePlugin.py,sha256=KINVC4oWVKZuoyrtmm2uVoJZ0ZOjm3i9JPPfFXW5zNA,6870
PIL/TiffImagePlugin.py,sha256=WqLCmFoofLwgeucqsyxh9Uuj4AJd2TNFEFQl8xDSvo8,78860
PIL/TiffTags.py,sha256=u-wSWOayqnJ8Imjbo1RyATZTRlIka0QH4aktDW88dGc,17112
PIL/WalImageFile.py,sha256=ot4zGTEp8udYiLYLfOlBrXWo_OSaCO2Bjq-G0Mft5Cs,5679
PIL/WebPImagePlugin.py,sha256=gMDVy88T501W5PXITziSxLSTua-r68GIbapymUE6U5Y,11686
PIL/WmfImagePlugin.py,sha256=K0RRLB76i_DLBA93_K1Z2SrMnm7hL79eaAEquYE9Ftc,4905
PIL/XVThumbImagePlugin.py,sha256=JeAEgJSsaKIpKSYdxQKfovyf_3FoA1ZqrWva-LGOB9o,2101
PIL/XbmImagePlugin.py,sha256=2l6Irj_0YiktkO8zVo8jk6B830rxGHUa8RfFDyowl-I,2618
PIL/XpmImagePlugin.py,sha256=29EM1rDZFMojYURvSRInFB3gvgbxCgiwSawtoNgNXEE,3347
PIL/__init__.py,sha256=TeNJFmU0fzHOfhUuBCX2MmDu-bZJqHo34L7hNVLaa1g,2099
PIL/__main__.py,sha256=AR24hLrcu25QlBBkLu0lO0VdZEVpzb-ERKkEJyYSMKU,82
PIL/__pycache__/BdfFontFile.cpython-38.pyc,,
PIL/__pycache__/BlpImagePlugin.cpython-38.pyc,,
PIL/__pycache__/BmpImagePlugin.cpython-38.pyc,,
PIL/__pycache__/BufrStubImagePlugin.cpython-38.pyc,,
PIL/__pycache__/ContainerIO.cpython-38.pyc,,
PIL/__pycache__/CurImagePlugin.cpython-38.pyc,,
PIL/__pycache__/DcxImagePlugin.cpython-38.pyc,,
PIL/__pycache__/DdsImagePlugin.cpython-38.pyc,,
PIL/__pycache__/EpsImagePlugin.cpython-38.pyc,,
PIL/__pycache__/ExifTags.cpython-38.pyc,,
PIL/__pycache__/FitsImagePlugin.cpython-38.pyc,,
PIL/__pycache__/FliImagePlugin.cpython-38.pyc,,
PIL/__pycache__/FontFile.cpython-38.pyc,,
PIL/__pycache__/FpxImagePlugin.cpython-38.pyc,,
PIL/__pycache__/FtexImagePlugin.cpython-38.pyc,,
PIL/__pycache__/GbrImagePlugin.cpython-38.pyc,,
PIL/__pycache__/GdImageFile.cpython-38.pyc,,
PIL/__pycache__/GifImagePlugin.cpython-38.pyc,,
PIL/__pycache__/GimpGradientFile.cpython-38.pyc,,
PIL/__pycache__/GimpPaletteFile.cpython-38.pyc,,
PIL/__pycache__/GribStubImagePlugin.cpython-38.pyc,,
PIL/__pycache__/Hdf5StubImagePlugin.cpython-38.pyc,,
PIL/__pycache__/IcnsImagePlugin.cpython-38.pyc,,
PIL/__pycache__/IcoImagePlugin.cpython-38.pyc,,
PIL/__pycache__/ImImagePlugin.cpython-38.pyc,,
PIL/__pycache__/Image.cpython-38.pyc,,
PIL/__pycache__/ImageChops.cpython-38.pyc,,
PIL/__pycache__/ImageCms.cpython-38.pyc,,
PIL/__pycache__/ImageColor.cpython-38.pyc,,
PIL/__pycache__/ImageDraw.cpython-38.pyc,,
PIL/__pycache__/ImageDraw2.cpython-38.pyc,,
PIL/__pycache__/ImageEnhance.cpython-38.pyc,,
PIL/__pycache__/ImageFile.cpython-38.pyc,,
PIL/__pycache__/ImageFilter.cpython-38.pyc,,
PIL/__pycache__/ImageFont.cpython-38.pyc,,
PIL/__pycache__/ImageGrab.cpython-38.pyc,,
PIL/__pycache__/ImageMath.cpython-38.pyc,,
PIL/__pycache__/ImageMode.cpython-38.pyc,,
PIL/__pycache__/ImageMorph.cpython-38.pyc,,
PIL/__pycache__/ImageOps.cpython-38.pyc,,
PIL/__pycache__/ImagePalette.cpython-38.pyc,,
PIL/__pycache__/ImagePath.cpython-38.pyc,,
PIL/__pycache__/ImageQt.cpython-38.pyc,,
PIL/__pycache__/ImageSequence.cpython-38.pyc,,
PIL/__pycache__/ImageShow.cpython-38.pyc,,
PIL/__pycache__/ImageStat.cpython-38.pyc,,
PIL/__pycache__/ImageTk.cpython-38.pyc,,
PIL/__pycache__/ImageTransform.cpython-38.pyc,,
PIL/__pycache__/ImageWin.cpython-38.pyc,,
PIL/__pycache__/ImtImagePlugin.cpython-38.pyc,,
PIL/__pycache__/IptcImagePlugin.cpython-38.pyc,,
PIL/__pycache__/Jpeg2KImagePlugin.cpython-38.pyc,,
PIL/__pycache__/JpegImagePlugin.cpython-38.pyc,,
PIL/__pycache__/JpegPresets.cpython-38.pyc,,
PIL/__pycache__/McIdasImagePlugin.cpython-38.pyc,,
PIL/__pycache__/MicImagePlugin.cpython-38.pyc,,
PIL/__pycache__/MpegImagePlugin.cpython-38.pyc,,
PIL/__pycache__/MpoImagePlugin.cpython-38.pyc,,
PIL/__pycache__/MspImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PSDraw.cpython-38.pyc,,
PIL/__pycache__/PaletteFile.cpython-38.pyc,,
PIL/__pycache__/PalmImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PcdImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PcfFontFile.cpython-38.pyc,,
PIL/__pycache__/PcxImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PdfImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PdfParser.cpython-38.pyc,,
PIL/__pycache__/PixarImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PngImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PpmImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PsdImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PyAccess.cpython-38.pyc,,
PIL/__pycache__/QoiImagePlugin.cpython-38.pyc,,
PIL/__pycache__/SgiImagePlugin.cpython-38.pyc,,
PIL/__pycache__/SpiderImagePlugin.cpython-38.pyc,,
PIL/__pycache__/SunImagePlugin.cpython-38.pyc,,
PIL/__pycache__/TarIO.cpython-38.pyc,,
PIL/__pycache__/TgaImagePlugin.cpython-38.pyc,,
PIL/__pycache__/TiffImagePlugin.cpython-38.pyc,,
PIL/__pycache__/TiffTags.cpython-38.pyc,,
PIL/__pycache__/WalImageFile.cpython-38.pyc,,
PIL/__pycache__/WebPImagePlugin.cpython-38.pyc,,
PIL/__pycache__/WmfImagePlugin.cpython-38.pyc,,
PIL/__pycache__/XVThumbImagePlugin.cpython-38.pyc,,
PIL/__pycache__/XbmImagePlugin.cpython-38.pyc,,
PIL/__pycache__/XpmImagePlugin.cpython-38.pyc,,
PIL/__pycache__/__init__.cpython-38.pyc,,
PIL/__pycache__/__main__.cpython-38.pyc,,
PIL/__pycache__/_binary.cpython-38.pyc,,
PIL/__pycache__/_deprecate.cpython-38.pyc,,
PIL/__pycache__/_tkinter_finder.cpython-38.pyc,,
PIL/__pycache__/_typing.cpython-38.pyc,,
PIL/__pycache__/_util.cpython-38.pyc,,
PIL/__pycache__/_version.cpython-38.pyc,,
PIL/__pycache__/features.cpython-38.pyc,,
PIL/_binary.py,sha256=DOgQDal7gytd2W-9WTVSGIRG9njd-7hGfuOgiZfZ3DA,2381
PIL/_deprecate.py,sha256=5WrrZE3Q65nRF3pwwRN9wsmY4lqFOJayT6Uxt-i9tf0,2071
PIL/_imaging.cp38-win_amd64.pyd,sha256=_9YWT9hhZ98SLK1rcfHRX0urFRPot26OUbN9XeVp3nY,2582016
PIL/_imagingcms.cp38-win_amd64.pyd,sha256=-f-7fWm1xsY-cHKPA4OWcyFCYXcvI_awH4jmVDjuvCA,262656
PIL/_imagingcms.pyi,sha256=vRzYdyMw9EgPWk7OhtXSJM1VrTfNC167im6kFyhywnc,104
PIL/_imagingft.cp38-win_amd64.pyd,sha256=wmlZXvlgPgHlJFLHKlTKQCSVw32KiLtrR-KUxW84Zbo,1800704
PIL/_imagingft.pyi,sha256=vRzYdyMw9EgPWk7OhtXSJM1VrTfNC167im6kFyhywnc,104
PIL/_imagingmath.cp38-win_amd64.pyd,sha256=MhSXZQP-w2baZXj2IvwnCmjuRJpslYPDn12GjZHzXoU,24064
PIL/_imagingmorph.cp38-win_amd64.pyd,sha256=I2dZHQeY2wgBxb94ucSnqWvc6D-iqP_EzGazFAK-yos,13312
PIL/_imagingtk.cp38-win_amd64.pyd,sha256=-fg4vDq1FynpgW7JkHRfzN1ZHLlz21EwVFJtTIz4AOo,14848
PIL/_tkinter_finder.py,sha256=dO2D28y3vCtR75snv3_2aamMXuCqP-DtwBFe48oSigA,558
PIL/_typing.py,sha256=TYognSeC3Nl4woAjfZECW-_cg9XAuXJ9u3FW8ZWkMXE,420
PIL/_util.py,sha256=ndWXDVsqRZIwlE0J7LcU9jWxskjaUQW0kboDg8Tz6wg,855
PIL/_version.py,sha256=fw9desqCJi5f3wyv9XK8Nq0GOciMnlZDxtLcQyDNfLM,91
PIL/_webp.cp38-win_amd64.pyd,sha256=COOHDtKUTp1KeO2EeyWQ4tpgpIvOMvp5pzWOSW4IwGM,534528
PIL/features.py,sha256=F4YkAHPaiD7uX-EFg1RvSzeIw5xVrhsEukKh-waX1EE,9985
pillow-10.2.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pillow-10.2.0.dist-info/LICENSE,sha256=xJVAhuvbB9KbSSTWo8OQQ0_f-jEW8BV33XonZu5CkoE,56516
pillow-10.2.0.dist-info/METADATA,sha256=-szxlzQ5TLFAF-g_PiCOn20TbwNMx3amoFU4OEXAJ7U,9890
pillow-10.2.0.dist-info/RECORD,,
pillow-10.2.0.dist-info/WHEEL,sha256=3SeyPJ5-Us2Ct5GSftUVKtLSlm-bNefW4m5qd0GLzww,100
pillow-10.2.0.dist-info/top_level.txt,sha256=riZqrk-hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF-NY,4
pillow-10.2.0.dist-info/zip-safe,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
