"""
内存安全的ECG处理包装器
"""
import gc
import numpy as np
import warnings
from functools import wraps

# 禁用所有警告
warnings.filterwarnings("ignore")

def memory_safe_processing(func):
    """内存安全处理装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            # 处理前强制垃圾回收
            gc.collect()
            
            # 限制NumPy数组大小
            for i, arg in enumerate(args):
                if isinstance(arg, np.ndarray) and arg.nbytes > 100 * 1024 * 1024:  # 100MB
                    print(f"Warning: Large array detected ({arg.nbytes / 1024 / 1024:.1f}MB)")
                    # 可以选择降采样或分块处理
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 处理后强制垃圾回收
            gc.collect()
            
            return result
            
        except MemoryError as e:
            print(f"Memory error in {func.__name__}: {e}")
            gc.collect()
            raise
        except Exception as e:
            print(f"Error in {func.__name__}: {e}")
            gc.collect()
            raise
    
    return wrapper

# 应用到关键函数
def apply_memory_safe_wrapper():
    """应用内存安全包装器到关键模块"""
    try:
        # 包装心率分析函数
        import apps.analysis.heart_rate.diagnosis as hr_diagnosis
        if hasattr(hr_diagnosis, 'process'):
            hr_diagnosis.process = memory_safe_processing(hr_diagnosis.process)
        
        # 包装ECG年龄诊断函数
        import apps.analysis.ecg_age.diagnosis as age_diagnosis
        if hasattr(age_diagnosis, 'process'):
            age_diagnosis.process = memory_safe_processing(age_diagnosis.process)
        
        # 包装多结论诊断函数
        import apps.analysis.common.multiple_model.conclusion_diagnostic as conclusion
        if hasattr(conclusion, 'diagnostic'):
            conclusion.diagnostic = memory_safe_processing(conclusion.diagnostic)
        
        print("✅ 内存安全包装器应用成功")
        return True
        
    except Exception as e:
        print(f"❌ 包装器应用失败: {e}")
        return False

if __name__ == "__main__":
    apply_memory_safe_wrapper()
