/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// SavedModelLiftVariablePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SavedModelLiftVariablePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SavedModelLiftVariablePassBase;

  SavedModelLiftVariablePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SavedModelLiftVariablePassBase(const SavedModelLiftVariablePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-savedmodel-lift-variable-pass");
  }
  ::llvm::StringRef getArgument() const override { return "tf-savedmodel-lift-variable-pass"; }

  ::llvm::StringRef getDescription() const override { return "Convert function arguments to global tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SavedModelLiftVariablePass");
  }
  ::llvm::StringRef getName() const override { return "SavedModelLiftVariablePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// SavedModelLiftVariablePass Registration
//===----------------------------------------------------------------------===//

inline void registerSavedModelLiftVariablePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::tf_saved_model::CreateLiftVariablesPass();
  });
}

//===----------------------------------------------------------------------===//
// TensorFlowSavedModel Registration
//===----------------------------------------------------------------------===//

inline void registerTensorFlowSavedModelPasses() {
  registerSavedModelLiftVariablePassPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
