import os
import sys
from datetime import date
from loguru import logger


class Logger:
    def __init__(self):
        # 获取当前日期
        today = date.today()
        # 定义日志目录
        self.log_dir = f"logs/{today.year}/{today.month:02}/{today.day:02}"

        self.logger = logger
        # 清空所有设置
        self.logger.remove()

        # 添加控制台输出的格式
        self.logger.add(sys.stdout,
                        format="<green>{time:YYYYMMDD HH:mm:ss}</green> | "  # 时间
                               "<level>{level}</level>: "  # 等级
                               "<level>{message}</level>",  # 日志内容
                        )

        # 添加不同类型的日志文件
        self.logger.add(
            os.path.join(self.log_dir, "info.log"),
            level="INFO",
            format='{time:YYYYMMDD HH:mm:ss} - {level} - {message}',
            filter=lambda record: record["level"].name == "INFO",  # 只记录 INFO 级别
            rotation="10 MB",
            delay=True
        )

        self.logger.add(
            os.path.join(self.log_dir, "debug.log"),
            level="DEBUG",
            format='{time:YYYYMMDD HH:mm:ss} - {level} - {message}',
            filter=lambda record: record["level"].name == "DEBUG",  # 只记录 DEBUG 级别
            rotation="10 MB",
            delay=True
        )

        self.logger.add(
            os.path.join(self.log_dir, "error.log"),
            level="ERROR",
            format='{time:YYYYMMDD HH:mm:ss} - {level} - {message}',
            filter=lambda record: record["level"].name == "ERROR",  # 只记录 ERROR 级别
            rotation="10 MB",
            delay=True
        )

        self.logger.add(
            os.path.join(self.log_dir, "warning.log"),
            level="WARNING",
            format='{time:YYYYMMDD HH:mm:ss} - {level} - {message}',
            filter=lambda record: record["level"].name == "WARNING",  # 只记录 WARNING 级别
            rotation="10 MB",
            delay=True
        )

    def info(self, message):
        self.logger.info(message)

    def debug(self, message):
        self.logger.debug(message)

    def error(self, message):
        self.logger.error(message)

    def warning(self, message):
        self.logger.warning(message)

    def exception(self, message):
        self.logger.exception(message)

