# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <o<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2014
# <PERSON>lia Volochii <<EMAIL>>, 2021
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-01-20 00:17+0000\n"
"Last-Translator: <PERSON><PERSON> Volochii <<EMAIL>>\n"
"Language-Team: Ukrainian (http://www.transifex.com/django/django/language/"
"uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != "
"11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % "
"100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || "
"(n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

msgid "Content Types"
msgstr "Типи вмісту"

msgid "python model class name"
msgstr "імʼя класу моделі"

msgid "content type"
msgstr "тип вмісту"

msgid "content types"
msgstr "типи вмісту"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Тип вмісту %(ct_id)s не має пов'язанної моделі"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr ""
"Обʼєкт з ідентифікатором %(ct_id)s, що має тип вмісту %(obj_id)s не існує"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Обʼєкт типу %(ct_name)s не має методу get_absolute_url()"
