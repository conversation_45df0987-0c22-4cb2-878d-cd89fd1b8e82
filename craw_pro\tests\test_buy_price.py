#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import logging
from selenium.webdriver.common.keys import Keys

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_driver():
    """设置Chrome驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def solve_verification(driver):
    """解决验证问题"""
    try:
        time.sleep(2)
        
        # 查找验证问题文本
        verification_text = ""
        try:
            body_text = driver.find_element(By.TAG_NAME, "body").text
            verification_text = body_text.strip()
        except:
            pass
        
        logger.info(f"🤖 验证问题: {verification_text}")
        
        answer = None
        
        # 字母序列问题
        if "ABC后3个大写字母" in verification_text:
            answer = "DEF"
            logger.info("🔤 字母序列: ABC后3个字母是 DEF")
        elif "XYZ前3个大写字母" in verification_text:
            answer = "UVW"
            logger.info("🔤 字母序列: XYZ前3个字母是 UVW")
        elif "羽毛球有几根毛" in verification_text or "几根毛" in verification_text:
            answer = "16"
            logger.info("🏸 羽毛球知识: 羽毛球有16根毛")
        else:
            # 数学计算模式
            math_patterns = [
                r'(\d+)\s*[+＋]\s*(\d+)',
                r'(\d+)\s*[-－]\s*(\d+)',
                r'(\d+)\s*[×*]\s*(\d+)',
                r'(\d+)\s*[÷/]\s*(\d+)'
            ]
            
            for pattern in math_patterns:
                match = re.search(pattern, verification_text)
                if match:
                    num1, num2 = int(match.group(1)), int(match.group(2))
                    if '+' in pattern or '＋' in pattern:
                        answer = str(num1 + num2)
                    elif '-' in pattern or '－' in pattern:
                        answer = str(num1 - num2)
                    elif '×' in pattern or '*' in pattern:
                        answer = str(num1 * num2)
                    elif '÷' in pattern or '/' in pattern:
                        answer = str(num1 // num2)
                    logger.info(f"🧮 计算结果: {num1} ? {num2} = {answer}")
                    break
        
        if answer:
            # 查找输入框 - 根据调试结果，输入框name为"a"
            input_selectors = [
                'input[name="a"]',
                'input[type="text"]',
                'input[name*="code"]',
                'input[name*="answer"]',
                'input:not([type="hidden"]):not([type="submit"]):not([type="button"])'
            ]
            
            input_element = None
            for selector in input_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        input_element = elements[0]
                        logger.info(f"✅ 找到输入框: {selector}")
                        break
                except:
                    continue
            
            if input_element:
                # 清空并输入答案
                input_element.clear()
                input_element.send_keys(answer)
                logger.info(f"✍️ 输入答案: {answer}")
                
                # 尝试使用回车键提交
                try:
                    input_element.send_keys(Keys.RETURN)
                    logger.info("🔘 使用回车键提交")
                    time.sleep(3)
                    return True
                except:
                    pass
                
                # 查找提交按钮
                submit_selectors = [
                    'input[name="submit"]',
                    'input[type="submit"]',
                    'button[type="submit"]',
                    'button'
                ]
                
                for selector in submit_selectors:
                    try:
                        buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                        if buttons:
                            buttons[0].click()
                            logger.info(f"🔘 点击提交按钮: {selector}")
                            time.sleep(3)
                            return True
                    except:
                        continue
                
                logger.warning("⚠️ 未找到提交按钮")
            else:
                logger.warning("⚠️ 未找到输入框")
        else:
            logger.warning("⚠️ 未能识别验证问题格式")
            
    except Exception as e:
        logger.warning(f"⚠️ 验证处理失败: {e}")
    
    return False

def test_buy_price_extraction(eid):
    """测试入手价信息提取"""
    buy_url = f"https://www.badmintoncn.com/cbo_eq/view_buy.php?eid={eid}"
    
    driver = setup_driver()
    logger.info(f"✅ Chrome浏览器启动成功")
    
    try:
        logger.info(f"💰 访问入手价页面: {buy_url}")
        driver.get(buy_url)
        time.sleep(3)
        
        # 检查是否需要验证
        if solve_verification(driver):
            logger.info("✅ 入手价页面验证成功")
        
        # 保存页面源码用于调试
        with open(f'debug_buy_price_{eid}.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        
        # 获取页面源码
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        clean_text = soup.get_text()
        
        logger.info(f"页面标题: {driver.title}")
        
        price_info = {}
        
        # 提取全新均价
        new_price_patterns = [
            r'最近全新均价[：:\s]*[¥￥]?\s*(\d+)',
            r'全新均价[：:\s]*[¥￥]?\s*(\d+)',
            r'新品均价[：:\s]*[¥￥]?\s*(\d+)'
        ]
        
        for pattern in new_price_patterns:
            matches = re.findall(pattern, clean_text)
            if matches:
                price_info['new_avg_price'] = matches[0]
                logger.info(f"✅ 提取到全新均价: {matches[0]}元")
                break
        
        # 提取二手均价
        used_price_patterns = [
            r'最近二手均价[：:\s]*[¥￥]?\s*(\d+)',
            r'二手均价[：:\s]*[¥￥]?\s*(\d+)',
            r'闲置均价[：:\s]*[¥￥]?\s*(\d+)'
        ]
        
        for pattern in used_price_patterns:
            matches = re.findall(pattern, clean_text)
            if matches:
                price_info['used_avg_price'] = matches[0]
                logger.info(f"✅ 提取到二手均价: {matches[0]}元")
                break
        
        # 提取登记球友数
        user_count_patterns = [
            r'总登记球友[：:\s]*(\d+)',
            r'登记球友[：:\s]*(\d+)',
            r'球友登记[：:\s]*(\d+)',
            r'总共\s*(\d+)\s*位球友',
            r'共有\s*(\d+)\s*位'
        ]
        
        for pattern in user_count_patterns:
            matches = re.findall(pattern, clean_text)
            if matches:
                price_info['registered_users'] = matches[0]
                logger.info(f"✅ 提取到登记球友数: {matches[0]}人")
                break
        
        # 查看页面中的表格和关键信息
        tables = soup.find_all('table')
        logger.info(f"发现 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            table_text = table.get_text()
            if '均价' in table_text or '球友' in table_text:
                logger.info(f"表格 {i+1} 包含价格相关信息:")
                rows = table.find_all('tr')
                for row in rows[:5]:  # 只显示前5行
                    cells = row.find_all(['td', 'th'])
                    if cells:
                        row_text = ' | '.join([cell.get_text(strip=True) for cell in cells])
                        logger.info(f"  {row_text}")
        
        if not price_info:
            logger.warning(f"⚠️ 未能提取到入手价信息")
            
            # 输出页面关键文本片段用于分析
            logger.info("页面关键文本片段:")
            lines = clean_text.split('\n')
            for line in lines:
                line = line.strip()
                if line and ('均价' in line or '球友' in line or '登记' in line):
                    logger.info(f"  关键行: {line}")
        else:
            logger.info(f"✅ 成功提取入手价信息: {price_info}")
            
        return price_info
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        return {}
        
    finally:
        driver.quit()
        logger.info("🔄 浏览器已关闭")

if __name__ == "__main__":
    # 测试装备ID: 22974 (8888AX ultra 斩鬼刀ultra)
    test_eid = "22974"
    
    print(f"🏸 测试入手价信息提取")
    print(f"🔍 装备ID: {test_eid}")
    print("="*60)
    
    result = test_buy_price_extraction(test_eid)
    
    print("\n" + "="*60)
    print(f"📊 测试结果:")
    if result:
        for key, value in result.items():
            key_name = {
                'new_avg_price': '全新均价',
                'used_avg_price': '二手均价',
                'registered_users': '登记球友数'
            }.get(key, key)
            print(f"  {key_name}: {value}")
    else:
        print("  未提取到任何入手价信息") 