# 建立 python 环境
FROM python:3.9

# 镜像作者
MAINTAINER zhouj

# 设置 python 环境变量
ENV PYTHONUNBUFFERED 1

# ============= 新增部分开始 =============
# 安装系统依赖（包含字体和wkhtmltopdf所需基础环境）
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    fontconfig \
    xfonts-75dpi \
    xfonts-base \
    ca-certificates \
    wget \
    # 安装 Noto Sans 字体
    fonts-noto-cjk \
    && rm -rf /var/lib/apt/lists/*

# 安装静态编译版 wkhtmltopdf（无需Qt依赖）
RUN wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox_0.12.6-1.buster_amd64.deb && \
    dpkg -i wkhtmltox_0.12.6-1.buster_amd64.deb && \
    rm wkhtmltox_0.12.6-1.buster_amd64.deb
# ============= 新增部分结束 =============

# 设置容器内工作目录
WORKDIR /app
ADD . .

# 配置时区和pip源
RUN cp ./Shanghai /etc/localtime
RUN python3 -m pip config set global.index-url https://pypi.mirrors.ustc.edu.cn/simple
RUN python3 -m pip config set install.trusted-host pypi.mirrors.ustc.edu.cn

# 安装Python依赖
RUN python3 -m pip install -r requirements.txt

# 设置执行权限
RUN chmod +x ./manage.py