// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/debug/debugger_event_metadata.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto;
namespace third_party {
namespace tensorflow {
namespace core {
namespace debug {
class DebuggerEventMetadata;
class DebuggerEventMetadataDefaultTypeInternal;
extern DebuggerEventMetadataDefaultTypeInternal _DebuggerEventMetadata_default_instance_;
}  // namespace debug
}  // namespace core
}  // namespace tensorflow
}  // namespace third_party
PROTOBUF_NAMESPACE_OPEN
template<> ::third_party::tensorflow::core::debug::DebuggerEventMetadata* Arena::CreateMaybeMessage<::third_party::tensorflow::core::debug::DebuggerEventMetadata>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace third_party {
namespace tensorflow {
namespace core {
namespace debug {

// ===================================================================

class DebuggerEventMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:third_party.tensorflow.core.debug.DebuggerEventMetadata) */ {
 public:
  DebuggerEventMetadata();
  virtual ~DebuggerEventMetadata();

  DebuggerEventMetadata(const DebuggerEventMetadata& from);
  DebuggerEventMetadata(DebuggerEventMetadata&& from) noexcept
    : DebuggerEventMetadata() {
    *this = ::std::move(from);
  }

  inline DebuggerEventMetadata& operator=(const DebuggerEventMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggerEventMetadata& operator=(DebuggerEventMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DebuggerEventMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebuggerEventMetadata* internal_default_instance() {
    return reinterpret_cast<const DebuggerEventMetadata*>(
               &_DebuggerEventMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DebuggerEventMetadata& a, DebuggerEventMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggerEventMetadata* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DebuggerEventMetadata* New() const final {
    return CreateMaybeMessage<DebuggerEventMetadata>(nullptr);
  }

  DebuggerEventMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DebuggerEventMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DebuggerEventMetadata& from);
  void MergeFrom(const DebuggerEventMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggerEventMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "third_party.tensorflow.core.debug.DebuggerEventMetadata";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceFieldNumber = 1,
    kOutputSlotFieldNumber = 2,
    kNumChunksFieldNumber = 3,
    kChunkIndexFieldNumber = 4,
  };
  // string device = 1;
  void clear_device();
  const std::string& device() const;
  void set_device(const std::string& value);
  void set_device(std::string&& value);
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  std::string* mutable_device();
  std::string* release_device();
  void set_allocated_device(std::string* device);

  // int32 output_slot = 2;
  void clear_output_slot();
  ::PROTOBUF_NAMESPACE_ID::int32 output_slot() const;
  void set_output_slot(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 num_chunks = 3;
  void clear_num_chunks();
  ::PROTOBUF_NAMESPACE_ID::int32 num_chunks() const;
  void set_num_chunks(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 chunk_index = 4;
  void clear_chunk_index();
  ::PROTOBUF_NAMESPACE_ID::int32 chunk_index() const;
  void set_chunk_index(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:third_party.tensorflow.core.debug.DebuggerEventMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
  ::PROTOBUF_NAMESPACE_ID::int32 output_slot_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_chunks_;
  ::PROTOBUF_NAMESPACE_ID::int32 chunk_index_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DebuggerEventMetadata

// string device = 1;
inline void DebuggerEventMetadata::clear_device() {
  device_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& DebuggerEventMetadata::device() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
  return device_.GetNoArena();
}
inline void DebuggerEventMetadata::set_device(const std::string& value) {
  
  device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}
inline void DebuggerEventMetadata::set_device(std::string&& value) {
  
  device_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}
inline void DebuggerEventMetadata::set_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}
inline void DebuggerEventMetadata::set_device(const char* value, size_t size) {
  
  device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}
inline std::string* DebuggerEventMetadata::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
  return device_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* DebuggerEventMetadata::release_device() {
  // @@protoc_insertion_point(field_release:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
  
  return device_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void DebuggerEventMetadata::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  device_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device);
  // @@protoc_insertion_point(field_set_allocated:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}

// int32 output_slot = 2;
inline void DebuggerEventMetadata::clear_output_slot() {
  output_slot_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebuggerEventMetadata::output_slot() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.output_slot)
  return output_slot_;
}
inline void DebuggerEventMetadata::set_output_slot(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  output_slot_ = value;
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.output_slot)
}

// int32 num_chunks = 3;
inline void DebuggerEventMetadata::clear_num_chunks() {
  num_chunks_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebuggerEventMetadata::num_chunks() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.num_chunks)
  return num_chunks_;
}
inline void DebuggerEventMetadata::set_num_chunks(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_chunks_ = value;
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.num_chunks)
}

// int32 chunk_index = 4;
inline void DebuggerEventMetadata::clear_chunk_index() {
  chunk_index_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebuggerEventMetadata::chunk_index() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.chunk_index)
  return chunk_index_;
}
inline void DebuggerEventMetadata::set_chunk_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  chunk_index_ = value;
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.chunk_index)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace debug
}  // namespace core
}  // namespace tensorflow
}  // namespace third_party

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto
