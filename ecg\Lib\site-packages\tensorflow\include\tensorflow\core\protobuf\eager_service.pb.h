// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/eager_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/device_attributes.pb.h"
#include "tensorflow/core/framework/function.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/versions.pb.h"
#include "tensorflow/core/protobuf/remote_tensor_handle.pb.h"
#include "tensorflow/core/protobuf/tensorflow_server.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[26]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
namespace tensorflow {
namespace eager {
class CleanupFunctionOp;
class CleanupFunctionOpDefaultTypeInternal;
extern CleanupFunctionOpDefaultTypeInternal _CleanupFunctionOp_default_instance_;
class CloseContextRequest;
class CloseContextRequestDefaultTypeInternal;
extern CloseContextRequestDefaultTypeInternal _CloseContextRequest_default_instance_;
class CloseContextResponse;
class CloseContextResponseDefaultTypeInternal;
extern CloseContextResponseDefaultTypeInternal _CloseContextResponse_default_instance_;
class CreateContextRequest;
class CreateContextRequestDefaultTypeInternal;
extern CreateContextRequestDefaultTypeInternal _CreateContextRequest_default_instance_;
class CreateContextResponse;
class CreateContextResponseDefaultTypeInternal;
extern CreateContextResponseDefaultTypeInternal _CreateContextResponse_default_instance_;
class EnqueueRequest;
class EnqueueRequestDefaultTypeInternal;
extern EnqueueRequestDefaultTypeInternal _EnqueueRequest_default_instance_;
class EnqueueResponse;
class EnqueueResponseDefaultTypeInternal;
extern EnqueueResponseDefaultTypeInternal _EnqueueResponse_default_instance_;
class KeepAliveRequest;
class KeepAliveRequestDefaultTypeInternal;
extern KeepAliveRequestDefaultTypeInternal _KeepAliveRequest_default_instance_;
class KeepAliveResponse;
class KeepAliveResponseDefaultTypeInternal;
extern KeepAliveResponseDefaultTypeInternal _KeepAliveResponse_default_instance_;
class Operation;
class OperationDefaultTypeInternal;
extern OperationDefaultTypeInternal _Operation_default_instance_;
class Operation_AttrsEntry_DoNotUse;
class Operation_AttrsEntry_DoNotUseDefaultTypeInternal;
extern Operation_AttrsEntry_DoNotUseDefaultTypeInternal _Operation_AttrsEntry_DoNotUse_default_instance_;
class Operation_Input;
class Operation_InputDefaultTypeInternal;
extern Operation_InputDefaultTypeInternal _Operation_Input_default_instance_;
class QueueItem;
class QueueItemDefaultTypeInternal;
extern QueueItemDefaultTypeInternal _QueueItem_default_instance_;
class QueueResponse;
class QueueResponseDefaultTypeInternal;
extern QueueResponseDefaultTypeInternal _QueueResponse_default_instance_;
class RegisterFunctionOp;
class RegisterFunctionOpDefaultTypeInternal;
extern RegisterFunctionOpDefaultTypeInternal _RegisterFunctionOp_default_instance_;
class RunComponentFunctionRequest;
class RunComponentFunctionRequestDefaultTypeInternal;
extern RunComponentFunctionRequestDefaultTypeInternal _RunComponentFunctionRequest_default_instance_;
class RunComponentFunctionResponse;
class RunComponentFunctionResponseDefaultTypeInternal;
extern RunComponentFunctionResponseDefaultTypeInternal _RunComponentFunctionResponse_default_instance_;
class SendPackedHandleOp;
class SendPackedHandleOpDefaultTypeInternal;
extern SendPackedHandleOpDefaultTypeInternal _SendPackedHandleOp_default_instance_;
class SendPackedHandleOp_Handle;
class SendPackedHandleOp_HandleDefaultTypeInternal;
extern SendPackedHandleOp_HandleDefaultTypeInternal _SendPackedHandleOp_Handle_default_instance_;
class SendPackedHandleOp_LocalTensorHandle;
class SendPackedHandleOp_LocalTensorHandleDefaultTypeInternal;
extern SendPackedHandleOp_LocalTensorHandleDefaultTypeInternal _SendPackedHandleOp_LocalTensorHandle_default_instance_;
class SendTensorOp;
class SendTensorOpDefaultTypeInternal;
extern SendTensorOpDefaultTypeInternal _SendTensorOp_default_instance_;
class SyncRemoteExecutorForStream;
class SyncRemoteExecutorForStreamDefaultTypeInternal;
extern SyncRemoteExecutorForStreamDefaultTypeInternal _SyncRemoteExecutorForStream_default_instance_;
class UpdateContextRequest;
class UpdateContextRequestDefaultTypeInternal;
extern UpdateContextRequestDefaultTypeInternal _UpdateContextRequest_default_instance_;
class UpdateContextResponse;
class UpdateContextResponseDefaultTypeInternal;
extern UpdateContextResponseDefaultTypeInternal _UpdateContextResponse_default_instance_;
class WaitQueueDoneRequest;
class WaitQueueDoneRequestDefaultTypeInternal;
extern WaitQueueDoneRequestDefaultTypeInternal _WaitQueueDoneRequest_default_instance_;
class WaitQueueDoneResponse;
class WaitQueueDoneResponseDefaultTypeInternal;
extern WaitQueueDoneResponseDefaultTypeInternal _WaitQueueDoneResponse_default_instance_;
}  // namespace eager
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::eager::CleanupFunctionOp* Arena::CreateMaybeMessage<::tensorflow::eager::CleanupFunctionOp>(Arena*);
template<> ::tensorflow::eager::CloseContextRequest* Arena::CreateMaybeMessage<::tensorflow::eager::CloseContextRequest>(Arena*);
template<> ::tensorflow::eager::CloseContextResponse* Arena::CreateMaybeMessage<::tensorflow::eager::CloseContextResponse>(Arena*);
template<> ::tensorflow::eager::CreateContextRequest* Arena::CreateMaybeMessage<::tensorflow::eager::CreateContextRequest>(Arena*);
template<> ::tensorflow::eager::CreateContextResponse* Arena::CreateMaybeMessage<::tensorflow::eager::CreateContextResponse>(Arena*);
template<> ::tensorflow::eager::EnqueueRequest* Arena::CreateMaybeMessage<::tensorflow::eager::EnqueueRequest>(Arena*);
template<> ::tensorflow::eager::EnqueueResponse* Arena::CreateMaybeMessage<::tensorflow::eager::EnqueueResponse>(Arena*);
template<> ::tensorflow::eager::KeepAliveRequest* Arena::CreateMaybeMessage<::tensorflow::eager::KeepAliveRequest>(Arena*);
template<> ::tensorflow::eager::KeepAliveResponse* Arena::CreateMaybeMessage<::tensorflow::eager::KeepAliveResponse>(Arena*);
template<> ::tensorflow::eager::Operation* Arena::CreateMaybeMessage<::tensorflow::eager::Operation>(Arena*);
template<> ::tensorflow::eager::Operation_AttrsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::eager::Operation_AttrsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::eager::Operation_Input* Arena::CreateMaybeMessage<::tensorflow::eager::Operation_Input>(Arena*);
template<> ::tensorflow::eager::QueueItem* Arena::CreateMaybeMessage<::tensorflow::eager::QueueItem>(Arena*);
template<> ::tensorflow::eager::QueueResponse* Arena::CreateMaybeMessage<::tensorflow::eager::QueueResponse>(Arena*);
template<> ::tensorflow::eager::RegisterFunctionOp* Arena::CreateMaybeMessage<::tensorflow::eager::RegisterFunctionOp>(Arena*);
template<> ::tensorflow::eager::RunComponentFunctionRequest* Arena::CreateMaybeMessage<::tensorflow::eager::RunComponentFunctionRequest>(Arena*);
template<> ::tensorflow::eager::RunComponentFunctionResponse* Arena::CreateMaybeMessage<::tensorflow::eager::RunComponentFunctionResponse>(Arena*);
template<> ::tensorflow::eager::SendPackedHandleOp* Arena::CreateMaybeMessage<::tensorflow::eager::SendPackedHandleOp>(Arena*);
template<> ::tensorflow::eager::SendPackedHandleOp_Handle* Arena::CreateMaybeMessage<::tensorflow::eager::SendPackedHandleOp_Handle>(Arena*);
template<> ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* Arena::CreateMaybeMessage<::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle>(Arena*);
template<> ::tensorflow::eager::SendTensorOp* Arena::CreateMaybeMessage<::tensorflow::eager::SendTensorOp>(Arena*);
template<> ::tensorflow::eager::SyncRemoteExecutorForStream* Arena::CreateMaybeMessage<::tensorflow::eager::SyncRemoteExecutorForStream>(Arena*);
template<> ::tensorflow::eager::UpdateContextRequest* Arena::CreateMaybeMessage<::tensorflow::eager::UpdateContextRequest>(Arena*);
template<> ::tensorflow::eager::UpdateContextResponse* Arena::CreateMaybeMessage<::tensorflow::eager::UpdateContextResponse>(Arena*);
template<> ::tensorflow::eager::WaitQueueDoneRequest* Arena::CreateMaybeMessage<::tensorflow::eager::WaitQueueDoneRequest>(Arena*);
template<> ::tensorflow::eager::WaitQueueDoneResponse* Arena::CreateMaybeMessage<::tensorflow::eager::WaitQueueDoneResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace eager {

// ===================================================================

class Operation_Input :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.Operation.Input) */ {
 public:
  Operation_Input();
  virtual ~Operation_Input();

  Operation_Input(const Operation_Input& from);
  Operation_Input(Operation_Input&& from) noexcept
    : Operation_Input() {
    *this = ::std::move(from);
  }

  inline Operation_Input& operator=(const Operation_Input& from) {
    CopyFrom(from);
    return *this;
  }
  inline Operation_Input& operator=(Operation_Input&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Operation_Input& default_instance();

  enum ItemCase {
    kRemoteHandle = 1,
    kTensor = 2,
    ITEM_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Operation_Input* internal_default_instance() {
    return reinterpret_cast<const Operation_Input*>(
               &_Operation_Input_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Operation_Input& a, Operation_Input& b) {
    a.Swap(&b);
  }
  inline void Swap(Operation_Input* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Operation_Input* New() const final {
    return CreateMaybeMessage<Operation_Input>(nullptr);
  }

  Operation_Input* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Operation_Input>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Operation_Input& from);
  void MergeFrom(const Operation_Input& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Operation_Input* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.Operation.Input";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRemoteHandleFieldNumber = 1,
    kTensorFieldNumber = 2,
  };
  // .tensorflow.eager.RemoteTensorHandle remote_handle = 1;
  bool has_remote_handle() const;
  void clear_remote_handle();
  const ::tensorflow::eager::RemoteTensorHandle& remote_handle() const;
  ::tensorflow::eager::RemoteTensorHandle* release_remote_handle();
  ::tensorflow::eager::RemoteTensorHandle* mutable_remote_handle();
  void set_allocated_remote_handle(::tensorflow::eager::RemoteTensorHandle* remote_handle);

  // .tensorflow.TensorProto tensor = 2;
  bool has_tensor() const;
  void clear_tensor();
  const ::tensorflow::TensorProto& tensor() const;
  ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);

  void clear_item();
  ItemCase item_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.eager.Operation.Input)
 private:
  class _Internal;
  void set_has_remote_handle();
  void set_has_tensor();

  inline bool has_item() const;
  inline void clear_has_item();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  union ItemUnion {
    ItemUnion() {}
    ::tensorflow::eager::RemoteTensorHandle* remote_handle_;
    ::tensorflow::TensorProto* tensor_;
  } item_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class Operation_AttrsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Operation_AttrsEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Operation_AttrsEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  Operation_AttrsEntry_DoNotUse();
  Operation_AttrsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const Operation_AttrsEntry_DoNotUse& other);
  static const Operation_AttrsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Operation_AttrsEntry_DoNotUse*>(&_Operation_AttrsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.eager.Operation.AttrsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[1];
  }

  public:
};

// -------------------------------------------------------------------

class Operation :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.Operation) */ {
 public:
  Operation();
  virtual ~Operation();

  Operation(const Operation& from);
  Operation(Operation&& from) noexcept
    : Operation() {
    *this = ::std::move(from);
  }

  inline Operation& operator=(const Operation& from) {
    CopyFrom(from);
    return *this;
  }
  inline Operation& operator=(Operation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Operation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Operation* internal_default_instance() {
    return reinterpret_cast<const Operation*>(
               &_Operation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Operation& a, Operation& b) {
    a.Swap(&b);
  }
  inline void Swap(Operation* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Operation* New() const final {
    return CreateMaybeMessage<Operation>(nullptr);
  }

  Operation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Operation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Operation& from);
  void MergeFrom(const Operation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Operation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.Operation";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef Operation_Input Input;

  // accessors -------------------------------------------------------

  enum : int {
    kControlOpIdsFieldNumber = 4,
    kAttrsFieldNumber = 5,
    kOpInputsFieldNumber = 10,
    kNameFieldNumber = 2,
    kDeviceFieldNumber = 6,
    kIdFieldNumber = 1,
    kFuncStepIdFieldNumber = 8,
    kIsComponentFunctionFieldNumber = 7,
    kIsFunctionFieldNumber = 9,
  };
  // repeated int64 control_op_ids = 4;
  int control_op_ids_size() const;
  void clear_control_op_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 control_op_ids(int index) const;
  void set_control_op_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_control_op_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      control_op_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_control_op_ids();

  // map<string, .tensorflow.AttrValue> attrs = 5;
  int attrs_size() const;
  void clear_attrs();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attrs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attrs();

  // repeated .tensorflow.eager.Operation.Input op_inputs = 10;
  int op_inputs_size() const;
  void clear_op_inputs();
  ::tensorflow::eager::Operation_Input* mutable_op_inputs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::Operation_Input >*
      mutable_op_inputs();
  const ::tensorflow::eager::Operation_Input& op_inputs(int index) const;
  ::tensorflow::eager::Operation_Input* add_op_inputs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::Operation_Input >&
      op_inputs() const;

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // string device = 6;
  void clear_device();
  const std::string& device() const;
  void set_device(const std::string& value);
  void set_device(std::string&& value);
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  std::string* mutable_device();
  std::string* release_device();
  void set_allocated_device(std::string* device);

  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 func_step_id = 8;
  void clear_func_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 func_step_id() const;
  void set_func_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool is_component_function = 7;
  void clear_is_component_function();
  bool is_component_function() const;
  void set_is_component_function(bool value);

  // bool is_function = 9;
  void clear_is_function();
  bool is_function() const;
  void set_is_function(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.Operation)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > control_op_ids_;
  mutable std::atomic<int> _control_op_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      Operation_AttrsEntry_DoNotUse,
      std::string, ::tensorflow::AttrValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attrs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::Operation_Input > op_inputs_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  ::PROTOBUF_NAMESPACE_ID::int64 func_step_id_;
  bool is_component_function_;
  bool is_function_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class QueueItem :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.QueueItem) */ {
 public:
  QueueItem();
  virtual ~QueueItem();

  QueueItem(const QueueItem& from);
  QueueItem(QueueItem&& from) noexcept
    : QueueItem() {
    *this = ::std::move(from);
  }

  inline QueueItem& operator=(const QueueItem& from) {
    CopyFrom(from);
    return *this;
  }
  inline QueueItem& operator=(QueueItem&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const QueueItem& default_instance();

  enum ItemCase {
    kHandleToDecref = 1,
    kOperation = 2,
    kSendTensor = 3,
    kRegisterFunction = 4,
    kCleanupFunction = 5,
    kSyncRemoteExecutorForStream = 6,
    kSendPackedHandle = 7,
    ITEM_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const QueueItem* internal_default_instance() {
    return reinterpret_cast<const QueueItem*>(
               &_QueueItem_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(QueueItem& a, QueueItem& b) {
    a.Swap(&b);
  }
  inline void Swap(QueueItem* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline QueueItem* New() const final {
    return CreateMaybeMessage<QueueItem>(nullptr);
  }

  QueueItem* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<QueueItem>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const QueueItem& from);
  void MergeFrom(const QueueItem& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QueueItem* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.QueueItem";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleToDecrefFieldNumber = 1,
    kOperationFieldNumber = 2,
    kSendTensorFieldNumber = 3,
    kRegisterFunctionFieldNumber = 4,
    kCleanupFunctionFieldNumber = 5,
    kSyncRemoteExecutorForStreamFieldNumber = 6,
    kSendPackedHandleFieldNumber = 7,
  };
  // .tensorflow.eager.RemoteTensorHandle handle_to_decref = 1;
  bool has_handle_to_decref() const;
  void clear_handle_to_decref();
  const ::tensorflow::eager::RemoteTensorHandle& handle_to_decref() const;
  ::tensorflow::eager::RemoteTensorHandle* release_handle_to_decref();
  ::tensorflow::eager::RemoteTensorHandle* mutable_handle_to_decref();
  void set_allocated_handle_to_decref(::tensorflow::eager::RemoteTensorHandle* handle_to_decref);

  // .tensorflow.eager.Operation operation = 2;
  bool has_operation() const;
  void clear_operation();
  const ::tensorflow::eager::Operation& operation() const;
  ::tensorflow::eager::Operation* release_operation();
  ::tensorflow::eager::Operation* mutable_operation();
  void set_allocated_operation(::tensorflow::eager::Operation* operation);

  // .tensorflow.eager.SendTensorOp send_tensor = 3;
  bool has_send_tensor() const;
  void clear_send_tensor();
  const ::tensorflow::eager::SendTensorOp& send_tensor() const;
  ::tensorflow::eager::SendTensorOp* release_send_tensor();
  ::tensorflow::eager::SendTensorOp* mutable_send_tensor();
  void set_allocated_send_tensor(::tensorflow::eager::SendTensorOp* send_tensor);

  // .tensorflow.eager.RegisterFunctionOp register_function = 4;
  bool has_register_function() const;
  void clear_register_function();
  const ::tensorflow::eager::RegisterFunctionOp& register_function() const;
  ::tensorflow::eager::RegisterFunctionOp* release_register_function();
  ::tensorflow::eager::RegisterFunctionOp* mutable_register_function();
  void set_allocated_register_function(::tensorflow::eager::RegisterFunctionOp* register_function);

  // .tensorflow.eager.CleanupFunctionOp cleanup_function = 5;
  bool has_cleanup_function() const;
  void clear_cleanup_function();
  const ::tensorflow::eager::CleanupFunctionOp& cleanup_function() const;
  ::tensorflow::eager::CleanupFunctionOp* release_cleanup_function();
  ::tensorflow::eager::CleanupFunctionOp* mutable_cleanup_function();
  void set_allocated_cleanup_function(::tensorflow::eager::CleanupFunctionOp* cleanup_function);

  // .tensorflow.eager.SyncRemoteExecutorForStream sync_remote_executor_for_stream = 6;
  bool has_sync_remote_executor_for_stream() const;
  void clear_sync_remote_executor_for_stream();
  const ::tensorflow::eager::SyncRemoteExecutorForStream& sync_remote_executor_for_stream() const;
  ::tensorflow::eager::SyncRemoteExecutorForStream* release_sync_remote_executor_for_stream();
  ::tensorflow::eager::SyncRemoteExecutorForStream* mutable_sync_remote_executor_for_stream();
  void set_allocated_sync_remote_executor_for_stream(::tensorflow::eager::SyncRemoteExecutorForStream* sync_remote_executor_for_stream);

  // .tensorflow.eager.SendPackedHandleOp send_packed_handle = 7;
  bool has_send_packed_handle() const;
  void clear_send_packed_handle();
  const ::tensorflow::eager::SendPackedHandleOp& send_packed_handle() const;
  ::tensorflow::eager::SendPackedHandleOp* release_send_packed_handle();
  ::tensorflow::eager::SendPackedHandleOp* mutable_send_packed_handle();
  void set_allocated_send_packed_handle(::tensorflow::eager::SendPackedHandleOp* send_packed_handle);

  void clear_item();
  ItemCase item_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.eager.QueueItem)
 private:
  class _Internal;
  void set_has_handle_to_decref();
  void set_has_operation();
  void set_has_send_tensor();
  void set_has_register_function();
  void set_has_cleanup_function();
  void set_has_sync_remote_executor_for_stream();
  void set_has_send_packed_handle();

  inline bool has_item() const;
  inline void clear_has_item();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  union ItemUnion {
    ItemUnion() {}
    ::tensorflow::eager::RemoteTensorHandle* handle_to_decref_;
    ::tensorflow::eager::Operation* operation_;
    ::tensorflow::eager::SendTensorOp* send_tensor_;
    ::tensorflow::eager::RegisterFunctionOp* register_function_;
    ::tensorflow::eager::CleanupFunctionOp* cleanup_function_;
    ::tensorflow::eager::SyncRemoteExecutorForStream* sync_remote_executor_for_stream_;
    ::tensorflow::eager::SendPackedHandleOp* send_packed_handle_;
  } item_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class QueueResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.QueueResponse) */ {
 public:
  QueueResponse();
  virtual ~QueueResponse();

  QueueResponse(const QueueResponse& from);
  QueueResponse(QueueResponse&& from) noexcept
    : QueueResponse() {
    *this = ::std::move(from);
  }

  inline QueueResponse& operator=(const QueueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline QueueResponse& operator=(QueueResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const QueueResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const QueueResponse* internal_default_instance() {
    return reinterpret_cast<const QueueResponse*>(
               &_QueueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(QueueResponse& a, QueueResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(QueueResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline QueueResponse* New() const final {
    return CreateMaybeMessage<QueueResponse>(nullptr);
  }

  QueueResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<QueueResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const QueueResponse& from);
  void MergeFrom(const QueueResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QueueResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.QueueResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 1,
    kTensorFieldNumber = 2,
    kDeviceFieldNumber = 3,
  };
  // repeated .tensorflow.TensorShapeProto shape = 1;
  int shape_size() const;
  void clear_shape();
  ::tensorflow::TensorShapeProto* mutable_shape(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_shape();
  const ::tensorflow::TensorShapeProto& shape(int index) const;
  ::tensorflow::TensorShapeProto* add_shape();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      shape() const;

  // repeated .tensorflow.TensorProto tensor = 2;
  int tensor_size() const;
  void clear_tensor();
  ::tensorflow::TensorProto* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensor();
  const ::tensorflow::TensorProto& tensor(int index) const;
  ::tensorflow::TensorProto* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensor() const;

  // repeated string device = 3;
  int device_size() const;
  void clear_device();
  const std::string& device(int index) const;
  std::string* mutable_device(int index);
  void set_device(int index, const std::string& value);
  void set_device(int index, std::string&& value);
  void set_device(int index, const char* value);
  void set_device(int index, const char* value, size_t size);
  std::string* add_device();
  void add_device(const std::string& value);
  void add_device(std::string&& value);
  void add_device(const char* value);
  void add_device(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& device() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_device();

  // @@protoc_insertion_point(class_scope:tensorflow.eager.QueueResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto > shape_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensor_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> device_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CreateContextRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CreateContextRequest) */ {
 public:
  CreateContextRequest();
  virtual ~CreateContextRequest();

  CreateContextRequest(const CreateContextRequest& from);
  CreateContextRequest(CreateContextRequest&& from) noexcept
    : CreateContextRequest() {
    *this = ::std::move(from);
  }

  inline CreateContextRequest& operator=(const CreateContextRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateContextRequest& operator=(CreateContextRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CreateContextRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateContextRequest* internal_default_instance() {
    return reinterpret_cast<const CreateContextRequest*>(
               &_CreateContextRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(CreateContextRequest& a, CreateContextRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateContextRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CreateContextRequest* New() const final {
    return CreateMaybeMessage<CreateContextRequest>(nullptr);
  }

  CreateContextRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CreateContextRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CreateContextRequest& from);
  void MergeFrom(const CreateContextRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateContextRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.CreateContextRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClusterDeviceAttributesFieldNumber = 6,
    kServerDefFieldNumber = 1,
    kVersionDefFieldNumber = 4,
    kKeepAliveSecsFieldNumber = 3,
    kContextIdFieldNumber = 7,
    kContextViewIdFieldNumber = 8,
    kAsyncFieldNumber = 2,
    kLazyCopyRemoteFunctionInputsFieldNumber = 9,
  };
  // repeated .tensorflow.DeviceAttributes cluster_device_attributes = 6;
  int cluster_device_attributes_size() const;
  void clear_cluster_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_cluster_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_cluster_device_attributes();
  const ::tensorflow::DeviceAttributes& cluster_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_cluster_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      cluster_device_attributes() const;

  // .tensorflow.ServerDef server_def = 1;
  bool has_server_def() const;
  void clear_server_def();
  const ::tensorflow::ServerDef& server_def() const;
  ::tensorflow::ServerDef* release_server_def();
  ::tensorflow::ServerDef* mutable_server_def();
  void set_allocated_server_def(::tensorflow::ServerDef* server_def);

  // .tensorflow.VersionDef version_def = 4;
  bool has_version_def() const;
  void clear_version_def();
  const ::tensorflow::VersionDef& version_def() const;
  ::tensorflow::VersionDef* release_version_def();
  ::tensorflow::VersionDef* mutable_version_def();
  void set_allocated_version_def(::tensorflow::VersionDef* version_def);

  // int64 keep_alive_secs = 3;
  void clear_keep_alive_secs();
  ::PROTOBUF_NAMESPACE_ID::int64 keep_alive_secs() const;
  void set_keep_alive_secs(::PROTOBUF_NAMESPACE_ID::int64 value);

  // fixed64 context_id = 7;
  void clear_context_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id() const;
  void set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // fixed64 context_view_id = 8;
  void clear_context_view_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_view_id() const;
  void set_context_view_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // bool async = 2;
  void clear_async();
  bool async() const;
  void set_async(bool value);

  // bool lazy_copy_remote_function_inputs = 9;
  void clear_lazy_copy_remote_function_inputs();
  bool lazy_copy_remote_function_inputs() const;
  void set_lazy_copy_remote_function_inputs(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CreateContextRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > cluster_device_attributes_;
  ::tensorflow::ServerDef* server_def_;
  ::tensorflow::VersionDef* version_def_;
  ::PROTOBUF_NAMESPACE_ID::int64 keep_alive_secs_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_view_id_;
  bool async_;
  bool lazy_copy_remote_function_inputs_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CreateContextResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CreateContextResponse) */ {
 public:
  CreateContextResponse();
  virtual ~CreateContextResponse();

  CreateContextResponse(const CreateContextResponse& from);
  CreateContextResponse(CreateContextResponse&& from) noexcept
    : CreateContextResponse() {
    *this = ::std::move(from);
  }

  inline CreateContextResponse& operator=(const CreateContextResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateContextResponse& operator=(CreateContextResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CreateContextResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateContextResponse* internal_default_instance() {
    return reinterpret_cast<const CreateContextResponse*>(
               &_CreateContextResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(CreateContextResponse& a, CreateContextResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateContextResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CreateContextResponse* New() const final {
    return CreateMaybeMessage<CreateContextResponse>(nullptr);
  }

  CreateContextResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CreateContextResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CreateContextResponse& from);
  void MergeFrom(const CreateContextResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateContextResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.CreateContextResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceAttributesFieldNumber = 2,
  };
  // repeated .tensorflow.DeviceAttributes device_attributes = 2;
  int device_attributes_size() const;
  void clear_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_device_attributes();
  const ::tensorflow::DeviceAttributes& device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      device_attributes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CreateContextResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > device_attributes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class UpdateContextRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.UpdateContextRequest) */ {
 public:
  UpdateContextRequest();
  virtual ~UpdateContextRequest();

  UpdateContextRequest(const UpdateContextRequest& from);
  UpdateContextRequest(UpdateContextRequest&& from) noexcept
    : UpdateContextRequest() {
    *this = ::std::move(from);
  }

  inline UpdateContextRequest& operator=(const UpdateContextRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpdateContextRequest& operator=(UpdateContextRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UpdateContextRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const UpdateContextRequest* internal_default_instance() {
    return reinterpret_cast<const UpdateContextRequest*>(
               &_UpdateContextRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(UpdateContextRequest& a, UpdateContextRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UpdateContextRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UpdateContextRequest* New() const final {
    return CreateMaybeMessage<UpdateContextRequest>(nullptr);
  }

  UpdateContextRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UpdateContextRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UpdateContextRequest& from);
  void MergeFrom(const UpdateContextRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UpdateContextRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.UpdateContextRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClusterDeviceAttributesFieldNumber = 2,
    kServerDefFieldNumber = 1,
    kContextIdFieldNumber = 3,
    kContextViewIdFieldNumber = 4,
  };
  // repeated .tensorflow.DeviceAttributes cluster_device_attributes = 2;
  int cluster_device_attributes_size() const;
  void clear_cluster_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_cluster_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_cluster_device_attributes();
  const ::tensorflow::DeviceAttributes& cluster_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_cluster_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      cluster_device_attributes() const;

  // .tensorflow.ServerDef server_def = 1;
  bool has_server_def() const;
  void clear_server_def();
  const ::tensorflow::ServerDef& server_def() const;
  ::tensorflow::ServerDef* release_server_def();
  ::tensorflow::ServerDef* mutable_server_def();
  void set_allocated_server_def(::tensorflow::ServerDef* server_def);

  // fixed64 context_id = 3;
  void clear_context_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id() const;
  void set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // fixed64 context_view_id = 4;
  void clear_context_view_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_view_id() const;
  void set_context_view_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.UpdateContextRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > cluster_device_attributes_;
  ::tensorflow::ServerDef* server_def_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_view_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class UpdateContextResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.UpdateContextResponse) */ {
 public:
  UpdateContextResponse();
  virtual ~UpdateContextResponse();

  UpdateContextResponse(const UpdateContextResponse& from);
  UpdateContextResponse(UpdateContextResponse&& from) noexcept
    : UpdateContextResponse() {
    *this = ::std::move(from);
  }

  inline UpdateContextResponse& operator=(const UpdateContextResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpdateContextResponse& operator=(UpdateContextResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UpdateContextResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const UpdateContextResponse* internal_default_instance() {
    return reinterpret_cast<const UpdateContextResponse*>(
               &_UpdateContextResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(UpdateContextResponse& a, UpdateContextResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(UpdateContextResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UpdateContextResponse* New() const final {
    return CreateMaybeMessage<UpdateContextResponse>(nullptr);
  }

  UpdateContextResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UpdateContextResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UpdateContextResponse& from);
  void MergeFrom(const UpdateContextResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UpdateContextResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.UpdateContextResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceAttributesFieldNumber = 1,
  };
  // repeated .tensorflow.DeviceAttributes device_attributes = 1;
  int device_attributes_size() const;
  void clear_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_device_attributes();
  const ::tensorflow::DeviceAttributes& device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      device_attributes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.eager.UpdateContextResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > device_attributes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class EnqueueRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.EnqueueRequest) */ {
 public:
  EnqueueRequest();
  virtual ~EnqueueRequest();

  EnqueueRequest(const EnqueueRequest& from);
  EnqueueRequest(EnqueueRequest&& from) noexcept
    : EnqueueRequest() {
    *this = ::std::move(from);
  }

  inline EnqueueRequest& operator=(const EnqueueRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnqueueRequest& operator=(EnqueueRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const EnqueueRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EnqueueRequest* internal_default_instance() {
    return reinterpret_cast<const EnqueueRequest*>(
               &_EnqueueRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(EnqueueRequest& a, EnqueueRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(EnqueueRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EnqueueRequest* New() const final {
    return CreateMaybeMessage<EnqueueRequest>(nullptr);
  }

  EnqueueRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EnqueueRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const EnqueueRequest& from);
  void MergeFrom(const EnqueueRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnqueueRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.EnqueueRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQueueFieldNumber = 3,
    kContextIdFieldNumber = 1,
  };
  // repeated .tensorflow.eager.QueueItem queue = 3;
  int queue_size() const;
  void clear_queue();
  ::tensorflow::eager::QueueItem* mutable_queue(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueItem >*
      mutable_queue();
  const ::tensorflow::eager::QueueItem& queue(int index) const;
  ::tensorflow::eager::QueueItem* add_queue();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueItem >&
      queue() const;

  // fixed64 context_id = 1;
  void clear_context_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id() const;
  void set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.EnqueueRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueItem > queue_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class EnqueueResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.EnqueueResponse) */ {
 public:
  EnqueueResponse();
  virtual ~EnqueueResponse();

  EnqueueResponse(const EnqueueResponse& from);
  EnqueueResponse(EnqueueResponse&& from) noexcept
    : EnqueueResponse() {
    *this = ::std::move(from);
  }

  inline EnqueueResponse& operator=(const EnqueueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnqueueResponse& operator=(EnqueueResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const EnqueueResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EnqueueResponse* internal_default_instance() {
    return reinterpret_cast<const EnqueueResponse*>(
               &_EnqueueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(EnqueueResponse& a, EnqueueResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(EnqueueResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EnqueueResponse* New() const final {
    return CreateMaybeMessage<EnqueueResponse>(nullptr);
  }

  EnqueueResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EnqueueResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const EnqueueResponse& from);
  void MergeFrom(const EnqueueResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnqueueResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.EnqueueResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQueueResponseFieldNumber = 1,
  };
  // repeated .tensorflow.eager.QueueResponse queue_response = 1;
  int queue_response_size() const;
  void clear_queue_response();
  ::tensorflow::eager::QueueResponse* mutable_queue_response(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueResponse >*
      mutable_queue_response();
  const ::tensorflow::eager::QueueResponse& queue_response(int index) const;
  ::tensorflow::eager::QueueResponse* add_queue_response();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueResponse >&
      queue_response() const;

  // @@protoc_insertion_point(class_scope:tensorflow.eager.EnqueueResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueResponse > queue_response_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class WaitQueueDoneRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.WaitQueueDoneRequest) */ {
 public:
  WaitQueueDoneRequest();
  virtual ~WaitQueueDoneRequest();

  WaitQueueDoneRequest(const WaitQueueDoneRequest& from);
  WaitQueueDoneRequest(WaitQueueDoneRequest&& from) noexcept
    : WaitQueueDoneRequest() {
    *this = ::std::move(from);
  }

  inline WaitQueueDoneRequest& operator=(const WaitQueueDoneRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline WaitQueueDoneRequest& operator=(WaitQueueDoneRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WaitQueueDoneRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WaitQueueDoneRequest* internal_default_instance() {
    return reinterpret_cast<const WaitQueueDoneRequest*>(
               &_WaitQueueDoneRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(WaitQueueDoneRequest& a, WaitQueueDoneRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(WaitQueueDoneRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WaitQueueDoneRequest* New() const final {
    return CreateMaybeMessage<WaitQueueDoneRequest>(nullptr);
  }

  WaitQueueDoneRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WaitQueueDoneRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WaitQueueDoneRequest& from);
  void MergeFrom(const WaitQueueDoneRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WaitQueueDoneRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.WaitQueueDoneRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpIdFieldNumber = 2,
    kContextIdFieldNumber = 1,
  };
  // repeated int64 op_id = 2;
  int op_id_size() const;
  void clear_op_id();
  ::PROTOBUF_NAMESPACE_ID::int64 op_id(int index) const;
  void set_op_id(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_op_id(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      op_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_op_id();

  // fixed64 context_id = 1;
  void clear_context_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id() const;
  void set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.WaitQueueDoneRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > op_id_;
  mutable std::atomic<int> _op_id_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class WaitQueueDoneResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.WaitQueueDoneResponse) */ {
 public:
  WaitQueueDoneResponse();
  virtual ~WaitQueueDoneResponse();

  WaitQueueDoneResponse(const WaitQueueDoneResponse& from);
  WaitQueueDoneResponse(WaitQueueDoneResponse&& from) noexcept
    : WaitQueueDoneResponse() {
    *this = ::std::move(from);
  }

  inline WaitQueueDoneResponse& operator=(const WaitQueueDoneResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline WaitQueueDoneResponse& operator=(WaitQueueDoneResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WaitQueueDoneResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WaitQueueDoneResponse* internal_default_instance() {
    return reinterpret_cast<const WaitQueueDoneResponse*>(
               &_WaitQueueDoneResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(WaitQueueDoneResponse& a, WaitQueueDoneResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(WaitQueueDoneResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WaitQueueDoneResponse* New() const final {
    return CreateMaybeMessage<WaitQueueDoneResponse>(nullptr);
  }

  WaitQueueDoneResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WaitQueueDoneResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WaitQueueDoneResponse& from);
  void MergeFrom(const WaitQueueDoneResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WaitQueueDoneResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.WaitQueueDoneResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.WaitQueueDoneResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RunComponentFunctionRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RunComponentFunctionRequest) */ {
 public:
  RunComponentFunctionRequest();
  virtual ~RunComponentFunctionRequest();

  RunComponentFunctionRequest(const RunComponentFunctionRequest& from);
  RunComponentFunctionRequest(RunComponentFunctionRequest&& from) noexcept
    : RunComponentFunctionRequest() {
    *this = ::std::move(from);
  }

  inline RunComponentFunctionRequest& operator=(const RunComponentFunctionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunComponentFunctionRequest& operator=(RunComponentFunctionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunComponentFunctionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunComponentFunctionRequest* internal_default_instance() {
    return reinterpret_cast<const RunComponentFunctionRequest*>(
               &_RunComponentFunctionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RunComponentFunctionRequest& a, RunComponentFunctionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RunComponentFunctionRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunComponentFunctionRequest* New() const final {
    return CreateMaybeMessage<RunComponentFunctionRequest>(nullptr);
  }

  RunComponentFunctionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunComponentFunctionRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunComponentFunctionRequest& from);
  void MergeFrom(const RunComponentFunctionRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunComponentFunctionRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.RunComponentFunctionRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputNumFieldNumber = 3,
    kOperationFieldNumber = 2,
    kContextIdFieldNumber = 1,
  };
  // repeated int32 output_num = 3;
  int output_num_size() const;
  void clear_output_num();
  ::PROTOBUF_NAMESPACE_ID::int32 output_num(int index) const;
  void set_output_num(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_output_num(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      output_num() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_output_num();

  // .tensorflow.eager.Operation operation = 2;
  bool has_operation() const;
  void clear_operation();
  const ::tensorflow::eager::Operation& operation() const;
  ::tensorflow::eager::Operation* release_operation();
  ::tensorflow::eager::Operation* mutable_operation();
  void set_allocated_operation(::tensorflow::eager::Operation* operation);

  // fixed64 context_id = 1;
  void clear_context_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id() const;
  void set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RunComponentFunctionRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > output_num_;
  mutable std::atomic<int> _output_num_cached_byte_size_;
  ::tensorflow::eager::Operation* operation_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RunComponentFunctionResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RunComponentFunctionResponse) */ {
 public:
  RunComponentFunctionResponse();
  virtual ~RunComponentFunctionResponse();

  RunComponentFunctionResponse(const RunComponentFunctionResponse& from);
  RunComponentFunctionResponse(RunComponentFunctionResponse&& from) noexcept
    : RunComponentFunctionResponse() {
    *this = ::std::move(from);
  }

  inline RunComponentFunctionResponse& operator=(const RunComponentFunctionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunComponentFunctionResponse& operator=(RunComponentFunctionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunComponentFunctionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunComponentFunctionResponse* internal_default_instance() {
    return reinterpret_cast<const RunComponentFunctionResponse*>(
               &_RunComponentFunctionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(RunComponentFunctionResponse& a, RunComponentFunctionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RunComponentFunctionResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunComponentFunctionResponse* New() const final {
    return CreateMaybeMessage<RunComponentFunctionResponse>(nullptr);
  }

  RunComponentFunctionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunComponentFunctionResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunComponentFunctionResponse& from);
  void MergeFrom(const RunComponentFunctionResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunComponentFunctionResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.RunComponentFunctionResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 1,
    kTensorFieldNumber = 2,
  };
  // repeated .tensorflow.TensorShapeProto shape = 1;
  int shape_size() const;
  void clear_shape();
  ::tensorflow::TensorShapeProto* mutable_shape(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_shape();
  const ::tensorflow::TensorShapeProto& shape(int index) const;
  ::tensorflow::TensorShapeProto* add_shape();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      shape() const;

  // repeated .tensorflow.TensorProto tensor = 2;
  int tensor_size() const;
  void clear_tensor();
  ::tensorflow::TensorProto* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensor();
  const ::tensorflow::TensorProto& tensor(int index) const;
  ::tensorflow::TensorProto* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensor() const;

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RunComponentFunctionResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto > shape_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensor_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class KeepAliveRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.KeepAliveRequest) */ {
 public:
  KeepAliveRequest();
  virtual ~KeepAliveRequest();

  KeepAliveRequest(const KeepAliveRequest& from);
  KeepAliveRequest(KeepAliveRequest&& from) noexcept
    : KeepAliveRequest() {
    *this = ::std::move(from);
  }

  inline KeepAliveRequest& operator=(const KeepAliveRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline KeepAliveRequest& operator=(KeepAliveRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const KeepAliveRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const KeepAliveRequest* internal_default_instance() {
    return reinterpret_cast<const KeepAliveRequest*>(
               &_KeepAliveRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(KeepAliveRequest& a, KeepAliveRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(KeepAliveRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline KeepAliveRequest* New() const final {
    return CreateMaybeMessage<KeepAliveRequest>(nullptr);
  }

  KeepAliveRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<KeepAliveRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const KeepAliveRequest& from);
  void MergeFrom(const KeepAliveRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KeepAliveRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.KeepAliveRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextIdFieldNumber = 1,
  };
  // fixed64 context_id = 1;
  void clear_context_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id() const;
  void set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.KeepAliveRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class KeepAliveResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.KeepAliveResponse) */ {
 public:
  KeepAliveResponse();
  virtual ~KeepAliveResponse();

  KeepAliveResponse(const KeepAliveResponse& from);
  KeepAliveResponse(KeepAliveResponse&& from) noexcept
    : KeepAliveResponse() {
    *this = ::std::move(from);
  }

  inline KeepAliveResponse& operator=(const KeepAliveResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline KeepAliveResponse& operator=(KeepAliveResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const KeepAliveResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const KeepAliveResponse* internal_default_instance() {
    return reinterpret_cast<const KeepAliveResponse*>(
               &_KeepAliveResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(KeepAliveResponse& a, KeepAliveResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(KeepAliveResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline KeepAliveResponse* New() const final {
    return CreateMaybeMessage<KeepAliveResponse>(nullptr);
  }

  KeepAliveResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<KeepAliveResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const KeepAliveResponse& from);
  void MergeFrom(const KeepAliveResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KeepAliveResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.KeepAliveResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextViewIdFieldNumber = 1,
  };
  // fixed64 context_view_id = 1;
  void clear_context_view_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_view_id() const;
  void set_context_view_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.KeepAliveResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_view_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CloseContextRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CloseContextRequest) */ {
 public:
  CloseContextRequest();
  virtual ~CloseContextRequest();

  CloseContextRequest(const CloseContextRequest& from);
  CloseContextRequest(CloseContextRequest&& from) noexcept
    : CloseContextRequest() {
    *this = ::std::move(from);
  }

  inline CloseContextRequest& operator=(const CloseContextRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CloseContextRequest& operator=(CloseContextRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CloseContextRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CloseContextRequest* internal_default_instance() {
    return reinterpret_cast<const CloseContextRequest*>(
               &_CloseContextRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(CloseContextRequest& a, CloseContextRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CloseContextRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CloseContextRequest* New() const final {
    return CreateMaybeMessage<CloseContextRequest>(nullptr);
  }

  CloseContextRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CloseContextRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CloseContextRequest& from);
  void MergeFrom(const CloseContextRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CloseContextRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.CloseContextRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextIdFieldNumber = 1,
    kContextViewIdFieldNumber = 2,
  };
  // fixed64 context_id = 1;
  void clear_context_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id() const;
  void set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // fixed64 context_view_id = 2;
  void clear_context_view_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 context_view_id() const;
  void set_context_view_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CloseContextRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_id_;
  ::PROTOBUF_NAMESPACE_ID::uint64 context_view_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CloseContextResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CloseContextResponse) */ {
 public:
  CloseContextResponse();
  virtual ~CloseContextResponse();

  CloseContextResponse(const CloseContextResponse& from);
  CloseContextResponse(CloseContextResponse&& from) noexcept
    : CloseContextResponse() {
    *this = ::std::move(from);
  }

  inline CloseContextResponse& operator=(const CloseContextResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CloseContextResponse& operator=(CloseContextResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CloseContextResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CloseContextResponse* internal_default_instance() {
    return reinterpret_cast<const CloseContextResponse*>(
               &_CloseContextResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(CloseContextResponse& a, CloseContextResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CloseContextResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CloseContextResponse* New() const final {
    return CreateMaybeMessage<CloseContextResponse>(nullptr);
  }

  CloseContextResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CloseContextResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CloseContextResponse& from);
  void MergeFrom(const CloseContextResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CloseContextResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.CloseContextResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CloseContextResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RegisterFunctionOp :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RegisterFunctionOp) */ {
 public:
  RegisterFunctionOp();
  virtual ~RegisterFunctionOp();

  RegisterFunctionOp(const RegisterFunctionOp& from);
  RegisterFunctionOp(RegisterFunctionOp&& from) noexcept
    : RegisterFunctionOp() {
    *this = ::std::move(from);
  }

  inline RegisterFunctionOp& operator=(const RegisterFunctionOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisterFunctionOp& operator=(RegisterFunctionOp&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RegisterFunctionOp& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RegisterFunctionOp* internal_default_instance() {
    return reinterpret_cast<const RegisterFunctionOp*>(
               &_RegisterFunctionOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(RegisterFunctionOp& a, RegisterFunctionOp& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisterFunctionOp* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RegisterFunctionOp* New() const final {
    return CreateMaybeMessage<RegisterFunctionOp>(nullptr);
  }

  RegisterFunctionOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RegisterFunctionOp>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RegisterFunctionOp& from);
  void MergeFrom(const RegisterFunctionOp& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterFunctionOp* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.RegisterFunctionOp";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionDefFieldNumber = 1,
    kLibraryFieldNumber = 3,
    kIsComponentFunctionFieldNumber = 2,
  };
  // .tensorflow.FunctionDef function_def = 1;
  bool has_function_def() const;
  void clear_function_def();
  const ::tensorflow::FunctionDef& function_def() const;
  ::tensorflow::FunctionDef* release_function_def();
  ::tensorflow::FunctionDef* mutable_function_def();
  void set_allocated_function_def(::tensorflow::FunctionDef* function_def);

  // .tensorflow.FunctionDefLibrary library = 3;
  bool has_library() const;
  void clear_library();
  const ::tensorflow::FunctionDefLibrary& library() const;
  ::tensorflow::FunctionDefLibrary* release_library();
  ::tensorflow::FunctionDefLibrary* mutable_library();
  void set_allocated_library(::tensorflow::FunctionDefLibrary* library);

  // bool is_component_function = 2;
  void clear_is_component_function();
  bool is_component_function() const;
  void set_is_component_function(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RegisterFunctionOp)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::FunctionDef* function_def_;
  ::tensorflow::FunctionDefLibrary* library_;
  bool is_component_function_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CleanupFunctionOp :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CleanupFunctionOp) */ {
 public:
  CleanupFunctionOp();
  virtual ~CleanupFunctionOp();

  CleanupFunctionOp(const CleanupFunctionOp& from);
  CleanupFunctionOp(CleanupFunctionOp&& from) noexcept
    : CleanupFunctionOp() {
    *this = ::std::move(from);
  }

  inline CleanupFunctionOp& operator=(const CleanupFunctionOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline CleanupFunctionOp& operator=(CleanupFunctionOp&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CleanupFunctionOp& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CleanupFunctionOp* internal_default_instance() {
    return reinterpret_cast<const CleanupFunctionOp*>(
               &_CleanupFunctionOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(CleanupFunctionOp& a, CleanupFunctionOp& b) {
    a.Swap(&b);
  }
  inline void Swap(CleanupFunctionOp* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CleanupFunctionOp* New() const final {
    return CreateMaybeMessage<CleanupFunctionOp>(nullptr);
  }

  CleanupFunctionOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CleanupFunctionOp>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CleanupFunctionOp& from);
  void MergeFrom(const CleanupFunctionOp& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupFunctionOp* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.CleanupFunctionOp";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepIdFieldNumber = 1,
  };
  // int64 step_id = 1;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CleanupFunctionOp)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SyncRemoteExecutorForStream :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SyncRemoteExecutorForStream) */ {
 public:
  SyncRemoteExecutorForStream();
  virtual ~SyncRemoteExecutorForStream();

  SyncRemoteExecutorForStream(const SyncRemoteExecutorForStream& from);
  SyncRemoteExecutorForStream(SyncRemoteExecutorForStream&& from) noexcept
    : SyncRemoteExecutorForStream() {
    *this = ::std::move(from);
  }

  inline SyncRemoteExecutorForStream& operator=(const SyncRemoteExecutorForStream& from) {
    CopyFrom(from);
    return *this;
  }
  inline SyncRemoteExecutorForStream& operator=(SyncRemoteExecutorForStream&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SyncRemoteExecutorForStream& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SyncRemoteExecutorForStream* internal_default_instance() {
    return reinterpret_cast<const SyncRemoteExecutorForStream*>(
               &_SyncRemoteExecutorForStream_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(SyncRemoteExecutorForStream& a, SyncRemoteExecutorForStream& b) {
    a.Swap(&b);
  }
  inline void Swap(SyncRemoteExecutorForStream* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SyncRemoteExecutorForStream* New() const final {
    return CreateMaybeMessage<SyncRemoteExecutorForStream>(nullptr);
  }

  SyncRemoteExecutorForStream* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SyncRemoteExecutorForStream>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SyncRemoteExecutorForStream& from);
  void MergeFrom(const SyncRemoteExecutorForStream& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SyncRemoteExecutorForStream* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.SyncRemoteExecutorForStream";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.SyncRemoteExecutorForStream)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SendTensorOp :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SendTensorOp) */ {
 public:
  SendTensorOp();
  virtual ~SendTensorOp();

  SendTensorOp(const SendTensorOp& from);
  SendTensorOp(SendTensorOp&& from) noexcept
    : SendTensorOp() {
    *this = ::std::move(from);
  }

  inline SendTensorOp& operator=(const SendTensorOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline SendTensorOp& operator=(SendTensorOp&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SendTensorOp& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SendTensorOp* internal_default_instance() {
    return reinterpret_cast<const SendTensorOp*>(
               &_SendTensorOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(SendTensorOp& a, SendTensorOp& b) {
    a.Swap(&b);
  }
  inline void Swap(SendTensorOp* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SendTensorOp* New() const final {
    return CreateMaybeMessage<SendTensorOp>(nullptr);
  }

  SendTensorOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SendTensorOp>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SendTensorOp& from);
  void MergeFrom(const SendTensorOp& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SendTensorOp* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.SendTensorOp";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorsFieldNumber = 2,
    kDeviceNameFieldNumber = 3,
    kOpIdFieldNumber = 1,
  };
  // repeated .tensorflow.TensorProto tensors = 2;
  int tensors_size() const;
  void clear_tensors();
  ::tensorflow::TensorProto* mutable_tensors(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensors();
  const ::tensorflow::TensorProto& tensors(int index) const;
  ::tensorflow::TensorProto* add_tensors();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensors() const;

  // string device_name = 3;
  void clear_device_name();
  const std::string& device_name() const;
  void set_device_name(const std::string& value);
  void set_device_name(std::string&& value);
  void set_device_name(const char* value);
  void set_device_name(const char* value, size_t size);
  std::string* mutable_device_name();
  std::string* release_device_name();
  void set_allocated_device_name(std::string* device_name);

  // int64 op_id = 1;
  void clear_op_id();
  ::PROTOBUF_NAMESPACE_ID::int64 op_id() const;
  void set_op_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.SendTensorOp)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensors_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 op_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SendPackedHandleOp_LocalTensorHandle :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle) */ {
 public:
  SendPackedHandleOp_LocalTensorHandle();
  virtual ~SendPackedHandleOp_LocalTensorHandle();

  SendPackedHandleOp_LocalTensorHandle(const SendPackedHandleOp_LocalTensorHandle& from);
  SendPackedHandleOp_LocalTensorHandle(SendPackedHandleOp_LocalTensorHandle&& from) noexcept
    : SendPackedHandleOp_LocalTensorHandle() {
    *this = ::std::move(from);
  }

  inline SendPackedHandleOp_LocalTensorHandle& operator=(const SendPackedHandleOp_LocalTensorHandle& from) {
    CopyFrom(from);
    return *this;
  }
  inline SendPackedHandleOp_LocalTensorHandle& operator=(SendPackedHandleOp_LocalTensorHandle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SendPackedHandleOp_LocalTensorHandle& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SendPackedHandleOp_LocalTensorHandle* internal_default_instance() {
    return reinterpret_cast<const SendPackedHandleOp_LocalTensorHandle*>(
               &_SendPackedHandleOp_LocalTensorHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(SendPackedHandleOp_LocalTensorHandle& a, SendPackedHandleOp_LocalTensorHandle& b) {
    a.Swap(&b);
  }
  inline void Swap(SendPackedHandleOp_LocalTensorHandle* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SendPackedHandleOp_LocalTensorHandle* New() const final {
    return CreateMaybeMessage<SendPackedHandleOp_LocalTensorHandle>(nullptr);
  }

  SendPackedHandleOp_LocalTensorHandle* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SendPackedHandleOp_LocalTensorHandle>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SendPackedHandleOp_LocalTensorHandle& from);
  void MergeFrom(const SendPackedHandleOp_LocalTensorHandle& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SendPackedHandleOp_LocalTensorHandle* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.SendPackedHandleOp.LocalTensorHandle";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceFieldNumber = 2,
    kTensorFieldNumber = 1,
  };
  // string device = 2;
  void clear_device();
  const std::string& device() const;
  void set_device(const std::string& value);
  void set_device(std::string&& value);
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  std::string* mutable_device();
  std::string* release_device();
  void set_allocated_device(std::string* device);

  // .tensorflow.TensorProto tensor = 1;
  bool has_tensor() const;
  void clear_tensor();
  const ::tensorflow::TensorProto& tensor() const;
  ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
  ::tensorflow::TensorProto* tensor_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SendPackedHandleOp_Handle :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SendPackedHandleOp.Handle) */ {
 public:
  SendPackedHandleOp_Handle();
  virtual ~SendPackedHandleOp_Handle();

  SendPackedHandleOp_Handle(const SendPackedHandleOp_Handle& from);
  SendPackedHandleOp_Handle(SendPackedHandleOp_Handle&& from) noexcept
    : SendPackedHandleOp_Handle() {
    *this = ::std::move(from);
  }

  inline SendPackedHandleOp_Handle& operator=(const SendPackedHandleOp_Handle& from) {
    CopyFrom(from);
    return *this;
  }
  inline SendPackedHandleOp_Handle& operator=(SendPackedHandleOp_Handle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SendPackedHandleOp_Handle& default_instance();

  enum ItemCase {
    kLocalHandle = 1,
    kRemoteHandle = 2,
    ITEM_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SendPackedHandleOp_Handle* internal_default_instance() {
    return reinterpret_cast<const SendPackedHandleOp_Handle*>(
               &_SendPackedHandleOp_Handle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(SendPackedHandleOp_Handle& a, SendPackedHandleOp_Handle& b) {
    a.Swap(&b);
  }
  inline void Swap(SendPackedHandleOp_Handle* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SendPackedHandleOp_Handle* New() const final {
    return CreateMaybeMessage<SendPackedHandleOp_Handle>(nullptr);
  }

  SendPackedHandleOp_Handle* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SendPackedHandleOp_Handle>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SendPackedHandleOp_Handle& from);
  void MergeFrom(const SendPackedHandleOp_Handle& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SendPackedHandleOp_Handle* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.SendPackedHandleOp.Handle";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocalHandleFieldNumber = 1,
    kRemoteHandleFieldNumber = 2,
  };
  // .tensorflow.eager.SendPackedHandleOp.LocalTensorHandle local_handle = 1;
  bool has_local_handle() const;
  void clear_local_handle();
  const ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle& local_handle() const;
  ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* release_local_handle();
  ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* mutable_local_handle();
  void set_allocated_local_handle(::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* local_handle);

  // .tensorflow.eager.RemoteTensorHandle remote_handle = 2;
  bool has_remote_handle() const;
  void clear_remote_handle();
  const ::tensorflow::eager::RemoteTensorHandle& remote_handle() const;
  ::tensorflow::eager::RemoteTensorHandle* release_remote_handle();
  ::tensorflow::eager::RemoteTensorHandle* mutable_remote_handle();
  void set_allocated_remote_handle(::tensorflow::eager::RemoteTensorHandle* remote_handle);

  void clear_item();
  ItemCase item_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.eager.SendPackedHandleOp.Handle)
 private:
  class _Internal;
  void set_has_local_handle();
  void set_has_remote_handle();

  inline bool has_item() const;
  inline void clear_has_item();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  union ItemUnion {
    ItemUnion() {}
    ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* local_handle_;
    ::tensorflow::eager::RemoteTensorHandle* remote_handle_;
  } item_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SendPackedHandleOp :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SendPackedHandleOp) */ {
 public:
  SendPackedHandleOp();
  virtual ~SendPackedHandleOp();

  SendPackedHandleOp(const SendPackedHandleOp& from);
  SendPackedHandleOp(SendPackedHandleOp&& from) noexcept
    : SendPackedHandleOp() {
    *this = ::std::move(from);
  }

  inline SendPackedHandleOp& operator=(const SendPackedHandleOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline SendPackedHandleOp& operator=(SendPackedHandleOp&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SendPackedHandleOp& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SendPackedHandleOp* internal_default_instance() {
    return reinterpret_cast<const SendPackedHandleOp*>(
               &_SendPackedHandleOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(SendPackedHandleOp& a, SendPackedHandleOp& b) {
    a.Swap(&b);
  }
  inline void Swap(SendPackedHandleOp* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SendPackedHandleOp* New() const final {
    return CreateMaybeMessage<SendPackedHandleOp>(nullptr);
  }

  SendPackedHandleOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SendPackedHandleOp>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SendPackedHandleOp& from);
  void MergeFrom(const SendPackedHandleOp& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SendPackedHandleOp* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.SendPackedHandleOp";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef SendPackedHandleOp_LocalTensorHandle LocalTensorHandle;
  typedef SendPackedHandleOp_Handle Handle;

  // accessors -------------------------------------------------------

  enum : int {
    kHandlesFieldNumber = 2,
    kDeviceNameFieldNumber = 3,
    kOpIdFieldNumber = 1,
  };
  // repeated .tensorflow.eager.SendPackedHandleOp.Handle handles = 2;
  int handles_size() const;
  void clear_handles();
  ::tensorflow::eager::SendPackedHandleOp_Handle* mutable_handles(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::SendPackedHandleOp_Handle >*
      mutable_handles();
  const ::tensorflow::eager::SendPackedHandleOp_Handle& handles(int index) const;
  ::tensorflow::eager::SendPackedHandleOp_Handle* add_handles();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::SendPackedHandleOp_Handle >&
      handles() const;

  // string device_name = 3;
  void clear_device_name();
  const std::string& device_name() const;
  void set_device_name(const std::string& value);
  void set_device_name(std::string&& value);
  void set_device_name(const char* value);
  void set_device_name(const char* value, size_t size);
  std::string* mutable_device_name();
  std::string* release_device_name();
  void set_allocated_device_name(std::string* device_name);

  // int64 op_id = 1;
  void clear_op_id();
  ::PROTOBUF_NAMESPACE_ID::int64 op_id() const;
  void set_op_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.SendPackedHandleOp)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::SendPackedHandleOp_Handle > handles_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 op_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Operation_Input

// .tensorflow.eager.RemoteTensorHandle remote_handle = 1;
inline bool Operation_Input::has_remote_handle() const {
  return item_case() == kRemoteHandle;
}
inline void Operation_Input::set_has_remote_handle() {
  _oneof_case_[0] = kRemoteHandle;
}
inline ::tensorflow::eager::RemoteTensorHandle* Operation_Input::release_remote_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.Operation.Input.remote_handle)
  if (has_remote_handle()) {
    clear_has_item();
      ::tensorflow::eager::RemoteTensorHandle* temp = item_.remote_handle_;
    item_.remote_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::RemoteTensorHandle& Operation_Input::remote_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.Input.remote_handle)
  return has_remote_handle()
      ? *item_.remote_handle_
      : *reinterpret_cast< ::tensorflow::eager::RemoteTensorHandle*>(&::tensorflow::eager::_RemoteTensorHandle_default_instance_);
}
inline ::tensorflow::eager::RemoteTensorHandle* Operation_Input::mutable_remote_handle() {
  if (!has_remote_handle()) {
    clear_item();
    set_has_remote_handle();
    item_.remote_handle_ = CreateMaybeMessage< ::tensorflow::eager::RemoteTensorHandle >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.Input.remote_handle)
  return item_.remote_handle_;
}

// .tensorflow.TensorProto tensor = 2;
inline bool Operation_Input::has_tensor() const {
  return item_case() == kTensor;
}
inline void Operation_Input::set_has_tensor() {
  _oneof_case_[0] = kTensor;
}
inline ::tensorflow::TensorProto* Operation_Input::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.Operation.Input.tensor)
  if (has_tensor()) {
    clear_has_item();
      ::tensorflow::TensorProto* temp = item_.tensor_;
    item_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorProto& Operation_Input::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.Input.tensor)
  return has_tensor()
      ? *item_.tensor_
      : *reinterpret_cast< ::tensorflow::TensorProto*>(&::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* Operation_Input::mutable_tensor() {
  if (!has_tensor()) {
    clear_item();
    set_has_tensor();
    item_.tensor_ = CreateMaybeMessage< ::tensorflow::TensorProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.Input.tensor)
  return item_.tensor_;
}

inline bool Operation_Input::has_item() const {
  return item_case() != ITEM_NOT_SET;
}
inline void Operation_Input::clear_has_item() {
  _oneof_case_[0] = ITEM_NOT_SET;
}
inline Operation_Input::ItemCase Operation_Input::item_case() const {
  return Operation_Input::ItemCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Operation

// int64 id = 1;
inline void Operation::clear_id() {
  id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Operation::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.id)
  return id_;
}
inline void Operation::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.id)
}

// string name = 2;
inline void Operation::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& Operation::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.name)
  return name_.GetNoArena();
}
inline void Operation::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.name)
}
inline void Operation::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.eager.Operation.name)
}
inline void Operation::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.eager.Operation.name)
}
inline void Operation::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.eager.Operation.name)
}
inline std::string* Operation::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* Operation::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.Operation.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void Operation::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.Operation.name)
}

// repeated .tensorflow.eager.Operation.Input op_inputs = 10;
inline int Operation::op_inputs_size() const {
  return op_inputs_.size();
}
inline void Operation::clear_op_inputs() {
  op_inputs_.Clear();
}
inline ::tensorflow::eager::Operation_Input* Operation::mutable_op_inputs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.op_inputs)
  return op_inputs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::Operation_Input >*
Operation::mutable_op_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.Operation.op_inputs)
  return &op_inputs_;
}
inline const ::tensorflow::eager::Operation_Input& Operation::op_inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.op_inputs)
  return op_inputs_.Get(index);
}
inline ::tensorflow::eager::Operation_Input* Operation::add_op_inputs() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.Operation.op_inputs)
  return op_inputs_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::Operation_Input >&
Operation::op_inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.Operation.op_inputs)
  return op_inputs_;
}

// repeated int64 control_op_ids = 4;
inline int Operation::control_op_ids_size() const {
  return control_op_ids_.size();
}
inline void Operation::clear_control_op_ids() {
  control_op_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Operation::control_op_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.control_op_ids)
  return control_op_ids_.Get(index);
}
inline void Operation::set_control_op_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  control_op_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.control_op_ids)
}
inline void Operation::add_control_op_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  control_op_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.eager.Operation.control_op_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
Operation::control_op_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.Operation.control_op_ids)
  return control_op_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
Operation::mutable_control_op_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.Operation.control_op_ids)
  return &control_op_ids_;
}

// map<string, .tensorflow.AttrValue> attrs = 5;
inline int Operation::attrs_size() const {
  return attrs_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
Operation::attrs() const {
  // @@protoc_insertion_point(field_map:tensorflow.eager.Operation.attrs)
  return attrs_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
Operation::mutable_attrs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.eager.Operation.attrs)
  return attrs_.MutableMap();
}

// string device = 6;
inline void Operation::clear_device() {
  device_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& Operation::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.device)
  return device_.GetNoArena();
}
inline void Operation::set_device(const std::string& value) {
  
  device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.device)
}
inline void Operation::set_device(std::string&& value) {
  
  device_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.eager.Operation.device)
}
inline void Operation::set_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.eager.Operation.device)
}
inline void Operation::set_device(const char* value, size_t size) {
  
  device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.eager.Operation.device)
}
inline std::string* Operation::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.device)
  return device_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* Operation::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.Operation.device)
  
  return device_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void Operation::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  device_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.Operation.device)
}

// bool is_component_function = 7;
inline void Operation::clear_is_component_function() {
  is_component_function_ = false;
}
inline bool Operation::is_component_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.is_component_function)
  return is_component_function_;
}
inline void Operation::set_is_component_function(bool value) {
  
  is_component_function_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.is_component_function)
}

// int64 func_step_id = 8;
inline void Operation::clear_func_step_id() {
  func_step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Operation::func_step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.func_step_id)
  return func_step_id_;
}
inline void Operation::set_func_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  func_step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.func_step_id)
}

// bool is_function = 9;
inline void Operation::clear_is_function() {
  is_function_ = false;
}
inline bool Operation::is_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.is_function)
  return is_function_;
}
inline void Operation::set_is_function(bool value) {
  
  is_function_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.is_function)
}

// -------------------------------------------------------------------

// QueueItem

// .tensorflow.eager.RemoteTensorHandle handle_to_decref = 1;
inline bool QueueItem::has_handle_to_decref() const {
  return item_case() == kHandleToDecref;
}
inline void QueueItem::set_has_handle_to_decref() {
  _oneof_case_[0] = kHandleToDecref;
}
inline ::tensorflow::eager::RemoteTensorHandle* QueueItem::release_handle_to_decref() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.handle_to_decref)
  if (has_handle_to_decref()) {
    clear_has_item();
      ::tensorflow::eager::RemoteTensorHandle* temp = item_.handle_to_decref_;
    item_.handle_to_decref_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::RemoteTensorHandle& QueueItem::handle_to_decref() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.handle_to_decref)
  return has_handle_to_decref()
      ? *item_.handle_to_decref_
      : *reinterpret_cast< ::tensorflow::eager::RemoteTensorHandle*>(&::tensorflow::eager::_RemoteTensorHandle_default_instance_);
}
inline ::tensorflow::eager::RemoteTensorHandle* QueueItem::mutable_handle_to_decref() {
  if (!has_handle_to_decref()) {
    clear_item();
    set_has_handle_to_decref();
    item_.handle_to_decref_ = CreateMaybeMessage< ::tensorflow::eager::RemoteTensorHandle >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.handle_to_decref)
  return item_.handle_to_decref_;
}

// .tensorflow.eager.Operation operation = 2;
inline bool QueueItem::has_operation() const {
  return item_case() == kOperation;
}
inline void QueueItem::set_has_operation() {
  _oneof_case_[0] = kOperation;
}
inline void QueueItem::clear_operation() {
  if (has_operation()) {
    delete item_.operation_;
    clear_has_item();
  }
}
inline ::tensorflow::eager::Operation* QueueItem::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.operation)
  if (has_operation()) {
    clear_has_item();
      ::tensorflow::eager::Operation* temp = item_.operation_;
    item_.operation_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::Operation& QueueItem::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.operation)
  return has_operation()
      ? *item_.operation_
      : *reinterpret_cast< ::tensorflow::eager::Operation*>(&::tensorflow::eager::_Operation_default_instance_);
}
inline ::tensorflow::eager::Operation* QueueItem::mutable_operation() {
  if (!has_operation()) {
    clear_item();
    set_has_operation();
    item_.operation_ = CreateMaybeMessage< ::tensorflow::eager::Operation >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.operation)
  return item_.operation_;
}

// .tensorflow.eager.SendTensorOp send_tensor = 3;
inline bool QueueItem::has_send_tensor() const {
  return item_case() == kSendTensor;
}
inline void QueueItem::set_has_send_tensor() {
  _oneof_case_[0] = kSendTensor;
}
inline void QueueItem::clear_send_tensor() {
  if (has_send_tensor()) {
    delete item_.send_tensor_;
    clear_has_item();
  }
}
inline ::tensorflow::eager::SendTensorOp* QueueItem::release_send_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.send_tensor)
  if (has_send_tensor()) {
    clear_has_item();
      ::tensorflow::eager::SendTensorOp* temp = item_.send_tensor_;
    item_.send_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::SendTensorOp& QueueItem::send_tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.send_tensor)
  return has_send_tensor()
      ? *item_.send_tensor_
      : *reinterpret_cast< ::tensorflow::eager::SendTensorOp*>(&::tensorflow::eager::_SendTensorOp_default_instance_);
}
inline ::tensorflow::eager::SendTensorOp* QueueItem::mutable_send_tensor() {
  if (!has_send_tensor()) {
    clear_item();
    set_has_send_tensor();
    item_.send_tensor_ = CreateMaybeMessage< ::tensorflow::eager::SendTensorOp >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.send_tensor)
  return item_.send_tensor_;
}

// .tensorflow.eager.RegisterFunctionOp register_function = 4;
inline bool QueueItem::has_register_function() const {
  return item_case() == kRegisterFunction;
}
inline void QueueItem::set_has_register_function() {
  _oneof_case_[0] = kRegisterFunction;
}
inline void QueueItem::clear_register_function() {
  if (has_register_function()) {
    delete item_.register_function_;
    clear_has_item();
  }
}
inline ::tensorflow::eager::RegisterFunctionOp* QueueItem::release_register_function() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.register_function)
  if (has_register_function()) {
    clear_has_item();
      ::tensorflow::eager::RegisterFunctionOp* temp = item_.register_function_;
    item_.register_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::RegisterFunctionOp& QueueItem::register_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.register_function)
  return has_register_function()
      ? *item_.register_function_
      : *reinterpret_cast< ::tensorflow::eager::RegisterFunctionOp*>(&::tensorflow::eager::_RegisterFunctionOp_default_instance_);
}
inline ::tensorflow::eager::RegisterFunctionOp* QueueItem::mutable_register_function() {
  if (!has_register_function()) {
    clear_item();
    set_has_register_function();
    item_.register_function_ = CreateMaybeMessage< ::tensorflow::eager::RegisterFunctionOp >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.register_function)
  return item_.register_function_;
}

// .tensorflow.eager.CleanupFunctionOp cleanup_function = 5;
inline bool QueueItem::has_cleanup_function() const {
  return item_case() == kCleanupFunction;
}
inline void QueueItem::set_has_cleanup_function() {
  _oneof_case_[0] = kCleanupFunction;
}
inline void QueueItem::clear_cleanup_function() {
  if (has_cleanup_function()) {
    delete item_.cleanup_function_;
    clear_has_item();
  }
}
inline ::tensorflow::eager::CleanupFunctionOp* QueueItem::release_cleanup_function() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.cleanup_function)
  if (has_cleanup_function()) {
    clear_has_item();
      ::tensorflow::eager::CleanupFunctionOp* temp = item_.cleanup_function_;
    item_.cleanup_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::CleanupFunctionOp& QueueItem::cleanup_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.cleanup_function)
  return has_cleanup_function()
      ? *item_.cleanup_function_
      : *reinterpret_cast< ::tensorflow::eager::CleanupFunctionOp*>(&::tensorflow::eager::_CleanupFunctionOp_default_instance_);
}
inline ::tensorflow::eager::CleanupFunctionOp* QueueItem::mutable_cleanup_function() {
  if (!has_cleanup_function()) {
    clear_item();
    set_has_cleanup_function();
    item_.cleanup_function_ = CreateMaybeMessage< ::tensorflow::eager::CleanupFunctionOp >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.cleanup_function)
  return item_.cleanup_function_;
}

// .tensorflow.eager.SyncRemoteExecutorForStream sync_remote_executor_for_stream = 6;
inline bool QueueItem::has_sync_remote_executor_for_stream() const {
  return item_case() == kSyncRemoteExecutorForStream;
}
inline void QueueItem::set_has_sync_remote_executor_for_stream() {
  _oneof_case_[0] = kSyncRemoteExecutorForStream;
}
inline void QueueItem::clear_sync_remote_executor_for_stream() {
  if (has_sync_remote_executor_for_stream()) {
    delete item_.sync_remote_executor_for_stream_;
    clear_has_item();
  }
}
inline ::tensorflow::eager::SyncRemoteExecutorForStream* QueueItem::release_sync_remote_executor_for_stream() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.sync_remote_executor_for_stream)
  if (has_sync_remote_executor_for_stream()) {
    clear_has_item();
      ::tensorflow::eager::SyncRemoteExecutorForStream* temp = item_.sync_remote_executor_for_stream_;
    item_.sync_remote_executor_for_stream_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::SyncRemoteExecutorForStream& QueueItem::sync_remote_executor_for_stream() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.sync_remote_executor_for_stream)
  return has_sync_remote_executor_for_stream()
      ? *item_.sync_remote_executor_for_stream_
      : *reinterpret_cast< ::tensorflow::eager::SyncRemoteExecutorForStream*>(&::tensorflow::eager::_SyncRemoteExecutorForStream_default_instance_);
}
inline ::tensorflow::eager::SyncRemoteExecutorForStream* QueueItem::mutable_sync_remote_executor_for_stream() {
  if (!has_sync_remote_executor_for_stream()) {
    clear_item();
    set_has_sync_remote_executor_for_stream();
    item_.sync_remote_executor_for_stream_ = CreateMaybeMessage< ::tensorflow::eager::SyncRemoteExecutorForStream >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.sync_remote_executor_for_stream)
  return item_.sync_remote_executor_for_stream_;
}

// .tensorflow.eager.SendPackedHandleOp send_packed_handle = 7;
inline bool QueueItem::has_send_packed_handle() const {
  return item_case() == kSendPackedHandle;
}
inline void QueueItem::set_has_send_packed_handle() {
  _oneof_case_[0] = kSendPackedHandle;
}
inline void QueueItem::clear_send_packed_handle() {
  if (has_send_packed_handle()) {
    delete item_.send_packed_handle_;
    clear_has_item();
  }
}
inline ::tensorflow::eager::SendPackedHandleOp* QueueItem::release_send_packed_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.send_packed_handle)
  if (has_send_packed_handle()) {
    clear_has_item();
      ::tensorflow::eager::SendPackedHandleOp* temp = item_.send_packed_handle_;
    item_.send_packed_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::SendPackedHandleOp& QueueItem::send_packed_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.send_packed_handle)
  return has_send_packed_handle()
      ? *item_.send_packed_handle_
      : *reinterpret_cast< ::tensorflow::eager::SendPackedHandleOp*>(&::tensorflow::eager::_SendPackedHandleOp_default_instance_);
}
inline ::tensorflow::eager::SendPackedHandleOp* QueueItem::mutable_send_packed_handle() {
  if (!has_send_packed_handle()) {
    clear_item();
    set_has_send_packed_handle();
    item_.send_packed_handle_ = CreateMaybeMessage< ::tensorflow::eager::SendPackedHandleOp >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.send_packed_handle)
  return item_.send_packed_handle_;
}

inline bool QueueItem::has_item() const {
  return item_case() != ITEM_NOT_SET;
}
inline void QueueItem::clear_has_item() {
  _oneof_case_[0] = ITEM_NOT_SET;
}
inline QueueItem::ItemCase QueueItem::item_case() const {
  return QueueItem::ItemCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// QueueResponse

// repeated .tensorflow.TensorShapeProto shape = 1;
inline int QueueResponse::shape_size() const {
  return shape_.size();
}
inline ::tensorflow::TensorShapeProto* QueueResponse::mutable_shape(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueResponse.shape)
  return shape_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
QueueResponse::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.QueueResponse.shape)
  return &shape_;
}
inline const ::tensorflow::TensorShapeProto& QueueResponse::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueResponse.shape)
  return shape_.Get(index);
}
inline ::tensorflow::TensorShapeProto* QueueResponse::add_shape() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.QueueResponse.shape)
  return shape_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
QueueResponse::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.QueueResponse.shape)
  return shape_;
}

// repeated string device = 3;
inline int QueueResponse::device_size() const {
  return device_.size();
}
inline void QueueResponse::clear_device() {
  device_.Clear();
}
inline const std::string& QueueResponse::device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueResponse.device)
  return device_.Get(index);
}
inline std::string* QueueResponse::mutable_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueResponse.device)
  return device_.Mutable(index);
}
inline void QueueResponse::set_device(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.eager.QueueResponse.device)
  device_.Mutable(index)->assign(value);
}
inline void QueueResponse::set_device(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.eager.QueueResponse.device)
  device_.Mutable(index)->assign(std::move(value));
}
inline void QueueResponse::set_device(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  device_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.eager.QueueResponse.device)
}
inline void QueueResponse::set_device(int index, const char* value, size_t size) {
  device_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.eager.QueueResponse.device)
}
inline std::string* QueueResponse::add_device() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.eager.QueueResponse.device)
  return device_.Add();
}
inline void QueueResponse::add_device(const std::string& value) {
  device_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.eager.QueueResponse.device)
}
inline void QueueResponse::add_device(std::string&& value) {
  device_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.eager.QueueResponse.device)
}
inline void QueueResponse::add_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  device_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.eager.QueueResponse.device)
}
inline void QueueResponse::add_device(const char* value, size_t size) {
  device_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.eager.QueueResponse.device)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
QueueResponse::device() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.QueueResponse.device)
  return device_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
QueueResponse::mutable_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.QueueResponse.device)
  return &device_;
}

// repeated .tensorflow.TensorProto tensor = 2;
inline int QueueResponse::tensor_size() const {
  return tensor_.size();
}
inline ::tensorflow::TensorProto* QueueResponse::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueResponse.tensor)
  return tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
QueueResponse::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.QueueResponse.tensor)
  return &tensor_;
}
inline const ::tensorflow::TensorProto& QueueResponse::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueResponse.tensor)
  return tensor_.Get(index);
}
inline ::tensorflow::TensorProto* QueueResponse::add_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.QueueResponse.tensor)
  return tensor_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
QueueResponse::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.QueueResponse.tensor)
  return tensor_;
}

// -------------------------------------------------------------------

// CreateContextRequest

// .tensorflow.ServerDef server_def = 1;
inline bool CreateContextRequest::has_server_def() const {
  return this != internal_default_instance() && server_def_ != nullptr;
}
inline const ::tensorflow::ServerDef& CreateContextRequest::server_def() const {
  const ::tensorflow::ServerDef* p = server_def_;
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.server_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ServerDef*>(
      &::tensorflow::_ServerDef_default_instance_);
}
inline ::tensorflow::ServerDef* CreateContextRequest::release_server_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.CreateContextRequest.server_def)
  
  ::tensorflow::ServerDef* temp = server_def_;
  server_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ServerDef* CreateContextRequest::mutable_server_def() {
  
  if (server_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ServerDef>(GetArenaNoVirtual());
    server_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextRequest.server_def)
  return server_def_;
}
inline void CreateContextRequest::set_allocated_server_def(::tensorflow::ServerDef* server_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_def_);
  }
  if (server_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_def)->GetArena();
    if (message_arena != submessage_arena) {
      server_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, server_def, submessage_arena);
    }
    
  } else {
    
  }
  server_def_ = server_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.CreateContextRequest.server_def)
}

// bool async = 2;
inline void CreateContextRequest::clear_async() {
  async_ = false;
}
inline bool CreateContextRequest::async() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.async)
  return async_;
}
inline void CreateContextRequest::set_async(bool value) {
  
  async_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.async)
}

// int64 keep_alive_secs = 3;
inline void CreateContextRequest::clear_keep_alive_secs() {
  keep_alive_secs_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CreateContextRequest::keep_alive_secs() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.keep_alive_secs)
  return keep_alive_secs_;
}
inline void CreateContextRequest::set_keep_alive_secs(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  keep_alive_secs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.keep_alive_secs)
}

// .tensorflow.VersionDef version_def = 4;
inline bool CreateContextRequest::has_version_def() const {
  return this != internal_default_instance() && version_def_ != nullptr;
}
inline const ::tensorflow::VersionDef& CreateContextRequest::version_def() const {
  const ::tensorflow::VersionDef* p = version_def_;
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.version_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::VersionDef*>(
      &::tensorflow::_VersionDef_default_instance_);
}
inline ::tensorflow::VersionDef* CreateContextRequest::release_version_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.CreateContextRequest.version_def)
  
  ::tensorflow::VersionDef* temp = version_def_;
  version_def_ = nullptr;
  return temp;
}
inline ::tensorflow::VersionDef* CreateContextRequest::mutable_version_def() {
  
  if (version_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VersionDef>(GetArenaNoVirtual());
    version_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextRequest.version_def)
  return version_def_;
}
inline void CreateContextRequest::set_allocated_version_def(::tensorflow::VersionDef* version_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(version_def_);
  }
  if (version_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(version_def)->GetArena();
    if (message_arena != submessage_arena) {
      version_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, version_def, submessage_arena);
    }
    
  } else {
    
  }
  version_def_ = version_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.CreateContextRequest.version_def)
}

// repeated .tensorflow.DeviceAttributes cluster_device_attributes = 6;
inline int CreateContextRequest::cluster_device_attributes_size() const {
  return cluster_device_attributes_.size();
}
inline ::tensorflow::DeviceAttributes* CreateContextRequest::mutable_cluster_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextRequest.cluster_device_attributes)
  return cluster_device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
CreateContextRequest::mutable_cluster_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.CreateContextRequest.cluster_device_attributes)
  return &cluster_device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& CreateContextRequest::cluster_device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.cluster_device_attributes)
  return cluster_device_attributes_.Get(index);
}
inline ::tensorflow::DeviceAttributes* CreateContextRequest::add_cluster_device_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.CreateContextRequest.cluster_device_attributes)
  return cluster_device_attributes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
CreateContextRequest::cluster_device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.CreateContextRequest.cluster_device_attributes)
  return cluster_device_attributes_;
}

// fixed64 context_id = 7;
inline void CreateContextRequest::clear_context_id() {
  context_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 CreateContextRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.context_id)
  return context_id_;
}
inline void CreateContextRequest::set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.context_id)
}

// fixed64 context_view_id = 8;
inline void CreateContextRequest::clear_context_view_id() {
  context_view_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 CreateContextRequest::context_view_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.context_view_id)
  return context_view_id_;
}
inline void CreateContextRequest::set_context_view_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_view_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.context_view_id)
}

// bool lazy_copy_remote_function_inputs = 9;
inline void CreateContextRequest::clear_lazy_copy_remote_function_inputs() {
  lazy_copy_remote_function_inputs_ = false;
}
inline bool CreateContextRequest::lazy_copy_remote_function_inputs() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.lazy_copy_remote_function_inputs)
  return lazy_copy_remote_function_inputs_;
}
inline void CreateContextRequest::set_lazy_copy_remote_function_inputs(bool value) {
  
  lazy_copy_remote_function_inputs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.lazy_copy_remote_function_inputs)
}

// -------------------------------------------------------------------

// CreateContextResponse

// repeated .tensorflow.DeviceAttributes device_attributes = 2;
inline int CreateContextResponse::device_attributes_size() const {
  return device_attributes_.size();
}
inline ::tensorflow::DeviceAttributes* CreateContextResponse::mutable_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextResponse.device_attributes)
  return device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
CreateContextResponse::mutable_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.CreateContextResponse.device_attributes)
  return &device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& CreateContextResponse::device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextResponse.device_attributes)
  return device_attributes_.Get(index);
}
inline ::tensorflow::DeviceAttributes* CreateContextResponse::add_device_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.CreateContextResponse.device_attributes)
  return device_attributes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
CreateContextResponse::device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.CreateContextResponse.device_attributes)
  return device_attributes_;
}

// -------------------------------------------------------------------

// UpdateContextRequest

// .tensorflow.ServerDef server_def = 1;
inline bool UpdateContextRequest::has_server_def() const {
  return this != internal_default_instance() && server_def_ != nullptr;
}
inline const ::tensorflow::ServerDef& UpdateContextRequest::server_def() const {
  const ::tensorflow::ServerDef* p = server_def_;
  // @@protoc_insertion_point(field_get:tensorflow.eager.UpdateContextRequest.server_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ServerDef*>(
      &::tensorflow::_ServerDef_default_instance_);
}
inline ::tensorflow::ServerDef* UpdateContextRequest::release_server_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.UpdateContextRequest.server_def)
  
  ::tensorflow::ServerDef* temp = server_def_;
  server_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ServerDef* UpdateContextRequest::mutable_server_def() {
  
  if (server_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ServerDef>(GetArenaNoVirtual());
    server_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.UpdateContextRequest.server_def)
  return server_def_;
}
inline void UpdateContextRequest::set_allocated_server_def(::tensorflow::ServerDef* server_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_def_);
  }
  if (server_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_def)->GetArena();
    if (message_arena != submessage_arena) {
      server_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, server_def, submessage_arena);
    }
    
  } else {
    
  }
  server_def_ = server_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.UpdateContextRequest.server_def)
}

// repeated .tensorflow.DeviceAttributes cluster_device_attributes = 2;
inline int UpdateContextRequest::cluster_device_attributes_size() const {
  return cluster_device_attributes_.size();
}
inline ::tensorflow::DeviceAttributes* UpdateContextRequest::mutable_cluster_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.UpdateContextRequest.cluster_device_attributes)
  return cluster_device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
UpdateContextRequest::mutable_cluster_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.UpdateContextRequest.cluster_device_attributes)
  return &cluster_device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& UpdateContextRequest::cluster_device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.UpdateContextRequest.cluster_device_attributes)
  return cluster_device_attributes_.Get(index);
}
inline ::tensorflow::DeviceAttributes* UpdateContextRequest::add_cluster_device_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.UpdateContextRequest.cluster_device_attributes)
  return cluster_device_attributes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
UpdateContextRequest::cluster_device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.UpdateContextRequest.cluster_device_attributes)
  return cluster_device_attributes_;
}

// fixed64 context_id = 3;
inline void UpdateContextRequest::clear_context_id() {
  context_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 UpdateContextRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.UpdateContextRequest.context_id)
  return context_id_;
}
inline void UpdateContextRequest::set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.UpdateContextRequest.context_id)
}

// fixed64 context_view_id = 4;
inline void UpdateContextRequest::clear_context_view_id() {
  context_view_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 UpdateContextRequest::context_view_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.UpdateContextRequest.context_view_id)
  return context_view_id_;
}
inline void UpdateContextRequest::set_context_view_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_view_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.UpdateContextRequest.context_view_id)
}

// -------------------------------------------------------------------

// UpdateContextResponse

// repeated .tensorflow.DeviceAttributes device_attributes = 1;
inline int UpdateContextResponse::device_attributes_size() const {
  return device_attributes_.size();
}
inline ::tensorflow::DeviceAttributes* UpdateContextResponse::mutable_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.UpdateContextResponse.device_attributes)
  return device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
UpdateContextResponse::mutable_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.UpdateContextResponse.device_attributes)
  return &device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& UpdateContextResponse::device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.UpdateContextResponse.device_attributes)
  return device_attributes_.Get(index);
}
inline ::tensorflow::DeviceAttributes* UpdateContextResponse::add_device_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.UpdateContextResponse.device_attributes)
  return device_attributes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
UpdateContextResponse::device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.UpdateContextResponse.device_attributes)
  return device_attributes_;
}

// -------------------------------------------------------------------

// EnqueueRequest

// fixed64 context_id = 1;
inline void EnqueueRequest::clear_context_id() {
  context_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 EnqueueRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.EnqueueRequest.context_id)
  return context_id_;
}
inline void EnqueueRequest::set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.EnqueueRequest.context_id)
}

// repeated .tensorflow.eager.QueueItem queue = 3;
inline int EnqueueRequest::queue_size() const {
  return queue_.size();
}
inline void EnqueueRequest::clear_queue() {
  queue_.Clear();
}
inline ::tensorflow::eager::QueueItem* EnqueueRequest::mutable_queue(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.EnqueueRequest.queue)
  return queue_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueItem >*
EnqueueRequest::mutable_queue() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.EnqueueRequest.queue)
  return &queue_;
}
inline const ::tensorflow::eager::QueueItem& EnqueueRequest::queue(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.EnqueueRequest.queue)
  return queue_.Get(index);
}
inline ::tensorflow::eager::QueueItem* EnqueueRequest::add_queue() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.EnqueueRequest.queue)
  return queue_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueItem >&
EnqueueRequest::queue() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.EnqueueRequest.queue)
  return queue_;
}

// -------------------------------------------------------------------

// EnqueueResponse

// repeated .tensorflow.eager.QueueResponse queue_response = 1;
inline int EnqueueResponse::queue_response_size() const {
  return queue_response_.size();
}
inline void EnqueueResponse::clear_queue_response() {
  queue_response_.Clear();
}
inline ::tensorflow::eager::QueueResponse* EnqueueResponse::mutable_queue_response(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.EnqueueResponse.queue_response)
  return queue_response_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueResponse >*
EnqueueResponse::mutable_queue_response() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.EnqueueResponse.queue_response)
  return &queue_response_;
}
inline const ::tensorflow::eager::QueueResponse& EnqueueResponse::queue_response(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.EnqueueResponse.queue_response)
  return queue_response_.Get(index);
}
inline ::tensorflow::eager::QueueResponse* EnqueueResponse::add_queue_response() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.EnqueueResponse.queue_response)
  return queue_response_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueResponse >&
EnqueueResponse::queue_response() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.EnqueueResponse.queue_response)
  return queue_response_;
}

// -------------------------------------------------------------------

// WaitQueueDoneRequest

// fixed64 context_id = 1;
inline void WaitQueueDoneRequest::clear_context_id() {
  context_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 WaitQueueDoneRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.WaitQueueDoneRequest.context_id)
  return context_id_;
}
inline void WaitQueueDoneRequest::set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.WaitQueueDoneRequest.context_id)
}

// repeated int64 op_id = 2;
inline int WaitQueueDoneRequest::op_id_size() const {
  return op_id_.size();
}
inline void WaitQueueDoneRequest::clear_op_id() {
  op_id_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 WaitQueueDoneRequest::op_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.WaitQueueDoneRequest.op_id)
  return op_id_.Get(index);
}
inline void WaitQueueDoneRequest::set_op_id(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  op_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.WaitQueueDoneRequest.op_id)
}
inline void WaitQueueDoneRequest::add_op_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  op_id_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.eager.WaitQueueDoneRequest.op_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
WaitQueueDoneRequest::op_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.WaitQueueDoneRequest.op_id)
  return op_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
WaitQueueDoneRequest::mutable_op_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.WaitQueueDoneRequest.op_id)
  return &op_id_;
}

// -------------------------------------------------------------------

// WaitQueueDoneResponse

// -------------------------------------------------------------------

// RunComponentFunctionRequest

// fixed64 context_id = 1;
inline void RunComponentFunctionRequest::clear_context_id() {
  context_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RunComponentFunctionRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RunComponentFunctionRequest.context_id)
  return context_id_;
}
inline void RunComponentFunctionRequest::set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.RunComponentFunctionRequest.context_id)
}

// .tensorflow.eager.Operation operation = 2;
inline bool RunComponentFunctionRequest::has_operation() const {
  return this != internal_default_instance() && operation_ != nullptr;
}
inline void RunComponentFunctionRequest::clear_operation() {
  if (GetArenaNoVirtual() == nullptr && operation_ != nullptr) {
    delete operation_;
  }
  operation_ = nullptr;
}
inline const ::tensorflow::eager::Operation& RunComponentFunctionRequest::operation() const {
  const ::tensorflow::eager::Operation* p = operation_;
  // @@protoc_insertion_point(field_get:tensorflow.eager.RunComponentFunctionRequest.operation)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::eager::Operation*>(
      &::tensorflow::eager::_Operation_default_instance_);
}
inline ::tensorflow::eager::Operation* RunComponentFunctionRequest::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.RunComponentFunctionRequest.operation)
  
  ::tensorflow::eager::Operation* temp = operation_;
  operation_ = nullptr;
  return temp;
}
inline ::tensorflow::eager::Operation* RunComponentFunctionRequest::mutable_operation() {
  
  if (operation_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::eager::Operation>(GetArenaNoVirtual());
    operation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RunComponentFunctionRequest.operation)
  return operation_;
}
inline void RunComponentFunctionRequest::set_allocated_operation(::tensorflow::eager::Operation* operation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete operation_;
  }
  if (operation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      operation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, operation, submessage_arena);
    }
    
  } else {
    
  }
  operation_ = operation;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.RunComponentFunctionRequest.operation)
}

// repeated int32 output_num = 3;
inline int RunComponentFunctionRequest::output_num_size() const {
  return output_num_.size();
}
inline void RunComponentFunctionRequest::clear_output_num() {
  output_num_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RunComponentFunctionRequest::output_num(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RunComponentFunctionRequest.output_num)
  return output_num_.Get(index);
}
inline void RunComponentFunctionRequest::set_output_num(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  output_num_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.RunComponentFunctionRequest.output_num)
}
inline void RunComponentFunctionRequest::add_output_num(::PROTOBUF_NAMESPACE_ID::int32 value) {
  output_num_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.eager.RunComponentFunctionRequest.output_num)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
RunComponentFunctionRequest::output_num() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.RunComponentFunctionRequest.output_num)
  return output_num_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
RunComponentFunctionRequest::mutable_output_num() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.RunComponentFunctionRequest.output_num)
  return &output_num_;
}

// -------------------------------------------------------------------

// RunComponentFunctionResponse

// repeated .tensorflow.TensorShapeProto shape = 1;
inline int RunComponentFunctionResponse::shape_size() const {
  return shape_.size();
}
inline ::tensorflow::TensorShapeProto* RunComponentFunctionResponse::mutable_shape(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RunComponentFunctionResponse.shape)
  return shape_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
RunComponentFunctionResponse::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.RunComponentFunctionResponse.shape)
  return &shape_;
}
inline const ::tensorflow::TensorShapeProto& RunComponentFunctionResponse::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RunComponentFunctionResponse.shape)
  return shape_.Get(index);
}
inline ::tensorflow::TensorShapeProto* RunComponentFunctionResponse::add_shape() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.RunComponentFunctionResponse.shape)
  return shape_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
RunComponentFunctionResponse::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.RunComponentFunctionResponse.shape)
  return shape_;
}

// repeated .tensorflow.TensorProto tensor = 2;
inline int RunComponentFunctionResponse::tensor_size() const {
  return tensor_.size();
}
inline ::tensorflow::TensorProto* RunComponentFunctionResponse::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RunComponentFunctionResponse.tensor)
  return tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
RunComponentFunctionResponse::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.RunComponentFunctionResponse.tensor)
  return &tensor_;
}
inline const ::tensorflow::TensorProto& RunComponentFunctionResponse::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RunComponentFunctionResponse.tensor)
  return tensor_.Get(index);
}
inline ::tensorflow::TensorProto* RunComponentFunctionResponse::add_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.RunComponentFunctionResponse.tensor)
  return tensor_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
RunComponentFunctionResponse::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.RunComponentFunctionResponse.tensor)
  return tensor_;
}

// -------------------------------------------------------------------

// KeepAliveRequest

// fixed64 context_id = 1;
inline void KeepAliveRequest::clear_context_id() {
  context_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 KeepAliveRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.KeepAliveRequest.context_id)
  return context_id_;
}
inline void KeepAliveRequest::set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.KeepAliveRequest.context_id)
}

// -------------------------------------------------------------------

// KeepAliveResponse

// fixed64 context_view_id = 1;
inline void KeepAliveResponse::clear_context_view_id() {
  context_view_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 KeepAliveResponse::context_view_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.KeepAliveResponse.context_view_id)
  return context_view_id_;
}
inline void KeepAliveResponse::set_context_view_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_view_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.KeepAliveResponse.context_view_id)
}

// -------------------------------------------------------------------

// CloseContextRequest

// fixed64 context_id = 1;
inline void CloseContextRequest::clear_context_id() {
  context_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 CloseContextRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CloseContextRequest.context_id)
  return context_id_;
}
inline void CloseContextRequest::set_context_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CloseContextRequest.context_id)
}

// fixed64 context_view_id = 2;
inline void CloseContextRequest::clear_context_view_id() {
  context_view_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 CloseContextRequest::context_view_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CloseContextRequest.context_view_id)
  return context_view_id_;
}
inline void CloseContextRequest::set_context_view_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  context_view_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CloseContextRequest.context_view_id)
}

// -------------------------------------------------------------------

// CloseContextResponse

// -------------------------------------------------------------------

// RegisterFunctionOp

// .tensorflow.FunctionDef function_def = 1;
inline bool RegisterFunctionOp::has_function_def() const {
  return this != internal_default_instance() && function_def_ != nullptr;
}
inline const ::tensorflow::FunctionDef& RegisterFunctionOp::function_def() const {
  const ::tensorflow::FunctionDef* p = function_def_;
  // @@protoc_insertion_point(field_get:tensorflow.eager.RegisterFunctionOp.function_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::FunctionDef*>(
      &::tensorflow::_FunctionDef_default_instance_);
}
inline ::tensorflow::FunctionDef* RegisterFunctionOp::release_function_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.RegisterFunctionOp.function_def)
  
  ::tensorflow::FunctionDef* temp = function_def_;
  function_def_ = nullptr;
  return temp;
}
inline ::tensorflow::FunctionDef* RegisterFunctionOp::mutable_function_def() {
  
  if (function_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionDef>(GetArenaNoVirtual());
    function_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RegisterFunctionOp.function_def)
  return function_def_;
}
inline void RegisterFunctionOp::set_allocated_function_def(::tensorflow::FunctionDef* function_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(function_def_);
  }
  if (function_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(function_def)->GetArena();
    if (message_arena != submessage_arena) {
      function_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, function_def, submessage_arena);
    }
    
  } else {
    
  }
  function_def_ = function_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.RegisterFunctionOp.function_def)
}

// bool is_component_function = 2;
inline void RegisterFunctionOp::clear_is_component_function() {
  is_component_function_ = false;
}
inline bool RegisterFunctionOp::is_component_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RegisterFunctionOp.is_component_function)
  return is_component_function_;
}
inline void RegisterFunctionOp::set_is_component_function(bool value) {
  
  is_component_function_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.RegisterFunctionOp.is_component_function)
}

// .tensorflow.FunctionDefLibrary library = 3;
inline bool RegisterFunctionOp::has_library() const {
  return this != internal_default_instance() && library_ != nullptr;
}
inline const ::tensorflow::FunctionDefLibrary& RegisterFunctionOp::library() const {
  const ::tensorflow::FunctionDefLibrary* p = library_;
  // @@protoc_insertion_point(field_get:tensorflow.eager.RegisterFunctionOp.library)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::FunctionDefLibrary*>(
      &::tensorflow::_FunctionDefLibrary_default_instance_);
}
inline ::tensorflow::FunctionDefLibrary* RegisterFunctionOp::release_library() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.RegisterFunctionOp.library)
  
  ::tensorflow::FunctionDefLibrary* temp = library_;
  library_ = nullptr;
  return temp;
}
inline ::tensorflow::FunctionDefLibrary* RegisterFunctionOp::mutable_library() {
  
  if (library_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionDefLibrary>(GetArenaNoVirtual());
    library_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RegisterFunctionOp.library)
  return library_;
}
inline void RegisterFunctionOp::set_allocated_library(::tensorflow::FunctionDefLibrary* library) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(library_);
  }
  if (library) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(library)->GetArena();
    if (message_arena != submessage_arena) {
      library = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, library, submessage_arena);
    }
    
  } else {
    
  }
  library_ = library;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.RegisterFunctionOp.library)
}

// -------------------------------------------------------------------

// CleanupFunctionOp

// int64 step_id = 1;
inline void CleanupFunctionOp::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CleanupFunctionOp::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CleanupFunctionOp.step_id)
  return step_id_;
}
inline void CleanupFunctionOp::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CleanupFunctionOp.step_id)
}

// -------------------------------------------------------------------

// SyncRemoteExecutorForStream

// -------------------------------------------------------------------

// SendTensorOp

// int64 op_id = 1;
inline void SendTensorOp::clear_op_id() {
  op_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SendTensorOp::op_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendTensorOp.op_id)
  return op_id_;
}
inline void SendTensorOp::set_op_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  op_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendTensorOp.op_id)
}

// repeated .tensorflow.TensorProto tensors = 2;
inline int SendTensorOp::tensors_size() const {
  return tensors_.size();
}
inline ::tensorflow::TensorProto* SendTensorOp::mutable_tensors(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendTensorOp.tensors)
  return tensors_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
SendTensorOp::mutable_tensors() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.SendTensorOp.tensors)
  return &tensors_;
}
inline const ::tensorflow::TensorProto& SendTensorOp::tensors(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendTensorOp.tensors)
  return tensors_.Get(index);
}
inline ::tensorflow::TensorProto* SendTensorOp::add_tensors() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.SendTensorOp.tensors)
  return tensors_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
SendTensorOp::tensors() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.SendTensorOp.tensors)
  return tensors_;
}

// string device_name = 3;
inline void SendTensorOp::clear_device_name() {
  device_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& SendTensorOp::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendTensorOp.device_name)
  return device_name_.GetNoArena();
}
inline void SendTensorOp::set_device_name(const std::string& value) {
  
  device_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendTensorOp.device_name)
}
inline void SendTensorOp::set_device_name(std::string&& value) {
  
  device_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.eager.SendTensorOp.device_name)
}
inline void SendTensorOp::set_device_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.eager.SendTensorOp.device_name)
}
inline void SendTensorOp::set_device_name(const char* value, size_t size) {
  
  device_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.eager.SendTensorOp.device_name)
}
inline std::string* SendTensorOp::mutable_device_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendTensorOp.device_name)
  return device_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* SendTensorOp::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendTensorOp.device_name)
  
  return device_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void SendTensorOp::set_allocated_device_name(std::string* device_name) {
  if (device_name != nullptr) {
    
  } else {
    
  }
  device_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.SendTensorOp.device_name)
}

// -------------------------------------------------------------------

// SendPackedHandleOp_LocalTensorHandle

// .tensorflow.TensorProto tensor = 1;
inline bool SendPackedHandleOp_LocalTensorHandle::has_tensor() const {
  return this != internal_default_instance() && tensor_ != nullptr;
}
inline const ::tensorflow::TensorProto& SendPackedHandleOp_LocalTensorHandle::tensor() const {
  const ::tensorflow::TensorProto* p = tensor_;
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.tensor)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* SendPackedHandleOp_LocalTensorHandle::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.tensor)
  
  ::tensorflow::TensorProto* temp = tensor_;
  tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* SendPackedHandleOp_LocalTensorHandle::mutable_tensor() {
  
  if (tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    tensor_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.tensor)
  return tensor_;
}
inline void SendPackedHandleOp_LocalTensorHandle::set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_);
  }
  if (tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor)->GetArena();
    if (message_arena != submessage_arena) {
      tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.tensor)
}

// string device = 2;
inline void SendPackedHandleOp_LocalTensorHandle::clear_device() {
  device_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& SendPackedHandleOp_LocalTensorHandle::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
  return device_.GetNoArena();
}
inline void SendPackedHandleOp_LocalTensorHandle::set_device(const std::string& value) {
  
  device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
}
inline void SendPackedHandleOp_LocalTensorHandle::set_device(std::string&& value) {
  
  device_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
}
inline void SendPackedHandleOp_LocalTensorHandle::set_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
}
inline void SendPackedHandleOp_LocalTensorHandle::set_device(const char* value, size_t size) {
  
  device_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
}
inline std::string* SendPackedHandleOp_LocalTensorHandle::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
  return device_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* SendPackedHandleOp_LocalTensorHandle::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
  
  return device_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void SendPackedHandleOp_LocalTensorHandle::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  device_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
}

// -------------------------------------------------------------------

// SendPackedHandleOp_Handle

// .tensorflow.eager.SendPackedHandleOp.LocalTensorHandle local_handle = 1;
inline bool SendPackedHandleOp_Handle::has_local_handle() const {
  return item_case() == kLocalHandle;
}
inline void SendPackedHandleOp_Handle::set_has_local_handle() {
  _oneof_case_[0] = kLocalHandle;
}
inline void SendPackedHandleOp_Handle::clear_local_handle() {
  if (has_local_handle()) {
    delete item_.local_handle_;
    clear_has_item();
  }
}
inline ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* SendPackedHandleOp_Handle::release_local_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendPackedHandleOp.Handle.local_handle)
  if (has_local_handle()) {
    clear_has_item();
      ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* temp = item_.local_handle_;
    item_.local_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle& SendPackedHandleOp_Handle::local_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.Handle.local_handle)
  return has_local_handle()
      ? *item_.local_handle_
      : *reinterpret_cast< ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle*>(&::tensorflow::eager::_SendPackedHandleOp_LocalTensorHandle_default_instance_);
}
inline ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* SendPackedHandleOp_Handle::mutable_local_handle() {
  if (!has_local_handle()) {
    clear_item();
    set_has_local_handle();
    item_.local_handle_ = CreateMaybeMessage< ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.Handle.local_handle)
  return item_.local_handle_;
}

// .tensorflow.eager.RemoteTensorHandle remote_handle = 2;
inline bool SendPackedHandleOp_Handle::has_remote_handle() const {
  return item_case() == kRemoteHandle;
}
inline void SendPackedHandleOp_Handle::set_has_remote_handle() {
  _oneof_case_[0] = kRemoteHandle;
}
inline ::tensorflow::eager::RemoteTensorHandle* SendPackedHandleOp_Handle::release_remote_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendPackedHandleOp.Handle.remote_handle)
  if (has_remote_handle()) {
    clear_has_item();
      ::tensorflow::eager::RemoteTensorHandle* temp = item_.remote_handle_;
    item_.remote_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::RemoteTensorHandle& SendPackedHandleOp_Handle::remote_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.Handle.remote_handle)
  return has_remote_handle()
      ? *item_.remote_handle_
      : *reinterpret_cast< ::tensorflow::eager::RemoteTensorHandle*>(&::tensorflow::eager::_RemoteTensorHandle_default_instance_);
}
inline ::tensorflow::eager::RemoteTensorHandle* SendPackedHandleOp_Handle::mutable_remote_handle() {
  if (!has_remote_handle()) {
    clear_item();
    set_has_remote_handle();
    item_.remote_handle_ = CreateMaybeMessage< ::tensorflow::eager::RemoteTensorHandle >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.Handle.remote_handle)
  return item_.remote_handle_;
}

inline bool SendPackedHandleOp_Handle::has_item() const {
  return item_case() != ITEM_NOT_SET;
}
inline void SendPackedHandleOp_Handle::clear_has_item() {
  _oneof_case_[0] = ITEM_NOT_SET;
}
inline SendPackedHandleOp_Handle::ItemCase SendPackedHandleOp_Handle::item_case() const {
  return SendPackedHandleOp_Handle::ItemCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// SendPackedHandleOp

// int64 op_id = 1;
inline void SendPackedHandleOp::clear_op_id() {
  op_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SendPackedHandleOp::op_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.op_id)
  return op_id_;
}
inline void SendPackedHandleOp::set_op_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  op_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendPackedHandleOp.op_id)
}

// repeated .tensorflow.eager.SendPackedHandleOp.Handle handles = 2;
inline int SendPackedHandleOp::handles_size() const {
  return handles_.size();
}
inline void SendPackedHandleOp::clear_handles() {
  handles_.Clear();
}
inline ::tensorflow::eager::SendPackedHandleOp_Handle* SendPackedHandleOp::mutable_handles(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.handles)
  return handles_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::SendPackedHandleOp_Handle >*
SendPackedHandleOp::mutable_handles() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.SendPackedHandleOp.handles)
  return &handles_;
}
inline const ::tensorflow::eager::SendPackedHandleOp_Handle& SendPackedHandleOp::handles(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.handles)
  return handles_.Get(index);
}
inline ::tensorflow::eager::SendPackedHandleOp_Handle* SendPackedHandleOp::add_handles() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.SendPackedHandleOp.handles)
  return handles_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::SendPackedHandleOp_Handle >&
SendPackedHandleOp::handles() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.SendPackedHandleOp.handles)
  return handles_;
}

// string device_name = 3;
inline void SendPackedHandleOp::clear_device_name() {
  device_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& SendPackedHandleOp::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.device_name)
  return device_name_.GetNoArena();
}
inline void SendPackedHandleOp::set_device_name(const std::string& value) {
  
  device_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendPackedHandleOp.device_name)
}
inline void SendPackedHandleOp::set_device_name(std::string&& value) {
  
  device_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.eager.SendPackedHandleOp.device_name)
}
inline void SendPackedHandleOp::set_device_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.eager.SendPackedHandleOp.device_name)
}
inline void SendPackedHandleOp::set_device_name(const char* value, size_t size) {
  
  device_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.eager.SendPackedHandleOp.device_name)
}
inline std::string* SendPackedHandleOp::mutable_device_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.device_name)
  return device_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* SendPackedHandleOp::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendPackedHandleOp.device_name)
  
  return device_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void SendPackedHandleOp::set_allocated_device_name(std::string* device_name) {
  if (device_name != nullptr) {
    
  } else {
    
  }
  device_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.SendPackedHandleOp.device_name)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace eager
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto
