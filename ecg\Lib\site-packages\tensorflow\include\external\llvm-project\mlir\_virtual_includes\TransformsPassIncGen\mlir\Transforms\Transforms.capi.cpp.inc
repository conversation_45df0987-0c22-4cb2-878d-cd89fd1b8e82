/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// Transforms Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterTransformsPasses() {
  registerTransformsPasses();
}

MlirPass mlirCreateTransformsAffineLoopFusion() {
  return wrap(mlir::createLoopFusionPass().release());
}
void mlirRegisterTransformsAffineLoopFusion() {
  registerAffineLoopFusionPass();
}


MlirPass mlirCreateTransformsAffinePipelineDataTransfer() {
  return wrap(mlir::createPipelineDataTransferPass().release());
}
void mlirRegisterTransformsAffinePipelineDataTransfer() {
  registerAffinePipelineDataTransferPass();
}


MlirPass mlirCreateTransformsBufferDeallocation() {
  return wrap(mlir::createBufferDeallocationPass().release());
}
void mlirRegisterTransformsBufferDeallocation() {
  registerBufferDeallocationPass();
}


MlirPass mlirCreateTransformsBufferHoisting() {
  return wrap(mlir::createBufferHoistingPass().release());
}
void mlirRegisterTransformsBufferHoisting() {
  registerBufferHoistingPass();
}


MlirPass mlirCreateTransformsBufferLoopHoisting() {
  return wrap(mlir::createBufferLoopHoistingPass().release());
}
void mlirRegisterTransformsBufferLoopHoisting() {
  registerBufferLoopHoistingPass();
}


MlirPass mlirCreateTransformsBufferResultsToOutParams() {
  return wrap(mlir::createBufferResultsToOutParamsPass().release());
}
void mlirRegisterTransformsBufferResultsToOutParams() {
  registerBufferResultsToOutParamsPass();
}


MlirPass mlirCreateTransformsCSE() {
  return wrap(mlir::createCSEPass().release());
}
void mlirRegisterTransformsCSE() {
  registerCSEPass();
}


MlirPass mlirCreateTransformsCanonicalizer() {
  return wrap(mlir::createCanonicalizerPass().release());
}
void mlirRegisterTransformsCanonicalizer() {
  registerCanonicalizerPass();
}


MlirPass mlirCreateTransformsFinalizingBufferize() {
  return wrap(mlir::createFinalizingBufferizePass().release());
}
void mlirRegisterTransformsFinalizingBufferize() {
  registerFinalizingBufferizePass();
}


MlirPass mlirCreateTransformsInliner() {
  return wrap(mlir::createInlinerPass().release());
}
void mlirRegisterTransformsInliner() {
  registerInlinerPass();
}


MlirPass mlirCreateTransformsLocationSnapshot() {
  return wrap(mlir::createLocationSnapshotPass().release());
}
void mlirRegisterTransformsLocationSnapshot() {
  registerLocationSnapshotPass();
}


MlirPass mlirCreateTransformsLoopCoalescing() {
  return wrap(mlir::createLoopCoalescingPass().release());
}
void mlirRegisterTransformsLoopCoalescing() {
  registerLoopCoalescingPass();
}


MlirPass mlirCreateTransformsLoopInvariantCodeMotion() {
  return wrap(mlir::createLoopInvariantCodeMotionPass().release());
}
void mlirRegisterTransformsLoopInvariantCodeMotion() {
  registerLoopInvariantCodeMotionPass();
}


MlirPass mlirCreateTransformsNormalizeMemRefs() {
  return wrap(mlir::createNormalizeMemRefsPass().release());
}
void mlirRegisterTransformsNormalizeMemRefs() {
  registerNormalizeMemRefsPass();
}


MlirPass mlirCreateTransformsParallelLoopCollapsing() {
  return wrap(mlir::createParallelLoopCollapsingPass().release());
}
void mlirRegisterTransformsParallelLoopCollapsing() {
  registerParallelLoopCollapsingPass();
}


MlirPass mlirCreateTransformsPrintCFG() {
  return wrap(mlir::createPrintCFGGraphPass().release());
}
void mlirRegisterTransformsPrintCFG() {
  registerPrintCFGPass();
}


MlirPass mlirCreateTransformsPrintOpStats() {
  return wrap(mlir::createPrintOpStatsPass().release());
}
void mlirRegisterTransformsPrintOpStats() {
  registerPrintOpStatsPass();
}


MlirPass mlirCreateTransformsPromoteBuffersToStack() {
  return wrap(mlir::createPromoteBuffersToStackPass().release());
}
void mlirRegisterTransformsPromoteBuffersToStack() {
  registerPromoteBuffersToStackPass();
}


MlirPass mlirCreateTransformsSCCP() {
  return wrap(mlir::createSCCPPass().release());
}
void mlirRegisterTransformsSCCP() {
  registerSCCPPass();
}


MlirPass mlirCreateTransformsStripDebugInfo() {
  return wrap(mlir::createStripDebugInfoPass().release());
}
void mlirRegisterTransformsStripDebugInfo() {
  registerStripDebugInfoPass();
}


MlirPass mlirCreateTransformsSymbolDCE() {
  return wrap(mlir::createSymbolDCEPass().release());
}
void mlirRegisterTransformsSymbolDCE() {
  registerSymbolDCEPass();
}


MlirPass mlirCreateTransformsViewOpGraphPass() {
  return wrap(mlir::createPrintOpGraphPass().release());
}
void mlirRegisterTransformsViewOpGraphPass() {
  registerViewOpGraphPassPass();
}

