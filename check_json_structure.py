#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查JSON数据结构，看看是否有ecgII字段
"""

import json

def check_json_structure(file_path):
    """检查JSON文件的结构"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("=== JSON数据结构分析 ===")
    print(f"数据类型: {type(data)}")
    
    if isinstance(data, dict):
        print(f"包含的键: {list(data.keys())}")
        print()
        
        for key, value in data.items():
            print(f"键: {key}")
            print(f"  类型: {type(value)}")
            if isinstance(value, str) and len(value) > 100:
                print(f"  长度: {len(value)} 字符")
                print(f"  前100字符: {value[:100]}...")
            elif isinstance(value, list):
                print(f"  长度: {len(value)} 个元素")
                if len(value) > 0:
                    print(f"  第一个元素类型: {type(value[0])}")
                    print(f"  第一个元素: {value[0]}")
            else:
                print(f"  值: {value}")
            print()
    
    # 检查是否有ecgII相关的字段
    ecg_fields = []
    if isinstance(data, dict):
        for key in data.keys():
            if 'ecg' in key.lower():
                ecg_fields.append(key)
    
    print("=== ECG相关字段 ===")
    if ecg_fields:
        print(f"找到ECG相关字段: {ecg_fields}")
        for field in ecg_fields:
            print(f"字段 '{field}' 的数据类型: {type(data[field])}")
    else:
        print("未找到ECG相关字段")
    
    return data

if __name__ == "__main__":
    file_path = r'qiniu_query_results\低电压异常数据\肖总0726异常数据.json'
    data = check_json_structure(file_path)
