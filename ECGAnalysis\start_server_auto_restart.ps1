# PowerShell script to run and monitor Django server
# 使用UTF-8编码保存此文件

# 设置环境变量 - 添加Matplotlib相关配置
$env:PYTHONPATH = $PWD
$env:MPLBACKEND = "Agg"  # 强制matplotlib使用非交互式后端
$env:TF_CPP_MIN_LOG_LEVEL = "3"  # 禁用TensorFlow警告
$env:PYTHONIOENCODING = "utf-8"  # 确保正确的字符编码

Write-Host "设置环境变量:"
Write-Host "MPLBACKEND = $env:MPLBACKEND (非交互式matplotlib后端)"
Write-Host "TF_CPP_MIN_LOG_LEVEL = $env:TF_CPP_MIN_LOG_LEVEL (禁用TensorFlow警告)"

# 使用脚本所在目录作为工作目录，避免中文路径问题
$WorkingDirectory = $PSScriptRoot
$ScriptToRun = "python manage.py runserver"

Write-Host "Monitoring and auto-restarting Django server."
Write-Host "Working Directory: $WorkingDirectory"
Write-Host "Script to run: $ScriptToRun"
Write-Host "Press Ctrl+C to stop this monitoring script."
Write-Host "--------------------------------------------------"

while ($true) {
    Write-Host "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Starting Django server..."
    
    try {
        # 切换到工作目录
        Set-Location -Path $WorkingDirectory
        
        # 直接在当前PowerShell会话中执行命令，便于查看错误
        & python manage.py runserver
        
        $exitCode = $LASTEXITCODE
        
        if ($exitCode -ne 0) {
            Write-Warning "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Django server process exited with code $exitCode."
        } else {
            Write-Host "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Django server process exited cleanly (code 0)."
        }
    }
    catch {
        Write-Warning "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Error: $_"
    }
    
    Write-Host "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Restarting in 5 seconds..."
    Start-Sleep -Seconds 5
}