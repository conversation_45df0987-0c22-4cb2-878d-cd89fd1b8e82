// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/tpu_embedding_configuration.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/tpu/optimization_parameters.pb.h"
#include "tensorflow/core/protobuf/tpu/tpu_embedding_output_layout.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto;
namespace tensorflow {
namespace tpu {
class TPUEmbeddingConfiguration;
class TPUEmbeddingConfigurationDefaultTypeInternal;
extern TPUEmbeddingConfigurationDefaultTypeInternal _TPUEmbeddingConfiguration_default_instance_;
class TPUEmbeddingConfiguration_TableDescriptor;
class TPUEmbeddingConfiguration_TableDescriptorDefaultTypeInternal;
extern TPUEmbeddingConfiguration_TableDescriptorDefaultTypeInternal _TPUEmbeddingConfiguration_TableDescriptor_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tpu::TPUEmbeddingConfiguration* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingConfiguration>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tpu {

enum TPUEmbeddingConfiguration_Mode : int {
  TPUEmbeddingConfiguration_Mode_UNSPECIFIED = 0,
  TPUEmbeddingConfiguration_Mode_INFERENCE = 1,
  TPUEmbeddingConfiguration_Mode_TRAINING = 2,
  TPUEmbeddingConfiguration_Mode_BACKWARD_PASS_ONLY = 3,
  TPUEmbeddingConfiguration_Mode_TPUEmbeddingConfiguration_Mode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  TPUEmbeddingConfiguration_Mode_TPUEmbeddingConfiguration_Mode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool TPUEmbeddingConfiguration_Mode_IsValid(int value);
constexpr TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration_Mode_Mode_MIN = TPUEmbeddingConfiguration_Mode_UNSPECIFIED;
constexpr TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration_Mode_Mode_MAX = TPUEmbeddingConfiguration_Mode_BACKWARD_PASS_ONLY;
constexpr int TPUEmbeddingConfiguration_Mode_Mode_ARRAYSIZE = TPUEmbeddingConfiguration_Mode_Mode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TPUEmbeddingConfiguration_Mode_descriptor();
template<typename T>
inline const std::string& TPUEmbeddingConfiguration_Mode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TPUEmbeddingConfiguration_Mode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TPUEmbeddingConfiguration_Mode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TPUEmbeddingConfiguration_Mode_descriptor(), enum_t_value);
}
inline bool TPUEmbeddingConfiguration_Mode_Parse(
    const std::string& name, TPUEmbeddingConfiguration_Mode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TPUEmbeddingConfiguration_Mode>(
    TPUEmbeddingConfiguration_Mode_descriptor(), name, value);
}
enum TPUEmbeddingConfiguration_ShardingStrategy : int {
  TPUEmbeddingConfiguration_ShardingStrategy_DIV_DEFAULT = 0,
  TPUEmbeddingConfiguration_ShardingStrategy_MOD = 1,
  TPUEmbeddingConfiguration_ShardingStrategy_TPUEmbeddingConfiguration_ShardingStrategy_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  TPUEmbeddingConfiguration_ShardingStrategy_TPUEmbeddingConfiguration_ShardingStrategy_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool TPUEmbeddingConfiguration_ShardingStrategy_IsValid(int value);
constexpr TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MIN = TPUEmbeddingConfiguration_ShardingStrategy_DIV_DEFAULT;
constexpr TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MAX = TPUEmbeddingConfiguration_ShardingStrategy_MOD;
constexpr int TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_ARRAYSIZE = TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TPUEmbeddingConfiguration_ShardingStrategy_descriptor();
template<typename T>
inline const std::string& TPUEmbeddingConfiguration_ShardingStrategy_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TPUEmbeddingConfiguration_ShardingStrategy>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TPUEmbeddingConfiguration_ShardingStrategy_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TPUEmbeddingConfiguration_ShardingStrategy_descriptor(), enum_t_value);
}
inline bool TPUEmbeddingConfiguration_ShardingStrategy_Parse(
    const std::string& name, TPUEmbeddingConfiguration_ShardingStrategy* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TPUEmbeddingConfiguration_ShardingStrategy>(
    TPUEmbeddingConfiguration_ShardingStrategy_descriptor(), name, value);
}
// ===================================================================

class TPUEmbeddingConfiguration_TableDescriptor :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor) */ {
 public:
  TPUEmbeddingConfiguration_TableDescriptor();
  virtual ~TPUEmbeddingConfiguration_TableDescriptor();

  TPUEmbeddingConfiguration_TableDescriptor(const TPUEmbeddingConfiguration_TableDescriptor& from);
  TPUEmbeddingConfiguration_TableDescriptor(TPUEmbeddingConfiguration_TableDescriptor&& from) noexcept
    : TPUEmbeddingConfiguration_TableDescriptor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingConfiguration_TableDescriptor& operator=(const TPUEmbeddingConfiguration_TableDescriptor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingConfiguration_TableDescriptor& operator=(TPUEmbeddingConfiguration_TableDescriptor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUEmbeddingConfiguration_TableDescriptor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingConfiguration_TableDescriptor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingConfiguration_TableDescriptor*>(
               &_TPUEmbeddingConfiguration_TableDescriptor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TPUEmbeddingConfiguration_TableDescriptor& a, TPUEmbeddingConfiguration_TableDescriptor& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingConfiguration_TableDescriptor* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingConfiguration_TableDescriptor* New() const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration_TableDescriptor>(nullptr);
  }

  TPUEmbeddingConfiguration_TableDescriptor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration_TableDescriptor>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUEmbeddingConfiguration_TableDescriptor& from);
  void MergeFrom(const TPUEmbeddingConfiguration_TableDescriptor& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingConfiguration_TableDescriptor* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kOptimizationParametersFieldNumber = 5,
    kVocabularySizeFieldNumber = 2,
    kDimensionFieldNumber = 3,
    kNumFeaturesFieldNumber = 4,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // .tensorflow.tpu.OptimizationParameters optimization_parameters = 5;
  bool has_optimization_parameters() const;
  void clear_optimization_parameters();
  const ::tensorflow::tpu::OptimizationParameters& optimization_parameters() const;
  ::tensorflow::tpu::OptimizationParameters* release_optimization_parameters();
  ::tensorflow::tpu::OptimizationParameters* mutable_optimization_parameters();
  void set_allocated_optimization_parameters(::tensorflow::tpu::OptimizationParameters* optimization_parameters);

  // int64 vocabulary_size = 2;
  void clear_vocabulary_size();
  ::PROTOBUF_NAMESPACE_ID::int64 vocabulary_size() const;
  void set_vocabulary_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int32 dimension = 3;
  void clear_dimension();
  ::PROTOBUF_NAMESPACE_ID::int32 dimension() const;
  void set_dimension(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 num_features = 4;
  void clear_num_features();
  ::PROTOBUF_NAMESPACE_ID::int32 num_features() const;
  void set_num_features(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::tpu::OptimizationParameters* optimization_parameters_;
  ::PROTOBUF_NAMESPACE_ID::int64 vocabulary_size_;
  ::PROTOBUF_NAMESPACE_ID::int32 dimension_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_features_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class TPUEmbeddingConfiguration :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingConfiguration) */ {
 public:
  TPUEmbeddingConfiguration();
  virtual ~TPUEmbeddingConfiguration();

  TPUEmbeddingConfiguration(const TPUEmbeddingConfiguration& from);
  TPUEmbeddingConfiguration(TPUEmbeddingConfiguration&& from) noexcept
    : TPUEmbeddingConfiguration() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingConfiguration& operator=(const TPUEmbeddingConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingConfiguration& operator=(TPUEmbeddingConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUEmbeddingConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingConfiguration* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingConfiguration*>(
               &_TPUEmbeddingConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TPUEmbeddingConfiguration& a, TPUEmbeddingConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingConfiguration* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingConfiguration* New() const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration>(nullptr);
  }

  TPUEmbeddingConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUEmbeddingConfiguration& from);
  void MergeFrom(const TPUEmbeddingConfiguration& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingConfiguration* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingConfiguration";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TPUEmbeddingConfiguration_TableDescriptor TableDescriptor;

  typedef TPUEmbeddingConfiguration_Mode Mode;
  static constexpr Mode UNSPECIFIED =
    TPUEmbeddingConfiguration_Mode_UNSPECIFIED;
  static constexpr Mode INFERENCE =
    TPUEmbeddingConfiguration_Mode_INFERENCE;
  static constexpr Mode TRAINING =
    TPUEmbeddingConfiguration_Mode_TRAINING;
  static constexpr Mode BACKWARD_PASS_ONLY =
    TPUEmbeddingConfiguration_Mode_BACKWARD_PASS_ONLY;
  static inline bool Mode_IsValid(int value) {
    return TPUEmbeddingConfiguration_Mode_IsValid(value);
  }
  static constexpr Mode Mode_MIN =
    TPUEmbeddingConfiguration_Mode_Mode_MIN;
  static constexpr Mode Mode_MAX =
    TPUEmbeddingConfiguration_Mode_Mode_MAX;
  static constexpr int Mode_ARRAYSIZE =
    TPUEmbeddingConfiguration_Mode_Mode_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Mode_descriptor() {
    return TPUEmbeddingConfiguration_Mode_descriptor();
  }
  template<typename T>
  static inline const std::string& Mode_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Mode>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Mode_Name.");
    return TPUEmbeddingConfiguration_Mode_Name(enum_t_value);
  }
  static inline bool Mode_Parse(const std::string& name,
      Mode* value) {
    return TPUEmbeddingConfiguration_Mode_Parse(name, value);
  }

  typedef TPUEmbeddingConfiguration_ShardingStrategy ShardingStrategy;
  static constexpr ShardingStrategy DIV_DEFAULT =
    TPUEmbeddingConfiguration_ShardingStrategy_DIV_DEFAULT;
  static constexpr ShardingStrategy MOD =
    TPUEmbeddingConfiguration_ShardingStrategy_MOD;
  static inline bool ShardingStrategy_IsValid(int value) {
    return TPUEmbeddingConfiguration_ShardingStrategy_IsValid(value);
  }
  static constexpr ShardingStrategy ShardingStrategy_MIN =
    TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MIN;
  static constexpr ShardingStrategy ShardingStrategy_MAX =
    TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MAX;
  static constexpr int ShardingStrategy_ARRAYSIZE =
    TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ShardingStrategy_descriptor() {
    return TPUEmbeddingConfiguration_ShardingStrategy_descriptor();
  }
  template<typename T>
  static inline const std::string& ShardingStrategy_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ShardingStrategy>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ShardingStrategy_Name.");
    return TPUEmbeddingConfiguration_ShardingStrategy_Name(enum_t_value);
  }
  static inline bool ShardingStrategy_Parse(const std::string& name,
      ShardingStrategy* value) {
    return TPUEmbeddingConfiguration_ShardingStrategy_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTableDescriptorFieldNumber = 1,
    kProfileDataDirectoryFieldNumber = 9,
    kOutputLayoutFieldNumber = 8,
    kModeFieldNumber = 2,
    kBatchSizePerTensorCoreFieldNumber = 3,
    kNumHostsFieldNumber = 4,
    kNumTensorCoresFieldNumber = 5,
    kShardingStrategyFieldNumber = 6,
    kPipelineExecutionWithTensorCoreFieldNumber = 7,
  };
  // repeated .tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor table_descriptor = 1;
  int table_descriptor_size() const;
  void clear_table_descriptor();
  ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* mutable_table_descriptor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >*
      mutable_table_descriptor();
  const ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor& table_descriptor(int index) const;
  ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* add_table_descriptor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >&
      table_descriptor() const;

  // string profile_data_directory = 9;
  void clear_profile_data_directory();
  const std::string& profile_data_directory() const;
  void set_profile_data_directory(const std::string& value);
  void set_profile_data_directory(std::string&& value);
  void set_profile_data_directory(const char* value);
  void set_profile_data_directory(const char* value, size_t size);
  std::string* mutable_profile_data_directory();
  std::string* release_profile_data_directory();
  void set_allocated_profile_data_directory(std::string* profile_data_directory);

  // .tensorflow.tpu.TPUEmbeddingOutputLayout output_layout = 8 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_output_layout() const;
  PROTOBUF_DEPRECATED void clear_output_layout();
  PROTOBUF_DEPRECATED const ::tensorflow::tpu::TPUEmbeddingOutputLayout& output_layout() const;
  PROTOBUF_DEPRECATED ::tensorflow::tpu::TPUEmbeddingOutputLayout* release_output_layout();
  PROTOBUF_DEPRECATED ::tensorflow::tpu::TPUEmbeddingOutputLayout* mutable_output_layout();
  PROTOBUF_DEPRECATED void set_allocated_output_layout(::tensorflow::tpu::TPUEmbeddingOutputLayout* output_layout);

  // .tensorflow.tpu.TPUEmbeddingConfiguration.Mode mode = 2;
  void clear_mode();
  ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode mode() const;
  void set_mode(::tensorflow::tpu::TPUEmbeddingConfiguration_Mode value);

  // int32 batch_size_per_tensor_core = 3;
  void clear_batch_size_per_tensor_core();
  ::PROTOBUF_NAMESPACE_ID::int32 batch_size_per_tensor_core() const;
  void set_batch_size_per_tensor_core(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 num_hosts = 4;
  void clear_num_hosts();
  ::PROTOBUF_NAMESPACE_ID::int32 num_hosts() const;
  void set_num_hosts(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 num_tensor_cores = 5;
  void clear_num_tensor_cores();
  ::PROTOBUF_NAMESPACE_ID::int32 num_tensor_cores() const;
  void set_num_tensor_cores(::PROTOBUF_NAMESPACE_ID::int32 value);

  // .tensorflow.tpu.TPUEmbeddingConfiguration.ShardingStrategy sharding_strategy = 6;
  void clear_sharding_strategy();
  ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy sharding_strategy() const;
  void set_sharding_strategy(::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy value);

  // bool pipeline_execution_with_tensor_core = 7;
  void clear_pipeline_execution_with_tensor_core();
  bool pipeline_execution_with_tensor_core() const;
  void set_pipeline_execution_with_tensor_core(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingConfiguration)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor > table_descriptor_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr profile_data_directory_;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout* output_layout_;
  int mode_;
  ::PROTOBUF_NAMESPACE_ID::int32 batch_size_per_tensor_core_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_hosts_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_tensor_cores_;
  int sharding_strategy_;
  bool pipeline_execution_with_tensor_core_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TPUEmbeddingConfiguration_TableDescriptor

// string name = 1;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& TPUEmbeddingConfiguration_TableDescriptor::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
  return name_.GetNoArena();
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}
inline std::string* TPUEmbeddingConfiguration_TableDescriptor::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TPUEmbeddingConfiguration_TableDescriptor::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}

// int64 vocabulary_size = 2;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_vocabulary_size() {
  vocabulary_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TPUEmbeddingConfiguration_TableDescriptor::vocabulary_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.vocabulary_size)
  return vocabulary_size_;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_vocabulary_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  vocabulary_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.vocabulary_size)
}

// int32 dimension = 3;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_dimension() {
  dimension_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUEmbeddingConfiguration_TableDescriptor::dimension() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.dimension)
  return dimension_;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_dimension(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  dimension_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.dimension)
}

// int32 num_features = 4;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_num_features() {
  num_features_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUEmbeddingConfiguration_TableDescriptor::num_features() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.num_features)
  return num_features_;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_num_features(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_features_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.num_features)
}

// .tensorflow.tpu.OptimizationParameters optimization_parameters = 5;
inline bool TPUEmbeddingConfiguration_TableDescriptor::has_optimization_parameters() const {
  return this != internal_default_instance() && optimization_parameters_ != nullptr;
}
inline const ::tensorflow::tpu::OptimizationParameters& TPUEmbeddingConfiguration_TableDescriptor::optimization_parameters() const {
  const ::tensorflow::tpu::OptimizationParameters* p = optimization_parameters_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tpu::OptimizationParameters*>(
      &::tensorflow::tpu::_OptimizationParameters_default_instance_);
}
inline ::tensorflow::tpu::OptimizationParameters* TPUEmbeddingConfiguration_TableDescriptor::release_optimization_parameters() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
  
  ::tensorflow::tpu::OptimizationParameters* temp = optimization_parameters_;
  optimization_parameters_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::OptimizationParameters* TPUEmbeddingConfiguration_TableDescriptor::mutable_optimization_parameters() {
  
  if (optimization_parameters_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::OptimizationParameters>(GetArenaNoVirtual());
    optimization_parameters_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
  return optimization_parameters_;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_allocated_optimization_parameters(::tensorflow::tpu::OptimizationParameters* optimization_parameters) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(optimization_parameters_);
  }
  if (optimization_parameters) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      optimization_parameters = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optimization_parameters, submessage_arena);
    }
    
  } else {
    
  }
  optimization_parameters_ = optimization_parameters;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
}

// -------------------------------------------------------------------

// TPUEmbeddingConfiguration

// repeated .tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor table_descriptor = 1;
inline int TPUEmbeddingConfiguration::table_descriptor_size() const {
  return table_descriptor_.size();
}
inline void TPUEmbeddingConfiguration::clear_table_descriptor() {
  table_descriptor_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* TPUEmbeddingConfiguration::mutable_table_descriptor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return table_descriptor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >*
TPUEmbeddingConfiguration::mutable_table_descriptor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return &table_descriptor_;
}
inline const ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor& TPUEmbeddingConfiguration::table_descriptor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return table_descriptor_.Get(index);
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* TPUEmbeddingConfiguration::add_table_descriptor() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return table_descriptor_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >&
TPUEmbeddingConfiguration::table_descriptor() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return table_descriptor_;
}

// .tensorflow.tpu.TPUEmbeddingConfiguration.Mode mode = 2;
inline void TPUEmbeddingConfiguration::clear_mode() {
  mode_ = 0;
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration::mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.mode)
  return static_cast< ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode >(mode_);
}
inline void TPUEmbeddingConfiguration::set_mode(::tensorflow::tpu::TPUEmbeddingConfiguration_Mode value) {
  
  mode_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.mode)
}

// int32 batch_size_per_tensor_core = 3;
inline void TPUEmbeddingConfiguration::clear_batch_size_per_tensor_core() {
  batch_size_per_tensor_core_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUEmbeddingConfiguration::batch_size_per_tensor_core() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.batch_size_per_tensor_core)
  return batch_size_per_tensor_core_;
}
inline void TPUEmbeddingConfiguration::set_batch_size_per_tensor_core(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  batch_size_per_tensor_core_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.batch_size_per_tensor_core)
}

// int32 num_hosts = 4;
inline void TPUEmbeddingConfiguration::clear_num_hosts() {
  num_hosts_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUEmbeddingConfiguration::num_hosts() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.num_hosts)
  return num_hosts_;
}
inline void TPUEmbeddingConfiguration::set_num_hosts(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_hosts_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.num_hosts)
}

// int32 num_tensor_cores = 5;
inline void TPUEmbeddingConfiguration::clear_num_tensor_cores() {
  num_tensor_cores_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUEmbeddingConfiguration::num_tensor_cores() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.num_tensor_cores)
  return num_tensor_cores_;
}
inline void TPUEmbeddingConfiguration::set_num_tensor_cores(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_tensor_cores_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.num_tensor_cores)
}

// .tensorflow.tpu.TPUEmbeddingConfiguration.ShardingStrategy sharding_strategy = 6;
inline void TPUEmbeddingConfiguration::clear_sharding_strategy() {
  sharding_strategy_ = 0;
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration::sharding_strategy() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.sharding_strategy)
  return static_cast< ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy >(sharding_strategy_);
}
inline void TPUEmbeddingConfiguration::set_sharding_strategy(::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy value) {
  
  sharding_strategy_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.sharding_strategy)
}

// bool pipeline_execution_with_tensor_core = 7;
inline void TPUEmbeddingConfiguration::clear_pipeline_execution_with_tensor_core() {
  pipeline_execution_with_tensor_core_ = false;
}
inline bool TPUEmbeddingConfiguration::pipeline_execution_with_tensor_core() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.pipeline_execution_with_tensor_core)
  return pipeline_execution_with_tensor_core_;
}
inline void TPUEmbeddingConfiguration::set_pipeline_execution_with_tensor_core(bool value) {
  
  pipeline_execution_with_tensor_core_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.pipeline_execution_with_tensor_core)
}

// string profile_data_directory = 9;
inline void TPUEmbeddingConfiguration::clear_profile_data_directory() {
  profile_data_directory_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& TPUEmbeddingConfiguration::profile_data_directory() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
  return profile_data_directory_.GetNoArena();
}
inline void TPUEmbeddingConfiguration::set_profile_data_directory(const std::string& value) {
  
  profile_data_directory_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
}
inline void TPUEmbeddingConfiguration::set_profile_data_directory(std::string&& value) {
  
  profile_data_directory_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
}
inline void TPUEmbeddingConfiguration::set_profile_data_directory(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  profile_data_directory_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
}
inline void TPUEmbeddingConfiguration::set_profile_data_directory(const char* value, size_t size) {
  
  profile_data_directory_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
}
inline std::string* TPUEmbeddingConfiguration::mutable_profile_data_directory() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
  return profile_data_directory_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TPUEmbeddingConfiguration::release_profile_data_directory() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
  
  return profile_data_directory_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TPUEmbeddingConfiguration::set_allocated_profile_data_directory(std::string* profile_data_directory) {
  if (profile_data_directory != nullptr) {
    
  } else {
    
  }
  profile_data_directory_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), profile_data_directory);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
}

// .tensorflow.tpu.TPUEmbeddingOutputLayout output_layout = 8 [deprecated = true];
inline bool TPUEmbeddingConfiguration::has_output_layout() const {
  return this != internal_default_instance() && output_layout_ != nullptr;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout& TPUEmbeddingConfiguration::output_layout() const {
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout* p = output_layout_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.output_layout)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tpu::TPUEmbeddingOutputLayout*>(
      &::tensorflow::tpu::_TPUEmbeddingOutputLayout_default_instance_);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout* TPUEmbeddingConfiguration::release_output_layout() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.output_layout)
  
  ::tensorflow::tpu::TPUEmbeddingOutputLayout* temp = output_layout_;
  output_layout_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout* TPUEmbeddingConfiguration::mutable_output_layout() {
  
  if (output_layout_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout>(GetArenaNoVirtual());
    output_layout_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.output_layout)
  return output_layout_;
}
inline void TPUEmbeddingConfiguration::set_allocated_output_layout(::tensorflow::tpu::TPUEmbeddingOutputLayout* output_layout) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(output_layout_);
  }
  if (output_layout) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      output_layout = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, output_layout, submessage_arena);
    }
    
  } else {
    
  }
  output_layout_ = output_layout;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.output_layout)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode>() {
  return ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy>() {
  return ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto
