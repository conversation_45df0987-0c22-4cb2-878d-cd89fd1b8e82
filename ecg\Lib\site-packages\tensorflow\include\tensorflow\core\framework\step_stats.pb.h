// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/step_stats.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/allocation_description.pb.h"
#include "tensorflow/core/framework/tensor_description.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[8]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
namespace tensorflow {
class AllocationRecord;
class AllocationRecordDefaultTypeInternal;
extern AllocationRecordDefaultTypeInternal _AllocationRecord_default_instance_;
class AllocatorMemoryUsed;
class AllocatorMemoryUsedDefaultTypeInternal;
extern AllocatorMemoryUsedDefaultTypeInternal _AllocatorMemoryUsed_default_instance_;
class DeviceStepStats;
class DeviceStepStatsDefaultTypeInternal;
extern DeviceStepStatsDefaultTypeInternal _DeviceStepStats_default_instance_;
class DeviceStepStats_ThreadNamesEntry_DoNotUse;
class DeviceStepStats_ThreadNamesEntry_DoNotUseDefaultTypeInternal;
extern DeviceStepStats_ThreadNamesEntry_DoNotUseDefaultTypeInternal _DeviceStepStats_ThreadNamesEntry_DoNotUse_default_instance_;
class MemoryStats;
class MemoryStatsDefaultTypeInternal;
extern MemoryStatsDefaultTypeInternal _MemoryStats_default_instance_;
class NodeExecStats;
class NodeExecStatsDefaultTypeInternal;
extern NodeExecStatsDefaultTypeInternal _NodeExecStats_default_instance_;
class NodeOutput;
class NodeOutputDefaultTypeInternal;
extern NodeOutputDefaultTypeInternal _NodeOutput_default_instance_;
class StepStats;
class StepStatsDefaultTypeInternal;
extern StepStatsDefaultTypeInternal _StepStats_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::AllocationRecord* Arena::CreateMaybeMessage<::tensorflow::AllocationRecord>(Arena*);
template<> ::tensorflow::AllocatorMemoryUsed* Arena::CreateMaybeMessage<::tensorflow::AllocatorMemoryUsed>(Arena*);
template<> ::tensorflow::DeviceStepStats* Arena::CreateMaybeMessage<::tensorflow::DeviceStepStats>(Arena*);
template<> ::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::MemoryStats* Arena::CreateMaybeMessage<::tensorflow::MemoryStats>(Arena*);
template<> ::tensorflow::NodeExecStats* Arena::CreateMaybeMessage<::tensorflow::NodeExecStats>(Arena*);
template<> ::tensorflow::NodeOutput* Arena::CreateMaybeMessage<::tensorflow::NodeOutput>(Arena*);
template<> ::tensorflow::StepStats* Arena::CreateMaybeMessage<::tensorflow::StepStats>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class AllocationRecord :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AllocationRecord) */ {
 public:
  AllocationRecord();
  virtual ~AllocationRecord();

  AllocationRecord(const AllocationRecord& from);
  AllocationRecord(AllocationRecord&& from) noexcept
    : AllocationRecord() {
    *this = ::std::move(from);
  }

  inline AllocationRecord& operator=(const AllocationRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline AllocationRecord& operator=(AllocationRecord&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AllocationRecord& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AllocationRecord* internal_default_instance() {
    return reinterpret_cast<const AllocationRecord*>(
               &_AllocationRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AllocationRecord& a, AllocationRecord& b) {
    a.Swap(&b);
  }
  inline void Swap(AllocationRecord* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AllocationRecord* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AllocationRecord* New() const final {
    return CreateMaybeMessage<AllocationRecord>(nullptr);
  }

  AllocationRecord* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AllocationRecord>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AllocationRecord& from);
  void MergeFrom(const AllocationRecord& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AllocationRecord* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AllocationRecord";
  }
  protected:
  explicit AllocationRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAllocMicrosFieldNumber = 1,
    kAllocBytesFieldNumber = 2,
  };
  // int64 alloc_micros = 1;
  void clear_alloc_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 alloc_micros() const;
  void set_alloc_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 alloc_bytes = 2;
  void clear_alloc_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 alloc_bytes() const;
  void set_alloc_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.AllocationRecord)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 alloc_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 alloc_bytes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class AllocatorMemoryUsed :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AllocatorMemoryUsed) */ {
 public:
  AllocatorMemoryUsed();
  virtual ~AllocatorMemoryUsed();

  AllocatorMemoryUsed(const AllocatorMemoryUsed& from);
  AllocatorMemoryUsed(AllocatorMemoryUsed&& from) noexcept
    : AllocatorMemoryUsed() {
    *this = ::std::move(from);
  }

  inline AllocatorMemoryUsed& operator=(const AllocatorMemoryUsed& from) {
    CopyFrom(from);
    return *this;
  }
  inline AllocatorMemoryUsed& operator=(AllocatorMemoryUsed&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AllocatorMemoryUsed& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AllocatorMemoryUsed* internal_default_instance() {
    return reinterpret_cast<const AllocatorMemoryUsed*>(
               &_AllocatorMemoryUsed_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(AllocatorMemoryUsed& a, AllocatorMemoryUsed& b) {
    a.Swap(&b);
  }
  inline void Swap(AllocatorMemoryUsed* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AllocatorMemoryUsed* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AllocatorMemoryUsed* New() const final {
    return CreateMaybeMessage<AllocatorMemoryUsed>(nullptr);
  }

  AllocatorMemoryUsed* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AllocatorMemoryUsed>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AllocatorMemoryUsed& from);
  void MergeFrom(const AllocatorMemoryUsed& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AllocatorMemoryUsed* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AllocatorMemoryUsed";
  }
  protected:
  explicit AllocatorMemoryUsed(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAllocationRecordsFieldNumber = 6,
    kAllocatorNameFieldNumber = 1,
    kTotalBytesFieldNumber = 2,
    kPeakBytesFieldNumber = 3,
    kLiveBytesFieldNumber = 4,
    kAllocatorBytesInUseFieldNumber = 5,
  };
  // repeated .tensorflow.AllocationRecord allocation_records = 6;
  int allocation_records_size() const;
  void clear_allocation_records();
  ::tensorflow::AllocationRecord* mutable_allocation_records(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >*
      mutable_allocation_records();
  const ::tensorflow::AllocationRecord& allocation_records(int index) const;
  ::tensorflow::AllocationRecord* add_allocation_records();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >&
      allocation_records() const;

  // string allocator_name = 1;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  void set_allocator_name(const std::string& value);
  void set_allocator_name(std::string&& value);
  void set_allocator_name(const char* value);
  void set_allocator_name(const char* value, size_t size);
  std::string* mutable_allocator_name();
  std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_allocator_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_allocator_name(
      std::string* allocator_name);

  // int64 total_bytes = 2;
  void clear_total_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 total_bytes() const;
  void set_total_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 peak_bytes = 3;
  void clear_peak_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 peak_bytes() const;
  void set_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 live_bytes = 4;
  void clear_live_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 live_bytes() const;
  void set_live_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 allocator_bytes_in_use = 5;
  void clear_allocator_bytes_in_use();
  ::PROTOBUF_NAMESPACE_ID::int64 allocator_bytes_in_use() const;
  void set_allocator_bytes_in_use(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.AllocatorMemoryUsed)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord > allocation_records_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 total_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 peak_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 live_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 allocator_bytes_in_use_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class NodeOutput :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NodeOutput) */ {
 public:
  NodeOutput();
  virtual ~NodeOutput();

  NodeOutput(const NodeOutput& from);
  NodeOutput(NodeOutput&& from) noexcept
    : NodeOutput() {
    *this = ::std::move(from);
  }

  inline NodeOutput& operator=(const NodeOutput& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeOutput& operator=(NodeOutput&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NodeOutput& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NodeOutput* internal_default_instance() {
    return reinterpret_cast<const NodeOutput*>(
               &_NodeOutput_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(NodeOutput& a, NodeOutput& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeOutput* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeOutput* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NodeOutput* New() const final {
    return CreateMaybeMessage<NodeOutput>(nullptr);
  }

  NodeOutput* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NodeOutput>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NodeOutput& from);
  void MergeFrom(const NodeOutput& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeOutput* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NodeOutput";
  }
  protected:
  explicit NodeOutput(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorDescriptionFieldNumber = 3,
    kSlotFieldNumber = 1,
  };
  // .tensorflow.TensorDescription tensor_description = 3;
  bool has_tensor_description() const;
  void clear_tensor_description();
  const ::tensorflow::TensorDescription& tensor_description() const;
  ::tensorflow::TensorDescription* release_tensor_description();
  ::tensorflow::TensorDescription* mutable_tensor_description();
  void set_allocated_tensor_description(::tensorflow::TensorDescription* tensor_description);
  void unsafe_arena_set_allocated_tensor_description(
      ::tensorflow::TensorDescription* tensor_description);
  ::tensorflow::TensorDescription* unsafe_arena_release_tensor_description();

  // int32 slot = 1;
  void clear_slot();
  ::PROTOBUF_NAMESPACE_ID::int32 slot() const;
  void set_slot(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.NodeOutput)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorDescription* tensor_description_;
  ::PROTOBUF_NAMESPACE_ID::int32 slot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class MemoryStats :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryStats) */ {
 public:
  MemoryStats();
  virtual ~MemoryStats();

  MemoryStats(const MemoryStats& from);
  MemoryStats(MemoryStats&& from) noexcept
    : MemoryStats() {
    *this = ::std::move(from);
  }

  inline MemoryStats& operator=(const MemoryStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryStats& operator=(MemoryStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MemoryStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MemoryStats* internal_default_instance() {
    return reinterpret_cast<const MemoryStats*>(
               &_MemoryStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MemoryStats& a, MemoryStats& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryStats* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MemoryStats* New() const final {
    return CreateMaybeMessage<MemoryStats>(nullptr);
  }

  MemoryStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MemoryStats>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MemoryStats& from);
  void MergeFrom(const MemoryStats& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryStats* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryStats";
  }
  protected:
  explicit MemoryStats(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPersistentTensorAllocIdsFieldNumber = 5,
    kDevicePersistentTensorAllocIdsFieldNumber = 6,
    kTempMemorySizeFieldNumber = 1,
    kDeviceTempMemorySizeFieldNumber = 2,
    kPersistentMemorySizeFieldNumber = 3,
    kDevicePersistentMemorySizeFieldNumber = 4,
  };
  // repeated int64 persistent_tensor_alloc_ids = 5;
  int persistent_tensor_alloc_ids_size() const;
  void clear_persistent_tensor_alloc_ids();
  ::PROTOBUF_NAMESPACE_ID::int64 persistent_tensor_alloc_ids(int index) const;
  void set_persistent_tensor_alloc_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_persistent_tensor_alloc_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      persistent_tensor_alloc_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_persistent_tensor_alloc_ids();

  // repeated int64 device_persistent_tensor_alloc_ids = 6 [deprecated = true];
  PROTOBUF_DEPRECATED int device_persistent_tensor_alloc_ids_size() const;
  PROTOBUF_DEPRECATED void clear_device_persistent_tensor_alloc_ids();
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int64 device_persistent_tensor_alloc_ids(int index) const;
  PROTOBUF_DEPRECATED void set_device_persistent_tensor_alloc_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  PROTOBUF_DEPRECATED void add_device_persistent_tensor_alloc_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      device_persistent_tensor_alloc_ids() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_device_persistent_tensor_alloc_ids();

  // int64 temp_memory_size = 1;
  void clear_temp_memory_size();
  ::PROTOBUF_NAMESPACE_ID::int64 temp_memory_size() const;
  void set_temp_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 device_temp_memory_size = 2 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_temp_memory_size();
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int64 device_temp_memory_size() const;
  PROTOBUF_DEPRECATED void set_device_temp_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 persistent_memory_size = 3;
  void clear_persistent_memory_size();
  ::PROTOBUF_NAMESPACE_ID::int64 persistent_memory_size() const;
  void set_persistent_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 device_persistent_memory_size = 4 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_persistent_memory_size();
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int64 device_persistent_memory_size() const;
  PROTOBUF_DEPRECATED void set_device_persistent_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryStats)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > persistent_tensor_alloc_ids_;
  mutable std::atomic<int> _persistent_tensor_alloc_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > device_persistent_tensor_alloc_ids_;
  mutable std::atomic<int> _device_persistent_tensor_alloc_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 temp_memory_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 device_temp_memory_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 persistent_memory_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 device_persistent_memory_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class NodeExecStats :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NodeExecStats) */ {
 public:
  NodeExecStats();
  virtual ~NodeExecStats();

  NodeExecStats(const NodeExecStats& from);
  NodeExecStats(NodeExecStats&& from) noexcept
    : NodeExecStats() {
    *this = ::std::move(from);
  }

  inline NodeExecStats& operator=(const NodeExecStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeExecStats& operator=(NodeExecStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NodeExecStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NodeExecStats* internal_default_instance() {
    return reinterpret_cast<const NodeExecStats*>(
               &_NodeExecStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(NodeExecStats& a, NodeExecStats& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeExecStats* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeExecStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NodeExecStats* New() const final {
    return CreateMaybeMessage<NodeExecStats>(nullptr);
  }

  NodeExecStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NodeExecStats>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NodeExecStats& from);
  void MergeFrom(const NodeExecStats& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeExecStats* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NodeExecStats";
  }
  protected:
  explicit NodeExecStats(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMemoryFieldNumber = 6,
    kOutputFieldNumber = 7,
    kReferencedTensorFieldNumber = 11,
    kNodeNameFieldNumber = 1,
    kTimelineLabelFieldNumber = 8,
    kMemoryStatsFieldNumber = 12,
    kAllStartMicrosFieldNumber = 2,
    kOpStartRelMicrosFieldNumber = 3,
    kOpEndRelMicrosFieldNumber = 4,
    kAllEndRelMicrosFieldNumber = 5,
    kScheduledMicrosFieldNumber = 9,
    kAllStartNanosFieldNumber = 13,
    kOpStartRelNanosFieldNumber = 14,
    kOpEndRelNanosFieldNumber = 15,
    kAllEndRelNanosFieldNumber = 16,
    kScheduledNanosFieldNumber = 17,
    kThreadIdFieldNumber = 10,
  };
  // repeated .tensorflow.AllocatorMemoryUsed memory = 6;
  int memory_size() const;
  void clear_memory();
  ::tensorflow::AllocatorMemoryUsed* mutable_memory(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >*
      mutable_memory();
  const ::tensorflow::AllocatorMemoryUsed& memory(int index) const;
  ::tensorflow::AllocatorMemoryUsed* add_memory();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >&
      memory() const;

  // repeated .tensorflow.NodeOutput output = 7;
  int output_size() const;
  void clear_output();
  ::tensorflow::NodeOutput* mutable_output(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeOutput >*
      mutable_output();
  const ::tensorflow::NodeOutput& output(int index) const;
  ::tensorflow::NodeOutput* add_output();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeOutput >&
      output() const;

  // repeated .tensorflow.AllocationDescription referenced_tensor = 11;
  int referenced_tensor_size() const;
  void clear_referenced_tensor();
  ::tensorflow::AllocationDescription* mutable_referenced_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationDescription >*
      mutable_referenced_tensor();
  const ::tensorflow::AllocationDescription& referenced_tensor(int index) const;
  ::tensorflow::AllocationDescription* add_referenced_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationDescription >&
      referenced_tensor() const;

  // string node_name = 1;
  void clear_node_name();
  const std::string& node_name() const;
  void set_node_name(const std::string& value);
  void set_node_name(std::string&& value);
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  std::string* mutable_node_name();
  std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_node_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node_name(
      std::string* node_name);

  // string timeline_label = 8;
  void clear_timeline_label();
  const std::string& timeline_label() const;
  void set_timeline_label(const std::string& value);
  void set_timeline_label(std::string&& value);
  void set_timeline_label(const char* value);
  void set_timeline_label(const char* value, size_t size);
  std::string* mutable_timeline_label();
  std::string* release_timeline_label();
  void set_allocated_timeline_label(std::string* timeline_label);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_timeline_label();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_timeline_label(
      std::string* timeline_label);

  // .tensorflow.MemoryStats memory_stats = 12;
  bool has_memory_stats() const;
  void clear_memory_stats();
  const ::tensorflow::MemoryStats& memory_stats() const;
  ::tensorflow::MemoryStats* release_memory_stats();
  ::tensorflow::MemoryStats* mutable_memory_stats();
  void set_allocated_memory_stats(::tensorflow::MemoryStats* memory_stats);
  void unsafe_arena_set_allocated_memory_stats(
      ::tensorflow::MemoryStats* memory_stats);
  ::tensorflow::MemoryStats* unsafe_arena_release_memory_stats();

  // int64 all_start_micros = 2;
  void clear_all_start_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 all_start_micros() const;
  void set_all_start_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 op_start_rel_micros = 3;
  void clear_op_start_rel_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 op_start_rel_micros() const;
  void set_op_start_rel_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 op_end_rel_micros = 4;
  void clear_op_end_rel_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 op_end_rel_micros() const;
  void set_op_end_rel_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 all_end_rel_micros = 5;
  void clear_all_end_rel_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 all_end_rel_micros() const;
  void set_all_end_rel_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 scheduled_micros = 9;
  void clear_scheduled_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 scheduled_micros() const;
  void set_scheduled_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 all_start_nanos = 13;
  void clear_all_start_nanos();
  ::PROTOBUF_NAMESPACE_ID::int64 all_start_nanos() const;
  void set_all_start_nanos(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 op_start_rel_nanos = 14;
  void clear_op_start_rel_nanos();
  ::PROTOBUF_NAMESPACE_ID::int64 op_start_rel_nanos() const;
  void set_op_start_rel_nanos(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 op_end_rel_nanos = 15;
  void clear_op_end_rel_nanos();
  ::PROTOBUF_NAMESPACE_ID::int64 op_end_rel_nanos() const;
  void set_op_end_rel_nanos(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 all_end_rel_nanos = 16;
  void clear_all_end_rel_nanos();
  ::PROTOBUF_NAMESPACE_ID::int64 all_end_rel_nanos() const;
  void set_all_end_rel_nanos(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 scheduled_nanos = 17;
  void clear_scheduled_nanos();
  ::PROTOBUF_NAMESPACE_ID::int64 scheduled_nanos() const;
  void set_scheduled_nanos(::PROTOBUF_NAMESPACE_ID::int64 value);

  // uint32 thread_id = 10;
  void clear_thread_id();
  ::PROTOBUF_NAMESPACE_ID::uint32 thread_id() const;
  void set_thread_id(::PROTOBUF_NAMESPACE_ID::uint32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.NodeExecStats)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed > memory_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeOutput > output_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationDescription > referenced_tensor_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr timeline_label_;
  ::tensorflow::MemoryStats* memory_stats_;
  ::PROTOBUF_NAMESPACE_ID::int64 all_start_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 op_start_rel_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 op_end_rel_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 all_end_rel_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 scheduled_micros_;
  ::PROTOBUF_NAMESPACE_ID::int64 all_start_nanos_;
  ::PROTOBUF_NAMESPACE_ID::int64 op_start_rel_nanos_;
  ::PROTOBUF_NAMESPACE_ID::int64 op_end_rel_nanos_;
  ::PROTOBUF_NAMESPACE_ID::int64 all_end_rel_nanos_;
  ::PROTOBUF_NAMESPACE_ID::int64 scheduled_nanos_;
  ::PROTOBUF_NAMESPACE_ID::uint32 thread_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class DeviceStepStats_ThreadNamesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceStepStats_ThreadNamesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::uint32, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceStepStats_ThreadNamesEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::uint32, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  DeviceStepStats_ThreadNamesEntry_DoNotUse();
  DeviceStepStats_ThreadNamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DeviceStepStats_ThreadNamesEntry_DoNotUse& other);
  static const DeviceStepStats_ThreadNamesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DeviceStepStats_ThreadNamesEntry_DoNotUse*>(&_DeviceStepStats_ThreadNamesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.DeviceStepStats.ThreadNamesEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto.file_level_metadata[5];
  }

  public:
};

// -------------------------------------------------------------------

class DeviceStepStats :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceStepStats) */ {
 public:
  DeviceStepStats();
  virtual ~DeviceStepStats();

  DeviceStepStats(const DeviceStepStats& from);
  DeviceStepStats(DeviceStepStats&& from) noexcept
    : DeviceStepStats() {
    *this = ::std::move(from);
  }

  inline DeviceStepStats& operator=(const DeviceStepStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceStepStats& operator=(DeviceStepStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeviceStepStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceStepStats* internal_default_instance() {
    return reinterpret_cast<const DeviceStepStats*>(
               &_DeviceStepStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DeviceStepStats& a, DeviceStepStats& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceStepStats* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceStepStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeviceStepStats* New() const final {
    return CreateMaybeMessage<DeviceStepStats>(nullptr);
  }

  DeviceStepStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeviceStepStats>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeviceStepStats& from);
  void MergeFrom(const DeviceStepStats& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceStepStats* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeviceStepStats";
  }
  protected:
  explicit DeviceStepStats(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kNodeStatsFieldNumber = 2,
    kThreadNamesFieldNumber = 3,
    kDeviceFieldNumber = 1,
  };
  // repeated .tensorflow.NodeExecStats node_stats = 2;
  int node_stats_size() const;
  void clear_node_stats();
  ::tensorflow::NodeExecStats* mutable_node_stats(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeExecStats >*
      mutable_node_stats();
  const ::tensorflow::NodeExecStats& node_stats(int index) const;
  ::tensorflow::NodeExecStats* add_node_stats();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeExecStats >&
      node_stats() const;

  // map<uint32, string> thread_names = 3;
  int thread_names_size() const;
  void clear_thread_names();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, std::string >&
      thread_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, std::string >*
      mutable_thread_names();

  // string device = 1;
  void clear_device();
  const std::string& device() const;
  void set_device(const std::string& value);
  void set_device(std::string&& value);
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  std::string* mutable_device();
  std::string* release_device();
  void set_allocated_device(std::string* device);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device(
      std::string* device);

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceStepStats)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeExecStats > node_stats_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      DeviceStepStats_ThreadNamesEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::uint32, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > thread_names_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class StepStats :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.StepStats) */ {
 public:
  StepStats();
  virtual ~StepStats();

  StepStats(const StepStats& from);
  StepStats(StepStats&& from) noexcept
    : StepStats() {
    *this = ::std::move(from);
  }

  inline StepStats& operator=(const StepStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline StepStats& operator=(StepStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StepStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StepStats* internal_default_instance() {
    return reinterpret_cast<const StepStats*>(
               &_StepStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(StepStats& a, StepStats& b) {
    a.Swap(&b);
  }
  inline void Swap(StepStats* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StepStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StepStats* New() const final {
    return CreateMaybeMessage<StepStats>(nullptr);
  }

  StepStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StepStats>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StepStats& from);
  void MergeFrom(const StepStats& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StepStats* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.StepStats";
  }
  protected:
  explicit StepStats(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDevStatsFieldNumber = 1,
  };
  // repeated .tensorflow.DeviceStepStats dev_stats = 1;
  int dev_stats_size() const;
  void clear_dev_stats();
  ::tensorflow::DeviceStepStats* mutable_dev_stats(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceStepStats >*
      mutable_dev_stats();
  const ::tensorflow::DeviceStepStats& dev_stats(int index) const;
  ::tensorflow::DeviceStepStats* add_dev_stats();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceStepStats >&
      dev_stats() const;

  // @@protoc_insertion_point(class_scope:tensorflow.StepStats)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceStepStats > dev_stats_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AllocationRecord

// int64 alloc_micros = 1;
inline void AllocationRecord::clear_alloc_micros() {
  alloc_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AllocationRecord::alloc_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationRecord.alloc_micros)
  return alloc_micros_;
}
inline void AllocationRecord::set_alloc_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  alloc_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocationRecord.alloc_micros)
}

// int64 alloc_bytes = 2;
inline void AllocationRecord::clear_alloc_bytes() {
  alloc_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AllocationRecord::alloc_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationRecord.alloc_bytes)
  return alloc_bytes_;
}
inline void AllocationRecord::set_alloc_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  alloc_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocationRecord.alloc_bytes)
}

// -------------------------------------------------------------------

// AllocatorMemoryUsed

// string allocator_name = 1;
inline void AllocatorMemoryUsed::clear_allocator_name() {
  allocator_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& AllocatorMemoryUsed::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.allocator_name)
  return allocator_name_.Get();
}
inline void AllocatorMemoryUsed::set_allocator_name(const std::string& value) {
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.allocator_name)
}
inline void AllocatorMemoryUsed::set_allocator_name(std::string&& value) {
  
  allocator_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AllocatorMemoryUsed.allocator_name)
}
inline void AllocatorMemoryUsed::set_allocator_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AllocatorMemoryUsed.allocator_name)
}
inline void AllocatorMemoryUsed::set_allocator_name(const char* value,
    size_t size) {
  
  allocator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AllocatorMemoryUsed.allocator_name)
}
inline std::string* AllocatorMemoryUsed::mutable_allocator_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AllocatorMemoryUsed.allocator_name)
  return allocator_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* AllocatorMemoryUsed::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.AllocatorMemoryUsed.allocator_name)
  
  return allocator_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void AllocatorMemoryUsed::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  allocator_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), allocator_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AllocatorMemoryUsed.allocator_name)
}
inline std::string* AllocatorMemoryUsed::unsafe_arena_release_allocator_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AllocatorMemoryUsed.allocator_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return allocator_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void AllocatorMemoryUsed::unsafe_arena_set_allocated_allocator_name(
    std::string* allocator_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  allocator_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      allocator_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AllocatorMemoryUsed.allocator_name)
}

// int64 total_bytes = 2;
inline void AllocatorMemoryUsed::clear_total_bytes() {
  total_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AllocatorMemoryUsed::total_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.total_bytes)
  return total_bytes_;
}
inline void AllocatorMemoryUsed::set_total_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  total_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.total_bytes)
}

// int64 peak_bytes = 3;
inline void AllocatorMemoryUsed::clear_peak_bytes() {
  peak_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AllocatorMemoryUsed::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.peak_bytes)
  return peak_bytes_;
}
inline void AllocatorMemoryUsed::set_peak_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.peak_bytes)
}

// int64 live_bytes = 4;
inline void AllocatorMemoryUsed::clear_live_bytes() {
  live_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AllocatorMemoryUsed::live_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.live_bytes)
  return live_bytes_;
}
inline void AllocatorMemoryUsed::set_live_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  live_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.live_bytes)
}

// repeated .tensorflow.AllocationRecord allocation_records = 6;
inline int AllocatorMemoryUsed::allocation_records_size() const {
  return allocation_records_.size();
}
inline void AllocatorMemoryUsed::clear_allocation_records() {
  allocation_records_.Clear();
}
inline ::tensorflow::AllocationRecord* AllocatorMemoryUsed::mutable_allocation_records(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AllocatorMemoryUsed.allocation_records)
  return allocation_records_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >*
AllocatorMemoryUsed::mutable_allocation_records() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AllocatorMemoryUsed.allocation_records)
  return &allocation_records_;
}
inline const ::tensorflow::AllocationRecord& AllocatorMemoryUsed::allocation_records(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.allocation_records)
  return allocation_records_.Get(index);
}
inline ::tensorflow::AllocationRecord* AllocatorMemoryUsed::add_allocation_records() {
  // @@protoc_insertion_point(field_add:tensorflow.AllocatorMemoryUsed.allocation_records)
  return allocation_records_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >&
AllocatorMemoryUsed::allocation_records() const {
  // @@protoc_insertion_point(field_list:tensorflow.AllocatorMemoryUsed.allocation_records)
  return allocation_records_;
}

// int64 allocator_bytes_in_use = 5;
inline void AllocatorMemoryUsed::clear_allocator_bytes_in_use() {
  allocator_bytes_in_use_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AllocatorMemoryUsed::allocator_bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.allocator_bytes_in_use)
  return allocator_bytes_in_use_;
}
inline void AllocatorMemoryUsed::set_allocator_bytes_in_use(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  allocator_bytes_in_use_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.allocator_bytes_in_use)
}

// -------------------------------------------------------------------

// NodeOutput

// int32 slot = 1;
inline void NodeOutput::clear_slot() {
  slot_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 NodeOutput::slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeOutput.slot)
  return slot_;
}
inline void NodeOutput::set_slot(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  slot_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeOutput.slot)
}

// .tensorflow.TensorDescription tensor_description = 3;
inline bool NodeOutput::has_tensor_description() const {
  return this != internal_default_instance() && tensor_description_ != nullptr;
}
inline const ::tensorflow::TensorDescription& NodeOutput::tensor_description() const {
  const ::tensorflow::TensorDescription* p = tensor_description_;
  // @@protoc_insertion_point(field_get:tensorflow.NodeOutput.tensor_description)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorDescription*>(
      &::tensorflow::_TensorDescription_default_instance_);
}
inline ::tensorflow::TensorDescription* NodeOutput::release_tensor_description() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeOutput.tensor_description)
  
  ::tensorflow::TensorDescription* temp = tensor_description_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  tensor_description_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorDescription* NodeOutput::unsafe_arena_release_tensor_description() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NodeOutput.tensor_description)
  
  ::tensorflow::TensorDescription* temp = tensor_description_;
  tensor_description_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorDescription* NodeOutput::mutable_tensor_description() {
  
  if (tensor_description_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorDescription>(GetArenaNoVirtual());
    tensor_description_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeOutput.tensor_description)
  return tensor_description_;
}
inline void NodeOutput::set_allocated_tensor_description(::tensorflow::TensorDescription* tensor_description) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_description_);
  }
  if (tensor_description) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_description)->GetArena();
    if (message_arena != submessage_arena) {
      tensor_description = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_description, submessage_arena);
    }
    
  } else {
    
  }
  tensor_description_ = tensor_description;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeOutput.tensor_description)
}

// -------------------------------------------------------------------

// MemoryStats

// int64 temp_memory_size = 1;
inline void MemoryStats::clear_temp_memory_size() {
  temp_memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryStats::temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.temp_memory_size)
  return temp_memory_size_;
}
inline void MemoryStats::set_temp_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  temp_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.temp_memory_size)
}

// int64 persistent_memory_size = 3;
inline void MemoryStats::clear_persistent_memory_size() {
  persistent_memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryStats::persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.persistent_memory_size)
  return persistent_memory_size_;
}
inline void MemoryStats::set_persistent_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  persistent_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.persistent_memory_size)
}

// repeated int64 persistent_tensor_alloc_ids = 5;
inline int MemoryStats::persistent_tensor_alloc_ids_size() const {
  return persistent_tensor_alloc_ids_.size();
}
inline void MemoryStats::clear_persistent_tensor_alloc_ids() {
  persistent_tensor_alloc_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryStats::persistent_tensor_alloc_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
  return persistent_tensor_alloc_ids_.Get(index);
}
inline void MemoryStats::set_persistent_tensor_alloc_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  persistent_tensor_alloc_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
}
inline void MemoryStats::add_persistent_tensor_alloc_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  persistent_tensor_alloc_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
MemoryStats::persistent_tensor_alloc_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
  return persistent_tensor_alloc_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
MemoryStats::mutable_persistent_tensor_alloc_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
  return &persistent_tensor_alloc_ids_;
}

// int64 device_temp_memory_size = 2 [deprecated = true];
inline void MemoryStats::clear_device_temp_memory_size() {
  device_temp_memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryStats::device_temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.device_temp_memory_size)
  return device_temp_memory_size_;
}
inline void MemoryStats::set_device_temp_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  device_temp_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.device_temp_memory_size)
}

// int64 device_persistent_memory_size = 4 [deprecated = true];
inline void MemoryStats::clear_device_persistent_memory_size() {
  device_persistent_memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryStats::device_persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.device_persistent_memory_size)
  return device_persistent_memory_size_;
}
inline void MemoryStats::set_device_persistent_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  device_persistent_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.device_persistent_memory_size)
}

// repeated int64 device_persistent_tensor_alloc_ids = 6 [deprecated = true];
inline int MemoryStats::device_persistent_tensor_alloc_ids_size() const {
  return device_persistent_tensor_alloc_ids_.size();
}
inline void MemoryStats::clear_device_persistent_tensor_alloc_ids() {
  device_persistent_tensor_alloc_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MemoryStats::device_persistent_tensor_alloc_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
  return device_persistent_tensor_alloc_ids_.Get(index);
}
inline void MemoryStats::set_device_persistent_tensor_alloc_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  device_persistent_tensor_alloc_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
}
inline void MemoryStats::add_device_persistent_tensor_alloc_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  device_persistent_tensor_alloc_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
MemoryStats::device_persistent_tensor_alloc_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
  return device_persistent_tensor_alloc_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
MemoryStats::mutable_device_persistent_tensor_alloc_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
  return &device_persistent_tensor_alloc_ids_;
}

// -------------------------------------------------------------------

// NodeExecStats

// string node_name = 1;
inline void NodeExecStats::clear_node_name() {
  node_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& NodeExecStats::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.node_name)
  return node_name_.Get();
}
inline void NodeExecStats::set_node_name(const std::string& value) {
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.node_name)
}
inline void NodeExecStats::set_node_name(std::string&& value) {
  
  node_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NodeExecStats.node_name)
}
inline void NodeExecStats::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.NodeExecStats.node_name)
}
inline void NodeExecStats::set_node_name(const char* value,
    size_t size) {
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NodeExecStats.node_name)
}
inline std::string* NodeExecStats::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.node_name)
  return node_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* NodeExecStats::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeExecStats.node_name)
  
  return node_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void NodeExecStats::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  node_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), node_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeExecStats.node_name)
}
inline std::string* NodeExecStats::unsafe_arena_release_node_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NodeExecStats.node_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return node_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void NodeExecStats::unsafe_arena_set_allocated_node_name(
    std::string* node_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (node_name != nullptr) {
    
  } else {
    
  }
  node_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      node_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeExecStats.node_name)
}

// int64 all_start_micros = 2;
inline void NodeExecStats::clear_all_start_micros() {
  all_start_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 NodeExecStats::all_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_start_micros)
  return all_start_micros_;
}
inline void NodeExecStats::set_all_start_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  all_start_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_start_micros)
}

// int64 op_start_rel_micros = 3;
inline void NodeExecStats::clear_op_start_rel_micros() {
  op_start_rel_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 NodeExecStats::op_start_rel_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_start_rel_micros)
  return op_start_rel_micros_;
}
inline void NodeExecStats::set_op_start_rel_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  op_start_rel_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_start_rel_micros)
}

// int64 op_end_rel_micros = 4;
inline void NodeExecStats::clear_op_end_rel_micros() {
  op_end_rel_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 NodeExecStats::op_end_rel_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_end_rel_micros)
  return op_end_rel_micros_;
}
inline void NodeExecStats::set_op_end_rel_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  op_end_rel_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_end_rel_micros)
}

// int64 all_end_rel_micros = 5;
inline void NodeExecStats::clear_all_end_rel_micros() {
  all_end_rel_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 NodeExecStats::all_end_rel_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_end_rel_micros)
  return all_end_rel_micros_;
}
inline void NodeExecStats::set_all_end_rel_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  all_end_rel_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_end_rel_micros)
}

// repeated .tensorflow.AllocatorMemoryUsed memory = 6;
inline int NodeExecStats::memory_size() const {
  return memory_.size();
}
inline void NodeExecStats::clear_memory() {
  memory_.Clear();
}
inline ::tensorflow::AllocatorMemoryUsed* NodeExecStats::mutable_memory(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.memory)
  return memory_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >*
NodeExecStats::mutable_memory() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeExecStats.memory)
  return &memory_;
}
inline const ::tensorflow::AllocatorMemoryUsed& NodeExecStats::memory(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.memory)
  return memory_.Get(index);
}
inline ::tensorflow::AllocatorMemoryUsed* NodeExecStats::add_memory() {
  // @@protoc_insertion_point(field_add:tensorflow.NodeExecStats.memory)
  return memory_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >&
NodeExecStats::memory() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeExecStats.memory)
  return memory_;
}

// repeated .tensorflow.NodeOutput output = 7;
inline int NodeExecStats::output_size() const {
  return output_.size();
}
inline void NodeExecStats::clear_output() {
  output_.Clear();
}
inline ::tensorflow::NodeOutput* NodeExecStats::mutable_output(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.output)
  return output_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeOutput >*
NodeExecStats::mutable_output() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeExecStats.output)
  return &output_;
}
inline const ::tensorflow::NodeOutput& NodeExecStats::output(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.output)
  return output_.Get(index);
}
inline ::tensorflow::NodeOutput* NodeExecStats::add_output() {
  // @@protoc_insertion_point(field_add:tensorflow.NodeExecStats.output)
  return output_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeOutput >&
NodeExecStats::output() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeExecStats.output)
  return output_;
}

// string timeline_label = 8;
inline void NodeExecStats::clear_timeline_label() {
  timeline_label_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& NodeExecStats::timeline_label() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.timeline_label)
  return timeline_label_.Get();
}
inline void NodeExecStats::set_timeline_label(const std::string& value) {
  
  timeline_label_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.timeline_label)
}
inline void NodeExecStats::set_timeline_label(std::string&& value) {
  
  timeline_label_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NodeExecStats.timeline_label)
}
inline void NodeExecStats::set_timeline_label(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  timeline_label_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.NodeExecStats.timeline_label)
}
inline void NodeExecStats::set_timeline_label(const char* value,
    size_t size) {
  
  timeline_label_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NodeExecStats.timeline_label)
}
inline std::string* NodeExecStats::mutable_timeline_label() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.timeline_label)
  return timeline_label_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* NodeExecStats::release_timeline_label() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeExecStats.timeline_label)
  
  return timeline_label_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void NodeExecStats::set_allocated_timeline_label(std::string* timeline_label) {
  if (timeline_label != nullptr) {
    
  } else {
    
  }
  timeline_label_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), timeline_label,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeExecStats.timeline_label)
}
inline std::string* NodeExecStats::unsafe_arena_release_timeline_label() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NodeExecStats.timeline_label)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return timeline_label_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void NodeExecStats::unsafe_arena_set_allocated_timeline_label(
    std::string* timeline_label) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (timeline_label != nullptr) {
    
  } else {
    
  }
  timeline_label_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      timeline_label, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeExecStats.timeline_label)
}

// int64 scheduled_micros = 9;
inline void NodeExecStats::clear_scheduled_micros() {
  scheduled_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 NodeExecStats::scheduled_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.scheduled_micros)
  return scheduled_micros_;
}
inline void NodeExecStats::set_scheduled_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  scheduled_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.scheduled_micros)
}

// uint32 thread_id = 10;
inline void NodeExecStats::clear_thread_id() {
  thread_id_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 NodeExecStats::thread_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.thread_id)
  return thread_id_;
}
inline void NodeExecStats::set_thread_id(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  thread_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.thread_id)
}

// repeated .tensorflow.AllocationDescription referenced_tensor = 11;
inline int NodeExecStats::referenced_tensor_size() const {
  return referenced_tensor_.size();
}
inline ::tensorflow::AllocationDescription* NodeExecStats::mutable_referenced_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.referenced_tensor)
  return referenced_tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationDescription >*
NodeExecStats::mutable_referenced_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeExecStats.referenced_tensor)
  return &referenced_tensor_;
}
inline const ::tensorflow::AllocationDescription& NodeExecStats::referenced_tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.referenced_tensor)
  return referenced_tensor_.Get(index);
}
inline ::tensorflow::AllocationDescription* NodeExecStats::add_referenced_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.NodeExecStats.referenced_tensor)
  return referenced_tensor_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationDescription >&
NodeExecStats::referenced_tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeExecStats.referenced_tensor)
  return referenced_tensor_;
}

// .tensorflow.MemoryStats memory_stats = 12;
inline bool NodeExecStats::has_memory_stats() const {
  return this != internal_default_instance() && memory_stats_ != nullptr;
}
inline void NodeExecStats::clear_memory_stats() {
  if (GetArenaNoVirtual() == nullptr && memory_stats_ != nullptr) {
    delete memory_stats_;
  }
  memory_stats_ = nullptr;
}
inline const ::tensorflow::MemoryStats& NodeExecStats::memory_stats() const {
  const ::tensorflow::MemoryStats* p = memory_stats_;
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.memory_stats)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::MemoryStats*>(
      &::tensorflow::_MemoryStats_default_instance_);
}
inline ::tensorflow::MemoryStats* NodeExecStats::release_memory_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeExecStats.memory_stats)
  
  ::tensorflow::MemoryStats* temp = memory_stats_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  memory_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::MemoryStats* NodeExecStats::unsafe_arena_release_memory_stats() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NodeExecStats.memory_stats)
  
  ::tensorflow::MemoryStats* temp = memory_stats_;
  memory_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::MemoryStats* NodeExecStats::mutable_memory_stats() {
  
  if (memory_stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::MemoryStats>(GetArenaNoVirtual());
    memory_stats_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.memory_stats)
  return memory_stats_;
}
inline void NodeExecStats::set_allocated_memory_stats(::tensorflow::MemoryStats* memory_stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete memory_stats_;
  }
  if (memory_stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(memory_stats);
    if (message_arena != submessage_arena) {
      memory_stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, memory_stats, submessage_arena);
    }
    
  } else {
    
  }
  memory_stats_ = memory_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeExecStats.memory_stats)
}

// int64 all_start_nanos = 13;
inline void NodeExecStats::clear_all_start_nanos() {
  all_start_nanos_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 NodeExecStats::all_start_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_start_nanos)
  return all_start_nanos_;
}
inline void NodeExecStats::set_all_start_nanos(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  all_start_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_start_nanos)
}

// int64 op_start_rel_nanos = 14;
inline void NodeExecStats::clear_op_start_rel_nanos() {
  op_start_rel_nanos_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 NodeExecStats::op_start_rel_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_start_rel_nanos)
  return op_start_rel_nanos_;
}
inline void NodeExecStats::set_op_start_rel_nanos(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  op_start_rel_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_start_rel_nanos)
}

// int64 op_end_rel_nanos = 15;
inline void NodeExecStats::clear_op_end_rel_nanos() {
  op_end_rel_nanos_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 NodeExecStats::op_end_rel_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_end_rel_nanos)
  return op_end_rel_nanos_;
}
inline void NodeExecStats::set_op_end_rel_nanos(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  op_end_rel_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_end_rel_nanos)
}

// int64 all_end_rel_nanos = 16;
inline void NodeExecStats::clear_all_end_rel_nanos() {
  all_end_rel_nanos_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 NodeExecStats::all_end_rel_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_end_rel_nanos)
  return all_end_rel_nanos_;
}
inline void NodeExecStats::set_all_end_rel_nanos(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  all_end_rel_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_end_rel_nanos)
}

// int64 scheduled_nanos = 17;
inline void NodeExecStats::clear_scheduled_nanos() {
  scheduled_nanos_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 NodeExecStats::scheduled_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.scheduled_nanos)
  return scheduled_nanos_;
}
inline void NodeExecStats::set_scheduled_nanos(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  scheduled_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.scheduled_nanos)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// DeviceStepStats

// string device = 1;
inline void DeviceStepStats::clear_device() {
  device_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DeviceStepStats::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceStepStats.device)
  return device_.Get();
}
inline void DeviceStepStats::set_device(const std::string& value) {
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceStepStats.device)
}
inline void DeviceStepStats::set_device(std::string&& value) {
  
  device_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceStepStats.device)
}
inline void DeviceStepStats::set_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceStepStats.device)
}
inline void DeviceStepStats::set_device(const char* value,
    size_t size) {
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceStepStats.device)
}
inline std::string* DeviceStepStats::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceStepStats.device)
  return device_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DeviceStepStats::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceStepStats.device)
  
  return device_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceStepStats::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  device_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceStepStats.device)
}
inline std::string* DeviceStepStats::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceStepStats.device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceStepStats::unsafe_arena_set_allocated_device(
    std::string* device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device != nullptr) {
    
  } else {
    
  }
  device_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceStepStats.device)
}

// repeated .tensorflow.NodeExecStats node_stats = 2;
inline int DeviceStepStats::node_stats_size() const {
  return node_stats_.size();
}
inline void DeviceStepStats::clear_node_stats() {
  node_stats_.Clear();
}
inline ::tensorflow::NodeExecStats* DeviceStepStats::mutable_node_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceStepStats.node_stats)
  return node_stats_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeExecStats >*
DeviceStepStats::mutable_node_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DeviceStepStats.node_stats)
  return &node_stats_;
}
inline const ::tensorflow::NodeExecStats& DeviceStepStats::node_stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceStepStats.node_stats)
  return node_stats_.Get(index);
}
inline ::tensorflow::NodeExecStats* DeviceStepStats::add_node_stats() {
  // @@protoc_insertion_point(field_add:tensorflow.DeviceStepStats.node_stats)
  return node_stats_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeExecStats >&
DeviceStepStats::node_stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.DeviceStepStats.node_stats)
  return node_stats_;
}

// map<uint32, string> thread_names = 3;
inline int DeviceStepStats::thread_names_size() const {
  return thread_names_.size();
}
inline void DeviceStepStats::clear_thread_names() {
  thread_names_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, std::string >&
DeviceStepStats::thread_names() const {
  // @@protoc_insertion_point(field_map:tensorflow.DeviceStepStats.thread_names)
  return thread_names_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, std::string >*
DeviceStepStats::mutable_thread_names() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.DeviceStepStats.thread_names)
  return thread_names_.MutableMap();
}

// -------------------------------------------------------------------

// StepStats

// repeated .tensorflow.DeviceStepStats dev_stats = 1;
inline int StepStats::dev_stats_size() const {
  return dev_stats_.size();
}
inline void StepStats::clear_dev_stats() {
  dev_stats_.Clear();
}
inline ::tensorflow::DeviceStepStats* StepStats::mutable_dev_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.StepStats.dev_stats)
  return dev_stats_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceStepStats >*
StepStats::mutable_dev_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.StepStats.dev_stats)
  return &dev_stats_;
}
inline const ::tensorflow::DeviceStepStats& StepStats::dev_stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.StepStats.dev_stats)
  return dev_stats_.Get(index);
}
inline ::tensorflow::DeviceStepStats* StepStats::add_dev_stats() {
  // @@protoc_insertion_point(field_add:tensorflow.StepStats.dev_stats)
  return dev_stats_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceStepStats >&
StepStats::dev_stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.StepStats.dev_stats)
  return dev_stats_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
