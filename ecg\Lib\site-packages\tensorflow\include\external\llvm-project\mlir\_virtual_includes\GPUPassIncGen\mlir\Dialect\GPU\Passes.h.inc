/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// GpuAsyncRegionPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class GpuAsyncRegionPassBase : public ::mlir::FunctionPass {
public:
  using Base = GpuAsyncRegionPassBase;

  GpuAsyncRegionPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  GpuAsyncRegionPassBase(const GpuAsyncRegionPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-async-region");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-async-region"; }

  ::llvm::StringRef getDescription() const override { return "Make GPU ops async"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuAsyncRegionPass");
  }
  ::llvm::StringRef getName() const override { return "GpuAsyncRegionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// GpuKernelOutlining
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class GpuKernelOutliningBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GpuKernelOutliningBase;

  GpuKernelOutliningBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuKernelOutliningBase(const GpuKernelOutliningBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-kernel-outlining");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-kernel-outlining"; }

  ::llvm::StringRef getDescription() const override { return "Outline gpu.launch bodies to kernel functions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuKernelOutlining");
  }
  ::llvm::StringRef getName() const override { return "GpuKernelOutlining"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// GpuAsyncRegionPass Registration
//===----------------------------------------------------------------------===//

inline void registerGpuAsyncRegionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuAsyncRegionPass();
  });
}

//===----------------------------------------------------------------------===//
// GpuKernelOutlining Registration
//===----------------------------------------------------------------------===//

inline void registerGpuKernelOutliningPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuKernelOutliningPass();
  });
}

//===----------------------------------------------------------------------===//
// GPU Registration
//===----------------------------------------------------------------------===//

inline void registerGPUPasses() {
  registerGpuAsyncRegionPassPass();
  registerGpuKernelOutliningPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
