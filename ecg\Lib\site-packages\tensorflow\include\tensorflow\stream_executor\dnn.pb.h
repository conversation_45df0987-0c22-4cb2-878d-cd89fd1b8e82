// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/stream_executor/dnn.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fstream_5fexecutor_2fdnn_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fstream_5fexecutor_2fdnn_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fstream_5fexecutor_2fdnn_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fstream_5fexecutor_2fdnn_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fstream_5fexecutor_2fdnn_2eproto;
namespace stream_executor {
namespace dnn {
class AlgorithmProto;
class AlgorithmProtoDefaultTypeInternal;
extern AlgorithmProtoDefaultTypeInternal _AlgorithmProto_default_instance_;
class ConvolutionDescriptorProto;
class ConvolutionDescriptorProtoDefaultTypeInternal;
extern ConvolutionDescriptorProtoDefaultTypeInternal _ConvolutionDescriptorProto_default_instance_;
class TensorDescriptorProto;
class TensorDescriptorProtoDefaultTypeInternal;
extern TensorDescriptorProtoDefaultTypeInternal _TensorDescriptorProto_default_instance_;
}  // namespace dnn
}  // namespace stream_executor
PROTOBUF_NAMESPACE_OPEN
template<> ::stream_executor::dnn::AlgorithmProto* Arena::CreateMaybeMessage<::stream_executor::dnn::AlgorithmProto>(Arena*);
template<> ::stream_executor::dnn::ConvolutionDescriptorProto* Arena::CreateMaybeMessage<::stream_executor::dnn::ConvolutionDescriptorProto>(Arena*);
template<> ::stream_executor::dnn::TensorDescriptorProto* Arena::CreateMaybeMessage<::stream_executor::dnn::TensorDescriptorProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace stream_executor {
namespace dnn {

enum AlgorithmProto_MathType : int {
  AlgorithmProto_MathType_DEFAULT_MATH = 0,
  AlgorithmProto_MathType_TENSOR_OP_MATH = 1,
  AlgorithmProto_MathType_AlgorithmProto_MathType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  AlgorithmProto_MathType_AlgorithmProto_MathType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool AlgorithmProto_MathType_IsValid(int value);
constexpr AlgorithmProto_MathType AlgorithmProto_MathType_MathType_MIN = AlgorithmProto_MathType_DEFAULT_MATH;
constexpr AlgorithmProto_MathType AlgorithmProto_MathType_MathType_MAX = AlgorithmProto_MathType_TENSOR_OP_MATH;
constexpr int AlgorithmProto_MathType_MathType_ARRAYSIZE = AlgorithmProto_MathType_MathType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlgorithmProto_MathType_descriptor();
template<typename T>
inline const std::string& AlgorithmProto_MathType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AlgorithmProto_MathType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AlgorithmProto_MathType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AlgorithmProto_MathType_descriptor(), enum_t_value);
}
inline bool AlgorithmProto_MathType_Parse(
    const std::string& name, AlgorithmProto_MathType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AlgorithmProto_MathType>(
    AlgorithmProto_MathType_descriptor(), name, value);
}
enum DataType : int {
  kFloat = 0,
  kDouble = 1,
  kHalf = 2,
  kInt8 = 3,
  kInt32 = 4,
  kComplexFloat = 5,
  kComplexDouble = 6,
  DataType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  DataType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool DataType_IsValid(int value);
constexpr DataType DataType_MIN = kFloat;
constexpr DataType DataType_MAX = kComplexDouble;
constexpr int DataType_ARRAYSIZE = DataType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataType_descriptor();
template<typename T>
inline const std::string& DataType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DataType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DataType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DataType_descriptor(), enum_t_value);
}
inline bool DataType_Parse(
    const std::string& name, DataType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DataType>(
    DataType_descriptor(), name, value);
}
enum DataLayout : int {
  kYXDepthBatch = 0,
  kYXBatchDepth = 1,
  kBatchYXDepth = 2,
  kBatchDepthYX = 3,
  kBatchDepthYX4 = 4,
  kBatchDepthYX32 = 5,
  DataLayout_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  DataLayout_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool DataLayout_IsValid(int value);
constexpr DataLayout DataLayout_MIN = kYXDepthBatch;
constexpr DataLayout DataLayout_MAX = kBatchDepthYX32;
constexpr int DataLayout_ARRAYSIZE = DataLayout_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataLayout_descriptor();
template<typename T>
inline const std::string& DataLayout_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DataLayout>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DataLayout_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DataLayout_descriptor(), enum_t_value);
}
inline bool DataLayout_Parse(
    const std::string& name, DataLayout* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DataLayout>(
    DataLayout_descriptor(), name, value);
}
enum FilterLayout : int {
  kOutputInputYX = 0,
  kOutputYXInput = 1,
  kOutputInputYX4 = 2,
  kOutputInputYX32 = 5,
  kInputYXOutput = 3,
  kYXInputOutput = 4,
  FilterLayout_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  FilterLayout_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool FilterLayout_IsValid(int value);
constexpr FilterLayout FilterLayout_MIN = kOutputInputYX;
constexpr FilterLayout FilterLayout_MAX = kOutputInputYX32;
constexpr int FilterLayout_ARRAYSIZE = FilterLayout_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FilterLayout_descriptor();
template<typename T>
inline const std::string& FilterLayout_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FilterLayout>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FilterLayout_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FilterLayout_descriptor(), enum_t_value);
}
inline bool FilterLayout_Parse(
    const std::string& name, FilterLayout* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FilterLayout>(
    FilterLayout_descriptor(), name, value);
}
enum ActivationMode : int {
  kNone = 0,
  kSigmoid = 1,
  kRelu = 2,
  kRelu6 = 3,
  kReluX = 4,
  kTanh = 5,
  kBandPass = 6,
  ActivationMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ActivationMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ActivationMode_IsValid(int value);
constexpr ActivationMode ActivationMode_MIN = kNone;
constexpr ActivationMode ActivationMode_MAX = kBandPass;
constexpr int ActivationMode_ARRAYSIZE = ActivationMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ActivationMode_descriptor();
template<typename T>
inline const std::string& ActivationMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ActivationMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ActivationMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ActivationMode_descriptor(), enum_t_value);
}
inline bool ActivationMode_Parse(
    const std::string& name, ActivationMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ActivationMode>(
    ActivationMode_descriptor(), name, value);
}
enum ConvolutionMode : int {
  CROSS_CORRELATION = 0,
  CONVOLUTION = 1,
  ConvolutionMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ConvolutionMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ConvolutionMode_IsValid(int value);
constexpr ConvolutionMode ConvolutionMode_MIN = CROSS_CORRELATION;
constexpr ConvolutionMode ConvolutionMode_MAX = CONVOLUTION;
constexpr int ConvolutionMode_ARRAYSIZE = ConvolutionMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConvolutionMode_descriptor();
template<typename T>
inline const std::string& ConvolutionMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ConvolutionMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ConvolutionMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ConvolutionMode_descriptor(), enum_t_value);
}
inline bool ConvolutionMode_Parse(
    const std::string& name, ConvolutionMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ConvolutionMode>(
    ConvolutionMode_descriptor(), name, value);
}
enum ConvolutionKind : int {
  INVALID = 0,
  FORWARD = 1,
  BACKWARD_FILTER = 2,
  BACKWARD_DATA = 3,
  FORWARD_BIAS_ACTIVATION = 4,
  ConvolutionKind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ConvolutionKind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ConvolutionKind_IsValid(int value);
constexpr ConvolutionKind ConvolutionKind_MIN = INVALID;
constexpr ConvolutionKind ConvolutionKind_MAX = FORWARD_BIAS_ACTIVATION;
constexpr int ConvolutionKind_ARRAYSIZE = ConvolutionKind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConvolutionKind_descriptor();
template<typename T>
inline const std::string& ConvolutionKind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ConvolutionKind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ConvolutionKind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ConvolutionKind_descriptor(), enum_t_value);
}
inline bool ConvolutionKind_Parse(
    const std::string& name, ConvolutionKind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ConvolutionKind>(
    ConvolutionKind_descriptor(), name, value);
}
// ===================================================================

class TensorDescriptorProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.dnn.TensorDescriptorProto) */ {
 public:
  TensorDescriptorProto();
  virtual ~TensorDescriptorProto();

  TensorDescriptorProto(const TensorDescriptorProto& from);
  TensorDescriptorProto(TensorDescriptorProto&& from) noexcept
    : TensorDescriptorProto() {
    *this = ::std::move(from);
  }

  inline TensorDescriptorProto& operator=(const TensorDescriptorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorDescriptorProto& operator=(TensorDescriptorProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TensorDescriptorProto& default_instance();

  enum LayoutOneofCase {
    kDataLayout = 3,
    kFilterLayout = 4,
    LAYOUT_ONEOF_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorDescriptorProto* internal_default_instance() {
    return reinterpret_cast<const TensorDescriptorProto*>(
               &_TensorDescriptorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TensorDescriptorProto& a, TensorDescriptorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorDescriptorProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TensorDescriptorProto* New() const final {
    return CreateMaybeMessage<TensorDescriptorProto>(nullptr);
  }

  TensorDescriptorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TensorDescriptorProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TensorDescriptorProto& from);
  void MergeFrom(const TensorDescriptorProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorDescriptorProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.dnn.TensorDescriptorProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fstream_5fexecutor_2fdnn_2eproto);
    return ::descriptor_table_tensorflow_2fstream_5fexecutor_2fdnn_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionsFieldNumber = 1,
    kDataTypeFieldNumber = 2,
    kDataLayoutFieldNumber = 3,
    kFilterLayoutFieldNumber = 4,
  };
  // repeated int64 dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  ::PROTOBUF_NAMESPACE_ID::int64 dimensions(int index) const;
  void set_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_dimensions();

  // .stream_executor.dnn.DataType data_type = 2;
  void clear_data_type();
  ::stream_executor::dnn::DataType data_type() const;
  void set_data_type(::stream_executor::dnn::DataType value);

  // .stream_executor.dnn.DataLayout data_layout = 3;
  private:
  bool has_data_layout() const;
  public:
  void clear_data_layout();
  ::stream_executor::dnn::DataLayout data_layout() const;
  void set_data_layout(::stream_executor::dnn::DataLayout value);

  // .stream_executor.dnn.FilterLayout filter_layout = 4;
  private:
  bool has_filter_layout() const;
  public:
  void clear_filter_layout();
  ::stream_executor::dnn::FilterLayout filter_layout() const;
  void set_filter_layout(::stream_executor::dnn::FilterLayout value);

  void clear_layout_oneof();
  LayoutOneofCase layout_oneof_case() const;
  // @@protoc_insertion_point(class_scope:stream_executor.dnn.TensorDescriptorProto)
 private:
  class _Internal;
  void set_has_data_layout();
  void set_has_filter_layout();

  inline bool has_layout_oneof() const;
  inline void clear_has_layout_oneof();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > dimensions_;
  mutable std::atomic<int> _dimensions_cached_byte_size_;
  int data_type_;
  union LayoutOneofUnion {
    LayoutOneofUnion() {}
    int data_layout_;
    int filter_layout_;
  } layout_oneof_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fstream_5fexecutor_2fdnn_2eproto;
};
// -------------------------------------------------------------------

class AlgorithmProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.dnn.AlgorithmProto) */ {
 public:
  AlgorithmProto();
  virtual ~AlgorithmProto();

  AlgorithmProto(const AlgorithmProto& from);
  AlgorithmProto(AlgorithmProto&& from) noexcept
    : AlgorithmProto() {
    *this = ::std::move(from);
  }

  inline AlgorithmProto& operator=(const AlgorithmProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlgorithmProto& operator=(AlgorithmProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AlgorithmProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AlgorithmProto* internal_default_instance() {
    return reinterpret_cast<const AlgorithmProto*>(
               &_AlgorithmProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(AlgorithmProto& a, AlgorithmProto& b) {
    a.Swap(&b);
  }
  inline void Swap(AlgorithmProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AlgorithmProto* New() const final {
    return CreateMaybeMessage<AlgorithmProto>(nullptr);
  }

  AlgorithmProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AlgorithmProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AlgorithmProto& from);
  void MergeFrom(const AlgorithmProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlgorithmProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.dnn.AlgorithmProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fstream_5fexecutor_2fdnn_2eproto);
    return ::descriptor_table_tensorflow_2fstream_5fexecutor_2fdnn_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef AlgorithmProto_MathType MathType;
  static constexpr MathType DEFAULT_MATH =
    AlgorithmProto_MathType_DEFAULT_MATH;
  static constexpr MathType TENSOR_OP_MATH =
    AlgorithmProto_MathType_TENSOR_OP_MATH;
  static inline bool MathType_IsValid(int value) {
    return AlgorithmProto_MathType_IsValid(value);
  }
  static constexpr MathType MathType_MIN =
    AlgorithmProto_MathType_MathType_MIN;
  static constexpr MathType MathType_MAX =
    AlgorithmProto_MathType_MathType_MAX;
  static constexpr int MathType_ARRAYSIZE =
    AlgorithmProto_MathType_MathType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MathType_descriptor() {
    return AlgorithmProto_MathType_descriptor();
  }
  template<typename T>
  static inline const std::string& MathType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MathType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MathType_Name.");
    return AlgorithmProto_MathType_Name(enum_t_value);
  }
  static inline bool MathType_Parse(const std::string& name,
      MathType* value) {
    return AlgorithmProto_MathType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kExecPlanIdFieldNumber = 3,
    kAlgoIdFieldNumber = 1,
    kMathTypeFieldNumber = 2,
  };
  // string exec_plan_id = 3;
  void clear_exec_plan_id();
  const std::string& exec_plan_id() const;
  void set_exec_plan_id(const std::string& value);
  void set_exec_plan_id(std::string&& value);
  void set_exec_plan_id(const char* value);
  void set_exec_plan_id(const char* value, size_t size);
  std::string* mutable_exec_plan_id();
  std::string* release_exec_plan_id();
  void set_allocated_exec_plan_id(std::string* exec_plan_id);

  // int64 algo_id = 1;
  void clear_algo_id();
  ::PROTOBUF_NAMESPACE_ID::int64 algo_id() const;
  void set_algo_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .stream_executor.dnn.AlgorithmProto.MathType math_type = 2;
  void clear_math_type();
  ::stream_executor::dnn::AlgorithmProto_MathType math_type() const;
  void set_math_type(::stream_executor::dnn::AlgorithmProto_MathType value);

  // @@protoc_insertion_point(class_scope:stream_executor.dnn.AlgorithmProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr exec_plan_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 algo_id_;
  int math_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fstream_5fexecutor_2fdnn_2eproto;
};
// -------------------------------------------------------------------

class ConvolutionDescriptorProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.dnn.ConvolutionDescriptorProto) */ {
 public:
  ConvolutionDescriptorProto();
  virtual ~ConvolutionDescriptorProto();

  ConvolutionDescriptorProto(const ConvolutionDescriptorProto& from);
  ConvolutionDescriptorProto(ConvolutionDescriptorProto&& from) noexcept
    : ConvolutionDescriptorProto() {
    *this = ::std::move(from);
  }

  inline ConvolutionDescriptorProto& operator=(const ConvolutionDescriptorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConvolutionDescriptorProto& operator=(ConvolutionDescriptorProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ConvolutionDescriptorProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConvolutionDescriptorProto* internal_default_instance() {
    return reinterpret_cast<const ConvolutionDescriptorProto*>(
               &_ConvolutionDescriptorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ConvolutionDescriptorProto& a, ConvolutionDescriptorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ConvolutionDescriptorProto* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ConvolutionDescriptorProto* New() const final {
    return CreateMaybeMessage<ConvolutionDescriptorProto>(nullptr);
  }

  ConvolutionDescriptorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ConvolutionDescriptorProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ConvolutionDescriptorProto& from);
  void MergeFrom(const ConvolutionDescriptorProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConvolutionDescriptorProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.dnn.ConvolutionDescriptorProto";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fstream_5fexecutor_2fdnn_2eproto);
    return ::descriptor_table_tensorflow_2fstream_5fexecutor_2fdnn_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPaddingsFieldNumber = 1,
    kStridesFieldNumber = 2,
    kDilationsFieldNumber = 3,
    kNameFieldNumber = 7,
    kComputeModeFieldNumber = 4,
    kGroupCountFieldNumber = 5,
    kConvolutionModeFieldNumber = 6,
  };
  // repeated int64 paddings = 1;
  int paddings_size() const;
  void clear_paddings();
  ::PROTOBUF_NAMESPACE_ID::int64 paddings(int index) const;
  void set_paddings(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_paddings(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      paddings() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_paddings();

  // repeated int64 strides = 2;
  int strides_size() const;
  void clear_strides();
  ::PROTOBUF_NAMESPACE_ID::int64 strides(int index) const;
  void set_strides(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_strides(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      strides() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_strides();

  // repeated int64 dilations = 3;
  int dilations_size() const;
  void clear_dilations();
  ::PROTOBUF_NAMESPACE_ID::int64 dilations(int index) const;
  void set_dilations(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_dilations(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      dilations() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_dilations();

  // string name = 7;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // .stream_executor.dnn.DataType compute_mode = 4;
  void clear_compute_mode();
  ::stream_executor::dnn::DataType compute_mode() const;
  void set_compute_mode(::stream_executor::dnn::DataType value);

  // int32 group_count = 5;
  void clear_group_count();
  ::PROTOBUF_NAMESPACE_ID::int32 group_count() const;
  void set_group_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // .stream_executor.dnn.ConvolutionMode convolution_mode = 6;
  void clear_convolution_mode();
  ::stream_executor::dnn::ConvolutionMode convolution_mode() const;
  void set_convolution_mode(::stream_executor::dnn::ConvolutionMode value);

  // @@protoc_insertion_point(class_scope:stream_executor.dnn.ConvolutionDescriptorProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > paddings_;
  mutable std::atomic<int> _paddings_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > strides_;
  mutable std::atomic<int> _strides_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > dilations_;
  mutable std::atomic<int> _dilations_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  int compute_mode_;
  ::PROTOBUF_NAMESPACE_ID::int32 group_count_;
  int convolution_mode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fstream_5fexecutor_2fdnn_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorDescriptorProto

// repeated int64 dimensions = 1;
inline int TensorDescriptorProto::dimensions_size() const {
  return dimensions_.size();
}
inline void TensorDescriptorProto::clear_dimensions() {
  dimensions_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TensorDescriptorProto::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.TensorDescriptorProto.dimensions)
  return dimensions_.Get(index);
}
inline void TensorDescriptorProto::set_dimensions(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.TensorDescriptorProto.dimensions)
}
inline void TensorDescriptorProto::add_dimensions(::PROTOBUF_NAMESPACE_ID::int64 value) {
  dimensions_.Add(value);
  // @@protoc_insertion_point(field_add:stream_executor.dnn.TensorDescriptorProto.dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
TensorDescriptorProto::dimensions() const {
  // @@protoc_insertion_point(field_list:stream_executor.dnn.TensorDescriptorProto.dimensions)
  return dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
TensorDescriptorProto::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:stream_executor.dnn.TensorDescriptorProto.dimensions)
  return &dimensions_;
}

// .stream_executor.dnn.DataType data_type = 2;
inline void TensorDescriptorProto::clear_data_type() {
  data_type_ = 0;
}
inline ::stream_executor::dnn::DataType TensorDescriptorProto::data_type() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.TensorDescriptorProto.data_type)
  return static_cast< ::stream_executor::dnn::DataType >(data_type_);
}
inline void TensorDescriptorProto::set_data_type(::stream_executor::dnn::DataType value) {
  
  data_type_ = value;
  // @@protoc_insertion_point(field_set:stream_executor.dnn.TensorDescriptorProto.data_type)
}

// .stream_executor.dnn.DataLayout data_layout = 3;
inline bool TensorDescriptorProto::has_data_layout() const {
  return layout_oneof_case() == kDataLayout;
}
inline void TensorDescriptorProto::set_has_data_layout() {
  _oneof_case_[0] = kDataLayout;
}
inline void TensorDescriptorProto::clear_data_layout() {
  if (has_data_layout()) {
    layout_oneof_.data_layout_ = 0;
    clear_has_layout_oneof();
  }
}
inline ::stream_executor::dnn::DataLayout TensorDescriptorProto::data_layout() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.TensorDescriptorProto.data_layout)
  if (has_data_layout()) {
    return static_cast< ::stream_executor::dnn::DataLayout >(layout_oneof_.data_layout_);
  }
  return static_cast< ::stream_executor::dnn::DataLayout >(0);
}
inline void TensorDescriptorProto::set_data_layout(::stream_executor::dnn::DataLayout value) {
  if (!has_data_layout()) {
    clear_layout_oneof();
    set_has_data_layout();
  }
  layout_oneof_.data_layout_ = value;
  // @@protoc_insertion_point(field_set:stream_executor.dnn.TensorDescriptorProto.data_layout)
}

// .stream_executor.dnn.FilterLayout filter_layout = 4;
inline bool TensorDescriptorProto::has_filter_layout() const {
  return layout_oneof_case() == kFilterLayout;
}
inline void TensorDescriptorProto::set_has_filter_layout() {
  _oneof_case_[0] = kFilterLayout;
}
inline void TensorDescriptorProto::clear_filter_layout() {
  if (has_filter_layout()) {
    layout_oneof_.filter_layout_ = 0;
    clear_has_layout_oneof();
  }
}
inline ::stream_executor::dnn::FilterLayout TensorDescriptorProto::filter_layout() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.TensorDescriptorProto.filter_layout)
  if (has_filter_layout()) {
    return static_cast< ::stream_executor::dnn::FilterLayout >(layout_oneof_.filter_layout_);
  }
  return static_cast< ::stream_executor::dnn::FilterLayout >(0);
}
inline void TensorDescriptorProto::set_filter_layout(::stream_executor::dnn::FilterLayout value) {
  if (!has_filter_layout()) {
    clear_layout_oneof();
    set_has_filter_layout();
  }
  layout_oneof_.filter_layout_ = value;
  // @@protoc_insertion_point(field_set:stream_executor.dnn.TensorDescriptorProto.filter_layout)
}

inline bool TensorDescriptorProto::has_layout_oneof() const {
  return layout_oneof_case() != LAYOUT_ONEOF_NOT_SET;
}
inline void TensorDescriptorProto::clear_has_layout_oneof() {
  _oneof_case_[0] = LAYOUT_ONEOF_NOT_SET;
}
inline TensorDescriptorProto::LayoutOneofCase TensorDescriptorProto::layout_oneof_case() const {
  return TensorDescriptorProto::LayoutOneofCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// AlgorithmProto

// int64 algo_id = 1;
inline void AlgorithmProto::clear_algo_id() {
  algo_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AlgorithmProto::algo_id() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.AlgorithmProto.algo_id)
  return algo_id_;
}
inline void AlgorithmProto::set_algo_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  algo_id_ = value;
  // @@protoc_insertion_point(field_set:stream_executor.dnn.AlgorithmProto.algo_id)
}

// .stream_executor.dnn.AlgorithmProto.MathType math_type = 2;
inline void AlgorithmProto::clear_math_type() {
  math_type_ = 0;
}
inline ::stream_executor::dnn::AlgorithmProto_MathType AlgorithmProto::math_type() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.AlgorithmProto.math_type)
  return static_cast< ::stream_executor::dnn::AlgorithmProto_MathType >(math_type_);
}
inline void AlgorithmProto::set_math_type(::stream_executor::dnn::AlgorithmProto_MathType value) {
  
  math_type_ = value;
  // @@protoc_insertion_point(field_set:stream_executor.dnn.AlgorithmProto.math_type)
}

// string exec_plan_id = 3;
inline void AlgorithmProto::clear_exec_plan_id() {
  exec_plan_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& AlgorithmProto::exec_plan_id() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.AlgorithmProto.exec_plan_id)
  return exec_plan_id_.GetNoArena();
}
inline void AlgorithmProto::set_exec_plan_id(const std::string& value) {
  
  exec_plan_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.AlgorithmProto.exec_plan_id)
}
inline void AlgorithmProto::set_exec_plan_id(std::string&& value) {
  
  exec_plan_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:stream_executor.dnn.AlgorithmProto.exec_plan_id)
}
inline void AlgorithmProto::set_exec_plan_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  exec_plan_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:stream_executor.dnn.AlgorithmProto.exec_plan_id)
}
inline void AlgorithmProto::set_exec_plan_id(const char* value, size_t size) {
  
  exec_plan_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:stream_executor.dnn.AlgorithmProto.exec_plan_id)
}
inline std::string* AlgorithmProto::mutable_exec_plan_id() {
  
  // @@protoc_insertion_point(field_mutable:stream_executor.dnn.AlgorithmProto.exec_plan_id)
  return exec_plan_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* AlgorithmProto::release_exec_plan_id() {
  // @@protoc_insertion_point(field_release:stream_executor.dnn.AlgorithmProto.exec_plan_id)
  
  return exec_plan_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void AlgorithmProto::set_allocated_exec_plan_id(std::string* exec_plan_id) {
  if (exec_plan_id != nullptr) {
    
  } else {
    
  }
  exec_plan_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), exec_plan_id);
  // @@protoc_insertion_point(field_set_allocated:stream_executor.dnn.AlgorithmProto.exec_plan_id)
}

// -------------------------------------------------------------------

// ConvolutionDescriptorProto

// repeated int64 paddings = 1;
inline int ConvolutionDescriptorProto::paddings_size() const {
  return paddings_.size();
}
inline void ConvolutionDescriptorProto::clear_paddings() {
  paddings_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDescriptorProto::paddings(int index) const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.paddings)
  return paddings_.Get(index);
}
inline void ConvolutionDescriptorProto::set_paddings(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  paddings_.Set(index, value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.paddings)
}
inline void ConvolutionDescriptorProto::add_paddings(::PROTOBUF_NAMESPACE_ID::int64 value) {
  paddings_.Add(value);
  // @@protoc_insertion_point(field_add:stream_executor.dnn.ConvolutionDescriptorProto.paddings)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ConvolutionDescriptorProto::paddings() const {
  // @@protoc_insertion_point(field_list:stream_executor.dnn.ConvolutionDescriptorProto.paddings)
  return paddings_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ConvolutionDescriptorProto::mutable_paddings() {
  // @@protoc_insertion_point(field_mutable_list:stream_executor.dnn.ConvolutionDescriptorProto.paddings)
  return &paddings_;
}

// repeated int64 strides = 2;
inline int ConvolutionDescriptorProto::strides_size() const {
  return strides_.size();
}
inline void ConvolutionDescriptorProto::clear_strides() {
  strides_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDescriptorProto::strides(int index) const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.strides)
  return strides_.Get(index);
}
inline void ConvolutionDescriptorProto::set_strides(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  strides_.Set(index, value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.strides)
}
inline void ConvolutionDescriptorProto::add_strides(::PROTOBUF_NAMESPACE_ID::int64 value) {
  strides_.Add(value);
  // @@protoc_insertion_point(field_add:stream_executor.dnn.ConvolutionDescriptorProto.strides)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ConvolutionDescriptorProto::strides() const {
  // @@protoc_insertion_point(field_list:stream_executor.dnn.ConvolutionDescriptorProto.strides)
  return strides_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ConvolutionDescriptorProto::mutable_strides() {
  // @@protoc_insertion_point(field_mutable_list:stream_executor.dnn.ConvolutionDescriptorProto.strides)
  return &strides_;
}

// repeated int64 dilations = 3;
inline int ConvolutionDescriptorProto::dilations_size() const {
  return dilations_.size();
}
inline void ConvolutionDescriptorProto::clear_dilations() {
  dilations_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ConvolutionDescriptorProto::dilations(int index) const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.dilations)
  return dilations_.Get(index);
}
inline void ConvolutionDescriptorProto::set_dilations(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  dilations_.Set(index, value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.dilations)
}
inline void ConvolutionDescriptorProto::add_dilations(::PROTOBUF_NAMESPACE_ID::int64 value) {
  dilations_.Add(value);
  // @@protoc_insertion_point(field_add:stream_executor.dnn.ConvolutionDescriptorProto.dilations)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ConvolutionDescriptorProto::dilations() const {
  // @@protoc_insertion_point(field_list:stream_executor.dnn.ConvolutionDescriptorProto.dilations)
  return dilations_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ConvolutionDescriptorProto::mutable_dilations() {
  // @@protoc_insertion_point(field_mutable_list:stream_executor.dnn.ConvolutionDescriptorProto.dilations)
  return &dilations_;
}

// .stream_executor.dnn.DataType compute_mode = 4;
inline void ConvolutionDescriptorProto::clear_compute_mode() {
  compute_mode_ = 0;
}
inline ::stream_executor::dnn::DataType ConvolutionDescriptorProto::compute_mode() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.compute_mode)
  return static_cast< ::stream_executor::dnn::DataType >(compute_mode_);
}
inline void ConvolutionDescriptorProto::set_compute_mode(::stream_executor::dnn::DataType value) {
  
  compute_mode_ = value;
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.compute_mode)
}

// int32 group_count = 5;
inline void ConvolutionDescriptorProto::clear_group_count() {
  group_count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ConvolutionDescriptorProto::group_count() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.group_count)
  return group_count_;
}
inline void ConvolutionDescriptorProto::set_group_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  group_count_ = value;
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.group_count)
}

// .stream_executor.dnn.ConvolutionMode convolution_mode = 6;
inline void ConvolutionDescriptorProto::clear_convolution_mode() {
  convolution_mode_ = 0;
}
inline ::stream_executor::dnn::ConvolutionMode ConvolutionDescriptorProto::convolution_mode() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.convolution_mode)
  return static_cast< ::stream_executor::dnn::ConvolutionMode >(convolution_mode_);
}
inline void ConvolutionDescriptorProto::set_convolution_mode(::stream_executor::dnn::ConvolutionMode value) {
  
  convolution_mode_ = value;
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.convolution_mode)
}

// string name = 7;
inline void ConvolutionDescriptorProto::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ConvolutionDescriptorProto::name() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.name)
  return name_.GetNoArena();
}
inline void ConvolutionDescriptorProto::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.name)
}
inline void ConvolutionDescriptorProto::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:stream_executor.dnn.ConvolutionDescriptorProto.name)
}
inline void ConvolutionDescriptorProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:stream_executor.dnn.ConvolutionDescriptorProto.name)
}
inline void ConvolutionDescriptorProto::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:stream_executor.dnn.ConvolutionDescriptorProto.name)
}
inline std::string* ConvolutionDescriptorProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:stream_executor.dnn.ConvolutionDescriptorProto.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ConvolutionDescriptorProto::release_name() {
  // @@protoc_insertion_point(field_release:stream_executor.dnn.ConvolutionDescriptorProto.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ConvolutionDescriptorProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:stream_executor.dnn.ConvolutionDescriptorProto.name)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace dnn
}  // namespace stream_executor

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::stream_executor::dnn::AlgorithmProto_MathType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::AlgorithmProto_MathType>() {
  return ::stream_executor::dnn::AlgorithmProto_MathType_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::DataType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::DataType>() {
  return ::stream_executor::dnn::DataType_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::DataLayout> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::DataLayout>() {
  return ::stream_executor::dnn::DataLayout_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::FilterLayout> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::FilterLayout>() {
  return ::stream_executor::dnn::FilterLayout_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::ActivationMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::ActivationMode>() {
  return ::stream_executor::dnn::ActivationMode_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::ConvolutionMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::ConvolutionMode>() {
  return ::stream_executor::dnn::ConvolutionMode_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::ConvolutionKind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::ConvolutionKind>() {
  return ::stream_executor::dnn::ConvolutionKind_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fstream_5fexecutor_2fdnn_2eproto
