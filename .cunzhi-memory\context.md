# 项目上下文信息

- 项目为心电图（ECG）年龄预测模型，使用TensorFlow 2.6.0和Keras 2.6.0框架，基于RTX4080 GPU训练。模型采用残差网络结构，通过分析心电图信号预测心脏年龄。
- ECG年龄预测模型改进预期：整体MAE降低15-25%，极端年龄段提升更显著(25-30%)，中年段提升较小(10-15%)。SE残差块和注意力机制贡献最大，分别可提升6-10%和8-12%。
- PVC检测问题分析：1) 参数类型不匹配 - pvc.py期望signals是字典形式，现在diagnosis.py直接传入ecg_data; 2) 移除了单导联数据适配逻辑，之前会将单导联ecg_data包装为{'lead2': ecg_data}; 3) pvc.py存在缩进错误
- 当前分析心率变异性(HRV)特征的代码已经可以计算时域特征(SDNN、RMSSD等)和频域特征(LF、HF、LF/HF等)，但需要添加基于国际标准的风险分级功能，将用户分为低/中/高压力和疲劳风险级别。
