/* Copyright 2016 Google Inc.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

#ifndef NSYNC_PLATFORM_MACOS_PLATFORM_CPP_OS_H_
#define NSYNC_PLATFORM_MACOS_PLATFORM_CPP_OS_H_

/* Some versions(!) of MacOS don't implement clock_gettime(). */
#include <sys/time.h>
#if !defined(CLOCK_REALTIME)

#define CLOCK_REALTIME 0
#define TIMER_ABSTIME 1

NSYNC_CPP_START_
typedef int clockid_t;
int clock_gettime (clockid_t clk_id, struct timespec *tp);
NSYNC_CPP_END_

#endif /*CLOCK_REALTIME*/

#endif /*NSYNC_PLATFORM_MACOS_PLATFORM_CPP_OS_H_*/
