from apps.models.arrhythmia_models import CADCardiomyopathyEntity


def process(ecg_data, sampling_rate):
    """
    心肌病冠心病诊断（0 or 1）
    :param ecg_data: ECG信号数据
    :param sampling_rate: 采样率
    :return:
    """
    cad_cardiomyopathy = CADCardiomyopathyEntity()
    cad_cardiomyopathy.ISC = 0
    cad_cardiomyopathy.LVH = 0
    cad_cardiomyopathy.RVH = 0
    cad_cardiomyopathy.LAH = 0
    cad_cardiomyopathy.RAH = 0
    cad_cardiomyopathy.MI = 0

    return cad_cardiomyopathy
