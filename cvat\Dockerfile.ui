FROM node:lts-slim AS cvat-ui

ENV TERM=xterm \
    LANG='C.UTF-8'  \
    LC_ALL='C.UTF-8'

# Install dependencies
COPY package.json /tmp/
COPY yarn.lock /tmp/
COPY cvat-core/package.json /tmp/cvat-core/
COPY cvat-canvas/package.json /tmp/cvat-canvas/
COPY cvat-canvas3d/package.json /tmp/cvat-canvas3d/
COPY cvat-ui/package.json /tmp/cvat-ui/
COPY cvat-data/package.json /tmp/cvat-data/

# Install common dependencies
WORKDIR /tmp/
RUN DISABLE_HUSKY=1 yarn --frozen-lockfile

# Build source code
COPY cvat-data/ /tmp/cvat-data/
COPY cvat-core/ /tmp/cvat-core/
COPY cvat-canvas3d/ /tmp/cvat-canvas3d/
COPY cvat-canvas/ /tmp/cvat-canvas/
COPY cvat-ui/ /tmp/cvat-ui/

ARG UI_APP_CONFIG
ARG CLIENT_PLUGINS
ARG DISABLE_SOURCE_MAPS
ARG SOURCE_MAPS_TOKEN

RUN CLIENT_PLUGINS="${CLIENT_PLUGINS}" \
DISABLE_SOURCE_MAPS="${DISABLE_SOURCE_MAPS}" \
UI_APP_CONFIG="${UI_APP_CONFIG}" \
SOURCE_MAPS_TOKEN="${SOURCE_MAPS_TOKEN}" yarn run build:cvat-ui

FROM nginx:1.27.4-alpine3.21-slim

# Replace default.conf configuration to remove unnecessary rules
COPY cvat-ui/react_nginx.conf /etc/nginx/conf.d/default.conf
COPY cvat-ui/robots.txt /usr/share/nginx/html/
COPY --from=cvat-ui /tmp/cvat-ui/dist /usr/share/nginx/html/
