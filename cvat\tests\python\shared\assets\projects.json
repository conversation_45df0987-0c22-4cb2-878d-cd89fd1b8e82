{"count": 15, "next": null, "previous": null, "results": [{"assignee": {"first_name": "User", "id": 10, "last_name": "Seventh", "url": "http://localhost:8080/api/users/10", "username": "user7"}, "assignee_updated_date": "2024-09-23T08:09:45.461000Z", "bug_tracker": "", "created_date": "2024-09-23T08:09:42.827000Z", "dimension": null, "guide_id": null, "id": 16, "labels": {"url": "http://localhost:8080/api/labels?project_id=16"}, "name": "project assigned to org owner", "organization": 2, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "source_storage": {"cloud_storage_id": null, "id": 43, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 44, "location": "local"}, "task_subsets": [], "tasks": {"count": 0, "url": "http://localhost:8080/api/tasks?project_id=16"}, "updated_date": "2024-09-23T08:09:45.474000Z", "url": "http://localhost:8080/api/projects/16"}, {"assignee": {"first_name": "User", "id": 5, "last_name": "Fourth", "url": "http://localhost:8080/api/users/5", "username": "user4"}, "assignee_updated_date": "2024-09-22T20:40:23.452000Z", "bug_tracker": "", "created_date": "2024-09-22T20:40:15.423000Z", "dimension": null, "guide_id": null, "id": 15, "labels": {"url": "http://localhost:8080/api/labels?project_id=15"}, "name": "project assigned to user4", "organization": 2, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "source_storage": {"cloud_storage_id": null, "id": 41, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 42, "location": "local"}, "task_subsets": [], "tasks": {"count": 0, "url": "http://localhost:8080/api/tasks?project_id=15"}, "updated_date": "2024-09-22T20:40:23.462000Z", "url": "http://localhost:8080/api/projects/15"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2024-07-15T15:29:04.426000Z", "dimension": "2d", "guide_id": null, "id": 14, "labels": {"url": "http://localhost:8080/api/labels?project_id=14"}, "name": "project with subsets", "organization": null, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "source_storage": {"cloud_storage_id": null, "id": 35, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 36, "location": "local"}, "task_subsets": ["Validation", "Train"], "tasks": {"count": 2, "url": "http://localhost:8080/api/tasks?project_id=14"}, "updated_date": "2024-07-15T15:34:53.175000Z", "url": "http://localhost:8080/api/projects/14"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2023-03-10T11:58:04.216000Z", "dimension": null, "guide_id": null, "id": 13, "labels": {"url": "http://localhost:8080/api/labels?project_id=13"}, "name": "project with attributes 2", "organization": null, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "source_storage": {"cloud_storage_id": null, "id": 31, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 32, "location": "local"}, "task_subsets": [], "tasks": {"count": 0, "url": "http://localhost:8080/api/tasks?project_id=13"}, "updated_date": "2023-03-10T11:58:04.216000Z", "url": "http://localhost:8080/api/projects/13"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2023-03-10T11:57:14.944000Z", "dimension": "2d", "guide_id": null, "id": 12, "labels": {"url": "http://localhost:8080/api/labels?project_id=12"}, "name": "project with attributes", "organization": null, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "source_storage": {"cloud_storage_id": null, "id": 27, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 28, "location": "local"}, "task_subsets": [], "tasks": {"count": 1, "url": "http://localhost:8080/api/tasks?project_id=12"}, "updated_date": "2023-03-10T11:57:48.747000Z", "url": "http://localhost:8080/api/projects/12"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2023-03-01T15:36:11.840000Z", "dimension": "2d", "guide_id": null, "id": 11, "labels": {"url": "http://localhost:8080/api/labels?project_id=11"}, "name": "project7", "organization": 2, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "source_storage": {"cloud_storage_id": null, "id": 21, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 22, "location": "local"}, "task_subsets": [], "tasks": {"count": 1, "url": "http://localhost:8080/api/tasks?project_id=11"}, "updated_date": "2023-03-01T15:36:37.812000Z", "url": "http://localhost:8080/api/projects/11"}, {"assignee": {"first_name": "Worker", "id": 8, "last_name": "Third", "url": "http://localhost:8080/api/users/8", "username": "worker3"}, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2023-02-10T11:42:43.192000Z", "dimension": "2d", "guide_id": null, "id": 10, "labels": {"url": "http://localhost:8080/api/labels?project_id=10"}, "name": "project 6", "organization": 2, "owner": {"first_name": "User", "id": 4, "last_name": "Third", "url": "http://localhost:8080/api/users/4", "username": "user3"}, "source_storage": {"cloud_storage_id": null, "id": 19, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 20, "location": "local"}, "task_subsets": [], "tasks": {"count": 1, "url": "http://localhost:8080/api/tasks?project_id=10"}, "updated_date": "2024-09-23T21:42:22.688000Z", "url": "http://localhost:8080/api/projects/10"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2022-12-01T12:52:42.454000Z", "dimension": "2d", "guide_id": null, "id": 8, "labels": {"url": "http://localhost:8080/api/labels?project_id=8"}, "name": "project with video data", "organization": null, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "source_storage": {"cloud_storage_id": null, "id": 13, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 14, "location": "local"}, "task_subsets": [], "tasks": {"count": 1, "url": "http://localhost:8080/api/tasks?project_id=8"}, "updated_date": "2022-12-01T12:53:34.917000Z", "url": "http://localhost:8080/api/projects/8"}, {"assignee": {"first_name": "Worker", "id": 9, "last_name": "Fourth", "url": "http://localhost:8080/api/users/9", "username": "worker4"}, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2022-09-28T12:26:25.296000Z", "dimension": null, "guide_id": null, "id": 7, "labels": {"url": "http://localhost:8080/api/labels?project_id=7"}, "name": "admin1_project", "organization": null, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "source_storage": {"cloud_storage_id": null, "id": 11, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 12, "location": "local"}, "task_subsets": [], "tasks": {"count": 0, "url": "http://localhost:8080/api/tasks?project_id=7"}, "updated_date": "2022-09-28T12:26:29.285000Z", "url": "http://localhost:8080/api/projects/7"}, {"assignee": {"first_name": "User", "id": 13, "last_name": "Tenth", "url": "http://localhost:8080/api/users/13", "username": "user10"}, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2022-09-28T12:15:50.768000Z", "dimension": null, "guide_id": null, "id": 6, "labels": {"url": "http://localhost:8080/api/labels?project_id=6"}, "name": "user1_project", "organization": null, "owner": {"first_name": "User", "id": 2, "last_name": "First", "url": "http://localhost:8080/api/users/2", "username": "user1"}, "source_storage": {"cloud_storage_id": null, "id": 9, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 10, "location": "local"}, "task_subsets": [], "tasks": {"count": 0, "url": "http://localhost:8080/api/tasks?project_id=6"}, "updated_date": "2022-09-28T12:25:54.563000Z", "url": "http://localhost:8080/api/projects/6"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2022-09-22T14:21:53.791000Z", "dimension": "2d", "guide_id": null, "id": 5, "labels": {"url": "http://localhost:8080/api/labels?project_id=5"}, "name": "project5", "organization": 2, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "source_storage": {"cloud_storage_id": null, "id": 5, "location": "local"}, "status": "annotation", "target_storage": {"cloud_storage_id": null, "id": 6, "location": "local"}, "task_subsets": [], "tasks": {"count": 1, "url": "http://localhost:8080/api/tasks?project_id=5"}, "updated_date": "2022-09-28T12:26:49.493000Z", "url": "http://localhost:8080/api/projects/5"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2022-06-08T08:32:45.521000Z", "dimension": "2d", "guide_id": null, "id": 4, "labels": {"url": "http://localhost:8080/api/labels?project_id=4"}, "name": "project4", "organization": 2, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "source_storage": null, "status": "annotation", "target_storage": null, "task_subsets": [], "tasks": {"count": 1, "url": "http://localhost:8080/api/tasks?project_id=4"}, "updated_date": "2023-02-10T11:50:18.436000Z", "url": "http://localhost:8080/api/projects/4"}, {"assignee": {"first_name": "User", "id": 19, "last_name": "Fifth", "url": "http://localhost:8080/api/users/19", "username": "user5"}, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2022-03-28T13:05:24.659000Z", "dimension": null, "guide_id": null, "id": 3, "labels": {"url": "http://localhost:8080/api/labels?project_id=3"}, "name": "project 3", "organization": 2, "owner": {"first_name": "User", "id": 3, "last_name": "Second", "url": "http://localhost:8080/api/users/3", "username": "user2"}, "source_storage": null, "status": "annotation", "target_storage": null, "task_subsets": [], "tasks": {"count": 0, "url": "http://localhost:8080/api/tasks?project_id=3"}, "updated_date": "2022-03-28T13:06:09.283000Z", "url": "http://localhost:8080/api/projects/3"}, {"assignee": {"first_name": "User", "id": 3, "last_name": "Second", "url": "http://localhost:8080/api/users/3", "username": "user2"}, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2021-12-14T19:52:37.278000Z", "dimension": "2d", "guide_id": null, "id": 2, "labels": {"url": "http://localhost:8080/api/labels?project_id=2"}, "name": "project2", "organization": 2, "owner": {"first_name": "User", "id": 10, "last_name": "Seventh", "url": "http://localhost:8080/api/users/10", "username": "user7"}, "source_storage": {"cloud_storage_id": 3, "id": 3, "location": "cloud_storage"}, "status": "annotation", "target_storage": {"cloud_storage_id": 3, "id": 1, "location": "cloud_storage"}, "task_subsets": ["Train"], "tasks": {"count": 1, "url": "http://localhost:8080/api/tasks?project_id=2"}, "updated_date": "2022-06-30T08:56:45.601000Z", "url": "http://localhost:8080/api/projects/2"}, {"assignee": {"first_name": "User", "id": 20, "last_name": "Sixth", "url": "http://localhost:8080/api/users/20", "username": "user6"}, "assignee_updated_date": null, "bug_tracker": "", "created_date": "2021-12-14T19:46:37.969000Z", "dimension": "2d", "guide_id": null, "id": 1, "labels": {"url": "http://localhost:8080/api/labels?project_id=1"}, "name": "project1", "organization": null, "owner": {"first_name": "User", "id": 10, "last_name": "Seventh", "url": "http://localhost:8080/api/users/10", "username": "user7"}, "source_storage": null, "status": "annotation", "target_storage": null, "task_subsets": [], "tasks": {"count": 1, "url": "http://localhost:8080/api/tasks?project_id=1"}, "updated_date": "2022-11-03T13:57:25.895000Z", "url": "http://localhost:8080/api/projects/1"}]}