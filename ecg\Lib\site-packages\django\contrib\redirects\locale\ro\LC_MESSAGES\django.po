# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <razvan.s<PERSON><PERSON><PERSON>@gmail.com>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-07-15 11:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Romanian (http://www.transifex.com/django/django/language/"
"ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?"
"2:1));\n"

msgid "Redirects"
msgstr "Redirecționări"

msgid "site"
msgstr "site"

msgid "redirect from"
msgstr "redirecționat de la "

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Trebuie sa fie o cale absoluta, exeptand numele domeniului. Exemplu: “/"
"events/search/”."

msgid "redirect to"
msgstr "redirecționat către"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with "
"“http://”."
msgstr ""
"Poate fi cale absolută (ca mai sus) sau link URL complet, începând cu "
"“http://”."

msgid "redirect"
msgstr "redirecționare"

msgid "redirects"
msgstr "redirecționări"
