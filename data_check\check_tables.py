from sql_helper import <PERSON>sqlHelper

def check_table_structures():
    db = MysqlHelper("default")
    try:
        conn = db.get_con()
        cursor = conn.cursor()
        
        # 检查t_patient_ecg_i表的所有字段
        print("\n=== t_patient_ecg_i表结构 ===")
        cursor.execute("SHOW COLUMNS FROM t_patient_ecg_i")
        columns = cursor.fetchall()
        print("字段列表:")
        for col in columns:
            print(f"字段名：{col[0]}, 类型：{col[1]}, 是否可空：{col[2]}, 键：{col[3]}")
            
        # 检查t_patient表的所有字段
        print("\n=== t_patient表结构 ===")
        cursor.execute("SHOW COLUMNS FROM t_patient")
        columns = cursor.fetchall()
        print("字段列表:")
        for col in columns:
            print(f"字段名：{col[0]}, 类型：{col[1]}, 是否可空：{col[2]}, 键：{col[3]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"查询出错：{str(e)}")

if __name__ == "__main__":
    check_table_structures() 