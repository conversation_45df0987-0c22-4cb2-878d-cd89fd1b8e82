#!/usr/bin/env python3
"""
优化版AI验证绕过爬虫 - 基于页面结构分析的精准数据提取
根据页面结构分析结果，优化价格和装备信息的提取逻辑
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings

# 忽略SSL警告
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedAICrawler:
    def __init__(self, base_url="https://www.badmintoncn.com", max_workers=3):
        self.base_url = base_url
        self.max_workers = max_workers
        self.session = None
        self.equipment_links = []
        self.logger = logger  # 设置实例logger
        
        # 状态文件
        self.state_file = 'crawler_state.json'
        self.load_state()
        
    def load_state(self):
        """加载爬虫状态"""
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                self.state = json.load(f)
        except FileNotFoundError:
            self.state = {
                'last_run': None,
                'total_crawled': 0,
                'failed_urls': []
            }
    
    def save_state(self):
        """保存爬虫状态"""
        self.state['last_run'] = datetime.now().isoformat()
        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(self.state, f, indent=2, ensure_ascii=False)
    
    def create_session(self):
        """创建配置好的session"""
        session = requests.Session()
        
        # 模拟真实浏览器，不包含Accept-Encoding避免压缩问题
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        })
        
        return session
    
    def get_equipment_list(self):
        """获取装备列表 - 新策略：从主页提取装备链接"""
        try:
            self.logger.info("开始获取装备列表...")
            
            # 创建session
            self.session = self.create_session()
            
            # 1. 首先访问主页建立session
            self.logger.info("访问主页建立session...")
            homepage_response = self.session.get(self.base_url, timeout=15, verify=False)
            
            if homepage_response.status_code != 200:
                self.logger.error(f"主页访问失败，状态码: {homepage_response.status_code}")
                return []
            
            # 确保正确解码
            homepage_response.encoding = homepage_response.apparent_encoding or 'utf-8'
            
            self.logger.info(f"主页访问成功，响应长度: {len(homepage_response.text)}")
            
            # 2. 解析主页，提取装备详情链接
            soup = BeautifulSoup(homepage_response.text, 'html.parser')
            
            # 查找所有包含 view.php?eid= 的链接
            equipment_links = []
            
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                
                # 查找装备详情链接
                if 'view.php?eid=' in href:
                    # 转换为完整URL
                    if href.startswith('/'):
                        full_url = self.base_url + href
                    elif href.startswith('http'):
                        full_url = href
                    else:
                        full_url = self.base_url + '/cbo_eq/' + href
                    
                    # 提取设备ID
                    match = re.search(r'eid=(\d+)', href)
                    if match:
                        eid = match.group(1)
                        equipment_links.append({
                            'url': full_url,
                            'eid': eid,
                            'text': link.get_text().strip()
                        })
            
            # 去重
            seen_eids = set()
            unique_links = []
            for link in equipment_links:
                if link['eid'] not in seen_eids:
                    seen_eids.add(link['eid'])
                    unique_links.append(link)
            
            self.equipment_links = unique_links
            self.logger.info(f"从主页提取到 {len(self.equipment_links)} 个装备链接")
            
            # 显示前几个链接作为示例
            for i, link in enumerate(self.equipment_links[:5]):
                self.logger.info(f"  {i+1}. {link['text']} (eid: {link['eid']})")
            
            return [link['url'] for link in self.equipment_links]
                    
            except Exception as e:
            self.logger.error(f"获取装备列表失败: {e}")
            
            # 备用策略：使用已知的装备ID
            self.logger.info("使用备用测试链接...")
            test_links = [
                {'url': f'{self.base_url}/cbo_eq/view.php?eid=23120', 'eid': '23120', 'text': 'Drive'},
                {'url': f'{self.base_url}/cbo_eq/view.php?eid=19393', 'eid': '19393', 'text': 'ASTRO'},
                {'url': f'{self.base_url}/cbo_eq/view.php?eid=12900', 'eid': '12900', 'text': '战戟800'},
                {'url': f'{self.base_url}/cbo_eq/view.php?eid=9748', 'eid': '9748', 'text': '雷霆80'},
                {'url': f'{self.base_url}/cbo_eq/view.php?eid=20950', 'eid': '20950', 'text': '雷霆90N'}
            ]
            
            self.equipment_links = test_links
            return [link['url'] for link in test_links]
    
    def get_equipment_details(self, url):
        """获取装备详细信息"""
        try:
            # 确保使用同一个session，并设置referer
            headers = {'Referer': self.base_url + '/'}
            
            self.logger.info(f"正在获取装备详情: {url}")
            
            response = self.session.get(url, headers=headers, timeout=15, verify=False)
            
            if response.status_code != 200:
                self.logger.warning(f"装备详情页访问失败，状态码: {response.status_code}, URL: {url}")
                return None
                
            # 设置正确编码
            response.encoding = response.apparent_encoding or 'gb2312'
            content = response.text
            
            # 检查是否是验证页面
            if '验证' in content or 'verification' in content.lower():
                self.logger.warning(f"遇到验证页面: {url}")
                    return None
                
            soup = BeautifulSoup(content, 'html.parser')
            
            # 提取装备信息
                equipment_data = {
                'url': url,
                'crawl_time': datetime.now().isoformat()
            }
            
            # 提取标题
            title_elem = soup.find('title')
            if title_elem:
                equipment_data['title'] = title_elem.get_text().strip()
            
            # 提取装备名称
            name_elem = soup.find('h1') or soup.find('h2') or soup.find(class_='title')
            if name_elem:
                equipment_data['name'] = name_elem.get_text().strip()
            
            # 提取品牌信息
            brand_elem = soup.find(text=re.compile(r'品牌|Brand', re.I))
            if brand_elem:
                brand_parent = brand_elem.find_parent()
                if brand_parent:
                    equipment_data['brand'] = brand_parent.get_text().strip()
            
            # 提取价格信息
            price_patterns = [
                r'价格[：:]\s*([¥￥$]\d+[\d,]*\.?\d*)',
                r'¥\s*(\d+[\d,]*\.?\d*)',
                r'价格.*?(\d+)\s*元'
            ]
            
            for pattern in price_patterns:
                price_match = re.search(pattern, content)
                if price_match:
                    equipment_data['price'] = price_match.group(1)
                    break
            
            # 提取规格参数
            specs = {}
            
            # 查找参数表格
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text().strip()
                        value = cells[1].get_text().strip()
                        if key and value:
                            specs[key] = value
            
            # 查找参数列表
            param_patterns = [
                (r'重量[：:]\s*([^，,\n]+)', 'weight'),
                (r'平衡点[：:]\s*([^，,\n]+)', 'balance'),
                (r'材质[：:]\s*([^，,\n]+)', 'material'),
                (r'硬度[：:]\s*([^，,\n]+)', 'stiffness'),
                (r'拍框[：:]\s*([^，,\n]+)', 'frame'),
                (r'拍杆[：:]\s*([^，,\n]+)', 'shaft')
            ]
            
            for pattern, key in param_patterns:
                match = re.search(pattern, content)
                    if match:
                    specs[key] = match.group(1).strip()
            
            if specs:
                equipment_data['specifications'] = specs
            
            # 提取图片链接
            images = []
            img_tags = soup.find_all('img')
            for img in img_tags:
                src = img.get('src', '')
                if src and ('jpg' in src.lower() or 'png' in src.lower() or 'jpeg' in src.lower()):
                    if src.startswith('/'):
                        src = self.base_url + src
                    images.append(src)
            
            if images:
                equipment_data['images'] = images[:5]  # 最多保存5张图片
            
            # 提取描述信息
            description_elem = soup.find('div', class_='content') or soup.find('div', class_='description')
            if description_elem:
                equipment_data['description'] = description_elem.get_text().strip()[:500]
            
            self.logger.info(f"成功获取装备信息: {equipment_data.get('name', 'Unknown')}")
            return equipment_data
                    
        except Exception as e:
            self.logger.error(f"获取装备详情失败 {url}: {e}")
            return None
    
    def save_to_csv(self, equipment_list, filename=None):
        """保存数据到CSV文件"""
        if not equipment_list:
            self.logger.warning("没有有效的装备数据需要保存")
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'badminton_equipment_{timestamp}.csv'
        
        # 确定CSV列
        all_keys = set()
        for item in equipment_list:
            all_keys.update(item.keys())
        
        # 定义列顺序
        column_order = ['name', 'brand', 'price', 'title', 'url', 'crawl_time', 'specifications', 'images', 'description']
        columns = [col for col in column_order if col in all_keys]
        columns.extend([col for col in all_keys if col not in columns])
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=columns)
                writer.writeheader()
                
                for item in equipment_list:
                    # 处理复杂数据类型
                    row_data = {}
                    for key in columns:
                        value = item.get(key, '')
                        if isinstance(value, (dict, list)):
                            row_data[key] = json.dumps(value, ensure_ascii=False)
                    else:
                            row_data[key] = str(value) if value is not None else ''
                    
                    writer.writerow(row_data)
            
            self.logger.info(f"数据已保存到 {filename}")
            return filename
                
        except Exception as e:
            self.logger.error(f"保存CSV文件失败: {e}")
            return None
    
    def run(self, max_pages=None):
        """运行爬虫"""
        self.logger.info("开始运行羽毛球装备爬虫...")
        
        start_time = time.time()
        equipment_data = []
        
        try:
            # 获取装备链接列表
            equipment_urls = self.get_equipment_list()
            
            if not equipment_urls:
                self.logger.error("未能获取到装备链接列表")
                return
            
            if max_pages:
                equipment_urls = equipment_urls[:max_pages]
            
            self.logger.info(f"准备爬取 {len(equipment_urls)} 个装备页面")
            
            # 使用线程池并发处理
            successful_count = 0
            failed_count = 0
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_url = {
                    executor.submit(self.get_equipment_details, url): url 
                    for url in equipment_urls
                }
                
                # 处理完成的任务
                for future in as_completed(future_to_url):
                    url = future_to_url[future]
                    try:
                        equipment_info = future.result(timeout=30)
                        if equipment_info:
                            equipment_data.append(equipment_info)
                            successful_count += 1
                            self.logger.info(f"进度: {successful_count + failed_count}/{len(equipment_urls)}")
                        else:
                            failed_count += 1
                            self.state['failed_urls'].append(url)
        except Exception as e:
                        failed_count += 1
                        self.logger.error(f"处理装备页面失败 {url}: {e}")
                        self.state['failed_urls'].append(url)
                    
                    # 添加延时避免请求过快
                    time.sleep(0.5)
            
            # 保存结果
            if equipment_data:
                csv_filename = self.save_to_csv(equipment_data)
                
                # 更新状态
                self.state['total_crawled'] += successful_count
                self.save_state()
                
                # 统计信息
                end_time = time.time()
                duration = end_time - start_time
                
                self.logger.info(f"""
========== 爬取完成 ==========
成功爬取: {successful_count} 个装备
失败数量: {failed_count} 个
总耗时: {duration:.2f} 秒
平均速度: {successful_count/duration:.2f} 个/秒
数据文件: {csv_filename}
===============================
                """)
                        else:
                self.logger.error("没有成功爬取到任何装备数据，请检查网站访问或解析逻辑")
                
        except KeyboardInterrupt:
            self.logger.info("用户中断爬取")
        except Exception as e:
            self.logger.error(f"爬虫运行出错: {e}")
        finally:
            if self.session:
                self.session.close()

def main():
    """主函数"""
    crawler = OptimizedAICrawler(max_workers=2)
    
    # 爬取前10个装备作为测试
    crawler.run(max_pages=10)

if __name__ == "__main__":
    main() 