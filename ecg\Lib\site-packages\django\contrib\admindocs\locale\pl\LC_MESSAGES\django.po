# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# angularcircle, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2016,2019
# <AUTHOR> <EMAIL>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2021
# c10516f0462e552b4c3672569f0745a7_cc5cca2 <841826256cd8f47d0e443806a8e56601_19204>, 2014
# <AUTHOR> <EMAIL>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-04-01 09:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Polish (http://www.transifex.com/django/django/language/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n"
"%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n"
"%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

msgid "Administrative Documentation"
msgstr "Dokumentacja administracyjna"

msgid "Home"
msgstr "Strona główna"

msgid "Documentation"
msgstr "Dokumentacja"

msgid "Bookmarklets"
msgstr "Zakładki"

msgid "Documentation bookmarklets"
msgstr "Zakładki Dokumentacji"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Aby zainstalować skryptozakładkę, przeciągnij jej link do paska zakładek lub "
"kliknij prawym przyciskiem na link i wybierz opcję zapisania do zakładek. Od "
"tego momentu możesz użyć tak zapisanej zakładki na dowolnej podstronie tego "
"serwisu."

msgid "Documentation for this page"
msgstr "Dokumentacja dla tej strony"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Przekierowuje z dowolnej strony do dokumentacji dla widoku, który ją "
"generuje."

msgid "Tags"
msgstr "Tagi"

msgid "List of all the template tags and their functions."
msgstr "Lista wszystkich templatetagów i ich funkcji."

msgid "Filters"
msgstr "Filtry"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtry to akcje, które mogą być stosowane w odniesieniu do zmiennych w "
"szablonie, aby zmienić wyjście."

msgid "Models"
msgstr "Modele"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modele to opisy wszystkich obiektów w systemie i związanych z nimi pól. "
"Każdy model ma listę pól, które mogą być dostępne jako zmienne szablonu"

msgid "Views"
msgstr "Widoki"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Każda strona dostępna publicznie jest generowana przez widok. Widok określa, "
"który szablon jest używany do wygenerowania strony i które obiekty są "
"dostępne dla tego szablonu."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Narzędzia do twojej przeglądarki, aby uzyskać szybki dostęp do "
"funkcjonalności admina."

msgid "Please install docutils"
msgstr "Zainstaluj docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"System dokumentacji admina wymaga Pythonowej biblioteki <a href=\"%(link)s"
"\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Poproś swoich administratorów, aby zainstalowali <a href=\"%(link)s"
"\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Pola"

msgid "Field"
msgstr "Pole"

msgid "Type"
msgstr "Typ"

msgid "Description"
msgstr "Opis"

msgid "Methods with arguments"
msgstr "Metody z argumentami"

msgid "Method"
msgstr "Metoda"

msgid "Arguments"
msgstr "Argumenty"

msgid "Back to Model documentation"
msgstr "Powrót do dokumentacji modelu"

msgid "Model documentation"
msgstr "Dokumentacja modelu"

msgid "Model groups"
msgstr "Grupy modelu"

msgid "Templates"
msgstr "Szablony"

#, python-format
msgid "Template: %(name)s"
msgstr "Szablon: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Szablon: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Ścieżki wyszukiwania dla szablonu  <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(nie istnieje)"

msgid "Back to Documentation"
msgstr "Powrót do dokumentacji"

msgid "Template filters"
msgstr "Filtry szablonu"

msgid "Template filter documentation"
msgstr "Dokumentacja filtrów szablonu"

msgid "Built-in filters"
msgstr "Wbudowane filtry"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Aby użyć tych filtrów, umieść <code>%(code)s</code> w swoim szablonie przed "
"użyciem filtra."

msgid "Template tags"
msgstr "Tagi szablonu"

msgid "Template tag documentation"
msgstr "Dokumentacja tagów szablonu"

msgid "Built-in tags"
msgstr "Wbudowane tagi"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Aby użyć tych tagów, umieść <code>%(code)s</code> w swoim szablonie przed "
"użyciem taga."

#, python-format
msgid "View: %(name)s"
msgstr "Widok: %(name)s"

msgid "Context:"
msgstr "Kontekst:"

msgid "Templates:"
msgstr "Szablony:"

msgid "Back to View documentation"
msgstr "Powrót do dokumentacji widoku"

msgid "View documentation"
msgstr "Zobacz dokumentację"

msgid "Jump to namespace"
msgstr "Przejdź do przestrzeni nazw"

msgid "Empty namespace"
msgstr "Pusta przestrzeń nazw"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Widoki według przestrzeni nazw %(name)s"

msgid "Views by empty namespace"
msgstr "Widoki po pustej przestrzeni nazw"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Funkcja widoku: <code>%(full_name)s</code>. Nazwa: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filtr:"

msgid "view:"
msgstr "widok:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Nie znaleziono aplikacji %(app_label)r"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r nie został znaleziony w aplikacji %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "powiązany obiekt `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "powiązane obiekty `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "wszystkie %s"

#, python-format
msgid "number of %s"
msgstr "liczba %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s nie jest obiektem urlpattern"
