# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON>, 2015
# Pet<PERSON> <<EMAIL>>, 2019
# <PERSON>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-05-12 20:01+0000\n"
"Last-Translator: Transifex Bot <>\n"
"Language-Team: Swedish (http://www.transifex.com/django/django/language/"
"sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "PostgreSQL-tillägg"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Element %(nth)s i arrayen gick inte att validera:"

msgid "Nested arrays must have the same length."
msgstr "Flerdimensionella arrayer måste vara av samma längd"

msgid "Map of strings to strings/nulls"
msgstr "Funktion från sträng till sträng/null"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr ""

msgid "Could not load JSON data."
msgstr "Kunde inte ladda JSON-data."

msgid "Input must be a JSON dictionary."
msgstr "Input måste vara en JSON-dictionary."

msgid "Enter two valid values."
msgstr "Fyll i två giltiga värden"

msgid "The start of the range must not exceed the end of the range."
msgstr "Starten av intervallet kan inte vara större än slutet av intervallet."

msgid "Enter two whole numbers."
msgstr "Fyll i två heltal."

msgid "Enter two numbers."
msgstr "Fyll i två tal."

msgid "Enter two valid date/times."
msgstr "Fyll i två giltiga datum/tider."

msgid "Enter two valid dates."
msgstr "Fyll i två giltiga datum."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Listan innehåller %(show_value)d objekt, men kan inte innehålla fler än "
"%(limit_value)d."
msgstr[1] ""
"Listan innehåller %(show_value)d objekt, men kan inte innehålla fler än "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Listan innehåller %(show_value)d objekt, men kan inte innehålla färre än "
"%(limit_value)d."
msgstr[1] ""
"Listan innehåller %(show_value)d objekt, men kan inte innehålla färre än "
"%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Några nycklar saknades: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Några okända okända nycklar skickades: %(keys)s"

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr ""
"Säkerställ att denna intervall är mindre än eller lika med %(limit_value)s."

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr ""
"Säkerställ att denna intervall är större än eller lika med %(limit_value)s."
