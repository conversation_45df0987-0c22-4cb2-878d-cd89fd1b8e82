from sqlalchemy import create_engine
import pymysql
import pandas as pd
import json
from tabulate import tabulate
from collections import defaultdict
from datetime import datetime, timedelta
import os

def get_data(table_date=None, phone=None, specific_time=None):
    """
    从阿里云RDS数据库获取ECG数据
    Args:
        table_date: 指定日期，默认为当天
        phone: 指定手机号
        specific_time: 指定具体时间（将获取前后1分钟的数据）
    Returns:
        DataFrame: 包含ECG数据的DataFrame
    """
    if table_date is None:
        table_date = datetime.now().date()
        
    # 生成源表名（动态）
    source_table = f"t_data_ecg_{table_date.strftime('%Y%m%d')}"
    
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 执行关联查询
        query = f"""
        SELECT 
            t1.*,
            t2.id as user_id,
            t2.union_id,
            t2.age,
            t2.gender,
            t2.phone
        FROM {source_table} t1
        JOIN backend_test.jahr_customer_user t2 ON t1.union_id = t2.union_id
        WHERE t2.deleted = 0
        AND t2.phone = %s
        """
        
        if specific_time:
            query += """ 
            AND t1.create_time BETWEEN 
                DATE_SUB(%s, INTERVAL 1 MINUTE) 
                AND DATE_ADD(%s, INTERVAL 1 MINUTE)
            """
            cursor.execute(query, (phone, specific_time, specific_time))
        else:
            cursor.execute(query, (phone,))
        
        tables = cursor.fetchall()
        
        # 获取列名
        columns = ['user_id', 'union_id', 'age', 'gender', 'phone']
        
        # 将结果转换为DataFrame
        df = pd.DataFrame(tables, columns=columns)
        
        if len(df) > 0:
            print("\n数据基本信息:")
            print(f"总记录数: {len(df)}")
            print("\n年龄分布:")
            print(df['age'].describe())
            print("\n性别分布:")
            print(df['gender'].map({1: '男', 2: '女'}).value_counts())
            print("\n数据示例:")
            print(df.head())
            
            return df
            
        return pd.DataFrame()
        
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return pd.DataFrame()
        
    finally:
        cursor.close()
        connection.close()

def list_all_tables():
    """
    列出数据库中的所有表
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 查询所有表名
        query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'backend_test'
        ORDER BY table_name
        """
        
        cursor.execute(query)
        tables = cursor.fetchall()
        
        print("\n所有表:")
        for i, table in enumerate(tables, 1):
            print(f"{i}. {table[0]}")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        cursor.close()
        connection.close()

def check_user_tables():
    """检查用户相关表的结构"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        tables = ['jahr_customer_user', 't_user', 't_user_wx_relate']
        
        for table in tables:
            print(f"\n{table} 表结构:")
            cursor.execute(f"SHOW COLUMNS FROM {table}")
            columns = cursor.fetchall()
            for col in columns:
                print(f"{col[0]}: {col[1]}")
            
            # 查看示例数据
            print(f"\n{table} 示例数据:")
            cursor.execute(f"SELECT * FROM {table} LIMIT 1")
            data = cursor.fetchone()
            if data:
                cursor.execute(f"SHOW COLUMNS FROM {table}")
                cols = [col[0] for col in cursor.fetchall()]
                for i, col in enumerate(cols):
                    print(f"{col}: {data[i]}")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        cursor.close()
        connection.close()

def check_phone(phone):
    """
    检查手机号是否存在于数据库中
    Args:
        phone: 要查询的手机号
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 查询 jahr_customer_user 表
        query = """
        SELECT id, union_id, phone, user_name, gender, age, deleted
        FROM jahr_customer_user
        WHERE phone = %s
        """
        
        cursor.execute(query, (phone,))
        results = cursor.fetchall()
        
        if results:
            print("\n在 jahr_customer_user 表中找到以下记录：")
            for row in results:
                print(f"ID: {row[0]}")
                print(f"union_id: {row[1]}")
                print(f"手机号: {row[2]}")
                print(f"用户名: {row[3]}")
                print(f"性别: {'男' if row[4]==1 else '女' if row[4]==2 else '未知'}")
                print(f"年龄: {row[5]}")
                print(f"是否删除: {row[6]}")
        else:
            print(f"\n在 jahr_customer_user 表中未找到手机号 {phone} 的记录")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        cursor.close()
        connection.close()

def query_by_union_id(union_id, target_date):
    """根据union_id和日期查询数据"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 构造表名
        table_date = target_date.strftime("%Y%m%d")
        table_name = f"t_data_ecg_{table_date}"
        print(f"尝试查询表: {table_name}")
        
        # 2. 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test' 
            AND table_name = '{table_name}'
        """)
        if not cursor.fetchone()[0]:
            print(f"表 {table_name} 不存在")
            return
            
        # 3. 执行查询
        query = f"""
            SELECT id, create_time, ecg_analysis 
            FROM {table_name} 
            WHERE union_id = %s
            ORDER BY create_time
        """
        cursor.execute(query, (union_id,))
        results = cursor.fetchall()
        
        if results:
            print(f"\n找到 {len(results)} 条记录:")
            for record in results:
                print(f"\nID: {record[0]}")
                print(f"时间: {record[1]}")
                print(f"ECG数据摘要: {record[2][:100]}...")
        else:
            print("未找到匹配记录")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        cursor.close()
        connection.close()

def check_ecg_table_contents():
    """导出全天数据到Excel"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 查询全天的记录
        cursor.execute("""
            SELECT id, union_id, create_time, ecg_analysis 
            FROM t_data_ecg_20250128 
            WHERE create_time >= '2025-01-28 00:00:00' 
            AND create_time < '2025-01-29 00:00:00'
            ORDER BY create_time
        """)
        
        results = cursor.fetchall()
        print(f"\n找到 {len(results)} 条记录")
        
        # 转换为DataFrame并保存到Excel
        df = pd.DataFrame(results, columns=['id', 'union_id', 'create_time', 'ecg_analysis'])
        excel_file = 'ecg_data_0128_full_day.xlsx'
        df.to_excel(excel_file, index=False)
        print(f"数据已保存到文件: {excel_file}")
        
        # 显示时间分布统计
        print("\n每小时数据分布:")
        hourly_stats = df.groupby(df['create_time'].dt.hour).size()
        for hour, count in hourly_stats.items():
            print(f"{hour:02d}:00 - {hour:02d}:59: {count}条记录")
            
    finally:
        cursor.close()
        connection.close()

def deep_investigation():
    """深度排查数据问题"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 目标用户信息
        target_phone = "15911090763"
        target_union = "13001213323"
        
        # 1. 验证用户基本信息
        print("\n🔍 用户基本信息验证:")
        cursor.execute("""
            SELECT id, union_id, deleted 
            FROM jahr_customer_user 
            WHERE phone = %s
        """, (target_phone,))
        user_id, union_id, deleted = cursor.fetchone()
        print(f"用户ID: {user_id} | union_id: {union_id} | 是否删除: {'是' if deleted else '否'}")
        
        # 2. 检查设备关联
        print("\n🔌 设备关联检查:")
        cursor.execute("""
            SELECT device_sn 
            FROM t_user_wx_relate 
            WHERE user_id = %s
        """, (user_id,))
        device_sn = cursor.fetchone()[0] if cursor.rowcount else None
        print(f"关联设备号: {device_sn or '无'}")
        
        # 3. 检查用户直接数据
        print("\n❤️ 用户直接数据检查:")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM t_data_ecg_20250206 
            WHERE union_id = %s
        """, (target_union,))
        user_count = cursor.fetchone()[0]
        print(f"用户直接数据记录数: {user_count}")
        
        # 4. 检查设备数据
        if device_sn:
            device_union = f"M-100000-{device_sn}"
            print(f"\n📡 设备数据检查 ({device_union}):")
            cursor.execute("""
                SELECT COUNT(*), MAX(create_time)
                FROM t_data_ecg_20250206 
                WHERE union_id = %s
            """, (device_union,))
            dev_count, latest_time = cursor.fetchone()
            print(f"设备数据记录数: {dev_count}")
            print(f"最新记录时间: {latest_time or '无'}")
            
            # 查看具体记录
            if dev_count > 0:
                cursor.execute("""
                    SELECT create_time, LENGTH(ecg_analysis) 
                    FROM t_data_ecg_20250206 
                    WHERE union_id = %s
                    ORDER BY create_time DESC
                    LIMIT 3
                """, (device_union,))
                print("\n最近3条设备数据:")
                for i, (time, length) in enumerate(cursor.fetchall(), 1):
                    print(f"{i}. {time} | 数据长度: {length}")
        
        # 5. 时间范围验证
        print("\n⏰ 时间范围验证:")
        cursor.execute("""
            SELECT MIN(create_time), MAX(create_time) 
            FROM t_data_ecg_20250206
        """)
        min_time, max_time = cursor.fetchone()
        print(f"全表时间范围: {min_time} 至 {max_time}")
        
    finally:
        cursor.close()
        connection.close()

def check_device_data(phone):
    """检查设备关联数据"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 获取用户基本信息
        cursor.execute("""
            SELECT id, union_id, phone, user_name 
            FROM jahr_customer_user 
            WHERE phone = %s
        """, (phone,))
        user_info = cursor.fetchone()
        
        if user_info:
            print(f"\n✅ 用户基本信息:")
            print(f"用户ID: {user_info[0]}")
            print(f"当前union_id: {user_info[1]}")
            print(f"手机号: {user_info[2]}")
            print(f"用户名: {user_info[3]}")
            
            # 2. 检查设备关联表（使用正确的字段）
            print("\n🔍 检查设备关联信息:")
            cursor.execute("""
                SELECT device_id  # 根据错误提示，使用正确的字段名
                FROM t_user_wx_relate 
                WHERE user_id = %s
            """, (user_info[0],))
            device_info = cursor.fetchone()
            
            if device_info:
                print(f"关联设备号: {device_info[0]}")
                # 3. 查询设备数据
                device_union = f"M-100000-{device_info[0]}"
                print(f"\n🔎 查询设备union_id: {device_union}")
                
                cursor.execute("""
                    SELECT create_time 
                    FROM t_data_ecg_20250206 
                    WHERE union_id = %s
                    ORDER BY create_time DESC
                    LIMIT 5
                """, (device_union,))
                
                records = cursor.fetchall()
                if records:
                    print(f"\n✅ 找到 {len(records)} 条设备数据:")
                    for i, (time,) in enumerate(records, 1):
                        print(f"{i}. 记录时间: {time}")
                else:
                    print("\n❌ 未找到设备相关数据")
            else:
                print("该用户未关联任何设备")
        else:
            print(f"\n❌ 未找到手机号 {phone} 的用户信息")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        cursor.close()
        connection.close()

def query_by_device(phone):
    """通过设备关联查询ECG数据"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 获取用户设备号（使用正确的字段名）
        cursor.execute("""
            SELECT t2.device_sn  # 根据表结构使用正确的字段名
            FROM jahr_customer_user t1
            JOIN t_user_wx_relate t2 ON t1.id = t2.user_id
            WHERE t1.phone = %s
        """, (phone,))
        result = cursor.fetchone()
        
        if result and result[0]:
            device_sn = result[0]
            # 2. 构造设备union_id
            device_union = f"M-100000-{device_sn}"
            print(f"\n设备union_id: {device_union}")
            
            # 3. 查询设备数据
            cursor.execute("""
                SELECT create_time, ecg_analysis
                FROM t_data_ecg_20250206
                WHERE union_id = %s
                ORDER BY create_time DESC
                LIMIT 10
            """, (device_union,))
            
            records = cursor.fetchall()
            if records:
                print(f"\n找到 {len(records)} 条ECG记录:")
                for i, (time, data) in enumerate(records, 1):
                    print(f"{i}. {time} | 数据长度: {len(data)}")
            else:
                print("未找到设备相关数据")
        else:
            print("用户未关联设备")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        cursor.close()
        connection.close()

def direct_query(phone, target_time):
    """直接查询ECG数据"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 直接关联查询
        query = """
        SELECT t1.* 
        FROM t_data_ecg_20250206 t1
        JOIN (
            SELECT union_id 
            FROM jahr_customer_user 
            WHERE phone = %s 
            AND deleted = 0
        ) t2 ON t1.union_id = t2.union_id
        WHERE t1.create_time BETWEEN 
            DATE_SUB(%s, INTERVAL 1 MINUTE) 
            AND DATE_ADD(%s, INTERVAL 1 MINUTE)
        """
        
        # 2. 执行查询
        cursor.execute(query, (phone, target_time, target_time))
        results = cursor.fetchall()
        
        # 3. 获取列名
        cursor.execute("SHOW COLUMNS FROM t_data_ecg_20250206")
        columns = [col[0] for col in cursor.fetchall()]
        
        if results:
            df = pd.DataFrame(results, columns=columns)
            print(f"\n找到 {len(df)} 条记录:")
            print(df[['create_time', 'ecg_analysis']].to_string(index=False))
        else:
            print("\n未找到匹配记录")
            # 检查关联是否存在
            cursor.execute("""
                SELECT 1 
                FROM jahr_customer_user 
                WHERE phone = %s 
                AND deleted = 0
                LIMIT 1
            """, (phone,))
            if not cursor.fetchone():
                print("警告：该手机号不存在或已被删除")
            
    finally:
        cursor.close()
        connection.close()

def analyze_ecg_table():
    """分析ECG表数据分布"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 统计不同union_id的记录数
        print("\n用户数据分布统计:")
        cursor.execute("""
            SELECT union_id, COUNT(*) as record_count
            FROM t_data_ecg_20250206
            GROUP BY union_id
            ORDER BY record_count DESC
            LIMIT 20
        """)
        print("\nTop 20用户记录数:")
        for i, (uid, count) in enumerate(cursor.fetchall(), 1):
            print(f"{i}. {uid}: {count} 条记录")
            
        # 2. 检查目标用户是否存在
        target_union = "CUSTOMER18787097395565117445439"
        cursor.execute("""
            SELECT create_time 
            FROM t_data_ecg_20250206
            WHERE union_id = %s
            ORDER BY create_time DESC
            LIMIT 1
        """, (target_union,))
        last_record = cursor.fetchone()
        if last_record:
            print(f"\n用户 {target_union} 最新记录时间: {last_record[0]}")
        else:
            print(f"\n用户 {target_union} 在表中无记录")
            
        # 3. 检查设备用户数据
        print("\n设备用户数据示例:")
        cursor.execute("""
            SELECT union_id, create_time
            FROM t_data_ecg_20250206
            WHERE union_id LIKE 'M-100000-%'
            ORDER BY create_time DESC
            LIMIT 5
        """)
        for i, (uid, time) in enumerate(cursor.fetchall(), 1):
            print(f"{i}. {uid} | {time}")
            
    finally:
        cursor.close()
        connection.close()

def full_investigation(phone):
    """完整用户信息排查"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 获取用户基本信息
        print("\n🔍 用户基本信息:")
        cursor.execute("""
            SELECT id, union_id, phone, user_name, gender, age, deleted
            FROM jahr_customer_user
            WHERE phone = %s
        """, (phone,))
        user_info = cursor.fetchone()
        
        if user_info:
            print(f"用户ID: {user_info[0]}")
            print(f"union_id: {user_info[1]}")
            print(f"手机号: {user_info[2]}")
            print(f"用户名: {user_info[3]}")
            print(f"性别: {'男' if user_info[4]==1 else '女' if user_info[4]==2 else '未知'}")
            print(f"年龄: {user_info[5]}")
            print(f"是否删除: {'是' if user_info[6] else '否'}")
            
            # 2. 检查设备关联（使用正确的字段）
            print("\n🔌 设备关联信息:")
            cursor.execute("""
                SELECT device_id, create_time  # 修改为正确的字段名
                FROM t_user_wx_relate
                WHERE user_id = %s
            """, (user_info[0],))
            device = cursor.fetchone()
            
            if device:
                print(f"设备ID: {device[0]}")
                print(f"关联时间: {device[1]}")
                
                # 3. 检查设备数据
                print("\n📊 设备数据记录:")
                device_union = f"M-100000-{device[0]}"
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM t_data_ecg_20250206 
                    WHERE union_id = %s
                """, (device_union,))
                count = cursor.fetchone()[0]
                print(f"找到 {count} 条设备数据记录")
                
                if count > 0:
                    cursor.execute("""
                        SELECT create_time 
                        FROM t_data_ecg_20250206 
                        WHERE union_id = %s
                        ORDER BY create_time DESC 
                        LIMIT 3
                    """, (device_union,))
                    print("\n最近3次记录时间:")
                    for i, (time,) in enumerate(cursor.fetchall(), 1):
                        print(f"{i}. {time}")
            else:
                print("未关联任何设备")
                
            # 4. 直接查询ECG数据
            print("\n❤️ 直接ECG数据查询:")
            cursor.execute("""
                SELECT create_time 
                FROM t_data_ecg_20250206
                WHERE union_id = %s
                ORDER BY create_time DESC
                LIMIT 3
            """, (user_info[1],))
            user_ecg = cursor.fetchall()
            
            if user_ecg:
                print(f"找到 {len(user_ecg)} 条用户直接数据:")
                for i, (time,) in enumerate(user_ecg, 1):
                    print(f"{i}. {time}")
            else:
                print("用户union_id无直接数据记录")
                
        else:
            print("未找到该手机号用户")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        cursor.close()
        connection.close()

def final_check(union_id, user_id):
    """最终数据验证"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 验证用户union_id是否存在
        print(f"\n🔍 验证用户union_id: {union_id}")
        cursor.execute("""
            SELECT create_time 
            FROM t_data_ecg_20250206
            WHERE union_id = %s
            ORDER BY create_time DESC
            LIMIT 1
        """, (union_id,))
        last_record = cursor.fetchone()
        print(f"最新记录时间: {last_record[0] if last_record else '无记录'}")
        
        # 2. 检查设备关联数据
        print("\n🔌 检查设备数据:")
        cursor.execute("""
            SELECT device_id 
            FROM t_user_wx_relate 
            WHERE user_id = %s
        """, (user_id,))
        device_id = cursor.fetchone()[0] if cursor.rowcount else None
        
        if device_id:
            device_union = f"M-100000-{device_id}"
            print(f"设备union_id: {device_union}")
            
            cursor.execute("""
                SELECT COUNT(*), MAX(create_time)
                FROM t_data_ecg_20250206
                WHERE union_id = %s
            """, (device_union,))
            count, latest_time = cursor.fetchone()
            print(f"设备数据记录数: {count} 条")
            print(f"最新记录时间: {latest_time if latest_time else '无记录'}")
        else:
            print("未关联设备")
            
    finally:
        cursor.close()
        connection.close()

def search_all_ecg_tables(phone):
    """在整个数据库中搜索ECG数据"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 获取所有ECG表（按日期排序）
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test' 
            AND table_name LIKE 't_data_ecg_%'
            ORDER BY table_name DESC
        """)
        ecg_tables = [row[0] for row in cursor.fetchall()]
        print(f"找到 {len(ecg_tables)} 个ECG数据表")
        
        # 2. 获取用户union_id和设备ID（使用正确的字段）
        cursor.execute("""
            SELECT 
                j.union_id,
                w.device_sn  -- 修改为正确的字段名
            FROM jahr_customer_user j
            LEFT JOIN t_user_wx_relate w ON j.id = w.user_id
            WHERE j.phone = %s
        """, (phone,))
        user_info = cursor.fetchone()
        
        if not user_info:
            print("用户不存在")
            return
            
        union_id, device_sn = user_info
        print(f"\n用户union_id: {union_id}")
        print(f"关联设备号: {device_sn or '无'}")
        
        # 3. 构造设备union_id
        device_union = f"M-100000-{device_sn}" if device_sn else None
        
        # 4. 在所有ECG表中搜索
        for table in ecg_tables[:10]:  # 只检查最新的10个表
            print(f"\n🔍 正在检查表 {table}...")
            
            # 检查用户union_id
            cursor.execute(f"""
                SELECT COUNT(*), MAX(create_time)
                FROM {table} 
                WHERE union_id = %s 
            """, (union_id,))
            count, max_time = cursor.fetchone()
            print(f"用户数据: {count} 条 | 最新时间: {max_time or '无'}")
            
            # 检查设备数据
            if device_union:
                cursor.execute(f"""
                    SELECT COUNT(*), MAX(create_time)
                    FROM {table} 
                    WHERE union_id = %s 
                """, (device_union,))
                dev_count, dev_max_time = cursor.fetchone()
                print(f"设备数据: {dev_count} 条 | 最新时间: {dev_max_time or '无'}")
                
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        cursor.close()
        connection.close()

def list_ecg_tables():
    """列出所有ECG数据表信息"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 获取所有ECG表信息
        cursor.execute("""
            SELECT 
                table_name,
                TABLE_ROWS,
                CREATE_TIME,
                UPDATE_TIME
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test' 
            AND table_name LIKE 't_data_ecg_%'
            ORDER BY table_name DESC
        """)
        
        # 2. 打印表信息
        print("ECG数据表列表：")
        print("表名\t\t记录数\t创建时间\t\t更新时间")
        for table in cursor.fetchall():
            print(f"{table[0]}\t{table[1]}\t{table[2]}\t{table[3]}")
            
    finally:
        cursor.close()
        connection.close()

def final_query(phone, target_time):
    """最终优化查询"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 获取设备号（使用正确的字段名）
        cursor.execute("""
            SELECT t2.device_id  # 修改为正确的字段名
            FROM jahr_customer_user t1
            JOIN t_user_wx_relate t2 ON t1.id = t2.user_id
            WHERE t1.phone = %s
        """, (phone,))
        result = cursor.fetchone()
        
        if not result or not result[0]:
            print("用户未关联设备")
            return
            
        device_id = result[0]
        
        # 2. 构造设备union_id
        device_union = f"M-100000-{device_id}"
        print(f"\n设备union_id: {device_union}")
        
        # 3. 精确时间查询
        start_time = (datetime.strptime(target_time, '%Y-%m-%d %H:%M:%S') 
                      - timedelta(seconds=30)).strftime('%Y-%m-%d %H:%M:%S')
        end_time = (datetime.strptime(target_time, '%Y-%m-%d %H:%M:%S') 
                    + timedelta(seconds=30)).strftime('%Y-%m-%d %H:%M:%S')
        
        # 4. 执行查询
        cursor.execute("""
            SELECT create_time, ecg_analysis
            FROM t_data_ecg_20250206
            WHERE union_id = %s
            AND create_time BETWEEN %s AND %s
            ORDER BY create_time DESC
        """, (device_union, start_time, end_time))
        
        records = cursor.fetchall()
        if records:
            print(f"\n找到 {len(records)} 条记录:")
            for i, (time, data) in enumerate(records, 1):
                print(f"{i}. {time} | 数据长度: {len(data)}")
                print(f"   ECG数据片段: {data[:100]}...")
        else:
            print("未找到匹配记录")
            print("建议检查：")
            print("1. 设备时间是否同步")
            print("2. 数据上传延迟")
            print("3. 表分区策略")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        cursor.close()
        connection.close()

def search_union_id_all_tables(union_id):
    """根据union_id搜索所有ECG表"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 获取所有ECG表
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test' 
            AND table_name LIKE 't_data_ecg_%'
            ORDER BY table_name DESC
        """)
        ecg_tables = [row[0] for row in cursor.fetchall()]
        print(f"找到 {len(ecg_tables)} 个ECG数据表")
        
        # 2. 在所有表中搜索
        found = False
        for table in ecg_tables:
            cursor.execute(f"""
                SELECT COUNT(*), MIN(create_time), MAX(create_time)
                FROM {table} 
                WHERE union_id = %s
            """, (union_id,))
            count, min_time, max_time = cursor.fetchone()
            
            if count > 0:
                found = True
                print(f"\n在表 {table} 中找到 {count} 条记录")
                print(f"时间范围: {min_time} 至 {max_time}")
                
                # 查看最新3条记录
                cursor.execute(f"""
                    SELECT id, create_time 
                    FROM {table} 
                    WHERE union_id = %s
                    ORDER BY create_time DESC 
                    LIMIT 3
                """, (union_id,))
                print("最新3条记录:")
                for i, (record_id, time) in enumerate(cursor.fetchall(), 1):
                    print(f"{i}. ID: {record_id} | 时间: {time}")
        
        if not found:
            print("\n在所有表中均未找到该union_id的记录")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        cursor.close()
        connection.close()

def search_user_all_ways(phone=None, union_id=None):
    """通过多种方式查找用户"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 在jahr_customer_user表中查找
        print("\n在jahr_customer_user表中查找:")
        if phone:
            cursor.execute("""
                SELECT id, union_id, phone, user_name, deleted 
                FROM jahr_customer_user 
                WHERE phone = %s
            """, (phone,))
        elif union_id:
            cursor.execute("""
                SELECT id, union_id, phone, user_name, deleted 
                FROM jahr_customer_user 
                WHERE union_id = %s OR uid = %s
            """, (union_id, union_id))
            
        results = cursor.fetchall()
        if results:
            for row in results:
                print(f"ID: {row[0]}")
                print(f"union_id: {row[1]}")
                print(f"phone: {row[2]}")
                print(f"user_name: {row[3]}")
                print(f"deleted: {row[4]}")
        else:
            print("未找到记录")
            
        # 2. 在t_user表中查找
        print("\n在t_user表中查找:")
        if phone:
            cursor.execute("""
                SELECT id, uid, phone, name, deleted 
                FROM t_user 
                WHERE phone = %s
            """, (phone,))
        elif union_id:
            cursor.execute("""
                SELECT id, uid, phone, name, deleted 
                FROM t_user 
                WHERE uid = %s
            """, (union_id,))
            
        results = cursor.fetchall()
        if results:
            for row in results:
                print(f"ID: {row[0]}")
                print(f"uid: {row[1]}")
                print(f"phone: {row[2]}")
                print(f"name: {row[3]}")
                print(f"deleted: {row[4]}")
        else:
            print("未找到记录")
            
        # 3. 在t_user_wx_relate表中查找关联
        print("\n在t_user_wx_relate表中查找:")
        cursor.execute("""
            SELECT id, uid, wx_union_id, deleted 
            FROM t_user_wx_relate 
            WHERE deleted = 0
        """)
        results = cursor.fetchall()
        if results:
            for row in results:
                print(f"ID: {row[0]}")
                print(f"uid: {row[1]}")
                print(f"wx_union_id: {row[2]}")
                print(f"deleted: {row[3]}")
        else:
            print("未找到记录")
            
    finally:
        cursor.close()
        connection.close()

def check_ecg_by_id():
    """根据ID查询ECG记录"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 查询指定ID的记录
        cursor.execute("""
            SELECT id, union_id, create_time, ecg_analysis 
            FROM t_data_ecg_20250128 
            WHERE id = 1884761953989603330
        """)
        
        result = cursor.fetchone()
        if result:
            print("\n找到记录:")
            print(f"ID: {result[0]}")
            print(f"union_id: {result[1]}")
            print(f"创建时间: {result[2]}")
            print(f"数据长度: {len(result[3])}")
            print(f"数据片段: {result[3][:200]}...")  # 只显示前200个字符
            
            # 保存完整数据到文件
            with open(f'ecg_data_{result[0]}.txt', 'w') as f:
                f.write(result[3])
            print(f"\n完整数据已保存到文件: ecg_data_{result[0]}.txt")
        else:
            print("未找到记录")
            
    finally:
        cursor.close()
        connection.close()

def search_id_all_tables():
    """在所有ECG表中搜索指定ID"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 1. 获取所有ECG表
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test' 
            AND table_name LIKE 't_data_ecg_%'
            ORDER BY table_name DESC
        """)
        ecg_tables = [row[0] for row in cursor.fetchall()]
        print(f"找到 {len(ecg_tables)} 个ECG数据表")
        
        target_id = 1884761953989603330
        found = False
        
        # 2. 在每个表中查找
        for table in ecg_tables:
            print(f"\n正在检查表 {table}...")
            cursor.execute(f"""
                SELECT id, union_id, create_time, ecg_analysis 
                FROM {table} 
                WHERE id = {target_id}
            """)
            
            result = cursor.fetchone()
            if result:
                found = True
                print("\n找到记录:")
                print(f"表名: {table}")
                print(f"ID: {result[0]}")
                print(f"union_id: {result[1]}")
                print(f"创建时间: {result[2]}")
                print(f"数据长度: {len(result[3])}")
                print(f"数据片段: {result[3][:200]}...")
                
                # 保存完整数据到文件
                with open(f'ecg_data_{result[0]}.txt', 'w') as f:
                    f.write(result[3])
                print(f"\n完整数据已保存到文件: ecg_data_{result[0]}.txt")
                break
                
        if not found:
            print("\n在所有表中均未找到该ID的记录")
            
    finally:
        cursor.close()
        connection.close()

def check_jan28_tables():
    """检查所有1月28号的ECG表，仅显示表名、记录数和时间范围"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    try:
        cursor = connection.cursor()
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test' 
              AND table_name LIKE 't_data_ecg_%0128'
            ORDER BY table_name DESC
        """)
        tables = [row[0] for row in cursor.fetchall()]
        print(f"找到 {len(tables)} 个1月28号的ECG表")
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*), MIN(create_time), MAX(create_time) FROM {table}")
            count, min_time, max_time = cursor.fetchone()
            print(f"{table}: 记录数: {count}, 时间范围: {min_time} 至 {max_time}")
            
    finally:
        cursor.close()
        connection.close()

def list_all_databases():
    """列出所有数据库"""
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='information_schema',  # 使用information_schema数据库可以查询所有数据库
        port=3306,
        charset='utf8mb4'
    )
    try:
        cursor = connection.cursor()
        cursor.execute("SHOW DATABASES")
        databases = [row[0] for row in cursor.fetchall()]
        print(f"找到 {len(databases)} 个数据库:")
        for db in databases:
            print(db)
    finally:
        cursor.close()
        connection.close()

def query_data_by_date(phone, date_str, export_csv=True, export_ecg=True):
    """
    查询指定手机号在特定日期的所有数据
    Args:
        phone: 用户手机号
        date_str: 日期字符串，格式为YYYYMMDD
        export_csv: 是否导出为CSV文件
        export_ecg: 是否导出ECG数据
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 构造表名
        table_name = f"t_data_ecg_{date_str}"
        
        # 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test' 
            AND table_name = '{table_name}'
        """)
        if not cursor.fetchone()[0]:
            print(f"表 {table_name} 不存在")
            return
        
        # 检查表结构，查看所有字段名称
        cursor.execute(f"DESCRIBE {table_name}")
        columns_info = cursor.fetchall()
        print(f"\n表 {table_name} 的结构:")
        print("字段名\t\t类型\t\t可否为空\t键\t默认值\t扩展")
        for col in columns_info:
            print(f"{col[0]}\t\t{col[1]}\t\t{col[2]}\t{col[3] or '无'}\t{col[4] or '无'}\t{col[5] or '无'}")
        
        # 找出可能包含ECG数据点的字段
        ecg_fields = [col[0] for col in columns_info if any(
            keyword in col[0].lower() for keyword in ['ecg', 'data', 'wave', 'signal', 'raw']
        )]
        
        print(f"\n可能包含ECG数据的字段: {', '.join(ecg_fields) or '未找到'}")
            
        # 1. 先获取用户的union_id
        cursor.execute("""
            SELECT id, union_id 
            FROM jahr_customer_user 
            WHERE phone = %s AND deleted = 0
        """, (phone,))
        user_info = cursor.fetchone()
        
        if not user_info:
            print(f"未找到手机号为 {phone} 的用户")
            return
            
        user_id, union_id = user_info
        print(f"\n用户信息:")
        print(f"用户ID: {user_id}")
        print(f"union_id: {union_id}")
        
        # 2. 查询用户在该日期的直接数据，获取所有列
        cursor.execute(f"SELECT * FROM {table_name} WHERE union_id = %s ORDER BY create_time ASC", (union_id,))
        direct_records = cursor.fetchall()
        print(f"\n找到用户直接数据: {len(direct_records)} 条")
        
        # 获取列名
        cursor.execute(f"SHOW COLUMNS FROM {table_name}")
        column_names = [column[0] for column in cursor.fetchall()]
        
        # 将结果转换为DataFrame
        if direct_records:
            df = pd.DataFrame(direct_records, columns=column_names)
            df['数据来源'] = '用户直接数据'
            
            # 显示数据记录基本信息
            print("\n用户直接数据记录:")
            for i, row in df.iterrows():
                record_info = f"{i+1}. ID: {row['id']} | 时间: {row['create_time']}"
                for field in ecg_fields:
                    if field in df.columns and pd.notna(row[field]):
                        record_info += f" | {field}长度: {len(str(row[field]))}"
                print(record_info)
            
            # 尝试解析ECG数据
            if export_ecg:
                # 创建文件夹存储ECG数据
                ecg_dir = f"{phone}_{date_str}_ecg_raw_data"
                if not os.path.exists(ecg_dir):
                    os.makedirs(ecg_dir)
                
                print(f"\n导出ECG原始数据到文件夹: {ecg_dir}")
                success_count = 0
                
                for i, row in df.iterrows():
                    time_str = row['create_time'].strftime('%H%M%S')
                    record_id = row['id']
                    
                    # 尝试从各个可能的字段提取ECG数据
                    for field in ecg_fields:
                        if field not in df.columns or pd.isna(row[field]):
                            continue
                        
                        field_data = row[field]
                        if not field_data:
                            continue
                            
                        try:
                            # 尝试解析为JSON
                            data_json = json.loads(field_data) if isinstance(field_data, str) else field_data
                            
                            # 检查是否包含数据点
                            has_data_points = False
                            
                            # 检查各种可能的数据点字段名
                            data_point_fields = ['data', 'dataPoints', 'points', 'ecgData', 'values', 'rawData', 'signal']
                            
                            for data_field in data_point_fields:
                                if data_field in data_json:
                                    has_data_points = True
                                    # 保存数据点到单独文件
                                    ecg_file = f"{ecg_dir}/{record_id}_{time_str}_{field}_{data_field}.json"
                                    
                                    # 提取数据点并保存
                                    with open(ecg_file, 'w', encoding='utf-8') as f:
                                        if isinstance(data_json[data_field], list):
                                            # 如果是列表，直接保存
                                            json.dump(data_json[data_field], f, ensure_ascii=False, indent=2)
                                        else:
                                            # 否则转为字符串
                                            f.write(str(data_json[data_field]))
                                            
                                    print(f"✓ 成功从字段 {field}.{data_field} 提取数据点: {ecg_file}")
                                    success_count += 1
                            
                            # 如果没有找到数据点但是整个JSON可能是数据点列表
                            if not has_data_points and isinstance(data_json, list):
                                # 检查是否像是数值列表
                                if len(data_json) > 0 and all(isinstance(x, (int, float)) for x in data_json[:10]):
                                    ecg_file = f"{ecg_dir}/{record_id}_{time_str}_{field}_points.json"
                                    with open(ecg_file, 'w', encoding='utf-8') as f:
                                        json.dump(data_json, f, ensure_ascii=False, indent=2)
                                    print(f"✓ 成功从字段 {field} 提取数据点列表: {ecg_file}")
                                    success_count += 1
                                    has_data_points = True
                            
                            # 如果没有找到数据点，保存完整JSON以备检查
                            if not has_data_points:
                                # 保存完整JSON
                                full_json_file = f"{ecg_dir}/{record_id}_{time_str}_{field}_full.json"
                                with open(full_json_file, 'w', encoding='utf-8') as f:
                                    json.dump(data_json, f, ensure_ascii=False, indent=2)
                                print(f"⚠ 未找到数据点，已保存完整JSON: {full_json_file}")
                                
                        except (json.JSONDecodeError, TypeError):
                            # 如果不是JSON格式，直接保存原始数据
                            raw_file = f"{ecg_dir}/{record_id}_{time_str}_{field}_raw.txt"
                            with open(raw_file, 'w', encoding='utf-8') as f:
                                f.write(str(field_data))
                            print(f"⚠ 非JSON格式，已保存原始数据: {raw_file}")
                
                print(f"已成功提取 {success_count} 个ECG数据点文件")
                
                # 如果没有成功提取任何数据点，尝试保存完整记录以便进一步分析
                if success_count == 0:
                    print("\n未能从现有字段提取数据点，保存完整记录以供分析")
                    complete_records_file = f"{ecg_dir}/complete_records.json"
                    # 将DataFrame转为字典再保存为JSON
                    records_data = df.to_dict('records')
                    with open(complete_records_file, 'w', encoding='utf-8') as f:
                        json.dump(records_data, f, ensure_ascii=False, indent=2)
                    print(f"完整记录已保存到: {complete_records_file}")
            
            # 导出到CSV
            if export_csv:
                # 创建一个不包含大型数据字段的版本用于导出
                export_columns = [col for col in df.columns if col not in ecg_fields and col != 'ecg_analysis']
                export_columns.append('数据来源')
                
                export_df = df[export_columns]
                csv_file = f"{phone}_{date_str}_user_data.csv"
                export_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                print(f"\n用户直接数据已导出到: {csv_file}")
        
        # 3. 查询设备关联数据
        try:
            # 尝试使用device_sn字段
            cursor.execute("""
                SELECT device_sn 
                FROM t_user_wx_relate 
                WHERE user_id = %s
            """, (user_id,))
            device_result = cursor.fetchone()
        except Exception:
            try:
                # 如果失败，尝试使用device_id字段
                cursor.execute("""
                    SELECT device_id 
                    FROM t_user_wx_relate 
                    WHERE user_id = %s
                """, (user_id,))
                device_result = cursor.fetchone()
            except Exception as e:
                print(f"查询设备关联失败: {str(e)}")
                device_result = None
        
        device_records = []
        if device_result and device_result[0]:
            device_id = device_result[0]
            device_union = f"M-100000-{device_id}"
            print(f"\n关联设备ID: {device_id}")
            print(f"设备union_id: {device_union}")
            
            # 查询设备数据，获取所有列
            cursor.execute(f"SELECT * FROM {table_name} WHERE union_id = %s ORDER BY create_time ASC", (device_union,))
            device_records = cursor.fetchall()
            print(f"\n找到设备数据: {len(device_records)} 条")
            
            if device_records:
                # 将结果转换为DataFrame
                device_df = pd.DataFrame(device_records, columns=column_names)
                device_df['数据来源'] = '设备数据'
                
                # 显示数据记录基本信息
                print("\n设备数据记录:")
                for i, row in device_df.iterrows():
                    record_info = f"{i+1}. ID: {row['id']} | 时间: {row['create_time']}"
                    for field in ecg_fields:
                        if field in device_df.columns and pd.notna(row[field]):
                            record_info += f" | {field}长度: {len(str(row[field]))}"
                    print(record_info)
                
                # 尝试解析ECG数据
                if export_ecg:
                    # 创建文件夹存储设备ECG数据
                    device_ecg_dir = f"{phone}_{date_str}_device_ecg_raw_data"
                    if not os.path.exists(device_ecg_dir):
                        os.makedirs(device_ecg_dir)
                    
                    print(f"\n导出设备ECG原始数据到文件夹: {device_ecg_dir}")
                    device_success_count = 0
                    
                    for i, row in device_df.iterrows():
                        time_str = row['create_time'].strftime('%H%M%S')
                        record_id = row['id']
                        
                        # 尝试从各个可能的字段提取ECG数据
                        for field in ecg_fields:
                            if field not in device_df.columns or pd.isna(row[field]):
                                continue
                            
                            field_data = row[field]
                            if not field_data:
                                continue
                                
                            try:
                                # 尝试解析为JSON
                                data_json = json.loads(field_data) if isinstance(field_data, str) else field_data
                                
                                # 检查是否包含数据点
                                has_data_points = False
                                
                                # 检查各种可能的数据点字段名
                                data_point_fields = ['data', 'dataPoints', 'points', 'ecgData', 'values', 'rawData', 'signal']
                                
                                for data_field in data_point_fields:
                                    if data_field in data_json:
                                        has_data_points = True
                                        # 保存数据点到单独文件
                                        ecg_file = f"{device_ecg_dir}/{record_id}_{time_str}_{field}_{data_field}.json"
                                        
                                        # 提取数据点并保存
                                        with open(ecg_file, 'w', encoding='utf-8') as f:
                                            if isinstance(data_json[data_field], list):
                                                # 如果是列表，直接保存
                                                json.dump(data_json[data_field], f, ensure_ascii=False, indent=2)
                                            else:
                                                # 否则转为字符串
                                                f.write(str(data_json[data_field]))
                                                
                                        print(f"✓ 成功从字段 {field}.{data_field} 提取数据点: {ecg_file}")
                                        device_success_count += 1
                                
                                # 如果没有找到数据点但是整个JSON可能是数据点列表
                                if not has_data_points and isinstance(data_json, list):
                                    # 检查是否像是数值列表
                                    if len(data_json) > 0 and all(isinstance(x, (int, float)) for x in data_json[:10]):
                                        ecg_file = f"{device_ecg_dir}/{record_id}_{time_str}_{field}_points.json"
                                        with open(ecg_file, 'w', encoding='utf-8') as f:
                                            json.dump(data_json, f, ensure_ascii=False, indent=2)
                                        print(f"✓ 成功从字段 {field} 提取数据点列表: {ecg_file}")
                                        device_success_count += 1
                                        has_data_points = True
                                
                                # 如果没有找到数据点，保存完整JSON以备检查
                                if not has_data_points:
                                    # 保存完整JSON
                                    full_json_file = f"{device_ecg_dir}/{record_id}_{time_str}_{field}_full.json"
                                    with open(full_json_file, 'w', encoding='utf-8') as f:
                                        json.dump(data_json, f, ensure_ascii=False, indent=2)
                                    print(f"⚠ 未找到数据点，已保存完整JSON: {full_json_file}")
                                    
                            except (json.JSONDecodeError, TypeError):
                                # 如果不是JSON格式，直接保存原始数据
                                raw_file = f"{device_ecg_dir}/{record_id}_{time_str}_{field}_raw.txt"
                                with open(raw_file, 'w', encoding='utf-8') as f:
                                    f.write(str(field_data))
                                print(f"⚠ 非JSON格式，已保存原始数据: {raw_file}")
                    
                    print(f"已成功提取 {device_success_count} 个设备ECG数据点文件")
                    
                    # 如果没有成功提取任何数据点，尝试保存完整记录以便进一步分析
                    if device_success_count == 0:
                        print("\n未能从现有字段提取数据点，保存完整记录以供分析")
                        device_records_file = f"{device_ecg_dir}/complete_device_records.json"
                        # 将DataFrame转为字典再保存为JSON
                        device_records_data = device_df.to_dict('records')
                        with open(device_records_file, 'w', encoding='utf-8') as f:
                            json.dump(device_records_data, f, ensure_ascii=False, indent=2)
                        print(f"完整设备记录已保存到: {device_records_file}")
                
                # 导出到CSV
                if export_csv:
                    # 创建一个不包含大型数据字段的版本用于导出
                    export_columns = [col for col in device_df.columns if col not in ecg_fields and col != 'ecg_analysis']
                    export_columns.append('数据来源')
                    
                    export_device_df = device_df[export_columns]
                    device_csv_file = f"{phone}_{date_str}_device_data.csv"
                    export_device_df.to_csv(device_csv_file, index=False, encoding='utf-8-sig')
                    print(f"\n设备数据已导出到: {device_csv_file}")
                
                # 合并两个DataFrame并导出完整数据
                if len(direct_records) > 0 and export_csv:
                    # 确保两个导出DataFrame有相同的列
                    common_columns = list(set(export_df.columns).intersection(set(export_device_df.columns)))
                    
                    combined_export_df = pd.concat([
                        export_df[common_columns],
                        export_device_df[common_columns]
                    ]).sort_values('create_time')
                    
                    combined_csv_file = f"{phone}_{date_str}_all_data.csv"
                    combined_export_df.to_csv(combined_csv_file, index=False, encoding='utf-8-sig')
                    print(f"\n全部数据已合并导出到: {combined_csv_file}")
        else:
            print("\n用户未关联设备")
            
        # 4. 汇总数据
        total_records = len(direct_records) + len(device_records)
        print(f"\n总计找到 {total_records} 条数据记录")
        
        if total_records > 0:
            if export_csv:
                print(f"\nCSV文件已成功导出")
            if export_ecg:
                print(f"原始ECG数据已尝试提取并保存到单独文件")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        cursor.close()
        connection.close()

def extract_ecg_lead_data(phone, date_str, lead_type="I"):
    """
    专门提取指定导联的ECG数据点
    Args:
        phone: 用户手机号
        date_str: 日期字符串，格式为YYYYMMDD
        lead_type: 导联类型，默认为"I"导联
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 构造表名
        table_name = f"t_data_ecg_{date_str}"
        
        # 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test' 
            AND table_name = '{table_name}'
        """)
        if not cursor.fetchone()[0]:
            print(f"表 {table_name} 不存在")
            return
        
        # 检查表结构，查看所有字段名称
        cursor.execute(f"DESCRIBE {table_name}")
        columns_info = cursor.fetchall()
        
        # 找出可能包含ECG数据点的字段
        ecg_fields = [col[0] for col in columns_info if any(
            keyword in col[0].lower() for keyword in [
                'ecg', 'data', 'wave', 'signal', 'raw', 'lead', 
                'i_lead', 'lead_i', 'lead1', 'leadi'
            ]
        )]
        
        print(f"\n可能包含ECG数据的字段: {', '.join(ecg_fields) or '未找到'}")
            
        # 获取用户的union_id
        cursor.execute("""
            SELECT id, union_id 
            FROM jahr_customer_user 
            WHERE phone = %s AND deleted = 0
        """, (phone,))
        user_info = cursor.fetchone()
        
        if not user_info:
            print(f"未找到手机号为 {phone} 的用户")
            return
            
        user_id, union_id = user_info
        print(f"\n用户信息:")
        print(f"用户ID: {user_id}")
        print(f"union_id: {union_id}")
        
        # 查询用户数据，获取所有列
        cursor.execute(f"SELECT * FROM {table_name} WHERE union_id = %s ORDER BY create_time ASC", (union_id,))
        direct_records = cursor.fetchall()
        print(f"\n找到用户数据: {len(direct_records)} 条")
        
        if len(direct_records) == 0:
            print("未找到任何记录")
            return
        
        # 获取列名
        cursor.execute(f"SHOW COLUMNS FROM {table_name}")
        column_names = [column[0] for column in cursor.fetchall()]
        
        # 将结果转换为DataFrame
        df = pd.DataFrame(direct_records, columns=column_names)
        
        # 创建文件夹存储提取的导联数据
        lead_data_dir = f"{phone}_{date_str}_lead_{lead_type}_data"
        if not os.path.exists(lead_data_dir):
            os.makedirs(lead_data_dir)
        
        print(f"\n正在查找{lead_type}导联数据...")
        lead_data_found = 0
        
        # 特定关键字，用于匹配导联数据
        lead_keywords = [
            f"lead{lead_type}", f"lead_{lead_type}", f"{lead_type}_lead", 
            f"{lead_type.lower()}lead", f"lead{lead_type.lower()}", f"{lead_type}"
        ]
        
        # 我们先获取几条记录的样本数据，看看数据结构
        sample_records = min(5, len(df))
        print(f"\n查看前{sample_records}条记录的数据结构:")
        
        # 保存几条完整样本记录用于分析
        samples_file = f"{lead_data_dir}/sample_records_analyze.txt"
        with open(samples_file, 'w', encoding='utf-8') as f:
            for i in range(sample_records):
                record = df.iloc[i]
                f.write(f"===== 记录 {i+1} | ID: {record['id']} | 时间: {record['create_time']} =====\n\n")
                
                # 检查有哪些可能包含ECG数据的字段
                for field in ecg_fields:
                    if field in df.columns and pd.notna(record[field]):
                        f.write(f"字段: {field}\n")
                        f.write(f"数据长度: {len(str(record[field]))}\n")
                        f.write(f"数据片段: {str(record[field])[:300]}...\n\n")
        
        print(f"样本记录已保存到 {samples_file} 用于分析")
        
        # 扩展搜索策略，不仅查找特定字段名，还要检查所有字段中的内容
        for i, row in df.iterrows():
            record_id = row['id']
            create_time = row['create_time']
            time_str = create_time.strftime('%H%M%S')
            
            # 检查每个可能的数据字段
            for field in ecg_fields:
                if field not in df.columns or pd.isna(row[field]):
                    continue
                
                field_data = row[field]
                if not field_data:
                    continue
                
                # 将数据保存为单独文件以便进一步分析
                if i < 10:  # 只保存前10条记录以免文件过多
                    raw_data_file = f"{lead_data_dir}/raw_{record_id}_{time_str}_{field}.txt"
                    with open(raw_data_file, 'w', encoding='utf-8') as f:
                        f.write(str(field_data))
                
                try:
                    # 尝试解析为JSON
                    if isinstance(field_data, str):
                        try:
                            # 检查字符串是否直接是数组格式
                            if field_data.startswith('[') and field_data.endswith(']'):
                                # 可能是一个数值数组字符串，尝试直接解析
                                data_array = json.loads(field_data)
                                if isinstance(data_array, list) and len(data_array) > 10:
                                    # 是数值数组，看是否匹配导联数据格式
                                    if all(isinstance(x, (int, float)) for x in data_array[:10]):
                                        # 找到符合格式的数值数组
                                        lead_file = f"{lead_data_dir}/{record_id}_{time_str}_{field}_direct_array.json"
                                        with open(lead_file, 'w', encoding='utf-8') as f:
                                            json.dump(data_array, f, ensure_ascii=False)
                                        print(f"✓ 记录 {record_id} | 时间 {create_time} | 找到数值数组：{len(data_array)}个数据点")
                                        lead_data_found += 1
                                        
                                        # 同时保存为CSV格式
                                        csv_file = f"{lead_data_dir}/{record_id}_{time_str}_{field}_direct_array.csv"
                                        pd.DataFrame(data_array, columns=['Value']).to_csv(csv_file, index=True, encoding='utf-8-sig')
                                        continue
                            
                            # 解析普通JSON
                            data_json = json.loads(field_data)
                        except json.JSONDecodeError:
                            # 不是JSON格式，但可能包含数值数组模式
                            # 查找类似 [-0.0701904296875,-0.0640869140625,...] 的模式
                            import re
                            array_pattern = r'\[(-?\d+\.?\d*,\s*)+(-?\d+\.?\d*)\]'
                            matches = re.findall(array_pattern, field_data)
                            if matches:
                                # 找到可能的数值数组模式，提取整个数组
                                array_start = field_data.find('[')
                                array_end = field_data.find(']', array_start) + 1
                                if array_start >= 0 and array_end > array_start:
                                    array_str = field_data[array_start:array_end]
                                    try:
                                        data_array = json.loads(array_str)
                                        if isinstance(data_array, list) and len(data_array) > 10:
                                            # 验证是否为数值数组
                                            if all(isinstance(x, (int, float)) for x in data_array[:10]):
                                                # 找到符合格式的数值数组
                                                lead_file = f"{lead_data_dir}/{record_id}_{time_str}_{field}_extracted_array.json"
                                                with open(lead_file, 'w', encoding='utf-8') as f:
                                                    json.dump(data_array, f, ensure_ascii=False)
                                                print(f"✓ 记录 {record_id} | 时间 {create_time} | 从文本中提取到数值数组：{len(data_array)}个数据点")
                                                lead_data_found += 1
                                                
                                                # 同时保存为CSV格式
                                                csv_file = f"{lead_data_dir}/{record_id}_{time_str}_{field}_extracted_array.csv"
                                                pd.DataFrame(data_array, columns=['Value']).to_csv(csv_file, index=True, encoding='utf-8-sig')
                                    except json.JSONDecodeError:
                                        pass  # 不是有效的JSON数组
                            continue  # 继续检查其他字段
                    else:
                        # 非字符串类型，尝试直接使用
                        data_json = field_data
                    
                    # 对于解析成功的JSON数据，进行常规检查
                    # 1. 直接查找导联数据的字段
                    for key, value in data_json.items() if isinstance(data_json, dict) else []:
                        key_lower = key.lower()
                        
                        # 检查是否与目标导联相关
                        if any(keyword.lower() in key_lower for keyword in lead_keywords):
                            if isinstance(value, list) and len(value) > 0 and all(isinstance(x, (int, float)) for x in value[:10]):
                                # 找到目标导联数据
                                lead_file = f"{lead_data_dir}/{record_id}_{time_str}_{field}_{key}.json"
                                with open(lead_file, 'w', encoding='utf-8') as f:
                                    json.dump(value, f, ensure_ascii=False)
                                print(f"✓ 记录 {record_id} | 时间 {create_time} | 找到{lead_type}导联数据：{len(value)}个数据点")
                                lead_data_found += 1
                                
                                # 同时保存为CSV格式，便于分析
                                csv_file = f"{lead_data_dir}/{record_id}_{time_str}_{field}_{key}.csv"
                                # 转换为DataFrame并保存为CSV
                                pd.DataFrame(value, columns=[f'Lead{lead_type}']).to_csv(csv_file, index=True, encoding='utf-8-sig')
                    
                    # 2. 查找嵌套数据结构中的导联数据
                    if isinstance(data_json, dict):
                        # 检查常见的嵌套结构
                        nested_fields = ['data', 'ecgData', 'rawData', 'leads', 'channelData', 'channels']
                        for nested_field in nested_fields:
                            if nested_field in data_json and isinstance(data_json[nested_field], dict):
                                nested_data = data_json[nested_field]
                                for key, value in nested_data.items():
                                    key_lower = key.lower()
                                    if any(keyword.lower() in key_lower for keyword in lead_keywords):
                                        if isinstance(value, list) and len(value) > 0 and all(isinstance(x, (int, float)) for x in value[:10]):
                                            # 找到嵌套结构中的导联数据
                                            lead_file = f"{lead_data_dir}/{record_id}_{time_str}_{field}_{nested_field}_{key}.json"
                                            with open(lead_file, 'w', encoding='utf-8') as f:
                                                json.dump(value, f, ensure_ascii=False)
                                            print(f"✓ 记录 {record_id} | 时间 {create_time} | 找到嵌套{lead_type}导联数据：{len(value)}个数据点")
                                            lead_data_found += 1
                                            
                                            # 同时保存为CSV格式
                                            csv_file = f"{lead_data_dir}/{record_id}_{time_str}_{field}_{nested_field}_{key}.csv"
                                            pd.DataFrame(value, columns=[f'Lead{lead_type}']).to_csv(csv_file, index=True, encoding='utf-8-sig')
                    
                    # 3. 查找任何包含数值数组的字段，即使没有特定命名
                    if isinstance(data_json, dict):
                        for key, value in data_json.items():
                            if isinstance(value, list) and len(value) > 100:  # 足够长的数组才可能是ECG数据
                                # 检查前10个元素是否都是数值
                                if all(isinstance(x, (int, float)) for x in value[:10]):
                                    # 可能是ECG数据，即使字段名不匹配
                                    potential_file = f"{lead_data_dir}/{record_id}_{time_str}_{field}_{key}_potential.json"
                                    with open(potential_file, 'w', encoding='utf-8') as f:
                                        json.dump(value, f, ensure_ascii=False)
                                    print(f"! 记录 {record_id} | 时间 {create_time} | 找到潜在数据数组：字段 {key} 长度 {len(value)}")
                                    lead_data_found += 1
                                    
                                    # 同时保存为CSV格式
                                    csv_file = f"{lead_data_dir}/{record_id}_{time_str}_{field}_{key}_potential.csv"
                                    pd.DataFrame(value, columns=['Value']).to_csv(csv_file, index=True, encoding='utf-8-sig')
                
                except Exception as e:
                    print(f"处理记录 {record_id} 的字段 {field} 时出错: {str(e)}")
        
        print(f"\n共找到 {lead_data_found} 条可能的{lead_type}导联数据")
        if lead_data_found == 0:
            print(f"未找到任何{lead_type}导联数据，尝试保存几条完整记录以供进一步分析")
            # 只保存少量记录避免序列化问题
            for i in range(min(5, len(df))):
                record = df.iloc[i]
                record_id = record['id']
                # 将DataFrame行转换为字典，并处理Timestamp对象
                record_dict = {}
                for col, val in record.items():
                    if isinstance(val, pd.Timestamp):
                        record_dict[col] = val.strftime('%Y-%m-%d %H:%M:%S')
                    elif pd.isna(val):
                        record_dict[col] = None
                    else:
                        record_dict[col] = val
                        
                # 保存单个记录
                record_file = f"{lead_data_dir}/complete_record_{record_id}.json"
                with open(record_file, 'w', encoding='utf-8') as f:
                    json.dump(record_dict, f, ensure_ascii=False, indent=2)
            print(f"完整记录已保存到文件夹: {lead_data_dir}")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        cursor.close()
        connection.close()

def extract_ecg_analysis(phone, date_str):
    """
    专门提取并分析ecg_analysis字段的完整内容
    Args:
        phone: 用户手机号
        date_str: 日期字符串，格式为YYYYMMDD
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 构造表名
        table_name = f"t_data_ecg_{date_str}"
        
        # 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test' 
            AND table_name = '{table_name}'
        """)
        if not cursor.fetchone()[0]:
            print(f"表 {table_name} 不存在")
            return
        
        # 获取用户的union_id
        cursor.execute("""
            SELECT id, union_id 
            FROM jahr_customer_user 
            WHERE phone = %s AND deleted = 0
        """, (phone,))
        user_info = cursor.fetchone()
        
        if not user_info:
            print(f"未找到手机号为 {phone} 的用户")
            return
            
        user_id, union_id = user_info
        print(f"\n用户信息:")
        print(f"用户ID: {user_id}")
        print(f"union_id: {union_id}")
        
        # 查询用户数据，只提取ecg_analysis字段
        cursor.execute(f"""
            SELECT id, create_time, ecg_analysis, ecg_analysisII, ecg_analysisIII
            FROM {table_name} 
            WHERE union_id = %s
            ORDER BY create_time ASC
        """, (union_id,))
        records = cursor.fetchall()
        
        print(f"\n找到 {len(records)} 条记录")
        if not records:
            print("未找到任何记录")
            return
        
        # 创建目录保存分析结果
        output_dir = f"{phone}_{date_str}_ecg_analysis_detail"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 分析每条记录
        print("\n正在分析ecg_analysis字段...")
        for i, (record_id, create_time, ecg_analysis, ecg_analysisII, ecg_analysisIII) in enumerate(records):
            time_str = create_time.strftime("%H%M%S")
            print(f"\n记录 {i+1}/{len(records)} | ID: {record_id} | 时间: {create_time}")
            
            # 保存并分析ecg_analysis
            if ecg_analysis:
                file_path = f"{output_dir}/{record_id}_{time_str}_ecg_analysis.json"
                
                try:
                    # 尝试解析JSON
                    analysis_data = json.loads(ecg_analysis)
                    
                    # 保存格式化的JSON
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(analysis_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"✅ 成功解析ecg_analysis并保存到: {file_path}")
                    
                    # 分析结构，查找可能的波形数据
                    print("分析结构:")
                    for key, value in analysis_data.items():
                        if isinstance(value, list) and len(value) > 10:
                            print(f"  - 发现可能的数据数组: {key} (长度: {len(value)})")
                            # 保存数组到单独文件
                            array_file = f"{output_dir}/{record_id}_{time_str}_{key}_array.json"
                            with open(array_file, 'w', encoding='utf-8') as f:
                                json.dump(value, f, ensure_ascii=False, indent=2)
                        elif isinstance(value, dict):
                            print(f"  - 嵌套对象: {key}")
                            # 查找嵌套对象中的数组
                            for sub_key, sub_value in value.items():
                                if isinstance(sub_value, list) and len(sub_value) > 10:
                                    print(f"    - 发现嵌套数据数组: {key}.{sub_key} (长度: {len(sub_value)})")
                                    # 保存数组到单独文件
                                    nested_array_file = f"{output_dir}/{record_id}_{time_str}_{key}_{sub_key}_array.json"
                                    with open(nested_array_file, 'w', encoding='utf-8') as f:
                                        json.dump(sub_value, f, ensure_ascii=False, indent=2)
                    
                except json.JSONDecodeError:
                    print(f"⚠️ ecg_analysis不是有效的JSON格式")
                    # 保存原始内容
                    with open(file_path + ".txt", 'w', encoding='utf-8') as f:
                        f.write(str(ecg_analysis))
            else:
                print("ecg_analysis字段为空")
            
            # 保存并分析ecg_analysisII
            if ecg_analysisII and ecg_analysisII != ecg_analysis:
                file_path = f"{output_dir}/{record_id}_{time_str}_ecg_analysisII.json"
                
                try:
                    # 尝试解析JSON
                    analysis_data = json.loads(ecg_analysisII)
                    
                    # 保存格式化的JSON
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(analysis_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"✅ 成功解析ecg_analysisII并保存到: {file_path}")
                    
                except json.JSONDecodeError:
                    print(f"⚠️ ecg_analysisII不是有效的JSON格式")
                    # 保存原始内容
                    with open(file_path + ".txt", 'w', encoding='utf-8') as f:
                        f.write(str(ecg_analysisII))
            
            # 保存并分析ecg_analysisIII
            if ecg_analysisIII and ecg_analysisIII != ecg_analysis and ecg_analysisIII != ecg_analysisII:
                file_path = f"{output_dir}/{record_id}_{time_str}_ecg_analysisIII.json"
                
                try:
                    # 尝试解析JSON
                    analysis_data = json.loads(ecg_analysisIII)
                    
                    # 保存格式化的JSON
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(analysis_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"✅ 成功解析ecg_analysisIII并保存到: {file_path}")
                    
                except json.JSONDecodeError:
                    print(f"⚠️ ecg_analysisIII不是有效的JSON格式")
                    # 保存原始内容
                    with open(file_path + ".txt", 'w', encoding='utf-8') as f:
                        f.write(str(ecg_analysisIII))
        
        print(f"\n分析完成。所有结果已保存到文件夹: {output_dir}")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        cursor.close()
        connection.close()

def analyze_database_structure():
    """
    分析整个数据库结构，包括表、字段和关系
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='information_schema',  # 使用information_schema数据库
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 创建结果目录
        output_dir = "database_structure_analysis"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 1. 查询所有数据库
        cursor.execute("SHOW DATABASES")
        databases = [row[0] for row in cursor.fetchall()]
        print(f"找到 {len(databases)} 个数据库:")
        for db in databases:
            print(f"- {db}")
        
        # 2. 主要分析backend_test数据库
        target_db = "backend_test"
        print(f"\n详细分析 {target_db} 数据库...")
        
        # 3. 获取所有表
        cursor.execute(f"""
            SELECT 
                table_name,
                table_rows,
                create_time,
                update_time,
                table_comment
            FROM tables 
            WHERE table_schema = '{target_db}'
            ORDER BY table_name
        """)
        
        tables = cursor.fetchall()
        print(f"找到 {len(tables)} 个表")
        
        # 保存表信息到文件
        with open(f"{output_dir}/tables_info.txt", "w", encoding="utf-8") as f:
            f.write(f"数据库 {target_db} 包含 {len(tables)} 个表:\n\n")
            f.write("表名\t\t\t\t记录数\t\t创建时间\t\t\t更新时间\t\t\t备注\n")
            f.write("-" * 120 + "\n")
            
            for table in tables:
                table_name, table_rows, create_time, update_time, comment = table
                f.write(f"{table_name:<30}\t{table_rows or 'N/A':<10}\t{create_time or 'N/A'}\t{update_time or 'N/A'}\t{comment or ''}\n")
        
        print(f"表信息已保存到 {output_dir}/tables_info.txt")
        
        # 4. 分析每个表的结构，特别关注ECG相关表
        ecg_tables = [t[0] for t in tables if "ecg" in t[0].lower()]
        print(f"\n找到 {len(ecg_tables)} 个ECG相关表")
        
        all_tables_structure = {}
        
        # 创建一个新连接使用backend_test数据库
        backend_connection = pymysql.connect(
            host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
            user='ai',
            password='wq$$4r%ixg',
            database='backend_test',
            port=3306,
            charset='utf8mb4'
        )
        
        backend_cursor = backend_connection.cursor()
        
        # 5. 详细分析所有表结构
        with open(f"{output_dir}/tables_structure.txt", "w", encoding="utf-8") as f:
            f.write(f"数据库 {target_db} 的表结构详情:\n\n")
            
            for table_name in sorted(t[0] for t in tables):
                f.write(f"\n{'=' * 80}\n")
                f.write(f"表: {table_name}\n")
                f.write(f"{'-' * 80}\n")
                
                # 获取表的字段信息
                cursor.execute(f"""
                    SELECT 
                        column_name, 
                        column_type,
                        is_nullable,
                        column_key,
                        column_default,
                        extra,
                        column_comment
                    FROM columns
                    WHERE table_schema = '{target_db}'
                    AND table_name = '{table_name}'
                    ORDER BY ordinal_position
                """)
                
                columns = cursor.fetchall()
                f.write("字段名\t\t\t类型\t\t\t可为空\t键\t默认值\t\t其他\t\t注释\n")
                f.write(f"{'-' * 120}\n")
                
                table_structure = []
                for col in columns:
                    col_name, col_type, nullable, key, default, extra, comment = col
                    table_structure.append({
                        "name": col_name,
                        "type": col_type,
                        "nullable": nullable,
                        "key": key,
                        "default": default,
                        "extra": extra,
                        "comment": comment
                    })
                    
                    f.write(f"{col_name:<20}\t{col_type:<20}\t{nullable:<5}\t{key or '-':<3}\t{str(default or '-'):<10}\t{extra or '-':<10}\t{comment or ''}\n")
                
                all_tables_structure[table_name] = table_structure
                
                # 如果是ECG表，获取一条样本数据
                if "ecg" in table_name.lower() and "_20" in table_name:
                    f.write(f"\n样本数据:\n")
                    try:
                        backend_cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
                        data = backend_cursor.fetchone()
                        if data:
                            for i, col in enumerate(columns):
                                value = data[i]
                                # 对于大型数据字段，只显示一部分
                                if isinstance(value, str) and len(value) > 100:
                                    value = value[:100] + "... (数据长度: " + str(len(value)) + ")"
                                f.write(f"{col[0]}: {value}\n")
                        else:
                            f.write("表为空，无样本数据\n")
                    except Exception as e:
                        f.write(f"获取样本数据出错: {str(e)}\n")
        
        print(f"表结构已保存到 {output_dir}/tables_structure.txt")
        
        # 6. 特别分析ECG表字段
        with open(f"{output_dir}/ecg_tables_analysis.txt", "w", encoding="utf-8") as f:
            f.write("ECG相关表分析\n\n")
            
            # 分析ECG表中的常见字段
            ecg_fields = defaultdict(int)
            
            for table_name in ecg_tables:
                if table_name in all_tables_structure:
                    for column in all_tables_structure[table_name]:
                        ecg_fields[column["name"]] += 1
            
            f.write("ECG表中的常见字段及出现次数:\n")
            for field, count in sorted(ecg_fields.items(), key=lambda x: x[1], reverse=True):
                f.write(f"{field}: 出现在 {count}/{len(ecg_tables)} 个ECG表中\n")
            
            # 分析特定ECG表的数据格式
            f.write("\n\nECG数据格式分析:\n")
            sample_table = next((t for t in ecg_tables if "_20" in t), ecg_tables[0] if ecg_tables else None)
            
            if sample_table:
                f.write(f"使用表 {sample_table} 作为样本分析\n\n")
                
                # 查找可能包含ECG数据的字段
                ecg_data_fields = [col["name"] for col in all_tables_structure[sample_table] 
                                  if any(kw in col["name"].lower() for kw in ["ecg", "data", "wave", "signal", "raw"])]
                
                f.write(f"可能包含ECG数据的字段: {', '.join(ecg_data_fields) or '未找到'}\n\n")
                
                # 获取几条数据样本进行分析
                try:
                    backend_cursor.execute(f"SELECT id, create_time, {', '.join(ecg_data_fields)} FROM {sample_table} LIMIT 3")
                    samples = backend_cursor.fetchall()
                    
                    column_names = ["id", "create_time"] + ecg_data_fields
                    
                    for i, sample in enumerate(samples):
                        f.write(f"样本 {i+1}:\n")
                        for j, field in enumerate(column_names):
                            value = sample[j]
                            if field in ecg_data_fields and value:
                                # 对于ECG数据字段，分析数据格式
                                if isinstance(value, str):
                                    if value.startswith("{") and value.endswith("}"):
                                        f.write(f"{field}: JSON对象 (长度: {len(value)})\n")
                                        # 尝试解析JSON结构
                                        try:
                                            data_json = json.loads(value)
                                            f.write(f"  JSON结构的键: {', '.join(data_json.keys())}\n")
                                            # 查找可能的ECG数据数组
                                            for key, val in data_json.items():
                                                if isinstance(val, list) and len(val) > 10:
                                                    f.write(f"  发现可能的数据数组: {key} (长度: {len(val)})\n")
                                                    if all(isinstance(x, (int, float)) for x in val[:10]):
                                                        f.write(f"  数组元素类型: 数值\n")
                                                        f.write(f"  数组示例: {val[:5]}\n")
                                        except:
                                            f.write("  JSON解析失败\n")
                                    elif value.startswith("[") and value.endswith("]"):
                                        f.write(f"{field}: JSON数组 (长度: {len(value)})\n")
                                    else:
                                        f.write(f"{field}: 字符串 (长度: {len(value)})\n")
                                else:
                                    f.write(f"{field}: {type(value).__name__}\n")
                            else:
                                f.write(f"{field}: {value}\n")
                        f.write("\n")
                except Exception as e:
                    f.write(f"分析样本数据出错: {str(e)}\n")
        
        print(f"ECG表分析已保存到 {output_dir}/ecg_tables_analysis.txt")
        
        # 7. 分析表关系
        with open(f"{output_dir}/table_relationships.txt", "w", encoding="utf-8") as f:
            f.write("表关系分析\n\n")
            
            # 查询所有外键关系
            cursor.execute(f"""
                SELECT 
                    table_name,
                    column_name,
                    referenced_table_name,
                    referenced_column_name
                FROM key_column_usage
                WHERE table_schema = '{target_db}'
                AND referenced_table_name IS NOT NULL
                ORDER BY table_name, column_name
            """)
            
            relations = cursor.fetchall()
            f.write(f"找到 {len(relations)} 个外键关系:\n\n")
            
            for relation in relations:
                table, column, ref_table, ref_column = relation
                f.write(f"{table}.{column} -> {ref_table}.{ref_column}\n")
            
            # 尝试识别其他命名模式的潜在关系
            f.write("\n潜在的命名模式关系 (不是外键但名称相似):\n")
            for table1 in (t[0] for t in tables):
                for table2 in (t[0] for t in tables):
                    if table1 != table2:
                        # 检查表名是否包含共同部分
                        common_prefix = os.path.commonprefix([table1, table2])
                        if len(common_prefix) > 3 and common_prefix != "t_" and table1 not in [r[0] for r in relations] and table2 not in [r[2] for r in relations]:
                            f.write(f"可能相关: {table1} <-> {table2} (共同前缀: {common_prefix})\n")
        
        print(f"表关系分析已保存到 {output_dir}/table_relationships.txt")
        
        # 8. 分析ECG数据表的时间序列
        with open(f"{output_dir}/ecg_tables_timeline.txt", "w", encoding="utf-8") as f:
            f.write("ECG表时间序列分析\n\n")
            
            # 查找按日期分表的ECG表
            date_pattern = r't_data_ecg_(\d{8})'
            date_tables = []
            
            for table in ecg_tables:
                import re
                match = re.search(date_pattern, table)
                if match:
                    date_str = match.group(1)
                    try:
                        date = datetime.strptime(date_str, '%Y%m%d')
                        date_tables.append((table, date))
                    except:
                        pass
            
            date_tables.sort(key=lambda x: x[1])
            f.write(f"找到 {len(date_tables)} 个按日期分表的ECG表\n\n")
            
            if date_tables:
                f.write("日期范围: {} 至 {}\n\n".format(
                    date_tables[0][1].strftime('%Y-%m-%d'),
                    date_tables[-1][1].strftime('%Y-%m-%d')
                ))
                
                f.write("表列表 (按日期排序):\n")
                for table, date in date_tables:
                    try:
                        backend_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = backend_cursor.fetchone()[0]
                        f.write(f"{date.strftime('%Y-%m-%d')}: {table} ({count} 条记录)\n")
                    except:
                        f.write(f"{date.strftime('%Y-%m-%d')}: {table} (无法获取记录数)\n")
        
        print(f"ECG表时间序列分析已保存到 {output_dir}/ecg_tables_timeline.txt")
        
        backend_cursor.close()
        backend_connection.close()
        
        print(f"\n数据库结构分析完成！所有结果已保存到 {output_dir} 目录")
        
    except Exception as e:
        print(f"数据库结构分析出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        cursor.close()
        connection.close()

def analyze_specific_ecg_table(table_name="t_data_ecg_20241031"):
    """
    专门分析指定的ECG表结构和数据
    Args:
        table_name: 要分析的表名，默认为t_data_ecg_20241031
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 创建结果目录
        output_dir = f"{table_name}_analysis"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        print(f"开始分析表 {table_name}...")
        
        # 1. 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test' 
            AND table_name = '{table_name}'
        """)
        if not cursor.fetchone()[0]:
            print(f"表 {table_name} 不存在!")
            return
            
        # 2. 获取表的基本信息
        cursor.execute(f"""
            SELECT 
                table_name,
                table_rows,
                create_time,
                update_time,
                table_comment
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test'
            AND table_name = '{table_name}'
        """)
        
        table_info = cursor.fetchone()
        if not table_info:
            print(f"无法获取表 {table_name} 的信息")
            return
            
        print("\n表基本信息:")
        print(f"表名: {table_info[0]}")
        print(f"记录数: {table_info[1] or '未知'}")
        print(f"创建时间: {table_info[2]}")
        print(f"更新时间: {table_info[3]}")
        print(f"备注: {table_info[4] or '无'}")
        
        # 3. 获取表的字段信息
        cursor.execute(f"""
            SELECT 
                column_name, 
                column_type,
                is_nullable,
                column_key,
                column_default,
                extra,
                column_comment
            FROM information_schema.columns
            WHERE table_schema = 'backend_test'
            AND table_name = '{table_name}'
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        print(f"\n找到 {len(columns)} 个字段")
        
        # 保存字段信息到文件
        with open(f"{output_dir}/table_structure.txt", "w", encoding="utf-8") as f:
            f.write(f"表 {table_name} 的结构:\n\n")
            f.write("字段名\t\t\t类型\t\t\t可为空\t键\t默认值\t\t其他\t\t注释\n")
            f.write("-" * 120 + "\n")
            
            for col in columns:
                col_name, col_type, nullable, key, default, extra, comment = col
                f.write(f"{col_name:<20}\t{col_type:<20}\t{nullable:<5}\t{key or '-':<3}\t{str(default or '-'):<10}\t{extra or '-':<10}\t{comment or ''}\n")
        
        print(f"表结构已保存到 {output_dir}/table_structure.txt")
        
        # 4. 获取表的记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        record_count = cursor.fetchone()[0]
        print(f"\n表 {table_name} 共有 {record_count} 条记录")
        
        if record_count == 0:
            print(f"表 {table_name} 没有数据，分析结束")
            return
        
        # 5. 找出可能包含ECG数据的字段
        ecg_fields = [col[0] for col in columns if any(
            keyword in col[0].lower() for keyword in [
                'ecg', 'data', 'wave', 'signal', 'raw', 'lead', 
                'i_lead', 'lead_i', 'lead1', 'leadi'
            ]
        )]
        
        print(f"\n可能包含ECG数据的字段: {', '.join(ecg_fields)}")
        
        # 6. 获取几条样本数据进行分析
        with open(f"{output_dir}/sample_data_analysis.txt", "w", encoding="utf-8") as f:
            f.write(f"表 {table_name} 的样本数据分析:\n\n")
            
            # 记录总数信息
            f.write(f"表记录总数: {record_count}\n\n")
            
            # 获取字段大小统计
            f.write("ECG相关字段大小统计:\n")
            f.write("字段名\t\t\t最小长度\t最大长度\t平均长度\t非空比例\n")
            f.write("-" * 80 + "\n")
            
            for field in ecg_fields:
                try:
                    # 获取非空记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {field} IS NOT NULL")
                    not_null_count = cursor.fetchone()[0]
                    not_null_ratio = not_null_count / record_count if record_count > 0 else 0
                    
                    # 获取字段长度信息
                    cursor.execute(f"""
                        SELECT 
                            MIN(LENGTH({field})), 
                            MAX(LENGTH({field})), 
                            AVG(LENGTH({field}))
                        FROM {table_name}
                        WHERE {field} IS NOT NULL
                    """)
                    min_len, max_len, avg_len = cursor.fetchone()
                    
                    f.write(f"{field:<20}\t{min_len or 'N/A':<10}\t{max_len or 'N/A':<10}\t{avg_len or 'N/A':<10.2f}\t{not_null_ratio:<.2%}\n")
                except Exception as e:
                    f.write(f"{field:<20}\t获取统计信息出错: {str(e)}\n")
            
            f.write("\n")
            
            # 获取样本记录
            sample_size = min(5, record_count)
            f.write(f"样本数据分析 ({sample_size} 条记录):\n\n")
            
            cursor.execute(f"""
                SELECT id, create_time, union_id, {', '.join(ecg_fields)}
                FROM {table_name}
                WHERE {ecg_fields[0] if ecg_fields else 'id'} IS NOT NULL
                LIMIT {sample_size}
            """)
            
            samples = cursor.fetchall()
            column_names = ["id", "create_time", "union_id"] + ecg_fields
            
            # 分析每个样本记录
            for i, sample in enumerate(samples):
                f.write(f"样本 {i+1}:\n")
                f.write(f"ID: {sample[0]}\n")
                f.write(f"时间: {sample[1]}\n")
                f.write(f"union_id: {sample[2]}\n")
                
                # 保存完整原始数据到单独文件
                raw_data_file = f"{output_dir}/sample_{i+1}_raw_data.json"
                with open(raw_data_file, "w", encoding="utf-8") as raw_f:
                    sample_dict = {}
                    for j, field in enumerate(column_names):
                        if j >= 3:  # 只保存ECG相关字段
                            value = sample[j]
                            # 转换不可序列化的类型
                            if isinstance(value, (datetime, pd.Timestamp)):
                                sample_dict[field] = value.isoformat()
                            elif pd and isinstance(value, pd.Series):
                                sample_dict[field] = value.to_dict()
                            elif hasattr(value, 'tolist'):  # numpy arrays
                                sample_dict[field] = value.tolist()
                            else:
                                sample_dict[field] = value
                    
                    json.dump(sample_dict, raw_f, ensure_ascii=False, indent=2, default=str)
                
                # 详细分析ECG字段
                for j, field in enumerate(column_names):
                    if j < 3:  # 跳过非ECG字段
                        continue
                        
                    value = sample[j]
                    if value:
                        f.write(f"\n字段: {field}\n")
                        f.write(f"数据长度: {len(str(value)) if isinstance(value, str) else 'N/A'}\n")
                        
                        # 详细分析ECG数据格式
                        if isinstance(value, str):
                            if value.startswith("{") and value.endswith("}"):
                                f.write("数据类型: JSON对象\n")
                                
                                # 保存格式化的JSON到单独文件
                                json_file = f"{output_dir}/sample_{i+1}_{field}.json"
                                try:
                                    # 解析JSON
                                    data_json = json.loads(value)
                                    with open(json_file, "w", encoding="utf-8") as json_f:
                                        json.dump(data_json, json_f, ensure_ascii=False, indent=2)
                                    
                                    f.write(f"完整JSON已保存到: {json_file}\n")
                                    f.write(f"JSON顶级键: {', '.join(data_json.keys())}\n")
                                    
                                    # 查找可能包含I导联数据的字段（特别关注）
                                    lead_i_keys = []
                                    lead_data_keys = []
                                    
                                    def search_for_lead_data(obj, prefix=""):
                                        if isinstance(obj, dict):
                                            for k, v in obj.items():
                                                key_path = f"{prefix}.{k}" if prefix else k
                                                
                                                # 检查是否包含 lead_i 相关关键字
                                                if any(keyword in k.lower() for keyword in ["lead_i", "leadi", "lead i", "i-lead", "i_lead"]):
                                                    lead_i_keys.append((key_path, v))
                                                    
                                                # 检查是否是数值数组，可能是ECG数据
                                                if isinstance(v, list) and len(v) > 10 and all(isinstance(x, (int, float)) for x in v[:10]):
                                                    lead_data_keys.append((key_path, len(v)))
                                                    
                                                search_for_lead_data(v, key_path)
                                        elif isinstance(obj, list) and len(obj) > 0 and isinstance(obj[0], dict):
                                            for i, item in enumerate(obj[:3]):  # 只检查前3个元素
                                                search_for_lead_data(item, f"{prefix}[{i}]")
                                    
                                    search_for_lead_data(data_json)
                                    
                                    if lead_i_keys:
                                        f.write("\n找到可能的I导联相关字段:\n")
                                        for key_path, value in lead_i_keys:
                                            if isinstance(value, list):
                                                f.write(f"- {key_path}: 数组 (长度: {len(value)})\n")
                                                
                                                # 保存I导联数据到单独文件
                                                if len(value) > 10 and all(isinstance(x, (int, float)) for x in value[:10]):
                                                    lead_file = f"{output_dir}/sample_{i+1}_{field}_{key_path.replace('.', '_')}_lead_I.json"
                                                    with open(lead_file, "w", encoding="utf-8") as lead_f:
                                                        json.dump(value, lead_f, ensure_ascii=False, indent=2)
                                                    f.write(f"  I导联数据已保存到: {lead_file}\n")
                                                    f.write(f"  数据示例: {value[:5]}\n")
                                            else:
                                                f.write(f"- {key_path}: {type(value).__name__}\n")
                                                if isinstance(value, dict):
                                                    f.write(f"  键: {', '.join(value.keys())}\n")
                                    
                                    if lead_data_keys and not lead_i_keys:
                                        f.write("\n找到可能的ECG数据数组 (未特定标记为I导联):\n")
                                        for key_path, length in lead_data_keys:
                                            f.write(f"- {key_path}: 数组 (长度: {length})\n")
                                            
                                            # 获取数组值
                                            array_value = data_json
                                            for key in key_path.split('.'):
                                                if '[' in key:
                                                    key_name, idx = key.split('[')
                                                    idx = int(idx.replace(']', ''))
                                                    array_value = array_value[key_name][idx] if key_name else array_value[idx]
                                                else:
                                                    array_value = array_value[key]
                                            
                                            # 保存到单独文件
                                            array_file = f"{output_dir}/sample_{i+1}_{field}_{key_path.replace('.', '_').replace('[', '_').replace(']', '')}_array.json"
                                            with open(array_file, "w", encoding="utf-8") as array_f:
                                                json.dump(array_value, array_f, ensure_ascii=False, indent=2)
                                            f.write(f"  数据已保存到: {array_file}\n")
                                            f.write(f"  数据示例: {array_value[:5]}\n")
                                    
                                except Exception as e:
                                    f.write(f"解析JSON出错: {str(e)}\n")
                                    with open(json_file + ".txt", "w", encoding="utf-8") as txt_f:
                                        txt_f.write(value)
                                    f.write(f"原始内容已保存到: {json_file}.txt\n")
                            elif value.startswith("[") and value.endswith("]"):
                                f.write("数据类型: JSON数组\n")
                                
                                # 尝试解析数组
                                try:
                                    data_array = json.loads(value)
                                    f.write(f"数组长度: {len(data_array)}\n")
                                    
                                    # 判断是否可能是ECG数据点
                                    if len(data_array) > 10 and all(isinstance(x, (int, float)) for x in data_array[:10]):
                                        f.write("可能是ECG数据点\n")
                                        f.write(f"数据示例: {data_array[:5]}\n")
                                        
                                        # 保存到单独文件
                                        array_file = f"{output_dir}/sample_{i+1}_{field}_array.json"
                                        with open(array_file, "w", encoding="utf-8") as array_f:
                                            json.dump(data_array, array_f, ensure_ascii=False, indent=2)
                                        f.write(f"完整数组已保存到: {array_file}\n")
                                except Exception as e:
                                    f.write(f"解析数组失败: {str(e)}\n")
                            else:
                                f.write("数据类型: 其他字符串\n")
                                f.write(f"数据片段: {value[:100]}...\n" if len(value) > 100 else f"数据内容: {value}\n")
                        else:
                            f.write(f"数据类型: {type(value).__name__}\n")
                    else:
                        f.write(f"\n字段: {field} - 为空\n")
                
                f.write("\n" + "=" * 80 + "\n")
        
        print(f"样本数据分析已保存到 {output_dir}/sample_data_analysis.txt")
        
        # 7. 分析union_id分布
        with open(f"{output_dir}/union_id_analysis.txt", "w", encoding="utf-8") as f:
            f.write(f"表 {table_name} 的union_id分布分析:\n\n")
            
            cursor.execute(f"""
                SELECT union_id, COUNT(*) as count
                FROM {table_name}
                GROUP BY union_id
                ORDER BY count DESC
                LIMIT 20
            """)
            
            union_id_stats = cursor.fetchall()
            f.write(f"Top 20 union_id记录分布:\n")
            f.write("union_id\t\t\t\t记录数\t百分比\n")
            f.write("-" * 80 + "\n")
            
            for union_id, count in union_id_stats:
                percent = (count / record_count) * 100 if record_count > 0 else 0
                f.write(f"{union_id:<40}\t{count:<8}\t{percent:.2f}%\n")
                
            # 检查设备vs用户数据分布
            cursor.execute(f"""
                SELECT 
                    CASE 
                        WHEN union_id LIKE 'M-100000-%' THEN '设备数据'
                        WHEN union_id LIKE 'CUSTOMER%' THEN '用户数据'
                        ELSE '其他'
                    END AS data_type,
                    COUNT(*) as count
                FROM {table_name}
                GROUP BY data_type
            """)
            
            type_stats = cursor.fetchall()
            f.write("\n数据类型分布:\n")
            f.write("数据类型\t记录数\t百分比\n")
            f.write("-" * 40 + "\n")
            
            for data_type, count in type_stats:
                percent = (count / record_count) * 100 if record_count > 0 else 0
                f.write(f"{data_type}\t{count}\t{percent:.2f}%\n")
        
        print(f"union_id分布分析已保存到 {output_dir}/union_id_analysis.txt")
        
        # 8. 检查特定手机号的数据 (移除设备相关查询)
        phone = "18511664543"
        with open(f"{output_dir}/phone_{phone}_data.txt", "w", encoding="utf-8") as f:
            f.write(f"查找手机号 {phone} 的数据:\n\n")
            
            # 先查找用户的union_id
            cursor.execute("""
                SELECT id, union_id 
                FROM jahr_customer_user 
                WHERE phone = %s AND deleted = 0
            """, (phone,))
            
            user_info = cursor.fetchone()
            if user_info:
                user_id, union_id = user_info
                f.write(f"用户ID: {user_id}\n")
                f.write(f"union_id: {union_id}\n\n")
                
                # 查询该用户在表中的记录
                cursor.execute(f"""
                    SELECT COUNT(*), MIN(create_time), MAX(create_time)
                    FROM {table_name}
                    WHERE union_id = %s
                """, (union_id,))
                
                count, min_time, max_time = cursor.fetchone()
                f.write(f"在表 {table_name} 中找到 {count} 条记录\n")
                if count > 0:
                    f.write(f"时间范围: {min_time} 至 {max_time}\n\n")
                    
                    # 获取该用户所有记录
                    cursor.execute(f"""
                        SELECT id, create_time, {', '.join(ecg_fields)}
                        FROM {table_name}
                        WHERE union_id = %s
                    """, (union_id,))
                    
                    user_samples = cursor.fetchall()
                    f.write(f"获取到 {len(user_samples)} 条记录，详细分析每条记录:\n\n")
                    
                    for idx, sample in enumerate(user_samples):
                        f.write(f"记录 {idx+1}:\n")
                        f.write(f"ID: {sample[0]}\n")
                        f.write(f"时间: {sample[1]}\n")
                        
                        # 保存用户的记录到单独文件
                        user_data_file = f"{output_dir}/{phone}_record_{idx+1}.json"
                        with open(user_data_file, "w", encoding="utf-8") as user_f:
                            record_dict = {
                                "id": sample[0],
                                "create_time": sample[1].isoformat() if sample[1] else None
                            }
                            
                            for i, field in enumerate(ecg_fields):
                                value = sample[i+2]
                                if value:
                                    if isinstance(value, (datetime, pd.Timestamp)):
                                        record_dict[field] = value.isoformat()
                                    elif pd and isinstance(value, pd.Series):
                                        record_dict[field] = value.to_dict()
                                    elif hasattr(value, 'tolist'):  # numpy arrays
                                        record_dict[field] = value.tolist()
                                    else:
                                        record_dict[field] = value
                            
                            json.dump(record_dict, user_f, ensure_ascii=False, indent=2, default=str)
                        
                        # 分析每个ECG相关字段
                        for i, field in enumerate(ecg_fields):
                            value = sample[i+2]
                            if value:
                                f.write(f"\n字段: {field}\n")
                                
                                # 对字符串类型的JSON做详细分析
                                if isinstance(value, str):
                                    if value.startswith("{") and value.endswith("}"):
                                        try:
                                            data_json = json.loads(value)
                                            field_file = f"{output_dir}/{phone}_record_{idx+1}_{field}.json"
                                            with open(field_file, "w", encoding="utf-8") as field_f:
                                                json.dump(data_json, field_f, ensure_ascii=False, indent=2)
                                            f.write(f"JSON数据已保存到: {field_file}\n")
                                            
                                            # 寻找I导联数据
                                            def find_lead_i_data(obj, path=""):
                                                lead_i_data = []
                                                
                                                if isinstance(obj, dict):
                                                    for k, v in obj.items():
                                                        new_path = f"{path}.{k}" if path else k
                                                        if any(keyword in k.lower() for keyword in 
                                                              ["lead_i", "leadi", "lead i", "i-lead", "lead1"]):
                                                            lead_i_data.append((new_path, v))
                                                        lead_i_data.extend(find_lead_i_data(v, new_path))
                                                
                                                return lead_i_data
                                            
                                            lead_data = find_lead_i_data(data_json)
                                            if lead_data:
                                                f.write("找到可能的I导联数据:\n")
                                                for path, value in lead_data:
                                                    f.write(f"- {path}: {type(value).__name__}\n")
                                                    if isinstance(value, list) and len(value) > 5:
                                                        f.write(f"  数组长度: {len(value)}\n")
                                                        f.write(f"  数据示例: {value[:5]}\n")
                                                        
                                                        # 保存导联数据
                                                        lead_file = f"{output_dir}/{phone}_record_{idx+1}_{field}_{path.replace('.', '_')}_lead_I.json"
                                                        with open(lead_file, "w", encoding="utf-8") as lead_f:
                                                            json.dump(value, lead_f, ensure_ascii=False, indent=2)
                                                        f.write(f"  完整数据已保存到: {lead_file}\n")
                                            
                                            # 寻找任何长数组，可能是ECG数据
                                            def find_long_arrays(obj, path=""):
                                                arrays = []
                                                
                                                if isinstance(obj, dict):
                                                    for k, v in obj.items():
                                                        new_path = f"{path}.{k}" if path else k
                                                        if isinstance(v, list) and len(v) > 50 and all(isinstance(x, (int, float)) for x in v[:10]):
                                                            arrays.append((new_path, v))
                                                        arrays.extend(find_long_arrays(v, new_path))
                                                
                                                return arrays
                                            
                                            array_data = find_long_arrays(data_json)
                                            if array_data:
                                                f.write("\n找到可能的ECG数据数组:\n")
                                                for path, value in array_data:
                                                    f.write(f"- {path}: 长度 {len(value)}\n")
                                                    f.write(f"  数据示例: {value[:5]}\n")
                                                    
                                                    # 保存数组数据
                                                    array_file = f"{output_dir}/{phone}_record_{idx+1}_{field}_{path.replace('.', '_')}_array.json"
                                                    with open(array_file, "w", encoding="utf-8") as array_f:
                                                        json.dump(value, array_f, ensure_ascii=False, indent=2)
                                                    f.write(f"  完整数组已保存到: {array_file}\n")
                                                    
                                        except Exception as e:
                                            f.write(f"解析JSON出错: {str(e)}\n")
                                    elif value.startswith("[") and value.endswith("]"):
                                        try:
                                            data_array = json.loads(value)
                                            if isinstance(data_array, list) and len(data_array) > 5:
                                                f.write(f"数组长度: {len(data_array)}\n")
                                                f.write(f"数据示例: {data_array[:5]}\n")
                                                
                                                # 保存到单独文件
                                                array_file = f"{output_dir}/{phone}_record_{idx+1}_{field}_array.json"
                                                with open(array_file, "w", encoding="utf-8") as array_f:
                                                    json.dump(data_array, array_f, ensure_ascii=False, indent=2)
                                                f.write(f"数组已保存到: {array_file}\n")
                                        except Exception as e:
                                            f.write(f"解析数组失败: {str(e)}\n")
                                    else:
                                        f.write(f"字符串内容: {value[:100]}...\n" if len(value) > 100 else f"字符串内容: {value}\n")
                                else:
                                    f.write(f"非字符串类型: {type(value).__name__}\n")
                            else:
                                f.write(f"\n字段: {field} - 为空\n")
                        
                        f.write("\n" + "=" * 80 + "\n\n")
                else:
                    f.write("该手机号的用户在此表中没有数据记录\n")
            else:
                f.write(f"未找到手机号为 {phone} 的用户\n")
        
        print(f"手机号 {phone} 的数据分析已保存到 {output_dir}/phone_{phone}_data.txt")
        
        print(f"\n表 {table_name} 分析完成！所有结果已保存到 {output_dir} 目录")
        
    except Exception as e:
        print(f"分析表结构出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        cursor.close()
        connection.close()

def list_ecg_tables_by_date_range():
    """
    按日期范围列出所有ECG表并分析其数据量
    """
    connection = pymysql.connect(
        host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
        user='ai',
        password='wq$$4r%ixg',
        database='backend_test',
        port=3306,
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        # 创建结果目录
        output_dir = "ecg_tables_analysis"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # 获取所有ECG表
        cursor.execute("""
            SELECT 
                table_name,
                table_rows,
                create_time,
                update_time
            FROM information_schema.tables 
            WHERE table_schema = 'backend_test'
            AND table_name LIKE 't_data_ecg_%'
            ORDER BY table_name DESC
        """)
        
        tables = cursor.fetchall()
        print(f"找到 {len(tables)} 个ECG表")
        
        with open(f"{output_dir}/ecg_tables_summary.txt", "w", encoding="utf-8") as f:
            f.write("ECG表数据分析汇总\n\n")
            f.write("表名\t\t\t记录数\t\t创建时间\t\t更新时间\t\t日期\t\t非空数据比例\n")
            f.write("-" * 120 + "\n")
            
            tables_with_data = []
            tables_by_year = defaultdict(list)
            
            for table in tables:
                table_name, rows, create_time, update_time = table
                
                # 从表名中提取日期
                date_part = table_name.replace("t_data_ecg_", "")
                date_str = date_part if len(date_part) == 8 else "未知日期"
                
                if len(date_part) == 8:
                    year = date_part[:4]
                    tables_by_year[year].append((table_name, rows or 0, date_str))
                
                # 获取表中数据量
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    actual_count = cursor.fetchone()[0]
                    
                    # 检查ECG数据字段非空记录比例
                    cursor.execute(f"""
                        SELECT column_name 
                        FROM information_schema.columns
                        WHERE table_schema = 'backend_test'
                        AND table_name = '{table_name}'
                        AND column_name LIKE '%ecg%'
                    """)
                    
                    ecg_fields = [col[0] for col in cursor.fetchall()]
                    non_null_ratio = "N/A"
                    
                    if ecg_fields and actual_count > 0:
                        field_conditions = " OR ".join([f"{field} IS NOT NULL" for field in ecg_fields])
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {field_conditions}")
                        non_null_count = cursor.fetchone()[0]
                        non_null_ratio = f"{(non_null_count / actual_count) * 100:.2f}%"
                    
                    f.write(f"{table_name}\t{actual_count}\t{create_time}\t{update_time}\t{date_str}\t{non_null_ratio}\n")
                    
                    if actual_count > 0:
                        tables_with_data.append((table_name, actual_count, date_str))
                except Exception as e:
                    f.write(f"{table_name}\t查询出错: {str(e)}\t{create_time}\t{update_time}\t{date_str}\tN/A\n")
            
            # 按年份汇总
            f.write("\n\n按年份统计ECG表数据量:\n")
            for year, year_tables in sorted(tables_by_year.items(), reverse=True):
                total_tables = len(year_tables)
                total_records = sum(count for _, count, _ in year_tables)
                f.write(f"\n{year}年 - 总表数: {total_tables}, 总记录数: {total_records}\n")
                
                # 按月汇总
                months = defaultdict(int)
                for _, count, date_str in year_tables:
                    if len(date_str) == 8:
                        month = date_str[4:6]
                        months[month] += count
                
                for month, count in sorted(months.items()):
                    f.write(f"  {month}月: {count} 条记录\n")
            
            # 输出有数据的表
            f.write("\n\n有数据的ECG表(按记录数排序):\n")
            for table_name, count, date_str in sorted(tables_with_data, key=lambda x: x[1], reverse=True)[:20]:
                f.write(f"{table_name}\t{count}\t{date_str}\n")
            
        print(f"ECG表分析结果已保存到 {output_dir}/ecg_tables_summary.txt")
        
        # 选择几个有数据的表进行详细分析
        if tables_with_data:
            print("\n推荐样本表进行深入分析:")
            # 按时间选择最近的几个表
            recent_tables = sorted(tables_with_data, key=lambda x: x[2], reverse=True)[:5]
            for table_name, count, date_str in recent_tables:
                print(f"推荐分析表: {table_name}, 记录数: {count}")
            
            # 建议用户使用哪个表
            recommended_table = recent_tables[0][0] if recent_tables else None
            if recommended_table:
                with open(f"{output_dir}/recommended_table.txt", "w", encoding="utf-8") as rec_f:
                    rec_f.write(f"推荐分析表: {recommended_table}\n")
                    rec_f.write(f"使用方法: analyze_specific_ecg_table(\"{recommended_table}\")")
                
                print(f"\n最佳推荐表: {recommended_table}")
                return recommended_table
        
        return None
        
    except Exception as e:
        print(f"分析ECG表出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        cursor.close()
        connection.close()

# 测试代码
if __name__ == "__main__":
    # 注释掉其他函数调用
    # extract_ecg_lead_data("18511664543", "20250325", lead_type="I")
    # extract_ecg_analysis("18511664543", "20250325")
    # analyze_database_structure()
    
    # 直接分析用户关心的日期表
    analyze_specific_ecg_table("t_data_ecg_20250325")
    
    # 之前的代码
    # best_table = list_ecg_tables_by_date_range()
    # if best_table:
    #     print(f"\n正在分析推荐表: {best_table}...")
    #     analyze_specific_ecg_table(best_table)
    
    # 其他函数调用
    # query_data_by_date("18511664543", "20250325", export_csv=True, export_ecg=True)
    # search_user_all_ways(phone="13970482552")