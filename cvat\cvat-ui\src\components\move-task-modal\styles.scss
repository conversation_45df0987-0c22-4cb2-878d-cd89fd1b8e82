// Copyright (C) 2021-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-task-move-modal  {
    .ant-modal-header {
        .anticon {
            margin-left: $grid-unit-size;

            svg {
                color: $text-color-secondary;
            }
        }
    }

    .ant-modal-body {
        > div:nth-child(1) {
            margin-bottom: $grid-unit-size * 2;

            > div:nth-child(1) {
                padding-right: $grid-unit-size * 2;
            }
        }
    }



    .ant-select {
        margin: 0 $grid-unit-size;
        width: $grid-unit-size * 25;
    }
}

.cvat-move-task-label-mapper-item {
    margin: $grid-unit-size * 2 0;
}
