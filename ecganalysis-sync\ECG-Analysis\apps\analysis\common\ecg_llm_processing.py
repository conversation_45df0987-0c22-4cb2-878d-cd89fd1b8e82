import requests
import json


def llm_api(prompt_intext,  sampling_rate):
    '''
    pass
    '''
    prompt = f"""基于心电数据诊断疾病，疾病划分[正常心电图、窦速、窦缓、窦性心律不齐、房颤、噪音]这六个,对于六种病症的定义如下，正常心电图：RR间期变异系数>0.12;
    窦速定义：心率>100次/min;
    窦缓定义：心率<60次/min;
    窦性心律不齐：RR间期变异系数>0.12且小于0.8;
    房颤：RR间期标准差>0.1,RR间期变异系数>0.15,RR间期的IQR>0.05;
    噪音:RR间期变异系数大于10或者小于0.03;
    正常心电图：RR间期变异系数<0.1 且与其他病症互斥。目前已知一个人的10S采样率为{sampling_rate}的心电数据，
    {prompt_intext}请帮我诊病，只给出疾病划分结论,多个结论保存为list，不要解释，不要输出多余内容。
    """
    # 设置请求的 URL
    url = 'http://qwen.aiweihe.com/v1/chat/completions'
    data = {
        "model": "Qwen2-7B-Instruct",
        "messages": [
            {"role": "system", "content": "你是卫和医疗研发的AI心电图医生"},
            {"role": "user", "content": prompt}
        ], 'temperature': 0.8, 'top_p': 0.9
    }

    headers = {
        'Content-Type': 'application/json'
    }
    try:
        response = requests.post(url, data=json.dumps(data), headers=headers, timeout=(2, 5))
        if response.status_code == 200:
            response_data = response.json()
            res = response_data['choices'][0]['message']['content'].replace('[', '').replace(']', '').replace("'",
                                                                                                              '').split(
                ',')
            return res
    except:
        return []
