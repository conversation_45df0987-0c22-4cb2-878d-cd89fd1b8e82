import os
import numpy as np
from scipy.signal import butter, lfilter
from tensorflow.keras.models import load_model
import pandas as pd
from sklearn.preprocessing import StandardScaler


def butter_lowpass_filter(data, cutoff=45, sampling_rate=500, order=5):
    nyq = 0.5 * sampling_rate
    normal_cutoff = cutoff / nyq
    b, a = butter(order, normal_cutoff, btype='low', analog=False)
    y = lfilter(b, a, data)
    return y


def preprocess_data(data, sample_points=5000, model_input_length=21):
    if data.ndim == 1:
        data = data.reshape(1, -1)

    ecg_data = data[:, 0:]
    filtered_ecg_signals = []
    for signal in ecg_data:
        signal = np.array(signal, dtype=np.float32)
        signal = np.nan_to_num(signal, nan=0.0, posinf=0.0, neginf=0.0)
        if len(signal) > sample_points:
            signal = signal[:sample_points]
        elif len(signal) < sample_points:
            signal = np.pad(signal, (0, sample_points - len(signal)), 'constant', constant_values=(0, 0))
        filtered_signal = butter_lowpass_filter(signal)

        if len(filtered_signal) > model_input_length:
            filtered_signal = filtered_signal[:model_input_length]
        elif len(filtered_signal) < model_input_length:
            filtered_signal = np.pad(filtered_signal, (0, model_input_length - len(filtered_signal)), 'constant', constant_values=(0, 0))

        filtered_ecg_signals.append(filtered_signal)

    return filtered_ecg_signals


def preprocess_features(ecg_signals):
    X = np.array(ecg_signals)

    X = X.reshape(X.shape[0], X.shape[1], 1)

    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X.reshape(-1, X.shape[-1])).reshape(X.shape)

    return X_scaled


# 3. 加载模型
def predict_with_model(model_filename, ecg_data, model_input_length=21):
    model = load_model(model_filename)

    filtered_ecg_signals = preprocess_data(ecg_data, model_input_length=model_input_length)
    ecg_data = preprocess_features(filtered_ecg_signals)

    predictions = model.predict(ecg_data).flatten()

    # 输出结果
    predictions = pd.DataFrame({'预测年龄': predictions})

    return predictions


def process(ecg_data, sampling_rate=500):
    """
    心脏年龄诊断
    :param ecg_data: 输入的ECG信号数据
    :param sampling_rate: 采样率
    :return: 预测的心脏年龄（整数）
    """
    if ecg_data.ndim == 1:
        ecg_data = ecg_data.reshape(1, -1)

    current_file_path = os.path.abspath(__file__)
    current_dir = os.path.dirname(current_file_path)
    model_path = os.path.join(current_dir, 'model', 'tf_ecg_model_cnn_1125.h5')

    predictions = predict_with_model(model_path, ecg_data)

    # 返回第一个预测值作为整数
    return int(predictions['预测年龄'].iloc[0])