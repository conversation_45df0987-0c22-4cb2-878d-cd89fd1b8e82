# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/core/profiler/profiler_service_monitor_result.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/core/profiler/profiler_service_monitor_result.proto',
  package='tensorflow',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n>tensorflow/core/profiler/profiler_service_monitor_result.proto\x12\ntensorflow\"\xad\x03\n\x1cProfilerServiceMonitorResult\x12L\n\rresponse_type\x18\x01 \x01(\x0e\x32\x35.tensorflow.ProfilerServiceMonitorResult.ResponseType\x12 \n\x18\x64\x65vice_idle_time_percent\x18\x02 \x01(\x01\x12\'\n\x1fmatrix_unit_utilization_percent\x18\x03 \x01(\x01\x12\x18\n\x10step_time_ms_avg\x18\x04 \x01(\x01\x12\x18\n\x10step_time_ms_min\x18\x05 \x01(\x01\x12\x18\n\x10step_time_ms_max\x18\x06 \x01(\x01\x12\x1a\n\x12infeed_percent_avg\x18\x07 \x01(\x01\x12\x1a\n\x12infeed_percent_min\x18\x08 \x01(\x01\x12\x1a\n\x12infeed_percent_max\x18\t \x01(\x01\"R\n\x0cResponseType\x12\x10\n\x0c\x45MPTY_RESULT\x10\x00\x12\r\n\tUTIL_ONLY\x10\x01\x12\r\n\tUTIL_IDLE\x10\x02\x12\x12\n\x0eUTIL_IDLE_STEP\x10\x03\x62\x06proto3')
)



_PROFILERSERVICEMONITORRESULT_RESPONSETYPE = _descriptor.EnumDescriptor(
  name='ResponseType',
  full_name='tensorflow.ProfilerServiceMonitorResult.ResponseType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='EMPTY_RESULT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UTIL_ONLY', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UTIL_IDLE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UTIL_IDLE_STEP', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=426,
  serialized_end=508,
)
_sym_db.RegisterEnumDescriptor(_PROFILERSERVICEMONITORRESULT_RESPONSETYPE)


_PROFILERSERVICEMONITORRESULT = _descriptor.Descriptor(
  name='ProfilerServiceMonitorResult',
  full_name='tensorflow.ProfilerServiceMonitorResult',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='response_type', full_name='tensorflow.ProfilerServiceMonitorResult.response_type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_idle_time_percent', full_name='tensorflow.ProfilerServiceMonitorResult.device_idle_time_percent', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='matrix_unit_utilization_percent', full_name='tensorflow.ProfilerServiceMonitorResult.matrix_unit_utilization_percent', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='step_time_ms_avg', full_name='tensorflow.ProfilerServiceMonitorResult.step_time_ms_avg', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='step_time_ms_min', full_name='tensorflow.ProfilerServiceMonitorResult.step_time_ms_min', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='step_time_ms_max', full_name='tensorflow.ProfilerServiceMonitorResult.step_time_ms_max', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infeed_percent_avg', full_name='tensorflow.ProfilerServiceMonitorResult.infeed_percent_avg', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infeed_percent_min', full_name='tensorflow.ProfilerServiceMonitorResult.infeed_percent_min', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infeed_percent_max', full_name='tensorflow.ProfilerServiceMonitorResult.infeed_percent_max', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _PROFILERSERVICEMONITORRESULT_RESPONSETYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=79,
  serialized_end=508,
)

_PROFILERSERVICEMONITORRESULT.fields_by_name['response_type'].enum_type = _PROFILERSERVICEMONITORRESULT_RESPONSETYPE
_PROFILERSERVICEMONITORRESULT_RESPONSETYPE.containing_type = _PROFILERSERVICEMONITORRESULT
DESCRIPTOR.message_types_by_name['ProfilerServiceMonitorResult'] = _PROFILERSERVICEMONITORRESULT
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ProfilerServiceMonitorResult = _reflection.GeneratedProtocolMessageType('ProfilerServiceMonitorResult', (_message.Message,), {
  'DESCRIPTOR' : _PROFILERSERVICEMONITORRESULT,
  '__module__' : 'tensorflow.core.profiler.profiler_service_monitor_result_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.ProfilerServiceMonitorResult)
  })
_sym_db.RegisterMessage(ProfilerServiceMonitorResult)


# @@protoc_insertion_point(module_scope)
