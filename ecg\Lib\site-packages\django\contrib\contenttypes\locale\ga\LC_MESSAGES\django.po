# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2019-06-22 21:48+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Irish (http://www.transifex.com/django/django/language/ga/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ga\n"
"Plural-Forms: nplurals=5; plural=(n==1 ? 0 : n==2 ? 1 : n<7 ? 2 : n<11 ? 3 : "
"4);\n"

msgid "Content Types"
msgstr "Cineál Inneachair"

msgid "python model class name"
msgstr "píotón samhail aicme ainm"

msgid "content type"
msgstr "tíopa inneachar "

msgid "content types"
msgstr "tíopaI inneachair"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Ní bhaineann samhail leis an cineál inneachar %(ct_id)s"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Níl cineál inneachar %(ct_id)s oibiacht %(obj_id)s ann"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "Níl modh get_absolute_url() ag %(ct_name)s oibiachtaí"
