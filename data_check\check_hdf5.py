import h5py
import numpy as np
import matplotlib.pyplot as plt

def check_hdf5_file(file_path):
    with h5py.File(file_path, 'r') as f:
        # 显示文件属性
        print("\n=== 文件属性 ===")
        for key, value in f.attrs.items():
            print(f"{key}: {value}")
            
        # 获取所有记录
        records = list(f.keys())
        total_records = len(records)
        print(f"\n总记录数: {total_records}")
        
        # 显示前5条记录的详细信息
        print("\n=== 前5条记录示例 ===")
        for i, record_name in enumerate(records[:5]):
            group = f[record_name]
            print(f"\n记录 {i+1}: {record_name}")
            print("属性:")
            for key, value in group.attrs.items():
                print(f"  {key}: {value}")
            
            # 显示ECG数据的基本统计信息
            ecg_data = group['ecg_data'][:]
            print("ECG数据统计:")
            print(f"  长度: {len(ecg_data)} 点")
            print(f"  最小值: {ecg_data.min():.4f}")
            print(f"  最大值: {ecg_data.max():.4f}")
            print(f"  均值: {ecg_data.mean():.4f}")
            print(f"  标准差: {ecg_data.std():.4f}")
            
            # 绘制第一条记录的波形图
            if i == 0:
                plt.figure(figsize=(15, 4))
                plt.plot(ecg_data[:1000])  # 只显示前1000个点
                plt.title(f"ECG波形示例 (记录: {record_name})")
                plt.xlabel("采样点")
                plt.ylabel("幅值")
                plt.grid(True)
                plt.savefig('D:/Project/data_check/ecg_data/waveform_example.png')
                plt.close()

if __name__ == "__main__":
    file_path = "D:/Project/data_check/ecg_data/ecg_lead_i_batch_1_20250113_183659.hdf5"
    check_hdf5_file(file_path) 