from apps.models.analysis_models import CADCardiomyopathyEntity


def process(waveform_info):
    """
    心肌病冠心病诊断
    :param waveform_info: 波形信息
    :return:
    """
    cad_cardiomyopathy = CADCardiomyopathyEntity()
    cad_cardiomyopathy.ISC = 0
    cad_cardiomyopathy.LVH = 0
    cad_cardiomyopathy.RVH = 0
    cad_cardiomyopathy.LAH = 0
    cad_cardiomyopathy.RAH = 0
    cad_cardiomyopathy.MI = 0

    return cad_cardiomyopathy
