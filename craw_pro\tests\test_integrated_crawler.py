#!/usr/bin/env python3
"""
羽毛球装备爬虫集成测试脚本
测试单个装备页面的完整数据提取功能
"""

import json
from browser_crawler import BrowserBadmintonCrawler

def test_single_equipment():
    """测试单个装备的数据提取"""
    print("🏸 测试集成入手价功能的爬虫")
    print("=" * 60)
    
    # 创建爬虫实例（显示浏览器便于调试）
    crawler = BrowserBadmintonCrawler(headless=False)
    
    # 初始化浏览器
    if not crawler.setup_driver():
        print("❌ 浏览器启动失败")
        return
    
    try:
        # 测试装备URL
        test_url = "https://www.badmintoncn.com/cbo_eq/view.php?eid=22974"
        
        print(f"🔍 开始提取装备详情: {test_url}")
        
        # 提取装备信息
        equipment_data = crawler.crawl_equipment_details(test_url)
        
        if equipment_data:
            print("\n✅ 装备详情提取成功:")
            print(f"  名称: {equipment_data.get('name', 'N/A')}")
            print(f"  品牌: {equipment_data.get('brand', 'N/A')}")
            print(f"  系列: {equipment_data.get('series', 'N/A')}")
            print(f"  价格区间: {equipment_data.get('price_range', 'N/A')}")
            print(f"  标签价: {equipment_data.get('msrp_price', 'N/A')}")
            
            # 显示新添加的字段
            if 'rating' in equipment_data:
                print(f"  中羽评分: {equipment_data['rating']}")
            if 'equipment_intro' in equipment_data:
                intro = equipment_data['equipment_intro'][:100] + "..." if len(equipment_data['equipment_intro']) > 100 else equipment_data['equipment_intro']
                print(f"  装备简介: {intro}")
            
            # 显示入手价信息
            if any(key in equipment_data for key in ['new_avg_price', 'used_avg_price', 'total_users']):
                print("\n💰 入手价信息:")
                print(f"  全新均价: {equipment_data.get('new_avg_price', 'N/A')}")
                print(f"  二手均价: {equipment_data.get('used_avg_price', 'N/A')}")
                print(f"  登记人数: {equipment_data.get('total_users', 'N/A')}")
            
            # 保存完整数据到JSON文件
            output_file = "integrated_test_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(equipment_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n📁 完整数据已保存到: {output_file}")
            
        else:
            print("❌ 装备详情提取失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        
    finally:
        # 清理资源
        if crawler.driver:
            crawler.driver.quit()
    
    print("\n🔄 测试完成")

if __name__ == "__main__":
    test_single_equipment() 