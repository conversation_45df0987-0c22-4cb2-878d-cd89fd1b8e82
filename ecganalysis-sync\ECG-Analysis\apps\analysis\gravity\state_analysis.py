# 阈值设置
import math
import traceback

import numpy as np
import pandas as pd

from apps.utils.logger_helper import Logger

# 阈值设置
# 静止和运动的阈值
x_std_sport_static = 3
y_std_sport_static = 3
z_std_sport_static = 3

# 上下楼的阈值
x_std_stair = 6.5
x_mean_stair = 5.5

# 慢跑的阈值
x_std_jogging = 8
x_mean_jogging = 7.5

# 走路的阈值
x_std_walk = 5.5
x_mean_walk = 3.5

# 坐着的阈值
z_mean_sit = 10
z_std_sit = 0.5

# 平躺的阈值
x_mean_lie_flat = 1
y_mean_lie_flat = 1
z_mean_lie_flat = 1.3

# 侧躺的阈值
x_mean_lie_on = 11
y_mean_lie_on = 2
z_mean_lie_on = 5

# 定义重力加速度的阈值和圆周率
g = 1024
PI = 3.14


def tumble_handle(df_data):
    tumble_history = []

    tumble_count = 0  # 使用全局变量来记录连续跌倒计数

    for index, row in df_data.iterrows():

        x_acc = row['x']
        y_acc = row['y']
        z_acc = row['z']

        # 计算角度
        try:
            angle_x = (math.atan(x_acc / math.sqrt(y_acc ** 2 + z_acc ** 2))) * 180 / PI
            angle_y = (math.atan(y_acc / math.sqrt(x_acc ** 2 + z_acc ** 2))) * 180 / PI
            angle_z = (math.atan(z_acc / math.sqrt(x_acc ** 2 + y_acc ** 2))) * 180 / PI

            # 判断跌倒
            if (abs(angle_x) > 60) or (abs(angle_y) > 60) or (abs(angle_z) > 60):
                tumble_count += 1
                if tumble_count >= 15:  # 连续15次超过阈值则认为摔倒
                    tumble_count = 0  # 重置计数器
                    tumble_history.append(True)
                else:
                    tumble_history.append(False)
            else:
                tumble_history.append(False)
        except ZeroDivisionError:
            Logger().error(f'Division by zero occurred. Check sensor data.{traceback.format_exc()}')
            tumble_history.append(False)

    return tumble_history


def process(info_data):
    try:
        x = [float(item['x']) for item in info_data]
        y = [float(item['y']) for item in info_data]
        z = [float(item['z']) for item in info_data]

        df_data = pd.DataFrame({
            'x': x,
            'y': y,
            'z': z
        })

        # 提取特征
        features = extract_features(df_data)

        # 状态判定
        status = motion_detection(features)

        tumble_history = tumble_handle(df_data)

        return status, features, tumble_history
    except Exception as e:
        Logger().error(f'加速度处理异常：{traceback.format_exc()}\n{info_data}')
        return None


def extract_features(data):
    """
    提取运动状态特征（均值和标准差）
    :param data:
    :return:
    """

    x_values = data['x']
    y_values = data['y']
    z_values = data['z']

    features = {
        'x_mean': np.mean(x_values),
        'x_std': np.std(x_values),
        'y_mean': np.mean(y_values),
        'y_std': np.std(y_values),
        'z_mean': np.mean(z_values),
        'z_std': np.std(z_values),
    }
    return features


def motion_detection(features):
    """
    根据标准差和均值判断运动状态
    :param features:
    :return:
        1-1 静止状态 - 坐着，1-2 静止状态 - 平躺，1-3 静止状态 - 侧躺，1-4 静止状态 - 未知静态活动
        2-1 运动状态 - 慢跑，2-2 运动状态 - 上下楼，,2-3 运动状态 - 走路，2-4 运动状态 - 未知动态活动
    """
    # 首先判断是否为静止状态
    if features['x_std'] < x_std_sport_static and features['y_std'] < y_std_sport_static and features[
        'z_std'] < z_std_sport_static:
        # 判断静态活动类型
        if features['z_mean'] > z_mean_sit:
            return "1-1"
        elif features['x_mean'] < x_mean_lie_flat and features['y_mean'] < y_mean_lie_flat and features[
            'z_mean'] < z_mean_lie_flat:
            return "1-2"
        elif features['x_mean'] > x_mean_lie_on and features['y_mean'] > y_mean_lie_on and features[
            'z_mean'] > z_mean_lie_on:
            return "1-3"
        else:
            return "1-4"
    else:
        # 判断动态活动类型
        if features['x_mean'] > x_mean_jogging and features['x_std'] > x_std_jogging:
            return "2-1"
        elif features['x_mean'] > x_mean_stair and features['x_std'] > x_std_stair:
            return "2-2"
        elif features['x_mean'] > x_mean_walk and features['x_std'] > x_std_walk:
            return "2-3"
        else:
            return "2-4"
