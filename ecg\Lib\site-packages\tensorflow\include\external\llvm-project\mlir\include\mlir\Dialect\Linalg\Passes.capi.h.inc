
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterLinalgPasses();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgConvertElementwiseToLinalg();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgConvertElementwiseToLinalg();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgBufferize();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgBufferize();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgComprehensiveFuncBufferize();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgComprehensiveFunc<PERSON><PERSON>erize();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgDetensorize();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgDetensorize();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgFoldReshapeOpsByLinearization();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgFoldReshapeOpsByLinearization();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgFoldUnitExtentDims();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgFoldUnitExtentDims();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgFusionOfTensorOps();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgFusionOfTensorOps();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgGeneralization();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgGeneralization();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgInlineScalarOperands();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgInlineScalarOperands();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgLowerTiledLoopsToSCF();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgLowerTiledLoopsToSCF();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgLowerToAffineLoops();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgLowerToAffineLoops();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgLowerToLoops();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgLowerToLoops();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgLowerToParallelLoops();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgLowerToParallelLoops();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgPromotion();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgPromotion();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgTiling();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgTiling();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgTilingToParallelLoops();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgTilingToParallelLoops();


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgTilingToTiledLoops();
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgTilingToTiledLoops();



#ifdef __cplusplus
}
#endif
