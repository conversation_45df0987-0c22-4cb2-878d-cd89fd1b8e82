
# This file was generated by 'versioneer.py' (0.19) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2022-08-31T10:07:09+0100",
 "dirty": false,
 "error": null,
 "full-revisionid": "ca60aab7340d9989d9428e11a51467658190bb6b",
 "version": "1.4.4"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
