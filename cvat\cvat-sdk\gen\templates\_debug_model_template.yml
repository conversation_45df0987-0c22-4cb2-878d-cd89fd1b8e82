        openApiType={{{openApiType}}}
        baseName={{{baseName}}}
        complexType={{{complexType}}}
        getter={{{getter}}}
        setter={{{setter}}}
        description={{{description}}}
        dataType={{{dataType}}}
        datatypeWithEnum={{{datatypeWithEnum}}}
        dataFormat={{{dataFormat}}}
        name={{{name}}}
        min={{{min}}}
        max={{{max}}}
        defaultValue={{{defaultValue}}}
        defaultValueWithParam={{{defaultValueWithParam}}}
        baseType={{{baseType}}}
        containerType={{{containerType}}}
        title={{{title}}}
        unescapedDescription={{{unescapedDescription}}}
        maxLength={{{maxLength}}}
        minLength={{{minLength}}}
        pattern={{{pattern}}}
        minimum={{{minimum}}}
        maximum={{{maximum}}}
        exclusiveMinimum={{{exclusiveMinimum}}}
        exclusiveMaximum={{{exclusiveMaximum}}}
        required={{{required}}}
        deprecated={{{deprecated}}}
        hasMoreNonReadOnly={{{hasMoreNonReadOnly}}}
        isPrimitiveType={{{isPrimitiveType}}}
        isModel={{{isModel}}}
        isContainer={{{isContainer}}}
        isString={{{isString}}}
        isNumeric={{{isNumeric}}}
        isInteger={{{isInteger}}}
        isShort={{{isShort}}}
        isLong={{{isLong}}}
        isUnboundedInteger={{{isUnboundedInteger}}}
        isNumber={{{isNumber}}}
        isFloat={{{isFloat}}}
        isDouble={{{isDouble}}}
        isDecimal={{{isDecimal}}}
        isByteArray={{{isByteArray}}}
        isBinary={{{isBinary}}}
        isFile={{{isFile}}}
        isBoolean={{{isBoolean}}}
        isDate={{{isDate}}}
        isDateTime={{{isDateTime}}}
        isUuid={{{isUuid}}}
        isUri={{{isUri}}}
        isEmail={{{isEmail}}}
        isFreeFormObject={{{isFreeFormObject}}}
        isArray={{{isArray}}}
        isMap={{{isMap}}}
        isEnum={{{isEnum}}}
        isAnyType={{{isAnyType}}}
        isReadOnly={{{isReadOnly}}}
        isWriteOnly={{{isWriteOnly}}}
        isNullable={{{isNullable}}}
        isSelfReference={{{isSelfReference}}}
        isCircularReference={{{isCircularReference}}}
        isDiscriminator={{{isDiscriminator}}}
        _enum={{{_enum}}}
        allowableValues={{{allowableValues}}}
        items={{{items}}}
        additionalProperties={{{additionalProperties}}}
        vars={{{vars}}}
        requiredVars={{{requiredVars}}}
        mostInnerItems={{{mostInnerItems}}}
        vendorExtensions={{{vendorExtensions}}}
        hasValidation={{{hasValidation}}}
        isInherited={{{isInherited}}}
        discriminatorValue={{{discriminatorValue}}}
        nameInCamelCase={{{nameInCamelCase}}}
        nameInSnakeCase={{{nameInSnakeCase}}}
        enumName={{{enumName}}}
        maxItems={{{maxItems}}}
        minItems={{{minItems}}}
        maxProperties={{{maxProperties}}}
        minProperties={{{minProperties}}}
        uniqueItems={{{uniqueItems}}}
        multipleOf={{{multipleOf}}}
        isXmlAttribute={{{isXmlAttribute}}}
        xmlPrefix={{{xmlPrefix}}}
        xmlName={{{xmlName}}}
        xmlNamespace={{{xmlNamespace}}}
        isXmlWrapped={{{isXmlWrapped}}}
        isNull={{{isNull}}}
        getAdditionalPropertiesIsAnyType={{{getAdditionalPropertiesIsAnyType}}}))
        getHasVars={{{getHasVars}}}))
        getHasRequired={{{getHasRequired}}}))
        getHasDiscriminatorWithNonEmptyMapping={{{hasDiscriminatorWithNonEmptyMapping}}}
        hasMultipleTypes={{{hasMultipleTypes}}}