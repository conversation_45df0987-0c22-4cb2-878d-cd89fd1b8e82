// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import './base';

hr {
    border: none;
    border-top: 1px solid $border-color-1;
    height: 1px;
}

html, body {
    width: 100%;
    height: 100%;
    margin: 0;
}

.cvat-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.cvat-disconnected {
    font-size: 36px;
}

.cvat-spinner-container {
    position: absolute;
    background: $background-color-1;
    opacity: 0.5;
    width: 100%;
    height: 100%;
    z-index: 2;
    top: 0;
    left: 0;
}

.cvat-notification-link {
    padding: 0;
    display: inline;
    height: auto;

    span {
        display: inline;
    }
}

.cvat-not-found {
    margin: 10% 25%;
}

.cvat-text-color {
    color: $text-color;
}

.cvat-title {
    font-weight: 400;
    font-size: 21px;
    color: $text-color;
    padding-top: 5px;
}

.cvat-menu-icon {
    font-size: 16px;
    margin-left: $grid-unit-size;
    align-self: center;
}

.ant-slider {
    > .ant-slider-track {
        background-color: $slider-color;
    }

    > .ant-slider-handle {
        border-color: $slider-color;
    }
}

.ant-notification {
    top: $header-height !important;
    right: 0 !important;
    margin-right: 0;
}

.cvat-info-circle-icon {
    color: $info-icon-color;
}

.cvat-danger-circle-icon {
    color: $danger-icon-color;
}

#root {
    width: 100%;
    height: 100%;
    display: grid;
    min-width: 1024px;
}

#layout-grid {
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    pointer-events: none;
}

.cvat-cloud-storage-select-provider {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    > svg {
        margin-right: $grid-unit-size;
    }
}

.cvat-divider {
    margin: $grid-unit-size * 0.5 0;
}

.cvat-advanced-configuration-wrapper {
    .ant-collapse-item > .ant-collapse-header {
        align-items: center;
    }

    .cvat-title {
        padding-top: 0;
    }
}

.ant-notification-notice {
    p {
        margin-bottom: 0;
    }
}

.query-builder-container {
    .query-builder {
        margin: $grid-unit-size;

        .group {
            background: none !important;
            border: none !important;
        }

        /* stylelint-disable selector-class-pattern */
        .group--header {
            display: flex;
            justify-content: space-between;

            &::before {
                left: 10px !important;
            }
        }

        .rule.group-or-rule {
            display: flex;
            background: none !important;
            border: none !important;

            .rule--body {
                display: flex;
                row-gap: $grid-unit-size;

                .rule--field {
                    > .ant-select {
                        min-width: $grid-unit-size * 16 !important;
                    }
                }

                .rule--operator {
                    margin-right: $grid-unit-size;

                    > .ant-select {
                        width: $grid-unit-size * 7 !important;
                    }
                }

                .rule--value {
                    .ant-select {
                        min-width: $grid-unit-size * 15 !important;
                    }

                    .ant-picker {
                        width: $grid-unit-size * 15 !important;
                    }
                }
            }
            /* stylelint-enable selector-class-pattern */
        }
    }
}

.cvat-settings-tooltip {
    margin-left: $grid-unit-size;
}

.cvat-settings-tooltip-inner {
    span {
        display: block;
        color: white;
    }

    div:not(:last-child) {
        margin-bottom: $grid-unit-size * 2;
    }
}