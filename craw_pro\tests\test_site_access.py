#!/usr/bin/env python3
"""
网站访问测试脚本 - 用于分析badmintoncn.com的访问规则
"""

import requests
from bs4 import BeautifulSoup
import time
import warnings

# 忽略SSL警告
warnings.filterwarnings('ignore')

def create_session():
    """创建一个配置好的session"""
    session = requests.Session()
    
    # 基本headers，不包含Accept-Encoding以避免压缩
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0'
    })
    
    return session

def test_page_access(session, url, referer=None):
    """测试单个页面访问"""
    try:
        headers = {}
        if referer:
            headers['Referer'] = referer
            
        response = session.get(url, headers=headers, timeout=10, verify=False)
        return response.status_code, len(response.text)
    except Exception as e:
        return f"Error: {e}", 0

def analyze_homepage_content(session, url):
    """分析主页内容"""
    try:
        response = session.get(url, timeout=10, verify=False)
        if response.status_code != 200:
            return f"无法访问主页，状态码: {response.status_code}"
        
        # 确保内容是正确解码的
        response.encoding = response.apparent_encoding or 'utf-8'
        content = response.text
        
        # 使用BeautifulSoup解析
        soup = BeautifulSoup(content, 'html.parser')
        
        # 保存原始内容用于调试
        with open('debug_homepage.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 分析内容
        title = soup.title.string if soup.title else "None"
        
        # 查找所有链接
        links = soup.find_all('a', href=True)
        total_links = len(links)
        
        # 查找包含'eq'的链接
        eq_links = [link for link in links if 'eq' in link.get('href', '').lower()]
        
        # 查找装备相关的链接
        equipment_links = []
        for link in links:
            href = link.get('href', '')
            text = link.get_text().strip()
            if any(keyword in href.lower() for keyword in ['eq', 'equipment', '装备']) or \
               any(keyword in text for keyword in ['装备', '器材', 'equipment']):
                equipment_links.append((href, text))
        
        # 查找JavaScript脚本
        scripts = soup.find_all('script')
        
        # 检查关键词
        content_lower = content.lower()
        has_equipment = '装备' in content
        has_cbo_eq = 'cbo_eq' in content_lower
        has_view_php = 'view.php' in content_lower
        
        analysis = f"""
   主页内容分析:
   - 页面标题: {title}
   - 页面包含'装备'文字: {has_equipment}
   - 页面包含'cbo_eq'文字: {has_cbo_eq}
   - 页面包含'view.php'文字: {has_view_php}
   - 总链接数: {total_links}
   - 包含'eq'的链接: {len(eq_links)}
   - 装备相关链接: {len(equipment_links)}
   - JavaScript脚本数: {len(scripts)}
   - 已保存主页内容到 debug_homepage.html"""
        
        if equipment_links:
            analysis += "\n   - 找到的装备链接:"
            for href, text in equipment_links[:5]:  # 只显示前5个
                analysis += f"\n     * {href} - {text}"
                
        return len(equipment_links), analysis
        
    except Exception as e:
        return 0, f"分析主页时出错: {e}"

def main():
    print("🔍 开始测试网站访问规则...")
    
    session = create_session()
    base_url = "https://www.badmintoncn.com"
    
    # 1. 测试主页访问
    print("\n1. 测试主页访问:")
    status, length = test_page_access(session, base_url)
    print(f"   主页状态码: {status}")
    print(f"   响应长度: {length}")
    
    # 分析主页内容
    eq_count, analysis = analyze_homepage_content(session, base_url)
    print(f"   找到装备相关链接: {eq_count}")
    print(analysis)
    
    # 2. 测试装备目录页面
    print("\n2. 测试装备目录页面:")
    eq_urls = [
        "https://www.badmintoncn.com/cbo_eq/",
        "https://www.badmintoncn.com/cbo_eq/list.php",
        "https://www.badmintoncn.com/cbo_eq/index.php"
    ]
    
    for url in eq_urls:
        status, length = test_page_access(session, url)
        print(f"   {url}: {status}")
    
    # 3. 测试装备详情页面（带不同referer）
    print("\n3. 测试装备详情页面 (带referer):")
    test_urls = [
        "https://www.badmintoncn.com/cbo_eq/view.php?eid=23120",
        "https://www.badmintoncn.com/cbo_eq/view.php?eid=19393"
    ]
    
    referers = [
        (None, "无referer"),
        (base_url, "主页referer"),
        ("https://www.badmintoncn.com/cbo_eq/", "装备页referer")
    ]
    
    for url in test_urls:
        for referer, desc in referers:
            status, length = test_page_access(session, url, referer)
            print(f"   {url} ({desc}): {status}")
    
    # 4. 测试cookies要求
    print("\n4. 测试cookies要求:")
    
    # 获取cookies
    session.get(base_url, verify=False)
    cookies_count = len(session.cookies)
    print(f"   获取到cookies: {cookies_count} 个")
    
    # 带cookies测试详情页
    status, length = test_page_access(session, test_urls[0])
    print(f"   带cookies访问详情页: {status}")

if __name__ == "__main__":
    main() 