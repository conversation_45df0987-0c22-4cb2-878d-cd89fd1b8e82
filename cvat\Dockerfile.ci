FROM cvat/server:local

ENV DJANGO_SETTINGS_MODULE=cvat.settings.testing
USER root

RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get --no-install-recommends install -yq \
        build-essential \
        python3-dev \
        && \
    rm -rf /var/lib/apt/lists/*;

COPY cvat/requirements/ /tmp/cvat/requirements/
COPY utils/dataset_manifest/requirements.txt /tmp/utils/dataset_manifest/requirements.txt

RUN python3 -m ensurepip
RUN DATUMARO_HEADLESS=1 python3 -m pip install --no-cache-dir -r /tmp/cvat/requirements/testing.txt

COPY .coveragerc .

ENTRYPOINT []
