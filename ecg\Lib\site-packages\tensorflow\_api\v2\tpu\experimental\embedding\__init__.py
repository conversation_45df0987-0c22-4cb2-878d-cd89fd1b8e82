# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.tpu.experimental.embedding namespace.
"""

from __future__ import print_function as _print_function

import sys as _sys

from tensorflow.python.tpu.tpu_embedding_v2 import TPUEmbedding
from tensorflow.python.tpu.tpu_embedding_v2 import cpu_embedding_lookup as serving_embedding_lookup
from tensorflow.python.tpu.tpu_embedding_v2_utils import Adagrad
from tensorflow.python.tpu.tpu_embedding_v2_utils import Adam
from tensorflow.python.tpu.tpu_embedding_v2_utils import FTRL
from tensorflow.python.tpu.tpu_embedding_v2_utils import FeatureConfig
from tensorflow.python.tpu.tpu_embedding_v2_utils import SGD
from tensorflow.python.tpu.tpu_embedding_v2_utils import TableConfig

del _print_function
