from sql_helper import <PERSON>s<PERSON>l<PERSON>el<PERSON>

def check_join_condition():
    db = MysqlHelper("default")
    try:
        conn = db.get_con()
        cursor = conn.cursor()
        
        # 检查t_patient_ecg_i表的study_uid
        print("\n=== t_patient_ecg_i表的study_uid示例 ===")
        cursor.execute("SELECT id, study_uid FROM t_patient_ecg_i LIMIT 5")
        results = cursor.fetchall()
        print("ECG表:")
        for row in results:
            print(f"id: {row[0]}, study_uid: {row[1]}")
            
        # 检查t_patient表的patient_id
        print("\n=== t_patient表的patient_id示例 ===")
        cursor.execute("SELECT id, patient_id FROM t_patient LIMIT 5")
        results = cursor.fetchall()
        print("患者表:")
        for row in results:
            print(f"id: {row[0]}, patient_id: {row[1]}")
            
        # 检查是否有匹配的记录
        print("\n=== 检查匹配情况 ===")
        sql = """
        SELECT COUNT(*) as total,
               COUNT(p.id) as matched
        FROM t_patient_ecg_i e
        LEFT JOIN t_patient p ON e.study_uid = p.patient_id
        """
        cursor.execute(sql)
        result = cursor.fetchone()
        print(f"总记录数: {result[0]}")
        print(f"成功匹配数: {result[1]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"查询出错：{str(e)}")

if __name__ == "__main__":
    check_join_condition() 