// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/saved_tensor_slice.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/tensor_slice.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/framework/versions.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto;
namespace tensorflow {
class SavedSlice;
class SavedSliceDefaultTypeInternal;
extern SavedSliceDefaultTypeInternal _SavedSlice_default_instance_;
class SavedSliceMeta;
class SavedSliceMetaDefaultTypeInternal;
extern SavedSliceMetaDefaultTypeInternal _SavedSliceMeta_default_instance_;
class SavedTensorSliceMeta;
class SavedTensorSliceMetaDefaultTypeInternal;
extern SavedTensorSliceMetaDefaultTypeInternal _SavedTensorSliceMeta_default_instance_;
class SavedTensorSlices;
class SavedTensorSlicesDefaultTypeInternal;
extern SavedTensorSlicesDefaultTypeInternal _SavedTensorSlices_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::SavedSlice* Arena::CreateMaybeMessage<::tensorflow::SavedSlice>(Arena*);
template<> ::tensorflow::SavedSliceMeta* Arena::CreateMaybeMessage<::tensorflow::SavedSliceMeta>(Arena*);
template<> ::tensorflow::SavedTensorSliceMeta* Arena::CreateMaybeMessage<::tensorflow::SavedTensorSliceMeta>(Arena*);
template<> ::tensorflow::SavedTensorSlices* Arena::CreateMaybeMessage<::tensorflow::SavedTensorSlices>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class SavedSliceMeta :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedSliceMeta) */ {
 public:
  SavedSliceMeta();
  virtual ~SavedSliceMeta();

  SavedSliceMeta(const SavedSliceMeta& from);
  SavedSliceMeta(SavedSliceMeta&& from) noexcept
    : SavedSliceMeta() {
    *this = ::std::move(from);
  }

  inline SavedSliceMeta& operator=(const SavedSliceMeta& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedSliceMeta& operator=(SavedSliceMeta&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedSliceMeta& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedSliceMeta* internal_default_instance() {
    return reinterpret_cast<const SavedSliceMeta*>(
               &_SavedSliceMeta_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SavedSliceMeta& a, SavedSliceMeta& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedSliceMeta* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedSliceMeta* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedSliceMeta* New() const final {
    return CreateMaybeMessage<SavedSliceMeta>(nullptr);
  }

  SavedSliceMeta* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedSliceMeta>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedSliceMeta& from);
  void MergeFrom(const SavedSliceMeta& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedSliceMeta* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedSliceMeta";
  }
  protected:
  explicit SavedSliceMeta(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSliceFieldNumber = 4,
    kNameFieldNumber = 1,
    kShapeFieldNumber = 2,
    kTypeFieldNumber = 3,
  };
  // repeated .tensorflow.TensorSliceProto slice = 4;
  int slice_size() const;
  void clear_slice();
  ::tensorflow::TensorSliceProto* mutable_slice(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto >*
      mutable_slice();
  const ::tensorflow::TensorSliceProto& slice(int index) const;
  ::tensorflow::TensorSliceProto* add_slice();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto >&
      slice() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 3;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedSliceMeta)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto > slice_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::TensorShapeProto* shape_;
  int type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto;
};
// -------------------------------------------------------------------

class SavedTensorSliceMeta :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedTensorSliceMeta) */ {
 public:
  SavedTensorSliceMeta();
  virtual ~SavedTensorSliceMeta();

  SavedTensorSliceMeta(const SavedTensorSliceMeta& from);
  SavedTensorSliceMeta(SavedTensorSliceMeta&& from) noexcept
    : SavedTensorSliceMeta() {
    *this = ::std::move(from);
  }

  inline SavedTensorSliceMeta& operator=(const SavedTensorSliceMeta& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedTensorSliceMeta& operator=(SavedTensorSliceMeta&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedTensorSliceMeta& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedTensorSliceMeta* internal_default_instance() {
    return reinterpret_cast<const SavedTensorSliceMeta*>(
               &_SavedTensorSliceMeta_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SavedTensorSliceMeta& a, SavedTensorSliceMeta& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedTensorSliceMeta* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedTensorSliceMeta* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedTensorSliceMeta* New() const final {
    return CreateMaybeMessage<SavedTensorSliceMeta>(nullptr);
  }

  SavedTensorSliceMeta* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedTensorSliceMeta>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedTensorSliceMeta& from);
  void MergeFrom(const SavedTensorSliceMeta& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedTensorSliceMeta* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedTensorSliceMeta";
  }
  protected:
  explicit SavedTensorSliceMeta(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorFieldNumber = 1,
    kVersionsFieldNumber = 2,
  };
  // repeated .tensorflow.SavedSliceMeta tensor = 1;
  int tensor_size() const;
  void clear_tensor();
  ::tensorflow::SavedSliceMeta* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedSliceMeta >*
      mutable_tensor();
  const ::tensorflow::SavedSliceMeta& tensor(int index) const;
  ::tensorflow::SavedSliceMeta* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedSliceMeta >&
      tensor() const;

  // .tensorflow.VersionDef versions = 2;
  bool has_versions() const;
  void clear_versions();
  const ::tensorflow::VersionDef& versions() const;
  ::tensorflow::VersionDef* release_versions();
  ::tensorflow::VersionDef* mutable_versions();
  void set_allocated_versions(::tensorflow::VersionDef* versions);
  void unsafe_arena_set_allocated_versions(
      ::tensorflow::VersionDef* versions);
  ::tensorflow::VersionDef* unsafe_arena_release_versions();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedTensorSliceMeta)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedSliceMeta > tensor_;
  ::tensorflow::VersionDef* versions_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto;
};
// -------------------------------------------------------------------

class SavedSlice :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedSlice) */ {
 public:
  SavedSlice();
  virtual ~SavedSlice();

  SavedSlice(const SavedSlice& from);
  SavedSlice(SavedSlice&& from) noexcept
    : SavedSlice() {
    *this = ::std::move(from);
  }

  inline SavedSlice& operator=(const SavedSlice& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedSlice& operator=(SavedSlice&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedSlice& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedSlice* internal_default_instance() {
    return reinterpret_cast<const SavedSlice*>(
               &_SavedSlice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SavedSlice& a, SavedSlice& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedSlice* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedSlice* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedSlice* New() const final {
    return CreateMaybeMessage<SavedSlice>(nullptr);
  }

  SavedSlice* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedSlice>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedSlice& from);
  void MergeFrom(const SavedSlice& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedSlice* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedSlice";
  }
  protected:
  explicit SavedSlice(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kSliceFieldNumber = 2,
    kDataFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .tensorflow.TensorSliceProto slice = 2;
  bool has_slice() const;
  void clear_slice();
  const ::tensorflow::TensorSliceProto& slice() const;
  ::tensorflow::TensorSliceProto* release_slice();
  ::tensorflow::TensorSliceProto* mutable_slice();
  void set_allocated_slice(::tensorflow::TensorSliceProto* slice);
  void unsafe_arena_set_allocated_slice(
      ::tensorflow::TensorSliceProto* slice);
  ::tensorflow::TensorSliceProto* unsafe_arena_release_slice();

  // .tensorflow.TensorProto data = 3;
  bool has_data() const;
  void clear_data();
  const ::tensorflow::TensorProto& data() const;
  ::tensorflow::TensorProto* release_data();
  ::tensorflow::TensorProto* mutable_data();
  void set_allocated_data(::tensorflow::TensorProto* data);
  void unsafe_arena_set_allocated_data(
      ::tensorflow::TensorProto* data);
  ::tensorflow::TensorProto* unsafe_arena_release_data();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedSlice)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::TensorSliceProto* slice_;
  ::tensorflow::TensorProto* data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto;
};
// -------------------------------------------------------------------

class SavedTensorSlices :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedTensorSlices) */ {
 public:
  SavedTensorSlices();
  virtual ~SavedTensorSlices();

  SavedTensorSlices(const SavedTensorSlices& from);
  SavedTensorSlices(SavedTensorSlices&& from) noexcept
    : SavedTensorSlices() {
    *this = ::std::move(from);
  }

  inline SavedTensorSlices& operator=(const SavedTensorSlices& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedTensorSlices& operator=(SavedTensorSlices&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedTensorSlices& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedTensorSlices* internal_default_instance() {
    return reinterpret_cast<const SavedTensorSlices*>(
               &_SavedTensorSlices_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SavedTensorSlices& a, SavedTensorSlices& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedTensorSlices* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedTensorSlices* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedTensorSlices* New() const final {
    return CreateMaybeMessage<SavedTensorSlices>(nullptr);
  }

  SavedTensorSlices* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedTensorSlices>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedTensorSlices& from);
  void MergeFrom(const SavedTensorSlices& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedTensorSlices* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedTensorSlices";
  }
  protected:
  explicit SavedTensorSlices(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMetaFieldNumber = 1,
    kDataFieldNumber = 2,
  };
  // .tensorflow.SavedTensorSliceMeta meta = 1;
  bool has_meta() const;
  void clear_meta();
  const ::tensorflow::SavedTensorSliceMeta& meta() const;
  ::tensorflow::SavedTensorSliceMeta* release_meta();
  ::tensorflow::SavedTensorSliceMeta* mutable_meta();
  void set_allocated_meta(::tensorflow::SavedTensorSliceMeta* meta);
  void unsafe_arena_set_allocated_meta(
      ::tensorflow::SavedTensorSliceMeta* meta);
  ::tensorflow::SavedTensorSliceMeta* unsafe_arena_release_meta();

  // .tensorflow.SavedSlice data = 2;
  bool has_data() const;
  void clear_data();
  const ::tensorflow::SavedSlice& data() const;
  ::tensorflow::SavedSlice* release_data();
  ::tensorflow::SavedSlice* mutable_data();
  void set_allocated_data(::tensorflow::SavedSlice* data);
  void unsafe_arena_set_allocated_data(
      ::tensorflow::SavedSlice* data);
  ::tensorflow::SavedSlice* unsafe_arena_release_data();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedTensorSlices)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::SavedTensorSliceMeta* meta_;
  ::tensorflow::SavedSlice* data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SavedSliceMeta

// string name = 1;
inline void SavedSliceMeta::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SavedSliceMeta::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.name)
  return name_.Get();
}
inline void SavedSliceMeta::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedSliceMeta.name)
}
inline void SavedSliceMeta::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedSliceMeta.name)
}
inline void SavedSliceMeta::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedSliceMeta.name)
}
inline void SavedSliceMeta::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedSliceMeta.name)
}
inline std::string* SavedSliceMeta::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSliceMeta.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SavedSliceMeta::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSliceMeta.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedSliceMeta::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSliceMeta.name)
}
inline std::string* SavedSliceMeta::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedSliceMeta.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedSliceMeta::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedSliceMeta.name)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool SavedSliceMeta::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& SavedSliceMeta::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* SavedSliceMeta::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSliceMeta.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedSliceMeta::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedSliceMeta.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedSliceMeta::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSliceMeta.shape)
  return shape_;
}
inline void SavedSliceMeta::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSliceMeta.shape)
}

// .tensorflow.DataType type = 3;
inline void SavedSliceMeta::clear_type() {
  type_ = 0;
}
inline ::tensorflow::DataType SavedSliceMeta::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.type)
  return static_cast< ::tensorflow::DataType >(type_);
}
inline void SavedSliceMeta::set_type(::tensorflow::DataType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedSliceMeta.type)
}

// repeated .tensorflow.TensorSliceProto slice = 4;
inline int SavedSliceMeta::slice_size() const {
  return slice_.size();
}
inline ::tensorflow::TensorSliceProto* SavedSliceMeta::mutable_slice(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSliceMeta.slice)
  return slice_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto >*
SavedSliceMeta::mutable_slice() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedSliceMeta.slice)
  return &slice_;
}
inline const ::tensorflow::TensorSliceProto& SavedSliceMeta::slice(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.slice)
  return slice_.Get(index);
}
inline ::tensorflow::TensorSliceProto* SavedSliceMeta::add_slice() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedSliceMeta.slice)
  return slice_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto >&
SavedSliceMeta::slice() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedSliceMeta.slice)
  return slice_;
}

// -------------------------------------------------------------------

// SavedTensorSliceMeta

// repeated .tensorflow.SavedSliceMeta tensor = 1;
inline int SavedTensorSliceMeta::tensor_size() const {
  return tensor_.size();
}
inline void SavedTensorSliceMeta::clear_tensor() {
  tensor_.Clear();
}
inline ::tensorflow::SavedSliceMeta* SavedTensorSliceMeta::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSliceMeta.tensor)
  return tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedSliceMeta >*
SavedTensorSliceMeta::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedTensorSliceMeta.tensor)
  return &tensor_;
}
inline const ::tensorflow::SavedSliceMeta& SavedTensorSliceMeta::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSliceMeta.tensor)
  return tensor_.Get(index);
}
inline ::tensorflow::SavedSliceMeta* SavedTensorSliceMeta::add_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedTensorSliceMeta.tensor)
  return tensor_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedSliceMeta >&
SavedTensorSliceMeta::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedTensorSliceMeta.tensor)
  return tensor_;
}

// .tensorflow.VersionDef versions = 2;
inline bool SavedTensorSliceMeta::has_versions() const {
  return this != internal_default_instance() && versions_ != nullptr;
}
inline const ::tensorflow::VersionDef& SavedTensorSliceMeta::versions() const {
  const ::tensorflow::VersionDef* p = versions_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSliceMeta.versions)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::VersionDef*>(
      &::tensorflow::_VersionDef_default_instance_);
}
inline ::tensorflow::VersionDef* SavedTensorSliceMeta::release_versions() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedTensorSliceMeta.versions)
  
  ::tensorflow::VersionDef* temp = versions_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  versions_ = nullptr;
  return temp;
}
inline ::tensorflow::VersionDef* SavedTensorSliceMeta::unsafe_arena_release_versions() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedTensorSliceMeta.versions)
  
  ::tensorflow::VersionDef* temp = versions_;
  versions_ = nullptr;
  return temp;
}
inline ::tensorflow::VersionDef* SavedTensorSliceMeta::mutable_versions() {
  
  if (versions_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VersionDef>(GetArenaNoVirtual());
    versions_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSliceMeta.versions)
  return versions_;
}
inline void SavedTensorSliceMeta::set_allocated_versions(::tensorflow::VersionDef* versions) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(versions_);
  }
  if (versions) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(versions)->GetArena();
    if (message_arena != submessage_arena) {
      versions = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, versions, submessage_arena);
    }
    
  } else {
    
  }
  versions_ = versions;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedTensorSliceMeta.versions)
}

// -------------------------------------------------------------------

// SavedSlice

// string name = 1;
inline void SavedSlice::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SavedSlice::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSlice.name)
  return name_.Get();
}
inline void SavedSlice::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedSlice.name)
}
inline void SavedSlice::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedSlice.name)
}
inline void SavedSlice::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedSlice.name)
}
inline void SavedSlice::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedSlice.name)
}
inline std::string* SavedSlice::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSlice.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SavedSlice::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSlice.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedSlice::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSlice.name)
}
inline std::string* SavedSlice::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedSlice.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedSlice::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedSlice.name)
}

// .tensorflow.TensorSliceProto slice = 2;
inline bool SavedSlice::has_slice() const {
  return this != internal_default_instance() && slice_ != nullptr;
}
inline const ::tensorflow::TensorSliceProto& SavedSlice::slice() const {
  const ::tensorflow::TensorSliceProto* p = slice_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedSlice.slice)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorSliceProto*>(
      &::tensorflow::_TensorSliceProto_default_instance_);
}
inline ::tensorflow::TensorSliceProto* SavedSlice::release_slice() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSlice.slice)
  
  ::tensorflow::TensorSliceProto* temp = slice_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  slice_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorSliceProto* SavedSlice::unsafe_arena_release_slice() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedSlice.slice)
  
  ::tensorflow::TensorSliceProto* temp = slice_;
  slice_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorSliceProto* SavedSlice::mutable_slice() {
  
  if (slice_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorSliceProto>(GetArenaNoVirtual());
    slice_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSlice.slice)
  return slice_;
}
inline void SavedSlice::set_allocated_slice(::tensorflow::TensorSliceProto* slice) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(slice_);
  }
  if (slice) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(slice)->GetArena();
    if (message_arena != submessage_arena) {
      slice = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, slice, submessage_arena);
    }
    
  } else {
    
  }
  slice_ = slice;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSlice.slice)
}

// .tensorflow.TensorProto data = 3;
inline bool SavedSlice::has_data() const {
  return this != internal_default_instance() && data_ != nullptr;
}
inline const ::tensorflow::TensorProto& SavedSlice::data() const {
  const ::tensorflow::TensorProto* p = data_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedSlice.data)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* SavedSlice::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSlice.data)
  
  ::tensorflow::TensorProto* temp = data_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  data_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* SavedSlice::unsafe_arena_release_data() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedSlice.data)
  
  ::tensorflow::TensorProto* temp = data_;
  data_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* SavedSlice::mutable_data() {
  
  if (data_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSlice.data)
  return data_;
}
inline void SavedSlice::set_allocated_data(::tensorflow::TensorProto* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_);
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data)->GetArena();
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSlice.data)
}

// -------------------------------------------------------------------

// SavedTensorSlices

// .tensorflow.SavedTensorSliceMeta meta = 1;
inline bool SavedTensorSlices::has_meta() const {
  return this != internal_default_instance() && meta_ != nullptr;
}
inline void SavedTensorSlices::clear_meta() {
  if (GetArenaNoVirtual() == nullptr && meta_ != nullptr) {
    delete meta_;
  }
  meta_ = nullptr;
}
inline const ::tensorflow::SavedTensorSliceMeta& SavedTensorSlices::meta() const {
  const ::tensorflow::SavedTensorSliceMeta* p = meta_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSlices.meta)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::SavedTensorSliceMeta*>(
      &::tensorflow::_SavedTensorSliceMeta_default_instance_);
}
inline ::tensorflow::SavedTensorSliceMeta* SavedTensorSlices::release_meta() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedTensorSlices.meta)
  
  ::tensorflow::SavedTensorSliceMeta* temp = meta_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  meta_ = nullptr;
  return temp;
}
inline ::tensorflow::SavedTensorSliceMeta* SavedTensorSlices::unsafe_arena_release_meta() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedTensorSlices.meta)
  
  ::tensorflow::SavedTensorSliceMeta* temp = meta_;
  meta_ = nullptr;
  return temp;
}
inline ::tensorflow::SavedTensorSliceMeta* SavedTensorSlices::mutable_meta() {
  
  if (meta_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SavedTensorSliceMeta>(GetArenaNoVirtual());
    meta_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSlices.meta)
  return meta_;
}
inline void SavedTensorSlices::set_allocated_meta(::tensorflow::SavedTensorSliceMeta* meta) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete meta_;
  }
  if (meta) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(meta);
    if (message_arena != submessage_arena) {
      meta = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, meta, submessage_arena);
    }
    
  } else {
    
  }
  meta_ = meta;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedTensorSlices.meta)
}

// .tensorflow.SavedSlice data = 2;
inline bool SavedTensorSlices::has_data() const {
  return this != internal_default_instance() && data_ != nullptr;
}
inline void SavedTensorSlices::clear_data() {
  if (GetArenaNoVirtual() == nullptr && data_ != nullptr) {
    delete data_;
  }
  data_ = nullptr;
}
inline const ::tensorflow::SavedSlice& SavedTensorSlices::data() const {
  const ::tensorflow::SavedSlice* p = data_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSlices.data)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::SavedSlice*>(
      &::tensorflow::_SavedSlice_default_instance_);
}
inline ::tensorflow::SavedSlice* SavedTensorSlices::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedTensorSlices.data)
  
  ::tensorflow::SavedSlice* temp = data_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  data_ = nullptr;
  return temp;
}
inline ::tensorflow::SavedSlice* SavedTensorSlices::unsafe_arena_release_data() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedTensorSlices.data)
  
  ::tensorflow::SavedSlice* temp = data_;
  data_ = nullptr;
  return temp;
}
inline ::tensorflow::SavedSlice* SavedTensorSlices::mutable_data() {
  
  if (data_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SavedSlice>(GetArenaNoVirtual());
    data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSlices.data)
  return data_;
}
inline void SavedTensorSlices::set_allocated_data(::tensorflow::SavedSlice* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete data_;
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(data);
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedTensorSlices.data)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto
