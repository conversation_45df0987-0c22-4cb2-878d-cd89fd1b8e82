import warnings

from django.forms import <PERSON><PERSON><PERSON><PERSON> as BuiltinJ<PERSON><PERSON><PERSON>
from django.utils.deprecation import RemovedInDjango40Warning

__all__ = ['JSONField']


class JSONField(BuiltinJSONField):
    def __init__(self, *args, **kwargs):
        warnings.warn(
            'django.contrib.postgres.forms.JSO<PERSON>ield is deprecated in favor '
            'of django.forms.JSONField.',
            RemovedInDjango40Warning, stacklevel=2,
        )
        super().__init__(*args, **kwargs)
