# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2012
# Irriep <PERSON>la <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2018-10-19 23:15+0000\n"
"Last-Translator: Irriep <PERSON>la <PERSON> <<EMAIL>>\n"
"Language-Team: Breton (http://www.transifex.com/django/django/language/br/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: br\n"
"Plural-Forms: nplurals=5; plural=((n%10 == 1) && (n%100 != 11) && (n%100 !"
"=71) && (n%100 !=91) ? 0 :(n%10 == 2) && (n%100 != 12) && (n%100 !=72) && (n"
"%100 !=92) ? 1 :(n%10 ==3 || n%10==4 || n%10==9) && (n%100 < 10 || n% 100 > "
"19) && (n%100 < 70 || n%100 > 79) && (n%100 < 90 || n%100 > 99) ? 2 :(n != 0 "
"&& n % 1000000 == 0) ? 3 : 4);\n"

msgid "Redirects"
msgstr "Adkasadennoù"

msgid "site"
msgstr "lec'hienn"

msgid "redirect from"
msgstr "adkaset eus"

msgid ""
"This should be an absolute path, excluding the domain name. Example: '/"
"events/search/'."
msgstr ""
"An dra-se a rankfe bezañ un hent absolud, en ur dennañ an holl anvioù "
"domani. Da skouer: '/events/search /'."

msgid "redirect to"
msgstr "adkas da"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with "
"'http://'."
msgstr ""
"An dra-se a c'hall bezañ un hent absolud (evel a-us) pe un URL klok o kregiñ "
"gant 'http://'."

msgid "redirect"
msgstr "adkas"

msgid "redirects"
msgstr "adkasoù"
