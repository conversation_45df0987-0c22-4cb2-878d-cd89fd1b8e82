// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/compile_metadata.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/compiler/xla/xla.pb.h"
#include "tensorflow/compiler/xla/xla_data.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/protobuf/tpu/dynamic_padding.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto;
namespace tensorflow {
namespace tpu {
class TPUCompileMetadataProto;
class TPUCompileMetadataProtoDefaultTypeInternal;
extern TPUCompileMetadataProtoDefaultTypeInternal _TPUCompileMetadataProto_default_instance_;
class TPUCompileMetadataProto_Arg;
class TPUCompileMetadataProto_ArgDefaultTypeInternal;
extern TPUCompileMetadataProto_ArgDefaultTypeInternal _TPUCompileMetadataProto_Arg_default_instance_;
class TPUCompileMetadataProto_Retval;
class TPUCompileMetadataProto_RetvalDefaultTypeInternal;
extern TPUCompileMetadataProto_RetvalDefaultTypeInternal _TPUCompileMetadataProto_Retval_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tpu::TPUCompileMetadataProto* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUCompileMetadataProto>(Arena*);
template<> ::tensorflow::tpu::TPUCompileMetadataProto_Arg* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUCompileMetadataProto_Arg>(Arena*);
template<> ::tensorflow::tpu::TPUCompileMetadataProto_Retval* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUCompileMetadataProto_Retval>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tpu {

enum TPUCompileMetadataProto_Arg_Kind : int {
  TPUCompileMetadataProto_Arg_Kind_INVALID = 0,
  TPUCompileMetadataProto_Arg_Kind_PARAMETER = 1,
  TPUCompileMetadataProto_Arg_Kind_VARIABLE = 2,
  TPUCompileMetadataProto_Arg_Kind_GUARANTEED_CONSTANT = 3,
  TPUCompileMetadataProto_Arg_Kind_TPUCompileMetadataProto_Arg_Kind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  TPUCompileMetadataProto_Arg_Kind_TPUCompileMetadataProto_Arg_Kind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool TPUCompileMetadataProto_Arg_Kind_IsValid(int value);
constexpr TPUCompileMetadataProto_Arg_Kind TPUCompileMetadataProto_Arg_Kind_Kind_MIN = TPUCompileMetadataProto_Arg_Kind_INVALID;
constexpr TPUCompileMetadataProto_Arg_Kind TPUCompileMetadataProto_Arg_Kind_Kind_MAX = TPUCompileMetadataProto_Arg_Kind_GUARANTEED_CONSTANT;
constexpr int TPUCompileMetadataProto_Arg_Kind_Kind_ARRAYSIZE = TPUCompileMetadataProto_Arg_Kind_Kind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TPUCompileMetadataProto_Arg_Kind_descriptor();
template<typename T>
inline const std::string& TPUCompileMetadataProto_Arg_Kind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TPUCompileMetadataProto_Arg_Kind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TPUCompileMetadataProto_Arg_Kind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TPUCompileMetadataProto_Arg_Kind_descriptor(), enum_t_value);
}
inline bool TPUCompileMetadataProto_Arg_Kind_Parse(
    const std::string& name, TPUCompileMetadataProto_Arg_Kind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TPUCompileMetadataProto_Arg_Kind>(
    TPUCompileMetadataProto_Arg_Kind_descriptor(), name, value);
}
enum TPUCompileMetadataProto_Arg_EnableXlaSharding : int {
  TPUCompileMetadataProto_Arg_EnableXlaSharding_DISALLOWED = 0,
  TPUCompileMetadataProto_Arg_EnableXlaSharding_TENTATIVE = 1,
  TPUCompileMetadataProto_Arg_EnableXlaSharding_ALLOWED = 2,
  TPUCompileMetadataProto_Arg_EnableXlaSharding_TPUCompileMetadataProto_Arg_EnableXlaSharding_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  TPUCompileMetadataProto_Arg_EnableXlaSharding_TPUCompileMetadataProto_Arg_EnableXlaSharding_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool TPUCompileMetadataProto_Arg_EnableXlaSharding_IsValid(int value);
constexpr TPUCompileMetadataProto_Arg_EnableXlaSharding TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_MIN = TPUCompileMetadataProto_Arg_EnableXlaSharding_DISALLOWED;
constexpr TPUCompileMetadataProto_Arg_EnableXlaSharding TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_MAX = TPUCompileMetadataProto_Arg_EnableXlaSharding_ALLOWED;
constexpr int TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_ARRAYSIZE = TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TPUCompileMetadataProto_Arg_EnableXlaSharding_descriptor();
template<typename T>
inline const std::string& TPUCompileMetadataProto_Arg_EnableXlaSharding_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TPUCompileMetadataProto_Arg_EnableXlaSharding>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TPUCompileMetadataProto_Arg_EnableXlaSharding_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TPUCompileMetadataProto_Arg_EnableXlaSharding_descriptor(), enum_t_value);
}
inline bool TPUCompileMetadataProto_Arg_EnableXlaSharding_Parse(
    const std::string& name, TPUCompileMetadataProto_Arg_EnableXlaSharding* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TPUCompileMetadataProto_Arg_EnableXlaSharding>(
    TPUCompileMetadataProto_Arg_EnableXlaSharding_descriptor(), name, value);
}
// ===================================================================

class TPUCompileMetadataProto_Arg :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUCompileMetadataProto.Arg) */ {
 public:
  TPUCompileMetadataProto_Arg();
  virtual ~TPUCompileMetadataProto_Arg();

  TPUCompileMetadataProto_Arg(const TPUCompileMetadataProto_Arg& from);
  TPUCompileMetadataProto_Arg(TPUCompileMetadataProto_Arg&& from) noexcept
    : TPUCompileMetadataProto_Arg() {
    *this = ::std::move(from);
  }

  inline TPUCompileMetadataProto_Arg& operator=(const TPUCompileMetadataProto_Arg& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUCompileMetadataProto_Arg& operator=(TPUCompileMetadataProto_Arg&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUCompileMetadataProto_Arg& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUCompileMetadataProto_Arg* internal_default_instance() {
    return reinterpret_cast<const TPUCompileMetadataProto_Arg*>(
               &_TPUCompileMetadataProto_Arg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TPUCompileMetadataProto_Arg& a, TPUCompileMetadataProto_Arg& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUCompileMetadataProto_Arg* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUCompileMetadataProto_Arg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUCompileMetadataProto_Arg* New() const final {
    return CreateMaybeMessage<TPUCompileMetadataProto_Arg>(nullptr);
  }

  TPUCompileMetadataProto_Arg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUCompileMetadataProto_Arg>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUCompileMetadataProto_Arg& from);
  void MergeFrom(const TPUCompileMetadataProto_Arg& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUCompileMetadataProto_Arg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUCompileMetadataProto.Arg";
  }
  protected:
  explicit TPUCompileMetadataProto_Arg(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TPUCompileMetadataProto_Arg_Kind Kind;
  static constexpr Kind INVALID =
    TPUCompileMetadataProto_Arg_Kind_INVALID;
  static constexpr Kind PARAMETER =
    TPUCompileMetadataProto_Arg_Kind_PARAMETER;
  static constexpr Kind VARIABLE =
    TPUCompileMetadataProto_Arg_Kind_VARIABLE;
  static constexpr Kind GUARANTEED_CONSTANT =
    TPUCompileMetadataProto_Arg_Kind_GUARANTEED_CONSTANT;
  static inline bool Kind_IsValid(int value) {
    return TPUCompileMetadataProto_Arg_Kind_IsValid(value);
  }
  static constexpr Kind Kind_MIN =
    TPUCompileMetadataProto_Arg_Kind_Kind_MIN;
  static constexpr Kind Kind_MAX =
    TPUCompileMetadataProto_Arg_Kind_Kind_MAX;
  static constexpr int Kind_ARRAYSIZE =
    TPUCompileMetadataProto_Arg_Kind_Kind_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Kind_descriptor() {
    return TPUCompileMetadataProto_Arg_Kind_descriptor();
  }
  template<typename T>
  static inline const std::string& Kind_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Kind>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Kind_Name.");
    return TPUCompileMetadataProto_Arg_Kind_Name(enum_t_value);
  }
  static inline bool Kind_Parse(const std::string& name,
      Kind* value) {
    return TPUCompileMetadataProto_Arg_Kind_Parse(name, value);
  }

  typedef TPUCompileMetadataProto_Arg_EnableXlaSharding EnableXlaSharding;
  static constexpr EnableXlaSharding DISALLOWED =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_DISALLOWED;
  static constexpr EnableXlaSharding TENTATIVE =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_TENTATIVE;
  static constexpr EnableXlaSharding ALLOWED =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_ALLOWED;
  static inline bool EnableXlaSharding_IsValid(int value) {
    return TPUCompileMetadataProto_Arg_EnableXlaSharding_IsValid(value);
  }
  static constexpr EnableXlaSharding EnableXlaSharding_MIN =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_MIN;
  static constexpr EnableXlaSharding EnableXlaSharding_MAX =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_MAX;
  static constexpr int EnableXlaSharding_ARRAYSIZE =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  EnableXlaSharding_descriptor() {
    return TPUCompileMetadataProto_Arg_EnableXlaSharding_descriptor();
  }
  template<typename T>
  static inline const std::string& EnableXlaSharding_Name(T enum_t_value) {
    static_assert(::std::is_same<T, EnableXlaSharding>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function EnableXlaSharding_Name.");
    return TPUCompileMetadataProto_Arg_EnableXlaSharding_Name(enum_t_value);
  }
  static inline bool EnableXlaSharding_Parse(const std::string& name,
      EnableXlaSharding* value) {
    return TPUCompileMetadataProto_Arg_EnableXlaSharding_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 10,
    kShapeFieldNumber = 2,
    kShardingFieldNumber = 4,
    kDtypeFieldNumber = 1,
    kKindFieldNumber = 3,
    kEnableXlaShardingFieldNumber = 6,
    kRetvalIndexForShardingFieldNumber = 8,
    kIsSameDataAcrossReplicasFieldNumber = 5,
    kFastMemFieldNumber = 7,
    kUnrestrictedLayoutFieldNumber = 9,
    kRequiresXlaBroadcastFieldNumber = 11,
  };
  // string name = 10;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .xla.OpSharding sharding = 4;
  bool has_sharding() const;
  void clear_sharding();
  const ::xla::OpSharding& sharding() const;
  ::xla::OpSharding* release_sharding();
  ::xla::OpSharding* mutable_sharding();
  void set_allocated_sharding(::xla::OpSharding* sharding);
  void unsafe_arena_set_allocated_sharding(
      ::xla::OpSharding* sharding);
  ::xla::OpSharding* unsafe_arena_release_sharding();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // .tensorflow.tpu.TPUCompileMetadataProto.Arg.Kind kind = 3;
  void clear_kind();
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind kind() const;
  void set_kind(::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind value);

  // .tensorflow.tpu.TPUCompileMetadataProto.Arg.EnableXlaSharding enable_xla_sharding = 6;
  void clear_enable_xla_sharding();
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding enable_xla_sharding() const;
  void set_enable_xla_sharding(::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding value);

  // int32 retval_index_for_sharding = 8;
  void clear_retval_index_for_sharding();
  ::PROTOBUF_NAMESPACE_ID::int32 retval_index_for_sharding() const;
  void set_retval_index_for_sharding(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool is_same_data_across_replicas = 5;
  void clear_is_same_data_across_replicas();
  bool is_same_data_across_replicas() const;
  void set_is_same_data_across_replicas(bool value);

  // bool fast_mem = 7;
  void clear_fast_mem();
  bool fast_mem() const;
  void set_fast_mem(bool value);

  // bool unrestricted_layout = 9;
  void clear_unrestricted_layout();
  bool unrestricted_layout() const;
  void set_unrestricted_layout(bool value);

  // bool requires_xla_broadcast = 11;
  void clear_requires_xla_broadcast();
  bool requires_xla_broadcast() const;
  void set_requires_xla_broadcast(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUCompileMetadataProto.Arg)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::TensorShapeProto* shape_;
  ::xla::OpSharding* sharding_;
  int dtype_;
  int kind_;
  int enable_xla_sharding_;
  ::PROTOBUF_NAMESPACE_ID::int32 retval_index_for_sharding_;
  bool is_same_data_across_replicas_;
  bool fast_mem_;
  bool unrestricted_layout_;
  bool requires_xla_broadcast_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto;
};
// -------------------------------------------------------------------

class TPUCompileMetadataProto_Retval :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUCompileMetadataProto.Retval) */ {
 public:
  TPUCompileMetadataProto_Retval();
  virtual ~TPUCompileMetadataProto_Retval();

  TPUCompileMetadataProto_Retval(const TPUCompileMetadataProto_Retval& from);
  TPUCompileMetadataProto_Retval(TPUCompileMetadataProto_Retval&& from) noexcept
    : TPUCompileMetadataProto_Retval() {
    *this = ::std::move(from);
  }

  inline TPUCompileMetadataProto_Retval& operator=(const TPUCompileMetadataProto_Retval& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUCompileMetadataProto_Retval& operator=(TPUCompileMetadataProto_Retval&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUCompileMetadataProto_Retval& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUCompileMetadataProto_Retval* internal_default_instance() {
    return reinterpret_cast<const TPUCompileMetadataProto_Retval*>(
               &_TPUCompileMetadataProto_Retval_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TPUCompileMetadataProto_Retval& a, TPUCompileMetadataProto_Retval& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUCompileMetadataProto_Retval* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUCompileMetadataProto_Retval* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUCompileMetadataProto_Retval* New() const final {
    return CreateMaybeMessage<TPUCompileMetadataProto_Retval>(nullptr);
  }

  TPUCompileMetadataProto_Retval* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUCompileMetadataProto_Retval>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUCompileMetadataProto_Retval& from);
  void MergeFrom(const TPUCompileMetadataProto_Retval& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUCompileMetadataProto_Retval* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUCompileMetadataProto.Retval";
  }
  protected:
  explicit TPUCompileMetadataProto_Retval(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShardingFieldNumber = 1,
  };
  // .xla.OpSharding sharding = 1;
  bool has_sharding() const;
  void clear_sharding();
  const ::xla::OpSharding& sharding() const;
  ::xla::OpSharding* release_sharding();
  ::xla::OpSharding* mutable_sharding();
  void set_allocated_sharding(::xla::OpSharding* sharding);
  void unsafe_arena_set_allocated_sharding(
      ::xla::OpSharding* sharding);
  ::xla::OpSharding* unsafe_arena_release_sharding();

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUCompileMetadataProto.Retval)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::xla::OpSharding* sharding_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto;
};
// -------------------------------------------------------------------

class TPUCompileMetadataProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUCompileMetadataProto) */ {
 public:
  TPUCompileMetadataProto();
  virtual ~TPUCompileMetadataProto();

  TPUCompileMetadataProto(const TPUCompileMetadataProto& from);
  TPUCompileMetadataProto(TPUCompileMetadataProto&& from) noexcept
    : TPUCompileMetadataProto() {
    *this = ::std::move(from);
  }

  inline TPUCompileMetadataProto& operator=(const TPUCompileMetadataProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUCompileMetadataProto& operator=(TPUCompileMetadataProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TPUCompileMetadataProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUCompileMetadataProto* internal_default_instance() {
    return reinterpret_cast<const TPUCompileMetadataProto*>(
               &_TPUCompileMetadataProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TPUCompileMetadataProto& a, TPUCompileMetadataProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUCompileMetadataProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUCompileMetadataProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TPUCompileMetadataProto* New() const final {
    return CreateMaybeMessage<TPUCompileMetadataProto>(nullptr);
  }

  TPUCompileMetadataProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TPUCompileMetadataProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TPUCompileMetadataProto& from);
  void MergeFrom(const TPUCompileMetadataProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUCompileMetadataProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUCompileMetadataProto";
  }
  protected:
  explicit TPUCompileMetadataProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef TPUCompileMetadataProto_Arg Arg;
  typedef TPUCompileMetadataProto_Retval Retval;

  // accessors -------------------------------------------------------

  enum : int {
    kArgsFieldNumber = 1,
    kRetvalsFieldNumber = 2,
    kPaddingMapsFieldNumber = 11,
    kSessionHandleFieldNumber = 9,
    kGuaranteedConstFingerprintFieldNumber = 10,
    kDeviceAssignmentFieldNumber = 8,
    kNumReplicasFieldNumber = 3,
    kNumCoresPerReplicaFieldNumber = 4,
    kFunctionLibraryFingerprintFieldNumber = 6,
    kXlaFusionAutotunerThreshFieldNumber = 13,
    kStepMarkerLocationFieldNumber = 12,
    kEnableAutomaticModelParallelismFieldNumber = 14,
    kUseSpmdForXlaPartitioningFieldNumber = 15,
  };
  // repeated .tensorflow.tpu.TPUCompileMetadataProto.Arg args = 1;
  int args_size() const;
  void clear_args();
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg* mutable_args(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Arg >*
      mutable_args();
  const ::tensorflow::tpu::TPUCompileMetadataProto_Arg& args(int index) const;
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg* add_args();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Arg >&
      args() const;

  // repeated .tensorflow.tpu.TPUCompileMetadataProto.Retval retvals = 2;
  int retvals_size() const;
  void clear_retvals();
  ::tensorflow::tpu::TPUCompileMetadataProto_Retval* mutable_retvals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Retval >*
      mutable_retvals();
  const ::tensorflow::tpu::TPUCompileMetadataProto_Retval& retvals(int index) const;
  ::tensorflow::tpu::TPUCompileMetadataProto_Retval* add_retvals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Retval >&
      retvals() const;

  // repeated .tensorflow.tpu.PaddingMap padding_maps = 11;
  int padding_maps_size() const;
  void clear_padding_maps();
  ::tensorflow::tpu::PaddingMap* mutable_padding_maps(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::PaddingMap >*
      mutable_padding_maps();
  const ::tensorflow::tpu::PaddingMap& padding_maps(int index) const;
  ::tensorflow::tpu::PaddingMap* add_padding_maps();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::PaddingMap >&
      padding_maps() const;

  // string session_handle = 9;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // string guaranteed_const_fingerprint = 10;
  void clear_guaranteed_const_fingerprint();
  const std::string& guaranteed_const_fingerprint() const;
  void set_guaranteed_const_fingerprint(const std::string& value);
  void set_guaranteed_const_fingerprint(std::string&& value);
  void set_guaranteed_const_fingerprint(const char* value);
  void set_guaranteed_const_fingerprint(const char* value, size_t size);
  std::string* mutable_guaranteed_const_fingerprint();
  std::string* release_guaranteed_const_fingerprint();
  void set_allocated_guaranteed_const_fingerprint(std::string* guaranteed_const_fingerprint);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_guaranteed_const_fingerprint();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_guaranteed_const_fingerprint(
      std::string* guaranteed_const_fingerprint);

  // .xla.DeviceAssignmentProto device_assignment = 8;
  bool has_device_assignment() const;
  void clear_device_assignment();
  const ::xla::DeviceAssignmentProto& device_assignment() const;
  ::xla::DeviceAssignmentProto* release_device_assignment();
  ::xla::DeviceAssignmentProto* mutable_device_assignment();
  void set_allocated_device_assignment(::xla::DeviceAssignmentProto* device_assignment);
  void unsafe_arena_set_allocated_device_assignment(
      ::xla::DeviceAssignmentProto* device_assignment);
  ::xla::DeviceAssignmentProto* unsafe_arena_release_device_assignment();

  // int32 num_replicas = 3;
  void clear_num_replicas();
  ::PROTOBUF_NAMESPACE_ID::int32 num_replicas() const;
  void set_num_replicas(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 num_cores_per_replica = 4;
  void clear_num_cores_per_replica();
  ::PROTOBUF_NAMESPACE_ID::int32 num_cores_per_replica() const;
  void set_num_cores_per_replica(::PROTOBUF_NAMESPACE_ID::int32 value);

  // uint64 function_library_fingerprint = 6;
  void clear_function_library_fingerprint();
  ::PROTOBUF_NAMESPACE_ID::uint64 function_library_fingerprint() const;
  void set_function_library_fingerprint(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int64 xla_fusion_autotuner_thresh = 13;
  void clear_xla_fusion_autotuner_thresh();
  ::PROTOBUF_NAMESPACE_ID::int64 xla_fusion_autotuner_thresh() const;
  void set_xla_fusion_autotuner_thresh(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .xla.DebugOptions.StepMarkerLocation step_marker_location = 12;
  void clear_step_marker_location();
  ::xla::DebugOptions_StepMarkerLocation step_marker_location() const;
  void set_step_marker_location(::xla::DebugOptions_StepMarkerLocation value);

  // bool enable_automatic_model_parallelism = 14;
  void clear_enable_automatic_model_parallelism();
  bool enable_automatic_model_parallelism() const;
  void set_enable_automatic_model_parallelism(bool value);

  // bool use_spmd_for_xla_partitioning = 15;
  void clear_use_spmd_for_xla_partitioning();
  bool use_spmd_for_xla_partitioning() const;
  void set_use_spmd_for_xla_partitioning(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUCompileMetadataProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Arg > args_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Retval > retvals_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::PaddingMap > padding_maps_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr guaranteed_const_fingerprint_;
  ::xla::DeviceAssignmentProto* device_assignment_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_replicas_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_cores_per_replica_;
  ::PROTOBUF_NAMESPACE_ID::uint64 function_library_fingerprint_;
  ::PROTOBUF_NAMESPACE_ID::int64 xla_fusion_autotuner_thresh_;
  int step_marker_location_;
  bool enable_automatic_model_parallelism_;
  bool use_spmd_for_xla_partitioning_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TPUCompileMetadataProto_Arg

// .tensorflow.DataType dtype = 1;
inline void TPUCompileMetadataProto_Arg::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType TPUCompileMetadataProto_Arg::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void TPUCompileMetadataProto_Arg::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool TPUCompileMetadataProto_Arg::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& TPUCompileMetadataProto_Arg::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* TPUCompileMetadataProto_Arg::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.Arg.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TPUCompileMetadataProto_Arg::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.TPUCompileMetadataProto.Arg.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TPUCompileMetadataProto_Arg::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.Arg.shape)
  return shape_;
}
inline void TPUCompileMetadataProto_Arg::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Arg.shape)
}

// .tensorflow.tpu.TPUCompileMetadataProto.Arg.Kind kind = 3;
inline void TPUCompileMetadataProto_Arg::clear_kind() {
  kind_ = 0;
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind TPUCompileMetadataProto_Arg::kind() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.kind)
  return static_cast< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind >(kind_);
}
inline void TPUCompileMetadataProto_Arg::set_kind(::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind value) {
  
  kind_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.kind)
}

// .xla.OpSharding sharding = 4;
inline bool TPUCompileMetadataProto_Arg::has_sharding() const {
  return this != internal_default_instance() && sharding_ != nullptr;
}
inline const ::xla::OpSharding& TPUCompileMetadataProto_Arg::sharding() const {
  const ::xla::OpSharding* p = sharding_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.sharding)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::OpSharding*>(
      &::xla::_OpSharding_default_instance_);
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Arg::release_sharding() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.Arg.sharding)
  
  ::xla::OpSharding* temp = sharding_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Arg::unsafe_arena_release_sharding() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.TPUCompileMetadataProto.Arg.sharding)
  
  ::xla::OpSharding* temp = sharding_;
  sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Arg::mutable_sharding() {
  
  if (sharding_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::OpSharding>(GetArenaNoVirtual());
    sharding_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.Arg.sharding)
  return sharding_;
}
inline void TPUCompileMetadataProto_Arg::set_allocated_sharding(::xla::OpSharding* sharding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(sharding_);
  }
  if (sharding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(sharding)->GetArena();
    if (message_arena != submessage_arena) {
      sharding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sharding, submessage_arena);
    }
    
  } else {
    
  }
  sharding_ = sharding;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Arg.sharding)
}

// bool is_same_data_across_replicas = 5;
inline void TPUCompileMetadataProto_Arg::clear_is_same_data_across_replicas() {
  is_same_data_across_replicas_ = false;
}
inline bool TPUCompileMetadataProto_Arg::is_same_data_across_replicas() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.is_same_data_across_replicas)
  return is_same_data_across_replicas_;
}
inline void TPUCompileMetadataProto_Arg::set_is_same_data_across_replicas(bool value) {
  
  is_same_data_across_replicas_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.is_same_data_across_replicas)
}

// .tensorflow.tpu.TPUCompileMetadataProto.Arg.EnableXlaSharding enable_xla_sharding = 6;
inline void TPUCompileMetadataProto_Arg::clear_enable_xla_sharding() {
  enable_xla_sharding_ = 0;
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding TPUCompileMetadataProto_Arg::enable_xla_sharding() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.enable_xla_sharding)
  return static_cast< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding >(enable_xla_sharding_);
}
inline void TPUCompileMetadataProto_Arg::set_enable_xla_sharding(::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding value) {
  
  enable_xla_sharding_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.enable_xla_sharding)
}

// int32 retval_index_for_sharding = 8;
inline void TPUCompileMetadataProto_Arg::clear_retval_index_for_sharding() {
  retval_index_for_sharding_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUCompileMetadataProto_Arg::retval_index_for_sharding() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.retval_index_for_sharding)
  return retval_index_for_sharding_;
}
inline void TPUCompileMetadataProto_Arg::set_retval_index_for_sharding(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  retval_index_for_sharding_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.retval_index_for_sharding)
}

// bool fast_mem = 7;
inline void TPUCompileMetadataProto_Arg::clear_fast_mem() {
  fast_mem_ = false;
}
inline bool TPUCompileMetadataProto_Arg::fast_mem() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.fast_mem)
  return fast_mem_;
}
inline void TPUCompileMetadataProto_Arg::set_fast_mem(bool value) {
  
  fast_mem_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.fast_mem)
}

// bool unrestricted_layout = 9;
inline void TPUCompileMetadataProto_Arg::clear_unrestricted_layout() {
  unrestricted_layout_ = false;
}
inline bool TPUCompileMetadataProto_Arg::unrestricted_layout() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.unrestricted_layout)
  return unrestricted_layout_;
}
inline void TPUCompileMetadataProto_Arg::set_unrestricted_layout(bool value) {
  
  unrestricted_layout_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.unrestricted_layout)
}

// string name = 10;
inline void TPUCompileMetadataProto_Arg::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TPUCompileMetadataProto_Arg::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
  return name_.Get();
}
inline void TPUCompileMetadataProto_Arg::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
}
inline void TPUCompileMetadataProto_Arg::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
}
inline void TPUCompileMetadataProto_Arg::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
}
inline void TPUCompileMetadataProto_Arg::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
}
inline std::string* TPUCompileMetadataProto_Arg::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TPUCompileMetadataProto_Arg::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TPUCompileMetadataProto_Arg::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
}
inline std::string* TPUCompileMetadataProto_Arg::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TPUCompileMetadataProto_Arg::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
}

// bool requires_xla_broadcast = 11;
inline void TPUCompileMetadataProto_Arg::clear_requires_xla_broadcast() {
  requires_xla_broadcast_ = false;
}
inline bool TPUCompileMetadataProto_Arg::requires_xla_broadcast() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.requires_xla_broadcast)
  return requires_xla_broadcast_;
}
inline void TPUCompileMetadataProto_Arg::set_requires_xla_broadcast(bool value) {
  
  requires_xla_broadcast_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.requires_xla_broadcast)
}

// -------------------------------------------------------------------

// TPUCompileMetadataProto_Retval

// .xla.OpSharding sharding = 1;
inline bool TPUCompileMetadataProto_Retval::has_sharding() const {
  return this != internal_default_instance() && sharding_ != nullptr;
}
inline const ::xla::OpSharding& TPUCompileMetadataProto_Retval::sharding() const {
  const ::xla::OpSharding* p = sharding_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Retval.sharding)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::OpSharding*>(
      &::xla::_OpSharding_default_instance_);
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Retval::release_sharding() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.Retval.sharding)
  
  ::xla::OpSharding* temp = sharding_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Retval::unsafe_arena_release_sharding() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.TPUCompileMetadataProto.Retval.sharding)
  
  ::xla::OpSharding* temp = sharding_;
  sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Retval::mutable_sharding() {
  
  if (sharding_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::OpSharding>(GetArenaNoVirtual());
    sharding_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.Retval.sharding)
  return sharding_;
}
inline void TPUCompileMetadataProto_Retval::set_allocated_sharding(::xla::OpSharding* sharding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(sharding_);
  }
  if (sharding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(sharding)->GetArena();
    if (message_arena != submessage_arena) {
      sharding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sharding, submessage_arena);
    }
    
  } else {
    
  }
  sharding_ = sharding;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Retval.sharding)
}

// -------------------------------------------------------------------

// TPUCompileMetadataProto

// repeated .tensorflow.tpu.TPUCompileMetadataProto.Arg args = 1;
inline int TPUCompileMetadataProto::args_size() const {
  return args_.size();
}
inline void TPUCompileMetadataProto::clear_args() {
  args_.Clear();
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg* TPUCompileMetadataProto::mutable_args(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.args)
  return args_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Arg >*
TPUCompileMetadataProto::mutable_args() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUCompileMetadataProto.args)
  return &args_;
}
inline const ::tensorflow::tpu::TPUCompileMetadataProto_Arg& TPUCompileMetadataProto::args(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.args)
  return args_.Get(index);
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg* TPUCompileMetadataProto::add_args() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUCompileMetadataProto.args)
  return args_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Arg >&
TPUCompileMetadataProto::args() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUCompileMetadataProto.args)
  return args_;
}

// repeated .tensorflow.tpu.TPUCompileMetadataProto.Retval retvals = 2;
inline int TPUCompileMetadataProto::retvals_size() const {
  return retvals_.size();
}
inline void TPUCompileMetadataProto::clear_retvals() {
  retvals_.Clear();
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Retval* TPUCompileMetadataProto::mutable_retvals(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.retvals)
  return retvals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Retval >*
TPUCompileMetadataProto::mutable_retvals() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUCompileMetadataProto.retvals)
  return &retvals_;
}
inline const ::tensorflow::tpu::TPUCompileMetadataProto_Retval& TPUCompileMetadataProto::retvals(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.retvals)
  return retvals_.Get(index);
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Retval* TPUCompileMetadataProto::add_retvals() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUCompileMetadataProto.retvals)
  return retvals_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Retval >&
TPUCompileMetadataProto::retvals() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUCompileMetadataProto.retvals)
  return retvals_;
}

// int32 num_replicas = 3;
inline void TPUCompileMetadataProto::clear_num_replicas() {
  num_replicas_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUCompileMetadataProto::num_replicas() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.num_replicas)
  return num_replicas_;
}
inline void TPUCompileMetadataProto::set_num_replicas(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_replicas_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.num_replicas)
}

// int32 num_cores_per_replica = 4;
inline void TPUCompileMetadataProto::clear_num_cores_per_replica() {
  num_cores_per_replica_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 TPUCompileMetadataProto::num_cores_per_replica() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.num_cores_per_replica)
  return num_cores_per_replica_;
}
inline void TPUCompileMetadataProto::set_num_cores_per_replica(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_cores_per_replica_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.num_cores_per_replica)
}

// .xla.DeviceAssignmentProto device_assignment = 8;
inline bool TPUCompileMetadataProto::has_device_assignment() const {
  return this != internal_default_instance() && device_assignment_ != nullptr;
}
inline const ::xla::DeviceAssignmentProto& TPUCompileMetadataProto::device_assignment() const {
  const ::xla::DeviceAssignmentProto* p = device_assignment_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.device_assignment)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::DeviceAssignmentProto*>(
      &::xla::_DeviceAssignmentProto_default_instance_);
}
inline ::xla::DeviceAssignmentProto* TPUCompileMetadataProto::release_device_assignment() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.device_assignment)
  
  ::xla::DeviceAssignmentProto* temp = device_assignment_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  device_assignment_ = nullptr;
  return temp;
}
inline ::xla::DeviceAssignmentProto* TPUCompileMetadataProto::unsafe_arena_release_device_assignment() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.TPUCompileMetadataProto.device_assignment)
  
  ::xla::DeviceAssignmentProto* temp = device_assignment_;
  device_assignment_ = nullptr;
  return temp;
}
inline ::xla::DeviceAssignmentProto* TPUCompileMetadataProto::mutable_device_assignment() {
  
  if (device_assignment_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DeviceAssignmentProto>(GetArenaNoVirtual());
    device_assignment_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.device_assignment)
  return device_assignment_;
}
inline void TPUCompileMetadataProto::set_allocated_device_assignment(::xla::DeviceAssignmentProto* device_assignment) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_assignment_);
  }
  if (device_assignment) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_assignment)->GetArena();
    if (message_arena != submessage_arena) {
      device_assignment = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_assignment, submessage_arena);
    }
    
  } else {
    
  }
  device_assignment_ = device_assignment;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.device_assignment)
}

// uint64 function_library_fingerprint = 6;
inline void TPUCompileMetadataProto::clear_function_library_fingerprint() {
  function_library_fingerprint_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 TPUCompileMetadataProto::function_library_fingerprint() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.function_library_fingerprint)
  return function_library_fingerprint_;
}
inline void TPUCompileMetadataProto::set_function_library_fingerprint(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  function_library_fingerprint_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.function_library_fingerprint)
}

// string session_handle = 9;
inline void TPUCompileMetadataProto::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TPUCompileMetadataProto::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
  return session_handle_.Get();
}
inline void TPUCompileMetadataProto::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
}
inline void TPUCompileMetadataProto::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
}
inline void TPUCompileMetadataProto::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
}
inline void TPUCompileMetadataProto::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
}
inline std::string* TPUCompileMetadataProto::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TPUCompileMetadataProto::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TPUCompileMetadataProto::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
}
inline std::string* TPUCompileMetadataProto::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TPUCompileMetadataProto::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
}

// string guaranteed_const_fingerprint = 10;
inline void TPUCompileMetadataProto::clear_guaranteed_const_fingerprint() {
  guaranteed_const_fingerprint_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& TPUCompileMetadataProto::guaranteed_const_fingerprint() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
  return guaranteed_const_fingerprint_.Get();
}
inline void TPUCompileMetadataProto::set_guaranteed_const_fingerprint(const std::string& value) {
  
  guaranteed_const_fingerprint_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
}
inline void TPUCompileMetadataProto::set_guaranteed_const_fingerprint(std::string&& value) {
  
  guaranteed_const_fingerprint_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
}
inline void TPUCompileMetadataProto::set_guaranteed_const_fingerprint(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  guaranteed_const_fingerprint_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
}
inline void TPUCompileMetadataProto::set_guaranteed_const_fingerprint(const char* value,
    size_t size) {
  
  guaranteed_const_fingerprint_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
}
inline std::string* TPUCompileMetadataProto::mutable_guaranteed_const_fingerprint() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
  return guaranteed_const_fingerprint_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* TPUCompileMetadataProto::release_guaranteed_const_fingerprint() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
  
  return guaranteed_const_fingerprint_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TPUCompileMetadataProto::set_allocated_guaranteed_const_fingerprint(std::string* guaranteed_const_fingerprint) {
  if (guaranteed_const_fingerprint != nullptr) {
    
  } else {
    
  }
  guaranteed_const_fingerprint_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), guaranteed_const_fingerprint,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
}
inline std::string* TPUCompileMetadataProto::unsafe_arena_release_guaranteed_const_fingerprint() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return guaranteed_const_fingerprint_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TPUCompileMetadataProto::unsafe_arena_set_allocated_guaranteed_const_fingerprint(
    std::string* guaranteed_const_fingerprint) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (guaranteed_const_fingerprint != nullptr) {
    
  } else {
    
  }
  guaranteed_const_fingerprint_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      guaranteed_const_fingerprint, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
}

// repeated .tensorflow.tpu.PaddingMap padding_maps = 11;
inline int TPUCompileMetadataProto::padding_maps_size() const {
  return padding_maps_.size();
}
inline ::tensorflow::tpu::PaddingMap* TPUCompileMetadataProto::mutable_padding_maps(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.padding_maps)
  return padding_maps_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::PaddingMap >*
TPUCompileMetadataProto::mutable_padding_maps() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUCompileMetadataProto.padding_maps)
  return &padding_maps_;
}
inline const ::tensorflow::tpu::PaddingMap& TPUCompileMetadataProto::padding_maps(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.padding_maps)
  return padding_maps_.Get(index);
}
inline ::tensorflow::tpu::PaddingMap* TPUCompileMetadataProto::add_padding_maps() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUCompileMetadataProto.padding_maps)
  return padding_maps_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::PaddingMap >&
TPUCompileMetadataProto::padding_maps() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUCompileMetadataProto.padding_maps)
  return padding_maps_;
}

// .xla.DebugOptions.StepMarkerLocation step_marker_location = 12;
inline void TPUCompileMetadataProto::clear_step_marker_location() {
  step_marker_location_ = 0;
}
inline ::xla::DebugOptions_StepMarkerLocation TPUCompileMetadataProto::step_marker_location() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.step_marker_location)
  return static_cast< ::xla::DebugOptions_StepMarkerLocation >(step_marker_location_);
}
inline void TPUCompileMetadataProto::set_step_marker_location(::xla::DebugOptions_StepMarkerLocation value) {
  
  step_marker_location_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.step_marker_location)
}

// int64 xla_fusion_autotuner_thresh = 13;
inline void TPUCompileMetadataProto::clear_xla_fusion_autotuner_thresh() {
  xla_fusion_autotuner_thresh_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TPUCompileMetadataProto::xla_fusion_autotuner_thresh() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.xla_fusion_autotuner_thresh)
  return xla_fusion_autotuner_thresh_;
}
inline void TPUCompileMetadataProto::set_xla_fusion_autotuner_thresh(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  xla_fusion_autotuner_thresh_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.xla_fusion_autotuner_thresh)
}

// bool enable_automatic_model_parallelism = 14;
inline void TPUCompileMetadataProto::clear_enable_automatic_model_parallelism() {
  enable_automatic_model_parallelism_ = false;
}
inline bool TPUCompileMetadataProto::enable_automatic_model_parallelism() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.enable_automatic_model_parallelism)
  return enable_automatic_model_parallelism_;
}
inline void TPUCompileMetadataProto::set_enable_automatic_model_parallelism(bool value) {
  
  enable_automatic_model_parallelism_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.enable_automatic_model_parallelism)
}

// bool use_spmd_for_xla_partitioning = 15;
inline void TPUCompileMetadataProto::clear_use_spmd_for_xla_partitioning() {
  use_spmd_for_xla_partitioning_ = false;
}
inline bool TPUCompileMetadataProto::use_spmd_for_xla_partitioning() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.use_spmd_for_xla_partitioning)
  return use_spmd_for_xla_partitioning_;
}
inline void TPUCompileMetadataProto::set_use_spmd_for_xla_partitioning(bool value) {
  
  use_spmd_for_xla_partitioning_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.use_spmd_for_xla_partitioning)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind>() {
  return ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding>() {
  return ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto
