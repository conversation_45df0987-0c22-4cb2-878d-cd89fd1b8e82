# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>u Bataa, 2017
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2011-2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-10-19 14:01+0000\n"
"Last-Translator: Bayarkhuu Bataa\n"
"Language-Team: Mongolian (http://www.transifex.com/django/django/language/"
"mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Агуулгын төрөл"

msgid "python model class name"
msgstr "пайтоны моделын классын нэр"

msgid "content type"
msgstr "агуулгын төрөл"

msgid "content types"
msgstr "агуулгын төрлүүд"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "%(ct_id)s төрлийн холбоотой модель олдсонгүй"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "%(ct_id)s төрлийн %(obj_id)s объект олдсонгүй"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr ""
"%(ct_name)s объектууд дээр get_absolute_url() функцийг тодорхойлоогүй байна."
