#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析脚本
查看爬虫获取的装备数据
"""

import csv
import json
from datetime import datetime

def analyze_csv_data(filename):
    """分析CSV数据"""
    print(f"🔍 分析文件: {filename}")
    print("=" * 60)
    
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:  # 使用utf-8-sig处理BOM
            reader = csv.DictReader(f)
            data = list(reader)
            
        print(f"📊 数据总数: {len(data)} 条")
        print(f"📋 字段列表: {list(data[0].keys()) if data else '无数据'}")
        print()
        
        # 逐条分析数据
        for i, item in enumerate(data, 1):
            print(f"🏸 装备 {i}: {item.get('name', '未知')}")
            print(f"   品牌: {item.get('brand', '未知')}")
            print(f"   价格: {item.get('price', '未知')}")
            print(f"   重量: {item.get('weight', '未知')}")
            print(f"   平衡: {item.get('balance', '未知')}")
            print(f"   硬度: {item.get('stiffness', '未知')}")
            print(f"   系列: {item.get('series', '未知')}")
            print(f"   链接: {item.get('url', '未知')}")
            print(f"   时间: {item.get('crawl_time', '未知')}")
            print("-" * 40)
            
        # 统计信息
        brands = [item.get('brand', '') for item in data if item.get('brand')]
        prices = [item.get('price', '') for item in data if item.get('price')]
        series = [item.get('series', '') for item in data if item.get('series')]
        
        print(f"\n📈 统计信息:")
        print(f"   有效品牌数: {len([b for b in brands if b])}")
        print(f"   有效价格数: {len([p for p in prices if p])}")
        print(f"   有效系列数: {len([s for s in series if s])}")
        print(f"   品牌分布: {set(brands)}")
        print(f"   系列分布: {set(series)}")
        
        return data
        
    except Exception as e:
        print(f"❌ 读取文件错误: {e}")
        return []

if __name__ == "__main__":
    # 分析最新的爬虫数据
    filename = "browser_crawl_20250617_112317.csv"
    analyze_csv_data(filename) 