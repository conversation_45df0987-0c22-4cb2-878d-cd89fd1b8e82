import random

from apps.analysis.health_metrics import rf_model_index
from apps.models.analysis_models import HealthMetricsEntity


def process(waveform_info, use_enhanced=True):
    """
    健康指标处理函数
    :param waveform_info: 波形信息
    :param use_enhanced: 是否使用增强版WESAD分类器 (默认True)
    :return: 健康指标
    """

    if use_enhanced:
        # 使用增强版WESAD分类器
        try:
            from apps.analysis.health_metrics.enhanced_diagnosis import process as enhanced_process
            return enhanced_process(waveform_info)
        except ImportError:
            # 如果增强版不可用，回退到原版
            pass

    # 原有的处理逻辑
    nn_intervals = waveform_info['waveform']['nn_intervals']
    sdnn_value = waveform_info['hrv']['linear']['sdnn']

    health_metrics = HealthMetricsEntity()

    y_pred = rf_model_index.run_index_main(nn_intervals)

    health_metrics.Pressure = y_pred[0]

    health_metrics.HRV = random.randint(130, 150) if sdnn_value < 30 or sdnn_value > 200 else sdnn_value
    health_metrics.Emotion = y_pred[1]
    health_metrics.Fatigue = y_pred[2]
    health_metrics.Vitality = y_pred[3]
    health_metrics.HeartAge = 0

    return health_metrics


def process_with_wesad(waveform_info):
    """
    使用WESAD增强版分类器的便捷函数
    :param waveform_info: 波形信息
    :return: 增强版健康指标
    """
    return process(waveform_info, use_enhanced=True)
