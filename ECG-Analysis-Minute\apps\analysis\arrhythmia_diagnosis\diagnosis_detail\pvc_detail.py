import numpy as np
from apps.utils.logger_helper import Logger
import traceback


def detect_classify_pvc(rr_intervals, r_peaks, prematurity_threshold=0.85, compensatory_threshold=1.15,
                        min_vt_run=3):
    """
    基于时序检测和分类室性早搏 。
    :param rr_intervals: RR间期列表 (秒)
    :param r_peaks: R峰索引列表
    :param prematurity_threshold: 提前阈值 (例如，RR[i] < 0.85 * local_avg_rr)
    :param compensatory_threshold: 代偿阈值 (例如，RR[i+1] > 1.15 * local_avg_rr)
    :param min_vt_run: 室速最小连发数
    :return: 字典 {'pvc_indices': list, 'classification': dict}
    """
    pvc_indices = []
    # 为所有R峰初始化心搏类型
    beat_types = ['N'] * len(r_peaks)  # N: 正常, V: 室性早搏

    if rr_intervals is None or len(rr_intervals) < 2 or r_peaks is None or len(r_peaks) < 3:
        return {'pvc_indices': [],
                'classification': {'single': 0, 'pair': 0, 'run': [], 'bigeminy_count': 0, 'trigeminy_count': 0}}

    rr_intervals = np.array(rr_intervals)
    r_peaks = np.array(r_peaks)

    try:
        # 使用移动窗口稳健地计算局部平均RR间期
        window_size = 5
        if len(rr_intervals) >= window_size:
            local_avg_rr = np.convolve(rr_intervals, np.ones(window_size) / window_size, mode='valid')
            padding_start = (window_size - 1) // 2
            padding_end = (window_size - 1) - padding_start
            local_avg_rr_padded = np.pad(local_avg_rr, (padding_start, padding_end), mode='edge')
        elif len(rr_intervals) > 0:
            # 如果数据不足以使用窗口，则使用全局平均值
            mean_rr = np.mean(rr_intervals)
            local_avg_rr_padded = np.full(len(rr_intervals), mean_rr)
        else:  # 没有RR间期
            return {'pvc_indices': [],
                    'classification': {'single': 0, 'pair': 0, 'run': [], 'bigeminy_count': 0, 'trigeminy_count': 0}}

        # 遍历RR间期以基于时序检测潜在的室性早搏
        for i in range(len(rr_intervals)):
            baseline_rr = local_avg_rr_padded[i]

            if baseline_rr <= 0: continue  # 避免除以零

            is_premature = rr_intervals[i] < prematurity_threshold * baseline_rr

            # 代偿间歇检测
            is_compensatory = False
            if i + 1 < len(rr_intervals):
                baseline_rr_comp = local_avg_rr_padded[i + 1]
                if baseline_rr_comp > 0:
                    is_compensatory = rr_intervals[i + 1] > compensatory_threshold * baseline_rr_comp

            if is_premature:
                pvc_r_peak_index = i + 1
                if pvc_r_peak_index < len(beat_types):
                    pvc_indices.append(int(r_peaks[pvc_r_peak_index]))
                    beat_types[pvc_r_peak_index] = 'V'

        # 基于分类的室性早搏统计
        singles = 0
        pairs = 0
        runs = []
        bigeminy_count = 0
        trigeminy_count = 0
        i = 0
        while i < len(beat_types):
            if beat_types[i] == 'V':
                run_count = 0
                start_beat_idx = i
                while i < len(beat_types) and beat_types[i] == 'V':
                    run_count += 1
                    i += 1

                if run_count == 1:
                    singles += 1
                    # 二联律/三联律检查依赖于前面的心搏
                    if start_beat_idx > 0 and beat_types[start_beat_idx - 1] == 'N':
                        bigeminy_count += 1
                    if start_beat_idx > 1 and beat_types[start_beat_idx - 1] == 'N' and beat_types[
                        start_beat_idx - 2] == 'N':
                        trigeminy_count += 1

                elif run_count == 2:
                    pairs += 1
                elif run_count >= min_vt_run:
                    run_start_rpeak_idx = start_beat_idx
                    run_end_rpeak_idx = i - 1
                    run_start_rpeak_idx = start_beat_idx
                    run_end_rpeak_idx = i - 1

                    if run_start_rpeak_idx < len(rr_intervals) and run_end_rpeak_idx > run_start_rpeak_idx:
                        run_rrs = rr_intervals[
                                  run_start_rpeak_idx: run_end_rpeak_idx]
                        if len(run_rrs) > 0:
                            avg_rr = np.mean(run_rrs)
                            min_hr = 60 / np.max(run_rrs) if np.max(run_rrs) > 0 else 0
                            max_hr = 60 / np.min(run_rrs) if np.min(run_rrs) > 0 else 0
                            runs.append({
                                'start_index': int(r_peaks[run_start_rpeak_idx]),
                                'count': run_count,
                                'avg_rr': avg_rr,
                                'min_hr': min_hr,
                                'max_hr': max_hr
                            })
                        else:
                            runs.append(
                                {'start_index': int(r_peaks[run_start_rpeak_idx]), 'count': run_count, 'avg_rr': 0,
                                 'min_hr': 0, 'max_hr': 0})
                    else:
                        runs.append({'start_index': int(r_peaks[run_start_rpeak_idx]), 'count': run_count, 'avg_rr': 0,
                                     'min_hr': 0, 'max_hr': 0})

                continue

            i += 1

        classification = {
            'single': singles,
            'pair': pairs,
            'run': runs,
            'bigeminy_count': bigeminy_count,
            'trigeminy_count': trigeminy_count
        }
        pvc_indices_list = [int(idx) for idx in pvc_indices]

    except Exception as e:
        Logger().error(f"detect_classify_pvc函数出错: {str(e)}\n{traceback.format_exc()}")
        return {'pvc_indices': [],
                'classification': {'single': 0, 'pair': 0, 'run': [], 'bigeminy_count': 0, 'trigeminy_count': 0}}

    return {'pvc_indices': pvc_indices_list, 'classification': classification}


def quantify_pvc(pvc_classification, total_beats):
    """量化室性早搏指标"""
    classification = pvc_classification.get('classification', {})
    runs = classification.get('run', [])

    if total_beats is None: total_beats = 0

    try:
        singles = classification.get('single', 0)
        pairs = classification.get('pair', 0)
        run_beats = sum(r.get('count', 0) for r in runs)

        total_pvc_count = singles + (pairs * 2) + run_beats

        percentage = (total_pvc_count / total_beats) * 100 if total_beats > 0 else 0

        max_consecutive_pvc = 0
        if runs:
            counts = [r.get('count', 0) for r in runs]
            if counts: max_consecutive_pvc = max(counts)
        if pairs > 0 and max_consecutive_pvc < 2:
            max_consecutive_pvc = 2
        if singles > 0 and max_consecutive_pvc < 1:
            max_consecutive_pvc = 1

        fastest_vt_hr = None
        slowest_vt_hr = None
        if runs:
            valid_max_hrs = [r.get('max_hr') for r in runs if r.get('max_hr', 0) > 0]
            if valid_max_hrs: fastest_vt_hr = max(valid_max_hrs)

            valid_min_hrs = [r.get('min_hr') for r in runs if r.get('min_hr', 0) > 0]
            if valid_min_hrs: slowest_vt_hr = min(valid_min_hrs)

        counts_by_type = {k: int(v) if isinstance(v, (int, np.integer)) else v for k, v in classification.items()}
        clean_runs = []
        for r in runs:
            clean_run = {k: int(v) if isinstance(v, (int, np.integer)) else v for k, v in r.items()}
            clean_runs.append(clean_run)
        counts_by_type['run'] = clean_runs

        return {
            'total_pvc_count': int(total_pvc_count),
            'percentage': percentage,
            'max_consecutive_pvc': int(max_consecutive_pvc),
            'fastest_vt_hr': fastest_vt_hr,
            'slowest_vt_hr': slowest_vt_hr,
            'counts_by_type': counts_by_type
        }
    except Exception as e:
        Logger().error(f"quantify_pvc函数出错: {str(e)}\n{traceback.format_exc()}")
        return {'total_pvc_count': 0, 'percentage': 0, 'max_consecutive_pvc': 0, 'fastest_vt_hr': None,
                'slowest_vt_hr': None, 'counts_by_type': {}}