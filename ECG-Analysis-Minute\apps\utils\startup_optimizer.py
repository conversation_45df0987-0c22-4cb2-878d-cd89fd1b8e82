"""
启动优化器 - 在Django启动时初始化所有优化组件
"""
import os
import gc
import warnings
from apps.utils.logger_helper import Logger
from apps.utils.memory_monitor import GPUManager, start_memory_monitoring
from apps.utils.model_manager import get_model_manager
from apps.utils.request_queue import get_request_queue

# 禁用各种警告
warnings.filterwarnings("ignore", category=UserWarning, module="sklearn")
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)

# 设置环境变量
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 禁用TensorFlow日志
os.environ['PYTHONWARNINGS'] = 'ignore'   # 禁用Python警告


def initialize_optimizations():
    """初始化所有优化组件 - 延迟初始化版本"""
    try:
        Logger().info("Starting ECG Analysis optimization initialization (lazy mode)...")

        # 只进行最基本的初始化，其他组件在需要时才初始化
        Logger().info("Basic initialization completed - components will be loaded on demand")

        return True

    except Exception as e:
        Logger().error(f"Optimization initialization failed: {e}")
        return False

def initialize_on_demand():
    """按需初始化优化组件"""
    try:
        Logger().info("Initializing optimization components on demand...")

        # 1. 配置GPU
        Logger().info("Configuring GPU settings...")
        gpu_configured = GPUManager.configure_gpu()
        if gpu_configured:
            Logger().info("GPU configuration completed successfully")
        else:
            Logger().info("No GPU available or configuration failed, using CPU")

        # 2. 初始化模型管理器
        Logger().info("Initializing model manager...")
        model_manager = get_model_manager()
        cache_info = model_manager.get_cache_info()
        Logger().info(f"Model manager initialized: {cache_info}")

        # 3. 启动内存监控
        Logger().info("Starting memory monitoring...")
        start_memory_monitoring(interval=15.0)  # 每15秒检查一次

        # 4. 初始化请求队列
        Logger().info("Initializing request queue...")
        request_queue = get_request_queue()
        queue_status = request_queue.get_status()
        Logger().info(f"Request queue initialized: {queue_status}")

        # 5. 执行初始内存清理
        Logger().info("Performing initial memory cleanup...")
        for _ in range(3):
            gc.collect()

        Logger().info("On-demand optimization initialization completed successfully!")

        return True

    except Exception as e:
        Logger().error(f"On-demand optimization initialization failed: {e}")
        return False


def get_system_status():
    """获取系统状态信息"""
    try:
        from apps.utils.memory_monitor import get_memory_monitor
        
        # 内存信息
        memory_monitor = get_memory_monitor()
        memory_info = memory_monitor.get_memory_info()
        
        # GPU信息
        gpu_info = GPUManager.get_gpu_info()
        
        # 模型缓存信息
        model_manager = get_model_manager()
        cache_info = model_manager.get_cache_info()
        
        # 请求队列信息
        request_queue = get_request_queue()
        queue_status = request_queue.get_status()
        
        return {
            'memory': memory_info,
            'gpu': gpu_info,
            'model_cache': cache_info,
            'request_queue': queue_status,
            'status': 'healthy'
        }
        
    except Exception as e:
        Logger().error(f"Failed to get system status: {e}")
        return {
            'status': 'error',
            'error': str(e)
        }


def cleanup_on_shutdown():
    """关闭时的清理工作"""
    try:
        Logger().info("Starting shutdown cleanup...")
        
        # 停止内存监控
        from apps.utils.memory_monitor import stop_memory_monitoring
        stop_memory_monitoring()
        
        # 关闭请求队列
        request_queue = get_request_queue()
        request_queue.shutdown()
        
        # 清理模型缓存
        model_manager = get_model_manager()
        model_manager.clear_all_cache()
        
        # 最终内存清理
        for _ in range(5):
            gc.collect()
        
        Logger().info("Shutdown cleanup completed")
        
    except Exception as e:
        Logger().error(f"Shutdown cleanup failed: {e}")


# Django应用启动时自动初始化
def ready():
    """Django应用就绪时调用"""
    initialize_optimizations()


# 注册关闭时的清理函数
import atexit
atexit.register(cleanup_on_shutdown)
