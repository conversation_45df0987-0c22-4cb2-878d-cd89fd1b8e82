{{- if .Values.ingress.enabled -}}
{{- $fullName := include "cvat.fullname" . -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cvat.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
{{- if .Values.ingress.className }}
  ingressClassName: {{ .Values.ingress.className | toYaml }}
{{- else if and .Values.traefik.enabled .Values.traefik.ingressClass.enabled }}
  ingressClassName: {{ .Values.traefik.ingressClass.name | default (include "traefik.fullname" .Subcharts.traefik) }}
{{- end }}
  {{- if .Values.ingress.tls }}
  tls:
  - hosts:
    - {{ .Values.ingress.hostname }}
    secretName: {{ .Values.ingress.tlsSecretName }}
  {{- end }}
  rules:
  - host: {{ .Values.ingress.hostname }}
    http:
      paths:
      {{- range tuple "api" "admin" "static" "django-rq" "profiler" }}
      - path: "/{{ . }}"
        pathType: Prefix
        backend:
          service:
            name: {{ $.Release.Name }}-backend-service
            port:
              number: 8080
      {{- end }}
      - path: /
        pathType: Prefix
        backend:
          service:
            name: {{ .Release.Name }}-frontend-service
            port:
              number: 80
{{- end }}
