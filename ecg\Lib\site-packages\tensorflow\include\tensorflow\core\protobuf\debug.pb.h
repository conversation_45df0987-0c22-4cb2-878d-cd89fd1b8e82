// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/debug.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto;
namespace tensorflow {
class DebugOptions;
class DebugOptionsDefaultTypeInternal;
extern DebugOptionsDefaultTypeInternal _DebugOptions_default_instance_;
class DebugTensorWatch;
class DebugTensorWatchDefaultTypeInternal;
extern DebugTensorWatchDefaultTypeInternal _DebugTensorWatch_default_instance_;
class DebuggedSourceFile;
class DebuggedSourceFileDefaultTypeInternal;
extern DebuggedSourceFileDefaultTypeInternal _DebuggedSourceFile_default_instance_;
class DebuggedSourceFiles;
class DebuggedSourceFilesDefaultTypeInternal;
extern DebuggedSourceFilesDefaultTypeInternal _DebuggedSourceFiles_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::DebugOptions* Arena::CreateMaybeMessage<::tensorflow::DebugOptions>(Arena*);
template<> ::tensorflow::DebugTensorWatch* Arena::CreateMaybeMessage<::tensorflow::DebugTensorWatch>(Arena*);
template<> ::tensorflow::DebuggedSourceFile* Arena::CreateMaybeMessage<::tensorflow::DebuggedSourceFile>(Arena*);
template<> ::tensorflow::DebuggedSourceFiles* Arena::CreateMaybeMessage<::tensorflow::DebuggedSourceFiles>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class DebugTensorWatch :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebugTensorWatch) */ {
 public:
  DebugTensorWatch();
  virtual ~DebugTensorWatch();

  DebugTensorWatch(const DebugTensorWatch& from);
  DebugTensorWatch(DebugTensorWatch&& from) noexcept
    : DebugTensorWatch() {
    *this = ::std::move(from);
  }

  inline DebugTensorWatch& operator=(const DebugTensorWatch& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebugTensorWatch& operator=(DebugTensorWatch&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DebugTensorWatch& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebugTensorWatch* internal_default_instance() {
    return reinterpret_cast<const DebugTensorWatch*>(
               &_DebugTensorWatch_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DebugTensorWatch& a, DebugTensorWatch& b) {
    a.Swap(&b);
  }
  inline void Swap(DebugTensorWatch* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebugTensorWatch* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DebugTensorWatch* New() const final {
    return CreateMaybeMessage<DebugTensorWatch>(nullptr);
  }

  DebugTensorWatch* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DebugTensorWatch>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DebugTensorWatch& from);
  void MergeFrom(const DebugTensorWatch& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugTensorWatch* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebugTensorWatch";
  }
  protected:
  explicit DebugTensorWatch(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDebugOpsFieldNumber = 3,
    kDebugUrlsFieldNumber = 4,
    kNodeNameFieldNumber = 1,
    kOutputSlotFieldNumber = 2,
    kTolerateDebugOpCreationFailuresFieldNumber = 5,
  };
  // repeated string debug_ops = 3;
  int debug_ops_size() const;
  void clear_debug_ops();
  const std::string& debug_ops(int index) const;
  std::string* mutable_debug_ops(int index);
  void set_debug_ops(int index, const std::string& value);
  void set_debug_ops(int index, std::string&& value);
  void set_debug_ops(int index, const char* value);
  void set_debug_ops(int index, const char* value, size_t size);
  std::string* add_debug_ops();
  void add_debug_ops(const std::string& value);
  void add_debug_ops(std::string&& value);
  void add_debug_ops(const char* value);
  void add_debug_ops(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& debug_ops() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_debug_ops();

  // repeated string debug_urls = 4;
  int debug_urls_size() const;
  void clear_debug_urls();
  const std::string& debug_urls(int index) const;
  std::string* mutable_debug_urls(int index);
  void set_debug_urls(int index, const std::string& value);
  void set_debug_urls(int index, std::string&& value);
  void set_debug_urls(int index, const char* value);
  void set_debug_urls(int index, const char* value, size_t size);
  std::string* add_debug_urls();
  void add_debug_urls(const std::string& value);
  void add_debug_urls(std::string&& value);
  void add_debug_urls(const char* value);
  void add_debug_urls(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& debug_urls() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_debug_urls();

  // string node_name = 1;
  void clear_node_name();
  const std::string& node_name() const;
  void set_node_name(const std::string& value);
  void set_node_name(std::string&& value);
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  std::string* mutable_node_name();
  std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_node_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node_name(
      std::string* node_name);

  // int32 output_slot = 2;
  void clear_output_slot();
  ::PROTOBUF_NAMESPACE_ID::int32 output_slot() const;
  void set_output_slot(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool tolerate_debug_op_creation_failures = 5;
  void clear_tolerate_debug_op_creation_failures();
  bool tolerate_debug_op_creation_failures() const;
  void set_tolerate_debug_op_creation_failures(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.DebugTensorWatch)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> debug_ops_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> debug_urls_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
  ::PROTOBUF_NAMESPACE_ID::int32 output_slot_;
  bool tolerate_debug_op_creation_failures_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto;
};
// -------------------------------------------------------------------

class DebugOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebugOptions) */ {
 public:
  DebugOptions();
  virtual ~DebugOptions();

  DebugOptions(const DebugOptions& from);
  DebugOptions(DebugOptions&& from) noexcept
    : DebugOptions() {
    *this = ::std::move(from);
  }

  inline DebugOptions& operator=(const DebugOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebugOptions& operator=(DebugOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DebugOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebugOptions* internal_default_instance() {
    return reinterpret_cast<const DebugOptions*>(
               &_DebugOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DebugOptions& a, DebugOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(DebugOptions* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebugOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DebugOptions* New() const final {
    return CreateMaybeMessage<DebugOptions>(nullptr);
  }

  DebugOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DebugOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DebugOptions& from);
  void MergeFrom(const DebugOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebugOptions";
  }
  protected:
  explicit DebugOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDebugTensorWatchOptsFieldNumber = 4,
    kResetDiskByteUsageFieldNumber = 11,
    kGlobalStepFieldNumber = 10,
  };
  // repeated .tensorflow.DebugTensorWatch debug_tensor_watch_opts = 4;
  int debug_tensor_watch_opts_size() const;
  void clear_debug_tensor_watch_opts();
  ::tensorflow::DebugTensorWatch* mutable_debug_tensor_watch_opts(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebugTensorWatch >*
      mutable_debug_tensor_watch_opts();
  const ::tensorflow::DebugTensorWatch& debug_tensor_watch_opts(int index) const;
  ::tensorflow::DebugTensorWatch* add_debug_tensor_watch_opts();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebugTensorWatch >&
      debug_tensor_watch_opts() const;

  // bool reset_disk_byte_usage = 11;
  void clear_reset_disk_byte_usage();
  bool reset_disk_byte_usage() const;
  void set_reset_disk_byte_usage(bool value);

  // int64 global_step = 10;
  void clear_global_step();
  ::PROTOBUF_NAMESPACE_ID::int64 global_step() const;
  void set_global_step(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DebugOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebugTensorWatch > debug_tensor_watch_opts_;
  bool reset_disk_byte_usage_;
  ::PROTOBUF_NAMESPACE_ID::int64 global_step_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto;
};
// -------------------------------------------------------------------

class DebuggedSourceFile :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebuggedSourceFile) */ {
 public:
  DebuggedSourceFile();
  virtual ~DebuggedSourceFile();

  DebuggedSourceFile(const DebuggedSourceFile& from);
  DebuggedSourceFile(DebuggedSourceFile&& from) noexcept
    : DebuggedSourceFile() {
    *this = ::std::move(from);
  }

  inline DebuggedSourceFile& operator=(const DebuggedSourceFile& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggedSourceFile& operator=(DebuggedSourceFile&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DebuggedSourceFile& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebuggedSourceFile* internal_default_instance() {
    return reinterpret_cast<const DebuggedSourceFile*>(
               &_DebuggedSourceFile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DebuggedSourceFile& a, DebuggedSourceFile& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggedSourceFile* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebuggedSourceFile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DebuggedSourceFile* New() const final {
    return CreateMaybeMessage<DebuggedSourceFile>(nullptr);
  }

  DebuggedSourceFile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DebuggedSourceFile>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DebuggedSourceFile& from);
  void MergeFrom(const DebuggedSourceFile& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggedSourceFile* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebuggedSourceFile";
  }
  protected:
  explicit DebuggedSourceFile(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLinesFieldNumber = 5,
    kHostFieldNumber = 1,
    kFilePathFieldNumber = 2,
    kLastModifiedFieldNumber = 3,
    kBytesFieldNumber = 4,
  };
  // repeated string lines = 5;
  int lines_size() const;
  void clear_lines();
  const std::string& lines(int index) const;
  std::string* mutable_lines(int index);
  void set_lines(int index, const std::string& value);
  void set_lines(int index, std::string&& value);
  void set_lines(int index, const char* value);
  void set_lines(int index, const char* value, size_t size);
  std::string* add_lines();
  void add_lines(const std::string& value);
  void add_lines(std::string&& value);
  void add_lines(const char* value);
  void add_lines(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& lines() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_lines();

  // string host = 1;
  void clear_host();
  const std::string& host() const;
  void set_host(const std::string& value);
  void set_host(std::string&& value);
  void set_host(const char* value);
  void set_host(const char* value, size_t size);
  std::string* mutable_host();
  std::string* release_host();
  void set_allocated_host(std::string* host);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_host();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_host(
      std::string* host);

  // string file_path = 2;
  void clear_file_path();
  const std::string& file_path() const;
  void set_file_path(const std::string& value);
  void set_file_path(std::string&& value);
  void set_file_path(const char* value);
  void set_file_path(const char* value, size_t size);
  std::string* mutable_file_path();
  std::string* release_file_path();
  void set_allocated_file_path(std::string* file_path);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_file_path();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_file_path(
      std::string* file_path);

  // int64 last_modified = 3;
  void clear_last_modified();
  ::PROTOBUF_NAMESPACE_ID::int64 last_modified() const;
  void set_last_modified(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 bytes = 4;
  void clear_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 bytes() const;
  void set_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DebuggedSourceFile)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> lines_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_path_;
  ::PROTOBUF_NAMESPACE_ID::int64 last_modified_;
  ::PROTOBUF_NAMESPACE_ID::int64 bytes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto;
};
// -------------------------------------------------------------------

class DebuggedSourceFiles :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebuggedSourceFiles) */ {
 public:
  DebuggedSourceFiles();
  virtual ~DebuggedSourceFiles();

  DebuggedSourceFiles(const DebuggedSourceFiles& from);
  DebuggedSourceFiles(DebuggedSourceFiles&& from) noexcept
    : DebuggedSourceFiles() {
    *this = ::std::move(from);
  }

  inline DebuggedSourceFiles& operator=(const DebuggedSourceFiles& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggedSourceFiles& operator=(DebuggedSourceFiles&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DebuggedSourceFiles& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebuggedSourceFiles* internal_default_instance() {
    return reinterpret_cast<const DebuggedSourceFiles*>(
               &_DebuggedSourceFiles_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DebuggedSourceFiles& a, DebuggedSourceFiles& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggedSourceFiles* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebuggedSourceFiles* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DebuggedSourceFiles* New() const final {
    return CreateMaybeMessage<DebuggedSourceFiles>(nullptr);
  }

  DebuggedSourceFiles* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DebuggedSourceFiles>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DebuggedSourceFiles& from);
  void MergeFrom(const DebuggedSourceFiles& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggedSourceFiles* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebuggedSourceFiles";
  }
  protected:
  explicit DebuggedSourceFiles(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceFilesFieldNumber = 1,
  };
  // repeated .tensorflow.DebuggedSourceFile source_files = 1;
  int source_files_size() const;
  void clear_source_files();
  ::tensorflow::DebuggedSourceFile* mutable_source_files(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >*
      mutable_source_files();
  const ::tensorflow::DebuggedSourceFile& source_files(int index) const;
  ::tensorflow::DebuggedSourceFile* add_source_files();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >&
      source_files() const;

  // @@protoc_insertion_point(class_scope:tensorflow.DebuggedSourceFiles)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebuggedSourceFile > source_files_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DebugTensorWatch

// string node_name = 1;
inline void DebugTensorWatch::clear_node_name() {
  node_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebugTensorWatch::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.node_name)
  return node_name_.Get();
}
inline void DebugTensorWatch::set_node_name(const std::string& value) {
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.node_name)
}
inline void DebugTensorWatch::set_node_name(std::string&& value) {
  
  node_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebugTensorWatch.node_name)
}
inline void DebugTensorWatch::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugTensorWatch.node_name)
}
inline void DebugTensorWatch::set_node_name(const char* value,
    size_t size) {
  
  node_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugTensorWatch.node_name)
}
inline std::string* DebugTensorWatch::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugTensorWatch.node_name)
  return node_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebugTensorWatch::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugTensorWatch.node_name)
  
  return node_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebugTensorWatch::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  node_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), node_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugTensorWatch.node_name)
}
inline std::string* DebugTensorWatch::unsafe_arena_release_node_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugTensorWatch.node_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return node_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebugTensorWatch::unsafe_arena_set_allocated_node_name(
    std::string* node_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (node_name != nullptr) {
    
  } else {
    
  }
  node_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      node_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugTensorWatch.node_name)
}

// int32 output_slot = 2;
inline void DebugTensorWatch::clear_output_slot() {
  output_slot_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebugTensorWatch::output_slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.output_slot)
  return output_slot_;
}
inline void DebugTensorWatch::set_output_slot(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  output_slot_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.output_slot)
}

// repeated string debug_ops = 3;
inline int DebugTensorWatch::debug_ops_size() const {
  return debug_ops_.size();
}
inline void DebugTensorWatch::clear_debug_ops() {
  debug_ops_.Clear();
}
inline const std::string& DebugTensorWatch::debug_ops(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.debug_ops)
  return debug_ops_.Get(index);
}
inline std::string* DebugTensorWatch::mutable_debug_ops(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugTensorWatch.debug_ops)
  return debug_ops_.Mutable(index);
}
inline void DebugTensorWatch::set_debug_ops(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_ops)
  debug_ops_.Mutable(index)->assign(value);
}
inline void DebugTensorWatch::set_debug_ops(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_ops)
  debug_ops_.Mutable(index)->assign(std::move(value));
}
inline void DebugTensorWatch::set_debug_ops(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  debug_ops_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::set_debug_ops(int index, const char* value, size_t size) {
  debug_ops_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugTensorWatch.debug_ops)
}
inline std::string* DebugTensorWatch::add_debug_ops() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebugTensorWatch.debug_ops)
  return debug_ops_.Add();
}
inline void DebugTensorWatch::add_debug_ops(const std::string& value) {
  debug_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::add_debug_ops(std::string&& value) {
  debug_ops_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::add_debug_ops(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  debug_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::add_debug_ops(const char* value, size_t size) {
  debug_ops_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebugTensorWatch.debug_ops)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebugTensorWatch::debug_ops() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebugTensorWatch.debug_ops)
  return debug_ops_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebugTensorWatch::mutable_debug_ops() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebugTensorWatch.debug_ops)
  return &debug_ops_;
}

// repeated string debug_urls = 4;
inline int DebugTensorWatch::debug_urls_size() const {
  return debug_urls_.size();
}
inline void DebugTensorWatch::clear_debug_urls() {
  debug_urls_.Clear();
}
inline const std::string& DebugTensorWatch::debug_urls(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.debug_urls)
  return debug_urls_.Get(index);
}
inline std::string* DebugTensorWatch::mutable_debug_urls(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugTensorWatch.debug_urls)
  return debug_urls_.Mutable(index);
}
inline void DebugTensorWatch::set_debug_urls(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_urls)
  debug_urls_.Mutable(index)->assign(value);
}
inline void DebugTensorWatch::set_debug_urls(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_urls)
  debug_urls_.Mutable(index)->assign(std::move(value));
}
inline void DebugTensorWatch::set_debug_urls(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  debug_urls_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::set_debug_urls(int index, const char* value, size_t size) {
  debug_urls_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugTensorWatch.debug_urls)
}
inline std::string* DebugTensorWatch::add_debug_urls() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebugTensorWatch.debug_urls)
  return debug_urls_.Add();
}
inline void DebugTensorWatch::add_debug_urls(const std::string& value) {
  debug_urls_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::add_debug_urls(std::string&& value) {
  debug_urls_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::add_debug_urls(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  debug_urls_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::add_debug_urls(const char* value, size_t size) {
  debug_urls_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebugTensorWatch.debug_urls)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebugTensorWatch::debug_urls() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebugTensorWatch.debug_urls)
  return debug_urls_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebugTensorWatch::mutable_debug_urls() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebugTensorWatch.debug_urls)
  return &debug_urls_;
}

// bool tolerate_debug_op_creation_failures = 5;
inline void DebugTensorWatch::clear_tolerate_debug_op_creation_failures() {
  tolerate_debug_op_creation_failures_ = false;
}
inline bool DebugTensorWatch::tolerate_debug_op_creation_failures() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.tolerate_debug_op_creation_failures)
  return tolerate_debug_op_creation_failures_;
}
inline void DebugTensorWatch::set_tolerate_debug_op_creation_failures(bool value) {
  
  tolerate_debug_op_creation_failures_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.tolerate_debug_op_creation_failures)
}

// -------------------------------------------------------------------

// DebugOptions

// repeated .tensorflow.DebugTensorWatch debug_tensor_watch_opts = 4;
inline int DebugOptions::debug_tensor_watch_opts_size() const {
  return debug_tensor_watch_opts_.size();
}
inline void DebugOptions::clear_debug_tensor_watch_opts() {
  debug_tensor_watch_opts_.Clear();
}
inline ::tensorflow::DebugTensorWatch* DebugOptions::mutable_debug_tensor_watch_opts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return debug_tensor_watch_opts_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebugTensorWatch >*
DebugOptions::mutable_debug_tensor_watch_opts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return &debug_tensor_watch_opts_;
}
inline const ::tensorflow::DebugTensorWatch& DebugOptions::debug_tensor_watch_opts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return debug_tensor_watch_opts_.Get(index);
}
inline ::tensorflow::DebugTensorWatch* DebugOptions::add_debug_tensor_watch_opts() {
  // @@protoc_insertion_point(field_add:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return debug_tensor_watch_opts_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebugTensorWatch >&
DebugOptions::debug_tensor_watch_opts() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return debug_tensor_watch_opts_;
}

// int64 global_step = 10;
inline void DebugOptions::clear_global_step() {
  global_step_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DebugOptions::global_step() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugOptions.global_step)
  return global_step_;
}
inline void DebugOptions::set_global_step(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  global_step_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebugOptions.global_step)
}

// bool reset_disk_byte_usage = 11;
inline void DebugOptions::clear_reset_disk_byte_usage() {
  reset_disk_byte_usage_ = false;
}
inline bool DebugOptions::reset_disk_byte_usage() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugOptions.reset_disk_byte_usage)
  return reset_disk_byte_usage_;
}
inline void DebugOptions::set_reset_disk_byte_usage(bool value) {
  
  reset_disk_byte_usage_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebugOptions.reset_disk_byte_usage)
}

// -------------------------------------------------------------------

// DebuggedSourceFile

// string host = 1;
inline void DebuggedSourceFile::clear_host() {
  host_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebuggedSourceFile::host() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.host)
  return host_.Get();
}
inline void DebuggedSourceFile::set_host(const std::string& value) {
  
  host_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.host)
}
inline void DebuggedSourceFile::set_host(std::string&& value) {
  
  host_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebuggedSourceFile.host)
}
inline void DebuggedSourceFile::set_host(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  host_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedSourceFile.host)
}
inline void DebuggedSourceFile::set_host(const char* value,
    size_t size) {
  
  host_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedSourceFile.host)
}
inline std::string* DebuggedSourceFile::mutable_host() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFile.host)
  return host_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebuggedSourceFile::release_host() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedSourceFile.host)
  
  return host_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebuggedSourceFile::set_allocated_host(std::string* host) {
  if (host != nullptr) {
    
  } else {
    
  }
  host_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), host,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedSourceFile.host)
}
inline std::string* DebuggedSourceFile::unsafe_arena_release_host() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebuggedSourceFile.host)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return host_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebuggedSourceFile::unsafe_arena_set_allocated_host(
    std::string* host) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (host != nullptr) {
    
  } else {
    
  }
  host_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      host, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebuggedSourceFile.host)
}

// string file_path = 2;
inline void DebuggedSourceFile::clear_file_path() {
  file_path_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DebuggedSourceFile::file_path() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.file_path)
  return file_path_.Get();
}
inline void DebuggedSourceFile::set_file_path(const std::string& value) {
  
  file_path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.file_path)
}
inline void DebuggedSourceFile::set_file_path(std::string&& value) {
  
  file_path_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebuggedSourceFile.file_path)
}
inline void DebuggedSourceFile::set_file_path(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  file_path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedSourceFile.file_path)
}
inline void DebuggedSourceFile::set_file_path(const char* value,
    size_t size) {
  
  file_path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedSourceFile.file_path)
}
inline std::string* DebuggedSourceFile::mutable_file_path() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFile.file_path)
  return file_path_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DebuggedSourceFile::release_file_path() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedSourceFile.file_path)
  
  return file_path_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebuggedSourceFile::set_allocated_file_path(std::string* file_path) {
  if (file_path != nullptr) {
    
  } else {
    
  }
  file_path_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), file_path,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedSourceFile.file_path)
}
inline std::string* DebuggedSourceFile::unsafe_arena_release_file_path() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebuggedSourceFile.file_path)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return file_path_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebuggedSourceFile::unsafe_arena_set_allocated_file_path(
    std::string* file_path) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (file_path != nullptr) {
    
  } else {
    
  }
  file_path_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      file_path, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebuggedSourceFile.file_path)
}

// int64 last_modified = 3;
inline void DebuggedSourceFile::clear_last_modified() {
  last_modified_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DebuggedSourceFile::last_modified() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.last_modified)
  return last_modified_;
}
inline void DebuggedSourceFile::set_last_modified(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  last_modified_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.last_modified)
}

// int64 bytes = 4;
inline void DebuggedSourceFile::clear_bytes() {
  bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 DebuggedSourceFile::bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.bytes)
  return bytes_;
}
inline void DebuggedSourceFile::set_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.bytes)
}

// repeated string lines = 5;
inline int DebuggedSourceFile::lines_size() const {
  return lines_.size();
}
inline void DebuggedSourceFile::clear_lines() {
  lines_.Clear();
}
inline const std::string& DebuggedSourceFile::lines(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.lines)
  return lines_.Get(index);
}
inline std::string* DebuggedSourceFile::mutable_lines(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFile.lines)
  return lines_.Mutable(index);
}
inline void DebuggedSourceFile::set_lines(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.lines)
  lines_.Mutable(index)->assign(value);
}
inline void DebuggedSourceFile::set_lines(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.lines)
  lines_.Mutable(index)->assign(std::move(value));
}
inline void DebuggedSourceFile::set_lines(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  lines_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::set_lines(int index, const char* value, size_t size) {
  lines_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedSourceFile.lines)
}
inline std::string* DebuggedSourceFile::add_lines() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebuggedSourceFile.lines)
  return lines_.Add();
}
inline void DebuggedSourceFile::add_lines(const std::string& value) {
  lines_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::add_lines(std::string&& value) {
  lines_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::add_lines(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  lines_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::add_lines(const char* value, size_t size) {
  lines_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebuggedSourceFile.lines)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebuggedSourceFile::lines() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebuggedSourceFile.lines)
  return lines_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebuggedSourceFile::mutable_lines() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebuggedSourceFile.lines)
  return &lines_;
}

// -------------------------------------------------------------------

// DebuggedSourceFiles

// repeated .tensorflow.DebuggedSourceFile source_files = 1;
inline int DebuggedSourceFiles::source_files_size() const {
  return source_files_.size();
}
inline void DebuggedSourceFiles::clear_source_files() {
  source_files_.Clear();
}
inline ::tensorflow::DebuggedSourceFile* DebuggedSourceFiles::mutable_source_files(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFiles.source_files)
  return source_files_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >*
DebuggedSourceFiles::mutable_source_files() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebuggedSourceFiles.source_files)
  return &source_files_;
}
inline const ::tensorflow::DebuggedSourceFile& DebuggedSourceFiles::source_files(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFiles.source_files)
  return source_files_.Get(index);
}
inline ::tensorflow::DebuggedSourceFile* DebuggedSourceFiles::add_source_files() {
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedSourceFiles.source_files)
  return source_files_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >&
DebuggedSourceFiles::source_files() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebuggedSourceFiles.source_files)
  return source_files_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
