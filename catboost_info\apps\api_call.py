import requests

import json

import pandas as pd

import numpy as np

import os

import argparse
import time
from datetime import datetime
from scipy.signal import butter, filtfilt, find_peaks, welch
import pywt
import traceback
import matplotlib.pyplot as plt



def read_data(file_path):
    """
    读取ECG数据文件

    参数:
        file_path: 文件路径

    返回:
    ecg_data: 读取的ECG数据，如失败则返回None
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在: {file_path}")
            return None
            
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            print(f"错误: 文件为空: {file_path}")
            return None
            
        print(f"读取文件: {file_path} (大小: {file_size/1024:.2f} KB)")
        
        # 根据文件扩展名确定读取方式
        file_ext = os.path.splitext(file_path)[1].lower()
        
        data = None
        
        # 读取文件内容
        with open(file_path, 'r') as f:
            content = f.read().strip()
            
        # 检查内容是否为空
        if not content:
            print("警告: 文件内容为空")
            return None
            
        # 尝试解析方括号格式的数据
        if content.startswith('[') and content.endswith(']'):
            try:
                # 移除方括号并分割数据
                content = content[1:-1]
                # 分割并转换为浮点数
                data = [float(x.strip()) for x in content.split(',') if x.strip()]
                if data:
                    print(f"成功解析方括号格式数据，共{len(data)}个点")
                    return np.array(data)
            except Exception as e:
                print(f"方括号格式解析失败: {str(e)}")
                
        # 如果方括号格式解析失败，尝试按行解析
        if data is None:
            # CSV/TXT文件处理 - 特别处理按行组织的ECG数据
            lines = content.split('\n')
            data = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                # 尝试将整行转换为一个浮点数
                try:
                    data.append(float(line))
                    continue
                except ValueError:
                    pass
                    
                # 如果整行转换失败，尝试拆分行并取第一个值
                try:
                    # 尝试用各种分隔符拆分
                    for sep in [',', ';', '\t', ' ']:
                        if sep in line:
                            value = line.split(sep)[0].strip()
                            if value:
                                data.append(float(value))
                                break
                except Exception:
                    # 如果仍然失败，跳过这一行
                    continue
            
            if data:
                print(f"按行读取ECG数据，共{len(data)}个点")
                
        # 如果上面的方法失败，尝试pandas读取
        if data is None or len(data) <= 1:
            try:
                print("尝试使用pandas读取数据...")
                
                # 使用pandas读取CSV，尝试不同的参数
                df = None
                
                # 尝试方法1：按行读取，无表头
                try:
                    df = pd.read_csv(file_path, header=None)
                    if len(df) > 1 and len(df.columns) == 1:
                        # 如果只有一列且多行，直接使用这一列的所有值
                        data = df[0].values
                        print(f"使用pandas按行读取，共{len(data)}个点")
                except Exception as e:
                    print(f"pandas方法1读取失败: {str(e)}")
                
                # 尝试方法2：按行读取，有表头
                if data is None or len(data) <= 1:
                    try:
                        df = pd.read_csv(file_path)
                        if len(df) > 0 and len(df.columns) > 0:
                            # 使用第一列数据
                            data = df.iloc[:, 0].values
                            print(f"使用pandas按行读取(有表头)，共{len(data)}个点")
                    except Exception as e:
                        print(f"pandas方法2读取失败: {str(e)}")
                
                # 尝试方法3：转置数据，将行视为列
                if data is None or len(data) <= 1:
                    try:
                        # 如果所有这些方法都失败，尝试读取第一行作为数据
                        df = pd.read_csv(file_path, header=None, nrows=1)
                        if len(df.columns) > 1:
                            # 如果第一行有多个值，使用它们
                            data = df.iloc[0].values
                            print(f"使用pandas读取第一行，共{len(data)}个点")
                    except Exception as e:
                        print(f"pandas方法3读取失败: {str(e)}")
            except Exception as e:
                print(f"所有pandas读取方法都失败: {str(e)}")
        
        # 确保数据不为空
        if data is None or len(data) <= 1:
            print("警告: 未能读取到足够的ECG数据点，将使用默认数据")
            # 创建一个默认的正弦波ECG信号
            t = np.linspace(0, 2, 1000)  # 2秒的数据
            data = 0.5 * np.sin(2 * np.pi * 1 * t) + 0.5 * np.sin(2 * np.pi * 2 * t)  # 简单的混合正弦波
        
        # 转换为numpy数组并清理
        data = np.array(data)
        
        # 替换NaN和Inf
        if np.isnan(data).any() or np.isinf(data).any():
            print("警告: 数据包含NaN或Inf值，将被替换为0")
            data = np.nan_to_num(data)
        
        # 显示数据基本信息
        print(f"数据统计: 最小值={np.min(data):.2f}, 最大值={np.max(data):.2f}, 平均值={np.mean(data):.2f}, 标准差={np.std(data):.2f}")

        return data

    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        traceback.print_exc()
        
        # 出错时返回默认数据
        print("生成默认的测试数据...")
        t = np.linspace(0, 2, 1000)  # 2秒的数据
        return 0.5 * np.sin(2 * np.pi * 1 * t) + 0.5 * np.sin(2 * np.pi * 2 * t)  # 简单的混合正弦波



# 直接在这个文件中定义ECG处理的核心功能
class SimpleECGProcessor:
    def __init__(self, ecg_data, sampling_rate=500):
        """初始化ECG处理器"""
        # 确保ecg_data是numpy数组且不为空
        if not isinstance(ecg_data, np.ndarray):
            ecg_data = np.array(ecg_data)
            
        # 检查数据是否为空或只有一个点
        if len(ecg_data) <= 1:
            print("警告: ECG数据为空或只有一个数据点，将使用默认数据")
            # 创建一个默认的正弦波作为替代
            t = np.linspace(0, 2*np.pi, 1000)
            ecg_data = np.sin(t)
            
        self.ecg_data = ecg_data
        self.sampling_rate = sampling_rate
        
    def detect_noise_types(self):
        """检测ECG信号中的噪声类型"""
        noise_types = []
        
        try:
            # 基线漂移检测
            if self._has_baseline_wander():
                noise_types.append("基线漂移")
        except Exception as e:
            print(f"基线漂移检测失败: {str(e)}")
            
        try:
            # 工频干扰检测
            if self._has_powerline_interference():
                noise_types.append("工频干扰")
        except Exception as e:
            print(f"工频干扰检测失败: {str(e)}")
            
        try:
            # 肌电噪声检测
            if self._has_muscle_artifact():
                noise_types.append("肌电噪声")
        except Exception as e:
            print(f"肌电噪声检测失败: {str(e)}")
            
        try:
            # 电极接触不良检测
            if self._has_electrode_artifact():
                noise_types.append("电极接触不良")
        except Exception as e:
            print(f"电极接触不良检测失败: {str(e)}")
            
        return noise_types
    
    def _has_baseline_wander(self):
        """检测基线漂移"""
        try:
            # 确保数据长度足够
            nperseg = min(len(self.ecg_data), 1024)
            if nperseg < 10:
                return False
                
            # 低频功率比例计算
            f, Pxx = welch(self.ecg_data, fs=self.sampling_rate, nperseg=nperseg)
            
            # 确保f和Pxx不为空
            if len(f) == 0 or len(Pxx) == 0:
                return False
                
            # 找到低频部分
            low_freq_indices = np.where(f < 0.5)[0]
            if len(low_freq_indices) == 0:
                return False
                
            low_freq_power = np.sum(Pxx[low_freq_indices])
            total_power = np.sum(Pxx)
            
            # 避免除以零
            if total_power == 0:
                return False
                
            ratio = low_freq_power / total_power
            
            # 如果低频成分超过一定阈值，认为存在基线漂移
            return ratio > 0.1
        except Exception as e:
            print(f"基线漂移检测计算错误: {str(e)}")
            return False
    
    def _has_powerline_interference(self):
        """检测工频干扰"""
        try:
            # 确保数据长度足够
            nperseg = min(len(self.ecg_data), 1024)
            if nperseg < 10:
                return False
                
            f, Pxx = welch(self.ecg_data, fs=self.sampling_rate, nperseg=nperseg)
            
            # 确保f和Pxx不为空
            if len(f) == 0 or len(Pxx) == 0:
                return False
            
            # 查找最接近50Hz和60Hz的频率索引
            if max(f) < 40:  # 如果最大频率小于40Hz，无法检测工频干扰
                return False
                
            idx_50 = np.argmin(np.abs(f - 50)) if 50 <= max(f) else -1
            idx_60 = np.argmin(np.abs(f - 60)) if 60 <= max(f) else -1
            
            if idx_50 == -1 and idx_60 == -1:
                return False
            
            # 计算工频附近的功率
            power_50 = np.mean(Pxx[max(0, idx_50-1):min(len(Pxx), idx_50+2)]) if idx_50 != -1 else 0
            power_60 = np.mean(Pxx[max(0, idx_60-1):min(len(Pxx), idx_60+2)]) if idx_60 != -1 else 0
            
            # 计算总功率
            total_power = np.mean(Pxx) if len(Pxx) > 0 else 1
            
            # 避免除以零
            if total_power == 0:
                total_power = 1
            
            # 如果工频附近的功率明显高于平均功率，认为存在工频干扰
            return (power_50 > 3 * total_power) or (power_60 > 3 * total_power)
        except Exception as e:
            print(f"工频干扰检测计算错误: {str(e)}")
            return False
    
    def _has_muscle_artifact(self):
        """检测肌电噪声"""
        try:
            # 确保数据长度足够
            if len(self.ecg_data) < 10:
                return False
                
            # 计算高频成分比例
            nperseg = min(len(self.ecg_data), 1024)
            f, Pxx = welch(self.ecg_data, fs=self.sampling_rate, nperseg=nperseg)
            
            # 确保f和Pxx不为空
            if len(f) == 0 or len(Pxx) == 0:
                return False
                
            # 找到高频部分
            high_freq_indices = np.where(f > 100)[0]
            if len(high_freq_indices) == 0:
                return False
                
            high_freq_power = np.sum(Pxx[high_freq_indices])
            total_power = np.sum(Pxx)
            
            # 避免除以零
            if total_power == 0:
                return False
                
            high_freq_ratio = high_freq_power / total_power
            
            # 计算信号的微分和变异系数
            diff = np.diff(self.ecg_data)
            if len(diff) == 0:
                return False
                
            std_diff = np.std(diff)
            mean_abs_diff = np.mean(np.abs(diff))
            
            # 避免除以零
            if mean_abs_diff == 0:
                cv = 0
            else:
                cv = std_diff / mean_abs_diff
            
            # 高频功率高或变异系数高表示存在肌电噪声
            return high_freq_ratio > 0.1 or cv > 2.0
        except Exception as e:
            print(f"肌电噪声检测计算错误: {str(e)}")
            return False
    
    def _has_electrode_artifact(self):
        """检测电极接触不良"""
        try:
            # 确保数据长度足够
            if len(self.ecg_data) < 2:
                return False
                
            # 计算信号中的剧烈波动
            diff = np.abs(np.diff(self.ecg_data))
            
            if len(diff) == 0:
                return False
            
            # 计算平均信号幅度
            p95 = np.percentile(np.abs(self.ecg_data), 95) if len(self.ecg_data) > 0 else 0
            p5 = np.percentile(np.abs(self.ecg_data), 5) if len(self.ecg_data) > 0 else 0
            signal_amp = p95 - p5
            
            # 避免除以零或接近零的值
            if signal_amp < 1e-6:
                signal_amp = 1.0
            
            # 如果存在超过平均信号幅度数倍的变化，认为是电极接触不良
            max_diff = np.max(diff) if len(diff) > 0 else 0
            return max_diff > 5 * signal_amp
        except Exception as e:
            print(f"电极接触不良检测计算错误: {str(e)}")
            return False
        
    def bandpass_filter(self, ecg_data=None, lowcut=0.5, highcut=40, order=4):
        """带通滤波"""
        if ecg_data is None:
            ecg_data = self.ecg_data
            
        nyquist = 0.5 * self.sampling_rate
        low = lowcut / nyquist
        high = highcut / nyquist
        
        b, a = butter(order, [low, high], btype='band')
        return filtfilt(b, a, ecg_data)
    
    def remove_baseline_wander(self, ecg_data=None, cutoff=0.5, order=4):
        """去除基线漂移"""
        if ecg_data is None:
            ecg_data = self.ecg_data
            
        nyquist = 0.5 * self.sampling_rate
        high = cutoff / nyquist
        
        b, a = butter(order, high, btype='high')
        return filtfilt(b, a, ecg_data)
    
    def notch_filter(self, ecg_data=None, freq=50, q=30):
        """陷波滤波器，用于去除工频干扰"""
        if ecg_data is None:
            ecg_data = self.ecg_data
            
        # 计算滤波器的系数
        nyquist = 0.5 * self.sampling_rate
        w0 = freq / nyquist
        
        # 创建简单的FIR陷波滤波器
        n = int(self.sampling_rate / freq * 2)  # 保证滤波器长度是工频周期的整数倍
        if n % 2 == 0:
            n += 1  # 确保滤波器长度为奇数
            
        h = np.ones(n)
        h[::int(self.sampling_rate/freq)] = 0  # 在工频点上设置为零
        h = h / np.sum(h)  # 归一化
        
        # 应用滤波器
        filtered_data = np.convolve(ecg_data, h, mode='same')
        return filtered_data
    
    def wavelet_denoise(self, ecg_data=None, wavelet='sym8', level=5):
        """小波去噪"""
        if ecg_data is None:
            ecg_data = self.ecg_data
            
        # 执行小波分解
        coeffs = pywt.wavedec(ecg_data, wavelet, level=level)
        
        # 应用软阈值去噪
        for i in range(1, len(coeffs)):
            # 计算自适应阈值
            threshold = np.median(np.abs(coeffs[i])) / 0.6745 * np.sqrt(2 * np.log(len(ecg_data)))
            coeffs[i] = pywt.threshold(coeffs[i], threshold, mode='soft')
        
        # 重构信号
        return pywt.waverec(coeffs, wavelet)
    
    def adaptive_noise_reduction(self, ecg_data=None):
        """自适应噪声减少处理"""
        if ecg_data is None:
            ecg_data = self.ecg_data
            
        processed_signal = ecg_data.copy()
        noise_types = self.detect_noise_types()
        
        # 根据检测到的噪声类型应用相应的滤波
        if "基线漂移" in noise_types:
            processed_signal = self.remove_baseline_wander(processed_signal)
            
        if "工频干扰" in noise_types:
            # 应用50Hz和60Hz的陷波滤波
            processed_signal = self.notch_filter(processed_signal, freq=50)
            processed_signal = self.notch_filter(processed_signal, freq=60)
            
        if "肌电噪声" in noise_types or "电极接触不良" in noise_types:
            # 应用小波去噪
            processed_signal = self.wavelet_denoise(processed_signal)
            
        # 最后应用带通滤波
        processed_signal = self.bandpass_filter(processed_signal)
        
        return processed_signal
    
    def assess_signal_quality(self, processed_signal=None):
        """评估信号质量"""
        if processed_signal is None:
            processed_signal = self.adaptive_noise_reduction()
            
        noise_types = self.detect_noise_types()
        
        # 计算信噪比
        if len(noise_types) > 0:
            noise = self.ecg_data - processed_signal
            snr = 10 * np.log10(np.sum(processed_signal**2) / np.sum(noise**2)) if np.sum(noise**2) > 0 else 100
        else:
            snr = 100  # 如果没有检测到噪声，假设SNR很高
            
        # 计算其他质量指标
        std_ratio = np.std(processed_signal) / np.std(self.ecg_data) if np.std(self.ecg_data) > 0 else 1
        peak_ratio = np.max(np.abs(processed_signal)) / np.max(np.abs(self.ecg_data)) if np.max(np.abs(self.ecg_data)) > 0 else 1
        
        # 基于噪声类型和指标计算质量分数
        base_score = 100
        
        # 根据噪声类型扣分
        for noise_type in noise_types:
            if noise_type == "基线漂移":
                base_score -= 10
            elif noise_type == "工频干扰":
                base_score -= 15
            elif noise_type == "肌电噪声":
                base_score -= 20
            elif noise_type == "电极接触不良":
                base_score -= 25
                
        # 根据SNR调整分数
        if snr < 0:
            base_score -= 30
        elif snr < 5:
            base_score -= 20
        elif snr < 10:
            base_score -= 10
        elif snr < 15:
            base_score -= 5
            
        # 确保分数在0-100之间
        quality_score = max(0, min(100, base_score))
        
        # 返回质量信息
        return {
            "quality_score": quality_score,
            "noise_types": noise_types,
            "metrics": {
                "SNR": round(snr, 2),
                "std_ratio": round(std_ratio, 2),
                "peak_ratio": round(peak_ratio, 2)
            }
        }

def preprocess_ecg_for_api(ecg_data, sampling_rate=500):
    """
    预处理ECG数据以准备API调用，包括噪声检测和自适应滤波
    
    参数:
    ecg_data: numpy数组，原始ECG数据
    sampling_rate: 采样率，默认500Hz
    
    返回:
    (processed_ecg, quality_info): 元组，包含处理后的ECG数据和信号质量信息
    """
    # 使用我们实现的简化版ECG处理器
    processor = SimpleECGProcessor(ecg_data, sampling_rate)
    
    # 自适应噪声处理
    processed_ecg = processor.adaptive_noise_reduction()
    
    # 评估信号质量
    quality_info = processor.assess_signal_quality(processed_ecg)
    
    print(f"信号质量评分: {quality_info['quality_score']}/100")
    if quality_info['noise_types']:
        print(f"检测到的噪声类型: {', '.join(quality_info['noise_types'])}")
    
    return processed_ecg, quality_info



def analyze_ecg(ecg_data, url, headers, categories):
    """调用API分析ECG数据并返回结果"""
    try:
        import json
        import time
        import numpy as np
        
        start_time = time.time()
        
        # 检查API URL是否有效
        if not url or not url.startswith("http"):
            print("错误: 无效的API URL")
            return {"error": "无效的API URL", "signal_quality": quality_info}
            
        # 检查API密钥是否有效
        if not headers or "Authorization" not in headers:
            print("警告: 没有提供API密钥，仅返回信号质量评估结果")
            # 创建一个模拟的结果，只包含信号质量信息
            processor = SimpleECGProcessor(ecg_data)
            processed_signal = processor.adaptive_noise_reduction()
            quality_info = processor.assess_signal_quality(processed_signal)
            
            return {
                "status": "signal_quality_only",
                "signal_quality": quality_info,
                "diagnosis": {
                    "提示": 1.0,
                    "需要API密钥": 1.0
                },
                "explanation": "没有有效的API密钥，只提供信号质量评估结果。要获取完整的ECG分析，请提供有效的DeepSeek API密钥。"
            }
        
        # 计算信号质量 (在API调用前预处理)
        processor = SimpleECGProcessor(ecg_data)
        processed_signal = processor.adaptive_noise_reduction()
        quality_info = processor.assess_signal_quality(processed_signal)
        
        # 处理信号数据：如果点数超过2500个，则只取前2500个点
        if len(processed_signal) > 2500:
            sampled_signal = processed_signal[:2500]
            print(f"信号已截取: 使用前2500个点")
        else:
            sampled_signal = processed_signal
            
        # 将ECG数据转换为字符串，使用较短的表示形式
        # 将数值四舍五入到小数点后2位，进一步减少数据量
        ecg_data_str = json.dumps([round(float(x), 2) for x in sampled_signal.tolist()])
        print(f"数据字符串长度: {len(ecg_data_str)} 字符")
        
        # 构建提示词
        quality_info_str = f"信号质量评分: {quality_info['quality_score']}/100"
        if quality_info['noise_types']:
            quality_info_str += f", 检测到的噪声类型: {', '.join(quality_info['noise_types'])}"
        
        # 减少提示词中的数据量，只包含简短的ECG数据摘要
        data_summary = f"数据点数: {len(sampled_signal)}, 最大值: {round(np.max(sampled_signal), 2)}, 最小值: {round(np.min(sampled_signal), 2)}, 平均值: {round(np.mean(sampled_signal), 2)}"
        
        prompt = f"""请作为心电图分析专家，分析以下ECG数据:

数据采样率: {250 * downsample_factor if 'downsample_factor' in locals() else 250}Hz
{quality_info_str}
{data_summary}
以下是ECG数据前1000个点(已预处理降噪):
{json.dumps([round(float(x), 2) for x in sampled_signal[:1000].tolist()])}

请分析以下可能的心律失常情况:
{', '.join(categories)}

请提供详细的心电分析，包括各种可能的心律失常及其可能性评估。如果信号质量不佳，请在分析中说明。
结果需要包含:
1. 主要发现和诊断
2. 可能性评估 (百分比)
3. 重要特征解释
"""
        
        print(f"提示词总长度: {len(prompt)} 字符")
        
        # 构建API请求 - 根据curl命令更新
        data = {
            "model": "deepseek-r1-250120",
            "messages": [
                {"role": "system", "content": "你是卫和医疗的AI心电医生，专业分析心电图数据"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.1,
            "top_p": 0.9,
            "max_tokens": 2000
        }
        
        # 发送API请求
        print("发送API请求...")
        
        # 设置更长的超时时间
        timeout = (30, 60)  # (连接超时, 读取超时)
        max_retries = 3
        
        # 尝试调用API
        for retry in range(max_retries):
            try:
                print(f"尝试API调用 (第{retry + 1}次)...")
                
                # 使用更简单的请求格式
                simplified_data = {
                    "model": "deepseek-r1-250120",
                    "messages": [
                        {
                            "role": "user",
                            "content": "test"
                        }
                    ]
                }
                
                # 先测试API连接
                print("测试API连接...")
                test_response = requests.post(url, headers=headers, json=simplified_data, timeout=(30, 30))
                if not test_response.ok:
                    print(f"API连接测试失败: {test_response.status_code}")
                    print("响应内容:", test_response.text)
                    if retry < max_retries - 1:
                        print("等待5秒后重试...")
                        time.sleep(5)
                        continue
                    raise requests.exceptions.RequestException(f"API连接测试失败: {test_response.status_code}")
                
                print("API连接测试成功，发送完整请求...")
                response = requests.post(url, headers=headers, json=data, timeout=(60, 120))  # 增加主请求的超时时间
                
                if not response.ok:
                    print(f"API请求失败，状态码: {response.status_code}")
                    print("响应内容:", response.text)
                    if retry < max_retries - 1:
                        print("等待5秒后重试...")
                        time.sleep(5)
                        continue
                    response.raise_for_status()
                
                # 解析API响应
                result = response.json()
                
                # 添加时间信息
                api_time = time.time() - start_time
                print(f"API调用成功，耗时: {api_time:.2f}秒")
                
                # 解析结果
                parsed_result = {}
                
                if "choices" in result and result["choices"]:
                    content = result["choices"][0]["message"]["content"]
                    
                    # 尝试提取诊断结果
                    try:
                        # 简单解析，可根据实际API返回格式调整
                        diagnosis = {}
                        for category in categories:
                            if category.lower() in content.lower():
                                # 简单估算可能性，实际应根据API返回调整
                                if "高度可能" in content or "极可能" in content or "明确" in content:
                                    probability = 0.9
                                elif "可能" in content or "考虑" in content:
                                    probability = 0.7
                                elif "不排除" in content or "可疑" in content:
                                    probability = 0.4
                                else:
                                    probability = 0.0
                                    
                                diagnosis[category] = probability
                        
                        parsed_result["diagnosis"] = diagnosis
                        parsed_result["explanation"] = content
                    except Exception as e:
                        print(f"解析诊断结果失败: {str(e)}")
                        parsed_result["diagnosis"] = {}
                        parsed_result["explanation"] = "无法解析诊断结果: " + content
                else:
                    parsed_result["explanation"] = "API返回了空结果"
                
                # 成功获取结果，跳出重试循环
                break
                
            except requests.Timeout as e:
                print(f"API请求超时 (第{retry + 1}次): {str(e)}")
                if retry < max_retries - 1:
                    print("等待5秒后重试...")
                    time.sleep(5)
                else:
                    print("达到最大重试次数，返回错误信息")
                    parsed_result = {
                        "status": "api_error",
                        "error": f"API请求超时（{max_retries}次尝试后）: {str(e)}",
                        "explanation": "API服务器响应超时，请稍后重试。"
                    }
            
            except requests.RequestException as e:
                print(f"API请求失败 (第{retry + 1}次): {str(e)}")
                if retry < max_retries - 1:
                    print("等待5秒后重试...")
                    time.sleep(5)
                else:
                    print("达到最大重试次数，返回错误信息")
                    error_msg = str(e)
                    if hasattr(e, 'response') and e.response is not None:
                        try:
                            error_json = e.response.json()
                            error_detail = error_json.get('error', {}).get('message', '')
                            if error_detail:
                                error_msg = f"{error_msg} - {error_detail}"
                        except:
                            if e.response.text:
                                error_msg = f"{error_msg} - {e.response.text}"
                    
                    parsed_result = {
                        "status": "api_error",
                        "error": error_msg,
                        "explanation": f"API调用失败（{max_retries}次尝试后）: {error_msg}"
                    }
        
        # 将信号质量信息添加到结果中
        parsed_result["signal_quality"] = quality_info
        
        return parsed_result
        
    except Exception as e:
        api_time = time.time() - start_time
        print(f"API调用失败，耗时: {api_time:.2f}秒")
        print(f"错误: {str(e)}")
        return {"error": str(e), "signal_quality": quality_info if 'quality_info' in locals() else None}



def display_results(results):
    """
    显示API返回的结果
    
    参数:
    results: API返回的结果
    """
    import json
    
    if not results:
        print("无结果返回")
        return

    if "error" in results:
        print(f"错误: {results['error']}")
        return

    # 显示信号质量信息（如果存在）
    if "signal_quality" in results:
        quality_info = results["signal_quality"]
        print("\n===== 信号质量评估 =====")
        print(f"质量得分: {quality_info['quality_score']}/100")
        
        if quality_info['quality_score'] >= 80:
            print("质量评级: 优 (良好的信号质量)")
        elif quality_info['quality_score'] >= 60:
            print("质量评级: 良 (可接受的信号质量)")
        elif quality_info['quality_score'] >= 40:
            print("质量评级: 中 (有噪声但可分析)")
        else:
            print("质量评级: 差 (信号质量较低，诊断结果可能不可靠)")
        
        if quality_info['noise_types']:
            print(f"检测到的噪声类型: {', '.join(quality_info['noise_types'])}")
        else:
            print("未检测到明显噪声")
            
        if 'metrics' in quality_info:
            print("\n信号指标:")
            for key, value in quality_info['metrics'].items():
                print(f"- {key}: {value}")
        
        print("\n注意: 所有分析结果都基于经过降噪处理后的信号")
    
    # 显示分析结果
    if "diagnosis" in results:
        print("\n===== ECG诊断结果 =====")
        for category, probability in results["diagnosis"].items():
            print(f"{category}: {probability:.2f}")
    
    # 显示解释
    if "explanation" in results:
        print("\n===== 分析解释 =====")
        print(results["explanation"])
    
    # 显示建议（如果有）
    if "suggestions" in results:
        print("\n===== 建议 =====")
        for suggestion in results["suggestions"]:
            print(f"- {suggestion}")


def save_results(results, file_path):
    """
    保存分析结果到文件
    
    参数:
    results: 分析结果
    file_path: 源文件路径
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = os.path.basename(file_path).split('.')[0]
    output_dir = "results"
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存JSON结果
    output_file = f"{output_dir}/{filename}_results_{timestamp}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"结果已保存至: {output_file}")


def process_directory(directory_path, url, headers, categories, save=False, verbose=False):
    """
    处理目录中的所有ECG文件
    
    参数:
    directory_path: 目录路径
    url: API URL
    headers: API请求头
    categories: 要分析的类别
    save: 是否保存结果
    verbose: 是否显示详细信息
    """
    import glob
    
    print(f"分析目录中的文件: {directory_path}")
    file_patterns = [os.path.join(directory_path, "*.csv"), os.path.join(directory_path, "*.txt")]
    
    all_files = []
    for pattern in file_patterns:
        all_files.extend(glob.glob(pattern))
    
    if not all_files:
        print(f"目录 {directory_path} 中未找到匹配的文件")
        return
    
    print(f"找到 {len(all_files)} 个文件")
    
    # 对每个文件进行处理
    all_results = []
    for i, file_path in enumerate(all_files, 1):
        print(f"\n处理文件 {i}/{len(all_files)}: {os.path.basename(file_path)}")
        
        # 读取ECG数据
        ecg_data = read_data(file_path)
        if ecg_data is None:
            print(f"跳过文件 {file_path} - 读取失败")
            continue
        
        # 预处理ECG数据
        processed_ecg, quality_info = preprocess_ecg_for_api(ecg_data)
        
        # 调用API
        print("调用API分析ECG数据...")
        result = analyze_ecg(processed_ecg, url, headers, categories)
        
        if result:
            # 添加文件信息
            result["file_info"] = {
                "path": file_path,
                "name": os.path.basename(file_path),
                "processed_at": datetime.now().isoformat()
            }
            all_results.append(result)
            
            # 显示结果
            if verbose:
                display_results(result)
            else:
                # 简洁模式只显示关键信息
                print(f"文件: {os.path.basename(file_path)}")
                if "signal_quality" in result:
                    print(f"信号质量: {result['signal_quality']['quality_score']}/100")
                if "diagnosis" in result:
                    top_diagnosis = max(result["diagnosis"].items(), key=lambda x: x[1])
                    print(f"主要诊断: {top_diagnosis[0]} ({top_diagnosis[1]:.2f})")
    
    # 保存所有结果
    if save and all_results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = "results"
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        output_file = f"{output_dir}/batch_analysis_{timestamp}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        print(f"所有结果已保存至: {output_file}")


def main():
    """主函数，处理ECG数据并调用API"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='ECG分析API调用工具')
    parser.add_argument('--file', type=str, help='要分析的ECG数据文件路径')
    parser.add_argument('--dir', type=str, help='要分析的ECG数据文件目录')
    parser.add_argument('--save', action='store_true', help='保存分析结果到文件')
    parser.add_argument('--verbose', action='store_true', help='显示详细信息')
    parser.add_argument('--test', action='store_true', help='测试噪声处理，不调用API')
    parser.add_argument('--no-api', action='store_true', help='不调用API，只提供信号质量评估')
    args = parser.parse_args()
    
    # API配置
    url = None
    headers = None
    api_key = None
    
    # 只有在非测试模式且非无API模式下才配置API
    if not args.test and not args.no_api:
        # API配置 - 根据curl命令更新
        url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        
        # 尝试从环境变量获取API密钥
        api_key = os.environ.get("DEEPSEEK_API_KEY", "")
        
        # 如果环境变量中没有，使用硬编码的密钥
        if not api_key:
            api_key = "7fc579f3-264b-4d56-a6e9-fe2f9047d044"
            print("警告: 使用硬编码的API密钥")
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        print("API已配置，使用URL:", url)
        print("Authorization格式:", f"Bearer {api_key[:5]}...{api_key[-4:]}")
    
    # 要分析的ECG类别
    categories = ["窦性节律", "窦性心动过速", "窦性心动过缓", "房颤", "室性早搏"]
    
    if args.file:
        # 读取单个文件
        print(f"读取文件: {args.file}")
        ecg_data = read_data(args.file)
        if ecg_data is None:
            print(f"无法读取文件: {args.file}")
            return

        # 预处理ECG数据
        processed_ecg, quality_info = preprocess_ecg_for_api(ecg_data)
        
        if args.test:
            # 测试模式：只显示原始和处理后的信号波形
            print("\n===== 测试噪声处理模式 =====")
            print(f"信号质量评分: {quality_info['quality_score']}/100")
            if quality_info['noise_types']:
                print(f"检测到的噪声类型: {', '.join(quality_info['noise_types'])}")
            else:
                print("未检测到明显噪声")
            sampling_rate = 250  # 默认采样率
            time_vector_original = np.arange(len(ecg_data)) / sampling_rate
            time_vector_processed = np.arange(len(processed_ecg)) / sampling_rate
            plt.figure(figsize=(15, 10))
            plt.subplot(2, 1, 1)
            plt.plot(time_vector_original, ecg_data)
            plt.title('原始ECG信号')
            plt.xlabel('时间 (秒)')
            plt.ylabel('幅度')
            plt.grid(True)
            plt.subplot(2, 1, 2)
            plt.plot(time_vector_processed, processed_ecg)
            plt.title(f'处理后ECG信号 (质量分数: {quality_info["quality_score"]}/100)')
            plt.xlabel('时间 (秒)')
            plt.ylabel('幅度')
            plt.grid(True)
            plt.tight_layout()
            if args.save:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = os.path.basename(args.file).split('.')[0]
                output_dir = "results"
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                plt.savefig(f"{output_dir}/{filename}_processed_{timestamp}.png")
                print(f"图像已保存至: {output_dir}/{filename}_processed_{timestamp}.png")
                min_len = min(len(time_vector_processed), len(ecg_data), len(processed_ecg))
                pd.DataFrame({
                    'time': time_vector_processed[:min_len],
                    'original': ecg_data[:min_len] if len(ecg_data) >= min_len else np.pad(ecg_data, (0, min_len - len(ecg_data))),
                    'processed': processed_ecg[:min_len]
                }).to_csv(f"{output_dir}/{filename}_processed_{timestamp}.csv", index=False)
                print(f"数据已保存至: {output_dir}/{filename}_processed_{timestamp}.csv")
            plt.show()
            return
        elif args.no_api:
            print("\n===== 信号质量评估模式 (无API) =====")
            print(f"信号质量评分: {quality_info['quality_score']}/100")
            if quality_info['quality_score'] >= 80:
                print("质量评级: 优 (良好的信号质量)")
            elif quality_info['quality_score'] >= 60:
                print("质量评级: 良 (可接受的信号质量)")
            elif quality_info['quality_score'] >= 40:
                print("质量评级: 中 (有噪声但可分析)")
            else:
                print("质量评级: 差 (信号质量较低，诊断结果可能不可靠)")
            if quality_info['noise_types']:
                print(f"检测到的噪声类型: {', '.join(quality_info['noise_types'])}")
            else:
                print("未检测到明显噪声")
            if 'metrics' in quality_info:
                print("\n信号指标:")
                for key, value in quality_info['metrics'].items():
                    print(f"- {key}: {value}")
            if args.save:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = os.path.basename(args.file).split('.')[0]
                output_dir = "results"
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                quality_result = {
                    "file": args.file,
                    "timestamp": datetime.now().isoformat(),
                    "signal_quality": quality_info,
                    "mode": "no_api"
                }
                with open(f"{output_dir}/{filename}_quality_{timestamp}.json", 'w', encoding='utf-8') as f:
                    json.dump(quality_result, f, ensure_ascii=False, indent=2)
                print(f"质量评估结果已保存至: {output_dir}/{filename}_quality_{timestamp}.json")
            return
        else:
            results = analyze_ecg(processed_ecg, url, headers, categories)
            display_results(results)
            if args.save and results:
                save_results(results, args.file)
    elif args.dir:
        if args.no_api:
            print("警告: 无API模式下不支持批量处理目录。请使用--file参数处理单个文件。")
            return
        process_directory(args.dir, url, headers, categories, args.save, args.verbose)
    else:
        print("请指定要分析的文件或目录")
        print("使用方法: python api_call.py --file <文件路径> 或 --dir <目录路径>")
        print("可选参数:")
        print("  --save         保存结果")
        print("  --verbose      显示详细信息")
        print("  --test         测试噪声处理，不调用API")
        print("  --no-api       不调用API，只提供信号质量评估")

if __name__ == "__main__":
    main()
