/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// TosaMakeBroadcastable
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaMakeBroadcastableBase : public ::mlir::FunctionPass {
public:
  using Base = TosaMakeBroadcastableBase;

  TosaMakeBroadcastableBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TosaMakeBroadcastableBase(const TosaMakeBroadcastableBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-make-broadcastable");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-make-broadcastable"; }

  ::llvm::StringRef getDescription() const override { return "TOSA rank Reshape to enable Broadcasting"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaMakeBroadcastable");
  }
  ::llvm::StringRef getName() const override { return "TosaMakeBroadcastable"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// TosaMakeBroadcastable Registration
//===----------------------------------------------------------------------===//

inline void registerTosaMakeBroadcastablePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaMakeBroadcastablePass();
  });
}

//===----------------------------------------------------------------------===//
// TosaOpt Registration
//===----------------------------------------------------------------------===//

inline void registerTosaOptPasses() {
  registerTosaMakeBroadcastablePass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
