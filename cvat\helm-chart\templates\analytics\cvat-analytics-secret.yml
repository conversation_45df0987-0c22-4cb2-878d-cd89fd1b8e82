{{- if .Values.analytics.enabled }}
apiVersion: v1
kind: Secret
metadata:
  # vector helm doesn't allow template name of CM
  name: cvat-analytics-secret
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cvat.labels" . | nindent 4 }}
    app: cvat-app
    tier: analytics
{{- if semverCompare ">=1.21-0" .Capabilities.KubeVersion.GitVersion }}
immutable: true
{{- end }}
stringData:
  CLICKHOUSE_DB: {{ .Values.analytics.clickhouseDb }}
  CLICKHOUSE_USER: {{ .Values.analytics.clickhouseUser }}
  CLICKHOUSE_PASSWORD: {{ .Values.analytics.clickhousePassword }}
  CLICKHOUSE_HOST: {{ tpl (.Values.analytics.clickhouseHost) . }}
  CLICKHOUSE_PORT: {{ .Values.analytics.clickhousePort | quote }}
{{- end }}
