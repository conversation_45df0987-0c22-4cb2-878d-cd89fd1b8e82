import numpy as np
from biosppy.signals import ecg
from scipy import signal
from scipy.fft import fft, fftfreq

from apps.signal_analysis.filtering import wavelet_filter


def identify_noise_type(features):
    """
    识别是否为平直线+高频振荡噪声
    :param features: 信号特征字典
    :return: 是否为平直线+高频振荡噪声
    """
    # 直接检查是否同时满足幅度极低和高过零率的条件
    if features['amp_range'] < 0.3 and features['zcr'] > 0.4:
        if features['amp_range'] < 0.001:
            return True
    return False


def get_available_signals(raw_signal, sample_rate, window_sec=10, overlap_ratio=0.9, max_hr=300):
    """
    可穿戴设备ECG噪声检测核心函数
    :param raw_signal：原始ECG信号（一维数组）。
    :param sample_rate：采样率。
    :param window_sec：滑动窗口的时长，默认为10秒。
    :param overlap_ratio：窗口之间的重叠比例，默认为0.9（即90 % 重叠）。
    :param max_hr：最大心率阈值，默认为200 bpm。
    :return 返回非噪音时间段，格式为[[start_time, end_time], ...]。
    """
    filter_raw_signal = wavelet_filter(raw_signal, sample_rate)     # 心电信号滤波处理

    # 初始化参数
    signal_quality = 1  # 信号质量
    normal_time_period = [[0, 10]]  # 非噪音时间段
    noise_diagnosis = []  # 存储噪声诊断结果

    # 计算窗口大小和步长
    window_size = int(window_sec * sample_rate)
    step_size = int(window_size * (1 - overlap_ratio))
    num_samples = len(filter_raw_signal)

    # 预计算整段信号的心率
    try:
        ecg_info = ecg.ecg(signal=filter_raw_signal, sampling_rate=sample_rate, show=False)
        rpeaks = ecg_info['rpeaks']  # 获取R波峰值的索引
        # 计算心率对应的时间点（基于R波峰值）
        heart_rate_times = rpeaks / sample_rate
        # 计算相邻R波之间的时间间隔（RR间期），然后转换为心率（bpm）
        rr_intervals = np.diff(rpeaks) / sample_rate
        heart_rates = 60 / rr_intervals
    except Exception:
        heart_rate_times = None
        heart_rates = None

    if heart_rate_times is not None:
        normal_time_period = []

        range_start = 0
        range_end = 1 if num_samples == window_size else window_size

        # 滑动窗口处理
        for index, start in enumerate(range(range_start, range_end, step_size)):
            end = start + window_size

            start_time = int(start if start == 0 else (start + index) / sample_rate)
            end_time = int(end / sample_rate if start == 0 else (end + index) / sample_rate)

            segment = filter_raw_signal[start:end]
            # 特征提取
            features = {
                'amp_range': np.ptp(segment),  # 幅度范围
                'zcr': zero_cross_rate(segment),  # 过零率
                'hf_energy': high_freq_energy(segment, sample_rate),
                'baseline_drift': baseline_variation(segment, sample_rate),
                'slopes': slope_detection(segment, sample_rate)
            }

            is_noisy = is_noisy_window(features)
            
            # 检查是否为平直线+高频振荡噪声
            if is_noisy and identify_noise_type(features):
                signal_quality = -1
                normal_time_period = [[0, 10]]
                get_available_signals.noise_diagnosis = [{'is_noisy': True, 'features': features}]
                break
            
            if not is_noisy:
                # 噪声判断（基于动态阈值）和心率检测
                max_heart_rate = 0

                window_hr_indices = np.where((heart_rate_times >= start_time) & (heart_rate_times <= end_time))[0]

                # 确保索引有效，并且考虑心率数据和R波数据的数量关系
                valid_indices = [i for i in window_hr_indices if i < len(heart_rates)]

                if len(valid_indices) >= 1:
                    window_hr = heart_rates[valid_indices]
                    max_heart_rate = int(np.max(window_hr))

                if max_heart_rate < max_hr:
                    normal_time_period.append([start_time, end_time])

    if len(normal_time_period) == 0:
        signal_quality = -1
        normal_time_period = [[0, 10]]
    return signal_quality, normal_time_period


def zero_cross_rate(sig):
    """
    优化过零率计算，忽略微小波动。

    参数:
    - sig: 输入信号

    返回:
    - 过零率
    """
    if len(sig) < 2:
        return 0.0

    # 计算信号的标准差
    std = np.std(sig)

    # 动态计算阈值倍数
    if std > 0:
        # 使用信号的标准差的分位数来动态调整阈值倍数
        threshold_multiplier = np.percentile(np.abs(sig), 75) / (std + 1e-6)
    else:
        threshold_multiplier = 1.0  # 如果标准差为零，使用默认值

    # 动态计算阈值
    threshold = threshold_multiplier * np.std(sig)

    # 计算相邻点的差值
    sig_diff = np.diff(sig)

    # 忽略微小波动
    crosses = np.where(np.abs(sig_diff) > threshold)[0]

    return len(crosses) / len(sig_diff)


def high_freq_energy(sig, fs, cutoff=25):
    """
    可穿戴设备特化高频能量检测。

    参数:
    - sig: 输入信号
    - fs: 采样频率
    - cutoff: 高频截止频率，默认为25

    返回:
    - 高频能量占比
    """
    if len(sig) == 0:
        return 0.0

    # 去除直流分量
    sig_centered = sig - np.mean(sig)

    # 计算FFT
    fft_vals = np.abs(fft(sig_centered))

    # 计算频率
    freqs = np.abs(fftfreq(len(sig), 1 / fs))

    # 高频掩码
    max_freq = min(fs // 2, 100)
    hf_mask = (freqs > cutoff) & (freqs < max_freq)

    # 计算高频能量占比
    total_energy = np.sum(fft_vals)
    hf_energy = np.sum(fft_vals[hf_mask]) / (total_energy + 1e-6)

    return hf_energy


def baseline_variation(sig, fs, cutoff=0.5):
    """
    基线漂移量化（低频频带能量）。

    参数:
    - sig: 输入信号
    - fs: 采样频率
    - cutoff: 高通滤波器截止频率，默认为0.5

    返回:
    - 基线漂移的标准差
    """
    if len(sig) == 0:
        return 0.0

    # 设计高通滤波器
    b, a = signal.butter(3, cutoff, btype='highpass', fs=fs)

    # 应用滤波器
    baseline = signal.filtfilt(b, a, sig)

    # 计算基线漂移的标准差
    return np.std(baseline)


def slope_detection(ecg_data, sample_rate, slope_threshold=0.035, window_size=0.2):
    """斜率检测"""
    # 初始化斜率数组
    slopes = []

    times = []

    # 设置滑动窗口的大小（例如，0.2秒）
    sliding_window = int(window_size * sample_rate)
    # 滑动窗口遍历
    for i in range(0, len(ecg_data) - sliding_window + 1, sliding_window // 2):
        window = ecg_data[i:i + sliding_window]
        x = np.arange(sliding_window)
        slope, _ = np.polyfit(x, window, 1)
        slopes.append(slope)

        # 计算当前窗口的起始时间
        start_time = i / sample_rate
        times.append(start_time)

    # 将斜率数组转换为numpy数组
    slopes = np.array(slopes)

    # 查找是否有大于阈值的斜率
    large_slopes = np.abs(slopes) > slope_threshold

    return np.any(large_slopes)


def is_noisy_window(features):
    """动态阈值噪声判断（基于可穿戴设备经验值）"""
    rules = [
        features['amp_range'] < 0.3,  # 信号丢失（<20μV）
        features['zcr'] > 0.4,  # 过零率
        features['hf_energy'] > 0.35,  # 肌电干扰阈值
        features['baseline_drift'] > 0.4,  # 基线漂移阈值(mV)
        features['slopes']  # 斜率检测
    ]
    
    # 检查是否有规则被触发
    is_noisy = any(rules)
    
    # 返回是否为噪声
    return is_noisy



