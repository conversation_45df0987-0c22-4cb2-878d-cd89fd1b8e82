#!/usr/bin/env python3
"""
调试版羽毛球装备爬虫 - 带详细日志
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os
from urllib.parse import urljoin

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DebugBadmintonCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        })
        
        self.base_url = "https://www.badmintoncn.com"
        self.equipment_types = {
            1: "羽毛球拍",
            2: "羽毛球鞋", 
            3: "运动包",
            4: "羽毛球线",
            5: "羽毛球",
            6: "运动服饰",
            7: "手胶"
        }
        
        os.makedirs('output', exist_ok=True)
        logger.info("🏸 调试版爬虫初始化完成")

    def test_single_type(self, equipment_type_id=1):
        """测试单个类型的爬取"""
        type_name = self.equipment_types.get(equipment_type_id, f"类型{equipment_type_id}")
        logger.info(f"🧪 测试爬取 {type_name} (类型ID: {equipment_type_id})")
        
        # 1. 测试获取列表
        list_url = f"{self.base_url}/cbo_eq/list.php?tid={equipment_type_id}"
        logger.info(f"📝 访问列表页: {list_url}")
        
        try:
            response = self.session.get(list_url, timeout=20)
            logger.info(f"📡 列表页响应状态: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找装备链接
                equipment_links = []
                all_links = soup.find_all('a', href=True)
                logger.info(f"🔗 页面总链接数: {len(all_links)}")
                
                for link in all_links:
                    href = link.get('href')
                    if href and 'view.php?eid=' in href:
                        if not href.startswith('http'):
                            href = urljoin(self.base_url, href)
                        equipment_links.append(href)
                        logger.debug(f"  找到装备链接: {href}")
                
                equipment_links = list(set(equipment_links))
                logger.info(f"✅ 找到 {len(equipment_links)} 个{type_name}装备链接")
                
                if equipment_links:
                    # 测试解析第一个装备
                    test_url = equipment_links[0]
                    logger.info(f"🔍 测试解析装备: {test_url}")
                    
                    equipment_data = self.parse_equipment_detail(test_url)
                    if equipment_data:
                        logger.info(f"✅ 解析成功!")
                        logger.info(f"  装备名称: {equipment_data['equipment_name']}")
                        logger.info(f"  装备品牌: {equipment_data['equipment_brand']}")
                        logger.info(f"  装备类型: {equipment_data['equipment_type']}")
                        
                        # 显示所有非空字段
                        filled_fields = {k: v for k, v in equipment_data.items() if v and v != ''}
                        logger.info(f"  已填充字段数: {len(filled_fields)}/{len(equipment_data)}")
                        for k, v in filled_fields.items():
                            if k not in ['detail_url', 'crawl_time', 'equipment_name']:
                                logger.debug(f"    {k}: {v}")
                        
                        return [equipment_data]
                    else:
                        logger.error("❌ 解析失败")
                        return []
                else:
                    logger.warning("⚠️ 未找到装备链接")
                    # 打印页面内容片段用于调试
                    page_text = soup.get_text()[:1000]
                    logger.debug(f"页面内容片段: {page_text}")
                    return []
            else:
                logger.error(f"❌ 列表页访问失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            import traceback
            logger.debug(f"错误详情: {traceback.format_exc()}")
            return []

    def parse_equipment_detail(self, url):
        """解析装备详情 - 调试版"""
        try:
            eid_match = re.search(r'eid=(\d+)', url)
            equipment_id = eid_match.group(1) if eid_match else 'unknown'
            
            logger.info(f"📋 开始解析装备详情 (ID: {equipment_id})")
            
            # 访问详情页
            response = self.session.get(url, timeout=20)
            logger.info(f"📡 详情页响应状态: {response.status_code}")
            
            if response.status_code != 200:
                logger.error(f"❌ 详情页访问失败: {response.status_code}")
                return None
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 初始化数据
            equipment_data = {
                'equipment_id': equipment_id,
                'equipment_name': '',
                'equipment_type': '',
                'equipment_brand': '',
                'equipment_series': '',
                'equipment_description': '',
                'release_date': '',
                'equipment_introduction': '',
                'specifications': '',
                'frame_material': '',
                'shaft_material': '',
                'weight': '',
                'length': '',
                'grip_size': '',
                'shaft_stiffness': '',
                'string_tension': '',
                'balance_point': '',
                'new_avg_price': '',
                'used_avg_price': '',
                'total_registered_users': '',
                'detail_url': url,
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 提取标题
            if soup.title:
                title = soup.title.string.strip()
                equipment_name = title.replace('中羽在线 badmintoncn.com', '').strip()
                equipment_data['equipment_name'] = equipment_name[:100]
                logger.info(f"📝 装备名称: {equipment_name}")
            
            # 查找表格
            tables = soup.find_all('table')
            logger.info(f"📊 找到 {len(tables)} 个表格")
            
            # 解析表格信息
            self.extract_table_info_debug(soup, equipment_data)
            
            # 获取价格信息
            self.get_price_info_debug(equipment_id, equipment_data)
            
            return equipment_data
            
        except Exception as e:
            logger.error(f"❌ 解析装备详情失败: {e}")
            import traceback
            logger.debug(f"错误详情: {traceback.format_exc()}")
            return None

    def extract_table_info_debug(self, soup, equipment_data):
        """提取表格信息 - 调试版"""
        try:
            tables = soup.find_all('table')
            
            field_counter = {}
            
            for table_idx, table in enumerate(tables):
                rows = table.find_all('tr')
                logger.debug(f"  表格{table_idx+1}: {len(rows)}行")
                
                for row_idx, row in enumerate(rows):
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if key and value:
                            logger.debug(f"    行{row_idx+1}: {key} = {value}")
                            
                            # 字段映射
                            mappings = {
                                '装备类型': 'equipment_type', '类型': 'equipment_type',
                                '装备品牌': 'equipment_brand', '品牌': 'equipment_brand',
                                '装备系列': 'equipment_series', '系列': 'equipment_series',
                                '上市日期': 'release_date', '发布日期': 'release_date',
                                '拍框材质': 'frame_material', '框架材质': 'frame_material',
                                '拍杆材质': 'shaft_material', '中管材质': 'shaft_material',
                                '重量': 'weight', '拍重': 'weight', '拍身重量': 'weight',
                                '长度': 'length', '拍身长度': 'length',
                                '手柄尺寸': 'grip_size', '拍柄粗细': 'grip_size',
                                '中管韧度': 'shaft_stiffness', '硬度': 'shaft_stiffness',
                                '拉线磅数': 'string_tension', '穿线磅数': 'string_tension',
                                '平衡点': 'balance_point', '重心': 'balance_point',
                            }
                            
                            for keyword, field in mappings.items():
                                if keyword in key and not equipment_data[field]:
                                    equipment_data[field] = value
                                    field_counter[field] = field_counter.get(field, 0) + 1
                                    logger.info(f"    ✅ 映射成功: {keyword} -> {field} = {value}")
                                    break
                            
                            # 规格参数
                            if equipment_data['specifications']:
                                equipment_data['specifications'] += f"; {key}: {value}"
                            else:
                                equipment_data['specifications'] = f"{key}: {value}"
            
            logger.info(f"📊 字段映射统计: {field_counter}")
                                
        except Exception as e:
            logger.error(f"❌ 提取表格信息失败: {e}")

    def get_price_info_debug(self, equipment_id, equipment_data):
        """获取价格信息 - 调试版"""
        try:
            price_url = f"{self.base_url}/cbo_eq/view_buy.php?eid={equipment_id}"
            logger.info(f"💰 访问价格页面: {price_url}")
            
            response = self.session.get(price_url, timeout=20)
            logger.info(f"📡 价格页响应状态: {response.status_code}")
            
            if response.status_code == 200:
                price_soup = BeautifulSoup(response.text, 'html.parser')
                price_text = price_soup.get_text()
                
                # 价格模式
                patterns = {
                    'new_avg_price': [r'最近全新均价[：:]\s*(\d+)', r'全新均价[：:]\s*(\d+)'],
                    'used_avg_price': [r'最近二手均价[：:]\s*(\d+)', r'二手均价[：:]\s*(\d+)'],
                    'total_registered_users': [r'总登记球友[：:]\s*(\d+)', r'登记球友[：:]\s*(\d+)'],
                }
                
                price_found = {}
                for field, field_patterns in patterns.items():
                    for pattern in field_patterns:
                        match = re.search(pattern, price_text)
                        if match:
                            equipment_data[field] = match.group(1)
                            price_found[field] = match.group(1)
                            logger.info(f"    ✅ 找到{field}: {match.group(1)}")
                            break
                
                if not price_found:
                    logger.warning("⚠️ 未找到价格信息")
                    # 打印页面内容片段用于调试
                    logger.debug(f"价格页内容片段: {price_text[:500]}")
                else:
                    logger.info(f"💰 价格信息: {price_found}")
                    
        except Exception as e:
            logger.error(f"❌ 获取价格信息失败: {e}")

def main():
    """主函数"""
    logger.info("🧪 启动调试版爬虫")
    
    crawler = DebugBadmintonCrawler()
    
    # 测试羽毛球拍类型
    logger.info("=" * 50)
    data = crawler.test_single_type(equipment_type_id=1)
    
    if data:
        logger.info("\n🎉 测试成功!")
        
        # 保存测试数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        test_file = f"output/debug_test_{timestamp}.json"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📁 测试数据已保存: {test_file}")
        
        # 显示数据完整性
        item = data[0]
        filled = sum(1 for v in item.values() if v and v != '')
        total = len(item)
        logger.info(f"📊 数据完整性: {filled/total*100:.1f}% ({filled}/{total})")
        
    else:
        logger.error("❌ 测试失败，未获取到数据")

if __name__ == "__main__":
    main() 