#!/usr/bin/env python3
"""
终极版羽毛球装备爬虫 - 模拟真实浏览器行为
解决验证后Session不生效的问题
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os
from urllib.parse import urljoin, urlparse
import random

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltimateBadmintonCrawler:
    def __init__(self):
        self.session = requests.Session()
        
        # 更完整的浏览器模拟Headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        })
        
        self.base_url = "https://www.badmintoncn.com"
        self.equipment_types = {
            1: "羽毛球拍",
            2: "羽毛球鞋", 
            3: "运动包",
            4: "羽毛球线",
            5: "羽毛球",
            6: "运动服饰",
            7: "手胶"
        }
        
        self.verification_solved = False
        self.session_cookies = None
        
        os.makedirs('output', exist_ok=True)
        logger.info("🏸 终极版爬虫初始化完成")

    def human_like_delay(self, min_seconds=1, max_seconds=3):
        """人类化延时"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)

    def simulate_browser_session(self):
        """模拟真实浏览器会话建立过程"""
        try:
            logger.info("🌐 模拟浏览器会话建立...")
            
            # 1. 访问主页，建立初始连接
            logger.info("📍 访问主页...")
            response = self.session.get(self.base_url, timeout=30)
            logger.info(f"主页状态码: {response.status_code}")
            
            if response.status_code != 200:
                logger.warning(f"主页访问异常: {response.status_code}")
                return False
            
            # 2. 等待，模拟用户查看页面
            self.human_like_delay(2, 4)
            
            # 3. 检查并保存初始Cookie
            initial_cookies = dict(self.session.cookies)
            logger.info(f"初始Cookie: {len(initial_cookies)} 个")
            
            # 4. 访问一个静态资源页面（模拟浏览器加载行为）
            try:
                # 通常网站会有CSS、JS等资源，先访问一些常见路径
                static_paths = ['/css/', '/js/', '/images/']
                for path in static_paths:
                    try:
                        static_url = f"{self.base_url}{path}"
                        self.session.get(static_url, timeout=10)
                        self.human_like_delay(0.5, 1)
                        break  # 成功一个就够了
                    except:
                        continue
            except:
                pass
            
            # 5. 现在访问装备页面
            logger.info("🎯 访问装备页面...")
            equipment_url = f"{self.base_url}/cbo_eq/list.php"
            
            # 更新Referer
            self.session.headers.update({
                'Referer': self.base_url,
                'Sec-Fetch-Site': 'same-origin'
            })
            
            response = self.session.get(equipment_url, timeout=30)
            logger.info(f"装备页面状态码: {response.status_code}")
            
            return response.text, response.status_code
            
        except Exception as e:
            logger.error(f"浏览器会话建立失败: {e}")
            return None, None

    def solve_verification_enhanced(self, html_content, page_url):
        """增强版验证解决"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            page_text = soup.get_text()
            
            # 检查是否需要验证
            verification_patterns = [
                r'(\d+[×*x]\d+)=？', r'(\d+[+]\d+)=？', r'(\d+[-]\d+)=？',
                r'羽毛球有几根毛', r'ZYZX.*?怎么写', r'中羽.*?缩写',
            ]
            
            question = None
            for pattern in verification_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    question = matches[0]
                    break
            
            if not question:
                logger.info("✅ 无需验证或已通过验证")
                return True
            
            logger.info(f"🔐 检测到验证问题: {question}")
            
            # 获取验证答案
            answer = self.get_verification_answer(question)
            if not answer:
                logger.error("❌ 无法获取验证答案")
                return False
            
            # 查找验证表单
            form = soup.find('form')
            if not form:
                logger.error("❌ 未找到验证表单")
                return False
            
            # 构建表单数据 - 更完整的模拟
            form_data = {}
            
            # 添加所有隐藏字段
            for input_tag in form.find_all('input'):
                input_name = input_tag.get('name')
                input_value = input_tag.get('value', '')
                input_type = input_tag.get('type', 'text')
                
                if input_name:
                    if input_type == 'hidden':
                        form_data[input_name] = input_value
                        logger.debug(f"添加隐藏字段: {input_name} = {input_value}")
            
            # 添加答案字段
            answer_fields = ['answer', 'verify', 'code', 'result', 'a']
            answer_added = False
            
            for field in answer_fields:
                if form.find('input', {'name': field}):
                    form_data[field] = answer
                    answer_added = True
                    logger.info(f"✅ 答案字段: {field} = {answer}")
                    break
            
            if not answer_added:
                form_data['a'] = answer
                logger.info(f"✅ 默认答案字段: a = {answer}")
            
            # 获取提交URL
            action = form.get('action', page_url)
            if not action.startswith('http'):
                submit_url = urljoin(self.base_url, action)
            else:
                submit_url = action
            
            logger.info(f"📤 提交验证到: {submit_url}")
            
            # 更新Headers为POST请求
            post_headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': self.base_url,
                'Referer': page_url,
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
            }
            
            # 提交验证
            submit_response = self.session.post(
                submit_url, 
                data=form_data, 
                headers=post_headers,
                timeout=30,
                allow_redirects=True
            )
            
            logger.info(f"验证提交状态码: {submit_response.status_code}")
            
            # 检查是否有重定向或Set-Cookie
            if submit_response.status_code in [200, 302]:
                # 检查新的Cookie
                new_cookies = dict(self.session.cookies)
                logger.info(f"验证后Cookie数量: {len(new_cookies)}")
                
                # 标记验证已解决
                self.verification_solved = True
                logger.info("✅ 验证处理完成")
                
                # 等待一段时间让Session生效
                self.human_like_delay(3, 5)
                return True
            else:
                logger.error(f"❌ 验证提交失败: {submit_response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"验证处理失败: {e}")
            return False

    def get_verification_answer(self, question):
        """获取验证答案"""
        logger.info(f"🤖 处理验证问题: {question}")
        
        # 羽毛球有几根毛？
        if '羽毛球' in question and ('几根毛' in question or '多少毛' in question):
            return "16"
        
        # ZYZX小写
        if 'ZYZX' in question and ('小写' in question or '怎么写' in question):
            return "zyzx"
        
        # 中羽在线缩写
        if '中羽' in question and '缩写' in question:
            return "zyzx"
        
        # 数学计算
        try:
            # 清理问题文本
            math_text = re.sub(r'[=？?].*', '', question).strip()
            
            # 处理乘法符号
            math_text = math_text.replace('×', '*').replace('x', '*').replace('X', '*')
            
            # 验证是否为简单数学表达式
            if re.match(r'^\d+\s*[+\-*/]\s*\d+$', math_text):
                result = eval(math_text)
                logger.info(f"  🧮 计算结果: {math_text} = {result}")
                return str(result)
        except Exception as e:
            logger.debug(f"数学计算失败: {e}")
        
        logger.warning(f"⚠️ 未识别的验证问题: {question}")
        return None

    def get_equipment_with_session(self, url, max_retries=2):
        """使用建立好的Session获取装备数据"""
        for retry in range(max_retries):
            try:
                logger.info(f"🔍 获取装备数据 (尝试 {retry+1}): {url}")
                
                # 使用已建立的Session访问
                response = self.session.get(url, timeout=30)
                logger.info(f"页面状态码: {response.status_code}")
                
                # 即使是400也检查内容
                if response.status_code in [200, 400]:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    page_text = soup.get_text()
                    
                    # 检查是否仍需验证
                    needs_verification = any(keyword in page_text for keyword in 
                                           ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛'])
                    
                    if needs_verification and not self.verification_solved:
                        logger.info("🔐 页面需要验证，开始处理...")
                        if self.solve_verification_enhanced(response.text, url):
                            # 验证成功后重新访问
                            self.human_like_delay(2, 4)
                            response = self.session.get(url, timeout=30)
                            soup = BeautifulSoup(response.text, 'html.parser')
                        else:
                            logger.warning("验证处理失败")
                            continue
                    
                    # 查找装备链接
                    equipment_links = []
                    for link in soup.find_all('a', href=True):
                        href = link.get('href')
                        if href and 'view.php?eid=' in href:
                            if not href.startswith('http'):
                                href = urljoin(self.base_url, href)
                            equipment_links.append(href)
                    
                    equipment_links = list(set(equipment_links))
                    
                    if equipment_links:
                        logger.info(f"✅ 找到 {len(equipment_links)} 个装备链接")
                        return equipment_links
                    else:
                        # 检查页面是否正常
                        if soup.title and '中羽在线' in soup.title.string:
                            logger.warning("页面正常但无装备链接")
                        else:
                            logger.warning("页面异常")
                        
                        if retry < max_retries - 1:
                            self.human_like_delay(3, 5)
                            continue
                
            except Exception as e:
                logger.error(f"获取失败 (尝试 {retry+1}): {e}")
                if retry < max_retries - 1:
                    self.human_like_delay(3, 5)
                    continue
        
        return []

    def crawl_with_full_simulation(self, max_total_items=10):
        """完整模拟浏览器行为进行爬取 - 专注羽毛球拍"""
        logger.info("🚀 开始完整浏览器模拟爬取（专注羽毛球拍）...")
        
        # 1. 建立浏览器会话
        initial_content, status_code = self.simulate_browser_session()
        if not initial_content:
            logger.error("❌ 无法建立浏览器会话")
            return []
        
        # 2. 如果初始页面需要验证，先解决
        if status_code == 400 or any(keyword in initial_content for keyword in 
                                   ['验证', '问题', '计算', 'ZYZX', '羽毛球有几根毛']):
            logger.info("🔐 初始页面需要验证")
            equipment_url = f"{self.base_url}/cbo_eq/list.php"
            if not self.solve_verification_enhanced(initial_content, equipment_url):
                logger.error("❌ 初始验证失败")
                return []
        
        # 3. 收集所有装备链接 - 重点关注羽毛球拍页面
        all_equipment_links = []
        
        # 尝试羽毛球拍专用页面
        racket_urls = [
            f"{self.base_url}/cbo_eq/list.php?tid=1",  # 羽毛球拍
            f"{self.base_url}/cbo_eq/list.php",       # 通用页面
        ]
        
        for url in racket_urls:
            logger.info(f"🏸 尝试获取羽毛球拍链接: {url}")
            links = self.get_equipment_with_session(url)
            all_equipment_links.extend(links)
            self.human_like_delay(2, 4)
        
        # 去重
        all_equipment_links = list(set(all_equipment_links))
        logger.info(f"🔗 总共收集到 {len(all_equipment_links)} 个装备链接")
        
        if not all_equipment_links:
            logger.error("❌ 未找到任何装备链接")
            return []
        
        # 4. 解析装备详情 - 只保留羽毛球拍
        equipment_data = []
        racket_count = 0
        
        for i, link in enumerate(all_equipment_links):
            if racket_count >= max_total_items:
                break
                
            logger.info(f"📋 解析装备 ({i+1}/{len(all_equipment_links)})")
            
            detail_data = self.parse_equipment_detail_safe(link)
            if detail_data:
                # 只保留羽毛球拍数据
                if self.is_badminton_racket(detail_data):
                    # 对羽毛球拍数据进行专门的后处理
                    self.enhance_racket_data(detail_data)
                    equipment_data.append(detail_data)
                    racket_count += 1
                    logger.info(f"  ✅ 羽毛球拍 ({racket_count}): {detail_data['equipment_name']}")
                else:
                    logger.info(f"  ⏭️ 跳过非羽毛球拍: {detail_data.get('equipment_type', '未知类型')}")
            else:
                logger.warning(f"  ❌ 解析失败")
            
            self.human_like_delay(2, 4)  # 装备间延时
        
        logger.info(f"🎉 爬取完成！成功获取 {len(equipment_data)} 条羽毛球拍数据")
        return equipment_data

    def is_badminton_racket(self, equipment_data):
        """判断是否为羽毛球拍"""
        name = equipment_data.get('equipment_name', '').lower()
        eq_type = equipment_data.get('equipment_type', '').lower()
        
        # 明确的羽毛球拍标识
        racket_keywords = ['拍', 'racket', 'racquet']
        exclude_keywords = ['球', '鞋', '包', '袋', '线', '服', '衣', '胶', 'shuttle', 'shoes', 'bag', 'string', 'shirt']
        
        # 检查类型
        if '羽毛球拍' in eq_type:
            return True
        
        # 检查名称
        has_racket = any(keyword in name for keyword in racket_keywords)
        has_exclude = any(keyword in name for keyword in exclude_keywords)
        
        return has_racket and not has_exclude

    def enhance_racket_data(self, equipment_data):
        """对羽毛球拍数据进行专门的增强处理"""
        try:
            name = equipment_data.get('equipment_name', '')
            specs = equipment_data.get('specifications', '')
            
            # 从商品名称中提取更多信息
            self.extract_from_name(equipment_data, name)
            
            # 重新分析specifications以提取更多羽毛球拍特有信息
            self.extract_racket_specific_info(equipment_data, specs)
            
            # 数据验证和标准化
            self.validate_and_standardize_racket_data(equipment_data)
            
        except Exception as e:
            logger.error(f"羽毛球拍数据增强失败: {e}")

    def extract_from_name(self, equipment_data, name):
        """从商品名称中提取信息"""
        try:
            name_lower = name.lower()
            
            # 从名称提取重量信息
            if not equipment_data['weight']:
                weight_matches = re.findall(r'(\d+[UuGg])', name)
                if weight_matches:
                    equipment_data['weight'] = weight_matches[0].upper()
                    logger.debug(f"    📝 从名称提取重量: {equipment_data['weight']}")
            
            # 从名称提取系列信息
            if not equipment_data['equipment_series']:
                # 常见系列名称模式
                series_patterns = [
                    r'(雷霆|风动|神速|攻击|THRUSTER|AURASPEED|JETSPEED)',
                    r'(龙珠|龙|DRAGON)',
                    r'(超级丹|林丹|LINDAN)',
                    r'(弓箭|BOW|ARC)',
                ]
                for pattern in series_patterns:
                    match = re.search(pattern, name, re.IGNORECASE)
                    if match:
                        equipment_data['equipment_series'] = match.group(1)
                        logger.debug(f"    📝 从名称提取系列: {equipment_data['equipment_series']}")
                        break
                        
        except Exception as e:
            logger.debug(f"从名称提取信息失败: {e}")

    def extract_racket_specific_info(self, equipment_data, specs):
        """提取羽毛球拍特有信息"""
        try:
            # 提取手柄规格
            if not equipment_data['grip_size']:
                grip_patterns = [
                    r'手柄[：:]?\s*(G\d+)',
                    r'握把[：:]?\s*(G\d+)',
                    r'柄[：:]?\s*(G\d+)',
                ]
                for pattern in grip_patterns:
                    match = re.search(pattern, specs)
                    if match:
                        equipment_data['grip_size'] = match.group(1)
                        logger.debug(f"    🤲 提取手柄规格: {equipment_data['grip_size']}")
                        break
            
            # 提取长度信息
            if not equipment_data['length']:
                length_patterns = [
                    r'长度[：:]?\s*(\d+mm?)',
                    r'拍长[：:]?\s*(\d+mm?)',
                    r'(\d+mm).*?长',
                ]
                for pattern in length_patterns:
                    match = re.search(pattern, specs)
                    if match:
                        equipment_data['length'] = match.group(1)
                        logger.debug(f"    📏 提取长度: {equipment_data['length']}")
                        break
                        
        except Exception as e:
            logger.debug(f"提取羽毛球拍特有信息失败: {e}")

    def validate_and_standardize_racket_data(self, equipment_data):
        """验证并标准化羽毛球拍数据"""
        try:
            # 标准化重量格式
            weight = equipment_data.get('weight', '')
            if weight:
                # 统一为大写U格式
                if weight.lower().endswith('u'):
                    equipment_data['weight'] = weight.upper()
                elif weight.endswith('g') or weight.endswith('G'):
                    # 尝试转换g为U
                    weight_num = re.search(r'(\d+)', weight)
                    if weight_num:
                        gram = int(weight_num.group(1))
                        if 75 <= gram <= 79:
                            equipment_data['weight'] = '5U'
                        elif 80 <= gram <= 84:
                            equipment_data['weight'] = '4U'
                        elif 85 <= gram <= 89:
                            equipment_data['weight'] = '3U'
                        elif 90 <= gram <= 94:
                            equipment_data['weight'] = '2U'
                        elif gram >= 95:
                            equipment_data['weight'] = '1U'
            
            # 标准化平衡点描述
            balance = equipment_data.get('balance_point', '')
            if balance:
                if '头重' in balance or '进攻' in balance:
                    equipment_data['balance_point'] = '头重'
                elif '头轻' in balance or '防守' in balance:
                    equipment_data['balance_point'] = '头轻'
                elif '平衡' in balance or '均衡' in balance:
                    equipment_data['balance_point'] = '平衡'
            
            # 标准化中管硬度
            stiffness = equipment_data.get('shaft_stiffness', '')
            if stiffness:
                if '硬' in stiffness:
                    equipment_data['shaft_stiffness'] = '偏硬'
                elif '软' in stiffness:
                    equipment_data['shaft_stiffness'] = '偏软'
                elif '适中' in stiffness or '中等' in stiffness:
                    equipment_data['shaft_stiffness'] = '适中'
                    
        except Exception as e:
            logger.debug(f"数据验证和标准化失败: {e}")

    def parse_equipment_detail_safe(self, url):
        """安全解析装备详情"""
        try:
            response = self.session.get(url, timeout=30)
            
            if response.status_code not in [200, 400]:
                return None
            
            soup = BeautifulSoup(response.text, 'html.parser')
            page_text = soup.get_text()
            
            # 检查验证
            if any(keyword in page_text for keyword in ['验证', '问题', '计算']):
                logger.debug("  详情页需要验证，跳过")
                return None
            
            # 提取装备ID
            eid_match = re.search(r'eid=(\d+)', url)
            equipment_id = eid_match.group(1) if eid_match else 'unknown'
            
            # 初始化数据
            equipment_data = {
                'equipment_id': equipment_id,
                'equipment_name': '',
                'equipment_type': '',
                'equipment_brand': '',
                'equipment_series': '',
                'equipment_description': '',
                'release_date': '',
                'equipment_introduction': '',
                'specifications': '',
                'frame_material': '',
                'shaft_material': '',
                'weight': '',
                'length': '',
                'grip_size': '',
                'shaft_stiffness': '',
                'string_tension': '',
                'balance_point': '',
                'new_avg_price': '',
                'used_avg_price': '',
                'total_registered_users': '',
                'rating_score': '',
                'rating_distribution': '',
                'user_reviews_count': '',
                'detail_url': url,
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 提取标题
            if soup.title:
                title = soup.title.string.strip()
                equipment_name = title.replace('中羽在线 badmintoncn.com', '').strip()
                if equipment_name:
                    equipment_data['equipment_name'] = equipment_name[:100]
            
            # 提取表格信息
            self.extract_table_data(soup, equipment_data)
            
            # 智能推断缺失字段
            self.infer_missing_fields(equipment_data)
            
            return equipment_data
            
        except Exception as e:
            logger.error(f"解析装备详情失败: {e}")
            return None

    def extract_table_data(self, soup, equipment_data):
        """增强版表格数据提取"""
        try:
            tables = soup.find_all('table')
            logger.debug(f"  找到 {len(tables)} 个表格")
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        # 更全面的字段映射
                        field_mappings = {
                            # 基础信息
                            '装备类型': 'equipment_type', '类型': 'equipment_type',
                            '装备品牌': 'equipment_brand', '品牌': 'equipment_brand', '制造商': 'equipment_brand',
                            '装备系列': 'equipment_series', '系列': 'equipment_series', '产品系列': 'equipment_series',
                            '上市日期': 'release_date', '发布日期': 'release_date', '发售时间': 'release_date',
                            
                            # 羽毛球拍技术规格
                            '拍框材质': 'frame_material', '框架材质': 'frame_material', '拍头材质': 'frame_material',
                            '拍杆材质': 'shaft_material', '中管材质': 'shaft_material', '拍柄材质': 'shaft_material',
                            '重量': 'weight', '拍重': 'weight', '拍身重量': 'weight', '净重': 'weight',
                            '长度': 'length', '拍身长度': 'length', '总长度': 'length',
                            '手柄尺寸': 'grip_size', '拍柄粗细': 'grip_size', '握把大小': 'grip_size',
                            '中管韧度': 'shaft_stiffness', '硬度': 'shaft_stiffness', '中管硬度': 'shaft_stiffness',
                            '拉线磅数': 'string_tension', '穿线磅数': 'string_tension', '建议拉力': 'string_tension',
                            '平衡点': 'balance_point', '重心': 'balance_point', '平衡': 'balance_point',
                            
                            # 价格信息
                            '最近全新均价': 'new_avg_price', '全新均价': 'new_avg_price', '新品价格': 'new_avg_price',
                            '最近二手均价': 'used_avg_price', '二手均价': 'used_avg_price', '二手价格': 'used_avg_price',
                            '总登记球友': 'total_registered_users', '登记球友': 'total_registered_users', '用户数': 'total_registered_users',
                        }
                        
                        # 尝试映射字段
                        for keyword, field in field_mappings.items():
                            if keyword in key and not equipment_data[field]:
                                cleaned_value = self.clean_field_value(value, field)
                                if cleaned_value:
                                    equipment_data[field] = cleaned_value
                                    logger.debug(f"    ✅ 映射: {keyword} -> {cleaned_value}")
                                break
                        
                        # 收集所有规格参数
                        if key and value and key not in ['', ' ']:
                            if equipment_data['specifications']:
                                equipment_data['specifications'] += f"; {key}: {value}"
                            else:
                                equipment_data['specifications'] = f"{key}: {value}"
                                
        except Exception as e:
            logger.error(f"增强表格数据提取失败: {e}")
            
        # 从specifications字段中提取缺失信息
        self.extract_from_specifications(equipment_data)

    def clean_field_value(self, value, field_type):
        """清理字段值"""
        if not value or value.strip() == '':
            return ''
        
        cleaned = value.strip()
        
        # 特定字段的清理规则
        if field_type in ['weight']:
            # 保留数字和单位
            weight_match = re.search(r'(\d+[UuGg]?)', cleaned)
            if weight_match:
                return weight_match.group(1)
        elif field_type in ['string_tension']:
            # 提取磅数
            tension_match = re.search(r'(\d+[-~]\d*)', cleaned)
            if tension_match:
                return tension_match.group(1) + '磅'
        elif field_type in ['balance_point']:
            # 提取平衡点数值
            balance_match = re.search(r'(\d+mm?|头重|头轻|平衡)', cleaned)
            if balance_match:
                return balance_match.group(1)
        
        return cleaned[:50]  # 限制长度

    def extract_from_specifications(self, equipment_data):
        """从specifications字段中提取缺失信息 - 增强版"""
        try:
            specs = equipment_data.get('specifications', '')
            if not specs:
                return
            
            # 增强价格提取 - 从多个商店价格中提取
            if not equipment_data['new_avg_price']:
                # 提取所有价格信息
                price_patterns = [
                    r'￥(\d+)起',  # ￥1782起
                    r'(\d+)元',     # 1000元
                    r'价格[：:]?\s*(\d+)',
                    r'单价[：:]?\s*(\d+)',
                ]
                
                all_prices = []
                for pattern in price_patterns:
                    matches = re.findall(pattern, specs)
                    for match in matches:
                        try:
                            price = int(match)
                            if 50 <= price <= 10000:  # 合理价格范围
                                all_prices.append(price)
                        except:
                            continue
                
                if all_prices:
                    # 取平均价格
                    avg_price = sum(all_prices) // len(all_prices)
                    equipment_data['new_avg_price'] = str(avg_price)
                    logger.debug(f"    💰 从多个价格计算平均值: {avg_price} (共{len(all_prices)}个价格)")
            
            # 增强重量提取
            if not equipment_data['weight']:
                weight_patterns = [
                    r'(\d+[Uu])',  # 3U, 4U
                    r'(\d+[gG])',  # 85g, 90g
                    r'重量[：:]\s*(\d+[UuGg]?)',
                    r'拍重[：:]\s*(\d+[UuGg]?)',
                    r'重.*?(\d+[UuGg])',
                    r'(?:买的是|用的是|拿的是)\s*(\d+[Uu])',  # 从评论中提取
                ]
                for pattern in weight_patterns:
                    match = re.search(pattern, specs)
                    if match:
                        weight = match.group(1)
                        # 标准化U格式
                        if weight.lower().endswith('u'):
                            equipment_data['weight'] = weight.upper()
                        else:
                            equipment_data['weight'] = weight
                        logger.debug(f"    ⚖️ 从specs提取重量: {equipment_data['weight']}")
                        break
            
            # 增强平衡点提取
            if not equipment_data['balance_point']:
                balance_patterns = [
                    r'平衡点[：:]\s*(\d+mm?)',
                    r'重心[：:]\s*(\d+mm?)',
                    r'(头重|头轻|平衡)',
                    r'(\d+mm).*?平衡',
                    r'头重感.*?(明显|很强|较强)',  # 从评论中提取头重描述
                    r'(偏头重|偏头轻)',
                    r'头.*?(重|轻).*?感',
                ]
                for pattern in balance_patterns:
                    match = re.search(pattern, specs)
                    if match:
                        balance = match.group(1)
                        if '明显' in balance or '很强' in balance or '较强' in balance:
                            equipment_data['balance_point'] = '头重'
                        else:
                            equipment_data['balance_point'] = balance
                        logger.debug(f"    ⚖️ 从specs提取平衡点: {equipment_data['balance_point']}")
                        break
            
            # 大幅增强中管硬度提取
            if not equipment_data['shaft_stiffness']:
                stiffness_patterns = [
                    r'中管[：:]?\s*(偏?[硬软]|适中|弹性)',
                    r'硬度[：:]?\s*(偏?[硬软]|适中)',
                    r'韧度[：:]?\s*(偏?[硬软]|适中)',
                    r'中杆[：:]?\s*(偏?[硬软]|适中)',
                    r'杆[：:]?\s*(偏?[硬软]|适中)',
                    r'(中杆偏硬|中杆偏软|中杆适中)',
                    r'感觉.*?中杆.*?(硬|软)',
                    r'中杆.*?(会有些晃动)',  # 软的特征
                    r'传导.*?(不错|很好)',   # 硬的特征  
                    r'中感.*?(硬|软|适中)',
                    r'弹.*?(性好|性强)',     # 弹性好通常是适中偏软
                ]
                
                for pattern in stiffness_patterns:
                    match = re.search(pattern, specs)
                    if match:
                        stiffness_text = match.group(1) if match.groups() else match.group(0)
                        
                        # 规范化描述
                        if '晃动' in stiffness_text:
                            equipment_data['shaft_stiffness'] = '偏软'
                        elif '传导' in stiffness_text and ('不错' in stiffness_text or '很好' in stiffness_text):
                            equipment_data['shaft_stiffness'] = '偏硬'
                        elif '弹性' in stiffness_text:
                            equipment_data['shaft_stiffness'] = '适中'
                        else:
                            equipment_data['shaft_stiffness'] = stiffness_text
                            
                        logger.debug(f"    🏹 从specs提取中管硬度: {equipment_data['shaft_stiffness']}")
                        break
            
            # 增强拉线磅数提取
            if not equipment_data['string_tension']:
                tension_patterns = [
                    r'(\d+[-~]\d+).*?磅',
                    r'(\d+[-~]\d+).*?[Ll][Bb][Ss]',
                    r'拉线[：:]?\s*(\d+[-~]\d+)',
                    r'穿线[：:]?\s*(\d+[-~]\d+)',
                    r'(\d+)磅',  # 单一磅数
                    r'线.*?(\d+[-~]\d+)',
                    r'拉的.*?(\d+[-~]\d+)',
                ]
                for pattern in tension_patterns:
                    match = re.search(pattern, specs)
                    if match:
                        tension = match.group(1)
                        equipment_data['string_tension'] = tension + ('磅' if not tension.endswith('磅') else '')
                        logger.debug(f"    🎾 从specs提取拉线磅数: {equipment_data['string_tension']}")
                        break
            
            # 增强材料提取
            if not equipment_data['frame_material']:
                frame_patterns = [
                    r'拍框[：:]?\s*([^；;，,。]+)',
                    r'框[：:]?\s*([碳纤维|铝合金|钛合金|复合材料]+)',
                    r'(碳纤维|铝合金|钛合金).*?框',
                ]
                for pattern in frame_patterns:
                    match = re.search(pattern, specs)
                    if match:
                        equipment_data['frame_material'] = match.group(1).strip()
                        logger.debug(f"    🏗️ 从specs提取框架材料: {equipment_data['frame_material']}")
                        break
            
            if not equipment_data['shaft_material']:
                shaft_patterns = [
                    r'中杆[：:]?\s*([^；;，,。]+)',
                    r'杆身[：:]?\s*([^；;，,。]+)',
                    r'(碳纤维|铝合金|钛合金).*?杆',
                ]
                for pattern in shaft_patterns:
                    match = re.search(pattern, specs)
                    if match:
                        equipment_data['shaft_material'] = match.group(1).strip()
                        logger.debug(f"    🏗️ 从specs提取中杆材料: {equipment_data['shaft_material']}")
                        break
            
            # 提取评分信息
            if not equipment_data['rating_score']:
                rating_pattern = r'(\d+\.?\d*)中羽评分'
                rating_match = re.search(rating_pattern, specs)
                if rating_match:
                    equipment_data['rating_score'] = rating_match.group(1)
                    logger.debug(f"    ⭐ 从specs提取评分: {rating_match.group(1)}")
            
            # 提取评分分布
            if not equipment_data['rating_distribution']:
                distribution_pattern = r'(5★\d+\.?\d*%.*?1★\d+\.?\d*%)'
                distribution_match = re.search(distribution_pattern, specs)
                if distribution_match:
                    equipment_data['rating_distribution'] = distribution_match.group(1)
                    logger.debug(f"    📊 从specs提取评分分布")
            
            # 统计用户评价数量
            if not equipment_data['user_reviews_count']:
                review_count = len(re.findall(r'Ta说', specs))
                if review_count > 0:
                    equipment_data['user_reviews_count'] = str(review_count)
                    logger.debug(f"    💬 从specs统计评价数量: {review_count}")
                        
        except Exception as e:
            logger.error(f"从specifications提取信息失败: {e}")

    def infer_missing_fields(self, equipment_data):
        """智能推断缺失字段"""
        try:
            # 从装备名称推断类型和品牌
            name = equipment_data.get('equipment_name', '').lower()
            
            # 推断装备类型
            if not equipment_data['equipment_type']:
                type_keywords = {
                    '羽毛球拍': ['拍', 'racket', 'racquet'],
                    '羽毛球鞋': ['鞋', 'shoes', 'shoe'],
                    '运动包': ['包', 'bag', '袋'],
                    '羽毛球线': ['线', 'string', '弦'],
                    '羽毛球': ['球', 'shuttle', 'cock'],
                    '运动服饰': ['服', '衣', 'shirt', 'wear'],
                    '手胶': ['胶', 'grip', '柄皮']
                }
                
                for eq_type, keywords in type_keywords.items():
                    if any(keyword in name for keyword in keywords):
                        equipment_data['equipment_type'] = eq_type
                        logger.debug(f"    🤖 推断类型: {eq_type}")
                        break
            
            # 推断品牌
            if not equipment_data['equipment_brand']:
                brand_keywords = {
                    '尤尼克斯 YONEX': ['yonex', '尤尼克斯', 'yy'],
                    '李宁 Lining': ['lining', '李宁', 'li-ning'],
                    '胜利 VICTOR': ['victor', '胜利'],
                    '川崎 Kawasaki': ['kawasaki', '川崎'],
                    '波力 Bonny': ['bonny', '波力'],
                    '凯胜 Kason': ['kason', '凯胜'],
                }
                
                for brand, keywords in brand_keywords.items():
                    if any(keyword in name for keyword in keywords):
                        equipment_data['equipment_brand'] = brand
                        logger.debug(f"    🤖 推断品牌: {brand}")
                        break
                        
        except Exception as e:
            logger.debug(f"智能推断失败: {e}")

    def save_data(self, data):
        """保存数据"""
        if not data:
            logger.warning("没有数据需要保存")
            return
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # CSV文件
        csv_file = f"output/ultimate_equipment_{timestamp}.csv"
        fieldnames = list(data[0].keys())
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        # JSON文件
        json_file = f"output/ultimate_equipment_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📁 数据已保存:")
        logger.info(f"  CSV: {csv_file}")
        logger.info(f"  JSON: {json_file}")
        
        return csv_file, json_file

    def analyze_data_completeness(self, data):
        """分析数据完整性"""
        if not data:
            return
            
        logger.info("\n📊 数据完整性分析:")
        logger.info(f"总数量: {len(data)}")
        
        # 分析每个字段的填充率
        field_stats = {}
        for item in data:
            for field, value in item.items():
                if field not in field_stats:
                    field_stats[field] = {'filled': 0, 'empty': 0}
                
                if value and value != '':
                    field_stats[field]['filled'] += 1
                else:
                    field_stats[field]['empty'] += 1
        
        logger.info("\n字段完整性:")
        important_fields = ['equipment_name', 'equipment_type', 'equipment_brand', 'weight', 
                          'shaft_stiffness', 'balance_point', 'new_avg_price', 'rating_score']
        
        for field in important_fields:
            if field in field_stats:
                stats = field_stats[field]
                total = stats['filled'] + stats['empty']
                fill_rate = (stats['filled'] / total) * 100 if total > 0 else 0
                logger.info(f"  {field}: {fill_rate:.1f}% ({stats['filled']}/{total})")
        
        return field_stats

    def analyze_data(self, data):
        """分析数据"""
        if not data:
            return
            
        logger.info("\n📊 数据分析:")
        logger.info(f"总数量: {len(data)}")
        
        # 统计分析
        types = {}
        brands = {}
        
        for item in data:
            eq_type = item.get('equipment_type', '未知')
            types[eq_type] = types.get(eq_type, 0) + 1
            
            brand = item.get('equipment_brand', '未知')
            brands[brand] = brands.get(brand, 0) + 1
        
        if types:
            logger.info("\n类型分布:")
            for eq_type, count in sorted(types.items(), key=lambda x: x[1], reverse=True):
                logger.info(f"  {eq_type}: {count}")
        
        if brands:
            logger.info("\n品牌分布 (前5):")
            for brand, count in sorted(brands.items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.info(f"  {brand}: {count}")

def main():
    """主函数"""
    crawler = UltimateBadmintonCrawler()
    
    # 执行爬取
    data = crawler.crawl_with_full_simulation(max_total_items=8)
    
    if data:
        # 保存和分析数据
        crawler.save_data(data)
        crawler.analyze_data_completeness(data)
        crawler.analyze_data(data)
        
        logger.info(f"\n🎊 增强版成功完成！获取了 {len(data)} 条羽毛球装备数据")
    else:
        logger.error("❌ 未获取到任何数据")

if __name__ == "__main__":
    main() 