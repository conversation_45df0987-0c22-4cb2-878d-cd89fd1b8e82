import numpy as np
from biosppy import utils
from biosppy.signals import ecg


def p_data_completion(p_peaks, p_onset_peaks, p_offset_peaks):
    """
    P波数据补全
    :param p_peaks: P波集合
    :param p_onset_peaks: P波起始点集合
    :param p_offset_peaks: P波结束点集合
    :return: 补全后的P波起始点集合，补全后的P波结束点集合
    """
    # 初始化两个列表来存储补齐后的 onset 和 offset 峰值
    p_onset_peaks_filled = np.array([], dtype=float)
    p_offset_peaks_filled = np.array([], dtype=float)

    # 遍历 p_peaks 数组
    for i, peak in enumerate(p_peaks):
        onset_idx = np.where(p_onset_peaks < peak)[0]
        if onset_idx.size > 0:
            p_onset_peaks_filled = np.append(p_onset_peaks_filled, p_onset_peaks[0])
            p_onset_peaks = np.delete(p_onset_peaks, 0)
        else:
            p_onset_peaks_filled = np.append(p_onset_peaks_filled, np.nan)

        offset_idx = np.where(p_offset_peaks > peak)[0]
        if offset_idx.size > 0:
            if i < len(p_peaks) - 1:
                offset_value = p_offset_peaks[0]
                if offset_value < p_peaks[i + 1]:
                    p_offset_peaks_filled = np.append(p_offset_peaks_filled, p_offset_peaks[0])
                    p_offset_peaks = np.delete(p_offset_peaks, 0)
                else:
                    p_offset_peaks_filled = np.append(p_offset_peaks_filled, np.nan)
            else:
                p_offset_peaks_filled = np.append(p_offset_peaks_filled, p_offset_peaks[0])
                p_offset_peaks = np.delete(p_offset_peaks, 0)
        else:
            p_offset_peaks_filled = np.append(p_offset_peaks_filled, np.nan)

    return p_onset_peaks_filled, p_offset_peaks_filled


def q_data_completion(q_peaks, q_onset_peaks):
    """
    Q波数据补全
    :param q_peaks: Q波集合
    :param q_onset_peaks: P波起始点集合
    :return: 补全后的Q波起始点集合
    """
    # 初始化两个列表来存储补齐后的 onset 和 offset 峰值
    q_onset_peaks_filled = np.array([], dtype=float)

    # 遍历 p_peaks 数组
    for i, qeak in enumerate(q_peaks):
        onset_idx = np.where(q_onset_peaks < qeak)[0]
        if onset_idx.size > 0:
            q_onset_peaks_filled = np.append(q_onset_peaks_filled, q_onset_peaks[0])
            q_onset_peaks = np.delete(q_onset_peaks, 0)
        else:
            q_onset_peaks_filled = np.append(q_onset_peaks_filled, np.nan)

    return q_onset_peaks_filled


def s_data_completion(s_peaks, s_offset_peaks):
    """
    S波数据补全
    :param s_peaks: S波集合
    :param s_offset_peaks: S波终点集合
    :return: 补全后的S波终点集合
    """
    # 初始化列表来存储补齐后的终点峰值
    s_offset_peaks_filled = np.array([], dtype=float)

    # 遍历 s_peaks 数组
    for i, peak in enumerate(s_peaks):
        # 找到当前峰值点之前最近的终点峰值
        idx = np.searchsorted(s_offset_peaks, peak, side='left')

        # 如果找到了终点峰值，并且它确实在当前峰值点之前
        if idx > 0 and s_offset_peaks[idx - 1] < peak:
            s_offset_peaks_filled = np.append(s_offset_peaks_filled, s_offset_peaks[idx - 1])
        else:
            # 如果没有找到合适的终点峰值，添加NaN
            s_offset_peaks_filled = np.append(s_offset_peaks_filled, np.nan)

    return s_offset_peaks_filled


def proccess(ecg_signal, sampling_rate):
    """
    执行波形分析
    :param ecg_signal: ndarray，心电信号
    :param sampling_rate: int，采样率
    :return: 字典对象
        'r_peaks': ndarray，R波峰值集合
        'rr_intervals': ndarray，RR间期
        's_peaks': ndarray，S波峰值集合,
        'q_peaks': ndarray，Q波峰值集合,
        'p_peaks': ndarray，P波峰值集合,
        'qrs_intervals': ndarray，QRS间期,
    """
    ecg_signal = np.array(ecg_signal)

    info = {}

    # 查找R波峰值
    ecg_proc = ecg.ecg(ecg_signal, sampling_rate=sampling_rate, show=False)

    info['heart_rate'] = ecg_proc['heart_rate']

    r_peaks = np.array(ecg_proc['rpeaks'])

    info['r_peaks'] = r_peaks
    info['rr_intervals'] = r_peaks[1:] - r_peaks[:-1]
    info['rr_intervals_value'] = info['rr_intervals'] / sampling_rate
    info['r_amplitude'] = ecg_signal[r_peaks]

    p_info = ecg.getPPositions(ecg_proc)
    p_peaks = np.array(p_info['P_positions'])
    info['p_peaks'] = p_peaks
    info['pp_intervals'] = p_peaks[1:] - p_peaks[:-1]
    info['pp_intervals_value'] = info['pp_intervals'] / sampling_rate
    p_onset_peaks = np.array(p_info['P_start_positions'])
    p_offset_peaks = np.array(p_info['P_end_positions'])
    p_onset_peaks, p_offset_peaks = p_data_completion(p_peaks, p_onset_peaks, p_offset_peaks)

    info['p_onset_peaks'] = p_onset_peaks
    info['p_offset_peaks'] = p_offset_peaks

    try:
        q_info = ecg.getQPositions(ecg_proc)
    except:
        q_info = utils.ReturnTuple(([], [],), ("Q_positions", "Q_start_positions",))

    if len(q_info['Q_positions']) > 0:
        q_peaks = np.array(q_info['Q_positions'])
        info['q_peaks'] = q_peaks

        q_start_peaks = np.array(q_info['Q_start_positions'])
        q_start_peaks = q_start_peaks
    else:
        info['q_peaks'] = r_peaks
        q_start_peaks = r_peaks

    if info['q_peaks'].size != q_start_peaks.size:
        q_start_peaks = q_data_completion(info['q_peaks'], q_start_peaks)

    info['q_start_peaks'] = q_start_peaks

    s_info = ecg.getSPositions(ecg_proc)
    info['s_peaks'] = np.array(s_info['S_positions'])
    s_end_positions = np.array(s_info['S_end_positions'])

    if info['s_peaks'].size != s_end_positions.size:
        s_end_positions = s_data_completion(info['s_peaks'], s_end_positions)

    info['s_end_peaks'] = s_end_positions

    info['qrs_durations'] = (s_end_positions - q_start_peaks) / sampling_rate

    t_info = ecg.getTPositions(ecg_proc)
    t_peaks = np.array(t_info['T_positions'])
    t_start_peaks = np.array(t_info['T_start_positions'])
    t_end_peaks = np.array(t_info['T_end_positions'])

    info['t_peaks'] = t_peaks
    info['t_start_peaks'] = t_start_peaks
    info['t_end_peaks'] = t_end_peaks
    info['t_amplitude'] = ecg_signal[t_peaks]

    # 计算PR间期
    pr_intervals = np.array([])

    for i, p_peak in enumerate(p_peaks):
        r_peak = r_peaks[i]
        p_onset_peak = p_onset_peaks[i]

        pr_intervals = np.append(pr_intervals, r_peak - (p_peak if np.isnan(p_onset_peak) else p_onset_peak))

    pr_intervals_value = pr_intervals / sampling_rate

    info['pr_intervals'] = pr_intervals
    info['pr_intervals_value'] = pr_intervals_value

    return info
