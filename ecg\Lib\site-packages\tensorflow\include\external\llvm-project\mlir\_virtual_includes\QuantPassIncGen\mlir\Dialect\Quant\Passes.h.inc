/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// QuantConvertConst
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class QuantConvertConstBase : public ::mlir::FunctionPass {
public:
  using Base = QuantConvertConstBase;

  QuantConvertConstBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  QuantConvertConstBase(const QuantConvertConstBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("quant-convert-const");
  }
  ::llvm::StringRef getArgument() const override { return "quant-convert-const"; }

  ::llvm::StringRef getDescription() const override { return "Converts constants followed by qbarrier to actual quantized values"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("QuantConvertConst");
  }
  ::llvm::StringRef getName() const override { return "QuantConvertConst"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// QuantConvertSimulatedQuant
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class QuantConvertSimulatedQuantBase : public ::mlir::FunctionPass {
public:
  using Base = QuantConvertSimulatedQuantBase;

  QuantConvertSimulatedQuantBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  QuantConvertSimulatedQuantBase(const QuantConvertSimulatedQuantBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("quant-convert-simulated-quantization");
  }
  ::llvm::StringRef getArgument() const override { return "quant-convert-simulated-quantization"; }

  ::llvm::StringRef getDescription() const override { return "Converts training-time simulated quantization ops to corresponding quantize/dequantize casts"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("QuantConvertSimulatedQuant");
  }
  ::llvm::StringRef getName() const override { return "QuantConvertSimulatedQuant"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// QuantConvertConst Registration
//===----------------------------------------------------------------------===//

inline void registerQuantConvertConstPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quant::createConvertConstPass();
  });
}

//===----------------------------------------------------------------------===//
// QuantConvertSimulatedQuant Registration
//===----------------------------------------------------------------------===//

inline void registerQuantConvertSimulatedQuantPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quant::createConvertSimulatedQuantPass();
  });
}

//===----------------------------------------------------------------------===//
// Quant Registration
//===----------------------------------------------------------------------===//

inline void registerQuantPasses() {
  registerQuantConvertConstPass();
  registerQuantConvertSimulatedQuantPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
