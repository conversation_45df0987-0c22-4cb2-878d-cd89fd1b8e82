import json
import os
import traceback
import requests
from qiniu import Auth
import pandas as pd
import glob
import urllib3
import concurrent.futures
from tqdm import tqdm

# 禁用因verify=False而产生的InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 直接在文件中定义七牛云配置，不再从global_settings导入
QINIU = {
    'test': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://test.upload.weiheyixue.com'
    },
    'prod': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://upload.weiheyixue.com'
    }
}

# --- 配置 ---
INPUT_DIR = os.path.join('qiniu_query_results', '平直线')
BASE_OUTPUT_DIR = 'qiniu_query_results'
ENVIRONMENT = 'prod'  # 根据需要修改为'test'
MAX_WORKERS = 50 # 并发下载的线程数，可根据网络情况调整
REQUEST_TIMEOUT = 10 # 请求超时时间（秒）

# --- 单一完整查询模式配置 ---
RUN_SINGLE_FULL_QUERY = True  # 设置为True以运行单一查询模式，否则运行批量模式
SINGLE_ES_KEY_TO_QUERY = "CUSTOMER18838092628219043846502/20250726213114" # 替换为您想查询的es_key
SINGLE_JSON_OUTPUT_PATH = "D:\Project\qiniu_query_results\低电压异常数据\肖总0726异常数据.json" # 完整数据输出文件名

# --- 指定文件模式配置 ---
RUN_SPECIFIC_FILES_MODE = False  # 设置为True以处理指定的CSV文件
SPECIFIC_FILES_TO_PROCESS = [
    "D:\\ECG\\0623运动状态\\肖总噪音数据\\异常状态es.csv"
]
USE_ORIGINAL_ES_KEY_AS_FILENAME = True  # 设置为True以使用原始es_key作为文件名

# --- 导联选择模式配置 ---
RUN_LEAD_SELECTION_MODE = False  # 设置为True以根据Excel文件中的lead列选择导联
EXCEL_FILE_PATH = "D:\\ECG\\0723一分钟项目测试\\标注平台数据\\无标题.xls"
OUTPUT_DIR_FOR_LEAD_DATA = "D:\\ECG\\0723一分钟项目测试\\标注平台数据"

# 从qiniu_helper.py复制过来的函数
def get_qiniu_data(file_path, environment):
    """获取七牛云数据，快速失败策略"""
    try:
        access_key = QINIU[environment]['access_key']
        secret_key = QINIU[environment]['secret_key']
        domain_prefix = QINIU[environment]['domain_prefix']  # 存储空间域名前缀
        # 构建鉴权对象
        q = Auth(access_key, secret_key)

        # 构建私有空间的下载链接
        private_url = q.private_download_url(domain_prefix + '/ecg/' + file_path)

        # 使用requests库下载文件，并禁用SSL验证、设置超时
        response = requests.get(private_url, verify=False, timeout=REQUEST_TIMEOUT)

        if response.status_code == 200:
            # 确保返回原始的完整JSON数据
            return json.loads(response.content)
        else:
            # 对于非200状态码，不打印警告以保持控制台清洁，直接返回None
            return None

    except requests.exceptions.RequestException:
        # 对于任何网络层面的异常（超时、连接错误等），静默失败，不打印信息
        return None

def query_and_save_full_json(es_key, output_path):
    """查询单个es_key的完整数据并保存为JSON文件。"""
    print(f"\n--- 单一查询模式 ---")
    print(f"正在查询es_key: {es_key}")
    
    data = get_qiniu_data(es_key, ENVIRONMENT)
    
    if data:
        # 输出数据信息，以便诊断
        data_type = type(data).__name__
        if isinstance(data, dict):
            keys = list(data.keys())
            print(f"[诊断] 获取到的数据类型: {data_type}，包含 {len(keys)} 个键")
            print(f"[诊断] 数据键列表: {', '.join(keys)}")
        else:
            print(f"[诊断] 获取到的数据类型: {data_type}，非字典类型")
        
        try:
            print(f"[诊断] 正在将完整数据保存到: {output_path}")
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"[成功] 已将完整数据保存到: {output_path}")
        except Exception as e:
            print(f"[错误] 保存文件失败: {e}")
    else:
        print(f"[失败] 未能获取到es_key '{es_key}' 的数据。")

def save_single_result(filename_base, data, output_dir):
    """
    保存单个查询结果中'ecg'导联的数据到txt文件。
    使用CSV中的'id'列作为文件名。
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查data是否为有效字典且包含'ecg'键
    if not isinstance(data, dict) or 'ecg' not in data:
        print(f"  - 数据格式无效或缺少'ecg'键，无法保存 (关联文件名: {filename_base}.txt)")
        return None
        
    ecg_data = data['ecg']
    
    # 构建文件名 e.g., "ECG-01-01.txt"
    safe_filename = str(filename_base) + ".txt"
    filepath = os.path.join(output_dir, safe_filename)
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            # 如果ecg_data是列表，每项占一行；否则直接写入
            if isinstance(ecg_data, list):
                f.write('\n'.join(map(str, ecg_data)))
            else:
                f.write(str(ecg_data))
        print(f"  - 已保存ECG(I)导联数据: {filepath}")
        return safe_filename
    except Exception as e:
        print(f"  - 保存失败: {filepath}，原因: {e}")
        return None

def save_full_json_result(filename_base, data, output_dir):
    """
    保存单个查询结果的完整JSON数据。
    使用与save_single_result相同的文件命名格式，但扩展名为.json。
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查data是否有效
    if not isinstance(data, dict):
        print(f"  - 数据格式无效，无法保存 (关联文件名: {filename_base}.json)")
        return None

    # 构建文件名 e.g., "ECG-01-01.json"
    safe_filename = str(filename_base) + ".json"
    filepath = os.path.join(output_dir, safe_filename)
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"  - 已保存完整JSON数据: {filepath}")
        return safe_filename
    except Exception as e:
        print(f"  - 保存失败: {filepath}，原因: {e}")
        return None

def save_full_json_with_original_name(es_key, data, output_dir):
    """
    保存单个查询结果的完整JSON数据，使用原始es_key作为文件名。
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查data是否有效
    if not isinstance(data, dict):
        print(f"  - 数据格式无效，无法保存 (es_key: {es_key})")
        return None
    
    # 将es_key处理成安全的文件名
    safe_filename = es_key.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_') + ".json"
    filepath = os.path.join(output_dir, safe_filename)
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"  - 已保存完整JSON数据: {filepath}")
        return safe_filename
    except Exception as e:
        print(f"  - 保存失败: {filepath}，原因: {e}")
        return None

# --- 导联选择模式函数 ---
def read_excel_with_lead_info(excel_path):
    """
    读取Excel文件，提取es_key和lead列信息。
    返回一个包含(es_key, lead)元组的列表。
    """
    try:
        # 尝试读取Excel文件
        df = pd.read_excel(excel_path)
        
        # 检查必要的列是否存在
        if 'es_key' not in df.columns:
            print(f"[错误] Excel文件 {excel_path} 中未找到 'es_key' 列。")
            return []
        
        if 'lead' not in df.columns:
            print(f"[错误] Excel文件 {excel_path} 中未找到 'lead' 列。")
            return []
        
        # 提取es_key和lead列，并删除空值行
        valid_data = df[['es_key', 'lead']].dropna()
        
        # 转换为元组列表
        result = [(row['es_key'], row['lead']) for _, row in valid_data.iterrows()]
        
        print(f"从Excel文件中读取到 {len(result)} 条有效的es_key-lead对。")
        return result
    
    except Exception as e:
        print(f"[错误] 读取Excel文件 {excel_path} 失败: {e}")
        return []

def save_lead_data(es_key, data, lead, output_dir):
    """
    根据lead参数选择相应的导联数据并保存。
    lead: "I" 或 "II"，表示要保存的导联
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查data是否为有效字典
    if not isinstance(data, dict):
        print(f"  - 数据格式无效，无法保存 (es_key: {es_key})")
        return None
    
    # 根据lead选择相应的数据字段
    lead_field = None
    if lead == "I" and "ecg" in data:
        lead_field = "ecg"
        lead_data = data["ecg"]
    elif lead == "II" and "ecgII" in data:
        lead_field = "ecgII"
        lead_data = data["ecgII"]
    else:
        print(f"  - 未找到导联 {lead} 的数据 (es_key: {es_key})")
        return None
    
    # 将es_key处理成安全的文件名
    safe_es_key = es_key.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
    
    # 构建文件名，包含导联信息
    safe_filename = f"{safe_es_key}_lead{lead}.txt"
    filepath = os.path.join(output_dir, safe_filename)
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            # 如果lead_data是列表，每项占一行；否则直接写入
            if isinstance(lead_data, list):
                f.write('\n'.join(map(str, lead_data)))
            else:
                f.write(str(lead_data))
        print(f"  - 已保存导联 {lead} ({lead_field}) 数据: {filepath}")
        return safe_filename
    except Exception as e:
        print(f"  - 保存失败: {filepath}，原因: {e}")
        return None

def save_lead_json_data(es_key, data, lead, output_dir):
    """
    保存完整的JSON数据，文件名中包含导联信息。
    lead: "I" 或 "II"，表示文件名中的导联标识
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查data是否为有效字典
    if not isinstance(data, dict):
        print(f"  - 数据格式无效，无法保存 (es_key: {es_key})")
        return None
    
    # 检查数据中是否包含对应的导联字段（仅用于日志输出）
    if lead == "I" and "ecg" not in data:
        print(f"  - 警告: 数据中不包含I导联 (es_key: {es_key})，但仍将保存完整JSON")
    elif lead == "II" and "ecgII" not in data:
        print(f"  - 警告: 数据中不包含II导联 (es_key: {es_key})，但仍将保存完整JSON")
    
    # 将es_key处理成安全的文件名
    safe_es_key = es_key.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
    
    # 构建文件名，包含导联信息
    safe_filename = f"{safe_es_key}_lead{lead}.json"
    filepath = os.path.join(output_dir, safe_filename)
        
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            # 保存完整的JSON数据
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"  - 已保存完整JSON数据 (lead{lead}): {filepath}")
        return safe_filename
    except Exception as e:
        print(f"  - 保存失败: {filepath}，原因: {e}")
        return None

def download_and_save_lead_data_task(es_key, lead, output_dir):
    """
    单个任务单元：下载数据并根据指定的导联保存。为并发执行而设计。
    """
    data = get_qiniu_data(es_key, ENVIRONMENT)
    if data:
        saved_filename = save_lead_json_data(es_key, data, lead, output_dir)
        if saved_filename:
            return {
                'original_es_key': es_key,
                'lead': lead,
                'saved_filename': saved_filename
            }
    return None

def process_csv_file(csv_path, base_output_dir, folder_index, save_full_json=False, use_original_name=False):
    """处理单个CSV文件：读取es_key，并发下载数据，保存数据并创建映射文件"""
    print(f"\n--- 开始处理CSV文件: {os.path.basename(csv_path)} (文件夹编号: {folder_index:02d}) ---")
    
    # 1. 创建该CSV专属的输出目录
    csv_filename = os.path.basename(csv_path)
    output_dir_name = f"output_{os.path.splitext(csv_filename)[0]}"
    specific_output_dir = os.path.join(base_output_dir, output_dir_name)
    os.makedirs(specific_output_dir, exist_ok=True)
    print(f"数据将保存到: {specific_output_dir}")
    
    # 显示保存模式
    if use_original_name:
        print(f"保存模式: 原始es_key命名的完整JSON")
    else:
        print(f"保存模式: {'完整JSON' if save_full_json else 'ECG导联数据'}")

    # 2. 读取CSV中的es_key
    try:
        df = pd.read_csv(csv_path, low_memory=False)
        if 'es_key' not in df.columns:
            print(f"[错误] CSV文件 {csv_filename} 中未找到 'es_key' 列。")
            return
        # 筛选出需要处理的列并去除空值和重复项
        es_keys = df['es_key'].dropna().unique().tolist()
        print(f"发现 {len(es_keys)} 个唯一的es_key。")
    except Exception as e:
        print(f"[错误] 读取CSV文件 {csv_filename} 失败: {e}")
        return

    # 3. 并发下载并保存数据
    mapping_records = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 准备所有任务
        future_to_es_key = {}
        
        for i, es_key in enumerate(es_keys):
            if use_original_name:
                # 使用原始es_key作为标识，filename_base在这种模式下不使用
                future_to_es_key[executor.submit(
                    download_and_save_task, 
                    es_key, 
                    None,  # filename_base不使用
                    specific_output_dir,
                    save_full_json,
                    use_original_name
                )] = es_key
            else:
                # 使用序列号作为文件名
                future_to_es_key[executor.submit(
                    download_and_save_task, 
                    es_key, 
                    f"ECG-{folder_index:02d}-{i+1:02d}", 
                    specific_output_dir,
                    save_full_json,
                    use_original_name
                )] = es_key
        
        # 使用tqdm显示进度
        for future in tqdm(concurrent.futures.as_completed(future_to_es_key), total=len(es_keys), desc=f"处理 {os.path.basename(csv_path)}"):
            try:
                result = future.result()
                if result:
                    mapping_records.append(result)
            except Exception as exc:
                es_key = future_to_es_key[future]
                print(f"\n[错误] 处理es_key '{es_key}' 时产生异常: {exc}")

    # 4. 创建并保存映射文件
    if mapping_records:
        manifest_path = os.path.join(specific_output_dir, '_manifest.csv')
        manifest_df = pd.DataFrame(mapping_records)
        manifest_df.to_csv(manifest_path, index=False, encoding='utf-8-sig')
        print(f"已创建映射文件: {manifest_path}")
    
    print(f"--- 完成处理: {csv_filename} ---")

def process_with_lead_selection(excel_path, output_dir):
    """
    处理Excel文件，根据lead列选择导联数据进行导出。
    """
    print(f"\n--- 导联选择模式 ---")
    print(f"正在读取Excel文件: {excel_path}")
    
    # 读取Excel文件，获取es_key和lead信息
    es_key_lead_pairs = read_excel_with_lead_info(excel_path)
    
    if not es_key_lead_pairs:
        print("[错误] 未能从Excel文件中获取有效数据。")
        return
    
    print(f"\n准备处理 {len(es_key_lead_pairs)} 条数据...")
    print(f"数据将保存到: {output_dir}")
    
    # 并发下载并保存数据
    mapping_records = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 准备所有任务
        future_to_pair = {}
        
        for i, (es_key, lead) in enumerate(es_key_lead_pairs):
            future_to_pair[executor.submit(
                download_and_save_lead_data_task, 
                es_key, 
                lead, 
                output_dir
            )] = (es_key, lead)
        
        # 使用tqdm显示进度
        for future in tqdm(concurrent.futures.as_completed(future_to_pair), total=len(es_key_lead_pairs), desc="处理导联数据"):
            try:
                result = future.result()
                if result:
                    mapping_records.append(result)
            except Exception as exc:
                es_key, lead = future_to_pair[future]
                print(f"\n[错误] 处理es_key '{es_key}' (导联 {lead}) 时产生异常: {exc}")
    
    # 创建并保存映射文件
    if mapping_records:
        manifest_path = os.path.join(output_dir, '_lead_selection_manifest.csv')
        manifest_df = pd.DataFrame(mapping_records)
        manifest_df.to_csv(manifest_path, index=False, encoding='utf-8-sig')
        print(f"已创建映射文件: {manifest_path}")
    
    print(f"\n--- 导联选择模式处理完成 ---")
    print(f"成功处理: {len(mapping_records)}/{len(es_key_lead_pairs)} 条数据")


if __name__ == "__main__":
    # 输出当前活动的模式
    print("\n=== 七牛云数据查询工具启动 ===")
    print(f"单一查询模式: {'启用' if RUN_SINGLE_FULL_QUERY else '禁用'}")
    print(f"指定文件模式: {'启用' if RUN_SPECIFIC_FILES_MODE else '禁用'}")
    print(f"导联选择模式: {'启用' if RUN_LEAD_SELECTION_MODE else '禁用'}")
    print(f"批量处理模式: {'启用' if not RUN_SINGLE_FULL_QUERY and not RUN_SPECIFIC_FILES_MODE and not RUN_LEAD_SELECTION_MODE else '禁用'}")
    print("=" * 30)
    
    if RUN_SINGLE_FULL_QUERY:
        print("\n执行单一查询模式...")
        query_and_save_full_json(SINGLE_ES_KEY_TO_QUERY, SINGLE_JSON_OUTPUT_PATH)
        print("\n单一查询模式执行完成。")
    elif RUN_SPECIFIC_FILES_MODE:
        # --- 指定文件模式 ---
        print(f"--- 指定文件模式 ---")
        for i, file_path in enumerate(SPECIFIC_FILES_TO_PROCESS, 1):
            if os.path.exists(file_path):
                # 从文件路径中提取文件名（不含路径和扩展名）
                file_name = os.path.splitext(os.path.basename(file_path))[0]
                print(f"\n处理指定文件 [{i}/{len(SPECIFIC_FILES_TO_PROCESS)}]: {file_name}")
                process_csv_file(file_path, BASE_OUTPUT_DIR, i, save_full_json=True, use_original_name=USE_ORIGINAL_ES_KEY_AS_FILENAME)
            else:
                print(f"\n[错误] 指定的文件不存在: {file_path}")
        print("\n所有指定文件处理完成！")
    elif RUN_LEAD_SELECTION_MODE:
        # --- 导联选择模式 ---
        process_with_lead_selection(EXCEL_FILE_PATH, OUTPUT_DIR_FOR_LEAD_DATA)
    else:
        # --- 批量处理模式 ---
        if not os.path.isdir(INPUT_DIR):
            print(f"[错误] 输入目录不存在: {INPUT_DIR}")
        else:
            # 使用glob查找所有CSV文件
            csv_files = glob.glob(os.path.join(INPUT_DIR, '*.csv'))
            
            if not csv_files:
                print(f"[警告] 在目录 {INPUT_DIR} 中未找到任何.csv文件。")
            else:
                print(f"找到 {len(csv_files)} 个CSV文件，准备开始处理...")
                for i, csv_file in enumerate(csv_files, 1):
                    process_csv_file(csv_file, BASE_OUTPUT_DIR, i)
                print("\n所有CSV文件处理完成！")
