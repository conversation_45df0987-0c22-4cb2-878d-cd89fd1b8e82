# 🏸 羽毛球装备爬虫 (Badminton Equipment Crawler)

一个基于 Selenium 的智能数据采集工具，专门用于从中羽在线网站收集详细的羽毛球装备信息。

## ✨ 核心功能

- 🔍 **智能链接获取**: 自动从首页提取装备详情链接
- 🤖 **验证码处理**: 智能识别并处理数学运算和羽毛球知识验证
- 📊 **全面数据提取**: 收集装备详情、价格信息、用户评分等完整数据
- 💰 **价格信息采集**: 获取新拍均价、二手均价和用户数量统计
- 🛡️ **反爬虫技术**: 使用随机延时、浏览器伪装等技术规避检测
- 📝 **多格式输出**: 支持 JSON 和 CSV 格式的数据导出

## 📋 数据字段

爬虫收集以下完整的装备信息：

| 字段名 | 描述 | 示例 |
|--------|------|------|
| `url` | 装备详情页面URL | https://www.badmintoncn.com/... |
| `crawl_time` | 数据爬取时间 | 2025-01-17T12:24:45.211205 |
| `title` | 页面标题 | 8888AX ultra 斩鬼刀ultra 羽毛球拍... |
| `brand` | 品牌 | 波力 Bonny |
| `series` | 系列 | 乌缺ZD系列 |
| `name` | 装备名称 | 8888AX ultra 斩鬼刀ultra |
| `msrp_price` | 市场指导价 | 1680 |
| `description` | 装备描述 | 首页 ＞ 羽毛球拍... |
| `shaft_material` | 拍杆材质 | 硼纤维 高弹性细中管6.8mm |
| `shaft_diameter` | 拍杆直径 | 6.8mm |
| `technology` | 技术特点 | 高弹性 |
| `user_tags` | 用户标签 | 手感好19, 漆水好19... |
| `new_average_price` | 新拍均价 | 464.9 |
| `used_average_price` | 二手均价 | 448.0 |
| `total_users` | 用户数量 | 66 |
| `rating` | **中羽评分** ⭐️ | 4.2 |
| `equipment_intro` | **装备简介** ⭐️ | 详细的装备介绍文本... |

> ⭐️ 标记的字段为最新添加的功能

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Chrome 浏览器
- macOS/Linux/Windows

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd badminton-equipment-crawler
```

2. **创建虚拟环境**
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

### 使用方法

#### 方法一：使用快速启动脚本（推荐）

```bash
./run.sh
```

这将启动交互式菜单，您可以选择：
1. 运行单个装备测试
2. 爬取指定URL
3. 批量爬取装备
4. 查看帮助信息

#### 方法二：使用主程序

```bash
# 运行默认测试
python main.py

# 爬取指定URL
python main.py --url "https://www.badmintoncn.com/cbo_eq/view.php?eid=22974"

# 批量爬取（限制5个）
python main.py --batch --limit 5

# 指定输出文件
python main.py --test --output "my_result.json"

# 查看所有选项
python main.py --help
```

#### 方法三：直接使用测试脚本

```bash
# 运行集成测试
python tests/test_integrated_crawler.py
```

## 📁 项目结构

```
badminton-equipment-crawler/
├── 📄 main.py                  # 主入口程序
├── 📄 browser_crawler.py       # 核心爬虫模块
├── 📄 run.sh                   # 快速启动脚本
├── 📄 organize_project.py      # 项目整理脚本
├── 📄 requirements.txt         # 依赖文件
├── 📄 README.md               # 项目说明
├── 📄 LICENSE                 # MIT许可证
├── 📄 .gitignore              # Git忽略文件
├── 📁 tests/                  # 测试文件目录
│   ├── 📄 test_integrated_crawler.py
│   ├── 📄 test_buy_price.py
│   └── 📄 ...
├── 📁 debug/                  # 调试文件目录
│   ├── 📄 debug_page_content.py
│   └── 📄 ...
├── 📁 scripts/                # 辅助脚本目录
│   ├── 📄 view_results.py
│   ├── 📄 analyze_data.py
│   └── 📄 ...
├── 📁 temp/                   # 临时文件目录
├── 📁 old_versions/           # 旧版本文件
├── 📁 output/                 # 输出文件目录
├── 📁 logs/                   # 日志文件目录
└── 📁 data/                   # 数据文件目录
```

## 🔧 核心技术

- **Selenium WebDriver**: 浏览器自动化
- **BeautifulSoup**: HTML 解析
- **WebDriver Manager**: 自动管理 ChromeDriver
- **正则表达式**: 数据清洗和提取
- **智能重试机制**: 提高稳定性

## ⚡ 性能特征

- **智能验证处理**: 自动识别数学题和羽毛球知识问题
- **反检测技术**: 随机延时、浏览器伪装
- **容错处理**: 网络异常自动重试
- **内存优化**: 及时清理浏览器资源
- **日志追踪**: 详细的运行日志记录

## 🔍 故障排除

### 常见问题

1. **ChromeDriver 版本不匹配**
   - 解决方案：脚本会自动下载匹配的 ChromeDriver

2. **验证码无法通过**
   - 解决方案：检查网络连接，或稍后重试

3. **数据提取失败**
   - 解决方案：检查目标网站是否更新了页面结构

### 调试模式

```bash
# 启用详细日志
export CRAWLER_DEBUG=1
python main.py --test
```


## 📤 输出格式

### JSON 格式
```json
{
  "url": "https://www.badmintoncn.com/cbo_eq/view.php?eid=22974",
  "name": "8888AX ultra 斩鬼刀ultra",
  "brand": "波力 Bonny",
  "rating": "4.2",
  "equipment_intro": "详细的装备介绍..."
}
```

### CSV 格式
包含所有字段的逗号分隔值文件，便于数据分析。

## 🤝 贡献指南

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📜 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢中羽在线提供的丰富羽毛球装备信息
- 感谢开源社区提供的优秀工具和库
- 感谢所有贡献者的支持和建议

---

**⚠️ 免责声明**: 本工具仅供学习和研究使用，请遵守网站的使用条款和相关法律法规。使用时请适度，避免对目标网站造成过大负担。 

