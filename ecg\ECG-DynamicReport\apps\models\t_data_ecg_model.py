from django.db import models


def get_data_ecg_model(date_str):
    class TDataEcg(models.Model):
        id = models.BigAutoField(primary_key=True)
        ecg_id = models.BigIntegerField(blank=True, null=True)
        union_id = models.CharField(max_length=255, blank=True, null=True)
        dead = models.IntegerField(blank=True, null=True)
        check_date = models.DateTimeField(blank=True, null=True)
        report_lead_i = models.TextField(blank=True, null=True)
        report_lead_ii = models.TextField(blank=True, null=True)
        report_lead_iii = models.TextField(blank=True, null=True)
        arrhythmiadiagnosis_af = models.IntegerField(db_column='ArrhythmiaDiagnosis_AF', blank=True,
                                                     null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_afl = models.IntegerField(db_column='ArrhythmiaDiagnosis_AFL', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_pac = models.IntegerField(db_column='ArrhythmiaDiagnosis_PAC', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_pvc = models.IntegerField(db_column='ArrhythmiaDiagnosis_PVC', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_ae = models.IntegerField(db_column='ArrhythmiaDiagnosis_AE', blank=True,
                                                     null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_avbi = models.IntegerField(db_column='ArrhythmiaDiagnosis_AVBI', blank=True,
                                                       null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_avbii = models.IntegerField(db_column='ArrhythmiaDiagnosis_AVBII', blank=True,
                                                        null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_avbiii = models.IntegerField(db_column='ArrhythmiaDiagnosis_AVBIII', blank=True,
                                                         null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_bru = models.IntegerField(db_column='ArrhythmiaDiagnosis_BRU', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_ivb = models.IntegerField(db_column='ArrhythmiaDiagnosis_IVB', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_je = models.IntegerField(db_column='ArrhythmiaDiagnosis_JE', blank=True,
                                                     null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_lafb = models.IntegerField(db_column='ArrhythmiaDiagnosis_LAFB', blank=True,
                                                       null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_lbbb = models.IntegerField(db_column='ArrhythmiaDiagnosis_LBBB', blank=True,
                                                       null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_lqt = models.IntegerField(db_column='ArrhythmiaDiagnosis_LQT', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_pjc = models.IntegerField(db_column='ArrhythmiaDiagnosis_PJC', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_psc = models.IntegerField(db_column='ArrhythmiaDiagnosis_PSC', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_rbbb = models.IntegerField(db_column='ArrhythmiaDiagnosis_RBBB', blank=True,
                                                       null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_sn = models.IntegerField(db_column='ArrhythmiaDiagnosis_SN', blank=True,
                                                     null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_sna = models.IntegerField(db_column='ArrhythmiaDiagnosis_SNA', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_snb = models.IntegerField(db_column='ArrhythmiaDiagnosis_SNB', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_snt = models.IntegerField(db_column='ArrhythmiaDiagnosis_SNT', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_svt = models.IntegerField(db_column='ArrhythmiaDiagnosis_SVT', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_ve = models.IntegerField(db_column='ArrhythmiaDiagnosis_VE', blank=True,
                                                     null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_vt = models.IntegerField(db_column='ArrhythmiaDiagnosis_VT', blank=True,
                                                     null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_wpw = models.IntegerField(db_column='ArrhythmiaDiagnosis_WPW', blank=True,
                                                      null=True)  # Field name made lowercase.
        arrhythmiadiagnosis_bpvc = models.IntegerField(db_column='ArrhythmiaDiagnosis_bPVC', blank=True,
                                                       null=True)  # Field name made lowercase.
        cadcardiomyopathy_isc = models.IntegerField(db_column='CADCardiomyopathy_ISC', blank=True,
                                                    null=True)  # Field name made lowercase.
        cadcardiomyopathy_lah = models.IntegerField(db_column='CADCardiomyopathy_LAH', blank=True,
                                                    null=True)  # Field name made lowercase.
        cadcardiomyopathy_lvh = models.IntegerField(db_column='CADCardiomyopathy_LVH', blank=True,
                                                    null=True)  # Field name made lowercase.
        cadcardiomyopathy_mi = models.IntegerField(db_column='CADCardiomyopathy_MI', blank=True,
                                                   null=True)  # Field name made lowercase.
        cadcardiomyopathy_rah = models.IntegerField(db_column='CADCardiomyopathy_RAH', blank=True,
                                                    null=True)  # Field name made lowercase.
        cadcardiomyopathy_rvh = models.IntegerField(db_column='CADCardiomyopathy_RVH', blank=True,
                                                    null=True)  # Field name made lowercase.
        healthmetrics_emotion = models.IntegerField(db_column='HealthMetrics_Emotion', blank=True,
                                                    null=True)  # Field name made lowercase.
        healthmetrics_fatigue = models.IntegerField(db_column='HealthMetrics_Fatigue', blank=True,
                                                    null=True)  # Field name made lowercase.
        healthmetrics_hrv = models.IntegerField(db_column='HealthMetrics_HRV', blank=True,
                                                null=True)  # Field name made lowercase.
        healthmetrics_pressure = models.IntegerField(db_column='HealthMetrics_Pressure', blank=True,
                                                     null=True)  # Field name made lowercase.
        healthmetrics_vitality = models.IntegerField(db_column='HealthMetrics_Vitality', blank=True,
                                                     null=True)  # Field name made lowercase.
        pqrstc_hr = models.IntegerField(db_column='PQRSTC_HR', blank=True, null=True)  # Field name made lowercase.
        pqrstc_pr_interval = models.IntegerField(db_column='PQRSTC_PR_interval', blank=True,
                                                 null=True)  # Field name made lowercase.
        pqrstc_p_duration = models.IntegerField(db_column='PQRSTC_P_duration', blank=True,
                                                null=True)  # Field name made lowercase.
        pqrstc_qt = models.IntegerField(db_column='PQRSTC_QT', blank=True, null=True)  # Field name made lowercase.
        pqrstc_qtc = models.IntegerField(db_column='PQRSTC_QTc', blank=True, null=True)  # Field name made lowercase.
        pqrstc_st_duration = models.IntegerField(db_column='PQRSTC_ST_duration', blank=True,
                                                 null=True)  # Field name made lowercase.
        pqrstc_nn_max = models.FloatField(db_column='PQRSTC_NN_MAX', blank=True,
                                          null=True)  # Field name made lowercase.
        pqrstc_nn_min = models.FloatField(db_column='PQRSTC_NN_MIN', blank=True,
                                          null=True)  # Field name made lowercase.
        pqrstc_rr_max = models.FloatField(db_column='PQRSTC_RR_MAX', blank=True,
                                          null=True)  # Field name made lowercase.
        pqrstc_rr_min = models.FloatField(db_column='PQRSTC_RR_MIN', blank=True,
                                          null=True)  # Field name made lowercase.
        ecgage = models.IntegerField(db_column='ECGAge', blank=True, null=True)  # Field name made lowercase.
        heartfailurerisk = models.IntegerField(db_column='HeartFailureRisk', blank=True,
                                               null=True)  # Field name made lowercase.
        osarisk = models.IntegerField(db_column='OSARisk', blank=True, null=True)  # Field name made lowercase.
        respiratoryrate = models.IntegerField(db_column='RespiratoryRate', blank=True,
                                              null=True)  # Field name made lowercase.
        signalquantity = models.IntegerField(db_column='SignalQuantity', blank=True,
                                             null=True)  # Field name made lowercase.
        sleepstage = models.IntegerField(db_column='SleepStage', blank=True, null=True)  # Field name made lowercase.
        syncoperisk = models.IntegerField(db_column='SyncopeRisk', blank=True, null=True)  # Field name made lowercase.
        ventricularfibrillationrisk = models.IntegerField(db_column='VentricularFibrillationRisk', blank=True,
                                                          null=True)  # Field name made lowercase.
        avghr = models.IntegerField(db_column='avgHr', blank=True, null=True)  # Field name made lowercase.
        rrintervals = models.CharField(db_column='RRIntervals', max_length=255, blank=True,
                                       null=True)  # Field name made lowercase.
        nnintervals = models.CharField(db_column='NNIntervals', max_length=255, blank=True,
                                       null=True)  # Field name made lowercase.

        class Meta:
            managed = False
            db_table = f't_data_ecg_{date_str}'

    return TDataEcg
