metadata:
  name: pth-foolwood-siammask
  namespace: cvat
  annotations:
    name: SiamMask
    type: tracker
    spec:

spec:
  description: Fast Online Object Tracking and Segmentation
  runtime: 'python:3.9'
  handler: main:handler
  eventTimeout: 30s
  env:
    - name: PYTHONPATH
      value: /opt/nuclio/SiamMask:/opt/nuclio/SiamMask/experiments/siammask_sharp

  build:
    image: cvat.pth.foolwood.siammask
    baseImage: ubuntu:20.04

    directives:
      preCopy:
        - kind: ENV
          value: PATH="/root/miniconda3/bin:${PATH}"
        - kind: ARG
          value: PATH="/root/miniconda3/bin:${PATH}"
        - kind: RUN
          value: apt update && apt install -y --no-install-recommends wget git ca-certificates && rm -rf /var/lib/apt/lists/*
        - kind: RUN
          value: wget https://repo.continuum.io/miniconda/Miniconda3-latest-Linux-x86_64.sh &&
            chmod +x Miniconda3-latest-Linux-x86_64.sh && bash Miniconda3-latest-Linux-x86_64.sh -b &&
            rm -f Miniconda3-latest-Linux-x86_64.sh
        - kind: WORKDIR
          value: /opt/nuclio
        - kind: RUN
          value: conda create -y -n siammask python=3.9
        - kind: SHELL
          value: '["conda", "run", "-n", "siammask", "/bin/bash", "-c"]'
        - kind: RUN
          value: git clone https://github.com/foolwood/SiamMask.git
        - kind: RUN
          value: pip install Cython colorama 'numpy<1.20' requests fire matplotlib numba scipy h5py pandas tqdm tensorboardX 'opencv-python-headless<4.9.10' torch torchvision jsonpickle
        - kind: RUN
          value: conda install -y gcc_linux-64
        - kind: RUN
          value: cd SiamMask && bash make.sh && cd -
        - kind: RUN
          value: wget -P SiamMask/experiments/siammask_sharp http://www.robots.ox.ac.uk/~qwang/SiamMask_DAVIS.pth
        - kind: ENTRYPOINT
          value: '["conda", "run", "--no-capture-output", "-n", "siammask"]'

  triggers:
    myHttpTrigger:
      numWorkers: 2
      kind: 'http'
      workerAvailabilityTimeoutMilliseconds: 10000
      attributes:
        maxRequestBodySize: 33554432 # 32MB

  platform:
    attributes:
      restartPolicy:
        name: always
        maximumRetryCount: 3
      mountMode: volume
