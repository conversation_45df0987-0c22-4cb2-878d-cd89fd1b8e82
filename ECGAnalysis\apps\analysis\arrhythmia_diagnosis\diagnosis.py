from apps.analysis.arrhythmia_diagnosis import atrial_premature
from apps.analysis.common import detect_wave, heart_rate_calculation
from apps.analysis.common.multiple_model import conclusion_diagnostic
from apps.analysis.diagnosis_filter.filter import apply_rules
from apps.models.arrhythmia_models import ArrhythmiaDiagnosisEntity
from apps.analysis.common.ecg_diagnosis_processing import is_diag_true
from global_settings import mutually_exclusive_conditions


def process(ecg_data, sampling_rate):
    """
    心率失常分析
    :param ecg_data: ECG信号数据
    :param sampling_rate: 采样率
    :return: 心率失常分析结果
    """

    arrhythmia_diagnosis_entity = ArrhythmiaDiagnosisEntity()   # 初始化心率失常对象

    # 多结论模型分析
    conclusions = conclusion_diagnostic.diagnostic(ecg_data, sampling_rate)

    # 大模型结论分析
    ai_model = is_diag_true(ecg_data, sampling_rate)

    ai_model_conclusions = []

    if ai_model.is_sn():
        ai_model_conclusions.append('SN')

    if ai_model.is_sna():
        ai_model_conclusions.append('SNA')

    if ai_model.is_snt():
        ai_model_conclusions.append('SNT')

    if ai_model.is_snb():
        ai_model_conclusions.append('SNB')

    if ai_model.is_af():
        ai_model_conclusions.append('AF')

    # 合并多结论模型和大模型结论
    priority_set = {'SN', 'SNA', 'SNT', 'SNB', 'AF'}

    # 从 conclusions 中保留非特定值
    merge_results = [item for item in conclusions if item not in priority_set]

    # 将 ai_model 中的值加入到结果中，优先保留特定值
    merge_results.extend(ai_model_conclusions)

    # 去除重复值并保持顺序
    merge_results = list(dict.fromkeys(merge_results))

    final_labels = apply_mutually_exclusive_rules(merge_results)

    if len(final_labels) == 0:
        arrhythmia_diagnosis_entity.SN = 1
    else:
        for final_label in final_labels:
            setattr(arrhythmia_diagnosis_entity, final_label, 1)

    return apply_rules(arrhythmia_diagnosis_entity)


def apply_mutually_exclusive_rules(predicted_labels):
    """
    根据互斥规则过滤预测标签
    :param predicted_labels: 模型预测的疾病标签列表
    :return: 经过互斥规则过滤后的疾病标签列表
    """
    final_labels = []

    for label in predicted_labels:
        # 如果该标签已经在 final_labels 列表中，跳过
        if label in final_labels:
            continue

        # 将标签加入最终标签列表
        final_labels.append(label)

        # 如果该标签有互斥条件，移除其他互斥标签
        if label in mutually_exclusive_conditions:
            mutually_exclusive = mutually_exclusive_conditions[label]
            predicted_labels = [lbl for lbl in predicted_labels if lbl not in mutually_exclusive]

    return final_labels