// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/saver.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto;
namespace tensorflow {
class SaverDef;
class SaverDefDefaultTypeInternal;
extern SaverDefDefaultTypeInternal _SaverDef_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::SaverDef* Arena::CreateMaybeMessage<::tensorflow::SaverDef>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum SaverDef_CheckpointFormatVersion : int {
  SaverDef_CheckpointFormatVersion_LEGACY = 0,
  SaverDef_CheckpointFormatVersion_V1 = 1,
  SaverDef_CheckpointFormatVersion_V2 = 2,
  SaverDef_CheckpointFormatVersion_SaverDef_CheckpointFormatVersion_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  SaverDef_CheckpointFormatVersion_SaverDef_CheckpointFormatVersion_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool SaverDef_CheckpointFormatVersion_IsValid(int value);
constexpr SaverDef_CheckpointFormatVersion SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_MIN = SaverDef_CheckpointFormatVersion_LEGACY;
constexpr SaverDef_CheckpointFormatVersion SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_MAX = SaverDef_CheckpointFormatVersion_V2;
constexpr int SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_ARRAYSIZE = SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SaverDef_CheckpointFormatVersion_descriptor();
template<typename T>
inline const std::string& SaverDef_CheckpointFormatVersion_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SaverDef_CheckpointFormatVersion>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SaverDef_CheckpointFormatVersion_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SaverDef_CheckpointFormatVersion_descriptor(), enum_t_value);
}
inline bool SaverDef_CheckpointFormatVersion_Parse(
    const std::string& name, SaverDef_CheckpointFormatVersion* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SaverDef_CheckpointFormatVersion>(
    SaverDef_CheckpointFormatVersion_descriptor(), name, value);
}
// ===================================================================

class SaverDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SaverDef) */ {
 public:
  SaverDef();
  virtual ~SaverDef();

  SaverDef(const SaverDef& from);
  SaverDef(SaverDef&& from) noexcept
    : SaverDef() {
    *this = ::std::move(from);
  }

  inline SaverDef& operator=(const SaverDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaverDef& operator=(SaverDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SaverDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SaverDef* internal_default_instance() {
    return reinterpret_cast<const SaverDef*>(
               &_SaverDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SaverDef& a, SaverDef& b) {
    a.Swap(&b);
  }
  inline void Swap(SaverDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaverDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SaverDef* New() const final {
    return CreateMaybeMessage<SaverDef>(nullptr);
  }

  SaverDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SaverDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SaverDef& from);
  void MergeFrom(const SaverDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaverDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SaverDef";
  }
  protected:
  explicit SaverDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef SaverDef_CheckpointFormatVersion CheckpointFormatVersion;
  static constexpr CheckpointFormatVersion LEGACY =
    SaverDef_CheckpointFormatVersion_LEGACY;
  static constexpr CheckpointFormatVersion V1 =
    SaverDef_CheckpointFormatVersion_V1;
  static constexpr CheckpointFormatVersion V2 =
    SaverDef_CheckpointFormatVersion_V2;
  static inline bool CheckpointFormatVersion_IsValid(int value) {
    return SaverDef_CheckpointFormatVersion_IsValid(value);
  }
  static constexpr CheckpointFormatVersion CheckpointFormatVersion_MIN =
    SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_MIN;
  static constexpr CheckpointFormatVersion CheckpointFormatVersion_MAX =
    SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_MAX;
  static constexpr int CheckpointFormatVersion_ARRAYSIZE =
    SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  CheckpointFormatVersion_descriptor() {
    return SaverDef_CheckpointFormatVersion_descriptor();
  }
  template<typename T>
  static inline const std::string& CheckpointFormatVersion_Name(T enum_t_value) {
    static_assert(::std::is_same<T, CheckpointFormatVersion>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function CheckpointFormatVersion_Name.");
    return SaverDef_CheckpointFormatVersion_Name(enum_t_value);
  }
  static inline bool CheckpointFormatVersion_Parse(const std::string& name,
      CheckpointFormatVersion* value) {
    return SaverDef_CheckpointFormatVersion_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kFilenameTensorNameFieldNumber = 1,
    kSaveTensorNameFieldNumber = 2,
    kRestoreOpNameFieldNumber = 3,
    kMaxToKeepFieldNumber = 4,
    kShardedFieldNumber = 5,
    kKeepCheckpointEveryNHoursFieldNumber = 6,
    kVersionFieldNumber = 7,
  };
  // string filename_tensor_name = 1;
  void clear_filename_tensor_name();
  const std::string& filename_tensor_name() const;
  void set_filename_tensor_name(const std::string& value);
  void set_filename_tensor_name(std::string&& value);
  void set_filename_tensor_name(const char* value);
  void set_filename_tensor_name(const char* value, size_t size);
  std::string* mutable_filename_tensor_name();
  std::string* release_filename_tensor_name();
  void set_allocated_filename_tensor_name(std::string* filename_tensor_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_filename_tensor_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_filename_tensor_name(
      std::string* filename_tensor_name);

  // string save_tensor_name = 2;
  void clear_save_tensor_name();
  const std::string& save_tensor_name() const;
  void set_save_tensor_name(const std::string& value);
  void set_save_tensor_name(std::string&& value);
  void set_save_tensor_name(const char* value);
  void set_save_tensor_name(const char* value, size_t size);
  std::string* mutable_save_tensor_name();
  std::string* release_save_tensor_name();
  void set_allocated_save_tensor_name(std::string* save_tensor_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_save_tensor_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_save_tensor_name(
      std::string* save_tensor_name);

  // string restore_op_name = 3;
  void clear_restore_op_name();
  const std::string& restore_op_name() const;
  void set_restore_op_name(const std::string& value);
  void set_restore_op_name(std::string&& value);
  void set_restore_op_name(const char* value);
  void set_restore_op_name(const char* value, size_t size);
  std::string* mutable_restore_op_name();
  std::string* release_restore_op_name();
  void set_allocated_restore_op_name(std::string* restore_op_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_restore_op_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_restore_op_name(
      std::string* restore_op_name);

  // int32 max_to_keep = 4;
  void clear_max_to_keep();
  ::PROTOBUF_NAMESPACE_ID::int32 max_to_keep() const;
  void set_max_to_keep(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool sharded = 5;
  void clear_sharded();
  bool sharded() const;
  void set_sharded(bool value);

  // float keep_checkpoint_every_n_hours = 6;
  void clear_keep_checkpoint_every_n_hours();
  float keep_checkpoint_every_n_hours() const;
  void set_keep_checkpoint_every_n_hours(float value);

  // .tensorflow.SaverDef.CheckpointFormatVersion version = 7;
  void clear_version();
  ::tensorflow::SaverDef_CheckpointFormatVersion version() const;
  void set_version(::tensorflow::SaverDef_CheckpointFormatVersion value);

  // @@protoc_insertion_point(class_scope:tensorflow.SaverDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr filename_tensor_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr save_tensor_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr restore_op_name_;
  ::PROTOBUF_NAMESPACE_ID::int32 max_to_keep_;
  bool sharded_;
  float keep_checkpoint_every_n_hours_;
  int version_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SaverDef

// string filename_tensor_name = 1;
inline void SaverDef::clear_filename_tensor_name() {
  filename_tensor_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SaverDef::filename_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.filename_tensor_name)
  return filename_tensor_name_.Get();
}
inline void SaverDef::set_filename_tensor_name(const std::string& value) {
  
  filename_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.filename_tensor_name)
}
inline void SaverDef::set_filename_tensor_name(std::string&& value) {
  
  filename_tensor_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SaverDef.filename_tensor_name)
}
inline void SaverDef::set_filename_tensor_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  filename_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SaverDef.filename_tensor_name)
}
inline void SaverDef::set_filename_tensor_name(const char* value,
    size_t size) {
  
  filename_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SaverDef.filename_tensor_name)
}
inline std::string* SaverDef::mutable_filename_tensor_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SaverDef.filename_tensor_name)
  return filename_tensor_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SaverDef::release_filename_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SaverDef.filename_tensor_name)
  
  return filename_tensor_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SaverDef::set_allocated_filename_tensor_name(std::string* filename_tensor_name) {
  if (filename_tensor_name != nullptr) {
    
  } else {
    
  }
  filename_tensor_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), filename_tensor_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SaverDef.filename_tensor_name)
}
inline std::string* SaverDef::unsafe_arena_release_filename_tensor_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SaverDef.filename_tensor_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return filename_tensor_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SaverDef::unsafe_arena_set_allocated_filename_tensor_name(
    std::string* filename_tensor_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (filename_tensor_name != nullptr) {
    
  } else {
    
  }
  filename_tensor_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      filename_tensor_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SaverDef.filename_tensor_name)
}

// string save_tensor_name = 2;
inline void SaverDef::clear_save_tensor_name() {
  save_tensor_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SaverDef::save_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.save_tensor_name)
  return save_tensor_name_.Get();
}
inline void SaverDef::set_save_tensor_name(const std::string& value) {
  
  save_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.save_tensor_name)
}
inline void SaverDef::set_save_tensor_name(std::string&& value) {
  
  save_tensor_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SaverDef.save_tensor_name)
}
inline void SaverDef::set_save_tensor_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  save_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SaverDef.save_tensor_name)
}
inline void SaverDef::set_save_tensor_name(const char* value,
    size_t size) {
  
  save_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SaverDef.save_tensor_name)
}
inline std::string* SaverDef::mutable_save_tensor_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SaverDef.save_tensor_name)
  return save_tensor_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SaverDef::release_save_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SaverDef.save_tensor_name)
  
  return save_tensor_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SaverDef::set_allocated_save_tensor_name(std::string* save_tensor_name) {
  if (save_tensor_name != nullptr) {
    
  } else {
    
  }
  save_tensor_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), save_tensor_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SaverDef.save_tensor_name)
}
inline std::string* SaverDef::unsafe_arena_release_save_tensor_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SaverDef.save_tensor_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return save_tensor_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SaverDef::unsafe_arena_set_allocated_save_tensor_name(
    std::string* save_tensor_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (save_tensor_name != nullptr) {
    
  } else {
    
  }
  save_tensor_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      save_tensor_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SaverDef.save_tensor_name)
}

// string restore_op_name = 3;
inline void SaverDef::clear_restore_op_name() {
  restore_op_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SaverDef::restore_op_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.restore_op_name)
  return restore_op_name_.Get();
}
inline void SaverDef::set_restore_op_name(const std::string& value) {
  
  restore_op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.restore_op_name)
}
inline void SaverDef::set_restore_op_name(std::string&& value) {
  
  restore_op_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SaverDef.restore_op_name)
}
inline void SaverDef::set_restore_op_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  restore_op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SaverDef.restore_op_name)
}
inline void SaverDef::set_restore_op_name(const char* value,
    size_t size) {
  
  restore_op_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SaverDef.restore_op_name)
}
inline std::string* SaverDef::mutable_restore_op_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SaverDef.restore_op_name)
  return restore_op_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SaverDef::release_restore_op_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SaverDef.restore_op_name)
  
  return restore_op_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SaverDef::set_allocated_restore_op_name(std::string* restore_op_name) {
  if (restore_op_name != nullptr) {
    
  } else {
    
  }
  restore_op_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), restore_op_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SaverDef.restore_op_name)
}
inline std::string* SaverDef::unsafe_arena_release_restore_op_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SaverDef.restore_op_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return restore_op_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SaverDef::unsafe_arena_set_allocated_restore_op_name(
    std::string* restore_op_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (restore_op_name != nullptr) {
    
  } else {
    
  }
  restore_op_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      restore_op_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SaverDef.restore_op_name)
}

// int32 max_to_keep = 4;
inline void SaverDef::clear_max_to_keep() {
  max_to_keep_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SaverDef::max_to_keep() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.max_to_keep)
  return max_to_keep_;
}
inline void SaverDef::set_max_to_keep(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  max_to_keep_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.max_to_keep)
}

// bool sharded = 5;
inline void SaverDef::clear_sharded() {
  sharded_ = false;
}
inline bool SaverDef::sharded() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.sharded)
  return sharded_;
}
inline void SaverDef::set_sharded(bool value) {
  
  sharded_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.sharded)
}

// float keep_checkpoint_every_n_hours = 6;
inline void SaverDef::clear_keep_checkpoint_every_n_hours() {
  keep_checkpoint_every_n_hours_ = 0;
}
inline float SaverDef::keep_checkpoint_every_n_hours() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.keep_checkpoint_every_n_hours)
  return keep_checkpoint_every_n_hours_;
}
inline void SaverDef::set_keep_checkpoint_every_n_hours(float value) {
  
  keep_checkpoint_every_n_hours_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.keep_checkpoint_every_n_hours)
}

// .tensorflow.SaverDef.CheckpointFormatVersion version = 7;
inline void SaverDef::clear_version() {
  version_ = 0;
}
inline ::tensorflow::SaverDef_CheckpointFormatVersion SaverDef::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.version)
  return static_cast< ::tensorflow::SaverDef_CheckpointFormatVersion >(version_);
}
inline void SaverDef::set_version(::tensorflow::SaverDef_CheckpointFormatVersion value) {
  
  version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::SaverDef_CheckpointFormatVersion> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::SaverDef_CheckpointFormatVersion>() {
  return ::tensorflow::SaverDef_CheckpointFormatVersion_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto
