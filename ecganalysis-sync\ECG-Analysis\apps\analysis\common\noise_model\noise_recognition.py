# 时域统计特征分析
import numpy as np
from scipy import signal
from scipy import stats
from scipy.signal import find_peaks


def is_noise(ecg_data, sampling_rate):
    """
    检测是否噪音
    :param ecg_data:
    :param sampling_rate:
    :return:
    """
    if len(ecg_data) == 0:
        return -1

    # 1. 信号功率与噪声功率的比值(SNR)检测
    # 计算信号总功率
    signal_power = np.mean(np.square(ecg_data))
    # 使用40Hz高通滤波器提取噪声成分
    fc = 40.0
    w = fc / (sampling_rate / 2)
    b, a = signal.butter(3, w, 'high')
    noise = signal.filtfilt(b, a, ecg_data)
    noise_power = np.mean(np.square(noise))

    # 计算信号功率与噪声功率的比值(dB)
    if noise_power > 0:
        snr = 10 * np.log10(signal_power / noise_power)
    else:
        snr = 100

    # 如果信号功率与噪声功率的比值低于1.5dB，认为信号质量差
    if snr < 1.5:
        return -1

    # 2. 基线漂移检测
    # 使用0.5Hz低通滤波器提取基线
    fc = 0.5
    w = fc / (sampling_rate / 2)
    b, a = signal.butter(2, w, 'low')
    baseline = signal.filtfilt(b, a, ecg_data)
    baseline_var = np.std(baseline)

    # 如果基线波动超过原始信号标准差的40%，认为存在严重基线漂移
    if baseline_var > 0.4 * np.std(ecg_data):
        return -1

    # 3. QRS波群检测
    # 使用5-15Hz带通滤波器提取QRS波群
    w1, w2 = 5.0 / (sampling_rate / 2), 15.0 / (sampling_rate / 2)
    b, a = signal.butter(3, [w1, w2], 'band')
    filtered_ecg = signal.filtfilt(b, a, ecg_data)

    # 计算信号的一阶差分和平方
    diff_ecg = np.diff(filtered_ecg)
    squared_ecg = diff_ecg ** 2

    # 使用移动平均窗口进行积分
    window_size = int(0.15 * sampling_rate)
    if window_size % 2 == 0:
        window_size += 1

    integrated_ecg = np.convolve(squared_ecg, np.ones(window_size) / window_size, mode='same')

    # 检测R波峰值
    threshold = 0.3 * np.max(integrated_ecg)
    min_distance = int(0.2 * sampling_rate)
    peaks, _ = find_peaks(integrated_ecg, height=threshold, distance=min_distance)

    # 如果检测到的R波数量少于3个，认为信号质量差
    if len(peaks) < 3:
        return -1

    # 4. RR间期分析
    # 计算RR间期（毫秒）
    rr_intervals = np.diff(peaks) / sampling_rate * 1000  # 毫秒

    # 筛选合理的RR间期（200-3000ms）
    valid_rr = rr_intervals[(rr_intervals > 200) & (rr_intervals < 3000)]

    # 如果有效RR间期数量少于5个，认为信号质量差
    if len(valid_rr) < 5:
        return -1

    # 计算RR间期的统计特征
    rr_mean = np.mean(valid_rr)
    rr_std = np.std(valid_rr)
    rr_cv = rr_std / rr_mean if rr_mean > 0 else 0

    # 计算心率
    heart_rate = 60000 / rr_mean if rr_mean > 0 else 0

    # 5. 心率变异性分析
    # 检测正常RR间期范围（600-1000ms）
    normal_rr_ranges = (valid_rr >= 600) & (valid_rr <= 1000)

    # 计算连续正常RR间期的最大数量
    consecutive_count = 0
    max_consecutive = 0
    for is_normal in normal_rr_ranges:
        if is_normal:
            consecutive_count += 1
            max_consecutive = max(max_consecutive, consecutive_count)
        else:
            consecutive_count = 0

    # 如果存在至少5个连续的正常RR间期且心率在60-100之间，认为信号质量好
    if max_consecutive >= 5 and 60 <= heart_rate <= 100:
        return 1

    # 6. 心率变异性频谱分析
    if rr_cv > 0.8:
        if len(valid_rr) > 10:
            # 使用Welch方法计算功率谱
            f, Pxx = signal.welch(valid_rr, fs=1.0, nperseg=min(len(valid_rr), 10))

            # 计算低频功率（0.04-0.15Hz）占总功率的比例
            low_freq_power = np.sum(Pxx[(f >= 0.04) & (f <= 0.15)])
            total_power = np.sum(Pxx)

            # 如果低频功率比例不足30%或RR间期不符合正态分布，认为信号质量差
            if low_freq_power / total_power > 0.3 and stats.normaltest(valid_rr)[1] < 0.05:
                pass
            else:
                return -1

    # 7. QRS波形相似性分析
    qrs_width = int(0.12 * sampling_rate)
    qrs_segments = []

    # 提取QRS波群片段
    for peak in peaks:
        if peak - qrs_width // 2 >= 0 and peak + qrs_width // 2 < len(filtered_ecg):
            segment = filtered_ecg[peak - qrs_width // 2:peak + qrs_width // 2]
            if np.max(segment) - np.min(segment) > 1e-6:
                segment = (segment - np.min(segment)) / (np.max(segment) - np.min(segment) + 1e-6)
                qrs_segments.append(segment)

    # 计算相邻QRS波群的相关系数
    correlations = []
    if len(qrs_segments) > 3:
        for i in range(len(qrs_segments) - 1):
            try:
                corr = np.corrcoef(qrs_segments[i], qrs_segments[i+1])[0, 1]
                if not np.isnan(corr):
                     correlations.append(corr)
            except Exception as corr_err:
                 pass

        if correlations:
            avg_correlation = np.mean(correlations)
            corr_std = np.std(correlations)

            # 如果QRS波群相似度低且变异小，认为存在噪声
            if avg_correlation < 0.3 and corr_std < 0.15:
                return -1

    # 8. 呼吸运动对ECG信号造成的干扰检测
    fc = 0.5
    w = fc / (sampling_rate / 2)
    b, a = signal.butter(2, w, 'low')
    baseline = signal.filtfilt(b, a, ecg_data)

    if np.std(baseline) > 0.5:
        # 分析基线信号的呼吸频率成分（0.1-0.4Hz）
        f, Pxx = signal.welch(baseline, fs=sampling_rate, nperseg=sampling_rate * 4)
        resp_power = np.sum(Pxx[(f >= 0.1) & (f <= 0.4)])
        total_power = np.sum(Pxx)

        # 如果呼吸频率成分不足30%，认为存在异常
        if total_power > 0 and resp_power / total_power < 0.3:
            return -1

    # 9. 工频干扰检测
    f, Pxx = signal.welch(ecg_data, fs=sampling_rate, nperseg=sampling_rate * 2)
    power_line_freq = 50
    line_idx = np.argmin(np.abs(f - power_line_freq))

    line_power = np.sum(Pxx[max(0, line_idx - 1):min(len(Pxx), line_idx + 2)])
    total_power = np.sum(Pxx)

    # 如果工频干扰功率超过总功率的40%，认为存在严重工频干扰
    if total_power > 0 and line_power / total_power > 0.4:
        return -1

    # 10. 信号饱和检测
    range_percent = 0.05
    signal_range = np.max(ecg_data) - np.min(ecg_data)
    if signal_range > 1e-6:
        # 计算信号饱和阈值
        high_threshold = np.max(ecg_data) - signal_range * range_percent
        low_threshold = np.min(ecg_data) + signal_range * range_percent

        # 统计饱和样本数量
        high_samples = np.sum(ecg_data > high_threshold)
        low_samples = np.sum(ecg_data < low_threshold)
        saturation_percent = (high_samples + low_samples) / len(ecg_data)

        # 如果饱和样本比例超过15%，认为信号质量差
        if saturation_percent > 0.15:
            return -1

    # try:
    #     ecg_data_resampled = resample(ecg_data, sampling_rate)
    #
    #     current_file_path = os.path.abspath(__file__)
    #     current_dir = os.path.dirname(current_file_path)
    #     model_path = os.path.join(current_dir, 'model', 'noise_model.h5')
    #     if not os.path.exists(model_path):
    #         return 1
    #
    #     model = tf.keras.models.load_model(model_path)
    #     input_data = np.expand_dims([ecg_data_resampled], axis=2)
    #     if input_data.shape[1] != model.input_shape[1]:
    #          return 1
    #
    #     predict = model.predict(input_data)
    #
    #     if predict[0][0] < 0.1:
    #         return -1
    # except Exception as dl_err:
    #     return 1

    return 1

