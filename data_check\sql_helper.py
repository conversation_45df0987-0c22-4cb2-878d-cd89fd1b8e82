import os
import sys
from dbutils.pooled_db import PooledDB
import pymysql
from global_settings import DATABASES
from utils.logger_helper import Logger


class MysqlHelper:
    """
    Mysql帮助类，使用连接池
    """
    pool = None

    def __init__(self, connect_name):
        self.host = DATABASES[connect_name]["HOST"]
        self.port = DATABASES[connect_name]["PORT"]
        self.user = DATABASES[connect_name]["USER"]
        self.password = DATABASES[connect_name]["PASSWORD"]
        self.dbName = DATABASES[connect_name]["DBNAME"]
        self.charset = DATABASES[connect_name]["CHARSET"]

        # 创建连接池
        self.create_pool()

    def create_pool(self):
        """ 创建连接池 """
        self.pool = PooledDB(
            creator=pymysql,
            maxconnections=10,
            mincached=2,
            maxcached=5,
            maxshared=3,
            blocking=True,
            host=self.host,
            port=self.port,
            user=self.user,
            passwd=self.password,
            db=self.dbName,
            charset=self.charset
        )

    def get_con(self):
        """ 获取数据库连接 """
        return self.pool.connection()

    def close(self, conn):
        """ 关闭数据库连接 """
        conn.close()

    def execute_sql(self, sql, params=None):
        """
        执行sql语句
        :param sql: 需要执行的sql语句
        :param params: sql参数
        :return: 返回执行结果
        """
        result = None
        conn = self.get_con()
        cursor = conn.cursor()
        try:
            if params:
                result = cursor.execute(sql, params)
            else:
                result = cursor.execute(sql)
            conn.commit()
        except Exception as e:
            Logger().error(str(e))
            conn.rollback()
        finally:
            cursor.close()
            self.close(conn)

        return result

    def datas_query(self, sql, params=()):
        """
        查询数据：
        :param sql: 需要查询的sql
        :param params:  查询参数集合
        :return:
        """
        result = None
        conn = self.get_con()
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            result = cursor.fetchall()
        except Exception as e:
            Logger().error(str(e))
        finally:
            cursor.close()
            self.close(conn)

        return result

    def insert_data(self, table, data):
        """
        插入单条数据：insert_data(table='tableName', data={'name': 'DLInsert'})
        :param table: 插入的表名
        :param data:  需要插入的字段
        :return:
        """
        results = None
        conn = self.get_con()
        cursor = conn.cursor()

        try:
            col_str = '(' + ','.join(data.keys()) + ')'
            data_str = ''
            for _ in data.keys():
                data_str += '%s,'
            data_str = data_str[:-1]
            data_str = '(' + data_str + ')'
            sql = 'insert into ' + table + col_str + ' values ' + data_str
            params = list(data.values())
            results = cursor.execute(sql, params)
            conn.commit()

            # 获取新插入行的主键ID
            return cursor.lastrowid
        except Exception as e:
            Logger().error(str(e))
            conn.rollback()
        finally:
            cursor.close()
            self.close(conn)

        return results

    def insert_datas(self, table, datas):
        """
        插入多条数据：insert_datas(table='tableName', data=[['name','account'], ['DL','123'], ['DL2','456']])
        :param table: 插入的表名
        :param datas:  需要插入的字段集合
        :return:
        """
        results = None
        conn = self.get_con()
        cursor = conn.cursor()

        try:
            # 列
            col_str = '(' + ','.join(datas[0]) + ')'
            # 值
            data_str = ''
            params = []
            for _ in datas[1:]:
                data_str += '('
                for __ in _:
                    data_str += '%s,'
                    params.append(__)
                data_str = data_str[:-1] + '),'
            data_str = data_str[:-1]

            sql = 'insert into ' + table + col_str + ' values ' + data_str
            results = cursor.execute(sql, params)
            conn.commit()
        except Exception as e:
            Logger().error(str(e))
            conn.rollback()
        finally:
            cursor.close()
            self.close(conn)

        return results

    def update_data(self, table, data, where):
        """
        更新数据: update(table='tableName', data={'name': 'DLTest'}, where='id = 2')
        :param table: 插入的表名
        :param data:  需要更新的字段
        :param where: 过滤条件
        :return:
        """
        results = None
        conn = self.get_con()
        cursor = conn.cursor()

        try:
            set_str = ', '.join([f"{k} = %s" for k in data.keys()])
            where_str = ' WHERE ' + where
            sql = f'UPDATE {table} SET {set_str}{where_str}'
            params = list(data.values())
            results = cursor.execute(sql, params)
            conn.commit()
        except Exception as e:
            Logger().error(str(e))
            conn.rollback()
        finally:
            cursor.close()
            self.close(conn)

        return results