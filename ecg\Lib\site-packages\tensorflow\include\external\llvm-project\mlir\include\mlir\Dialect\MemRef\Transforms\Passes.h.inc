/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// FoldSubViewOps
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class FoldSubViewOpsBase : public ::mlir::OperationPass<> {
public:
  using Base = FoldSubViewOpsBase;

  FoldSubViewOpsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  FoldSubViewOpsBase(const FoldSubViewOpsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("fold-memref-subview-ops");
  }
  ::llvm::StringRef getArgument() const override { return "fold-memref-subview-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fold memref.subview ops into consumer load/store ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FoldSubViewOps");
  }
  ::llvm::StringRef getName() const override { return "FoldSubViewOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<vector::VectorDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ResolveShapedTypeResultDims
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ResolveShapedTypeResultDimsBase : public ::mlir::OperationPass<> {
public:
  using Base = ResolveShapedTypeResultDimsBase;

  ResolveShapedTypeResultDimsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ResolveShapedTypeResultDimsBase(const ResolveShapedTypeResultDimsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("resolve-shaped-type-result-dims");
  }
  ::llvm::StringRef getArgument() const override { return "resolve-shaped-type-result-dims"; }

  ::llvm::StringRef getDescription() const override { return "Resolve memref.dim of result values"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResolveShapedTypeResultDims");
  }
  ::llvm::StringRef getName() const override { return "ResolveShapedTypeResultDims"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<tensor::TensorDialect>();

  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// FoldSubViewOps Registration
//===----------------------------------------------------------------------===//

inline void registerFoldSubViewOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createFoldSubViewOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// ResolveShapedTypeResultDims Registration
//===----------------------------------------------------------------------===//

inline void registerResolveShapedTypeResultDimsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::memref::createResolveShapedTypeResultDimsPass();
  });
}

//===----------------------------------------------------------------------===//
// MemRef Registration
//===----------------------------------------------------------------------===//

inline void registerMemRefPasses() {
  registerFoldSubViewOpsPass();
  registerResolveShapedTypeResultDimsPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
