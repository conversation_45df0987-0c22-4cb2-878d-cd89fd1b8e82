import json
import traceback
from django.http import HttpResponse
import re

from apps.utils.logger_helper import Logger


class GetResponse(object):
    """
    获取返回对象
    """


    @staticmethod
    def get_response(code, data=None):
        """
        封装响应函数
        :param code: 返回编码
        :param param:
        :param data:
        :param msg:
        :return:
        """
        code_dict = {
            0: '正常',
            1: '鉴权失败',
            2: '内部异常',
            3: '用户验证失败',
            4: 'token验证失败',
            5: '参数缺失',
            6: '参数异常',
            7: '心电数据异常，无法分析',
            8: '平直线+高频振荡噪声，信号质量极差'
        }

        if code == 0:
            msg = 'SUCCESS'
        else:
            msg = 'FAIL'
            if data is None:
                data = code_dict[code]

        try:
            content = {'code': code, 'data': data, 'msg': msg}
            content = json.dumps(content)

            response = HttpResponse(re.sub('None', '', content, flags=re.IGNORECASE))
            response['Access-Control-Allow-Origin'] = '*'
            return response
        except Exception:
            # 记录错误日志
            Logger().error(f'响应函数封装异常：{traceback.format_exc()}')

            content = {'code': 5, 'data': data, 'msg': msg}
            content = json.dumps(content, ensure_ascii=False)
            response = HttpResponse(re.sub('None', '', content, flags=re.IGNORECASE))
            response['Access-Control-Allow-Origin'] = '*'
            return response

