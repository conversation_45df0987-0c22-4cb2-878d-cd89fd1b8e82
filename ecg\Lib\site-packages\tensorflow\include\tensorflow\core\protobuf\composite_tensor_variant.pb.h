// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/composite_tensor_variant.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/struct.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto;
namespace tensorflow {
class CompositeTensorVariantMetadata;
class CompositeTensorVariantMetadataDefaultTypeInternal;
extern CompositeTensorVariantMetadataDefaultTypeInternal _CompositeTensorVariantMetadata_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CompositeTensorVariantMetadata* Arena::CreateMaybeMessage<::tensorflow::CompositeTensorVariantMetadata>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class CompositeTensorVariantMetadata :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompositeTensorVariantMetadata) */ {
 public:
  CompositeTensorVariantMetadata();
  virtual ~CompositeTensorVariantMetadata();

  CompositeTensorVariantMetadata(const CompositeTensorVariantMetadata& from);
  CompositeTensorVariantMetadata(CompositeTensorVariantMetadata&& from) noexcept
    : CompositeTensorVariantMetadata() {
    *this = ::std::move(from);
  }

  inline CompositeTensorVariantMetadata& operator=(const CompositeTensorVariantMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompositeTensorVariantMetadata& operator=(CompositeTensorVariantMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CompositeTensorVariantMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompositeTensorVariantMetadata* internal_default_instance() {
    return reinterpret_cast<const CompositeTensorVariantMetadata*>(
               &_CompositeTensorVariantMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CompositeTensorVariantMetadata& a, CompositeTensorVariantMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(CompositeTensorVariantMetadata* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CompositeTensorVariantMetadata* New() const final {
    return CreateMaybeMessage<CompositeTensorVariantMetadata>(nullptr);
  }

  CompositeTensorVariantMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CompositeTensorVariantMetadata>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CompositeTensorVariantMetadata& from);
  void MergeFrom(const CompositeTensorVariantMetadata& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompositeTensorVariantMetadata* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CompositeTensorVariantMetadata";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypeSpecProtoFieldNumber = 1,
  };
  // .tensorflow.TypeSpecProto type_spec_proto = 1;
  bool has_type_spec_proto() const;
  void clear_type_spec_proto();
  const ::tensorflow::TypeSpecProto& type_spec_proto() const;
  ::tensorflow::TypeSpecProto* release_type_spec_proto();
  ::tensorflow::TypeSpecProto* mutable_type_spec_proto();
  void set_allocated_type_spec_proto(::tensorflow::TypeSpecProto* type_spec_proto);

  // @@protoc_insertion_point(class_scope:tensorflow.CompositeTensorVariantMetadata)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::TypeSpecProto* type_spec_proto_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CompositeTensorVariantMetadata

// .tensorflow.TypeSpecProto type_spec_proto = 1;
inline bool CompositeTensorVariantMetadata::has_type_spec_proto() const {
  return this != internal_default_instance() && type_spec_proto_ != nullptr;
}
inline const ::tensorflow::TypeSpecProto& CompositeTensorVariantMetadata::type_spec_proto() const {
  const ::tensorflow::TypeSpecProto* p = type_spec_proto_;
  // @@protoc_insertion_point(field_get:tensorflow.CompositeTensorVariantMetadata.type_spec_proto)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TypeSpecProto*>(
      &::tensorflow::_TypeSpecProto_default_instance_);
}
inline ::tensorflow::TypeSpecProto* CompositeTensorVariantMetadata::release_type_spec_proto() {
  // @@protoc_insertion_point(field_release:tensorflow.CompositeTensorVariantMetadata.type_spec_proto)
  
  ::tensorflow::TypeSpecProto* temp = type_spec_proto_;
  type_spec_proto_ = nullptr;
  return temp;
}
inline ::tensorflow::TypeSpecProto* CompositeTensorVariantMetadata::mutable_type_spec_proto() {
  
  if (type_spec_proto_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TypeSpecProto>(GetArenaNoVirtual());
    type_spec_proto_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CompositeTensorVariantMetadata.type_spec_proto)
  return type_spec_proto_;
}
inline void CompositeTensorVariantMetadata::set_allocated_type_spec_proto(::tensorflow::TypeSpecProto* type_spec_proto) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(type_spec_proto_);
  }
  if (type_spec_proto) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      type_spec_proto = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type_spec_proto, submessage_arena);
    }
    
  } else {
    
  }
  type_spec_proto_ = type_spec_proto;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompositeTensorVariantMetadata.type_spec_proto)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto
