/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// BroadcastPropagationPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class BroadcastPropagationPassBase : public ::mlir::FunctionPass {
public:
  using Base = BroadcastPropagationPassBase;

  BroadcastPropagationPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  BroadcastPropagationPassBase(const BroadcastPropagationPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-broadcast-propagation");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-broadcast-propagation"; }

  ::llvm::StringRef getDescription() const override { return "Move dynamic broadcasts up over element-wise operations and broadcast the operands rather than the result. This will eventually allow for larger fusions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BroadcastPropagationPass");
  }
  ::llvm::StringRef getName() const override { return "BroadcastPropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// ChloLegalizeToHloPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ChloLegalizeToHloPassBase : public ::mlir::FunctionPass {
public:
  using Base = ChloLegalizeToHloPassBase;

  ChloLegalizeToHloPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ChloLegalizeToHloPassBase(const ChloLegalizeToHloPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("chlo-legalize-to-hlo");
  }
  ::llvm::StringRef getArgument() const override { return "chlo-legalize-to-hlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize CHLO to HLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ChloLegalizeToHloPass");
  }
  ::llvm::StringRef getName() const override { return "ChloLegalizeToHloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> legalize_broadcasts_{*this, "legalize-broadcasts", ::llvm::cl::desc("Legalize implicit broadcasts to explicit HLO broadcasting forms"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> expand_compositions_{*this, "expand-compositions", ::llvm::cl::desc("Expands client-centric compositions to HLO primitives"), ::llvm::cl::init(true)};
};

//===----------------------------------------------------------------------===//
// HloLegalizeToLhloPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class HloLegalizeToLhloPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = HloLegalizeToLhloPassBase;

  HloLegalizeToLhloPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  HloLegalizeToLhloPassBase(const HloLegalizeToLhloPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-legalize-to-lhlo");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-legalize-to-lhlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from HLO dialect to LHLO dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HloLegalizeToLhloPass");
  }
  ::llvm::StringRef getName() const override { return "HloLegalizeToLhloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> convert_to_lmhlo_only_{*this, "convert-to-lmhlo-only", ::llvm::cl::desc("If enabled, simply lower all mhlo ops to their lmhlo counterparts, otherwise, some metadata-only ops (e.g. reshape) may be lowerred to memref dialect to elide some buffer copy."), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// HloLegalizeToLinalgPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class HloLegalizeToLinalgPassBase : public ::mlir::FunctionPass {
public:
  using Base = HloLegalizeToLinalgPassBase;

  HloLegalizeToLinalgPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  HloLegalizeToLinalgPassBase(const HloLegalizeToLinalgPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-legalize-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-legalize-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from HLO dialect to Linalg dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HloLegalizeToLinalgPass");
  }
  ::llvm::StringRef getName() const override { return "HloLegalizeToLinalgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LegalizeControlFlowPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeControlFlowPassBase : public ::mlir::FunctionPass {
public:
  using Base = LegalizeControlFlowPassBase;

  LegalizeControlFlowPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeControlFlowPassBase(const LegalizeControlFlowPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-control-flow");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-control-flow"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from MHLO control flow to CFG control flow."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeControlFlowPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeControlFlowPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LegalizeControlFlowToScfPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeControlFlowToScfPassBase : public ::mlir::FunctionPass {
public:
  using Base = LegalizeControlFlowToScfPassBase;

  LegalizeControlFlowToScfPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeControlFlowToScfPassBase(const LegalizeControlFlowToScfPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-control-flow-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-control-flow-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from MHLO control flow to SCF control flow."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeControlFlowToScfPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeControlFlowToScfPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LegalizeEinsumToDotGeneralPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeEinsumToDotGeneralPassBase : public ::mlir::FunctionPass {
public:
  using Base = LegalizeEinsumToDotGeneralPassBase;

  LegalizeEinsumToDotGeneralPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeEinsumToDotGeneralPassBase(const LegalizeEinsumToDotGeneralPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-einsum-to-dot-general");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-einsum-to-dot-general"; }

  ::llvm::StringRef getDescription() const override { return "Legalizes einsum ops to dot_general ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeEinsumToDotGeneralPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeEinsumToDotGeneralPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LegalizeGatherToTorchIndexSelectPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeGatherToTorchIndexSelectPassBase : public ::mlir::FunctionPass {
public:
  using Base = LegalizeGatherToTorchIndexSelectPassBase;

  LegalizeGatherToTorchIndexSelectPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeGatherToTorchIndexSelectPassBase(const LegalizeGatherToTorchIndexSelectPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-gather-to-torch-index-select");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-gather-to-torch-index-select"; }

  ::llvm::StringRef getDescription() const override { return "Legalizes gathers to a torch index select."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeGatherToTorchIndexSelectPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeGatherToTorchIndexSelectPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LegalizeGeneralDotPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeGeneralDotPassBase : public ::mlir::FunctionPass {
public:
  using Base = LegalizeGeneralDotPassBase;

  LegalizeGeneralDotPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeGeneralDotPassBase(const LegalizeGeneralDotPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-lower-general-dot");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-lower-general-dot"; }

  ::llvm::StringRef getDescription() const override { return "Tests lowering general dot to a non-batched dot when possible."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeGeneralDotPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeGeneralDotPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LegalizeTanhToApproximationPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeTanhToApproximationPassBase : public ::mlir::FunctionPass {
public:
  using Base = LegalizeTanhToApproximationPassBase;

  LegalizeTanhToApproximationPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTanhToApproximationPassBase(const LegalizeTanhToApproximationPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-trigonometric-to-approximation");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-trigonometric-to-approximation"; }

  ::llvm::StringRef getDescription() const override { return "Legalize trigonometric operations from standard dialect to an approximation."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTanhToApproximationPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTanhToApproximationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LegalizeToStandardPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LegalizeToStandardPassBase : public ::mlir::FunctionPass {
public:
  using Base = LegalizeToStandardPassBase;

  LegalizeToStandardPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeToStandardPassBase(const LegalizeToStandardPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from MHLO dialect to standard dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeToStandardPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeToStandardPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LowerComplexPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LowerComplexPassBase : public ::mlir::FunctionPass {
public:
  using Base = LowerComplexPassBase;

  LowerComplexPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LowerComplexPassBase(const LowerComplexPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-lower-complex");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-lower-complex"; }

  ::llvm::StringRef getDescription() const override { return "Lower complex operations into non-complex operations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerComplexPass");
  }
  ::llvm::StringRef getName() const override { return "LowerComplexPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// MhloFusionPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class MhloFusionPassBase : public ::mlir::FunctionPass {
public:
  using Base = MhloFusionPassBase;

  MhloFusionPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  MhloFusionPassBase(const MhloFusionPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse mhlo ops to kLoop/kInput fusion patterns."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MhloFusionPass");
  }
  ::llvm::StringRef getName() const override { return "MhloFusionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// OptimizeMhloPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class OptimizeMhloPassBase : public ::mlir::FunctionPass {
public:
  using Base = OptimizeMhloPassBase;

  OptimizeMhloPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  OptimizeMhloPassBase(const OptimizeMhloPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-optimize");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-optimize"; }

  ::llvm::StringRef getDescription() const override { return "Run optional HLO optimizations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OptimizeMhloPass");
  }
  ::llvm::StringRef getName() const override { return "OptimizeMhloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// RankSpecializationClusterPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class RankSpecializationClusterPassBase : public ::mlir::FunctionPass {
public:
  using Base = RankSpecializationClusterPassBase;

  RankSpecializationClusterPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  RankSpecializationClusterPassBase(const RankSpecializationClusterPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-rank-specialization-cluster");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-rank-specialization-cluster"; }

  ::llvm::StringRef getDescription() const override { return ""; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RankSpecializationClusterPass");
  }
  ::llvm::StringRef getName() const override { return "RankSpecializationClusterPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// RankSpecializationToSCFPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class RankSpecializationToSCFPassBase : public ::mlir::FunctionPass {
public:
  using Base = RankSpecializationToSCFPassBase;

  RankSpecializationToSCFPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  RankSpecializationToSCFPassBase(const RankSpecializationToSCFPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-rank-specialization-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-rank-specialization-to-scf"; }

  ::llvm::StringRef getDescription() const override { return ""; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RankSpecializationToSCFPass");
  }
  ::llvm::StringRef getName() const override { return "RankSpecializationToSCFPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<int> max_target_rank_{*this, "max-target-rank", ::llvm::cl::desc("The maximum supported rank after rank specialization. Any argument of greater rank may result in a runtime failure."), ::llvm::cl::init(8)};
};

//===----------------------------------------------------------------------===//
// SinkConstantsToControlFlowPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SinkConstantsToControlFlowPassBase : public ::mlir::FunctionPass {
public:
  using Base = SinkConstantsToControlFlowPassBase;

  SinkConstantsToControlFlowPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  SinkConstantsToControlFlowPassBase(const SinkConstantsToControlFlowPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-sink-constants-to-control-flow");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-sink-constants-to-control-flow"; }

  ::llvm::StringRef getDescription() const override { return "Sink constants implicitly captured in control flow regions. This is necessary to export to XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SinkConstantsToControlFlowPass");
  }
  ::llvm::StringRef getName() const override { return "SinkConstantsToControlFlowPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TestInferShapedTypeMethodsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TestInferShapedTypeMethodsPassBase : public ::mlir::FunctionPass {
public:
  using Base = TestInferShapedTypeMethodsPassBase;

  TestInferShapedTypeMethodsPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TestInferShapedTypeMethodsPassBase(const TestInferShapedTypeMethodsPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-infer-shaped-type-methods");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-infer-shaped-type-methods"; }

  ::llvm::StringRef getDescription() const override { return "Uses test ops to invoke InferShapedTypeOpInterface methods."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestInferShapedTypeMethodsPass");
  }
  ::llvm::StringRef getName() const override { return "TestInferShapedTypeMethodsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TestMaterializeBroadcastsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TestMaterializeBroadcastsPassBase : public ::mlir::FunctionPass {
public:
  using Base = TestMaterializeBroadcastsPassBase;

  TestMaterializeBroadcastsPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TestMaterializeBroadcastsPassBase(const TestMaterializeBroadcastsPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-materialize-broadcasts");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-materialize-broadcasts"; }

  ::llvm::StringRef getDescription() const override { return "Test pass for materializing 'broadcast_dimensions' attributes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestMaterializeBroadcastsPass");
  }
  ::llvm::StringRef getName() const override { return "TestMaterializeBroadcastsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TestUnfuseBatchNormPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TestUnfuseBatchNormPassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = TestUnfuseBatchNormPassBase;

  TestUnfuseBatchNormPassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TestUnfuseBatchNormPassBase(const TestUnfuseBatchNormPassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-unfuse-batch-norm");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-unfuse-batch-norm"; }

  ::llvm::StringRef getDescription() const override { return "Test pass for materializing 'broadcast_dimensions' attributes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestUnfuseBatchNormPass");
  }
  ::llvm::StringRef getName() const override { return "TestUnfuseBatchNormPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// BroadcastPropagationPass Registration
//===----------------------------------------------------------------------===//

inline void registerBroadcastPropagationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createBroadcastPropagationPass();
  });
}

//===----------------------------------------------------------------------===//
// ChloLegalizeToHloPass Registration
//===----------------------------------------------------------------------===//

inline void registerChloLegalizeToHloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createChloLegalizeToHloPass();
  });
}

//===----------------------------------------------------------------------===//
// HloLegalizeToLhloPass Registration
//===----------------------------------------------------------------------===//

inline void registerHloLegalizeToLhloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeToLhloPass();
  });
}

//===----------------------------------------------------------------------===//
// HloLegalizeToLinalgPass Registration
//===----------------------------------------------------------------------===//

inline void registerHloLegalizeToLinalgPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeHloToLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeControlFlowPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeControlFlowPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeControlFlowPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeControlFlowToScfPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeControlFlowToScfPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createControlFlowToScfPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeEinsumToDotGeneralPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeEinsumToDotGeneralPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeEinsumToDotGeneralPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeGatherToTorchIndexSelectPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeGatherToTorchIndexSelectPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeGatherToTorchIndexSelectPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeGeneralDotPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeGeneralDotPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeGeneralDotPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTanhToApproximationPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTanhToApproximationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeTrigonometricToApproximationPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeToStandardPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeToStandardPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeToStdPass();
  });
}

//===----------------------------------------------------------------------===//
// LowerComplexPass Registration
//===----------------------------------------------------------------------===//

inline void registerLowerComplexPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLowerComplexPass();
  });
}

//===----------------------------------------------------------------------===//
// MhloFusionPass Registration
//===----------------------------------------------------------------------===//

inline void registerMhloFusionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createMhloFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// OptimizeMhloPass Registration
//===----------------------------------------------------------------------===//

inline void registerOptimizeMhloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createOptimizeMhloPass();
  });
}

//===----------------------------------------------------------------------===//
// RankSpecializationClusterPass Registration
//===----------------------------------------------------------------------===//

inline void registerRankSpecializationClusterPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createRankSpecializationClusterPass();
  });
}

//===----------------------------------------------------------------------===//
// RankSpecializationToSCFPass Registration
//===----------------------------------------------------------------------===//

inline void registerRankSpecializationToSCFPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createRankSpecializationToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// SinkConstantsToControlFlowPass Registration
//===----------------------------------------------------------------------===//

inline void registerSinkConstantsToControlFlowPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSinkConstantsToControlFlowPass();
  });
}

//===----------------------------------------------------------------------===//
// TestInferShapedTypeMethodsPass Registration
//===----------------------------------------------------------------------===//

inline void registerTestInferShapedTypeMethodsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTestInferShapedTypeMethodsPass();
  });
}

//===----------------------------------------------------------------------===//
// TestMaterializeBroadcastsPass Registration
//===----------------------------------------------------------------------===//

inline void registerTestMaterializeBroadcastsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTestMaterializeBroadcastsPass();
  });
}

//===----------------------------------------------------------------------===//
// TestUnfuseBatchNormPass Registration
//===----------------------------------------------------------------------===//

inline void registerTestUnfuseBatchNormPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTestUnfuseBatchNormPass();
  });
}

//===----------------------------------------------------------------------===//
// MHLO Registration
//===----------------------------------------------------------------------===//

inline void registerMHLOPasses() {
  registerBroadcastPropagationPassPass();
  registerChloLegalizeToHloPassPass();
  registerHloLegalizeToLhloPassPass();
  registerHloLegalizeToLinalgPassPass();
  registerLegalizeControlFlowPassPass();
  registerLegalizeControlFlowToScfPassPass();
  registerLegalizeEinsumToDotGeneralPassPass();
  registerLegalizeGatherToTorchIndexSelectPassPass();
  registerLegalizeGeneralDotPassPass();
  registerLegalizeTanhToApproximationPassPass();
  registerLegalizeToStandardPassPass();
  registerLowerComplexPassPass();
  registerMhloFusionPassPass();
  registerOptimizeMhloPassPass();
  registerRankSpecializationClusterPassPass();
  registerRankSpecializationToSCFPassPass();
  registerSinkConstantsToControlFlowPassPass();
  registerTestInferShapedTypeMethodsPassPass();
  registerTestMaterializeBroadcastsPassPass();
  registerTestUnfuseBatchNormPassPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
