"""
请求队列管理器，控制并发请求数量，防止内存溢出
"""
import time
import threading
import queue
from typing import Callable, Any, Optional
from functools import wraps
from apps.utils.logger_helper import Logger
from apps.utils.memory_monitor import get_memory_monitor


class RequestQueue:
    """请求队列管理器"""
    
    def __init__(self, max_concurrent: int = 3, max_queue_size: int = 50):
        """
        初始化请求队列
        
        Args:
            max_concurrent: 最大并发处理数量
            max_queue_size: 最大队列长度
        """
        self.max_concurrent = max_concurrent
        self.max_queue_size = max_queue_size
        self.current_processing = 0
        self.request_queue = queue.Queue(maxsize=max_queue_size)
        self.processing_lock = threading.Lock()
        self.worker_threads = []
        self.running = True
        
        # 启动工作线程
        for i in range(max_concurrent):
            worker = threading.Thread(
                target=self._worker_loop, 
                name=f"RequestWorker-{i}",
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)
        
        Logger().info(f"RequestQueue initialized with {max_concurrent} workers, queue size: {max_queue_size}")
    
    def _worker_loop(self):
        """工作线程循环"""
        while self.running:
            try:
                # 从队列获取请求
                request_item = self.request_queue.get(timeout=1.0)
                if request_item is None:  # 停止信号
                    break
                
                func, args, kwargs, result_queue, request_id = request_item
                
                with self.processing_lock:
                    self.current_processing += 1
                
                try:
                    # 检查内存状态
                    memory_monitor = get_memory_monitor()
                    memory_info = memory_monitor.get_memory_info()
                    
                    if memory_info['memory_percent'] > 90:
                        Logger().warning(f"High memory usage before processing request {request_id}: {memory_info['memory_percent']:.1f}%")
                        # 触发紧急清理
                        memory_monitor.emergency_cleanup()
                        time.sleep(0.5)  # 等待清理完成
                    
                    # 执行请求
                    Logger().debug(f"Processing request {request_id}")
                    start_time = time.time()
                    result = func(*args, **kwargs)
                    end_time = time.time()
                    
                    # 将结果放入结果队列
                    result_queue.put(('success', result))
                    
                    Logger().debug(f"Request {request_id} completed in {end_time - start_time:.2f}s")
                    
                except Exception as e:
                    Logger().error(f"Request {request_id} failed: {e}")
                    result_queue.put(('error', e))
                
                finally:
                    with self.processing_lock:
                        self.current_processing -= 1
                    
                    # 标记任务完成
                    self.request_queue.task_done()
                    
            except queue.Empty:
                continue
            except Exception as e:
                Logger().error(f"Worker thread error: {e}")
    
    def submit_request(self, func: Callable, *args, timeout: float = 30.0, **kwargs) -> Any:
        """
        提交请求到队列
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            timeout: 超时时间
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            queue.Full: 队列已满
            TimeoutError: 请求超时
            Exception: 函数执行异常
        """
        if not self.running:
            raise RuntimeError("RequestQueue is not running")
        
        request_id = f"{int(time.time() * 1000)}-{threading.current_thread().ident}"
        result_queue = queue.Queue(maxsize=1)
        
        try:
            # 将请求放入队列
            request_item = (func, args, kwargs, result_queue, request_id)
            self.request_queue.put(request_item, timeout=1.0)
            
            Logger().debug(f"Request {request_id} queued, queue size: {self.request_queue.qsize()}")
            
            # 等待结果
            try:
                status, result = result_queue.get(timeout=timeout)
                if status == 'success':
                    return result
                else:
                    raise result
            except queue.Empty:
                raise TimeoutError(f"Request {request_id} timed out after {timeout}s")
                
        except queue.Full:
            raise queue.Full("Request queue is full, please try again later")
    
    def get_status(self) -> dict:
        """获取队列状态"""
        return {
            'running': self.running,
            'current_processing': self.current_processing,
            'queue_size': self.request_queue.qsize(),
            'max_concurrent': self.max_concurrent,
            'max_queue_size': self.max_queue_size,
            'worker_threads': len([t for t in self.worker_threads if t.is_alive()])
        }
    
    def shutdown(self, timeout: float = 5.0):
        """关闭队列管理器"""
        Logger().info("Shutting down RequestQueue")
        self.running = False
        
        # 发送停止信号给所有工作线程
        for _ in self.worker_threads:
            try:
                self.request_queue.put(None, timeout=0.1)
            except queue.Full:
                pass
        
        # 等待工作线程结束
        for worker in self.worker_threads:
            worker.join(timeout=timeout)
        
        Logger().info("RequestQueue shutdown completed")


# 全局请求队列实例
_global_request_queue: Optional[RequestQueue] = None
_queue_lock = threading.Lock()


def get_request_queue() -> RequestQueue:
    """获取全局请求队列实例"""
    global _global_request_queue
    
    if _global_request_queue is None:
        with _queue_lock:
            if _global_request_queue is None:
                _global_request_queue = RequestQueue(
                    max_concurrent=2,  # 减少并发数，避免内存压力
                    max_queue_size=20   # 减少队列大小
                )
    
    return _global_request_queue


def queued_request(timeout: float = 30.0):
    """
    装饰器：将函数调用放入请求队列
    
    Args:
        timeout: 请求超时时间
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            request_queue = get_request_queue()
            return request_queue.submit_request(func, *args, timeout=timeout, **kwargs)
        return wrapper
    return decorator


def with_rate_limit(calls_per_second: float = 2.0):
    """
    装饰器：限制函数调用频率
    
    Args:
        calls_per_second: 每秒允许的调用次数
    """
    min_interval = 1.0 / calls_per_second
    last_call_time = [0.0]  # 使用列表以便在闭包中修改
    call_lock = threading.Lock()
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            with call_lock:
                current_time = time.time()
                time_since_last_call = current_time - last_call_time[0]
                
                if time_since_last_call < min_interval:
                    sleep_time = min_interval - time_since_last_call
                    Logger().debug(f"Rate limiting: sleeping for {sleep_time:.2f}s")
                    time.sleep(sleep_time)
                
                last_call_time[0] = time.time()
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


class RequestLimiter:
    """请求限制器"""
    
    def __init__(self, max_requests_per_minute: int = 30):
        self.max_requests = max_requests_per_minute
        self.requests = []
        self.lock = threading.Lock()
    
    def can_process_request(self) -> bool:
        """检查是否可以处理新请求"""
        current_time = time.time()
        
        with self.lock:
            # 清理超过1分钟的请求记录
            self.requests = [req_time for req_time in self.requests 
                           if current_time - req_time < 60]
            
            # 检查是否超过限制
            if len(self.requests) >= self.max_requests:
                return False
            
            # 记录新请求
            self.requests.append(current_time)
            return True
    
    def get_wait_time(self) -> float:
        """获取需要等待的时间"""
        if not self.requests:
            return 0.0
        
        current_time = time.time()
        oldest_request = min(self.requests)
        return max(0, 60 - (current_time - oldest_request))


# 全局请求限制器
_global_limiter = RequestLimiter(max_requests_per_minute=20)  # 每分钟最多20个请求


def with_request_limit(func: Callable) -> Callable:
    """装饰器：添加请求频率限制"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not _global_limiter.can_process_request():
            wait_time = _global_limiter.get_wait_time()
            Logger().warning(f"Request rate limit exceeded, need to wait {wait_time:.1f}s")
            raise Exception(f"Request rate limit exceeded, please wait {wait_time:.1f} seconds")
        
        return func(*args, **kwargs)
    return wrapper
