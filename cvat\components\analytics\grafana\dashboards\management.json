{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["User 1"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 0}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.3.6", "targets": [{"builderOptions": {"fields": [], "filters": [], "groupBy": ["user_id"], "metrics": [{"aggregation": "count", "alias": "value", "field": "*"}], "mode": "trend", "orderBy": [], "table": "events", "timeField": "timestamp", "timeFieldType": "DateTime64(3, 'Etc/UTC')"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "format": 0, "hide": false, "meta": {"builderOptions": {"fields": [], "filters": [], "groupBy": ["user_id"], "metrics": [{"aggregation": "count", "alias": "value", "field": "*"}], "mode": "trend", "orderBy": [], "table": "events", "timeField": "timestamp", "timeFieldType": "DateTime64(3, 'Etc/UTC')"}}, "queryType": "sql", "rawSql": "SELECT $__timeInterval(timestamp) as time, toString(user_id), count() as User\r\nFROM events\r\nWHERE $__timeFilter(timestamp)\r\nGROUP BY time, user_id\r\nORDER BY time ASC, user_id ASC", "refId": "A"}], "title": "User Activity", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 90, "gradientMode": "scheme", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 9}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"builderOptions": {"fields": [], "filters": [], "metrics": [{"aggregation": "count", "alias": "Count", "field": "*"}], "mode": "trend", "orderBy": [], "table": "events", "timeField": "timestamp", "timeFieldType": "DateTime64(3, 'Etc/UTC')"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "format": 0, "queryType": "builder", "rawSql": "SELECT $__timeInterval(timestamp) as time,  count(*) Count FROM events WHERE $__timeFilter(timestamp) GROUP BY time ORDER BY time ASC", "refId": "A"}], "title": "Overall Activity", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false, "inspect": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 16}, "id": 2, "options": {"footer": {"enablePagination": true, "fields": ["Working time(h)", "Activity"], "reducer": ["sum"], "show": true}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT\r\n  user_id as User,\r\n  project_id as Project,\r\n  task_id as Task,\r\n  job_id as Job, sum(JSONExtractUInt(payload, 'working_time')) / 1000 / 3600 as \"Working time(h)\",\r\n  count() as Activity\r\nFROM events\r\nWHERE JSONHas(payload, 'working_time')\r\n  AND $__timeFilter(timestamp)\r\n  AND user_id IN (${users})\r\n  AND (-1 IN (${projects}) OR project_id IN (${projects}))\r\n  AND task_id IN (${tasks})\r\n  AND job_id IN (${jobs})\r\nGROUP BY user_id, project_id, task_id, job_id", "refId": "A"}], "title": "Working time", "type": "table"}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT user_id\nFROM events\nWHERE $__timeFilter(timestamp)\n  AND source = 'client'", "hide": 0, "includeAll": true, "label": "User", "multi": true, "name": "users", "options": [], "query": "SELECT DISTINCT user_id\nFROM events\nWHERE $__timeFilter(timestamp)\n  AND source = 'client'", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"allValue": "-1", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT project_id\nFROM events\nWHERE  $__timeFilter(timestamp)\n  AND project_id IS NOT NULL\n  AND source = 'client'", "hide": 0, "includeAll": true, "label": "Project", "multi": true, "name": "projects", "options": [], "query": "SELECT DISTINCT project_id\nFROM events\nWHERE  $__timeFilter(timestamp)\n  AND project_id IS NOT NULL\n  AND source = 'client'", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT task_id\nFROM events\nWHERE $__timeFilter(timestamp) \n  AND task_id IS NOT NULL\n  AND source = 'client'\n  AND (-1 IN (${projects}) OR project_id IN (${projects}))", "description": "", "hide": 0, "includeAll": true, "label": "Task", "multi": true, "name": "tasks", "options": [], "query": "SELECT DISTINCT task_id\nFROM events\nWHERE $__timeFilter(timestamp) \n  AND task_id IS NOT NULL\n  AND source = 'client'\n  AND (-1 IN (${projects}) OR project_id IN (${projects}))", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT job_id\nFROM events\nWHERE $__timeFilter(timestamp)\n  AND job_id IS NOT NULL\n  AND source = 'client'\n  AND task_id in (${tasks})", "hide": 0, "includeAll": true, "label": "Job", "multi": true, "name": "jobs", "options": [], "query": "SELECT DISTINCT job_id\nFROM events\nWHERE $__timeFilter(timestamp)\n  AND job_id IS NOT NULL\n  AND source = 'client'\n  AND task_id in (${tasks})", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Management", "uid": "w0if6WAVz", "version": 2, "weekStart": ""}