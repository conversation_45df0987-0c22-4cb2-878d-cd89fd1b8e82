// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/function.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffunction_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffunction_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/node_def.pb.h"
#include "tensorflow/core/framework/op_def.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2ffunction_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[11]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto;
namespace tensorflow {
class FunctionDef;
class FunctionDefDefaultTypeInternal;
extern FunctionDefDefaultTypeInternal _FunctionDef_default_instance_;
class FunctionDefLibrary;
class FunctionDefLibraryDefaultTypeInternal;
extern FunctionDefLibraryDefaultTypeInternal _FunctionDefLibrary_default_instance_;
class FunctionDef_ArgAttrEntry_DoNotUse;
class FunctionDef_ArgAttrEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_ArgAttrEntry_DoNotUseDefaultTypeInternal _FunctionDef_ArgAttrEntry_DoNotUse_default_instance_;
class FunctionDef_ArgAttrs;
class FunctionDef_ArgAttrsDefaultTypeInternal;
extern FunctionDef_ArgAttrsDefaultTypeInternal _FunctionDef_ArgAttrs_default_instance_;
class FunctionDef_ArgAttrs_AttrEntry_DoNotUse;
class FunctionDef_ArgAttrs_AttrEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_ArgAttrs_AttrEntry_DoNotUseDefaultTypeInternal _FunctionDef_ArgAttrs_AttrEntry_DoNotUse_default_instance_;
class FunctionDef_AttrEntry_DoNotUse;
class FunctionDef_AttrEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_AttrEntry_DoNotUseDefaultTypeInternal _FunctionDef_AttrEntry_DoNotUse_default_instance_;
class FunctionDef_ControlRetEntry_DoNotUse;
class FunctionDef_ControlRetEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_ControlRetEntry_DoNotUseDefaultTypeInternal _FunctionDef_ControlRetEntry_DoNotUse_default_instance_;
class FunctionDef_ResourceArgUniqueIdEntry_DoNotUse;
class FunctionDef_ResourceArgUniqueIdEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_ResourceArgUniqueIdEntry_DoNotUseDefaultTypeInternal _FunctionDef_ResourceArgUniqueIdEntry_DoNotUse_default_instance_;
class FunctionDef_RetEntry_DoNotUse;
class FunctionDef_RetEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_RetEntry_DoNotUseDefaultTypeInternal _FunctionDef_RetEntry_DoNotUse_default_instance_;
class GradientDef;
class GradientDefDefaultTypeInternal;
extern GradientDefDefaultTypeInternal _GradientDef_default_instance_;
class RegisteredGradient;
class RegisteredGradientDefaultTypeInternal;
extern RegisteredGradientDefaultTypeInternal _RegisteredGradient_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::FunctionDef* Arena::CreateMaybeMessage<::tensorflow::FunctionDef>(Arena*);
template<> ::tensorflow::FunctionDefLibrary* Arena::CreateMaybeMessage<::tensorflow::FunctionDefLibrary>(Arena*);
template<> ::tensorflow::FunctionDef_ArgAttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ArgAttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_ArgAttrs* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ArgAttrs>(Arena*);
template<> ::tensorflow::FunctionDef_ArgAttrs_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ArgAttrs_AttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_AttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_ControlRetEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ControlRetEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_ResourceArgUniqueIdEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ResourceArgUniqueIdEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_RetEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_RetEntry_DoNotUse>(Arena*);
template<> ::tensorflow::GradientDef* Arena::CreateMaybeMessage<::tensorflow::GradientDef>(Arena*);
template<> ::tensorflow::RegisteredGradient* Arena::CreateMaybeMessage<::tensorflow::RegisteredGradient>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class FunctionDefLibrary :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionDefLibrary) */ {
 public:
  FunctionDefLibrary();
  virtual ~FunctionDefLibrary();

  FunctionDefLibrary(const FunctionDefLibrary& from);
  FunctionDefLibrary(FunctionDefLibrary&& from) noexcept
    : FunctionDefLibrary() {
    *this = ::std::move(from);
  }

  inline FunctionDefLibrary& operator=(const FunctionDefLibrary& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionDefLibrary& operator=(FunctionDefLibrary&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FunctionDefLibrary& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FunctionDefLibrary* internal_default_instance() {
    return reinterpret_cast<const FunctionDefLibrary*>(
               &_FunctionDefLibrary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(FunctionDefLibrary& a, FunctionDefLibrary& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionDefLibrary* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionDefLibrary* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FunctionDefLibrary* New() const final {
    return CreateMaybeMessage<FunctionDefLibrary>(nullptr);
  }

  FunctionDefLibrary* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FunctionDefLibrary>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FunctionDefLibrary& from);
  void MergeFrom(const FunctionDefLibrary& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDefLibrary* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FunctionDefLibrary";
  }
  protected:
  explicit FunctionDefLibrary(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionFieldNumber = 1,
    kGradientFieldNumber = 2,
    kRegisteredGradientsFieldNumber = 3,
  };
  // repeated .tensorflow.FunctionDef function = 1;
  int function_size() const;
  void clear_function();
  ::tensorflow::FunctionDef* mutable_function(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FunctionDef >*
      mutable_function();
  const ::tensorflow::FunctionDef& function(int index) const;
  ::tensorflow::FunctionDef* add_function();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FunctionDef >&
      function() const;

  // repeated .tensorflow.GradientDef gradient = 2;
  int gradient_size() const;
  void clear_gradient();
  ::tensorflow::GradientDef* mutable_gradient(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GradientDef >*
      mutable_gradient();
  const ::tensorflow::GradientDef& gradient(int index) const;
  ::tensorflow::GradientDef* add_gradient();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GradientDef >&
      gradient() const;

  // repeated .tensorflow.RegisteredGradient registered_gradients = 3;
  int registered_gradients_size() const;
  void clear_registered_gradients();
  ::tensorflow::RegisteredGradient* mutable_registered_gradients(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RegisteredGradient >*
      mutable_registered_gradients();
  const ::tensorflow::RegisteredGradient& registered_gradients(int index) const;
  ::tensorflow::RegisteredGradient* add_registered_gradients();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RegisteredGradient >&
      registered_gradients() const;

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionDefLibrary)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FunctionDef > function_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GradientDef > gradient_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RegisteredGradient > registered_gradients_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};
// -------------------------------------------------------------------

class FunctionDef_AttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  FunctionDef_AttrEntry_DoNotUse();
  FunctionDef_AttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_AttrEntry_DoNotUse& other);
  static const FunctionDef_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_AttrEntry_DoNotUse*>(&_FunctionDef_AttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.AttrEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[1];
  }

  public:
};

// -------------------------------------------------------------------

class FunctionDef_ArgAttrs_AttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ArgAttrs_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ArgAttrs_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  FunctionDef_ArgAttrs_AttrEntry_DoNotUse();
  FunctionDef_ArgAttrs_AttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_ArgAttrs_AttrEntry_DoNotUse& other);
  static const FunctionDef_ArgAttrs_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_ArgAttrs_AttrEntry_DoNotUse*>(&_FunctionDef_ArgAttrs_AttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.ArgAttrs.AttrEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[2];
  }

  public:
};

// -------------------------------------------------------------------

class FunctionDef_ArgAttrs :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionDef.ArgAttrs) */ {
 public:
  FunctionDef_ArgAttrs();
  virtual ~FunctionDef_ArgAttrs();

  FunctionDef_ArgAttrs(const FunctionDef_ArgAttrs& from);
  FunctionDef_ArgAttrs(FunctionDef_ArgAttrs&& from) noexcept
    : FunctionDef_ArgAttrs() {
    *this = ::std::move(from);
  }

  inline FunctionDef_ArgAttrs& operator=(const FunctionDef_ArgAttrs& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionDef_ArgAttrs& operator=(FunctionDef_ArgAttrs&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FunctionDef_ArgAttrs& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FunctionDef_ArgAttrs* internal_default_instance() {
    return reinterpret_cast<const FunctionDef_ArgAttrs*>(
               &_FunctionDef_ArgAttrs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(FunctionDef_ArgAttrs& a, FunctionDef_ArgAttrs& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionDef_ArgAttrs* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionDef_ArgAttrs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FunctionDef_ArgAttrs* New() const final {
    return CreateMaybeMessage<FunctionDef_ArgAttrs>(nullptr);
  }

  FunctionDef_ArgAttrs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FunctionDef_ArgAttrs>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FunctionDef_ArgAttrs& from);
  void MergeFrom(const FunctionDef_ArgAttrs& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDef_ArgAttrs* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FunctionDef.ArgAttrs";
  }
  protected:
  explicit FunctionDef_ArgAttrs(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAttrFieldNumber = 1,
  };
  // map<string, .tensorflow.AttrValue> attr = 1;
  int attr_size() const;
  void clear_attr();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionDef.ArgAttrs)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FunctionDef_ArgAttrs_AttrEntry_DoNotUse,
      std::string, ::tensorflow::AttrValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attr_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};
// -------------------------------------------------------------------

class FunctionDef_ArgAttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ArgAttrEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::uint32, ::tensorflow::FunctionDef_ArgAttrs,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ArgAttrEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::uint32, ::tensorflow::FunctionDef_ArgAttrs,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  FunctionDef_ArgAttrEntry_DoNotUse();
  FunctionDef_ArgAttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_ArgAttrEntry_DoNotUse& other);
  static const FunctionDef_ArgAttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_ArgAttrEntry_DoNotUse*>(&_FunctionDef_ArgAttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[4];
  }

  public:
};

// -------------------------------------------------------------------

class FunctionDef_ResourceArgUniqueIdEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ResourceArgUniqueIdEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::uint32, ::PROTOBUF_NAMESPACE_ID::uint32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ResourceArgUniqueIdEntry_DoNotUse, 
    ::PROTOBUF_NAMESPACE_ID::uint32, ::PROTOBUF_NAMESPACE_ID::uint32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    0 > SuperType;
  FunctionDef_ResourceArgUniqueIdEntry_DoNotUse();
  FunctionDef_ResourceArgUniqueIdEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_ResourceArgUniqueIdEntry_DoNotUse& other);
  static const FunctionDef_ResourceArgUniqueIdEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_ResourceArgUniqueIdEntry_DoNotUse*>(&_FunctionDef_ResourceArgUniqueIdEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[5];
  }

  public:
};

// -------------------------------------------------------------------

class FunctionDef_RetEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_RetEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_RetEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  FunctionDef_RetEntry_DoNotUse();
  FunctionDef_RetEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_RetEntry_DoNotUse& other);
  static const FunctionDef_RetEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_RetEntry_DoNotUse*>(&_FunctionDef_RetEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.RetEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.RetEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[6];
  }

  public:
};

// -------------------------------------------------------------------

class FunctionDef_ControlRetEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ControlRetEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ControlRetEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  FunctionDef_ControlRetEntry_DoNotUse();
  FunctionDef_ControlRetEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_ControlRetEntry_DoNotUse& other);
  static const FunctionDef_ControlRetEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_ControlRetEntry_DoNotUse*>(&_FunctionDef_ControlRetEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.ControlRetEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.ControlRetEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[7];
  }

  public:
};

// -------------------------------------------------------------------

class FunctionDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionDef) */ {
 public:
  FunctionDef();
  virtual ~FunctionDef();

  FunctionDef(const FunctionDef& from);
  FunctionDef(FunctionDef&& from) noexcept
    : FunctionDef() {
    *this = ::std::move(from);
  }

  inline FunctionDef& operator=(const FunctionDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionDef& operator=(FunctionDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FunctionDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FunctionDef* internal_default_instance() {
    return reinterpret_cast<const FunctionDef*>(
               &_FunctionDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(FunctionDef& a, FunctionDef& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FunctionDef* New() const final {
    return CreateMaybeMessage<FunctionDef>(nullptr);
  }

  FunctionDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FunctionDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FunctionDef& from);
  void MergeFrom(const FunctionDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FunctionDef";
  }
  protected:
  explicit FunctionDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef FunctionDef_ArgAttrs ArgAttrs;

  // accessors -------------------------------------------------------

  enum : int {
    kNodeDefFieldNumber = 3,
    kRetFieldNumber = 4,
    kAttrFieldNumber = 5,
    kControlRetFieldNumber = 6,
    kArgAttrFieldNumber = 7,
    kResourceArgUniqueIdFieldNumber = 8,
    kSignatureFieldNumber = 1,
  };
  // repeated .tensorflow.NodeDef node_def = 3;
  int node_def_size() const;
  void clear_node_def();
  ::tensorflow::NodeDef* mutable_node_def(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeDef >*
      mutable_node_def();
  const ::tensorflow::NodeDef& node_def(int index) const;
  ::tensorflow::NodeDef* add_node_def();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeDef >&
      node_def() const;

  // map<string, string> ret = 4;
  int ret_size() const;
  void clear_ret();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      ret() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_ret();

  // map<string, .tensorflow.AttrValue> attr = 5;
  int attr_size() const;
  void clear_attr();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // map<string, string> control_ret = 6;
  int control_ret_size() const;
  void clear_control_ret();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      control_ret() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_control_ret();

  // map<uint32, .tensorflow.FunctionDef.ArgAttrs> arg_attr = 7;
  int arg_attr_size() const;
  void clear_arg_attr();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, ::tensorflow::FunctionDef_ArgAttrs >&
      arg_attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, ::tensorflow::FunctionDef_ArgAttrs >*
      mutable_arg_attr();

  // map<uint32, uint32> resource_arg_unique_id = 8;
  int resource_arg_unique_id_size() const;
  void clear_resource_arg_unique_id();
  const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, ::PROTOBUF_NAMESPACE_ID::uint32 >&
      resource_arg_unique_id() const;
  ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, ::PROTOBUF_NAMESPACE_ID::uint32 >*
      mutable_resource_arg_unique_id();

  // .tensorflow.OpDef signature = 1;
  bool has_signature() const;
  void clear_signature();
  const ::tensorflow::OpDef& signature() const;
  ::tensorflow::OpDef* release_signature();
  ::tensorflow::OpDef* mutable_signature();
  void set_allocated_signature(::tensorflow::OpDef* signature);
  void unsafe_arena_set_allocated_signature(
      ::tensorflow::OpDef* signature);
  ::tensorflow::OpDef* unsafe_arena_release_signature();

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeDef > node_def_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FunctionDef_RetEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > ret_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FunctionDef_AttrEntry_DoNotUse,
      std::string, ::tensorflow::AttrValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attr_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FunctionDef_ControlRetEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > control_ret_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FunctionDef_ArgAttrEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::uint32, ::tensorflow::FunctionDef_ArgAttrs,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > arg_attr_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FunctionDef_ResourceArgUniqueIdEntry_DoNotUse,
      ::PROTOBUF_NAMESPACE_ID::uint32, ::PROTOBUF_NAMESPACE_ID::uint32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
      0 > resource_arg_unique_id_;
  ::tensorflow::OpDef* signature_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};
// -------------------------------------------------------------------

class GradientDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GradientDef) */ {
 public:
  GradientDef();
  virtual ~GradientDef();

  GradientDef(const GradientDef& from);
  GradientDef(GradientDef&& from) noexcept
    : GradientDef() {
    *this = ::std::move(from);
  }

  inline GradientDef& operator=(const GradientDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline GradientDef& operator=(GradientDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GradientDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GradientDef* internal_default_instance() {
    return reinterpret_cast<const GradientDef*>(
               &_GradientDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GradientDef& a, GradientDef& b) {
    a.Swap(&b);
  }
  inline void Swap(GradientDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GradientDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GradientDef* New() const final {
    return CreateMaybeMessage<GradientDef>(nullptr);
  }

  GradientDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GradientDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GradientDef& from);
  void MergeFrom(const GradientDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GradientDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GradientDef";
  }
  protected:
  explicit GradientDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionNameFieldNumber = 1,
    kGradientFuncFieldNumber = 2,
  };
  // string function_name = 1;
  void clear_function_name();
  const std::string& function_name() const;
  void set_function_name(const std::string& value);
  void set_function_name(std::string&& value);
  void set_function_name(const char* value);
  void set_function_name(const char* value, size_t size);
  std::string* mutable_function_name();
  std::string* release_function_name();
  void set_allocated_function_name(std::string* function_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_function_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_function_name(
      std::string* function_name);

  // string gradient_func = 2;
  void clear_gradient_func();
  const std::string& gradient_func() const;
  void set_gradient_func(const std::string& value);
  void set_gradient_func(std::string&& value);
  void set_gradient_func(const char* value);
  void set_gradient_func(const char* value, size_t size);
  std::string* mutable_gradient_func();
  std::string* release_gradient_func();
  void set_allocated_gradient_func(std::string* gradient_func);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_gradient_func();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_gradient_func(
      std::string* gradient_func);

  // @@protoc_insertion_point(class_scope:tensorflow.GradientDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr function_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr gradient_func_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};
// -------------------------------------------------------------------

class RegisteredGradient :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisteredGradient) */ {
 public:
  RegisteredGradient();
  virtual ~RegisteredGradient();

  RegisteredGradient(const RegisteredGradient& from);
  RegisteredGradient(RegisteredGradient&& from) noexcept
    : RegisteredGradient() {
    *this = ::std::move(from);
  }

  inline RegisteredGradient& operator=(const RegisteredGradient& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisteredGradient& operator=(RegisteredGradient&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RegisteredGradient& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RegisteredGradient* internal_default_instance() {
    return reinterpret_cast<const RegisteredGradient*>(
               &_RegisteredGradient_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(RegisteredGradient& a, RegisteredGradient& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisteredGradient* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisteredGradient* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RegisteredGradient* New() const final {
    return CreateMaybeMessage<RegisteredGradient>(nullptr);
  }

  RegisteredGradient* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RegisteredGradient>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RegisteredGradient& from);
  void MergeFrom(const RegisteredGradient& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisteredGradient* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RegisteredGradient";
  }
  protected:
  explicit RegisteredGradient(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGradientFuncFieldNumber = 1,
    kRegisteredOpTypeFieldNumber = 2,
  };
  // string gradient_func = 1;
  void clear_gradient_func();
  const std::string& gradient_func() const;
  void set_gradient_func(const std::string& value);
  void set_gradient_func(std::string&& value);
  void set_gradient_func(const char* value);
  void set_gradient_func(const char* value, size_t size);
  std::string* mutable_gradient_func();
  std::string* release_gradient_func();
  void set_allocated_gradient_func(std::string* gradient_func);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_gradient_func();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_gradient_func(
      std::string* gradient_func);

  // string registered_op_type = 2;
  void clear_registered_op_type();
  const std::string& registered_op_type() const;
  void set_registered_op_type(const std::string& value);
  void set_registered_op_type(std::string&& value);
  void set_registered_op_type(const char* value);
  void set_registered_op_type(const char* value, size_t size);
  std::string* mutable_registered_op_type();
  std::string* release_registered_op_type();
  void set_allocated_registered_op_type(std::string* registered_op_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_registered_op_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_registered_op_type(
      std::string* registered_op_type);

  // @@protoc_insertion_point(class_scope:tensorflow.RegisteredGradient)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr gradient_func_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr registered_op_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// FunctionDefLibrary

// repeated .tensorflow.FunctionDef function = 1;
inline int FunctionDefLibrary::function_size() const {
  return function_.size();
}
inline void FunctionDefLibrary::clear_function() {
  function_.Clear();
}
inline ::tensorflow::FunctionDef* FunctionDefLibrary::mutable_function(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDefLibrary.function)
  return function_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FunctionDef >*
FunctionDefLibrary::mutable_function() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDefLibrary.function)
  return &function_;
}
inline const ::tensorflow::FunctionDef& FunctionDefLibrary::function(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDefLibrary.function)
  return function_.Get(index);
}
inline ::tensorflow::FunctionDef* FunctionDefLibrary::add_function() {
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDefLibrary.function)
  return function_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FunctionDef >&
FunctionDefLibrary::function() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDefLibrary.function)
  return function_;
}

// repeated .tensorflow.GradientDef gradient = 2;
inline int FunctionDefLibrary::gradient_size() const {
  return gradient_.size();
}
inline void FunctionDefLibrary::clear_gradient() {
  gradient_.Clear();
}
inline ::tensorflow::GradientDef* FunctionDefLibrary::mutable_gradient(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDefLibrary.gradient)
  return gradient_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GradientDef >*
FunctionDefLibrary::mutable_gradient() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDefLibrary.gradient)
  return &gradient_;
}
inline const ::tensorflow::GradientDef& FunctionDefLibrary::gradient(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDefLibrary.gradient)
  return gradient_.Get(index);
}
inline ::tensorflow::GradientDef* FunctionDefLibrary::add_gradient() {
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDefLibrary.gradient)
  return gradient_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GradientDef >&
FunctionDefLibrary::gradient() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDefLibrary.gradient)
  return gradient_;
}

// repeated .tensorflow.RegisteredGradient registered_gradients = 3;
inline int FunctionDefLibrary::registered_gradients_size() const {
  return registered_gradients_.size();
}
inline void FunctionDefLibrary::clear_registered_gradients() {
  registered_gradients_.Clear();
}
inline ::tensorflow::RegisteredGradient* FunctionDefLibrary::mutable_registered_gradients(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDefLibrary.registered_gradients)
  return registered_gradients_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RegisteredGradient >*
FunctionDefLibrary::mutable_registered_gradients() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDefLibrary.registered_gradients)
  return &registered_gradients_;
}
inline const ::tensorflow::RegisteredGradient& FunctionDefLibrary::registered_gradients(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDefLibrary.registered_gradients)
  return registered_gradients_.Get(index);
}
inline ::tensorflow::RegisteredGradient* FunctionDefLibrary::add_registered_gradients() {
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDefLibrary.registered_gradients)
  return registered_gradients_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RegisteredGradient >&
FunctionDefLibrary::registered_gradients() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDefLibrary.registered_gradients)
  return registered_gradients_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FunctionDef_ArgAttrs

// map<string, .tensorflow.AttrValue> attr = 1;
inline int FunctionDef_ArgAttrs::attr_size() const {
  return attr_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
FunctionDef_ArgAttrs::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.ArgAttrs.attr)
  return attr_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
FunctionDef_ArgAttrs::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.ArgAttrs.attr)
  return attr_.MutableMap();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FunctionDef

// .tensorflow.OpDef signature = 1;
inline bool FunctionDef::has_signature() const {
  return this != internal_default_instance() && signature_ != nullptr;
}
inline const ::tensorflow::OpDef& FunctionDef::signature() const {
  const ::tensorflow::OpDef* p = signature_;
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDef.signature)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::OpDef*>(
      &::tensorflow::_OpDef_default_instance_);
}
inline ::tensorflow::OpDef* FunctionDef::release_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionDef.signature)
  
  ::tensorflow::OpDef* temp = signature_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  signature_ = nullptr;
  return temp;
}
inline ::tensorflow::OpDef* FunctionDef::unsafe_arena_release_signature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FunctionDef.signature)
  
  ::tensorflow::OpDef* temp = signature_;
  signature_ = nullptr;
  return temp;
}
inline ::tensorflow::OpDef* FunctionDef::mutable_signature() {
  
  if (signature_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OpDef>(GetArenaNoVirtual());
    signature_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDef.signature)
  return signature_;
}
inline void FunctionDef::set_allocated_signature(::tensorflow::OpDef* signature) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(signature_);
  }
  if (signature) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(signature)->GetArena();
    if (message_arena != submessage_arena) {
      signature = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, signature, submessage_arena);
    }
    
  } else {
    
  }
  signature_ = signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionDef.signature)
}

// map<string, .tensorflow.AttrValue> attr = 5;
inline int FunctionDef::attr_size() const {
  return attr_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
FunctionDef::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.attr)
  return attr_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
FunctionDef::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.attr)
  return attr_.MutableMap();
}

// map<uint32, .tensorflow.FunctionDef.ArgAttrs> arg_attr = 7;
inline int FunctionDef::arg_attr_size() const {
  return arg_attr_.size();
}
inline void FunctionDef::clear_arg_attr() {
  arg_attr_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, ::tensorflow::FunctionDef_ArgAttrs >&
FunctionDef::arg_attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.arg_attr)
  return arg_attr_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, ::tensorflow::FunctionDef_ArgAttrs >*
FunctionDef::mutable_arg_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.arg_attr)
  return arg_attr_.MutableMap();
}

// map<uint32, uint32> resource_arg_unique_id = 8;
inline int FunctionDef::resource_arg_unique_id_size() const {
  return resource_arg_unique_id_.size();
}
inline void FunctionDef::clear_resource_arg_unique_id() {
  resource_arg_unique_id_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, ::PROTOBUF_NAMESPACE_ID::uint32 >&
FunctionDef::resource_arg_unique_id() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.resource_arg_unique_id)
  return resource_arg_unique_id_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< ::PROTOBUF_NAMESPACE_ID::uint32, ::PROTOBUF_NAMESPACE_ID::uint32 >*
FunctionDef::mutable_resource_arg_unique_id() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.resource_arg_unique_id)
  return resource_arg_unique_id_.MutableMap();
}

// repeated .tensorflow.NodeDef node_def = 3;
inline int FunctionDef::node_def_size() const {
  return node_def_.size();
}
inline ::tensorflow::NodeDef* FunctionDef::mutable_node_def(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDef.node_def)
  return node_def_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeDef >*
FunctionDef::mutable_node_def() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDef.node_def)
  return &node_def_;
}
inline const ::tensorflow::NodeDef& FunctionDef::node_def(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDef.node_def)
  return node_def_.Get(index);
}
inline ::tensorflow::NodeDef* FunctionDef::add_node_def() {
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDef.node_def)
  return node_def_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeDef >&
FunctionDef::node_def() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDef.node_def)
  return node_def_;
}

// map<string, string> ret = 4;
inline int FunctionDef::ret_size() const {
  return ret_.size();
}
inline void FunctionDef::clear_ret() {
  ret_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
FunctionDef::ret() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.ret)
  return ret_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
FunctionDef::mutable_ret() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.ret)
  return ret_.MutableMap();
}

// map<string, string> control_ret = 6;
inline int FunctionDef::control_ret_size() const {
  return control_ret_.size();
}
inline void FunctionDef::clear_control_ret() {
  control_ret_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
FunctionDef::control_ret() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.control_ret)
  return control_ret_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
FunctionDef::mutable_control_ret() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.control_ret)
  return control_ret_.MutableMap();
}

// -------------------------------------------------------------------

// GradientDef

// string function_name = 1;
inline void GradientDef::clear_function_name() {
  function_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GradientDef::function_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GradientDef.function_name)
  return function_name_.Get();
}
inline void GradientDef::set_function_name(const std::string& value) {
  
  function_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GradientDef.function_name)
}
inline void GradientDef::set_function_name(std::string&& value) {
  
  function_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GradientDef.function_name)
}
inline void GradientDef::set_function_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  function_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GradientDef.function_name)
}
inline void GradientDef::set_function_name(const char* value,
    size_t size) {
  
  function_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GradientDef.function_name)
}
inline std::string* GradientDef::mutable_function_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GradientDef.function_name)
  return function_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GradientDef::release_function_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GradientDef.function_name)
  
  return function_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GradientDef::set_allocated_function_name(std::string* function_name) {
  if (function_name != nullptr) {
    
  } else {
    
  }
  function_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), function_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GradientDef.function_name)
}
inline std::string* GradientDef::unsafe_arena_release_function_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GradientDef.function_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return function_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GradientDef::unsafe_arena_set_allocated_function_name(
    std::string* function_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (function_name != nullptr) {
    
  } else {
    
  }
  function_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      function_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GradientDef.function_name)
}

// string gradient_func = 2;
inline void GradientDef::clear_gradient_func() {
  gradient_func_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GradientDef::gradient_func() const {
  // @@protoc_insertion_point(field_get:tensorflow.GradientDef.gradient_func)
  return gradient_func_.Get();
}
inline void GradientDef::set_gradient_func(const std::string& value) {
  
  gradient_func_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GradientDef.gradient_func)
}
inline void GradientDef::set_gradient_func(std::string&& value) {
  
  gradient_func_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GradientDef.gradient_func)
}
inline void GradientDef::set_gradient_func(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  gradient_func_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GradientDef.gradient_func)
}
inline void GradientDef::set_gradient_func(const char* value,
    size_t size) {
  
  gradient_func_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GradientDef.gradient_func)
}
inline std::string* GradientDef::mutable_gradient_func() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GradientDef.gradient_func)
  return gradient_func_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GradientDef::release_gradient_func() {
  // @@protoc_insertion_point(field_release:tensorflow.GradientDef.gradient_func)
  
  return gradient_func_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GradientDef::set_allocated_gradient_func(std::string* gradient_func) {
  if (gradient_func != nullptr) {
    
  } else {
    
  }
  gradient_func_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), gradient_func,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GradientDef.gradient_func)
}
inline std::string* GradientDef::unsafe_arena_release_gradient_func() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GradientDef.gradient_func)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return gradient_func_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GradientDef::unsafe_arena_set_allocated_gradient_func(
    std::string* gradient_func) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (gradient_func != nullptr) {
    
  } else {
    
  }
  gradient_func_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      gradient_func, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GradientDef.gradient_func)
}

// -------------------------------------------------------------------

// RegisteredGradient

// string gradient_func = 1;
inline void RegisteredGradient::clear_gradient_func() {
  gradient_func_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RegisteredGradient::gradient_func() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisteredGradient.gradient_func)
  return gradient_func_.Get();
}
inline void RegisteredGradient::set_gradient_func(const std::string& value) {
  
  gradient_func_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RegisteredGradient.gradient_func)
}
inline void RegisteredGradient::set_gradient_func(std::string&& value) {
  
  gradient_func_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RegisteredGradient.gradient_func)
}
inline void RegisteredGradient::set_gradient_func(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  gradient_func_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RegisteredGradient.gradient_func)
}
inline void RegisteredGradient::set_gradient_func(const char* value,
    size_t size) {
  
  gradient_func_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RegisteredGradient.gradient_func)
}
inline std::string* RegisteredGradient::mutable_gradient_func() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisteredGradient.gradient_func)
  return gradient_func_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RegisteredGradient::release_gradient_func() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisteredGradient.gradient_func)
  
  return gradient_func_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RegisteredGradient::set_allocated_gradient_func(std::string* gradient_func) {
  if (gradient_func != nullptr) {
    
  } else {
    
  }
  gradient_func_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), gradient_func,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisteredGradient.gradient_func)
}
inline std::string* RegisteredGradient::unsafe_arena_release_gradient_func() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisteredGradient.gradient_func)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return gradient_func_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RegisteredGradient::unsafe_arena_set_allocated_gradient_func(
    std::string* gradient_func) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (gradient_func != nullptr) {
    
  } else {
    
  }
  gradient_func_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      gradient_func, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisteredGradient.gradient_func)
}

// string registered_op_type = 2;
inline void RegisteredGradient::clear_registered_op_type() {
  registered_op_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RegisteredGradient::registered_op_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisteredGradient.registered_op_type)
  return registered_op_type_.Get();
}
inline void RegisteredGradient::set_registered_op_type(const std::string& value) {
  
  registered_op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RegisteredGradient.registered_op_type)
}
inline void RegisteredGradient::set_registered_op_type(std::string&& value) {
  
  registered_op_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RegisteredGradient.registered_op_type)
}
inline void RegisteredGradient::set_registered_op_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  registered_op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RegisteredGradient.registered_op_type)
}
inline void RegisteredGradient::set_registered_op_type(const char* value,
    size_t size) {
  
  registered_op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RegisteredGradient.registered_op_type)
}
inline std::string* RegisteredGradient::mutable_registered_op_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisteredGradient.registered_op_type)
  return registered_op_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RegisteredGradient::release_registered_op_type() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisteredGradient.registered_op_type)
  
  return registered_op_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RegisteredGradient::set_allocated_registered_op_type(std::string* registered_op_type) {
  if (registered_op_type != nullptr) {
    
  } else {
    
  }
  registered_op_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), registered_op_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisteredGradient.registered_op_type)
}
inline std::string* RegisteredGradient::unsafe_arena_release_registered_op_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisteredGradient.registered_op_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return registered_op_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RegisteredGradient::unsafe_arena_set_allocated_registered_op_type(
    std::string* registered_op_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (registered_op_type != nullptr) {
    
  } else {
    
  }
  registered_op_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      registered_op_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisteredGradient.registered_op_type)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffunction_2eproto
