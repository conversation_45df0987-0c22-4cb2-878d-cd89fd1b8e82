#
# Copyright (C) 2021-2022 Intel Corporation
# Copyright (C) CVAT.ai Corporation
#
# SPDX-License-Identifier: MIT
#

services:
  cvat_db:
    ports:
      - '5432:5432'

  cvat_server:
    build:
      context: .
      args:
        http_proxy:
        https_proxy:
        socks_proxy:
        CLAM_AV:
        CVAT_DEBUG_ENABLED:
        COVERAGE_PROCESS_START:
    environment:
      # Use this with CVAT_DEBUG_ENABLED to avoid server response timeouts
      CVAT_DEBUG_ENABLED: '${CVAT_DEBUG_ENABLED:-no}'
      CVAT_DEBUG_PORT: '9090'
      # If 'yes', wait for a debugger connection on startup
      CVAT_DEBUG_WAIT: '${CVAT_DEBUG_WAIT_CLIENT:-no}'
      COVERAGE_PROCESS_START:
    ports:
      - '9090:9090'

  cvat_worker_export:
    environment:
      # For debugging, make sure to set 1 process
      # Due to the supervisord specifics, the extra processes will fail and
      # after few attempts supervisord will give up restarting, leaving only 1 process
      # NUMPROCS: 1
      CVAT_DEBUG_ENABLED: '${CVAT_DEBUG_ENABLED:-no}'
      CVAT_DEBUG_PORT: '9092'
      COVERAGE_PROCESS_START:
    ports:
      - '9092:9092'

  cvat_worker_import:
    environment:
      # For debugging, make sure to set 1 process
      # Due to the supervisord specifics, the extra processes will fail and
      # after few attempts supervisord will give up restarting, leaving only 1 process
      # NUMPROCS: 1
      CVAT_DEBUG_ENABLED: '${CVAT_DEBUG_ENABLED:-no}'
      CVAT_DEBUG_PORT: '9093'
      COVERAGE_PROCESS_START:
    ports:
      - '9093:9093'

  cvat_worker_quality_reports:
    environment:
      # For debugging, make sure to set 1 process
      # Due to the supervisord specifics, the extra processes will fail and
      # after few attempts supervisord will give up restarting, leaving only 1 process
      # NUMPROCS: 1
      CVAT_DEBUG_ENABLED: '${CVAT_DEBUG_ENABLED:-no}'
      CVAT_DEBUG_PORT: '9094'
      COVERAGE_PROCESS_START:
    ports:
      - '9094:9094'

  cvat_worker_consensus:
    environment:
      # For debugging, make sure to set 1 process
      # Due to the supervisord specifics, the extra processes will fail and
      # after few attempts supervisord will give up restarting, leaving only 1 process
      # NUMPROCS: 1
      CVAT_DEBUG_ENABLED: '${CVAT_DEBUG_ENABLED:-no}'
      CVAT_DEBUG_PORT: '9096'
      COVERAGE_PROCESS_START:
    ports:
      - '9096:9096'

  cvat_worker_annotation:
    environment:
      # For debugging, make sure to set 1 process
      # Due to the supervisord specifics, the extra processes will fail and
      # after few attempts supervisord will give up restarting, leaving only 1 process
      # NUMPROCS: 1
      CVAT_DEBUG_ENABLED: '${CVAT_DEBUG_ENABLED:-no}'
      CVAT_DEBUG_PORT: '9091'
      COVERAGE_PROCESS_START:
    ports:
      - '9091:9091'

  cvat_ui:
    build:
      context: .
      args:
        http_proxy:
        https_proxy:
        no_proxy:
        socks_proxy:
      dockerfile: Dockerfile.ui

  cvat_clickhouse:
    ports:
      - '8123:8123'

  cvat_opa:
    ports:
      - '8181:8181'

  cvat_redis_inmem:
    ports:
      - '6379:6379'

  cvat_redis_ondisk:
    ports:
      - '6666:6666'

  cvat_vector:
    ports:
      - '8282:80'
