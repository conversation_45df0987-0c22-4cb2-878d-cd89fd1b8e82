// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/jit/xla_activity.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/config.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
namespace tensorflow {
class XlaAutoClusteringActivity;
class XlaAutoClusteringActivityDefaultTypeInternal;
extern XlaAutoClusteringActivityDefaultTypeInternal _XlaAutoClusteringActivity_default_instance_;
class XlaAutoClusteringSummary;
class XlaAutoClusteringSummaryDefaultTypeInternal;
extern XlaAutoClusteringSummaryDefaultTypeInternal _XlaAutoClusteringSummary_default_instance_;
class XlaAutoClusteringSummary_Cluster;
class XlaAutoClusteringSummary_ClusterDefaultTypeInternal;
extern XlaAutoClusteringSummary_ClusterDefaultTypeInternal _XlaAutoClusteringSummary_Cluster_default_instance_;
class XlaAutoClusteringSummary_OpAndCount;
class XlaAutoClusteringSummary_OpAndCountDefaultTypeInternal;
extern XlaAutoClusteringSummary_OpAndCountDefaultTypeInternal _XlaAutoClusteringSummary_OpAndCount_default_instance_;
class XlaJitCompilationActivity;
class XlaJitCompilationActivityDefaultTypeInternal;
extern XlaJitCompilationActivityDefaultTypeInternal _XlaJitCompilationActivity_default_instance_;
class XlaOptimizationRemark;
class XlaOptimizationRemarkDefaultTypeInternal;
extern XlaOptimizationRemarkDefaultTypeInternal _XlaOptimizationRemark_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::XlaAutoClusteringActivity* Arena::CreateMaybeMessage<::tensorflow::XlaAutoClusteringActivity>(Arena*);
template<> ::tensorflow::XlaAutoClusteringSummary* Arena::CreateMaybeMessage<::tensorflow::XlaAutoClusteringSummary>(Arena*);
template<> ::tensorflow::XlaAutoClusteringSummary_Cluster* Arena::CreateMaybeMessage<::tensorflow::XlaAutoClusteringSummary_Cluster>(Arena*);
template<> ::tensorflow::XlaAutoClusteringSummary_OpAndCount* Arena::CreateMaybeMessage<::tensorflow::XlaAutoClusteringSummary_OpAndCount>(Arena*);
template<> ::tensorflow::XlaJitCompilationActivity* Arena::CreateMaybeMessage<::tensorflow::XlaJitCompilationActivity>(Arena*);
template<> ::tensorflow::XlaOptimizationRemark* Arena::CreateMaybeMessage<::tensorflow::XlaOptimizationRemark>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum XlaOptimizationRemark_Warning : int {
  XlaOptimizationRemark_Warning_NONE = 0,
  XlaOptimizationRemark_Warning_INACCURATE_OPERATION = 1,
  XlaOptimizationRemark_Warning_SLOW_OPERATION = 2,
  XlaOptimizationRemark_Warning_UNIMPLEMENTED_OPERATION = 3,
  XlaOptimizationRemark_Warning_SLOW_IMAGE_RESIZE_DIMENSIONS = 4,
  XlaOptimizationRemark_Warning_MEGAMORPHIC_FUNCTION = 5,
  XlaOptimizationRemark_Warning_XlaOptimizationRemark_Warning_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  XlaOptimizationRemark_Warning_XlaOptimizationRemark_Warning_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool XlaOptimizationRemark_Warning_IsValid(int value);
constexpr XlaOptimizationRemark_Warning XlaOptimizationRemark_Warning_Warning_MIN = XlaOptimizationRemark_Warning_NONE;
constexpr XlaOptimizationRemark_Warning XlaOptimizationRemark_Warning_Warning_MAX = XlaOptimizationRemark_Warning_MEGAMORPHIC_FUNCTION;
constexpr int XlaOptimizationRemark_Warning_Warning_ARRAYSIZE = XlaOptimizationRemark_Warning_Warning_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* XlaOptimizationRemark_Warning_descriptor();
template<typename T>
inline const std::string& XlaOptimizationRemark_Warning_Name(T enum_t_value) {
  static_assert(::std::is_same<T, XlaOptimizationRemark_Warning>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function XlaOptimizationRemark_Warning_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    XlaOptimizationRemark_Warning_descriptor(), enum_t_value);
}
inline bool XlaOptimizationRemark_Warning_Parse(
    const std::string& name, XlaOptimizationRemark_Warning* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<XlaOptimizationRemark_Warning>(
    XlaOptimizationRemark_Warning_descriptor(), name, value);
}
// ===================================================================

class XlaAutoClusteringSummary_OpAndCount :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaAutoClusteringSummary.OpAndCount) */ {
 public:
  XlaAutoClusteringSummary_OpAndCount();
  virtual ~XlaAutoClusteringSummary_OpAndCount();

  XlaAutoClusteringSummary_OpAndCount(const XlaAutoClusteringSummary_OpAndCount& from);
  XlaAutoClusteringSummary_OpAndCount(XlaAutoClusteringSummary_OpAndCount&& from) noexcept
    : XlaAutoClusteringSummary_OpAndCount() {
    *this = ::std::move(from);
  }

  inline XlaAutoClusteringSummary_OpAndCount& operator=(const XlaAutoClusteringSummary_OpAndCount& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaAutoClusteringSummary_OpAndCount& operator=(XlaAutoClusteringSummary_OpAndCount&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XlaAutoClusteringSummary_OpAndCount& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XlaAutoClusteringSummary_OpAndCount* internal_default_instance() {
    return reinterpret_cast<const XlaAutoClusteringSummary_OpAndCount*>(
               &_XlaAutoClusteringSummary_OpAndCount_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(XlaAutoClusteringSummary_OpAndCount& a, XlaAutoClusteringSummary_OpAndCount& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaAutoClusteringSummary_OpAndCount* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XlaAutoClusteringSummary_OpAndCount* New() const final {
    return CreateMaybeMessage<XlaAutoClusteringSummary_OpAndCount>(nullptr);
  }

  XlaAutoClusteringSummary_OpAndCount* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XlaAutoClusteringSummary_OpAndCount>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XlaAutoClusteringSummary_OpAndCount& from);
  void MergeFrom(const XlaAutoClusteringSummary_OpAndCount& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaAutoClusteringSummary_OpAndCount* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaAutoClusteringSummary.OpAndCount";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpFieldNumber = 1,
    kCountFieldNumber = 2,
  };
  // string op = 1;
  void clear_op();
  const std::string& op() const;
  void set_op(const std::string& value);
  void set_op(std::string&& value);
  void set_op(const char* value);
  void set_op(const char* value, size_t size);
  std::string* mutable_op();
  std::string* release_op();
  void set_allocated_op(std::string* op);

  // int32 count = 2;
  void clear_count();
  ::PROTOBUF_NAMESPACE_ID::int32 count() const;
  void set_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.XlaAutoClusteringSummary.OpAndCount)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_;
  ::PROTOBUF_NAMESPACE_ID::int32 count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// -------------------------------------------------------------------

class XlaAutoClusteringSummary_Cluster :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaAutoClusteringSummary.Cluster) */ {
 public:
  XlaAutoClusteringSummary_Cluster();
  virtual ~XlaAutoClusteringSummary_Cluster();

  XlaAutoClusteringSummary_Cluster(const XlaAutoClusteringSummary_Cluster& from);
  XlaAutoClusteringSummary_Cluster(XlaAutoClusteringSummary_Cluster&& from) noexcept
    : XlaAutoClusteringSummary_Cluster() {
    *this = ::std::move(from);
  }

  inline XlaAutoClusteringSummary_Cluster& operator=(const XlaAutoClusteringSummary_Cluster& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaAutoClusteringSummary_Cluster& operator=(XlaAutoClusteringSummary_Cluster&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XlaAutoClusteringSummary_Cluster& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XlaAutoClusteringSummary_Cluster* internal_default_instance() {
    return reinterpret_cast<const XlaAutoClusteringSummary_Cluster*>(
               &_XlaAutoClusteringSummary_Cluster_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(XlaAutoClusteringSummary_Cluster& a, XlaAutoClusteringSummary_Cluster& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaAutoClusteringSummary_Cluster* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XlaAutoClusteringSummary_Cluster* New() const final {
    return CreateMaybeMessage<XlaAutoClusteringSummary_Cluster>(nullptr);
  }

  XlaAutoClusteringSummary_Cluster* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XlaAutoClusteringSummary_Cluster>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XlaAutoClusteringSummary_Cluster& from);
  void MergeFrom(const XlaAutoClusteringSummary_Cluster& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaAutoClusteringSummary_Cluster* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaAutoClusteringSummary.Cluster";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpHistogramFieldNumber = 3,
    kNameFieldNumber = 1,
    kSizeFieldNumber = 2,
  };
  // repeated .tensorflow.XlaAutoClusteringSummary.OpAndCount op_histogram = 3;
  int op_histogram_size() const;
  void clear_op_histogram();
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* mutable_op_histogram(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >*
      mutable_op_histogram();
  const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& op_histogram(int index) const;
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* add_op_histogram();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >&
      op_histogram() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // int32 size = 2;
  void clear_size();
  ::PROTOBUF_NAMESPACE_ID::int32 size() const;
  void set_size(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.XlaAutoClusteringSummary.Cluster)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount > op_histogram_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::int32 size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// -------------------------------------------------------------------

class XlaAutoClusteringSummary :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaAutoClusteringSummary) */ {
 public:
  XlaAutoClusteringSummary();
  virtual ~XlaAutoClusteringSummary();

  XlaAutoClusteringSummary(const XlaAutoClusteringSummary& from);
  XlaAutoClusteringSummary(XlaAutoClusteringSummary&& from) noexcept
    : XlaAutoClusteringSummary() {
    *this = ::std::move(from);
  }

  inline XlaAutoClusteringSummary& operator=(const XlaAutoClusteringSummary& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaAutoClusteringSummary& operator=(XlaAutoClusteringSummary&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XlaAutoClusteringSummary& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XlaAutoClusteringSummary* internal_default_instance() {
    return reinterpret_cast<const XlaAutoClusteringSummary*>(
               &_XlaAutoClusteringSummary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(XlaAutoClusteringSummary& a, XlaAutoClusteringSummary& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaAutoClusteringSummary* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XlaAutoClusteringSummary* New() const final {
    return CreateMaybeMessage<XlaAutoClusteringSummary>(nullptr);
  }

  XlaAutoClusteringSummary* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XlaAutoClusteringSummary>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XlaAutoClusteringSummary& from);
  void MergeFrom(const XlaAutoClusteringSummary& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaAutoClusteringSummary* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaAutoClusteringSummary";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef XlaAutoClusteringSummary_OpAndCount OpAndCount;
  typedef XlaAutoClusteringSummary_Cluster Cluster;

  // accessors -------------------------------------------------------

  enum : int {
    kClustersFieldNumber = 3,
    kUnclusteredOpHistogramFieldNumber = 4,
    kUnclusteredNodeCountFieldNumber = 1,
    kClusteredNodeCountFieldNumber = 2,
  };
  // repeated .tensorflow.XlaAutoClusteringSummary.Cluster clusters = 3;
  int clusters_size() const;
  void clear_clusters();
  ::tensorflow::XlaAutoClusteringSummary_Cluster* mutable_clusters(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_Cluster >*
      mutable_clusters();
  const ::tensorflow::XlaAutoClusteringSummary_Cluster& clusters(int index) const;
  ::tensorflow::XlaAutoClusteringSummary_Cluster* add_clusters();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_Cluster >&
      clusters() const;

  // repeated .tensorflow.XlaAutoClusteringSummary.OpAndCount unclustered_op_histogram = 4;
  int unclustered_op_histogram_size() const;
  void clear_unclustered_op_histogram();
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* mutable_unclustered_op_histogram(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >*
      mutable_unclustered_op_histogram();
  const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& unclustered_op_histogram(int index) const;
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* add_unclustered_op_histogram();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >&
      unclustered_op_histogram() const;

  // int32 unclustered_node_count = 1;
  void clear_unclustered_node_count();
  ::PROTOBUF_NAMESPACE_ID::int32 unclustered_node_count() const;
  void set_unclustered_node_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 clustered_node_count = 2;
  void clear_clustered_node_count();
  ::PROTOBUF_NAMESPACE_ID::int32 clustered_node_count() const;
  void set_clustered_node_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.XlaAutoClusteringSummary)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_Cluster > clusters_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount > unclustered_op_histogram_;
  ::PROTOBUF_NAMESPACE_ID::int32 unclustered_node_count_;
  ::PROTOBUF_NAMESPACE_ID::int32 clustered_node_count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// -------------------------------------------------------------------

class XlaAutoClusteringActivity :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaAutoClusteringActivity) */ {
 public:
  XlaAutoClusteringActivity();
  virtual ~XlaAutoClusteringActivity();

  XlaAutoClusteringActivity(const XlaAutoClusteringActivity& from);
  XlaAutoClusteringActivity(XlaAutoClusteringActivity&& from) noexcept
    : XlaAutoClusteringActivity() {
    *this = ::std::move(from);
  }

  inline XlaAutoClusteringActivity& operator=(const XlaAutoClusteringActivity& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaAutoClusteringActivity& operator=(XlaAutoClusteringActivity&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XlaAutoClusteringActivity& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XlaAutoClusteringActivity* internal_default_instance() {
    return reinterpret_cast<const XlaAutoClusteringActivity*>(
               &_XlaAutoClusteringActivity_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(XlaAutoClusteringActivity& a, XlaAutoClusteringActivity& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaAutoClusteringActivity* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XlaAutoClusteringActivity* New() const final {
    return CreateMaybeMessage<XlaAutoClusteringActivity>(nullptr);
  }

  XlaAutoClusteringActivity* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XlaAutoClusteringActivity>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XlaAutoClusteringActivity& from);
  void MergeFrom(const XlaAutoClusteringActivity& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaAutoClusteringActivity* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaAutoClusteringActivity";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSummaryFieldNumber = 3,
    kGlobalJitLevelFieldNumber = 1,
    kCpuGlobalJitEnabledFieldNumber = 2,
  };
  // .tensorflow.XlaAutoClusteringSummary summary = 3;
  bool has_summary() const;
  void clear_summary();
  const ::tensorflow::XlaAutoClusteringSummary& summary() const;
  ::tensorflow::XlaAutoClusteringSummary* release_summary();
  ::tensorflow::XlaAutoClusteringSummary* mutable_summary();
  void set_allocated_summary(::tensorflow::XlaAutoClusteringSummary* summary);

  // .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 1;
  void clear_global_jit_level();
  ::tensorflow::OptimizerOptions_GlobalJitLevel global_jit_level() const;
  void set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value);

  // bool cpu_global_jit_enabled = 2;
  void clear_cpu_global_jit_enabled();
  bool cpu_global_jit_enabled() const;
  void set_cpu_global_jit_enabled(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.XlaAutoClusteringActivity)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::XlaAutoClusteringSummary* summary_;
  int global_jit_level_;
  bool cpu_global_jit_enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// -------------------------------------------------------------------

class XlaJitCompilationActivity :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaJitCompilationActivity) */ {
 public:
  XlaJitCompilationActivity();
  virtual ~XlaJitCompilationActivity();

  XlaJitCompilationActivity(const XlaJitCompilationActivity& from);
  XlaJitCompilationActivity(XlaJitCompilationActivity&& from) noexcept
    : XlaJitCompilationActivity() {
    *this = ::std::move(from);
  }

  inline XlaJitCompilationActivity& operator=(const XlaJitCompilationActivity& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaJitCompilationActivity& operator=(XlaJitCompilationActivity&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XlaJitCompilationActivity& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XlaJitCompilationActivity* internal_default_instance() {
    return reinterpret_cast<const XlaJitCompilationActivity*>(
               &_XlaJitCompilationActivity_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(XlaJitCompilationActivity& a, XlaJitCompilationActivity& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaJitCompilationActivity* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XlaJitCompilationActivity* New() const final {
    return CreateMaybeMessage<XlaJitCompilationActivity>(nullptr);
  }

  XlaJitCompilationActivity* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XlaJitCompilationActivity>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XlaJitCompilationActivity& from);
  void MergeFrom(const XlaJitCompilationActivity& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaJitCompilationActivity* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaJitCompilationActivity";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClusterNameFieldNumber = 1,
    kCompileTimeUsFieldNumber = 3,
    kCumulativeCompileTimeUsFieldNumber = 4,
    kCompileCountFieldNumber = 2,
  };
  // string cluster_name = 1;
  void clear_cluster_name();
  const std::string& cluster_name() const;
  void set_cluster_name(const std::string& value);
  void set_cluster_name(std::string&& value);
  void set_cluster_name(const char* value);
  void set_cluster_name(const char* value, size_t size);
  std::string* mutable_cluster_name();
  std::string* release_cluster_name();
  void set_allocated_cluster_name(std::string* cluster_name);

  // int64 compile_time_us = 3;
  void clear_compile_time_us();
  ::PROTOBUF_NAMESPACE_ID::int64 compile_time_us() const;
  void set_compile_time_us(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 cumulative_compile_time_us = 4;
  void clear_cumulative_compile_time_us();
  ::PROTOBUF_NAMESPACE_ID::int64 cumulative_compile_time_us() const;
  void set_cumulative_compile_time_us(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int32 compile_count = 2;
  void clear_compile_count();
  ::PROTOBUF_NAMESPACE_ID::int32 compile_count() const;
  void set_compile_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.XlaJitCompilationActivity)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cluster_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 compile_time_us_;
  ::PROTOBUF_NAMESPACE_ID::int64 cumulative_compile_time_us_;
  ::PROTOBUF_NAMESPACE_ID::int32 compile_count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// -------------------------------------------------------------------

class XlaOptimizationRemark :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaOptimizationRemark) */ {
 public:
  XlaOptimizationRemark();
  virtual ~XlaOptimizationRemark();

  XlaOptimizationRemark(const XlaOptimizationRemark& from);
  XlaOptimizationRemark(XlaOptimizationRemark&& from) noexcept
    : XlaOptimizationRemark() {
    *this = ::std::move(from);
  }

  inline XlaOptimizationRemark& operator=(const XlaOptimizationRemark& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaOptimizationRemark& operator=(XlaOptimizationRemark&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const XlaOptimizationRemark& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const XlaOptimizationRemark* internal_default_instance() {
    return reinterpret_cast<const XlaOptimizationRemark*>(
               &_XlaOptimizationRemark_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(XlaOptimizationRemark& a, XlaOptimizationRemark& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaOptimizationRemark* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline XlaOptimizationRemark* New() const final {
    return CreateMaybeMessage<XlaOptimizationRemark>(nullptr);
  }

  XlaOptimizationRemark* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<XlaOptimizationRemark>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const XlaOptimizationRemark& from);
  void MergeFrom(const XlaOptimizationRemark& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaOptimizationRemark* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaOptimizationRemark";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef XlaOptimizationRemark_Warning Warning;
  static constexpr Warning NONE =
    XlaOptimizationRemark_Warning_NONE;
  static constexpr Warning INACCURATE_OPERATION =
    XlaOptimizationRemark_Warning_INACCURATE_OPERATION;
  static constexpr Warning SLOW_OPERATION =
    XlaOptimizationRemark_Warning_SLOW_OPERATION;
  static constexpr Warning UNIMPLEMENTED_OPERATION =
    XlaOptimizationRemark_Warning_UNIMPLEMENTED_OPERATION;
  static constexpr Warning SLOW_IMAGE_RESIZE_DIMENSIONS =
    XlaOptimizationRemark_Warning_SLOW_IMAGE_RESIZE_DIMENSIONS;
  static constexpr Warning MEGAMORPHIC_FUNCTION =
    XlaOptimizationRemark_Warning_MEGAMORPHIC_FUNCTION;
  static inline bool Warning_IsValid(int value) {
    return XlaOptimizationRemark_Warning_IsValid(value);
  }
  static constexpr Warning Warning_MIN =
    XlaOptimizationRemark_Warning_Warning_MIN;
  static constexpr Warning Warning_MAX =
    XlaOptimizationRemark_Warning_Warning_MAX;
  static constexpr int Warning_ARRAYSIZE =
    XlaOptimizationRemark_Warning_Warning_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Warning_descriptor() {
    return XlaOptimizationRemark_Warning_descriptor();
  }
  template<typename T>
  static inline const std::string& Warning_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Warning>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Warning_Name.");
    return XlaOptimizationRemark_Warning_Name(enum_t_value);
  }
  static inline bool Warning_Parse(const std::string& name,
      Warning* value) {
    return XlaOptimizationRemark_Warning_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kDebugInformationFieldNumber = 2,
    kWarningFieldNumber = 1,
  };
  // string debug_information = 2;
  void clear_debug_information();
  const std::string& debug_information() const;
  void set_debug_information(const std::string& value);
  void set_debug_information(std::string&& value);
  void set_debug_information(const char* value);
  void set_debug_information(const char* value, size_t size);
  std::string* mutable_debug_information();
  std::string* release_debug_information();
  void set_allocated_debug_information(std::string* debug_information);

  // .tensorflow.XlaOptimizationRemark.Warning warning = 1;
  void clear_warning();
  ::tensorflow::XlaOptimizationRemark_Warning warning() const;
  void set_warning(::tensorflow::XlaOptimizationRemark_Warning value);

  // @@protoc_insertion_point(class_scope:tensorflow.XlaOptimizationRemark)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr debug_information_;
  int warning_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// XlaAutoClusteringSummary_OpAndCount

// string op = 1;
inline void XlaAutoClusteringSummary_OpAndCount::clear_op() {
  op_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& XlaAutoClusteringSummary_OpAndCount::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
  return op_.GetNoArena();
}
inline void XlaAutoClusteringSummary_OpAndCount::set_op(const std::string& value) {
  
  op_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
}
inline void XlaAutoClusteringSummary_OpAndCount::set_op(std::string&& value) {
  
  op_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
}
inline void XlaAutoClusteringSummary_OpAndCount::set_op(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
}
inline void XlaAutoClusteringSummary_OpAndCount::set_op(const char* value, size_t size) {
  
  op_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
}
inline std::string* XlaAutoClusteringSummary_OpAndCount::mutable_op() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
  return op_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* XlaAutoClusteringSummary_OpAndCount::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
  
  return op_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void XlaAutoClusteringSummary_OpAndCount::set_allocated_op(std::string* op) {
  if (op != nullptr) {
    
  } else {
    
  }
  op_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
}

// int32 count = 2;
inline void XlaAutoClusteringSummary_OpAndCount::clear_count() {
  count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 XlaAutoClusteringSummary_OpAndCount::count() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.OpAndCount.count)
  return count_;
}
inline void XlaAutoClusteringSummary_OpAndCount::set_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.OpAndCount.count)
}

// -------------------------------------------------------------------

// XlaAutoClusteringSummary_Cluster

// string name = 1;
inline void XlaAutoClusteringSummary_Cluster::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& XlaAutoClusteringSummary_Cluster::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.Cluster.name)
  return name_.GetNoArena();
}
inline void XlaAutoClusteringSummary_Cluster::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.Cluster.name)
}
inline void XlaAutoClusteringSummary_Cluster::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.XlaAutoClusteringSummary.Cluster.name)
}
inline void XlaAutoClusteringSummary_Cluster::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.XlaAutoClusteringSummary.Cluster.name)
}
inline void XlaAutoClusteringSummary_Cluster::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.XlaAutoClusteringSummary.Cluster.name)
}
inline std::string* XlaAutoClusteringSummary_Cluster::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringSummary.Cluster.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* XlaAutoClusteringSummary_Cluster::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.XlaAutoClusteringSummary.Cluster.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void XlaAutoClusteringSummary_Cluster::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.XlaAutoClusteringSummary.Cluster.name)
}

// int32 size = 2;
inline void XlaAutoClusteringSummary_Cluster::clear_size() {
  size_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 XlaAutoClusteringSummary_Cluster::size() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.Cluster.size)
  return size_;
}
inline void XlaAutoClusteringSummary_Cluster::set_size(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.Cluster.size)
}

// repeated .tensorflow.XlaAutoClusteringSummary.OpAndCount op_histogram = 3;
inline int XlaAutoClusteringSummary_Cluster::op_histogram_size() const {
  return op_histogram_.size();
}
inline void XlaAutoClusteringSummary_Cluster::clear_op_histogram() {
  op_histogram_.Clear();
}
inline ::tensorflow::XlaAutoClusteringSummary_OpAndCount* XlaAutoClusteringSummary_Cluster::mutable_op_histogram(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringSummary.Cluster.op_histogram)
  return op_histogram_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >*
XlaAutoClusteringSummary_Cluster::mutable_op_histogram() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.XlaAutoClusteringSummary.Cluster.op_histogram)
  return &op_histogram_;
}
inline const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& XlaAutoClusteringSummary_Cluster::op_histogram(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.Cluster.op_histogram)
  return op_histogram_.Get(index);
}
inline ::tensorflow::XlaAutoClusteringSummary_OpAndCount* XlaAutoClusteringSummary_Cluster::add_op_histogram() {
  // @@protoc_insertion_point(field_add:tensorflow.XlaAutoClusteringSummary.Cluster.op_histogram)
  return op_histogram_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >&
XlaAutoClusteringSummary_Cluster::op_histogram() const {
  // @@protoc_insertion_point(field_list:tensorflow.XlaAutoClusteringSummary.Cluster.op_histogram)
  return op_histogram_;
}

// -------------------------------------------------------------------

// XlaAutoClusteringSummary

// int32 unclustered_node_count = 1;
inline void XlaAutoClusteringSummary::clear_unclustered_node_count() {
  unclustered_node_count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 XlaAutoClusteringSummary::unclustered_node_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.unclustered_node_count)
  return unclustered_node_count_;
}
inline void XlaAutoClusteringSummary::set_unclustered_node_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  unclustered_node_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.unclustered_node_count)
}

// int32 clustered_node_count = 2;
inline void XlaAutoClusteringSummary::clear_clustered_node_count() {
  clustered_node_count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 XlaAutoClusteringSummary::clustered_node_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.clustered_node_count)
  return clustered_node_count_;
}
inline void XlaAutoClusteringSummary::set_clustered_node_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  clustered_node_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.clustered_node_count)
}

// repeated .tensorflow.XlaAutoClusteringSummary.Cluster clusters = 3;
inline int XlaAutoClusteringSummary::clusters_size() const {
  return clusters_.size();
}
inline void XlaAutoClusteringSummary::clear_clusters() {
  clusters_.Clear();
}
inline ::tensorflow::XlaAutoClusteringSummary_Cluster* XlaAutoClusteringSummary::mutable_clusters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringSummary.clusters)
  return clusters_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_Cluster >*
XlaAutoClusteringSummary::mutable_clusters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.XlaAutoClusteringSummary.clusters)
  return &clusters_;
}
inline const ::tensorflow::XlaAutoClusteringSummary_Cluster& XlaAutoClusteringSummary::clusters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.clusters)
  return clusters_.Get(index);
}
inline ::tensorflow::XlaAutoClusteringSummary_Cluster* XlaAutoClusteringSummary::add_clusters() {
  // @@protoc_insertion_point(field_add:tensorflow.XlaAutoClusteringSummary.clusters)
  return clusters_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_Cluster >&
XlaAutoClusteringSummary::clusters() const {
  // @@protoc_insertion_point(field_list:tensorflow.XlaAutoClusteringSummary.clusters)
  return clusters_;
}

// repeated .tensorflow.XlaAutoClusteringSummary.OpAndCount unclustered_op_histogram = 4;
inline int XlaAutoClusteringSummary::unclustered_op_histogram_size() const {
  return unclustered_op_histogram_.size();
}
inline void XlaAutoClusteringSummary::clear_unclustered_op_histogram() {
  unclustered_op_histogram_.Clear();
}
inline ::tensorflow::XlaAutoClusteringSummary_OpAndCount* XlaAutoClusteringSummary::mutable_unclustered_op_histogram(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringSummary.unclustered_op_histogram)
  return unclustered_op_histogram_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >*
XlaAutoClusteringSummary::mutable_unclustered_op_histogram() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.XlaAutoClusteringSummary.unclustered_op_histogram)
  return &unclustered_op_histogram_;
}
inline const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& XlaAutoClusteringSummary::unclustered_op_histogram(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.unclustered_op_histogram)
  return unclustered_op_histogram_.Get(index);
}
inline ::tensorflow::XlaAutoClusteringSummary_OpAndCount* XlaAutoClusteringSummary::add_unclustered_op_histogram() {
  // @@protoc_insertion_point(field_add:tensorflow.XlaAutoClusteringSummary.unclustered_op_histogram)
  return unclustered_op_histogram_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >&
XlaAutoClusteringSummary::unclustered_op_histogram() const {
  // @@protoc_insertion_point(field_list:tensorflow.XlaAutoClusteringSummary.unclustered_op_histogram)
  return unclustered_op_histogram_;
}

// -------------------------------------------------------------------

// XlaAutoClusteringActivity

// .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 1;
inline void XlaAutoClusteringActivity::clear_global_jit_level() {
  global_jit_level_ = 0;
}
inline ::tensorflow::OptimizerOptions_GlobalJitLevel XlaAutoClusteringActivity::global_jit_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringActivity.global_jit_level)
  return static_cast< ::tensorflow::OptimizerOptions_GlobalJitLevel >(global_jit_level_);
}
inline void XlaAutoClusteringActivity::set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value) {
  
  global_jit_level_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringActivity.global_jit_level)
}

// bool cpu_global_jit_enabled = 2;
inline void XlaAutoClusteringActivity::clear_cpu_global_jit_enabled() {
  cpu_global_jit_enabled_ = false;
}
inline bool XlaAutoClusteringActivity::cpu_global_jit_enabled() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringActivity.cpu_global_jit_enabled)
  return cpu_global_jit_enabled_;
}
inline void XlaAutoClusteringActivity::set_cpu_global_jit_enabled(bool value) {
  
  cpu_global_jit_enabled_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringActivity.cpu_global_jit_enabled)
}

// .tensorflow.XlaAutoClusteringSummary summary = 3;
inline bool XlaAutoClusteringActivity::has_summary() const {
  return this != internal_default_instance() && summary_ != nullptr;
}
inline void XlaAutoClusteringActivity::clear_summary() {
  if (GetArenaNoVirtual() == nullptr && summary_ != nullptr) {
    delete summary_;
  }
  summary_ = nullptr;
}
inline const ::tensorflow::XlaAutoClusteringSummary& XlaAutoClusteringActivity::summary() const {
  const ::tensorflow::XlaAutoClusteringSummary* p = summary_;
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringActivity.summary)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::XlaAutoClusteringSummary*>(
      &::tensorflow::_XlaAutoClusteringSummary_default_instance_);
}
inline ::tensorflow::XlaAutoClusteringSummary* XlaAutoClusteringActivity::release_summary() {
  // @@protoc_insertion_point(field_release:tensorflow.XlaAutoClusteringActivity.summary)
  
  ::tensorflow::XlaAutoClusteringSummary* temp = summary_;
  summary_ = nullptr;
  return temp;
}
inline ::tensorflow::XlaAutoClusteringSummary* XlaAutoClusteringActivity::mutable_summary() {
  
  if (summary_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::XlaAutoClusteringSummary>(GetArenaNoVirtual());
    summary_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringActivity.summary)
  return summary_;
}
inline void XlaAutoClusteringActivity::set_allocated_summary(::tensorflow::XlaAutoClusteringSummary* summary) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete summary_;
  }
  if (summary) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      summary = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, summary, submessage_arena);
    }
    
  } else {
    
  }
  summary_ = summary;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.XlaAutoClusteringActivity.summary)
}

// -------------------------------------------------------------------

// XlaJitCompilationActivity

// string cluster_name = 1;
inline void XlaJitCompilationActivity::clear_cluster_name() {
  cluster_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& XlaJitCompilationActivity::cluster_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaJitCompilationActivity.cluster_name)
  return cluster_name_.GetNoArena();
}
inline void XlaJitCompilationActivity::set_cluster_name(const std::string& value) {
  
  cluster_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaJitCompilationActivity.cluster_name)
}
inline void XlaJitCompilationActivity::set_cluster_name(std::string&& value) {
  
  cluster_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.XlaJitCompilationActivity.cluster_name)
}
inline void XlaJitCompilationActivity::set_cluster_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  cluster_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.XlaJitCompilationActivity.cluster_name)
}
inline void XlaJitCompilationActivity::set_cluster_name(const char* value, size_t size) {
  
  cluster_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.XlaJitCompilationActivity.cluster_name)
}
inline std::string* XlaJitCompilationActivity::mutable_cluster_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaJitCompilationActivity.cluster_name)
  return cluster_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* XlaJitCompilationActivity::release_cluster_name() {
  // @@protoc_insertion_point(field_release:tensorflow.XlaJitCompilationActivity.cluster_name)
  
  return cluster_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void XlaJitCompilationActivity::set_allocated_cluster_name(std::string* cluster_name) {
  if (cluster_name != nullptr) {
    
  } else {
    
  }
  cluster_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cluster_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.XlaJitCompilationActivity.cluster_name)
}

// int32 compile_count = 2;
inline void XlaJitCompilationActivity::clear_compile_count() {
  compile_count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 XlaJitCompilationActivity::compile_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaJitCompilationActivity.compile_count)
  return compile_count_;
}
inline void XlaJitCompilationActivity::set_compile_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  compile_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.XlaJitCompilationActivity.compile_count)
}

// int64 compile_time_us = 3;
inline void XlaJitCompilationActivity::clear_compile_time_us() {
  compile_time_us_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XlaJitCompilationActivity::compile_time_us() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaJitCompilationActivity.compile_time_us)
  return compile_time_us_;
}
inline void XlaJitCompilationActivity::set_compile_time_us(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  compile_time_us_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.XlaJitCompilationActivity.compile_time_us)
}

// int64 cumulative_compile_time_us = 4;
inline void XlaJitCompilationActivity::clear_cumulative_compile_time_us() {
  cumulative_compile_time_us_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 XlaJitCompilationActivity::cumulative_compile_time_us() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaJitCompilationActivity.cumulative_compile_time_us)
  return cumulative_compile_time_us_;
}
inline void XlaJitCompilationActivity::set_cumulative_compile_time_us(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  cumulative_compile_time_us_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.XlaJitCompilationActivity.cumulative_compile_time_us)
}

// -------------------------------------------------------------------

// XlaOptimizationRemark

// .tensorflow.XlaOptimizationRemark.Warning warning = 1;
inline void XlaOptimizationRemark::clear_warning() {
  warning_ = 0;
}
inline ::tensorflow::XlaOptimizationRemark_Warning XlaOptimizationRemark::warning() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaOptimizationRemark.warning)
  return static_cast< ::tensorflow::XlaOptimizationRemark_Warning >(warning_);
}
inline void XlaOptimizationRemark::set_warning(::tensorflow::XlaOptimizationRemark_Warning value) {
  
  warning_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.XlaOptimizationRemark.warning)
}

// string debug_information = 2;
inline void XlaOptimizationRemark::clear_debug_information() {
  debug_information_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& XlaOptimizationRemark::debug_information() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaOptimizationRemark.debug_information)
  return debug_information_.GetNoArena();
}
inline void XlaOptimizationRemark::set_debug_information(const std::string& value) {
  
  debug_information_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaOptimizationRemark.debug_information)
}
inline void XlaOptimizationRemark::set_debug_information(std::string&& value) {
  
  debug_information_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.XlaOptimizationRemark.debug_information)
}
inline void XlaOptimizationRemark::set_debug_information(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  debug_information_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.XlaOptimizationRemark.debug_information)
}
inline void XlaOptimizationRemark::set_debug_information(const char* value, size_t size) {
  
  debug_information_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.XlaOptimizationRemark.debug_information)
}
inline std::string* XlaOptimizationRemark::mutable_debug_information() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaOptimizationRemark.debug_information)
  return debug_information_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* XlaOptimizationRemark::release_debug_information() {
  // @@protoc_insertion_point(field_release:tensorflow.XlaOptimizationRemark.debug_information)
  
  return debug_information_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void XlaOptimizationRemark::set_allocated_debug_information(std::string* debug_information) {
  if (debug_information != nullptr) {
    
  } else {
    
  }
  debug_information_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), debug_information);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.XlaOptimizationRemark.debug_information)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::XlaOptimizationRemark_Warning> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::XlaOptimizationRemark_Warning>() {
  return ::tensorflow::XlaOptimizationRemark_Warning_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto
