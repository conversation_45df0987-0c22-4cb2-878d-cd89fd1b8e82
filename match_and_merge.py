#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据匹配和合并工具
实现两个数据文件的关联匹配并合并特定列的数据

源文件1: D:\ECG\0723一分钟项目测试\标注平台数据\数据\一分钟接口结论\合并结果.csv
源文件2: D:\ECG\0723一分钟项目测试\标注平台数据\无标题.xls

匹配逻辑:
- 源文件1的最后一列 与 源文件2的 es_key + lead 列进行匹配
- 匹配成功时，将源文件2的 disease_name 列内容添加到合并结果中
"""

import pandas as pd
import numpy as np
import os
import logging
from typing import Tuple, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('match_and_merge.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DataMatcher:
    """数据匹配和合并类"""

    def __init__(self, file1_path=None, file2_path=None, output_path=None):
        # 默认文件路径
        self.file1_path = file1_path or r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\一分钟接口结论\合并结果.csv"
        self.file2_path = file2_path or r"D:\ECG\0723一分钟项目测试\标注平台数据\无标题.xls"
        self.output_path = output_path or "merged_result_with_disease.csv"
        
    def load_data(self) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """加载数据文件"""
        df1, df2 = None, None
        
        try:
            # 加载源文件1 (CSV格式)
            if os.path.exists(self.file1_path):
                df1 = pd.read_csv(self.file1_path, encoding='utf-8')
                logger.info(f"成功加载文件1: {self.file1_path}")
                logger.info(f"文件1形状: {df1.shape}")
                logger.info(f"文件1列名: {list(df1.columns)}")
            else:
                logger.error(f"文件1不存在: {self.file1_path}")
                
        except Exception as e:
            logger.error(f"加载文件1失败: {e}")
            try:
                # 尝试其他编码
                df1 = pd.read_csv(self.file1_path, encoding='gbk')
                logger.info("使用GBK编码成功加载文件1")
            except Exception as e2:
                logger.error(f"使用GBK编码也失败: {e2}")
        
        try:
            # 加载源文件2 (Excel格式)
            if os.path.exists(self.file2_path):
                df2 = pd.read_excel(self.file2_path)
                logger.info(f"成功加载文件2: {self.file2_path}")
                logger.info(f"文件2形状: {df2.shape}")
                logger.info(f"文件2列名: {list(df2.columns)}")
            else:
                logger.error(f"文件2不存在: {self.file2_path}")
                
        except Exception as e:
            logger.error(f"加载文件2失败: {e}")
            
        return df1, df2
    
    def prepare_matching_keys(self, df1: pd.DataFrame, df2: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
        """准备匹配键"""
        # 获取文件1的最后一列作为匹配键
        last_column = df1.columns[-1]
        key1 = df1[last_column].astype(str).str.strip()
        logger.info(f"文件1匹配键列名: {last_column}")
        logger.info(f"文件1匹配键示例: {key1.head().tolist()}")
        
        # 检查文件2是否包含必需的列
        required_cols = ['es_key', 'lead', 'disease_name']
        missing_cols = [col for col in required_cols if col not in df2.columns]
        
        if missing_cols:
            logger.error(f"文件2缺少必需的列: {missing_cols}")
            logger.info(f"文件2实际列名: {list(df2.columns)}")
            # 尝试模糊匹配列名
            self._suggest_column_matches(df2.columns, required_cols)
            raise ValueError(f"文件2缺少必需的列: {missing_cols}")
        
        # 创建文件2的复合匹配键 (es_key + "_" + lead)
        # 尝试多种匹配格式以提高匹配成功率
        key2_with_underscore = (df2['es_key'].astype(str).str.strip() + "_" +
                               df2['lead'].astype(str).str.strip())
        key2_without_underscore = (df2['es_key'].astype(str).str.strip() +
                                  df2['lead'].astype(str).str.strip())

        logger.info(f"文件2匹配键示例(带下划线): {key2_with_underscore.head().tolist()}")
        logger.info(f"文件2匹配键示例(不带下划线): {key2_without_underscore.head().tolist()}")

        return key1, key2_with_underscore, key2_without_underscore
    
    def _suggest_column_matches(self, actual_cols, required_cols):
        """建议可能的列名匹配"""
        logger.info("尝试匹配相似的列名:")
        for req_col in required_cols:
            suggestions = []
            for actual_col in actual_cols:
                # 简单的相似度检查
                if req_col.lower() in actual_col.lower() or actual_col.lower() in req_col.lower():
                    suggestions.append(actual_col)
            if suggestions:
                logger.info(f"  {req_col} 可能对应: {suggestions}")
    
    def perform_matching(self, df1: pd.DataFrame, df2: pd.DataFrame) -> pd.DataFrame:
        """执行数据匹配"""
        try:
            # 准备匹配键
            key1, key2_with_underscore, key2_without_underscore = self.prepare_matching_keys(df1, df2)

            # 创建多种格式的匹配映射字典
            disease_mapping_with_underscore = dict(zip(key2_with_underscore, df2['disease_name']))
            disease_mapping_without_underscore = dict(zip(key2_without_underscore, df2['disease_name']))

            # 首先尝试带下划线的匹配
            df1['disease_name'] = key1.map(disease_mapping_with_underscore)
            matched_with_underscore = df1['disease_name'].notna().sum()

            # 对于未匹配的记录，尝试不带下划线的匹配
            unmatched_mask = df1['disease_name'].isna()
            if unmatched_mask.any():
                df1.loc[unmatched_mask, 'disease_name'] = key1[unmatched_mask].map(disease_mapping_without_underscore)

            matched_without_underscore = df1['disease_name'].notna().sum() - matched_with_underscore

            logger.info(f"匹配策略统计:")
            logger.info(f"  带下划线匹配成功: {matched_with_underscore}")
            logger.info(f"  不带下划线匹配成功: {matched_without_underscore}")
            
            # 统计匹配结果
            matched_count = df1['disease_name'].notna().sum()
            total_count = len(df1)
            match_rate = matched_count / total_count * 100
            
            logger.info(f"匹配统计:")
            logger.info(f"  总记录数: {total_count}")
            logger.info(f"  成功匹配: {matched_count}")
            logger.info(f"  未匹配: {total_count - matched_count}")
            logger.info(f"  匹配率: {match_rate:.2f}%")
            
            # 显示未匹配的键示例
            unmatched_keys = key1[df1['disease_name'].isna()].unique()[:10]
            if len(unmatched_keys) > 0:
                logger.warning(f"未匹配键示例: {unmatched_keys.tolist()}")
            
            # 显示匹配成功的示例
            matched_sample = df1[df1['disease_name'].notna()].head()
            if not matched_sample.empty:
                logger.info("匹配成功示例:")
                for idx, row in matched_sample.iterrows():
                    logger.info(f"  {key1.iloc[idx]} -> {row['disease_name']}")
            
            return df1
            
        except Exception as e:
            logger.error(f"匹配过程中发生错误: {e}")
            raise
    
    def save_result(self, merged_df: pd.DataFrame):
        """保存合并结果"""
        try:
            merged_df.to_csv(self.output_path, index=False, encoding='utf-8-sig')
            logger.info(f"合并结果已保存到: {self.output_path}")
            
            # 保存匹配统计报告
            report_path = "matching_report.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("数据匹配和合并报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"输入文件1: {self.file1_path}\n")
                f.write(f"输入文件2: {self.file2_path}\n")
                f.write(f"输出文件: {self.output_path}\n\n")
                
                total_count = len(merged_df)
                matched_count = merged_df['disease_name'].notna().sum()
                f.write(f"总记录数: {total_count}\n")
                f.write(f"成功匹配: {matched_count}\n")
                f.write(f"未匹配: {total_count - matched_count}\n")
                f.write(f"匹配率: {matched_count/total_count*100:.2f}%\n\n")
                
                # 疾病名称分布
                disease_counts = merged_df['disease_name'].value_counts()
                f.write("疾病名称分布:\n")
                for disease, count in disease_counts.head(20).items():
                    f.write(f"  {disease}: {count}\n")
                    
            logger.info(f"匹配报告已保存到: {report_path}")
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise
    
    def run(self):
        """执行完整的匹配和合并流程"""
        logger.info("开始数据匹配和合并流程")
        
        try:
            # 1. 加载数据
            df1, df2 = self.load_data()
            
            if df1 is None or df2 is None:
                logger.error("数据加载失败，无法继续")
                return False
            
            # 2. 执行匹配
            merged_df = self.perform_matching(df1, df2)
            
            # 3. 保存结果
            self.save_result(merged_df)
            
            logger.info("数据匹配和合并流程完成")
            return True
            
        except Exception as e:
            logger.error(f"流程执行失败: {e}")
            return False


def find_possible_files():
    """查找可能的数据文件"""
    possible_files = []

    # 在当前目录及子目录中查找可能的文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith(('.csv', '.xls', '.xlsx')):
                full_path = os.path.join(root, file)
                possible_files.append(full_path)

    return possible_files


def interactive_file_selection():
    """交互式文件选择"""
    print("正在查找可能的数据文件...")
    possible_files = find_possible_files()

    if not possible_files:
        print("未找到任何CSV或Excel文件")
        return None, None

    print(f"\n找到 {len(possible_files)} 个可能的文件:")
    for i, file_path in enumerate(possible_files, 1):
        print(f"{i}. {file_path}")

    print("\n请选择文件:")

    # 选择文件1
    while True:
        try:
            choice1 = input("请输入源文件1的编号 (包含最后一列作为匹配键): ")
            if choice1.lower() == 'q':
                return None, None
            idx1 = int(choice1) - 1
            if 0 <= idx1 < len(possible_files):
                file1 = possible_files[idx1]
                break
            else:
                print("无效的编号，请重新输入")
        except ValueError:
            print("请输入有效的数字")

    # 选择文件2
    while True:
        try:
            choice2 = input("请输入源文件2的编号 (包含es_key, lead, disease_name列): ")
            if choice2.lower() == 'q':
                return None, None
            idx2 = int(choice2) - 1
            if 0 <= idx2 < len(possible_files):
                file2 = possible_files[idx2]
                break
            else:
                print("无效的编号，请重新输入")
        except ValueError:
            print("请输入有效的数字")

    return file1, file2


def main():
    """主函数"""
    print("数据匹配和合并工具")
    print("=" * 50)

    # 尝试使用默认路径
    matcher = DataMatcher()

    # 检查默认文件是否存在
    if not (os.path.exists(matcher.file1_path) and os.path.exists(matcher.file2_path)):
        print("默认文件路径不存在，启动交互式文件选择...")
        file1, file2 = interactive_file_selection()

        if file1 is None or file2 is None:
            print("未选择文件，程序退出")
            return

        matcher = DataMatcher(file1, file2)

    success = matcher.run()

    if success:
        print("\n" + "=" * 50)
        print("数据匹配和合并成功完成！")
        print(f"结果文件: {matcher.output_path}")
        print("详细日志请查看: match_and_merge.log")
        print("匹配报告请查看: matching_report.txt")
    else:
        print("\n" + "=" * 50)
        print("数据匹配和合并失败，请查看日志了解详情")


if __name__ == "__main__":
    main()
