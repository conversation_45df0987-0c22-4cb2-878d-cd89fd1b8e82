         1141159 function calls (1133418 primitive calls) in 183.574 seconds

   Ordered by: internal time

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
      235  178.725    0.761  178.725    0.761 {method 'recv_into' of '_socket.socket' objects}
        2    4.195    2.097    4.195    2.097 {built-in method builtins.input}
      154    0.074    0.000    0.074    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:204(iterencode)
        1    0.071    0.071    0.071    0.071 {method 'do_handshake' of '_ssl._SSLSocket' objects}
        2    0.058    0.029    0.082    0.041 {method 'read_low_memory' of 'pandas._libs.parsers.TextReader' objects}
        2    0.041    0.020    0.041    0.020 {method 'read' of '_ssl._SSLSocket' objects}
       78    0.037    0.000    0.037    0.000 {method 'connect' of '_socket.socket' objects}
231563/231561    0.018    0.000    0.030    0.000 {built-in method builtins.isinstance}
       78    0.015    0.000    0.015    0.000 {built-in method _socket.getaddrinfo}
     6670    0.010    0.000    0.030    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:470(sanitize_array)
        1    0.010    0.010    0.038    0.038 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:344(_concatenate_chunks)
        2    0.009    0.004    0.009    0.004 {built-in method io.open}
    66218    0.008    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\generic.py:43(_check)
    23030    0.007    0.000    0.016    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:670(__getitem__)
    24565    0.006    0.000    0.025    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:742(__iter__)
        1    0.006    0.006  179.218  179.218 D:\Project\apps\api\tests\api_test.py:236(process_single_file)
      186    0.006    0.000    0.006    0.000 {built-in method nt.stat}
     1009    0.005    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:288(__init__)
       77    0.005    0.000    0.005    0.000 {method 'tolist' of 'numpy.ndarray' objects}
   101988    0.005    0.000    0.005    0.000 {built-in method builtins.getattr}
    19712    0.005    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1587(_is_dtype_type)
    23030    0.005    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:740(encodekey)
      156    0.004    0.000    0.028    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2494(getproxies_environment)
    14657    0.004    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1429(is_extension_array_dtype)
        2    0.004    0.002    0.038    0.019 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:596(_homogenize)
       81    0.004    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:227(_isna_array)
    13009    0.004    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:680(is_integer_dtype)
     6519    0.004    0.000    0.004    0.000 {built-in method numpy.core._multiarray_umath.implement_array_function}
41960/34867    0.004    0.000    0.005    0.000 {built-in method builtins.len}
    13000    0.003    0.000    0.018    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:497(is_categorical_dtype)
     6670    0.003    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:698(_try_cast)
     1009    0.003    0.000    0.003    0.000 {method 'write' of '_io.TextIOWrapper' objects}
    13000    0.003    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\base.py:286(is_dtype)
       76    0.003    0.000    0.003    0.000 {function socket.close at 0x00000245F79A4670}
      312    0.003    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:210(_encode_invalid_chars)
        3    0.003    0.001    0.003    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2074(_stack_arrays)
    22464    0.003    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:693(__iter__)
        1    0.002    0.002    0.003    0.003 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:45(__init__)
    23030    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:734(check_str)
       79    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:219(__init__)
    27709    0.002    0.000    0.002    0.000 {method 'lower' of 'str' objects}
     1012    0.002    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:180(split)
  236/235    0.002    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:323(__init__)
    13097    0.002    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:155(<lambda>)
     6515    0.002    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1240(is_float_dtype)
    34898    0.002    0.000    0.002    0.000 {built-in method builtins.issubclass}
       14    0.002    0.000    0.002    0.000 {pandas._libs.lib.maybe_convert_objects}
     1009    0.002    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1514(findCaller)
     6670    0.002    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:630(_sanitize_ndim)
    13097    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:150(classes_and_not_datetimelike)
       78    0.002    0.000  178.942    2.294 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:535(urlopen)
      156    0.002    0.000    0.002    0.000 {built-in method winreg.OpenKey}
      154    0.002    0.000    0.002    0.000 {method 'sendall' of '_socket.socket' objects}
       78    0.002    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\decoder.py:343(raw_decode)
      234    0.002    0.000    0.002    0.000 {built-in method winreg.QueryValueEx}
     6500    0.002    0.000    0.006    0.000 <__array_function__ internals>:2(concatenate)
     6500    0.002    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:360(<setcomp>)
    23424    0.002    0.000    0.002    0.000 {method 'upper' of 'str' objects}
     2205    0.002    0.000    0.002    0.000 {method 'match' of 're.Pattern' objects}
      782    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:366(urlparse)
       78    0.001    0.000  178.924    2.294 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:379(_make_request)
      156    0.001    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:333(parse_url)
       78    0.001    0.000  178.967    2.294 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:673(send)
     6672    0.001    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:379(extract_array)
     6509    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2006(_grouping_func)
    13900    0.001    0.000    0.001    0.000 {built-in method builtins.hasattr}
     2018    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:44(normcase)
       77    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:310(_slice)
      624    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:824(update)
     1014    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:124(splitdrive)
     6666    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:552(require_length_match)
     1009    0.001    0.000    0.010    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1073(emit)
       78    0.001    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:204(parse_headers)
  453/373    0.001    0.000    0.001    0.000 {built-in method numpy.array}
     1009    0.001    0.000    0.034    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1565(_log)
       78    0.001    0.000  178.957    2.294 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:613(send)
      156    0.001    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:218(_parsegen)
3225/3146    0.001    0.000    0.001    0.000 {method 'encode' of 'str' objects}
     6500    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:356(<listcomp>)
      623    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1208(putheader)
     1010    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:121(_splitext)
       78    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:471(_parse_headers)
     6670    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:667(_sanitize_str_dtypes)
      546    0.001    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:61(merge_setting)
     6670    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:687(_maybe_repeat)
      637    0.001    0.000  178.767    0.281 {method 'readline' of '_io.BufferedReader' objects}
     1009    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1062(flush)
      860    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:417(urlsplit)
     2051    0.001    0.000    0.001    0.000 {built-in method _abc._abc_instancecheck}
        1    0.001    0.001    0.001    0.001 {pandas._libs.writers.write_csv_rows}
       79    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5037(__getitem__)
     1009    0.001    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:941(handle)
     6658    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1711(sanitize_to_nanoseconds)
       78    0.001    0.000  178.766    2.292 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:267(_read_status)
     1009    0.001    0.000    0.036    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2074(info)
       78    0.001    0.000  178.782    2.292 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:300(begin)
     1009    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:655(format)
     1009    0.001    0.000    0.035    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1436(info)
       76    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:42(_retry_on_intr)
       78    0.001    0.000  179.034    2.295 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:500(request)
       78    0.001    0.000    0.030    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:457(prepare_request)
      240    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:239(__init__)
     1798    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:110(_coerce_args)
       78    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:301(makefile)
        1    0.001    0.001    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:340(_maybe_dedup_names)
       78    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:201(__init__)
     6500    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:358(<setcomp>)
     4652    0.001    0.000    0.001    0.000 {method 'get' of 'dict' objects}
     2577    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:46(__setitem__)
     6520    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:909(__len__)
       78    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:79(_default_key_normalizer)
       78    0.001    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:633(from_httplib)
        2    0.001    0.000    0.006    0.003 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2022(_form_blocks)
        1    0.001    0.001    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:494(<listcomp>)
      234    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:81(RLock)
        2    0.001    0.000    0.171    0.086 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1276(get_chunk)
       78    0.001    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:204(get_netrc_auth)
     1009    0.001    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1550(makeRecord)
        3    0.001    0.000    0.001    0.000 {built-in method io.open_code}
     1009    0.001    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1645(callHandlers)
     1010    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:203(splitext)
      870    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:657(get)
       78    0.001    0.000    0.022    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1254(_send_request)
      624    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:462(get)
       77    0.001    0.000  178.954    2.324 D:\Project\apps\api\tests\api_test.py:111(ecg_analysis)
       78    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:658(__init__)
      234    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1258(__init__)
      159    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2043(new_block)
      547    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:345(to_key_val_list)
       78    0.001    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:409(prepare_url)
     7235    0.001    0.000    0.001    0.000 {pandas._libs.lib.is_list_like}
     6615    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:147(<lambda>)
    10345    0.001    0.000    0.001    0.000 {method 'append' of 'list' objects}
      156    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:521(cookiejar_from_dict)
       77    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:476(readinto)
       77    0.001    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:535(read)
       77    0.001    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:943(__getitem__)
    13346    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:1149(cast)
       78    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1052(putrequest)
     1009    0.001    0.000    0.013    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1591(handle)
     2018    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:898(acquire)
     6668    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1416(is_1d_only_ea_dtype)
     2024    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:51(__getitem__)
       78    0.001    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:359(build_response)
       78    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:139(__init__)
       78    0.001    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:262(_get_conn)
       78    0.001    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:1279(iterrows)
      242    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5577(__setattr__)
      237    0.001    0.000  178.766    0.754 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:655(readinto)
       78    0.001    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:59(parsestr)
       23    0.001    0.000    0.001    0.000 {built-in method _codecs.utf_8_decode}
      468    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:40(__init__)
       82    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5517(__finalize__)
       77    0.001    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\tools\numeric.py:27(to_numeric)
      312    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:564(get_content_type)
      478    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:293(header_source_parse)
       98    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:121(put)
      788    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:156(__getitem__)
      162    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:289(expanduser)
       78    0.000    0.000    0.056    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:37(create_connection)
     7185    0.000    0.000    0.000    0.000 {method 'pop' of 'dict' objects}
     3042    0.000    0.000    0.000    0.000 {method 'rfind' of 'str' objects}
     6063    0.000    0.000    0.000    0.000 {method 'decode' of 'bytes' objects}
     6615    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:145(classes)
      392    0.000    0.000    0.000    0.000 {built-in method builtins.sorted}
     1015    0.000    0.000    0.000    0.000 {method 'search' of 're.Pattern' objects}
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:141(__init__)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexers\utils.py:457(check_array_indexer)
      156    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1596(make_cookies)
     2018    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:905(release)
      702    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:155(hostname)
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:483(prepare_headers)
      869    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:281(_sanitize_header)
      712    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:78(readline)
     1501    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\base.py:465(find)
      154    0.000    0.000    0.075    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:182(encode)
      234    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:35(__init__)
      156    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1677(extract_cookies)
     1009    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:427(usesTime)
     2104    0.000    0.000    0.000    0.000 {method 'startswith' of 'str' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:53(__init__)
     2051    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\abc.py:96(__instancecheck__)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1353(add_cookie_header)
       78    0.000    0.000    0.024    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:231(request)
       78    0.000    0.000    0.020    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:765(should_bypass_proxies)
      623    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:221(putheader)
     2880    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:58(<genexpr>)
       77    0.000    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:616(remove_na_arraylike)
      234    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:877(__init__)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:82(__init__)
     1009    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:435(_format)
       78    0.000    0.000    0.018    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:114(proxy_bypass)
     5227    0.000    0.000    0.000    0.000 {built-in method nt.fspath}
     1427    0.000    0.000    0.000    0.000 {method 'split' of 'str' objects}
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:231(extend)
     2332    0.000    0.000    0.000    0.000 {method 'acquire' of '_thread.RLock' objects}
      780    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:195(_hostinfo)
     1165    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1689(isEnabledFor)
      154    0.000    0.000    0.075    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:183(dumps)
       78    0.000    0.000  179.035    2.295 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:626(post)
      176    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:341(notify)
       78    0.000    0.000    0.036    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:750(merge_environment_settings)
      390    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1219(vals_sorted_by_key)
       77    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:444(read)
     1011    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:214(basename)
       78    0.000    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:41(parse)
       78    0.000    0.000  178.784    2.292 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1300(getresponse)
     3044    0.000    0.000    0.000    0.000 {method 'replace' of 'str' objects}
     1009    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:918(format)
       77    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1820(getitem_mgr)
   211/18    0.000    0.000    0.000    0.000 {built-in method _abc._abc_subclasscheck}
       78    0.000    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:351(prepare)
      156    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:124(extract_cookies_to_jar)
       78    0.000    0.000    0.000    0.000 {method 'setsockopt' of '_socket.socket' objects}
     6500    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:143(concatenate)
      157    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1745(from_array)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:323(_init_length)
     1662    0.000    0.000    0.000    0.000 {method 'find' of 'str' objects}
      315    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:640(name)
     1009    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:160(<lambda>)
      163    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:109(_get_single_key)
       77    0.000    0.000    0.000    0.000 D:\Project\apps\api\tests\api_test.py:150(get_disease_name)
     1009    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1306(current_thread)
      156    0.000    0.000    0.000    0.000 {method 'readlines' of '_io._IOBase' objects}
      312    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:497(get_all)
     1009    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:119(getLevelName)
       77    0.000    0.000    0.016    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:5301(dropna)
     1009    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:633(usesTime)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:153(get)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:101(push)
      156    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:244(_remove_path_dot_segments)
       77    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:288(notna)
     1113    0.000    0.000    0.000    0.000 {built-in method __new__ of type object at 0x00007FFA0EFDB810}
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:40(assert_header_parsing)
     6577    0.000    0.000    0.000    0.000 {method 'pop' of 'set' objects}
      932    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1045(_validate_header_part)
      391    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5561(__getattr__)
       78    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:446(get_connection_with_tls_context)
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1858(external_values)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1962(construct_1d_object_array_from_listlike)
     5888    0.000    0.000    0.000    0.000 {built-in method builtins.ord}
     2018    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:796(filter)
      712    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:128(__next__)
      624    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:676(items)
     1168    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:34(_get_bothseps)
      236    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:101(__init__)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:258(__init__)
      235    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1714(__init__)
     1009    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:364(getMessage)
     1009    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:639(formatMessage)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:643(__init__)
      234    0.000    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:890(content)
       78    0.000    0.000    0.016    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:998(_send_output)
     1877    0.000    0.000    0.000    0.000 {method 'rstrip' of 'str' objects}
      394    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:590(name)
      312    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:29(_splitparam)
       78    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:76(proxy_bypass_registry)
       78    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:947(json)
      859    0.000    0.000    0.007    0.000 {method 'join' of 'bytes' objects}
     1009    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:438(format)
      157    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\idna.py:147(encode)
      947    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\utils.py:51(_has_surrogates)
      623    0.000    0.000    0.000    0.000 {method 'fullmatch' of 're.Pattern' objects}
      159    0.000    0.000    0.000    0.000 {method 'settimeout' of '_socket.socket' objects}
      468    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:57(__iter__)
      155    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:105(is_bool_indexer)
       78    0.000    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:901(getaddrinfo)
      156    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:238(helper)
      156    0.000    0.000    0.016    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:939(send)
      156    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:838(select_proxy)
      100    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:91(_path_join)
      478    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:215(add)
      162    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1991(get_block_type)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:90(_urllib3_request_context)
        1    0.000    0.000    0.048    0.048 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:425(dict_to_mgr)
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:299(loads)
      869    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:311(header_fetch_parse)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:91(__new__)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:539(get_encoding_from_headers)
     1009    0.000    0.000    0.000    0.000 {method 'flush' of '_io.TextIOWrapper' objects}
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:120(__init__)
      469    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:664(__contains__)
      236    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1274(is_bool_dtype)
     2597    0.000    0.000    0.000    0.000 {method 'partition' of 'str' objects}
      390    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1223(deepvalues)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:205(<dictcomp>)
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\decoder.py:332(decode)
       77    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:1042(_get_values)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1018(get_auth_from_url)
      562    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:321(is_hashable)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:237(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:308(<dictcomp>)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:732(close)
      159    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1962(maybe_coerce_values)
      156    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:106(_encode_params)
     1100    0.000    0.000    0.000    0.000 {built-in method builtins.max}
      159    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2057(check_ndim)
       76    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:925(close)
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2357(check_bool_indexer)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:494(prepare_body)
      240    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:47(__init__)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1424(debug)
       78    0.000    0.000    0.001    0.000 {method 'close' of '_io.BufferedReader' objects}
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:140(get_cookie_header)
     1922    0.000    0.000    0.000    0.000 {method 'join' of 'str' objects}
      315    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1721(validate_all_hashable)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:514(_parse_content_type_header)
     2332    0.000    0.000    0.000    0.000 {method 'release' of '_thread.RLock' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:213(get_payload)
     1246    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:223(<genexpr>)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:263(connection_from_pool_key)
     1323    0.000    0.000    0.000    0.000 {built-in method time.time}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:909(text)
      713    0.000    0.000    0.000    0.000 {built-in method builtins.any}
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:248(connection_from_context)
       86    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:150(_isna)
      156    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:607(stream)
       78    0.000    0.000    0.056    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:161(_new_conn)
      163    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:127(_get_option)
     2026    0.000    0.000    0.000    0.000 {built-in method _thread.get_ident}
       82    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:654(_simple_new)
  403/402    0.000    0.000    0.001    0.000 {built-in method builtins.all}
       76    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:70(select_wait_for_socket)
      156    0.000    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:178(_call_parse)
      158    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:274(_normalize_host)
      158    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:97(_intenum_converter)
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:411(close)
      554    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:187(__iter__)
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:208(putrequest)
      163    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:589(_get_root)
      156    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:117(__exit__)
      390    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1750(__iter__)
      156    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:816(generate)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2662(getproxies_registry)
      478    0.000    0.000    0.000    0.000 {method 'setdefault' of 'collections.OrderedDict' objects}
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1198(is_numeric_dtype)
      466    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1034(check_header_validity)
      326    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:603(_get_deprecated_option)
     1009    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1031(name)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:546(request_url)
      157    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:16(exists)
      546    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\packages\six.py:1014(ensure_text)
        5    0.000    0.000    0.000    0.000 {method 'read' of '_io.BufferedReader' objects}
      159    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:805(is_empty_data)
       78    0.000    0.000    0.029    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_decorators.py:302(wrapper)
      156    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:542(merge_cookies)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:450(_is_method_retryable)
      176    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:246(__enter__)
      167    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7099(maybe_extract_name)
     1009    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\multiprocessing\process.py:37(current_process)
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1877(_can_hold_na)
       78    0.000    0.000    0.022    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1249(request)
      630    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1740(<genexpr>)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:474(urlunparse)
        3    0.000    0.000    0.000    0.000 {method 'close' of 'pandas._libs.parsers.TextReader' objects}
      168    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:313(__call__)
     1172    0.000    0.000    0.000    0.000 {method 'strip' of 'str' objects}
       78    0.000    0.000    0.035    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:826(get_environ_proxies)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:781(get_adapter)
       77    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:487(_fp_read)
      478    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:479(set_raw)
     1009    0.000    0.000    0.000    0.000 {built-in method nt.getpid}
       77    0.000    0.000    0.014    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:204(connect)
      234    0.000    0.000    0.000    0.000 {method 'subn' of 're.Pattern' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:375(_init_decoder)
      234    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:588(get_content_maintype)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:184(close)
      701    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:977(_output)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:459(<listcomp>)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:159(resolve_redirects)
       78    0.000    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:173(feed)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:135(super_len)
      168    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:631(__new__)
        1    0.000    0.000  179.218  179.218 D:\Project\apps\api\tests\api_test.py:38(wrapper)
     1491    0.000    0.000    0.000    0.000 {method 'items' of 'dict' objects}
      158    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:542(_set_axis)
      623    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1256(<genexpr>)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:108(__enter__)
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:225(connection_from_host)
     1798    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:99(_noop)
     1009    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\multiprocessing\process.py:189(name)
        3    0.000    0.000    0.000    0.000 {built-in method marshal.loads}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:85(path_url)
       82    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:870(quote_from_bytes)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:799(iter_content)
      156    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:76(copy)
  418/339    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:14(asarray)
      971    0.000    0.000    0.000    0.000 {method 'rpartition' of 'str' objects}
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:572(prepare_content_length)
      401    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:884(__len__)
       78    0.000    0.000    0.000    0.000 {method 'Close' of 'PyHKEY' objects}
       77    0.000    0.000    0.000    0.000 D:\Project\apps\api\tests\api_test.py:189(get_multi_label_disease_name)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:301(_put_conn)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:485(urlunsplit)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:334(__init__)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:184(clone)
        2    0.000    0.000    0.000    0.000 {built-in method pandas._libs.missing.isnaobj}
     1009    0.000    0.000    0.000    0.000 {built-in method sys._getframe}
       82    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:799(quote)
        2    0.000    0.000    0.000    0.000 {method 'close' of '_io.TextIOWrapper' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:206(<listcomp>)
      156    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:431(_error_catcher)
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:178(_can_hold_na)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:207(register_hook)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:15(default_hooks)
      163    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:571(_select_options)
      237    0.000    0.000    0.000    0.000 {method '_checkReadable' of '_io._IOBase' objects}
      163    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:255(__call__)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:197(_new_message)
       78    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:610(prepare_cookies)
      315    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:694(readable)
       77    0.000    0.000    0.001    0.000 {method 'readinto' of '_io.BufferedReader' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:304(<listcomp>)
      315    0.000    0.000    0.002    0.000 {built-in method builtins.next}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:70(close)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:54(__getitem__)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:393(prepare_method)
      936    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:416(ensure_type)
      624    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:698(__init__)
      563    0.000    0.000    0.000    0.000 {method 'endswith' of 'str' objects}
       78    0.000    0.000    0.000    0.000 {method 'pop' of 'collections.OrderedDict' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:107(get_redirect_target)
       77    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\base.py:723(tolist)
       20    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:1498(find_spec)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:342(_get_timeout)
      154    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:9(is_fp_closed)
      561    0.000    0.000    0.000    0.000 {method 'lstrip' of 'str' objects}
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:660(requote_uri)
       79    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:892(urlencode)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:210(_pop_message)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:157(_get_module_lock)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:624(unquote)
      565    0.000    0.000    0.000    0.000 {built-in method builtins.hash}
      176    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:249(__exit__)
      166    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7004(ensure_index)
       78    0.000    0.000    0.015    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2707(getproxies)
       76    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:11(is_connection_dropped)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:769(is_redirect)
      163    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:642(_warn_if_deprecated)
      155    0.000    0.000    0.000    0.000 {function HTTPResponse.close at 0x00000245F7C32EE0}
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:104(__init__)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:825(__array__)
       78    0.000    0.000    0.017    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1236(endheaders)
      544    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\_internal_utils.py:25(to_native_string)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:358(update)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:122(pushlines)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:281(release_conn)
      234    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:84(<listcomp>)
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2486(check_deprecated_indexers)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:304(cert_verify)
      176    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:261(_is_owned)
      161    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:687(_values)
       86    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:834(_reset_identity)
      161    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1862(internal_values)
       76    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:496(close)
       89    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:161(is_object_dtype)
      156    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:534(<listcomp>)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:451(items)
      708    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:122(_validate_timeout)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:636(unquote_unreserved)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1731(clear_expired_cookies)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:242(<genexpr>)
      252    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_scalar}
        7    0.000    0.000    0.000    0.000 {built-in method builtins.__build_class__}
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:323(_encode_target)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:303(_merge_pool_kwargs)
        2    0.000    0.000    0.122    0.061 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:215(read)
      313    0.000    0.000    0.000    0.000 {method 'count' of 'str' objects}
       76    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:141(wait_for_read)
        2    0.000    0.000    0.170    0.085 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1243(read)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:470(is_retry)
      156    0.000    0.000    0.000    0.000 {method 'read' of '_io.StringIO' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:486(_decref_socketios)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:774(get_proxy)
       77    0.000    0.000    0.000    0.000 {method 'tobytes' of 'memoryview' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:166(port)
      165    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:438(ensure_wrapped_if_datetimelike)
      176    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\queue.py:15(_qsize)
      234    0.000    0.000    0.000    0.000 {method 'count' of 'bytes' objects}
       91    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1148(needs_i8_conversion)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:588(prepare_auth)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:100(_set_socket_options)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:997(raise_for_status)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:630(prepare_hooks)
      156    0.000    0.000    0.000    0.000 <string>:1(__new__)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:108(allowed_gai_family)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1294(_cookie_attrs)
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:538(_can_hold_na)
      702    0.000    0.000    0.000    0.000 {method 'values' of 'collections.OrderedDict' objects}
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:209(external_values)
       82    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:83(allows_duplicate_labels)
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:423(flush)
      8/4    0.000    0.000    0.002    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:397(__new__)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\queue.py:21(_get)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:699(closed)
      312    0.000    0.000    0.000    0.000 {method 'groups' of 're.Match' objects}
       76    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:492(_real_close)
      250    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1552(get_dtype)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:396(build_connection_pool_key_attributes)
       86    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:67(isna)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:303(set_payload)
        2    0.000    0.000    0.006    0.003 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1951(create_block_manager_from_column_arrays)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:147(username)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_validators.py:218(validate_bool_kwarg)
       98    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\queue.py:18(_put)
      157    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:184(is_array_like)
      234    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:60(__len__)
       78    0.000    0.000    0.000    0.000 {method 'write' of '_io.StringIO' objects}
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:1034(get_data)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:377(_check_close)
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:196(_prepare_conn)
      234    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:44(_debug)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:181(is_multipart)
      237    0.000    0.000    0.000    0.000 {method '_checkClosed' of '_io._IOBase' objects}
      234    0.000    0.000    0.000    0.000 {method 'decode' of 'bytearray' objects}
       77    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:645(values)
      626    0.000    0.000    0.000    0.000 {method 'keys' of 'dict' objects}
       24    0.000    0.000    0.000    0.000 {built-in method numpy.empty}
       81    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:533(is_string_or_object_np_dtype)
      159    0.000    0.000    0.000    0.000 {method 'split' of 'bytes' objects}
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:406(_close_conn)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1183(_validate_method)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:183(_userinfo)
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2241(external_values)
      162    0.000    0.000    0.000    0.000 {method 'extend' of 'list' objects}
      235    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1806(_block)
       81    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:132(host)
      156    0.000    0.000    0.000    0.000 {method 'extend' of 'collections.deque' objects}
       84    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:575(dtype)
      234    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:211(<genexpr>)
      158    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\packages\six.py:991(ensure_str)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:493(_parse)
      176    0.000    0.000    0.000    0.000 {method 'acquire' of '_thread.lock' objects}
      310    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:434(isclosed)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:17(__init__)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:198(start_connect)
        1    0.000    0.000    0.000    0.000 {method '_wrap_socket' of '_ssl._SSLContext' objects}
       84    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:546(_get_axis_number)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:16(<dictcomp>)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\_internal_utils.py:38(unicode_is_ascii)
       78    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:724(<listcomp>)
      159    0.000    0.000    0.000    0.000 {method 'copy' of 'dict' objects}
       78    0.000    0.000    0.000    0.000 {method 'sort' of 'list' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:22(dispatch_hook)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:110(__init__)
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:346(apply_if_callable)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:427(__iter__)
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:349(<listcomp>)
       78    0.000    0.000    0.000    0.000 {built-in method _codecs.utf_8_encode}
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:257(<genexpr>)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\request.py:101(set_file_position)
      312    0.000    0.000    0.000    0.000 {method 'seek' of '_io.StringIO' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1287(_cookies_for_request)
      163    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:630(_translate_key)
       82    0.000    0.000    0.000    0.000 {method 'rstrip' of 'bytes' objects}
       80    0.000    0.000    0.000    0.000 {method 'update' of 'dict' objects}
      164    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_integer}
       92    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1747(pandas_dtype)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1317(_path_hooks)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:701(__len__)
        1    0.000    0.000    0.000    0.000 {method 'getpeercert' of '_ssl._SSLSocket' objects}
      556    0.000    0.000    0.000    0.000 {method 'popleft' of 'collections.deque' objects}
        4    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap>:890(_find_spec)
     12/4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:71(_compile)
      156    0.000    0.000    0.000    0.000 {method 'truncate' of '_io.StringIO' objects}
        1    0.000    0.000    0.114    0.114 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:361(connect)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:91(merge_hooks)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1192(_validate_path)
        1    0.000    0.000    0.000    0.000 {built-in method nt.listdir}
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:463(_init_dict)
      176    0.000    0.000    0.000    0.000 {method '__enter__' of '_thread.lock' objects}
        2    0.000    0.000    0.000    0.000 {method 'write' of '_ssl._SSLSocket' objects}
       84    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1851(dtype)
       23    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:319(decode)
        4    0.000    0.000    0.049    0.012 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:587(__init__)
        1    0.000    0.000    0.000    0.000 {pandas._libs.missing.isnaobj2d}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:192(_is_using_tunnel)
        2    0.000    0.000    0.011    0.005 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:601(get_handle)
      156    0.000    0.000    0.000    0.000 {built-in method time.perf_counter}
      312    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:117(info)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:523(_constructor)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_decorators.py:214(_format_argument_list)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:177(__init__)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numeric.py:2317(array_equal)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:958(<genexpr>)
      312    0.000    0.000    0.000    0.000 {method 'items' of 'collections.OrderedDict' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1179(_encode_request)
      158    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:223(connect_timeout)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:159(_encode)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:241(read_timeout)
      100    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:114(<listcomp>)
      277    0.000    0.000    0.000    0.000 {method 'setdefault' of 'dict' objects}
       77    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1847(index)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:118(resolve_default_timeout)
        9    0.000    0.000    0.000    0.000 {method 'reduce' of 'numpy.ufunc' objects}
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:743(set_environ)
       82    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:328(attrs)
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:354(dtype)
        7    0.000    0.000    0.000    0.000 {method 'astype' of 'numpy.ndarray' objects}
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:289(_compile)
        1    0.000    0.000    0.071    0.071 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:983(_create)
       78    0.000    0.000    0.000    0.000 {method 'pop' of 'list' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:398(_decode)
       83    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_float}
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:914(get_code)
        4    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap>:986(_find_and_load)
        4    0.000    0.000    0.003    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:672(_with_infer)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:1(<module>)
        4    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:1367(_get_spec)
       91    0.000    0.000    0.000    0.000 {built-in method builtins.callable}
      158    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:331(_is_all_dates)
        1    0.000    0.000    0.000    0.000 {pandas._libs.lib.dicts_to_array}
   211/18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\abc.py:100(__subclasscheck__)
      156    0.000    0.000    0.000    0.000 {method 'update' of 'collections.OrderedDict' objects}
       79    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_bool}
      156    0.000    0.000    0.000    0.000 {method 'end' of 're.Match' objects}
      164    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:349(flags)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:83(__init__)
       83    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_iterator}
        1    0.000    0.000    0.000    0.000 {pandas._libs.ops.scalar_compare}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2240(is_unique)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:125(__iter__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:1(<module>)
        1    0.000    0.000    0.013    0.013 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1191(_make_engine)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1579(maybe_cast_to_datetime)
        2    0.000    0.000    0.000    0.000 {method '_rebuild_blknos_and_blklocs' of 'pandas._libs.internals.BlockManager' objects}
      176    0.000    0.000    0.000    0.000 {method '__exit__' of '_thread.lock' objects}
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:354(cache_from_source)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1017(_clean_options)
        4    0.000    0.000    0.000    0.000 {built-in method nt.get_terminal_size}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:581(iter_slices)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:87(get_new_headers)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1466(maybe_infer_to_datetimelike)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1255(__init__)
        1    0.000    0.000    0.000    0.000 {pandas._libs.lib.fast_unique_multiple_list_gen}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:251(_get_filepath_or_buffer)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:578(add_headers)
     12/4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:174(getwidth)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1154(_process_date_conversion)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1337(add_argument)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:486(validate_integer)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numeric.py:268(full)
       10    0.000    0.000    0.000    0.000 {built-in method _thread.allocate_lock}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:31(encode)
       92    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:164(__getitem__)
       78    0.000    0.000    0.000    0.000 {built-in method time.monotonic}
        1    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_int64_int64}
       78    0.000    0.000    0.000    0.000 {method 'pop' of 'collections.deque' objects}
        1    0.000    0.000    0.013    0.013 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:885(__init__)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\proxy.py:4(connection_requires_http_tunnel)
        1    0.000    0.000    0.000    0.000 {pandas._libs.lib.infer_dtype}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:221(require)
        4    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1109(_is_binary_mode)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:216(<genexpr>)
      103    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:222(_verbose_message)
        4    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap>:956(_find_and_load_unlocked)
       47    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:233(__next)
        1    0.000    0.000    0.013    0.013 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:539(_read)
       82    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:51(allows_duplicate_labels)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:332(_validate_conn)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:536(_compile_info)
       14    0.000    0.000    0.000    0.000 {pandas._libs.lib.infer_datetimelike_array}
       22    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1330(_path_importer_cache)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:759(compile)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\weakref.py:517(__init__)
        1    0.000    0.000    0.000    0.000 {method 'get_slice' of 'pandas._libs.internals.BlockManager' objects}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\shutil.py:1312(get_terminal_size)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexers\utils.py:249(maybe_convert_indices)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:435(_parse_sub)
        4    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7123(_maybe_cast_data_without_dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:816(__init__)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:477(_init_module_attrs)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:560(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:939(_get_options_with_defaults)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:222(__init__)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:229(asarray_tuplesafe)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:161(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:937(parse)
        1    0.000    0.000    0.000    0.000 {built-in method today}
        3    0.000    0.000    0.000    0.000 {built-in method builtins.locals}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:130(filterwarnings)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3463(__getitem__)
        1    0.000    0.000    0.158    0.158 D:\Project\apps\api\tests\api_test.py:58(get_token)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1549(_fill_cache)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:306(_save_chunk)
      155    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:392(__subclasshook__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\__init__.py:70(search_function)
        1    0.000    0.000    0.003    0.003 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3376(to_csv)
       28    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:135(_path_stat)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:789(read_chunked)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:33(__init__)
       98    0.000    0.000    0.000    0.000 {method 'append' of 'collections.deque' objects}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:78(acquire)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:296(_save_body)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:544(set_alpn_protocols)
        3    0.000    0.000    0.002    0.001 <frozen importlib._bootstrap>:650(_load_unlocked)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:845(_engine)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:554(_dtype_to_subclass)
        1    0.000    0.000    0.071    0.071 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:1302(do_handshake)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:32(seterr)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1638(__init__)
       77    0.000    0.000    0.000    0.000 {function HTTPResponse.flush at 0x00000245F7C32F70}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:689(spec_from_file_location)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1427(_refine_defaults_read)
        9    0.000    0.000    0.000    0.000 {method 'format' of 'str' objects}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5192(equals)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:5613(_cmp_method)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:1193(sendall)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:934(_list_of_dict_to_arrays)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:710(verify_mode)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:112(__new__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2153(to_native_types)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:115(__init__)
       37    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1308(register)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:458(__enter__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:167(_simple_new)
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:842(exec_module)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:391(array_equivalent)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1551(as_array)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:103(release)
       14    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(copyto)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1478(_get_optional_kwargs)
        2    0.000    0.000    0.044    0.022 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:102(arrays_to_mgr)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:538(find)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:47(CSVFormatter)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:58(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:120(_take_nd_ndarray)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:189(_new_pool)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\_distutils_hack\__init__.py:102(find_spec)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:276(_optimize_charset)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:1017(_new_conn)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:638(_compile_bytecode)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:321(_name_get)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:971(_finalize_columns_and_data)
        1    0.000    0.000    0.013    0.013 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:584(read_csv)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\ssl_.py:466(is_ipaddress)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3609(take)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:212(_expand_lang)
       23    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:331(getstate)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:938(__and__)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:254(get)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ipaddress.py:1125(_ip_int_from_string)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:579(translation)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\types.py:171(__get__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:181(_add_filter)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1038(iget_values)
        1    0.000    0.000    0.003    0.003 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:1131(to_csv)
        5    0.000    0.000    0.000    0.000 {method 'all' of 'numpy.ndarray' objects}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\__init__.py:43(normalize_encoding)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1289(_is_potential_multi_index)
       48    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:306(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ipaddress.py:27(ip_address)
        3    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_all_arraylike}
        4    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:1399(find_spec)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:132(geterr)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1106(take)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:497(infer_compression)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1067(convert)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:1120(getpeercert)
        1    0.000    0.000    0.002    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:236(save)
       39    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:172(append)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:176(_validate_parse_dates_presence)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:305(_idna_encode)
        2    0.000    0.000    0.041    0.020 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:1230(recv_into)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:167(_path_isabs)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:868(__new__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1987(maybe_cast_to_integer_array)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:10400(_logical_func)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:139(_ensure_array)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1026(iget)
        1    0.000    0.000    0.000    0.000 {method 'getsockopt' of '_socket.socket' objects}
        9    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:79(_unpack_uint32)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:64(parse_parts)
       16    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1483(is_ea_or_datetimelike_dtype)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:253(apply)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:50(__init__)
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:127(_path_split)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\array_ops.py:227(comparison_op)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1451(_format_native_types)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4719(reindex)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:195(stringify_path)
        5    0.000    0.000    0.000    0.000 {method 'remove' of 'list' objects}
        2    0.000    0.000    0.000    0.000 {built-in method builtins.__import__}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:147(__enter__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:598(_code)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:718(verify_mode)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:549(module_from_spec)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:553(_classify_pyc)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:288(_get_take_nd_function_cached)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:4435(_reduce)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:536(_match_hostname)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:872(take)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1398(_add_action)
        9    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:1017(_handle_fromlist)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:631(to_native_types)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1809(_parse_known_args)
       20    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:64(_relax_case)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1774(parse_known_args)
       37    0.000    0.000    0.000    0.000 {built-in method builtins.min}
        2    0.000    0.000    0.041    0.020 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:1090(read)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:586(_format_args)
        1    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_object_object}
        2    0.000    0.000    0.000    0.000 {built-in method _imp.is_builtin}
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:250(compile)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:224(__init__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:268(_isna_string_dtype)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3530(_getitem_bool_array)
        1    0.000    0.000    0.071    0.071 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\ssl_.py:364(ssl_wrap_socket)
        1    0.000    0.000    0.000    0.000 {method '_set_alpn_protocols' of '_ssl._SSLContext' objects}
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:389(is_timedelta64_dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\base.py:744(__iter__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3908(_slice)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1116(take_nd)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:933(__init__)
        2    0.000    0.000    0.000    0.000 {built-in method numpy.arange}
        1    0.000    0.000    0.001    0.001 D:\Project\apps\api\tests\api_test.py:49(parse_arguments)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\compat\_optional.py:87(import_optional_dependency)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:189(_data)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:155(_expand_user)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:505(nested_data_to_arrays)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:437(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:338(_format_native_types)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:965(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:241(_new_conn)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ipaddress.py:1151(_parse_octet)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:355(_escape)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1567(__init__)
       19    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:863(__enter__)
       18    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:129(<genexpr>)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1493(_get_spec)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:103(close)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\ssl_match_hostname.py:98(match_hostname)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:61(__setitem__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:391(_splitnetloc)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:554(_take_preprocess_indexer_and_fill_value)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5650(f)
        1    0.000    0.000    0.114    0.114 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:1053(_validate_conn)
       19    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:867(__exit__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:223(encoded_labels)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:94(__new__)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\importlib\__init__.py:109(import_module)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3411(_ixs)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:130(iloc)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:798(to_arrays)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:83(__init__)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:484(_get_cached)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:634(reindex_indexer)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:127(is_url)
        1    0.000    0.000    0.071    0.071 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:494(wrap_socket)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:298(<setcomp>)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:262(is_dict_like)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:390(is_dataclass)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:954(__getitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1689(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1318(_validate_parse_dates_arg)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:194(_set_noconvert_columns)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:309(__init__)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:160(__len__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:560(_get_axis)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:53(shape)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:556(check_parent_directory)
        1    0.000    0.000    0.000    0.000 {method 'get_loc' of 'pandas._libs.index.IndexEngine' objects}
       14    0.000    0.000    0.000    0.000 {pandas._libs.algos.ensure_object}
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:753(value)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:249(_compile_charset)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:581(is_dtype_equal)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\idna.py:300(getregentry)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:342(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:461(_get_literal_prefix)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5632(_protect_consolidate)
        1    0.000    0.000    0.000    0.000 <frozen zipimport>:63(__init__)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:111(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\array_ops.py:58(comp_method_OBJECT_ARRAY)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\nanops.py:257(_get_values)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:610(_set_noconvert_dtype_columns)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\ssl_match_hostname.py:25(_dnsname_match)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:809(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:944(_getitem_slice)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:156(blknos)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:934(close)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:176(cb)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:502(detach)
       10    0.000    0.000    0.000    0.000 {built-in method from_bytes}
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:376(cached)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1683(_consolidate_check)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:4655(reindex)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ipaddress.py:1219(__init__)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1459(__init__)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:423(_simple)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1039(__new__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:1527(_get_slice_axis)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:689(<listcomp>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:72(_check_methods)
        5    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(array_equal)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:732(is_signed_integer_dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:57(take_nd)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3906(_box_col_values)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5646(_consolidate_inplace)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:168(from_float)
        2    0.000    0.000    0.000    0.000 {built-in method _codecs.lookup}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1075(path_stats)
        4    0.000    0.000    0.000    0.000 {built-in method numpy.seterrobj}
       27    0.000    0.000    0.000    0.000 {built-in method _imp.acquire_lock}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:77(join)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:477(__exit__)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4834(_values)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:302(__init__)
        3    0.000    0.000    0.000    0.000 {built-in method builtins.exec}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:144(_initialize_columns)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:168(_number_format)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:35(_new_module)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:145(_path_is_mode_type)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:516(_check_name_wrapper)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:853(quote_plus)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1675(is_consolidated)
        1    0.000    0.000    0.071    0.071 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\ssl_.py:490(_ssl_wrap_socket_impl)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:300(<listcomp>)
        1    0.000    0.000    0.000    0.000 {method 'take' of 'numpy.ndarray' objects}
        1    0.000    0.000    0.000    0.000 {method 'writerow' of '_csv.writer' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:1(<module>)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1578(<setcomp>)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\locale.py:384(normalize)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_methods.py:56(_all)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:768(__instancecheck__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:515(_maybe_promote)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3708(_take_with_is_copy)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:358(iget)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1055(_maybe_memory_map)
       23    0.000    0.000    0.000    0.000 {method 'add' of 'set' objects}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:586(_validate_timestamp_pyc)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:572(na_value_for_dtype)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3923(_get_item_cache)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\queue.py:12(_init)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:660(dgettext)
        1    0.000    0.000    0.000    0.000 {method 'getpeername' of '_socket.socket' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:658(_parse_args)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:786(is_unsigned_integer_dtype)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:187(close)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:753(_update_chunk_length)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2480(_get_formatter)
        1    0.000    0.000    0.000    0.000 {built-in method _csv.writer}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:725(find_spec)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:825(create_series_with_explicit_dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:522(equals)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:812(_do_date_conversions)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1589(_add_action)
        8    0.000    0.000    0.000    0.000 {built-in method numpy.geterrobj}
        4    0.000    0.000    0.000    0.000 {built-in method _sre.compile}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:286(tell)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:307(_name_includes_bit_suffix)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:98(is_file_like)
        2    0.000    0.000    0.171    0.086 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1184(__next__)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:800(find_spec)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:881(_find_spec_legacy)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:433(__enter__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:468(maybe_promote)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\nanops.py:488(nanany)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:10817(values)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:7235(isna)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:239(is_fsspec_url)
        2    0.000    0.000    0.000    0.000 <string>:2(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:2988(_construct_result)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1388(add_argument_group)
        4    0.000    0.000    0.000    0.000 {built-in method _imp.is_frozen}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:151(__exit__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1417(is_dir)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:771(__subclasscheck__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:3585(get_loc)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:566(_get_block_manager_axis)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:45(__len__)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:143(__init__)
        1    0.000    0.000    0.001    0.001 <frozen importlib._bootstrap>:1002(_gcd_import)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:76(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:921(fix_flags)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5279(identical)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1130(_get_binary_io_classes)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:113(_have_working_poll)
       27    0.000    0.000    0.000    0.000 {built-in method _imp.release_lock}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:312(<listcomp>)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:154(_path_isfile)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\common.py:55(new_method)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:347(dtype)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:468(to_native_types)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:570(_metavar_formatter)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1518(_get_handler)
        5    0.000    0.000    0.000    0.000 {built-in method builtins.setattr}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:208(write_cols)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:268(_save_header)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:678(_from_parts)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:399(_checknetloc)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\arraylike.py:38(__eq__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4952(<genexpr>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:970(from_blocks)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:512(family)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:1166(send)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:1114(_normalize_host)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1954(consume_positionals)
        4    0.000    0.000    0.000    0.000 {method 'any' of 'numpy.ndarray' objects}
       21    0.000    0.000    0.000    0.000 {built-in method _sre.unicode_iscased}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:121(getregentry)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1590(path_hook_for_FileFinder)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:492(_get_charset_prefix)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:595(isstring)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:81(groups)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:255(inner)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:288(<genexpr>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:626(is_valid_na_for_dtype)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:10458(any)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\api.py:331(default_index)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:199(_ensure_dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:252(make_block)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1693(_consolidate_inplace)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ipaddress.py:1553(_ip_int_from_string)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ipaddress.py:1805(__init__)
        2    0.000    0.000    0.000    0.000 {built-in method pandas._libs.writers.word_len}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1004(__init__)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:453(_get_iscased)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1675(getEffectiveLevel)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:429(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1091(is_datetimelike_v_numeric)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:267(make_block_same_class)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:767(_handle_chunk)
        4    0.000    0.000    0.000    0.000 {built-in method nt._path_splitroot}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:211(_call_with_frames_removed)
       16    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:302(__subclasshook__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:146(splitroot)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:718(__str__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numerictypes.py:360(issubdtype)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\algorithms.py:1356(take)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:188(_validate_dtype)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:618(consolidate)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1073(<listcomp>)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1514(_pop_action_class)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numerictypes.py:286(issubclass_)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:1413(__len__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:475(get_adjustment)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:233(mgr_to_mgr)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:383(_make_index)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:982(_prepare_conn)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\ssl_match_hostname.py:79(_to_unicode)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:126(wait_for_socket)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:851(__init__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:140(has_mi_columns)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:456(<genexpr>)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_methods.py:53(_any)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:438(__exit__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:720(__hash__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:115(ensure_python_int)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:326(_get_take_nd_function)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:890(__array__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2660(_isnan)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4013(_validate_positional_slice)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:514(_construct_axes_from_arguments)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:55(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:679(_initialize_justify)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:1238(_set_as_cached)
        4    0.000    0.000    0.000    0.000 {built-in method pandas._libs.missing.checknull}
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:263(_save)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:249(match)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:218(_acquireLock)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:287(maybe_iterable_to_list)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6298(_maybe_cast_indexer)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1314(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:486(_encode_hostname)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:202(__init__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1312(_registry_get)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:200(_has_aliases)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:27(__init__)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:389(parent)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:221(dirname)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2440(is_object)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:172(blklocs)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:462(get_compression_method)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1200(_validate_host)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\ssl_.py:220(resolve_cert_reqs)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:328(set_cert)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1747(_add_action)
        2    0.000    0.000    0.000    0.000 {built-in method _codecs.lookup_error}
       12    0.000    0.000    0.000    0.000 {built-in method _warnings._filters_mutated}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:367(__subclasshook__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:945(parent)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\copy.py:66(copy)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2352(is_floating)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:1490(_getitem_axis)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:238(fill_value)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1006(_check_file_or_buffer)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:602(_safe_read)
        2    0.000    0.000    0.000    0.000 {method 'copy' of 'numpy.ndarray' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:701(_format_parsed_parts)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:785(truncate)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:534(treat_as_nested)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1031(__init__)
        1    0.000    0.000    0.000    0.000 {method 'nonzero' of 'numpy.ndarray' objects}
       13    0.000    0.000    0.000    0.000 {method 'isalnum' of 'str' objects}
        3    0.000    0.000    0.000    0.000 {built-in method _imp._fix_co_filename}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:121(_get_index_label_from_obj)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:432(_generate_overlap_table)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:803(is_)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2604(inferred_type)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:441(_validate_dtype)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:222(get_values)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2121(extend_blocks)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:916(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:728(_calc_max_rows_fitted)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:735(gettext)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1527(_check_conflict)
       19    0.000    0.000    0.000    0.000 {built-in method _sre.unicode_tolower}
        4    0.000    0.000    0.000    0.000 {method 'fileno' of '_io.TextIOWrapper' objects}
        4    0.000    0.000    0.000    0.000 {method 'find' of 'bytearray' objects}
        3    0.000    0.000    0.000    0.000 {built-in method sys.intern}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1049(_init)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:1043(copyto)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:24(_kind_name)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1042(is_numeric_v_string_like)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:945(dtype)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:761(_is_in_terminal)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:579(format)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:168(__setitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:454(ensure_dtype_can_hold_na)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2650(_na_value)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2685(isna)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4987(__contains__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6313(_validate_indexer)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3925(_set_is_copy)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:241(start)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:919(__getitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\dataclasses.py:1045(is_dataclass)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1051(_convert_object_array)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:743(__len__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1352(_clean_na_values)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\request.py:43(__init__)
        4    0.000    0.000    0.000    0.000 {method 'insert' of 'list' objects}
        2    0.000    0.000    0.000    0.000 {method 'cast' of 'memoryview' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:130(_get_index_label_flat)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:204(_need_to_save_header)
        8    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1465(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:260(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1496(is_complex_dtype)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:160(cast_scalar_indexer)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\common.py:75(get_op_result_name)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:10880(any)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:5276(isna)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:832(_check_data_length)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1767(parse_args)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:227(_releaseLock)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:691(_from_parsed_parts)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:728(__fspath__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1193(stat)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:201(all_none)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\missing.py:911(clean_reindex_fill_method)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2323(convert_to_index_sliceable)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:222(items)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:423(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:717(_calc_max_cols_fitted)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:123(__exit__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:518(type)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:1078(_checkClosed)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ssl.py:1082(_check_connected)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1759(_get_positional_actions)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2106(_match_arguments_partial)
        1    0.000    0.000    0.000    0.000 {method 'fill' of 'numpy.ndarray' objects}
        1    0.000    0.000    0.000    0.000 {method 'reshape' of 'numpy.ndarray' objects}
        1    0.000    0.000    0.000    0.000 {method 'append' of 'bytearray' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:22(netrc)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:406(__subclasshook__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1276(disable)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\six.py:184(find_module)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:294(is_named_tuple)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\compat\numpy\function.py:49(__call__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\base.py:326(ndim)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:540(_ensure_array)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:685(_initialize_columns)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:174(validate_header_arg)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:371(_maybe_make_multi_index_columns)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:744(supports_chunked_reads)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\packages\six.py:190(find_spec)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:151(host)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:94(is_response_to_head)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:574(<listcomp>)
        1    0.000    0.000    0.000    0.000 {method 'transpose' of 'numpy.ndarray' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:163(_initialize_chunksize)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:26(IncrementalEncoder)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:937(_sanity_check)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:159(_path_isdir)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:186(__init__)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numeric.py:2313(_array_equal_dispatcher)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:507(_maybe_promote_cached)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\nanops.py:213(_maybe_get_mask)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1658(name)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4860(_get_engine_target)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5292(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:217(is_single_block)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:959(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:993(_validate_or_indexify_columns)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1062(_make_date_converter)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\c_parser_wrapper.py:408(ensure_dtype_objs)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:862(_get_hostport)
        1    0.000    0.000    0.000    0.000 {method 'extend' of 'bytearray' objects}
        6    0.000    0.000    0.000    0.000 {method 'reverse' of 'list' objects}
        3    0.000    0.000    0.000    0.000 {method 'isascii' of 'str' objects}
        1    0.000    0.000    0.000    0.000 {method 'isdigit' of 'str' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:49(IncrementalDecoder)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:100(decimal)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:112(_initialize_index_label)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:179(data_index)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:397(has_location)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:839(create_module)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:162(__delitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\nanops.py:346(_na_ok_dtype)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:229(disallow_kwargs)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:518(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2473(need_slice)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:99(_can_hold_na)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:264(stop)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:287(step)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:661(_initialize_sparsify)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:1019(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1605(_extract_dialect)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:1703(_validate_skipfooter)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:1340(is_index_col)
        1    0.000    0.000    0.000    0.000 {built-in method sys.getfilesystemencoding}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:10(NetrcParseError)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\dispatch.py:11(should_extension_dispatch)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:543(<dictcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:633(is_truncated_vertically)
        4    0.000    0.000    0.000    0.000 {pandas._libs.lib.item_from_zerodim}
        1    0.000    0.000    0.000    0.000 {method 'fileno' of '_socket.socket' objects}
        1    0.000    0.000    0.000    0.000 {method 'flush' of '_io.BufferedReader' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:134(_initialize_quotechar)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1029(get_filename)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\_distutils_hack\__init__.py:109(<lambda>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:205(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\missing.py:107(clean_fill_method)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:692(_constructor)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2632(_is_multi)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:578(_constructor)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:183(_constructor)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:629(is_truncated_horizontally)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:693(_initialize_colspace)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:240(_has_complex_date_col)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:920(_validate_usecols_arg)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:420(_flush_decoder)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1760(<listcomp>)
        3    0.000    0.000    0.000    0.000 {pandas._libs.algos.ensure_platform_int}
        1    0.000    0.000    0.000    0.000 {method 'ravel' of 'numpy.ndarray' objects}
        1    0.000    0.000    0.000    0.000 {built-in method atexit.register}
        2    0.000    0.000    0.000    0.000 {method 'values' of 'dict' objects}
        2    0.000    0.000    0.000    0.000 {built-in method builtins.iter}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:92(na_rep)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:85(StreamWriter)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:104(header)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:193(nlevels)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:86(asanyarray)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1132(_maybe_disallow_fill)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:666(_initialize_formatters)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\readers.py:515(_validate_names)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:220(<setcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1240(__init__)
        3    0.000    0.000    0.000    0.000 {method 'gettimeout' of '_socket.socket' objects}
        1    0.000    0.000    0.000    0.000 {method 'disable' of '_lsprof.Profiler' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:97(StreamReader)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:96(float_format)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:108(index)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\copy.py:107(_copy_immutable)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\nanops.py:191(_get_fill_value)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:150(is_consolidated)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:282(<dictcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:747(_adjust_max_rows)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:120(__enter__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\parsers\base_parser.py:232(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\importlib_metadata\_compat.py:44(find_spec)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:604(<listcomp>)
        1    0.000    0.000    0.000    0.000 {built-in method _stat.S_ISDIR}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:268(__iter__)
        1    0.000    0.000    0.000    0.000 {function socket.detach at 0x00000245F79A4700}


