# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Implementation of the Keras API, the high-level API of TensorFlow.

Detailed documentation and user guides are available at
[keras.io](https://keras.io).

"""

from __future__ import print_function as _print_function

import sys as _sys

from . import __internal__
from . import activations
from . import applications
from . import backend
from . import callbacks
from . import constraints
from . import datasets
from . import estimator
from . import experimental
from . import initializers
from . import layers
from . import losses
from . import metrics
from . import mixed_precision
from . import models
from . import optimizers
from . import preprocessing
from . import regularizers
from . import utils
from . import wrappers
from tensorflow.python.keras import __version__
from tensorflow.python.keras.engine.input_layer import Input
from tensorflow.python.keras.engine.sequential import Sequential
from tensorflow.python.keras.engine.training import Model

del _print_function

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras", public_apis=None, deprecation=True,
      has_lite=False)
