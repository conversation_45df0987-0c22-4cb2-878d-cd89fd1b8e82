#!/usr/bin/env python3
"""
🏸 羽毛球装备爬虫主程序 (Main Entry Point)

这是羽毛球装备爬虫的主入口文件，提供简单的命令行界面。

Author: Badminton Equipment Crawler Team
Version: 2.0
Last Update: 2025-01-17
"""

import argparse
import sys
import json
from pathlib import Path
from browser_crawler import BrowserBadmintonCrawler


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(
        description="🏸 羽毛球装备爬虫 - 智能数据采集工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 运行单个装备测试
  python main.py --url URL          # 爬取指定装备页面
  python main.py --batch            # 批量爬取多个装备
  python main.py --test             # 运行完整测试
        """
    )
    
    parser.add_argument(
        '--url',
        type=str,
        help='指定要爬取的装备页面URL'
    )
    
    parser.add_argument(
        '--batch',
        action='store_true',
        help='批量爬取多个装备（从首页获取链接）'
    )
    
    parser.add_argument(
        '--test',
        action='store_true',
        help='运行完整功能测试'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        default='output/crawler_result.json',
        help='输出文件路径（默认: output/crawler_result.json）'
    )
    
    parser.add_argument(
        '--limit',
        type=int,
        default=5,
        help='批量爬取时的数量限制（默认: 5）'
    )
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    print("🏸 羽毛球装备爬虫启动中...")
    print("=" * 50)
    
    try:
        crawler = BrowserBadmintonCrawler()
        
        if args.test:
            # 运行测试
            run_test(crawler, args.output)
        elif args.url:
            # 爬取指定URL
            crawl_single_url(crawler, args.url, args.output)
        elif args.batch:
            # 批量爬取
            crawl_batch(crawler, args.output, args.limit)
        else:
            # 默认运行单个测试
            run_default_test(crawler, args.output)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        sys.exit(1)
    finally:
        print("\n🎉 程序运行完成！")


def run_test(crawler, output_file):
    """运行完整功能测试"""
    print("🧪 运行完整功能测试...")
    test_url = "https://www.badmintoncn.com/cbo_eq/view.php?eid=22974"
    
    try:
        result = crawler.crawl_equipment_details(test_url)
        
        if result:
            save_result(result, output_file)
            print_result_summary(result)
        else:
            print("❌ 测试失败：未能获取数据")
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")


def crawl_single_url(crawler, url, output_file):
    """爬取单个URL"""
    print(f"🎯 爬取指定装备: {url}")
    
    try:
        result = crawler.crawl_equipment_details(url)
        
        if result:
            save_result(result, output_file)
            print_result_summary(result)
        else:
            print("❌ 爬取失败：未能获取数据")
            
    except Exception as e:
        print(f"❌ 爬取出错: {e}")


def crawl_batch(crawler, output_file, limit):
    """批量爬取装备"""
    print(f"📦 批量爬取装备（限制 {limit} 个）...")
    
    try:
        # 获取装备链接
        print("🔍 获取装备链接...")
        links = crawler.get_equipment_links_from_homepage()
        
        if not links:
            print("❌ 未能获取装备链接")
            return
            
        print(f"✅ 获取到 {len(links)} 个装备链接")
        
        # 限制爬取数量
        links_to_crawl = links[:limit]
        results = []
        
        for i, link in enumerate(links_to_crawl, 1):
            print(f"\n📊 爬取第 {i}/{len(links_to_crawl)} 个装备...")
            print(f"🔗 URL: {link}")
            
            try:
                result = crawler.crawl_equipment_details(link)
                if result:
                    results.append(result)
                    print(f"✅ 成功爬取: {result.get('name', '未知装备')}")
                else:
                    print("❌ 爬取失败")
                    
            except Exception as e:
                print(f"❌ 爬取出错: {e}")
        
        # 保存结果
        if results:
            save_results(results, output_file)
            print(f"\n🎉 批量爬取完成！成功爬取 {len(results)} 个装备")
        else:
            print("\n❌ 批量爬取失败：没有成功爬取任何装备")
            
    except Exception as e:
        print(f"❌ 批量爬取出错: {e}")


def run_default_test(crawler, output_file):
    """运行默认测试"""
    print("🔬 运行默认单装备测试...")
    run_test(crawler, output_file)


def save_result(result, output_file):
    """保存单个结果"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"💾 结果已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存失败: {e}")


def save_results(results, output_file):
    """保存多个结果"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"💾 结果已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存失败: {e}")


def print_result_summary(result):
    """打印结果摘要"""
    print("\n📊 爬取结果摘要:")
    print("-" * 30)
    print(f"装备名称: {result.get('name', '未知')}")
    print(f"品牌: {result.get('brand', '未知')}")
    print(f"系列: {result.get('series', '未知')}")
    print(f"市场价: {result.get('msrp_price', '未知')}")
    print(f"新球拍均价: {result.get('new_average_price', '未知')}")
    print(f"二手均价: {result.get('used_average_price', '未知')}")
    print(f"中羽评分: {result.get('rating', '未知')}")
    print(f"用户数量: {result.get('total_users', '未知')}")


if __name__ == "__main__":
    main() 