// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-create-work-form-wrapper {
    text-align: center;
    padding-top: 40px;
    overflow-y: auto;
    height: 90%;
    position: fixed;
    width: 100%;

    > div:first-child {
        max-width: $grid-unit-size * 100;
    }

    > div > span {
        font-size: 36px;
    }

    .cvat-create-task-content {
        margin-top: 20px;
        width: 100%;
        height: auto;
        border: 1px solid $border-color-1;
        border-radius: $border-radius-base;
        padding: 20px;
        background: $background-color-1;
        text-align: initial;

        > div:not(first-child) {
            margin-top: $grid-unit-size;
        }

        .cvat-project-search-field {
            width: 100%;
        }

        .cvat-task-name-field-has-tooltip {
            margin-bottom: $grid-unit-size;
        }
    }

    .cvat-settings-switch {
        display: table-cell;
    }
}


.cvat-quality-configuration-wrapper {
    .ant-collapse-item > .ant-collapse-header {
        align-items: center;
    }
}
