
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterTransformsPasses();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsAffineLoopFusion();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsAffineLoopFusion();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsAffinePipelineDataTransfer();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsAffinePipelineDataTransfer();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsBufferDeallocation();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsBufferDeallocation();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsBufferHoisting();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsBufferHoisting();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsBufferLoopHoisting();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsBufferLoopHoisting();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsBufferResultsToOutParams();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsBufferResultsToOutParams();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsCSE();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsCSE();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsCanonicalizer();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsCanonicalizer();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsFinalizingBufferize();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsFinalizingBufferize();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsInliner();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsInliner();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsLocationSnapshot();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsLocationSnapshot();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsLoopCoalescing();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsLoopCoalescing();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsLoopInvariantCodeMotion();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsLoopInvariantCodeMotion();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsNormalizeMemRefs();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsNormalizeMemRefs();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsParallelLoopCollapsing();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsParallelLoopCollapsing();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsPrintCFG();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsPrintCFG();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsPrintOpStats();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsPrintOpStats();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsPromoteBuffersToStack();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsPromoteBuffersToStack();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsSCCP();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsSCCP();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsStripDebugInfo();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsStripDebugInfo();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsSymbolDCE();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsSymbolDCE();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsViewOpGraphPass();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsViewOpGraphPass();



#ifdef __cplusplus
}
#endif
