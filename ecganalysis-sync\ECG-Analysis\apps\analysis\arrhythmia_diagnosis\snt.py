import traceback

from apps.utils.logger_helper import Logger


def process(waveform_info):
    """
    心电信号SNT检测
    :param waveform_info: 心电波形信息
    :return: True or False
    """
    try:
        hr = waveform_info['heart_rate']['hr']
        rr_cv = waveform_info['waveform']['rr_cv']

        return rr_cv < 0.03 and hr > 100
    except Exception:
        Logger().error(f'SNT诊断异常：\n{traceback.format_exc()}')
        return False
