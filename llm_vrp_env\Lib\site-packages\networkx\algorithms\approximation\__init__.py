"""Approximations of graph properties and Heuristic methods for optimization.

    .. warning:: These functions are not imported in the top-level of ``networkx``

    These functions can be accessed using
    ``networkx.approximation.function_name``

    They can be imported using ``from networkx.algorithms import approximation``
    or ``from networkx.algorithms.approximation import function_name``

"""
from networkx.algorithms.approximation.clustering_coefficient import *
from networkx.algorithms.approximation.clique import *
from networkx.algorithms.approximation.connectivity import *
from networkx.algorithms.approximation.distance_measures import *
from networkx.algorithms.approximation.dominating_set import *
from networkx.algorithms.approximation.kcomponents import *
from networkx.algorithms.approximation.matching import *
from networkx.algorithms.approximation.ramsey import *
from networkx.algorithms.approximation.steinertree import *
from networkx.algorithms.approximation.traveling_salesman import *
from networkx.algorithms.approximation.treewidth import *
from networkx.algorithms.approximation.vertex_cover import *
from networkx.algorithms.approximation.maxcut import *
