# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/graph.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorboard.compat.proto import function_pb2 as tensorboard_dot_compat_dot_proto_dot_function__pb2
from tensorboard.compat.proto import graph_debug_info_pb2 as tensorboard_dot_compat_dot_proto_dot_graph__debug__info__pb2
from tensorboard.compat.proto import node_def_pb2 as tensorboard_dot_compat_dot_proto_dot_node__def__pb2
from tensorboard.compat.proto import versions_pb2 as tensorboard_dot_compat_dot_proto_dot_versions__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n$tensorboard/compat/proto/graph.proto\x12\x0btensorboard\x1a\'tensorboard/compat/proto/function.proto\x1a/tensorboard/compat/proto/graph_debug_info.proto\x1a\'tensorboard/compat/proto/node_def.proto\x1a\'tensorboard/compat/proto/versions.proto\"\xd1\x01\n\x08GraphDef\x12\"\n\x04node\x18\x01 \x03(\x0b\x32\x14.tensorboard.NodeDef\x12)\n\x08versions\x18\x04 \x01(\x0b\x32\x17.tensorboard.VersionDef\x12\x13\n\x07version\x18\x03 \x01(\x05\x42\x02\x18\x01\x12\x30\n\x07library\x18\x02 \x01(\x0b\x32\x1f.tensorboard.FunctionDefLibrary\x12/\n\ndebug_info\x18\x05 \x01(\x0b\x32\x1b.tensorboard.GraphDebugInfoBz\n\x18org.tensorflow.frameworkB\x0bGraphProtosP\x01ZLgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto\xf8\x01\x01\x62\x06proto3')



_GRAPHDEF = DESCRIPTOR.message_types_by_name['GraphDef']
GraphDef = _reflection.GeneratedProtocolMessageType('GraphDef', (_message.Message,), {
  'DESCRIPTOR' : _GRAPHDEF,
  '__module__' : 'tensorboard.compat.proto.graph_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.GraphDef)
  })
_sym_db.RegisterMessage(GraphDef)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkB\013GraphProtosP\001ZLgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto\370\001\001'
  _GRAPHDEF.fields_by_name['version']._options = None
  _GRAPHDEF.fields_by_name['version']._serialized_options = b'\030\001'
  _GRAPHDEF._serialized_start=226
  _GRAPHDEF._serialized_end=435
# @@protoc_insertion_point(module_scope)
