import requests
from bs4 import BeautifulSoup
import warnings
import urllib.parse

# 忽略SSL警告
warnings.filterwarnings('ignore')

def get_equipment_links_from_homepage():
    """从主页获取装备相关链接"""
    session = requests.Session()
    
    # 基本headers，不包含Accept-Encoding以避免压缩
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0'
    })
    
    try:
        response = session.get("https://www.badmintoncn.com", timeout=10, verify=False)
        if response.status_code != 200:
            return [], f"主页访问失败: {response.status_code}"
        
        response.encoding = response.apparent_encoding or 'utf-8'
        soup = BeautifulSoup(response.text, 'html.parser')
        
        equipment_links = []
        
        # 查找装备相关的链接
        for link in soup.find_all('a', href=True):
            href = link.get('href', '')
            text = link.get_text().strip()
            
            # 检查链接是否与装备相关
            if any(keyword in href.lower() for keyword in ['eq', 'equipment', '装备']) or \
               any(keyword in text for keyword in ['装备', '器材', 'equipment', '装备库', '热门', '开箱']):
                
                # 转换相对链接为绝对链接
                if href.startswith('/'):
                    full_url = "https://www.badmintoncn.com" + href
                elif href.startswith('http'):
                    full_url = href
                else:
                    continue
                    
                equipment_links.append({
                    'url': full_url,
                    'text': text,
                    'href': href
                })
        
        return equipment_links, None
        
    except Exception as e:
        return [], f"获取主页链接时出错: {e}"

def test_equipment_link(session, link_info):
    """测试单个装备链接"""
    url = link_info['url']
    text = link_info['text']
    
    try:
        # 带Referer访问
        headers = {'Referer': 'https://www.badmintoncn.com/'}
        response = session.get(url, headers=headers, timeout=10, verify=False)
        
        status = response.status_code
        length = len(response.text)
        
        # 如果成功，检查内容
        content_info = ""
        if status == 200:
            response.encoding = response.apparent_encoding or 'gb2312'
            if '装备' in response.text or '球拍' in response.text or 'view.php' in response.text:
                content_info = " ✅ 包含装备信息"
            elif '验证' in response.text or 'verification' in response.text.lower():
                content_info = " ⚠️ 需要验证"
            else:
                content_info = " ❓ 内容待确认"
        
        return f"   {status:3d} | {length:6d} | {text:15s} | {url}{content_info}"
        
    except Exception as e:
        return f"   ERR | 0      | {text:15s} | {url} - {e}"

def main():
    print("🔍 测试主页发现的装备链接...")
    
    # 1. 获取主页的装备链接
    equipment_links, error = get_equipment_links_from_homepage()
    
    if error:
        print(f"❌ {error}")
        return
    
    print(f"📋 从主页找到 {len(equipment_links)} 个装备相关链接")
    print("\n" + "="*80)
    print("状态 | 长度   | 链接文本        | URL")
    print("="*80)
    
    # 2. 创建session测试每个链接
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    successful_links = []
    
    for link_info in equipment_links:
        result = test_equipment_link(session, link_info)
        print(result)
        
        # 收集成功的链接
        if " 200 |" in result and "装备信息" in result:
            successful_links.append(link_info)
    
    print("="*80)
    print(f"\n✅ 测试完成！找到 {len(successful_links)} 个可访问的装备页面")
    
    if successful_links:
        print("\n🎯 可用的装备页面链接:")
        for link in successful_links:
            print(f"   - {link['text']}: {link['url']}")

if __name__ == "__main__":
    main() 