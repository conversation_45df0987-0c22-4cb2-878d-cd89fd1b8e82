import numpy as np
from scipy import signal
from scipy.signal import hilbert
import pywt
from apps.analysis.common.noise_model.noise_recognition import is_noise
from apps.utils.logger_helper import Logger

def perform_cwt(ecg_signal, sampling_rate, f_min=1, f_max=20, scales=50):
    """
    对ECG信号执行连续小波变换
    """
    try:
        if ecg_signal is None or len(ecg_signal) == 0:
            return np.zeros((scales, 1)), np.linspace(f_min, f_max, scales)
            
        if f_min < 1:
            f_min = 1
        
        frequencies = np.linspace(f_min, f_max, scales)
        scales_array = pywt.scale2frequency('morl', frequencies) / sampling_rate
        
        min_scale = 0.1
        scales_array = np.maximum(scales_array, min_scale)
        
        if len(ecg_signal) > 10000:
            if f_max < 50:  
                downsample_factor = max(1, int(sampling_rate / (2 * f_max * 2)))
                if downsample_factor > 1:
                    b, a = signal.butter(3, f_max*2/sampling_rate)
                    filtered = signal.filtfilt(b, a, ecg_signal)
                    downsampled = filtered[::downsample_factor]
                    effective_fs = sampling_rate / downsample_factor
                    cwt_matrix, _ = pywt.cwt(downsampled, scales_array, 'morl', sampling_period=1.0/effective_fs)
                    if f_min <= 5:
                        result = (cwt_matrix, frequencies)
                        return result
                    else:
                        cwt_matrix_full = np.zeros((scales, len(ecg_signal)))
                        for i in range(scales):
                            cwt_matrix_full[i] = np.interp(
                                np.arange(len(ecg_signal)),
                                np.arange(len(downsampled)) * downsample_factor,
                                cwt_matrix[i]
                            )
                        result = (cwt_matrix_full, frequencies)
                        return result
            
            segment_size = 8192  
            overlap = segment_size // 4
            
            cwt_matrix = np.zeros((scales, len(ecg_signal)))
            
            for i in range(0, len(ecg_signal), segment_size - overlap):
                end = min(i + segment_size, len(ecg_signal))
                segment = ecg_signal[i:end]
                
                segment_cwt, _ = pywt.cwt(segment, scales_array, 'morl', sampling_period=1.0/sampling_rate)
                
                if i == 0:
                    cwt_matrix[:, i:end] = segment_cwt
                else:
                    overlap_start = i
                    overlap_end = min(i + overlap, len(ecg_signal))
                    
                    weights = np.linspace(0, 1, overlap_end - overlap_start)
                    
                    for s in range(scales):
                        cwt_matrix[s, overlap_start:overlap_end] = (
                            (1 - weights) * cwt_matrix[s, overlap_start:overlap_end] + 
                            weights * segment_cwt[s, :overlap_end - overlap_start]
                        )
                    
                    if overlap_end < end:
                        cwt_matrix[:, overlap_end:end] = segment_cwt[:, overlap_end - i:]
        else:
            cwt_matrix, _ = pywt.cwt(ecg_signal, scales_array, 'morl', sampling_period=1.0/sampling_rate)
            
        result = (cwt_matrix, frequencies)
        
        return result
    except Exception as e:
        Logger().error(f"CWT变换错误: {str(e)}")
        return np.zeros((scales, len(ecg_signal))), np.linspace(f_min, f_max, scales)

def phasor_transform(ecg_signal, sampling_rate):
    """
    相量变换(Phasor Transform)用于增强P波检测
    """
    try:
        if len(ecg_signal) == 0:
            return np.array([]), np.array([]), np.array([])
            
        nyq = 0.5 * sampling_rate
        low = 1.0 / nyq
        high = 15.0 / nyq
        b, a = signal.butter(3, [low, high], 'bandpass')
        
        filtered = signal.filtfilt(b, a, ecg_signal)
        
        if len(filtered) > 10000:  
            segment_size = 4096  
            
            analytic_signal = np.zeros(len(filtered), dtype=complex)
            
            overlap = segment_size // 4
            for i in range(0, len(filtered), segment_size - overlap):
                end = min(i + segment_size, len(filtered))
                segment = filtered[i:end]

                segment_analytic = hilbert(segment)
                
                if i == 0:
                    analytic_signal[i:end] = segment_analytic
                else:
                    overlap_start = i
                    overlap_end = min(i + overlap, len(filtered))
                    
                    weights = np.linspace(0, 1, overlap_end - overlap_start)
                    
                    analytic_signal[overlap_start:overlap_end] = (
                        (1 - weights) * analytic_signal[overlap_start:overlap_end] + 
                        weights * segment_analytic[:overlap_end - overlap_start]
                    )
                    
                    if overlap_end < end:
                        analytic_signal[overlap_end:end] = segment_analytic[overlap_end - i:]
        else:
            analytic_signal = hilbert(filtered)
        
        envelope = np.abs(analytic_signal)
        
        phase = np.angle(analytic_signal)
        
        unwrapped_phase = np.unwrap(phase)
        instant_freq = np.zeros_like(unwrapped_phase)
        instant_freq[1:-1] = (unwrapped_phase[2:] - unwrapped_phase[:-2]) / 2.0
        instant_freq[0] = unwrapped_phase[1] - unwrapped_phase[0]
        instant_freq[-1] = unwrapped_phase[-1] - unwrapped_phase[-2]
        instant_freq = instant_freq * sampling_rate / (2.0 * np.pi)
        
        return envelope, phase, instant_freq
    except Exception as e:
        Logger().error(f"相量变换错误: {str(e)}")
        return np.zeros_like(ecg_signal), np.zeros_like(ecg_signal), np.zeros_like(ecg_signal)

def detect_p_waves_cwt(ecg_signal, r_peaks, sampling_rate):
    """
    使用CWT检测P波 - 房颤特定优化版本
    """
    try:
        if ecg_signal is None or len(ecg_signal) == 0 or len(r_peaks) == 0:
            return {
                'P_positions': [],
                'P_start_positions': [],
                'P_end_positions': []
            }
        
        cwt_matrix, frequencies = perform_cwt(ecg_signal, sampling_rate, f_min=5, f_max=15)
        
        idx_8hz = np.argmin(np.abs(frequencies - 8))
        p_wave_coefs = cwt_matrix[idx_8hz]
        
        max_p_count = len(r_peaks)
        p_positions = np.zeros(max_p_count, dtype=int)
        p_start_positions = np.zeros(max_p_count, dtype=int)
        p_end_positions = np.zeros(max_p_count, dtype=int)
        valid_count = 0
        
        search_window = int(0.2 * sampling_rate)  
        min_pr_interval = int(0.05 * sampling_rate)  
        
        valid_r_peaks = [r for r in r_peaks if r > search_window]
        
        for r_pos in valid_r_peaks:
            start_idx = r_pos - search_window
            end_idx = r_pos - min_pr_interval
                
            if start_idx < 0 or end_idx <= start_idx or end_idx >= len(p_wave_coefs):
                continue
                
            window_coefs = p_wave_coefs[start_idx:end_idx]
            if len(window_coefs) > 0:
                p_peak_idx = start_idx + np.argmax(window_coefs)
                
                p_amp = p_wave_coefs[p_peak_idx]
                threshold = 0.3 * p_amp
                
                p_start = p_peak_idx
                while p_start > start_idx and p_wave_coefs[p_start] > threshold:
                    p_start -= 1
                
                p_end = p_peak_idx
                while p_end < end_idx and p_wave_coefs[p_end] > threshold:
                    p_end += 1
                
                if valid_count < max_p_count:
                    p_positions[valid_count] = p_peak_idx
                    p_start_positions[valid_count] = p_start
                    p_end_positions[valid_count] = p_end
                    valid_count += 1
        
        return {
            'P_positions': p_positions[:valid_count].tolist(),
            'P_start_positions': p_start_positions[:valid_count].tolist(),
            'P_end_positions': p_end_positions[:valid_count].tolist()
        }
    except Exception as e:
        Logger().error(f"P波检测错误: {str(e)}")
        empty_positions = {
            'P_positions': [],
            'P_start_positions': [],
            'P_end_positions': []
        }
        return empty_positions

def detect_f_waves(ecg_signal, r_peaks, sampling_rate):
    """
    检测F波(房颤时的快速不规则心房波)
    """
    try:
        if ecg_signal is None or len(ecg_signal) == 0 or len(r_peaks) < 2:
            return {
                'f_wave_freq': 0,
                'f_wave_power': 0,
                'f_wave_regularity': 0,
                'f_wave_score': 0
            }
            
        cwt_matrix, frequencies = perform_cwt(ecg_signal, sampling_rate, f_min=4, f_max=9)
        
        max_features = len(r_peaks) - 1
        mean_powers = np.zeros(max_features)
        std_powers = np.zeros(max_features)
        dominant_freqs = np.zeros(max_features)
        regularities = np.zeros(max_features)
        valid_count = 0
        
        min_segment_len = int(0.1 * sampling_rate)  
        
        for i in range(len(r_peaks)-1):
            start_idx = r_peaks[i] + int(0.05 * sampling_rate)
            end_idx = r_peaks[i+1] - int(0.05 * sampling_rate)
            
            if end_idx - start_idx >= min_segment_len:
                segment_cwt = cwt_matrix[:, start_idx:end_idx]
                
                mean_power = np.mean(np.abs(segment_cwt))
                std_power = np.std(np.abs(segment_cwt))
                
                avg_spectrum = np.mean(np.abs(segment_cwt), axis=1)
                dominant_freq_idx = np.argmax(avg_spectrum)
                dominant_freq = frequencies[dominant_freq_idx]
                
                regularity = mean_power / (std_power + 1e-6)
                
                if valid_count < max_features:
                    mean_powers[valid_count] = mean_power
                    std_powers[valid_count] = std_power
                    dominant_freqs[valid_count] = dominant_freq
                    regularities[valid_count] = regularity
                    valid_count += 1
        
        if valid_count > 0:
            mean_powers = mean_powers[:valid_count]
            std_powers = std_powers[:valid_count]
            dominant_freqs = dominant_freqs[:valid_count]
            regularities = regularities[:valid_count]
            
            mean_power = np.mean(mean_powers)
            mean_freq = np.mean(dominant_freqs)
            regularity = np.mean(regularities)
            power_std = np.std(mean_powers)
            
            f_wave_score = (mean_power * (power_std / (mean_power + 1e-6))) / regularity
            
            return {
                'f_wave_freq': mean_freq,
                'f_wave_power': mean_power,
                'f_wave_regularity': regularity,
                'f_wave_score': f_wave_score
            }
        else:
            return {
                'f_wave_freq': 0,
                'f_wave_power': 0,
                'f_wave_regularity': 0,
                'f_wave_score': 0
            }
    except Exception as e:
        Logger().error(f"F波检测错误: {str(e)}")
        return {
            'f_wave_freq': 0,
            'f_wave_power': 0,
            'f_wave_regularity': 0,
            'f_wave_score': 0
        }

def detect_p_waves_phasor(ecg_signal, r_peaks, sampling_rate):
    """
    使用相量变换检测P波，特别适合I导联中低振幅P波
    """
    try:
        if ecg_signal is None or len(ecg_signal) == 0 or len(r_peaks) == 0:
            return {
                'P_positions': [],
                'P_start_positions': [],
                'P_end_positions': []
            }
            
        envelope, phase, instant_freq = phasor_transform(ecg_signal, sampling_rate)
        
        pr_min = int(0.12 * sampling_rate)
        pr_max = int(0.22 * sampling_rate)
        p_duration = int(0.10 * sampling_rate)  
        
        max_p_count = len(r_peaks) 
        p_positions = np.zeros(max_p_count, dtype=int)
        p_start_positions = np.zeros(max_p_count, dtype=int)
        p_end_positions = np.zeros(max_p_count, dtype=int)
        valid_count = 0
            
        valid_r_peaks = r_peaks[r_peaks >= pr_max]
        
        for r_pos in valid_r_peaks:
            search_start = r_pos - pr_max
            search_end = r_pos - pr_min
            
            window_envelope = envelope[search_start:search_end]
            
            if len(window_envelope) > 0:
                p_peak_rel_idx = np.argmax(window_envelope)
                p_peak_idx = search_start + p_peak_rel_idx
                
                freq_start = max(0, p_peak_idx-5)
                freq_end = min(len(instant_freq), p_peak_idx+6)
                mean_freq = np.mean(instant_freq[freq_start:freq_end])
                
                if 1 <= mean_freq <= 10:
                    segment_start = max(0, p_peak_idx - p_duration)
                    segment_end = min(len(envelope), p_peak_idx + p_duration + 1)
                    p_env_segment = envelope[segment_start:segment_end]
                    
                    p_env_grad = np.gradient(p_env_segment)
                    
                    left_idx = p_duration
                    for i in range(p_duration, 0, -1):
                        if i < len(p_env_grad) and p_env_grad[i] >= 0 and p_env_grad[i-1] < 0:
                            left_idx = i
                            break
                    
                    right_idx = p_duration
                    for i in range(p_duration, len(p_env_grad)-1):
                        if p_env_grad[i] <= 0 and p_env_grad[i+1] > 0:
                            right_idx = i
                            break
                    
                    p_start = max(0, segment_start + left_idx)
                    p_end = min(len(ecg_signal)-1, segment_start + right_idx)
                    
                    if valid_count < max_p_count:
                        p_positions[valid_count] = p_peak_idx
                        p_start_positions[valid_count] = p_start
                        p_end_positions[valid_count] = p_end
                        valid_count += 1
        
        return {
            'P_positions': p_positions[:valid_count].tolist(),
            'P_start_positions': p_start_positions[:valid_count].tolist(),
            'P_end_positions': p_end_positions[:valid_count].tolist()
        }
    except Exception as e:
        Logger().error(f"相量变换P波检测错误: {str(e)}")
        return {
            'P_positions': [],
            'P_start_positions': [],
            'P_end_positions': []
        }

def tq_segment_analysis(ecg_signal, r_peaks, t_peaks, p_starts, sampling_rate):
    """
    分析TQ段(T波结束到下一个P波开始)，寻找F波特征
    特别适合I导联中F波检测
    """
    try:
        if (ecg_signal is None or len(ecg_signal) == 0 or 
            len(t_peaks) == 0 or len(p_starts) < 2):
            return {
                'f_wave_freq': 0,
                'f_wave_power': 0,
                'f_wave_regularity': 1.0,
                'f_wave_score': 0
            }
            
        min_segment_len = int(sampling_rate / 10) 
        t_offset = int(0.05 * sampling_rate)      
        p_offset = int(0.02 * sampling_rate)      
        
        max_segments = min(len(t_peaks), len(p_starts) - 1)
        f_wave_features = []
        
        f_min, f_max = 4, 9
        
        for i in range(min(len(t_peaks), len(p_starts) - 1)):
            t_pos = t_peaks[i]
            next_p_start = p_starts[i+1]
            
            if next_p_start > t_pos + t_offset:
                tq_start = t_pos + t_offset
                tq_end = next_p_start - p_offset
                
                if tq_end > tq_start and (tq_end - tq_start) >= min_segment_len:
                    tq_segment = ecg_signal[tq_start:tq_end]
                    
                    yf = np.abs(np.fft.rfft(tq_segment))
                    xf = np.fft.rfftfreq(len(tq_segment), 1/sampling_rate)
                    
                    f_mask = (xf >= f_min) & (xf <= f_max)
                    
                    if np.any(f_mask):
                        total_power = np.sum(yf)
                        f_wave_power = np.sum(yf[f_mask])
                        f_ratio = f_wave_power / (total_power + 1e-10)
                        
                        f_peak_idx = np.argmax(yf[f_mask])
                        f_peak_freq = xf[f_mask][f_peak_idx]
                        
                        f_regularity = np.max(yf[f_mask]) / (f_wave_power + 1e-10)
                        
                        f_wave_features.append({
                            'power': f_ratio,
                            'frequency': f_peak_freq,
                            'regularity': f_regularity
                        })
        
        if f_wave_features:
            powers = np.array([f['power'] for f in f_wave_features])
            freqs = np.array([f['frequency'] for f in f_wave_features])
            regularities = np.array([f['regularity'] for f in f_wave_features])
            
            avg_power = np.mean(powers)
            avg_freq = np.mean(freqs)
            avg_regularity = np.mean(regularities)
            
            freq_factor = 1.5 if (5 <= avg_freq <= 7) else 1.0
            
            f_wave_score = 100 * avg_power * freq_factor / (avg_regularity + 0.1)
            
            return {
                'f_wave_freq': avg_freq,
                'f_wave_power': avg_power,
                'f_wave_regularity': avg_regularity,
                'f_wave_score': f_wave_score
            }
        else:
            return {
                'f_wave_freq': 0,
                'f_wave_power': 0,
                'f_wave_regularity': 1.0,
                'f_wave_score': 0
            }
    except Exception as e:
        Logger().error(f"TQ段分析错误: {str(e)}")
        return {
            'f_wave_freq': 0,
            'f_wave_power': 0,
            'f_wave_regularity': 1.0,
            'f_wave_score': 0
        } 