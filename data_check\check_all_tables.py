from sql_helper import <PERSON>sql<PERSON>el<PERSON>

def check_all_tables():
    db = MysqlHelper("default")
    try:
        conn = db.get_con()
        cursor = conn.cursor()
        
        # 获取所有表名
        print("\n=== db_ecg 数据库中的所有表 ===")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        # 分类显示表
        patient_tables = []
        ecg_tables = []
        other_tables = []
        
        for table in tables:
            table_name = table[0]
            if 'patient' in table_name.lower():
                patient_tables.append(table_name)
            elif 'ecg' in table_name.lower():
                ecg_tables.append(table_name)
            else:
                other_tables.append(table_name)
        
        print("\n患者相关表：")
        for table in patient_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"- {table} (记录数: {count})")
            # 显示表结构
            cursor.execute(f"SHOW COLUMNS FROM {table}")
            columns = cursor.fetchall()
            print("  字段列表:")
            for col in columns:
                print(f"  - {col[0]} ({col[1]})")
        
        print("\nECG相关表：")
        for table in ecg_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"- {table} (记录数: {count})")
            # 显示表结构
            cursor.execute(f"SHOW COLUMNS FROM {table}")
            columns = cursor.fetchall()
            print("  字段列表:")
            for col in columns:
                print(f"  - {col[0]} ({col[1]})")
        
        print("\n其他表：")
        for table in other_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"- {table} (记录数: {count})")
        
        # 检查表之间可能的关联
        print("\n=== 检查可能的表关联 ===")
        for ecg_table in ecg_tables:
            cursor.execute(f"SHOW COLUMNS FROM {ecg_table}")
            ecg_columns = cursor.fetchall()
            ecg_fields = [col[0] for col in ecg_columns]
            
            for patient_table in patient_tables:
                cursor.execute(f"SHOW COLUMNS FROM {patient_table}")
                patient_columns = cursor.fetchall()
                patient_fields = [col[0] for col in patient_columns]
                
                # 查找可能的关联字段
                common_fields = set(ecg_fields) & set(patient_fields)
                if common_fields:
                    print(f"\n{ecg_table} 和 {patient_table} 的可能关联字段:")
                    for field in common_fields:
                        print(f"- {field}")
                        # 检查一些示例值
                        cursor.execute(f"SELECT DISTINCT {field} FROM {ecg_table} LIMIT 5")
                        ecg_values = cursor.fetchall()
                        cursor.execute(f"SELECT DISTINCT {field} FROM {patient_table} LIMIT 5")
                        patient_values = cursor.fetchall()
                        print(f"  ECG表中的值: {[v[0] for v in ecg_values]}")
                        print(f"  患者表中的值: {[v[0] for v in patient_values]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"查询出错：{str(e)}")

if __name__ == "__main__":
    check_all_tables() 