# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Resource management library.
"""

from __future__ import print_function as _print_function

import sys as _sys

from tensorflow.python.platform.resource_loader import get_data_files_path
from tensorflow.python.platform.resource_loader import get_path_to_datafile
from tensorflow.python.platform.resource_loader import get_root_dir_with_all_resources
from tensorflow.python.platform.resource_loader import load_resource
from tensorflow.python.platform.resource_loader import readahead_file_path

del _print_function
