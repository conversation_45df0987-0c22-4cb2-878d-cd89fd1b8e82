/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// Async Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterAsyncPasses() {
  registerAsyncPasses();
}

MlirPass mlirCreateAsyncAsyncParallelFor() {
  return wrap(mlir::createAsyncParallelForPass().release());
}
void mlirRegisterAsyncAsyncParallelFor() {
  registerAsyncParallelForPass();
}


MlirPass mlirCreateAsyncAsyncRuntimeRefCounting() {
  return wrap(mlir::createAsyncRuntimeRefCountingPass().release());
}
void mlirRegisterAsyncAsyncRuntimeRefCounting() {
  registerAsyncRuntimeRefCountingPass();
}


MlirPass mlirCreateAsyncAsyncRuntimeRefCountingOpt() {
  return wrap(mlir::createAsyncRuntimeRefCountingOptPass().release());
}
void mlirRegisterAsyncAsyncRuntimeRefCountingOpt() {
  registerAsyncRuntimeRefCountingOptPass();
}


MlirPass mlirCreateAsyncAsyncToAsyncRuntime() {
  return wrap(mlir::createAsyncToAsyncRuntimePass().release());
}
void mlirRegisterAsyncAsyncToAsyncRuntime() {
  registerAsyncToAsyncRuntimePass();
}

