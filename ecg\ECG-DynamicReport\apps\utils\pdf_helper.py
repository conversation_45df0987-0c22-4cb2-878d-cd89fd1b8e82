import os
import uuid
from datetime import datetime
import pdfkit
from django.http import HttpResponse
from django.template.loader import render_to_string


def generate_pdf(report_model, report_template):
    """
    生成PDF报告
    :param report_model: 报表数据
    """
    # 获取当前时间戳
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    # 生成一个随机的 UUID（确保唯一性）
    unique_id = str(uuid.uuid4())[:8]  # 取 UUID 的前 8 位作为简短标识
    # 组合成文件名
    filename = f"{timestamp}{unique_id}"

    context = {'report': report_model}
    html_string = render_to_string(report_template, context)

    # 配置PDF生成选项
    # 自动判断操作系统
    if os.name == 'nt':
        # Windows环境路径
        wkhtmltopdf_path = r'E:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe'
    else:
        # Linux环境路径
        wkhtmltopdf_path = '/usr/local/bin/wkhtmltopdf'

    config = pdfkit.configuration(wkhtmltopdf=wkhtmltopdf_path)

    options = {
        'encoding': 'UTF-8',
        'page-size': 'A4',
        'margin-top': '5mm',
        'margin-right': '5mm',
        'margin-bottom': '5mm',
        'margin-left': '5mm',
    }

    # 4. 生成PDF（关键步骤）
    pdf_data = pdfkit.from_string(
        input=html_string,
        output_path=False,  # 直接获取二进制数据
        configuration=config,
        options=options
    )

    response = HttpResponse(pdf_data, content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{filename}.pdf"'

    return response