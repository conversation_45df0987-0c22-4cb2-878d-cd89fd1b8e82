"""
基于WESAD数据集标签的HRV压力疲劳分级系统
结合国际HRV标准和WESAD情绪状态标签进行科学分级
"""

import numpy as np
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from apps.utils.logger_helper import Logger
import traceback


class WESADHRVClassifier:
    """
    基于WESAD数据集的HRV分级器
    
    WESAD标签映射:
    - baseline: 基线状态 -> 正常状态
    - stress: 压力状态 -> 高压力风险
    - amusement: 娱乐状态 -> 高活力/正面情绪
    - meditation: 冥想状态 -> 低压力/高放松
    """
    
    def __init__(self):
        self.logger = Logger()
        
        # 基于国际HRV标准的阈值 (参考Task Force 1996, Shaffer & Ginsberg 2017)
        self.hrv_thresholds = {
            # 时域指标阈值 (ms)
            'sdnn': {
                'low': 20,      # < 20ms: 低HRV，高压力风险
                'normal': 50,   # 20-50ms: 正常
                'high': 100     # > 50ms: 高HRV，良好状态
            },
            'rmssd': {
                'low': 15,      # < 15ms: 低副交感活性
                'normal': 42,   # 15-42ms: 正常
                'high': 80      # > 42ms: 高副交感活性
            },
            'pnn50': {
                'low': 3,       # < 3%: 低副交感活性
                'normal': 15,   # 3-15%: 正常
                'high': 30      # > 15%: 高副交感活性
            },
            # 频域指标阈值
            'lf_hf_ratio': {
                'low': 0.5,     # < 0.5: 副交感占优
                'normal': 2.0,  # 0.5-2.0: 平衡
                'high': 4.0     # > 2.0: 交感占优，压力状态
            },
            # 心率阈值 (bpm)
            'heart_rate': {
                'low': 60,      # < 60: 心动过缓
                'normal': 100,  # 60-100: 正常
                'high': 120     # > 100: 心动过速
            }
        }
        
        # WESAD状态映射权重
        self.wesad_weights = {
            'stress_indicators': {
                'sdnn': -0.3,       # SDNN降低表示压力增加
                'rmssd': -0.25,     # RMSSD降低表示副交感活性下降
                'pnn50': -0.2,      # pNN50降低表示压力增加
                'lf_hf_ratio': 0.25 # LF/HF升高表示交感激活
            },
            'vitality_indicators': {
                'sdnn': 0.25,       # 适度的HRV表示活力
                'rmssd': 0.3,       # 良好的副交感功能
                'total_power': 0.2, # 总功率反映整体活力
                'heart_rate': -0.25 # 适度心率
            },
            'fatigue_indicators': {
                'sdnn': -0.2,       # 极低或极高SDNN可能表示疲劳
                'lf_hf_ratio': 0.15, # 交感过度激活
                'heart_rate': 0.3,   # 心率异常
                'rmssd': -0.25      # 副交感功能下降
            }
        }

    def calculate_hrv_features(self, waveform_info):
        """
        从波形信息中提取HRV特征
        """
        try:
            hrv_linear = waveform_info['hrv']['linear']
            hrv_nonlinear = waveform_info['hrv']['nonlinear']
            heart_rate_info = waveform_info['heart_rate']
            
            # 获取频域特征 (如果存在)
            frequency_features = {}
            if 'frequency' in waveform_info['hrv']:
                frequency_features = waveform_info['hrv']['frequency']
            else:
                # 如果没有频域特征，使用现有的计算方法
                from apps.analysis.health_metrics.rf_model_index import get_frequency_domain_features
                nn_intervals = waveform_info['waveform']['nn_intervals']
                if len(nn_intervals) > 10:  # 确保有足够的数据
                    frequency_features = get_frequency_domain_features(nn_intervals)
            
            features = {
                # 时域特征
                'sdnn': hrv_linear.get('sdnn', 0),
                'rmssd': hrv_linear.get('rmssd', 0),
                'pnn50': hrv_linear.get('pnn50', 0),
                'pnn20': hrv_linear.get('pnn20', 0),
                'sdsd': hrv_linear.get('sdsd', 0),
                
                # 非线性特征
                'sd1': hrv_nonlinear.get('sd1', 0),
                'sd2': hrv_nonlinear.get('sd2', 0),
                'sd_ratio': hrv_nonlinear.get('sd_ratio', 0),
                
                # 频域特征
                'lf': frequency_features.get('lf', 0),
                'hf': frequency_features.get('hf', 0),
                'lf_hf_ratio': frequency_features.get('lf_hf_ratio', 0),
                'total_power': frequency_features.get('total_power', 0),
                'lfnu': frequency_features.get('lfnu', 0),
                'hfnu': frequency_features.get('hfnu', 0),
                
                # 心率特征
                'mean_hr': heart_rate_info.get('hr', 0),
                'max_hr': heart_rate_info.get('max_hr', 0),
                'min_hr': heart_rate_info.get('min_hr', 0)
            }
            
            return features
            
        except Exception as e:
            self.logger.error(f"计算HRV特征时出错: {traceback.format_exc()}")
            return {}

    def classify_stress_level(self, features):
        """
        基于WESAD压力标签映射的压力等级分类
        
        返回:
        - stress_level: 'low', 'medium', 'high'
        - stress_score: 0-100的压力分数
        - confidence: 分类置信度
        """
        try:
            stress_score = 0
            weight_sum = 0
            
            # 基于SDNN的压力评估
            sdnn = features.get('sdnn', 0)
            if sdnn > 0:
                if sdnn < self.hrv_thresholds['sdnn']['low']:
                    stress_score += 80 * abs(self.wesad_weights['stress_indicators']['sdnn'])
                elif sdnn < self.hrv_thresholds['sdnn']['normal']:
                    stress_score += 50 * abs(self.wesad_weights['stress_indicators']['sdnn'])
                else:
                    stress_score += 20 * abs(self.wesad_weights['stress_indicators']['sdnn'])
                weight_sum += abs(self.wesad_weights['stress_indicators']['sdnn'])
            
            # 基于RMSSD的压力评估
            rmssd = features.get('rmssd', 0)
            if rmssd > 0:
                if rmssd < self.hrv_thresholds['rmssd']['low']:
                    stress_score += 80 * abs(self.wesad_weights['stress_indicators']['rmssd'])
                elif rmssd < self.hrv_thresholds['rmssd']['normal']:
                    stress_score += 50 * abs(self.wesad_weights['stress_indicators']['rmssd'])
                else:
                    stress_score += 20 * abs(self.wesad_weights['stress_indicators']['rmssd'])
                weight_sum += abs(self.wesad_weights['stress_indicators']['rmssd'])
            
            # 基于pNN50的压力评估
            pnn50 = features.get('pnn50', 0)
            if pnn50 >= 0:
                if pnn50 < self.hrv_thresholds['pnn50']['low']:
                    stress_score += 80 * abs(self.wesad_weights['stress_indicators']['pnn50'])
                elif pnn50 < self.hrv_thresholds['pnn50']['normal']:
                    stress_score += 50 * abs(self.wesad_weights['stress_indicators']['pnn50'])
                else:
                    stress_score += 20 * abs(self.wesad_weights['stress_indicators']['pnn50'])
                weight_sum += abs(self.wesad_weights['stress_indicators']['pnn50'])
            
            # 基于LF/HF比值的压力评估
            lf_hf_ratio = features.get('lf_hf_ratio', 0)
            if lf_hf_ratio > 0:
                if lf_hf_ratio > self.hrv_thresholds['lf_hf_ratio']['high']:
                    stress_score += 80 * self.wesad_weights['stress_indicators']['lf_hf_ratio']
                elif lf_hf_ratio > self.hrv_thresholds['lf_hf_ratio']['normal']:
                    stress_score += 60 * self.wesad_weights['stress_indicators']['lf_hf_ratio']
                else:
                    stress_score += 30 * self.wesad_weights['stress_indicators']['lf_hf_ratio']
                weight_sum += abs(self.wesad_weights['stress_indicators']['lf_hf_ratio'])
            
            # 标准化分数
            if weight_sum > 0:
                stress_score = stress_score / weight_sum
            else:
                stress_score = 50  # 默认中等压力
            
            # 确保分数在0-100范围内
            stress_score = max(0, min(100, stress_score))
            
            # 分类压力等级
            if stress_score < 30:
                stress_level = 'low'
            elif stress_score < 70:
                stress_level = 'medium'
            else:
                stress_level = 'high'
            
            # 计算置信度
            confidence = min(1.0, weight_sum / 1.0)  # 基于使用的特征权重
            
            return stress_level, round(stress_score, 1), round(confidence, 2)
            
        except Exception as e:
            self.logger.error(f"压力分类时出错: {traceback.format_exc()}")
            return 'medium', 50.0, 0.5

    def classify_fatigue_level(self, features):
        """
        基于HRV特征的疲劳等级分类
        
        返回:
        - fatigue_level: 'low', 'medium', 'high'
        - fatigue_score: 0-100的疲劳分数
        - confidence: 分类置信度
        """
        try:
            fatigue_score = 0
            weight_sum = 0
            
            # 疲劳通常表现为HRV的异常模式
            sdnn = features.get('sdnn', 0)
            mean_hr = features.get('mean_hr', 0)
            lf_hf_ratio = features.get('lf_hf_ratio', 0)
            rmssd = features.get('rmssd', 0)
            
            # SDNN异常（过低或过高都可能表示疲劳）
            if sdnn > 0:
                if sdnn < 15 or sdnn > 150:  # 极端值表示疲劳
                    fatigue_score += 80 * abs(self.wesad_weights['fatigue_indicators']['sdnn'])
                elif sdnn < 25 or sdnn > 100:
                    fatigue_score += 60 * abs(self.wesad_weights['fatigue_indicators']['sdnn'])
                else:
                    fatigue_score += 20 * abs(self.wesad_weights['fatigue_indicators']['sdnn'])
                weight_sum += abs(self.wesad_weights['fatigue_indicators']['sdnn'])
            
            # 心率异常
            if mean_hr > 0:
                if mean_hr < 50 or mean_hr > 110:  # 异常心率
                    fatigue_score += 70 * self.wesad_weights['fatigue_indicators']['heart_rate']
                elif mean_hr < 55 or mean_hr > 95:
                    fatigue_score += 50 * self.wesad_weights['fatigue_indicators']['heart_rate']
                else:
                    fatigue_score += 20 * self.wesad_weights['fatigue_indicators']['heart_rate']
                weight_sum += abs(self.wesad_weights['fatigue_indicators']['heart_rate'])
            
            # LF/HF比值异常
            if lf_hf_ratio > 0:
                if lf_hf_ratio > 3.0:  # 交感过度激活
                    fatigue_score += 70 * self.wesad_weights['fatigue_indicators']['lf_hf_ratio']
                elif lf_hf_ratio > 2.5:
                    fatigue_score += 50 * self.wesad_weights['fatigue_indicators']['lf_hf_ratio']
                else:
                    fatigue_score += 20 * self.wesad_weights['fatigue_indicators']['lf_hf_ratio']
                weight_sum += abs(self.wesad_weights['fatigue_indicators']['lf_hf_ratio'])
            
            # RMSSD降低
            if rmssd > 0:
                if rmssd < 10:
                    fatigue_score += 80 * abs(self.wesad_weights['fatigue_indicators']['rmssd'])
                elif rmssd < 20:
                    fatigue_score += 60 * abs(self.wesad_weights['fatigue_indicators']['rmssd'])
                else:
                    fatigue_score += 30 * abs(self.wesad_weights['fatigue_indicators']['rmssd'])
                weight_sum += abs(self.wesad_weights['fatigue_indicators']['rmssd'])
            
            # 标准化分数
            if weight_sum > 0:
                fatigue_score = fatigue_score / weight_sum
            else:
                fatigue_score = 50
            
            fatigue_score = max(0, min(100, fatigue_score))
            
            # 分类疲劳等级
            if fatigue_score < 30:
                fatigue_level = 'low'
            elif fatigue_score < 70:
                fatigue_level = 'medium'
            else:
                fatigue_level = 'high'
            
            confidence = min(1.0, weight_sum / 1.0)
            
            return fatigue_level, round(fatigue_score, 1), round(confidence, 2)

        except Exception as e:
            self.logger.error(f"疲劳分类时出错: {traceback.format_exc()}")
            return 'medium', 50.0, 0.5

    def classify_vitality_level(self, features):
        """
        基于WESAD娱乐状态映射的活力等级分类

        返回:
        - vitality_level: 'low', 'medium', 'high'
        - vitality_score: 0-100的活力分数
        - confidence: 分类置信度
        """
        try:
            vitality_score = 0
            weight_sum = 0

            # 活力通常表现为适度的HRV和良好的心率变异性
            sdnn = features.get('sdnn', 0)
            rmssd = features.get('rmssd', 0)
            total_power = features.get('total_power', 0)
            mean_hr = features.get('mean_hr', 0)

            # 适度的SDNN表示良好的活力
            if sdnn > 0:
                if 30 <= sdnn <= 80:  # 理想范围
                    vitality_score += 80 * self.wesad_weights['vitality_indicators']['sdnn']
                elif 20 <= sdnn <= 100:  # 良好范围
                    vitality_score += 60 * self.wesad_weights['vitality_indicators']['sdnn']
                else:
                    vitality_score += 30 * self.wesad_weights['vitality_indicators']['sdnn']
                weight_sum += abs(self.wesad_weights['vitality_indicators']['sdnn'])

            # 良好的副交感功能
            if rmssd > 0:
                if rmssd >= 25:  # 良好的副交感活性
                    vitality_score += 80 * self.wesad_weights['vitality_indicators']['rmssd']
                elif rmssd >= 15:
                    vitality_score += 60 * self.wesad_weights['vitality_indicators']['rmssd']
                else:
                    vitality_score += 30 * self.wesad_weights['vitality_indicators']['rmssd']
                weight_sum += abs(self.wesad_weights['vitality_indicators']['rmssd'])

            # 总功率反映整体活力
            if total_power > 0:
                # 标准化总功率评估
                if total_power > 1000:  # 高功率
                    vitality_score += 80 * self.wesad_weights['vitality_indicators']['total_power']
                elif total_power > 500:
                    vitality_score += 60 * self.wesad_weights['vitality_indicators']['total_power']
                else:
                    vitality_score += 40 * self.wesad_weights['vitality_indicators']['total_power']
                weight_sum += abs(self.wesad_weights['vitality_indicators']['total_power'])

            # 适度心率
            if mean_hr > 0:
                if 60 <= mean_hr <= 85:  # 理想心率范围
                    vitality_score += 80 * abs(self.wesad_weights['vitality_indicators']['heart_rate'])
                elif 55 <= mean_hr <= 95:
                    vitality_score += 60 * abs(self.wesad_weights['vitality_indicators']['heart_rate'])
                else:
                    vitality_score += 30 * abs(self.wesad_weights['vitality_indicators']['heart_rate'])
                weight_sum += abs(self.wesad_weights['vitality_indicators']['heart_rate'])

            # 标准化分数
            if weight_sum > 0:
                vitality_score = vitality_score / weight_sum
            else:
                vitality_score = 50

            vitality_score = max(0, min(100, vitality_score))

            # 分类活力等级
            if vitality_score >= 70:
                vitality_level = 'high'
            elif vitality_score >= 40:
                vitality_level = 'medium'
            else:
                vitality_level = 'low'

            confidence = min(1.0, weight_sum / 1.0)

            return vitality_level, round(vitality_score, 1), round(confidence, 2)

        except Exception as e:
            self.logger.error(f"活力分类时出错: {traceback.format_exc()}")
            return 'medium', 50.0, 0.5

    def perform_clustering_analysis(self, features):
        """
        使用聚类方法进行更细致的分类

        返回:
        - cluster_label: 聚类标签
        - cluster_description: 聚类描述
        """
        try:
            # 准备聚类特征
            feature_vector = [
                features.get('sdnn', 0),
                features.get('rmssd', 0),
                features.get('pnn50', 0),
                features.get('lf_hf_ratio', 0),
                features.get('mean_hr', 0)
            ]

            # 检查特征是否有效
            if all(f == 0 for f in feature_vector):
                return 0, "数据不足"

            # 标准化特征
            scaler = StandardScaler()
            feature_vector = np.array(feature_vector).reshape(1, -1)

            # 避免除零错误
            if np.std(feature_vector) == 0:
                return 0, "特征无变化"

            feature_scaled = scaler.fit_transform(feature_vector)

            # 预定义的聚类中心（基于WESAD状态）
            cluster_centers = np.array([
                [-1, -1, -1, 1, 0.5],    # 高压力状态
                [0, 0, 0, 0, 0],         # 基线状态
                [1, 1, 1, -0.5, -0.5],   # 高活力状态
                [0.5, 1, 0.5, -1, -1]    # 放松状态
            ])

            # 计算到各聚类中心的距离
            distances = np.linalg.norm(feature_scaled - cluster_centers, axis=1)
            cluster_label = np.argmin(distances)

            # 聚类描述
            descriptions = [
                "高压力状态",
                "基线状态",
                "高活力状态",
                "放松状态"
            ]

            return cluster_label, descriptions[cluster_label]

        except Exception as e:
            self.logger.error(f"聚类分析时出错: {traceback.format_exc()}")
            return 1, "基线状态"

    def comprehensive_assessment(self, waveform_info):
        """
        综合评估函数，整合所有分类结果

        返回完整的评估报告
        """
        try:
            # 提取HRV特征
            features = self.calculate_hrv_features(waveform_info)

            if not features:
                return self._default_assessment()

            # 各项分类
            stress_level, stress_score, stress_confidence = self.classify_stress_level(features)
            fatigue_level, fatigue_score, fatigue_confidence = self.classify_fatigue_level(features)
            vitality_level, vitality_score, vitality_confidence = self.classify_vitality_level(features)
            cluster_label, cluster_description = self.perform_clustering_analysis(features)

            # 综合评估
            assessment = {
                'stress_assessment': {
                    'level': stress_level,
                    'score': stress_score,
                    'confidence': stress_confidence,
                    'risk_category': self._get_risk_category(stress_score, 'stress')
                },
                'fatigue_assessment': {
                    'level': fatigue_level,
                    'score': fatigue_score,
                    'confidence': fatigue_confidence,
                    'risk_category': self._get_risk_category(fatigue_score, 'fatigue')
                },
                'vitality_assessment': {
                    'level': vitality_level,
                    'score': vitality_score,
                    'confidence': vitality_confidence
                },
                'cluster_analysis': {
                    'cluster_id': cluster_label,
                    'description': cluster_description
                },
                'hrv_features': features,
                'overall_status': self._determine_overall_status(stress_score, fatigue_score, vitality_score)
            }

            return assessment

        except Exception as e:
            self.logger.error(f"综合评估时出错: {traceback.format_exc()}")
            return self._default_assessment()

    def _get_risk_category(self, score, assessment_type):
        """获取风险分类"""
        if assessment_type in ['stress', 'fatigue']:
            if score < 30:
                return '低风险'
            elif score < 70:
                return '中等风险'
            else:
                return '高风险'
        return '正常'

    def _determine_overall_status(self, stress_score, fatigue_score, vitality_score):
        """确定整体状态"""
        if stress_score > 70:
            return '高压力状态'
        elif fatigue_score > 70:
            return '高疲劳状态'
        elif vitality_score > 70:
            return '高活力状态'
        elif stress_score < 30 and fatigue_score < 30:
            return '良好状态'
        else:
            return '一般状态'

    def _default_assessment(self):
        """默认评估结果"""
        return {
            'stress_assessment': {
                'level': 'medium',
                'score': 50.0,
                'confidence': 0.5,
                'risk_category': '中等风险'
            },
            'fatigue_assessment': {
                'level': 'medium',
                'score': 50.0,
                'confidence': 0.5,
                'risk_category': '中等风险'
            },
            'vitality_assessment': {
                'level': 'medium',
                'score': 50.0,
                'confidence': 0.5
            },
            'cluster_analysis': {
                'cluster_id': 1,
                'description': '基线状态'
            },
            'hrv_features': {},
            'overall_status': '数据不足'
        }
