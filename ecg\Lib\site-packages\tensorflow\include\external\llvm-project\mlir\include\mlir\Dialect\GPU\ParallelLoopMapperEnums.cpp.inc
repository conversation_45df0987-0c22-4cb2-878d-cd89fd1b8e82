/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Enum Utility Definitions                                                   *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

namespace mlir {
namespace gpu {
::llvm::StringRef stringifyProcessor(Processor val) {
  switch (val) {
    case Processor::BlockX: return "BlockX";
    case Processor::BlockY: return "BlockY";
    case Processor::BlockZ: return "BlockZ";
    case Processor::ThreadX: return "ThreadX";
    case Processor::ThreadY: return "ThreadY";
    case Processor::ThreadZ: return "ThreadZ";
    case Processor::Sequential: return "Sequential";
  }
  return "";
}

::llvm::Optional<Processor> symbolizeProcessor(::llvm::StringRef str) {
  return ::llvm::StringSwitch<::llvm::Optional<Processor>>(str)
      .Case("BlockX", Processor::BlockX)
      .Case("BlockY", Processor::BlockY)
      .Case("BlockZ", Processor::BlockZ)
      .Case("ThreadX", Processor::ThreadX)
      .Case("ThreadY", Processor::ThreadY)
      .Case("ThreadZ", Processor::ThreadZ)
      .Case("Sequential", Processor::Sequential)
      .Default(::llvm::None);
}
::llvm::Optional<Processor> symbolizeProcessor(uint64_t value) {
  switch (value) {
  case 0: return Processor::BlockX;
  case 1: return Processor::BlockY;
  case 2: return Processor::BlockZ;
  case 3: return Processor::ThreadX;
  case 4: return Processor::ThreadY;
  case 5: return Processor::ThreadZ;
  case 6: return Processor::Sequential;
  default: return ::llvm::None;
  }
}

bool ProcessorAttr::classof(::mlir::Attribute attr) {
  return (((attr.isa<::mlir::IntegerAttr>())) && ((attr.cast<::mlir::IntegerAttr>().getType().isSignlessInteger(64)))) && (((attr.cast<::mlir::IntegerAttr>().getInt() == 0)) || ((attr.cast<::mlir::IntegerAttr>().getInt() == 1)) || ((attr.cast<::mlir::IntegerAttr>().getInt() == 2)) || ((attr.cast<::mlir::IntegerAttr>().getInt() == 3)) || ((attr.cast<::mlir::IntegerAttr>().getInt() == 4)) || ((attr.cast<::mlir::IntegerAttr>().getInt() == 5)) || ((attr.cast<::mlir::IntegerAttr>().getInt() == 6)));
}
ProcessorAttr ProcessorAttr::get(::mlir::MLIRContext *context, Processor val) {
  ::mlir::IntegerType intType = ::mlir::IntegerType::get(context, 64);
  ::mlir::IntegerAttr baseAttr = ::mlir::IntegerAttr::get(intType, static_cast<uint64_t>(val));
  return baseAttr.cast<ProcessorAttr>();
}
Processor ProcessorAttr::getValue() const {
  return static_cast<Processor>(::mlir::IntegerAttr::getInt());
}
} // namespace gpu
} // namespace mlir

::llvm::StringRef EnumToLayoutStr(Layout val) {
  switch (val) {
    case Layout::RowMajor: return "RowMajor";
    case Layout::ColMajor: return "ColMajor";
  }
  return "";
}

::llvm::Optional<Layout> LayoutStrToEnum(::llvm::StringRef str) {
  return ::llvm::StringSwitch<::llvm::Optional<Layout>>(str)
      .Case("RowMajor", Layout::RowMajor)
      .Case("ColMajor", Layout::ColMajor)
      .Default(::llvm::None);
}
::llvm::Optional<Layout> symbolizeLayout(unsigned value) {
  switch (value) {
  case 0: return Layout::RowMajor;
  case 1: return Layout::ColMajor;
  default: return ::llvm::None;
  }
}


