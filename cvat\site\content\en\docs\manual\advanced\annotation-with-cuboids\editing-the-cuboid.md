---
title: 'Editing the cuboid'
linkTitle: 'Editing the cuboid'
weight: 2
---

![](/images/image178_mapillary_vistas.jpg)

The cuboid can be edited in multiple ways: by dragging points, by dragging certain faces or by dragging planes.
First notice that there is a face that is painted with gray lines only, let us call it the front face.

You can move the cuboid by simply dragging the shape behind the front face.
The cuboid can be extended by dragging on the point in the middle of the edges.
The cuboid can also be extended up and down by dragging the point at the vertices.

![](/images/gif017_mapillary_vistas.gif)

To draw with perspective effects it should be assumed that the front face is the closest to the camera.
To begin simply drag the points on the vertices that are not on the gray/front face while holding `Shift`.
The cuboid can then be edited as usual.

![](/images/gif018_mapillary_vistas.gif)

If you wish to reset perspective effects, you may right click on the cuboid,
and select `Reset perspective` to return to a regular cuboid.

![](/images/image180_mapillary_vistas.jpg)

The location of the gray face can be swapped with the adjacent visible side face.
You can do it by right clicking on the cuboid and selecting `Switch perspective orientation`.
Note that this will also reset the perspective effects.

![](/images/image179_mapillary_vistas.jpg)

Certain faces of the cuboid can also be edited,
these faces are: the left, right and dorsal faces, relative to the gray face.
Simply drag the faces to move them independently from the rest of the cuboid.

![](/images/gif020_mapillary_vistas.gif)

You can also use cuboids in track mode, similar to rectangles in track mode
({{< ilink "/docs/manual/basics/track-mode-basics" "basics" >}} and
{{< ilink "/docs/manual/advanced/track-mode-advanced" "advanced" >}}) or
{{< ilink "/docs/manual/advanced/annotation-with-polygons/track-mode-with-polygons" "Track mode with polygons" >}}
