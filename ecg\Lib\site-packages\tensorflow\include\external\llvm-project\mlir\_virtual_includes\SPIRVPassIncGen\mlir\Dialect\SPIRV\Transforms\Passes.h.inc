/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// SPIRVCompositeTypeLayout
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SPIRVCompositeTypeLayoutBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SPIRVCompositeTypeLayoutBase;

  SPIRVCompositeTypeLayoutBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SPIRVCompositeTypeLayoutBase(const SPIRVCompositeTypeLayoutBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("decorate-spirv-composite-type-layout");
  }
  ::llvm::StringRef getArgument() const override { return "decorate-spirv-composite-type-layout"; }

  ::llvm::StringRef getDescription() const override { return "Decorate SPIR-V composite type with layout info"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SPIRVCompositeTypeLayout");
  }
  ::llvm::StringRef getName() const override { return "SPIRVCompositeTypeLayout"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SPIRVLowerABIAttributes
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SPIRVLowerABIAttributesBase : public ::mlir::OperationPass<spirv::ModuleOp> {
public:
  using Base = SPIRVLowerABIAttributesBase;

  SPIRVLowerABIAttributesBase() : ::mlir::OperationPass<spirv::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SPIRVLowerABIAttributesBase(const SPIRVLowerABIAttributesBase &other) : ::mlir::OperationPass<spirv::ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("spirv-lower-abi-attrs");
  }
  ::llvm::StringRef getArgument() const override { return "spirv-lower-abi-attrs"; }

  ::llvm::StringRef getDescription() const override { return "Decorate SPIR-V composite type with layout info"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SPIRVLowerABIAttributes");
  }
  ::llvm::StringRef getName() const override { return "SPIRVLowerABIAttributes"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SPIRVRewriteInsertsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SPIRVRewriteInsertsPassBase : public ::mlir::OperationPass<spirv::ModuleOp> {
public:
  using Base = SPIRVRewriteInsertsPassBase;

  SPIRVRewriteInsertsPassBase() : ::mlir::OperationPass<spirv::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SPIRVRewriteInsertsPassBase(const SPIRVRewriteInsertsPassBase &other) : ::mlir::OperationPass<spirv::ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("spirv-rewrite-inserts");
  }
  ::llvm::StringRef getArgument() const override { return "spirv-rewrite-inserts"; }

  ::llvm::StringRef getDescription() const override { return "Rewrite sequential chains of spv.CompositeInsert operations into spv.CompositeConstruct operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SPIRVRewriteInsertsPass");
  }
  ::llvm::StringRef getName() const override { return "SPIRVRewriteInsertsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SPIRVUpdateVCE
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SPIRVUpdateVCEBase : public ::mlir::OperationPass<spirv::ModuleOp> {
public:
  using Base = SPIRVUpdateVCEBase;

  SPIRVUpdateVCEBase() : ::mlir::OperationPass<spirv::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SPIRVUpdateVCEBase(const SPIRVUpdateVCEBase &other) : ::mlir::OperationPass<spirv::ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("spirv-update-vce");
  }
  ::llvm::StringRef getArgument() const override { return "spirv-update-vce"; }

  ::llvm::StringRef getDescription() const override { return "Deduce and attach minimal (version, capabilities, extensions) requirements to spv.module ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SPIRVUpdateVCE");
  }
  ::llvm::StringRef getName() const override { return "SPIRVUpdateVCE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// SPIRVCompositeTypeLayout Registration
//===----------------------------------------------------------------------===//

inline void registerSPIRVCompositeTypeLayoutPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::spirv::createDecorateSPIRVCompositeTypeLayoutPass();
  });
}

//===----------------------------------------------------------------------===//
// SPIRVLowerABIAttributes Registration
//===----------------------------------------------------------------------===//

inline void registerSPIRVLowerABIAttributesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::spirv::createLowerABIAttributesPass();
  });
}

//===----------------------------------------------------------------------===//
// SPIRVRewriteInsertsPass Registration
//===----------------------------------------------------------------------===//

inline void registerSPIRVRewriteInsertsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::spirv::createRewriteInsertsPass();
  });
}

//===----------------------------------------------------------------------===//
// SPIRVUpdateVCE Registration
//===----------------------------------------------------------------------===//

inline void registerSPIRVUpdateVCEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::spirv::createUpdateVersionCapabilityExtensionPass();
  });
}

//===----------------------------------------------------------------------===//
// SPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerSPIRVPasses() {
  registerSPIRVCompositeTypeLayoutPass();
  registerSPIRVLowerABIAttributesPass();
  registerSPIRVRewriteInsertsPassPass();
  registerSPIRVUpdateVCEPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
