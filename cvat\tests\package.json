{"scripts": {"cypress:run:chrome": "cypress run --browser chrome", "cypress:run:firefox": "cypress run --browser firefox --config-file nightly_cypress.config.js", "cypress:run:chrome:canvas3d": "cypress run --headed --browser chrome --config-file cypress_canvas3d.config.js"}, "dependencies": {"@cypress/code-coverage": "^3.9.10", "archiver": "^5.3.0", "cy-verify-downloads": "^0.0.5", "cypress": "^13.10.0", "cypress-file-upload": "^5.0.8", "cypress-localstorage-commands": "^1.7.0", "cypress-real-events": "^1.6.0", "extract-zip": "^2.0.1", "fs-extra": "^10.1.0", "jimp": "^0.22.10"}, "version": "0.0.0", "devDependencies": {}}