# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/hparams/hparams_util.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from tensorboard.plugins.hparams import api_pb2 as tensorboard_dot_plugins_dot_hparams_dot_api__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.tensorboard/plugins/hparams/hparams_util.proto\x12\x13tensorboard.hparams\x1a\x1cgoogle/protobuf/struct.proto\x1a%tensorboard/plugins/hparams/api.proto\"H\n\x0fHParamInfosList\x12\x35\n\x0chparam_infos\x18\x01 \x03(\x0b\x32\x1f.tensorboard.hparams.HParamInfo\"H\n\x0fMetricInfosList\x12\x35\n\x0cmetric_infos\x18\x01 \x03(\x0b\x32\x1f.tensorboard.hparams.MetricInfo\"\x8d\x01\n\x07HParams\x12:\n\x07hparams\x18\x01 \x03(\x0b\x32).tensorboard.hparams.HParams.HparamsEntry\x1a\x46\n\x0cHparamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\x62\x06proto3')



_HPARAMINFOSLIST = DESCRIPTOR.message_types_by_name['HParamInfosList']
_METRICINFOSLIST = DESCRIPTOR.message_types_by_name['MetricInfosList']
_HPARAMS = DESCRIPTOR.message_types_by_name['HParams']
_HPARAMS_HPARAMSENTRY = _HPARAMS.nested_types_by_name['HparamsEntry']
HParamInfosList = _reflection.GeneratedProtocolMessageType('HParamInfosList', (_message.Message,), {
  'DESCRIPTOR' : _HPARAMINFOSLIST,
  '__module__' : 'tensorboard.plugins.hparams.hparams_util_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.HParamInfosList)
  })
_sym_db.RegisterMessage(HParamInfosList)

MetricInfosList = _reflection.GeneratedProtocolMessageType('MetricInfosList', (_message.Message,), {
  'DESCRIPTOR' : _METRICINFOSLIST,
  '__module__' : 'tensorboard.plugins.hparams.hparams_util_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.MetricInfosList)
  })
_sym_db.RegisterMessage(MetricInfosList)

HParams = _reflection.GeneratedProtocolMessageType('HParams', (_message.Message,), {

  'HparamsEntry' : _reflection.GeneratedProtocolMessageType('HparamsEntry', (_message.Message,), {
    'DESCRIPTOR' : _HPARAMS_HPARAMSENTRY,
    '__module__' : 'tensorboard.plugins.hparams.hparams_util_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.hparams.HParams.HparamsEntry)
    })
  ,
  'DESCRIPTOR' : _HPARAMS,
  '__module__' : 'tensorboard.plugins.hparams.hparams_util_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.HParams)
  })
_sym_db.RegisterMessage(HParams)
_sym_db.RegisterMessage(HParams.HparamsEntry)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _HPARAMS_HPARAMSENTRY._options = None
  _HPARAMS_HPARAMSENTRY._serialized_options = b'8\001'
  _HPARAMINFOSLIST._serialized_start=140
  _HPARAMINFOSLIST._serialized_end=212
  _METRICINFOSLIST._serialized_start=214
  _METRICINFOSLIST._serialized_end=286
  _HPARAMS._serialized_start=289
  _HPARAMS._serialized_end=430
  _HPARAMS_HPARAMSENTRY._serialized_start=360
  _HPARAMS_HPARAMSENTRY._serialized_end=430
# @@protoc_insertion_point(module_scope)
