import pymysql
import pandas as pd
import os
import numpy as np
import matplotlib.pyplot as plt
import socket
import time

# 数据库配置
DB_CONFIG = {
    "host": "*************",    # 数据库服务器地址
    "port": 8306,               # 端口号
    "user": "ai",               # 用户名
    "password": "ai@1a3!c50",   # 密码
    "charset": "utf8mb4"        # 字符集
}

def test_connection_details():
    """
    详细测试数据库连接的各个环节
    """
    print("\n开始详细检查数据库连接...")
    print("-" * 50)
    
    # 1. 检查网络连通性
    print("\n1. 检查网络连通性:")
    try:
        # 创建套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        # 尝试连接
        start_time = time.time()
        result = sock.connect_ex((DB_CONFIG["host"], DB_CONFIG["port"]))
        end_time = time.time()
        
        if result == 0:
            print(f"✓ 服务器可访问！响应时间: {(end_time - start_time)*1000:.2f}ms")
            # 尝试发送MySQL握手包
            try:
                sock.send(b'\x0a')  # 发送一个简单的包
                response = sock.recv(1024)
                if response:
                    print("✓ 服务器响应MySQL协议！")
            except:
                print("✗ 服务器没有响应MySQL协议")
        else:
            print(f"✗ 无法连接到服务器！错误代码: {result}")
            print("  可能原因:")
            print("  - 服务器地址错误")
            print("  - 端口号错误")
            print("  - 服务器防火墙限制")
            print("  - 网络连接问题")
    except Exception as e:
        print(f"✗ 网络测试出错: {str(e)}")
    finally:
        sock.close()
    
    # 2. 检查数据库认证
    print("\n2. 检查数据库认证:")
    try:
        # 尝试不同的连接方式
        try:
            connection = pymysql.connect(
                host=DB_CONFIG["host"],
                port=DB_CONFIG["port"],
                user=DB_CONFIG["user"],
                password=DB_CONFIG["password"],
                charset=DB_CONFIG["charset"],
                connect_timeout=10
            )
            print("✓ 方式1：用户认证成功！")
            connection.close()
        except Exception as e1:
            print(f"✗ 方式1认证失败: {str(e1)}")
            print("\n尝试方式2...")
            try:
                connection = pymysql.connect(
                    host=DB_CONFIG["host"],
                    port=DB_CONFIG["port"],
                    user=DB_CONFIG["user"],
                    password=DB_CONFIG["password"],
                    charset=DB_CONFIG["charset"],
                    connect_timeout=10,
                    client_flag=pymysql.constants.CLIENT.MULTI_STATEMENTS
                )
                print("✓ 方式2：用户认证成功！")
                connection.close()
            except Exception as e2:
                print(f"✗ 方式2认证失败: {str(e2)}")
                raise e2
    except Exception as e:
        print(f"✗ 所有认证方式都失败")
        print(f"  最后的错误: {str(e)}")
    
    print("\n3. 当前配置信息:")
    print(f"  主机: {DB_CONFIG['host']}")
    print(f"  端口: {DB_CONFIG['port']}")
    print(f"  用户名: {DB_CONFIG['user']}")
    print(f"  字符集: {DB_CONFIG['charset']}")
    
    print("\n4. 建议:")
    print("  - 确认服务器IP和端口是否正确")
    print("  - 检查用户名和密码是否正确")
    print("  - 确认该用户是否有权限访问数据库")
    print("  - 检查服务器防火墙设置")
    print("  - 检查MySQL用户的主机访问权限设置")
    print("-" * 50)

def get_db_connection(database=None):
    """
    获取数据库连接
    Args:
        database: 要连接的数据库名称，如果不指定，则不选择特定数据库
    """
    config = DB_CONFIG.copy()
    if database:
        config["database"] = database
    
    # 添加本地主机名到配置中
    config["host"] = config["host"]
    
    try:
        return pymysql.connect(
            **config,
            connect_timeout=10,
            read_timeout=30,
            write_timeout=30,
            client_flag=pymysql.constants.CLIENT.MULTI_STATEMENTS
        )
    except Exception as e:
        print(f"连接错误: {str(e)}")
        print("\n尝试使用不同的连接方式...")
        
        # 如果第一次连接失败，尝试不同的连接配置
        try:
            return pymysql.connect(
                host=config["host"],
                port=config["port"],
                user=config["user"],
                password=config["password"],
                database=config.get("database"),
                charset=config["charset"],
                connect_timeout=10
            )
        except Exception as e2:
            print(f"第二次连接也失败: {str(e2)}")
            raise e2

def list_databases():
    """
    列出所有可用的数据库
    Returns:
        list: 数据库名称列表
    """
    try:
        # 不指定数据库名称进行连接
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 查询所有数据库
        cursor.execute("SHOW DATABASES")
        databases = cursor.fetchall()
        
        # 过滤系统数据库
        db_list = [db[0] for db in databases if db[0] not in ['information_schema', 'mysql', 'performance_schema', 'sys']]
        
        print("\n可用的数据库列表:")
        print("-" * 50)
        for i, db in enumerate(db_list, 1):
            print(f"{i}. {db}")
        print("-" * 50)
        
        return db_list
        
    except Exception as e:
        print(f"查询数据库列表时出错: {str(e)}")
        return []
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

def query_patient_ecg_by_study_uid(study_uid, database):
    """
    根据study_uid查询患者的ECG数据
    Args:
        study_uid: 唯一ID
        database: 数据库名称
    Returns:
        DataFrame: 查询结果
    """
    try:
        connection = get_db_connection(database)
        cursor = connection.cursor()
        
        # 直接查询t_patient_ecg_i表（I导数据）
        query = """
        SELECT * FROM t_patient_ecg_i WHERE study_uid = %s
        """
        
        print(f"\n开始查询study_uid为 {study_uid} 的ECG数据...")
        cursor.execute(query, (study_uid,))
        results = cursor.fetchall()
        
        if not results:
            print(f"未找到study_uid为 {study_uid} 的ECG数据")
            return None
            
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 转换为DataFrame
        df = pd.DataFrame(results, columns=columns)
        print(f"\n找到 {len(df)} 条记录")
        
        # 基本信息
        print("\n基本信息:")
        for col in df.columns:
            if col not in ['waveform', 'raw_data']:
                print(f"{col}: {df[col].values[0]}")
                
        # 保存ECG数据到CSV文件
        if 'waveform' in df.columns and df['waveform'].iloc[0] is not None:
            # 保存处理后的波形数据
            waveform_data = df['waveform'].iloc[0]
            csv_file = f"patient_ecg_{study_uid}.csv"
            with open(csv_file, 'w') as f:
                f.write(waveform_data)
            print(f"\n波形数据已保存到文件: {csv_file}")
            
            # 绘制ECG波形图
            try:
                data = np.fromstring(waveform_data, sep=',')
                plt.figure(figsize=(15, 5))
                plt.plot(data)
                plt.title(f"ECG Waveform - Study UID: {study_uid}")
                plt.xlabel("Sample")
                plt.ylabel("Amplitude")
                plt.grid(True)
                
                # 保存图像
                plt.savefig(f"ecg_waveform_{study_uid}.png")
                print(f"波形图已保存为: ecg_waveform_{study_uid}.png")
                plt.close()
            except Exception as e:
                print(f"绘制波形图时出错: {str(e)}")
        
        return df
        
    except Exception as e:
        print(f"查询数据时出错: {str(e)}")
        return None
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

def query_patient_info(study_uid, database):
    """
    查询患者基本信息
    Args:
        study_uid: 唯一ID
        database: 数据库名称
    """
    try:
        connection = get_db_connection(database)
        cursor = connection.cursor()
        
        # 查询主表信息
        query = """
        SELECT * FROM t_patient_ecg WHERE study_uid = %s
        """
        
        print(f"\n查询患者信息 (study_uid: {study_uid})...")
        cursor.execute(query, (study_uid,))
        results = cursor.fetchall()
        
        if not results:
            print(f"未找到study_uid为 {study_uid} 的患者信息")
            return
            
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 打印患者信息
        print("\n患者基本信息:")
        patient_data = results[0]
        for i, col in enumerate(columns):
            print(f"{col}: {patient_data[i]}")
            
    except Exception as e:
        print(f"查询患者信息时出错: {str(e)}")
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

def check_tables(database):
    """
    检查数据库表结构
    Args:
        database: 数据库名称
    """
    try:
        connection = get_db_connection(database)
        cursor = connection.cursor()
        
        # 1. 检查表是否存在
        print(f"\n检查数据库 {database} 中的表...")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print("\n存在的表:")
        for table in tables:
            print(f"- {table[0]}")
            
        # 2. 检查表结构
        for table_name in ['t_patient_ecg', 't_patient_ecg_i']:
            print(f"\n检查 {table_name} 表结构:")
            try:
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                print("\n字段列表:")
                for col in columns:
                    print(f"- {col[0]} ({col[1]})")
                    
                # 3. 检查表中的记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"\n{table_name} 表中共有 {count} 条记录")
                
                # 4. 获取示例数据
                print(f"\n{table_name} 表数据示例:")
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                samples = cursor.fetchall()
                if samples:
                    # 获取列名
                    cursor.execute(f"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '{database}' AND TABLE_NAME = '{table_name}'")
                    columns = [col[0] for col in cursor.fetchall()]
                    
                    # 打印每条记录的详细信息
                    for i, sample in enumerate(samples, 1):
                        print(f"\n示例 {i}:")
                        for col_name, value in zip(columns, sample):
                            # 对于大字段数据，只显示长度信息
                            if isinstance(value, (bytes, bytearray)) or (isinstance(value, str) and len(str(value)) > 100):
                                print(f"  {col_name}: <数据长度: {len(str(value))}字节>")
                            else:
                                print(f"  {col_name}: {value}")
                else:
                    print("表中没有数据")
                
            except Exception as e:
                print(f"检查表 {table_name} 时出错: {str(e)}")
                
    except Exception as e:
        print(f"检查表结构时出错: {str(e)}")
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    try:
        # 详细测试数据库连接
        test_connection_details()
        
        # 如果连接测试失败，询问是否继续
        response = input("\n是否继续执行查询操作？(y/n): ")
        if response.lower() != 'y':
            print("程序已终止")
            exit(0)
            
        # 列出所有可用的数据库
        print("\n正在获取可用的数据库列表...")
        databases = list_databases()
        
        if not databases:
            print("未找到任何可用的数据库")
            exit(1)
            
        # 选择数据库
        while True:
            try:
                db_index = int(input("\n请输入要使用的数据库编号: ")) - 1
                if 0 <= db_index < len(databases):
                    selected_db = databases[db_index]
                    print(f"\n已选择数据库: {selected_db}")
                    break
                else:
                    print("无效的数据库编号，请重新输入")
            except ValueError:
                print("请输入有效的数字")
        
        # 检查表结构
        check_tables(selected_db)
        
        # 输入study_uid
        study_uid = input("\n请输入要查询的study_uid: ")
        
        # 查询患者信息
        query_patient_info(study_uid, selected_db)
        
        # 查询ECG数据
        df = query_patient_ecg_by_study_uid(study_uid, selected_db)
        
    except Exception as e:
        print(f"\n程序执行出错: {str(e)}")
        print("\n请检查：")
        print("1. 数据库连接信息是否正确")
        print("2. 数据库服务器是否可访问")
        print("3. 用户名和密码是否正确")
        print("4. 是否有相应的数据库访问权限")