# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# angularcircle, 2011
# angularcircle, 2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <PERSON>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2018-05-19 00:03+0000\n"
"Last-Translator: m_aciek <<EMAIL>>\n"
"Language-Team: Polish (http://www.transifex.com/django/django/language/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n"
"%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n"
"%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

msgid "Humanize"
msgstr "Humanizacja"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}."

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}."

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f milion"
msgstr[1] "%(value).1f miliony"
msgstr[2] "%(value).1f milionów"
msgstr[3] "%(value).1f milionów"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s milion"
msgstr[1] "%(value)s miliony"
msgstr[2] "%(value)s milionów"
msgstr[3] "%(value)s milionów"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f miliard"
msgstr[1] "%(value).1f miliardy"
msgstr[2] "%(value).1f miliardów"
msgstr[3] "%(value).1f miliardów"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s miliard"
msgstr[1] "%(value)s miliardy"
msgstr[2] "%(value)s miliardów"
msgstr[3] "%(value)s miliardów"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f kwintylion"
msgstr[1] "%(value).1f biliony"
msgstr[2] "%(value).1f kwintylionów"
msgstr[3] "%(value).1f kwintylionów"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s kwintylion"
msgstr[1] "%(value)s biliony"
msgstr[2] "%(value)s kwintylionów"
msgstr[3] "%(value)s kwintylionów"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] "%(value).1f kwadrylion"
msgstr[1] "%(value).1f biliardy"
msgstr[2] "%(value).1f kwadrylionów"
msgstr[3] "%(value).1f kwadrylionów"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s kwadrylion"
msgstr[1] "%(value)s biliardy"
msgstr[2] "%(value)s kwadrylionów"
msgstr[3] "%(value)s kwadrylionów"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] "%(value).1f trylion"
msgstr[1] "%(value).1f tryliony"
msgstr[2] "%(value).1f trylionów"
msgstr[3] "%(value).1f trylionów"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s trylion"
msgstr[1] "%(value)s tryliony"
msgstr[2] "%(value)s trylionyów"
msgstr[3] "%(value)s trylionyów"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] "%(value).1f tryliard"
msgstr[1] "%(value).1f tryliardy"
msgstr[2] "%(value).1f tryliardów"
msgstr[3] "%(value).1f tryliardów"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] " %(value)s tryliard"
msgstr[1] "%(value)s tryliardy"
msgstr[2] "%(value)s tryliardów"
msgstr[3] "%(value)s tryliardów"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] "%(value).1f septylion"
msgstr[1] "%(value).1f septyliony"
msgstr[2] "%(value).1f septylionów"
msgstr[3] "%(value).1f septylionów"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septylion"
msgstr[1] "%(value)s septyliony"
msgstr[2] "%(value)s septylionów"
msgstr[3] "%(value)s septylionów"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] "%(value).1f kwadryliard"
msgstr[1] "%(value).1f kwardyliardy"
msgstr[2] "%(value).1f kwadryliardów"
msgstr[3] "%(value).1f kwadryliardów"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s kwadryliard"
msgstr[1] "%(value)s kwardyliardy"
msgstr[2] "%(value)s kwadryliardów"
msgstr[3] "%(value)s kwadryliardów"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] "%(value).1f kwintylion"
msgstr[1] "%(value).1f kwintyliony"
msgstr[2] "%(value).1f kwintylionów"
msgstr[3] "%(value).1f kwintylionów"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s kwintylion"
msgstr[1] "%(value)s kwintyliony"
msgstr[2] "%(value)s kwintylionów"
msgstr[3] "%(value)s kwintylionów"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] "%(value).1f kwintyliard"
msgstr[1] "%(value).1f kwintyliardy"
msgstr[2] "%(value).1f kwintyliardów"
msgstr[3] "%(value).1f kwintyliardów"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s kwintyliard"
msgstr[1] "%(value)s kwintyliardy"
msgstr[2] "%(value)s kwintyliardów"
msgstr[3] "%(value)s kwintyliardów"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] "%(value).1f googol"
msgstr[1] "%(value).1f googole"
msgstr[2] "%(value).1f googolów"
msgstr[3] "%(value).1f googolów"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googole"
msgstr[2] "%(value)s googolów"
msgstr[3] "%(value)s googolów"

msgid "one"
msgstr "jeden"

msgid "two"
msgstr "dwa"

msgid "three"
msgstr "trzy"

msgid "four"
msgstr "cztery"

msgid "five"
msgstr "pięć"

msgid "six"
msgstr "sześć"

msgid "seven"
msgstr "siedem"

msgid "eight"
msgstr "osiem"

msgid "nine"
msgstr "dziewięć"

msgid "today"
msgstr "dzisiaj"

msgid "tomorrow"
msgstr "jutro"

msgid "yesterday"
msgstr "wczoraj"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s temu"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "godzinę temu"
msgstr[1] "%(count)s godziny temu"
msgstr[2] "%(count)s godzin temu"
msgstr[3] "%(count)s godzin temu"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "minutę temu"
msgstr[1] "%(count)s minuty temu"
msgstr[2] "%(count)s minut temu"
msgstr[3] "%(count)s minut temu"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "sekundę temu"
msgstr[1] "%(count)s sekundy temu"
msgstr[2] "%(count)s sekund temu"
msgstr[3] "%(count)s sekund temu"

msgid "now"
msgstr "teraz"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "za sekundę"
msgstr[1] "za %(count)s sekundy"
msgstr[2] "za %(count)s sekund"
msgstr[3] "za %(count)s sekund"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "za minutę"
msgstr[1] "za %(count)s minuty"
msgstr[2] "za %(count)s minut"
msgstr[3] "za %(count)s minut"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "za godzinę"
msgstr[1] "za %(count)s godziny"
msgstr[2] "za %(count)s godzin"
msgstr[3] "za %(count)s godzin"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "za %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d rok"
msgstr[1] "%d lata"
msgstr[2] "%d lat"
msgstr[3] "%d lat"

#, python-format
msgctxt "naturaltime-past"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d miesiąc"
msgstr[1] "%d miesiące"
msgstr[2] "%d miesięcy"
msgstr[3] "%d miesięcy"

#, python-format
msgctxt "naturaltime-past"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d tydzień"
msgstr[1] "%d tygodnie"
msgstr[2] "%d tygodni"
msgstr[3] "%d tygodni"

#, python-format
msgctxt "naturaltime-past"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dzień"
msgstr[1] "%d dni"
msgstr[2] "%d dni"
msgstr[3] "%d dni"

#, python-format
msgctxt "naturaltime-past"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d godzinę"
msgstr[1] "%d godziny"
msgstr[2] "%d godzin"
msgstr[3] "%d godzin"

#, python-format
msgctxt "naturaltime-past"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minutę"
msgstr[1] "%d minuty"
msgstr[2] "%d minut"
msgstr[3] "%d minut"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d rok"
msgstr[1] "%d lata"
msgstr[2] "%d lat"
msgstr[3] "%d lat"

#, python-format
msgctxt "naturaltime-future"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d miesiąc"
msgstr[1] "%d miesiące"
msgstr[2] "%d miesięcy"
msgstr[3] "%d miesięcy"

#, python-format
msgctxt "naturaltime-future"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d tydzień"
msgstr[1] "%d tygodnie"
msgstr[2] "%d tygodni"
msgstr[3] "%d tygodni"

#, python-format
msgctxt "naturaltime-future"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dzień"
msgstr[1] "%d dni"
msgstr[2] "%d dni"
msgstr[3] "%d dni"

#, python-format
msgctxt "naturaltime-future"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d godzinę"
msgstr[1] "%d godziny"
msgstr[2] "%d godzin"
msgstr[3] "%d godzin"

#, python-format
msgctxt "naturaltime-future"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minutę"
msgstr[1] "%d minuty"
msgstr[2] "%d minut"
msgstr[3] "%d minut"
