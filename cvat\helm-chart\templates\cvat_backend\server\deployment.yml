{{- $localValues := .Values.cvat.backend.server -}}

apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-backend-server
  namespace: {{ .Release.Namespace }}
  labels:
    app: cvat-app
    tier: backend
    component: server
    {{- include "cvat.labels" . | nindent 4 }}
    {{- with merge $localValues.labels .Values.cvat.backend.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with merge $localValues.annotations .Values.cvat.backend.annotations }}
  annotations:
  {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ $localValues.replicas }}
  strategy:
    type: Recreate
  selector:
    matchLabels:
      {{- include "cvat.labels" . | nindent 6 }}
      {{- with merge $localValues.labels .Values.cvat.backend.labels }}
      {{- toYaml . | nindent 6 }}
      {{- end }}
      app: cvat-app
      tier: backend
      component: server
  template:
    metadata:
      labels:
        app: cvat-app
        tier: backend
        component: server
        {{- include "cvat.labels" . | nindent 8 }}
        {{- with merge $localValues.labels .Values.cvat.backend.labels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with merge $localValues.annotations .Values.cvat.backend.annotations }}
      annotations:
      {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      serviceAccountName: {{ include "cvat.backend.serviceAccountName" . }}
      containers:
        - name: cvat-backend
          image: {{ .Values.cvat.backend.image }}:{{ .Values.cvat.backend.tag }}
          imagePullPolicy: {{ .Values.cvat.backend.imagePullPolicy }}
          {{- with merge $localValues.resources .Values.cvat.backend.resources }}
          resources:
          {{- toYaml . | nindent 12 }}
          {{- end }}
          args: ["run", "server"]
          env:
          - name: ALLOWED_HOSTS
            value: {{ $localValues.envs.ALLOWED_HOSTS | squote}}
          {{ include "cvat.sharedBackendEnv" . | indent 10 }}
          {{ include "cvat.sharedClickhouseEnv" . | indent 10 }}
          {{- with concat .Values.cvat.backend.additionalEnv $localValues.additionalEnv }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          ports:
          - containerPort: 8080
          {{- if $localValues.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: /api/server/about
              port: 8080
            {{- toYaml (omit $localValues.readinessProbe "enabled") | nindent 12 }}
          {{- end }}
          {{- if $localValues.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: /api/server/about
              port: 8080
            {{- toYaml (omit $localValues.livenessProbe "enabled") | nindent 12 }}
          {{- end }}
          volumeMounts:
          - mountPath: /home/<USER>/data
            name: cvat-backend-data
            subPath: data
          - mountPath: /home/<USER>/keys
            name: cvat-backend-data
            subPath: keys
          - mountPath: /home/<USER>/logs
            name: cvat-backend-data
            subPath: logs
          - mountPath: /home/<USER>/models
            name: cvat-backend-data
            subPath: models
          {{- with concat .Values.cvat.backend.additionalVolumeMounts $localValues.additionalVolumeMounts }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
      initContainers:
        {{- if .Values.cvat.backend.permissionFix.enabled }}
        - name: user-data-permission-fix
          image: busybox
          command: ["/bin/chmod", "-R", "777", "/home/<USER>"]
          {{- with merge $localValues.resources .Values.cvat.backend.resources }}
          resources:
          {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
          {{- if .Values.cvat.backend.defaultStorage.enabled }}
          - mountPath: /home/<USER>/data
            name: cvat-backend-data
            subPath: data
          - mountPath: /home/<USER>/keys
            name: cvat-backend-data
            subPath: keys
          - mountPath: /home/<USER>/logs
            name: cvat-backend-data
            subPath: logs
          - mountPath: /home/<USER>/models
            name: cvat-backend-data
            subPath: models
          {{- end }}
          {{- with concat .Values.cvat.backend.additionalVolumeMounts $localValues.additionalVolumeMounts }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
        {{- end }}
      {{- with merge $localValues.affinity .Values.cvat.backend.affinity }}
      affinity:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with concat .Values.cvat.backend.tolerations $localValues.tolerations }}
      tolerations:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        {{- if .Values.cvat.backend.defaultStorage.enabled }}
        - name: cvat-backend-data
          persistentVolumeClaim:
            claimName: "{{ .Release.Name }}-backend-data"
        {{- end }}
        {{- with concat .Values.cvat.backend.additionalVolumes $localValues.additionalVolumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
