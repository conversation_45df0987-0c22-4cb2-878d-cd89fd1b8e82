import json
import traceback
from datetime import datetime, timedelta
from itertools import chain

import pandas as pd
from django.utils.decorators import method_decorator
from django.views import View

from apps.api.report.business import get_report
from apps.models.data_models import TReportLog
from apps.models.report_model import ReportModel
from apps.models.t_data_ecg_model import get_data_ecg_model
from apps.utils.datetime_helper import convert_to_time_format
from apps.utils.decorator import authentication
from apps.utils.get_response import GetResponse
from apps.utils.logger_helper import Logger
from apps.utils.param_extraction import param_extraction
from apps.utils.pdf_helper import generate_pdf
from apps.utils.sql_helper import table_exists


@method_decorator(authentication, name="dispatch")
class ReportView(View):
    """
    报告
    """

    def post(self, request, *args, **kwargs):
        """
        POST请求接口
        :param request: 请求对象
        :return: 诊断结果
        """
        custom_id = kwargs.get('custom_id')

        try:
            union_id = param_extraction(request, 'union_id', 'POST')
            date = param_extraction(request, 'date', 'POST')

            if union_id is None:
                return GetResponse.get_response(code=5)

            report_model = ReportModel()

            if date:
                # 固定日期模式
                date = datetime.strptime(date, '%Y-%m-%d')
                # 检查数据是否存在
                if not table_exists(f"t_data_ecg_{date.strftime('%Y%m%d')}"):
                    return GetResponse.get_response(code=0, data='未查询到数据')

                # 查询数据
                data_table = get_data_ecg_model(date.strftime('%Y%m%d'))
                ecg_datas = list(data_table.objects.filter(union_id=union_id).all())

                # 设置时间
                report_model.start_time = f"{date.strftime('%Y-%m-%d %H:%M:%S')}"
                report_model.analysis_time = f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            else:
                # 24小时模式
                # 获取当前时间
                current_time = datetime.now()

                # 计算前24小时的时间点
                previous_time = current_time - timedelta(hours=24)

                current_time_str = current_time.strftime('%Y%m%d')

                previous_time_str = previous_time.strftime('%Y%m%d')

                if not table_exists(f't_data_ecg_{current_time_str}') and not table_exists(
                        f't_data_ecg_{previous_time_str}'):
                    return GetResponse.get_response(code=0, data='未查询到数据')

                if not table_exists(f't_data_ecg_{current_time_str}'):
                    # 如果当天的表还未创建，使用前一天的数据
                    previous_data_table = get_data_ecg_model(previous_time_str)
                    ecg_datas = previous_data_table.objects.filter(union_id=union_id,
                                                                   check_date__gte=previous_time).all()
                elif not table_exists(f't_data_ecg_{previous_time_str}') or current_time_str == previous_time_str:
                    # 如果如果前天的表还未创建或者开始时间和结束时间相等，使用当天的数据
                    data_table = get_data_ecg_model(current_time_str)
                    ecg_datas = list(data_table.objects.filter(union_id=union_id).all())
                else:
                    # 分别读取当天结束时间前的数据和前一天开始时间后的数据进行合并
                    current_data_table = get_data_ecg_model(current_time_str)
                    current_ecg_data = current_data_table.objects.filter(union_id=union_id,
                                                                         check_date__lte=current_time).all()

                    previous_data_table = get_data_ecg_model(previous_time_str)
                    previous_ecg_data = previous_data_table.objects.filter(union_id=union_id,
                                                                           check_date__gte=previous_time).all()

                    ecg_datas = list(chain(current_ecg_data, previous_ecg_data))

                report_model.start_time = f"{previous_time.strftime('%Y-%m-%d %H:%M:%S')}"
                report_model.analysis_time = f"{current_time.strftime('%Y-%m-%d %H:%M:%S')}"

                report_model.check_time = f"{previous_time.strftime('%Y-%m-%d %H:%M:%S')} 至 {current_time.strftime('%Y-%m-%d %H:%M:%S')}"

            if len(ecg_datas) == 0:
                return GetResponse.get_response(code=0, data='未查询到数据')

            data_dicts = [item.__dict__ for item in ecg_datas]

            datas = pd.DataFrame(data_dicts)
            normal_data = datas[datas['dead'] == 1]  # 获取信号质量正常的数据

            report_model = get_report(report_model, datas, normal_data)

            # 设置记录和有效时长
            report_model.record_duration = convert_to_time_format(len(datas))
            report_model.effective_duration = convert_to_time_format(len(normal_data))

            # 记录请求日志
            report_log = TReportLog()
            report_log.custom_id = custom_id
            report_log.request_param = json.dumps(request.POST)
            report_log.create_date = datetime.now()
            report_log.save()

            return generate_pdf(report_model, 'report_template.html')
        except Exception:
            Logger().error(traceback.format_exc())
            return GetResponse.get_response(code=2)



