import os
import glob
import csv
import json
import decimal

def convert_txt_to_csv(txt_file_path):
    """
    将单个 txt 文件转换为 csv 文件
    假定 txt 文件中的数据为 JSON 格式的列表，例如：
    [-0.04436173025902826, -0.053796155406700984, ...]
    使用 decimal.Decimal 保持高精度不丢失末尾数字。
    
    由于后续用于诊断计算，需要确保数据的每一位数字都准确，采用 Decimal 可以保证所读取数据的精度。
    """
    # 读取 txt 文件内容并去除头尾空白字符
    with open(txt_file_path, 'r', encoding='utf-8') as f:
        data = f.read().strip()
    
    try:
        # 利用 parse_float 参数将 JSON 中的数字转换为 Decimal 对象
        data_points = json.loads(data, parse_float=decimal.Decimal)
    except Exception as e:
        print(f"解析文件 {txt_file_path} 时出错: {e}")
        return

    # 生成 csv 文件路径，将后缀从 .txt 改为 .csv
    csv_file_path = os.path.splitext(txt_file_path)[0] + '.csv'

    # 写入 csv 文件时，将 Decimal 对象转换为字符串以保持全精度
    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow([str(dp) for dp in data_points])
    
    print(f"转换成功：\n  {txt_file_path} \n--> {csv_file_path}")

def batch_convert(directory):
    """
    遍历指定目录，将所有 txt 文件批量转换为 csv 文件
    """
    txt_files = glob.glob(os.path.join(directory, '*.txt'))
    if not txt_files:
        print("在该目录下未找到任何 txt 文件！")
        return
    
    for txt_file in txt_files:
        convert_txt_to_csv(txt_file)

if __name__ == '__main__':
    # 指定存储数据的目录，确保路径正确并且数据文件为 JSON 格式列表
    data_dir = r'D:\ECG\0205室性早搏\待测数据'
    batch_convert(data_dir)