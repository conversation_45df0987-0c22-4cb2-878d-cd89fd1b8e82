---
title: 'Branching model'
linkTitle: 'Branching model'
weight: 5
description: 'Information about the branching model that is used in the project.'
---

The project uses [a successful Git branching model](https://nvie.com/posts/a-successful-git-branching-model).
Thus it has a couple of branches. Some of them are described below:

- `origin/master` to be the main branch where the source code of
  HEAD always reflects a production-ready state

- `origin/develop` to be the main branch where the source code of
  HEAD always reflects a state with the latest delivered development
  changes for the next release. Some would call this the “integration branch”.
