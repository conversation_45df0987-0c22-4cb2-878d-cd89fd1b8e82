// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tensorflow/core/profiler/profiler_analysis.proto
#ifndef GRPC_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto__INCLUDED
#define GRPC_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto__INCLUDED

#include "tensorflow/core/profiler/profiler_analysis.pb.h"

#include <functional>
#include <grpc/impl/codegen/port_platform.h>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace tensorflow {


namespace grpc {

// //////////////////////////////////////////////////////////////////////////////
// ProfileAnalysis service provide entry point for profiling TPU and for
// serving profiled data to Tensorboard through GRPC
// //////////////////////////////////////////////////////////////////////////////
class ProfileAnalysis final {
 public:
  static constexpr char const* service_full_name() {
    return "tensorflow.ProfileAnalysis";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Starts a profiling session, blocks until it completes.
    // TPUProfileAnalysis service delegate this to TPUProfiler service.
    // Populate the profiled data in repository, then return status to caller.
    virtual ::grpc::Status NewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest& request, ::tensorflow::NewProfileSessionResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::NewProfileSessionResponse>> AsyncNewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::NewProfileSessionResponse>>(AsyncNewSessionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::NewProfileSessionResponse>> PrepareAsyncNewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::NewProfileSessionResponse>>(PrepareAsyncNewSessionRaw(context, request, cq));
    }
    // Enumerate existing sessions and return available profile tools.
    virtual ::grpc::Status EnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest& request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::EnumProfileSessionsAndToolsResponse>> AsyncEnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::EnumProfileSessionsAndToolsResponse>>(AsyncEnumSessionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::EnumProfileSessionsAndToolsResponse>> PrepareAsyncEnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::EnumProfileSessionsAndToolsResponse>>(PrepareAsyncEnumSessionsRaw(context, request, cq));
    }
    // Retrieve specific tool's data for specific session.
    virtual ::grpc::Status GetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest& request, ::tensorflow::ProfileSessionDataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileSessionDataResponse>> AsyncGetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileSessionDataResponse>>(AsyncGetSessionToolDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileSessionDataResponse>> PrepareAsyncGetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileSessionDataResponse>>(PrepareAsyncGetSessionToolDataRaw(context, request, cq));
    }
    class experimental_async_interface {
     public:
      virtual ~experimental_async_interface() {}
      // Starts a profiling session, blocks until it completes.
      // TPUProfileAnalysis service delegate this to TPUProfiler service.
      // Populate the profiled data in repository, then return status to caller.
      virtual void NewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest* request, ::tensorflow::NewProfileSessionResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void NewSession(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::NewProfileSessionResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void NewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest* request, ::tensorflow::NewProfileSessionResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void NewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest* request, ::tensorflow::NewProfileSessionResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void NewSession(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::NewProfileSessionResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void NewSession(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::NewProfileSessionResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Enumerate existing sessions and return available profile tools.
      virtual void EnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void EnumSessions(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void EnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void EnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void EnumSessions(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void EnumSessions(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Retrieve specific tool's data for specific session.
      virtual void GetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest* request, ::tensorflow::ProfileSessionDataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetSessionToolData(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileSessionDataResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void GetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest* request, ::tensorflow::ProfileSessionDataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void GetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest* request, ::tensorflow::ProfileSessionDataResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void GetSessionToolData(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileSessionDataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void GetSessionToolData(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileSessionDataResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
    };
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    typedef class experimental_async_interface async_interface;
    #endif
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    async_interface* async() { return experimental_async(); }
    #endif
    virtual class experimental_async_interface* experimental_async() { return nullptr; }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::NewProfileSessionResponse>* AsyncNewSessionRaw(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::NewProfileSessionResponse>* PrepareAsyncNewSessionRaw(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::EnumProfileSessionsAndToolsResponse>* AsyncEnumSessionsRaw(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::EnumProfileSessionsAndToolsResponse>* PrepareAsyncEnumSessionsRaw(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileSessionDataResponse>* AsyncGetSessionToolDataRaw(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileSessionDataResponse>* PrepareAsyncGetSessionToolDataRaw(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status NewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest& request, ::tensorflow::NewProfileSessionResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::NewProfileSessionResponse>> AsyncNewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::NewProfileSessionResponse>>(AsyncNewSessionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::NewProfileSessionResponse>> PrepareAsyncNewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::NewProfileSessionResponse>>(PrepareAsyncNewSessionRaw(context, request, cq));
    }
    ::grpc::Status EnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest& request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::EnumProfileSessionsAndToolsResponse>> AsyncEnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::EnumProfileSessionsAndToolsResponse>>(AsyncEnumSessionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::EnumProfileSessionsAndToolsResponse>> PrepareAsyncEnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::EnumProfileSessionsAndToolsResponse>>(PrepareAsyncEnumSessionsRaw(context, request, cq));
    }
    ::grpc::Status GetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest& request, ::tensorflow::ProfileSessionDataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileSessionDataResponse>> AsyncGetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileSessionDataResponse>>(AsyncGetSessionToolDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileSessionDataResponse>> PrepareAsyncGetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileSessionDataResponse>>(PrepareAsyncGetSessionToolDataRaw(context, request, cq));
    }
    class experimental_async final :
      public StubInterface::experimental_async_interface {
     public:
      void NewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest* request, ::tensorflow::NewProfileSessionResponse* response, std::function<void(::grpc::Status)>) override;
      void NewSession(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::NewProfileSessionResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void NewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest* request, ::tensorflow::NewProfileSessionResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void NewSession(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest* request, ::tensorflow::NewProfileSessionResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void NewSession(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::NewProfileSessionResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void NewSession(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::NewProfileSessionResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void EnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, std::function<void(::grpc::Status)>) override;
      void EnumSessions(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void EnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void EnumSessions(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void EnumSessions(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void EnumSessions(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void GetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest* request, ::tensorflow::ProfileSessionDataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetSessionToolData(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileSessionDataResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void GetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest* request, ::tensorflow::ProfileSessionDataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void GetSessionToolData(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest* request, ::tensorflow::ProfileSessionDataResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void GetSessionToolData(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileSessionDataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void GetSessionToolData(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileSessionDataResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
     private:
      friend class Stub;
      explicit experimental_async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class experimental_async_interface* experimental_async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class experimental_async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::tensorflow::NewProfileSessionResponse>* AsyncNewSessionRaw(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::NewProfileSessionResponse>* PrepareAsyncNewSessionRaw(::grpc::ClientContext* context, const ::tensorflow::NewProfileSessionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::EnumProfileSessionsAndToolsResponse>* AsyncEnumSessionsRaw(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::EnumProfileSessionsAndToolsResponse>* PrepareAsyncEnumSessionsRaw(::grpc::ClientContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileSessionDataResponse>* AsyncGetSessionToolDataRaw(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileSessionDataResponse>* PrepareAsyncGetSessionToolDataRaw(::grpc::ClientContext* context, const ::tensorflow::ProfileSessionDataRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_NewSession_;
    const ::grpc::internal::RpcMethod rpcmethod_EnumSessions_;
    const ::grpc::internal::RpcMethod rpcmethod_GetSessionToolData_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Starts a profiling session, blocks until it completes.
    // TPUProfileAnalysis service delegate this to TPUProfiler service.
    // Populate the profiled data in repository, then return status to caller.
    virtual ::grpc::Status NewSession(::grpc::ServerContext* context, const ::tensorflow::NewProfileSessionRequest* request, ::tensorflow::NewProfileSessionResponse* response);
    // Enumerate existing sessions and return available profile tools.
    virtual ::grpc::Status EnumSessions(::grpc::ServerContext* context, const ::tensorflow::EnumProfileSessionsAndToolsRequest* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response);
    // Retrieve specific tool's data for specific session.
    virtual ::grpc::Status GetSessionToolData(::grpc::ServerContext* context, const ::tensorflow::ProfileSessionDataRequest* request, ::tensorflow::ProfileSessionDataResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_NewSession : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_NewSession() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_NewSession() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status NewSession(::grpc::ServerContext* /*context*/, const ::tensorflow::NewProfileSessionRequest* /*request*/, ::tensorflow::NewProfileSessionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestNewSession(::grpc::ServerContext* context, ::tensorflow::NewProfileSessionRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::NewProfileSessionResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_EnumSessions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_EnumSessions() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_EnumSessions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EnumSessions(::grpc::ServerContext* /*context*/, const ::tensorflow::EnumProfileSessionsAndToolsRequest* /*request*/, ::tensorflow::EnumProfileSessionsAndToolsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestEnumSessions(::grpc::ServerContext* context, ::tensorflow::EnumProfileSessionsAndToolsRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::EnumProfileSessionsAndToolsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetSessionToolData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetSessionToolData() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetSessionToolData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSessionToolData(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileSessionDataRequest* /*request*/, ::tensorflow::ProfileSessionDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSessionToolData(::grpc::ServerContext* context, ::tensorflow::ProfileSessionDataRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::ProfileSessionDataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_NewSession<WithAsyncMethod_EnumSessions<WithAsyncMethod_GetSessionToolData<Service > > > AsyncService;
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_NewSession : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_NewSession() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::NewProfileSessionRequest, ::tensorflow::NewProfileSessionResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::NewProfileSessionRequest* request, ::tensorflow::NewProfileSessionResponse* response) { return this->NewSession(context, request, response); }));}
    void SetMessageAllocatorFor_NewSession(
        ::grpc::experimental::MessageAllocator< ::tensorflow::NewProfileSessionRequest, ::tensorflow::NewProfileSessionResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(0);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::NewProfileSessionRequest, ::tensorflow::NewProfileSessionResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_NewSession() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status NewSession(::grpc::ServerContext* /*context*/, const ::tensorflow::NewProfileSessionRequest* /*request*/, ::tensorflow::NewProfileSessionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* NewSession(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::NewProfileSessionRequest* /*request*/, ::tensorflow::NewProfileSessionResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* NewSession(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::NewProfileSessionRequest* /*request*/, ::tensorflow::NewProfileSessionResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_EnumSessions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_EnumSessions() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(1,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::EnumProfileSessionsAndToolsRequest, ::tensorflow::EnumProfileSessionsAndToolsResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::EnumProfileSessionsAndToolsRequest* request, ::tensorflow::EnumProfileSessionsAndToolsResponse* response) { return this->EnumSessions(context, request, response); }));}
    void SetMessageAllocatorFor_EnumSessions(
        ::grpc::experimental::MessageAllocator< ::tensorflow::EnumProfileSessionsAndToolsRequest, ::tensorflow::EnumProfileSessionsAndToolsResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(1);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::EnumProfileSessionsAndToolsRequest, ::tensorflow::EnumProfileSessionsAndToolsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_EnumSessions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EnumSessions(::grpc::ServerContext* /*context*/, const ::tensorflow::EnumProfileSessionsAndToolsRequest* /*request*/, ::tensorflow::EnumProfileSessionsAndToolsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* EnumSessions(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::EnumProfileSessionsAndToolsRequest* /*request*/, ::tensorflow::EnumProfileSessionsAndToolsResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* EnumSessions(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::EnumProfileSessionsAndToolsRequest* /*request*/, ::tensorflow::EnumProfileSessionsAndToolsResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_GetSessionToolData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_GetSessionToolData() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(2,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ProfileSessionDataRequest, ::tensorflow::ProfileSessionDataResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::ProfileSessionDataRequest* request, ::tensorflow::ProfileSessionDataResponse* response) { return this->GetSessionToolData(context, request, response); }));}
    void SetMessageAllocatorFor_GetSessionToolData(
        ::grpc::experimental::MessageAllocator< ::tensorflow::ProfileSessionDataRequest, ::tensorflow::ProfileSessionDataResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(2);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ProfileSessionDataRequest, ::tensorflow::ProfileSessionDataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_GetSessionToolData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSessionToolData(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileSessionDataRequest* /*request*/, ::tensorflow::ProfileSessionDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* GetSessionToolData(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::ProfileSessionDataRequest* /*request*/, ::tensorflow::ProfileSessionDataResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* GetSessionToolData(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::ProfileSessionDataRequest* /*request*/, ::tensorflow::ProfileSessionDataResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
  typedef ExperimentalWithCallbackMethod_NewSession<ExperimentalWithCallbackMethod_EnumSessions<ExperimentalWithCallbackMethod_GetSessionToolData<Service > > > CallbackService;
  #endif

  typedef ExperimentalWithCallbackMethod_NewSession<ExperimentalWithCallbackMethod_EnumSessions<ExperimentalWithCallbackMethod_GetSessionToolData<Service > > > ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_NewSession : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_NewSession() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_NewSession() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status NewSession(::grpc::ServerContext* /*context*/, const ::tensorflow::NewProfileSessionRequest* /*request*/, ::tensorflow::NewProfileSessionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_EnumSessions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_EnumSessions() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_EnumSessions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EnumSessions(::grpc::ServerContext* /*context*/, const ::tensorflow::EnumProfileSessionsAndToolsRequest* /*request*/, ::tensorflow::EnumProfileSessionsAndToolsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetSessionToolData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetSessionToolData() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetSessionToolData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSessionToolData(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileSessionDataRequest* /*request*/, ::tensorflow::ProfileSessionDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_NewSession : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_NewSession() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_NewSession() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status NewSession(::grpc::ServerContext* /*context*/, const ::tensorflow::NewProfileSessionRequest* /*request*/, ::tensorflow::NewProfileSessionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestNewSession(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_EnumSessions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_EnumSessions() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_EnumSessions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EnumSessions(::grpc::ServerContext* /*context*/, const ::tensorflow::EnumProfileSessionsAndToolsRequest* /*request*/, ::tensorflow::EnumProfileSessionsAndToolsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestEnumSessions(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetSessionToolData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetSessionToolData() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetSessionToolData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSessionToolData(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileSessionDataRequest* /*request*/, ::tensorflow::ProfileSessionDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSessionToolData(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_NewSession : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_NewSession() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->NewSession(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_NewSession() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status NewSession(::grpc::ServerContext* /*context*/, const ::tensorflow::NewProfileSessionRequest* /*request*/, ::tensorflow::NewProfileSessionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* NewSession(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* NewSession(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_EnumSessions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_EnumSessions() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(1,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->EnumSessions(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_EnumSessions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EnumSessions(::grpc::ServerContext* /*context*/, const ::tensorflow::EnumProfileSessionsAndToolsRequest* /*request*/, ::tensorflow::EnumProfileSessionsAndToolsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* EnumSessions(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* EnumSessions(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_GetSessionToolData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_GetSessionToolData() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(2,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetSessionToolData(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_GetSessionToolData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSessionToolData(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileSessionDataRequest* /*request*/, ::tensorflow::ProfileSessionDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* GetSessionToolData(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* GetSessionToolData(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_NewSession : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_NewSession() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::NewProfileSessionRequest, ::tensorflow::NewProfileSessionResponse>(std::bind(&WithStreamedUnaryMethod_NewSession<BaseClass>::StreamedNewSession, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_NewSession() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status NewSession(::grpc::ServerContext* /*context*/, const ::tensorflow::NewProfileSessionRequest* /*request*/, ::tensorflow::NewProfileSessionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedNewSession(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::NewProfileSessionRequest,::tensorflow::NewProfileSessionResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_EnumSessions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_EnumSessions() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::EnumProfileSessionsAndToolsRequest, ::tensorflow::EnumProfileSessionsAndToolsResponse>(std::bind(&WithStreamedUnaryMethod_EnumSessions<BaseClass>::StreamedEnumSessions, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_EnumSessions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status EnumSessions(::grpc::ServerContext* /*context*/, const ::tensorflow::EnumProfileSessionsAndToolsRequest* /*request*/, ::tensorflow::EnumProfileSessionsAndToolsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedEnumSessions(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::EnumProfileSessionsAndToolsRequest,::tensorflow::EnumProfileSessionsAndToolsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetSessionToolData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetSessionToolData() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::ProfileSessionDataRequest, ::tensorflow::ProfileSessionDataResponse>(std::bind(&WithStreamedUnaryMethod_GetSessionToolData<BaseClass>::StreamedGetSessionToolData, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetSessionToolData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetSessionToolData(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileSessionDataRequest* /*request*/, ::tensorflow::ProfileSessionDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetSessionToolData(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::ProfileSessionDataRequest,::tensorflow::ProfileSessionDataResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_NewSession<WithStreamedUnaryMethod_EnumSessions<WithStreamedUnaryMethod_GetSessionToolData<Service > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_NewSession<WithStreamedUnaryMethod_EnumSessions<WithStreamedUnaryMethod_GetSessionToolData<Service > > > StreamedService;
};

}  // namespace grpc

}  // namespace tensorflow


#endif  // GRPC_tensorflow_2fcore_2fprofiler_2fprofiler_5fanalysis_2eproto__INCLUDED
