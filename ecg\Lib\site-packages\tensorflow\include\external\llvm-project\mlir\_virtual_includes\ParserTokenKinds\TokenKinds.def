//===- TokenKinds.def - MLIR Token Description ------------------*- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//
//
// This file is intended to be #include'd multiple times to extract information
// about tokens for various clients in the lexer.
//
//===----------------------------------------------------------------------===//

#if !defined(TOK_MARKER) && !defined(TOK_IDENTIFIER) &&                        \
    !defined(TOK_LITERAL) && !defined(TOK_PUNCTUATION) &&                      \
    !defined(TOK_KEYWORD)
#error Must define one of the TOK_ macros.
#endif

#ifndef TOK_MARKER
#define TOK_MARKER(X)
#endif
#ifndef TOK_IDENTIFIER
#define TOK_IDENTIFIER(NAME)
#endif
#ifndef TOK_LITERAL
#define TOK_LITERAL(NAME)
#endif
#ifndef TOK_PUNCTUATION
#define TOK_PUNCTUATION(NAME, SPELLING)
#endif
#ifndef TOK_KEYWORD
#define TOK_KEYWORD(SPELLING)
#endif

// Markers
TOK_MARKER(eof)
TOK_MARKER(error)

// Identifiers.
TOK_IDENTIFIER(bare_identifier)        // foo
TOK_IDENTIFIER(at_identifier)          // @foo
TOK_IDENTIFIER(hash_identifier)        // #foo
TOK_IDENTIFIER(percent_identifier)     // %foo
TOK_IDENTIFIER(caret_identifier)       // ^foo
TOK_IDENTIFIER(exclamation_identifier) // !foo

// Literals
TOK_LITERAL(floatliteral) // 2.0
TOK_LITERAL(integer)      // 42
TOK_LITERAL(string)       // "foo"
TOK_LITERAL(inttype)      // i4, si8, ui16

// Punctuation.
TOK_PUNCTUATION(arrow, "->")
TOK_PUNCTUATION(at, "@")
TOK_PUNCTUATION(colon, ":")
TOK_PUNCTUATION(comma, ",")
TOK_PUNCTUATION(ellipsis, "...")
TOK_PUNCTUATION(equal, "=")
TOK_PUNCTUATION(greater, ">")
TOK_PUNCTUATION(l_brace, "{")
TOK_PUNCTUATION(l_paren, "(")
TOK_PUNCTUATION(l_square, "[")
TOK_PUNCTUATION(less, "<")
TOK_PUNCTUATION(minus, "-")
TOK_PUNCTUATION(plus, "+")
TOK_PUNCTUATION(question, "?")
TOK_PUNCTUATION(r_brace, "}")
TOK_PUNCTUATION(r_paren, ")")
TOK_PUNCTUATION(r_square, "]")
TOK_PUNCTUATION(star, "*")

// Keywords.  These turn "foo" into Token::kw_foo enums.

// NOTE: Please key these alphabetized to make it easier to find something in
// this list and to cater to OCD.
TOK_KEYWORD(affine_map)
TOK_KEYWORD(affine_set)
TOK_KEYWORD(attributes)
TOK_KEYWORD(bf16)
TOK_KEYWORD(ceildiv)
TOK_KEYWORD(complex)
TOK_KEYWORD(dense)
TOK_KEYWORD(f16)
TOK_KEYWORD(f32)
TOK_KEYWORD(f64)
TOK_KEYWORD(f80)
TOK_KEYWORD(f128)
TOK_KEYWORD(false)
TOK_KEYWORD(floordiv)
TOK_KEYWORD(for)
TOK_KEYWORD(func)
TOK_KEYWORD(index)
TOK_KEYWORD(loc)
TOK_KEYWORD(max)
TOK_KEYWORD(memref)
TOK_KEYWORD(min)
TOK_KEYWORD(mod)
TOK_KEYWORD(none)
TOK_KEYWORD(offset)
TOK_KEYWORD(opaque)
TOK_KEYWORD(size)
TOK_KEYWORD(sparse)
TOK_KEYWORD(step)
TOK_KEYWORD(strides)
TOK_KEYWORD(symbol)
TOK_KEYWORD(tensor)
TOK_KEYWORD(to)
TOK_KEYWORD(true)
TOK_KEYWORD(tuple)
TOK_KEYWORD(type)
TOK_KEYWORD(unit)
TOK_KEYWORD(vector)

#undef TOK_MARKER
#undef TOK_IDENTIFIER
#undef TOK_LITERAL
#undef TOK_PUNCTUATION
#undef TOK_KEYWORD
