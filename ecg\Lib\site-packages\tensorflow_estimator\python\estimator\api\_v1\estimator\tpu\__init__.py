# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf_estimator.python.estimator.api._v1.estimator.tpu namespace
"""

import sys as _sys

from tensorflow_estimator.python.estimator.api._v1.estimator.tpu import experimental
from tensorflow_estimator.python.estimator.tpu.tpu_config import InputPipelineConfig # line: 36
from tensorflow_estimator.python.estimator.tpu.tpu_config import RunConfig # line: 237
from tensorflow_estimator.python.estimator.tpu.tpu_config import TPUConfig # line: 54
from tensorflow_estimator.python.estimator.tpu.tpu_estimator import TPUEstimator # line: 2457
from tensorflow_estimator.python.estimator.tpu.tpu_estimator import TPUEstimatorSpec # line: 282

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "estimator.tpu", public_apis=None, deprecation=True,
      has_lite=False)
