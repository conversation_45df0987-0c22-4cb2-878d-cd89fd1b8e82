import traceback

import numpy as np
from scipy.signal import welch
from pyhrv.time_domain import nn50, rmssd

from apps.utils.logger_helper import <PERSON><PERSON>


def process(waveform_info):
    """
    心电信号SNA检测
    :param waveform_info: 心电波形信息
    :return: True or False
    """
    try:
        waveform = waveform_info['waveform']
        hrv_nonlinear = waveform_info['hrv']['nonlinear']

        rr_intervals = waveform['rr_intervals']
        rr_cv = waveform['rr_cv']
        mean_rr = np.mean(rr_intervals) if len(rr_intervals) > 0 else 0.0

        try:
            pnn50_local = nn50(rr_intervals)['pnn50']
            rmssd_local = rmssd(rr_intervals)['rmssd']
        except Exception as e:
            Logger().warning(f"SNA: Error calculating local pnn50/rmssd: {e}")
            pnn50_local = 0
            rmssd_local = 0

        sd_ratio = hrv_nonlinear['sd_ratio']

        rr_changes = np.abs(np.diff(rr_intervals) / rr_intervals[:-1] * 100)
        max_change = np.max(rr_changes) if len(rr_changes) > 0 else 0.0

        score = 0

        if 0.06 <= rr_cv < 0.10:
            score += 1
        elif 0.10 <= rr_cv < 0.15:
            score += 2
        elif rr_cv >= 0.15:
            score += 3

        rr_diffs = np.abs(np.diff(rr_intervals))
        consecutive_large_changes = np.sum(rr_diffs > 0.12)
        if consecutive_large_changes >= 3:
            score += 2

        if len(rr_intervals) >= 5:
            freqs, psd = welch(rr_intervals, nperseg=min(len(rr_intervals), 256))
            lf_power = np.sum(psd[(freqs >= 0.04) & (freqs <= 0.15)])
            hf_power = np.sum(psd[(freqs >= 0.15) & (freqs <= 0.4)])
            lf_hf_ratio = lf_power / hf_power if hf_power > 0 else 0

            if lf_hf_ratio > 2.5:
                score += 2

        if 0.3 <= sd_ratio <= 0.7:
            score += 0
        elif sd_ratio > 0.7:
            score += 2

        if pnn50_local >= 15 and rmssd_local >= 0.07:
            score += 2

        is_sna = score >= 7

        if is_sna:
            if max_change > 50 or rr_cv > 0.25:
                is_sna = False

            mean_hr = 60 / mean_rr if mean_rr > 0 else 0
            if mean_hr > 100 or mean_hr < 50:
                is_sna = False

        return bool(is_sna)
    except Exception:
        Logger().error(f'SNA诊断异常：\n{traceback.format_exc()}')
        return False


