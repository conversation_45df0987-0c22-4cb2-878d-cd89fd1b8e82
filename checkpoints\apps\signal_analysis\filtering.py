import signal

from scipy.signal import medfilt, butter, lfilter, filtfilt
from scipy.signal.windows import gaussian
from sklearn.metrics import mean_squared_error
import pywt
import numpy as np


def wavelet_filter(ecg_signal, sampling_rate, wavelet='sym5', level=3,
                              cutoff=0.5, median_kernel=3, threshold_scale=0.8):
    """
    增强型心电图滤波函数，结合小波去噪、中值滤波和基线漂移校正

    参数：
    ecg_signal : numpy.ndarray
        输入的心电图信号
    sampling_rate : float
        采样频率 (Hz)
    wavelet : str
        小波基函数，默认使用'sym5'
    level : int
        小波分解层数，默认为5
    cutoff : float
        高通滤波器截止频率 (Hz)，默认为0.5Hz
    median_kernel : int
        中值滤波核大小，默认为3
    threshold_scale : float
        小波阈值缩放因子，默认为0.8

    返回：
    filtered_ecg : numpy.ndarray
        滤波后的心电图信号
    """
    # 第一步：中值滤波去除突刺噪声
    median_filtered = medfilt(ecg_signal, kernel_size=median_kernel)

    # 第二步：小波去噪
    coeffs = pywt.wavedec(median_filtered, wavelet, level=level)

    # 改进的阈值计算方法
    sigma = np.median(np.abs(coeffs[-level])) / 0.6745
    universal_thresh = sigma * np.sqrt(2 * np.log(len(ecg_signal)))

    # 对各层细节系数进行阈值处理
    for i in range(1, len(coeffs)):
        coeffs[i] = pywt.threshold(coeffs[i], threshold_scale * universal_thresh)

    # 第三步：基线漂移校正
    # 结合高通滤波
    wav_filtered = pywt.waverec(coeffs, wavelet)

    # 设计高通滤波器
    nyq = 0.5 * sampling_rate
    normal_cutoff = cutoff / nyq
    b, a = butter(4, normal_cutoff, btype='highpass')

    # 应用零相位滤波
    baseline_corrected = filtfilt(b, a, wav_filtered)

    # 确保信号长度一致
    if len(baseline_corrected) != len(ecg_signal):
        baseline_corrected = baseline_corrected[:len(ecg_signal)]

    return baseline_corrected


def calculate_snr(original_signal, filtered_signal):
    """
    计算信噪比（SNR）

    参数：
    original_signal : numpy.ndarray
        原始心电图信号
    filtered_signal : numpy.ndarray
        滤波后的心电图信号

    返回：
    snr : float
        信噪比，单位为dB
    """
    noise = original_signal - filtered_signal
    signal_power = np.mean(original_signal ** 2)
    noise_power = np.mean(noise ** 2)
    snr = 10 * np.log10(signal_power / noise_power)
    return snr


def calculate_rmse(original_signal, filtered_signal):
    """
    计算均方根误差（RMSE）

    参数：
    original_signal : numpy.ndarray
        原始心电图信号
    filtered_signal : numpy.ndarray
        滤波后的心电图信号

    返回：
    rmse : float
        均方根误差
    """
    rmse = np.sqrt(mean_squared_error(original_signal, filtered_signal))
    return rmse


def calculate_correlation_coefficient(original_signal, filtered_signal):
    """
    计算相关系数（CC）

    参数：
    original_signal : numpy.ndarray
        原始心电图信号
    filtered_signal : numpy.ndarray
        滤波后的心电图信号

    返回：
    cc : float
        相关系数
    """
    cc = np.corrcoef(original_signal, filtered_signal)[0, 1]
    return cc
