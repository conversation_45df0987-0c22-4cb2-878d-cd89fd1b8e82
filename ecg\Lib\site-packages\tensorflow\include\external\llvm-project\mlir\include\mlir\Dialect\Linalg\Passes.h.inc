/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// ConvertElementwiseToLinalg
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertElementwiseToLinalgBase : public ::mlir::FunctionPass {
public:
  using Base = ConvertElementwiseToLinalgBase;

  ConvertElementwiseToLinalgBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ConvertElementwiseToLinalgBase(const ConvertElementwiseToLinalgBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-elementwise-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "convert-elementwise-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Convert ElementwiseMappable ops to linalg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertElementwiseToLinalg");
  }
  ::llvm::StringRef getName() const override { return "ConvertElementwiseToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgBufferizeBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgBufferizeBase;

  LinalgBufferizeBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgBufferizeBase(const LinalgBufferizeBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgBufferize");
  }
  ::llvm::StringRef getName() const override { return "LinalgBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgComprehensiveFuncBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgComprehensiveFuncBufferizeBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgComprehensiveFuncBufferizeBase;

  LinalgComprehensiveFuncBufferizeBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgComprehensiveFuncBufferizeBase(const LinalgComprehensiveFuncBufferizeBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-comprehensive-func-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-comprehensive-func-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize (tensor into memref) the body of a FuncOp and try to reuse the buffers for those arguments that a) have been annotated 'inplaceable' and b) whose buffer uses would be free of memory hazards"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgComprehensiveFuncBufferize");
  }
  ::llvm::StringRef getName() const override { return "LinalgComprehensiveFuncBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> testAnalysisOnly{*this, "test-analysis-only", ::llvm::cl::desc("Only runs inplaceability analysis (for testing purposes only)"), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LinalgDetensorize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgDetensorizeBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgDetensorizeBase;

  LinalgDetensorizeBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgDetensorizeBase(const LinalgDetensorizeBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-detensorize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-detensorize"; }

  ::llvm::StringRef getDescription() const override { return "Detensorize linalg ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgDetensorize");
  }
  ::llvm::StringRef getName() const override { return "LinalgDetensorize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgFoldReshapeOpsByLinearization
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgFoldReshapeOpsByLinearizationBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgFoldReshapeOpsByLinearizationBase;

  LinalgFoldReshapeOpsByLinearizationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgFoldReshapeOpsByLinearizationBase(const LinalgFoldReshapeOpsByLinearizationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fold-reshape-ops-by-linearization");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fold-reshape-ops-by-linearization"; }

  ::llvm::StringRef getDescription() const override { return "Fold TensorReshapeOps with generic/indexed generic ops by linearization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgFoldReshapeOpsByLinearization");
  }
  ::llvm::StringRef getName() const override { return "LinalgFoldReshapeOpsByLinearization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgFoldUnitExtentDims
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgFoldUnitExtentDimsBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgFoldUnitExtentDimsBase;

  LinalgFoldUnitExtentDimsBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgFoldUnitExtentDimsBase(const LinalgFoldUnitExtentDimsBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fold-unit-extent-dims");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fold-unit-extent-dims"; }

  ::llvm::StringRef getDescription() const override { return "Remove unit-extent dimension in Linalg ops on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgFoldUnitExtentDims");
  }
  ::llvm::StringRef getName() const override { return "LinalgFoldUnitExtentDims"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> foldOneTripLoopsOnly{*this, "fold-one-trip-loops-only", ::llvm::cl::desc("Only folds the one-trip loops from Linalg ops on tensors (for testing purposes only)"), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LinalgFusionOfTensorOps
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgFusionOfTensorOpsBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgFusionOfTensorOpsBase;

  LinalgFusionOfTensorOpsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgFusionOfTensorOpsBase(const LinalgFusionOfTensorOpsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fusion-for-tensor-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fusion-for-tensor-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fuse operations on RankedTensorType in linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgFusionOfTensorOps");
  }
  ::llvm::StringRef getName() const override { return "LinalgFusionOfTensorOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> allowFoldingUnitDimReshapes{*this, "allow-folding-unit-dim-reshapes", ::llvm::cl::desc("Allow fusing linalg.tensor_reshape ops that performs unit dimension collapsing"), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LinalgGeneralization
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgGeneralizationBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgGeneralizationBase;

  LinalgGeneralizationBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgGeneralizationBase(const LinalgGeneralizationBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-generalize-named-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-generalize-named-ops"; }

  ::llvm::StringRef getDescription() const override { return "Convert named ops into generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgGeneralization");
  }
  ::llvm::StringRef getName() const override { return "LinalgGeneralization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgInlineScalarOperands
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgInlineScalarOperandsBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgInlineScalarOperandsBase;

  LinalgInlineScalarOperandsBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgInlineScalarOperandsBase(const LinalgInlineScalarOperandsBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-inline-scalar-operands");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-inline-scalar-operands"; }

  ::llvm::StringRef getDescription() const override { return "Inline scalar operands into linalg generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgInlineScalarOperands");
  }
  ::llvm::StringRef getName() const override { return "LinalgInlineScalarOperands"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgLowerTiledLoopsToSCF
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgLowerTiledLoopsToSCFBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgLowerTiledLoopsToSCFBase;

  LinalgLowerTiledLoopsToSCFBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerTiledLoopsToSCFBase(const LinalgLowerTiledLoopsToSCFBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-tiled-loops-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-tiled-loops-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower linalg tiled loops to SCF loops and parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerTiledLoopsToSCF");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerTiledLoopsToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<AffineDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgLowerToAffineLoops
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgLowerToAffineLoopsBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgLowerToAffineLoopsBase;

  LinalgLowerToAffineLoopsBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToAffineLoopsBase(const LinalgLowerToAffineLoopsBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-affine-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-affine-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToAffineLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToAffineLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgLowerToLoops
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgLowerToLoopsBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgLowerToLoopsBase;

  LinalgLowerToLoopsBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToLoopsBase(const LinalgLowerToLoopsBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<AffineDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgLowerToParallelLoops
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgLowerToParallelLoopsBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgLowerToParallelLoopsBase;

  LinalgLowerToParallelLoopsBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToParallelLoopsBase(const LinalgLowerToParallelLoopsBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-parallel-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-parallel-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToParallelLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToParallelLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgPromotion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgPromotionBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgPromotionBase;

  LinalgPromotionBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgPromotionBase(const LinalgPromotionBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-promote-subviews");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-promote-subviews"; }

  ::llvm::StringRef getDescription() const override { return "Promote subview ops to local buffers"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgPromotion");
  }
  ::llvm::StringRef getName() const override { return "LinalgPromotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> dynamicBuffers{*this, "test-promote-dynamic", ::llvm::cl::desc("Test generation of dynamic promoted buffers"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useAlloca{*this, "test-use-alloca", ::llvm::cl::desc("Test generation of alloca'ed buffers."), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LinalgTiling
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgTilingBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgTilingBase;

  LinalgTilingBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgTilingBase(const LinalgTilingBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-tile");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-tile"; }

  ::llvm::StringRef getDescription() const override { return "Tile operations in the linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgTiling");
  }
  ::llvm::StringRef getName() const override { return "LinalgTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
  ::mlir::Pass::ListOption<int64_t> tileSizes{*this, "linalg-tile-sizes", ::llvm::cl::desc("Tile sizes"), llvm::cl::ZeroOrMore, llvm::cl::MiscFlags::CommaSeparated};
};

//===----------------------------------------------------------------------===//
// LinalgTilingToParallelLoops
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgTilingToParallelLoopsBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgTilingToParallelLoopsBase;

  LinalgTilingToParallelLoopsBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgTilingToParallelLoopsBase(const LinalgTilingToParallelLoopsBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-tile-to-parallel-loops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-tile-to-parallel-loops"; }

  ::llvm::StringRef getDescription() const override { return "Tile operations in the linalg dialect to parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgTilingToParallelLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgTilingToParallelLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
  ::mlir::Pass::ListOption<int64_t> tileSizes{*this, "linalg-tile-sizes", ::llvm::cl::desc("Tile sizes"), llvm::cl::ZeroOrMore, llvm::cl::MiscFlags::CommaSeparated};
};

//===----------------------------------------------------------------------===//
// LinalgTilingToTiledLoops
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgTilingToTiledLoopsBase : public ::mlir::FunctionPass {
public:
  using Base = LinalgTilingToTiledLoopsBase;

  LinalgTilingToTiledLoopsBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LinalgTilingToTiledLoopsBase(const LinalgTilingToTiledLoopsBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-tile-to-tiled-loop");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-tile-to-tiled-loop"; }

  ::llvm::StringRef getDescription() const override { return "Tile operations in the linalg dialect to linalg.tiled_loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgTilingToTiledLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgTilingToTiledLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
  ::mlir::Pass::ListOption<int64_t> tileSizes{*this, "linalg-tile-sizes", ::llvm::cl::desc("Tile sizes"), llvm::cl::ZeroOrMore, llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::ListOption<std::string> distributionTypes{*this, "linalg-distribution-types", ::llvm::cl::desc("DistributionTypes"), llvm::cl::ZeroOrMore, llvm::cl::MiscFlags::CommaSeparated};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ConvertElementwiseToLinalg Registration
//===----------------------------------------------------------------------===//

inline void registerConvertElementwiseToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertElementwiseToLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgComprehensiveFuncBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgComprehensiveFuncBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgComprehensiveFuncBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgDetensorize Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgDetensorizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgDetensorizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgFoldReshapeOpsByLinearization Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgFoldReshapeOpsByLinearizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createFoldReshapeOpsByLinearizationPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgFoldUnitExtentDims Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgFoldUnitExtentDimsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgFoldUnitExtentDimsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgFusionOfTensorOps Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgFusionOfTensorOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgFusionOfTensorOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgGeneralization Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgGeneralizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgGeneralizationPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgInlineScalarOperands Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgInlineScalarOperandsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgInlineScalarOperandsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgLowerTiledLoopsToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgLowerTiledLoopsToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgTiledLoopsToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgLowerToAffineLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgLowerToAffineLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToAffineLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgLowerToLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgLowerToLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgLowerToParallelLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgLowerToParallelLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToParallelLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgPromotion Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgPromotionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgPromotionPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgTiling Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgTilingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgTilingPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgTilingToParallelLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgTilingToParallelLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgTilingToParallelLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgTilingToTiledLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgTilingToTiledLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgTilingToTiledLoopPass();
  });
}

//===----------------------------------------------------------------------===//
// Linalg Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgPasses() {
  registerConvertElementwiseToLinalgPass();
  registerLinalgBufferizePass();
  registerLinalgComprehensiveFuncBufferizePass();
  registerLinalgDetensorizePass();
  registerLinalgFoldReshapeOpsByLinearizationPass();
  registerLinalgFoldUnitExtentDimsPass();
  registerLinalgFusionOfTensorOpsPass();
  registerLinalgGeneralizationPass();
  registerLinalgInlineScalarOperandsPass();
  registerLinalgLowerTiledLoopsToSCFPass();
  registerLinalgLowerToAffineLoopsPass();
  registerLinalgLowerToLoopsPass();
  registerLinalgLowerToParallelLoopsPass();
  registerLinalgPromotionPass();
  registerLinalgTilingPass();
  registerLinalgTilingToParallelLoopsPass();
  registerLinalgTilingToTiledLoopsPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
