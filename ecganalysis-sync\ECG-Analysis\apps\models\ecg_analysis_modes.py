# 实体生成：python manage.py inspectdb [表名]
from django.db import models


class TCustom(models.Model):
    custom_name = models.CharField(max_length=255, blank=True, null=True)
    salt = models.CharField(max_length=255, blank=True, null=True)
    custom_no = models.CharField(max_length=255, blank=True, null=True)
    custom_secret = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 't_custom'


class TAnalysisLog(models.Model):
    custom_id = models.IntegerField(blank=True, null=True)
    analysis_info_path = models.CharField(max_length=255, blank=True, null=True)
    create_date = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 't_analysis_log'


class TWhitelist(models.Model):
    union_id = models.CharField(max_length=255, blank=True, null=True)
    ecg_age = models.Integer<PERSON>ield(blank=True, null=True)
    arrhythmia_diagnosis = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    create_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 't_whitelist'


class TSysConfig(models.Model):
    config_name = models.CharField(max_length=255, blank=True, null=True)
    config_value = models.CharField(max_length=255, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 't_sys_config'


class TGravityInfo(models.Model):
    union_id = models.CharField(max_length=255, blank=True, null=True)
    gravity = models.TextField(blank=True, null=True)
    motion_state = models.CharField(max_length=50, blank=True, null=True)
    features = models.CharField(max_length=500, blank=True, null=True)
    tumble_history = models.CharField(max_length=5000, blank=True, null=True)
    create_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 't_gravity_info'
