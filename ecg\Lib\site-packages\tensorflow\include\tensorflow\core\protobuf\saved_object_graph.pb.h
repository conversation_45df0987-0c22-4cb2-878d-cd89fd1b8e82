// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/saved_object_graph.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/framework/variable.pb.h"
#include "tensorflow/core/framework/versions.pb.h"
#include "tensorflow/core/protobuf/struct.pb.h"
#include "tensorflow/core/protobuf/trackable_object_graph.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[15]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
namespace tensorflow {
class CapturedTensor;
class CapturedTensorDefaultTypeInternal;
extern CapturedTensorDefaultTypeInternal _CapturedTensor_default_instance_;
class FunctionSpec;
class FunctionSpecDefaultTypeInternal;
extern FunctionSpecDefaultTypeInternal _FunctionSpec_default_instance_;
class SaveableObject;
class SaveableObjectDefaultTypeInternal;
extern SaveableObjectDefaultTypeInternal _SaveableObject_default_instance_;
class SavedAsset;
class SavedAssetDefaultTypeInternal;
extern SavedAssetDefaultTypeInternal _SavedAsset_default_instance_;
class SavedBareConcreteFunction;
class SavedBareConcreteFunctionDefaultTypeInternal;
extern SavedBareConcreteFunctionDefaultTypeInternal _SavedBareConcreteFunction_default_instance_;
class SavedConcreteFunction;
class SavedConcreteFunctionDefaultTypeInternal;
extern SavedConcreteFunctionDefaultTypeInternal _SavedConcreteFunction_default_instance_;
class SavedConstant;
class SavedConstantDefaultTypeInternal;
extern SavedConstantDefaultTypeInternal _SavedConstant_default_instance_;
class SavedFunction;
class SavedFunctionDefaultTypeInternal;
extern SavedFunctionDefaultTypeInternal _SavedFunction_default_instance_;
class SavedObject;
class SavedObjectDefaultTypeInternal;
extern SavedObjectDefaultTypeInternal _SavedObject_default_instance_;
class SavedObjectGraph;
class SavedObjectGraphDefaultTypeInternal;
extern SavedObjectGraphDefaultTypeInternal _SavedObjectGraph_default_instance_;
class SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse;
class SavedObjectGraph_ConcreteFunctionsEntry_DoNotUseDefaultTypeInternal;
extern SavedObjectGraph_ConcreteFunctionsEntry_DoNotUseDefaultTypeInternal _SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse_default_instance_;
class SavedObject_SaveableObjectsEntry_DoNotUse;
class SavedObject_SaveableObjectsEntry_DoNotUseDefaultTypeInternal;
extern SavedObject_SaveableObjectsEntry_DoNotUseDefaultTypeInternal _SavedObject_SaveableObjectsEntry_DoNotUse_default_instance_;
class SavedResource;
class SavedResourceDefaultTypeInternal;
extern SavedResourceDefaultTypeInternal _SavedResource_default_instance_;
class SavedUserObject;
class SavedUserObjectDefaultTypeInternal;
extern SavedUserObjectDefaultTypeInternal _SavedUserObject_default_instance_;
class SavedVariable;
class SavedVariableDefaultTypeInternal;
extern SavedVariableDefaultTypeInternal _SavedVariable_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CapturedTensor* Arena::CreateMaybeMessage<::tensorflow::CapturedTensor>(Arena*);
template<> ::tensorflow::FunctionSpec* Arena::CreateMaybeMessage<::tensorflow::FunctionSpec>(Arena*);
template<> ::tensorflow::SaveableObject* Arena::CreateMaybeMessage<::tensorflow::SaveableObject>(Arena*);
template<> ::tensorflow::SavedAsset* Arena::CreateMaybeMessage<::tensorflow::SavedAsset>(Arena*);
template<> ::tensorflow::SavedBareConcreteFunction* Arena::CreateMaybeMessage<::tensorflow::SavedBareConcreteFunction>(Arena*);
template<> ::tensorflow::SavedConcreteFunction* Arena::CreateMaybeMessage<::tensorflow::SavedConcreteFunction>(Arena*);
template<> ::tensorflow::SavedConstant* Arena::CreateMaybeMessage<::tensorflow::SavedConstant>(Arena*);
template<> ::tensorflow::SavedFunction* Arena::CreateMaybeMessage<::tensorflow::SavedFunction>(Arena*);
template<> ::tensorflow::SavedObject* Arena::CreateMaybeMessage<::tensorflow::SavedObject>(Arena*);
template<> ::tensorflow::SavedObjectGraph* Arena::CreateMaybeMessage<::tensorflow::SavedObjectGraph>(Arena*);
template<> ::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::SavedObject_SaveableObjectsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::SavedObject_SaveableObjectsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::SavedResource* Arena::CreateMaybeMessage<::tensorflow::SavedResource>(Arena*);
template<> ::tensorflow::SavedUserObject* Arena::CreateMaybeMessage<::tensorflow::SavedUserObject>(Arena*);
template<> ::tensorflow::SavedVariable* Arena::CreateMaybeMessage<::tensorflow::SavedVariable>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum FunctionSpec_JitCompile : int {
  FunctionSpec_JitCompile_DEFAULT = 0,
  FunctionSpec_JitCompile_ON = 1,
  FunctionSpec_JitCompile_OFF = 2,
  FunctionSpec_JitCompile_FunctionSpec_JitCompile_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  FunctionSpec_JitCompile_FunctionSpec_JitCompile_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool FunctionSpec_JitCompile_IsValid(int value);
constexpr FunctionSpec_JitCompile FunctionSpec_JitCompile_JitCompile_MIN = FunctionSpec_JitCompile_DEFAULT;
constexpr FunctionSpec_JitCompile FunctionSpec_JitCompile_JitCompile_MAX = FunctionSpec_JitCompile_OFF;
constexpr int FunctionSpec_JitCompile_JitCompile_ARRAYSIZE = FunctionSpec_JitCompile_JitCompile_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FunctionSpec_JitCompile_descriptor();
template<typename T>
inline const std::string& FunctionSpec_JitCompile_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FunctionSpec_JitCompile>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FunctionSpec_JitCompile_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FunctionSpec_JitCompile_descriptor(), enum_t_value);
}
inline bool FunctionSpec_JitCompile_Parse(
    const std::string& name, FunctionSpec_JitCompile* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FunctionSpec_JitCompile>(
    FunctionSpec_JitCompile_descriptor(), name, value);
}
// ===================================================================

class SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse, 
    std::string, ::tensorflow::SavedConcreteFunction,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse, 
    std::string, ::tensorflow::SavedConcreteFunction,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse();
  SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse& other);
  static const SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse*>(&_SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.SavedObjectGraph.ConcreteFunctionsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[0];
  }

  public:
};

// -------------------------------------------------------------------

class SavedObjectGraph :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedObjectGraph) */ {
 public:
  SavedObjectGraph();
  virtual ~SavedObjectGraph();

  SavedObjectGraph(const SavedObjectGraph& from);
  SavedObjectGraph(SavedObjectGraph&& from) noexcept
    : SavedObjectGraph() {
    *this = ::std::move(from);
  }

  inline SavedObjectGraph& operator=(const SavedObjectGraph& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedObjectGraph& operator=(SavedObjectGraph&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedObjectGraph& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedObjectGraph* internal_default_instance() {
    return reinterpret_cast<const SavedObjectGraph*>(
               &_SavedObjectGraph_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SavedObjectGraph& a, SavedObjectGraph& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedObjectGraph* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedObjectGraph* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedObjectGraph* New() const final {
    return CreateMaybeMessage<SavedObjectGraph>(nullptr);
  }

  SavedObjectGraph* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedObjectGraph>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedObjectGraph& from);
  void MergeFrom(const SavedObjectGraph& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedObjectGraph* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedObjectGraph";
  }
  protected:
  explicit SavedObjectGraph(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kNodesFieldNumber = 1,
    kConcreteFunctionsFieldNumber = 2,
  };
  // repeated .tensorflow.SavedObject nodes = 1;
  int nodes_size() const;
  void clear_nodes();
  ::tensorflow::SavedObject* mutable_nodes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedObject >*
      mutable_nodes();
  const ::tensorflow::SavedObject& nodes(int index) const;
  ::tensorflow::SavedObject* add_nodes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedObject >&
      nodes() const;

  // map<string, .tensorflow.SavedConcreteFunction> concrete_functions = 2;
  int concrete_functions_size() const;
  void clear_concrete_functions();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >&
      concrete_functions() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >*
      mutable_concrete_functions();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedObjectGraph)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedObject > nodes_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse,
      std::string, ::tensorflow::SavedConcreteFunction,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > concrete_functions_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedObject_SaveableObjectsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SavedObject_SaveableObjectsEntry_DoNotUse, 
    std::string, ::tensorflow::SaveableObject,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SavedObject_SaveableObjectsEntry_DoNotUse, 
    std::string, ::tensorflow::SaveableObject,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  SavedObject_SaveableObjectsEntry_DoNotUse();
  SavedObject_SaveableObjectsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const SavedObject_SaveableObjectsEntry_DoNotUse& other);
  static const SavedObject_SaveableObjectsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const SavedObject_SaveableObjectsEntry_DoNotUse*>(&_SavedObject_SaveableObjectsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.SavedObject.SaveableObjectsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[2];
  }

  public:
};

// -------------------------------------------------------------------

class SavedObject :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedObject) */ {
 public:
  SavedObject();
  virtual ~SavedObject();

  SavedObject(const SavedObject& from);
  SavedObject(SavedObject&& from) noexcept
    : SavedObject() {
    *this = ::std::move(from);
  }

  inline SavedObject& operator=(const SavedObject& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedObject& operator=(SavedObject&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedObject& default_instance();

  enum KindCase {
    kUserObject = 4,
    kAsset = 5,
    kFunction = 6,
    kVariable = 7,
    kBareConcreteFunction = 8,
    kConstant = 9,
    kResource = 10,
    kCapturedTensor = 12,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedObject* internal_default_instance() {
    return reinterpret_cast<const SavedObject*>(
               &_SavedObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SavedObject& a, SavedObject& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedObject* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedObject* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedObject* New() const final {
    return CreateMaybeMessage<SavedObject>(nullptr);
  }

  SavedObject* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedObject>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedObject& from);
  void MergeFrom(const SavedObject& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedObject* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedObject";
  }
  protected:
  explicit SavedObject(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kChildrenFieldNumber = 1,
    kSlotVariablesFieldNumber = 3,
    kSaveableObjectsFieldNumber = 11,
    kUserObjectFieldNumber = 4,
    kAssetFieldNumber = 5,
    kFunctionFieldNumber = 6,
    kVariableFieldNumber = 7,
    kBareConcreteFunctionFieldNumber = 8,
    kConstantFieldNumber = 9,
    kResourceFieldNumber = 10,
    kCapturedTensorFieldNumber = 12,
  };
  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  int children_size() const;
  void clear_children();
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* mutable_children(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
      mutable_children();
  const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& children(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* add_children();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
      children() const;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  int slot_variables_size() const;
  void clear_slot_variables();
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* mutable_slot_variables(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
      mutable_slot_variables();
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& slot_variables(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* add_slot_variables();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
      slot_variables() const;

  // map<string, .tensorflow.SaveableObject> saveable_objects = 11;
  int saveable_objects_size() const;
  void clear_saveable_objects();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >&
      saveable_objects() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >*
      mutable_saveable_objects();

  // .tensorflow.SavedUserObject user_object = 4;
  bool has_user_object() const;
  void clear_user_object();
  const ::tensorflow::SavedUserObject& user_object() const;
  ::tensorflow::SavedUserObject* release_user_object();
  ::tensorflow::SavedUserObject* mutable_user_object();
  void set_allocated_user_object(::tensorflow::SavedUserObject* user_object);
  void unsafe_arena_set_allocated_user_object(
      ::tensorflow::SavedUserObject* user_object);
  ::tensorflow::SavedUserObject* unsafe_arena_release_user_object();

  // .tensorflow.SavedAsset asset = 5;
  bool has_asset() const;
  void clear_asset();
  const ::tensorflow::SavedAsset& asset() const;
  ::tensorflow::SavedAsset* release_asset();
  ::tensorflow::SavedAsset* mutable_asset();
  void set_allocated_asset(::tensorflow::SavedAsset* asset);
  void unsafe_arena_set_allocated_asset(
      ::tensorflow::SavedAsset* asset);
  ::tensorflow::SavedAsset* unsafe_arena_release_asset();

  // .tensorflow.SavedFunction function = 6;
  bool has_function() const;
  void clear_function();
  const ::tensorflow::SavedFunction& function() const;
  ::tensorflow::SavedFunction* release_function();
  ::tensorflow::SavedFunction* mutable_function();
  void set_allocated_function(::tensorflow::SavedFunction* function);
  void unsafe_arena_set_allocated_function(
      ::tensorflow::SavedFunction* function);
  ::tensorflow::SavedFunction* unsafe_arena_release_function();

  // .tensorflow.SavedVariable variable = 7;
  bool has_variable() const;
  void clear_variable();
  const ::tensorflow::SavedVariable& variable() const;
  ::tensorflow::SavedVariable* release_variable();
  ::tensorflow::SavedVariable* mutable_variable();
  void set_allocated_variable(::tensorflow::SavedVariable* variable);
  void unsafe_arena_set_allocated_variable(
      ::tensorflow::SavedVariable* variable);
  ::tensorflow::SavedVariable* unsafe_arena_release_variable();

  // .tensorflow.SavedBareConcreteFunction bare_concrete_function = 8;
  bool has_bare_concrete_function() const;
  void clear_bare_concrete_function();
  const ::tensorflow::SavedBareConcreteFunction& bare_concrete_function() const;
  ::tensorflow::SavedBareConcreteFunction* release_bare_concrete_function();
  ::tensorflow::SavedBareConcreteFunction* mutable_bare_concrete_function();
  void set_allocated_bare_concrete_function(::tensorflow::SavedBareConcreteFunction* bare_concrete_function);
  void unsafe_arena_set_allocated_bare_concrete_function(
      ::tensorflow::SavedBareConcreteFunction* bare_concrete_function);
  ::tensorflow::SavedBareConcreteFunction* unsafe_arena_release_bare_concrete_function();

  // .tensorflow.SavedConstant constant = 9;
  bool has_constant() const;
  void clear_constant();
  const ::tensorflow::SavedConstant& constant() const;
  ::tensorflow::SavedConstant* release_constant();
  ::tensorflow::SavedConstant* mutable_constant();
  void set_allocated_constant(::tensorflow::SavedConstant* constant);
  void unsafe_arena_set_allocated_constant(
      ::tensorflow::SavedConstant* constant);
  ::tensorflow::SavedConstant* unsafe_arena_release_constant();

  // .tensorflow.SavedResource resource = 10;
  bool has_resource() const;
  void clear_resource();
  const ::tensorflow::SavedResource& resource() const;
  ::tensorflow::SavedResource* release_resource();
  ::tensorflow::SavedResource* mutable_resource();
  void set_allocated_resource(::tensorflow::SavedResource* resource);
  void unsafe_arena_set_allocated_resource(
      ::tensorflow::SavedResource* resource);
  ::tensorflow::SavedResource* unsafe_arena_release_resource();

  // .tensorflow.CapturedTensor captured_tensor = 12;
  bool has_captured_tensor() const;
  void clear_captured_tensor();
  const ::tensorflow::CapturedTensor& captured_tensor() const;
  ::tensorflow::CapturedTensor* release_captured_tensor();
  ::tensorflow::CapturedTensor* mutable_captured_tensor();
  void set_allocated_captured_tensor(::tensorflow::CapturedTensor* captured_tensor);
  void unsafe_arena_set_allocated_captured_tensor(
      ::tensorflow::CapturedTensor* captured_tensor);
  ::tensorflow::CapturedTensor* unsafe_arena_release_captured_tensor();

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.SavedObject)
 private:
  class _Internal;
  void set_has_user_object();
  void set_has_asset();
  void set_has_function();
  void set_has_variable();
  void set_has_bare_concrete_function();
  void set_has_constant();
  void set_has_resource();
  void set_has_captured_tensor();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference > children_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference > slot_variables_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      SavedObject_SaveableObjectsEntry_DoNotUse,
      std::string, ::tensorflow::SaveableObject,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > saveable_objects_;
  union KindUnion {
    KindUnion() {}
    ::tensorflow::SavedUserObject* user_object_;
    ::tensorflow::SavedAsset* asset_;
    ::tensorflow::SavedFunction* function_;
    ::tensorflow::SavedVariable* variable_;
    ::tensorflow::SavedBareConcreteFunction* bare_concrete_function_;
    ::tensorflow::SavedConstant* constant_;
    ::tensorflow::SavedResource* resource_;
    ::tensorflow::CapturedTensor* captured_tensor_;
  } kind_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedUserObject :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedUserObject) */ {
 public:
  SavedUserObject();
  virtual ~SavedUserObject();

  SavedUserObject(const SavedUserObject& from);
  SavedUserObject(SavedUserObject&& from) noexcept
    : SavedUserObject() {
    *this = ::std::move(from);
  }

  inline SavedUserObject& operator=(const SavedUserObject& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedUserObject& operator=(SavedUserObject&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedUserObject& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedUserObject* internal_default_instance() {
    return reinterpret_cast<const SavedUserObject*>(
               &_SavedUserObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SavedUserObject& a, SavedUserObject& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedUserObject* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedUserObject* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedUserObject* New() const final {
    return CreateMaybeMessage<SavedUserObject>(nullptr);
  }

  SavedUserObject* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedUserObject>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedUserObject& from);
  void MergeFrom(const SavedUserObject& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedUserObject* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedUserObject";
  }
  protected:
  explicit SavedUserObject(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdentifierFieldNumber = 1,
    kMetadataFieldNumber = 3,
    kVersionFieldNumber = 2,
  };
  // string identifier = 1;
  void clear_identifier();
  const std::string& identifier() const;
  void set_identifier(const std::string& value);
  void set_identifier(std::string&& value);
  void set_identifier(const char* value);
  void set_identifier(const char* value, size_t size);
  std::string* mutable_identifier();
  std::string* release_identifier();
  void set_allocated_identifier(std::string* identifier);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_identifier();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_identifier(
      std::string* identifier);

  // string metadata = 3 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_metadata();
  PROTOBUF_DEPRECATED const std::string& metadata() const;
  PROTOBUF_DEPRECATED void set_metadata(const std::string& value);
  PROTOBUF_DEPRECATED void set_metadata(std::string&& value);
  PROTOBUF_DEPRECATED void set_metadata(const char* value);
  PROTOBUF_DEPRECATED void set_metadata(const char* value, size_t size);
  PROTOBUF_DEPRECATED std::string* mutable_metadata();
  PROTOBUF_DEPRECATED std::string* release_metadata();
  PROTOBUF_DEPRECATED void set_allocated_metadata(std::string* metadata);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_metadata();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_metadata(
      std::string* metadata);

  // .tensorflow.VersionDef version = 2;
  bool has_version() const;
  void clear_version();
  const ::tensorflow::VersionDef& version() const;
  ::tensorflow::VersionDef* release_version();
  ::tensorflow::VersionDef* mutable_version();
  void set_allocated_version(::tensorflow::VersionDef* version);
  void unsafe_arena_set_allocated_version(
      ::tensorflow::VersionDef* version);
  ::tensorflow::VersionDef* unsafe_arena_release_version();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedUserObject)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr identifier_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr metadata_;
  ::tensorflow::VersionDef* version_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedAsset :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedAsset) */ {
 public:
  SavedAsset();
  virtual ~SavedAsset();

  SavedAsset(const SavedAsset& from);
  SavedAsset(SavedAsset&& from) noexcept
    : SavedAsset() {
    *this = ::std::move(from);
  }

  inline SavedAsset& operator=(const SavedAsset& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedAsset& operator=(SavedAsset&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedAsset& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedAsset* internal_default_instance() {
    return reinterpret_cast<const SavedAsset*>(
               &_SavedAsset_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(SavedAsset& a, SavedAsset& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedAsset* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedAsset* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedAsset* New() const final {
    return CreateMaybeMessage<SavedAsset>(nullptr);
  }

  SavedAsset* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedAsset>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedAsset& from);
  void MergeFrom(const SavedAsset& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedAsset* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedAsset";
  }
  protected:
  explicit SavedAsset(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAssetFileDefIndexFieldNumber = 1,
  };
  // int32 asset_file_def_index = 1;
  void clear_asset_file_def_index();
  ::PROTOBUF_NAMESPACE_ID::int32 asset_file_def_index() const;
  void set_asset_file_def_index(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedAsset)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int32 asset_file_def_index_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedFunction :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedFunction) */ {
 public:
  SavedFunction();
  virtual ~SavedFunction();

  SavedFunction(const SavedFunction& from);
  SavedFunction(SavedFunction&& from) noexcept
    : SavedFunction() {
    *this = ::std::move(from);
  }

  inline SavedFunction& operator=(const SavedFunction& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedFunction& operator=(SavedFunction&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedFunction& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedFunction* internal_default_instance() {
    return reinterpret_cast<const SavedFunction*>(
               &_SavedFunction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(SavedFunction& a, SavedFunction& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedFunction* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedFunction* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedFunction* New() const final {
    return CreateMaybeMessage<SavedFunction>(nullptr);
  }

  SavedFunction* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedFunction>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedFunction& from);
  void MergeFrom(const SavedFunction& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedFunction* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedFunction";
  }
  protected:
  explicit SavedFunction(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConcreteFunctionsFieldNumber = 1,
    kFunctionSpecFieldNumber = 2,
  };
  // repeated string concrete_functions = 1;
  int concrete_functions_size() const;
  void clear_concrete_functions();
  const std::string& concrete_functions(int index) const;
  std::string* mutable_concrete_functions(int index);
  void set_concrete_functions(int index, const std::string& value);
  void set_concrete_functions(int index, std::string&& value);
  void set_concrete_functions(int index, const char* value);
  void set_concrete_functions(int index, const char* value, size_t size);
  std::string* add_concrete_functions();
  void add_concrete_functions(const std::string& value);
  void add_concrete_functions(std::string&& value);
  void add_concrete_functions(const char* value);
  void add_concrete_functions(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& concrete_functions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_concrete_functions();

  // .tensorflow.FunctionSpec function_spec = 2;
  bool has_function_spec() const;
  void clear_function_spec();
  const ::tensorflow::FunctionSpec& function_spec() const;
  ::tensorflow::FunctionSpec* release_function_spec();
  ::tensorflow::FunctionSpec* mutable_function_spec();
  void set_allocated_function_spec(::tensorflow::FunctionSpec* function_spec);
  void unsafe_arena_set_allocated_function_spec(
      ::tensorflow::FunctionSpec* function_spec);
  ::tensorflow::FunctionSpec* unsafe_arena_release_function_spec();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedFunction)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> concrete_functions_;
  ::tensorflow::FunctionSpec* function_spec_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CapturedTensor :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CapturedTensor) */ {
 public:
  CapturedTensor();
  virtual ~CapturedTensor();

  CapturedTensor(const CapturedTensor& from);
  CapturedTensor(CapturedTensor&& from) noexcept
    : CapturedTensor() {
    *this = ::std::move(from);
  }

  inline CapturedTensor& operator=(const CapturedTensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline CapturedTensor& operator=(CapturedTensor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CapturedTensor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CapturedTensor* internal_default_instance() {
    return reinterpret_cast<const CapturedTensor*>(
               &_CapturedTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(CapturedTensor& a, CapturedTensor& b) {
    a.Swap(&b);
  }
  inline void Swap(CapturedTensor* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CapturedTensor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CapturedTensor* New() const final {
    return CreateMaybeMessage<CapturedTensor>(nullptr);
  }

  CapturedTensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CapturedTensor>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CapturedTensor& from);
  void MergeFrom(const CapturedTensor& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CapturedTensor* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CapturedTensor";
  }
  protected:
  explicit CapturedTensor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kConcreteFunctionFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string concrete_function = 2;
  void clear_concrete_function();
  const std::string& concrete_function() const;
  void set_concrete_function(const std::string& value);
  void set_concrete_function(std::string&& value);
  void set_concrete_function(const char* value);
  void set_concrete_function(const char* value, size_t size);
  std::string* mutable_concrete_function();
  std::string* release_concrete_function();
  void set_allocated_concrete_function(std::string* concrete_function);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_concrete_function();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_concrete_function(
      std::string* concrete_function);

  // @@protoc_insertion_point(class_scope:tensorflow.CapturedTensor)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr concrete_function_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedConcreteFunction :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedConcreteFunction) */ {
 public:
  SavedConcreteFunction();
  virtual ~SavedConcreteFunction();

  SavedConcreteFunction(const SavedConcreteFunction& from);
  SavedConcreteFunction(SavedConcreteFunction&& from) noexcept
    : SavedConcreteFunction() {
    *this = ::std::move(from);
  }

  inline SavedConcreteFunction& operator=(const SavedConcreteFunction& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedConcreteFunction& operator=(SavedConcreteFunction&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedConcreteFunction& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedConcreteFunction* internal_default_instance() {
    return reinterpret_cast<const SavedConcreteFunction*>(
               &_SavedConcreteFunction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(SavedConcreteFunction& a, SavedConcreteFunction& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedConcreteFunction* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedConcreteFunction* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedConcreteFunction* New() const final {
    return CreateMaybeMessage<SavedConcreteFunction>(nullptr);
  }

  SavedConcreteFunction* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedConcreteFunction>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedConcreteFunction& from);
  void MergeFrom(const SavedConcreteFunction& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedConcreteFunction* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedConcreteFunction";
  }
  protected:
  explicit SavedConcreteFunction(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBoundInputsFieldNumber = 2,
    kCanonicalizedInputSignatureFieldNumber = 3,
    kOutputSignatureFieldNumber = 4,
  };
  // repeated int32 bound_inputs = 2;
  int bound_inputs_size() const;
  void clear_bound_inputs();
  ::PROTOBUF_NAMESPACE_ID::int32 bound_inputs(int index) const;
  void set_bound_inputs(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_bound_inputs(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      bound_inputs() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_bound_inputs();

  // .tensorflow.StructuredValue canonicalized_input_signature = 3;
  bool has_canonicalized_input_signature() const;
  void clear_canonicalized_input_signature();
  const ::tensorflow::StructuredValue& canonicalized_input_signature() const;
  ::tensorflow::StructuredValue* release_canonicalized_input_signature();
  ::tensorflow::StructuredValue* mutable_canonicalized_input_signature();
  void set_allocated_canonicalized_input_signature(::tensorflow::StructuredValue* canonicalized_input_signature);
  void unsafe_arena_set_allocated_canonicalized_input_signature(
      ::tensorflow::StructuredValue* canonicalized_input_signature);
  ::tensorflow::StructuredValue* unsafe_arena_release_canonicalized_input_signature();

  // .tensorflow.StructuredValue output_signature = 4;
  bool has_output_signature() const;
  void clear_output_signature();
  const ::tensorflow::StructuredValue& output_signature() const;
  ::tensorflow::StructuredValue* release_output_signature();
  ::tensorflow::StructuredValue* mutable_output_signature();
  void set_allocated_output_signature(::tensorflow::StructuredValue* output_signature);
  void unsafe_arena_set_allocated_output_signature(
      ::tensorflow::StructuredValue* output_signature);
  ::tensorflow::StructuredValue* unsafe_arena_release_output_signature();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedConcreteFunction)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > bound_inputs_;
  mutable std::atomic<int> _bound_inputs_cached_byte_size_;
  ::tensorflow::StructuredValue* canonicalized_input_signature_;
  ::tensorflow::StructuredValue* output_signature_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedBareConcreteFunction :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedBareConcreteFunction) */ {
 public:
  SavedBareConcreteFunction();
  virtual ~SavedBareConcreteFunction();

  SavedBareConcreteFunction(const SavedBareConcreteFunction& from);
  SavedBareConcreteFunction(SavedBareConcreteFunction&& from) noexcept
    : SavedBareConcreteFunction() {
    *this = ::std::move(from);
  }

  inline SavedBareConcreteFunction& operator=(const SavedBareConcreteFunction& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedBareConcreteFunction& operator=(SavedBareConcreteFunction&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedBareConcreteFunction& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedBareConcreteFunction* internal_default_instance() {
    return reinterpret_cast<const SavedBareConcreteFunction*>(
               &_SavedBareConcreteFunction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(SavedBareConcreteFunction& a, SavedBareConcreteFunction& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedBareConcreteFunction* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedBareConcreteFunction* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedBareConcreteFunction* New() const final {
    return CreateMaybeMessage<SavedBareConcreteFunction>(nullptr);
  }

  SavedBareConcreteFunction* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedBareConcreteFunction>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedBareConcreteFunction& from);
  void MergeFrom(const SavedBareConcreteFunction& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedBareConcreteFunction* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedBareConcreteFunction";
  }
  protected:
  explicit SavedBareConcreteFunction(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArgumentKeywordsFieldNumber = 2,
    kConcreteFunctionNameFieldNumber = 1,
    kFunctionSpecFieldNumber = 4,
    kAllowedPositionalArgumentsFieldNumber = 3,
  };
  // repeated string argument_keywords = 2;
  int argument_keywords_size() const;
  void clear_argument_keywords();
  const std::string& argument_keywords(int index) const;
  std::string* mutable_argument_keywords(int index);
  void set_argument_keywords(int index, const std::string& value);
  void set_argument_keywords(int index, std::string&& value);
  void set_argument_keywords(int index, const char* value);
  void set_argument_keywords(int index, const char* value, size_t size);
  std::string* add_argument_keywords();
  void add_argument_keywords(const std::string& value);
  void add_argument_keywords(std::string&& value);
  void add_argument_keywords(const char* value);
  void add_argument_keywords(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& argument_keywords() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_argument_keywords();

  // string concrete_function_name = 1;
  void clear_concrete_function_name();
  const std::string& concrete_function_name() const;
  void set_concrete_function_name(const std::string& value);
  void set_concrete_function_name(std::string&& value);
  void set_concrete_function_name(const char* value);
  void set_concrete_function_name(const char* value, size_t size);
  std::string* mutable_concrete_function_name();
  std::string* release_concrete_function_name();
  void set_allocated_concrete_function_name(std::string* concrete_function_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_concrete_function_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_concrete_function_name(
      std::string* concrete_function_name);

  // .tensorflow.FunctionSpec function_spec = 4;
  bool has_function_spec() const;
  void clear_function_spec();
  const ::tensorflow::FunctionSpec& function_spec() const;
  ::tensorflow::FunctionSpec* release_function_spec();
  ::tensorflow::FunctionSpec* mutable_function_spec();
  void set_allocated_function_spec(::tensorflow::FunctionSpec* function_spec);
  void unsafe_arena_set_allocated_function_spec(
      ::tensorflow::FunctionSpec* function_spec);
  ::tensorflow::FunctionSpec* unsafe_arena_release_function_spec();

  // int64 allowed_positional_arguments = 3;
  void clear_allowed_positional_arguments();
  ::PROTOBUF_NAMESPACE_ID::int64 allowed_positional_arguments() const;
  void set_allowed_positional_arguments(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedBareConcreteFunction)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> argument_keywords_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr concrete_function_name_;
  ::tensorflow::FunctionSpec* function_spec_;
  ::PROTOBUF_NAMESPACE_ID::int64 allowed_positional_arguments_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedConstant :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedConstant) */ {
 public:
  SavedConstant();
  virtual ~SavedConstant();

  SavedConstant(const SavedConstant& from);
  SavedConstant(SavedConstant&& from) noexcept
    : SavedConstant() {
    *this = ::std::move(from);
  }

  inline SavedConstant& operator=(const SavedConstant& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedConstant& operator=(SavedConstant&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedConstant& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedConstant* internal_default_instance() {
    return reinterpret_cast<const SavedConstant*>(
               &_SavedConstant_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(SavedConstant& a, SavedConstant& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedConstant* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedConstant* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedConstant* New() const final {
    return CreateMaybeMessage<SavedConstant>(nullptr);
  }

  SavedConstant* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedConstant>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedConstant& from);
  void MergeFrom(const SavedConstant& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedConstant* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedConstant";
  }
  protected:
  explicit SavedConstant(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperationFieldNumber = 1,
  };
  // string operation = 1;
  void clear_operation();
  const std::string& operation() const;
  void set_operation(const std::string& value);
  void set_operation(std::string&& value);
  void set_operation(const char* value);
  void set_operation(const char* value, size_t size);
  std::string* mutable_operation();
  std::string* release_operation();
  void set_allocated_operation(std::string* operation);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_operation();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_operation(
      std::string* operation);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedConstant)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr operation_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedVariable :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedVariable) */ {
 public:
  SavedVariable();
  virtual ~SavedVariable();

  SavedVariable(const SavedVariable& from);
  SavedVariable(SavedVariable&& from) noexcept
    : SavedVariable() {
    *this = ::std::move(from);
  }

  inline SavedVariable& operator=(const SavedVariable& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedVariable& operator=(SavedVariable&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedVariable& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedVariable* internal_default_instance() {
    return reinterpret_cast<const SavedVariable*>(
               &_SavedVariable_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(SavedVariable& a, SavedVariable& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedVariable* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedVariable* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedVariable* New() const final {
    return CreateMaybeMessage<SavedVariable>(nullptr);
  }

  SavedVariable* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedVariable>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedVariable& from);
  void MergeFrom(const SavedVariable& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedVariable* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedVariable";
  }
  protected:
  explicit SavedVariable(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExperimentalDistributedVariableComponentsFieldNumber = 8,
    kNameFieldNumber = 6,
    kDeviceFieldNumber = 7,
    kShapeFieldNumber = 2,
    kDtypeFieldNumber = 1,
    kTrainableFieldNumber = 3,
    kSynchronizationFieldNumber = 4,
    kAggregationFieldNumber = 5,
  };
  // repeated .tensorflow.SavedVariable experimental_distributed_variable_components = 8;
  int experimental_distributed_variable_components_size() const;
  void clear_experimental_distributed_variable_components();
  ::tensorflow::SavedVariable* mutable_experimental_distributed_variable_components(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedVariable >*
      mutable_experimental_distributed_variable_components();
  const ::tensorflow::SavedVariable& experimental_distributed_variable_components(int index) const;
  ::tensorflow::SavedVariable* add_experimental_distributed_variable_components();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedVariable >&
      experimental_distributed_variable_components() const;

  // string name = 6;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string device = 7;
  void clear_device();
  const std::string& device() const;
  void set_device(const std::string& value);
  void set_device(std::string&& value);
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  std::string* mutable_device();
  std::string* release_device();
  void set_allocated_device(std::string* device);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device(
      std::string* device);

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // bool trainable = 3;
  void clear_trainable();
  bool trainable() const;
  void set_trainable(bool value);

  // .tensorflow.VariableSynchronization synchronization = 4;
  void clear_synchronization();
  ::tensorflow::VariableSynchronization synchronization() const;
  void set_synchronization(::tensorflow::VariableSynchronization value);

  // .tensorflow.VariableAggregation aggregation = 5;
  void clear_aggregation();
  ::tensorflow::VariableAggregation aggregation() const;
  void set_aggregation(::tensorflow::VariableAggregation value);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedVariable)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedVariable > experimental_distributed_variable_components_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
  ::tensorflow::TensorShapeProto* shape_;
  int dtype_;
  bool trainable_;
  int synchronization_;
  int aggregation_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class FunctionSpec :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionSpec) */ {
 public:
  FunctionSpec();
  virtual ~FunctionSpec();

  FunctionSpec(const FunctionSpec& from);
  FunctionSpec(FunctionSpec&& from) noexcept
    : FunctionSpec() {
    *this = ::std::move(from);
  }

  inline FunctionSpec& operator=(const FunctionSpec& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionSpec& operator=(FunctionSpec&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FunctionSpec& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FunctionSpec* internal_default_instance() {
    return reinterpret_cast<const FunctionSpec*>(
               &_FunctionSpec_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(FunctionSpec& a, FunctionSpec& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionSpec* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionSpec* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FunctionSpec* New() const final {
    return CreateMaybeMessage<FunctionSpec>(nullptr);
  }

  FunctionSpec* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FunctionSpec>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FunctionSpec& from);
  void MergeFrom(const FunctionSpec& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionSpec* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FunctionSpec";
  }
  protected:
  explicit FunctionSpec(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef FunctionSpec_JitCompile JitCompile;
  static constexpr JitCompile DEFAULT =
    FunctionSpec_JitCompile_DEFAULT;
  static constexpr JitCompile ON =
    FunctionSpec_JitCompile_ON;
  static constexpr JitCompile OFF =
    FunctionSpec_JitCompile_OFF;
  static inline bool JitCompile_IsValid(int value) {
    return FunctionSpec_JitCompile_IsValid(value);
  }
  static constexpr JitCompile JitCompile_MIN =
    FunctionSpec_JitCompile_JitCompile_MIN;
  static constexpr JitCompile JitCompile_MAX =
    FunctionSpec_JitCompile_JitCompile_MAX;
  static constexpr int JitCompile_ARRAYSIZE =
    FunctionSpec_JitCompile_JitCompile_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  JitCompile_descriptor() {
    return FunctionSpec_JitCompile_descriptor();
  }
  template<typename T>
  static inline const std::string& JitCompile_Name(T enum_t_value) {
    static_assert(::std::is_same<T, JitCompile>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function JitCompile_Name.");
    return FunctionSpec_JitCompile_Name(enum_t_value);
  }
  static inline bool JitCompile_Parse(const std::string& name,
      JitCompile* value) {
    return FunctionSpec_JitCompile_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kFullargspecFieldNumber = 1,
    kInputSignatureFieldNumber = 5,
    kIsMethodFieldNumber = 2,
    kJitCompileFieldNumber = 6,
  };
  // .tensorflow.StructuredValue fullargspec = 1;
  bool has_fullargspec() const;
  void clear_fullargspec();
  const ::tensorflow::StructuredValue& fullargspec() const;
  ::tensorflow::StructuredValue* release_fullargspec();
  ::tensorflow::StructuredValue* mutable_fullargspec();
  void set_allocated_fullargspec(::tensorflow::StructuredValue* fullargspec);
  void unsafe_arena_set_allocated_fullargspec(
      ::tensorflow::StructuredValue* fullargspec);
  ::tensorflow::StructuredValue* unsafe_arena_release_fullargspec();

  // .tensorflow.StructuredValue input_signature = 5;
  bool has_input_signature() const;
  void clear_input_signature();
  const ::tensorflow::StructuredValue& input_signature() const;
  ::tensorflow::StructuredValue* release_input_signature();
  ::tensorflow::StructuredValue* mutable_input_signature();
  void set_allocated_input_signature(::tensorflow::StructuredValue* input_signature);
  void unsafe_arena_set_allocated_input_signature(
      ::tensorflow::StructuredValue* input_signature);
  ::tensorflow::StructuredValue* unsafe_arena_release_input_signature();

  // bool is_method = 2;
  void clear_is_method();
  bool is_method() const;
  void set_is_method(bool value);

  // .tensorflow.FunctionSpec.JitCompile jit_compile = 6;
  void clear_jit_compile();
  ::tensorflow::FunctionSpec_JitCompile jit_compile() const;
  void set_jit_compile(::tensorflow::FunctionSpec_JitCompile value);

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionSpec)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::StructuredValue* fullargspec_;
  ::tensorflow::StructuredValue* input_signature_;
  bool is_method_;
  int jit_compile_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedResource :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedResource) */ {
 public:
  SavedResource();
  virtual ~SavedResource();

  SavedResource(const SavedResource& from);
  SavedResource(SavedResource&& from) noexcept
    : SavedResource() {
    *this = ::std::move(from);
  }

  inline SavedResource& operator=(const SavedResource& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedResource& operator=(SavedResource&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SavedResource& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedResource* internal_default_instance() {
    return reinterpret_cast<const SavedResource*>(
               &_SavedResource_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(SavedResource& a, SavedResource& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedResource* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedResource* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SavedResource* New() const final {
    return CreateMaybeMessage<SavedResource>(nullptr);
  }

  SavedResource* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SavedResource>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SavedResource& from);
  void MergeFrom(const SavedResource& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedResource* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedResource";
  }
  protected:
  explicit SavedResource(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceFieldNumber = 1,
  };
  // string device = 1;
  void clear_device();
  const std::string& device() const;
  void set_device(const std::string& value);
  void set_device(std::string&& value);
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  std::string* mutable_device();
  std::string* release_device();
  void set_allocated_device(std::string* device);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device(
      std::string* device);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedResource)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SaveableObject :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SaveableObject) */ {
 public:
  SaveableObject();
  virtual ~SaveableObject();

  SaveableObject(const SaveableObject& from);
  SaveableObject(SaveableObject&& from) noexcept
    : SaveableObject() {
    *this = ::std::move(from);
  }

  inline SaveableObject& operator=(const SaveableObject& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveableObject& operator=(SaveableObject&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SaveableObject& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SaveableObject* internal_default_instance() {
    return reinterpret_cast<const SaveableObject*>(
               &_SaveableObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(SaveableObject& a, SaveableObject& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveableObject* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveableObject* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SaveableObject* New() const final {
    return CreateMaybeMessage<SaveableObject>(nullptr);
  }

  SaveableObject* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SaveableObject>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SaveableObject& from);
  void MergeFrom(const SaveableObject& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveableObject* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SaveableObject";
  }
  protected:
  explicit SaveableObject(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSaveFunctionFieldNumber = 2,
    kRestoreFunctionFieldNumber = 3,
  };
  // int32 save_function = 2;
  void clear_save_function();
  ::PROTOBUF_NAMESPACE_ID::int32 save_function() const;
  void set_save_function(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 restore_function = 3;
  void clear_restore_function();
  ::PROTOBUF_NAMESPACE_ID::int32 restore_function() const;
  void set_restore_function(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.SaveableObject)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int32 save_function_;
  ::PROTOBUF_NAMESPACE_ID::int32 restore_function_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// SavedObjectGraph

// repeated .tensorflow.SavedObject nodes = 1;
inline int SavedObjectGraph::nodes_size() const {
  return nodes_.size();
}
inline void SavedObjectGraph::clear_nodes() {
  nodes_.Clear();
}
inline ::tensorflow::SavedObject* SavedObjectGraph::mutable_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObjectGraph.nodes)
  return nodes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedObject >*
SavedObjectGraph::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedObjectGraph.nodes)
  return &nodes_;
}
inline const ::tensorflow::SavedObject& SavedObjectGraph::nodes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObjectGraph.nodes)
  return nodes_.Get(index);
}
inline ::tensorflow::SavedObject* SavedObjectGraph::add_nodes() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedObjectGraph.nodes)
  return nodes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedObject >&
SavedObjectGraph::nodes() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedObjectGraph.nodes)
  return nodes_;
}

// map<string, .tensorflow.SavedConcreteFunction> concrete_functions = 2;
inline int SavedObjectGraph::concrete_functions_size() const {
  return concrete_functions_.size();
}
inline void SavedObjectGraph::clear_concrete_functions() {
  concrete_functions_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >&
SavedObjectGraph::concrete_functions() const {
  // @@protoc_insertion_point(field_map:tensorflow.SavedObjectGraph.concrete_functions)
  return concrete_functions_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >*
SavedObjectGraph::mutable_concrete_functions() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.SavedObjectGraph.concrete_functions)
  return concrete_functions_.MutableMap();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// SavedObject

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
inline int SavedObject::children_size() const {
  return children_.size();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* SavedObject::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.children)
  return children_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
SavedObject::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedObject.children)
  return &children_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& SavedObject::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.children)
  return children_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* SavedObject::add_children() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedObject.children)
  return children_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
SavedObject::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedObject.children)
  return children_;
}

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
inline int SavedObject::slot_variables_size() const {
  return slot_variables_.size();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* SavedObject::mutable_slot_variables(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.slot_variables)
  return slot_variables_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
SavedObject::mutable_slot_variables() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedObject.slot_variables)
  return &slot_variables_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& SavedObject::slot_variables(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.slot_variables)
  return slot_variables_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* SavedObject::add_slot_variables() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedObject.slot_variables)
  return slot_variables_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
SavedObject::slot_variables() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedObject.slot_variables)
  return slot_variables_;
}

// .tensorflow.SavedUserObject user_object = 4;
inline bool SavedObject::has_user_object() const {
  return kind_case() == kUserObject;
}
inline void SavedObject::set_has_user_object() {
  _oneof_case_[0] = kUserObject;
}
inline void SavedObject::clear_user_object() {
  if (has_user_object()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.user_object_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedUserObject* SavedObject::release_user_object() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.user_object)
  if (has_user_object()) {
    clear_has_kind();
      ::tensorflow::SavedUserObject* temp = kind_.user_object_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.user_object_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedUserObject& SavedObject::user_object() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.user_object)
  return has_user_object()
      ? *kind_.user_object_
      : *reinterpret_cast< ::tensorflow::SavedUserObject*>(&::tensorflow::_SavedUserObject_default_instance_);
}
inline ::tensorflow::SavedUserObject* SavedObject::unsafe_arena_release_user_object() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.user_object)
  if (has_user_object()) {
    clear_has_kind();
    ::tensorflow::SavedUserObject* temp = kind_.user_object_;
    kind_.user_object_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_user_object(::tensorflow::SavedUserObject* user_object) {
  clear_kind();
  if (user_object) {
    set_has_user_object();
    kind_.user_object_ = user_object;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.user_object)
}
inline ::tensorflow::SavedUserObject* SavedObject::mutable_user_object() {
  if (!has_user_object()) {
    clear_kind();
    set_has_user_object();
    kind_.user_object_ = CreateMaybeMessage< ::tensorflow::SavedUserObject >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.user_object)
  return kind_.user_object_;
}

// .tensorflow.SavedAsset asset = 5;
inline bool SavedObject::has_asset() const {
  return kind_case() == kAsset;
}
inline void SavedObject::set_has_asset() {
  _oneof_case_[0] = kAsset;
}
inline void SavedObject::clear_asset() {
  if (has_asset()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.asset_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedAsset* SavedObject::release_asset() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.asset)
  if (has_asset()) {
    clear_has_kind();
      ::tensorflow::SavedAsset* temp = kind_.asset_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.asset_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedAsset& SavedObject::asset() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.asset)
  return has_asset()
      ? *kind_.asset_
      : *reinterpret_cast< ::tensorflow::SavedAsset*>(&::tensorflow::_SavedAsset_default_instance_);
}
inline ::tensorflow::SavedAsset* SavedObject::unsafe_arena_release_asset() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.asset)
  if (has_asset()) {
    clear_has_kind();
    ::tensorflow::SavedAsset* temp = kind_.asset_;
    kind_.asset_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_asset(::tensorflow::SavedAsset* asset) {
  clear_kind();
  if (asset) {
    set_has_asset();
    kind_.asset_ = asset;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.asset)
}
inline ::tensorflow::SavedAsset* SavedObject::mutable_asset() {
  if (!has_asset()) {
    clear_kind();
    set_has_asset();
    kind_.asset_ = CreateMaybeMessage< ::tensorflow::SavedAsset >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.asset)
  return kind_.asset_;
}

// .tensorflow.SavedFunction function = 6;
inline bool SavedObject::has_function() const {
  return kind_case() == kFunction;
}
inline void SavedObject::set_has_function() {
  _oneof_case_[0] = kFunction;
}
inline void SavedObject::clear_function() {
  if (has_function()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.function_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedFunction* SavedObject::release_function() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.function)
  if (has_function()) {
    clear_has_kind();
      ::tensorflow::SavedFunction* temp = kind_.function_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedFunction& SavedObject::function() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.function)
  return has_function()
      ? *kind_.function_
      : *reinterpret_cast< ::tensorflow::SavedFunction*>(&::tensorflow::_SavedFunction_default_instance_);
}
inline ::tensorflow::SavedFunction* SavedObject::unsafe_arena_release_function() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.function)
  if (has_function()) {
    clear_has_kind();
    ::tensorflow::SavedFunction* temp = kind_.function_;
    kind_.function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_function(::tensorflow::SavedFunction* function) {
  clear_kind();
  if (function) {
    set_has_function();
    kind_.function_ = function;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.function)
}
inline ::tensorflow::SavedFunction* SavedObject::mutable_function() {
  if (!has_function()) {
    clear_kind();
    set_has_function();
    kind_.function_ = CreateMaybeMessage< ::tensorflow::SavedFunction >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.function)
  return kind_.function_;
}

// .tensorflow.SavedVariable variable = 7;
inline bool SavedObject::has_variable() const {
  return kind_case() == kVariable;
}
inline void SavedObject::set_has_variable() {
  _oneof_case_[0] = kVariable;
}
inline void SavedObject::clear_variable() {
  if (has_variable()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.variable_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedVariable* SavedObject::release_variable() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.variable)
  if (has_variable()) {
    clear_has_kind();
      ::tensorflow::SavedVariable* temp = kind_.variable_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.variable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedVariable& SavedObject::variable() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.variable)
  return has_variable()
      ? *kind_.variable_
      : *reinterpret_cast< ::tensorflow::SavedVariable*>(&::tensorflow::_SavedVariable_default_instance_);
}
inline ::tensorflow::SavedVariable* SavedObject::unsafe_arena_release_variable() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.variable)
  if (has_variable()) {
    clear_has_kind();
    ::tensorflow::SavedVariable* temp = kind_.variable_;
    kind_.variable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_variable(::tensorflow::SavedVariable* variable) {
  clear_kind();
  if (variable) {
    set_has_variable();
    kind_.variable_ = variable;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.variable)
}
inline ::tensorflow::SavedVariable* SavedObject::mutable_variable() {
  if (!has_variable()) {
    clear_kind();
    set_has_variable();
    kind_.variable_ = CreateMaybeMessage< ::tensorflow::SavedVariable >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.variable)
  return kind_.variable_;
}

// .tensorflow.SavedBareConcreteFunction bare_concrete_function = 8;
inline bool SavedObject::has_bare_concrete_function() const {
  return kind_case() == kBareConcreteFunction;
}
inline void SavedObject::set_has_bare_concrete_function() {
  _oneof_case_[0] = kBareConcreteFunction;
}
inline void SavedObject::clear_bare_concrete_function() {
  if (has_bare_concrete_function()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.bare_concrete_function_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedBareConcreteFunction* SavedObject::release_bare_concrete_function() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.bare_concrete_function)
  if (has_bare_concrete_function()) {
    clear_has_kind();
      ::tensorflow::SavedBareConcreteFunction* temp = kind_.bare_concrete_function_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.bare_concrete_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedBareConcreteFunction& SavedObject::bare_concrete_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.bare_concrete_function)
  return has_bare_concrete_function()
      ? *kind_.bare_concrete_function_
      : *reinterpret_cast< ::tensorflow::SavedBareConcreteFunction*>(&::tensorflow::_SavedBareConcreteFunction_default_instance_);
}
inline ::tensorflow::SavedBareConcreteFunction* SavedObject::unsafe_arena_release_bare_concrete_function() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.bare_concrete_function)
  if (has_bare_concrete_function()) {
    clear_has_kind();
    ::tensorflow::SavedBareConcreteFunction* temp = kind_.bare_concrete_function_;
    kind_.bare_concrete_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_bare_concrete_function(::tensorflow::SavedBareConcreteFunction* bare_concrete_function) {
  clear_kind();
  if (bare_concrete_function) {
    set_has_bare_concrete_function();
    kind_.bare_concrete_function_ = bare_concrete_function;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.bare_concrete_function)
}
inline ::tensorflow::SavedBareConcreteFunction* SavedObject::mutable_bare_concrete_function() {
  if (!has_bare_concrete_function()) {
    clear_kind();
    set_has_bare_concrete_function();
    kind_.bare_concrete_function_ = CreateMaybeMessage< ::tensorflow::SavedBareConcreteFunction >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.bare_concrete_function)
  return kind_.bare_concrete_function_;
}

// .tensorflow.SavedConstant constant = 9;
inline bool SavedObject::has_constant() const {
  return kind_case() == kConstant;
}
inline void SavedObject::set_has_constant() {
  _oneof_case_[0] = kConstant;
}
inline void SavedObject::clear_constant() {
  if (has_constant()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.constant_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedConstant* SavedObject::release_constant() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.constant)
  if (has_constant()) {
    clear_has_kind();
      ::tensorflow::SavedConstant* temp = kind_.constant_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.constant_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedConstant& SavedObject::constant() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.constant)
  return has_constant()
      ? *kind_.constant_
      : *reinterpret_cast< ::tensorflow::SavedConstant*>(&::tensorflow::_SavedConstant_default_instance_);
}
inline ::tensorflow::SavedConstant* SavedObject::unsafe_arena_release_constant() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.constant)
  if (has_constant()) {
    clear_has_kind();
    ::tensorflow::SavedConstant* temp = kind_.constant_;
    kind_.constant_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_constant(::tensorflow::SavedConstant* constant) {
  clear_kind();
  if (constant) {
    set_has_constant();
    kind_.constant_ = constant;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.constant)
}
inline ::tensorflow::SavedConstant* SavedObject::mutable_constant() {
  if (!has_constant()) {
    clear_kind();
    set_has_constant();
    kind_.constant_ = CreateMaybeMessage< ::tensorflow::SavedConstant >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.constant)
  return kind_.constant_;
}

// .tensorflow.SavedResource resource = 10;
inline bool SavedObject::has_resource() const {
  return kind_case() == kResource;
}
inline void SavedObject::set_has_resource() {
  _oneof_case_[0] = kResource;
}
inline void SavedObject::clear_resource() {
  if (has_resource()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.resource_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedResource* SavedObject::release_resource() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.resource)
  if (has_resource()) {
    clear_has_kind();
      ::tensorflow::SavedResource* temp = kind_.resource_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.resource_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedResource& SavedObject::resource() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.resource)
  return has_resource()
      ? *kind_.resource_
      : *reinterpret_cast< ::tensorflow::SavedResource*>(&::tensorflow::_SavedResource_default_instance_);
}
inline ::tensorflow::SavedResource* SavedObject::unsafe_arena_release_resource() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.resource)
  if (has_resource()) {
    clear_has_kind();
    ::tensorflow::SavedResource* temp = kind_.resource_;
    kind_.resource_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_resource(::tensorflow::SavedResource* resource) {
  clear_kind();
  if (resource) {
    set_has_resource();
    kind_.resource_ = resource;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.resource)
}
inline ::tensorflow::SavedResource* SavedObject::mutable_resource() {
  if (!has_resource()) {
    clear_kind();
    set_has_resource();
    kind_.resource_ = CreateMaybeMessage< ::tensorflow::SavedResource >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.resource)
  return kind_.resource_;
}

// .tensorflow.CapturedTensor captured_tensor = 12;
inline bool SavedObject::has_captured_tensor() const {
  return kind_case() == kCapturedTensor;
}
inline void SavedObject::set_has_captured_tensor() {
  _oneof_case_[0] = kCapturedTensor;
}
inline void SavedObject::clear_captured_tensor() {
  if (has_captured_tensor()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete kind_.captured_tensor_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::CapturedTensor* SavedObject::release_captured_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.captured_tensor)
  if (has_captured_tensor()) {
    clear_has_kind();
      ::tensorflow::CapturedTensor* temp = kind_.captured_tensor_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    kind_.captured_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CapturedTensor& SavedObject::captured_tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.captured_tensor)
  return has_captured_tensor()
      ? *kind_.captured_tensor_
      : *reinterpret_cast< ::tensorflow::CapturedTensor*>(&::tensorflow::_CapturedTensor_default_instance_);
}
inline ::tensorflow::CapturedTensor* SavedObject::unsafe_arena_release_captured_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.captured_tensor)
  if (has_captured_tensor()) {
    clear_has_kind();
    ::tensorflow::CapturedTensor* temp = kind_.captured_tensor_;
    kind_.captured_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_captured_tensor(::tensorflow::CapturedTensor* captured_tensor) {
  clear_kind();
  if (captured_tensor) {
    set_has_captured_tensor();
    kind_.captured_tensor_ = captured_tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.captured_tensor)
}
inline ::tensorflow::CapturedTensor* SavedObject::mutable_captured_tensor() {
  if (!has_captured_tensor()) {
    clear_kind();
    set_has_captured_tensor();
    kind_.captured_tensor_ = CreateMaybeMessage< ::tensorflow::CapturedTensor >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.captured_tensor)
  return kind_.captured_tensor_;
}

// map<string, .tensorflow.SaveableObject> saveable_objects = 11;
inline int SavedObject::saveable_objects_size() const {
  return saveable_objects_.size();
}
inline void SavedObject::clear_saveable_objects() {
  saveable_objects_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >&
SavedObject::saveable_objects() const {
  // @@protoc_insertion_point(field_map:tensorflow.SavedObject.saveable_objects)
  return saveable_objects_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >*
SavedObject::mutable_saveable_objects() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.SavedObject.saveable_objects)
  return saveable_objects_.MutableMap();
}

inline bool SavedObject::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void SavedObject::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline SavedObject::KindCase SavedObject::kind_case() const {
  return SavedObject::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// SavedUserObject

// string identifier = 1;
inline void SavedUserObject::clear_identifier() {
  identifier_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SavedUserObject::identifier() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedUserObject.identifier)
  return identifier_.Get();
}
inline void SavedUserObject::set_identifier(const std::string& value) {
  
  identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedUserObject.identifier)
}
inline void SavedUserObject::set_identifier(std::string&& value) {
  
  identifier_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedUserObject.identifier)
}
inline void SavedUserObject::set_identifier(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedUserObject.identifier)
}
inline void SavedUserObject::set_identifier(const char* value,
    size_t size) {
  
  identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedUserObject.identifier)
}
inline std::string* SavedUserObject::mutable_identifier() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedUserObject.identifier)
  return identifier_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SavedUserObject::release_identifier() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedUserObject.identifier)
  
  return identifier_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedUserObject::set_allocated_identifier(std::string* identifier) {
  if (identifier != nullptr) {
    
  } else {
    
  }
  identifier_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), identifier,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedUserObject.identifier)
}
inline std::string* SavedUserObject::unsafe_arena_release_identifier() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedUserObject.identifier)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return identifier_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedUserObject::unsafe_arena_set_allocated_identifier(
    std::string* identifier) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (identifier != nullptr) {
    
  } else {
    
  }
  identifier_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      identifier, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedUserObject.identifier)
}

// .tensorflow.VersionDef version = 2;
inline bool SavedUserObject::has_version() const {
  return this != internal_default_instance() && version_ != nullptr;
}
inline const ::tensorflow::VersionDef& SavedUserObject::version() const {
  const ::tensorflow::VersionDef* p = version_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedUserObject.version)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::VersionDef*>(
      &::tensorflow::_VersionDef_default_instance_);
}
inline ::tensorflow::VersionDef* SavedUserObject::release_version() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedUserObject.version)
  
  ::tensorflow::VersionDef* temp = version_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  version_ = nullptr;
  return temp;
}
inline ::tensorflow::VersionDef* SavedUserObject::unsafe_arena_release_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedUserObject.version)
  
  ::tensorflow::VersionDef* temp = version_;
  version_ = nullptr;
  return temp;
}
inline ::tensorflow::VersionDef* SavedUserObject::mutable_version() {
  
  if (version_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VersionDef>(GetArenaNoVirtual());
    version_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedUserObject.version)
  return version_;
}
inline void SavedUserObject::set_allocated_version(::tensorflow::VersionDef* version) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(version_);
  }
  if (version) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(version)->GetArena();
    if (message_arena != submessage_arena) {
      version = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, version, submessage_arena);
    }
    
  } else {
    
  }
  version_ = version;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedUserObject.version)
}

// string metadata = 3 [deprecated = true];
inline void SavedUserObject::clear_metadata() {
  metadata_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SavedUserObject::metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedUserObject.metadata)
  return metadata_.Get();
}
inline void SavedUserObject::set_metadata(const std::string& value) {
  
  metadata_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedUserObject.metadata)
}
inline void SavedUserObject::set_metadata(std::string&& value) {
  
  metadata_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedUserObject.metadata)
}
inline void SavedUserObject::set_metadata(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  metadata_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedUserObject.metadata)
}
inline void SavedUserObject::set_metadata(const char* value,
    size_t size) {
  
  metadata_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedUserObject.metadata)
}
inline std::string* SavedUserObject::mutable_metadata() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedUserObject.metadata)
  return metadata_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SavedUserObject::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedUserObject.metadata)
  
  return metadata_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedUserObject::set_allocated_metadata(std::string* metadata) {
  if (metadata != nullptr) {
    
  } else {
    
  }
  metadata_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), metadata,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedUserObject.metadata)
}
inline std::string* SavedUserObject::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedUserObject.metadata)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return metadata_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedUserObject::unsafe_arena_set_allocated_metadata(
    std::string* metadata) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (metadata != nullptr) {
    
  } else {
    
  }
  metadata_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      metadata, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedUserObject.metadata)
}

// -------------------------------------------------------------------

// SavedAsset

// int32 asset_file_def_index = 1;
inline void SavedAsset::clear_asset_file_def_index() {
  asset_file_def_index_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SavedAsset::asset_file_def_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedAsset.asset_file_def_index)
  return asset_file_def_index_;
}
inline void SavedAsset::set_asset_file_def_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  asset_file_def_index_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedAsset.asset_file_def_index)
}

// -------------------------------------------------------------------

// SavedFunction

// repeated string concrete_functions = 1;
inline int SavedFunction::concrete_functions_size() const {
  return concrete_functions_.size();
}
inline void SavedFunction::clear_concrete_functions() {
  concrete_functions_.Clear();
}
inline const std::string& SavedFunction::concrete_functions(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedFunction.concrete_functions)
  return concrete_functions_.Get(index);
}
inline std::string* SavedFunction::mutable_concrete_functions(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedFunction.concrete_functions)
  return concrete_functions_.Mutable(index);
}
inline void SavedFunction::set_concrete_functions(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.SavedFunction.concrete_functions)
  concrete_functions_.Mutable(index)->assign(value);
}
inline void SavedFunction::set_concrete_functions(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.SavedFunction.concrete_functions)
  concrete_functions_.Mutable(index)->assign(std::move(value));
}
inline void SavedFunction::set_concrete_functions(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  concrete_functions_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::set_concrete_functions(int index, const char* value, size_t size) {
  concrete_functions_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedFunction.concrete_functions)
}
inline std::string* SavedFunction::add_concrete_functions() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.SavedFunction.concrete_functions)
  return concrete_functions_.Add();
}
inline void SavedFunction::add_concrete_functions(const std::string& value) {
  concrete_functions_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::add_concrete_functions(std::string&& value) {
  concrete_functions_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::add_concrete_functions(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  concrete_functions_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::add_concrete_functions(const char* value, size_t size) {
  concrete_functions_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.SavedFunction.concrete_functions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SavedFunction::concrete_functions() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedFunction.concrete_functions)
  return concrete_functions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SavedFunction::mutable_concrete_functions() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedFunction.concrete_functions)
  return &concrete_functions_;
}

// .tensorflow.FunctionSpec function_spec = 2;
inline bool SavedFunction::has_function_spec() const {
  return this != internal_default_instance() && function_spec_ != nullptr;
}
inline void SavedFunction::clear_function_spec() {
  if (GetArenaNoVirtual() == nullptr && function_spec_ != nullptr) {
    delete function_spec_;
  }
  function_spec_ = nullptr;
}
inline const ::tensorflow::FunctionSpec& SavedFunction::function_spec() const {
  const ::tensorflow::FunctionSpec* p = function_spec_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedFunction.function_spec)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::FunctionSpec*>(
      &::tensorflow::_FunctionSpec_default_instance_);
}
inline ::tensorflow::FunctionSpec* SavedFunction::release_function_spec() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedFunction.function_spec)
  
  ::tensorflow::FunctionSpec* temp = function_spec_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  function_spec_ = nullptr;
  return temp;
}
inline ::tensorflow::FunctionSpec* SavedFunction::unsafe_arena_release_function_spec() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedFunction.function_spec)
  
  ::tensorflow::FunctionSpec* temp = function_spec_;
  function_spec_ = nullptr;
  return temp;
}
inline ::tensorflow::FunctionSpec* SavedFunction::mutable_function_spec() {
  
  if (function_spec_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionSpec>(GetArenaNoVirtual());
    function_spec_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedFunction.function_spec)
  return function_spec_;
}
inline void SavedFunction::set_allocated_function_spec(::tensorflow::FunctionSpec* function_spec) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete function_spec_;
  }
  if (function_spec) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(function_spec);
    if (message_arena != submessage_arena) {
      function_spec = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, function_spec, submessage_arena);
    }
    
  } else {
    
  }
  function_spec_ = function_spec;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedFunction.function_spec)
}

// -------------------------------------------------------------------

// CapturedTensor

// string name = 1;
inline void CapturedTensor::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CapturedTensor::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CapturedTensor.name)
  return name_.Get();
}
inline void CapturedTensor::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CapturedTensor.name)
}
inline void CapturedTensor::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CapturedTensor.name)
}
inline void CapturedTensor::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CapturedTensor.name)
}
inline void CapturedTensor::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CapturedTensor.name)
}
inline std::string* CapturedTensor::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CapturedTensor.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CapturedTensor::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CapturedTensor.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CapturedTensor::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CapturedTensor.name)
}
inline std::string* CapturedTensor::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CapturedTensor.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CapturedTensor::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CapturedTensor.name)
}

// string concrete_function = 2;
inline void CapturedTensor::clear_concrete_function() {
  concrete_function_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CapturedTensor::concrete_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.CapturedTensor.concrete_function)
  return concrete_function_.Get();
}
inline void CapturedTensor::set_concrete_function(const std::string& value) {
  
  concrete_function_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CapturedTensor.concrete_function)
}
inline void CapturedTensor::set_concrete_function(std::string&& value) {
  
  concrete_function_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CapturedTensor.concrete_function)
}
inline void CapturedTensor::set_concrete_function(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  concrete_function_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CapturedTensor.concrete_function)
}
inline void CapturedTensor::set_concrete_function(const char* value,
    size_t size) {
  
  concrete_function_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CapturedTensor.concrete_function)
}
inline std::string* CapturedTensor::mutable_concrete_function() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CapturedTensor.concrete_function)
  return concrete_function_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CapturedTensor::release_concrete_function() {
  // @@protoc_insertion_point(field_release:tensorflow.CapturedTensor.concrete_function)
  
  return concrete_function_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CapturedTensor::set_allocated_concrete_function(std::string* concrete_function) {
  if (concrete_function != nullptr) {
    
  } else {
    
  }
  concrete_function_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), concrete_function,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CapturedTensor.concrete_function)
}
inline std::string* CapturedTensor::unsafe_arena_release_concrete_function() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CapturedTensor.concrete_function)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return concrete_function_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CapturedTensor::unsafe_arena_set_allocated_concrete_function(
    std::string* concrete_function) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (concrete_function != nullptr) {
    
  } else {
    
  }
  concrete_function_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      concrete_function, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CapturedTensor.concrete_function)
}

// -------------------------------------------------------------------

// SavedConcreteFunction

// repeated int32 bound_inputs = 2;
inline int SavedConcreteFunction::bound_inputs_size() const {
  return bound_inputs_.size();
}
inline void SavedConcreteFunction::clear_bound_inputs() {
  bound_inputs_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SavedConcreteFunction::bound_inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedConcreteFunction.bound_inputs)
  return bound_inputs_.Get(index);
}
inline void SavedConcreteFunction::set_bound_inputs(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  bound_inputs_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedConcreteFunction.bound_inputs)
}
inline void SavedConcreteFunction::add_bound_inputs(::PROTOBUF_NAMESPACE_ID::int32 value) {
  bound_inputs_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.SavedConcreteFunction.bound_inputs)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
SavedConcreteFunction::bound_inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedConcreteFunction.bound_inputs)
  return bound_inputs_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
SavedConcreteFunction::mutable_bound_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedConcreteFunction.bound_inputs)
  return &bound_inputs_;
}

// .tensorflow.StructuredValue canonicalized_input_signature = 3;
inline bool SavedConcreteFunction::has_canonicalized_input_signature() const {
  return this != internal_default_instance() && canonicalized_input_signature_ != nullptr;
}
inline const ::tensorflow::StructuredValue& SavedConcreteFunction::canonicalized_input_signature() const {
  const ::tensorflow::StructuredValue* p = canonicalized_input_signature_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::release_canonicalized_input_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  
  ::tensorflow::StructuredValue* temp = canonicalized_input_signature_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  canonicalized_input_signature_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::unsafe_arena_release_canonicalized_input_signature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  
  ::tensorflow::StructuredValue* temp = canonicalized_input_signature_;
  canonicalized_input_signature_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::mutable_canonicalized_input_signature() {
  
  if (canonicalized_input_signature_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    canonicalized_input_signature_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  return canonicalized_input_signature_;
}
inline void SavedConcreteFunction::set_allocated_canonicalized_input_signature(::tensorflow::StructuredValue* canonicalized_input_signature) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(canonicalized_input_signature_);
  }
  if (canonicalized_input_signature) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      canonicalized_input_signature = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, canonicalized_input_signature, submessage_arena);
    }
    
  } else {
    
  }
  canonicalized_input_signature_ = canonicalized_input_signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
}

// .tensorflow.StructuredValue output_signature = 4;
inline bool SavedConcreteFunction::has_output_signature() const {
  return this != internal_default_instance() && output_signature_ != nullptr;
}
inline const ::tensorflow::StructuredValue& SavedConcreteFunction::output_signature() const {
  const ::tensorflow::StructuredValue* p = output_signature_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedConcreteFunction.output_signature)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::release_output_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedConcreteFunction.output_signature)
  
  ::tensorflow::StructuredValue* temp = output_signature_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  output_signature_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::unsafe_arena_release_output_signature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedConcreteFunction.output_signature)
  
  ::tensorflow::StructuredValue* temp = output_signature_;
  output_signature_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::mutable_output_signature() {
  
  if (output_signature_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    output_signature_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedConcreteFunction.output_signature)
  return output_signature_;
}
inline void SavedConcreteFunction::set_allocated_output_signature(::tensorflow::StructuredValue* output_signature) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(output_signature_);
  }
  if (output_signature) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      output_signature = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, output_signature, submessage_arena);
    }
    
  } else {
    
  }
  output_signature_ = output_signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedConcreteFunction.output_signature)
}

// -------------------------------------------------------------------

// SavedBareConcreteFunction

// string concrete_function_name = 1;
inline void SavedBareConcreteFunction::clear_concrete_function_name() {
  concrete_function_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SavedBareConcreteFunction::concrete_function_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  return concrete_function_name_.Get();
}
inline void SavedBareConcreteFunction::set_concrete_function_name(const std::string& value) {
  
  concrete_function_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
inline void SavedBareConcreteFunction::set_concrete_function_name(std::string&& value) {
  
  concrete_function_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
inline void SavedBareConcreteFunction::set_concrete_function_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  concrete_function_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
inline void SavedBareConcreteFunction::set_concrete_function_name(const char* value,
    size_t size) {
  
  concrete_function_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
inline std::string* SavedBareConcreteFunction::mutable_concrete_function_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  return concrete_function_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SavedBareConcreteFunction::release_concrete_function_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  
  return concrete_function_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedBareConcreteFunction::set_allocated_concrete_function_name(std::string* concrete_function_name) {
  if (concrete_function_name != nullptr) {
    
  } else {
    
  }
  concrete_function_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), concrete_function_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
inline std::string* SavedBareConcreteFunction::unsafe_arena_release_concrete_function_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return concrete_function_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedBareConcreteFunction::unsafe_arena_set_allocated_concrete_function_name(
    std::string* concrete_function_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (concrete_function_name != nullptr) {
    
  } else {
    
  }
  concrete_function_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      concrete_function_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}

// repeated string argument_keywords = 2;
inline int SavedBareConcreteFunction::argument_keywords_size() const {
  return argument_keywords_.size();
}
inline void SavedBareConcreteFunction::clear_argument_keywords() {
  argument_keywords_.Clear();
}
inline const std::string& SavedBareConcreteFunction::argument_keywords(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return argument_keywords_.Get(index);
}
inline std::string* SavedBareConcreteFunction::mutable_argument_keywords(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return argument_keywords_.Mutable(index);
}
inline void SavedBareConcreteFunction::set_argument_keywords(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.argument_keywords)
  argument_keywords_.Mutable(index)->assign(value);
}
inline void SavedBareConcreteFunction::set_argument_keywords(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.argument_keywords)
  argument_keywords_.Mutable(index)->assign(std::move(value));
}
inline void SavedBareConcreteFunction::set_argument_keywords(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  argument_keywords_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::set_argument_keywords(int index, const char* value, size_t size) {
  argument_keywords_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline std::string* SavedBareConcreteFunction::add_argument_keywords() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return argument_keywords_.Add();
}
inline void SavedBareConcreteFunction::add_argument_keywords(const std::string& value) {
  argument_keywords_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::add_argument_keywords(std::string&& value) {
  argument_keywords_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::add_argument_keywords(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  argument_keywords_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::add_argument_keywords(const char* value, size_t size) {
  argument_keywords_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SavedBareConcreteFunction::argument_keywords() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return argument_keywords_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SavedBareConcreteFunction::mutable_argument_keywords() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return &argument_keywords_;
}

// int64 allowed_positional_arguments = 3;
inline void SavedBareConcreteFunction::clear_allowed_positional_arguments() {
  allowed_positional_arguments_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 SavedBareConcreteFunction::allowed_positional_arguments() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.allowed_positional_arguments)
  return allowed_positional_arguments_;
}
inline void SavedBareConcreteFunction::set_allowed_positional_arguments(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  allowed_positional_arguments_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.allowed_positional_arguments)
}

// .tensorflow.FunctionSpec function_spec = 4;
inline bool SavedBareConcreteFunction::has_function_spec() const {
  return this != internal_default_instance() && function_spec_ != nullptr;
}
inline void SavedBareConcreteFunction::clear_function_spec() {
  if (GetArenaNoVirtual() == nullptr && function_spec_ != nullptr) {
    delete function_spec_;
  }
  function_spec_ = nullptr;
}
inline const ::tensorflow::FunctionSpec& SavedBareConcreteFunction::function_spec() const {
  const ::tensorflow::FunctionSpec* p = function_spec_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.function_spec)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::FunctionSpec*>(
      &::tensorflow::_FunctionSpec_default_instance_);
}
inline ::tensorflow::FunctionSpec* SavedBareConcreteFunction::release_function_spec() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedBareConcreteFunction.function_spec)
  
  ::tensorflow::FunctionSpec* temp = function_spec_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  function_spec_ = nullptr;
  return temp;
}
inline ::tensorflow::FunctionSpec* SavedBareConcreteFunction::unsafe_arena_release_function_spec() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedBareConcreteFunction.function_spec)
  
  ::tensorflow::FunctionSpec* temp = function_spec_;
  function_spec_ = nullptr;
  return temp;
}
inline ::tensorflow::FunctionSpec* SavedBareConcreteFunction::mutable_function_spec() {
  
  if (function_spec_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionSpec>(GetArenaNoVirtual());
    function_spec_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedBareConcreteFunction.function_spec)
  return function_spec_;
}
inline void SavedBareConcreteFunction::set_allocated_function_spec(::tensorflow::FunctionSpec* function_spec) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete function_spec_;
  }
  if (function_spec) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(function_spec);
    if (message_arena != submessage_arena) {
      function_spec = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, function_spec, submessage_arena);
    }
    
  } else {
    
  }
  function_spec_ = function_spec;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedBareConcreteFunction.function_spec)
}

// -------------------------------------------------------------------

// SavedConstant

// string operation = 1;
inline void SavedConstant::clear_operation() {
  operation_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SavedConstant::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedConstant.operation)
  return operation_.Get();
}
inline void SavedConstant::set_operation(const std::string& value) {
  
  operation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedConstant.operation)
}
inline void SavedConstant::set_operation(std::string&& value) {
  
  operation_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedConstant.operation)
}
inline void SavedConstant::set_operation(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  operation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedConstant.operation)
}
inline void SavedConstant::set_operation(const char* value,
    size_t size) {
  
  operation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedConstant.operation)
}
inline std::string* SavedConstant::mutable_operation() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedConstant.operation)
  return operation_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SavedConstant::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedConstant.operation)
  
  return operation_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedConstant::set_allocated_operation(std::string* operation) {
  if (operation != nullptr) {
    
  } else {
    
  }
  operation_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), operation,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedConstant.operation)
}
inline std::string* SavedConstant::unsafe_arena_release_operation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedConstant.operation)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return operation_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedConstant::unsafe_arena_set_allocated_operation(
    std::string* operation) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (operation != nullptr) {
    
  } else {
    
  }
  operation_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      operation, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedConstant.operation)
}

// -------------------------------------------------------------------

// SavedVariable

// .tensorflow.DataType dtype = 1;
inline void SavedVariable::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType SavedVariable::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void SavedVariable::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool SavedVariable::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& SavedVariable::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* SavedVariable::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedVariable.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedVariable::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedVariable.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedVariable::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedVariable.shape)
  return shape_;
}
inline void SavedVariable::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedVariable.shape)
}

// bool trainable = 3;
inline void SavedVariable::clear_trainable() {
  trainable_ = false;
}
inline bool SavedVariable::trainable() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.trainable)
  return trainable_;
}
inline void SavedVariable::set_trainable(bool value) {
  
  trainable_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.trainable)
}

// .tensorflow.VariableSynchronization synchronization = 4;
inline void SavedVariable::clear_synchronization() {
  synchronization_ = 0;
}
inline ::tensorflow::VariableSynchronization SavedVariable::synchronization() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.synchronization)
  return static_cast< ::tensorflow::VariableSynchronization >(synchronization_);
}
inline void SavedVariable::set_synchronization(::tensorflow::VariableSynchronization value) {
  
  synchronization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.synchronization)
}

// .tensorflow.VariableAggregation aggregation = 5;
inline void SavedVariable::clear_aggregation() {
  aggregation_ = 0;
}
inline ::tensorflow::VariableAggregation SavedVariable::aggregation() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.aggregation)
  return static_cast< ::tensorflow::VariableAggregation >(aggregation_);
}
inline void SavedVariable::set_aggregation(::tensorflow::VariableAggregation value) {
  
  aggregation_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.aggregation)
}

// string name = 6;
inline void SavedVariable::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SavedVariable::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.name)
  return name_.Get();
}
inline void SavedVariable::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.name)
}
inline void SavedVariable::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedVariable.name)
}
inline void SavedVariable::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedVariable.name)
}
inline void SavedVariable::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedVariable.name)
}
inline std::string* SavedVariable::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedVariable.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SavedVariable::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedVariable.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedVariable::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedVariable.name)
}
inline std::string* SavedVariable::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedVariable.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedVariable::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedVariable.name)
}

// string device = 7;
inline void SavedVariable::clear_device() {
  device_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SavedVariable::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.device)
  return device_.Get();
}
inline void SavedVariable::set_device(const std::string& value) {
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.device)
}
inline void SavedVariable::set_device(std::string&& value) {
  
  device_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedVariable.device)
}
inline void SavedVariable::set_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedVariable.device)
}
inline void SavedVariable::set_device(const char* value,
    size_t size) {
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedVariable.device)
}
inline std::string* SavedVariable::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedVariable.device)
  return device_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SavedVariable::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedVariable.device)
  
  return device_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedVariable::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  device_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedVariable.device)
}
inline std::string* SavedVariable::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedVariable.device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedVariable::unsafe_arena_set_allocated_device(
    std::string* device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device != nullptr) {
    
  } else {
    
  }
  device_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedVariable.device)
}

// repeated .tensorflow.SavedVariable experimental_distributed_variable_components = 8;
inline int SavedVariable::experimental_distributed_variable_components_size() const {
  return experimental_distributed_variable_components_.size();
}
inline void SavedVariable::clear_experimental_distributed_variable_components() {
  experimental_distributed_variable_components_.Clear();
}
inline ::tensorflow::SavedVariable* SavedVariable::mutable_experimental_distributed_variable_components(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedVariable.experimental_distributed_variable_components)
  return experimental_distributed_variable_components_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedVariable >*
SavedVariable::mutable_experimental_distributed_variable_components() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedVariable.experimental_distributed_variable_components)
  return &experimental_distributed_variable_components_;
}
inline const ::tensorflow::SavedVariable& SavedVariable::experimental_distributed_variable_components(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.experimental_distributed_variable_components)
  return experimental_distributed_variable_components_.Get(index);
}
inline ::tensorflow::SavedVariable* SavedVariable::add_experimental_distributed_variable_components() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedVariable.experimental_distributed_variable_components)
  return experimental_distributed_variable_components_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedVariable >&
SavedVariable::experimental_distributed_variable_components() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedVariable.experimental_distributed_variable_components)
  return experimental_distributed_variable_components_;
}

// -------------------------------------------------------------------

// FunctionSpec

// .tensorflow.StructuredValue fullargspec = 1;
inline bool FunctionSpec::has_fullargspec() const {
  return this != internal_default_instance() && fullargspec_ != nullptr;
}
inline const ::tensorflow::StructuredValue& FunctionSpec::fullargspec() const {
  const ::tensorflow::StructuredValue* p = fullargspec_;
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.fullargspec)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* FunctionSpec::release_fullargspec() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionSpec.fullargspec)
  
  ::tensorflow::StructuredValue* temp = fullargspec_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  fullargspec_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::unsafe_arena_release_fullargspec() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FunctionSpec.fullargspec)
  
  ::tensorflow::StructuredValue* temp = fullargspec_;
  fullargspec_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::mutable_fullargspec() {
  
  if (fullargspec_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    fullargspec_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionSpec.fullargspec)
  return fullargspec_;
}
inline void FunctionSpec::set_allocated_fullargspec(::tensorflow::StructuredValue* fullargspec) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(fullargspec_);
  }
  if (fullargspec) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      fullargspec = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, fullargspec, submessage_arena);
    }
    
  } else {
    
  }
  fullargspec_ = fullargspec;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionSpec.fullargspec)
}

// bool is_method = 2;
inline void FunctionSpec::clear_is_method() {
  is_method_ = false;
}
inline bool FunctionSpec::is_method() const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.is_method)
  return is_method_;
}
inline void FunctionSpec::set_is_method(bool value) {
  
  is_method_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.FunctionSpec.is_method)
}

// .tensorflow.StructuredValue input_signature = 5;
inline bool FunctionSpec::has_input_signature() const {
  return this != internal_default_instance() && input_signature_ != nullptr;
}
inline const ::tensorflow::StructuredValue& FunctionSpec::input_signature() const {
  const ::tensorflow::StructuredValue* p = input_signature_;
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.input_signature)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* FunctionSpec::release_input_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionSpec.input_signature)
  
  ::tensorflow::StructuredValue* temp = input_signature_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  input_signature_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::unsafe_arena_release_input_signature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FunctionSpec.input_signature)
  
  ::tensorflow::StructuredValue* temp = input_signature_;
  input_signature_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::mutable_input_signature() {
  
  if (input_signature_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    input_signature_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionSpec.input_signature)
  return input_signature_;
}
inline void FunctionSpec::set_allocated_input_signature(::tensorflow::StructuredValue* input_signature) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(input_signature_);
  }
  if (input_signature) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      input_signature = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, input_signature, submessage_arena);
    }
    
  } else {
    
  }
  input_signature_ = input_signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionSpec.input_signature)
}

// .tensorflow.FunctionSpec.JitCompile jit_compile = 6;
inline void FunctionSpec::clear_jit_compile() {
  jit_compile_ = 0;
}
inline ::tensorflow::FunctionSpec_JitCompile FunctionSpec::jit_compile() const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.jit_compile)
  return static_cast< ::tensorflow::FunctionSpec_JitCompile >(jit_compile_);
}
inline void FunctionSpec::set_jit_compile(::tensorflow::FunctionSpec_JitCompile value) {
  
  jit_compile_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.FunctionSpec.jit_compile)
}

// -------------------------------------------------------------------

// SavedResource

// string device = 1;
inline void SavedResource::clear_device() {
  device_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& SavedResource::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedResource.device)
  return device_.Get();
}
inline void SavedResource::set_device(const std::string& value) {
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedResource.device)
}
inline void SavedResource::set_device(std::string&& value) {
  
  device_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedResource.device)
}
inline void SavedResource::set_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedResource.device)
}
inline void SavedResource::set_device(const char* value,
    size_t size) {
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedResource.device)
}
inline std::string* SavedResource::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedResource.device)
  return device_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* SavedResource::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedResource.device)
  
  return device_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedResource::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  device_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedResource.device)
}
inline std::string* SavedResource::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedResource.device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedResource::unsafe_arena_set_allocated_device(
    std::string* device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device != nullptr) {
    
  } else {
    
  }
  device_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedResource.device)
}

// -------------------------------------------------------------------

// SaveableObject

// int32 save_function = 2;
inline void SaveableObject::clear_save_function() {
  save_function_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SaveableObject::save_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveableObject.save_function)
  return save_function_;
}
inline void SaveableObject::set_save_function(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  save_function_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SaveableObject.save_function)
}

// int32 restore_function = 3;
inline void SaveableObject::clear_restore_function() {
  restore_function_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SaveableObject::restore_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveableObject.restore_function)
  return restore_function_;
}
inline void SaveableObject::set_restore_function(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  restore_function_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SaveableObject.restore_function)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::FunctionSpec_JitCompile> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::FunctionSpec_JitCompile>() {
  return ::tensorflow::FunctionSpec_JitCompile_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
