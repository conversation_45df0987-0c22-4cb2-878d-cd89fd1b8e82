# Generated by Django 4.2.6 on 2023-11-21 19:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("engine", "0076_remove_storages_that_refer_to_deleted_cloud_storages"),
    ]

    operations = [
        migrations.AlterField(
            model_name="storage",
            name="cloud_storage_id",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to="engine.cloudstorage",
            ),
        ),
        migrations.RenameField(
            model_name="storage",
            old_name="cloud_storage_id",
            new_name="cloud_storage",
        ),
    ]
