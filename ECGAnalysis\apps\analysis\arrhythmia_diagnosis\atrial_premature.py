import numpy as np


def process(wave_info, hr, p_threshold=0.12, qrs_threshold=0.02, rr_threshold=0.02):
    """
    房性早搏识别
    :param wave_info: 波形信息
    :param hr: 心率
    :param p_threshold: P波判断阈值
    :param qrs_threshold: QRS判断阈值
    :param rr_threshold: RR间期判断阈值
    :return: 0 未识别， 1 已识别
    """
    if hr < 60:
        return 0

    r_peaks = wave_info['r_peaks']
    rr_intervals_value = wave_info['rr_intervals_value']
    pp_intervals_value = wave_info['pp_intervals_value']
    qrs_durations = wave_info['qrs_durations']
    pr_intervals_value = wave_info['pr_intervals_value']

    atrial_premature_r = np.empty((0, 2), dtype=float)

    if pp_intervals_value.size == 0 or qrs_durations.size == 0:
        return 0

    pp_intervals_mean_value = round(np.nanmean(pp_intervals_value), 2) * 2  # PP间期的2倍

    rr_intervals_mean_value = round(np.nanmean(rr_intervals_value), 2)  # RR间期平均值
    qrs_mean_durations = np.nanmean(qrs_durations)  # QRS间期平均值

    i = 0
    for rr_value in rr_intervals_value:
        rr_value = round(rr_value, 2)
        if i == len(rr_intervals_value) - 1:
            continue

        next_rr_value = round(rr_intervals_value[i + 1], 2)
        if next_rr_value > 1:
            continue

        if rr_value < rr_intervals_mean_value + rr_threshold < next_rr_value:
            qrs_value = qrs_durations[i]
            if abs(qrs_value - qrs_mean_durations) < qrs_threshold:  # QRS波处于整体QRS波群的正常宽度
                pr_value = pr_intervals_value[i]  # 获取对应PR间期的值

                if np.isnan(pr_value):
                    print(f'第{i + 1}个R波的P波未检测到PR间期')
                elif pr_value >= p_threshold:  # 判断当前PR间期是否大于0.12秒
                    pp_value = pp_intervals_value[i]
                    prev_pp_value = pp_intervals_value[i - 1]

                    if pp_value + prev_pp_value < pp_intervals_mean_value:
                        atrial_premature_r = np.append(atrial_premature_r, np.array([[i, r_peaks[i + 1]]]), axis=0)
        i += 1

    return 1 if len(atrial_premature_r[:, 0]) > 0 else 0

