apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}-backend-service
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cvat.labels" . | nindent 4 }}
    app: cvat-app
    tier: backend
  annotations:
    {{- with .Values.cvat.backend.service.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  selector:
    app: cvat-app
    tier: backend
    component: server
    {{- include "cvat.labels" . | nindent 4 }}
  {{- with .Values.cvat.backend.service.spec }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
