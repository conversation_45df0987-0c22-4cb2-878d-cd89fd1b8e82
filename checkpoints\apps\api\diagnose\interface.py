import ast
import json
import traceback

import numpy as np
from django.utils.decorators import method_decorator
from django.views import View

import apps.analysis.arrhythmia_diagnosis.diagnosis as arrhythmia_diagnosis_diagnosis
import apps.analysis.cad_cardiomyopathy.diagnosis as cad_cardiomyopathy_diagnosis
import apps.analysis.health_metrics.diagnosis as health_metrics_diagnosis
import apps.analysis.pqrstc.diagnosis as pqrstc_diagnosis
from apps.analysis import whitelist
from apps.analysis.common.custom_exception import BiosppyEcgError
from apps.analysis.common.data_filter import filter_negative_values
from apps.analysis.ecg_age import diagnosis as ecg_age_diagnosis
from apps.api.diagnose.business import save_api_log, get_diagnosis_details
from apps.models.analysis_models import AnalysisEntity, ArrhythmiaDiagnosisEntity, HealthMetricsEntity, CadCardiomyopathyEntity
from apps.signal_analysis.available import get_available_signals
from apps.signal_analysis.waveform import get_waveform
from apps.utils.decorator import authentication
from apps.utils.get_response import GetResponse
from apps.utils.logger_helper import Logger
from apps.analysis.common.gravity import process as gravity_process


@method_decorator(authentication, name="dispatch")
class ArrhythmiaView(View):
    """
    心率失常诊断
    """

    def post(self, request, *args, **kwargs):
        """
        POST请求接口
        :param request: 请求对象
        :return: 诊断结果
        """
        custom_id = kwargs.get('custom_id')  # 默认给一个整数ID，避免错误
        ecg_data = None
        union_id = None

        param_list = ['signal', 'fs', 'adc_gain', 'adc_zero', 'union_id', 'ecg_age_key', 'health_metrics']

        try:
            data = json.loads(request.body)

            # 验证参数是否存在
            missing_params = [param for param in param_list if param not in data]

            if len(missing_params) > 0:
                return GetResponse.get_response(code=6, data=f"所需参数 {','.join(missing_params)} 不存在")

            ecg_data_str = data['signal']  # ecg信号

            if isinstance(ecg_data_str, list):
                ecg_data = np.array(data['signal'])
            elif ecg_data_str.find('[') != -1 and ecg_data_str.find(']') != -1:
                ecg_data = np.array(ast.literal_eval(f"{ecg_data_str}"))
            else:
                ecg_data = np.array(ast.literal_eval(f"[{ecg_data_str}]"))  # 将心电信号转为nparray

            sampling_rate = data['fs']  # 采样率
            gain = data['adc_gain']  # 增益
            zero = data['adc_zero']  # 零点（基线）
            union_id = data['union_id']  # 用户ID
            ecg_age_key = data['ecg_age_key']  # 心脏年龄 0-不需要计算 1-需要计算
            health_metrics = data['health_metrics']  # 情绪指数 0-不需要计算 1-需要计算

            sampling_rate = int(sampling_rate)

            if len(ecg_data) < sampling_rate * 10:
                return GetResponse.get_response(code=6, data='心电信号不能小于10秒')

            if ecg_data is None or sampling_rate is None or gain is None or zero is None or gain <= 0:
                return GetResponse.get_response(code=5)

            if not isinstance(sampling_rate, int):
                return GetResponse.get_response(code=6, data='采样率请传递整数')

            ecg_data = (ecg_data - zero) / gain  # 计算实际电压（检测电压-基线）/ 增益

            # 分析实体对象
            analysis_entity = AnalysisEntity()

            # 获取可用信号
            raw_signal_quality, normal_time_period_list = get_available_signals(ecg_data, sampling_rate)
            # analysis_entity.SignalQuantity = signal_quality  # 信号质量 1-正常,-1-噪声 <-- 这行将被新的逻辑取代
            
            # -- 新增噪音计算逻辑 开始 --
            total_signal_duration_s = 0
            if sampling_rate > 0: # 避免除以零
                total_signal_duration_s = len(ecg_data) / sampling_rate

            normal_duration_s = 0
            if raw_signal_quality != -1 and normal_time_period_list:
                for period_info in normal_time_period_list:
                    time_period = period_info.get('time_period')
                    if time_period and len(time_period) == 2 and isinstance(time_period[0], (int, float)) and isinstance(time_period[1], (int, float)):
                        # 确保时间段的起始和结束都是数值类型
                        start_s = time_period[0]
                        end_s = time_period[1]
                        if end_s > start_s: # 确保结束时间大于开始时间
                            normal_duration_s += (end_s - start_s)
            
            noise_percentage = 100.0 # 默认为100%噪音
            if total_signal_duration_s > 0:
                noise_percentage = ((total_signal_duration_s - normal_duration_s) / total_signal_duration_s) * 100
            # -- 新增噪音计算逻辑 结束 --
            
            # -- 开始三层噪音处理逻辑 --
            waveform_info = None # Initialize waveform_info to None
            pqrstc = None # Initialize pqrstc to None
            
            if noise_percentage > 80:
                analysis_entity.SignalQuantity = -1
                # 对于高噪音，许多诊断字段保持默认/空状态
                analysis_entity.ArrhythmiaDiagnosis = ArrhythmiaDiagnosisEntity()
                analysis_entity.CADCardiomyopathy = CadCardiomyopathyEntity()
                analysis_entity.HealthMetrics = HealthMetricsEntity()
                analysis_entity.PQRSTC = None
                analysis_entity.ECGAge = 0
                
                if total_signal_duration_s > 0:
                    analysis_entity.SignalTimePeriod = f"0-{total_signal_duration_s:.2f} (High Noise)"
                else:
                    analysis_entity.SignalTimePeriod = "N/A (High Noise)"

                analysis_entity.ecg_id = save_api_log(ecg_data_str, sampling_rate, gain, zero, analysis_entity, custom_id)
                return GetResponse.get_response(code=8, data={"message": "信号噪音过高，分析中止", "noise_percentage": round(noise_percentage, 2), "SignalQuantity": -1, "analysis_id": analysis_entity.ecg_id if hasattr(analysis_entity, 'ecg_id') else None})

            elif noise_percentage > 40: # 40% < 噪音 <= 80%
                analysis_entity.SignalQuantity = 0

                # 提取 ecg_data_processed (基于 normal_time_period_list[0] 或默认)
                first_segment_info = normal_time_period_list[0] if normal_time_period_list and isinstance(normal_time_period_list, list) else {}
                actual_time_period_s = first_segment_info.get('time_period', [0, min(10, total_signal_duration_s if total_signal_duration_s > 0 else 10)]) 
                # Ensure actual_time_period_s has at least two elements and are numbers
                start_time_s = actual_time_period_s[0] if len(actual_time_period_s) > 0 and isinstance(actual_time_period_s[0], (int,float)) else 0
                end_time_s = actual_time_period_s[1] if len(actual_time_period_s) > 1 and isinstance(actual_time_period_s[1], (int,float)) else min(start_time_s + 10, total_signal_duration_s if total_signal_duration_s > 0 else 10)
                if end_time_s <= start_time_s: # Ensure end_time is after start_time and there's a minimal duration for processing
                    end_time_s = min(start_time_s + 10, total_signal_duration_s if total_signal_duration_s > 0 else 10) 
                    if end_time_s <= start_time_s : # If total_signal_duration_s is too short, e.g. 0
                         start_time_s = 0 # reset to default if sensible segment can't be derived.
                         end_time_s = min(10, total_signal_duration_s if total_signal_duration_s > 0 else 10)

                analysis_entity.SignalTimePeriod = f'{start_time_s:.2f}-{end_time_s:.2f}'
                start_sample_idx = int(start_time_s * sampling_rate)
                end_sample_idx = int(end_time_s * sampling_rate)
                ecg_data_processed = ecg_data[start_sample_idx:end_sample_idx]

                if len(ecg_data_processed) < sampling_rate * 1: # Minimal length for get_waveform, e.g. 1 sec
                    # Not enough data even in the selected segment for reliable basic analysis
                    analysis_entity.ArrhythmiaDiagnosis = ArrhythmiaDiagnosisEntity() # Empty
                    analysis_entity.CADCardiomyopathy = CadCardiomyopathyEntity()
                    analysis_entity.HealthMetrics = HealthMetricsEntity()
                    analysis_entity.PQRSTC = None 
                    analysis_entity.ECGAge = 0
                    # analysis_entity.SignalTimePeriod is already set
                else:
                    waveform_info = get_waveform(ecg_data_processed, sampling_rate)
                    if waveform_info is None:
                        # This implies get_waveform failed, treat as very poor signal segment
                        analysis_entity.ArrhythmiaDiagnosis = ArrhythmiaDiagnosisEntity()
                        analysis_entity.CADCardiomyopathy = CadCardiomyopathyEntity()
                        analysis_entity.HealthMetrics = HealthMetricsEntity()
                        analysis_entity.PQRSTC = None
                        analysis_entity.ECGAge = 0
                        # Optionally, could return GetResponse.get_response(code=7) if this failure is critical
                    else:
                        pqrstc = pqrstc_diagnosis.process(waveform_info)
                        new_diagnosis = ArrhythmiaDiagnosisEntity()
                        hr = pqrstc.HR if pqrstc and hasattr(pqrstc, 'HR') else 75
                        if hr > 100: new_diagnosis.SNT = 1
                        elif hr < 60: new_diagnosis.SNB = 1
                        else: new_diagnosis.SN = 1
                        analysis_entity.ArrhythmiaDiagnosis = new_diagnosis
                        analysis_entity.CADCardiomyopathy = CadCardiomyopathyEntity() # Empty for this noise level
                        analysis_entity.HealthMetrics = HealthMetricsEntity() # Empty for this noise level
                        analysis_entity.ECGAge = 0 # No age calculation for this noise level
                        analysis_entity.PQRSTC = pqrstc
                        analysis_entity.RRIntervals = ','.join(map(str, waveform_info.get('waveform', {}).get('rr_intervals', [])))
                        analysis_entity.NNIntervals = ','.join(map(str, waveform_info.get('waveform', {}).get('nn_intervals', [])))

            else: # 噪音 <= 40%
                analysis_entity.SignalQuantity = 1 # 优质信号

                # 提取 ecg_data_processed (基于 normal_time_period_list[0] 或默认)
                # This logic is similar to the elif branch, ensure precomputed_features is handled if needed by arrhythmia_diagnosis
                first_segment_info = normal_time_period_list[0] if normal_time_period_list and isinstance(normal_time_period_list, list) else {}
                actual_time_period_s = first_segment_info.get('time_period', [0, min(10, total_signal_duration_s if total_signal_duration_s > 0 else 10)])
                precomputed_features = first_segment_info.get('features', {}) # Get precomputed features for this segment

                start_time_s = actual_time_period_s[0] if len(actual_time_period_s) > 0 and isinstance(actual_time_period_s[0], (int,float)) else 0
                end_time_s = actual_time_period_s[1] if len(actual_time_period_s) > 1 and isinstance(actual_time_period_s[1], (int,float)) else min(start_time_s + 10, total_signal_duration_s if total_signal_duration_s > 0 else 10)
                if end_time_s <= start_time_s:
                    end_time_s = min(start_time_s + 10, total_signal_duration_s if total_signal_duration_s > 0 else 10)
                    if end_time_s <= start_time_s :
                         start_time_s = 0
                         end_time_s = min(10, total_signal_duration_s if total_signal_duration_s > 0 else 10)
                
                analysis_entity.SignalTimePeriod = f'{start_time_s:.2f}-{end_time_s:.2f}'
                start_sample_idx = int(start_time_s * sampling_rate)
                end_sample_idx = int(end_time_s * sampling_rate)
                ecg_data_processed = ecg_data[start_sample_idx:end_sample_idx]

                if len(ecg_data_processed) < sampling_rate * 1: # Minimal length for get_waveform
                    # Not enough data for reliable full analysis
                    # Fallback to basic sinus rhythm based on overall HR if possible, or just empty
                    # For simplicity here, setting to empty. Or could mimic the noise_percentage > 40 logic.
                    analysis_entity.ArrhythmiaDiagnosis = ArrhythmiaDiagnosisEntity()
                    analysis_entity.CADCardiomyopathy = CadCardiomyopathyEntity()
                    analysis_entity.HealthMetrics = HealthMetricsEntity()
                    analysis_entity.PQRSTC = None
                    analysis_entity.ECGAge = 0
                else:
                    waveform_info = get_waveform(ecg_data_processed, sampling_rate)
                    if waveform_info is None:
                        return GetResponse.get_response(code=7) # Critical failure if waveform cannot be processed for low noise

                    pqrstc = pqrstc_diagnosis.process(waveform_info)
                    analysis_entity.PQRSTC = pqrstc

                    # 白名单处理 (仅在此分支)
                    whitelist_arrhythmia_diagnosis, whitelist_ecg_age = None, None
                    # The original raw_signal_quality was based on get_available_signals not returning -1
                    # We use analysis_entity.SignalQuantity == 1 here (low noise)
                    if union_id: # Check if union_id is available before calling whitelist
                         whitelist_arrhythmia_diagnosis, whitelist_ecg_age = whitelist.process(union_id)

                    # 心脏年龄
                    if ecg_age_key == 1:
                        if whitelist_ecg_age:
                            analysis_entity.ECGAge = whitelist_ecg_age
                        else:
                            analysis_entity.ECGAge = ecg_age_diagnosis.process(ecg_data_processed, sampling_rate) # Corrected to ecg_data_processed
                    else:
                        analysis_entity.ECGAge = 0 # Default if not requested

                    # 心率失常诊断
                    if whitelist_arrhythmia_diagnosis:
                        analysis_entity.ArrhythmiaDiagnosis = whitelist_arrhythmia_diagnosis
                    else:
                        analysis_entity.ArrhythmiaDiagnosis = arrhythmia_diagnosis_diagnosis.process(
                            ecg_data_processed, # Corrected to ecg_data_processed
                            sampling_rate,
                            waveform_info,
                            precomputed_signal_features=precomputed_features
                        )
                    
                    analysis_entity.CADCardiomyopathy = cad_cardiomyopathy_diagnosis.process(waveform_info)
                    
                    if health_metrics == 1:
                        analysis_entity.HealthMetrics = health_metrics_diagnosis.process(waveform_info)
                    else:
                        analysis_entity.HealthMetrics = HealthMetricsEntity()

                    analysis_entity.RRIntervals = ','.join(map(str, waveform_info.get('waveform', {}).get('rr_intervals', [])))
                    analysis_entity.NNIntervals = ','.join(map(str, waveform_info.get('waveform', {}).get('nn_intervals', [])))
            
            # -- 三层噪音处理逻辑结束 --

            # Default values for risks and other metrics if not set in branches
            if not hasattr(analysis_entity, 'HeartFailureRisk') or analysis_entity.HeartFailureRisk is None: # Example check
                analysis_entity.HeartFailureRisk = 0
            if not hasattr(analysis_entity, 'VentricularFibrillationRisk') or analysis_entity.VentricularFibrillationRisk is None:
                analysis_entity.VentricularFibrillationRisk = 0
            if not hasattr(analysis_entity, 'SyncopeRisk') or analysis_entity.SyncopeRisk is None:
                analysis_entity.SyncopeRisk = 0
            if not hasattr(analysis_entity, 'SleepStage') or analysis_entity.SleepStage is None:
                analysis_entity.SleepStage = 0
            if not hasattr(analysis_entity, 'OSARisk') or analysis_entity.OSARisk is None:
                analysis_entity.OSARisk = 0
            if not hasattr(analysis_entity, 'RespiratoryRate') or analysis_entity.RespiratoryRate is None:
                analysis_entity.RespiratoryRate = 0

            # 处理加速度 (common to medium and low noise branches if successful so far)
            if noise_percentage <= 80 and 'gravity' in data and data['gravity']:
                gravity_process(union_id, data['gravity'])

            # 诊断详情设置 (common to medium and low noise branches if waveform_info is available)
            if waveform_info: # Only if waveform_info was successfully processed
                analysis_entity.DiagnosisDetails = get_diagnosis_details(analysis_entity, waveform_info, sampling_rate)
            else: # Default for high noise or if waveform processing failed in medium noise
                analysis_entity.DiagnosisDetails = get_diagnosis_details(analysis_entity, {}, sampling_rate) # Pass empty dict for waveform_info

            # Save API log (common to medium and low noise branches, high noise branch saves and returns earlier)
            analysis_entity.ecg_id = save_api_log(ecg_data_str, sampling_rate, gain, zero, analysis_entity, custom_id)

            return GetResponse.get_response(code=0, data=filter_negative_values(analysis_entity.to_entity_dict()))

        except BiosppyEcgError as e:
            Logger().error(f'\n客户ID: {union_id}, 心电信号：{ecg_data}\n{traceback.format_exc()}')
            return GetResponse.get_response(code=7)
        except Exception as e:
            Logger().error(f'\n客户ID: {union_id}, 心电信号：{ecg_data}\n{traceback.format_exc()}')
            return GetResponse.get_response(code=2)
