/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// ConvertAffineForToGPU
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertAffineForToGPUBase : public ::mlir::FunctionPass {
public:
  using Base = ConvertAffineForToGPUBase;

  ConvertAffineForToGPUBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineForToGPUBase(const ConvertAffineForToGPUBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-affine-for-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-affine-for-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert top-level AffineFor Ops to GPU kernels"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineForToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineForToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<gpu::GPUDialect>();

  }

protected:
  ::mlir::Pass::Option<unsigned> numBlockDims{*this, "gpu-block-dims", ::llvm::cl::desc("Number of GPU block dimensions for mapping"), ::llvm::cl::init(1u)};
  ::mlir::Pass::Option<unsigned> numThreadDims{*this, "gpu-thread-dims", ::llvm::cl::desc("Number of GPU thread dimensions for mapping"), ::llvm::cl::init(1u)};
};

//===----------------------------------------------------------------------===//
// ConvertAffineToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertAffineToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertAffineToStandardBase;

  ConvertAffineToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineToStandardBase(const ConvertAffineToStandardBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-affine");
  }
  ::llvm::StringRef getArgument() const override { return "lower-affine"; }

  ::llvm::StringRef getDescription() const override { return "Lower Affine operations to a combination of Standard and SCF operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<StandardOpsDialect>();

  registry.insert<vector::VectorDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertArmNeon2dToIntr
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertArmNeon2dToIntrBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = ConvertArmNeon2dToIntrBase;

  ConvertArmNeon2dToIntrBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArmNeon2dToIntrBase(const ConvertArmNeon2dToIntrBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arm-neon-2d-to-intr");
  }
  ::llvm::StringRef getArgument() const override { return "arm-neon-2d-to-intr"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arm NEON structured ops to intrinsics"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArmNeon2dToIntr");
  }
  ::llvm::StringRef getName() const override { return "ConvertArmNeon2dToIntr"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arm_neon::ArmNeonDialect>();

  registry.insert<vector::VectorDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertAsyncToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertAsyncToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertAsyncToLLVMBase;

  ConvertAsyncToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAsyncToLLVMBase(const ConvertAsyncToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-async-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-async-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the async dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAsyncToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertAsyncToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertComplexToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertComplexToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertComplexToLLVMBase;

  ConvertComplexToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToLLVMBase(const ConvertComplexToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertComplexToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertComplexToStandardBase : public ::mlir::FunctionPass {
public:
  using Base = ConvertComplexToStandardBase;

  ConvertComplexToStandardBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToStandardBase(const ConvertComplexToStandardBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-standard");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-standard"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<complex::ComplexDialect>();

  registry.insert<math::MathDialect>();

  registry.insert<StandardOpsDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertGPUToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertGPUToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertGPUToSPIRVBase;

  ConvertGPUToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGPUToSPIRVBase(const ConvertGPUToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGPUToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertGPUToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertGpuLaunchFuncToVulkanLaunchFunc
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertGpuLaunchFuncToVulkanLaunchFuncBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertGpuLaunchFuncToVulkanLaunchFuncBase;

  ConvertGpuLaunchFuncToVulkanLaunchFuncBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuLaunchFuncToVulkanLaunchFuncBase(const ConvertGpuLaunchFuncToVulkanLaunchFuncBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-launch-to-vulkan-launch");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-launch-to-vulkan-launch"; }

  ::llvm::StringRef getDescription() const override { return "Convert gpu.launch_func to vulkanLaunch external call"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuLaunchFuncToVulkanLaunchFunc");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuLaunchFuncToVulkanLaunchFunc"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToNVVMOps
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertGpuOpsToNVVMOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToNVVMOpsBase;

  ConvertGpuOpsToNVVMOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToNVVMOpsBase(const ConvertGpuOpsToNVVMOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-nvvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-nvvm"; }

  ::llvm::StringRef getDescription() const override { return "Generate NVVM operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToNVVMOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToNVVMOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<NVVM::NVVMDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToROCDLOps
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertGpuOpsToROCDLOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToROCDLOpsBase;

  ConvertGpuOpsToROCDLOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToROCDLOpsBase(const ConvertGpuOpsToROCDLOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Generate ROCDL operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToROCDLOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToROCDLOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<ROCDL::ROCDLDialect>();

  }

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

//===----------------------------------------------------------------------===//
// ConvertLinalgToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertLinalgToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToLLVMBase;

  ConvertLinalgToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToLLVMBase(const ConvertLinalgToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the linalg dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertLinalgToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertLinalgToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToSPIRVBase;

  ConvertLinalgToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToSPIRVBase(const ConvertLinalgToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Linalg dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertLinalgToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertLinalgToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToStandardBase;

  ConvertLinalgToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToStandardBase(const ConvertLinalgToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the linalg dialect into the Standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<StandardOpsDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertMathToLibm
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertMathToLibmBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToLibmBase;

  ConvertMathToLibmBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLibmBase(const ConvertMathToLibmBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-libm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-libm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to libm calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLibm");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLibm"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<StandardOpsDialect>();

  registry.insert<vector::VectorDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertOpenACCToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertOpenACCToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenACCToLLVMBase;

  ConvertOpenACCToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenACCToLLVMBase(const ConvertOpenACCToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openacc-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openacc-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenACC ops to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenACCToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenACCToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertOpenACCToSCF
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertOpenACCToSCFBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenACCToSCFBase;

  ConvertOpenACCToSCFBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenACCToSCFBase(const ConvertOpenACCToSCFBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openacc-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openacc-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenACC ops to OpenACC with SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenACCToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenACCToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  registry.insert<acc::OpenACCDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertOpenMPToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertOpenMPToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenMPToLLVMBase;

  ConvertOpenMPToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenMPToLLVMBase(const ConvertOpenMPToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openmp-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openmp-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenMP ops to OpenMP ops with LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenMPToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenMPToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertPDLToPDLInterp
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertPDLToPDLInterpBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertPDLToPDLInterpBase;

  ConvertPDLToPDLInterpBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertPDLToPDLInterpBase(const ConvertPDLToPDLInterpBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-pdl-to-pdl-interp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-pdl-to-pdl-interp"; }

  ::llvm::StringRef getDescription() const override { return "Convert PDL ops to PDL interpreter ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertPDLToPDLInterp");
  }
  ::llvm::StringRef getName() const override { return "ConvertPDLToPDLInterp"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<pdl_interp::PDLInterpDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertParallelLoopToGpu
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertParallelLoopToGpuBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertParallelLoopToGpuBase;

  ConvertParallelLoopToGpuBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertParallelLoopToGpuBase(const ConvertParallelLoopToGpuBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-parallel-loops-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-parallel-loops-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert mapped scf.parallel ops to gpu launch operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertParallelLoopToGpu");
  }
  ::llvm::StringRef getName() const override { return "ConvertParallelLoopToGpu"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<gpu::GPUDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertSCFToOpenMP
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertSCFToOpenMPBase : public ::mlir::FunctionPass {
public:
  using Base = ConvertSCFToOpenMPBase;

  ConvertSCFToOpenMPBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSCFToOpenMPBase(const ConvertSCFToOpenMPBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-openmp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-openmp"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF parallel loop to OpenMP parallel + workshare constructs."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSCFToOpenMP");
  }
  ::llvm::StringRef getName() const override { return "ConvertSCFToOpenMP"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<omp::OpenMPDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertSPIRVToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertSPIRVToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSPIRVToLLVMBase;

  ConvertSPIRVToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSPIRVToLLVMBase(const ConvertSPIRVToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-spirv-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-spirv-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert SPIR-V dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSPIRVToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertSPIRVToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertShapeConstraints
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertShapeConstraintsBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = ConvertShapeConstraintsBase;

  ConvertShapeConstraintsBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeConstraintsBase(const ConvertShapeConstraintsBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Convert shape constraint operations to the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeConstraints");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeConstraints"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<StandardOpsDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertShapeToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertShapeToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertShapeToStandardBase;

  ConvertShapeToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeToStandardBase(const ConvertShapeToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the shape dialect into the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<StandardOpsDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<tensor::TensorDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertStandardToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertStandardToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertStandardToLLVMBase;

  ConvertStandardToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertStandardToLLVMBase(const ConvertStandardToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-std-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-std-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert scalar and vector operations from the Standard to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertStandardToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertStandardToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> useAlignedAlloc{*this, "use-aligned-alloc", ::llvm::cl::desc("Use aligned_alloc in place of malloc for heap allocations"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace FuncOp's MemRef arguments with bare pointers to the MemRef element types"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> emitCWrappers{*this, "emit-c-wrappers", ::llvm::cl::desc("Emit wrappers for C-compatible pointer-to-struct memref descriptors"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<std::string> dataLayout{*this, "data-layout", ::llvm::cl::desc("String description (LLVM format) of the data layout that is expected on the produced module"), ::llvm::cl::init("")};
};

//===----------------------------------------------------------------------===//
// ConvertStandardToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertStandardToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertStandardToSPIRVBase;

  ConvertStandardToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertStandardToSPIRVBase(const ConvertStandardToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-std-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-std-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Standard dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertStandardToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertStandardToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> emulateNon32BitScalarTypes{*this, "emulate-non-32-bit-scalar-types", ::llvm::cl::desc("Emulate non-32-bit scalar types with 32-bit ones if missing native support"), ::llvm::cl::init(true)};
};

//===----------------------------------------------------------------------===//
// ConvertVectorToGPU
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVectorToGPUBase : public ::mlir::FunctionPass {
public:
  using Base = ConvertVectorToGPUBase;

  ConvertVectorToGPUBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToGPUBase(const ConvertVectorToGPUBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the GPU dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<gpu::GPUDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertVectorToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVectorToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVectorToLLVMBase;

  ConvertVectorToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToLLVMBase(const ConvertVectorToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> reassociateFPReductions{*this, "reassociate-fp-reductions", ::llvm::cl::desc("Allows llvm to reassociate floating-point reductions for speed"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> enableIndexOptimizations{*this, "enable-index-optimizations", ::llvm::cl::desc("Allows compiler to assume indices fit in 32-bit if that yields faster code"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableAMX{*this, "enable-amx", ::llvm::cl::desc("Enables the use of AMX dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> enableArmNeon{*this, "enable-arm-neon", ::llvm::cl::desc("Enables the use of ArmNeon dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> enableArmSVE{*this, "enable-arm-sve", ::llvm::cl::desc("Enables the use of ArmSVE dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> enableX86Vector{*this, "enable-x86vector", ::llvm::cl::desc("Enables the use of X86Vector dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// ConvertVectorToROCDL
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVectorToROCDLBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVectorToROCDLBase;

  ConvertVectorToROCDLBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToROCDLBase(const ConvertVectorToROCDLBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the ROCDL dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToROCDL");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToROCDL"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<ROCDL::ROCDLDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertVectorToSCF
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVectorToSCFBase : public ::mlir::FunctionPass {
public:
  using Base = ConvertVectorToSCFBase;

  ConvertVectorToSCFBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSCFBase(const ConvertVectorToSCFBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> fullUnroll{*this, "full-unroll", ::llvm::cl::desc("Perform full unrolling when converting vector transfers to SCF"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> targetRank{*this, "target-rank", ::llvm::cl::desc("Target vector rank to which transfer ops should be lowered"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> lowerPermutationMaps{*this, "lower-permutation-maps", ::llvm::cl::desc("Replace permutation maps with vector transposes/broadcasts before lowering transfer ops"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> lowerTensors{*this, "lower-tensors", ::llvm::cl::desc("Lower transfer ops that operate on tensors"), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// ConvertVectorToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVectorToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVectorToSPIRVBase;

  ConvertVectorToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSPIRVBase(const ConvertVectorToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Vector dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertVulkanLaunchFuncToVulkanCalls
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVulkanLaunchFuncToVulkanCallsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVulkanLaunchFuncToVulkanCallsBase;

  ConvertVulkanLaunchFuncToVulkanCallsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVulkanLaunchFuncToVulkanCallsBase(const ConvertVulkanLaunchFuncToVulkanCallsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("launch-func-to-vulkan");
  }
  ::llvm::StringRef getArgument() const override { return "launch-func-to-vulkan"; }

  ::llvm::StringRef getDescription() const override { return "Convert vulkanLaunch external call to Vulkan runtime external calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVulkanLaunchFuncToVulkanCalls");
  }
  ::llvm::StringRef getName() const override { return "ConvertVulkanLaunchFuncToVulkanCalls"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// GpuToLLVMConversionPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class GpuToLLVMConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GpuToLLVMConversionPassBase;

  GpuToLLVMConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuToLLVMConversionPassBase(const GpuToLLVMConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to LLVM dialect with GPU runtime calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "GpuToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LowerHostCodeToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LowerHostCodeToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LowerHostCodeToLLVMBase;

  LowerHostCodeToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerHostCodeToLLVMBase(const LowerHostCodeToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-host-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "lower-host-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lowers the host module code and `gpu.launch_func` to LLVM"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerHostCodeToLLVM");
  }
  ::llvm::StringRef getName() const override { return "LowerHostCodeToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SCFToSPIRVBase;

  SCFToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToSPIRVBase(const SCFToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to SPIR-V dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "SCFToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToStandardBase;

  SCFToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToStandardBase(const SCFToStandardBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to Standard dialect, replacing structured control flow with a CFG"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToStandard");
  }
  ::llvm::StringRef getName() const override { return "SCFToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<StandardOpsDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// TosaToLinalgOnTensors
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaToLinalgOnTensorsBase : public ::mlir::FunctionPass {
public:
  using Base = TosaToLinalgOnTensorsBase;

  TosaToLinalgOnTensorsBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgOnTensorsBase(const TosaToLinalgOnTensorsBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg-on-tensors");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg-on-tensors"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalgOnTensors");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalgOnTensors"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TosaToSCF
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToSCFBase;

  TosaToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToSCFBase(const TosaToSCFBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToSCF");
  }
  ::llvm::StringRef getName() const override { return "TosaToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<tensor::TensorDialect, scf::SCFDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// TosaToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToStandardBase;

  TosaToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToStandardBase(const TosaToStandardBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-standard");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-standard"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the Standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToStandard");
  }
  ::llvm::StringRef getName() const override { return "TosaToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<StandardOpsDialect>();

  registry.insert<tensor::TensorDialect>();

  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ConvertAffineForToGPU Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAffineForToGPUPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineForToGPUPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAffineToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAffineToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerAffinePass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArmNeon2dToIntr Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArmNeon2dToIntrPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertArmNeon2dToIntrPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAsyncToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAsyncToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertAsyncToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGPUToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGPUToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertGPUToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuLaunchFuncToVulkanLaunchFunc Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuLaunchFuncToVulkanLaunchFuncPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertGpuLaunchFuncToVulkanLaunchFuncPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToNVVMOps Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuOpsToNVVMOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerGpuOpsToNVVMOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToROCDLOps Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuOpsToROCDLOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerGpuOpsToROCDLOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToLibm Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToLibmPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToLibmPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenACCToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenACCToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertOpenACCToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenACCToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenACCToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertOpenACCToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenMPToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenMPToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertOpenMPToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertPDLToPDLInterp Registration
//===----------------------------------------------------------------------===//

inline void registerConvertPDLToPDLInterpPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPDLToPDLInterpPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertParallelLoopToGpu Registration
//===----------------------------------------------------------------------===//

inline void registerConvertParallelLoopToGpuPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopToGpuPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertSCFToOpenMP Registration
//===----------------------------------------------------------------------===//

inline void registerConvertSCFToOpenMPPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSCFToOpenMPPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertSPIRVToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertSPIRVToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSPIRVToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertShapeConstraints Registration
//===----------------------------------------------------------------------===//

inline void registerConvertShapeConstraintsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeConstraintsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertShapeToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertShapeToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertStandardToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertStandardToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertStandardToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertStandardToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertStandardToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToGPU Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToGPUPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToGPUPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToROCDL Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToROCDLPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToROCDLPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVulkanLaunchFuncToVulkanCalls Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVulkanLaunchFuncToVulkanCallsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVulkanLaunchFuncToVulkanCallsPass();
  });
}

//===----------------------------------------------------------------------===//
// GpuToLLVMConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerGpuToLLVMConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuToLLVMConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// LowerHostCodeToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerLowerHostCodeToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerHostCodeToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerSCFToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSCFToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerSCFToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerToCFGPass();
  });
}

//===----------------------------------------------------------------------===//
// TosaToLinalgOnTensors Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToLinalgOnTensorsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalgOnTensors();
  });
}

//===----------------------------------------------------------------------===//
// TosaToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToSCF();
  });
}

//===----------------------------------------------------------------------===//
// TosaToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToStandard();
  });
}

//===----------------------------------------------------------------------===//
// Conversion Registration
//===----------------------------------------------------------------------===//

inline void registerConversionPasses() {
  registerConvertAffineForToGPUPass();
  registerConvertAffineToStandardPass();
  registerConvertArmNeon2dToIntrPass();
  registerConvertAsyncToLLVMPass();
  registerConvertComplexToLLVMPass();
  registerConvertComplexToStandardPass();
  registerConvertGPUToSPIRVPass();
  registerConvertGpuLaunchFuncToVulkanLaunchFuncPass();
  registerConvertGpuOpsToNVVMOpsPass();
  registerConvertGpuOpsToROCDLOpsPass();
  registerConvertLinalgToLLVMPass();
  registerConvertLinalgToSPIRVPass();
  registerConvertLinalgToStandardPass();
  registerConvertMathToLibmPass();
  registerConvertOpenACCToLLVMPass();
  registerConvertOpenACCToSCFPass();
  registerConvertOpenMPToLLVMPass();
  registerConvertPDLToPDLInterpPass();
  registerConvertParallelLoopToGpuPass();
  registerConvertSCFToOpenMPPass();
  registerConvertSPIRVToLLVMPass();
  registerConvertShapeConstraintsPass();
  registerConvertShapeToStandardPass();
  registerConvertStandardToLLVMPass();
  registerConvertStandardToSPIRVPass();
  registerConvertVectorToGPUPass();
  registerConvertVectorToLLVMPass();
  registerConvertVectorToROCDLPass();
  registerConvertVectorToSCFPass();
  registerConvertVectorToSPIRVPass();
  registerConvertVulkanLaunchFuncToVulkanCallsPass();
  registerGpuToLLVMConversionPassPass();
  registerLowerHostCodeToLLVMPass();
  registerSCFToSPIRVPass();
  registerSCFToStandardPass();
  registerTosaToLinalgOnTensorsPass();
  registerTosaToSCFPass();
  registerTosaToStandardPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
