/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// AffineLoopFusion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineLoopFusionBase : public ::mlir::FunctionPass {
public:
  using Base = AffineLoopFusionBase;

  AffineLoopFusionBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopFusionBase(const AffineLoopFusionBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse affine loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopFusion");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

protected:
  ::mlir::Pass::Option<double> computeToleranceThreshold{*this, "fusion-compute-tolerance", ::llvm::cl::desc("Fractional increase in additional computation tolerated while fusing"), ::llvm::cl::init(0.30f)};
  ::mlir::Pass::Option<unsigned> fastMemorySpace{*this, "fusion-fast-mem-space", ::llvm::cl::desc("Faster memory space number to promote fusion buffers to"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<uint64_t> localBufSizeThreshold{*this, "fusion-local-buf-threshold", ::llvm::cl::desc("Threshold size (KiB) for promoting local buffers to fast memory space"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> maximalFusion{*this, "fusion-maximal", ::llvm::cl::desc("Enables maximal loop fusion"), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// AffinePipelineDataTransfer
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffinePipelineDataTransferBase : public ::mlir::FunctionPass {
public:
  using Base = AffinePipelineDataTransferBase;

  AffinePipelineDataTransferBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  AffinePipelineDataTransferBase(const AffinePipelineDataTransferBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-pipeline-data-transfer");
  }
  ::llvm::StringRef getArgument() const override { return "affine-pipeline-data-transfer"; }

  ::llvm::StringRef getDescription() const override { return "Pipeline non-blocking data transfers between explicitly managed levels of the memory hierarchy"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffinePipelineDataTransfer");
  }
  ::llvm::StringRef getName() const override { return "AffinePipelineDataTransfer"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// BufferDeallocation
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class BufferDeallocationBase : public ::mlir::FunctionPass {
public:
  using Base = BufferDeallocationBase;

  BufferDeallocationBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  BufferDeallocationBase(const BufferDeallocationBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-deallocation");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-deallocation"; }

  ::llvm::StringRef getDescription() const override { return "Adds all required dealloc operations for all allocations in the input program"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferDeallocation");
  }
  ::llvm::StringRef getName() const override { return "BufferDeallocation"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// BufferHoisting
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class BufferHoistingBase : public ::mlir::FunctionPass {
public:
  using Base = BufferHoistingBase;

  BufferHoistingBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  BufferHoistingBase(const BufferHoistingBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Optimizes placement of allocation operations by moving them into common dominators and out of nested regions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferHoisting");
  }
  ::llvm::StringRef getName() const override { return "BufferHoisting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// BufferLoopHoisting
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class BufferLoopHoistingBase : public ::mlir::FunctionPass {
public:
  using Base = BufferLoopHoistingBase;

  BufferLoopHoistingBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  BufferLoopHoistingBase(const BufferLoopHoistingBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-loop-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-loop-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Optimizes placement of allocation operations by moving them out of loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferLoopHoisting");
  }
  ::llvm::StringRef getName() const override { return "BufferLoopHoisting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// BufferResultsToOutParams
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class BufferResultsToOutParamsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = BufferResultsToOutParamsBase;

  BufferResultsToOutParamsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferResultsToOutParamsBase(const BufferResultsToOutParamsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-results-to-out-params");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-results-to-out-params"; }

  ::llvm::StringRef getDescription() const override { return "Converts memref-typed function results to out-params"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferResultsToOutParams");
  }
  ::llvm::StringRef getName() const override { return "BufferResultsToOutParams"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// CSE
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class CSEBase : public ::mlir::OperationPass<> {
public:
  using Base = CSEBase;

  CSEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CSEBase(const CSEBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("cse");
  }
  ::llvm::StringRef getArgument() const override { return "cse"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate common sub-expressions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CSE");
  }
  ::llvm::StringRef getName() const override { return "CSE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Statistic numCSE{this, "num-cse'd", "Number of operations CSE'd"};
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of operations DCE'd"};
};

//===----------------------------------------------------------------------===//
// Canonicalizer
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class CanonicalizerBase : public ::mlir::OperationPass<> {
public:
  using Base = CanonicalizerBase;

  CanonicalizerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CanonicalizerBase(const CanonicalizerBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("canonicalize");
  }
  ::llvm::StringRef getArgument() const override { return "canonicalize"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Canonicalizer");
  }
  ::llvm::StringRef getName() const override { return "Canonicalizer"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> topDownProcessingEnabled{*this, "top-down", ::llvm::cl::desc("Seed the worklist in general top-down order"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableRegionSimplification{*this, "region-simplify", ::llvm::cl::desc("Seed the worklist in general top-down order"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<unsigned> maxIterations{*this, "max-iterations", ::llvm::cl::desc("Seed the worklist in general top-down order"), ::llvm::cl::init(10)};
  ::mlir::Pass::ListOption<std::string> disabledPatterns{*this, "disable-patterns", ::llvm::cl::desc("Labels of patterns that should be filtered out during application"), llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::ListOption<std::string> enabledPatterns{*this, "enable-patterns", ::llvm::cl::desc("Labels of patterns that should be used during application, all other patterns are filtered out"), llvm::cl::MiscFlags::CommaSeparated};
};

//===----------------------------------------------------------------------===//
// FinalizingBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class FinalizingBufferizeBase : public ::mlir::FunctionPass {
public:
  using Base = FinalizingBufferizeBase;

  FinalizingBufferizeBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  FinalizingBufferizeBase(const FinalizingBufferizeBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("finalizing-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "finalizing-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Finalize a partial bufferization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FinalizingBufferize");
  }
  ::llvm::StringRef getName() const override { return "FinalizingBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// Inliner
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class InlinerBase : public ::mlir::OperationPass<> {
public:
  using Base = InlinerBase;

  InlinerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InlinerBase(const InlinerBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("inline");
  }
  ::llvm::StringRef getArgument() const override { return "inline"; }

  ::llvm::StringRef getDescription() const override { return "Inline function calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Inliner");
  }
  ::llvm::StringRef getName() const override { return "Inliner"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<std::string> defaultPipelineStr{*this, "default-pipeline", ::llvm::cl::desc("The default optimizer pipeline used for callables")};
  ::mlir::Pass::ListOption<std::string> opPipelineStrs{*this, "op-pipelines", ::llvm::cl::desc("Callable operation specific optimizer pipelines (in the form of `dialect.op(pipeline)`)"), llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::Option<unsigned> maxInliningIterations{*this, "max-iterations", ::llvm::cl::desc("Maximum number of iterations when inlining within an SCC"), ::llvm::cl::init(4)};
};

//===----------------------------------------------------------------------===//
// LocationSnapshot
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LocationSnapshotBase : public ::mlir::OperationPass<> {
public:
  using Base = LocationSnapshotBase;

  LocationSnapshotBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LocationSnapshotBase(const LocationSnapshotBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("snapshot-op-locations");
  }
  ::llvm::StringRef getArgument() const override { return "snapshot-op-locations"; }

  ::llvm::StringRef getDescription() const override { return "Generate new locations from the current IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LocationSnapshot");
  }
  ::llvm::StringRef getName() const override { return "LocationSnapshot"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<std::string> fileName{*this, "filename", ::llvm::cl::desc("The filename to print the generated IR")};
  ::mlir::Pass::Option<std::string> tag{*this, "tag", ::llvm::cl::desc("A tag to use when fusing the new locations with the original. If unset, the locations are replaced.")};
};

//===----------------------------------------------------------------------===//
// LoopCoalescing
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LoopCoalescingBase : public ::mlir::FunctionPass {
public:
  using Base = LoopCoalescingBase;

  LoopCoalescingBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  LoopCoalescingBase(const LoopCoalescingBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("loop-coalescing");
  }
  ::llvm::StringRef getArgument() const override { return "loop-coalescing"; }

  ::llvm::StringRef getDescription() const override { return "Coalesce nested loops with independent bounds into a single loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopCoalescing");
  }
  ::llvm::StringRef getName() const override { return "LoopCoalescing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// LoopInvariantCodeMotion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LoopInvariantCodeMotionBase : public ::mlir::OperationPass<> {
public:
  using Base = LoopInvariantCodeMotionBase;

  LoopInvariantCodeMotionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LoopInvariantCodeMotionBase(const LoopInvariantCodeMotionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("loop-invariant-code-motion");
  }
  ::llvm::StringRef getArgument() const override { return "loop-invariant-code-motion"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant instructions outside of the loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopInvariantCodeMotion");
  }
  ::llvm::StringRef getName() const override { return "LoopInvariantCodeMotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// NormalizeMemRefs
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class NormalizeMemRefsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = NormalizeMemRefsBase;

  NormalizeMemRefsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  NormalizeMemRefsBase(const NormalizeMemRefsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("normalize-memrefs");
  }
  ::llvm::StringRef getArgument() const override { return "normalize-memrefs"; }

  ::llvm::StringRef getDescription() const override { return "Normalize memrefs"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("NormalizeMemRefs");
  }
  ::llvm::StringRef getName() const override { return "NormalizeMemRefs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ParallelLoopCollapsing
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ParallelLoopCollapsingBase : public ::mlir::OperationPass<> {
public:
  using Base = ParallelLoopCollapsingBase;

  ParallelLoopCollapsingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ParallelLoopCollapsingBase(const ParallelLoopCollapsingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("parallel-loop-collapsing");
  }
  ::llvm::StringRef getArgument() const override { return "parallel-loop-collapsing"; }

  ::llvm::StringRef getDescription() const override { return "Collapse parallel loops to use less induction variables"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ParallelLoopCollapsing");
  }
  ::llvm::StringRef getName() const override { return "ParallelLoopCollapsing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices0{*this, "collapsed-indices-0", ::llvm::cl::desc("Which loop indices to combine 0th loop index"), llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices1{*this, "collapsed-indices-1", ::llvm::cl::desc("Which loop indices to combine into the position 1 loop index"), llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices2{*this, "collapsed-indices-2", ::llvm::cl::desc("Which loop indices to combine into the position 2 loop index"), llvm::cl::MiscFlags::CommaSeparated};
};

//===----------------------------------------------------------------------===//
// PrintCFG
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class PrintCFGBase : public ::mlir::FunctionPass {
public:
  using Base = PrintCFGBase;

  PrintCFGBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  PrintCFGBase(const PrintCFGBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-cfg-graph");
  }
  ::llvm::StringRef getArgument() const override { return "print-cfg-graph"; }

  ::llvm::StringRef getDescription() const override { return "Print CFG graph per-Region"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintCFG");
  }
  ::llvm::StringRef getName() const override { return "PrintCFG"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// PrintOpStats
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class PrintOpStatsBase : public ::mlir::OperationPass<> {
public:
  using Base = PrintOpStatsBase;

  PrintOpStatsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PrintOpStatsBase(const PrintOpStatsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-op-stats");
  }
  ::llvm::StringRef getArgument() const override { return "print-op-stats"; }

  ::llvm::StringRef getDescription() const override { return "Print statistics of operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintOpStats");
  }
  ::llvm::StringRef getName() const override { return "PrintOpStats"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// PromoteBuffersToStack
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class PromoteBuffersToStackBase : public ::mlir::FunctionPass {
public:
  using Base = PromoteBuffersToStackBase;

  PromoteBuffersToStackBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  PromoteBuffersToStackBase(const PromoteBuffersToStackBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("promote-buffers-to-stack");
  }
  ::llvm::StringRef getArgument() const override { return "promote-buffers-to-stack"; }

  ::llvm::StringRef getDescription() const override { return "Promotes heap-based allocations to automatically managed stack-based allocations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PromoteBuffersToStack");
  }
  ::llvm::StringRef getName() const override { return "PromoteBuffersToStack"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<unsigned> maxAllocSizeInBytes{*this, "max-alloc-size-in-bytes", ::llvm::cl::desc("Maximal size in bytes to promote allocations to stack."), ::llvm::cl::init(1024)};
  ::mlir::Pass::Option<unsigned> bitwidthOfIndexType{*this, "bitwidth-of-index-type", ::llvm::cl::desc("Bitwidth of the index type. Used for size estimation."), ::llvm::cl::init(64)};
  ::mlir::Pass::Option<unsigned> maxRankOfAllocatedMemRef{*this, "max-rank-of-allocated-memref", ::llvm::cl::desc("Maximal memref rank to promote dynamic buffers."), ::llvm::cl::init(1)};
};

//===----------------------------------------------------------------------===//
// SCCP
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCCPBase : public ::mlir::OperationPass<> {
public:
  using Base = SCCPBase;

  SCCPBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCCPBase(const SCCPBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sccp");
  }
  ::llvm::StringRef getArgument() const override { return "sccp"; }

  ::llvm::StringRef getDescription() const override { return "Sparse Conditional Constant Propagation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCCP");
  }
  ::llvm::StringRef getName() const override { return "SCCP"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// StripDebugInfo
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class StripDebugInfoBase : public ::mlir::OperationPass<> {
public:
  using Base = StripDebugInfoBase;

  StripDebugInfoBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  StripDebugInfoBase(const StripDebugInfoBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("strip-debuginfo");
  }
  ::llvm::StringRef getArgument() const override { return "strip-debuginfo"; }

  ::llvm::StringRef getDescription() const override { return "Strip debug info from all operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StripDebugInfo");
  }
  ::llvm::StringRef getName() const override { return "StripDebugInfo"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SymbolDCE
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SymbolDCEBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolDCEBase;

  SymbolDCEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolDCEBase(const SymbolDCEBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-dce");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-dce"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate dead symbols"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolDCE");
  }
  ::llvm::StringRef getName() const override { return "SymbolDCE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// ViewOpGraphPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ViewOpGraphPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ViewOpGraphPassBase;

  ViewOpGraphPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ViewOpGraphPassBase(const ViewOpGraphPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("view-op-graph");
  }
  ::llvm::StringRef getArgument() const override { return "view-op-graph"; }

  ::llvm::StringRef getDescription() const override { return "Print graphviz view of module"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ViewOpGraphPass");
  }
  ::llvm::StringRef getName() const override { return "ViewOpGraphPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<std::string> title{*this, "title", ::llvm::cl::desc("The prefix of the title of the graph")};
  ::mlir::Pass::Option<bool> shortNames{*this, "short-names", ::llvm::cl::desc("Use short names"), ::llvm::cl::init(false)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AffineLoopFusion Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// AffinePipelineDataTransfer Registration
//===----------------------------------------------------------------------===//

inline void registerAffinePipelineDataTransferPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPipelineDataTransferPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferDeallocation Registration
//===----------------------------------------------------------------------===//

inline void registerBufferDeallocationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createBufferDeallocationPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferHoisting Registration
//===----------------------------------------------------------------------===//

inline void registerBufferHoistingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createBufferHoistingPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferLoopHoisting Registration
//===----------------------------------------------------------------------===//

inline void registerBufferLoopHoistingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createBufferLoopHoistingPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferResultsToOutParams Registration
//===----------------------------------------------------------------------===//

inline void registerBufferResultsToOutParamsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createBufferResultsToOutParamsPass();
  });
}

//===----------------------------------------------------------------------===//
// CSE Registration
//===----------------------------------------------------------------------===//

inline void registerCSEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCSEPass();
  });
}

//===----------------------------------------------------------------------===//
// Canonicalizer Registration
//===----------------------------------------------------------------------===//

inline void registerCanonicalizerPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCanonicalizerPass();
  });
}

//===----------------------------------------------------------------------===//
// FinalizingBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerFinalizingBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createFinalizingBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// Inliner Registration
//===----------------------------------------------------------------------===//

inline void registerInlinerPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createInlinerPass();
  });
}

//===----------------------------------------------------------------------===//
// LocationSnapshot Registration
//===----------------------------------------------------------------------===//

inline void registerLocationSnapshotPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLocationSnapshotPass();
  });
}

//===----------------------------------------------------------------------===//
// LoopCoalescing Registration
//===----------------------------------------------------------------------===//

inline void registerLoopCoalescingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopCoalescingPass();
  });
}

//===----------------------------------------------------------------------===//
// LoopInvariantCodeMotion Registration
//===----------------------------------------------------------------------===//

inline void registerLoopInvariantCodeMotionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopInvariantCodeMotionPass();
  });
}

//===----------------------------------------------------------------------===//
// NormalizeMemRefs Registration
//===----------------------------------------------------------------------===//

inline void registerNormalizeMemRefsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createNormalizeMemRefsPass();
  });
}

//===----------------------------------------------------------------------===//
// ParallelLoopCollapsing Registration
//===----------------------------------------------------------------------===//

inline void registerParallelLoopCollapsingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopCollapsingPass();
  });
}

//===----------------------------------------------------------------------===//
// PrintCFG Registration
//===----------------------------------------------------------------------===//

inline void registerPrintCFGPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintCFGGraphPass();
  });
}

//===----------------------------------------------------------------------===//
// PrintOpStats Registration
//===----------------------------------------------------------------------===//

inline void registerPrintOpStatsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpStatsPass();
  });
}

//===----------------------------------------------------------------------===//
// PromoteBuffersToStack Registration
//===----------------------------------------------------------------------===//

inline void registerPromoteBuffersToStackPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPromoteBuffersToStackPass();
  });
}

//===----------------------------------------------------------------------===//
// SCCP Registration
//===----------------------------------------------------------------------===//

inline void registerSCCPPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCCPPass();
  });
}

//===----------------------------------------------------------------------===//
// StripDebugInfo Registration
//===----------------------------------------------------------------------===//

inline void registerStripDebugInfoPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createStripDebugInfoPass();
  });
}

//===----------------------------------------------------------------------===//
// SymbolDCE Registration
//===----------------------------------------------------------------------===//

inline void registerSymbolDCEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolDCEPass();
  });
}

//===----------------------------------------------------------------------===//
// ViewOpGraphPass Registration
//===----------------------------------------------------------------------===//

inline void registerViewOpGraphPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpGraphPass();
  });
}

//===----------------------------------------------------------------------===//
// Transforms Registration
//===----------------------------------------------------------------------===//

inline void registerTransformsPasses() {
  registerAffineLoopFusionPass();
  registerAffinePipelineDataTransferPass();
  registerBufferDeallocationPass();
  registerBufferHoistingPass();
  registerBufferLoopHoistingPass();
  registerBufferResultsToOutParamsPass();
  registerCSEPass();
  registerCanonicalizerPass();
  registerFinalizingBufferizePass();
  registerInlinerPass();
  registerLocationSnapshotPass();
  registerLoopCoalescingPass();
  registerLoopInvariantCodeMotionPass();
  registerNormalizeMemRefsPass();
  registerParallelLoopCollapsingPass();
  registerPrintCFGPass();
  registerPrintOpStatsPass();
  registerPromoteBuffersToStackPass();
  registerSCCPPass();
  registerStripDebugInfoPass();
  registerSymbolDCEPass();
  registerViewOpGraphPassPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
