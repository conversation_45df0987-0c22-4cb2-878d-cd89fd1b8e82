# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/core/protobuf/config.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.core.framework import cost_graph_pb2 as tensorflow_dot_core_dot_framework_dot_cost__graph__pb2
from tensorflow.core.framework import graph_pb2 as tensorflow_dot_core_dot_framework_dot_graph__pb2
from tensorflow.core.framework import step_stats_pb2 as tensorflow_dot_core_dot_framework_dot_step__stats__pb2
from tensorflow.core.protobuf import cluster_pb2 as tensorflow_dot_core_dot_protobuf_dot_cluster__pb2
from tensorflow.core.protobuf import debug_pb2 as tensorflow_dot_core_dot_protobuf_dot_debug__pb2
from tensorflow.core.protobuf import rewriter_config_pb2 as tensorflow_dot_core_dot_protobuf_dot_rewriter__config__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/core/protobuf/config.proto',
  package='tensorflow',
  syntax='proto3',
  serialized_options=_b('\n\030org.tensorflow.frameworkB\014ConfigProtosP\001ZUgithub.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto\370\001\001'),
  serialized_pb=_b('\n%tensorflow/core/protobuf/config.proto\x12\ntensorflow\x1a*tensorflow/core/framework/cost_graph.proto\x1a%tensorflow/core/framework/graph.proto\x1a*tensorflow/core/framework/step_stats.proto\x1a&tensorflow/core/protobuf/cluster.proto\x1a$tensorflow/core/protobuf/debug.proto\x1a.tensorflow/core/protobuf/rewriter_config.proto\"\x91\x06\n\nGPUOptions\x12\'\n\x1fper_process_gpu_memory_fraction\x18\x01 \x01(\x01\x12\x14\n\x0c\x61llow_growth\x18\x04 \x01(\x08\x12\x16\n\x0e\x61llocator_type\x18\x02 \x01(\t\x12\x1f\n\x17\x64\x65\x66\x65rred_deletion_bytes\x18\x03 \x01(\x03\x12\x1b\n\x13visible_device_list\x18\x05 \x01(\t\x12\"\n\x1apolling_active_delay_usecs\x18\x06 \x01(\x05\x12$\n\x1cpolling_inactive_delay_msecs\x18\x07 \x01(\x05\x12\x1c\n\x14\x66orce_gpu_compatible\x18\x08 \x01(\x08\x12\x39\n\x0c\x65xperimental\x18\t \x01(\x0b\x32#.tensorflow.GPUOptions.Experimental\x1a\xca\x03\n\x0c\x45xperimental\x12K\n\x0fvirtual_devices\x18\x01 \x03(\x0b\x32\x32.tensorflow.GPUOptions.Experimental.VirtualDevices\x12\x1a\n\x12use_unified_memory\x18\x02 \x01(\x08\x12#\n\x1bnum_dev_to_dev_copy_streams\x18\x03 \x01(\x05\x12\x1d\n\x15\x63ollective_ring_order\x18\x04 \x01(\t\x12\x1d\n\x15timestamped_allocator\x18\x05 \x01(\x08\x12#\n\x1bkernel_tracker_max_interval\x18\x07 \x01(\x05\x12 \n\x18kernel_tracker_max_bytes\x18\x08 \x01(\x05\x12\"\n\x1akernel_tracker_max_pending\x18\t \x01(\x05\x12\'\n\x1finternal_fragmentation_fraction\x18\n \x01(\x01\x12\x1d\n\x15use_cuda_malloc_async\x18\x0b \x01(\x08\x1a;\n\x0eVirtualDevices\x12\x17\n\x0fmemory_limit_mb\x18\x01 \x03(\x02\x12\x10\n\x08priority\x18\x02 \x03(\x05\"\x85\x03\n\x10OptimizerOptions\x12+\n#do_common_subexpression_elimination\x18\x01 \x01(\x08\x12\x1b\n\x13\x64o_constant_folding\x18\x02 \x01(\x08\x12$\n\x1cmax_folded_constant_in_bytes\x18\x06 \x01(\x03\x12\x1c\n\x14\x64o_function_inlining\x18\x04 \x01(\x08\x12\x35\n\topt_level\x18\x03 \x01(\x0e\x32\".tensorflow.OptimizerOptions.Level\x12\x45\n\x10global_jit_level\x18\x05 \x01(\x0e\x32+.tensorflow.OptimizerOptions.GlobalJitLevel\" \n\x05Level\x12\x06\n\x02L1\x10\x00\x12\x0f\n\x02L0\x10\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"C\n\x0eGlobalJitLevel\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x10\n\x03OFF\x10\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\x12\x08\n\x04ON_1\x10\x01\x12\x08\n\x04ON_2\x10\x02\"\xee\x02\n\x0cGraphOptions\x12\x1e\n\x16\x65nable_recv_scheduling\x18\x02 \x01(\x08\x12\x37\n\x11optimizer_options\x18\x03 \x01(\x0b\x32\x1c.tensorflow.OptimizerOptions\x12\x18\n\x10\x62uild_cost_model\x18\x04 \x01(\x03\x12\x1e\n\x16\x62uild_cost_model_after\x18\t \x01(\x03\x12\x14\n\x0cinfer_shapes\x18\x05 \x01(\x08\x12\x1a\n\x12place_pruned_graph\x18\x06 \x01(\x08\x12 \n\x18\x65nable_bfloat16_sendrecv\x18\x07 \x01(\x08\x12\x15\n\rtimeline_step\x18\x08 \x01(\x05\x12\x33\n\x0frewrite_options\x18\n \x01(\x0b\x32\x1a.tensorflow.RewriterConfigJ\x04\x08\x01\x10\x02R%skip_common_subexpression_elimination\"A\n\x15ThreadPoolOptionProto\x12\x13\n\x0bnum_threads\x18\x01 \x01(\x05\x12\x13\n\x0bglobal_name\x18\x02 \x01(\t\"\xd5\x01\n\nRPCOptions\x12$\n\x1cuse_rpc_for_inprocess_master\x18\x01 \x01(\x08\x12\x1d\n\x15\x63ompression_algorithm\x18\x02 \x01(\t\x12\x19\n\x11\x63ompression_level\x18\x03 \x01(\x05\x12\x1a\n\x12\x63\x61\x63he_rpc_response\x18\x04 \x01(\x08\x12*\n\"disable_session_connection_sharing\x18\x05 \x01(\x08\x12\x1f\n\x17num_channels_per_target\x18\x06 \x01(\x05\"0\n\x0fSessionMetadata\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\x03\"\xd8\r\n\x0b\x43onfigProto\x12>\n\x0c\x64\x65vice_count\x18\x01 \x03(\x0b\x32(.tensorflow.ConfigProto.DeviceCountEntry\x12$\n\x1cintra_op_parallelism_threads\x18\x02 \x01(\x05\x12$\n\x1cinter_op_parallelism_threads\x18\x05 \x01(\x05\x12\x1f\n\x17use_per_session_threads\x18\t \x01(\x08\x12G\n\x1csession_inter_op_thread_pool\x18\x0c \x03(\x0b\x32!.tensorflow.ThreadPoolOptionProto\x12\x18\n\x10placement_period\x18\x03 \x01(\x05\x12\x16\n\x0e\x64\x65vice_filters\x18\x04 \x03(\t\x12+\n\x0bgpu_options\x18\x06 \x01(\x0b\x32\x16.tensorflow.GPUOptions\x12\x1c\n\x14\x61llow_soft_placement\x18\x07 \x01(\x08\x12\x1c\n\x14log_device_placement\x18\x08 \x01(\x08\x12/\n\rgraph_options\x18\n \x01(\x0b\x32\x18.tensorflow.GraphOptions\x12\x1f\n\x17operation_timeout_in_ms\x18\x0b \x01(\x03\x12+\n\x0brpc_options\x18\r \x01(\x0b\x32\x16.tensorflow.RPCOptions\x12+\n\x0b\x63luster_def\x18\x0e \x01(\x0b\x32\x16.tensorflow.ClusterDef\x12\x1d\n\x15isolate_session_state\x18\x0f \x01(\x08\x12(\n share_cluster_devices_in_session\x18\x11 \x01(\x08\x12:\n\x0c\x65xperimental\x18\x10 \x01(\x0b\x32$.tensorflow.ConfigProto.Experimental\x1a\x32\n\x10\x44\x65viceCountEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\x1a\xd2\x07\n\x0c\x45xperimental\x12\x1f\n\x17\x63ollective_group_leader\x18\x01 \x01(\t\x12\x15\n\rexecutor_type\x18\x03 \x01(\t\x12\x1a\n\x12recv_buf_max_chunk\x18\x04 \x01(\x05\x12\x19\n\x11use_numa_affinity\x18\x05 \x01(\x08\x12\x35\n-collective_deterministic_sequential_execution\x18\x06 \x01(\x08\x12\x17\n\x0f\x63ollective_nccl\x18\x07 \x01(\x08\x12\x36\n.share_session_state_in_clusterspec_propagation\x18\x08 \x01(\x08\x12\x1f\n\x17\x64isable_thread_spinning\x18\t \x01(\x08\x12(\n share_cluster_devices_in_session\x18\n \x01(\x08\x12\x35\n\x10session_metadata\x18\x0b \x01(\x0b\x32\x1b.tensorflow.SessionMetadata\x12!\n\x19optimize_for_static_graph\x18\x0c \x01(\x08\x12\x1a\n\x12\x65nable_mlir_bridge\x18\r \x01(\x08\x12S\n\x13mlir_bridge_rollout\x18\x11 \x01(\x0e\x32\x36.tensorflow.ConfigProto.Experimental.MlirBridgeRollout\x12&\n\x1e\x65nable_mlir_graph_optimization\x18\x10 \x01(\x08\x12\'\n\x1f\x64isable_output_partition_graphs\x18\x0e \x01(\x08\x12#\n\x1bxla_fusion_autotuner_thresh\x18\x0f \x01(\x03\x12\x10\n\x08use_tfrt\x18\x12 \x01(\x08\x12\x1c\n\x14\x63oordination_service\x18\x13 \x01(\t\x12,\n$fetch_remote_devices_in_multi_client\x18\x14 \x01(\x08\"\xda\x01\n\x11MlirBridgeRollout\x12#\n\x1fMLIR_BRIDGE_ROLLOUT_UNSPECIFIED\x10\x00\x12\x1f\n\x1bMLIR_BRIDGE_ROLLOUT_ENABLED\x10\x01\x12 \n\x1cMLIR_BRIDGE_ROLLOUT_DISABLED\x10\x02\x12)\n%MLIR_BRIDGE_ROLLOUT_SAFE_MODE_ENABLED\x10\x03\x12\x32\n.MLIR_BRIDGE_ROLLOUT_SAFE_MODE_FALLBACK_ENABLED\x10\x04J\x04\x08\x02\x10\x03\"\xe1\x04\n\nRunOptions\x12\x36\n\x0btrace_level\x18\x01 \x01(\x0e\x32!.tensorflow.RunOptions.TraceLevel\x12\x15\n\rtimeout_in_ms\x18\x02 \x01(\x03\x12\x1c\n\x14inter_op_thread_pool\x18\x03 \x01(\x05\x12\x1f\n\x17output_partition_graphs\x18\x05 \x01(\x08\x12/\n\rdebug_options\x18\x06 \x01(\x0b\x32\x18.tensorflow.DebugOptions\x12*\n\"report_tensor_allocations_upon_oom\x18\x07 \x01(\x08\x12\x39\n\x0c\x65xperimental\x18\x08 \x01(\x0b\x32#.tensorflow.RunOptions.Experimental\x1a\xd2\x01\n\x0c\x45xperimental\x12\x1c\n\x14\x63ollective_graph_key\x18\x01 \x01(\x03\x12\x1c\n\x14use_run_handler_pool\x18\x02 \x01(\x08\x12[\n\x18run_handler_pool_options\x18\x03 \x01(\x0b\x32\x39.tensorflow.RunOptions.Experimental.RunHandlerPoolOptions\x1a)\n\x15RunHandlerPoolOptions\x12\x10\n\x08priority\x18\x01 \x01(\x03\"R\n\nTraceLevel\x12\x0c\n\x08NO_TRACE\x10\x00\x12\x12\n\x0eSOFTWARE_TRACE\x10\x01\x12\x12\n\x0eHARDWARE_TRACE\x10\x02\x12\x0e\n\nFULL_TRACE\x10\x03J\x04\x08\x04\x10\x05\"\x87\x03\n\x0bRunMetadata\x12)\n\nstep_stats\x18\x01 \x01(\x0b\x32\x15.tensorflow.StepStats\x12,\n\ncost_graph\x18\x02 \x01(\x0b\x32\x18.tensorflow.CostGraphDef\x12.\n\x10partition_graphs\x18\x03 \x03(\x0b\x32\x14.tensorflow.GraphDef\x12?\n\x0f\x66unction_graphs\x18\x04 \x03(\x0b\x32&.tensorflow.RunMetadata.FunctionGraphs\x1a\xad\x01\n\x0e\x46unctionGraphs\x12.\n\x10partition_graphs\x18\x01 \x03(\x0b\x32\x14.tensorflow.GraphDef\x12\x34\n\x16pre_optimization_graph\x18\x02 \x01(\x0b\x32\x14.tensorflow.GraphDef\x12\x35\n\x17post_optimization_graph\x18\x03 \x01(\x0b\x32\x14.tensorflow.GraphDef\":\n\x10TensorConnection\x12\x13\n\x0b\x66rom_tensor\x18\x01 \x01(\t\x12\x11\n\tto_tensor\x18\x02 \x01(\t\"\xb0\x03\n\x0f\x43\x61llableOptions\x12\x0c\n\x04\x66\x65\x65\x64\x18\x01 \x03(\t\x12\r\n\x05\x66\x65tch\x18\x02 \x03(\t\x12\x0e\n\x06target\x18\x03 \x03(\t\x12+\n\x0brun_options\x18\x04 \x01(\x0b\x32\x16.tensorflow.RunOptions\x12\x37\n\x11tensor_connection\x18\x05 \x03(\x0b\x32\x1c.tensorflow.TensorConnection\x12\x42\n\x0c\x66\x65\x65\x64_devices\x18\x06 \x03(\x0b\x32,.tensorflow.CallableOptions.FeedDevicesEntry\x12\x44\n\rfetch_devices\x18\x07 \x03(\x0b\x32-.tensorflow.CallableOptions.FetchDevicesEntry\x12\x17\n\x0f\x66\x65tch_skip_sync\x18\x08 \x01(\x08\x1a\x32\n\x10\x46\x65\x65\x64\x44\x65vicesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x33\n\x11\x46\x65tchDevicesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\x84\x01\n\x18org.tensorflow.frameworkB\x0c\x43onfigProtosP\x01ZUgithub.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto\xf8\x01\x01\x62\x06proto3')
  ,
  dependencies=[tensorflow_dot_core_dot_framework_dot_cost__graph__pb2.DESCRIPTOR,tensorflow_dot_core_dot_framework_dot_graph__pb2.DESCRIPTOR,tensorflow_dot_core_dot_framework_dot_step__stats__pb2.DESCRIPTOR,tensorflow_dot_core_dot_protobuf_dot_cluster__pb2.DESCRIPTOR,tensorflow_dot_core_dot_protobuf_dot_debug__pb2.DESCRIPTOR,tensorflow_dot_core_dot_protobuf_dot_rewriter__config__pb2.DESCRIPTOR,])



_OPTIMIZEROPTIONS_LEVEL = _descriptor.EnumDescriptor(
  name='Level',
  full_name='tensorflow.OptimizerOptions.Level',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='L1', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='L0', index=1, number=-1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1383,
  serialized_end=1415,
)
_sym_db.RegisterEnumDescriptor(_OPTIMIZEROPTIONS_LEVEL)

_OPTIMIZEROPTIONS_GLOBALJITLEVEL = _descriptor.EnumDescriptor(
  name='GlobalJitLevel',
  full_name='tensorflow.OptimizerOptions.GlobalJitLevel',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DEFAULT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OFF', index=1, number=-1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ON_1', index=2, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ON_2', index=3, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1417,
  serialized_end=1484,
)
_sym_db.RegisterEnumDescriptor(_OPTIMIZEROPTIONS_GLOBALJITLEVEL)

_CONFIGPROTO_EXPERIMENTAL_MLIRBRIDGEROLLOUT = _descriptor.EnumDescriptor(
  name='MlirBridgeRollout',
  full_name='tensorflow.ConfigProto.Experimental.MlirBridgeRollout',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='MLIR_BRIDGE_ROLLOUT_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MLIR_BRIDGE_ROLLOUT_ENABLED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MLIR_BRIDGE_ROLLOUT_DISABLED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MLIR_BRIDGE_ROLLOUT_SAFE_MODE_ENABLED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MLIR_BRIDGE_ROLLOUT_SAFE_MODE_FALLBACK_ENABLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3717,
  serialized_end=3935,
)
_sym_db.RegisterEnumDescriptor(_CONFIGPROTO_EXPERIMENTAL_MLIRBRIDGEROLLOUT)

_RUNOPTIONS_TRACELEVEL = _descriptor.EnumDescriptor(
  name='TraceLevel',
  full_name='tensorflow.RunOptions.TraceLevel',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NO_TRACE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SOFTWARE_TRACE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HARDWARE_TRACE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FULL_TRACE', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4465,
  serialized_end=4547,
)
_sym_db.RegisterEnumDescriptor(_RUNOPTIONS_TRACELEVEL)


_GPUOPTIONS_EXPERIMENTAL_VIRTUALDEVICES = _descriptor.Descriptor(
  name='VirtualDevices',
  full_name='tensorflow.GPUOptions.Experimental.VirtualDevices',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='memory_limit_mb', full_name='tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb', index=0,
      number=1, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priority', full_name='tensorflow.GPUOptions.Experimental.VirtualDevices.priority', index=1,
      number=2, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1033,
  serialized_end=1092,
)

_GPUOPTIONS_EXPERIMENTAL = _descriptor.Descriptor(
  name='Experimental',
  full_name='tensorflow.GPUOptions.Experimental',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='virtual_devices', full_name='tensorflow.GPUOptions.Experimental.virtual_devices', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_unified_memory', full_name='tensorflow.GPUOptions.Experimental.use_unified_memory', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_dev_to_dev_copy_streams', full_name='tensorflow.GPUOptions.Experimental.num_dev_to_dev_copy_streams', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='collective_ring_order', full_name='tensorflow.GPUOptions.Experimental.collective_ring_order', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamped_allocator', full_name='tensorflow.GPUOptions.Experimental.timestamped_allocator', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kernel_tracker_max_interval', full_name='tensorflow.GPUOptions.Experimental.kernel_tracker_max_interval', index=5,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kernel_tracker_max_bytes', full_name='tensorflow.GPUOptions.Experimental.kernel_tracker_max_bytes', index=6,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kernel_tracker_max_pending', full_name='tensorflow.GPUOptions.Experimental.kernel_tracker_max_pending', index=7,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='internal_fragmentation_fraction', full_name='tensorflow.GPUOptions.Experimental.internal_fragmentation_fraction', index=8,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_cuda_malloc_async', full_name='tensorflow.GPUOptions.Experimental.use_cuda_malloc_async', index=9,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GPUOPTIONS_EXPERIMENTAL_VIRTUALDEVICES, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=634,
  serialized_end=1092,
)

_GPUOPTIONS = _descriptor.Descriptor(
  name='GPUOptions',
  full_name='tensorflow.GPUOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='per_process_gpu_memory_fraction', full_name='tensorflow.GPUOptions.per_process_gpu_memory_fraction', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_growth', full_name='tensorflow.GPUOptions.allow_growth', index=1,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocator_type', full_name='tensorflow.GPUOptions.allocator_type', index=2,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deferred_deletion_bytes', full_name='tensorflow.GPUOptions.deferred_deletion_bytes', index=3,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='visible_device_list', full_name='tensorflow.GPUOptions.visible_device_list', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='polling_active_delay_usecs', full_name='tensorflow.GPUOptions.polling_active_delay_usecs', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='polling_inactive_delay_msecs', full_name='tensorflow.GPUOptions.polling_inactive_delay_msecs', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='force_gpu_compatible', full_name='tensorflow.GPUOptions.force_gpu_compatible', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='experimental', full_name='tensorflow.GPUOptions.experimental', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GPUOPTIONS_EXPERIMENTAL, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=307,
  serialized_end=1092,
)


_OPTIMIZEROPTIONS = _descriptor.Descriptor(
  name='OptimizerOptions',
  full_name='tensorflow.OptimizerOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='do_common_subexpression_elimination', full_name='tensorflow.OptimizerOptions.do_common_subexpression_elimination', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='do_constant_folding', full_name='tensorflow.OptimizerOptions.do_constant_folding', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_folded_constant_in_bytes', full_name='tensorflow.OptimizerOptions.max_folded_constant_in_bytes', index=2,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='do_function_inlining', full_name='tensorflow.OptimizerOptions.do_function_inlining', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opt_level', full_name='tensorflow.OptimizerOptions.opt_level', index=4,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='global_jit_level', full_name='tensorflow.OptimizerOptions.global_jit_level', index=5,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _OPTIMIZEROPTIONS_LEVEL,
    _OPTIMIZEROPTIONS_GLOBALJITLEVEL,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1095,
  serialized_end=1484,
)


_GRAPHOPTIONS = _descriptor.Descriptor(
  name='GraphOptions',
  full_name='tensorflow.GraphOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='enable_recv_scheduling', full_name='tensorflow.GraphOptions.enable_recv_scheduling', index=0,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='optimizer_options', full_name='tensorflow.GraphOptions.optimizer_options', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='build_cost_model', full_name='tensorflow.GraphOptions.build_cost_model', index=2,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='build_cost_model_after', full_name='tensorflow.GraphOptions.build_cost_model_after', index=3,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infer_shapes', full_name='tensorflow.GraphOptions.infer_shapes', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='place_pruned_graph', full_name='tensorflow.GraphOptions.place_pruned_graph', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='enable_bfloat16_sendrecv', full_name='tensorflow.GraphOptions.enable_bfloat16_sendrecv', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timeline_step', full_name='tensorflow.GraphOptions.timeline_step', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rewrite_options', full_name='tensorflow.GraphOptions.rewrite_options', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1487,
  serialized_end=1853,
)


_THREADPOOLOPTIONPROTO = _descriptor.Descriptor(
  name='ThreadPoolOptionProto',
  full_name='tensorflow.ThreadPoolOptionProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='num_threads', full_name='tensorflow.ThreadPoolOptionProto.num_threads', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='global_name', full_name='tensorflow.ThreadPoolOptionProto.global_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1855,
  serialized_end=1920,
)


_RPCOPTIONS = _descriptor.Descriptor(
  name='RPCOptions',
  full_name='tensorflow.RPCOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='use_rpc_for_inprocess_master', full_name='tensorflow.RPCOptions.use_rpc_for_inprocess_master', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compression_algorithm', full_name='tensorflow.RPCOptions.compression_algorithm', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compression_level', full_name='tensorflow.RPCOptions.compression_level', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cache_rpc_response', full_name='tensorflow.RPCOptions.cache_rpc_response', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='disable_session_connection_sharing', full_name='tensorflow.RPCOptions.disable_session_connection_sharing', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_channels_per_target', full_name='tensorflow.RPCOptions.num_channels_per_target', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1923,
  serialized_end=2136,
)


_SESSIONMETADATA = _descriptor.Descriptor(
  name='SessionMetadata',
  full_name='tensorflow.SessionMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='tensorflow.SessionMetadata.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='version', full_name='tensorflow.SessionMetadata.version', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2138,
  serialized_end=2186,
)


_CONFIGPROTO_DEVICECOUNTENTRY = _descriptor.Descriptor(
  name='DeviceCountEntry',
  full_name='tensorflow.ConfigProto.DeviceCountEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.ConfigProto.DeviceCountEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.ConfigProto.DeviceCountEntry.value', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2910,
  serialized_end=2960,
)

_CONFIGPROTO_EXPERIMENTAL = _descriptor.Descriptor(
  name='Experimental',
  full_name='tensorflow.ConfigProto.Experimental',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='collective_group_leader', full_name='tensorflow.ConfigProto.Experimental.collective_group_leader', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='executor_type', full_name='tensorflow.ConfigProto.Experimental.executor_type', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recv_buf_max_chunk', full_name='tensorflow.ConfigProto.Experimental.recv_buf_max_chunk', index=2,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_numa_affinity', full_name='tensorflow.ConfigProto.Experimental.use_numa_affinity', index=3,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='collective_deterministic_sequential_execution', full_name='tensorflow.ConfigProto.Experimental.collective_deterministic_sequential_execution', index=4,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='collective_nccl', full_name='tensorflow.ConfigProto.Experimental.collective_nccl', index=5,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='share_session_state_in_clusterspec_propagation', full_name='tensorflow.ConfigProto.Experimental.share_session_state_in_clusterspec_propagation', index=6,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='disable_thread_spinning', full_name='tensorflow.ConfigProto.Experimental.disable_thread_spinning', index=7,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='share_cluster_devices_in_session', full_name='tensorflow.ConfigProto.Experimental.share_cluster_devices_in_session', index=8,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='session_metadata', full_name='tensorflow.ConfigProto.Experimental.session_metadata', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='optimize_for_static_graph', full_name='tensorflow.ConfigProto.Experimental.optimize_for_static_graph', index=10,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='enable_mlir_bridge', full_name='tensorflow.ConfigProto.Experimental.enable_mlir_bridge', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mlir_bridge_rollout', full_name='tensorflow.ConfigProto.Experimental.mlir_bridge_rollout', index=12,
      number=17, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='enable_mlir_graph_optimization', full_name='tensorflow.ConfigProto.Experimental.enable_mlir_graph_optimization', index=13,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='disable_output_partition_graphs', full_name='tensorflow.ConfigProto.Experimental.disable_output_partition_graphs', index=14,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='xla_fusion_autotuner_thresh', full_name='tensorflow.ConfigProto.Experimental.xla_fusion_autotuner_thresh', index=15,
      number=15, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_tfrt', full_name='tensorflow.ConfigProto.Experimental.use_tfrt', index=16,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='coordination_service', full_name='tensorflow.ConfigProto.Experimental.coordination_service', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fetch_remote_devices_in_multi_client', full_name='tensorflow.ConfigProto.Experimental.fetch_remote_devices_in_multi_client', index=18,
      number=20, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _CONFIGPROTO_EXPERIMENTAL_MLIRBRIDGEROLLOUT,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2963,
  serialized_end=3941,
)

_CONFIGPROTO = _descriptor.Descriptor(
  name='ConfigProto',
  full_name='tensorflow.ConfigProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='device_count', full_name='tensorflow.ConfigProto.device_count', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='intra_op_parallelism_threads', full_name='tensorflow.ConfigProto.intra_op_parallelism_threads', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inter_op_parallelism_threads', full_name='tensorflow.ConfigProto.inter_op_parallelism_threads', index=2,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_per_session_threads', full_name='tensorflow.ConfigProto.use_per_session_threads', index=3,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='session_inter_op_thread_pool', full_name='tensorflow.ConfigProto.session_inter_op_thread_pool', index=4,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='placement_period', full_name='tensorflow.ConfigProto.placement_period', index=5,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_filters', full_name='tensorflow.ConfigProto.device_filters', index=6,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gpu_options', full_name='tensorflow.ConfigProto.gpu_options', index=7,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_soft_placement', full_name='tensorflow.ConfigProto.allow_soft_placement', index=8,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='log_device_placement', full_name='tensorflow.ConfigProto.log_device_placement', index=9,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='graph_options', full_name='tensorflow.ConfigProto.graph_options', index=10,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation_timeout_in_ms', full_name='tensorflow.ConfigProto.operation_timeout_in_ms', index=11,
      number=11, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rpc_options', full_name='tensorflow.ConfigProto.rpc_options', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cluster_def', full_name='tensorflow.ConfigProto.cluster_def', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isolate_session_state', full_name='tensorflow.ConfigProto.isolate_session_state', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='share_cluster_devices_in_session', full_name='tensorflow.ConfigProto.share_cluster_devices_in_session', index=15,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='experimental', full_name='tensorflow.ConfigProto.experimental', index=16,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CONFIGPROTO_DEVICECOUNTENTRY, _CONFIGPROTO_EXPERIMENTAL, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2189,
  serialized_end=3941,
)


_RUNOPTIONS_EXPERIMENTAL_RUNHANDLERPOOLOPTIONS = _descriptor.Descriptor(
  name='RunHandlerPoolOptions',
  full_name='tensorflow.RunOptions.Experimental.RunHandlerPoolOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='priority', full_name='tensorflow.RunOptions.Experimental.RunHandlerPoolOptions.priority', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4422,
  serialized_end=4463,
)

_RUNOPTIONS_EXPERIMENTAL = _descriptor.Descriptor(
  name='Experimental',
  full_name='tensorflow.RunOptions.Experimental',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='collective_graph_key', full_name='tensorflow.RunOptions.Experimental.collective_graph_key', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_run_handler_pool', full_name='tensorflow.RunOptions.Experimental.use_run_handler_pool', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='run_handler_pool_options', full_name='tensorflow.RunOptions.Experimental.run_handler_pool_options', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RUNOPTIONS_EXPERIMENTAL_RUNHANDLERPOOLOPTIONS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4253,
  serialized_end=4463,
)

_RUNOPTIONS = _descriptor.Descriptor(
  name='RunOptions',
  full_name='tensorflow.RunOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trace_level', full_name='tensorflow.RunOptions.trace_level', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timeout_in_ms', full_name='tensorflow.RunOptions.timeout_in_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inter_op_thread_pool', full_name='tensorflow.RunOptions.inter_op_thread_pool', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_partition_graphs', full_name='tensorflow.RunOptions.output_partition_graphs', index=3,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='debug_options', full_name='tensorflow.RunOptions.debug_options', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='report_tensor_allocations_upon_oom', full_name='tensorflow.RunOptions.report_tensor_allocations_upon_oom', index=5,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='experimental', full_name='tensorflow.RunOptions.experimental', index=6,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RUNOPTIONS_EXPERIMENTAL, ],
  enum_types=[
    _RUNOPTIONS_TRACELEVEL,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3944,
  serialized_end=4553,
)


_RUNMETADATA_FUNCTIONGRAPHS = _descriptor.Descriptor(
  name='FunctionGraphs',
  full_name='tensorflow.RunMetadata.FunctionGraphs',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partition_graphs', full_name='tensorflow.RunMetadata.FunctionGraphs.partition_graphs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_optimization_graph', full_name='tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='post_optimization_graph', full_name='tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4774,
  serialized_end=4947,
)

_RUNMETADATA = _descriptor.Descriptor(
  name='RunMetadata',
  full_name='tensorflow.RunMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='step_stats', full_name='tensorflow.RunMetadata.step_stats', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_graph', full_name='tensorflow.RunMetadata.cost_graph', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partition_graphs', full_name='tensorflow.RunMetadata.partition_graphs', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='function_graphs', full_name='tensorflow.RunMetadata.function_graphs', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RUNMETADATA_FUNCTIONGRAPHS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4556,
  serialized_end=4947,
)


_TENSORCONNECTION = _descriptor.Descriptor(
  name='TensorConnection',
  full_name='tensorflow.TensorConnection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='from_tensor', full_name='tensorflow.TensorConnection.from_tensor', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='to_tensor', full_name='tensorflow.TensorConnection.to_tensor', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4949,
  serialized_end=5007,
)


_CALLABLEOPTIONS_FEEDDEVICESENTRY = _descriptor.Descriptor(
  name='FeedDevicesEntry',
  full_name='tensorflow.CallableOptions.FeedDevicesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.CallableOptions.FeedDevicesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.CallableOptions.FeedDevicesEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5339,
  serialized_end=5389,
)

_CALLABLEOPTIONS_FETCHDEVICESENTRY = _descriptor.Descriptor(
  name='FetchDevicesEntry',
  full_name='tensorflow.CallableOptions.FetchDevicesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.CallableOptions.FetchDevicesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.CallableOptions.FetchDevicesEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5391,
  serialized_end=5442,
)

_CALLABLEOPTIONS = _descriptor.Descriptor(
  name='CallableOptions',
  full_name='tensorflow.CallableOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='feed', full_name='tensorflow.CallableOptions.feed', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fetch', full_name='tensorflow.CallableOptions.fetch', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target', full_name='tensorflow.CallableOptions.target', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='run_options', full_name='tensorflow.CallableOptions.run_options', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tensor_connection', full_name='tensorflow.CallableOptions.tensor_connection', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='feed_devices', full_name='tensorflow.CallableOptions.feed_devices', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fetch_devices', full_name='tensorflow.CallableOptions.fetch_devices', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fetch_skip_sync', full_name='tensorflow.CallableOptions.fetch_skip_sync', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CALLABLEOPTIONS_FEEDDEVICESENTRY, _CALLABLEOPTIONS_FETCHDEVICESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5010,
  serialized_end=5442,
)

_GPUOPTIONS_EXPERIMENTAL_VIRTUALDEVICES.containing_type = _GPUOPTIONS_EXPERIMENTAL
_GPUOPTIONS_EXPERIMENTAL.fields_by_name['virtual_devices'].message_type = _GPUOPTIONS_EXPERIMENTAL_VIRTUALDEVICES
_GPUOPTIONS_EXPERIMENTAL.containing_type = _GPUOPTIONS
_GPUOPTIONS.fields_by_name['experimental'].message_type = _GPUOPTIONS_EXPERIMENTAL
_OPTIMIZEROPTIONS.fields_by_name['opt_level'].enum_type = _OPTIMIZEROPTIONS_LEVEL
_OPTIMIZEROPTIONS.fields_by_name['global_jit_level'].enum_type = _OPTIMIZEROPTIONS_GLOBALJITLEVEL
_OPTIMIZEROPTIONS_LEVEL.containing_type = _OPTIMIZEROPTIONS
_OPTIMIZEROPTIONS_GLOBALJITLEVEL.containing_type = _OPTIMIZEROPTIONS
_GRAPHOPTIONS.fields_by_name['optimizer_options'].message_type = _OPTIMIZEROPTIONS
_GRAPHOPTIONS.fields_by_name['rewrite_options'].message_type = tensorflow_dot_core_dot_protobuf_dot_rewriter__config__pb2._REWRITERCONFIG
_CONFIGPROTO_DEVICECOUNTENTRY.containing_type = _CONFIGPROTO
_CONFIGPROTO_EXPERIMENTAL.fields_by_name['session_metadata'].message_type = _SESSIONMETADATA
_CONFIGPROTO_EXPERIMENTAL.fields_by_name['mlir_bridge_rollout'].enum_type = _CONFIGPROTO_EXPERIMENTAL_MLIRBRIDGEROLLOUT
_CONFIGPROTO_EXPERIMENTAL.containing_type = _CONFIGPROTO
_CONFIGPROTO_EXPERIMENTAL_MLIRBRIDGEROLLOUT.containing_type = _CONFIGPROTO_EXPERIMENTAL
_CONFIGPROTO.fields_by_name['device_count'].message_type = _CONFIGPROTO_DEVICECOUNTENTRY
_CONFIGPROTO.fields_by_name['session_inter_op_thread_pool'].message_type = _THREADPOOLOPTIONPROTO
_CONFIGPROTO.fields_by_name['gpu_options'].message_type = _GPUOPTIONS
_CONFIGPROTO.fields_by_name['graph_options'].message_type = _GRAPHOPTIONS
_CONFIGPROTO.fields_by_name['rpc_options'].message_type = _RPCOPTIONS
_CONFIGPROTO.fields_by_name['cluster_def'].message_type = tensorflow_dot_core_dot_protobuf_dot_cluster__pb2._CLUSTERDEF
_CONFIGPROTO.fields_by_name['experimental'].message_type = _CONFIGPROTO_EXPERIMENTAL
_RUNOPTIONS_EXPERIMENTAL_RUNHANDLERPOOLOPTIONS.containing_type = _RUNOPTIONS_EXPERIMENTAL
_RUNOPTIONS_EXPERIMENTAL.fields_by_name['run_handler_pool_options'].message_type = _RUNOPTIONS_EXPERIMENTAL_RUNHANDLERPOOLOPTIONS
_RUNOPTIONS_EXPERIMENTAL.containing_type = _RUNOPTIONS
_RUNOPTIONS.fields_by_name['trace_level'].enum_type = _RUNOPTIONS_TRACELEVEL
_RUNOPTIONS.fields_by_name['debug_options'].message_type = tensorflow_dot_core_dot_protobuf_dot_debug__pb2._DEBUGOPTIONS
_RUNOPTIONS.fields_by_name['experimental'].message_type = _RUNOPTIONS_EXPERIMENTAL
_RUNOPTIONS_TRACELEVEL.containing_type = _RUNOPTIONS
_RUNMETADATA_FUNCTIONGRAPHS.fields_by_name['partition_graphs'].message_type = tensorflow_dot_core_dot_framework_dot_graph__pb2._GRAPHDEF
_RUNMETADATA_FUNCTIONGRAPHS.fields_by_name['pre_optimization_graph'].message_type = tensorflow_dot_core_dot_framework_dot_graph__pb2._GRAPHDEF
_RUNMETADATA_FUNCTIONGRAPHS.fields_by_name['post_optimization_graph'].message_type = tensorflow_dot_core_dot_framework_dot_graph__pb2._GRAPHDEF
_RUNMETADATA_FUNCTIONGRAPHS.containing_type = _RUNMETADATA
_RUNMETADATA.fields_by_name['step_stats'].message_type = tensorflow_dot_core_dot_framework_dot_step__stats__pb2._STEPSTATS
_RUNMETADATA.fields_by_name['cost_graph'].message_type = tensorflow_dot_core_dot_framework_dot_cost__graph__pb2._COSTGRAPHDEF
_RUNMETADATA.fields_by_name['partition_graphs'].message_type = tensorflow_dot_core_dot_framework_dot_graph__pb2._GRAPHDEF
_RUNMETADATA.fields_by_name['function_graphs'].message_type = _RUNMETADATA_FUNCTIONGRAPHS
_CALLABLEOPTIONS_FEEDDEVICESENTRY.containing_type = _CALLABLEOPTIONS
_CALLABLEOPTIONS_FETCHDEVICESENTRY.containing_type = _CALLABLEOPTIONS
_CALLABLEOPTIONS.fields_by_name['run_options'].message_type = _RUNOPTIONS
_CALLABLEOPTIONS.fields_by_name['tensor_connection'].message_type = _TENSORCONNECTION
_CALLABLEOPTIONS.fields_by_name['feed_devices'].message_type = _CALLABLEOPTIONS_FEEDDEVICESENTRY
_CALLABLEOPTIONS.fields_by_name['fetch_devices'].message_type = _CALLABLEOPTIONS_FETCHDEVICESENTRY
DESCRIPTOR.message_types_by_name['GPUOptions'] = _GPUOPTIONS
DESCRIPTOR.message_types_by_name['OptimizerOptions'] = _OPTIMIZEROPTIONS
DESCRIPTOR.message_types_by_name['GraphOptions'] = _GRAPHOPTIONS
DESCRIPTOR.message_types_by_name['ThreadPoolOptionProto'] = _THREADPOOLOPTIONPROTO
DESCRIPTOR.message_types_by_name['RPCOptions'] = _RPCOPTIONS
DESCRIPTOR.message_types_by_name['SessionMetadata'] = _SESSIONMETADATA
DESCRIPTOR.message_types_by_name['ConfigProto'] = _CONFIGPROTO
DESCRIPTOR.message_types_by_name['RunOptions'] = _RUNOPTIONS
DESCRIPTOR.message_types_by_name['RunMetadata'] = _RUNMETADATA
DESCRIPTOR.message_types_by_name['TensorConnection'] = _TENSORCONNECTION
DESCRIPTOR.message_types_by_name['CallableOptions'] = _CALLABLEOPTIONS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GPUOptions = _reflection.GeneratedProtocolMessageType('GPUOptions', (_message.Message,), {

  'Experimental' : _reflection.GeneratedProtocolMessageType('Experimental', (_message.Message,), {

    'VirtualDevices' : _reflection.GeneratedProtocolMessageType('VirtualDevices', (_message.Message,), {
      'DESCRIPTOR' : _GPUOPTIONS_EXPERIMENTAL_VIRTUALDEVICES,
      '__module__' : 'tensorflow.core.protobuf.config_pb2'
      # @@protoc_insertion_point(class_scope:tensorflow.GPUOptions.Experimental.VirtualDevices)
      })
    ,
    'DESCRIPTOR' : _GPUOPTIONS_EXPERIMENTAL,
    '__module__' : 'tensorflow.core.protobuf.config_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.GPUOptions.Experimental)
    })
  ,
  'DESCRIPTOR' : _GPUOPTIONS,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.GPUOptions)
  })
_sym_db.RegisterMessage(GPUOptions)
_sym_db.RegisterMessage(GPUOptions.Experimental)
_sym_db.RegisterMessage(GPUOptions.Experimental.VirtualDevices)

OptimizerOptions = _reflection.GeneratedProtocolMessageType('OptimizerOptions', (_message.Message,), {
  'DESCRIPTOR' : _OPTIMIZEROPTIONS,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.OptimizerOptions)
  })
_sym_db.RegisterMessage(OptimizerOptions)

GraphOptions = _reflection.GeneratedProtocolMessageType('GraphOptions', (_message.Message,), {
  'DESCRIPTOR' : _GRAPHOPTIONS,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.GraphOptions)
  })
_sym_db.RegisterMessage(GraphOptions)

ThreadPoolOptionProto = _reflection.GeneratedProtocolMessageType('ThreadPoolOptionProto', (_message.Message,), {
  'DESCRIPTOR' : _THREADPOOLOPTIONPROTO,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.ThreadPoolOptionProto)
  })
_sym_db.RegisterMessage(ThreadPoolOptionProto)

RPCOptions = _reflection.GeneratedProtocolMessageType('RPCOptions', (_message.Message,), {
  'DESCRIPTOR' : _RPCOPTIONS,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.RPCOptions)
  })
_sym_db.RegisterMessage(RPCOptions)

SessionMetadata = _reflection.GeneratedProtocolMessageType('SessionMetadata', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONMETADATA,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.SessionMetadata)
  })
_sym_db.RegisterMessage(SessionMetadata)

ConfigProto = _reflection.GeneratedProtocolMessageType('ConfigProto', (_message.Message,), {

  'DeviceCountEntry' : _reflection.GeneratedProtocolMessageType('DeviceCountEntry', (_message.Message,), {
    'DESCRIPTOR' : _CONFIGPROTO_DEVICECOUNTENTRY,
    '__module__' : 'tensorflow.core.protobuf.config_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.ConfigProto.DeviceCountEntry)
    })
  ,

  'Experimental' : _reflection.GeneratedProtocolMessageType('Experimental', (_message.Message,), {
    'DESCRIPTOR' : _CONFIGPROTO_EXPERIMENTAL,
    '__module__' : 'tensorflow.core.protobuf.config_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.ConfigProto.Experimental)
    })
  ,
  'DESCRIPTOR' : _CONFIGPROTO,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.ConfigProto)
  })
_sym_db.RegisterMessage(ConfigProto)
_sym_db.RegisterMessage(ConfigProto.DeviceCountEntry)
_sym_db.RegisterMessage(ConfigProto.Experimental)

RunOptions = _reflection.GeneratedProtocolMessageType('RunOptions', (_message.Message,), {

  'Experimental' : _reflection.GeneratedProtocolMessageType('Experimental', (_message.Message,), {

    'RunHandlerPoolOptions' : _reflection.GeneratedProtocolMessageType('RunHandlerPoolOptions', (_message.Message,), {
      'DESCRIPTOR' : _RUNOPTIONS_EXPERIMENTAL_RUNHANDLERPOOLOPTIONS,
      '__module__' : 'tensorflow.core.protobuf.config_pb2'
      # @@protoc_insertion_point(class_scope:tensorflow.RunOptions.Experimental.RunHandlerPoolOptions)
      })
    ,
    'DESCRIPTOR' : _RUNOPTIONS_EXPERIMENTAL,
    '__module__' : 'tensorflow.core.protobuf.config_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.RunOptions.Experimental)
    })
  ,
  'DESCRIPTOR' : _RUNOPTIONS,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.RunOptions)
  })
_sym_db.RegisterMessage(RunOptions)
_sym_db.RegisterMessage(RunOptions.Experimental)
_sym_db.RegisterMessage(RunOptions.Experimental.RunHandlerPoolOptions)

RunMetadata = _reflection.GeneratedProtocolMessageType('RunMetadata', (_message.Message,), {

  'FunctionGraphs' : _reflection.GeneratedProtocolMessageType('FunctionGraphs', (_message.Message,), {
    'DESCRIPTOR' : _RUNMETADATA_FUNCTIONGRAPHS,
    '__module__' : 'tensorflow.core.protobuf.config_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.RunMetadata.FunctionGraphs)
    })
  ,
  'DESCRIPTOR' : _RUNMETADATA,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.RunMetadata)
  })
_sym_db.RegisterMessage(RunMetadata)
_sym_db.RegisterMessage(RunMetadata.FunctionGraphs)

TensorConnection = _reflection.GeneratedProtocolMessageType('TensorConnection', (_message.Message,), {
  'DESCRIPTOR' : _TENSORCONNECTION,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.TensorConnection)
  })
_sym_db.RegisterMessage(TensorConnection)

CallableOptions = _reflection.GeneratedProtocolMessageType('CallableOptions', (_message.Message,), {

  'FeedDevicesEntry' : _reflection.GeneratedProtocolMessageType('FeedDevicesEntry', (_message.Message,), {
    'DESCRIPTOR' : _CALLABLEOPTIONS_FEEDDEVICESENTRY,
    '__module__' : 'tensorflow.core.protobuf.config_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.CallableOptions.FeedDevicesEntry)
    })
  ,

  'FetchDevicesEntry' : _reflection.GeneratedProtocolMessageType('FetchDevicesEntry', (_message.Message,), {
    'DESCRIPTOR' : _CALLABLEOPTIONS_FETCHDEVICESENTRY,
    '__module__' : 'tensorflow.core.protobuf.config_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.CallableOptions.FetchDevicesEntry)
    })
  ,
  'DESCRIPTOR' : _CALLABLEOPTIONS,
  '__module__' : 'tensorflow.core.protobuf.config_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.CallableOptions)
  })
_sym_db.RegisterMessage(CallableOptions)
_sym_db.RegisterMessage(CallableOptions.FeedDevicesEntry)
_sym_db.RegisterMessage(CallableOptions.FetchDevicesEntry)


DESCRIPTOR._options = None
_CONFIGPROTO_DEVICECOUNTENTRY._options = None
_CALLABLEOPTIONS_FEEDDEVICESENTRY._options = None
_CALLABLEOPTIONS_FETCHDEVICESENTRY._options = None
# @@protoc_insertion_point(module_scope)
