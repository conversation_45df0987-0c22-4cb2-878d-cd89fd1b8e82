# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <vvan<PERSON><PERSON><PERSON>@gmail.com>, 2016-2017
# <PERSON><PERSON><PERSON> <vvangel<PERSON><EMAIL>>, 2013-2014
# V<PERSON><PERSON> <vvan<PERSON><PERSON><EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: V<PERSON><PERSON> <vvangel<PERSON><EMAIL>>\n"
"Language-Team: Macedonian (http://www.transifex.com/django/django/language/"
"mk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: mk\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

msgid "Administrative Documentation"
msgstr "Административна документација"

msgid "Home"
msgstr "Дома"

msgid "Documentation"
msgstr "Документација"

msgid "Bookmarklets"
msgstr "Обележувачи"

msgid "Documentation bookmarklets"
msgstr "Обележувачи на документација"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"За да инсталирате букмарклети повлечете го линкот во траката со bookmarks, "
"или кликенете со десното копче и додадете го во bookmarks. Така ќе можете да "
"го одберете букмарклетот од било која страна на сајтот."

msgid "Documentation for this page"
msgstr "Документација за оваа страница"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Ве носи од било која страница од документацијата до погледот кој ја генерира "
"таа страница."

msgid "Tags"
msgstr "Тагови"

msgid "List of all the template tags and their functions."
msgstr "Листа на сите шаблонски тагови и нивните функции."

msgid "Filters"
msgstr "Филтри"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Филтри се акции кои може да се применат на променливи во шаблон за менување "
"на излезниот резултат."

msgid "Models"
msgstr "Модели"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Модели се описи на сите објекти во системот и нивните полиња. Секој модел "
"има листа на полиња до кои може да се пристапи со променливи во шаблони."

msgid "Views"
msgstr "Погледи (вјуа)"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Секоја страна на јавниот сајт се генерира од поглед. Подгледот дефинира кој "
"шаблон се користи за да се генерира страната и кој објекти се достапни за "
"тој шаблон."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Алатки за вашиот веб прегледувач за брз пристап до администраторските "
"функционалности."

msgid "Please install docutils"
msgstr "Ве молиме инсталирајте docutils"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"На админ документацискиот систем му е потребна <a href=\"%(link)s"
"\">docutils</a> Python библиотеката."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Ве молиме побарајте од вашите администратори да инсталираат <a href="
"\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Модел %(name)s"

msgid "Fields"
msgstr "Полиња"

msgid "Field"
msgstr "Поле"

msgid "Type"
msgstr "Тип"

msgid "Description"
msgstr "Опис"

msgid "Methods with arguments"
msgstr "Методи со аргументи"

msgid "Method"
msgstr "Метод"

msgid "Arguments"
msgstr "Аргументи"

msgid "Back to Model documentation"
msgstr "Назад до документација за модели."

msgid "Model documentation"
msgstr "Документација за модели"

msgid "Model groups"
msgstr "Групи на модели"

msgid "Templates"
msgstr "Шаблони (темплејти)"

#, python-format
msgid "Template: %(name)s"
msgstr "Шаблон: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "Шаблон: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr "Пребарај ја патеката за шаблонот \"%(name)s\":"

msgid "(does not exist)"
msgstr "(не постои)"

msgid "Back to Documentation"
msgstr "Назад во документација"

msgid "Template filters"
msgstr "Шаблонски филтри"

msgid "Template filter documentation"
msgstr "Документација за шаблонски филтер"

msgid "Built-in filters"
msgstr "Вградени филтри"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"За да ги користите овие филтри, внесете <code>%(code)s</code> во вашиот "
"шаблон пред да го користите филтерот."

msgid "Template tags"
msgstr "Шаблонски тагови"

msgid "Template tag documentation"
msgstr "Документација за шаблонски таг"

msgid "Built-in tags"
msgstr "Вградени тагови"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"За да ги користите овие тагови, внесете <code>%(code)s</code> во вашиот "
"шаблон пред да го користите тагот."

#, python-format
msgid "View: %(name)s"
msgstr "Поглед: %(name)s"

msgid "Context:"
msgstr "Контекст:"

msgid "Templates:"
msgstr "Шаблони:"

msgid "Back to View documentation"
msgstr "Назад до документација за погледи"

msgid "View documentation"
msgstr "Документација за поглед"

msgid "Jump to namespace"
msgstr "Скокни до именски простор"

msgid "Empty namespace"
msgstr "Празен именски простор"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Погледи по именски простор %(name)s"

msgid "Views by empty namespace"
msgstr "Погледи по празен именски простор"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Погледска функција (вју функција): <code>%(full_name)s</code>. Име: "
"<code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "таг:"

msgid "filter:"
msgstr "филтер:"

msgid "view:"
msgstr "поглед:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Апликацијата %(app_label)r не е пронајдена"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Моделот %(model_name)r не е најден во апликацијата %(app_label)r"

msgid "model:"
msgstr "модел:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "поврзаниот `%(app_label)s.%(data_type)s` објект"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "поврзани `%(app_label)s.%(object_name)s` објекти"

#, python-format
msgid "all %s"
msgstr "сите %s"

#, python-format
msgid "number of %s"
msgstr "број на %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s не изгледа дека е url објект"
