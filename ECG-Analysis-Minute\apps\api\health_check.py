"""
健康检查API端点
"""
import json
import time
from django.http import JsonResponse
from django.views import View
from apps.utils.startup_optimizer import get_system_status
from apps.utils.logger_helper import Logger


class HealthCheckView(View):
    """健康检查视图"""
    
    def get(self, request):
        """获取系统健康状态"""
        try:
            status = get_system_status()
            
            # 判断系统是否健康
            is_healthy = (
                status.get('status') == 'healthy' and
                status.get('memory', {}).get('memory_percent', 100) < 90 and
                status.get('request_queue', {}).get('running', False)
            )
            
            response_data = {
                'healthy': is_healthy,
                'timestamp': int(time.time()),
                'details': status
            }
            
            http_status = 200 if is_healthy else 503
            return JsonResponse(response_data, status=http_status)
            
        except Exception as e:
            Logger().error(f"Health check failed: {e}")
            return JsonResponse({
                'healthy': False,
                'error': str(e),
                'timestamp': int(time.time())
            }, status=500)


class SystemStatusView(View):
    """系统状态详情视图"""
    
    def get(self, request):
        """获取详细的系统状态"""
        try:
            status = get_system_status()
            return JsonResponse(status)
            
        except Exception as e:
            Logger().error(f"System status check failed: {e}")
            return JsonResponse({
                'status': 'error',
                'error': str(e)
            }, status=500)


class MemoryCleanupView(View):
    """手动内存清理视图"""
    
    def post(self, request):
        """手动触发内存清理"""
        try:
            from apps.utils.memory_monitor import get_memory_monitor
            from apps.utils.model_manager import get_model_manager
            
            # 获取清理前的内存状态
            memory_monitor = get_memory_monitor()
            before_cleanup = memory_monitor.get_memory_info()
            
            # 执行清理
            memory_monitor.emergency_cleanup()
            
            # 清理模型缓存
            model_manager = get_model_manager()
            model_manager.clear_all_cache()
            
            # 获取清理后的内存状态
            after_cleanup = memory_monitor.get_memory_info()
            
            return JsonResponse({
                'success': True,
                'before_cleanup': before_cleanup,
                'after_cleanup': after_cleanup,
                'memory_freed_percent': before_cleanup['memory_percent'] - after_cleanup['memory_percent']
            })
            
        except Exception as e:
            Logger().error(f"Manual cleanup failed: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
