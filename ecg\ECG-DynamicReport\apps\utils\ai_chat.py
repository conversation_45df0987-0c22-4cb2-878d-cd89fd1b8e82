import requests
import json

from data_frame.global_settings import AI_URI


def ai_reply(question):
    reply = ""
    ai_url = AI_URI
    headers = {
        "Authorization": "Bearer c885e555-ea0a-4350-bcfe-66ddf4987f8d",
        "Content-Type": "application/json"
    }

    # 创建请求体
    request_body = {
        "model": "ep-20250123173701-9zzgm",
        "messages": [
            {"role": "system", "content": "你是卫和医疗研发的AI心电图医生"},
            {"role": "user", "content": question}
        ]
    }

    try:
        # 发送POST请求
        response = requests.post(ai_url, headers=headers, data=json.dumps(request_body), timeout=60)

        if response.status_code == 200:
            result = response.json()
            choices = result.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                reply = message.get("content", "")
        else:
            print(f"请求失败，状态码: {response.status_code}")
    except requests.RequestException as e:
        print(f"请求失败: {e}")

    return reply