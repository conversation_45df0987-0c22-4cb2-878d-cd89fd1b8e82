#!/usr/bin/env python3
"""
测试网站访问和数据提取
"""

import requests
from bs4 import BeautifulSoup
import re
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_website_access():
    """测试网站访问"""
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
    })
    
    base_url = "https://www.badmintoncn.com"
    
    # 测试主页
    logger.info("🌐 测试主页访问...")
    try:
        response = session.get(base_url, timeout=20)
        logger.info(f"主页状态码: {response.status_code}")
        if response.status_code == 200:
            logger.info("✅ 主页访问成功")
        else:
            logger.error(f"❌ 主页访问失败: {response.status_code}")
            return
    except Exception as e:
        logger.error(f"❌ 主页访问异常: {e}")
        return
    
    # 测试装备列表页
    logger.info("\n🏸 测试装备列表页访问...")
    list_url = f"{base_url}/cbo_eq/list.php"
    
    try:
        response = session.get(list_url, timeout=20)
        logger.info(f"装备列表页状态码: {response.status_code}")
        
        if response.status_code == 200:
            logger.info("✅ 装备列表页访问成功")
            
            # 检查页面内容
            soup = BeautifulSoup(response.text, 'html.parser')
            page_text = soup.get_text()
            
            # 检查是否有验证
            verification_patterns = [
                r'\d+[×*]\d+=？', r'\d+[+]\d+=？', r'\d+[-]\d+=？',
                r'羽毛球有几根毛', r'ZYZX.*?怎么写', r'中羽.*?缩写',
                r'验证.*?问题', r'请输入.*?答案',
            ]
            
            has_verification = any(re.search(pattern, page_text, re.IGNORECASE) 
                                 for pattern in verification_patterns)
            
            if has_verification:
                logger.info("⚠️  检测到验证页面")
                # 打印前500字符来查看验证内容
                logger.info(f"页面内容预览: {page_text[:500]}...")
                
                # 查找验证问题
                for pattern in verification_patterns:
                    matches = re.findall(pattern, page_text, re.IGNORECASE)
                    if matches:
                        logger.info(f"找到验证问题: {matches[0]}")
                        break
            else:
                logger.info("✅ 未检测到验证，直接访问成功")
                
                # 查找装备链接
                equipment_links = []
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    if href and 'view.php?eid=' in href:
                        equipment_links.append(href)
                
                logger.info(f"找到 {len(equipment_links)} 个装备链接")
                if equipment_links:
                    logger.info(f"示例链接: {equipment_links[0]}")
                
        else:
            logger.error(f"❌ 装备列表页访问失败: {response.status_code}")
            
    except Exception as e:
        logger.error(f"❌ 装备列表页访问异常: {e}")
    
    # 测试具体类型的装备列表
    logger.info("\n🏓 测试羽毛球拍类型页面...")
    racket_url = f"{base_url}/cbo_eq/list.php?tid=1"
    
    try:
        response = session.get(racket_url, timeout=20)
        logger.info(f"羽毛球拍页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找装备链接
            equipment_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and 'view.php?eid=' in href:
                    equipment_links.append(href)
            
            logger.info(f"羽毛球拍页面找到 {len(equipment_links)} 个装备链接")
            
            if equipment_links:
                # 测试第一个装备详情页
                logger.info("\n📋 测试装备详情页...")
                detail_url = equipment_links[0]
                if not detail_url.startswith('http'):
                    detail_url = f"{base_url}/{detail_url.lstrip('/')}"
                
                logger.info(f"访问详情页: {detail_url}")
                
                try:
                    detail_response = session.get(detail_url, timeout=20)
                    logger.info(f"详情页状态码: {detail_response.status_code}")
                    
                    if detail_response.status_code == 200:
                        detail_soup = BeautifulSoup(detail_response.text, 'html.parser')
                        
                        # 提取标题
                        if detail_soup.title:
                            title = detail_soup.title.string.strip()
                            logger.info(f"装备标题: {title}")
                        
                        # 查找表格
                        tables = detail_soup.find_all('table')
                        logger.info(f"找到 {len(tables)} 个表格")
                        
                        # 提取一些基本信息
                        for table in tables[:3]:  # 只检查前3个表格
                            rows = table.find_all('tr')
                            for row in rows[:5]:  # 只检查前5行
                                cells = row.find_all(['td', 'th'])
                                if len(cells) >= 2:
                                    key = cells[0].get_text(strip=True)
                                    value = cells[1].get_text(strip=True)
                                    if key and value:
                                        logger.info(f"  {key}: {value}")
                    
                except Exception as e:
                    logger.error(f"❌ 详情页访问异常: {e}")
            
        else:
            logger.error(f"❌ 羽毛球拍页面访问失败: {response.status_code}")
            
    except Exception as e:
        logger.error(f"❌ 羽毛球拍页面访问异常: {e}")

if __name__ == "__main__":
    test_website_access() 