# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.keras.backend namespace.
"""

from __future__ import print_function as _print_function

import sys as _sys

from keras.backend import _v1_name_scope as name_scope
from keras.backend import abs
from keras.backend import all
from keras.backend import any
from keras.backend import arange
from keras.backend import argmax
from keras.backend import argmin
from keras.backend import backend
from keras.backend import batch_dot
from keras.backend import batch_flatten
from keras.backend import batch_get_value
from keras.backend import batch_normalization
from keras.backend import batch_set_value
from keras.backend import bias_add
from keras.backend import binary_crossentropy
from keras.backend import cast
from keras.backend import cast_to_floatx
from keras.backend import categorical_crossentropy
from keras.backend import clear_session
from keras.backend import clip
from keras.backend import concatenate
from keras.backend import constant
from keras.backend import conv1d
from keras.backend import conv2d
from keras.backend import conv2d_transpose
from keras.backend import conv3d
from keras.backend import cos
from keras.backend import count_params
from keras.backend import ctc_batch_cost
from keras.backend import ctc_decode
from keras.backend import ctc_label_dense_to_sparse
from keras.backend import cumprod
from keras.backend import cumsum
from keras.backend import depthwise_conv2d
from keras.backend import dot
from keras.backend import dropout
from keras.backend import dtype
from keras.backend import elu
from keras.backend import equal
from keras.backend import eval
from keras.backend import exp
from keras.backend import expand_dims
from keras.backend import eye
from keras.backend import flatten
from keras.backend import foldl
from keras.backend import foldr
from keras.backend import function
from keras.backend import gather
from keras.backend import get_session
from keras.backend import get_uid
from keras.backend import get_value
from keras.backend import gradients
from keras.backend import greater
from keras.backend import greater_equal
from keras.backend import hard_sigmoid
from keras.backend import in_test_phase
from keras.backend import in_top_k
from keras.backend import in_train_phase
from keras.backend import int_shape
from keras.backend import is_keras_tensor
from keras.backend import is_sparse
from keras.backend import l2_normalize
from keras.backend import learning_phase
from keras.backend import learning_phase_scope
from keras.backend import less
from keras.backend import less_equal
from keras.backend import local_conv1d
from keras.backend import local_conv2d
from keras.backend import log
from keras.backend import manual_variable_initialization
from keras.backend import map_fn
from keras.backend import max
from keras.backend import maximum
from keras.backend import mean
from keras.backend import min
from keras.backend import minimum
from keras.backend import moving_average_update
from keras.backend import ndim
from keras.backend import normalize_batch_in_training
from keras.backend import not_equal
from keras.backend import one_hot
from keras.backend import ones
from keras.backend import ones_like
from keras.backend import permute_dimensions
from keras.backend import placeholder
from keras.backend import pool2d
from keras.backend import pool3d
from keras.backend import pow
from keras.backend import print_tensor
from keras.backend import prod
from keras.backend import random_bernoulli
from keras.backend import random_binomial
from keras.backend import random_normal
from keras.backend import random_normal_variable
from keras.backend import random_uniform
from keras.backend import random_uniform_variable
from keras.backend import relu
from keras.backend import repeat
from keras.backend import repeat_elements
from keras.backend import reset_uids
from keras.backend import reshape
from keras.backend import resize_images
from keras.backend import resize_volumes
from keras.backend import reverse
from keras.backend import rnn
from keras.backend import round
from keras.backend import separable_conv2d
from keras.backend import set_learning_phase
from keras.backend import set_session
from keras.backend import set_value
from keras.backend import shape
from keras.backend import sigmoid
from keras.backend import sign
from keras.backend import sin
from keras.backend import softmax
from keras.backend import softplus
from keras.backend import softsign
from keras.backend import sparse_categorical_crossentropy
from keras.backend import spatial_2d_padding
from keras.backend import spatial_3d_padding
from keras.backend import sqrt
from keras.backend import square
from keras.backend import squeeze
from keras.backend import stack
from keras.backend import std
from keras.backend import stop_gradient
from keras.backend import sum
from keras.backend import switch
from keras.backend import tanh
from keras.backend import temporal_padding
from keras.backend import tile
from keras.backend import to_dense
from keras.backend import transpose
from keras.backend import truncated_normal
from keras.backend import update
from keras.backend import update_add
from keras.backend import update_sub
from keras.backend import var
from keras.backend import variable
from keras.backend import zeros
from keras.backend import zeros_like
from keras.backend_config import epsilon
from keras.backend_config import floatx
from keras.backend_config import image_data_format
from keras.backend_config import set_epsilon
from keras.backend_config import set_floatx
from keras.backend_config import set_image_data_format

del _print_function

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.backend", public_apis=None, deprecation=True,
      has_lite=False)
