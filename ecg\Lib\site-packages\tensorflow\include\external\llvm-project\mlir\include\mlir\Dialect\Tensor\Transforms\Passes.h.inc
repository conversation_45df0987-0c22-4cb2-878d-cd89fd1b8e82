/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// TensorBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TensorBufferizeBase : public ::mlir::FunctionPass {
public:
  using Base = TensorBufferizeBase;

  TensorBufferizeBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  TensorBufferizeBase(const TensorBufferizeBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tensor-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "tensor-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the `tensor` dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TensorBufferize");
  }
  ::llvm::StringRef getName() const override { return "TensorBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// TensorBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerTensorBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createTensorBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// Tensor Registration
//===----------------------------------------------------------------------===//

inline void registerTensorPasses() {
  registerTensorBufferizePass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
