{"count": 26, "next": null, "previous": null, "results": [{"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": true, "created_date": "2025-01-27T16:42:31.302000Z", "data": 30, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 31, "image_quality": 70, "jobs": {"completed": 0, "count": 3, "url": "http://localhost:8080/api/jobs?task_id=31", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=31"}, "mode": "annotation", "name": "sandbox task with consensus", "organization": null, "overlap": 0, "owner": {"first_name": "User", "id": 2, "last_name": "First", "url": "http://localhost:8080/api/users/2", "username": "user1"}, "project_id": null, "segment_size": 3, "size": 3, "source_storage": {"cloud_storage_id": null, "id": 53, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 54, "location": "local"}, "updated_date": "2025-01-27T16:43:36.188000Z", "url": "http://localhost:8080/api/tasks/31", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": true, "created_date": "2025-01-14T17:19:03.665000Z", "data": 29, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 30, "image_quality": 70, "jobs": {"completed": 0, "count": 9, "url": "http://localhost:8080/api/jobs?task_id=30", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=30"}, "mode": "annotation", "name": "task with consensus and honeypots", "organization": 2, "overlap": 0, "owner": {"first_name": "User", "id": 3, "last_name": "Second", "url": "http://localhost:8080/api/users/3", "username": "user2"}, "project_id": null, "segment_size": 0, "size": 10, "source_storage": {"cloud_storage_id": null, "id": 51, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 52, "location": "local"}, "updated_date": "2025-01-14T17:26:01.139000Z", "url": "http://localhost:8080/api/tasks/30", "validation_mode": "gt_pool"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2024-10-01T12:36:21.364000Z", "data": 28, "data_chunk_size": 3, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 29, "image_quality": 70, "jobs": {"completed": 0, "count": 4, "url": "http://localhost:8080/api/jobs?task_id=29", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=29"}, "mode": "annotation", "name": "task with honeypots", "organization": 2, "overlap": 0, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": null, "segment_size": 0, "size": 29, "source_storage": null, "status": "annotation", "subset": "", "target_storage": null, "updated_date": "2024-10-01T12:38:26.883000Z", "url": "http://localhost:8080/api/tasks/29", "validation_mode": "gt_pool"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2024-09-23T21:42:22.676000Z", "data": 27, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 28, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=28", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=28"}, "mode": "annotation", "name": "task 1", "organization": 2, "overlap": 0, "owner": {"first_name": "User", "id": 5, "last_name": "Fourth", "url": "http://localhost:8080/api/users/5", "username": "user4"}, "project_id": 10, "segment_size": 1, "size": 1, "source_storage": {"cloud_storage_id": null, "id": 49, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 50, "location": "local"}, "updated_date": "2024-09-23T21:42:23.082000Z", "url": "http://localhost:8080/api/tasks/28", "validation_mode": null}, {"assignee": {"first_name": "User", "id": 5, "last_name": "Fourth", "url": "http://localhost:8080/api/users/5", "username": "user4"}, "assignee_updated_date": "2024-09-23T10:52:48.769000Z", "bug_tracker": "", "consensus_enabled": false, "created_date": "2024-09-23T10:52:04.357000Z", "data": 26, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 27, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=27", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=27"}, "mode": "annotation", "name": "task assigned to maintainer", "organization": 2, "overlap": 0, "owner": {"first_name": "User", "id": 4, "last_name": "Third", "url": "http://localhost:8080/api/users/4", "username": "user3"}, "project_id": null, "segment_size": 1, "size": 1, "source_storage": {"cloud_storage_id": null, "id": 47, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 48, "location": "local"}, "updated_date": "2024-09-23T10:52:48.778000Z", "url": "http://localhost:8080/api/tasks/27", "validation_mode": null}, {"assignee": {"first_name": "User", "id": 10, "last_name": "Seventh", "url": "http://localhost:8080/api/users/10", "username": "user7"}, "assignee_updated_date": "2024-09-23T10:51:45.525000Z", "bug_tracker": "", "consensus_enabled": false, "created_date": "2024-09-23T10:51:32.778000Z", "data": 25, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 26, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=26", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=26"}, "mode": "annotation", "name": "task assigned to owner", "organization": 2, "overlap": 0, "owner": {"first_name": "User", "id": 4, "last_name": "Third", "url": "http://localhost:8080/api/users/4", "username": "user3"}, "project_id": null, "segment_size": 1, "size": 1, "source_storage": {"cloud_storage_id": null, "id": 45, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 46, "location": "local"}, "updated_date": "2024-09-23T10:51:45.533000Z", "url": "http://localhost:8080/api/tasks/26", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2024-07-15T15:34:53.156000Z", "data": 24, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 25, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=25", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=25"}, "mode": "annotation", "name": "task-validation", "organization": null, "overlap": 0, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": 14, "segment_size": 1, "size": 1, "source_storage": {"cloud_storage_id": null, "id": 39, "location": "local"}, "status": "annotation", "subset": "Validation", "target_storage": {"cloud_storage_id": null, "id": 40, "location": "local"}, "updated_date": "2024-07-15T15:34:53.692000Z", "url": "http://localhost:8080/api/tasks/25", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2024-07-15T15:33:10.135000Z", "data": 23, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 24, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=24", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=24"}, "mode": "annotation", "name": "task_train", "organization": null, "overlap": 0, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": 14, "segment_size": 1, "size": 1, "source_storage": {"cloud_storage_id": null, "id": 37, "location": "local"}, "status": "annotation", "subset": "Train", "target_storage": {"cloud_storage_id": null, "id": 38, "location": "local"}, "updated_date": "2024-07-15T15:33:10.641000Z", "url": "http://localhost:8080/api/tasks/24", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2024-03-21T20:50:05.694000Z", "data": 22, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 23, "image_quality": 70, "jobs": {"completed": 4, "count": 4, "url": "http://localhost:8080/api/jobs?task_id=23", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=23"}, "mode": "annotation", "name": "task with several jobs and ground truth job", "organization": null, "overlap": 0, "owner": {"first_name": "Admin", "id": 18, "last_name": "Second", "url": "http://localhost:8080/api/users/18", "username": "admin2"}, "project_id": null, "segment_size": 5, "size": 11, "source_storage": null, "status": "completed", "subset": "", "target_storage": null, "updated_date": "2024-03-21T20:50:05.947000Z", "url": "http://localhost:8080/api/tasks/23", "validation_mode": "gt"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2023-05-26T16:11:23.540000Z", "data": 21, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 22, "image_quality": 70, "jobs": {"completed": 1, "count": 2, "url": "http://localhost:8080/api/jobs?task_id=22", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=22"}, "mode": "annotation", "name": "task3_with_gt_job", "organization": 2, "overlap": 0, "owner": {"first_name": "User", "id": 3, "last_name": "Second", "url": "http://localhost:8080/api/users/3", "username": "user2"}, "project_id": null, "segment_size": 11, "size": 11, "source_storage": null, "status": "validation", "subset": "Train", "target_storage": null, "updated_date": "2023-11-24T15:23:30.045000Z", "url": "http://localhost:8080/api/tasks/22", "validation_mode": "gt"}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2023-03-27T19:08:07.649000Z", "data": 20, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 21, "image_quality": 70, "jobs": {"completed": 0, "count": 2, "url": "http://localhost:8080/api/jobs?task_id=21", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=21"}, "mode": "annotation", "name": "task with several jobs", "organization": null, "overlap": 0, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": null, "segment_size": 6, "size": 10, "source_storage": {"cloud_storage_id": null, "id": 33, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 34, "location": "local"}, "updated_date": "2023-03-27T19:08:40.032000Z", "url": "http://localhost:8080/api/tasks/21", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2023-03-10T11:57:31.614000Z", "data": 19, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 20, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=20", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=20"}, "mode": "annotation", "name": "task in project with attributes", "organization": null, "overlap": 0, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": 12, "segment_size": 2, "size": 2, "source_storage": {"cloud_storage_id": null, "id": 29, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 30, "location": "local"}, "updated_date": "2023-03-10T11:57:48.835000Z", "url": "http://localhost:8080/api/tasks/20", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2023-03-10T11:56:33.757000Z", "data": 18, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 19, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=19", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=19"}, "mode": "annotation", "name": "task with attributes", "organization": null, "overlap": 0, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": null, "segment_size": 2, "size": 2, "source_storage": {"cloud_storage_id": null, "id": 25, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 26, "location": "local"}, "updated_date": "2023-03-10T11:56:54.904000Z", "url": "http://localhost:8080/api/tasks/19", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2023-03-01T15:36:26.668000Z", "data": 17, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 18, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=18", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=18"}, "mode": "annotation", "name": "task in project7", "organization": 2, "overlap": 0, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": 11, "segment_size": 2, "size": 2, "source_storage": {"cloud_storage_id": null, "id": 23, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 24, "location": "local"}, "updated_date": "2023-03-01T15:36:37.897000Z", "url": "http://localhost:8080/api/tasks/18", "validation_mode": null}, {"assignee": {"first_name": "User", "id": 3, "last_name": "Second", "url": "http://localhost:8080/api/users/3", "username": "user2"}, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2023-02-10T14:05:25.947000Z", "data": 16, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 17, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=17", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=17"}, "mode": "annotation", "name": "task_3_org2", "organization": 2, "overlap": 0, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": null, "segment_size": 5, "size": 5, "source_storage": null, "status": "annotation", "subset": "", "target_storage": null, "updated_date": "2023-02-10T14:08:05.873000Z", "url": "http://localhost:8080/api/tasks/17", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2022-12-01T12:53:10.425000Z", "data": 14, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "video", "dimension": "2d", "guide_id": null, "id": 15, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=15", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=15"}, "mode": "interpolation", "name": "task with video data", "organization": null, "overlap": 5, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": 8, "segment_size": 25, "size": 25, "source_storage": {"cloud_storage_id": null, "id": 15, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 16, "location": "local"}, "updated_date": "2022-12-01T12:53:35.028000Z", "url": "http://localhost:8080/api/tasks/15", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "https://bugtracker.localhost/task/12345", "consensus_enabled": false, "created_date": "2022-09-22T14:22:25.820000Z", "data": 13, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 14, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=14", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=14"}, "mode": "annotation", "name": "task1 in project5", "organization": 2, "overlap": 0, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": 5, "segment_size": 8, "size": 8, "source_storage": {"cloud_storage_id": null, "id": 7, "location": "local"}, "status": "annotation", "subset": "", "target_storage": {"cloud_storage_id": null, "id": 8, "location": "local"}, "updated_date": "2022-09-23T11:57:02.300000Z", "url": "http://localhost:8080/api/tasks/14", "validation_mode": null}, {"assignee": {"first_name": "User", "id": 4, "last_name": "Third", "url": "http://localhost:8080/api/users/4", "username": "user3"}, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2022-06-08T08:33:06.505000Z", "data": 12, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 13, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=13", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=13"}, "mode": "annotation", "name": "task1_in_project4", "organization": 2, "overlap": 0, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": 4, "segment_size": 5, "size": 5, "source_storage": null, "status": "annotation", "subset": "", "target_storage": null, "updated_date": "2023-02-10T11:50:18.414000Z", "url": "http://localhost:8080/api/tasks/13", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2022-03-14T13:24:05.852000Z", "dimension": "2d", "guide_id": null, "id": 12, "jobs": {"completed": 0, "count": 0, "url": "http://localhost:8080/api/jobs?task_id=12", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=12"}, "mode": "", "name": "task_without_data", "organization": null, "overlap": null, "owner": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "project_id": null, "segment_size": 0, "source_storage": null, "status": "annotation", "subset": "", "target_storage": null, "updated_date": "2022-03-14T13:24:05.861000Z", "url": "http://localhost:8080/api/tasks/12", "validation_mode": null}, {"assignee": {"first_name": "User", "id": 19, "last_name": "Fifth", "url": "http://localhost:8080/api/users/19", "username": "user5"}, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2022-03-05T10:32:19.149000Z", "data": 11, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 11, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=11", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=11"}, "mode": "annotation", "name": "task1_in_project2", "organization": 2, "overlap": 0, "owner": {"first_name": "User", "id": 10, "last_name": "Seventh", "url": "http://localhost:8080/api/users/10", "username": "user7"}, "project_id": 2, "segment_size": 11, "size": 11, "source_storage": {"cloud_storage_id": 3, "id": 4, "location": "cloud_storage"}, "status": "annotation", "subset": "Train", "target_storage": {"cloud_storage_id": 3, "id": 2, "location": "cloud_storage"}, "updated_date": "2022-06-30T08:56:45.594000Z", "url": "http://localhost:8080/api/tasks/11", "validation_mode": null}, {"assignee": {"first_name": "Admin", "id": 1, "last_name": "First", "url": "http://localhost:8080/api/users/1", "username": "admin1"}, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2022-03-05T09:33:10.420000Z", "data": 9, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 9, "image_quality": 70, "jobs": {"completed": 0, "count": 4, "url": "http://localhost:8080/api/jobs?task_id=9", "validation": 1}, "labels": {"url": "http://localhost:8080/api/labels?task_id=9"}, "mode": "annotation", "name": "task1_in_project1", "organization": null, "overlap": 0, "owner": {"first_name": "User", "id": 10, "last_name": "Seventh", "url": "http://localhost:8080/api/users/10", "username": "user7"}, "project_id": 1, "segment_size": 5, "size": 20, "source_storage": null, "status": "annotation", "subset": "", "target_storage": null, "updated_date": "2022-11-03T13:57:26.007000Z", "url": "http://localhost:8080/api/tasks/9", "validation_mode": null}, {"assignee": {"first_name": "Worker", "id": 9, "last_name": "Fourth", "url": "http://localhost:8080/api/users/9", "username": "worker4"}, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2022-03-05T08:30:48.612000Z", "data": 8, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 8, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=8", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=8"}, "mode": "annotation", "name": "task1", "organization": null, "overlap": 0, "owner": {"first_name": "User", "id": 2, "last_name": "First", "url": "http://localhost:8080/api/users/2", "username": "user1"}, "project_id": null, "segment_size": 14, "size": 14, "source_storage": null, "status": "annotation", "subset": "", "target_storage": null, "updated_date": "2023-05-02T09:28:57.638000Z", "url": "http://localhost:8080/api/tasks/8", "validation_mode": null}, {"assignee": {"first_name": "Worker", "id": 7, "last_name": "Second", "url": "http://localhost:8080/api/users/7", "username": "worker2"}, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2022-02-21T10:31:52.429000Z", "data": 7, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 7, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=7", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=7"}, "mode": "annotation", "name": "task_2_org2", "organization": 2, "overlap": 0, "owner": {"first_name": "User", "id": 11, "last_name": "Eighth", "url": "http://localhost:8080/api/users/11", "username": "user8"}, "project_id": null, "segment_size": 11, "size": 11, "source_storage": null, "status": "annotation", "subset": "", "target_storage": null, "updated_date": "2022-02-21T10:41:38.540000Z", "url": "http://localhost:8080/api/tasks/7", "validation_mode": null}, {"assignee": null, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2022-02-16T06:26:54.631000Z", "data": 6, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "3d", "guide_id": null, "id": 6, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=6", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=6"}, "mode": "annotation", "name": "task3", "organization": null, "overlap": 0, "owner": {"first_name": "User", "id": 2, "last_name": "First", "url": "http://localhost:8080/api/users/2", "username": "user1"}, "project_id": null, "segment_size": 1, "size": 1, "source_storage": null, "status": "annotation", "subset": "", "target_storage": null, "updated_date": "2022-02-16T06:26:54.836000Z", "url": "http://localhost:8080/api/tasks/6", "validation_mode": null}, {"assignee": {"first_name": "Dummy", "id": 15, "last_name": "Second", "url": "http://localhost:8080/api/users/15", "username": "dummy2"}, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2022-02-16T06:25:48.168000Z", "data": 5, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "video", "dimension": "2d", "guide_id": null, "id": 5, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=5", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=5"}, "mode": "interpolation", "name": "task2", "organization": null, "overlap": 5, "owner": {"first_name": "User", "id": 2, "last_name": "First", "url": "http://localhost:8080/api/users/2", "username": "user1"}, "project_id": null, "segment_size": 25, "size": 25, "source_storage": null, "status": "annotation", "subset": "", "target_storage": null, "updated_date": "2022-02-21T10:40:21.257000Z", "url": "http://localhost:8080/api/tasks/5", "validation_mode": null}, {"assignee": {"first_name": "Worker", "id": 7, "last_name": "Second", "url": "http://localhost:8080/api/users/7", "username": "worker2"}, "assignee_updated_date": null, "bug_tracker": "", "consensus_enabled": false, "created_date": "2021-12-14T18:50:29.458000Z", "data": 2, "data_chunk_size": 72, "data_compressed_chunk_type": "imageset", "data_original_chunk_type": "imageset", "dimension": "2d", "guide_id": null, "id": 2, "image_quality": 70, "jobs": {"completed": 0, "count": 1, "url": "http://localhost:8080/api/jobs?task_id=2", "validation": 0}, "labels": {"url": "http://localhost:8080/api/labels?task_id=2"}, "mode": "annotation", "name": "task2", "organization": 1, "overlap": 0, "owner": {"first_name": "User", "id": 2, "last_name": "First", "url": "http://localhost:8080/api/users/2", "username": "user1"}, "project_id": null, "segment_size": 23, "size": 23, "source_storage": null, "status": "annotation", "subset": "", "target_storage": null, "updated_date": "2021-12-22T07:14:15.234000Z", "url": "http://localhost:8080/api/tasks/2", "validation_mode": null}]}