import pandas as pd
import numpy as np
import h5py
from sql_helper import MysqlHelper
import json
import os
import time
from datetime import datetime
from tqdm import tqdm
import pickle

def get_db_connection(max_retries=10, retry_delay=30):
    """获取数据库连接，带重试机制"""
    for attempt in range(max_retries):
        try:
            print(f"尝试连接数据库 (尝试 {attempt + 1}/{max_retries})...")
            db = MysqlHelper("default")
            conn = db.get_con()
            print("数据库连接成功！")
            return db, conn
        except Exception as e:
            print(f"连接失败: {str(e)}")
            if attempt < max_retries - 1:
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay = min(retry_delay * 2, 300)
    raise Exception("无法连接到数据库，请检查服务器状态或联系管理员")

def process_record(record, points_per_10s):
    """处理单条记录的函数"""
    record_id, ecg_data_json, sample_rate, age, gender = record
    if not ecg_data_json or not sample_rate:
        return None
        
    try:
        # 解析ECG数据
        ecg_data = np.array(json.loads(ecg_data_json), dtype=np.float32)
        
        # 检查数据长度是否符合要求
        if len(ecg_data) < points_per_10s:
            print(f"跳过记录 {record_id}: 数据长度不足 (长度: {len(ecg_data)}, 需要: {points_per_10s})")
            return None
            
        # 计算可以分割的完整10秒片段
        num_segments = len(ecg_data) // points_per_10s
        if num_segments == 0:
            return None
            
        # 直接创建大数组，避免列表操作
        result = np.zeros((num_segments, points_per_10s), dtype=np.float32)
        for i in range(num_segments):
            result[i] = ecg_data[i * points_per_10s:(i + 1) * points_per_10s]
            
        return {
            'record_id': record_id,
            'segments': result,
            'sample_rate': sample_rate,
            'age': age if age is not None else -1,
            'gender': gender if gender is not None else -1,
            'num_segments': num_segments
        }
        
    except Exception as e:
        print(f"处理记录 {record_id} 时出错: {str(e)}")
        return None

def save_batch_to_hdf5(processed_data, filepath):
    """优化的HDF5保存函数"""
    if not processed_data:
        return 0
        
    total_segments = sum(data['num_segments'] for data in processed_data if data is not None)
    
    with h5py.File(filepath, 'w') as f:
        # 创建数据集
        data_shape = (total_segments, processed_data[0]['segments'].shape[1])
        segments_dataset = f.create_dataset('ecg_data', shape=data_shape, dtype=np.float32)
        
        # 创建属性数据集
        dt = h5py.special_dtype(vlen=str)
        record_ids = f.create_dataset('record_ids', (total_segments,), dtype=dt)
        ages = f.create_dataset('ages', (total_segments,), dtype=np.int32)
        genders = f.create_dataset('genders', (total_segments,), dtype=np.int32)
        sampling_rates = f.create_dataset('sampling_rates', (total_segments,), dtype=np.int32)
        
        # 批量写入数据
        idx = 0
        for data in processed_data:
            if data is None:
                continue
                
            num_segs = data['num_segments']
            segments_dataset[idx:idx + num_segs] = data['segments']
            
            # 填充属性数据
            for i in range(num_segs):
                record_ids[idx + i] = f"record_{data['record_id']}_seg_{i + 1}"
                ages[idx + i] = data['age']
                genders[idx + i] = data['gender']
                sampling_rates[idx + i] = data['sample_rate']
            
            idx += num_segs
        
        # 添加元数据
        f.attrs['total_records'] = total_segments
        f.attrs['duration'] = 10

def save_checkpoint(checkpoint_data, filepath):
    """保存断点信息"""
    with open(filepath, 'wb') as f:
        pickle.dump(checkpoint_data, f)

def load_checkpoint(filepath):
    """加载断点信息"""
    if os.path.exists(filepath):
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    return None

def export_lead_i_data():
    save_dir = 'D:/Project/data_check/ecg_data'
    os.makedirs(save_dir, exist_ok=True)
    checkpoint_file = os.path.join(save_dir, 'checkpoint.pkl')
    
    try:
        # 加载断点信息
        checkpoint = load_checkpoint(checkpoint_file)
        if checkpoint:
            last_id = checkpoint['last_id']
            file_count = checkpoint['file_count']
            total_processed = checkpoint['total_processed']
            print(f"从断点继续：上次处理到ID {last_id}，已处理 {total_processed} 条记录")
        else:
            last_id = 0
            file_count = 1
            total_processed = 0
        
        # 获取数据库连接
        db, conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取总记录数
        cursor.execute("SELECT COUNT(*) FROM t_patient_ecg_i WHERE ecg_data IS NOT NULL")
        total_count = cursor.fetchone()[0]
        print(f"总记录数：{total_count}")
        
        # 设置批次大小
        batch_size = 50000  # 每50000条记录保存一个文件
        
        # 准备SQL语句
        sql = """
        SELECT 
            i.id,
            i.ecg_data,
            e.sample_rate,
            p.age,
            p.gender
        FROM t_patient_ecg_i i
        LEFT JOIN t_patient_ecg e ON i.study_uid = e.study_uid
        LEFT JOIN t_patient p ON e.patient_id = p.patient_id
        WHERE i.id > %s
        ORDER BY i.id
        LIMIT 1
        """
        
        # 初始化变量
        processed_data = []
        
        # 使用tqdm显示总体进度
        with tqdm(total=total_count, initial=total_processed, desc="处理记录") as pbar:
            while True:
                try:
                    # 查询单条记录
                    cursor.execute(sql, (last_id,))
                    result = cursor.fetchone()
                    
                    if not result:
                        break
                        
                    # 更新last_id
                    last_id = result[0]
                    
                    # 处理记录
                    if result[1] and result[2]:  # 确保有ECG数据和采样率
                        points_per_10s = int(10 * (result[2] or 500))
                        processed = process_record(result, points_per_10s)
                        if processed:
                            processed_data.append(processed)
                    
                    # 更新进度条
                    pbar.update(1)
                    total_processed += 1
                    
                    # 当累积到足够多的记录时，保存文件
                    total_segments = sum(data['num_segments'] for data in processed_data if data is not None)
                    if total_segments >= batch_size:
                        # 创建HDF5文件
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f'ecg_lead_i_batch_{file_count}_{timestamp}.hdf5'
                        filepath = os.path.join(save_dir, filename)
                        
                        # 保存数据
                        print(f"\n保存第 {file_count} 个文件...")
                        save_batch_to_hdf5(processed_data, filepath)
                        print(f"已保存 {total_segments} 条10秒长度的记录到文件: {filename}")
                        
                        # 保存断点信息
                        checkpoint_data = {
                            'last_id': last_id,
                            'file_count': file_count + 1,
                            'total_processed': total_processed
                        }
                        save_checkpoint(checkpoint_data, checkpoint_file)
                        
                        # 重置变量
                        processed_data = []
                        file_count += 1
                    
                except Exception as e:
                    print(f"\n处理出错：{str(e)}")
                    print("保存断点并尝试重新连接...")
                    
                    # 保存断点信息
                    checkpoint_data = {
                        'last_id': last_id,
                        'file_count': file_count,
                        'total_processed': total_processed
                    }
                    save_checkpoint(checkpoint_data, checkpoint_file)
                    
                    # 重新连接数据库
                    try:
                        cursor.close()
                        db.close(conn)
                    except:
                        pass
                    
                    db, conn = get_db_connection()
                    cursor = conn.cursor()
                    continue
        
        # 保存剩余的数据
        if processed_data:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'ecg_lead_i_batch_{file_count}_{timestamp}.hdf5'
            filepath = os.path.join(save_dir, filename)
            save_batch_to_hdf5(processed_data, filepath)
            total_segments = sum(data['num_segments'] for data in processed_data if data is not None)
            print(f"\n已保存最后 {total_segments} 条记录到文件: {filename}")
        
        # 清理断点文件
        if os.path.exists(checkpoint_file):
            os.remove(checkpoint_file)
        
        cursor.close()
        db.close(conn)
        print("\n所有数据处理完成！")
        
    except Exception as e:
        print(f"发生错误：{str(e)}")
        if 'last_id' in locals():
            checkpoint_data = {
                'last_id': last_id,
                'file_count': file_count,
                'total_processed': total_processed
            }
            save_checkpoint(checkpoint_data, checkpoint_file)
            print(f"已保存断点信息，下次可以从ID {last_id} 继续")

if __name__ == "__main__":
    export_lead_i_data()
