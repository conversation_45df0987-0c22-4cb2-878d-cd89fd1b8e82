# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Norwegian Nynorsk (http://www.transifex.com/django/django/"
"language/nn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr ""

msgid "python model class name"
msgstr "python-modell klassenamn"

msgid "content type"
msgstr "innhaldstype"

msgid "content types"
msgstr "innhaldstypar"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Innhaldstype %(ct_id)s-objektet har ingen modell knytta til seg"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Innhaldstype %(ct_id)s-objektet med id %(obj_id)s finst ikkje"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s-objekt har ingen get_absolute_url()-metode"
