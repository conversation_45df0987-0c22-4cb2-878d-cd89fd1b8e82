/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// GPU Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterGPUPasses() {
  registerGPUPasses();
}

MlirPass mlirCreateGPUGpuAsyncRegionPass() {
  return wrap(mlir::createGpuAsyncRegionPass().release());
}
void mlirRegisterGPUGpuAsyncRegionPass() {
  registerGpuAsyncRegionPassPass();
}


MlirPass mlirCreateGPUGpuKernelOutlining() {
  return wrap(mlir::createGpuKernelOutliningPass().release());
}
void mlirRegisterGPUGpuKernelOutlining() {
  registerGpuKernelOutliningPass();
}

