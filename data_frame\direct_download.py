import requests
import os
import json
import csv
from qiniu import Auth

# 要下载的特定es_key
TARGET_ES_KEY = "CUSTOMER19054257726245560321078/20250401152805"

# 七牛云配置
QINIU = {
    'test': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://test.upload.weiheyixue.com'
    },
    'prod': {
        'access_key': 'TZJd1cMaDUqCf_sJlAQBHuf2vH8HlMj9d9NItVgL',
        'secret_key': '7yZmEoVeQv0S0Uvb_uZSbV065JBNZ_uDqY_cVjG',
        'domain_prefix': 'https://upload.weiheyixue.com'
    }
}

# 本地保存路径
SAVE_DIR = r'D:\ECG\0331标注平台数据\特定查询'

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")

def sanitize_filename(filename):
    """将 es_key 转换为有效的文件名"""
    return filename.replace('/', '_').replace('\\', '_')

def direct_download(es_key, environment='prod'):
    """直接从七牛云下载指定es_key的文件，不通过数据库查询"""
    # 确保保存目录存在
    ensure_dir_exists(SAVE_DIR)
    
    try:
        # 获取七牛云配置
        access_key = QINIU[environment]['access_key']
        secret_key = QINIU[environment]['secret_key']
        domain_prefix = QINIU[environment]['domain_prefix']
        
        # 尝试几种不同的路径格式
        paths_to_try = [
            f'ecg/{es_key}',                 # 标准路径
            f'{es_key}',                     # 直接使用es_key
            f'data/{es_key}',                # 可能的替代路径
            f'file/{es_key}',                # 可能的替代路径
            f'ecg_data/{es_key}',            # 可能的替代路径
            f'ecg/{es_key}.json',            # 添加可能的扩展名
            f'ecg/{es_key}.dat'              # 添加可能的扩展名
        ]
        
        # 构建鉴权对象
        q = Auth(access_key, secret_key)
        
        for path in paths_to_try:
            print(f"\n尝试路径: {path}")
            
            # 生成私有下载链接
            private_url = q.private_download_url(domain_prefix + '/' + path, expires=3600)
            
            # 下载文件
            print(f"请求URL: {private_url.split('?')[0]}")  # 不打印完整URL以隐藏签名
            response = requests.get(private_url)
            
            if response.status_code == 200:
                print(f"成功! 状态码: {response.status_code}, 内容长度: {len(response.content)} 字节")
                
                # 准备文件名
                filename_base = sanitize_filename(es_key)
                
                # 保存原始文件
                ext = '.dat'
                if 'json' in path or 'application/json' in response.headers.get('Content-Type', ''):
                    ext = '.json'
                    
                raw_filename = f"{filename_base}_from_{path.replace('/', '_')}{ext}"
                raw_path = os.path.join(SAVE_DIR, raw_filename)
                
                with open(raw_path, 'wb') as f:
                    f.write(response.content)
                print(f"已保存原始文件: {raw_path}")
                
                # 尝试解析为JSON
                try:
                    data = json.loads(response.content)
                    
                    # 保存美化的JSON便于查看
                    pretty_path = os.path.join(SAVE_DIR, f"{filename_base}_pretty.json")
                    with open(pretty_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2, ensure_ascii=False)
                    print(f"已保存美化的JSON: {pretty_path}")
                    
                    # 检查是否包含ECG数据
                    if isinstance(data, dict) and 'ecg' in data:
                        ecg_data = None
                        
                        # 如果ecg字段是字符串，尝试解析为JSON
                        if isinstance(data['ecg'], str):
                            try:
                                ecg_data = json.loads(data['ecg'])
                            except:
                                # 尝试手动解析
                                if data['ecg'].startswith('[') and data['ecg'].endswith(']'):
                                    try:
                                        values_str = data['ecg'][1:-1].split(',')
                                        ecg_data = [float(x.strip()) for x in values_str]
                                    except:
                                        pass
                        elif isinstance(data['ecg'], list):
                            ecg_data = data['ecg']
                            
                        # 如果成功提取了ECG数据，保存为CSV
                        if ecg_data:
                            csv_path = os.path.join(SAVE_DIR, f"{filename_base}.csv")
                            with open(csv_path, 'w', newline='') as csvfile:
                                writer = csv.writer(csvfile)
                                writer.writerow(ecg_data)  # 写入单行，每个数据点一列
                            print(f"已提取并保存ECG数据为CSV: {csv_path}")
                            print(f"ECG数据包含 {len(ecg_data)} 个数据点")
                except:
                    print("文件不是有效的JSON格式")
                
                return True
            else:
                print(f"失败: 状态码 {response.status_code}")
        
        print("\n所有尝试路径均失败")
        return False
    
    except Exception as e:
        print(f"下载过程中出错: {e}")
        return False

if __name__ == "__main__":
    print(f"开始直接从七牛云下载 es_key: {TARGET_ES_KEY}")
    
    # 尝试生产环境
    print("\n--- 尝试生产环境 (prod) ---")
    success_prod = direct_download(TARGET_ES_KEY, environment='prod')
    
    # 如果生产环境失败，尝试测试环境
    if not success_prod:
        print("\n--- 尝试测试环境 (test) ---")
        success_test = direct_download(TARGET_ES_KEY, environment='test')
        success = success_test
    else:
        success = success_prod
    
    if success:
        print("\n下载成功完成!")
    else:
        print("\n两种环境都下载失败。")
        print("可能原因:")
        print("1. 这个es_key数据可能不存在或已被删除")
        print("2. 存储方式不是使用七牛云") 
        print("3. 路径格式完全不同")
        print("4. 权限不足")
        
    print("\n任务完成") 