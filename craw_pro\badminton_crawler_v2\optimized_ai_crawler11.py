#!/usr/bin/env python3
"""
优化版AI验证绕过爬虫 - 基于页面结构分析的精准数据提取
根据页面结构分析结果，优化价格和装备信息的提取逻辑
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedAICrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 确保输出目录存在
        os.makedirs('output', exist_ok=True)
    
    def ask_ai_for_answer(self, question):
        """向AI询问验证问题的答案 - 增强版"""
        try:
            logger.info(f"向AI询问问题: {question}")
            
            # 预处理常见问题
            question_lower = str(question).lower()
            
            # 直接回答的问题
            direct_answers = {
                '羽毛球有几根毛': '16',
                '羽毛球几根毛': '16', 
                'zyzx小写怎么写': 'zyzx',
                'zyzx大写怎么写': 'ZYZX',
                'zyzx怎么写': 'zyzx',
                '中羽在线英文缩写': 'ZYZX',
                '中羽缩写': 'ZYZX',
            }
            
            for key, answer in direct_answers.items():
                if key in question_lower:
                    logger.info(f"直接回答: {answer}")
                    return answer
            
            # 数学计算
            math_result = self.calculate_math_expression(question)
            if math_result is not None:
                logger.info(f"数学计算结果: {math_result}")
                return str(math_result)
            
            # 调用AI API
            api_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer pat-20241215-QGKMa1gNWFqZCeaQ4nO5Wjrd5e1YdOZWGWH1GsQN35BUKi5m39sVhL4iGQXO1Pj5"
            }
            
            system_prompt = """你是一个专门回答验证问题的助手。请直接给出准确的答案，不要解释。

常见问题类型和答案：
1. 羽毛球有几根毛？答案：16
2. ZYZX小写怎么写？答案：zyzx  
3. ZYZX大写怎么写？答案：ZYZX
4. 数学运算：如1+1=2, 5×10=50, 8-3=5等
5. 中羽在线英文缩写？答案：ZYZX

请只返回答案内容，不要其他文字。"""
            
            data = {
                "model": "ep-20241215142258-fwxf9",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"问题：{question}\n请直接给出答案："}
                ],
                "max_tokens": 50,
                "temperature": 0.1
            }
            
            response = requests.post(api_url, headers=headers, json=data, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                answer = result['choices'][0]['message']['content'].strip()
                
                # 提取数字答案
                numbers = re.findall(r'\d+', answer)
                if numbers:
                    final_answer = numbers[0]
                    logger.info(f"AI回答: {final_answer}")
                    return final_answer
                else:
                    logger.info(f"AI回答: {answer}")
                    return answer
            else:
                logger.error(f"AI API请求失败: {response.status_code}")
                return self.get_fallback_answer(question)
                
        except Exception as e:
            logger.error(f"AI请求失败: {e}")
            return self.get_fallback_answer(question)
    
    def calculate_math_expression(self, question):
        """计算数学表达式"""
        try:
            # 提取数学表达式
            patterns = [
                r'(\d+)\s*[×*]\s*(\d+)',
                r'(\d+)\s*[+]\s*(\d+)',
                r'(\d+)\s*[-]\s*(\d+)',
                r'(\d+)\s*[/÷]\s*(\d+)',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, str(question))
                if match:
                    num1, num2 = int(match.group(1)), int(match.group(2))
                    
                    if '×' in question or '*' in question:
                        return num1 * num2
                    elif '+' in question:
                        return num1 + num2
                    elif '-' in question:
                        return num1 - num2
                    elif '/' in question or '÷' in question:
                        return num1 // num2 if num2 != 0 else None
            
            return None
            
        except Exception:
            return None
    
    def get_fallback_answer(self, question):
        """备用答案策略"""
        # 简单的数学计算
        if '×' in question or '*' in question:
            parts = re.findall(r'\d+', question)
            if len(parts) >= 2:
                return str(int(parts[0]) * int(parts[1]))
        elif '+' in question:
            parts = re.findall(r'\d+', question)
            if len(parts) >= 2:
                return str(int(parts[0]) + int(parts[1]))
        elif '-' in question:
            parts = re.findall(r'\d+', question)
            if len(parts) >= 2:
                return str(int(parts[0]) - int(parts[1]))
        
        return "42"  # 默认答案
    
    def bypass_verification(self, url, max_retries=3):
        """绕过验证页面 - 增强版带重试机制"""
        for attempt in range(max_retries):
            try:
                logger.info(f"访问 (尝试 {attempt + 1}/{max_retries}): {url}")
                response = self.session.get(url, timeout=15)
                
                if response.status_code != 200:
                    logger.warning(f"请求状态码异常: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                        continue
                    else:
                        logger.error(f"最终请求失败，状态码: {response.status_code}")
                        return None
                    
                # 检查是否是验证页面 - 更精确的判断
                response_text = response.text
                
                # 先检查是否包含验证问题的具体模式
                verification_patterns = [
                    r'\d+[×*]\d+=？',
                    r'\d+[+]\d+=？', 
                    r'\d+[-]\d+=？',
                    r'请回答.*?\d+[×*+\-]\d+',
                    r'羽毛球有几根毛',
                    r'ZYZX.*?怎么写',
                    r'中羽.*?缩写',
                    r'验证.*?问题',
                    r'请输入.*?答案',
                ]
                
                has_verification_question = any(re.search(pattern, response_text, re.IGNORECASE) 
                                              for pattern in verification_patterns)
                
                # 检查是否有验证表单
                soup = BeautifulSoup(response_text, 'html.parser')
                verification_form = soup.find('form')
                has_verification_form = False
                
                if verification_form:
                    form_text = verification_form.get_text()
                    form_inputs = verification_form.find_all('input')
                    
                    # 检查表单是否包含验证相关的输入字段
                    verification_inputs = ['answer', 'verify', 'code', 'result', 'a']
                    has_answer_input = any(inp.get('name') in verification_inputs for inp in form_inputs)
                    
                    # 检查表单文本是否包含验证问题
                    has_question_in_form = any(re.search(pattern, form_text, re.IGNORECASE) 
                                              for pattern in verification_patterns)
                    
                    has_verification_form = has_answer_input and has_question_in_form
                
                # 只有同时包含验证问题和验证表单才认为是验证页面
                is_verification = has_verification_question and has_verification_form
                
                if is_verification:
                    logger.info(f"检测到验证页面 (尝试 {attempt + 1})")
                    verified_content = self.handle_verification(response.text, url)
                    
                    if verified_content:
                        logger.info(f"✅ 验证成功 (尝试 {attempt + 1})")
                        
                        # 验证成功后，重新访问原始URL确保获取到正确内容
                        time.sleep(1)
                        final_response = self.session.get(url, timeout=15)
                        
                        if final_response.status_code == 200:
                            # 再次检查是否还有验证
                            final_has_question = any(re.search(pattern, final_response.text, re.IGNORECASE) 
                                                   for pattern in verification_patterns)
                            if not final_has_question:
                                logger.info("✅ 最终访问成功，无验证")
                                return final_response.text
                            else:
                                logger.warning("验证后仍有验证内容，重试...")
                                if attempt < max_retries - 1:
                                    time.sleep(3)
                                    continue
                        else:
                            logger.warning(f"验证后重访问失败: {final_response.status_code}")
                            if attempt < max_retries - 1:
                                time.sleep(2)
                                continue
                    else:
                        logger.warning(f"验证处理失败 (尝试 {attempt + 1})")
                        if attempt < max_retries - 1:
                            time.sleep(3)
                            continue
                else:
                    logger.info(f"✅ 直接访问成功，无需验证 (尝试 {attempt + 1})")
                    return response.text
                    
            except Exception as e:
                logger.error(f"访问失败 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue
                else:
                    logger.error(f"所有重试失败: {e}")
                    return None
        
        logger.error(f"达到最大重试次数 ({max_retries})，访问失败")
        return None
    
    def handle_verification(self, html_content, original_url):
        """处理验证页面 - 增强版"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找验证问题 - 扩展模式匹配
            question_patterns = [
                r'(\d+[×*]\d+)=？',
                r'(\d+[+]\d+)=？', 
                r'(\d+[-]\d+)=？',
                r'请回答：(\d+[×*+\-]\d+)=？',
                r'(\d+[×*+\-]\d+)等于多少',
                r'计算：(\d+[×*+\-]\d+)',
                r'(\d+[×*+\-]\d+)\s*=\s*\?',
                r'羽毛球有几根毛',
                r'ZYZX.*?怎么写',
                r'中羽.*?缩写',
            ]
            
            question = None
            question_type = 'unknown'
            
            # 从HTML文本中查找问题
            full_text = soup.get_text()
            
            for pattern in question_patterns:
                matches = re.findall(pattern, full_text, re.IGNORECASE)
                if matches:
                    question = matches[0]
                    if '羽毛球' in pattern:
                        question_type = 'feather'
                    elif 'ZYZX' in pattern:
                        question_type = 'zyzx'
                    elif '中羽' in pattern:
                        question_type = 'abbreviation'
                    else:
                        question_type = 'math'
                    break
            
            # 如果没找到，尝试从表单标签中查找
            if not question:
                form_texts = soup.find_all(text=True)
                for text in form_texts:
                    text = text.strip()
                    for pattern in question_patterns:
                        matches = re.findall(pattern, text, re.IGNORECASE)
                        if matches:
                            question = matches[0]
                            question_type = 'math' if any(op in text for op in ['×', '*', '+', '-']) else 'other'
                            break
                    if question:
                        break
            
            if not question:
                logger.error("未找到验证问题")
                logger.info(f"页面内容片段: {full_text[:500]}...")
                return None
                
            logger.info(f"识别到验证问题: {question} (类型: {question_type})")
            
            # 获取答案
            answer = self.ask_ai_for_answer(question)
            
            # 查找表单和输入字段
            form = soup.find('form')
            if not form:
                # 尝试查找任何可能的提交表单
                forms = soup.find_all('form')
                if forms:
                    form = forms[0]
                else:
                    logger.error("未找到验证表单")
                    return None
                
            form_data = {}
            
            # 收集所有隐藏字段
            hidden_inputs = form.find_all('input', {'type': 'hidden'})
            for inp in hidden_inputs:
                name = inp.get('name')
                value = inp.get('value', '')
                if name:
                    form_data[name] = value
                    logger.debug(f"隐藏字段: {name} = {value}")
            
            # 查找答案输入字段
            answer_fields = ['answer', 'verify', 'code', 'result', 'a', 'verification']
            answer_field = None
            
            for field in answer_fields:
                if field in form_data or form.find('input', {'name': field}):
                    answer_field = field
                    break
            
            # 如果没找到标准字段，查找所有text类型的输入
            if not answer_field:
                text_inputs = form.find_all('input', {'type': ['text', None]})
                for inp in text_inputs:
                    name = inp.get('name')
                    if name and name not in form_data:
                        answer_field = name
                        break
            
            if answer_field:
                form_data[answer_field] = answer
                logger.info(f"设置答案字段 {answer_field} = {answer}")
            else:
                # 尝试常见的字段名
                form_data['a'] = answer
                logger.info(f"使用默认字段名 'a' = {answer}")
            
            # 添加提交按钮数据
            submit_button = form.find('input', {'type': 'submit'})
            if submit_button:
                submit_name = submit_button.get('name')
                submit_value = submit_button.get('value', '提交')
                if submit_name:
                    form_data[submit_name] = submit_value
            else:
                form_data['submit'] = '提交'
            
            # 确保referer字段
            if 'referer' not in form_data:
                form_data['referer'] = original_url
            
            logger.info(f"表单数据: {form_data}")
            
            # 确定提交URL
            submit_url = form.get('action')
            if not submit_url:
                # 如果没有action，使用当前页面URL
                submit_url = original_url
            elif not submit_url.startswith('http'):
                # 相对URL转绝对URL
                if submit_url.startswith('/'):
                    submit_url = 'https://www.badmintoncn.com' + submit_url
                else:
                    submit_url = 'https://www.badmintoncn.com/' + submit_url.lstrip('/')
            
            logger.info(f"提交到: {submit_url}")
            
            # 设置提交头部
            headers = {
                'Referer': original_url,
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://www.badmintoncn.com',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            }
            
            # 提交验证
            response = self.session.post(submit_url, data=form_data, headers=headers, timeout=15)
            
            logger.info(f"验证提交响应: 状态码 {response.status_code}")
            
            if response.status_code == 200:
                # 检查响应是否还包含验证内容
                if "验证" in response.text or "请回答" in response.text:
                    logger.warning("验证可能失败，响应仍包含验证内容")
                    return None
                else:
                    logger.info("✅ 验证提交成功")
                    return response.text
            else:
                logger.error(f"验证提交失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"验证处理失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None
    
    def get_equipment_list(self):
        """获取装备列表"""
        try:
            list_url = "https://www.badmintoncn.com/cbo_eq/list.php"
            
            logger.info(f"访问: {list_url}")
            html_content = self.bypass_verification(list_url)
            
            if not html_content:
                logger.error("无法访问装备列表页面")
                return []
            
            soup = BeautifulSoup(html_content, 'html.parser')
            equipment_links = []
            
            # 查找装备链接
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and 'view.php?eid=' in href:
                    if not href.startswith('http'):
                        href = 'https://www.badmintoncn.com/' + href.lstrip('/')
                    equipment_links.append(href)
            
            # 去重
            equipment_links = list(set(equipment_links))
            logger.info(f"找到 {len(equipment_links)} 个装备链接")
            
            return equipment_links
            
        except Exception as e:
            logger.error(f"获取装备列表失败: {e}")
            return []
    
    def parse_equipment_detail(self, url, max_retries=2):
        """解析装备详细信息 - 增强版带重试"""
        for attempt in range(max_retries):
            try:
                logger.info(f"解析装备详情 (尝试 {attempt + 1}/{max_retries}): {url}")
                
                html_content = self.bypass_verification(url)
                if not html_content:
                    logger.warning(f"获取页面内容失败 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue
                    else:
                        return None
                    
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 验证页面是否正确加载（检查是否有装备相关内容）
                page_text = soup.get_text().lower()
                equipment_indicators = ['装备', '羽毛球', '品牌', '型号', '价格', '评分', '球拍', '球鞋']
                
                if not any(indicator in page_text for indicator in equipment_indicators):
                    logger.warning(f"页面内容异常，可能不是装备详情页 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                
                # 提取装备ID
                eid_match = re.search(r'eid=(\d+)', url)
                equipment_id = eid_match.group(1) if eid_match else 'unknown'
                
                # 基础信息
                title = soup.title.string if soup.title else ""
                equipment_name = self.extract_equipment_name(title)
                
                # 初始化完整数据结构
                equipment_data = {
                    'equipment_id': equipment_id,
                    'equipment_name': equipment_name,
                    'equipment_type': '',
                    'brand': '',
                    'series': '',
                    'description': '',
                    'release_date': '',
                    'introduction': '',
                    'specifications': '',
                    'frame_material': '',
                    'shaft_material': '',
                    'weight': '',
                    'length': '',
                    'grip_size': '',
                    'shaft_stiffness': '',
                    'string_tension': '',
                    'balance_point': '',
                    'purchase_price': '',
                    'new_avg_price': '',
                    'used_avg_price': '',
                    'total_users': '',
                    'review_count': '',
                    'pro_players': '',
                    'image_url': '',
                    'detail_url': url,
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # 解析表格数据
                tables = soup.find_all('table')
                logger.info(f"找到 {len(tables)} 个表格")
                
                if len(tables) == 0:
                    logger.warning("未找到表格数据，可能页面加载不完整")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                
                # 解析所有相关信息
                self.parse_comprehensive_equipment_info(soup, tables, html_content, equipment_data)
                
                # 验证数据完整性
                essential_fields = ['equipment_name', 'brand', 'equipment_type']
                missing_fields = [field for field in essential_fields if not equipment_data.get(field)]
                
                if missing_fields:
                    logger.warning(f"关键字段缺失: {missing_fields} (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                
                logger.info(f"✅ 解析完成: {equipment_data['equipment_name']} - {equipment_data['brand']}")
                return equipment_data
                
            except Exception as e:
                logger.error(f"解析装备详情失败 (尝试 {attempt + 1}) {url}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue
                else:
                    logger.error(f"所有解析尝试失败: {e}")
                    return None
        
        return None

    def parse_comprehensive_equipment_info(self, soup, tables, html_content, equipment_data):
        """全面解析装备信息"""
        try:
            # 解析基本参数表格
            self.parse_basic_parameters(tables, equipment_data)
            
            # 解析详细规格
            self.parse_detailed_specifications(tables, equipment_data)
            
            # 解析材质信息
            self.parse_material_info(tables, html_content, equipment_data)
            
            # 解析价格信息
            self.parse_comprehensive_price_info(tables, html_content, equipment_data)
            
            # 解析用户和评价信息
            self.parse_user_review_info(tables, html_content, equipment_data)
            
            # 解析职业球员信息
            self.parse_pro_player_info(html_content, equipment_data)
            
            # 提取图片URL
            self.extract_image_url(soup, equipment_data)
            
            # 提取描述和介绍
            self.extract_description_and_intro(html_content, equipment_data)
            
        except Exception as e:
            logger.error(f"全面解析装备信息失败: {e}")

    def parse_basic_parameters(self, tables, equipment_data):
        """解析基本参数"""
        try:
            for table_idx, table in enumerate(tables):
                rows = table.find_all('tr')
                
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        # 基本信息映射
                        if key in ['装备类型', '类型']:
                            equipment_data['equipment_type'] = value
                        elif key in ['装备品牌', '品牌']:
                            equipment_data['brand'] = value
                        elif key in ['装备系列', '系列']:
                            equipment_data['series'] = value
                        elif key in ['上市日期', '发布日期', '上市时间']:
                            equipment_data['release_date'] = value
                        elif key in ['重量', '拍重', '球拍重量']:
                            equipment_data['weight'] = value
                        elif key in ['长度', '球拍长度']:
                            equipment_data['length'] = value
                        elif key in ['手柄尺寸', '握把尺寸', '柄围']:
                            equipment_data['grip_size'] = value
                        elif key in ['中管弹性', '硬度', '中杆硬度']:
                            equipment_data['shaft_stiffness'] = value
                        elif key in ['穿线磅数', '拉线磅数', '建议磅数']:
                            equipment_data['string_tension'] = value
                        elif key in ['平衡点', '重心']:
                            equipment_data['balance_point'] = value
                            
        except Exception as e:
            logger.error(f"解析基本参数失败: {e}")

    def parse_detailed_specifications(self, tables, equipment_data):
        """解析详细规格信息"""
        try:
            specifications = []
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        # 收集详细规格
                        if key and value and key not in ['', ' ']:
                            specifications.append(f"{key}: {value}")
            
            equipment_data['specifications'] = '; '.join(specifications) if specifications else ''
            
        except Exception as e:
            logger.error(f"解析详细规格失败: {e}")

    def parse_material_info(self, tables, html_content, equipment_data):
        """解析材质信息"""
        try:
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if key in ['拍框材质', '球拍框材质', '框架材质']:
                            equipment_data['frame_material'] = value
                        elif key in ['中管材质', '杆身材质', '拍杆材质']:
                            equipment_data['shaft_material'] = value
                            
            # 如果表格中没有，尝试从文本中提取
            if not equipment_data['frame_material']:
                frame_patterns = [
                    r'拍框[：:]\s*([^，\n]+)',
                    r'框架材质[：:]\s*([^，\n]+)',
                    r'Frame[：:]\s*([^，\n]+)',
                ]
                for pattern in frame_patterns:
                    match = re.search(pattern, html_content)
                    if match:
                        equipment_data['frame_material'] = match.group(1).strip()
                        break
                        
            if not equipment_data['shaft_material']:
                shaft_patterns = [
                    r'中管[：:]\s*([^，\n]+)',
                    r'杆身材质[：:]\s*([^，\n]+)',
                    r'Shaft[：:]\s*([^，\n]+)',
                ]
                for pattern in shaft_patterns:
                    match = re.search(pattern, html_content)
                    if match:
                        equipment_data['shaft_material'] = match.group(1).strip()
                        break
                        
        except Exception as e:
            logger.error(f"解析材质信息失败: {e}")

    def parse_comprehensive_price_info(self, tables, html_content, equipment_data):
        """解析全面的价格信息"""
        try:
            # 从表格中提取价格
            shop_prices = []
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    for cell in cells:
                        text = cell.get_text(strip=True)
                        # 查找价格模式
                        price_matches = re.findall(r'[¥￥](\d+)', text)
                        if price_matches:
                            shop_prices.extend([int(p) for p in price_matches])
            
            # 设置价格信息
            if shop_prices:
                equipment_data['new_avg_price'] = str(int(sum(shop_prices) / len(shop_prices)))
                equipment_data['purchase_price'] = str(min(shop_prices))
            
            # 从文本中提取更多价格信息
            price_patterns = {
                'purchase_price': [r'最低价[：:]\s*[¥￥]?(\d+)', r'商店最低[：:]\s*[¥￥]?(\d+)'],
                'new_avg_price': [r'平均价[：:]\s*[¥￥]?(\d+)', r'新球拍价[：:]\s*[¥￥]?(\d+)'],
                'used_avg_price': [r'二手价[：:]\s*[¥￥]?(\d+)', r'转让价[：:]\s*[¥￥]?(\d+)'],
            }
            
            for price_type, patterns in price_patterns.items():
                if not equipment_data[price_type]:
                    for pattern in patterns:
                        match = re.search(pattern, html_content)
                        if match:
                            equipment_data[price_type] = match.group(1)
                            break
                            
        except Exception as e:
            logger.error(f"解析价格信息失败: {e}")

    def parse_user_review_info(self, tables, html_content, equipment_data):
        """解析用户和评价信息"""
        try:
            # 从表格中查找评价信息
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    text = row.get_text(strip=True)
                    
                    # 查找评价数量
                    review_patterns = [
                        r'评价[：:\s]*\((\d+)\)',
                        r'评论[：:\s]*(\d+)',
                        r'(\d+)\s*条评价',
                        r'(\d+)\s*人评价',
                    ]
                    
                    for pattern in review_patterns:
                        match = re.search(pattern, text)
                        if match:
                            equipment_data['review_count'] = match.group(1)
                            break
                    
                    # 查找用户数量
                    user_patterns = [
                        r'(\d+)\s*人使用',
                        r'(\d+)\s*位用户',
                        r'使用人数[：:\s]*(\d+)',
                        r'拥有者[：:\s]*(\d+)',
                    ]
                    
                    for pattern in user_patterns:
                        match = re.search(pattern, text)
                        if match:
                            equipment_data['total_users'] = match.group(1)
                            break
            
            # 从文本中查找更多信息
            if not equipment_data['total_users']:
                user_text_patterns = [
                    r'有(\d+)人使用',
                    r'(\d+)位球友使用',
                    r'共(\d+)人拥有',
                ]
                
                for pattern in user_text_patterns:
                    match = re.search(pattern, html_content)
                    if match:
                        equipment_data['total_users'] = match.group(1)
                        break
                        
        except Exception as e:
            logger.error(f"解析用户评价信息失败: {e}")

    def parse_pro_player_info(self, html_content, equipment_data):
        """解析职业球员信息"""
        try:
            pro_patterns = [
                r'使用球员[：:]\s*([^，\n]+)',
                r'职业球员[：:]\s*([^，\n]+)',
                r'代言球员[：:]\s*([^，\n]+)',
                r'明星球员[：:]\s*([^，\n]+)',
            ]
            
            for pattern in pro_patterns:
                match = re.search(pattern, html_content)
                if match:
                    equipment_data['pro_players'] = match.group(1).strip()
                    break
                    
        except Exception as e:
            logger.error(f"解析职业球员信息失败: {e}")

    def extract_image_url(self, soup, equipment_data):
        """提取图片URL"""
        try:
            # 查找主要产品图片
            img_selectors = [
                'img[src*="equipment"]',
                'img[src*="eq_"]',
                'img[alt*="羽毛球"]',
                'img[src*="jpg"]',
                'img[src*="png"]',
            ]
            
            for selector in img_selectors:
                imgs = soup.select(selector)
                if imgs:
                    src = imgs[0].get('src')
                    if src:
                        if not src.startswith('http'):
                            src = 'https://www.badmintoncn.com/' + src.lstrip('/')
                        equipment_data['image_url'] = src
                        break
                        
        except Exception as e:
            logger.error(f"提取图片URL失败: {e}")

    def extract_description_and_intro(self, html_content, equipment_data):
        """提取描述和介绍信息"""
        try:
            # 提取产品介绍
            intro_patterns = [
                r'产品介绍[：:](.+?)(?:\n|$)',
                r'装备介绍[：:](.+?)(?:\n|$)',
                r'产品特点[：:](.+?)(?:\n|$)',
                r'球拍介绍[：:](.+?)(?:\n|$)',
            ]
            
            for pattern in intro_patterns:
                match = re.search(pattern, html_content, re.DOTALL)
                if match:
                    intro = re.sub(r'<[^>]+>', '', match.group(1)).strip()
                    equipment_data['introduction'] = intro[:200] if len(intro) > 200 else intro
                    break
            
            # 提取产品描述
            desc_patterns = [
                r'产品描述[：:](.+?)(?:\n|$)',
                r'详细描述[：:](.+?)(?:\n|$)',
                r'产品说明[：:](.+?)(?:\n|$)',
            ]
            
            for pattern in desc_patterns:
                match = re.search(pattern, html_content, re.DOTALL)
                if match:
                    desc = re.sub(r'<[^>]+>', '', match.group(1)).strip()
                    equipment_data['description'] = desc[:300] if len(desc) > 300 else desc
                    break
                    
        except Exception as e:
            logger.error(f"提取描述信息失败: {e}")

    def extract_equipment_name(self, title):
        """从标题中提取装备名称"""
        if not title:
            return "未知装备"
            
        # 移除网站名称等无关信息
        name = title.replace('中羽在线 badmintoncn.com', '').strip()
        name = re.sub(r'\s+', ' ', name)  # 合并多个空格
        
        # 取前50个字符作为名称
        return name[:50] if len(name) > 50 else name
    
    def crawl_equipment_data(self, max_items=20):
        """爬取装备数据"""
        logger.info(f"🚀 开始爬取装备数据，最大数量: {max_items}")
        
        equipment_links = self.get_equipment_list()
        if not equipment_links:
            logger.error("未获取到装备链接")
            return []
        
        crawled_data = []
        for i, url in enumerate(equipment_links[:max_items]):
            logger.info(f"\n📦 正在爬取 ({i+1}/{min(len(equipment_links), max_items)}): {url}")
            
            equipment_data = self.parse_equipment_detail(url)
            if equipment_data:
                crawled_data.append(equipment_data)
                logger.info(f"✅ 成功解析: {equipment_data['equipment_name']}")
            else:
                logger.warning(f"❌ 解析失败: {url}")
            
            # 添加延迟避免过于频繁的请求
            if i < len(equipment_links) - 1:
                time.sleep(2)
        
        logger.info(f"\n🎉 爬取完成！成功获取 {len(crawled_data)} 条装备数据")
        return crawled_data
    
    def save_to_csv(self, data, filename):
        """保存数据到CSV文件 - 完整字段版本"""
        if not data:
            return
            
        fieldnames = [
            'equipment_id', 'equipment_name', 'equipment_type', 'brand', 'series',
            'description', 'release_date', 'introduction', 'specifications',
            'frame_material', 'shaft_material', 'weight', 'length', 'grip_size',
            'shaft_stiffness', 'string_tension', 'balance_point', 'purchase_price',
            'new_avg_price', 'used_avg_price', 'total_users', 'review_count',
            'pro_players', 'image_url', 'detail_url', 'crawl_time'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
            
        logger.info(f"📁 CSV数据已保存到: {filename}")
    
    def save_to_json(self, data, filename):
        """保存数据到JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"📁 JSON数据已保存到: {filename}")
    
    def analyze_data(self, data):
        """分析爬取的数据"""
        if not data:
            return
            
        logger.info("\n📊 数据分析报告:")
        logger.info(f"总装备数量: {len(data)}")
        
        # 品牌分布
        brands = {}
        types = {}
        prices = []
        
        for item in data:
            brand = item.get('brand', '未知')
            if brand:
                brands[brand] = brands.get(brand, 0) + 1
                
            item_type = item.get('equipment_type', '未知')
            if item_type:
                types[item_type] = types.get(item_type, 0) + 1
                
            # 收集价格信息
            for price_field in ['purchase_price', 'new_avg_price', 'used_avg_price']:
                price_str = item.get(price_field, '')
                if price_str and price_str.isdigit():
                    prices.append(int(price_str))
        
        # 输出品牌分布
        if brands:
            logger.info("品牌分布:")
            for brand, count in sorted(brands.items(), key=lambda x: x[1], reverse=True):
                logger.info(f"  {brand}: {count}")
        
        # 输出类型分布
        if types:
            logger.info("类型分布:")
            for item_type, count in sorted(types.items(), key=lambda x: x[1], reverse=True):
                logger.info(f"  {item_type}: {count}")
        
        # 价格分析
        if prices:
            logger.info(f"价格分析:")
            logger.info(f"  最低价: ¥{min(prices)}")
            logger.info(f"  最高价: ¥{max(prices)}")
            logger.info(f"  平均价: ¥{sum(prices)//len(prices)}")

def main():
    """主函数"""
    crawler = OptimizedAICrawler()
    
    # 爬取数据 - 小批量测试
    data = crawler.crawl_equipment_data(max_items=5)
    
    if data:
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        csv_filename = f"output/optimized_equipment_{timestamp}.csv"
        json_filename = f"output/optimized_equipment_{timestamp}.json"
        
        # 保存数据
        crawler.save_to_csv(data, csv_filename)
        crawler.save_to_json(data, json_filename)
        
        # 分析数据
        crawler.analyze_data(data)
        
        logger.info(f"\n🎯 爬取任务完成！")
        logger.info(f"📄 CSV文件: {csv_filename}")
        logger.info(f"📄 JSON文件: {json_filename}")
    else:
        logger.error("未能获取任何数据")

if __name__ == "__main__":
    main() 