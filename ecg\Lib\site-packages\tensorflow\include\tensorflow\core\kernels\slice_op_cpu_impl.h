/* Copyright 2016 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_CORE_KERNELS_SLICE_OP_CPU_IMPL_H_
#define TENSORFLOW_CORE_KERNELS_SLICE_OP_CPU_IMPL_H_

#define EIGEN_USE_THREADS

#include "tensorflow/core/framework/bfloat16.h"
#include "tensorflow/core/framework/register_types.h"
#include "tensorflow/core/kernels/slice_op.h"

namespace tensorflow {

using CpuDevice = Eigen::ThreadPoolDevice;

#define DEFINE_CPU_KERNELS(T) \
  template struct functor::Slice<CpuDevice, T, CPU_PROVIDED_IXDIM>;

TF_CALL_ALL_TYPES(DEFINE_CPU_KERNELS);

#undef DEFINE_CPU_KERNELS


}  // namespace tensorflow

#endif  // TENSORFLOW_CORE_KERNELS_SLICE_OP_CPU_IMPL_H_
