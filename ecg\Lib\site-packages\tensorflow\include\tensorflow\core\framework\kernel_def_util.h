/* Copyright 2018 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_CORE_FRAMEWORK_KERNEL_DEF_UTIL_H_
#define TENSORFLOW_CORE_FRAMEWORK_KERNEL_DEF_UTIL_H_

#include "tensorflow/core/framework/kernel_def.pb.h"
#include "tensorflow/core/framework/node_def_util.h"

namespace tensorflow {

// Returns whether the attrs satisfy the constraints in the kernel_def. Returns
// an error if attrs in kernel_def are not found, or have a mismatching type.
Status KernelAttrsMatch(const KernelDef& kernel_def, AttrSlice attrs,
                        bool* match);

}  // namespace tensorflow

#endif  // TENSORFLOW_CORE_FRAMEWORK_KERNEL_DEF_UTIL_H_
