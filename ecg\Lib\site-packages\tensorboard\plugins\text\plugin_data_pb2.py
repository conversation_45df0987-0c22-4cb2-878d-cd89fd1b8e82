# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/text/plugin_data.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*tensorboard/plugins/text/plugin_data.proto\x12\x0btensorboard\"!\n\x0eTextPluginData\x12\x0f\n\x07version\x18\x01 \x01(\x05\x62\x06proto3')



_TEXTPLUGINDATA = DESCRIPTOR.message_types_by_name['TextPluginData']
TextPluginData = _reflection.GeneratedProtocolMessageType('TextPluginData', (_message.Message,), {
  'DESCRIPTOR' : _TEXTPLUGINDATA,
  '__module__' : 'tensorboard.plugins.text.plugin_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.TextPluginData)
  })
_sym_db.RegisterMessage(TextPluginData)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _TEXTPLUGINDATA._serialized_start=59
  _TEXTPLUGINDATA._serialized_end=92
# @@protoc_insertion_point(module_scope)
