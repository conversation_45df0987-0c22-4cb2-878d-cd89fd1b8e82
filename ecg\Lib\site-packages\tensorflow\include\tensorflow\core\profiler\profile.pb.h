// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/profile.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprofile_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprofile_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprofiler_2fprofile_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprofiler_2fprofile_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[8]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto;
namespace tensorflow {
namespace tfprof {
namespace pprof {
class Function;
class FunctionDefaultTypeInternal;
extern FunctionDefaultTypeInternal _Function_default_instance_;
class Label;
class LabelDefaultTypeInternal;
extern LabelDefaultTypeInternal _Label_default_instance_;
class Line;
class LineDefaultTypeInternal;
extern LineDefaultTypeInternal _Line_default_instance_;
class Location;
class LocationDefaultTypeInternal;
extern LocationDefaultTypeInternal _Location_default_instance_;
class Mapping;
class MappingDefaultTypeInternal;
extern MappingDefaultTypeInternal _Mapping_default_instance_;
class Profile;
class ProfileDefaultTypeInternal;
extern ProfileDefaultTypeInternal _Profile_default_instance_;
class Sample;
class SampleDefaultTypeInternal;
extern SampleDefaultTypeInternal _Sample_default_instance_;
class ValueType;
class ValueTypeDefaultTypeInternal;
extern ValueTypeDefaultTypeInternal _ValueType_default_instance_;
}  // namespace pprof
}  // namespace tfprof
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tfprof::pprof::Function* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Function>(Arena*);
template<> ::tensorflow::tfprof::pprof::Label* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Label>(Arena*);
template<> ::tensorflow::tfprof::pprof::Line* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Line>(Arena*);
template<> ::tensorflow::tfprof::pprof::Location* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Location>(Arena*);
template<> ::tensorflow::tfprof::pprof::Mapping* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Mapping>(Arena*);
template<> ::tensorflow::tfprof::pprof::Profile* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Profile>(Arena*);
template<> ::tensorflow::tfprof::pprof::Sample* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Sample>(Arena*);
template<> ::tensorflow::tfprof::pprof::ValueType* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::ValueType>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tfprof {
namespace pprof {

// ===================================================================

class Profile :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Profile) */ {
 public:
  Profile();
  virtual ~Profile();

  Profile(const Profile& from);
  Profile(Profile&& from) noexcept
    : Profile() {
    *this = ::std::move(from);
  }

  inline Profile& operator=(const Profile& from) {
    CopyFrom(from);
    return *this;
  }
  inline Profile& operator=(Profile&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Profile& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Profile* internal_default_instance() {
    return reinterpret_cast<const Profile*>(
               &_Profile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Profile& a, Profile& b) {
    a.Swap(&b);
  }
  inline void Swap(Profile* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Profile* New() const final {
    return CreateMaybeMessage<Profile>(nullptr);
  }

  Profile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Profile>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Profile& from);
  void MergeFrom(const Profile& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Profile* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Profile";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSampleTypeFieldNumber = 1,
    kSampleFieldNumber = 2,
    kMappingFieldNumber = 3,
    kLocationFieldNumber = 4,
    kFunctionFieldNumber = 5,
    kStringTableFieldNumber = 6,
    kCommentFieldNumber = 13,
    kPeriodTypeFieldNumber = 11,
    kDropFramesFieldNumber = 7,
    kKeepFramesFieldNumber = 8,
    kTimeNanosFieldNumber = 9,
    kDurationNanosFieldNumber = 10,
    kPeriodFieldNumber = 12,
    kDefaultSampleTypeFieldNumber = 14,
  };
  // repeated .tensorflow.tfprof.pprof.ValueType sample_type = 1;
  int sample_type_size() const;
  void clear_sample_type();
  ::tensorflow::tfprof::pprof::ValueType* mutable_sample_type(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::ValueType >*
      mutable_sample_type();
  const ::tensorflow::tfprof::pprof::ValueType& sample_type(int index) const;
  ::tensorflow::tfprof::pprof::ValueType* add_sample_type();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::ValueType >&
      sample_type() const;

  // repeated .tensorflow.tfprof.pprof.Sample sample = 2;
  int sample_size() const;
  void clear_sample();
  ::tensorflow::tfprof::pprof::Sample* mutable_sample(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Sample >*
      mutable_sample();
  const ::tensorflow::tfprof::pprof::Sample& sample(int index) const;
  ::tensorflow::tfprof::pprof::Sample* add_sample();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Sample >&
      sample() const;

  // repeated .tensorflow.tfprof.pprof.Mapping mapping = 3;
  int mapping_size() const;
  void clear_mapping();
  ::tensorflow::tfprof::pprof::Mapping* mutable_mapping(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Mapping >*
      mutable_mapping();
  const ::tensorflow::tfprof::pprof::Mapping& mapping(int index) const;
  ::tensorflow::tfprof::pprof::Mapping* add_mapping();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Mapping >&
      mapping() const;

  // repeated .tensorflow.tfprof.pprof.Location location = 4;
  int location_size() const;
  void clear_location();
  ::tensorflow::tfprof::pprof::Location* mutable_location(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Location >*
      mutable_location();
  const ::tensorflow::tfprof::pprof::Location& location(int index) const;
  ::tensorflow::tfprof::pprof::Location* add_location();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Location >&
      location() const;

  // repeated .tensorflow.tfprof.pprof.Function function = 5;
  int function_size() const;
  void clear_function();
  ::tensorflow::tfprof::pprof::Function* mutable_function(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Function >*
      mutable_function();
  const ::tensorflow::tfprof::pprof::Function& function(int index) const;
  ::tensorflow::tfprof::pprof::Function* add_function();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Function >&
      function() const;

  // repeated string string_table = 6;
  int string_table_size() const;
  void clear_string_table();
  const std::string& string_table(int index) const;
  std::string* mutable_string_table(int index);
  void set_string_table(int index, const std::string& value);
  void set_string_table(int index, std::string&& value);
  void set_string_table(int index, const char* value);
  void set_string_table(int index, const char* value, size_t size);
  std::string* add_string_table();
  void add_string_table(const std::string& value);
  void add_string_table(std::string&& value);
  void add_string_table(const char* value);
  void add_string_table(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& string_table() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_string_table();

  // repeated int64 comment = 13;
  int comment_size() const;
  void clear_comment();
  ::PROTOBUF_NAMESPACE_ID::int64 comment(int index) const;
  void set_comment(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_comment(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      comment() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_comment();

  // .tensorflow.tfprof.pprof.ValueType period_type = 11;
  bool has_period_type() const;
  void clear_period_type();
  const ::tensorflow::tfprof::pprof::ValueType& period_type() const;
  ::tensorflow::tfprof::pprof::ValueType* release_period_type();
  ::tensorflow::tfprof::pprof::ValueType* mutable_period_type();
  void set_allocated_period_type(::tensorflow::tfprof::pprof::ValueType* period_type);

  // int64 drop_frames = 7;
  void clear_drop_frames();
  ::PROTOBUF_NAMESPACE_ID::int64 drop_frames() const;
  void set_drop_frames(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 keep_frames = 8;
  void clear_keep_frames();
  ::PROTOBUF_NAMESPACE_ID::int64 keep_frames() const;
  void set_keep_frames(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 time_nanos = 9;
  void clear_time_nanos();
  ::PROTOBUF_NAMESPACE_ID::int64 time_nanos() const;
  void set_time_nanos(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 duration_nanos = 10;
  void clear_duration_nanos();
  ::PROTOBUF_NAMESPACE_ID::int64 duration_nanos() const;
  void set_duration_nanos(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 period = 12;
  void clear_period();
  ::PROTOBUF_NAMESPACE_ID::int64 period() const;
  void set_period(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 default_sample_type = 14;
  void clear_default_sample_type();
  ::PROTOBUF_NAMESPACE_ID::int64 default_sample_type() const;
  void set_default_sample_type(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Profile)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::ValueType > sample_type_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Sample > sample_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Mapping > mapping_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Location > location_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Function > function_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> string_table_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > comment_;
  mutable std::atomic<int> _comment_cached_byte_size_;
  ::tensorflow::tfprof::pprof::ValueType* period_type_;
  ::PROTOBUF_NAMESPACE_ID::int64 drop_frames_;
  ::PROTOBUF_NAMESPACE_ID::int64 keep_frames_;
  ::PROTOBUF_NAMESPACE_ID::int64 time_nanos_;
  ::PROTOBUF_NAMESPACE_ID::int64 duration_nanos_;
  ::PROTOBUF_NAMESPACE_ID::int64 period_;
  ::PROTOBUF_NAMESPACE_ID::int64 default_sample_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class ValueType :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.ValueType) */ {
 public:
  ValueType();
  virtual ~ValueType();

  ValueType(const ValueType& from);
  ValueType(ValueType&& from) noexcept
    : ValueType() {
    *this = ::std::move(from);
  }

  inline ValueType& operator=(const ValueType& from) {
    CopyFrom(from);
    return *this;
  }
  inline ValueType& operator=(ValueType&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ValueType& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ValueType* internal_default_instance() {
    return reinterpret_cast<const ValueType*>(
               &_ValueType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ValueType& a, ValueType& b) {
    a.Swap(&b);
  }
  inline void Swap(ValueType* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ValueType* New() const final {
    return CreateMaybeMessage<ValueType>(nullptr);
  }

  ValueType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ValueType>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ValueType& from);
  void MergeFrom(const ValueType& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ValueType* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.ValueType";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypeFieldNumber = 1,
    kUnitFieldNumber = 2,
  };
  // int64 type = 1;
  void clear_type();
  ::PROTOBUF_NAMESPACE_ID::int64 type() const;
  void set_type(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 unit = 2;
  void clear_unit();
  ::PROTOBUF_NAMESPACE_ID::int64 unit() const;
  void set_unit(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.ValueType)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 type_;
  ::PROTOBUF_NAMESPACE_ID::int64 unit_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Sample :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Sample) */ {
 public:
  Sample();
  virtual ~Sample();

  Sample(const Sample& from);
  Sample(Sample&& from) noexcept
    : Sample() {
    *this = ::std::move(from);
  }

  inline Sample& operator=(const Sample& from) {
    CopyFrom(from);
    return *this;
  }
  inline Sample& operator=(Sample&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Sample& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Sample* internal_default_instance() {
    return reinterpret_cast<const Sample*>(
               &_Sample_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Sample& a, Sample& b) {
    a.Swap(&b);
  }
  inline void Swap(Sample* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Sample* New() const final {
    return CreateMaybeMessage<Sample>(nullptr);
  }

  Sample* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Sample>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Sample& from);
  void MergeFrom(const Sample& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Sample* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Sample";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocationIdFieldNumber = 1,
    kValueFieldNumber = 2,
    kLabelFieldNumber = 3,
  };
  // repeated uint64 location_id = 1;
  int location_id_size() const;
  void clear_location_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 location_id(int index) const;
  void set_location_id(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value);
  void add_location_id(::PROTOBUF_NAMESPACE_ID::uint64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
      location_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
      mutable_location_id();

  // repeated int64 value = 2;
  int value_size() const;
  void clear_value();
  ::PROTOBUF_NAMESPACE_ID::int64 value(int index) const;
  void set_value(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_value(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_value();

  // repeated .tensorflow.tfprof.pprof.Label label = 3;
  int label_size() const;
  void clear_label();
  ::tensorflow::tfprof::pprof::Label* mutable_label(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Label >*
      mutable_label();
  const ::tensorflow::tfprof::pprof::Label& label(int index) const;
  ::tensorflow::tfprof::pprof::Label* add_label();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Label >&
      label() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Sample)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 > location_id_;
  mutable std::atomic<int> _location_id_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > value_;
  mutable std::atomic<int> _value_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Label > label_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Label :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Label) */ {
 public:
  Label();
  virtual ~Label();

  Label(const Label& from);
  Label(Label&& from) noexcept
    : Label() {
    *this = ::std::move(from);
  }

  inline Label& operator=(const Label& from) {
    CopyFrom(from);
    return *this;
  }
  inline Label& operator=(Label&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Label& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Label* internal_default_instance() {
    return reinterpret_cast<const Label*>(
               &_Label_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Label& a, Label& b) {
    a.Swap(&b);
  }
  inline void Swap(Label* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Label* New() const final {
    return CreateMaybeMessage<Label>(nullptr);
  }

  Label* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Label>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Label& from);
  void MergeFrom(const Label& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Label* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Label";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kStrFieldNumber = 2,
    kNumFieldNumber = 3,
  };
  // int64 key = 1;
  void clear_key();
  ::PROTOBUF_NAMESPACE_ID::int64 key() const;
  void set_key(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 str = 2;
  void clear_str();
  ::PROTOBUF_NAMESPACE_ID::int64 str() const;
  void set_str(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 num = 3;
  void clear_num();
  ::PROTOBUF_NAMESPACE_ID::int64 num() const;
  void set_num(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Label)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 key_;
  ::PROTOBUF_NAMESPACE_ID::int64 str_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Mapping :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Mapping) */ {
 public:
  Mapping();
  virtual ~Mapping();

  Mapping(const Mapping& from);
  Mapping(Mapping&& from) noexcept
    : Mapping() {
    *this = ::std::move(from);
  }

  inline Mapping& operator=(const Mapping& from) {
    CopyFrom(from);
    return *this;
  }
  inline Mapping& operator=(Mapping&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Mapping& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Mapping* internal_default_instance() {
    return reinterpret_cast<const Mapping*>(
               &_Mapping_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Mapping& a, Mapping& b) {
    a.Swap(&b);
  }
  inline void Swap(Mapping* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Mapping* New() const final {
    return CreateMaybeMessage<Mapping>(nullptr);
  }

  Mapping* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Mapping>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Mapping& from);
  void MergeFrom(const Mapping& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Mapping* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Mapping";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kMemoryStartFieldNumber = 2,
    kMemoryLimitFieldNumber = 3,
    kFileOffsetFieldNumber = 4,
    kFilenameFieldNumber = 5,
    kBuildIdFieldNumber = 6,
    kHasFunctionsFieldNumber = 7,
    kHasFilenamesFieldNumber = 8,
    kHasLineNumbersFieldNumber = 9,
    kHasInlineFramesFieldNumber = 10,
  };
  // uint64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // uint64 memory_start = 2;
  void clear_memory_start();
  ::PROTOBUF_NAMESPACE_ID::uint64 memory_start() const;
  void set_memory_start(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // uint64 memory_limit = 3;
  void clear_memory_limit();
  ::PROTOBUF_NAMESPACE_ID::uint64 memory_limit() const;
  void set_memory_limit(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // uint64 file_offset = 4;
  void clear_file_offset();
  ::PROTOBUF_NAMESPACE_ID::uint64 file_offset() const;
  void set_file_offset(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int64 filename = 5;
  void clear_filename();
  ::PROTOBUF_NAMESPACE_ID::int64 filename() const;
  void set_filename(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 build_id = 6;
  void clear_build_id();
  ::PROTOBUF_NAMESPACE_ID::int64 build_id() const;
  void set_build_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool has_functions = 7;
  void clear_has_functions();
  bool has_functions() const;
  void set_has_functions(bool value);

  // bool has_filenames = 8;
  void clear_has_filenames();
  bool has_filenames() const;
  void set_has_filenames(bool value);

  // bool has_line_numbers = 9;
  void clear_has_line_numbers();
  bool has_line_numbers() const;
  void set_has_line_numbers(bool value);

  // bool has_inline_frames = 10;
  void clear_has_inline_frames();
  bool has_inline_frames() const;
  void set_has_inline_frames(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Mapping)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::uint64 id_;
  ::PROTOBUF_NAMESPACE_ID::uint64 memory_start_;
  ::PROTOBUF_NAMESPACE_ID::uint64 memory_limit_;
  ::PROTOBUF_NAMESPACE_ID::uint64 file_offset_;
  ::PROTOBUF_NAMESPACE_ID::int64 filename_;
  ::PROTOBUF_NAMESPACE_ID::int64 build_id_;
  bool has_functions_;
  bool has_filenames_;
  bool has_line_numbers_;
  bool has_inline_frames_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Location :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Location) */ {
 public:
  Location();
  virtual ~Location();

  Location(const Location& from);
  Location(Location&& from) noexcept
    : Location() {
    *this = ::std::move(from);
  }

  inline Location& operator=(const Location& from) {
    CopyFrom(from);
    return *this;
  }
  inline Location& operator=(Location&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Location& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Location* internal_default_instance() {
    return reinterpret_cast<const Location*>(
               &_Location_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Location& a, Location& b) {
    a.Swap(&b);
  }
  inline void Swap(Location* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Location* New() const final {
    return CreateMaybeMessage<Location>(nullptr);
  }

  Location* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Location>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Location& from);
  void MergeFrom(const Location& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Location* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Location";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLineFieldNumber = 4,
    kIdFieldNumber = 1,
    kMappingIdFieldNumber = 2,
    kAddressFieldNumber = 3,
  };
  // repeated .tensorflow.tfprof.pprof.Line line = 4;
  int line_size() const;
  void clear_line();
  ::tensorflow::tfprof::pprof::Line* mutable_line(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Line >*
      mutable_line();
  const ::tensorflow::tfprof::pprof::Line& line(int index) const;
  ::tensorflow::tfprof::pprof::Line* add_line();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Line >&
      line() const;

  // uint64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // uint64 mapping_id = 2;
  void clear_mapping_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 mapping_id() const;
  void set_mapping_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // uint64 address = 3;
  void clear_address();
  ::PROTOBUF_NAMESPACE_ID::uint64 address() const;
  void set_address(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Location)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Line > line_;
  ::PROTOBUF_NAMESPACE_ID::uint64 id_;
  ::PROTOBUF_NAMESPACE_ID::uint64 mapping_id_;
  ::PROTOBUF_NAMESPACE_ID::uint64 address_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Line :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Line) */ {
 public:
  Line();
  virtual ~Line();

  Line(const Line& from);
  Line(Line&& from) noexcept
    : Line() {
    *this = ::std::move(from);
  }

  inline Line& operator=(const Line& from) {
    CopyFrom(from);
    return *this;
  }
  inline Line& operator=(Line&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Line& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Line* internal_default_instance() {
    return reinterpret_cast<const Line*>(
               &_Line_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(Line& a, Line& b) {
    a.Swap(&b);
  }
  inline void Swap(Line* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Line* New() const final {
    return CreateMaybeMessage<Line>(nullptr);
  }

  Line* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Line>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Line& from);
  void MergeFrom(const Line& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Line* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Line";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionIdFieldNumber = 1,
    kLineFieldNumber = 2,
  };
  // uint64 function_id = 1;
  void clear_function_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 function_id() const;
  void set_function_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int64 line = 2;
  void clear_line();
  ::PROTOBUF_NAMESPACE_ID::int64 line() const;
  void set_line(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Line)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::uint64 function_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 line_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Function :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Function) */ {
 public:
  Function();
  virtual ~Function();

  Function(const Function& from);
  Function(Function&& from) noexcept
    : Function() {
    *this = ::std::move(from);
  }

  inline Function& operator=(const Function& from) {
    CopyFrom(from);
    return *this;
  }
  inline Function& operator=(Function&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Function& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Function* internal_default_instance() {
    return reinterpret_cast<const Function*>(
               &_Function_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(Function& a, Function& b) {
    a.Swap(&b);
  }
  inline void Swap(Function* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Function* New() const final {
    return CreateMaybeMessage<Function>(nullptr);
  }

  Function* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Function>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Function& from);
  void MergeFrom(const Function& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Function* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Function";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofile_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kNameFieldNumber = 2,
    kSystemNameFieldNumber = 3,
    kFilenameFieldNumber = 4,
    kStartLineFieldNumber = 5,
  };
  // uint64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int64 name = 2;
  void clear_name();
  ::PROTOBUF_NAMESPACE_ID::int64 name() const;
  void set_name(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 system_name = 3;
  void clear_system_name();
  ::PROTOBUF_NAMESPACE_ID::int64 system_name() const;
  void set_system_name(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 filename = 4;
  void clear_filename();
  ::PROTOBUF_NAMESPACE_ID::int64 filename() const;
  void set_filename(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 start_line = 5;
  void clear_start_line();
  ::PROTOBUF_NAMESPACE_ID::int64 start_line() const;
  void set_start_line(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Function)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::uint64 id_;
  ::PROTOBUF_NAMESPACE_ID::int64 name_;
  ::PROTOBUF_NAMESPACE_ID::int64 system_name_;
  ::PROTOBUF_NAMESPACE_ID::int64 filename_;
  ::PROTOBUF_NAMESPACE_ID::int64 start_line_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofile_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Profile

// repeated .tensorflow.tfprof.pprof.ValueType sample_type = 1;
inline int Profile::sample_type_size() const {
  return sample_type_.size();
}
inline void Profile::clear_sample_type() {
  sample_type_.Clear();
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::mutable_sample_type(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.sample_type)
  return sample_type_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::ValueType >*
Profile::mutable_sample_type() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.sample_type)
  return &sample_type_;
}
inline const ::tensorflow::tfprof::pprof::ValueType& Profile::sample_type(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.sample_type)
  return sample_type_.Get(index);
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::add_sample_type() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.sample_type)
  return sample_type_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::ValueType >&
Profile::sample_type() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.sample_type)
  return sample_type_;
}

// repeated .tensorflow.tfprof.pprof.Sample sample = 2;
inline int Profile::sample_size() const {
  return sample_.size();
}
inline void Profile::clear_sample() {
  sample_.Clear();
}
inline ::tensorflow::tfprof::pprof::Sample* Profile::mutable_sample(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.sample)
  return sample_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Sample >*
Profile::mutable_sample() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.sample)
  return &sample_;
}
inline const ::tensorflow::tfprof::pprof::Sample& Profile::sample(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.sample)
  return sample_.Get(index);
}
inline ::tensorflow::tfprof::pprof::Sample* Profile::add_sample() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.sample)
  return sample_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Sample >&
Profile::sample() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.sample)
  return sample_;
}

// repeated .tensorflow.tfprof.pprof.Mapping mapping = 3;
inline int Profile::mapping_size() const {
  return mapping_.size();
}
inline void Profile::clear_mapping() {
  mapping_.Clear();
}
inline ::tensorflow::tfprof::pprof::Mapping* Profile::mutable_mapping(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.mapping)
  return mapping_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Mapping >*
Profile::mutable_mapping() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.mapping)
  return &mapping_;
}
inline const ::tensorflow::tfprof::pprof::Mapping& Profile::mapping(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.mapping)
  return mapping_.Get(index);
}
inline ::tensorflow::tfprof::pprof::Mapping* Profile::add_mapping() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.mapping)
  return mapping_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Mapping >&
Profile::mapping() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.mapping)
  return mapping_;
}

// repeated .tensorflow.tfprof.pprof.Location location = 4;
inline int Profile::location_size() const {
  return location_.size();
}
inline void Profile::clear_location() {
  location_.Clear();
}
inline ::tensorflow::tfprof::pprof::Location* Profile::mutable_location(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.location)
  return location_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Location >*
Profile::mutable_location() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.location)
  return &location_;
}
inline const ::tensorflow::tfprof::pprof::Location& Profile::location(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.location)
  return location_.Get(index);
}
inline ::tensorflow::tfprof::pprof::Location* Profile::add_location() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.location)
  return location_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Location >&
Profile::location() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.location)
  return location_;
}

// repeated .tensorflow.tfprof.pprof.Function function = 5;
inline int Profile::function_size() const {
  return function_.size();
}
inline void Profile::clear_function() {
  function_.Clear();
}
inline ::tensorflow::tfprof::pprof::Function* Profile::mutable_function(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.function)
  return function_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Function >*
Profile::mutable_function() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.function)
  return &function_;
}
inline const ::tensorflow::tfprof::pprof::Function& Profile::function(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.function)
  return function_.Get(index);
}
inline ::tensorflow::tfprof::pprof::Function* Profile::add_function() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.function)
  return function_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Function >&
Profile::function() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.function)
  return function_;
}

// repeated string string_table = 6;
inline int Profile::string_table_size() const {
  return string_table_.size();
}
inline void Profile::clear_string_table() {
  string_table_.Clear();
}
inline const std::string& Profile::string_table(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.string_table)
  return string_table_.Get(index);
}
inline std::string* Profile::mutable_string_table(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.string_table)
  return string_table_.Mutable(index);
}
inline void Profile::set_string_table(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.string_table)
  string_table_.Mutable(index)->assign(value);
}
inline void Profile::set_string_table(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.string_table)
  string_table_.Mutable(index)->assign(std::move(value));
}
inline void Profile::set_string_table(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  string_table_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.pprof.Profile.string_table)
}
inline void Profile::set_string_table(int index, const char* value, size_t size) {
  string_table_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.pprof.Profile.string_table)
}
inline std::string* Profile::add_string_table() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.pprof.Profile.string_table)
  return string_table_.Add();
}
inline void Profile::add_string_table(const std::string& value) {
  string_table_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.string_table)
}
inline void Profile::add_string_table(std::string&& value) {
  string_table_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.string_table)
}
inline void Profile::add_string_table(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  string_table_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.pprof.Profile.string_table)
}
inline void Profile::add_string_table(const char* value, size_t size) {
  string_table_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.pprof.Profile.string_table)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Profile::string_table() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.string_table)
  return string_table_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Profile::mutable_string_table() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.string_table)
  return &string_table_;
}

// int64 drop_frames = 7;
inline void Profile::clear_drop_frames() {
  drop_frames_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Profile::drop_frames() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.drop_frames)
  return drop_frames_;
}
inline void Profile::set_drop_frames(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  drop_frames_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.drop_frames)
}

// int64 keep_frames = 8;
inline void Profile::clear_keep_frames() {
  keep_frames_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Profile::keep_frames() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.keep_frames)
  return keep_frames_;
}
inline void Profile::set_keep_frames(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  keep_frames_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.keep_frames)
}

// int64 time_nanos = 9;
inline void Profile::clear_time_nanos() {
  time_nanos_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Profile::time_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.time_nanos)
  return time_nanos_;
}
inline void Profile::set_time_nanos(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  time_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.time_nanos)
}

// int64 duration_nanos = 10;
inline void Profile::clear_duration_nanos() {
  duration_nanos_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Profile::duration_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.duration_nanos)
  return duration_nanos_;
}
inline void Profile::set_duration_nanos(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  duration_nanos_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.duration_nanos)
}

// .tensorflow.tfprof.pprof.ValueType period_type = 11;
inline bool Profile::has_period_type() const {
  return this != internal_default_instance() && period_type_ != nullptr;
}
inline void Profile::clear_period_type() {
  if (GetArenaNoVirtual() == nullptr && period_type_ != nullptr) {
    delete period_type_;
  }
  period_type_ = nullptr;
}
inline const ::tensorflow::tfprof::pprof::ValueType& Profile::period_type() const {
  const ::tensorflow::tfprof::pprof::ValueType* p = period_type_;
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.period_type)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tfprof::pprof::ValueType*>(
      &::tensorflow::tfprof::pprof::_ValueType_default_instance_);
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::release_period_type() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.pprof.Profile.period_type)
  
  ::tensorflow::tfprof::pprof::ValueType* temp = period_type_;
  period_type_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::mutable_period_type() {
  
  if (period_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::pprof::ValueType>(GetArenaNoVirtual());
    period_type_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.period_type)
  return period_type_;
}
inline void Profile::set_allocated_period_type(::tensorflow::tfprof::pprof::ValueType* period_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete period_type_;
  }
  if (period_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      period_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, period_type, submessage_arena);
    }
    
  } else {
    
  }
  period_type_ = period_type;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.pprof.Profile.period_type)
}

// int64 period = 12;
inline void Profile::clear_period() {
  period_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Profile::period() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.period)
  return period_;
}
inline void Profile::set_period(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  period_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.period)
}

// repeated int64 comment = 13;
inline int Profile::comment_size() const {
  return comment_.size();
}
inline void Profile::clear_comment() {
  comment_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Profile::comment(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.comment)
  return comment_.Get(index);
}
inline void Profile::set_comment(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  comment_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.comment)
}
inline void Profile::add_comment(::PROTOBUF_NAMESPACE_ID::int64 value) {
  comment_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.comment)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
Profile::comment() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.comment)
  return comment_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
Profile::mutable_comment() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.comment)
  return &comment_;
}

// int64 default_sample_type = 14;
inline void Profile::clear_default_sample_type() {
  default_sample_type_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Profile::default_sample_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.default_sample_type)
  return default_sample_type_;
}
inline void Profile::set_default_sample_type(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  default_sample_type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.default_sample_type)
}

// -------------------------------------------------------------------

// ValueType

// int64 type = 1;
inline void ValueType::clear_type() {
  type_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ValueType::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.ValueType.type)
  return type_;
}
inline void ValueType::set_type(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.ValueType.type)
}

// int64 unit = 2;
inline void ValueType::clear_unit() {
  unit_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ValueType::unit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.ValueType.unit)
  return unit_;
}
inline void ValueType::set_unit(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  unit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.ValueType.unit)
}

// -------------------------------------------------------------------

// Sample

// repeated uint64 location_id = 1;
inline int Sample::location_id_size() const {
  return location_id_.size();
}
inline void Sample::clear_location_id() {
  location_id_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Sample::location_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Sample.location_id)
  return location_id_.Get(index);
}
inline void Sample::set_location_id(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value) {
  location_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Sample.location_id)
}
inline void Sample::add_location_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  location_id_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Sample.location_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
Sample::location_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Sample.location_id)
  return location_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
Sample::mutable_location_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Sample.location_id)
  return &location_id_;
}

// repeated int64 value = 2;
inline int Sample::value_size() const {
  return value_.size();
}
inline void Sample::clear_value() {
  value_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Sample::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Sample.value)
  return value_.Get(index);
}
inline void Sample::set_value(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Sample.value)
}
inline void Sample::add_value(::PROTOBUF_NAMESPACE_ID::int64 value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Sample.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
Sample::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Sample.value)
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
Sample::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Sample.value)
  return &value_;
}

// repeated .tensorflow.tfprof.pprof.Label label = 3;
inline int Sample::label_size() const {
  return label_.size();
}
inline void Sample::clear_label() {
  label_.Clear();
}
inline ::tensorflow::tfprof::pprof::Label* Sample::mutable_label(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Sample.label)
  return label_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Label >*
Sample::mutable_label() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Sample.label)
  return &label_;
}
inline const ::tensorflow::tfprof::pprof::Label& Sample::label(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Sample.label)
  return label_.Get(index);
}
inline ::tensorflow::tfprof::pprof::Label* Sample::add_label() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Sample.label)
  return label_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Label >&
Sample::label() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Sample.label)
  return label_;
}

// -------------------------------------------------------------------

// Label

// int64 key = 1;
inline void Label::clear_key() {
  key_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Label::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Label.key)
  return key_;
}
inline void Label::set_key(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Label.key)
}

// int64 str = 2;
inline void Label::clear_str() {
  str_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Label::str() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Label.str)
  return str_;
}
inline void Label::set_str(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  str_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Label.str)
}

// int64 num = 3;
inline void Label::clear_num() {
  num_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Label::num() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Label.num)
  return num_;
}
inline void Label::set_num(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Label.num)
}

// -------------------------------------------------------------------

// Mapping

// uint64 id = 1;
inline void Mapping::clear_id() {
  id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Mapping::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.id)
  return id_;
}
inline void Mapping::set_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.id)
}

// uint64 memory_start = 2;
inline void Mapping::clear_memory_start() {
  memory_start_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Mapping::memory_start() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.memory_start)
  return memory_start_;
}
inline void Mapping::set_memory_start(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  memory_start_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.memory_start)
}

// uint64 memory_limit = 3;
inline void Mapping::clear_memory_limit() {
  memory_limit_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Mapping::memory_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.memory_limit)
  return memory_limit_;
}
inline void Mapping::set_memory_limit(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  memory_limit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.memory_limit)
}

// uint64 file_offset = 4;
inline void Mapping::clear_file_offset() {
  file_offset_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Mapping::file_offset() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.file_offset)
  return file_offset_;
}
inline void Mapping::set_file_offset(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  file_offset_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.file_offset)
}

// int64 filename = 5;
inline void Mapping::clear_filename() {
  filename_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Mapping::filename() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.filename)
  return filename_;
}
inline void Mapping::set_filename(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  filename_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.filename)
}

// int64 build_id = 6;
inline void Mapping::clear_build_id() {
  build_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Mapping::build_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.build_id)
  return build_id_;
}
inline void Mapping::set_build_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  build_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.build_id)
}

// bool has_functions = 7;
inline void Mapping::clear_has_functions() {
  has_functions_ = false;
}
inline bool Mapping::has_functions() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.has_functions)
  return has_functions_;
}
inline void Mapping::set_has_functions(bool value) {
  
  has_functions_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.has_functions)
}

// bool has_filenames = 8;
inline void Mapping::clear_has_filenames() {
  has_filenames_ = false;
}
inline bool Mapping::has_filenames() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.has_filenames)
  return has_filenames_;
}
inline void Mapping::set_has_filenames(bool value) {
  
  has_filenames_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.has_filenames)
}

// bool has_line_numbers = 9;
inline void Mapping::clear_has_line_numbers() {
  has_line_numbers_ = false;
}
inline bool Mapping::has_line_numbers() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.has_line_numbers)
  return has_line_numbers_;
}
inline void Mapping::set_has_line_numbers(bool value) {
  
  has_line_numbers_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.has_line_numbers)
}

// bool has_inline_frames = 10;
inline void Mapping::clear_has_inline_frames() {
  has_inline_frames_ = false;
}
inline bool Mapping::has_inline_frames() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.has_inline_frames)
  return has_inline_frames_;
}
inline void Mapping::set_has_inline_frames(bool value) {
  
  has_inline_frames_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.has_inline_frames)
}

// -------------------------------------------------------------------

// Location

// uint64 id = 1;
inline void Location::clear_id() {
  id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Location::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Location.id)
  return id_;
}
inline void Location::set_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Location.id)
}

// uint64 mapping_id = 2;
inline void Location::clear_mapping_id() {
  mapping_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Location::mapping_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Location.mapping_id)
  return mapping_id_;
}
inline void Location::set_mapping_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  mapping_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Location.mapping_id)
}

// uint64 address = 3;
inline void Location::clear_address() {
  address_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Location::address() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Location.address)
  return address_;
}
inline void Location::set_address(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  address_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Location.address)
}

// repeated .tensorflow.tfprof.pprof.Line line = 4;
inline int Location::line_size() const {
  return line_.size();
}
inline void Location::clear_line() {
  line_.Clear();
}
inline ::tensorflow::tfprof::pprof::Line* Location::mutable_line(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Location.line)
  return line_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Line >*
Location::mutable_line() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Location.line)
  return &line_;
}
inline const ::tensorflow::tfprof::pprof::Line& Location::line(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Location.line)
  return line_.Get(index);
}
inline ::tensorflow::tfprof::pprof::Line* Location::add_line() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Location.line)
  return line_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Line >&
Location::line() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Location.line)
  return line_;
}

// -------------------------------------------------------------------

// Line

// uint64 function_id = 1;
inline void Line::clear_function_id() {
  function_id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Line::function_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Line.function_id)
  return function_id_;
}
inline void Line::set_function_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  function_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Line.function_id)
}

// int64 line = 2;
inline void Line::clear_line() {
  line_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Line::line() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Line.line)
  return line_;
}
inline void Line::set_line(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  line_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Line.line)
}

// -------------------------------------------------------------------

// Function

// uint64 id = 1;
inline void Function::clear_id() {
  id_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Function::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Function.id)
  return id_;
}
inline void Function::set_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Function.id)
}

// int64 name = 2;
inline void Function::clear_name() {
  name_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Function::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Function.name)
  return name_;
}
inline void Function::set_name(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  name_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Function.name)
}

// int64 system_name = 3;
inline void Function::clear_system_name() {
  system_name_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Function::system_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Function.system_name)
  return system_name_;
}
inline void Function::set_system_name(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  system_name_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Function.system_name)
}

// int64 filename = 4;
inline void Function::clear_filename() {
  filename_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Function::filename() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Function.filename)
  return filename_;
}
inline void Function::set_filename(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  filename_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Function.filename)
}

// int64 start_line = 5;
inline void Function::clear_start_line() {
  start_line_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Function::start_line() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Function.start_line)
  return start_line_;
}
inline void Function::set_start_line(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  start_line_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Function.start_line)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace pprof
}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprofile_2eproto
