# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-20 19:57+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Nepali (http://www.transifex.com/django/django/language/ne/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ne\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "प्रशासनिक दस्तावेज"

msgid "Home"
msgstr "गृह "

msgid "Documentation"
msgstr "प्रलेखन"

msgid "Bookmarklets"
msgstr "बुकमार्कलेटहरू"

msgid "Documentation bookmarklets"
msgstr "कागजातका बुकमार्कलेटहरू"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"बुकमार्कलेटहरू स्थापना गर्न, लिंकलाई तपाईंको बुकमार्क टूलबारमा ड्र्याग गर्नुहोस्, वा लिंकमा "
"दायाँ क्लिक गर्नुहोस् र तपाईंको बुकमार्कमा थप्नुहोस्। अब तपाईं साइटमा कुनै पृष्ठबाट बुकमार्क "
"चयन गर्न सक्नुहुन्छ।"

msgid "Documentation for this page"
msgstr "यो पृस्ठको लागी प्रलेखन"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""

msgid "Tags"
msgstr "ट्यागहरु "

msgid "List of all the template tags and their functions."
msgstr ""

msgid "Filters"
msgstr ""

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""

msgid "Models"
msgstr "नमुनाहरु "

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

msgid "Views"
msgstr "धृस्यहरु "

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

msgid "Please install docutils"
msgstr ""

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#, python-format
msgid "Model: %(name)s"
msgstr ""

msgid "Fields"
msgstr ""

msgid "Field"
msgstr ""

msgid "Type"
msgstr ""

msgid "Description"
msgstr "वर्णन"

msgid "Methods with arguments"
msgstr ""

msgid "Method"
msgstr ""

msgid "Arguments"
msgstr ""

msgid "Back to Model documentation"
msgstr ""

msgid "Model documentation"
msgstr ""

msgid "Model groups"
msgstr ""

msgid "Templates"
msgstr ""

#, python-format
msgid "Template: %(name)s"
msgstr ""

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr ""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr ""

msgid "(does not exist)"
msgstr ""

msgid "Back to Documentation"
msgstr ""

msgid "Template filters"
msgstr ""

msgid "Template filter documentation"
msgstr ""

msgid "Built-in filters"
msgstr ""

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr ""

msgid "Template tag documentation"
msgstr ""

msgid "Built-in tags"
msgstr ""

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr ""

msgid "Context:"
msgstr ""

msgid "Templates:"
msgstr ""

msgid "Back to View documentation"
msgstr ""

msgid "View documentation"
msgstr ""

msgid "Jump to namespace"
msgstr ""

msgid "Empty namespace"
msgstr ""

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "tag:"
msgstr "ट्याग:"

msgid "filter:"
msgstr ""

msgid "view:"
msgstr "धृस्य :"

#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "%(model_name)r नमुना %(app_label)r appमा   भेटिएन"

msgid "model:"
msgstr "नमुना :"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "`%(app_label)s.%(data_type)s` संबंधित बस्तु "

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "`%(app_label)s.%(object_name)s` संबंधित बस्तु "

#, python-format
msgid "all %s"
msgstr "सबै  %s"

#, python-format
msgid "number of %s"
msgstr "%sको संख्या"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr ""
