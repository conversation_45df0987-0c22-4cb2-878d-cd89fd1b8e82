import pymysql
import pandas as pd
import json
from datetime import datetime

def query_ecg_waveform(start_date=None, end_date=None, union_id=None):
    """
    从数据库查询ECG波形数据
    Args:
        start_date: 开始日期，格式：'YYYY-MM-DD'
        end_date: 结束日期，格式：'YYYY-MM-DD'
        union_id: 用户唯一标识
    Returns:
        DataFrame: 包含ECG波形数据的DataFrame
    """
    # 默认查询当天数据
    if start_date is None:
        start_date = datetime.now().strftime('%Y-%m-%d')
    if end_date is None:
        end_date = start_date

    try:
        # 建立数据库连接
        connection = pymysql.connect(
            host='rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
            user='ai',
            password='wq$$4r%ixg',
            database='backend_v2',
            port=3306,
            charset='utf8mb4'
        )

        cursor = connection.cursor()

        # 构建查询语句
        query = """
        SELECT 
            id,
            union_id,
            start_time,
            end_time,
            source,
            sample_rate,
            ecg_data,
            ecg_analysis
        FROM t_data_ecg
        WHERE deleted = 0
        AND DATE(start_time) BETWEEN %s AND %s
        """

        params = [start_date, end_date]

        if union_id:
            query += " AND union_id = %s"
            params.append(union_id)

        # 执行查询
        cursor.execute(query, params)
        results = cursor.fetchall()

        # 获取列名
        columns = ['id', 'union_id', 'start_time', 'end_time', 'source', 
                  'sample_rate', 'ecg_data', 'ecg_analysis']

        # 转换为DataFrame
        df = pd.DataFrame(results, columns=columns)

        if len(df) > 0:
            print(f"\n查询到 {len(df)} 条ECG波形数据记录")
            print("\n数据统计信息:")
            print(f"时间范围: {df['start_time'].min()} 至 {df['start_time'].max()}")
            print("\n数据来源分布:")
            print(df['source'].value_counts())
            print("\n采样率分布:")
            print(df['sample_rate'].value_counts())

            # 解析ecg_analysis字段
            def parse_ecg_analysis(json_str):
                if not json_str:
                    return {}
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    return {}

            df['ecg_analysis'] = df['ecg_analysis'].apply(parse_ecg_analysis)

            return df
        else:
            print("未找到匹配的ECG波形数据记录")
            return pd.DataFrame()

    except Exception as e:
        print(f"查询ECG波形数据时出错: {str(e)}")
        return pd.DataFrame()

    finally:
        cursor.close()
        connection.close()

def save_to_file(df, output_format='csv'):
    """
    将查询结果保存到文件
    Args:
        df: DataFrame对象
        output_format: 输出格式，支持'csv'和'excel'
    """
    if df.empty:
        print("没有数据需要保存")
        return

    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        if output_format.lower() == 'csv':
            filename = f'ecg_waveform_{timestamp}.csv'
            df.to_csv(filename, index=False)
        else:
            filename = f'ecg_waveform_{timestamp}.xlsx'
            df.to_excel(filename, index=False)

        print(f"\n数据已保存到文件: {filename}")

    except Exception as e:
        print(f"保存文件时出错: {str(e)}")

# 使用示例
if __name__ == '__main__':
    # 查询示例：查询指定日期范围的数据
    start_date = '2025-02-18'
    end_date = '2025-02-18'
    
    # 执行查询
    results = query_ecg_waveform(start_date, end_date)
    
    # 保存结果
    if not results.empty:
        save_to_file(results, 'excel')