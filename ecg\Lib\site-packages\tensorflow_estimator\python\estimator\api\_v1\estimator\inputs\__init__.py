# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf_estimator.python.estimator.api._v1.estimator.inputs namespace
"""

import sys as _sys

from tensorflow_estimator.python.estimator.inputs.numpy_io import numpy_input_fn # line: 89
from tensorflow_estimator.python.estimator.inputs.pandas_io import pandas_input_fn # line: 55

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "estimator.inputs", public_apis=None, deprecation=True,
      has_lite=False)
