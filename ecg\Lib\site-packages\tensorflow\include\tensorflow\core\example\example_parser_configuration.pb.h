// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/example/example_parser_configuration.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
namespace tensorflow {
class ExampleParserConfiguration;
class ExampleParserConfigurationDefaultTypeInternal;
extern ExampleParserConfigurationDefaultTypeInternal _ExampleParserConfiguration_default_instance_;
class ExampleParserConfiguration_FeatureMapEntry_DoNotUse;
class ExampleParserConfiguration_FeatureMapEntry_DoNotUseDefaultTypeInternal;
extern ExampleParserConfiguration_FeatureMapEntry_DoNotUseDefaultTypeInternal _ExampleParserConfiguration_FeatureMapEntry_DoNotUse_default_instance_;
class FeatureConfiguration;
class FeatureConfigurationDefaultTypeInternal;
extern FeatureConfigurationDefaultTypeInternal _FeatureConfiguration_default_instance_;
class FixedLenFeatureProto;
class FixedLenFeatureProtoDefaultTypeInternal;
extern FixedLenFeatureProtoDefaultTypeInternal _FixedLenFeatureProto_default_instance_;
class VarLenFeatureProto;
class VarLenFeatureProtoDefaultTypeInternal;
extern VarLenFeatureProtoDefaultTypeInternal _VarLenFeatureProto_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ExampleParserConfiguration* Arena::CreateMaybeMessage<::tensorflow::ExampleParserConfiguration>(Arena*);
template<> ::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FeatureConfiguration* Arena::CreateMaybeMessage<::tensorflow::FeatureConfiguration>(Arena*);
template<> ::tensorflow::FixedLenFeatureProto* Arena::CreateMaybeMessage<::tensorflow::FixedLenFeatureProto>(Arena*);
template<> ::tensorflow::VarLenFeatureProto* Arena::CreateMaybeMessage<::tensorflow::VarLenFeatureProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class VarLenFeatureProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.VarLenFeatureProto) */ {
 public:
  VarLenFeatureProto();
  virtual ~VarLenFeatureProto();

  VarLenFeatureProto(const VarLenFeatureProto& from);
  VarLenFeatureProto(VarLenFeatureProto&& from) noexcept
    : VarLenFeatureProto() {
    *this = ::std::move(from);
  }

  inline VarLenFeatureProto& operator=(const VarLenFeatureProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline VarLenFeatureProto& operator=(VarLenFeatureProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const VarLenFeatureProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const VarLenFeatureProto* internal_default_instance() {
    return reinterpret_cast<const VarLenFeatureProto*>(
               &_VarLenFeatureProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(VarLenFeatureProto& a, VarLenFeatureProto& b) {
    a.Swap(&b);
  }
  inline void Swap(VarLenFeatureProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VarLenFeatureProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline VarLenFeatureProto* New() const final {
    return CreateMaybeMessage<VarLenFeatureProto>(nullptr);
  }

  VarLenFeatureProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<VarLenFeatureProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const VarLenFeatureProto& from);
  void MergeFrom(const VarLenFeatureProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VarLenFeatureProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.VarLenFeatureProto";
  }
  protected:
  explicit VarLenFeatureProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesOutputTensorNameFieldNumber = 2,
    kIndicesOutputTensorNameFieldNumber = 3,
    kShapesOutputTensorNameFieldNumber = 4,
    kDtypeFieldNumber = 1,
  };
  // string values_output_tensor_name = 2;
  void clear_values_output_tensor_name();
  const std::string& values_output_tensor_name() const;
  void set_values_output_tensor_name(const std::string& value);
  void set_values_output_tensor_name(std::string&& value);
  void set_values_output_tensor_name(const char* value);
  void set_values_output_tensor_name(const char* value, size_t size);
  std::string* mutable_values_output_tensor_name();
  std::string* release_values_output_tensor_name();
  void set_allocated_values_output_tensor_name(std::string* values_output_tensor_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_values_output_tensor_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_values_output_tensor_name(
      std::string* values_output_tensor_name);

  // string indices_output_tensor_name = 3;
  void clear_indices_output_tensor_name();
  const std::string& indices_output_tensor_name() const;
  void set_indices_output_tensor_name(const std::string& value);
  void set_indices_output_tensor_name(std::string&& value);
  void set_indices_output_tensor_name(const char* value);
  void set_indices_output_tensor_name(const char* value, size_t size);
  std::string* mutable_indices_output_tensor_name();
  std::string* release_indices_output_tensor_name();
  void set_allocated_indices_output_tensor_name(std::string* indices_output_tensor_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_indices_output_tensor_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_indices_output_tensor_name(
      std::string* indices_output_tensor_name);

  // string shapes_output_tensor_name = 4;
  void clear_shapes_output_tensor_name();
  const std::string& shapes_output_tensor_name() const;
  void set_shapes_output_tensor_name(const std::string& value);
  void set_shapes_output_tensor_name(std::string&& value);
  void set_shapes_output_tensor_name(const char* value);
  void set_shapes_output_tensor_name(const char* value, size_t size);
  std::string* mutable_shapes_output_tensor_name();
  std::string* release_shapes_output_tensor_name();
  void set_allocated_shapes_output_tensor_name(std::string* shapes_output_tensor_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_shapes_output_tensor_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_shapes_output_tensor_name(
      std::string* shapes_output_tensor_name);

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.VarLenFeatureProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr values_output_tensor_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr indices_output_tensor_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr shapes_output_tensor_name_;
  int dtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class FixedLenFeatureProto :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FixedLenFeatureProto) */ {
 public:
  FixedLenFeatureProto();
  virtual ~FixedLenFeatureProto();

  FixedLenFeatureProto(const FixedLenFeatureProto& from);
  FixedLenFeatureProto(FixedLenFeatureProto&& from) noexcept
    : FixedLenFeatureProto() {
    *this = ::std::move(from);
  }

  inline FixedLenFeatureProto& operator=(const FixedLenFeatureProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline FixedLenFeatureProto& operator=(FixedLenFeatureProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FixedLenFeatureProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FixedLenFeatureProto* internal_default_instance() {
    return reinterpret_cast<const FixedLenFeatureProto*>(
               &_FixedLenFeatureProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(FixedLenFeatureProto& a, FixedLenFeatureProto& b) {
    a.Swap(&b);
  }
  inline void Swap(FixedLenFeatureProto* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FixedLenFeatureProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FixedLenFeatureProto* New() const final {
    return CreateMaybeMessage<FixedLenFeatureProto>(nullptr);
  }

  FixedLenFeatureProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FixedLenFeatureProto>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FixedLenFeatureProto& from);
  void MergeFrom(const FixedLenFeatureProto& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FixedLenFeatureProto* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FixedLenFeatureProto";
  }
  protected:
  explicit FixedLenFeatureProto(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesOutputTensorNameFieldNumber = 4,
    kShapeFieldNumber = 2,
    kDefaultValueFieldNumber = 3,
    kDtypeFieldNumber = 1,
  };
  // string values_output_tensor_name = 4;
  void clear_values_output_tensor_name();
  const std::string& values_output_tensor_name() const;
  void set_values_output_tensor_name(const std::string& value);
  void set_values_output_tensor_name(std::string&& value);
  void set_values_output_tensor_name(const char* value);
  void set_values_output_tensor_name(const char* value, size_t size);
  std::string* mutable_values_output_tensor_name();
  std::string* release_values_output_tensor_name();
  void set_allocated_values_output_tensor_name(std::string* values_output_tensor_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_values_output_tensor_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_values_output_tensor_name(
      std::string* values_output_tensor_name);

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.TensorProto default_value = 3;
  bool has_default_value() const;
  void clear_default_value();
  const ::tensorflow::TensorProto& default_value() const;
  ::tensorflow::TensorProto* release_default_value();
  ::tensorflow::TensorProto* mutable_default_value();
  void set_allocated_default_value(::tensorflow::TensorProto* default_value);
  void unsafe_arena_set_allocated_default_value(
      ::tensorflow::TensorProto* default_value);
  ::tensorflow::TensorProto* unsafe_arena_release_default_value();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.FixedLenFeatureProto)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr values_output_tensor_name_;
  ::tensorflow::TensorShapeProto* shape_;
  ::tensorflow::TensorProto* default_value_;
  int dtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class FeatureConfiguration :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FeatureConfiguration) */ {
 public:
  FeatureConfiguration();
  virtual ~FeatureConfiguration();

  FeatureConfiguration(const FeatureConfiguration& from);
  FeatureConfiguration(FeatureConfiguration&& from) noexcept
    : FeatureConfiguration() {
    *this = ::std::move(from);
  }

  inline FeatureConfiguration& operator=(const FeatureConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureConfiguration& operator=(FeatureConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FeatureConfiguration& default_instance();

  enum ConfigCase {
    kFixedLenFeature = 1,
    kVarLenFeature = 2,
    CONFIG_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureConfiguration* internal_default_instance() {
    return reinterpret_cast<const FeatureConfiguration*>(
               &_FeatureConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(FeatureConfiguration& a, FeatureConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(FeatureConfiguration* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FeatureConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FeatureConfiguration* New() const final {
    return CreateMaybeMessage<FeatureConfiguration>(nullptr);
  }

  FeatureConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FeatureConfiguration>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FeatureConfiguration& from);
  void MergeFrom(const FeatureConfiguration& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureConfiguration* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FeatureConfiguration";
  }
  protected:
  explicit FeatureConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFixedLenFeatureFieldNumber = 1,
    kVarLenFeatureFieldNumber = 2,
  };
  // .tensorflow.FixedLenFeatureProto fixed_len_feature = 1;
  bool has_fixed_len_feature() const;
  void clear_fixed_len_feature();
  const ::tensorflow::FixedLenFeatureProto& fixed_len_feature() const;
  ::tensorflow::FixedLenFeatureProto* release_fixed_len_feature();
  ::tensorflow::FixedLenFeatureProto* mutable_fixed_len_feature();
  void set_allocated_fixed_len_feature(::tensorflow::FixedLenFeatureProto* fixed_len_feature);
  void unsafe_arena_set_allocated_fixed_len_feature(
      ::tensorflow::FixedLenFeatureProto* fixed_len_feature);
  ::tensorflow::FixedLenFeatureProto* unsafe_arena_release_fixed_len_feature();

  // .tensorflow.VarLenFeatureProto var_len_feature = 2;
  bool has_var_len_feature() const;
  void clear_var_len_feature();
  const ::tensorflow::VarLenFeatureProto& var_len_feature() const;
  ::tensorflow::VarLenFeatureProto* release_var_len_feature();
  ::tensorflow::VarLenFeatureProto* mutable_var_len_feature();
  void set_allocated_var_len_feature(::tensorflow::VarLenFeatureProto* var_len_feature);
  void unsafe_arena_set_allocated_var_len_feature(
      ::tensorflow::VarLenFeatureProto* var_len_feature);
  ::tensorflow::VarLenFeatureProto* unsafe_arena_release_var_len_feature();

  void clear_config();
  ConfigCase config_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.FeatureConfiguration)
 private:
  class _Internal;
  void set_has_fixed_len_feature();
  void set_has_var_len_feature();

  inline bool has_config() const;
  inline void clear_has_config();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ConfigUnion {
    ConfigUnion() {}
    ::tensorflow::FixedLenFeatureProto* fixed_len_feature_;
    ::tensorflow::VarLenFeatureProto* var_len_feature_;
  } config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class ExampleParserConfiguration_FeatureMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExampleParserConfiguration_FeatureMapEntry_DoNotUse, 
    std::string, ::tensorflow::FeatureConfiguration,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExampleParserConfiguration_FeatureMapEntry_DoNotUse, 
    std::string, ::tensorflow::FeatureConfiguration,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ExampleParserConfiguration_FeatureMapEntry_DoNotUse();
  ExampleParserConfiguration_FeatureMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ExampleParserConfiguration_FeatureMapEntry_DoNotUse& other);
  static const ExampleParserConfiguration_FeatureMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExampleParserConfiguration_FeatureMapEntry_DoNotUse*>(&_ExampleParserConfiguration_FeatureMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ExampleParserConfiguration.FeatureMapEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto.file_level_metadata[3];
  }

  public:
};

// -------------------------------------------------------------------

class ExampleParserConfiguration :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExampleParserConfiguration) */ {
 public:
  ExampleParserConfiguration();
  virtual ~ExampleParserConfiguration();

  ExampleParserConfiguration(const ExampleParserConfiguration& from);
  ExampleParserConfiguration(ExampleParserConfiguration&& from) noexcept
    : ExampleParserConfiguration() {
    *this = ::std::move(from);
  }

  inline ExampleParserConfiguration& operator=(const ExampleParserConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExampleParserConfiguration& operator=(ExampleParserConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExampleParserConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExampleParserConfiguration* internal_default_instance() {
    return reinterpret_cast<const ExampleParserConfiguration*>(
               &_ExampleParserConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ExampleParserConfiguration& a, ExampleParserConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(ExampleParserConfiguration* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExampleParserConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExampleParserConfiguration* New() const final {
    return CreateMaybeMessage<ExampleParserConfiguration>(nullptr);
  }

  ExampleParserConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExampleParserConfiguration>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExampleParserConfiguration& from);
  void MergeFrom(const ExampleParserConfiguration& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExampleParserConfiguration* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ExampleParserConfiguration";
  }
  protected:
  explicit ExampleParserConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFeatureMapFieldNumber = 1,
  };
  // map<string, .tensorflow.FeatureConfiguration> feature_map = 1;
  int feature_map_size() const;
  void clear_feature_map();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >&
      feature_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >*
      mutable_feature_map();

  // @@protoc_insertion_point(class_scope:tensorflow.ExampleParserConfiguration)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ExampleParserConfiguration_FeatureMapEntry_DoNotUse,
      std::string, ::tensorflow::FeatureConfiguration,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > feature_map_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// VarLenFeatureProto

// .tensorflow.DataType dtype = 1;
inline void VarLenFeatureProto::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType VarLenFeatureProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.VarLenFeatureProto.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void VarLenFeatureProto::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.VarLenFeatureProto.dtype)
}

// string values_output_tensor_name = 2;
inline void VarLenFeatureProto::clear_values_output_tensor_name() {
  values_output_tensor_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& VarLenFeatureProto::values_output_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VarLenFeatureProto.values_output_tensor_name)
  return values_output_tensor_name_.Get();
}
inline void VarLenFeatureProto::set_values_output_tensor_name(const std::string& value) {
  
  values_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VarLenFeatureProto.values_output_tensor_name)
}
inline void VarLenFeatureProto::set_values_output_tensor_name(std::string&& value) {
  
  values_output_tensor_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VarLenFeatureProto.values_output_tensor_name)
}
inline void VarLenFeatureProto::set_values_output_tensor_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  values_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VarLenFeatureProto.values_output_tensor_name)
}
inline void VarLenFeatureProto::set_values_output_tensor_name(const char* value,
    size_t size) {
  
  values_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VarLenFeatureProto.values_output_tensor_name)
}
inline std::string* VarLenFeatureProto::mutable_values_output_tensor_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VarLenFeatureProto.values_output_tensor_name)
  return values_output_tensor_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* VarLenFeatureProto::release_values_output_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VarLenFeatureProto.values_output_tensor_name)
  
  return values_output_tensor_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VarLenFeatureProto::set_allocated_values_output_tensor_name(std::string* values_output_tensor_name) {
  if (values_output_tensor_name != nullptr) {
    
  } else {
    
  }
  values_output_tensor_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), values_output_tensor_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VarLenFeatureProto.values_output_tensor_name)
}
inline std::string* VarLenFeatureProto::unsafe_arena_release_values_output_tensor_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VarLenFeatureProto.values_output_tensor_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return values_output_tensor_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VarLenFeatureProto::unsafe_arena_set_allocated_values_output_tensor_name(
    std::string* values_output_tensor_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (values_output_tensor_name != nullptr) {
    
  } else {
    
  }
  values_output_tensor_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      values_output_tensor_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VarLenFeatureProto.values_output_tensor_name)
}

// string indices_output_tensor_name = 3;
inline void VarLenFeatureProto::clear_indices_output_tensor_name() {
  indices_output_tensor_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& VarLenFeatureProto::indices_output_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
  return indices_output_tensor_name_.Get();
}
inline void VarLenFeatureProto::set_indices_output_tensor_name(const std::string& value) {
  
  indices_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
}
inline void VarLenFeatureProto::set_indices_output_tensor_name(std::string&& value) {
  
  indices_output_tensor_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
}
inline void VarLenFeatureProto::set_indices_output_tensor_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  indices_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
}
inline void VarLenFeatureProto::set_indices_output_tensor_name(const char* value,
    size_t size) {
  
  indices_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
}
inline std::string* VarLenFeatureProto::mutable_indices_output_tensor_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
  return indices_output_tensor_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* VarLenFeatureProto::release_indices_output_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
  
  return indices_output_tensor_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VarLenFeatureProto::set_allocated_indices_output_tensor_name(std::string* indices_output_tensor_name) {
  if (indices_output_tensor_name != nullptr) {
    
  } else {
    
  }
  indices_output_tensor_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), indices_output_tensor_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
}
inline std::string* VarLenFeatureProto::unsafe_arena_release_indices_output_tensor_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return indices_output_tensor_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VarLenFeatureProto::unsafe_arena_set_allocated_indices_output_tensor_name(
    std::string* indices_output_tensor_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (indices_output_tensor_name != nullptr) {
    
  } else {
    
  }
  indices_output_tensor_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      indices_output_tensor_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
}

// string shapes_output_tensor_name = 4;
inline void VarLenFeatureProto::clear_shapes_output_tensor_name() {
  shapes_output_tensor_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& VarLenFeatureProto::shapes_output_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
  return shapes_output_tensor_name_.Get();
}
inline void VarLenFeatureProto::set_shapes_output_tensor_name(const std::string& value) {
  
  shapes_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
}
inline void VarLenFeatureProto::set_shapes_output_tensor_name(std::string&& value) {
  
  shapes_output_tensor_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
}
inline void VarLenFeatureProto::set_shapes_output_tensor_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  shapes_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
}
inline void VarLenFeatureProto::set_shapes_output_tensor_name(const char* value,
    size_t size) {
  
  shapes_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
}
inline std::string* VarLenFeatureProto::mutable_shapes_output_tensor_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
  return shapes_output_tensor_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* VarLenFeatureProto::release_shapes_output_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
  
  return shapes_output_tensor_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void VarLenFeatureProto::set_allocated_shapes_output_tensor_name(std::string* shapes_output_tensor_name) {
  if (shapes_output_tensor_name != nullptr) {
    
  } else {
    
  }
  shapes_output_tensor_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), shapes_output_tensor_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
}
inline std::string* VarLenFeatureProto::unsafe_arena_release_shapes_output_tensor_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return shapes_output_tensor_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void VarLenFeatureProto::unsafe_arena_set_allocated_shapes_output_tensor_name(
    std::string* shapes_output_tensor_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (shapes_output_tensor_name != nullptr) {
    
  } else {
    
  }
  shapes_output_tensor_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      shapes_output_tensor_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
}

// -------------------------------------------------------------------

// FixedLenFeatureProto

// .tensorflow.DataType dtype = 1;
inline void FixedLenFeatureProto::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType FixedLenFeatureProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.FixedLenFeatureProto.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void FixedLenFeatureProto::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.FixedLenFeatureProto.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool FixedLenFeatureProto::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& FixedLenFeatureProto::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.FixedLenFeatureProto.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* FixedLenFeatureProto::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.FixedLenFeatureProto.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* FixedLenFeatureProto::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FixedLenFeatureProto.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* FixedLenFeatureProto::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FixedLenFeatureProto.shape)
  return shape_;
}
inline void FixedLenFeatureProto::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FixedLenFeatureProto.shape)
}

// .tensorflow.TensorProto default_value = 3;
inline bool FixedLenFeatureProto::has_default_value() const {
  return this != internal_default_instance() && default_value_ != nullptr;
}
inline const ::tensorflow::TensorProto& FixedLenFeatureProto::default_value() const {
  const ::tensorflow::TensorProto* p = default_value_;
  // @@protoc_insertion_point(field_get:tensorflow.FixedLenFeatureProto.default_value)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* FixedLenFeatureProto::release_default_value() {
  // @@protoc_insertion_point(field_release:tensorflow.FixedLenFeatureProto.default_value)
  
  ::tensorflow::TensorProto* temp = default_value_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  default_value_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* FixedLenFeatureProto::unsafe_arena_release_default_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FixedLenFeatureProto.default_value)
  
  ::tensorflow::TensorProto* temp = default_value_;
  default_value_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* FixedLenFeatureProto::mutable_default_value() {
  
  if (default_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    default_value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FixedLenFeatureProto.default_value)
  return default_value_;
}
inline void FixedLenFeatureProto::set_allocated_default_value(::tensorflow::TensorProto* default_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(default_value_);
  }
  if (default_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(default_value)->GetArena();
    if (message_arena != submessage_arena) {
      default_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, default_value, submessage_arena);
    }
    
  } else {
    
  }
  default_value_ = default_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FixedLenFeatureProto.default_value)
}

// string values_output_tensor_name = 4;
inline void FixedLenFeatureProto::clear_values_output_tensor_name() {
  values_output_tensor_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& FixedLenFeatureProto::values_output_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
  return values_output_tensor_name_.Get();
}
inline void FixedLenFeatureProto::set_values_output_tensor_name(const std::string& value) {
  
  values_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
}
inline void FixedLenFeatureProto::set_values_output_tensor_name(std::string&& value) {
  
  values_output_tensor_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
}
inline void FixedLenFeatureProto::set_values_output_tensor_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  values_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
}
inline void FixedLenFeatureProto::set_values_output_tensor_name(const char* value,
    size_t size) {
  
  values_output_tensor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
}
inline std::string* FixedLenFeatureProto::mutable_values_output_tensor_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
  return values_output_tensor_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* FixedLenFeatureProto::release_values_output_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
  
  return values_output_tensor_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void FixedLenFeatureProto::set_allocated_values_output_tensor_name(std::string* values_output_tensor_name) {
  if (values_output_tensor_name != nullptr) {
    
  } else {
    
  }
  values_output_tensor_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), values_output_tensor_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
}
inline std::string* FixedLenFeatureProto::unsafe_arena_release_values_output_tensor_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return values_output_tensor_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void FixedLenFeatureProto::unsafe_arena_set_allocated_values_output_tensor_name(
    std::string* values_output_tensor_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (values_output_tensor_name != nullptr) {
    
  } else {
    
  }
  values_output_tensor_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      values_output_tensor_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
}

// -------------------------------------------------------------------

// FeatureConfiguration

// .tensorflow.FixedLenFeatureProto fixed_len_feature = 1;
inline bool FeatureConfiguration::has_fixed_len_feature() const {
  return config_case() == kFixedLenFeature;
}
inline void FeatureConfiguration::set_has_fixed_len_feature() {
  _oneof_case_[0] = kFixedLenFeature;
}
inline void FeatureConfiguration::clear_fixed_len_feature() {
  if (has_fixed_len_feature()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete config_.fixed_len_feature_;
    }
    clear_has_config();
  }
}
inline ::tensorflow::FixedLenFeatureProto* FeatureConfiguration::release_fixed_len_feature() {
  // @@protoc_insertion_point(field_release:tensorflow.FeatureConfiguration.fixed_len_feature)
  if (has_fixed_len_feature()) {
    clear_has_config();
      ::tensorflow::FixedLenFeatureProto* temp = config_.fixed_len_feature_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    config_.fixed_len_feature_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::FixedLenFeatureProto& FeatureConfiguration::fixed_len_feature() const {
  // @@protoc_insertion_point(field_get:tensorflow.FeatureConfiguration.fixed_len_feature)
  return has_fixed_len_feature()
      ? *config_.fixed_len_feature_
      : *reinterpret_cast< ::tensorflow::FixedLenFeatureProto*>(&::tensorflow::_FixedLenFeatureProto_default_instance_);
}
inline ::tensorflow::FixedLenFeatureProto* FeatureConfiguration::unsafe_arena_release_fixed_len_feature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FeatureConfiguration.fixed_len_feature)
  if (has_fixed_len_feature()) {
    clear_has_config();
    ::tensorflow::FixedLenFeatureProto* temp = config_.fixed_len_feature_;
    config_.fixed_len_feature_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void FeatureConfiguration::unsafe_arena_set_allocated_fixed_len_feature(::tensorflow::FixedLenFeatureProto* fixed_len_feature) {
  clear_config();
  if (fixed_len_feature) {
    set_has_fixed_len_feature();
    config_.fixed_len_feature_ = fixed_len_feature;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FeatureConfiguration.fixed_len_feature)
}
inline ::tensorflow::FixedLenFeatureProto* FeatureConfiguration::mutable_fixed_len_feature() {
  if (!has_fixed_len_feature()) {
    clear_config();
    set_has_fixed_len_feature();
    config_.fixed_len_feature_ = CreateMaybeMessage< ::tensorflow::FixedLenFeatureProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FeatureConfiguration.fixed_len_feature)
  return config_.fixed_len_feature_;
}

// .tensorflow.VarLenFeatureProto var_len_feature = 2;
inline bool FeatureConfiguration::has_var_len_feature() const {
  return config_case() == kVarLenFeature;
}
inline void FeatureConfiguration::set_has_var_len_feature() {
  _oneof_case_[0] = kVarLenFeature;
}
inline void FeatureConfiguration::clear_var_len_feature() {
  if (has_var_len_feature()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete config_.var_len_feature_;
    }
    clear_has_config();
  }
}
inline ::tensorflow::VarLenFeatureProto* FeatureConfiguration::release_var_len_feature() {
  // @@protoc_insertion_point(field_release:tensorflow.FeatureConfiguration.var_len_feature)
  if (has_var_len_feature()) {
    clear_has_config();
      ::tensorflow::VarLenFeatureProto* temp = config_.var_len_feature_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    config_.var_len_feature_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::VarLenFeatureProto& FeatureConfiguration::var_len_feature() const {
  // @@protoc_insertion_point(field_get:tensorflow.FeatureConfiguration.var_len_feature)
  return has_var_len_feature()
      ? *config_.var_len_feature_
      : *reinterpret_cast< ::tensorflow::VarLenFeatureProto*>(&::tensorflow::_VarLenFeatureProto_default_instance_);
}
inline ::tensorflow::VarLenFeatureProto* FeatureConfiguration::unsafe_arena_release_var_len_feature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FeatureConfiguration.var_len_feature)
  if (has_var_len_feature()) {
    clear_has_config();
    ::tensorflow::VarLenFeatureProto* temp = config_.var_len_feature_;
    config_.var_len_feature_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void FeatureConfiguration::unsafe_arena_set_allocated_var_len_feature(::tensorflow::VarLenFeatureProto* var_len_feature) {
  clear_config();
  if (var_len_feature) {
    set_has_var_len_feature();
    config_.var_len_feature_ = var_len_feature;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FeatureConfiguration.var_len_feature)
}
inline ::tensorflow::VarLenFeatureProto* FeatureConfiguration::mutable_var_len_feature() {
  if (!has_var_len_feature()) {
    clear_config();
    set_has_var_len_feature();
    config_.var_len_feature_ = CreateMaybeMessage< ::tensorflow::VarLenFeatureProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FeatureConfiguration.var_len_feature)
  return config_.var_len_feature_;
}

inline bool FeatureConfiguration::has_config() const {
  return config_case() != CONFIG_NOT_SET;
}
inline void FeatureConfiguration::clear_has_config() {
  _oneof_case_[0] = CONFIG_NOT_SET;
}
inline FeatureConfiguration::ConfigCase FeatureConfiguration::config_case() const {
  return FeatureConfiguration::ConfigCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ExampleParserConfiguration

// map<string, .tensorflow.FeatureConfiguration> feature_map = 1;
inline int ExampleParserConfiguration::feature_map_size() const {
  return feature_map_.size();
}
inline void ExampleParserConfiguration::clear_feature_map() {
  feature_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >&
ExampleParserConfiguration::feature_map() const {
  // @@protoc_insertion_point(field_map:tensorflow.ExampleParserConfiguration.feature_map)
  return feature_map_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >*
ExampleParserConfiguration::mutable_feature_map() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.ExampleParserConfiguration.feature_map)
  return feature_map_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto
