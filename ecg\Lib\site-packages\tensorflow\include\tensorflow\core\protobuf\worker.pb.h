// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/worker.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fworker_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fworker_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include "tensorflow/core/framework/cost_graph.pb.h"
#include "tensorflow/core/framework/device_attributes.pb.h"
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/step_stats.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/protobuf/config.pb.h"
#include "tensorflow/core/protobuf/debug.pb.h"
#include "tensorflow/core/protobuf/error_codes.pb.h"
#include "tensorflow/core/protobuf/named_tensor.pb.h"
#include "tensorflow/core/protobuf/tensorflow_server.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fworker_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[36]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
namespace tensorflow {
class CleanupAllRequest;
class CleanupAllRequestDefaultTypeInternal;
extern CleanupAllRequestDefaultTypeInternal _CleanupAllRequest_default_instance_;
class CleanupAllResponse;
class CleanupAllResponseDefaultTypeInternal;
extern CleanupAllResponseDefaultTypeInternal _CleanupAllResponse_default_instance_;
class CleanupGraphRequest;
class CleanupGraphRequestDefaultTypeInternal;
extern CleanupGraphRequestDefaultTypeInternal _CleanupGraphRequest_default_instance_;
class CleanupGraphResponse;
class CleanupGraphResponseDefaultTypeInternal;
extern CleanupGraphResponseDefaultTypeInternal _CleanupGraphResponse_default_instance_;
class CompleteGroupRequest;
class CompleteGroupRequestDefaultTypeInternal;
extern CompleteGroupRequestDefaultTypeInternal _CompleteGroupRequest_default_instance_;
class CompleteGroupResponse;
class CompleteGroupResponseDefaultTypeInternal;
extern CompleteGroupResponseDefaultTypeInternal _CompleteGroupResponse_default_instance_;
class CompleteInstanceRequest;
class CompleteInstanceRequestDefaultTypeInternal;
extern CompleteInstanceRequestDefaultTypeInternal _CompleteInstanceRequest_default_instance_;
class CompleteInstanceResponse;
class CompleteInstanceResponseDefaultTypeInternal;
extern CompleteInstanceResponseDefaultTypeInternal _CompleteInstanceResponse_default_instance_;
class CreateWorkerSessionRequest;
class CreateWorkerSessionRequestDefaultTypeInternal;
extern CreateWorkerSessionRequestDefaultTypeInternal _CreateWorkerSessionRequest_default_instance_;
class CreateWorkerSessionResponse;
class CreateWorkerSessionResponseDefaultTypeInternal;
extern CreateWorkerSessionResponseDefaultTypeInternal _CreateWorkerSessionResponse_default_instance_;
class DeleteWorkerSessionRequest;
class DeleteWorkerSessionRequestDefaultTypeInternal;
extern DeleteWorkerSessionRequestDefaultTypeInternal _DeleteWorkerSessionRequest_default_instance_;
class DeleteWorkerSessionResponse;
class DeleteWorkerSessionResponseDefaultTypeInternal;
extern DeleteWorkerSessionResponseDefaultTypeInternal _DeleteWorkerSessionResponse_default_instance_;
class DeregisterGraphRequest;
class DeregisterGraphRequestDefaultTypeInternal;
extern DeregisterGraphRequestDefaultTypeInternal _DeregisterGraphRequest_default_instance_;
class DeregisterGraphResponse;
class DeregisterGraphResponseDefaultTypeInternal;
extern DeregisterGraphResponseDefaultTypeInternal _DeregisterGraphResponse_default_instance_;
class ExecutorOpts;
class ExecutorOptsDefaultTypeInternal;
extern ExecutorOptsDefaultTypeInternal _ExecutorOpts_default_instance_;
class GetStatusRequest;
class GetStatusRequestDefaultTypeInternal;
extern GetStatusRequestDefaultTypeInternal _GetStatusRequest_default_instance_;
class GetStatusResponse;
class GetStatusResponseDefaultTypeInternal;
extern GetStatusResponseDefaultTypeInternal _GetStatusResponse_default_instance_;
class GetStepSequenceRequest;
class GetStepSequenceRequestDefaultTypeInternal;
extern GetStepSequenceRequestDefaultTypeInternal _GetStepSequenceRequest_default_instance_;
class GetStepSequenceResponse;
class GetStepSequenceResponseDefaultTypeInternal;
extern GetStepSequenceResponseDefaultTypeInternal _GetStepSequenceResponse_default_instance_;
class LabeledStepStats;
class LabeledStepStatsDefaultTypeInternal;
extern LabeledStepStatsDefaultTypeInternal _LabeledStepStats_default_instance_;
class LoggingRequest;
class LoggingRequestDefaultTypeInternal;
extern LoggingRequestDefaultTypeInternal _LoggingRequest_default_instance_;
class LoggingResponse;
class LoggingResponseDefaultTypeInternal;
extern LoggingResponseDefaultTypeInternal _LoggingResponse_default_instance_;
class MarkRecvFinishedRequest;
class MarkRecvFinishedRequestDefaultTypeInternal;
extern MarkRecvFinishedRequestDefaultTypeInternal _MarkRecvFinishedRequest_default_instance_;
class MarkRecvFinishedResponse;
class MarkRecvFinishedResponseDefaultTypeInternal;
extern MarkRecvFinishedResponseDefaultTypeInternal _MarkRecvFinishedResponse_default_instance_;
class RecvBufRequest;
class RecvBufRequestDefaultTypeInternal;
extern RecvBufRequestDefaultTypeInternal _RecvBufRequest_default_instance_;
class RecvBufResponse;
class RecvBufResponseDefaultTypeInternal;
extern RecvBufResponseDefaultTypeInternal _RecvBufResponse_default_instance_;
class RecvTensorRequest;
class RecvTensorRequestDefaultTypeInternal;
extern RecvTensorRequestDefaultTypeInternal _RecvTensorRequest_default_instance_;
class RecvTensorResponse;
class RecvTensorResponseDefaultTypeInternal;
extern RecvTensorResponseDefaultTypeInternal _RecvTensorResponse_default_instance_;
class RegisterGraphRequest;
class RegisterGraphRequestDefaultTypeInternal;
extern RegisterGraphRequestDefaultTypeInternal _RegisterGraphRequest_default_instance_;
class RegisterGraphResponse;
class RegisterGraphResponseDefaultTypeInternal;
extern RegisterGraphResponseDefaultTypeInternal _RegisterGraphResponse_default_instance_;
class RunGraphRequest;
class RunGraphRequestDefaultTypeInternal;
extern RunGraphRequestDefaultTypeInternal _RunGraphRequest_default_instance_;
class RunGraphResponse;
class RunGraphResponseDefaultTypeInternal;
extern RunGraphResponseDefaultTypeInternal _RunGraphResponse_default_instance_;
class StepSequence;
class StepSequenceDefaultTypeInternal;
extern StepSequenceDefaultTypeInternal _StepSequence_default_instance_;
class TraceOpts;
class TraceOptsDefaultTypeInternal;
extern TraceOptsDefaultTypeInternal _TraceOpts_default_instance_;
class TracingRequest;
class TracingRequestDefaultTypeInternal;
extern TracingRequestDefaultTypeInternal _TracingRequest_default_instance_;
class TracingResponse;
class TracingResponseDefaultTypeInternal;
extern TracingResponseDefaultTypeInternal _TracingResponse_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CleanupAllRequest* Arena::CreateMaybeMessage<::tensorflow::CleanupAllRequest>(Arena*);
template<> ::tensorflow::CleanupAllResponse* Arena::CreateMaybeMessage<::tensorflow::CleanupAllResponse>(Arena*);
template<> ::tensorflow::CleanupGraphRequest* Arena::CreateMaybeMessage<::tensorflow::CleanupGraphRequest>(Arena*);
template<> ::tensorflow::CleanupGraphResponse* Arena::CreateMaybeMessage<::tensorflow::CleanupGraphResponse>(Arena*);
template<> ::tensorflow::CompleteGroupRequest* Arena::CreateMaybeMessage<::tensorflow::CompleteGroupRequest>(Arena*);
template<> ::tensorflow::CompleteGroupResponse* Arena::CreateMaybeMessage<::tensorflow::CompleteGroupResponse>(Arena*);
template<> ::tensorflow::CompleteInstanceRequest* Arena::CreateMaybeMessage<::tensorflow::CompleteInstanceRequest>(Arena*);
template<> ::tensorflow::CompleteInstanceResponse* Arena::CreateMaybeMessage<::tensorflow::CompleteInstanceResponse>(Arena*);
template<> ::tensorflow::CreateWorkerSessionRequest* Arena::CreateMaybeMessage<::tensorflow::CreateWorkerSessionRequest>(Arena*);
template<> ::tensorflow::CreateWorkerSessionResponse* Arena::CreateMaybeMessage<::tensorflow::CreateWorkerSessionResponse>(Arena*);
template<> ::tensorflow::DeleteWorkerSessionRequest* Arena::CreateMaybeMessage<::tensorflow::DeleteWorkerSessionRequest>(Arena*);
template<> ::tensorflow::DeleteWorkerSessionResponse* Arena::CreateMaybeMessage<::tensorflow::DeleteWorkerSessionResponse>(Arena*);
template<> ::tensorflow::DeregisterGraphRequest* Arena::CreateMaybeMessage<::tensorflow::DeregisterGraphRequest>(Arena*);
template<> ::tensorflow::DeregisterGraphResponse* Arena::CreateMaybeMessage<::tensorflow::DeregisterGraphResponse>(Arena*);
template<> ::tensorflow::ExecutorOpts* Arena::CreateMaybeMessage<::tensorflow::ExecutorOpts>(Arena*);
template<> ::tensorflow::GetStatusRequest* Arena::CreateMaybeMessage<::tensorflow::GetStatusRequest>(Arena*);
template<> ::tensorflow::GetStatusResponse* Arena::CreateMaybeMessage<::tensorflow::GetStatusResponse>(Arena*);
template<> ::tensorflow::GetStepSequenceRequest* Arena::CreateMaybeMessage<::tensorflow::GetStepSequenceRequest>(Arena*);
template<> ::tensorflow::GetStepSequenceResponse* Arena::CreateMaybeMessage<::tensorflow::GetStepSequenceResponse>(Arena*);
template<> ::tensorflow::LabeledStepStats* Arena::CreateMaybeMessage<::tensorflow::LabeledStepStats>(Arena*);
template<> ::tensorflow::LoggingRequest* Arena::CreateMaybeMessage<::tensorflow::LoggingRequest>(Arena*);
template<> ::tensorflow::LoggingResponse* Arena::CreateMaybeMessage<::tensorflow::LoggingResponse>(Arena*);
template<> ::tensorflow::MarkRecvFinishedRequest* Arena::CreateMaybeMessage<::tensorflow::MarkRecvFinishedRequest>(Arena*);
template<> ::tensorflow::MarkRecvFinishedResponse* Arena::CreateMaybeMessage<::tensorflow::MarkRecvFinishedResponse>(Arena*);
template<> ::tensorflow::RecvBufRequest* Arena::CreateMaybeMessage<::tensorflow::RecvBufRequest>(Arena*);
template<> ::tensorflow::RecvBufResponse* Arena::CreateMaybeMessage<::tensorflow::RecvBufResponse>(Arena*);
template<> ::tensorflow::RecvTensorRequest* Arena::CreateMaybeMessage<::tensorflow::RecvTensorRequest>(Arena*);
template<> ::tensorflow::RecvTensorResponse* Arena::CreateMaybeMessage<::tensorflow::RecvTensorResponse>(Arena*);
template<> ::tensorflow::RegisterGraphRequest* Arena::CreateMaybeMessage<::tensorflow::RegisterGraphRequest>(Arena*);
template<> ::tensorflow::RegisterGraphResponse* Arena::CreateMaybeMessage<::tensorflow::RegisterGraphResponse>(Arena*);
template<> ::tensorflow::RunGraphRequest* Arena::CreateMaybeMessage<::tensorflow::RunGraphRequest>(Arena*);
template<> ::tensorflow::RunGraphResponse* Arena::CreateMaybeMessage<::tensorflow::RunGraphResponse>(Arena*);
template<> ::tensorflow::StepSequence* Arena::CreateMaybeMessage<::tensorflow::StepSequence>(Arena*);
template<> ::tensorflow::TraceOpts* Arena::CreateMaybeMessage<::tensorflow::TraceOpts>(Arena*);
template<> ::tensorflow::TracingRequest* Arena::CreateMaybeMessage<::tensorflow::TracingRequest>(Arena*);
template<> ::tensorflow::TracingResponse* Arena::CreateMaybeMessage<::tensorflow::TracingResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class GetStatusRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStatusRequest) */ {
 public:
  GetStatusRequest();
  virtual ~GetStatusRequest();

  GetStatusRequest(const GetStatusRequest& from);
  GetStatusRequest(GetStatusRequest&& from) noexcept
    : GetStatusRequest() {
    *this = ::std::move(from);
  }

  inline GetStatusRequest& operator=(const GetStatusRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetStatusRequest& operator=(GetStatusRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GetStatusRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetStatusRequest* internal_default_instance() {
    return reinterpret_cast<const GetStatusRequest*>(
               &_GetStatusRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GetStatusRequest& a, GetStatusRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetStatusRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetStatusRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GetStatusRequest* New() const final {
    return CreateMaybeMessage<GetStatusRequest>(nullptr);
  }

  GetStatusRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GetStatusRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GetStatusRequest& from);
  void MergeFrom(const GetStatusRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStatusRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetStatusRequest";
  }
  protected:
  explicit GetStatusRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.GetStatusRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class GetStatusResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStatusResponse) */ {
 public:
  GetStatusResponse();
  virtual ~GetStatusResponse();

  GetStatusResponse(const GetStatusResponse& from);
  GetStatusResponse(GetStatusResponse&& from) noexcept
    : GetStatusResponse() {
    *this = ::std::move(from);
  }

  inline GetStatusResponse& operator=(const GetStatusResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetStatusResponse& operator=(GetStatusResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GetStatusResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetStatusResponse* internal_default_instance() {
    return reinterpret_cast<const GetStatusResponse*>(
               &_GetStatusResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GetStatusResponse& a, GetStatusResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetStatusResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetStatusResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GetStatusResponse* New() const final {
    return CreateMaybeMessage<GetStatusResponse>(nullptr);
  }

  GetStatusResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GetStatusResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GetStatusResponse& from);
  void MergeFrom(const GetStatusResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStatusResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetStatusResponse";
  }
  protected:
  explicit GetStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceAttributesFieldNumber = 1,
  };
  // repeated .tensorflow.DeviceAttributes device_attributes = 1;
  int device_attributes_size() const;
  void clear_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_device_attributes();
  const ::tensorflow::DeviceAttributes& device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      device_attributes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.GetStatusResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > device_attributes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CreateWorkerSessionRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateWorkerSessionRequest) */ {
 public:
  CreateWorkerSessionRequest();
  virtual ~CreateWorkerSessionRequest();

  CreateWorkerSessionRequest(const CreateWorkerSessionRequest& from);
  CreateWorkerSessionRequest(CreateWorkerSessionRequest&& from) noexcept
    : CreateWorkerSessionRequest() {
    *this = ::std::move(from);
  }

  inline CreateWorkerSessionRequest& operator=(const CreateWorkerSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateWorkerSessionRequest& operator=(CreateWorkerSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CreateWorkerSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateWorkerSessionRequest* internal_default_instance() {
    return reinterpret_cast<const CreateWorkerSessionRequest*>(
               &_CreateWorkerSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CreateWorkerSessionRequest& a, CreateWorkerSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateWorkerSessionRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateWorkerSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CreateWorkerSessionRequest* New() const final {
    return CreateMaybeMessage<CreateWorkerSessionRequest>(nullptr);
  }

  CreateWorkerSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CreateWorkerSessionRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CreateWorkerSessionRequest& from);
  void MergeFrom(const CreateWorkerSessionRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateWorkerSessionRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CreateWorkerSessionRequest";
  }
  protected:
  explicit CreateWorkerSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClusterDeviceAttributesFieldNumber = 4,
    kSessionHandleFieldNumber = 1,
    kMasterTaskFieldNumber = 5,
    kServerDefFieldNumber = 2,
    kMasterIncarnationFieldNumber = 6,
    kIsolateSessionStateFieldNumber = 3,
  };
  // repeated .tensorflow.DeviceAttributes cluster_device_attributes = 4;
  int cluster_device_attributes_size() const;
  void clear_cluster_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_cluster_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_cluster_device_attributes();
  const ::tensorflow::DeviceAttributes& cluster_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_cluster_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      cluster_device_attributes() const;

  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // string master_task = 5;
  void clear_master_task();
  const std::string& master_task() const;
  void set_master_task(const std::string& value);
  void set_master_task(std::string&& value);
  void set_master_task(const char* value);
  void set_master_task(const char* value, size_t size);
  std::string* mutable_master_task();
  std::string* release_master_task();
  void set_allocated_master_task(std::string* master_task);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_master_task();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_master_task(
      std::string* master_task);

  // .tensorflow.ServerDef server_def = 2;
  bool has_server_def() const;
  void clear_server_def();
  const ::tensorflow::ServerDef& server_def() const;
  ::tensorflow::ServerDef* release_server_def();
  ::tensorflow::ServerDef* mutable_server_def();
  void set_allocated_server_def(::tensorflow::ServerDef* server_def);
  void unsafe_arena_set_allocated_server_def(
      ::tensorflow::ServerDef* server_def);
  ::tensorflow::ServerDef* unsafe_arena_release_server_def();

  // int64 master_incarnation = 6;
  void clear_master_incarnation();
  ::PROTOBUF_NAMESPACE_ID::int64 master_incarnation() const;
  void set_master_incarnation(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool isolate_session_state = 3;
  void clear_isolate_session_state();
  bool isolate_session_state() const;
  void set_isolate_session_state(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.CreateWorkerSessionRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > cluster_device_attributes_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr master_task_;
  ::tensorflow::ServerDef* server_def_;
  ::PROTOBUF_NAMESPACE_ID::int64 master_incarnation_;
  bool isolate_session_state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CreateWorkerSessionResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateWorkerSessionResponse) */ {
 public:
  CreateWorkerSessionResponse();
  virtual ~CreateWorkerSessionResponse();

  CreateWorkerSessionResponse(const CreateWorkerSessionResponse& from);
  CreateWorkerSessionResponse(CreateWorkerSessionResponse&& from) noexcept
    : CreateWorkerSessionResponse() {
    *this = ::std::move(from);
  }

  inline CreateWorkerSessionResponse& operator=(const CreateWorkerSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateWorkerSessionResponse& operator=(CreateWorkerSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CreateWorkerSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateWorkerSessionResponse* internal_default_instance() {
    return reinterpret_cast<const CreateWorkerSessionResponse*>(
               &_CreateWorkerSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CreateWorkerSessionResponse& a, CreateWorkerSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateWorkerSessionResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateWorkerSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CreateWorkerSessionResponse* New() const final {
    return CreateMaybeMessage<CreateWorkerSessionResponse>(nullptr);
  }

  CreateWorkerSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CreateWorkerSessionResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CreateWorkerSessionResponse& from);
  void MergeFrom(const CreateWorkerSessionResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateWorkerSessionResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CreateWorkerSessionResponse";
  }
  protected:
  explicit CreateWorkerSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CreateWorkerSessionResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class DeleteWorkerSessionRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeleteWorkerSessionRequest) */ {
 public:
  DeleteWorkerSessionRequest();
  virtual ~DeleteWorkerSessionRequest();

  DeleteWorkerSessionRequest(const DeleteWorkerSessionRequest& from);
  DeleteWorkerSessionRequest(DeleteWorkerSessionRequest&& from) noexcept
    : DeleteWorkerSessionRequest() {
    *this = ::std::move(from);
  }

  inline DeleteWorkerSessionRequest& operator=(const DeleteWorkerSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteWorkerSessionRequest& operator=(DeleteWorkerSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeleteWorkerSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeleteWorkerSessionRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteWorkerSessionRequest*>(
               &_DeleteWorkerSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(DeleteWorkerSessionRequest& a, DeleteWorkerSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteWorkerSessionRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteWorkerSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeleteWorkerSessionRequest* New() const final {
    return CreateMaybeMessage<DeleteWorkerSessionRequest>(nullptr);
  }

  DeleteWorkerSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeleteWorkerSessionRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeleteWorkerSessionRequest& from);
  void MergeFrom(const DeleteWorkerSessionRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteWorkerSessionRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeleteWorkerSessionRequest";
  }
  protected:
  explicit DeleteWorkerSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // @@protoc_insertion_point(class_scope:tensorflow.DeleteWorkerSessionRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class DeleteWorkerSessionResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeleteWorkerSessionResponse) */ {
 public:
  DeleteWorkerSessionResponse();
  virtual ~DeleteWorkerSessionResponse();

  DeleteWorkerSessionResponse(const DeleteWorkerSessionResponse& from);
  DeleteWorkerSessionResponse(DeleteWorkerSessionResponse&& from) noexcept
    : DeleteWorkerSessionResponse() {
    *this = ::std::move(from);
  }

  inline DeleteWorkerSessionResponse& operator=(const DeleteWorkerSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteWorkerSessionResponse& operator=(DeleteWorkerSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeleteWorkerSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeleteWorkerSessionResponse* internal_default_instance() {
    return reinterpret_cast<const DeleteWorkerSessionResponse*>(
               &_DeleteWorkerSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(DeleteWorkerSessionResponse& a, DeleteWorkerSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteWorkerSessionResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteWorkerSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeleteWorkerSessionResponse* New() const final {
    return CreateMaybeMessage<DeleteWorkerSessionResponse>(nullptr);
  }

  DeleteWorkerSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeleteWorkerSessionResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeleteWorkerSessionResponse& from);
  void MergeFrom(const DeleteWorkerSessionResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteWorkerSessionResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeleteWorkerSessionResponse";
  }
  protected:
  explicit DeleteWorkerSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.DeleteWorkerSessionResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RegisterGraphRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisterGraphRequest) */ {
 public:
  RegisterGraphRequest();
  virtual ~RegisterGraphRequest();

  RegisterGraphRequest(const RegisterGraphRequest& from);
  RegisterGraphRequest(RegisterGraphRequest&& from) noexcept
    : RegisterGraphRequest() {
    *this = ::std::move(from);
  }

  inline RegisterGraphRequest& operator=(const RegisterGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisterGraphRequest& operator=(RegisterGraphRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RegisterGraphRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RegisterGraphRequest* internal_default_instance() {
    return reinterpret_cast<const RegisterGraphRequest*>(
               &_RegisterGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(RegisterGraphRequest& a, RegisterGraphRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisterGraphRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisterGraphRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RegisterGraphRequest* New() const final {
    return CreateMaybeMessage<RegisterGraphRequest>(nullptr);
  }

  RegisterGraphRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RegisterGraphRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RegisterGraphRequest& from);
  void MergeFrom(const RegisterGraphRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterGraphRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RegisterGraphRequest";
  }
  protected:
  explicit RegisterGraphRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
    kGraphDefFieldNumber = 2,
    kGraphOptionsFieldNumber = 4,
    kDebugOptionsFieldNumber = 5,
    kConfigProtoFieldNumber = 8,
    kCollectiveGraphKeyFieldNumber = 7,
    kCreateWorkerSessionCalledFieldNumber = 6,
    kHasControlFlowFieldNumber = 3,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // .tensorflow.GraphDef graph_def = 2;
  bool has_graph_def() const;
  void clear_graph_def();
  const ::tensorflow::GraphDef& graph_def() const;
  ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // .tensorflow.GraphOptions graph_options = 4;
  bool has_graph_options() const;
  void clear_graph_options();
  const ::tensorflow::GraphOptions& graph_options() const;
  ::tensorflow::GraphOptions* release_graph_options();
  ::tensorflow::GraphOptions* mutable_graph_options();
  void set_allocated_graph_options(::tensorflow::GraphOptions* graph_options);
  void unsafe_arena_set_allocated_graph_options(
      ::tensorflow::GraphOptions* graph_options);
  ::tensorflow::GraphOptions* unsafe_arena_release_graph_options();

  // .tensorflow.DebugOptions debug_options = 5;
  bool has_debug_options() const;
  void clear_debug_options();
  const ::tensorflow::DebugOptions& debug_options() const;
  ::tensorflow::DebugOptions* release_debug_options();
  ::tensorflow::DebugOptions* mutable_debug_options();
  void set_allocated_debug_options(::tensorflow::DebugOptions* debug_options);
  void unsafe_arena_set_allocated_debug_options(
      ::tensorflow::DebugOptions* debug_options);
  ::tensorflow::DebugOptions* unsafe_arena_release_debug_options();

  // .tensorflow.ConfigProto config_proto = 8;
  bool has_config_proto() const;
  void clear_config_proto();
  const ::tensorflow::ConfigProto& config_proto() const;
  ::tensorflow::ConfigProto* release_config_proto();
  ::tensorflow::ConfigProto* mutable_config_proto();
  void set_allocated_config_proto(::tensorflow::ConfigProto* config_proto);
  void unsafe_arena_set_allocated_config_proto(
      ::tensorflow::ConfigProto* config_proto);
  ::tensorflow::ConfigProto* unsafe_arena_release_config_proto();

  // int64 collective_graph_key = 7;
  void clear_collective_graph_key();
  ::PROTOBUF_NAMESPACE_ID::int64 collective_graph_key() const;
  void set_collective_graph_key(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool create_worker_session_called = 6;
  void clear_create_worker_session_called();
  bool create_worker_session_called() const;
  void set_create_worker_session_called(bool value);

  // bool has_control_flow = 3 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_has_control_flow();
  PROTOBUF_DEPRECATED bool has_control_flow() const;
  PROTOBUF_DEPRECATED void set_has_control_flow(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RegisterGraphRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::tensorflow::GraphDef* graph_def_;
  ::tensorflow::GraphOptions* graph_options_;
  ::tensorflow::DebugOptions* debug_options_;
  ::tensorflow::ConfigProto* config_proto_;
  ::PROTOBUF_NAMESPACE_ID::int64 collective_graph_key_;
  bool create_worker_session_called_;
  bool has_control_flow_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RegisterGraphResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisterGraphResponse) */ {
 public:
  RegisterGraphResponse();
  virtual ~RegisterGraphResponse();

  RegisterGraphResponse(const RegisterGraphResponse& from);
  RegisterGraphResponse(RegisterGraphResponse&& from) noexcept
    : RegisterGraphResponse() {
    *this = ::std::move(from);
  }

  inline RegisterGraphResponse& operator=(const RegisterGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisterGraphResponse& operator=(RegisterGraphResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RegisterGraphResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RegisterGraphResponse* internal_default_instance() {
    return reinterpret_cast<const RegisterGraphResponse*>(
               &_RegisterGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(RegisterGraphResponse& a, RegisterGraphResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisterGraphResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisterGraphResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RegisterGraphResponse* New() const final {
    return CreateMaybeMessage<RegisterGraphResponse>(nullptr);
  }

  RegisterGraphResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RegisterGraphResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RegisterGraphResponse& from);
  void MergeFrom(const RegisterGraphResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterGraphResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RegisterGraphResponse";
  }
  protected:
  explicit RegisterGraphResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGraphHandleFieldNumber = 1,
  };
  // string graph_handle = 1;
  void clear_graph_handle();
  const std::string& graph_handle() const;
  void set_graph_handle(const std::string& value);
  void set_graph_handle(std::string&& value);
  void set_graph_handle(const char* value);
  void set_graph_handle(const char* value, size_t size);
  std::string* mutable_graph_handle();
  std::string* release_graph_handle();
  void set_allocated_graph_handle(std::string* graph_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_graph_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_handle(
      std::string* graph_handle);

  // @@protoc_insertion_point(class_scope:tensorflow.RegisterGraphResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class DeregisterGraphRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeregisterGraphRequest) */ {
 public:
  DeregisterGraphRequest();
  virtual ~DeregisterGraphRequest();

  DeregisterGraphRequest(const DeregisterGraphRequest& from);
  DeregisterGraphRequest(DeregisterGraphRequest&& from) noexcept
    : DeregisterGraphRequest() {
    *this = ::std::move(from);
  }

  inline DeregisterGraphRequest& operator=(const DeregisterGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeregisterGraphRequest& operator=(DeregisterGraphRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeregisterGraphRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeregisterGraphRequest* internal_default_instance() {
    return reinterpret_cast<const DeregisterGraphRequest*>(
               &_DeregisterGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(DeregisterGraphRequest& a, DeregisterGraphRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeregisterGraphRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeregisterGraphRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeregisterGraphRequest* New() const final {
    return CreateMaybeMessage<DeregisterGraphRequest>(nullptr);
  }

  DeregisterGraphRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeregisterGraphRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeregisterGraphRequest& from);
  void MergeFrom(const DeregisterGraphRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeregisterGraphRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeregisterGraphRequest";
  }
  protected:
  explicit DeregisterGraphRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGraphHandleFieldNumber = 1,
    kSessionHandleFieldNumber = 2,
    kCreateWorkerSessionCalledFieldNumber = 3,
  };
  // string graph_handle = 1;
  void clear_graph_handle();
  const std::string& graph_handle() const;
  void set_graph_handle(const std::string& value);
  void set_graph_handle(std::string&& value);
  void set_graph_handle(const char* value);
  void set_graph_handle(const char* value, size_t size);
  std::string* mutable_graph_handle();
  std::string* release_graph_handle();
  void set_allocated_graph_handle(std::string* graph_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_graph_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_handle(
      std::string* graph_handle);

  // string session_handle = 2;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // bool create_worker_session_called = 3;
  void clear_create_worker_session_called();
  bool create_worker_session_called() const;
  void set_create_worker_session_called(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.DeregisterGraphRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_handle_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  bool create_worker_session_called_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class DeregisterGraphResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeregisterGraphResponse) */ {
 public:
  DeregisterGraphResponse();
  virtual ~DeregisterGraphResponse();

  DeregisterGraphResponse(const DeregisterGraphResponse& from);
  DeregisterGraphResponse(DeregisterGraphResponse&& from) noexcept
    : DeregisterGraphResponse() {
    *this = ::std::move(from);
  }

  inline DeregisterGraphResponse& operator=(const DeregisterGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeregisterGraphResponse& operator=(DeregisterGraphResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeregisterGraphResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeregisterGraphResponse* internal_default_instance() {
    return reinterpret_cast<const DeregisterGraphResponse*>(
               &_DeregisterGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(DeregisterGraphResponse& a, DeregisterGraphResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(DeregisterGraphResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeregisterGraphResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeregisterGraphResponse* New() const final {
    return CreateMaybeMessage<DeregisterGraphResponse>(nullptr);
  }

  DeregisterGraphResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeregisterGraphResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeregisterGraphResponse& from);
  void MergeFrom(const DeregisterGraphResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeregisterGraphResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeregisterGraphResponse";
  }
  protected:
  explicit DeregisterGraphResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.DeregisterGraphResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CleanupAllRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CleanupAllRequest) */ {
 public:
  CleanupAllRequest();
  virtual ~CleanupAllRequest();

  CleanupAllRequest(const CleanupAllRequest& from);
  CleanupAllRequest(CleanupAllRequest&& from) noexcept
    : CleanupAllRequest() {
    *this = ::std::move(from);
  }

  inline CleanupAllRequest& operator=(const CleanupAllRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CleanupAllRequest& operator=(CleanupAllRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CleanupAllRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CleanupAllRequest* internal_default_instance() {
    return reinterpret_cast<const CleanupAllRequest*>(
               &_CleanupAllRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(CleanupAllRequest& a, CleanupAllRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CleanupAllRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CleanupAllRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CleanupAllRequest* New() const final {
    return CreateMaybeMessage<CleanupAllRequest>(nullptr);
  }

  CleanupAllRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CleanupAllRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CleanupAllRequest& from);
  void MergeFrom(const CleanupAllRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupAllRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CleanupAllRequest";
  }
  protected:
  explicit CleanupAllRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContainerFieldNumber = 1,
  };
  // repeated string container = 1;
  int container_size() const;
  void clear_container();
  const std::string& container(int index) const;
  std::string* mutable_container(int index);
  void set_container(int index, const std::string& value);
  void set_container(int index, std::string&& value);
  void set_container(int index, const char* value);
  void set_container(int index, const char* value, size_t size);
  std::string* add_container();
  void add_container(const std::string& value);
  void add_container(std::string&& value);
  void add_container(const char* value);
  void add_container(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& container() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_container();

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupAllRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> container_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CleanupAllResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CleanupAllResponse) */ {
 public:
  CleanupAllResponse();
  virtual ~CleanupAllResponse();

  CleanupAllResponse(const CleanupAllResponse& from);
  CleanupAllResponse(CleanupAllResponse&& from) noexcept
    : CleanupAllResponse() {
    *this = ::std::move(from);
  }

  inline CleanupAllResponse& operator=(const CleanupAllResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CleanupAllResponse& operator=(CleanupAllResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CleanupAllResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CleanupAllResponse* internal_default_instance() {
    return reinterpret_cast<const CleanupAllResponse*>(
               &_CleanupAllResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(CleanupAllResponse& a, CleanupAllResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CleanupAllResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CleanupAllResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CleanupAllResponse* New() const final {
    return CreateMaybeMessage<CleanupAllResponse>(nullptr);
  }

  CleanupAllResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CleanupAllResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CleanupAllResponse& from);
  void MergeFrom(const CleanupAllResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupAllResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CleanupAllResponse";
  }
  protected:
  explicit CleanupAllResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupAllResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class ExecutorOpts :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExecutorOpts) */ {
 public:
  ExecutorOpts();
  virtual ~ExecutorOpts();

  ExecutorOpts(const ExecutorOpts& from);
  ExecutorOpts(ExecutorOpts&& from) noexcept
    : ExecutorOpts() {
    *this = ::std::move(from);
  }

  inline ExecutorOpts& operator=(const ExecutorOpts& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecutorOpts& operator=(ExecutorOpts&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecutorOpts& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecutorOpts* internal_default_instance() {
    return reinterpret_cast<const ExecutorOpts*>(
               &_ExecutorOpts_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(ExecutorOpts& a, ExecutorOpts& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecutorOpts* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExecutorOpts* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecutorOpts* New() const final {
    return CreateMaybeMessage<ExecutorOpts>(nullptr);
  }

  ExecutorOpts* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecutorOpts>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecutorOpts& from);
  void MergeFrom(const ExecutorOpts& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecutorOpts* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ExecutorOpts";
  }
  protected:
  explicit ExecutorOpts(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRecordCostsFieldNumber = 1,
    kRecordTimelineFieldNumber = 3,
    kRecordPartitionGraphsFieldNumber = 4,
    kReportTensorAllocationsUponOomFieldNumber = 5,
  };
  // bool record_costs = 1;
  void clear_record_costs();
  bool record_costs() const;
  void set_record_costs(bool value);

  // bool record_timeline = 3;
  void clear_record_timeline();
  bool record_timeline() const;
  void set_record_timeline(bool value);

  // bool record_partition_graphs = 4;
  void clear_record_partition_graphs();
  bool record_partition_graphs() const;
  void set_record_partition_graphs(bool value);

  // bool report_tensor_allocations_upon_oom = 5;
  void clear_report_tensor_allocations_upon_oom();
  bool report_tensor_allocations_upon_oom() const;
  void set_report_tensor_allocations_upon_oom(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.ExecutorOpts)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool record_costs_;
  bool record_timeline_;
  bool record_partition_graphs_;
  bool report_tensor_allocations_upon_oom_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RunGraphRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunGraphRequest) */ {
 public:
  RunGraphRequest();
  virtual ~RunGraphRequest();

  RunGraphRequest(const RunGraphRequest& from);
  RunGraphRequest(RunGraphRequest&& from) noexcept
    : RunGraphRequest() {
    *this = ::std::move(from);
  }

  inline RunGraphRequest& operator=(const RunGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunGraphRequest& operator=(RunGraphRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunGraphRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunGraphRequest* internal_default_instance() {
    return reinterpret_cast<const RunGraphRequest*>(
               &_RunGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RunGraphRequest& a, RunGraphRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RunGraphRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunGraphRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunGraphRequest* New() const final {
    return CreateMaybeMessage<RunGraphRequest>(nullptr);
  }

  RunGraphRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunGraphRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunGraphRequest& from);
  void MergeFrom(const RunGraphRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunGraphRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunGraphRequest";
  }
  protected:
  explicit RunGraphRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSendFieldNumber = 3,
    kRecvKeyFieldNumber = 4,
    kGraphHandleFieldNumber = 1,
    kSessionHandleFieldNumber = 8,
    kExecOptsFieldNumber = 5,
    kStepIdFieldNumber = 2,
    kRequestIdFieldNumber = 11,
    kCreateWorkerSessionCalledFieldNumber = 10,
    kIsPartialFieldNumber = 6,
    kIsLastPartialRunFieldNumber = 7,
    kStoreErrorsInResponseBodyFieldNumber = 9,
  };
  // repeated .tensorflow.NamedTensorProto send = 3;
  int send_size() const;
  void clear_send();
  ::tensorflow::NamedTensorProto* mutable_send(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_send();
  const ::tensorflow::NamedTensorProto& send(int index) const;
  ::tensorflow::NamedTensorProto* add_send();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      send() const;

  // repeated string recv_key = 4;
  int recv_key_size() const;
  void clear_recv_key();
  const std::string& recv_key(int index) const;
  std::string* mutable_recv_key(int index);
  void set_recv_key(int index, const std::string& value);
  void set_recv_key(int index, std::string&& value);
  void set_recv_key(int index, const char* value);
  void set_recv_key(int index, const char* value, size_t size);
  std::string* add_recv_key();
  void add_recv_key(const std::string& value);
  void add_recv_key(std::string&& value);
  void add_recv_key(const char* value);
  void add_recv_key(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& recv_key() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_recv_key();

  // string graph_handle = 1;
  void clear_graph_handle();
  const std::string& graph_handle() const;
  void set_graph_handle(const std::string& value);
  void set_graph_handle(std::string&& value);
  void set_graph_handle(const char* value);
  void set_graph_handle(const char* value, size_t size);
  std::string* mutable_graph_handle();
  std::string* release_graph_handle();
  void set_allocated_graph_handle(std::string* graph_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_graph_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_handle(
      std::string* graph_handle);

  // string session_handle = 8;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // .tensorflow.ExecutorOpts exec_opts = 5;
  bool has_exec_opts() const;
  void clear_exec_opts();
  const ::tensorflow::ExecutorOpts& exec_opts() const;
  ::tensorflow::ExecutorOpts* release_exec_opts();
  ::tensorflow::ExecutorOpts* mutable_exec_opts();
  void set_allocated_exec_opts(::tensorflow::ExecutorOpts* exec_opts);
  void unsafe_arena_set_allocated_exec_opts(
      ::tensorflow::ExecutorOpts* exec_opts);
  ::tensorflow::ExecutorOpts* unsafe_arena_release_exec_opts();

  // int64 step_id = 2;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 request_id = 11;
  void clear_request_id();
  ::PROTOBUF_NAMESPACE_ID::int64 request_id() const;
  void set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool create_worker_session_called = 10;
  void clear_create_worker_session_called();
  bool create_worker_session_called() const;
  void set_create_worker_session_called(bool value);

  // bool is_partial = 6;
  void clear_is_partial();
  bool is_partial() const;
  void set_is_partial(bool value);

  // bool is_last_partial_run = 7;
  void clear_is_last_partial_run();
  bool is_last_partial_run() const;
  void set_is_last_partial_run(bool value);

  // bool store_errors_in_response_body = 9;
  void clear_store_errors_in_response_body();
  bool store_errors_in_response_body() const;
  void set_store_errors_in_response_body(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunGraphRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto > send_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> recv_key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_handle_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::tensorflow::ExecutorOpts* exec_opts_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 request_id_;
  bool create_worker_session_called_;
  bool is_partial_;
  bool is_last_partial_run_;
  bool store_errors_in_response_body_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RunGraphResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunGraphResponse) */ {
 public:
  RunGraphResponse();
  virtual ~RunGraphResponse();

  RunGraphResponse(const RunGraphResponse& from);
  RunGraphResponse(RunGraphResponse&& from) noexcept
    : RunGraphResponse() {
    *this = ::std::move(from);
  }

  inline RunGraphResponse& operator=(const RunGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunGraphResponse& operator=(RunGraphResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunGraphResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunGraphResponse* internal_default_instance() {
    return reinterpret_cast<const RunGraphResponse*>(
               &_RunGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(RunGraphResponse& a, RunGraphResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RunGraphResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunGraphResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunGraphResponse* New() const final {
    return CreateMaybeMessage<RunGraphResponse>(nullptr);
  }

  RunGraphResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunGraphResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunGraphResponse& from);
  void MergeFrom(const RunGraphResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunGraphResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunGraphResponse";
  }
  protected:
  explicit RunGraphResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRecvFieldNumber = 1,
    kPartitionGraphFieldNumber = 4,
    kStatusErrorMessageFieldNumber = 6,
    kStepStatsFieldNumber = 2,
    kCostGraphFieldNumber = 3,
    kStatusCodeFieldNumber = 5,
  };
  // repeated .tensorflow.NamedTensorProto recv = 1;
  int recv_size() const;
  void clear_recv();
  ::tensorflow::NamedTensorProto* mutable_recv(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_recv();
  const ::tensorflow::NamedTensorProto& recv(int index) const;
  ::tensorflow::NamedTensorProto* add_recv();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      recv() const;

  // repeated .tensorflow.GraphDef partition_graph = 4;
  int partition_graph_size() const;
  void clear_partition_graph();
  ::tensorflow::GraphDef* mutable_partition_graph(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
      mutable_partition_graph();
  const ::tensorflow::GraphDef& partition_graph(int index) const;
  ::tensorflow::GraphDef* add_partition_graph();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
      partition_graph() const;

  // string status_error_message = 6;
  void clear_status_error_message();
  const std::string& status_error_message() const;
  void set_status_error_message(const std::string& value);
  void set_status_error_message(std::string&& value);
  void set_status_error_message(const char* value);
  void set_status_error_message(const char* value, size_t size);
  std::string* mutable_status_error_message();
  std::string* release_status_error_message();
  void set_allocated_status_error_message(std::string* status_error_message);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_status_error_message();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_status_error_message(
      std::string* status_error_message);

  // .tensorflow.StepStats step_stats = 2;
  bool has_step_stats() const;
  void clear_step_stats();
  const ::tensorflow::StepStats& step_stats() const;
  ::tensorflow::StepStats* release_step_stats();
  ::tensorflow::StepStats* mutable_step_stats();
  void set_allocated_step_stats(::tensorflow::StepStats* step_stats);
  void unsafe_arena_set_allocated_step_stats(
      ::tensorflow::StepStats* step_stats);
  ::tensorflow::StepStats* unsafe_arena_release_step_stats();

  // .tensorflow.CostGraphDef cost_graph = 3;
  bool has_cost_graph() const;
  void clear_cost_graph();
  const ::tensorflow::CostGraphDef& cost_graph() const;
  ::tensorflow::CostGraphDef* release_cost_graph();
  ::tensorflow::CostGraphDef* mutable_cost_graph();
  void set_allocated_cost_graph(::tensorflow::CostGraphDef* cost_graph);
  void unsafe_arena_set_allocated_cost_graph(
      ::tensorflow::CostGraphDef* cost_graph);
  ::tensorflow::CostGraphDef* unsafe_arena_release_cost_graph();

  // .tensorflow.error.Code status_code = 5;
  void clear_status_code();
  ::tensorflow::error::Code status_code() const;
  void set_status_code(::tensorflow::error::Code value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunGraphResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto > recv_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef > partition_graph_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_error_message_;
  ::tensorflow::StepStats* step_stats_;
  ::tensorflow::CostGraphDef* cost_graph_;
  int status_code_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CleanupGraphRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CleanupGraphRequest) */ {
 public:
  CleanupGraphRequest();
  virtual ~CleanupGraphRequest();

  CleanupGraphRequest(const CleanupGraphRequest& from);
  CleanupGraphRequest(CleanupGraphRequest&& from) noexcept
    : CleanupGraphRequest() {
    *this = ::std::move(from);
  }

  inline CleanupGraphRequest& operator=(const CleanupGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CleanupGraphRequest& operator=(CleanupGraphRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CleanupGraphRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CleanupGraphRequest* internal_default_instance() {
    return reinterpret_cast<const CleanupGraphRequest*>(
               &_CleanupGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(CleanupGraphRequest& a, CleanupGraphRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CleanupGraphRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CleanupGraphRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CleanupGraphRequest* New() const final {
    return CreateMaybeMessage<CleanupGraphRequest>(nullptr);
  }

  CleanupGraphRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CleanupGraphRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CleanupGraphRequest& from);
  void MergeFrom(const CleanupGraphRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupGraphRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CleanupGraphRequest";
  }
  protected:
  explicit CleanupGraphRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepIdFieldNumber = 1,
  };
  // int64 step_id = 1;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupGraphRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CleanupGraphResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CleanupGraphResponse) */ {
 public:
  CleanupGraphResponse();
  virtual ~CleanupGraphResponse();

  CleanupGraphResponse(const CleanupGraphResponse& from);
  CleanupGraphResponse(CleanupGraphResponse&& from) noexcept
    : CleanupGraphResponse() {
    *this = ::std::move(from);
  }

  inline CleanupGraphResponse& operator=(const CleanupGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CleanupGraphResponse& operator=(CleanupGraphResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CleanupGraphResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CleanupGraphResponse* internal_default_instance() {
    return reinterpret_cast<const CleanupGraphResponse*>(
               &_CleanupGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(CleanupGraphResponse& a, CleanupGraphResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CleanupGraphResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CleanupGraphResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CleanupGraphResponse* New() const final {
    return CreateMaybeMessage<CleanupGraphResponse>(nullptr);
  }

  CleanupGraphResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CleanupGraphResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CleanupGraphResponse& from);
  void MergeFrom(const CleanupGraphResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupGraphResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CleanupGraphResponse";
  }
  protected:
  explicit CleanupGraphResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupGraphResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RecvTensorRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvTensorRequest) */ {
 public:
  RecvTensorRequest();
  virtual ~RecvTensorRequest();

  RecvTensorRequest(const RecvTensorRequest& from);
  RecvTensorRequest(RecvTensorRequest&& from) noexcept
    : RecvTensorRequest() {
    *this = ::std::move(from);
  }

  inline RecvTensorRequest& operator=(const RecvTensorRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecvTensorRequest& operator=(RecvTensorRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecvTensorRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecvTensorRequest* internal_default_instance() {
    return reinterpret_cast<const RecvTensorRequest*>(
               &_RecvTensorRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(RecvTensorRequest& a, RecvTensorRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RecvTensorRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RecvTensorRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RecvTensorRequest* New() const final {
    return CreateMaybeMessage<RecvTensorRequest>(nullptr);
  }

  RecvTensorRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecvTensorRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecvTensorRequest& from);
  void MergeFrom(const RecvTensorRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvTensorRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RecvTensorRequest";
  }
  protected:
  explicit RecvTensorRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRendezvousKeyFieldNumber = 2,
    kClientLocalityFieldNumber = 4,
    kServerLocalityFieldNumber = 5,
    kTransportOptionsFieldNumber = 6,
    kStepIdFieldNumber = 1,
    kRequestIdFieldNumber = 7,
    kDmaOkFieldNumber = 3,
  };
  // string rendezvous_key = 2;
  void clear_rendezvous_key();
  const std::string& rendezvous_key() const;
  void set_rendezvous_key(const std::string& value);
  void set_rendezvous_key(std::string&& value);
  void set_rendezvous_key(const char* value);
  void set_rendezvous_key(const char* value, size_t size);
  std::string* mutable_rendezvous_key();
  std::string* release_rendezvous_key();
  void set_allocated_rendezvous_key(std::string* rendezvous_key);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_rendezvous_key();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_rendezvous_key(
      std::string* rendezvous_key);

  // .tensorflow.DeviceLocality client_locality = 4;
  bool has_client_locality() const;
  void clear_client_locality();
  const ::tensorflow::DeviceLocality& client_locality() const;
  ::tensorflow::DeviceLocality* release_client_locality();
  ::tensorflow::DeviceLocality* mutable_client_locality();
  void set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality);
  void unsafe_arena_set_allocated_client_locality(
      ::tensorflow::DeviceLocality* client_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_client_locality();

  // .tensorflow.DeviceLocality server_locality = 5;
  bool has_server_locality() const;
  void clear_server_locality();
  const ::tensorflow::DeviceLocality& server_locality() const;
  ::tensorflow::DeviceLocality* release_server_locality();
  ::tensorflow::DeviceLocality* mutable_server_locality();
  void set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality);
  void unsafe_arena_set_allocated_server_locality(
      ::tensorflow::DeviceLocality* server_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_server_locality();

  // .google.protobuf.Any transport_options = 6;
  bool has_transport_options() const;
  void clear_transport_options();
  const PROTOBUF_NAMESPACE_ID::Any& transport_options() const;
  PROTOBUF_NAMESPACE_ID::Any* release_transport_options();
  PROTOBUF_NAMESPACE_ID::Any* mutable_transport_options();
  void set_allocated_transport_options(PROTOBUF_NAMESPACE_ID::Any* transport_options);
  void unsafe_arena_set_allocated_transport_options(
      PROTOBUF_NAMESPACE_ID::Any* transport_options);
  PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_transport_options();

  // int64 step_id = 1;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 request_id = 7;
  void clear_request_id();
  ::PROTOBUF_NAMESPACE_ID::int64 request_id() const;
  void set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool dma_ok = 3;
  void clear_dma_ok();
  bool dma_ok() const;
  void set_dma_ok(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RecvTensorRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rendezvous_key_;
  ::tensorflow::DeviceLocality* client_locality_;
  ::tensorflow::DeviceLocality* server_locality_;
  PROTOBUF_NAMESPACE_ID::Any* transport_options_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 request_id_;
  bool dma_ok_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RecvTensorResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvTensorResponse) */ {
 public:
  RecvTensorResponse();
  virtual ~RecvTensorResponse();

  RecvTensorResponse(const RecvTensorResponse& from);
  RecvTensorResponse(RecvTensorResponse&& from) noexcept
    : RecvTensorResponse() {
    *this = ::std::move(from);
  }

  inline RecvTensorResponse& operator=(const RecvTensorResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecvTensorResponse& operator=(RecvTensorResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecvTensorResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecvTensorResponse* internal_default_instance() {
    return reinterpret_cast<const RecvTensorResponse*>(
               &_RecvTensorResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(RecvTensorResponse& a, RecvTensorResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RecvTensorResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RecvTensorResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RecvTensorResponse* New() const final {
    return CreateMaybeMessage<RecvTensorResponse>(nullptr);
  }

  RecvTensorResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecvTensorResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecvTensorResponse& from);
  void MergeFrom(const RecvTensorResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvTensorResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RecvTensorResponse";
  }
  protected:
  explicit RecvTensorResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorFieldNumber = 1,
    kTransportOptionsFieldNumber = 4,
    kSendStartMicrosFieldNumber = 3,
    kIsDeadFieldNumber = 2,
    kRequireAckFieldNumber = 5,
  };
  // .tensorflow.TensorProto tensor = 1;
  bool has_tensor() const;
  void clear_tensor();
  const ::tensorflow::TensorProto& tensor() const;
  ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorProto* tensor);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor();

  // .google.protobuf.Any transport_options = 4;
  bool has_transport_options() const;
  void clear_transport_options();
  const PROTOBUF_NAMESPACE_ID::Any& transport_options() const;
  PROTOBUF_NAMESPACE_ID::Any* release_transport_options();
  PROTOBUF_NAMESPACE_ID::Any* mutable_transport_options();
  void set_allocated_transport_options(PROTOBUF_NAMESPACE_ID::Any* transport_options);
  void unsafe_arena_set_allocated_transport_options(
      PROTOBUF_NAMESPACE_ID::Any* transport_options);
  PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_transport_options();

  // int64 send_start_micros = 3;
  void clear_send_start_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 send_start_micros() const;
  void set_send_start_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool is_dead = 2;
  void clear_is_dead();
  bool is_dead() const;
  void set_is_dead(bool value);

  // bool require_ack = 5;
  void clear_require_ack();
  bool require_ack() const;
  void set_require_ack(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RecvTensorResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorProto* tensor_;
  PROTOBUF_NAMESPACE_ID::Any* transport_options_;
  ::PROTOBUF_NAMESPACE_ID::int64 send_start_micros_;
  bool is_dead_;
  bool require_ack_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class MarkRecvFinishedRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MarkRecvFinishedRequest) */ {
 public:
  MarkRecvFinishedRequest();
  virtual ~MarkRecvFinishedRequest();

  MarkRecvFinishedRequest(const MarkRecvFinishedRequest& from);
  MarkRecvFinishedRequest(MarkRecvFinishedRequest&& from) noexcept
    : MarkRecvFinishedRequest() {
    *this = ::std::move(from);
  }

  inline MarkRecvFinishedRequest& operator=(const MarkRecvFinishedRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MarkRecvFinishedRequest& operator=(MarkRecvFinishedRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MarkRecvFinishedRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MarkRecvFinishedRequest* internal_default_instance() {
    return reinterpret_cast<const MarkRecvFinishedRequest*>(
               &_MarkRecvFinishedRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(MarkRecvFinishedRequest& a, MarkRecvFinishedRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MarkRecvFinishedRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MarkRecvFinishedRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MarkRecvFinishedRequest* New() const final {
    return CreateMaybeMessage<MarkRecvFinishedRequest>(nullptr);
  }

  MarkRecvFinishedRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MarkRecvFinishedRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MarkRecvFinishedRequest& from);
  void MergeFrom(const MarkRecvFinishedRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MarkRecvFinishedRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MarkRecvFinishedRequest";
  }
  protected:
  explicit MarkRecvFinishedRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRequestIdFieldNumber = 1,
  };
  // int64 request_id = 1;
  void clear_request_id();
  ::PROTOBUF_NAMESPACE_ID::int64 request_id() const;
  void set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MarkRecvFinishedRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 request_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class MarkRecvFinishedResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MarkRecvFinishedResponse) */ {
 public:
  MarkRecvFinishedResponse();
  virtual ~MarkRecvFinishedResponse();

  MarkRecvFinishedResponse(const MarkRecvFinishedResponse& from);
  MarkRecvFinishedResponse(MarkRecvFinishedResponse&& from) noexcept
    : MarkRecvFinishedResponse() {
    *this = ::std::move(from);
  }

  inline MarkRecvFinishedResponse& operator=(const MarkRecvFinishedResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MarkRecvFinishedResponse& operator=(MarkRecvFinishedResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MarkRecvFinishedResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MarkRecvFinishedResponse* internal_default_instance() {
    return reinterpret_cast<const MarkRecvFinishedResponse*>(
               &_MarkRecvFinishedResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(MarkRecvFinishedResponse& a, MarkRecvFinishedResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MarkRecvFinishedResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MarkRecvFinishedResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MarkRecvFinishedResponse* New() const final {
    return CreateMaybeMessage<MarkRecvFinishedResponse>(nullptr);
  }

  MarkRecvFinishedResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MarkRecvFinishedResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MarkRecvFinishedResponse& from);
  void MergeFrom(const MarkRecvFinishedResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MarkRecvFinishedResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MarkRecvFinishedResponse";
  }
  protected:
  explicit MarkRecvFinishedResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.MarkRecvFinishedResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class LoggingRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LoggingRequest) */ {
 public:
  LoggingRequest();
  virtual ~LoggingRequest();

  LoggingRequest(const LoggingRequest& from);
  LoggingRequest(LoggingRequest&& from) noexcept
    : LoggingRequest() {
    *this = ::std::move(from);
  }

  inline LoggingRequest& operator=(const LoggingRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoggingRequest& operator=(LoggingRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LoggingRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LoggingRequest* internal_default_instance() {
    return reinterpret_cast<const LoggingRequest*>(
               &_LoggingRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(LoggingRequest& a, LoggingRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(LoggingRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoggingRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LoggingRequest* New() const final {
    return CreateMaybeMessage<LoggingRequest>(nullptr);
  }

  LoggingRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LoggingRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LoggingRequest& from);
  void MergeFrom(const LoggingRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoggingRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LoggingRequest";
  }
  protected:
  explicit LoggingRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFetchStepIdFieldNumber = 3,
    kEnableRpcLoggingFieldNumber = 1,
    kDisableRpcLoggingFieldNumber = 4,
    kClearFieldNumber = 2,
  };
  // repeated int64 fetch_step_id = 3;
  int fetch_step_id_size() const;
  void clear_fetch_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 fetch_step_id(int index) const;
  void set_fetch_step_id(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_fetch_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      fetch_step_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_fetch_step_id();

  // bool enable_rpc_logging = 1;
  void clear_enable_rpc_logging();
  bool enable_rpc_logging() const;
  void set_enable_rpc_logging(bool value);

  // bool disable_rpc_logging = 4;
  void clear_disable_rpc_logging();
  bool disable_rpc_logging() const;
  void set_disable_rpc_logging(bool value);

  // bool clear = 2;
  void clear_clear();
  bool clear() const;
  void set_clear(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.LoggingRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > fetch_step_id_;
  mutable std::atomic<int> _fetch_step_id_cached_byte_size_;
  bool enable_rpc_logging_;
  bool disable_rpc_logging_;
  bool clear_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class LabeledStepStats :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LabeledStepStats) */ {
 public:
  LabeledStepStats();
  virtual ~LabeledStepStats();

  LabeledStepStats(const LabeledStepStats& from);
  LabeledStepStats(LabeledStepStats&& from) noexcept
    : LabeledStepStats() {
    *this = ::std::move(from);
  }

  inline LabeledStepStats& operator=(const LabeledStepStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline LabeledStepStats& operator=(LabeledStepStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LabeledStepStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LabeledStepStats* internal_default_instance() {
    return reinterpret_cast<const LabeledStepStats*>(
               &_LabeledStepStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(LabeledStepStats& a, LabeledStepStats& b) {
    a.Swap(&b);
  }
  inline void Swap(LabeledStepStats* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LabeledStepStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LabeledStepStats* New() const final {
    return CreateMaybeMessage<LabeledStepStats>(nullptr);
  }

  LabeledStepStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LabeledStepStats>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LabeledStepStats& from);
  void MergeFrom(const LabeledStepStats& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LabeledStepStats* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LabeledStepStats";
  }
  protected:
  explicit LabeledStepStats(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepStatsFieldNumber = 2,
    kStepIdFieldNumber = 1,
  };
  // .tensorflow.StepStats step_stats = 2;
  bool has_step_stats() const;
  void clear_step_stats();
  const ::tensorflow::StepStats& step_stats() const;
  ::tensorflow::StepStats* release_step_stats();
  ::tensorflow::StepStats* mutable_step_stats();
  void set_allocated_step_stats(::tensorflow::StepStats* step_stats);
  void unsafe_arena_set_allocated_step_stats(
      ::tensorflow::StepStats* step_stats);
  ::tensorflow::StepStats* unsafe_arena_release_step_stats();

  // int64 step_id = 1;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.LabeledStepStats)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::StepStats* step_stats_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class LoggingResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LoggingResponse) */ {
 public:
  LoggingResponse();
  virtual ~LoggingResponse();

  LoggingResponse(const LoggingResponse& from);
  LoggingResponse(LoggingResponse&& from) noexcept
    : LoggingResponse() {
    *this = ::std::move(from);
  }

  inline LoggingResponse& operator=(const LoggingResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoggingResponse& operator=(LoggingResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LoggingResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LoggingResponse* internal_default_instance() {
    return reinterpret_cast<const LoggingResponse*>(
               &_LoggingResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(LoggingResponse& a, LoggingResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(LoggingResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoggingResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LoggingResponse* New() const final {
    return CreateMaybeMessage<LoggingResponse>(nullptr);
  }

  LoggingResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LoggingResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LoggingResponse& from);
  void MergeFrom(const LoggingResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoggingResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LoggingResponse";
  }
  protected:
  explicit LoggingResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepFieldNumber = 1,
  };
  // repeated .tensorflow.LabeledStepStats step = 1;
  int step_size() const;
  void clear_step();
  ::tensorflow::LabeledStepStats* mutable_step(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::LabeledStepStats >*
      mutable_step();
  const ::tensorflow::LabeledStepStats& step(int index) const;
  ::tensorflow::LabeledStepStats* add_step();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::LabeledStepStats >&
      step() const;

  // @@protoc_insertion_point(class_scope:tensorflow.LoggingResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::LabeledStepStats > step_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class TraceOpts :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TraceOpts) */ {
 public:
  TraceOpts();
  virtual ~TraceOpts();

  TraceOpts(const TraceOpts& from);
  TraceOpts(TraceOpts&& from) noexcept
    : TraceOpts() {
    *this = ::std::move(from);
  }

  inline TraceOpts& operator=(const TraceOpts& from) {
    CopyFrom(from);
    return *this;
  }
  inline TraceOpts& operator=(TraceOpts&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TraceOpts& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TraceOpts* internal_default_instance() {
    return reinterpret_cast<const TraceOpts*>(
               &_TraceOpts_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(TraceOpts& a, TraceOpts& b) {
    a.Swap(&b);
  }
  inline void Swap(TraceOpts* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TraceOpts* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TraceOpts* New() const final {
    return CreateMaybeMessage<TraceOpts>(nullptr);
  }

  TraceOpts* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TraceOpts>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TraceOpts& from);
  void MergeFrom(const TraceOpts& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TraceOpts* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TraceOpts";
  }
  protected:
  explicit TraceOpts(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDurationFieldNumber = 1,
    kUseStepProfilerFieldNumber = 2,
    kUseKernelProfilerFieldNumber = 3,
    kUseExtendedProfilerFieldNumber = 4,
    kUseGpuProfilerFieldNumber = 5,
    kUseSampleProfilerFieldNumber = 6,
  };
  // double duration = 1;
  void clear_duration();
  double duration() const;
  void set_duration(double value);

  // bool use_step_profiler = 2;
  void clear_use_step_profiler();
  bool use_step_profiler() const;
  void set_use_step_profiler(bool value);

  // bool use_kernel_profiler = 3;
  void clear_use_kernel_profiler();
  bool use_kernel_profiler() const;
  void set_use_kernel_profiler(bool value);

  // bool use_extended_profiler = 4;
  void clear_use_extended_profiler();
  bool use_extended_profiler() const;
  void set_use_extended_profiler(bool value);

  // bool use_gpu_profiler = 5;
  void clear_use_gpu_profiler();
  bool use_gpu_profiler() const;
  void set_use_gpu_profiler(bool value);

  // bool use_sample_profiler = 6;
  void clear_use_sample_profiler();
  bool use_sample_profiler() const;
  void set_use_sample_profiler(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.TraceOpts)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double duration_;
  bool use_step_profiler_;
  bool use_kernel_profiler_;
  bool use_extended_profiler_;
  bool use_gpu_profiler_;
  bool use_sample_profiler_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class TracingRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TracingRequest) */ {
 public:
  TracingRequest();
  virtual ~TracingRequest();

  TracingRequest(const TracingRequest& from);
  TracingRequest(TracingRequest&& from) noexcept
    : TracingRequest() {
    *this = ::std::move(from);
  }

  inline TracingRequest& operator=(const TracingRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TracingRequest& operator=(TracingRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TracingRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TracingRequest* internal_default_instance() {
    return reinterpret_cast<const TracingRequest*>(
               &_TracingRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(TracingRequest& a, TracingRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TracingRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TracingRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TracingRequest* New() const final {
    return CreateMaybeMessage<TracingRequest>(nullptr);
  }

  TracingRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TracingRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TracingRequest& from);
  void MergeFrom(const TracingRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TracingRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TracingRequest";
  }
  protected:
  explicit TracingRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOptionsFieldNumber = 1,
  };
  // .tensorflow.TraceOpts options = 1;
  bool has_options() const;
  void clear_options();
  const ::tensorflow::TraceOpts& options() const;
  ::tensorflow::TraceOpts* release_options();
  ::tensorflow::TraceOpts* mutable_options();
  void set_allocated_options(::tensorflow::TraceOpts* options);
  void unsafe_arena_set_allocated_options(
      ::tensorflow::TraceOpts* options);
  ::tensorflow::TraceOpts* unsafe_arena_release_options();

  // @@protoc_insertion_point(class_scope:tensorflow.TracingRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TraceOpts* options_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class TracingResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TracingResponse) */ {
 public:
  TracingResponse();
  virtual ~TracingResponse();

  TracingResponse(const TracingResponse& from);
  TracingResponse(TracingResponse&& from) noexcept
    : TracingResponse() {
    *this = ::std::move(from);
  }

  inline TracingResponse& operator=(const TracingResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TracingResponse& operator=(TracingResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TracingResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TracingResponse* internal_default_instance() {
    return reinterpret_cast<const TracingResponse*>(
               &_TracingResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  friend void swap(TracingResponse& a, TracingResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TracingResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TracingResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TracingResponse* New() const final {
    return CreateMaybeMessage<TracingResponse>(nullptr);
  }

  TracingResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TracingResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TracingResponse& from);
  void MergeFrom(const TracingResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TracingResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TracingResponse";
  }
  protected:
  explicit TracingResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.TracingResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RecvBufRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvBufRequest) */ {
 public:
  RecvBufRequest();
  virtual ~RecvBufRequest();

  RecvBufRequest(const RecvBufRequest& from);
  RecvBufRequest(RecvBufRequest&& from) noexcept
    : RecvBufRequest() {
    *this = ::std::move(from);
  }

  inline RecvBufRequest& operator=(const RecvBufRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecvBufRequest& operator=(RecvBufRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecvBufRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecvBufRequest* internal_default_instance() {
    return reinterpret_cast<const RecvBufRequest*>(
               &_RecvBufRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  friend void swap(RecvBufRequest& a, RecvBufRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RecvBufRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RecvBufRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RecvBufRequest* New() const final {
    return CreateMaybeMessage<RecvBufRequest>(nullptr);
  }

  RecvBufRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecvBufRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecvBufRequest& from);
  void MergeFrom(const RecvBufRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvBufRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RecvBufRequest";
  }
  protected:
  explicit RecvBufRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBufRendezvousKeyFieldNumber = 2,
    kSrcDeviceFieldNumber = 8,
    kDstDeviceFieldNumber = 9,
    kClientLocalityFieldNumber = 5,
    kServerLocalityFieldNumber = 6,
    kTransportOptionsFieldNumber = 7,
    kStepIdFieldNumber = 1,
    kNumBytesFieldNumber = 3,
    kBufPtrFieldNumber = 4,
    kRequestIdFieldNumber = 10,
    kSrcIncarnationFieldNumber = 11,
  };
  // string buf_rendezvous_key = 2;
  void clear_buf_rendezvous_key();
  const std::string& buf_rendezvous_key() const;
  void set_buf_rendezvous_key(const std::string& value);
  void set_buf_rendezvous_key(std::string&& value);
  void set_buf_rendezvous_key(const char* value);
  void set_buf_rendezvous_key(const char* value, size_t size);
  std::string* mutable_buf_rendezvous_key();
  std::string* release_buf_rendezvous_key();
  void set_allocated_buf_rendezvous_key(std::string* buf_rendezvous_key);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_buf_rendezvous_key();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_buf_rendezvous_key(
      std::string* buf_rendezvous_key);

  // string src_device = 8;
  void clear_src_device();
  const std::string& src_device() const;
  void set_src_device(const std::string& value);
  void set_src_device(std::string&& value);
  void set_src_device(const char* value);
  void set_src_device(const char* value, size_t size);
  std::string* mutable_src_device();
  std::string* release_src_device();
  void set_allocated_src_device(std::string* src_device);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_src_device();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_src_device(
      std::string* src_device);

  // string dst_device = 9;
  void clear_dst_device();
  const std::string& dst_device() const;
  void set_dst_device(const std::string& value);
  void set_dst_device(std::string&& value);
  void set_dst_device(const char* value);
  void set_dst_device(const char* value, size_t size);
  std::string* mutable_dst_device();
  std::string* release_dst_device();
  void set_allocated_dst_device(std::string* dst_device);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_dst_device();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_dst_device(
      std::string* dst_device);

  // .tensorflow.DeviceLocality client_locality = 5;
  bool has_client_locality() const;
  void clear_client_locality();
  const ::tensorflow::DeviceLocality& client_locality() const;
  ::tensorflow::DeviceLocality* release_client_locality();
  ::tensorflow::DeviceLocality* mutable_client_locality();
  void set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality);
  void unsafe_arena_set_allocated_client_locality(
      ::tensorflow::DeviceLocality* client_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_client_locality();

  // .tensorflow.DeviceLocality server_locality = 6;
  bool has_server_locality() const;
  void clear_server_locality();
  const ::tensorflow::DeviceLocality& server_locality() const;
  ::tensorflow::DeviceLocality* release_server_locality();
  ::tensorflow::DeviceLocality* mutable_server_locality();
  void set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality);
  void unsafe_arena_set_allocated_server_locality(
      ::tensorflow::DeviceLocality* server_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_server_locality();

  // .google.protobuf.Any transport_options = 7;
  bool has_transport_options() const;
  void clear_transport_options();
  const PROTOBUF_NAMESPACE_ID::Any& transport_options() const;
  PROTOBUF_NAMESPACE_ID::Any* release_transport_options();
  PROTOBUF_NAMESPACE_ID::Any* mutable_transport_options();
  void set_allocated_transport_options(PROTOBUF_NAMESPACE_ID::Any* transport_options);
  void unsafe_arena_set_allocated_transport_options(
      PROTOBUF_NAMESPACE_ID::Any* transport_options);
  PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_transport_options();

  // int64 step_id = 1;
  void clear_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 step_id() const;
  void set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 num_bytes = 3;
  void clear_num_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 num_bytes() const;
  void set_num_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // fixed64 buf_ptr = 4;
  void clear_buf_ptr();
  ::PROTOBUF_NAMESPACE_ID::uint64 buf_ptr() const;
  void set_buf_ptr(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int64 request_id = 10;
  void clear_request_id();
  ::PROTOBUF_NAMESPACE_ID::int64 request_id() const;
  void set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // uint64 src_incarnation = 11;
  void clear_src_incarnation();
  ::PROTOBUF_NAMESPACE_ID::uint64 src_incarnation() const;
  void set_src_incarnation(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.RecvBufRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr buf_rendezvous_key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr src_device_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dst_device_;
  ::tensorflow::DeviceLocality* client_locality_;
  ::tensorflow::DeviceLocality* server_locality_;
  PROTOBUF_NAMESPACE_ID::Any* transport_options_;
  ::PROTOBUF_NAMESPACE_ID::int64 step_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_bytes_;
  ::PROTOBUF_NAMESPACE_ID::uint64 buf_ptr_;
  ::PROTOBUF_NAMESPACE_ID::int64 request_id_;
  ::PROTOBUF_NAMESPACE_ID::uint64 src_incarnation_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RecvBufResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvBufResponse) */ {
 public:
  RecvBufResponse();
  virtual ~RecvBufResponse();

  RecvBufResponse(const RecvBufResponse& from);
  RecvBufResponse(RecvBufResponse&& from) noexcept
    : RecvBufResponse() {
    *this = ::std::move(from);
  }

  inline RecvBufResponse& operator=(const RecvBufResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecvBufResponse& operator=(RecvBufResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecvBufResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecvBufResponse* internal_default_instance() {
    return reinterpret_cast<const RecvBufResponse*>(
               &_RecvBufResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  friend void swap(RecvBufResponse& a, RecvBufResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RecvBufResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RecvBufResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RecvBufResponse* New() const final {
    return CreateMaybeMessage<RecvBufResponse>(nullptr);
  }

  RecvBufResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecvBufResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecvBufResponse& from);
  void MergeFrom(const RecvBufResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvBufResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RecvBufResponse";
  }
  protected:
  explicit RecvBufResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTransportOptionsFieldNumber = 4,
    kBufPtrFieldNumber = 1,
    kNumBytesFieldNumber = 2,
    kSendStartMicrosFieldNumber = 5,
    kIsDeadFieldNumber = 3,
    kRequireAckFieldNumber = 6,
  };
  // .google.protobuf.Any transport_options = 4;
  bool has_transport_options() const;
  void clear_transport_options();
  const PROTOBUF_NAMESPACE_ID::Any& transport_options() const;
  PROTOBUF_NAMESPACE_ID::Any* release_transport_options();
  PROTOBUF_NAMESPACE_ID::Any* mutable_transport_options();
  void set_allocated_transport_options(PROTOBUF_NAMESPACE_ID::Any* transport_options);
  void unsafe_arena_set_allocated_transport_options(
      PROTOBUF_NAMESPACE_ID::Any* transport_options);
  PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_transport_options();

  // fixed64 buf_ptr = 1;
  void clear_buf_ptr();
  ::PROTOBUF_NAMESPACE_ID::uint64 buf_ptr() const;
  void set_buf_ptr(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int64 num_bytes = 2;
  void clear_num_bytes();
  ::PROTOBUF_NAMESPACE_ID::int64 num_bytes() const;
  void set_num_bytes(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 send_start_micros = 5;
  void clear_send_start_micros();
  ::PROTOBUF_NAMESPACE_ID::int64 send_start_micros() const;
  void set_send_start_micros(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool is_dead = 3;
  void clear_is_dead();
  bool is_dead() const;
  void set_is_dead(bool value);

  // bool require_ack = 6;
  void clear_require_ack();
  bool require_ack() const;
  void set_require_ack(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RecvBufResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  PROTOBUF_NAMESPACE_ID::Any* transport_options_;
  ::PROTOBUF_NAMESPACE_ID::uint64 buf_ptr_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_bytes_;
  ::PROTOBUF_NAMESPACE_ID::int64 send_start_micros_;
  bool is_dead_;
  bool require_ack_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CompleteGroupRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteGroupRequest) */ {
 public:
  CompleteGroupRequest();
  virtual ~CompleteGroupRequest();

  CompleteGroupRequest(const CompleteGroupRequest& from);
  CompleteGroupRequest(CompleteGroupRequest&& from) noexcept
    : CompleteGroupRequest() {
    *this = ::std::move(from);
  }

  inline CompleteGroupRequest& operator=(const CompleteGroupRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompleteGroupRequest& operator=(CompleteGroupRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CompleteGroupRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompleteGroupRequest* internal_default_instance() {
    return reinterpret_cast<const CompleteGroupRequest*>(
               &_CompleteGroupRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    29;

  friend void swap(CompleteGroupRequest& a, CompleteGroupRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CompleteGroupRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompleteGroupRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CompleteGroupRequest* New() const final {
    return CreateMaybeMessage<CompleteGroupRequest>(nullptr);
  }

  CompleteGroupRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CompleteGroupRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CompleteGroupRequest& from);
  void MergeFrom(const CompleteGroupRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteGroupRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CompleteGroupRequest";
  }
  protected:
  explicit CompleteGroupRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceTypeFieldNumber = 3,
    kDeviceAttributesFieldNumber = 6,
    kGroupKeyFieldNumber = 1,
    kGroupSizeFieldNumber = 2,
    kCollectiveTypeFieldNumber = 5,
  };
  // string device_type = 3;
  void clear_device_type();
  const std::string& device_type() const;
  void set_device_type(const std::string& value);
  void set_device_type(std::string&& value);
  void set_device_type(const char* value);
  void set_device_type(const char* value, size_t size);
  std::string* mutable_device_type();
  std::string* release_device_type();
  void set_allocated_device_type(std::string* device_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_type(
      std::string* device_type);

  // .tensorflow.DeviceAttributes device_attributes = 6;
  bool has_device_attributes() const;
  void clear_device_attributes();
  const ::tensorflow::DeviceAttributes& device_attributes() const;
  ::tensorflow::DeviceAttributes* release_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_device_attributes();
  void set_allocated_device_attributes(::tensorflow::DeviceAttributes* device_attributes);
  void unsafe_arena_set_allocated_device_attributes(
      ::tensorflow::DeviceAttributes* device_attributes);
  ::tensorflow::DeviceAttributes* unsafe_arena_release_device_attributes();

  // int32 group_key = 1;
  void clear_group_key();
  ::PROTOBUF_NAMESPACE_ID::int32 group_key() const;
  void set_group_key(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 group_size = 2;
  void clear_group_size();
  ::PROTOBUF_NAMESPACE_ID::int32 group_size() const;
  void set_group_size(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 collective_type = 5;
  void clear_collective_type();
  ::PROTOBUF_NAMESPACE_ID::int32 collective_type() const;
  void set_collective_type(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteGroupRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_type_;
  ::tensorflow::DeviceAttributes* device_attributes_;
  ::PROTOBUF_NAMESPACE_ID::int32 group_key_;
  ::PROTOBUF_NAMESPACE_ID::int32 group_size_;
  ::PROTOBUF_NAMESPACE_ID::int32 collective_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CompleteGroupResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteGroupResponse) */ {
 public:
  CompleteGroupResponse();
  virtual ~CompleteGroupResponse();

  CompleteGroupResponse(const CompleteGroupResponse& from);
  CompleteGroupResponse(CompleteGroupResponse&& from) noexcept
    : CompleteGroupResponse() {
    *this = ::std::move(from);
  }

  inline CompleteGroupResponse& operator=(const CompleteGroupResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompleteGroupResponse& operator=(CompleteGroupResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CompleteGroupResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompleteGroupResponse* internal_default_instance() {
    return reinterpret_cast<const CompleteGroupResponse*>(
               &_CompleteGroupResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    30;

  friend void swap(CompleteGroupResponse& a, CompleteGroupResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CompleteGroupResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompleteGroupResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CompleteGroupResponse* New() const final {
    return CreateMaybeMessage<CompleteGroupResponse>(nullptr);
  }

  CompleteGroupResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CompleteGroupResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CompleteGroupResponse& from);
  void MergeFrom(const CompleteGroupResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteGroupResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CompleteGroupResponse";
  }
  protected:
  explicit CompleteGroupResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceAttributesFieldNumber = 8,
    kDeviceTypeFieldNumber = 3,
    kCommunicatorKeyFieldNumber = 7,
    kGroupKeyFieldNumber = 1,
    kGroupSizeFieldNumber = 2,
    kNumTasksFieldNumber = 4,
  };
  // repeated .tensorflow.DeviceAttributes device_attributes = 8;
  int device_attributes_size() const;
  void clear_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_device_attributes();
  const ::tensorflow::DeviceAttributes& device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      device_attributes() const;

  // string device_type = 3;
  void clear_device_type();
  const std::string& device_type() const;
  void set_device_type(const std::string& value);
  void set_device_type(std::string&& value);
  void set_device_type(const char* value);
  void set_device_type(const char* value, size_t size);
  std::string* mutable_device_type();
  std::string* release_device_type();
  void set_allocated_device_type(std::string* device_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_type(
      std::string* device_type);

  // bytes communicator_key = 7;
  void clear_communicator_key();
  const std::string& communicator_key() const;
  void set_communicator_key(const std::string& value);
  void set_communicator_key(std::string&& value);
  void set_communicator_key(const char* value);
  void set_communicator_key(const void* value, size_t size);
  std::string* mutable_communicator_key();
  std::string* release_communicator_key();
  void set_allocated_communicator_key(std::string* communicator_key);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_communicator_key();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_communicator_key(
      std::string* communicator_key);

  // int32 group_key = 1;
  void clear_group_key();
  ::PROTOBUF_NAMESPACE_ID::int32 group_key() const;
  void set_group_key(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 group_size = 2;
  void clear_group_size();
  ::PROTOBUF_NAMESPACE_ID::int32 group_size() const;
  void set_group_size(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 num_tasks = 4;
  void clear_num_tasks();
  ::PROTOBUF_NAMESPACE_ID::int32 num_tasks() const;
  void set_num_tasks(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteGroupResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > device_attributes_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr communicator_key_;
  ::PROTOBUF_NAMESPACE_ID::int32 group_key_;
  ::PROTOBUF_NAMESPACE_ID::int32 group_size_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_tasks_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CompleteInstanceRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteInstanceRequest) */ {
 public:
  CompleteInstanceRequest();
  virtual ~CompleteInstanceRequest();

  CompleteInstanceRequest(const CompleteInstanceRequest& from);
  CompleteInstanceRequest(CompleteInstanceRequest&& from) noexcept
    : CompleteInstanceRequest() {
    *this = ::std::move(from);
  }

  inline CompleteInstanceRequest& operator=(const CompleteInstanceRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompleteInstanceRequest& operator=(CompleteInstanceRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CompleteInstanceRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompleteInstanceRequest* internal_default_instance() {
    return reinterpret_cast<const CompleteInstanceRequest*>(
               &_CompleteInstanceRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    31;

  friend void swap(CompleteInstanceRequest& a, CompleteInstanceRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CompleteInstanceRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompleteInstanceRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CompleteInstanceRequest* New() const final {
    return CreateMaybeMessage<CompleteInstanceRequest>(nullptr);
  }

  CompleteInstanceRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CompleteInstanceRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CompleteInstanceRequest& from);
  void MergeFrom(const CompleteInstanceRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteInstanceRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CompleteInstanceRequest";
  }
  protected:
  explicit CompleteInstanceRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSubdivOffsetFieldNumber = 9,
    kNameFieldNumber = 1,
    kDeviceTypeFieldNumber = 8,
    kDeviceFieldNumber = 10,
    kShapeFieldNumber = 4,
    kTypeFieldNumber = 2,
    kDataTypeFieldNumber = 3,
    kGroupKeyFieldNumber = 5,
    kGroupSizeFieldNumber = 6,
    kInstanceKeyFieldNumber = 7,
    kIsSourceFieldNumber = 11,
  };
  // repeated int32 subdiv_offset = 9;
  int subdiv_offset_size() const;
  void clear_subdiv_offset();
  ::PROTOBUF_NAMESPACE_ID::int32 subdiv_offset(int index) const;
  void set_subdiv_offset(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_subdiv_offset(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      subdiv_offset() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_subdiv_offset();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string device_type = 8;
  void clear_device_type();
  const std::string& device_type() const;
  void set_device_type(const std::string& value);
  void set_device_type(std::string&& value);
  void set_device_type(const char* value);
  void set_device_type(const char* value, size_t size);
  std::string* mutable_device_type();
  std::string* release_device_type();
  void set_allocated_device_type(std::string* device_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_type(
      std::string* device_type);

  // string device = 10;
  void clear_device();
  const std::string& device() const;
  void set_device(const std::string& value);
  void set_device(std::string&& value);
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  std::string* mutable_device();
  std::string* release_device();
  void set_allocated_device(std::string* device);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device(
      std::string* device);

  // .tensorflow.TensorShapeProto shape = 4;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // int32 type = 2;
  void clear_type();
  ::PROTOBUF_NAMESPACE_ID::int32 type() const;
  void set_type(::PROTOBUF_NAMESPACE_ID::int32 value);

  // .tensorflow.DataType data_type = 3;
  void clear_data_type();
  ::tensorflow::DataType data_type() const;
  void set_data_type(::tensorflow::DataType value);

  // int32 group_key = 5;
  void clear_group_key();
  ::PROTOBUF_NAMESPACE_ID::int32 group_key() const;
  void set_group_key(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 group_size = 6;
  void clear_group_size();
  ::PROTOBUF_NAMESPACE_ID::int32 group_size() const;
  void set_group_size(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 instance_key = 7;
  void clear_instance_key();
  ::PROTOBUF_NAMESPACE_ID::int32 instance_key() const;
  void set_instance_key(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool is_source = 11;
  void clear_is_source();
  bool is_source() const;
  void set_is_source(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteInstanceRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > subdiv_offset_;
  mutable std::atomic<int> _subdiv_offset_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
  ::tensorflow::TensorShapeProto* shape_;
  ::PROTOBUF_NAMESPACE_ID::int32 type_;
  int data_type_;
  ::PROTOBUF_NAMESPACE_ID::int32 group_key_;
  ::PROTOBUF_NAMESPACE_ID::int32 group_size_;
  ::PROTOBUF_NAMESPACE_ID::int32 instance_key_;
  bool is_source_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CompleteInstanceResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteInstanceResponse) */ {
 public:
  CompleteInstanceResponse();
  virtual ~CompleteInstanceResponse();

  CompleteInstanceResponse(const CompleteInstanceResponse& from);
  CompleteInstanceResponse(CompleteInstanceResponse&& from) noexcept
    : CompleteInstanceResponse() {
    *this = ::std::move(from);
  }

  inline CompleteInstanceResponse& operator=(const CompleteInstanceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompleteInstanceResponse& operator=(CompleteInstanceResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CompleteInstanceResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompleteInstanceResponse* internal_default_instance() {
    return reinterpret_cast<const CompleteInstanceResponse*>(
               &_CompleteInstanceResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    32;

  friend void swap(CompleteInstanceResponse& a, CompleteInstanceResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CompleteInstanceResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompleteInstanceResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CompleteInstanceResponse* New() const final {
    return CreateMaybeMessage<CompleteInstanceResponse>(nullptr);
  }

  CompleteInstanceResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CompleteInstanceResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CompleteInstanceResponse& from);
  void MergeFrom(const CompleteInstanceResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteInstanceResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CompleteInstanceResponse";
  }
  protected:
  explicit CompleteInstanceResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstanceKeyFieldNumber = 1,
    kSourceRankFieldNumber = 2,
  };
  // int32 instance_key = 1;
  void clear_instance_key();
  ::PROTOBUF_NAMESPACE_ID::int32 instance_key() const;
  void set_instance_key(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 source_rank = 2;
  void clear_source_rank();
  ::PROTOBUF_NAMESPACE_ID::int32 source_rank() const;
  void set_source_rank(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteInstanceResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int32 instance_key_;
  ::PROTOBUF_NAMESPACE_ID::int32 source_rank_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class GetStepSequenceRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStepSequenceRequest) */ {
 public:
  GetStepSequenceRequest();
  virtual ~GetStepSequenceRequest();

  GetStepSequenceRequest(const GetStepSequenceRequest& from);
  GetStepSequenceRequest(GetStepSequenceRequest&& from) noexcept
    : GetStepSequenceRequest() {
    *this = ::std::move(from);
  }

  inline GetStepSequenceRequest& operator=(const GetStepSequenceRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetStepSequenceRequest& operator=(GetStepSequenceRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GetStepSequenceRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetStepSequenceRequest* internal_default_instance() {
    return reinterpret_cast<const GetStepSequenceRequest*>(
               &_GetStepSequenceRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    33;

  friend void swap(GetStepSequenceRequest& a, GetStepSequenceRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetStepSequenceRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetStepSequenceRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GetStepSequenceRequest* New() const final {
    return CreateMaybeMessage<GetStepSequenceRequest>(nullptr);
  }

  GetStepSequenceRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GetStepSequenceRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GetStepSequenceRequest& from);
  void MergeFrom(const GetStepSequenceRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStepSequenceRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetStepSequenceRequest";
  }
  protected:
  explicit GetStepSequenceRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGraphKeyFieldNumber = 1,
  };
  // repeated int64 graph_key = 1;
  int graph_key_size() const;
  void clear_graph_key();
  ::PROTOBUF_NAMESPACE_ID::int64 graph_key(int index) const;
  void set_graph_key(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_graph_key(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      graph_key() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_graph_key();

  // @@protoc_insertion_point(class_scope:tensorflow.GetStepSequenceRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > graph_key_;
  mutable std::atomic<int> _graph_key_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class StepSequence :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.StepSequence) */ {
 public:
  StepSequence();
  virtual ~StepSequence();

  StepSequence(const StepSequence& from);
  StepSequence(StepSequence&& from) noexcept
    : StepSequence() {
    *this = ::std::move(from);
  }

  inline StepSequence& operator=(const StepSequence& from) {
    CopyFrom(from);
    return *this;
  }
  inline StepSequence& operator=(StepSequence&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StepSequence& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StepSequence* internal_default_instance() {
    return reinterpret_cast<const StepSequence*>(
               &_StepSequence_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    34;

  friend void swap(StepSequence& a, StepSequence& b) {
    a.Swap(&b);
  }
  inline void Swap(StepSequence* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StepSequence* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StepSequence* New() const final {
    return CreateMaybeMessage<StepSequence>(nullptr);
  }

  StepSequence* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StepSequence>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StepSequence& from);
  void MergeFrom(const StepSequence& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StepSequence* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.StepSequence";
  }
  protected:
  explicit StepSequence(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGraphKeyFieldNumber = 1,
    kNextStepIdFieldNumber = 2,
  };
  // int64 graph_key = 1;
  void clear_graph_key();
  ::PROTOBUF_NAMESPACE_ID::int64 graph_key() const;
  void set_graph_key(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 next_step_id = 2;
  void clear_next_step_id();
  ::PROTOBUF_NAMESPACE_ID::int64 next_step_id() const;
  void set_next_step_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.StepSequence)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 graph_key_;
  ::PROTOBUF_NAMESPACE_ID::int64 next_step_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class GetStepSequenceResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStepSequenceResponse) */ {
 public:
  GetStepSequenceResponse();
  virtual ~GetStepSequenceResponse();

  GetStepSequenceResponse(const GetStepSequenceResponse& from);
  GetStepSequenceResponse(GetStepSequenceResponse&& from) noexcept
    : GetStepSequenceResponse() {
    *this = ::std::move(from);
  }

  inline GetStepSequenceResponse& operator=(const GetStepSequenceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetStepSequenceResponse& operator=(GetStepSequenceResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GetStepSequenceResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetStepSequenceResponse* internal_default_instance() {
    return reinterpret_cast<const GetStepSequenceResponse*>(
               &_GetStepSequenceResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    35;

  friend void swap(GetStepSequenceResponse& a, GetStepSequenceResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetStepSequenceResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetStepSequenceResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GetStepSequenceResponse* New() const final {
    return CreateMaybeMessage<GetStepSequenceResponse>(nullptr);
  }

  GetStepSequenceResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GetStepSequenceResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GetStepSequenceResponse& from);
  void MergeFrom(const GetStepSequenceResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStepSequenceResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetStepSequenceResponse";
  }
  protected:
  explicit GetStepSequenceResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepSequenceFieldNumber = 1,
  };
  // repeated .tensorflow.StepSequence step_sequence = 1;
  int step_sequence_size() const;
  void clear_step_sequence();
  ::tensorflow::StepSequence* mutable_step_sequence(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StepSequence >*
      mutable_step_sequence();
  const ::tensorflow::StepSequence& step_sequence(int index) const;
  ::tensorflow::StepSequence* add_step_sequence();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StepSequence >&
      step_sequence() const;

  // @@protoc_insertion_point(class_scope:tensorflow.GetStepSequenceResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StepSequence > step_sequence_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetStatusRequest

// -------------------------------------------------------------------

// GetStatusResponse

// repeated .tensorflow.DeviceAttributes device_attributes = 1;
inline int GetStatusResponse::device_attributes_size() const {
  return device_attributes_.size();
}
inline ::tensorflow::DeviceAttributes* GetStatusResponse::mutable_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetStatusResponse.device_attributes)
  return device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
GetStatusResponse::mutable_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetStatusResponse.device_attributes)
  return &device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& GetStatusResponse::device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetStatusResponse.device_attributes)
  return device_attributes_.Get(index);
}
inline ::tensorflow::DeviceAttributes* GetStatusResponse::add_device_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.GetStatusResponse.device_attributes)
  return device_attributes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
GetStatusResponse::device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetStatusResponse.device_attributes)
  return device_attributes_;
}

// -------------------------------------------------------------------

// CreateWorkerSessionRequest

// string session_handle = 1;
inline void CreateWorkerSessionRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CreateWorkerSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.session_handle)
  return session_handle_.Get();
}
inline void CreateWorkerSessionRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CreateWorkerSessionRequest.session_handle)
}
inline void CreateWorkerSessionRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CreateWorkerSessionRequest.session_handle)
}
inline void CreateWorkerSessionRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CreateWorkerSessionRequest.session_handle)
}
inline void CreateWorkerSessionRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CreateWorkerSessionRequest.session_handle)
}
inline std::string* CreateWorkerSessionRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateWorkerSessionRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CreateWorkerSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateWorkerSessionRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CreateWorkerSessionRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateWorkerSessionRequest.session_handle)
}
inline std::string* CreateWorkerSessionRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateWorkerSessionRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CreateWorkerSessionRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateWorkerSessionRequest.session_handle)
}

// .tensorflow.ServerDef server_def = 2;
inline bool CreateWorkerSessionRequest::has_server_def() const {
  return this != internal_default_instance() && server_def_ != nullptr;
}
inline const ::tensorflow::ServerDef& CreateWorkerSessionRequest::server_def() const {
  const ::tensorflow::ServerDef* p = server_def_;
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.server_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ServerDef*>(
      &::tensorflow::_ServerDef_default_instance_);
}
inline ::tensorflow::ServerDef* CreateWorkerSessionRequest::release_server_def() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateWorkerSessionRequest.server_def)
  
  ::tensorflow::ServerDef* temp = server_def_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  server_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ServerDef* CreateWorkerSessionRequest::unsafe_arena_release_server_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateWorkerSessionRequest.server_def)
  
  ::tensorflow::ServerDef* temp = server_def_;
  server_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ServerDef* CreateWorkerSessionRequest::mutable_server_def() {
  
  if (server_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ServerDef>(GetArenaNoVirtual());
    server_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateWorkerSessionRequest.server_def)
  return server_def_;
}
inline void CreateWorkerSessionRequest::set_allocated_server_def(::tensorflow::ServerDef* server_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_def_);
  }
  if (server_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_def)->GetArena();
    if (message_arena != submessage_arena) {
      server_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, server_def, submessage_arena);
    }
    
  } else {
    
  }
  server_def_ = server_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateWorkerSessionRequest.server_def)
}

// bool isolate_session_state = 3;
inline void CreateWorkerSessionRequest::clear_isolate_session_state() {
  isolate_session_state_ = false;
}
inline bool CreateWorkerSessionRequest::isolate_session_state() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.isolate_session_state)
  return isolate_session_state_;
}
inline void CreateWorkerSessionRequest::set_isolate_session_state(bool value) {
  
  isolate_session_state_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CreateWorkerSessionRequest.isolate_session_state)
}

// repeated .tensorflow.DeviceAttributes cluster_device_attributes = 4;
inline int CreateWorkerSessionRequest::cluster_device_attributes_size() const {
  return cluster_device_attributes_.size();
}
inline ::tensorflow::DeviceAttributes* CreateWorkerSessionRequest::mutable_cluster_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateWorkerSessionRequest.cluster_device_attributes)
  return cluster_device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
CreateWorkerSessionRequest::mutable_cluster_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CreateWorkerSessionRequest.cluster_device_attributes)
  return &cluster_device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& CreateWorkerSessionRequest::cluster_device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.cluster_device_attributes)
  return cluster_device_attributes_.Get(index);
}
inline ::tensorflow::DeviceAttributes* CreateWorkerSessionRequest::add_cluster_device_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.CreateWorkerSessionRequest.cluster_device_attributes)
  return cluster_device_attributes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
CreateWorkerSessionRequest::cluster_device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.CreateWorkerSessionRequest.cluster_device_attributes)
  return cluster_device_attributes_;
}

// string master_task = 5;
inline void CreateWorkerSessionRequest::clear_master_task() {
  master_task_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CreateWorkerSessionRequest::master_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.master_task)
  return master_task_.Get();
}
inline void CreateWorkerSessionRequest::set_master_task(const std::string& value) {
  
  master_task_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CreateWorkerSessionRequest.master_task)
}
inline void CreateWorkerSessionRequest::set_master_task(std::string&& value) {
  
  master_task_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CreateWorkerSessionRequest.master_task)
}
inline void CreateWorkerSessionRequest::set_master_task(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  master_task_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CreateWorkerSessionRequest.master_task)
}
inline void CreateWorkerSessionRequest::set_master_task(const char* value,
    size_t size) {
  
  master_task_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CreateWorkerSessionRequest.master_task)
}
inline std::string* CreateWorkerSessionRequest::mutable_master_task() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateWorkerSessionRequest.master_task)
  return master_task_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CreateWorkerSessionRequest::release_master_task() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateWorkerSessionRequest.master_task)
  
  return master_task_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CreateWorkerSessionRequest::set_allocated_master_task(std::string* master_task) {
  if (master_task != nullptr) {
    
  } else {
    
  }
  master_task_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), master_task,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateWorkerSessionRequest.master_task)
}
inline std::string* CreateWorkerSessionRequest::unsafe_arena_release_master_task() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateWorkerSessionRequest.master_task)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return master_task_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CreateWorkerSessionRequest::unsafe_arena_set_allocated_master_task(
    std::string* master_task) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (master_task != nullptr) {
    
  } else {
    
  }
  master_task_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      master_task, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateWorkerSessionRequest.master_task)
}

// int64 master_incarnation = 6;
inline void CreateWorkerSessionRequest::clear_master_incarnation() {
  master_incarnation_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CreateWorkerSessionRequest::master_incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.master_incarnation)
  return master_incarnation_;
}
inline void CreateWorkerSessionRequest::set_master_incarnation(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  master_incarnation_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CreateWorkerSessionRequest.master_incarnation)
}

// -------------------------------------------------------------------

// CreateWorkerSessionResponse

// -------------------------------------------------------------------

// DeleteWorkerSessionRequest

// string session_handle = 1;
inline void DeleteWorkerSessionRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DeleteWorkerSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeleteWorkerSessionRequest.session_handle)
  return session_handle_.Get();
}
inline void DeleteWorkerSessionRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
inline void DeleteWorkerSessionRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
inline void DeleteWorkerSessionRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
inline void DeleteWorkerSessionRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
inline std::string* DeleteWorkerSessionRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeleteWorkerSessionRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DeleteWorkerSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.DeleteWorkerSessionRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeleteWorkerSessionRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
inline std::string* DeleteWorkerSessionRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeleteWorkerSessionRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeleteWorkerSessionRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeleteWorkerSessionRequest.session_handle)
}

// -------------------------------------------------------------------

// DeleteWorkerSessionResponse

// -------------------------------------------------------------------

// RegisterGraphRequest

// string session_handle = 1;
inline void RegisterGraphRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RegisterGraphRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.session_handle)
  return session_handle_.Get();
}
inline void RegisterGraphRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.session_handle)
}
inline void RegisterGraphRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RegisterGraphRequest.session_handle)
}
inline void RegisterGraphRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RegisterGraphRequest.session_handle)
}
inline void RegisterGraphRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RegisterGraphRequest.session_handle)
}
inline std::string* RegisterGraphRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RegisterGraphRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RegisterGraphRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.session_handle)
}
inline std::string* RegisterGraphRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RegisterGraphRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisterGraphRequest.session_handle)
}

// bool create_worker_session_called = 6;
inline void RegisterGraphRequest::clear_create_worker_session_called() {
  create_worker_session_called_ = false;
}
inline bool RegisterGraphRequest::create_worker_session_called() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.create_worker_session_called)
  return create_worker_session_called_;
}
inline void RegisterGraphRequest::set_create_worker_session_called(bool value) {
  
  create_worker_session_called_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.create_worker_session_called)
}

// .tensorflow.GraphDef graph_def = 2;
inline bool RegisterGraphRequest::has_graph_def() const {
  return this != internal_default_instance() && graph_def_ != nullptr;
}
inline const ::tensorflow::GraphDef& RegisterGraphRequest::graph_def() const {
  const ::tensorflow::GraphDef* p = graph_def_;
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.graph_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::GraphDef*>(
      &::tensorflow::_GraphDef_default_instance_);
}
inline ::tensorflow::GraphDef* RegisterGraphRequest::release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* RegisterGraphRequest::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* RegisterGraphRequest::mutable_graph_def() {
  
  if (graph_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaNoVirtual());
    graph_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.graph_def)
  return graph_def_;
}
inline void RegisterGraphRequest::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def_);
  }
  if (graph_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def)->GetArena();
    if (message_arena != submessage_arena) {
      graph_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.graph_def)
}

// bool has_control_flow = 3 [deprecated = true];
inline void RegisterGraphRequest::clear_has_control_flow() {
  has_control_flow_ = false;
}
inline bool RegisterGraphRequest::has_control_flow() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.has_control_flow)
  return has_control_flow_;
}
inline void RegisterGraphRequest::set_has_control_flow(bool value) {
  
  has_control_flow_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.has_control_flow)
}

// .tensorflow.GraphOptions graph_options = 4;
inline bool RegisterGraphRequest::has_graph_options() const {
  return this != internal_default_instance() && graph_options_ != nullptr;
}
inline const ::tensorflow::GraphOptions& RegisterGraphRequest::graph_options() const {
  const ::tensorflow::GraphOptions* p = graph_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.graph_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::GraphOptions*>(
      &::tensorflow::_GraphOptions_default_instance_);
}
inline ::tensorflow::GraphOptions* RegisterGraphRequest::release_graph_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.graph_options)
  
  ::tensorflow::GraphOptions* temp = graph_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  graph_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphOptions* RegisterGraphRequest::unsafe_arena_release_graph_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphRequest.graph_options)
  
  ::tensorflow::GraphOptions* temp = graph_options_;
  graph_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphOptions* RegisterGraphRequest::mutable_graph_options() {
  
  if (graph_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphOptions>(GetArenaNoVirtual());
    graph_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.graph_options)
  return graph_options_;
}
inline void RegisterGraphRequest::set_allocated_graph_options(::tensorflow::GraphOptions* graph_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_options_);
  }
  if (graph_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_options)->GetArena();
    if (message_arena != submessage_arena) {
      graph_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_options, submessage_arena);
    }
    
  } else {
    
  }
  graph_options_ = graph_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.graph_options)
}

// .tensorflow.DebugOptions debug_options = 5;
inline bool RegisterGraphRequest::has_debug_options() const {
  return this != internal_default_instance() && debug_options_ != nullptr;
}
inline const ::tensorflow::DebugOptions& RegisterGraphRequest::debug_options() const {
  const ::tensorflow::DebugOptions* p = debug_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.debug_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::DebugOptions*>(
      &::tensorflow::_DebugOptions_default_instance_);
}
inline ::tensorflow::DebugOptions* RegisterGraphRequest::release_debug_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.debug_options)
  
  ::tensorflow::DebugOptions* temp = debug_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  debug_options_ = nullptr;
  return temp;
}
inline ::tensorflow::DebugOptions* RegisterGraphRequest::unsafe_arena_release_debug_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphRequest.debug_options)
  
  ::tensorflow::DebugOptions* temp = debug_options_;
  debug_options_ = nullptr;
  return temp;
}
inline ::tensorflow::DebugOptions* RegisterGraphRequest::mutable_debug_options() {
  
  if (debug_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DebugOptions>(GetArenaNoVirtual());
    debug_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.debug_options)
  return debug_options_;
}
inline void RegisterGraphRequest::set_allocated_debug_options(::tensorflow::DebugOptions* debug_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(debug_options_);
  }
  if (debug_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(debug_options)->GetArena();
    if (message_arena != submessage_arena) {
      debug_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, debug_options, submessage_arena);
    }
    
  } else {
    
  }
  debug_options_ = debug_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.debug_options)
}

// int64 collective_graph_key = 7;
inline void RegisterGraphRequest::clear_collective_graph_key() {
  collective_graph_key_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RegisterGraphRequest::collective_graph_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.collective_graph_key)
  return collective_graph_key_;
}
inline void RegisterGraphRequest::set_collective_graph_key(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  collective_graph_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.collective_graph_key)
}

// .tensorflow.ConfigProto config_proto = 8;
inline bool RegisterGraphRequest::has_config_proto() const {
  return this != internal_default_instance() && config_proto_ != nullptr;
}
inline const ::tensorflow::ConfigProto& RegisterGraphRequest::config_proto() const {
  const ::tensorflow::ConfigProto* p = config_proto_;
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.config_proto)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ConfigProto*>(
      &::tensorflow::_ConfigProto_default_instance_);
}
inline ::tensorflow::ConfigProto* RegisterGraphRequest::release_config_proto() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.config_proto)
  
  ::tensorflow::ConfigProto* temp = config_proto_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  config_proto_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto* RegisterGraphRequest::unsafe_arena_release_config_proto() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphRequest.config_proto)
  
  ::tensorflow::ConfigProto* temp = config_proto_;
  config_proto_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto* RegisterGraphRequest::mutable_config_proto() {
  
  if (config_proto_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ConfigProto>(GetArenaNoVirtual());
    config_proto_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.config_proto)
  return config_proto_;
}
inline void RegisterGraphRequest::set_allocated_config_proto(::tensorflow::ConfigProto* config_proto) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_proto_);
  }
  if (config_proto) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_proto)->GetArena();
    if (message_arena != submessage_arena) {
      config_proto = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config_proto, submessage_arena);
    }
    
  } else {
    
  }
  config_proto_ = config_proto;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.config_proto)
}

// -------------------------------------------------------------------

// RegisterGraphResponse

// string graph_handle = 1;
inline void RegisterGraphResponse::clear_graph_handle() {
  graph_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RegisterGraphResponse::graph_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphResponse.graph_handle)
  return graph_handle_.Get();
}
inline void RegisterGraphResponse::set_graph_handle(const std::string& value) {
  
  graph_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphResponse.graph_handle)
}
inline void RegisterGraphResponse::set_graph_handle(std::string&& value) {
  
  graph_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RegisterGraphResponse.graph_handle)
}
inline void RegisterGraphResponse::set_graph_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  graph_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RegisterGraphResponse.graph_handle)
}
inline void RegisterGraphResponse::set_graph_handle(const char* value,
    size_t size) {
  
  graph_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RegisterGraphResponse.graph_handle)
}
inline std::string* RegisterGraphResponse::mutable_graph_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphResponse.graph_handle)
  return graph_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RegisterGraphResponse::release_graph_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphResponse.graph_handle)
  
  return graph_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RegisterGraphResponse::set_allocated_graph_handle(std::string* graph_handle) {
  if (graph_handle != nullptr) {
    
  } else {
    
  }
  graph_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), graph_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphResponse.graph_handle)
}
inline std::string* RegisterGraphResponse::unsafe_arena_release_graph_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphResponse.graph_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return graph_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RegisterGraphResponse::unsafe_arena_set_allocated_graph_handle(
    std::string* graph_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (graph_handle != nullptr) {
    
  } else {
    
  }
  graph_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      graph_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisterGraphResponse.graph_handle)
}

// -------------------------------------------------------------------

// DeregisterGraphRequest

// string session_handle = 2;
inline void DeregisterGraphRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DeregisterGraphRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeregisterGraphRequest.session_handle)
  return session_handle_.Get();
}
inline void DeregisterGraphRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeregisterGraphRequest.session_handle)
}
inline void DeregisterGraphRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeregisterGraphRequest.session_handle)
}
inline void DeregisterGraphRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeregisterGraphRequest.session_handle)
}
inline void DeregisterGraphRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeregisterGraphRequest.session_handle)
}
inline std::string* DeregisterGraphRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeregisterGraphRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DeregisterGraphRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.DeregisterGraphRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeregisterGraphRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeregisterGraphRequest.session_handle)
}
inline std::string* DeregisterGraphRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeregisterGraphRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeregisterGraphRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeregisterGraphRequest.session_handle)
}

// bool create_worker_session_called = 3;
inline void DeregisterGraphRequest::clear_create_worker_session_called() {
  create_worker_session_called_ = false;
}
inline bool DeregisterGraphRequest::create_worker_session_called() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeregisterGraphRequest.create_worker_session_called)
  return create_worker_session_called_;
}
inline void DeregisterGraphRequest::set_create_worker_session_called(bool value) {
  
  create_worker_session_called_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeregisterGraphRequest.create_worker_session_called)
}

// string graph_handle = 1;
inline void DeregisterGraphRequest::clear_graph_handle() {
  graph_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& DeregisterGraphRequest::graph_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeregisterGraphRequest.graph_handle)
  return graph_handle_.Get();
}
inline void DeregisterGraphRequest::set_graph_handle(const std::string& value) {
  
  graph_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeregisterGraphRequest.graph_handle)
}
inline void DeregisterGraphRequest::set_graph_handle(std::string&& value) {
  
  graph_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeregisterGraphRequest.graph_handle)
}
inline void DeregisterGraphRequest::set_graph_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  graph_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeregisterGraphRequest.graph_handle)
}
inline void DeregisterGraphRequest::set_graph_handle(const char* value,
    size_t size) {
  
  graph_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeregisterGraphRequest.graph_handle)
}
inline std::string* DeregisterGraphRequest::mutable_graph_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeregisterGraphRequest.graph_handle)
  return graph_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* DeregisterGraphRequest::release_graph_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.DeregisterGraphRequest.graph_handle)
  
  return graph_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeregisterGraphRequest::set_allocated_graph_handle(std::string* graph_handle) {
  if (graph_handle != nullptr) {
    
  } else {
    
  }
  graph_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), graph_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeregisterGraphRequest.graph_handle)
}
inline std::string* DeregisterGraphRequest::unsafe_arena_release_graph_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeregisterGraphRequest.graph_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return graph_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeregisterGraphRequest::unsafe_arena_set_allocated_graph_handle(
    std::string* graph_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (graph_handle != nullptr) {
    
  } else {
    
  }
  graph_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      graph_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeregisterGraphRequest.graph_handle)
}

// -------------------------------------------------------------------

// DeregisterGraphResponse

// -------------------------------------------------------------------

// CleanupAllRequest

// repeated string container = 1;
inline int CleanupAllRequest::container_size() const {
  return container_.size();
}
inline void CleanupAllRequest::clear_container() {
  container_.Clear();
}
inline const std::string& CleanupAllRequest::container(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CleanupAllRequest.container)
  return container_.Get(index);
}
inline std::string* CleanupAllRequest::mutable_container(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CleanupAllRequest.container)
  return container_.Mutable(index);
}
inline void CleanupAllRequest::set_container(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CleanupAllRequest.container)
  container_.Mutable(index)->assign(value);
}
inline void CleanupAllRequest::set_container(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CleanupAllRequest.container)
  container_.Mutable(index)->assign(std::move(value));
}
inline void CleanupAllRequest::set_container(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  container_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::set_container(int index, const char* value, size_t size) {
  container_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CleanupAllRequest.container)
}
inline std::string* CleanupAllRequest::add_container() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CleanupAllRequest.container)
  return container_.Add();
}
inline void CleanupAllRequest::add_container(const std::string& value) {
  container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::add_container(std::string&& value) {
  container_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::add_container(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::add_container(const char* value, size_t size) {
  container_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CleanupAllRequest.container)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CleanupAllRequest::container() const {
  // @@protoc_insertion_point(field_list:tensorflow.CleanupAllRequest.container)
  return container_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CleanupAllRequest::mutable_container() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CleanupAllRequest.container)
  return &container_;
}

// -------------------------------------------------------------------

// CleanupAllResponse

// -------------------------------------------------------------------

// ExecutorOpts

// bool record_costs = 1;
inline void ExecutorOpts::clear_record_costs() {
  record_costs_ = false;
}
inline bool ExecutorOpts::record_costs() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.record_costs)
  return record_costs_;
}
inline void ExecutorOpts::set_record_costs(bool value) {
  
  record_costs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.record_costs)
}

// bool record_timeline = 3;
inline void ExecutorOpts::clear_record_timeline() {
  record_timeline_ = false;
}
inline bool ExecutorOpts::record_timeline() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.record_timeline)
  return record_timeline_;
}
inline void ExecutorOpts::set_record_timeline(bool value) {
  
  record_timeline_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.record_timeline)
}

// bool record_partition_graphs = 4;
inline void ExecutorOpts::clear_record_partition_graphs() {
  record_partition_graphs_ = false;
}
inline bool ExecutorOpts::record_partition_graphs() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.record_partition_graphs)
  return record_partition_graphs_;
}
inline void ExecutorOpts::set_record_partition_graphs(bool value) {
  
  record_partition_graphs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.record_partition_graphs)
}

// bool report_tensor_allocations_upon_oom = 5;
inline void ExecutorOpts::clear_report_tensor_allocations_upon_oom() {
  report_tensor_allocations_upon_oom_ = false;
}
inline bool ExecutorOpts::report_tensor_allocations_upon_oom() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.report_tensor_allocations_upon_oom)
  return report_tensor_allocations_upon_oom_;
}
inline void ExecutorOpts::set_report_tensor_allocations_upon_oom(bool value) {
  
  report_tensor_allocations_upon_oom_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.report_tensor_allocations_upon_oom)
}

// -------------------------------------------------------------------

// RunGraphRequest

// string session_handle = 8;
inline void RunGraphRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RunGraphRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.session_handle)
  return session_handle_.Get();
}
inline void RunGraphRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.session_handle)
}
inline void RunGraphRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunGraphRequest.session_handle)
}
inline void RunGraphRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunGraphRequest.session_handle)
}
inline void RunGraphRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunGraphRequest.session_handle)
}
inline std::string* RunGraphRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RunGraphRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunGraphRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphRequest.session_handle)
}
inline std::string* RunGraphRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunGraphRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunGraphRequest.session_handle)
}

// bool create_worker_session_called = 10;
inline void RunGraphRequest::clear_create_worker_session_called() {
  create_worker_session_called_ = false;
}
inline bool RunGraphRequest::create_worker_session_called() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.create_worker_session_called)
  return create_worker_session_called_;
}
inline void RunGraphRequest::set_create_worker_session_called(bool value) {
  
  create_worker_session_called_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.create_worker_session_called)
}

// string graph_handle = 1;
inline void RunGraphRequest::clear_graph_handle() {
  graph_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RunGraphRequest::graph_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.graph_handle)
  return graph_handle_.Get();
}
inline void RunGraphRequest::set_graph_handle(const std::string& value) {
  
  graph_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.graph_handle)
}
inline void RunGraphRequest::set_graph_handle(std::string&& value) {
  
  graph_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunGraphRequest.graph_handle)
}
inline void RunGraphRequest::set_graph_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  graph_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunGraphRequest.graph_handle)
}
inline void RunGraphRequest::set_graph_handle(const char* value,
    size_t size) {
  
  graph_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunGraphRequest.graph_handle)
}
inline std::string* RunGraphRequest::mutable_graph_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.graph_handle)
  return graph_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RunGraphRequest::release_graph_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphRequest.graph_handle)
  
  return graph_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunGraphRequest::set_allocated_graph_handle(std::string* graph_handle) {
  if (graph_handle != nullptr) {
    
  } else {
    
  }
  graph_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), graph_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphRequest.graph_handle)
}
inline std::string* RunGraphRequest::unsafe_arena_release_graph_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphRequest.graph_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return graph_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunGraphRequest::unsafe_arena_set_allocated_graph_handle(
    std::string* graph_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (graph_handle != nullptr) {
    
  } else {
    
  }
  graph_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      graph_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunGraphRequest.graph_handle)
}

// int64 step_id = 2;
inline void RunGraphRequest::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RunGraphRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.step_id)
  return step_id_;
}
inline void RunGraphRequest::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.step_id)
}

// .tensorflow.ExecutorOpts exec_opts = 5;
inline bool RunGraphRequest::has_exec_opts() const {
  return this != internal_default_instance() && exec_opts_ != nullptr;
}
inline void RunGraphRequest::clear_exec_opts() {
  if (GetArenaNoVirtual() == nullptr && exec_opts_ != nullptr) {
    delete exec_opts_;
  }
  exec_opts_ = nullptr;
}
inline const ::tensorflow::ExecutorOpts& RunGraphRequest::exec_opts() const {
  const ::tensorflow::ExecutorOpts* p = exec_opts_;
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.exec_opts)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ExecutorOpts*>(
      &::tensorflow::_ExecutorOpts_default_instance_);
}
inline ::tensorflow::ExecutorOpts* RunGraphRequest::release_exec_opts() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphRequest.exec_opts)
  
  ::tensorflow::ExecutorOpts* temp = exec_opts_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  exec_opts_ = nullptr;
  return temp;
}
inline ::tensorflow::ExecutorOpts* RunGraphRequest::unsafe_arena_release_exec_opts() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphRequest.exec_opts)
  
  ::tensorflow::ExecutorOpts* temp = exec_opts_;
  exec_opts_ = nullptr;
  return temp;
}
inline ::tensorflow::ExecutorOpts* RunGraphRequest::mutable_exec_opts() {
  
  if (exec_opts_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ExecutorOpts>(GetArenaNoVirtual());
    exec_opts_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.exec_opts)
  return exec_opts_;
}
inline void RunGraphRequest::set_allocated_exec_opts(::tensorflow::ExecutorOpts* exec_opts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete exec_opts_;
  }
  if (exec_opts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(exec_opts);
    if (message_arena != submessage_arena) {
      exec_opts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, exec_opts, submessage_arena);
    }
    
  } else {
    
  }
  exec_opts_ = exec_opts;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphRequest.exec_opts)
}

// repeated .tensorflow.NamedTensorProto send = 3;
inline int RunGraphRequest::send_size() const {
  return send_.size();
}
inline ::tensorflow::NamedTensorProto* RunGraphRequest::mutable_send(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.send)
  return send_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunGraphRequest::mutable_send() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphRequest.send)
  return &send_;
}
inline const ::tensorflow::NamedTensorProto& RunGraphRequest::send(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.send)
  return send_.Get(index);
}
inline ::tensorflow::NamedTensorProto* RunGraphRequest::add_send() {
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphRequest.send)
  return send_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunGraphRequest::send() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphRequest.send)
  return send_;
}

// repeated string recv_key = 4;
inline int RunGraphRequest::recv_key_size() const {
  return recv_key_.size();
}
inline void RunGraphRequest::clear_recv_key() {
  recv_key_.Clear();
}
inline const std::string& RunGraphRequest::recv_key(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.recv_key)
  return recv_key_.Get(index);
}
inline std::string* RunGraphRequest::mutable_recv_key(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.recv_key)
  return recv_key_.Mutable(index);
}
inline void RunGraphRequest::set_recv_key(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.recv_key)
  recv_key_.Mutable(index)->assign(value);
}
inline void RunGraphRequest::set_recv_key(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.recv_key)
  recv_key_.Mutable(index)->assign(std::move(value));
}
inline void RunGraphRequest::set_recv_key(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  recv_key_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::set_recv_key(int index, const char* value, size_t size) {
  recv_key_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunGraphRequest.recv_key)
}
inline std::string* RunGraphRequest::add_recv_key() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunGraphRequest.recv_key)
  return recv_key_.Add();
}
inline void RunGraphRequest::add_recv_key(const std::string& value) {
  recv_key_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::add_recv_key(std::string&& value) {
  recv_key_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::add_recv_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  recv_key_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::add_recv_key(const char* value, size_t size) {
  recv_key_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunGraphRequest.recv_key)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RunGraphRequest::recv_key() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphRequest.recv_key)
  return recv_key_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RunGraphRequest::mutable_recv_key() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphRequest.recv_key)
  return &recv_key_;
}

// bool is_partial = 6;
inline void RunGraphRequest::clear_is_partial() {
  is_partial_ = false;
}
inline bool RunGraphRequest::is_partial() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.is_partial)
  return is_partial_;
}
inline void RunGraphRequest::set_is_partial(bool value) {
  
  is_partial_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.is_partial)
}

// bool is_last_partial_run = 7;
inline void RunGraphRequest::clear_is_last_partial_run() {
  is_last_partial_run_ = false;
}
inline bool RunGraphRequest::is_last_partial_run() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.is_last_partial_run)
  return is_last_partial_run_;
}
inline void RunGraphRequest::set_is_last_partial_run(bool value) {
  
  is_last_partial_run_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.is_last_partial_run)
}

// bool store_errors_in_response_body = 9;
inline void RunGraphRequest::clear_store_errors_in_response_body() {
  store_errors_in_response_body_ = false;
}
inline bool RunGraphRequest::store_errors_in_response_body() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.store_errors_in_response_body)
  return store_errors_in_response_body_;
}
inline void RunGraphRequest::set_store_errors_in_response_body(bool value) {
  
  store_errors_in_response_body_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.store_errors_in_response_body)
}

// int64 request_id = 11;
inline void RunGraphRequest::clear_request_id() {
  request_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RunGraphRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.request_id)
  return request_id_;
}
inline void RunGraphRequest::set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.request_id)
}

// -------------------------------------------------------------------

// RunGraphResponse

// repeated .tensorflow.NamedTensorProto recv = 1;
inline int RunGraphResponse::recv_size() const {
  return recv_.size();
}
inline ::tensorflow::NamedTensorProto* RunGraphResponse::mutable_recv(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.recv)
  return recv_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunGraphResponse::mutable_recv() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphResponse.recv)
  return &recv_;
}
inline const ::tensorflow::NamedTensorProto& RunGraphResponse::recv(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.recv)
  return recv_.Get(index);
}
inline ::tensorflow::NamedTensorProto* RunGraphResponse::add_recv() {
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphResponse.recv)
  return recv_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunGraphResponse::recv() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphResponse.recv)
  return recv_;
}

// .tensorflow.StepStats step_stats = 2;
inline bool RunGraphResponse::has_step_stats() const {
  return this != internal_default_instance() && step_stats_ != nullptr;
}
inline const ::tensorflow::StepStats& RunGraphResponse::step_stats() const {
  const ::tensorflow::StepStats* p = step_stats_;
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.step_stats)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::StepStats*>(
      &::tensorflow::_StepStats_default_instance_);
}
inline ::tensorflow::StepStats* RunGraphResponse::release_step_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphResponse.step_stats)
  
  ::tensorflow::StepStats* temp = step_stats_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  step_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::StepStats* RunGraphResponse::unsafe_arena_release_step_stats() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphResponse.step_stats)
  
  ::tensorflow::StepStats* temp = step_stats_;
  step_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::StepStats* RunGraphResponse::mutable_step_stats() {
  
  if (step_stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StepStats>(GetArenaNoVirtual());
    step_stats_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.step_stats)
  return step_stats_;
}
inline void RunGraphResponse::set_allocated_step_stats(::tensorflow::StepStats* step_stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(step_stats_);
  }
  if (step_stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(step_stats)->GetArena();
    if (message_arena != submessage_arena) {
      step_stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, step_stats, submessage_arena);
    }
    
  } else {
    
  }
  step_stats_ = step_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphResponse.step_stats)
}

// .tensorflow.CostGraphDef cost_graph = 3;
inline bool RunGraphResponse::has_cost_graph() const {
  return this != internal_default_instance() && cost_graph_ != nullptr;
}
inline const ::tensorflow::CostGraphDef& RunGraphResponse::cost_graph() const {
  const ::tensorflow::CostGraphDef* p = cost_graph_;
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.cost_graph)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::CostGraphDef*>(
      &::tensorflow::_CostGraphDef_default_instance_);
}
inline ::tensorflow::CostGraphDef* RunGraphResponse::release_cost_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphResponse.cost_graph)
  
  ::tensorflow::CostGraphDef* temp = cost_graph_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  cost_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::CostGraphDef* RunGraphResponse::unsafe_arena_release_cost_graph() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphResponse.cost_graph)
  
  ::tensorflow::CostGraphDef* temp = cost_graph_;
  cost_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::CostGraphDef* RunGraphResponse::mutable_cost_graph() {
  
  if (cost_graph_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CostGraphDef>(GetArenaNoVirtual());
    cost_graph_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.cost_graph)
  return cost_graph_;
}
inline void RunGraphResponse::set_allocated_cost_graph(::tensorflow::CostGraphDef* cost_graph) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(cost_graph_);
  }
  if (cost_graph) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cost_graph)->GetArena();
    if (message_arena != submessage_arena) {
      cost_graph = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cost_graph, submessage_arena);
    }
    
  } else {
    
  }
  cost_graph_ = cost_graph;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphResponse.cost_graph)
}

// repeated .tensorflow.GraphDef partition_graph = 4;
inline int RunGraphResponse::partition_graph_size() const {
  return partition_graph_.size();
}
inline ::tensorflow::GraphDef* RunGraphResponse::mutable_partition_graph(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.partition_graph)
  return partition_graph_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
RunGraphResponse::mutable_partition_graph() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphResponse.partition_graph)
  return &partition_graph_;
}
inline const ::tensorflow::GraphDef& RunGraphResponse::partition_graph(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.partition_graph)
  return partition_graph_.Get(index);
}
inline ::tensorflow::GraphDef* RunGraphResponse::add_partition_graph() {
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphResponse.partition_graph)
  return partition_graph_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
RunGraphResponse::partition_graph() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphResponse.partition_graph)
  return partition_graph_;
}

// .tensorflow.error.Code status_code = 5;
inline void RunGraphResponse::clear_status_code() {
  status_code_ = 0;
}
inline ::tensorflow::error::Code RunGraphResponse::status_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.status_code)
  return static_cast< ::tensorflow::error::Code >(status_code_);
}
inline void RunGraphResponse::set_status_code(::tensorflow::error::Code value) {
  
  status_code_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphResponse.status_code)
}

// string status_error_message = 6;
inline void RunGraphResponse::clear_status_error_message() {
  status_error_message_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RunGraphResponse::status_error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.status_error_message)
  return status_error_message_.Get();
}
inline void RunGraphResponse::set_status_error_message(const std::string& value) {
  
  status_error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphResponse.status_error_message)
}
inline void RunGraphResponse::set_status_error_message(std::string&& value) {
  
  status_error_message_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunGraphResponse.status_error_message)
}
inline void RunGraphResponse::set_status_error_message(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  status_error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunGraphResponse.status_error_message)
}
inline void RunGraphResponse::set_status_error_message(const char* value,
    size_t size) {
  
  status_error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunGraphResponse.status_error_message)
}
inline std::string* RunGraphResponse::mutable_status_error_message() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.status_error_message)
  return status_error_message_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RunGraphResponse::release_status_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphResponse.status_error_message)
  
  return status_error_message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunGraphResponse::set_allocated_status_error_message(std::string* status_error_message) {
  if (status_error_message != nullptr) {
    
  } else {
    
  }
  status_error_message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), status_error_message,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphResponse.status_error_message)
}
inline std::string* RunGraphResponse::unsafe_arena_release_status_error_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphResponse.status_error_message)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return status_error_message_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunGraphResponse::unsafe_arena_set_allocated_status_error_message(
    std::string* status_error_message) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (status_error_message != nullptr) {
    
  } else {
    
  }
  status_error_message_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      status_error_message, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunGraphResponse.status_error_message)
}

// -------------------------------------------------------------------

// CleanupGraphRequest

// int64 step_id = 1;
inline void CleanupGraphRequest::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CleanupGraphRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.CleanupGraphRequest.step_id)
  return step_id_;
}
inline void CleanupGraphRequest::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CleanupGraphRequest.step_id)
}

// -------------------------------------------------------------------

// CleanupGraphResponse

// -------------------------------------------------------------------

// RecvTensorRequest

// int64 step_id = 1;
inline void RecvTensorRequest::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecvTensorRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.step_id)
  return step_id_;
}
inline void RecvTensorRequest::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.step_id)
}

// string rendezvous_key = 2;
inline void RecvTensorRequest::clear_rendezvous_key() {
  rendezvous_key_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RecvTensorRequest::rendezvous_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.rendezvous_key)
  return rendezvous_key_.Get();
}
inline void RecvTensorRequest::set_rendezvous_key(const std::string& value) {
  
  rendezvous_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.rendezvous_key)
}
inline void RecvTensorRequest::set_rendezvous_key(std::string&& value) {
  
  rendezvous_key_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RecvTensorRequest.rendezvous_key)
}
inline void RecvTensorRequest::set_rendezvous_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  rendezvous_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RecvTensorRequest.rendezvous_key)
}
inline void RecvTensorRequest::set_rendezvous_key(const char* value,
    size_t size) {
  
  rendezvous_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RecvTensorRequest.rendezvous_key)
}
inline std::string* RecvTensorRequest::mutable_rendezvous_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.rendezvous_key)
  return rendezvous_key_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RecvTensorRequest::release_rendezvous_key() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.rendezvous_key)
  
  return rendezvous_key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RecvTensorRequest::set_allocated_rendezvous_key(std::string* rendezvous_key) {
  if (rendezvous_key != nullptr) {
    
  } else {
    
  }
  rendezvous_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), rendezvous_key,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.rendezvous_key)
}
inline std::string* RecvTensorRequest::unsafe_arena_release_rendezvous_key() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorRequest.rendezvous_key)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return rendezvous_key_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RecvTensorRequest::unsafe_arena_set_allocated_rendezvous_key(
    std::string* rendezvous_key) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (rendezvous_key != nullptr) {
    
  } else {
    
  }
  rendezvous_key_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      rendezvous_key, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvTensorRequest.rendezvous_key)
}

// bool dma_ok = 3;
inline void RecvTensorRequest::clear_dma_ok() {
  dma_ok_ = false;
}
inline bool RecvTensorRequest::dma_ok() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.dma_ok)
  return dma_ok_;
}
inline void RecvTensorRequest::set_dma_ok(bool value) {
  
  dma_ok_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.dma_ok)
}

// .tensorflow.DeviceLocality client_locality = 4;
inline bool RecvTensorRequest::has_client_locality() const {
  return this != internal_default_instance() && client_locality_ != nullptr;
}
inline const ::tensorflow::DeviceLocality& RecvTensorRequest::client_locality() const {
  const ::tensorflow::DeviceLocality* p = client_locality_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.client_locality)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::DeviceLocality*>(
      &::tensorflow::_DeviceLocality_default_instance_);
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::release_client_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.client_locality)
  
  ::tensorflow::DeviceLocality* temp = client_locality_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  client_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::unsafe_arena_release_client_locality() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorRequest.client_locality)
  
  ::tensorflow::DeviceLocality* temp = client_locality_;
  client_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::mutable_client_locality() {
  
  if (client_locality_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaNoVirtual());
    client_locality_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.client_locality)
  return client_locality_;
}
inline void RecvTensorRequest::set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(client_locality_);
  }
  if (client_locality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(client_locality)->GetArena();
    if (message_arena != submessage_arena) {
      client_locality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, client_locality, submessage_arena);
    }
    
  } else {
    
  }
  client_locality_ = client_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.client_locality)
}

// .tensorflow.DeviceLocality server_locality = 5;
inline bool RecvTensorRequest::has_server_locality() const {
  return this != internal_default_instance() && server_locality_ != nullptr;
}
inline const ::tensorflow::DeviceLocality& RecvTensorRequest::server_locality() const {
  const ::tensorflow::DeviceLocality* p = server_locality_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.server_locality)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::DeviceLocality*>(
      &::tensorflow::_DeviceLocality_default_instance_);
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::release_server_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.server_locality)
  
  ::tensorflow::DeviceLocality* temp = server_locality_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  server_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::unsafe_arena_release_server_locality() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorRequest.server_locality)
  
  ::tensorflow::DeviceLocality* temp = server_locality_;
  server_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::mutable_server_locality() {
  
  if (server_locality_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaNoVirtual());
    server_locality_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.server_locality)
  return server_locality_;
}
inline void RecvTensorRequest::set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_locality_);
  }
  if (server_locality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_locality)->GetArena();
    if (message_arena != submessage_arena) {
      server_locality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, server_locality, submessage_arena);
    }
    
  } else {
    
  }
  server_locality_ = server_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.server_locality)
}

// .google.protobuf.Any transport_options = 6;
inline bool RecvTensorRequest::has_transport_options() const {
  return this != internal_default_instance() && transport_options_ != nullptr;
}
inline const PROTOBUF_NAMESPACE_ID::Any& RecvTensorRequest::transport_options() const {
  const PROTOBUF_NAMESPACE_ID::Any* p = transport_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.transport_options)
  return p != nullptr ? *p : *reinterpret_cast<const PROTOBUF_NAMESPACE_ID::Any*>(
      &PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvTensorRequest::release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.transport_options)
  
  PROTOBUF_NAMESPACE_ID::Any* temp = transport_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  transport_options_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvTensorRequest::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorRequest.transport_options)
  
  PROTOBUF_NAMESPACE_ID::Any* temp = transport_options_;
  transport_options_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvTensorRequest::mutable_transport_options() {
  
  if (transport_options_ == nullptr) {
    auto* p = CreateMaybeMessage<PROTOBUF_NAMESPACE_ID::Any>(GetArenaNoVirtual());
    transport_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.transport_options)
  return transport_options_;
}
inline void RecvTensorRequest::set_allocated_transport_options(PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(transport_options_);
  }
  if (transport_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      transport_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.transport_options)
}

// int64 request_id = 7;
inline void RecvTensorRequest::clear_request_id() {
  request_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecvTensorRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.request_id)
  return request_id_;
}
inline void RecvTensorRequest::set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.request_id)
}

// -------------------------------------------------------------------

// RecvTensorResponse

// .tensorflow.TensorProto tensor = 1;
inline bool RecvTensorResponse::has_tensor() const {
  return this != internal_default_instance() && tensor_ != nullptr;
}
inline const ::tensorflow::TensorProto& RecvTensorResponse::tensor() const {
  const ::tensorflow::TensorProto* p = tensor_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.tensor)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* RecvTensorResponse::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorResponse.tensor)
  
  ::tensorflow::TensorProto* temp = tensor_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* RecvTensorResponse::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorResponse.tensor)
  
  ::tensorflow::TensorProto* temp = tensor_;
  tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* RecvTensorResponse::mutable_tensor() {
  
  if (tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    tensor_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorResponse.tensor)
  return tensor_;
}
inline void RecvTensorResponse::set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_);
  }
  if (tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor)->GetArena();
    if (message_arena != submessage_arena) {
      tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorResponse.tensor)
}

// bool is_dead = 2;
inline void RecvTensorResponse::clear_is_dead() {
  is_dead_ = false;
}
inline bool RecvTensorResponse::is_dead() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.is_dead)
  return is_dead_;
}
inline void RecvTensorResponse::set_is_dead(bool value) {
  
  is_dead_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorResponse.is_dead)
}

// int64 send_start_micros = 3;
inline void RecvTensorResponse::clear_send_start_micros() {
  send_start_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecvTensorResponse::send_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.send_start_micros)
  return send_start_micros_;
}
inline void RecvTensorResponse::set_send_start_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  send_start_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorResponse.send_start_micros)
}

// .google.protobuf.Any transport_options = 4;
inline bool RecvTensorResponse::has_transport_options() const {
  return this != internal_default_instance() && transport_options_ != nullptr;
}
inline const PROTOBUF_NAMESPACE_ID::Any& RecvTensorResponse::transport_options() const {
  const PROTOBUF_NAMESPACE_ID::Any* p = transport_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.transport_options)
  return p != nullptr ? *p : *reinterpret_cast<const PROTOBUF_NAMESPACE_ID::Any*>(
      &PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvTensorResponse::release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorResponse.transport_options)
  
  PROTOBUF_NAMESPACE_ID::Any* temp = transport_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  transport_options_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvTensorResponse::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorResponse.transport_options)
  
  PROTOBUF_NAMESPACE_ID::Any* temp = transport_options_;
  transport_options_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvTensorResponse::mutable_transport_options() {
  
  if (transport_options_ == nullptr) {
    auto* p = CreateMaybeMessage<PROTOBUF_NAMESPACE_ID::Any>(GetArenaNoVirtual());
    transport_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorResponse.transport_options)
  return transport_options_;
}
inline void RecvTensorResponse::set_allocated_transport_options(PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(transport_options_);
  }
  if (transport_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      transport_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorResponse.transport_options)
}

// bool require_ack = 5;
inline void RecvTensorResponse::clear_require_ack() {
  require_ack_ = false;
}
inline bool RecvTensorResponse::require_ack() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.require_ack)
  return require_ack_;
}
inline void RecvTensorResponse::set_require_ack(bool value) {
  
  require_ack_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorResponse.require_ack)
}

// -------------------------------------------------------------------

// MarkRecvFinishedRequest

// int64 request_id = 1;
inline void MarkRecvFinishedRequest::clear_request_id() {
  request_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MarkRecvFinishedRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MarkRecvFinishedRequest.request_id)
  return request_id_;
}
inline void MarkRecvFinishedRequest::set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MarkRecvFinishedRequest.request_id)
}

// -------------------------------------------------------------------

// MarkRecvFinishedResponse

// -------------------------------------------------------------------

// LoggingRequest

// bool enable_rpc_logging = 1;
inline void LoggingRequest::clear_enable_rpc_logging() {
  enable_rpc_logging_ = false;
}
inline bool LoggingRequest::enable_rpc_logging() const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.enable_rpc_logging)
  return enable_rpc_logging_;
}
inline void LoggingRequest::set_enable_rpc_logging(bool value) {
  
  enable_rpc_logging_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.enable_rpc_logging)
}

// bool disable_rpc_logging = 4;
inline void LoggingRequest::clear_disable_rpc_logging() {
  disable_rpc_logging_ = false;
}
inline bool LoggingRequest::disable_rpc_logging() const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.disable_rpc_logging)
  return disable_rpc_logging_;
}
inline void LoggingRequest::set_disable_rpc_logging(bool value) {
  
  disable_rpc_logging_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.disable_rpc_logging)
}

// bool clear = 2;
inline void LoggingRequest::clear_clear() {
  clear_ = false;
}
inline bool LoggingRequest::clear() const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.clear)
  return clear_;
}
inline void LoggingRequest::set_clear(bool value) {
  
  clear_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.clear)
}

// repeated int64 fetch_step_id = 3;
inline int LoggingRequest::fetch_step_id_size() const {
  return fetch_step_id_.size();
}
inline void LoggingRequest::clear_fetch_step_id() {
  fetch_step_id_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LoggingRequest::fetch_step_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.fetch_step_id)
  return fetch_step_id_.Get(index);
}
inline void LoggingRequest::set_fetch_step_id(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  fetch_step_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.fetch_step_id)
}
inline void LoggingRequest::add_fetch_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  fetch_step_id_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.LoggingRequest.fetch_step_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
LoggingRequest::fetch_step_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.LoggingRequest.fetch_step_id)
  return fetch_step_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
LoggingRequest::mutable_fetch_step_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.LoggingRequest.fetch_step_id)
  return &fetch_step_id_;
}

// -------------------------------------------------------------------

// LabeledStepStats

// int64 step_id = 1;
inline void LabeledStepStats::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LabeledStepStats::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.LabeledStepStats.step_id)
  return step_id_;
}
inline void LabeledStepStats::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LabeledStepStats.step_id)
}

// .tensorflow.StepStats step_stats = 2;
inline bool LabeledStepStats::has_step_stats() const {
  return this != internal_default_instance() && step_stats_ != nullptr;
}
inline const ::tensorflow::StepStats& LabeledStepStats::step_stats() const {
  const ::tensorflow::StepStats* p = step_stats_;
  // @@protoc_insertion_point(field_get:tensorflow.LabeledStepStats.step_stats)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::StepStats*>(
      &::tensorflow::_StepStats_default_instance_);
}
inline ::tensorflow::StepStats* LabeledStepStats::release_step_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.LabeledStepStats.step_stats)
  
  ::tensorflow::StepStats* temp = step_stats_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  step_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::StepStats* LabeledStepStats::unsafe_arena_release_step_stats() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.LabeledStepStats.step_stats)
  
  ::tensorflow::StepStats* temp = step_stats_;
  step_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::StepStats* LabeledStepStats::mutable_step_stats() {
  
  if (step_stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StepStats>(GetArenaNoVirtual());
    step_stats_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.LabeledStepStats.step_stats)
  return step_stats_;
}
inline void LabeledStepStats::set_allocated_step_stats(::tensorflow::StepStats* step_stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(step_stats_);
  }
  if (step_stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(step_stats)->GetArena();
    if (message_arena != submessage_arena) {
      step_stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, step_stats, submessage_arena);
    }
    
  } else {
    
  }
  step_stats_ = step_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.LabeledStepStats.step_stats)
}

// -------------------------------------------------------------------

// LoggingResponse

// repeated .tensorflow.LabeledStepStats step = 1;
inline int LoggingResponse::step_size() const {
  return step_.size();
}
inline void LoggingResponse::clear_step() {
  step_.Clear();
}
inline ::tensorflow::LabeledStepStats* LoggingResponse::mutable_step(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.LoggingResponse.step)
  return step_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::LabeledStepStats >*
LoggingResponse::mutable_step() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.LoggingResponse.step)
  return &step_;
}
inline const ::tensorflow::LabeledStepStats& LoggingResponse::step(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingResponse.step)
  return step_.Get(index);
}
inline ::tensorflow::LabeledStepStats* LoggingResponse::add_step() {
  // @@protoc_insertion_point(field_add:tensorflow.LoggingResponse.step)
  return step_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::LabeledStepStats >&
LoggingResponse::step() const {
  // @@protoc_insertion_point(field_list:tensorflow.LoggingResponse.step)
  return step_;
}

// -------------------------------------------------------------------

// TraceOpts

// double duration = 1;
inline void TraceOpts::clear_duration() {
  duration_ = 0;
}
inline double TraceOpts::duration() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.duration)
  return duration_;
}
inline void TraceOpts::set_duration(double value) {
  
  duration_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.duration)
}

// bool use_step_profiler = 2;
inline void TraceOpts::clear_use_step_profiler() {
  use_step_profiler_ = false;
}
inline bool TraceOpts::use_step_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_step_profiler)
  return use_step_profiler_;
}
inline void TraceOpts::set_use_step_profiler(bool value) {
  
  use_step_profiler_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_step_profiler)
}

// bool use_kernel_profiler = 3;
inline void TraceOpts::clear_use_kernel_profiler() {
  use_kernel_profiler_ = false;
}
inline bool TraceOpts::use_kernel_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_kernel_profiler)
  return use_kernel_profiler_;
}
inline void TraceOpts::set_use_kernel_profiler(bool value) {
  
  use_kernel_profiler_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_kernel_profiler)
}

// bool use_extended_profiler = 4;
inline void TraceOpts::clear_use_extended_profiler() {
  use_extended_profiler_ = false;
}
inline bool TraceOpts::use_extended_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_extended_profiler)
  return use_extended_profiler_;
}
inline void TraceOpts::set_use_extended_profiler(bool value) {
  
  use_extended_profiler_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_extended_profiler)
}

// bool use_gpu_profiler = 5;
inline void TraceOpts::clear_use_gpu_profiler() {
  use_gpu_profiler_ = false;
}
inline bool TraceOpts::use_gpu_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_gpu_profiler)
  return use_gpu_profiler_;
}
inline void TraceOpts::set_use_gpu_profiler(bool value) {
  
  use_gpu_profiler_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_gpu_profiler)
}

// bool use_sample_profiler = 6;
inline void TraceOpts::clear_use_sample_profiler() {
  use_sample_profiler_ = false;
}
inline bool TraceOpts::use_sample_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_sample_profiler)
  return use_sample_profiler_;
}
inline void TraceOpts::set_use_sample_profiler(bool value) {
  
  use_sample_profiler_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_sample_profiler)
}

// -------------------------------------------------------------------

// TracingRequest

// .tensorflow.TraceOpts options = 1;
inline bool TracingRequest::has_options() const {
  return this != internal_default_instance() && options_ != nullptr;
}
inline void TracingRequest::clear_options() {
  if (GetArenaNoVirtual() == nullptr && options_ != nullptr) {
    delete options_;
  }
  options_ = nullptr;
}
inline const ::tensorflow::TraceOpts& TracingRequest::options() const {
  const ::tensorflow::TraceOpts* p = options_;
  // @@protoc_insertion_point(field_get:tensorflow.TracingRequest.options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TraceOpts*>(
      &::tensorflow::_TraceOpts_default_instance_);
}
inline ::tensorflow::TraceOpts* TracingRequest::release_options() {
  // @@protoc_insertion_point(field_release:tensorflow.TracingRequest.options)
  
  ::tensorflow::TraceOpts* temp = options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  options_ = nullptr;
  return temp;
}
inline ::tensorflow::TraceOpts* TracingRequest::unsafe_arena_release_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TracingRequest.options)
  
  ::tensorflow::TraceOpts* temp = options_;
  options_ = nullptr;
  return temp;
}
inline ::tensorflow::TraceOpts* TracingRequest::mutable_options() {
  
  if (options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TraceOpts>(GetArenaNoVirtual());
    options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TracingRequest.options)
  return options_;
}
inline void TracingRequest::set_allocated_options(::tensorflow::TraceOpts* options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete options_;
  }
  if (options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(options);
    if (message_arena != submessage_arena) {
      options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, options, submessage_arena);
    }
    
  } else {
    
  }
  options_ = options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TracingRequest.options)
}

// -------------------------------------------------------------------

// TracingResponse

// -------------------------------------------------------------------

// RecvBufRequest

// int64 step_id = 1;
inline void RecvBufRequest::clear_step_id() {
  step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecvBufRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.step_id)
  return step_id_;
}
inline void RecvBufRequest::set_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.step_id)
}

// string buf_rendezvous_key = 2;
inline void RecvBufRequest::clear_buf_rendezvous_key() {
  buf_rendezvous_key_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RecvBufRequest::buf_rendezvous_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.buf_rendezvous_key)
  return buf_rendezvous_key_.Get();
}
inline void RecvBufRequest::set_buf_rendezvous_key(const std::string& value) {
  
  buf_rendezvous_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
inline void RecvBufRequest::set_buf_rendezvous_key(std::string&& value) {
  
  buf_rendezvous_key_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
inline void RecvBufRequest::set_buf_rendezvous_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  buf_rendezvous_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
inline void RecvBufRequest::set_buf_rendezvous_key(const char* value,
    size_t size) {
  
  buf_rendezvous_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
inline std::string* RecvBufRequest::mutable_buf_rendezvous_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.buf_rendezvous_key)
  return buf_rendezvous_key_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RecvBufRequest::release_buf_rendezvous_key() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.buf_rendezvous_key)
  
  return buf_rendezvous_key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RecvBufRequest::set_allocated_buf_rendezvous_key(std::string* buf_rendezvous_key) {
  if (buf_rendezvous_key != nullptr) {
    
  } else {
    
  }
  buf_rendezvous_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), buf_rendezvous_key,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
inline std::string* RecvBufRequest::unsafe_arena_release_buf_rendezvous_key() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.buf_rendezvous_key)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return buf_rendezvous_key_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RecvBufRequest::unsafe_arena_set_allocated_buf_rendezvous_key(
    std::string* buf_rendezvous_key) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (buf_rendezvous_key != nullptr) {
    
  } else {
    
  }
  buf_rendezvous_key_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      buf_rendezvous_key, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvBufRequest.buf_rendezvous_key)
}

// int64 num_bytes = 3;
inline void RecvBufRequest::clear_num_bytes() {
  num_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecvBufRequest::num_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.num_bytes)
  return num_bytes_;
}
inline void RecvBufRequest::set_num_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.num_bytes)
}

// fixed64 buf_ptr = 4;
inline void RecvBufRequest::clear_buf_ptr() {
  buf_ptr_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RecvBufRequest::buf_ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.buf_ptr)
  return buf_ptr_;
}
inline void RecvBufRequest::set_buf_ptr(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  buf_ptr_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.buf_ptr)
}

// .tensorflow.DeviceLocality client_locality = 5;
inline bool RecvBufRequest::has_client_locality() const {
  return this != internal_default_instance() && client_locality_ != nullptr;
}
inline const ::tensorflow::DeviceLocality& RecvBufRequest::client_locality() const {
  const ::tensorflow::DeviceLocality* p = client_locality_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.client_locality)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::DeviceLocality*>(
      &::tensorflow::_DeviceLocality_default_instance_);
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::release_client_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.client_locality)
  
  ::tensorflow::DeviceLocality* temp = client_locality_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  client_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::unsafe_arena_release_client_locality() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.client_locality)
  
  ::tensorflow::DeviceLocality* temp = client_locality_;
  client_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::mutable_client_locality() {
  
  if (client_locality_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaNoVirtual());
    client_locality_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.client_locality)
  return client_locality_;
}
inline void RecvBufRequest::set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(client_locality_);
  }
  if (client_locality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(client_locality)->GetArena();
    if (message_arena != submessage_arena) {
      client_locality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, client_locality, submessage_arena);
    }
    
  } else {
    
  }
  client_locality_ = client_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.client_locality)
}

// .tensorflow.DeviceLocality server_locality = 6;
inline bool RecvBufRequest::has_server_locality() const {
  return this != internal_default_instance() && server_locality_ != nullptr;
}
inline const ::tensorflow::DeviceLocality& RecvBufRequest::server_locality() const {
  const ::tensorflow::DeviceLocality* p = server_locality_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.server_locality)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::DeviceLocality*>(
      &::tensorflow::_DeviceLocality_default_instance_);
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::release_server_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.server_locality)
  
  ::tensorflow::DeviceLocality* temp = server_locality_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  server_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::unsafe_arena_release_server_locality() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.server_locality)
  
  ::tensorflow::DeviceLocality* temp = server_locality_;
  server_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::mutable_server_locality() {
  
  if (server_locality_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaNoVirtual());
    server_locality_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.server_locality)
  return server_locality_;
}
inline void RecvBufRequest::set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_locality_);
  }
  if (server_locality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_locality)->GetArena();
    if (message_arena != submessage_arena) {
      server_locality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, server_locality, submessage_arena);
    }
    
  } else {
    
  }
  server_locality_ = server_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.server_locality)
}

// .google.protobuf.Any transport_options = 7;
inline bool RecvBufRequest::has_transport_options() const {
  return this != internal_default_instance() && transport_options_ != nullptr;
}
inline const PROTOBUF_NAMESPACE_ID::Any& RecvBufRequest::transport_options() const {
  const PROTOBUF_NAMESPACE_ID::Any* p = transport_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.transport_options)
  return p != nullptr ? *p : *reinterpret_cast<const PROTOBUF_NAMESPACE_ID::Any*>(
      &PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvBufRequest::release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.transport_options)
  
  PROTOBUF_NAMESPACE_ID::Any* temp = transport_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  transport_options_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvBufRequest::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.transport_options)
  
  PROTOBUF_NAMESPACE_ID::Any* temp = transport_options_;
  transport_options_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvBufRequest::mutable_transport_options() {
  
  if (transport_options_ == nullptr) {
    auto* p = CreateMaybeMessage<PROTOBUF_NAMESPACE_ID::Any>(GetArenaNoVirtual());
    transport_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.transport_options)
  return transport_options_;
}
inline void RecvBufRequest::set_allocated_transport_options(PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(transport_options_);
  }
  if (transport_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      transport_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.transport_options)
}

// string src_device = 8;
inline void RecvBufRequest::clear_src_device() {
  src_device_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RecvBufRequest::src_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.src_device)
  return src_device_.Get();
}
inline void RecvBufRequest::set_src_device(const std::string& value) {
  
  src_device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.src_device)
}
inline void RecvBufRequest::set_src_device(std::string&& value) {
  
  src_device_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RecvBufRequest.src_device)
}
inline void RecvBufRequest::set_src_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  src_device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RecvBufRequest.src_device)
}
inline void RecvBufRequest::set_src_device(const char* value,
    size_t size) {
  
  src_device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RecvBufRequest.src_device)
}
inline std::string* RecvBufRequest::mutable_src_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.src_device)
  return src_device_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RecvBufRequest::release_src_device() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.src_device)
  
  return src_device_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RecvBufRequest::set_allocated_src_device(std::string* src_device) {
  if (src_device != nullptr) {
    
  } else {
    
  }
  src_device_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), src_device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.src_device)
}
inline std::string* RecvBufRequest::unsafe_arena_release_src_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.src_device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return src_device_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RecvBufRequest::unsafe_arena_set_allocated_src_device(
    std::string* src_device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (src_device != nullptr) {
    
  } else {
    
  }
  src_device_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      src_device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvBufRequest.src_device)
}

// string dst_device = 9;
inline void RecvBufRequest::clear_dst_device() {
  dst_device_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RecvBufRequest::dst_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.dst_device)
  return dst_device_.Get();
}
inline void RecvBufRequest::set_dst_device(const std::string& value) {
  
  dst_device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.dst_device)
}
inline void RecvBufRequest::set_dst_device(std::string&& value) {
  
  dst_device_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RecvBufRequest.dst_device)
}
inline void RecvBufRequest::set_dst_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  dst_device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RecvBufRequest.dst_device)
}
inline void RecvBufRequest::set_dst_device(const char* value,
    size_t size) {
  
  dst_device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RecvBufRequest.dst_device)
}
inline std::string* RecvBufRequest::mutable_dst_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.dst_device)
  return dst_device_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RecvBufRequest::release_dst_device() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.dst_device)
  
  return dst_device_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RecvBufRequest::set_allocated_dst_device(std::string* dst_device) {
  if (dst_device != nullptr) {
    
  } else {
    
  }
  dst_device_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), dst_device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.dst_device)
}
inline std::string* RecvBufRequest::unsafe_arena_release_dst_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.dst_device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return dst_device_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RecvBufRequest::unsafe_arena_set_allocated_dst_device(
    std::string* dst_device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (dst_device != nullptr) {
    
  } else {
    
  }
  dst_device_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      dst_device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvBufRequest.dst_device)
}

// int64 request_id = 10;
inline void RecvBufRequest::clear_request_id() {
  request_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecvBufRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.request_id)
  return request_id_;
}
inline void RecvBufRequest::set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.request_id)
}

// uint64 src_incarnation = 11;
inline void RecvBufRequest::clear_src_incarnation() {
  src_incarnation_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RecvBufRequest::src_incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.src_incarnation)
  return src_incarnation_;
}
inline void RecvBufRequest::set_src_incarnation(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  src_incarnation_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.src_incarnation)
}

// -------------------------------------------------------------------

// RecvBufResponse

// fixed64 buf_ptr = 1;
inline void RecvBufResponse::clear_buf_ptr() {
  buf_ptr_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RecvBufResponse::buf_ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.buf_ptr)
  return buf_ptr_;
}
inline void RecvBufResponse::set_buf_ptr(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  buf_ptr_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.buf_ptr)
}

// int64 num_bytes = 2;
inline void RecvBufResponse::clear_num_bytes() {
  num_bytes_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecvBufResponse::num_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.num_bytes)
  return num_bytes_;
}
inline void RecvBufResponse::set_num_bytes(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.num_bytes)
}

// bool is_dead = 3;
inline void RecvBufResponse::clear_is_dead() {
  is_dead_ = false;
}
inline bool RecvBufResponse::is_dead() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.is_dead)
  return is_dead_;
}
inline void RecvBufResponse::set_is_dead(bool value) {
  
  is_dead_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.is_dead)
}

// .google.protobuf.Any transport_options = 4;
inline bool RecvBufResponse::has_transport_options() const {
  return this != internal_default_instance() && transport_options_ != nullptr;
}
inline const PROTOBUF_NAMESPACE_ID::Any& RecvBufResponse::transport_options() const {
  const PROTOBUF_NAMESPACE_ID::Any* p = transport_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.transport_options)
  return p != nullptr ? *p : *reinterpret_cast<const PROTOBUF_NAMESPACE_ID::Any*>(
      &PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvBufResponse::release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufResponse.transport_options)
  
  PROTOBUF_NAMESPACE_ID::Any* temp = transport_options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  transport_options_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvBufResponse::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufResponse.transport_options)
  
  PROTOBUF_NAMESPACE_ID::Any* temp = transport_options_;
  transport_options_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::Any* RecvBufResponse::mutable_transport_options() {
  
  if (transport_options_ == nullptr) {
    auto* p = CreateMaybeMessage<PROTOBUF_NAMESPACE_ID::Any>(GetArenaNoVirtual());
    transport_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufResponse.transport_options)
  return transport_options_;
}
inline void RecvBufResponse::set_allocated_transport_options(PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(transport_options_);
  }
  if (transport_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      transport_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufResponse.transport_options)
}

// int64 send_start_micros = 5;
inline void RecvBufResponse::clear_send_start_micros() {
  send_start_micros_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecvBufResponse::send_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.send_start_micros)
  return send_start_micros_;
}
inline void RecvBufResponse::set_send_start_micros(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  send_start_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.send_start_micros)
}

// bool require_ack = 6;
inline void RecvBufResponse::clear_require_ack() {
  require_ack_ = false;
}
inline bool RecvBufResponse::require_ack() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.require_ack)
  return require_ack_;
}
inline void RecvBufResponse::set_require_ack(bool value) {
  
  require_ack_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.require_ack)
}

// -------------------------------------------------------------------

// CompleteGroupRequest

// int32 group_key = 1;
inline void CompleteGroupRequest::clear_group_key() {
  group_key_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteGroupRequest::group_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.group_key)
  return group_key_;
}
inline void CompleteGroupRequest::set_group_key(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  group_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.group_key)
}

// int32 group_size = 2;
inline void CompleteGroupRequest::clear_group_size() {
  group_size_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteGroupRequest::group_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.group_size)
  return group_size_;
}
inline void CompleteGroupRequest::set_group_size(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  group_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.group_size)
}

// string device_type = 3;
inline void CompleteGroupRequest::clear_device_type() {
  device_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CompleteGroupRequest::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.device_type)
  return device_type_.Get();
}
inline void CompleteGroupRequest::set_device_type(const std::string& value) {
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.device_type)
}
inline void CompleteGroupRequest::set_device_type(std::string&& value) {
  
  device_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteGroupRequest.device_type)
}
inline void CompleteGroupRequest::set_device_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteGroupRequest.device_type)
}
inline void CompleteGroupRequest::set_device_type(const char* value,
    size_t size) {
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteGroupRequest.device_type)
}
inline std::string* CompleteGroupRequest::mutable_device_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupRequest.device_type)
  return device_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CompleteGroupRequest::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteGroupRequest.device_type)
  
  return device_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteGroupRequest::set_allocated_device_type(std::string* device_type) {
  if (device_type != nullptr) {
    
  } else {
    
  }
  device_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteGroupRequest.device_type)
}
inline std::string* CompleteGroupRequest::unsafe_arena_release_device_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteGroupRequest.device_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteGroupRequest::unsafe_arena_set_allocated_device_type(
    std::string* device_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device_type != nullptr) {
    
  } else {
    
  }
  device_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteGroupRequest.device_type)
}

// int32 collective_type = 5;
inline void CompleteGroupRequest::clear_collective_type() {
  collective_type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteGroupRequest::collective_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.collective_type)
  return collective_type_;
}
inline void CompleteGroupRequest::set_collective_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  collective_type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.collective_type)
}

// .tensorflow.DeviceAttributes device_attributes = 6;
inline bool CompleteGroupRequest::has_device_attributes() const {
  return this != internal_default_instance() && device_attributes_ != nullptr;
}
inline const ::tensorflow::DeviceAttributes& CompleteGroupRequest::device_attributes() const {
  const ::tensorflow::DeviceAttributes* p = device_attributes_;
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.device_attributes)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::DeviceAttributes*>(
      &::tensorflow::_DeviceAttributes_default_instance_);
}
inline ::tensorflow::DeviceAttributes* CompleteGroupRequest::release_device_attributes() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteGroupRequest.device_attributes)
  
  ::tensorflow::DeviceAttributes* temp = device_attributes_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  device_attributes_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceAttributes* CompleteGroupRequest::unsafe_arena_release_device_attributes() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteGroupRequest.device_attributes)
  
  ::tensorflow::DeviceAttributes* temp = device_attributes_;
  device_attributes_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceAttributes* CompleteGroupRequest::mutable_device_attributes() {
  
  if (device_attributes_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceAttributes>(GetArenaNoVirtual());
    device_attributes_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupRequest.device_attributes)
  return device_attributes_;
}
inline void CompleteGroupRequest::set_allocated_device_attributes(::tensorflow::DeviceAttributes* device_attributes) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_attributes_);
  }
  if (device_attributes) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_attributes)->GetArena();
    if (message_arena != submessage_arena) {
      device_attributes = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_attributes, submessage_arena);
    }
    
  } else {
    
  }
  device_attributes_ = device_attributes;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteGroupRequest.device_attributes)
}

// -------------------------------------------------------------------

// CompleteGroupResponse

// int32 group_key = 1;
inline void CompleteGroupResponse::clear_group_key() {
  group_key_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteGroupResponse::group_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.group_key)
  return group_key_;
}
inline void CompleteGroupResponse::set_group_key(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  group_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.group_key)
}

// int32 group_size = 2;
inline void CompleteGroupResponse::clear_group_size() {
  group_size_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteGroupResponse::group_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.group_size)
  return group_size_;
}
inline void CompleteGroupResponse::set_group_size(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  group_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.group_size)
}

// string device_type = 3;
inline void CompleteGroupResponse::clear_device_type() {
  device_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CompleteGroupResponse::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.device_type)
  return device_type_.Get();
}
inline void CompleteGroupResponse::set_device_type(const std::string& value) {
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.device_type)
}
inline void CompleteGroupResponse::set_device_type(std::string&& value) {
  
  device_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteGroupResponse.device_type)
}
inline void CompleteGroupResponse::set_device_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteGroupResponse.device_type)
}
inline void CompleteGroupResponse::set_device_type(const char* value,
    size_t size) {
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteGroupResponse.device_type)
}
inline std::string* CompleteGroupResponse::mutable_device_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupResponse.device_type)
  return device_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CompleteGroupResponse::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteGroupResponse.device_type)
  
  return device_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteGroupResponse::set_allocated_device_type(std::string* device_type) {
  if (device_type != nullptr) {
    
  } else {
    
  }
  device_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteGroupResponse.device_type)
}
inline std::string* CompleteGroupResponse::unsafe_arena_release_device_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteGroupResponse.device_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteGroupResponse::unsafe_arena_set_allocated_device_type(
    std::string* device_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device_type != nullptr) {
    
  } else {
    
  }
  device_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteGroupResponse.device_type)
}

// int32 num_tasks = 4;
inline void CompleteGroupResponse::clear_num_tasks() {
  num_tasks_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteGroupResponse::num_tasks() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.num_tasks)
  return num_tasks_;
}
inline void CompleteGroupResponse::set_num_tasks(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_tasks_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.num_tasks)
}

// bytes communicator_key = 7;
inline void CompleteGroupResponse::clear_communicator_key() {
  communicator_key_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CompleteGroupResponse::communicator_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.communicator_key)
  return communicator_key_.Get();
}
inline void CompleteGroupResponse::set_communicator_key(const std::string& value) {
  
  communicator_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.communicator_key)
}
inline void CompleteGroupResponse::set_communicator_key(std::string&& value) {
  
  communicator_key_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteGroupResponse.communicator_key)
}
inline void CompleteGroupResponse::set_communicator_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  communicator_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteGroupResponse.communicator_key)
}
inline void CompleteGroupResponse::set_communicator_key(const void* value,
    size_t size) {
  
  communicator_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteGroupResponse.communicator_key)
}
inline std::string* CompleteGroupResponse::mutable_communicator_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupResponse.communicator_key)
  return communicator_key_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CompleteGroupResponse::release_communicator_key() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteGroupResponse.communicator_key)
  
  return communicator_key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteGroupResponse::set_allocated_communicator_key(std::string* communicator_key) {
  if (communicator_key != nullptr) {
    
  } else {
    
  }
  communicator_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), communicator_key,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteGroupResponse.communicator_key)
}
inline std::string* CompleteGroupResponse::unsafe_arena_release_communicator_key() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteGroupResponse.communicator_key)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return communicator_key_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteGroupResponse::unsafe_arena_set_allocated_communicator_key(
    std::string* communicator_key) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (communicator_key != nullptr) {
    
  } else {
    
  }
  communicator_key_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      communicator_key, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteGroupResponse.communicator_key)
}

// repeated .tensorflow.DeviceAttributes device_attributes = 8;
inline int CompleteGroupResponse::device_attributes_size() const {
  return device_attributes_.size();
}
inline ::tensorflow::DeviceAttributes* CompleteGroupResponse::mutable_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupResponse.device_attributes)
  return device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
CompleteGroupResponse::mutable_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CompleteGroupResponse.device_attributes)
  return &device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& CompleteGroupResponse::device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.device_attributes)
  return device_attributes_.Get(index);
}
inline ::tensorflow::DeviceAttributes* CompleteGroupResponse::add_device_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.CompleteGroupResponse.device_attributes)
  return device_attributes_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
CompleteGroupResponse::device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.CompleteGroupResponse.device_attributes)
  return device_attributes_;
}

// -------------------------------------------------------------------

// CompleteInstanceRequest

// string name = 1;
inline void CompleteInstanceRequest::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CompleteInstanceRequest::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.name)
  return name_.Get();
}
inline void CompleteInstanceRequest::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.name)
}
inline void CompleteInstanceRequest::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteInstanceRequest.name)
}
inline void CompleteInstanceRequest::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteInstanceRequest.name)
}
inline void CompleteInstanceRequest::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteInstanceRequest.name)
}
inline std::string* CompleteInstanceRequest::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CompleteInstanceRequest::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.name)
}
inline std::string* CompleteInstanceRequest::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteInstanceRequest.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteInstanceRequest.name)
}

// int32 type = 2;
inline void CompleteInstanceRequest::clear_type() {
  type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteInstanceRequest::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.type)
  return type_;
}
inline void CompleteInstanceRequest::set_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.type)
}

// .tensorflow.DataType data_type = 3;
inline void CompleteInstanceRequest::clear_data_type() {
  data_type_ = 0;
}
inline ::tensorflow::DataType CompleteInstanceRequest::data_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.data_type)
  return static_cast< ::tensorflow::DataType >(data_type_);
}
inline void CompleteInstanceRequest::set_data_type(::tensorflow::DataType value) {
  
  data_type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.data_type)
}

// .tensorflow.TensorShapeProto shape = 4;
inline bool CompleteInstanceRequest::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& CompleteInstanceRequest::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* CompleteInstanceRequest::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CompleteInstanceRequest::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteInstanceRequest.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CompleteInstanceRequest::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.shape)
  return shape_;
}
inline void CompleteInstanceRequest::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.shape)
}

// int32 group_key = 5;
inline void CompleteInstanceRequest::clear_group_key() {
  group_key_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteInstanceRequest::group_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.group_key)
  return group_key_;
}
inline void CompleteInstanceRequest::set_group_key(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  group_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.group_key)
}

// int32 group_size = 6;
inline void CompleteInstanceRequest::clear_group_size() {
  group_size_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteInstanceRequest::group_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.group_size)
  return group_size_;
}
inline void CompleteInstanceRequest::set_group_size(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  group_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.group_size)
}

// int32 instance_key = 7;
inline void CompleteInstanceRequest::clear_instance_key() {
  instance_key_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteInstanceRequest::instance_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.instance_key)
  return instance_key_;
}
inline void CompleteInstanceRequest::set_instance_key(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  instance_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.instance_key)
}

// string device_type = 8;
inline void CompleteInstanceRequest::clear_device_type() {
  device_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CompleteInstanceRequest::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.device_type)
  return device_type_.Get();
}
inline void CompleteInstanceRequest::set_device_type(const std::string& value) {
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.device_type)
}
inline void CompleteInstanceRequest::set_device_type(std::string&& value) {
  
  device_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteInstanceRequest.device_type)
}
inline void CompleteInstanceRequest::set_device_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteInstanceRequest.device_type)
}
inline void CompleteInstanceRequest::set_device_type(const char* value,
    size_t size) {
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteInstanceRequest.device_type)
}
inline std::string* CompleteInstanceRequest::mutable_device_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.device_type)
  return device_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CompleteInstanceRequest::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.device_type)
  
  return device_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::set_allocated_device_type(std::string* device_type) {
  if (device_type != nullptr) {
    
  } else {
    
  }
  device_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.device_type)
}
inline std::string* CompleteInstanceRequest::unsafe_arena_release_device_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteInstanceRequest.device_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::unsafe_arena_set_allocated_device_type(
    std::string* device_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device_type != nullptr) {
    
  } else {
    
  }
  device_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteInstanceRequest.device_type)
}

// repeated int32 subdiv_offset = 9;
inline int CompleteInstanceRequest::subdiv_offset_size() const {
  return subdiv_offset_.size();
}
inline void CompleteInstanceRequest::clear_subdiv_offset() {
  subdiv_offset_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteInstanceRequest::subdiv_offset(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.subdiv_offset)
  return subdiv_offset_.Get(index);
}
inline void CompleteInstanceRequest::set_subdiv_offset(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  subdiv_offset_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.subdiv_offset)
}
inline void CompleteInstanceRequest::add_subdiv_offset(::PROTOBUF_NAMESPACE_ID::int32 value) {
  subdiv_offset_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.CompleteInstanceRequest.subdiv_offset)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
CompleteInstanceRequest::subdiv_offset() const {
  // @@protoc_insertion_point(field_list:tensorflow.CompleteInstanceRequest.subdiv_offset)
  return subdiv_offset_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
CompleteInstanceRequest::mutable_subdiv_offset() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CompleteInstanceRequest.subdiv_offset)
  return &subdiv_offset_;
}

// string device = 10;
inline void CompleteInstanceRequest::clear_device() {
  device_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CompleteInstanceRequest::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.device)
  return device_.Get();
}
inline void CompleteInstanceRequest::set_device(const std::string& value) {
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.device)
}
inline void CompleteInstanceRequest::set_device(std::string&& value) {
  
  device_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteInstanceRequest.device)
}
inline void CompleteInstanceRequest::set_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteInstanceRequest.device)
}
inline void CompleteInstanceRequest::set_device(const char* value,
    size_t size) {
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteInstanceRequest.device)
}
inline std::string* CompleteInstanceRequest::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.device)
  return device_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CompleteInstanceRequest::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.device)
  
  return device_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  device_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.device)
}
inline std::string* CompleteInstanceRequest::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteInstanceRequest.device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::unsafe_arena_set_allocated_device(
    std::string* device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device != nullptr) {
    
  } else {
    
  }
  device_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteInstanceRequest.device)
}

// bool is_source = 11;
inline void CompleteInstanceRequest::clear_is_source() {
  is_source_ = false;
}
inline bool CompleteInstanceRequest::is_source() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.is_source)
  return is_source_;
}
inline void CompleteInstanceRequest::set_is_source(bool value) {
  
  is_source_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.is_source)
}

// -------------------------------------------------------------------

// CompleteInstanceResponse

// int32 instance_key = 1;
inline void CompleteInstanceResponse::clear_instance_key() {
  instance_key_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteInstanceResponse::instance_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceResponse.instance_key)
  return instance_key_;
}
inline void CompleteInstanceResponse::set_instance_key(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  instance_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceResponse.instance_key)
}

// int32 source_rank = 2;
inline void CompleteInstanceResponse::clear_source_rank() {
  source_rank_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CompleteInstanceResponse::source_rank() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceResponse.source_rank)
  return source_rank_;
}
inline void CompleteInstanceResponse::set_source_rank(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  source_rank_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceResponse.source_rank)
}

// -------------------------------------------------------------------

// GetStepSequenceRequest

// repeated int64 graph_key = 1;
inline int GetStepSequenceRequest::graph_key_size() const {
  return graph_key_.size();
}
inline void GetStepSequenceRequest::clear_graph_key() {
  graph_key_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GetStepSequenceRequest::graph_key(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetStepSequenceRequest.graph_key)
  return graph_key_.Get(index);
}
inline void GetStepSequenceRequest::set_graph_key(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  graph_key_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GetStepSequenceRequest.graph_key)
}
inline void GetStepSequenceRequest::add_graph_key(::PROTOBUF_NAMESPACE_ID::int64 value) {
  graph_key_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.GetStepSequenceRequest.graph_key)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
GetStepSequenceRequest::graph_key() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetStepSequenceRequest.graph_key)
  return graph_key_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
GetStepSequenceRequest::mutable_graph_key() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetStepSequenceRequest.graph_key)
  return &graph_key_;
}

// -------------------------------------------------------------------

// StepSequence

// int64 graph_key = 1;
inline void StepSequence::clear_graph_key() {
  graph_key_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 StepSequence::graph_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.StepSequence.graph_key)
  return graph_key_;
}
inline void StepSequence::set_graph_key(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  graph_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StepSequence.graph_key)
}

// int64 next_step_id = 2;
inline void StepSequence::clear_next_step_id() {
  next_step_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 StepSequence::next_step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.StepSequence.next_step_id)
  return next_step_id_;
}
inline void StepSequence::set_next_step_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  next_step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StepSequence.next_step_id)
}

// -------------------------------------------------------------------

// GetStepSequenceResponse

// repeated .tensorflow.StepSequence step_sequence = 1;
inline int GetStepSequenceResponse::step_sequence_size() const {
  return step_sequence_.size();
}
inline void GetStepSequenceResponse::clear_step_sequence() {
  step_sequence_.Clear();
}
inline ::tensorflow::StepSequence* GetStepSequenceResponse::mutable_step_sequence(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetStepSequenceResponse.step_sequence)
  return step_sequence_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StepSequence >*
GetStepSequenceResponse::mutable_step_sequence() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetStepSequenceResponse.step_sequence)
  return &step_sequence_;
}
inline const ::tensorflow::StepSequence& GetStepSequenceResponse::step_sequence(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetStepSequenceResponse.step_sequence)
  return step_sequence_.Get(index);
}
inline ::tensorflow::StepSequence* GetStepSequenceResponse::add_step_sequence() {
  // @@protoc_insertion_point(field_add:tensorflow.GetStepSequenceResponse.step_sequence)
  return step_sequence_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StepSequence >&
GetStepSequenceResponse::step_sequence() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetStepSequenceResponse.step_sequence)
  return step_sequence_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fworker_2eproto
