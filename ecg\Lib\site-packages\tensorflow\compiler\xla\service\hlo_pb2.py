# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/compiler/xla/service/hlo.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.compiler.xla import xla_data_pb2 as tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/compiler/xla/service/hlo.proto',
  package='xla',
  syntax='proto3',
  serialized_options=_b('\370\001\001'),
  serialized_pb=_b('\n)tensorflow/compiler/xla/service/hlo.proto\x12\x03xla\x1a&tensorflow/compiler/xla/xla_data.proto\"\xe3\x14\n\x13HloInstructionProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06opcode\x18\x02 \x01(\t\x12\x1e\n\x05shape\x18\x03 \x01(\x0b\x32\x0f.xla.ShapeProto\x12!\n\x08metadata\x18\x07 \x01(\x0b\x32\x0f.xla.OpMetadata\x12\"\n\x07literal\x18\x08 \x01(\x0b\x32\x11.xla.LiteralProto\x12\x18\n\x10parameter_number\x18\t \x01(\x03\x12\x13\n\x0b\x66usion_kind\x18\x0b \x01(\t\x12\x13\n\x0btuple_index\x18\r \x01(\x03\x12\x12\n\ndimensions\x18\x0e \x03(\x03\x12\x1b\n\x06window\x18\x0f \x01(\x0b\x32\x0b.xla.Window\x12G\n\x1d\x63onvolution_dimension_numbers\x18\x10 \x01(\x0b\x32 .xla.ConvolutionDimensionNumbers\x12\x1b\n\x13\x66\x65\x61ture_group_count\x18\x32 \x01(\x03\x12\x19\n\x11\x62\x61tch_group_count\x18: \x01(\x03\x12\x42\n\x10slice_dimensions\x18\x11 \x03(\x0b\x32(.xla.HloInstructionProto.SliceDimensions\x12\x15\n\rexponent_bits\x18\x12 \x01(\x05\x12\x15\n\rmantissa_bits\x18\x13 \x01(\x05\x12\x1b\n\x13\x64ynamic_slice_sizes\x18\x14 \x03(\x03\x12*\n\x0epadding_config\x18\x15 \x01(\x0b\x32\x12.xla.PaddingConfig\x12\x16\n\x0eoutfeed_config\x18\x16 \x01(\x0c\x12-\n\x0c\x64istribution\x18\x17 \x01(\x0e\x32\x17.xla.RandomDistribution\x12\x0f\n\x07\x65psilon\x18\x18 \x01(\x02\x12\x15\n\rfeature_index\x18\x19 \x01(\x03\x12\x12\n\nchannel_id\x18\x1a \x01(\x03\x12\x15\n\rinfeed_config\x18\x1b \x01(\x0c\x12\x1a\n\x12\x63ustom_call_target\x18\x1c \x01(\t\x12&\n\routfeed_shape\x18\x1d \x01(\x0b\x32\x0f.xla.ShapeProto\x12\x37\n\x15\x64ot_dimension_numbers\x18\x1e \x01(\x0b\x32\x18.xla.DotDimensionNumbers\x12\x1e\n\x08\x66\x66t_type\x18\x1f \x01(\x0e\x32\x0c.xla.FftType\x12\x12\n\nfft_length\x18  \x03(\x03\x12\x1c\n\x14\x63omparison_direction\x18? \x01(\t\x12=\n\x18gather_dimension_numbers\x18! \x01(\x0b\x32\x1b.xla.GatherDimensionNumbers\x12\x1a\n\x12gather_slice_sizes\x18\" \x03(\x03\x12\x14\n\x0c\x63hannel_name\x18) \x01(\t\x12\x18\n\x10\x63ost_estimate_ns\x18* \x01(\x03\x12\n\n\x02id\x18# \x01(\x03\x12\x13\n\x0boperand_ids\x18$ \x03(\x03\x12\x1f\n\x17\x63ontrol_predecessor_ids\x18% \x03(\x03\x12\x1e\n\x16\x63\x61lled_computation_ids\x18& \x03(\x03\x12!\n\x08sharding\x18( \x01(\x0b\x32\x0f.xla.OpSharding\x12\x16\n\x0e\x62\x61\x63kend_config\x18+ \x01(\x0c\x12)\n\x0ereplica_groups\x18\x31 \x03(\x0b\x32\x11.xla.ReplicaGroup\x12\x19\n\rall_reduce_id\x18- \x01(\x03\x42\x02\x18\x01\x12\x1d\n\x15use_global_device_ids\x18G \x01(\x08\x12\x18\n\x10is_host_transfer\x18/ \x01(\x08\x12\x11\n\tis_stable\x18< \x01(\x08\x12?\n\x19scatter_dimension_numbers\x18\x30 \x01(\x0b\x32\x1c.xla.ScatterDimensionNumbers\x12.\n\x10precision_config\x18\x33 \x01(\x0b\x32\x14.xla.PrecisionConfig\x12.\n\x13source_target_pairs\x18\x34 \x03(\x0b\x32\x11.xla.SourceTarget\x12.\n\x15\x64omain_entry_sharding\x18\x36 \x01(\x0b\x32\x0f.xla.OpSharding\x12-\n\x14\x64omain_exit_sharding\x18\x37 \x01(\x0b\x32\x0f.xla.OpSharding\x12\x18\n\x10\x63onstrain_layout\x18\x38 \x01(\x08\x12\x33\n\x1aoperand_shapes_with_layout\x18\x39 \x03(\x0b\x32\x0f.xla.ShapeProto\x12=\n\x18triangular_solve_options\x18; \x01(\x0b\x32\x1b.xla.TriangularSolveOptions\x12.\n\x10\x63holesky_options\x18> \x01(\x0b\x32\x14.xla.CholeskyOptions\x12\x38\n\x15parameter_replication\x18= \x01(\x0b\x32\x19.xla.ParameterReplication\x12\"\n\x1aouter_dimension_partitions\x18@ \x03(\x03\x12#\n\x1b\x63ustom_call_has_side_effect\x18\x41 \x01(\x08\x12Q\n#custom_call_output_operand_aliasing\x18J \x03(\x0b\x32$.xla.CustomCallOutputOperandAliasing\x12\x35\n\x14\x63ustom_call_schedule\x18L \x01(\x0e\x32\x17.xla.CustomCallSchedule\x12\r\n\x05\x64\x65lta\x18\x42 \x01(\x03\x12\x1a\n\x12indices_are_sorted\x18\x43 \x01(\x08\x12\x34\n\x13\x66rontend_attributes\x18\x44 \x01(\x0b\x32\x17.xla.FrontendAttributes\x12\x16\n\x0eunique_indices\x18\x45 \x01(\x08\x12+\n\rrng_algorithm\x18\x46 \x01(\x0e\x32\x14.xla.RandomAlgorithm\x12\x17\n\x0f\x63omparison_type\x18H \x01(\t\x12!\n\x19is_cross_program_prefetch\x18I \x01(\x08\x12&\n\x0cpadding_type\x18K \x01(\x0e\x32\x10.xla.PaddingType\x1a?\n\x0fSliceDimensions\x12\r\n\x05start\x18\x01 \x01(\x03\x12\r\n\x05limit\x18\x02 \x01(\x03\x12\x0e\n\x06stride\x18\x03 \x01(\x03J\x04\x08\n\x10\x0bJ\x04\x08\x0c\x10\rJ\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06J\x04\x08\x06\x10\x07J\x04\x08,\x10-J\x04\x08\x35\x10\x36J\x04\x08.\x10/R\x0eparameter_nameR\x1e\x66used_instructions_computationR\roperand_namesR\x19\x63ontrol_predecessor_namesR\x18\x63\x61lled_computation_namesR\x11replica_group_idsR\x12\x63ustom_call_opaqueR\x12\x61ll_reduce_barrier\"\xb0\x01\n\x13HloComputationProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12.\n\x0cinstructions\x18\x02 \x03(\x0b\x32\x18.xla.HloInstructionProto\x12-\n\rprogram_shape\x18\x04 \x01(\x0b\x32\x16.xla.ProgramShapeProto\x12\n\n\x02id\x18\x05 \x01(\x03\x12\x0f\n\x07root_id\x18\x06 \x01(\x03J\x04\x08\x03\x10\x04R\troot_name\"\xd8\x01\n\x10HloScheduleProto\x12\x37\n\tsequences\x18\x01 \x03(\x0b\x32$.xla.HloScheduleProto.SequencesEntry\x1a.\n\x13InstructionSequence\x12\x17\n\x0finstruction_ids\x18\x01 \x03(\x03\x1a[\n\x0eSequencesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x38\n\x05value\x18\x02 \x01(\x0b\x32).xla.HloScheduleProto.InstructionSequence:\x02\x38\x01\"\xdb\x01\n\x18HloInputOutputAliasProto\x12>\n\x07\x65ntries\x18\x01 \x03(\x0b\x32-.xla.HloInputOutputAliasProto.AliasEntryProto\x1a\x7f\n\x0f\x41liasEntryProto\x12\x1a\n\x12output_shape_index\x18\x01 \x03(\x03\x12\x18\n\x10parameter_number\x18\x02 \x01(\x03\x12\x1d\n\x15parameter_shape_index\x18\x03 \x03(\x03\x12\x17\n\x04kind\x18\x04 \x01(\x0e\x32\t.xla.Kind\"\xf2\x01\n\x1c\x44ynamicParameterBindingProto\x12:\n\x07\x65ntries\x18\x01 \x03(\x0b\x32).xla.DynamicParameterBindingProto.Binding\x1a\x95\x01\n\x07\x42inding\x12\x19\n\x11\x64ynamic_param_num\x18\x01 \x01(\x03\x12\x1b\n\x13\x64ynamic_param_index\x18\x02 \x03(\x03\x12\x18\n\x10target_param_num\x18\x03 \x01(\x03\x12\x1a\n\x12target_param_index\x18\x04 \x03(\x03\x12\x1c\n\x14target_param_dim_num\x18\x05 \x01(\x03\"8\n\x14\x43rossProgramPrefetch\x12\x11\n\tparameter\x18\x01 \x01(\x03\x12\r\n\x05index\x18\x02 \x03(\x03\"\xc7\x03\n\x0eHloModuleProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x1e\n\x16\x65ntry_computation_name\x18\x02 \x01(\t\x12\x1c\n\x14\x65ntry_computation_id\x18\x06 \x01(\x03\x12.\n\x0c\x63omputations\x18\x03 \x03(\x0b\x32\x18.xla.HloComputationProto\x12\x32\n\x12host_program_shape\x18\x04 \x01(\x0b\x32\x16.xla.ProgramShapeProto\x12\n\n\x02id\x18\x05 \x01(\x03\x12\'\n\x08schedule\x18\x07 \x01(\x0b\x32\x15.xla.HloScheduleProto\x12\x39\n\x12input_output_alias\x18\x08 \x01(\x0b\x32\x1d.xla.HloInputOutputAliasProto\x12\x44\n\x19\x64ynamic_parameter_binding\x18\t \x01(\x0b\x32!.xla.DynamicParameterBindingProto\x12;\n\x18\x63ross_program_prefetches\x18\n \x03(\x0b\x32\x19.xla.CrossProgramPrefetch\x12\x12\n\nis_dynamic\x18\x0b \x01(\x08\"\xc8\x01\n\x12LogicalBufferProto\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x0c\n\x04size\x18\x02 \x01(\x03\x12\x34\n\ndefined_at\x18\x03 \x01(\x0b\x32 .xla.LogicalBufferProto.Location\x12\r\n\x05\x63olor\x18\x04 \x01(\x03\x1aS\n\x08Location\x12\x18\n\x10\x63omputation_name\x18\x01 \x01(\t\x12\x18\n\x10instruction_name\x18\x02 \x01(\t\x12\x13\n\x0bshape_index\x18\x03 \x03(\x03\"\xf8\x02\n\x15\x42ufferAllocationProto\x12\r\n\x05index\x18\x01 \x01(\x03\x12\x0c\n\x04size\x18\x02 \x01(\x03\x12\x17\n\x0fis_thread_local\x18\x03 \x01(\x08\x12\x10\n\x08is_tuple\x18\x0b \x01(\x08\x12&\n\x1eis_entry_computation_parameter\x18\x05 \x01(\x08\x12\x13\n\x0bis_constant\x18\x0c \x01(\x08\x12\x18\n\x10parameter_number\x18\x06 \x01(\x03\x12\x1d\n\x15parameter_shape_index\x18\n \x03(\x03\x12\x16\n\x0emaybe_live_out\x18\x07 \x01(\x08\x12\r\n\x05\x63olor\x18\x08 \x01(\x03\x12\x35\n\x08\x61ssigned\x18\t \x03(\x0b\x32#.xla.BufferAllocationProto.Assigned\x1a\x43\n\x08\x41ssigned\x12\x19\n\x11logical_buffer_id\x18\x01 \x01(\x03\x12\x0e\n\x06offset\x18\x02 \x01(\x03\x12\x0c\n\x04size\x18\x03 \x01(\x03\"\xd6\x02\n\x12HeapSimulatorTrace\x12-\n\x06\x65vents\x18\x01 \x03(\x0b\x32\x1d.xla.HeapSimulatorTrace.Event\x12\x1f\n\x17whole_module_simulation\x18\x02 \x01(\x08\x12\x1f\n\x17\x62uffer_allocation_index\x18\x03 \x01(\x03\x1a\xce\x01\n\x05\x45vent\x12\x30\n\x04kind\x18\x01 \x01(\x0e\x32\".xla.HeapSimulatorTrace.Event.Kind\x12\x11\n\tbuffer_id\x18\x02 \x01(\x03\x12\x18\n\x10\x63omputation_name\x18\x03 \x01(\t\x12\x18\n\x10instruction_name\x18\x04 \x01(\t\x12\x1f\n\x17share_with_canonical_id\x18\x05 \x01(\x03\"+\n\x04Kind\x12\t\n\x05\x41LLOC\x10\x00\x12\x08\n\x04\x46REE\x10\x01\x12\x0e\n\nSHARE_WITH\x10\x02\"M\n\x13HloModuleGroupProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12(\n\x0bhlo_modules\x18\x02 \x03(\x0b\x32\x13.xla.HloModuleProto\"\xd6\x02\n\x15\x42ufferAssignmentProto\x12\x30\n\x0flogical_buffers\x18\x01 \x03(\x0b\x32\x17.xla.LogicalBufferProto\x12>\n\x0e\x62uffer_aliases\x18\x02 \x03(\x0b\x32&.xla.BufferAssignmentProto.BufferAlias\x12\x36\n\x12\x62uffer_allocations\x18\x03 \x03(\x0b\x32\x1a.xla.BufferAllocationProto\x12\x36\n\x15heap_simulator_traces\x18\x04 \x03(\x0b\x32\x17.xla.HeapSimulatorTrace\x1a[\n\x0b\x42ufferAlias\x12\x18\n\x10source_buffer_id\x18\x01 \x01(\x03\x12\x32\n\x08location\x18\x02 \x01(\x0b\x32 .xla.LogicalBufferProto.Location\"~\n\x08HloProto\x12\'\n\nhlo_module\x18\x01 \x01(\x0b\x32\x13.xla.HloModuleProto\x12\x35\n\x11\x62uffer_assignment\x18\x03 \x01(\x0b\x32\x1a.xla.BufferAssignmentProtoJ\x04\x08\x02\x10\x03R\x0chlo_ordering\"\x8e\x01\n\x0bHloSnapshot\x12\x1a\n\x03hlo\x18\x01 \x01(\x0b\x32\r.xla.HloProto\x12$\n\targuments\x18\x02 \x03(\x0b\x32\x11.xla.LiteralProto\x12!\n\x06result\x18\x03 \x01(\x0b\x32\x11.xla.LiteralProto\x12\x1a\n\x12\x65xecution_platform\x18\x04 \x01(\t\"\xb9\x01\n\x16HloModuleMetadataProto\x12\x1b\n\x13\x63\x61nonical_module_id\x18\x01 \x01(\x03\x12\x19\n\x11module_group_name\x18\x02 \x01(\t\x12\x1a\n\x12original_module_id\x18\x03 \x01(\x03\x12\x1e\n\x16partitioned_module_ids\x18\x04 \x03(\x03\x12+\n\rpass_metadata\x18\x05 \x03(\x0b\x32\x14.xla.HloPassMetadata\"\xea\x01\n\x0fHloPassMetadata\x12\x0f\n\x07pass_id\x18\x01 \x01(\x03\x12\x11\n\tpass_name\x18\x02 \x01(\t\x12\x15\n\rpipeline_name\x18\x03 \x01(\t\x12\x16\n\x0e\x64ump_filenames\x18\x04 \x03(\t\x12\x16\n\x0emodule_changed\x18\x05 \x01(\x08\x12\x11\n\tmodule_id\x18\x06 \x01(\x03\x12\x1f\n\x17module_group_module_ids\x18\x07 \x03(\x03\x12\x1c\n\x14start_timestamp_usec\x18\x08 \x01(\x03\x12\x1a\n\x12\x65nd_timestamp_usec\x18\t \x01(\x03*S\n\x12\x43ustomCallSchedule\x12\x11\n\rSCHEDULE_NONE\x10\x00\x12\x13\n\x0fSCHEDULE_LATEST\x10\x01\x12\x15\n\x11SCHEDULE_EARLIEST\x10\x02*:\n\x04Kind\x12\x13\n\x0fUNDEFINED_ALIAS\x10\x00\x12\r\n\tMAY_ALIAS\x10\x01\x12\x0e\n\nMUST_ALIAS\x10\x02\x42\x03\xf8\x01\x01\x62\x06proto3')
  ,
  dependencies=[tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2.DESCRIPTOR,])

_CUSTOMCALLSCHEDULE = _descriptor.EnumDescriptor(
  name='CustomCallSchedule',
  full_name='xla.CustomCallSchedule',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SCHEDULE_NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SCHEDULE_LATEST', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SCHEDULE_EARLIEST', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=6182,
  serialized_end=6265,
)
_sym_db.RegisterEnumDescriptor(_CUSTOMCALLSCHEDULE)

CustomCallSchedule = enum_type_wrapper.EnumTypeWrapper(_CUSTOMCALLSCHEDULE)
_KIND = _descriptor.EnumDescriptor(
  name='Kind',
  full_name='xla.Kind',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNDEFINED_ALIAS', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MAY_ALIAS', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MUST_ALIAS', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=6267,
  serialized_end=6325,
)
_sym_db.RegisterEnumDescriptor(_KIND)

Kind = enum_type_wrapper.EnumTypeWrapper(_KIND)
SCHEDULE_NONE = 0
SCHEDULE_LATEST = 1
SCHEDULE_EARLIEST = 2
UNDEFINED_ALIAS = 0
MAY_ALIAS = 1
MUST_ALIAS = 2


_HEAPSIMULATORTRACE_EVENT_KIND = _descriptor.EnumDescriptor(
  name='Kind',
  full_name='xla.HeapSimulatorTrace.Event.Kind',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ALLOC', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FREE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SHARE_WITH', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=5015,
  serialized_end=5058,
)
_sym_db.RegisterEnumDescriptor(_HEAPSIMULATORTRACE_EVENT_KIND)


_HLOINSTRUCTIONPROTO_SLICEDIMENSIONS = _descriptor.Descriptor(
  name='SliceDimensions',
  full_name='xla.HloInstructionProto.SliceDimensions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start', full_name='xla.HloInstructionProto.SliceDimensions.start', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='xla.HloInstructionProto.SliceDimensions.limit', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stride', full_name='xla.HloInstructionProto.SliceDimensions.stride', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2464,
  serialized_end=2527,
)

_HLOINSTRUCTIONPROTO = _descriptor.Descriptor(
  name='HloInstructionProto',
  full_name='xla.HloInstructionProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='xla.HloInstructionProto.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opcode', full_name='xla.HloInstructionProto.opcode', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shape', full_name='xla.HloInstructionProto.shape', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='metadata', full_name='xla.HloInstructionProto.metadata', index=3,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='literal', full_name='xla.HloInstructionProto.literal', index=4,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parameter_number', full_name='xla.HloInstructionProto.parameter_number', index=5,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fusion_kind', full_name='xla.HloInstructionProto.fusion_kind', index=6,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tuple_index', full_name='xla.HloInstructionProto.tuple_index', index=7,
      number=13, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='xla.HloInstructionProto.dimensions', index=8,
      number=14, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='window', full_name='xla.HloInstructionProto.window', index=9,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convolution_dimension_numbers', full_name='xla.HloInstructionProto.convolution_dimension_numbers', index=10,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='feature_group_count', full_name='xla.HloInstructionProto.feature_group_count', index=11,
      number=50, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_group_count', full_name='xla.HloInstructionProto.batch_group_count', index=12,
      number=58, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='slice_dimensions', full_name='xla.HloInstructionProto.slice_dimensions', index=13,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exponent_bits', full_name='xla.HloInstructionProto.exponent_bits', index=14,
      number=18, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mantissa_bits', full_name='xla.HloInstructionProto.mantissa_bits', index=15,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dynamic_slice_sizes', full_name='xla.HloInstructionProto.dynamic_slice_sizes', index=16,
      number=20, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='padding_config', full_name='xla.HloInstructionProto.padding_config', index=17,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outfeed_config', full_name='xla.HloInstructionProto.outfeed_config', index=18,
      number=22, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution', full_name='xla.HloInstructionProto.distribution', index=19,
      number=23, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='epsilon', full_name='xla.HloInstructionProto.epsilon', index=20,
      number=24, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='feature_index', full_name='xla.HloInstructionProto.feature_index', index=21,
      number=25, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_id', full_name='xla.HloInstructionProto.channel_id', index=22,
      number=26, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infeed_config', full_name='xla.HloInstructionProto.infeed_config', index=23,
      number=27, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='custom_call_target', full_name='xla.HloInstructionProto.custom_call_target', index=24,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outfeed_shape', full_name='xla.HloInstructionProto.outfeed_shape', index=25,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dot_dimension_numbers', full_name='xla.HloInstructionProto.dot_dimension_numbers', index=26,
      number=30, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fft_type', full_name='xla.HloInstructionProto.fft_type', index=27,
      number=31, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fft_length', full_name='xla.HloInstructionProto.fft_length', index=28,
      number=32, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='comparison_direction', full_name='xla.HloInstructionProto.comparison_direction', index=29,
      number=63, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gather_dimension_numbers', full_name='xla.HloInstructionProto.gather_dimension_numbers', index=30,
      number=33, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gather_slice_sizes', full_name='xla.HloInstructionProto.gather_slice_sizes', index=31,
      number=34, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_name', full_name='xla.HloInstructionProto.channel_name', index=32,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_estimate_ns', full_name='xla.HloInstructionProto.cost_estimate_ns', index=33,
      number=42, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='xla.HloInstructionProto.id', index=34,
      number=35, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operand_ids', full_name='xla.HloInstructionProto.operand_ids', index=35,
      number=36, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='control_predecessor_ids', full_name='xla.HloInstructionProto.control_predecessor_ids', index=36,
      number=37, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='called_computation_ids', full_name='xla.HloInstructionProto.called_computation_ids', index=37,
      number=38, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sharding', full_name='xla.HloInstructionProto.sharding', index=38,
      number=40, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='backend_config', full_name='xla.HloInstructionProto.backend_config', index=39,
      number=43, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='replica_groups', full_name='xla.HloInstructionProto.replica_groups', index=40,
      number=49, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_reduce_id', full_name='xla.HloInstructionProto.all_reduce_id', index=41,
      number=45, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\030\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_global_device_ids', full_name='xla.HloInstructionProto.use_global_device_ids', index=42,
      number=71, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_host_transfer', full_name='xla.HloInstructionProto.is_host_transfer', index=43,
      number=47, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_stable', full_name='xla.HloInstructionProto.is_stable', index=44,
      number=60, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scatter_dimension_numbers', full_name='xla.HloInstructionProto.scatter_dimension_numbers', index=45,
      number=48, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='precision_config', full_name='xla.HloInstructionProto.precision_config', index=46,
      number=51, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_target_pairs', full_name='xla.HloInstructionProto.source_target_pairs', index=47,
      number=52, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain_entry_sharding', full_name='xla.HloInstructionProto.domain_entry_sharding', index=48,
      number=54, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain_exit_sharding', full_name='xla.HloInstructionProto.domain_exit_sharding', index=49,
      number=55, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='constrain_layout', full_name='xla.HloInstructionProto.constrain_layout', index=50,
      number=56, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operand_shapes_with_layout', full_name='xla.HloInstructionProto.operand_shapes_with_layout', index=51,
      number=57, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='triangular_solve_options', full_name='xla.HloInstructionProto.triangular_solve_options', index=52,
      number=59, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cholesky_options', full_name='xla.HloInstructionProto.cholesky_options', index=53,
      number=62, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parameter_replication', full_name='xla.HloInstructionProto.parameter_replication', index=54,
      number=61, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outer_dimension_partitions', full_name='xla.HloInstructionProto.outer_dimension_partitions', index=55,
      number=64, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='custom_call_has_side_effect', full_name='xla.HloInstructionProto.custom_call_has_side_effect', index=56,
      number=65, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='custom_call_output_operand_aliasing', full_name='xla.HloInstructionProto.custom_call_output_operand_aliasing', index=57,
      number=74, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='custom_call_schedule', full_name='xla.HloInstructionProto.custom_call_schedule', index=58,
      number=76, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delta', full_name='xla.HloInstructionProto.delta', index=59,
      number=66, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='indices_are_sorted', full_name='xla.HloInstructionProto.indices_are_sorted', index=60,
      number=67, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='frontend_attributes', full_name='xla.HloInstructionProto.frontend_attributes', index=61,
      number=68, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unique_indices', full_name='xla.HloInstructionProto.unique_indices', index=62,
      number=69, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rng_algorithm', full_name='xla.HloInstructionProto.rng_algorithm', index=63,
      number=70, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='comparison_type', full_name='xla.HloInstructionProto.comparison_type', index=64,
      number=72, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_cross_program_prefetch', full_name='xla.HloInstructionProto.is_cross_program_prefetch', index=65,
      number=73, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='padding_type', full_name='xla.HloInstructionProto.padding_type', index=66,
      number=75, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_HLOINSTRUCTIONPROTO_SLICEDIMENSIONS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=91,
  serialized_end=2750,
)


_HLOCOMPUTATIONPROTO = _descriptor.Descriptor(
  name='HloComputationProto',
  full_name='xla.HloComputationProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='xla.HloComputationProto.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='instructions', full_name='xla.HloComputationProto.instructions', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='program_shape', full_name='xla.HloComputationProto.program_shape', index=2,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='xla.HloComputationProto.id', index=3,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='root_id', full_name='xla.HloComputationProto.root_id', index=4,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2753,
  serialized_end=2929,
)


_HLOSCHEDULEPROTO_INSTRUCTIONSEQUENCE = _descriptor.Descriptor(
  name='InstructionSequence',
  full_name='xla.HloScheduleProto.InstructionSequence',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='instruction_ids', full_name='xla.HloScheduleProto.InstructionSequence.instruction_ids', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3009,
  serialized_end=3055,
)

_HLOSCHEDULEPROTO_SEQUENCESENTRY = _descriptor.Descriptor(
  name='SequencesEntry',
  full_name='xla.HloScheduleProto.SequencesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='xla.HloScheduleProto.SequencesEntry.key', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='xla.HloScheduleProto.SequencesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3057,
  serialized_end=3148,
)

_HLOSCHEDULEPROTO = _descriptor.Descriptor(
  name='HloScheduleProto',
  full_name='xla.HloScheduleProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sequences', full_name='xla.HloScheduleProto.sequences', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_HLOSCHEDULEPROTO_INSTRUCTIONSEQUENCE, _HLOSCHEDULEPROTO_SEQUENCESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2932,
  serialized_end=3148,
)


_HLOINPUTOUTPUTALIASPROTO_ALIASENTRYPROTO = _descriptor.Descriptor(
  name='AliasEntryProto',
  full_name='xla.HloInputOutputAliasProto.AliasEntryProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='output_shape_index', full_name='xla.HloInputOutputAliasProto.AliasEntryProto.output_shape_index', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parameter_number', full_name='xla.HloInputOutputAliasProto.AliasEntryProto.parameter_number', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parameter_shape_index', full_name='xla.HloInputOutputAliasProto.AliasEntryProto.parameter_shape_index', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kind', full_name='xla.HloInputOutputAliasProto.AliasEntryProto.kind', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3243,
  serialized_end=3370,
)

_HLOINPUTOUTPUTALIASPROTO = _descriptor.Descriptor(
  name='HloInputOutputAliasProto',
  full_name='xla.HloInputOutputAliasProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='entries', full_name='xla.HloInputOutputAliasProto.entries', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_HLOINPUTOUTPUTALIASPROTO_ALIASENTRYPROTO, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3151,
  serialized_end=3370,
)


_DYNAMICPARAMETERBINDINGPROTO_BINDING = _descriptor.Descriptor(
  name='Binding',
  full_name='xla.DynamicParameterBindingProto.Binding',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dynamic_param_num', full_name='xla.DynamicParameterBindingProto.Binding.dynamic_param_num', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dynamic_param_index', full_name='xla.DynamicParameterBindingProto.Binding.dynamic_param_index', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_param_num', full_name='xla.DynamicParameterBindingProto.Binding.target_param_num', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_param_index', full_name='xla.DynamicParameterBindingProto.Binding.target_param_index', index=3,
      number=4, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_param_dim_num', full_name='xla.DynamicParameterBindingProto.Binding.target_param_dim_num', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3466,
  serialized_end=3615,
)

_DYNAMICPARAMETERBINDINGPROTO = _descriptor.Descriptor(
  name='DynamicParameterBindingProto',
  full_name='xla.DynamicParameterBindingProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='entries', full_name='xla.DynamicParameterBindingProto.entries', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_DYNAMICPARAMETERBINDINGPROTO_BINDING, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3373,
  serialized_end=3615,
)


_CROSSPROGRAMPREFETCH = _descriptor.Descriptor(
  name='CrossProgramPrefetch',
  full_name='xla.CrossProgramPrefetch',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='parameter', full_name='xla.CrossProgramPrefetch.parameter', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='index', full_name='xla.CrossProgramPrefetch.index', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3617,
  serialized_end=3673,
)


_HLOMODULEPROTO = _descriptor.Descriptor(
  name='HloModuleProto',
  full_name='xla.HloModuleProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='xla.HloModuleProto.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='entry_computation_name', full_name='xla.HloModuleProto.entry_computation_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='entry_computation_id', full_name='xla.HloModuleProto.entry_computation_id', index=2,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='computations', full_name='xla.HloModuleProto.computations', index=3,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_program_shape', full_name='xla.HloModuleProto.host_program_shape', index=4,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='xla.HloModuleProto.id', index=5,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule', full_name='xla.HloModuleProto.schedule', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='input_output_alias', full_name='xla.HloModuleProto.input_output_alias', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dynamic_parameter_binding', full_name='xla.HloModuleProto.dynamic_parameter_binding', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cross_program_prefetches', full_name='xla.HloModuleProto.cross_program_prefetches', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_dynamic', full_name='xla.HloModuleProto.is_dynamic', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3676,
  serialized_end=4131,
)


_LOGICALBUFFERPROTO_LOCATION = _descriptor.Descriptor(
  name='Location',
  full_name='xla.LogicalBufferProto.Location',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='computation_name', full_name='xla.LogicalBufferProto.Location.computation_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='instruction_name', full_name='xla.LogicalBufferProto.Location.instruction_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shape_index', full_name='xla.LogicalBufferProto.Location.shape_index', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4251,
  serialized_end=4334,
)

_LOGICALBUFFERPROTO = _descriptor.Descriptor(
  name='LogicalBufferProto',
  full_name='xla.LogicalBufferProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='xla.LogicalBufferProto.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='size', full_name='xla.LogicalBufferProto.size', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='defined_at', full_name='xla.LogicalBufferProto.defined_at', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='color', full_name='xla.LogicalBufferProto.color', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LOGICALBUFFERPROTO_LOCATION, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4134,
  serialized_end=4334,
)


_BUFFERALLOCATIONPROTO_ASSIGNED = _descriptor.Descriptor(
  name='Assigned',
  full_name='xla.BufferAllocationProto.Assigned',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='logical_buffer_id', full_name='xla.BufferAllocationProto.Assigned.logical_buffer_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='xla.BufferAllocationProto.Assigned.offset', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='size', full_name='xla.BufferAllocationProto.Assigned.size', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4646,
  serialized_end=4713,
)

_BUFFERALLOCATIONPROTO = _descriptor.Descriptor(
  name='BufferAllocationProto',
  full_name='xla.BufferAllocationProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='index', full_name='xla.BufferAllocationProto.index', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='size', full_name='xla.BufferAllocationProto.size', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_thread_local', full_name='xla.BufferAllocationProto.is_thread_local', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_tuple', full_name='xla.BufferAllocationProto.is_tuple', index=3,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_entry_computation_parameter', full_name='xla.BufferAllocationProto.is_entry_computation_parameter', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_constant', full_name='xla.BufferAllocationProto.is_constant', index=5,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parameter_number', full_name='xla.BufferAllocationProto.parameter_number', index=6,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parameter_shape_index', full_name='xla.BufferAllocationProto.parameter_shape_index', index=7,
      number=10, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maybe_live_out', full_name='xla.BufferAllocationProto.maybe_live_out', index=8,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='color', full_name='xla.BufferAllocationProto.color', index=9,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='assigned', full_name='xla.BufferAllocationProto.assigned', index=10,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_BUFFERALLOCATIONPROTO_ASSIGNED, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4337,
  serialized_end=4713,
)


_HEAPSIMULATORTRACE_EVENT = _descriptor.Descriptor(
  name='Event',
  full_name='xla.HeapSimulatorTrace.Event',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='kind', full_name='xla.HeapSimulatorTrace.Event.kind', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='buffer_id', full_name='xla.HeapSimulatorTrace.Event.buffer_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='computation_name', full_name='xla.HeapSimulatorTrace.Event.computation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='instruction_name', full_name='xla.HeapSimulatorTrace.Event.instruction_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='share_with_canonical_id', full_name='xla.HeapSimulatorTrace.Event.share_with_canonical_id', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _HEAPSIMULATORTRACE_EVENT_KIND,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4852,
  serialized_end=5058,
)

_HEAPSIMULATORTRACE = _descriptor.Descriptor(
  name='HeapSimulatorTrace',
  full_name='xla.HeapSimulatorTrace',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='events', full_name='xla.HeapSimulatorTrace.events', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='whole_module_simulation', full_name='xla.HeapSimulatorTrace.whole_module_simulation', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='buffer_allocation_index', full_name='xla.HeapSimulatorTrace.buffer_allocation_index', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_HEAPSIMULATORTRACE_EVENT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4716,
  serialized_end=5058,
)


_HLOMODULEGROUPPROTO = _descriptor.Descriptor(
  name='HloModuleGroupProto',
  full_name='xla.HloModuleGroupProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='xla.HloModuleGroupProto.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hlo_modules', full_name='xla.HloModuleGroupProto.hlo_modules', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5060,
  serialized_end=5137,
)


_BUFFERASSIGNMENTPROTO_BUFFERALIAS = _descriptor.Descriptor(
  name='BufferAlias',
  full_name='xla.BufferAssignmentProto.BufferAlias',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source_buffer_id', full_name='xla.BufferAssignmentProto.BufferAlias.source_buffer_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='location', full_name='xla.BufferAssignmentProto.BufferAlias.location', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5391,
  serialized_end=5482,
)

_BUFFERASSIGNMENTPROTO = _descriptor.Descriptor(
  name='BufferAssignmentProto',
  full_name='xla.BufferAssignmentProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='logical_buffers', full_name='xla.BufferAssignmentProto.logical_buffers', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='buffer_aliases', full_name='xla.BufferAssignmentProto.buffer_aliases', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='buffer_allocations', full_name='xla.BufferAssignmentProto.buffer_allocations', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='heap_simulator_traces', full_name='xla.BufferAssignmentProto.heap_simulator_traces', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_BUFFERASSIGNMENTPROTO_BUFFERALIAS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5140,
  serialized_end=5482,
)


_HLOPROTO = _descriptor.Descriptor(
  name='HloProto',
  full_name='xla.HloProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hlo_module', full_name='xla.HloProto.hlo_module', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='buffer_assignment', full_name='xla.HloProto.buffer_assignment', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5484,
  serialized_end=5610,
)


_HLOSNAPSHOT = _descriptor.Descriptor(
  name='HloSnapshot',
  full_name='xla.HloSnapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hlo', full_name='xla.HloSnapshot.hlo', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arguments', full_name='xla.HloSnapshot.arguments', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='xla.HloSnapshot.result', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='execution_platform', full_name='xla.HloSnapshot.execution_platform', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5613,
  serialized_end=5755,
)


_HLOMODULEMETADATAPROTO = _descriptor.Descriptor(
  name='HloModuleMetadataProto',
  full_name='xla.HloModuleMetadataProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='canonical_module_id', full_name='xla.HloModuleMetadataProto.canonical_module_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module_group_name', full_name='xla.HloModuleMetadataProto.module_group_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_module_id', full_name='xla.HloModuleMetadataProto.original_module_id', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partitioned_module_ids', full_name='xla.HloModuleMetadataProto.partitioned_module_ids', index=3,
      number=4, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pass_metadata', full_name='xla.HloModuleMetadataProto.pass_metadata', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5758,
  serialized_end=5943,
)


_HLOPASSMETADATA = _descriptor.Descriptor(
  name='HloPassMetadata',
  full_name='xla.HloPassMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pass_id', full_name='xla.HloPassMetadata.pass_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pass_name', full_name='xla.HloPassMetadata.pass_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pipeline_name', full_name='xla.HloPassMetadata.pipeline_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dump_filenames', full_name='xla.HloPassMetadata.dump_filenames', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module_changed', full_name='xla.HloPassMetadata.module_changed', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module_id', full_name='xla.HloPassMetadata.module_id', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module_group_module_ids', full_name='xla.HloPassMetadata.module_group_module_ids', index=6,
      number=7, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_timestamp_usec', full_name='xla.HloPassMetadata.start_timestamp_usec', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_timestamp_usec', full_name='xla.HloPassMetadata.end_timestamp_usec', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5946,
  serialized_end=6180,
)

_HLOINSTRUCTIONPROTO_SLICEDIMENSIONS.containing_type = _HLOINSTRUCTIONPROTO
_HLOINSTRUCTIONPROTO.fields_by_name['shape'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._SHAPEPROTO
_HLOINSTRUCTIONPROTO.fields_by_name['metadata'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._OPMETADATA
_HLOINSTRUCTIONPROTO.fields_by_name['literal'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._LITERALPROTO
_HLOINSTRUCTIONPROTO.fields_by_name['window'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._WINDOW
_HLOINSTRUCTIONPROTO.fields_by_name['convolution_dimension_numbers'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._CONVOLUTIONDIMENSIONNUMBERS
_HLOINSTRUCTIONPROTO.fields_by_name['slice_dimensions'].message_type = _HLOINSTRUCTIONPROTO_SLICEDIMENSIONS
_HLOINSTRUCTIONPROTO.fields_by_name['padding_config'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._PADDINGCONFIG
_HLOINSTRUCTIONPROTO.fields_by_name['distribution'].enum_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._RANDOMDISTRIBUTION
_HLOINSTRUCTIONPROTO.fields_by_name['outfeed_shape'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._SHAPEPROTO
_HLOINSTRUCTIONPROTO.fields_by_name['dot_dimension_numbers'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._DOTDIMENSIONNUMBERS
_HLOINSTRUCTIONPROTO.fields_by_name['fft_type'].enum_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._FFTTYPE
_HLOINSTRUCTIONPROTO.fields_by_name['gather_dimension_numbers'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._GATHERDIMENSIONNUMBERS
_HLOINSTRUCTIONPROTO.fields_by_name['sharding'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._OPSHARDING
_HLOINSTRUCTIONPROTO.fields_by_name['replica_groups'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._REPLICAGROUP
_HLOINSTRUCTIONPROTO.fields_by_name['scatter_dimension_numbers'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._SCATTERDIMENSIONNUMBERS
_HLOINSTRUCTIONPROTO.fields_by_name['precision_config'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._PRECISIONCONFIG
_HLOINSTRUCTIONPROTO.fields_by_name['source_target_pairs'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._SOURCETARGET
_HLOINSTRUCTIONPROTO.fields_by_name['domain_entry_sharding'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._OPSHARDING
_HLOINSTRUCTIONPROTO.fields_by_name['domain_exit_sharding'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._OPSHARDING
_HLOINSTRUCTIONPROTO.fields_by_name['operand_shapes_with_layout'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._SHAPEPROTO
_HLOINSTRUCTIONPROTO.fields_by_name['triangular_solve_options'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._TRIANGULARSOLVEOPTIONS
_HLOINSTRUCTIONPROTO.fields_by_name['cholesky_options'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._CHOLESKYOPTIONS
_HLOINSTRUCTIONPROTO.fields_by_name['parameter_replication'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._PARAMETERREPLICATION
_HLOINSTRUCTIONPROTO.fields_by_name['custom_call_output_operand_aliasing'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._CUSTOMCALLOUTPUTOPERANDALIASING
_HLOINSTRUCTIONPROTO.fields_by_name['custom_call_schedule'].enum_type = _CUSTOMCALLSCHEDULE
_HLOINSTRUCTIONPROTO.fields_by_name['frontend_attributes'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._FRONTENDATTRIBUTES
_HLOINSTRUCTIONPROTO.fields_by_name['rng_algorithm'].enum_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._RANDOMALGORITHM
_HLOINSTRUCTIONPROTO.fields_by_name['padding_type'].enum_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._PADDINGTYPE
_HLOCOMPUTATIONPROTO.fields_by_name['instructions'].message_type = _HLOINSTRUCTIONPROTO
_HLOCOMPUTATIONPROTO.fields_by_name['program_shape'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._PROGRAMSHAPEPROTO
_HLOSCHEDULEPROTO_INSTRUCTIONSEQUENCE.containing_type = _HLOSCHEDULEPROTO
_HLOSCHEDULEPROTO_SEQUENCESENTRY.fields_by_name['value'].message_type = _HLOSCHEDULEPROTO_INSTRUCTIONSEQUENCE
_HLOSCHEDULEPROTO_SEQUENCESENTRY.containing_type = _HLOSCHEDULEPROTO
_HLOSCHEDULEPROTO.fields_by_name['sequences'].message_type = _HLOSCHEDULEPROTO_SEQUENCESENTRY
_HLOINPUTOUTPUTALIASPROTO_ALIASENTRYPROTO.fields_by_name['kind'].enum_type = _KIND
_HLOINPUTOUTPUTALIASPROTO_ALIASENTRYPROTO.containing_type = _HLOINPUTOUTPUTALIASPROTO
_HLOINPUTOUTPUTALIASPROTO.fields_by_name['entries'].message_type = _HLOINPUTOUTPUTALIASPROTO_ALIASENTRYPROTO
_DYNAMICPARAMETERBINDINGPROTO_BINDING.containing_type = _DYNAMICPARAMETERBINDINGPROTO
_DYNAMICPARAMETERBINDINGPROTO.fields_by_name['entries'].message_type = _DYNAMICPARAMETERBINDINGPROTO_BINDING
_HLOMODULEPROTO.fields_by_name['computations'].message_type = _HLOCOMPUTATIONPROTO
_HLOMODULEPROTO.fields_by_name['host_program_shape'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._PROGRAMSHAPEPROTO
_HLOMODULEPROTO.fields_by_name['schedule'].message_type = _HLOSCHEDULEPROTO
_HLOMODULEPROTO.fields_by_name['input_output_alias'].message_type = _HLOINPUTOUTPUTALIASPROTO
_HLOMODULEPROTO.fields_by_name['dynamic_parameter_binding'].message_type = _DYNAMICPARAMETERBINDINGPROTO
_HLOMODULEPROTO.fields_by_name['cross_program_prefetches'].message_type = _CROSSPROGRAMPREFETCH
_LOGICALBUFFERPROTO_LOCATION.containing_type = _LOGICALBUFFERPROTO
_LOGICALBUFFERPROTO.fields_by_name['defined_at'].message_type = _LOGICALBUFFERPROTO_LOCATION
_BUFFERALLOCATIONPROTO_ASSIGNED.containing_type = _BUFFERALLOCATIONPROTO
_BUFFERALLOCATIONPROTO.fields_by_name['assigned'].message_type = _BUFFERALLOCATIONPROTO_ASSIGNED
_HEAPSIMULATORTRACE_EVENT.fields_by_name['kind'].enum_type = _HEAPSIMULATORTRACE_EVENT_KIND
_HEAPSIMULATORTRACE_EVENT.containing_type = _HEAPSIMULATORTRACE
_HEAPSIMULATORTRACE_EVENT_KIND.containing_type = _HEAPSIMULATORTRACE_EVENT
_HEAPSIMULATORTRACE.fields_by_name['events'].message_type = _HEAPSIMULATORTRACE_EVENT
_HLOMODULEGROUPPROTO.fields_by_name['hlo_modules'].message_type = _HLOMODULEPROTO
_BUFFERASSIGNMENTPROTO_BUFFERALIAS.fields_by_name['location'].message_type = _LOGICALBUFFERPROTO_LOCATION
_BUFFERASSIGNMENTPROTO_BUFFERALIAS.containing_type = _BUFFERASSIGNMENTPROTO
_BUFFERASSIGNMENTPROTO.fields_by_name['logical_buffers'].message_type = _LOGICALBUFFERPROTO
_BUFFERASSIGNMENTPROTO.fields_by_name['buffer_aliases'].message_type = _BUFFERASSIGNMENTPROTO_BUFFERALIAS
_BUFFERASSIGNMENTPROTO.fields_by_name['buffer_allocations'].message_type = _BUFFERALLOCATIONPROTO
_BUFFERASSIGNMENTPROTO.fields_by_name['heap_simulator_traces'].message_type = _HEAPSIMULATORTRACE
_HLOPROTO.fields_by_name['hlo_module'].message_type = _HLOMODULEPROTO
_HLOPROTO.fields_by_name['buffer_assignment'].message_type = _BUFFERASSIGNMENTPROTO
_HLOSNAPSHOT.fields_by_name['hlo'].message_type = _HLOPROTO
_HLOSNAPSHOT.fields_by_name['arguments'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._LITERALPROTO
_HLOSNAPSHOT.fields_by_name['result'].message_type = tensorflow_dot_compiler_dot_xla_dot_xla__data__pb2._LITERALPROTO
_HLOMODULEMETADATAPROTO.fields_by_name['pass_metadata'].message_type = _HLOPASSMETADATA
DESCRIPTOR.message_types_by_name['HloInstructionProto'] = _HLOINSTRUCTIONPROTO
DESCRIPTOR.message_types_by_name['HloComputationProto'] = _HLOCOMPUTATIONPROTO
DESCRIPTOR.message_types_by_name['HloScheduleProto'] = _HLOSCHEDULEPROTO
DESCRIPTOR.message_types_by_name['HloInputOutputAliasProto'] = _HLOINPUTOUTPUTALIASPROTO
DESCRIPTOR.message_types_by_name['DynamicParameterBindingProto'] = _DYNAMICPARAMETERBINDINGPROTO
DESCRIPTOR.message_types_by_name['CrossProgramPrefetch'] = _CROSSPROGRAMPREFETCH
DESCRIPTOR.message_types_by_name['HloModuleProto'] = _HLOMODULEPROTO
DESCRIPTOR.message_types_by_name['LogicalBufferProto'] = _LOGICALBUFFERPROTO
DESCRIPTOR.message_types_by_name['BufferAllocationProto'] = _BUFFERALLOCATIONPROTO
DESCRIPTOR.message_types_by_name['HeapSimulatorTrace'] = _HEAPSIMULATORTRACE
DESCRIPTOR.message_types_by_name['HloModuleGroupProto'] = _HLOMODULEGROUPPROTO
DESCRIPTOR.message_types_by_name['BufferAssignmentProto'] = _BUFFERASSIGNMENTPROTO
DESCRIPTOR.message_types_by_name['HloProto'] = _HLOPROTO
DESCRIPTOR.message_types_by_name['HloSnapshot'] = _HLOSNAPSHOT
DESCRIPTOR.message_types_by_name['HloModuleMetadataProto'] = _HLOMODULEMETADATAPROTO
DESCRIPTOR.message_types_by_name['HloPassMetadata'] = _HLOPASSMETADATA
DESCRIPTOR.enum_types_by_name['CustomCallSchedule'] = _CUSTOMCALLSCHEDULE
DESCRIPTOR.enum_types_by_name['Kind'] = _KIND
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

HloInstructionProto = _reflection.GeneratedProtocolMessageType('HloInstructionProto', (_message.Message,), {

  'SliceDimensions' : _reflection.GeneratedProtocolMessageType('SliceDimensions', (_message.Message,), {
    'DESCRIPTOR' : _HLOINSTRUCTIONPROTO_SLICEDIMENSIONS,
    '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
    # @@protoc_insertion_point(class_scope:xla.HloInstructionProto.SliceDimensions)
    })
  ,
  'DESCRIPTOR' : _HLOINSTRUCTIONPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HloInstructionProto)
  })
_sym_db.RegisterMessage(HloInstructionProto)
_sym_db.RegisterMessage(HloInstructionProto.SliceDimensions)

HloComputationProto = _reflection.GeneratedProtocolMessageType('HloComputationProto', (_message.Message,), {
  'DESCRIPTOR' : _HLOCOMPUTATIONPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HloComputationProto)
  })
_sym_db.RegisterMessage(HloComputationProto)

HloScheduleProto = _reflection.GeneratedProtocolMessageType('HloScheduleProto', (_message.Message,), {

  'InstructionSequence' : _reflection.GeneratedProtocolMessageType('InstructionSequence', (_message.Message,), {
    'DESCRIPTOR' : _HLOSCHEDULEPROTO_INSTRUCTIONSEQUENCE,
    '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
    # @@protoc_insertion_point(class_scope:xla.HloScheduleProto.InstructionSequence)
    })
  ,

  'SequencesEntry' : _reflection.GeneratedProtocolMessageType('SequencesEntry', (_message.Message,), {
    'DESCRIPTOR' : _HLOSCHEDULEPROTO_SEQUENCESENTRY,
    '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
    # @@protoc_insertion_point(class_scope:xla.HloScheduleProto.SequencesEntry)
    })
  ,
  'DESCRIPTOR' : _HLOSCHEDULEPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HloScheduleProto)
  })
_sym_db.RegisterMessage(HloScheduleProto)
_sym_db.RegisterMessage(HloScheduleProto.InstructionSequence)
_sym_db.RegisterMessage(HloScheduleProto.SequencesEntry)

HloInputOutputAliasProto = _reflection.GeneratedProtocolMessageType('HloInputOutputAliasProto', (_message.Message,), {

  'AliasEntryProto' : _reflection.GeneratedProtocolMessageType('AliasEntryProto', (_message.Message,), {
    'DESCRIPTOR' : _HLOINPUTOUTPUTALIASPROTO_ALIASENTRYPROTO,
    '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
    # @@protoc_insertion_point(class_scope:xla.HloInputOutputAliasProto.AliasEntryProto)
    })
  ,
  'DESCRIPTOR' : _HLOINPUTOUTPUTALIASPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HloInputOutputAliasProto)
  })
_sym_db.RegisterMessage(HloInputOutputAliasProto)
_sym_db.RegisterMessage(HloInputOutputAliasProto.AliasEntryProto)

DynamicParameterBindingProto = _reflection.GeneratedProtocolMessageType('DynamicParameterBindingProto', (_message.Message,), {

  'Binding' : _reflection.GeneratedProtocolMessageType('Binding', (_message.Message,), {
    'DESCRIPTOR' : _DYNAMICPARAMETERBINDINGPROTO_BINDING,
    '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
    # @@protoc_insertion_point(class_scope:xla.DynamicParameterBindingProto.Binding)
    })
  ,
  'DESCRIPTOR' : _DYNAMICPARAMETERBINDINGPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.DynamicParameterBindingProto)
  })
_sym_db.RegisterMessage(DynamicParameterBindingProto)
_sym_db.RegisterMessage(DynamicParameterBindingProto.Binding)

CrossProgramPrefetch = _reflection.GeneratedProtocolMessageType('CrossProgramPrefetch', (_message.Message,), {
  'DESCRIPTOR' : _CROSSPROGRAMPREFETCH,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.CrossProgramPrefetch)
  })
_sym_db.RegisterMessage(CrossProgramPrefetch)

HloModuleProto = _reflection.GeneratedProtocolMessageType('HloModuleProto', (_message.Message,), {
  'DESCRIPTOR' : _HLOMODULEPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HloModuleProto)
  })
_sym_db.RegisterMessage(HloModuleProto)

LogicalBufferProto = _reflection.GeneratedProtocolMessageType('LogicalBufferProto', (_message.Message,), {

  'Location' : _reflection.GeneratedProtocolMessageType('Location', (_message.Message,), {
    'DESCRIPTOR' : _LOGICALBUFFERPROTO_LOCATION,
    '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
    # @@protoc_insertion_point(class_scope:xla.LogicalBufferProto.Location)
    })
  ,
  'DESCRIPTOR' : _LOGICALBUFFERPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.LogicalBufferProto)
  })
_sym_db.RegisterMessage(LogicalBufferProto)
_sym_db.RegisterMessage(LogicalBufferProto.Location)

BufferAllocationProto = _reflection.GeneratedProtocolMessageType('BufferAllocationProto', (_message.Message,), {

  'Assigned' : _reflection.GeneratedProtocolMessageType('Assigned', (_message.Message,), {
    'DESCRIPTOR' : _BUFFERALLOCATIONPROTO_ASSIGNED,
    '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
    # @@protoc_insertion_point(class_scope:xla.BufferAllocationProto.Assigned)
    })
  ,
  'DESCRIPTOR' : _BUFFERALLOCATIONPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.BufferAllocationProto)
  })
_sym_db.RegisterMessage(BufferAllocationProto)
_sym_db.RegisterMessage(BufferAllocationProto.Assigned)

HeapSimulatorTrace = _reflection.GeneratedProtocolMessageType('HeapSimulatorTrace', (_message.Message,), {

  'Event' : _reflection.GeneratedProtocolMessageType('Event', (_message.Message,), {
    'DESCRIPTOR' : _HEAPSIMULATORTRACE_EVENT,
    '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
    # @@protoc_insertion_point(class_scope:xla.HeapSimulatorTrace.Event)
    })
  ,
  'DESCRIPTOR' : _HEAPSIMULATORTRACE,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HeapSimulatorTrace)
  })
_sym_db.RegisterMessage(HeapSimulatorTrace)
_sym_db.RegisterMessage(HeapSimulatorTrace.Event)

HloModuleGroupProto = _reflection.GeneratedProtocolMessageType('HloModuleGroupProto', (_message.Message,), {
  'DESCRIPTOR' : _HLOMODULEGROUPPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HloModuleGroupProto)
  })
_sym_db.RegisterMessage(HloModuleGroupProto)

BufferAssignmentProto = _reflection.GeneratedProtocolMessageType('BufferAssignmentProto', (_message.Message,), {

  'BufferAlias' : _reflection.GeneratedProtocolMessageType('BufferAlias', (_message.Message,), {
    'DESCRIPTOR' : _BUFFERASSIGNMENTPROTO_BUFFERALIAS,
    '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
    # @@protoc_insertion_point(class_scope:xla.BufferAssignmentProto.BufferAlias)
    })
  ,
  'DESCRIPTOR' : _BUFFERASSIGNMENTPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.BufferAssignmentProto)
  })
_sym_db.RegisterMessage(BufferAssignmentProto)
_sym_db.RegisterMessage(BufferAssignmentProto.BufferAlias)

HloProto = _reflection.GeneratedProtocolMessageType('HloProto', (_message.Message,), {
  'DESCRIPTOR' : _HLOPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HloProto)
  })
_sym_db.RegisterMessage(HloProto)

HloSnapshot = _reflection.GeneratedProtocolMessageType('HloSnapshot', (_message.Message,), {
  'DESCRIPTOR' : _HLOSNAPSHOT,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HloSnapshot)
  })
_sym_db.RegisterMessage(HloSnapshot)

HloModuleMetadataProto = _reflection.GeneratedProtocolMessageType('HloModuleMetadataProto', (_message.Message,), {
  'DESCRIPTOR' : _HLOMODULEMETADATAPROTO,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HloModuleMetadataProto)
  })
_sym_db.RegisterMessage(HloModuleMetadataProto)

HloPassMetadata = _reflection.GeneratedProtocolMessageType('HloPassMetadata', (_message.Message,), {
  'DESCRIPTOR' : _HLOPASSMETADATA,
  '__module__' : 'tensorflow.compiler.xla.service.hlo_pb2'
  # @@protoc_insertion_point(class_scope:xla.HloPassMetadata)
  })
_sym_db.RegisterMessage(HloPassMetadata)


DESCRIPTOR._options = None
_HLOINSTRUCTIONPROTO.fields_by_name['all_reduce_id']._options = None
_HLOSCHEDULEPROTO_SEQUENCESENTRY._options = None
# @@protoc_insertion_point(module_scope)
