---
title: 'CVAT and Human Protocol'
linkTitle: 'Human Protocol'
weight: 2
---

The integration of CVAT with HUMAN Protocol offers a groundbreaking approach to data annotation
for AI and machine learning projects.

This collaboration combines CVAT's advanced annotation tools with HUMAN Protocol's
innovative task distribution and compensation system, creating a seamless,
efficient workflow for crowdsourcing annotations.

For more details on how to leverage the features of both platforms
for your projects, check out these articles:

<!--lint disable maximum-line-length-->

- [CVAT.ai & HUMAN Protocol: A New Dawn in Visual Data Annotation](https://www.cvat.ai/post/cvat-ai-human-protocol-a-new-dawn-in-visual-data-annotation)
- [Mastering Image Annotation Crowdsourcing for Computer Vision with CVAT.ai and HUMAN Protocol](https://www.cvat.ai/post/mastering-image-annotation-crowdsourcing-for-computer-vision-with-cvat-ai-and-human-protocol)
- [Crowdsourcing Annotation with CVAT and Human Protocol: Real Data Experiment Showed Amazing Results](https://www.cvat.ai/post/crowdsource-annotations-with-cvat-and-human-protocol)
<!--lint enable maximum-line-length-->

<br>
See:

- [Basic terms](#basic-terms)
- [Requester: How to get data annotated?](#requester-how-to-get-data-annotated)
- [Annotator: How to earn money?](#annotator-how-to-earn-money)
- [Video tutorial](#video-tutorial)

## Basic terms

In the realm of computer vision and AI, understanding the roles and components within data
annotation projects is crucial.

Here's a quick overview of basic terms related to CVAT and Human Protocol
integration, providing a clear picture of the workflow and participants involved.

<!--lint disable maximum-line-length-->

| Term      | Explanation                                                                |
| --------- | -------------------------------------------------------------------------- |
| Requester | An individual or organization that needs data annotated for AI models.     |
| Annotator | A person who labels data, such as images or videos, for machine learning.  |
| Dataset   | A collection of data, often images or videos, used for training AI models. |

<!--lint enable maximum-line-length-->

## Requester: How to get data annotated?

> Note: Either you are Requester or Annotator,
> to access this feature, you'll need to [send a request to HUMAN Protocol](mailto:<EMAIL>).

To register and launch a job on the HUMAN Protocol site, follow these steps:

1. [Create a Requester account](https://docs.humanprotocol.org/human-tech-docs/job-launcher/user-guide/signup)
   on the Human Protocol site.
2. Log in to your account and [launch a Job](https://docs.humanprotocol.org/human-tech-docs/job-launcher/user-guide/launch-jobs/fortune).

The job will appear on the Annotators' dashboard and will be
annotated according to the specified requirements and quality standards.

## Annotator: How to earn money?

> Note: Either you are Requester or Annotator,
> to access this feature, you'll need to [send a request to HUMAN Protocol](mailto:<EMAIL>).

To start earning money on the Human Protocol site, follow these simple steps:

1. [Register on the Human Protocol site](https://app.humanprotocol.org/)
2. Complete KYC process.
3. Connect crypto wallet to your account.
4. Select job from **Data Labeling Jobs** menu.
5. Annotate.
6. After annotation is complete, change job status to **Complete**.

After the job is reviewed and accepted by the Requester,
the money will be deposited into your account.

## Video tutorial

<!--lint disable maximum-line-length-->

<iframe width="560" height="315" src="https://www.youtube.com/embed/XHQ25vhx0xE?si=6q2KVLfK2Tg62Xyj" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>

<!--lint enable maximum-line-length-->
