# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Norwegian Nynorsk (http://www.transifex.com/django/django/"
"language/nn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Avanserte innstillingar"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "Nettadresse"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"Eksempel: '/om/kontakt/'. Kontroller at det er ein skråstrek framfor og bak."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Dette feltet kan berre innehalde bokstavar, nummer, skilleteikn, "
"understrekar, bindestrekar, skråstrekar eller tilder."

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "tittel"

msgid "content"
msgstr "innhald"

msgid "enable comments"
msgstr "tillat kommentarer"

msgid "template name"
msgstr "malnamn"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"Døme: 'flatpages/kontakt_side.html'. Dersom denne ikkje er gjeve, vil "
"'flatpages/default.html' bli brukt."

msgid "registration required"
msgstr "krevar registrering"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Dersom denne er kryssa av, kan berre innlogga brukarar sjå sida."

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "flatside"

msgid "flat pages"
msgstr "flatsider"
