# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.keras.initializers namespace.
"""

from __future__ import print_function as _print_function

import sys as _sys

from keras.initializers import deserialize
from keras.initializers import get
from keras.initializers import serialize
from keras.initializers.initializers_v1 import HeN<PERSON><PERSON> as he_normal
from keras.initializers.initializers_v1 import HeUniform as he_uniform
from keras.initializers.initializers_v1 import LecunNormal as lecun_normal
from keras.initializers.initializers_v1 import LecunUniform as lecun_uniform
from keras.initializers.initializers_v1 import RandomNormal
from keras.initializers.initializers_v1 import RandomNormal as normal
from keras.initializers.initializers_v1 import RandomNormal as random_normal
from keras.initializers.initializers_v1 import RandomUniform
from keras.initializers.initializers_v1 import RandomUniform as random_uniform
from keras.initializers.initializers_v1 import RandomUniform as uniform
from keras.initializers.initializers_v1 import TruncatedNormal
from keras.initializers.initializers_v1 import TruncatedNormal as truncated_normal
from keras.initializers.initializers_v1 import _v1_constant_initializer as Constant
from keras.initializers.initializers_v1 import _v1_constant_initializer as constant
from keras.initializers.initializers_v1 import _v1_glorot_normal_initializer as glorot_normal
from keras.initializers.initializers_v1 import _v1_glorot_uniform_initializer as glorot_uniform
from keras.initializers.initializers_v1 import _v1_identity as Identity
from keras.initializers.initializers_v1 import _v1_identity as identity
from keras.initializers.initializers_v1 import _v1_ones_initializer as Ones
from keras.initializers.initializers_v1 import _v1_ones_initializer as ones
from keras.initializers.initializers_v1 import _v1_orthogonal_initializer as Orthogonal
from keras.initializers.initializers_v1 import _v1_orthogonal_initializer as orthogonal
from keras.initializers.initializers_v1 import _v1_variance_scaling_initializer as VarianceScaling
from keras.initializers.initializers_v1 import _v1_zeros_initializer as Zeros
from keras.initializers.initializers_v1 import _v1_zeros_initializer as zeros
from keras.initializers.initializers_v2 import Initializer

del _print_function

from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.initializers", public_apis=None, deprecation=True,
      has_lite=False)
