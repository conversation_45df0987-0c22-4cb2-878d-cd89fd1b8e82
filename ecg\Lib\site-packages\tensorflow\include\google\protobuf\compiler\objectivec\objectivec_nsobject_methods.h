// NSObject methods
// Autogenerated by method_dump.sh. Do not edit by hand.
// Date: Thu Nov  1 14:12:16 PDT 2018
// macOS: MacOSX10.14.sdk
// iOS: iPhoneSimulator12.1.sdk

const char* const kNSObjectMethodsList[] = {
	"CAMLType",
	"CA_copyRenderValue",
	"CA_prepareRenderValue",
	"NS_copyCGImage",
	"NS_tiledLayerVisibleRect",
	"___tryRetain_OA",
	"__autorelease_OA",
	"__dealloc_zombie",
	"__release_OA",
	"__retain_OA",
	"_accessibilityFinalize",
	"_accessibilityIsTableViewDescendant",
	"_accessibilityUIElementSpecifier",
	"_accessibilityUseConvenienceAPI",
	"_allowsDirectEncoding",
	"_asScriptTerminologyNameArray",
	"_asScriptTerminologyNameString",
	"_bindingAdaptor",
	"_cfTypeID",
	"_copyDescription",
	"_destroyObserverList",
	"_didEndKeyValueObserving",
	"_implicitObservationInfo",
	"_internalAccessibilityAttributedHint",
	"_internalAccessibilityAttributedLabel",
	"_internalAccessibilityAttributedValue",
	"_isAXConnector",
	"_isAccessibilityContainerSectionCandidate",
	"_isAccessibilityContentNavigatorSectionCandidate",
	"_isAccessibilityContentSectionCandidate",
	"_isAccessibilityTopLevelNavigatorSectionCandidate",
	"_isDeallocating",
	"_isKVOA",
	"_isToManyChangeInformation",
	"_ivarDescription",
	"_localClassNameForClass",
	"_methodDescription",
	"_observerStorage",
	"_overrideUseFastBlockObservers",
	"_propertyDescription",
	"_releaseBindingAdaptor",
	"_scriptingCount",
	"_scriptingCountNonrecursively",
	"_scriptingDebugDescription",
	"_scriptingExists",
	"_scriptingShouldCheckObjectIndexes",
	"_shortMethodDescription",
	"_shouldSearchChildrenForSection",
	"_traitStorageList",
	"_tryRetain",
	"_ui_descriptionBuilder",
	"_uikit_variesByTraitCollections",
	"_web_description",
	"_webkit_invokeOnMainThread",
	"_willBeginKeyValueObserving",
	"accessibilityActivate",
	"accessibilityActivationPoint",
	"accessibilityAllowsOverriddenAttributesWhenIgnored",
	"accessibilityAssistiveTechnologyFocusedIdentifiers",
	"accessibilityAttributedHint",
	"accessibilityAttributedLabel",
	"accessibilityAttributedValue",
	"accessibilityContainer",
	"accessibilityContainerType",
	"accessibilityCustomActions",
	"accessibilityCustomRotors",
	"accessibilityDecrement",
	"accessibilityDragSourceDescriptors",
	"accessibilityDropPointDescriptors",
	"accessibilityElementCount",
	"accessibilityElementDidBecomeFocused",
	"accessibilityElementDidLoseFocus",
	"accessibilityElementIsFocused",
	"accessibilityElements",
	"accessibilityElementsHidden",
	"accessibilityFrame",
	"accessibilityHeaderElements",
	"accessibilityHint",
	"accessibilityIdentification",
	"accessibilityIdentifier",
	"accessibilityIncrement",
	"accessibilityLabel",
	"accessibilityLanguage",
	"accessibilityLocalizedStringKey",
	"accessibilityNavigationStyle",
	"accessibilityOverriddenAttributes",
	"accessibilityParameterizedAttributeNames",
	"accessibilityPath",
	"accessibilityPerformEscape",
	"accessibilityPerformMagicTap",
	"accessibilityPresenterProcessIdentifier",
	"accessibilityShouldUseUniqueId",
	"accessibilitySupportsNotifications",
	"accessibilitySupportsOverriddenAttributes",
	"accessibilityTemporaryChildren",
	"accessibilityTraits",
	"accessibilityValue",
	"accessibilityViewIsModal",
	"accessibilityVisibleArea",
	"allPropertyKeys",
	"allowsWeakReference",
	"attributeKeys",
	"autoContentAccessingProxy",
	"autorelease",
	"awakeFromNib",
	"boolValueSafe",
	"bs_encoded",
	"bs_isPlistableType",
	"bs_secureEncoded",
	"cl_json_serializeKey",
	"class",
	"classCode",
	"classDescription",
	"classForArchiver",
	"classForCoder",
	"classForKeyedArchiver",
	"classForPortCoder",
	"className",
	"clearProperties",
	"copy",
	"dealloc",
	"debugDescription",
	"defaultAccessibilityTraits",
	"description",
	"doubleValueSafe",
	"entityName",
	"exposedBindings",
	"finalize",
	"finishObserving",
	"flushKeyBindings",
	"hash",
	"init",
	"int64ValueSafe",
	"isAccessibilityElement",
	"isAccessibilityElementByDefault",
	"isElementAccessibilityExposedToInterfaceBuilder",
	"isFault",
	"isNSArray__",
	"isNSCFConstantString__",
	"isNSData__",
	"isNSDate__",
	"isNSDictionary__",
	"isNSNumber__",
	"isNSObject__",
	"isNSOrderedSet__",
	"isNSSet__",
	"isNSString__",
	"isNSTimeZone__",
	"isNSValue__",
	"isProxy",
	"mutableCopy",
	"nilValueForKey",
	"objectSpecifier",
	"observationInfo",
	"pep_onDetachedThread",
	"pep_onMainThread",
	"pep_onMainThreadIfNecessary",
	"prepareForInterfaceBuilder",
	"release",
	"releaseOnMainThread",
	"retain",
	"retainCount",
	"retainWeakReference",
	"scriptingProperties",
	"self",
	"shouldGroupAccessibilityChildren",
	"storedAccessibilityActivationPoint",
	"storedAccessibilityContainerType",
	"storedAccessibilityElementsHidden",
	"storedAccessibilityFrame",
	"storedAccessibilityNavigationStyle",
	"storedAccessibilityTraits",
	"storedAccessibilityViewIsModal",
	"storedIsAccessibilityElement",
	"storedShouldGroupAccessibilityChildren",
	"stringValueSafe",
	"superclass",
	"toManyRelationshipKeys",
	"toOneRelationshipKeys",
	"traitStorageList",
	"un_safeBoolValue",
	"userInterfaceItemIdentifier",
	"utf8ValueSafe",
	"valuesForKeysWithDictionary",
	"zone",
// Protocol: CAAnimatableValue
// Protocol: CARenderValue
// Protocol: NSObject
// Protocol: ROCKRemoteInvocationInterface
};
