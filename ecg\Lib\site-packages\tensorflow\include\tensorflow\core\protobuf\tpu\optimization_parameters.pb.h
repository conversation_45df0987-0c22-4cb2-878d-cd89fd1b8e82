// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/optimization_parameters.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/wrappers.pb.h>
#include "tensorflow/compiler/xla/service/hlo.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[25]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
namespace tensorflow {
namespace tpu {
class AdadeltaParameters;
class AdadeltaParametersDefaultTypeInternal;
extern AdadeltaParametersDefaultTypeInternal _AdadeltaParameters_default_instance_;
class AdagradParameters;
class AdagradParametersDefaultTypeInternal;
extern AdagradParametersDefaultTypeInternal _AdagradParameters_default_instance_;
class AdamParameters;
class AdamParametersDefaultTypeInternal;
extern AdamParametersDefaultTypeInternal _AdamParameters_default_instance_;
class AssignParameters;
class AssignParametersDefaultTypeInternal;
extern AssignParametersDefaultTypeInternal _AssignParameters_default_instance_;
class BoundedAdagradParameters;
class BoundedAdagradParametersDefaultTypeInternal;
extern BoundedAdagradParametersDefaultTypeInternal _BoundedAdagradParameters_default_instance_;
class CenteredRmsPropParameters;
class CenteredRmsPropParametersDefaultTypeInternal;
extern CenteredRmsPropParametersDefaultTypeInternal _CenteredRmsPropParameters_default_instance_;
class ClippingLimits;
class ClippingLimitsDefaultTypeInternal;
extern ClippingLimitsDefaultTypeInternal _ClippingLimits_default_instance_;
class DynamicLearningRate;
class DynamicLearningRateDefaultTypeInternal;
extern DynamicLearningRateDefaultTypeInternal _DynamicLearningRate_default_instance_;
class FrequencyEstimatorParameters;
class FrequencyEstimatorParametersDefaultTypeInternal;
extern FrequencyEstimatorParametersDefaultTypeInternal _FrequencyEstimatorParameters_default_instance_;
class FtrlParameters;
class FtrlParametersDefaultTypeInternal;
extern FtrlParametersDefaultTypeInternal _FtrlParameters_default_instance_;
class GradientAccumulationStatus;
class GradientAccumulationStatusDefaultTypeInternal;
extern GradientAccumulationStatusDefaultTypeInternal _GradientAccumulationStatus_default_instance_;
class HotIdReplicationConfiguration;
class HotIdReplicationConfigurationDefaultTypeInternal;
extern HotIdReplicationConfigurationDefaultTypeInternal _HotIdReplicationConfiguration_default_instance_;
class LearningRate;
class LearningRateDefaultTypeInternal;
extern LearningRateDefaultTypeInternal _LearningRate_default_instance_;
class MdlAdagradLightParameters;
class MdlAdagradLightParametersDefaultTypeInternal;
extern MdlAdagradLightParametersDefaultTypeInternal _MdlAdagradLightParameters_default_instance_;
class MomentumParameters;
class MomentumParametersDefaultTypeInternal;
extern MomentumParametersDefaultTypeInternal _MomentumParameters_default_instance_;
class OnlineYogiParameters;
class OnlineYogiParametersDefaultTypeInternal;
extern OnlineYogiParametersDefaultTypeInternal _OnlineYogiParameters_default_instance_;
class OptimizationParameters;
class OptimizationParametersDefaultTypeInternal;
extern OptimizationParametersDefaultTypeInternal _OptimizationParameters_default_instance_;
class ProximalAdagradParameters;
class ProximalAdagradParametersDefaultTypeInternal;
extern ProximalAdagradParametersDefaultTypeInternal _ProximalAdagradParameters_default_instance_;
class ProximalYogiParameters;
class ProximalYogiParametersDefaultTypeInternal;
extern ProximalYogiParametersDefaultTypeInternal _ProximalYogiParameters_default_instance_;
class RmsPropParameters;
class RmsPropParametersDefaultTypeInternal;
extern RmsPropParametersDefaultTypeInternal _RmsPropParameters_default_instance_;
class StateVariableSpecification;
class StateVariableSpecificationDefaultTypeInternal;
extern StateVariableSpecificationDefaultTypeInternal _StateVariableSpecification_default_instance_;
class StateVariableSpecification_FillWithConstant;
class StateVariableSpecification_FillWithConstantDefaultTypeInternal;
extern StateVariableSpecification_FillWithConstantDefaultTypeInternal _StateVariableSpecification_FillWithConstant_default_instance_;
class StateVariableSpecification_UserDefined;
class StateVariableSpecification_UserDefinedDefaultTypeInternal;
extern StateVariableSpecification_UserDefinedDefaultTypeInternal _StateVariableSpecification_UserDefined_default_instance_;
class StochasticGradientDescentParameters;
class StochasticGradientDescentParametersDefaultTypeInternal;
extern StochasticGradientDescentParametersDefaultTypeInternal _StochasticGradientDescentParameters_default_instance_;
class UserDefinedProgramParameters;
class UserDefinedProgramParametersDefaultTypeInternal;
extern UserDefinedProgramParametersDefaultTypeInternal _UserDefinedProgramParameters_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tpu::AdadeltaParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AdadeltaParameters>(Arena*);
template<> ::tensorflow::tpu::AdagradParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AdagradParameters>(Arena*);
template<> ::tensorflow::tpu::AdamParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AdamParameters>(Arena*);
template<> ::tensorflow::tpu::AssignParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AssignParameters>(Arena*);
template<> ::tensorflow::tpu::BoundedAdagradParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::BoundedAdagradParameters>(Arena*);
template<> ::tensorflow::tpu::CenteredRmsPropParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::CenteredRmsPropParameters>(Arena*);
template<> ::tensorflow::tpu::ClippingLimits* Arena::CreateMaybeMessage<::tensorflow::tpu::ClippingLimits>(Arena*);
template<> ::tensorflow::tpu::DynamicLearningRate* Arena::CreateMaybeMessage<::tensorflow::tpu::DynamicLearningRate>(Arena*);
template<> ::tensorflow::tpu::FrequencyEstimatorParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::FrequencyEstimatorParameters>(Arena*);
template<> ::tensorflow::tpu::FtrlParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::FtrlParameters>(Arena*);
template<> ::tensorflow::tpu::GradientAccumulationStatus* Arena::CreateMaybeMessage<::tensorflow::tpu::GradientAccumulationStatus>(Arena*);
template<> ::tensorflow::tpu::HotIdReplicationConfiguration* Arena::CreateMaybeMessage<::tensorflow::tpu::HotIdReplicationConfiguration>(Arena*);
template<> ::tensorflow::tpu::LearningRate* Arena::CreateMaybeMessage<::tensorflow::tpu::LearningRate>(Arena*);
template<> ::tensorflow::tpu::MdlAdagradLightParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::MdlAdagradLightParameters>(Arena*);
template<> ::tensorflow::tpu::MomentumParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::MomentumParameters>(Arena*);
template<> ::tensorflow::tpu::OnlineYogiParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::OnlineYogiParameters>(Arena*);
template<> ::tensorflow::tpu::OptimizationParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::OptimizationParameters>(Arena*);
template<> ::tensorflow::tpu::ProximalAdagradParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::ProximalAdagradParameters>(Arena*);
template<> ::tensorflow::tpu::ProximalYogiParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::ProximalYogiParameters>(Arena*);
template<> ::tensorflow::tpu::RmsPropParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::RmsPropParameters>(Arena*);
template<> ::tensorflow::tpu::StateVariableSpecification* Arena::CreateMaybeMessage<::tensorflow::tpu::StateVariableSpecification>(Arena*);
template<> ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* Arena::CreateMaybeMessage<::tensorflow::tpu::StateVariableSpecification_FillWithConstant>(Arena*);
template<> ::tensorflow::tpu::StateVariableSpecification_UserDefined* Arena::CreateMaybeMessage<::tensorflow::tpu::StateVariableSpecification_UserDefined>(Arena*);
template<> ::tensorflow::tpu::StochasticGradientDescentParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::StochasticGradientDescentParameters>(Arena*);
template<> ::tensorflow::tpu::UserDefinedProgramParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::UserDefinedProgramParameters>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tpu {

enum GradientAccumulationStatus_Status : int {
  GradientAccumulationStatus_Status_UNSPECIFIED = 0,
  GradientAccumulationStatus_Status_ENABLED = 1,
  GradientAccumulationStatus_Status_DISABLED = 2,
  GradientAccumulationStatus_Status_GradientAccumulationStatus_Status_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  GradientAccumulationStatus_Status_GradientAccumulationStatus_Status_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool GradientAccumulationStatus_Status_IsValid(int value);
constexpr GradientAccumulationStatus_Status GradientAccumulationStatus_Status_Status_MIN = GradientAccumulationStatus_Status_UNSPECIFIED;
constexpr GradientAccumulationStatus_Status GradientAccumulationStatus_Status_Status_MAX = GradientAccumulationStatus_Status_DISABLED;
constexpr int GradientAccumulationStatus_Status_Status_ARRAYSIZE = GradientAccumulationStatus_Status_Status_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GradientAccumulationStatus_Status_descriptor();
template<typename T>
inline const std::string& GradientAccumulationStatus_Status_Name(T enum_t_value) {
  static_assert(::std::is_same<T, GradientAccumulationStatus_Status>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function GradientAccumulationStatus_Status_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    GradientAccumulationStatus_Status_descriptor(), enum_t_value);
}
inline bool GradientAccumulationStatus_Status_Parse(
    const std::string& name, GradientAccumulationStatus_Status* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<GradientAccumulationStatus_Status>(
    GradientAccumulationStatus_Status_descriptor(), name, value);
}
enum HotIdReplicationConfiguration_Status : int {
  HotIdReplicationConfiguration_Status_UNSPECIFIED = 0,
  HotIdReplicationConfiguration_Status_ENABLED = 1,
  HotIdReplicationConfiguration_Status_DISABLED = 2,
  HotIdReplicationConfiguration_Status_HotIdReplicationConfiguration_Status_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  HotIdReplicationConfiguration_Status_HotIdReplicationConfiguration_Status_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool HotIdReplicationConfiguration_Status_IsValid(int value);
constexpr HotIdReplicationConfiguration_Status HotIdReplicationConfiguration_Status_Status_MIN = HotIdReplicationConfiguration_Status_UNSPECIFIED;
constexpr HotIdReplicationConfiguration_Status HotIdReplicationConfiguration_Status_Status_MAX = HotIdReplicationConfiguration_Status_DISABLED;
constexpr int HotIdReplicationConfiguration_Status_Status_ARRAYSIZE = HotIdReplicationConfiguration_Status_Status_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HotIdReplicationConfiguration_Status_descriptor();
template<typename T>
inline const std::string& HotIdReplicationConfiguration_Status_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HotIdReplicationConfiguration_Status>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HotIdReplicationConfiguration_Status_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HotIdReplicationConfiguration_Status_descriptor(), enum_t_value);
}
inline bool HotIdReplicationConfiguration_Status_Parse(
    const std::string& name, HotIdReplicationConfiguration_Status* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HotIdReplicationConfiguration_Status>(
    HotIdReplicationConfiguration_Status_descriptor(), name, value);
}
// ===================================================================

class ClippingLimits :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.ClippingLimits) */ {
 public:
  ClippingLimits();
  virtual ~ClippingLimits();

  ClippingLimits(const ClippingLimits& from);
  ClippingLimits(ClippingLimits&& from) noexcept
    : ClippingLimits() {
    *this = ::std::move(from);
  }

  inline ClippingLimits& operator=(const ClippingLimits& from) {
    CopyFrom(from);
    return *this;
  }
  inline ClippingLimits& operator=(ClippingLimits&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ClippingLimits& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ClippingLimits* internal_default_instance() {
    return reinterpret_cast<const ClippingLimits*>(
               &_ClippingLimits_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ClippingLimits& a, ClippingLimits& b) {
    a.Swap(&b);
  }
  inline void Swap(ClippingLimits* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ClippingLimits* New() const final {
    return CreateMaybeMessage<ClippingLimits>(nullptr);
  }

  ClippingLimits* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ClippingLimits>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ClippingLimits& from);
  void MergeFrom(const ClippingLimits& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ClippingLimits* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.ClippingLimits";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLowerFieldNumber = 1,
    kUpperFieldNumber = 2,
  };
  // .google.protobuf.FloatValue lower = 1;
  bool has_lower() const;
  void clear_lower();
  const PROTOBUF_NAMESPACE_ID::FloatValue& lower() const;
  PROTOBUF_NAMESPACE_ID::FloatValue* release_lower();
  PROTOBUF_NAMESPACE_ID::FloatValue* mutable_lower();
  void set_allocated_lower(PROTOBUF_NAMESPACE_ID::FloatValue* lower);

  // .google.protobuf.FloatValue upper = 2;
  bool has_upper() const;
  void clear_upper();
  const PROTOBUF_NAMESPACE_ID::FloatValue& upper() const;
  PROTOBUF_NAMESPACE_ID::FloatValue* release_upper();
  PROTOBUF_NAMESPACE_ID::FloatValue* mutable_upper();
  void set_allocated_upper(PROTOBUF_NAMESPACE_ID::FloatValue* upper);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.ClippingLimits)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  PROTOBUF_NAMESPACE_ID::FloatValue* lower_;
  PROTOBUF_NAMESPACE_ID::FloatValue* upper_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class DynamicLearningRate :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.DynamicLearningRate) */ {
 public:
  DynamicLearningRate();
  virtual ~DynamicLearningRate();

  DynamicLearningRate(const DynamicLearningRate& from);
  DynamicLearningRate(DynamicLearningRate&& from) noexcept
    : DynamicLearningRate() {
    *this = ::std::move(from);
  }

  inline DynamicLearningRate& operator=(const DynamicLearningRate& from) {
    CopyFrom(from);
    return *this;
  }
  inline DynamicLearningRate& operator=(DynamicLearningRate&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DynamicLearningRate& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DynamicLearningRate* internal_default_instance() {
    return reinterpret_cast<const DynamicLearningRate*>(
               &_DynamicLearningRate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DynamicLearningRate& a, DynamicLearningRate& b) {
    a.Swap(&b);
  }
  inline void Swap(DynamicLearningRate* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DynamicLearningRate* New() const final {
    return CreateMaybeMessage<DynamicLearningRate>(nullptr);
  }

  DynamicLearningRate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DynamicLearningRate>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DynamicLearningRate& from);
  void MergeFrom(const DynamicLearningRate& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DynamicLearningRate* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.DynamicLearningRate";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTagFieldNumber = 1,
  };
  // int32 tag = 1;
  void clear_tag();
  ::PROTOBUF_NAMESPACE_ID::int32 tag() const;
  void set_tag(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.DynamicLearningRate)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int32 tag_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class LearningRate :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.LearningRate) */ {
 public:
  LearningRate();
  virtual ~LearningRate();

  LearningRate(const LearningRate& from);
  LearningRate(LearningRate&& from) noexcept
    : LearningRate() {
    *this = ::std::move(from);
  }

  inline LearningRate& operator=(const LearningRate& from) {
    CopyFrom(from);
    return *this;
  }
  inline LearningRate& operator=(LearningRate&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LearningRate& default_instance();

  enum LearningRateCase {
    kConstant = 1,
    kDynamic = 2,
    LEARNING_RATE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LearningRate* internal_default_instance() {
    return reinterpret_cast<const LearningRate*>(
               &_LearningRate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(LearningRate& a, LearningRate& b) {
    a.Swap(&b);
  }
  inline void Swap(LearningRate* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LearningRate* New() const final {
    return CreateMaybeMessage<LearningRate>(nullptr);
  }

  LearningRate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LearningRate>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LearningRate& from);
  void MergeFrom(const LearningRate& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LearningRate* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.LearningRate";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConstantFieldNumber = 1,
    kDynamicFieldNumber = 2,
  };
  // float constant = 1;
  private:
  bool has_constant() const;
  public:
  void clear_constant();
  float constant() const;
  void set_constant(float value);

  // .tensorflow.tpu.DynamicLearningRate dynamic = 2;
  bool has_dynamic() const;
  void clear_dynamic();
  const ::tensorflow::tpu::DynamicLearningRate& dynamic() const;
  ::tensorflow::tpu::DynamicLearningRate* release_dynamic();
  ::tensorflow::tpu::DynamicLearningRate* mutable_dynamic();
  void set_allocated_dynamic(::tensorflow::tpu::DynamicLearningRate* dynamic);

  void clear_learning_rate();
  LearningRateCase learning_rate_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.LearningRate)
 private:
  class _Internal;
  void set_has_constant();
  void set_has_dynamic();

  inline bool has_learning_rate() const;
  inline void clear_has_learning_rate();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  union LearningRateUnion {
    LearningRateUnion() {}
    float constant_;
    ::tensorflow::tpu::DynamicLearningRate* dynamic_;
  } learning_rate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class AdagradParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AdagradParameters) */ {
 public:
  AdagradParameters();
  virtual ~AdagradParameters();

  AdagradParameters(const AdagradParameters& from);
  AdagradParameters(AdagradParameters&& from) noexcept
    : AdagradParameters() {
    *this = ::std::move(from);
  }

  inline AdagradParameters& operator=(const AdagradParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdagradParameters& operator=(AdagradParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AdagradParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdagradParameters* internal_default_instance() {
    return reinterpret_cast<const AdagradParameters*>(
               &_AdagradParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(AdagradParameters& a, AdagradParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(AdagradParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AdagradParameters* New() const final {
    return CreateMaybeMessage<AdagradParameters>(nullptr);
  }

  AdagradParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AdagradParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AdagradParameters& from);
  void MergeFrom(const AdagradParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdagradParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.AdagradParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AdagradParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class BoundedAdagradParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.BoundedAdagradParameters) */ {
 public:
  BoundedAdagradParameters();
  virtual ~BoundedAdagradParameters();

  BoundedAdagradParameters(const BoundedAdagradParameters& from);
  BoundedAdagradParameters(BoundedAdagradParameters&& from) noexcept
    : BoundedAdagradParameters() {
    *this = ::std::move(from);
  }

  inline BoundedAdagradParameters& operator=(const BoundedAdagradParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline BoundedAdagradParameters& operator=(BoundedAdagradParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BoundedAdagradParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BoundedAdagradParameters* internal_default_instance() {
    return reinterpret_cast<const BoundedAdagradParameters*>(
               &_BoundedAdagradParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(BoundedAdagradParameters& a, BoundedAdagradParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(BoundedAdagradParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline BoundedAdagradParameters* New() const final {
    return CreateMaybeMessage<BoundedAdagradParameters>(nullptr);
  }

  BoundedAdagradParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BoundedAdagradParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BoundedAdagradParameters& from);
  void MergeFrom(const BoundedAdagradParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BoundedAdagradParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.BoundedAdagradParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUpdateAccumulatorFirstFieldNumber = 1,
    kMaxVarUpdateFieldNumber = 2,
    kMaxAccumulatorFieldNumber = 3,
  };
  // bool update_accumulator_first = 1;
  void clear_update_accumulator_first();
  bool update_accumulator_first() const;
  void set_update_accumulator_first(bool value);

  // float max_var_update = 2;
  void clear_max_var_update();
  float max_var_update() const;
  void set_max_var_update(float value);

  // float max_accumulator = 3;
  void clear_max_accumulator();
  float max_accumulator() const;
  void set_max_accumulator(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.BoundedAdagradParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  bool update_accumulator_first_;
  float max_var_update_;
  float max_accumulator_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class StochasticGradientDescentParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StochasticGradientDescentParameters) */ {
 public:
  StochasticGradientDescentParameters();
  virtual ~StochasticGradientDescentParameters();

  StochasticGradientDescentParameters(const StochasticGradientDescentParameters& from);
  StochasticGradientDescentParameters(StochasticGradientDescentParameters&& from) noexcept
    : StochasticGradientDescentParameters() {
    *this = ::std::move(from);
  }

  inline StochasticGradientDescentParameters& operator=(const StochasticGradientDescentParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline StochasticGradientDescentParameters& operator=(StochasticGradientDescentParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StochasticGradientDescentParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StochasticGradientDescentParameters* internal_default_instance() {
    return reinterpret_cast<const StochasticGradientDescentParameters*>(
               &_StochasticGradientDescentParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(StochasticGradientDescentParameters& a, StochasticGradientDescentParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(StochasticGradientDescentParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StochasticGradientDescentParameters* New() const final {
    return CreateMaybeMessage<StochasticGradientDescentParameters>(nullptr);
  }

  StochasticGradientDescentParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StochasticGradientDescentParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StochasticGradientDescentParameters& from);
  void MergeFrom(const StochasticGradientDescentParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StochasticGradientDescentParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.StochasticGradientDescentParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StochasticGradientDescentParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class FtrlParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.FtrlParameters) */ {
 public:
  FtrlParameters();
  virtual ~FtrlParameters();

  FtrlParameters(const FtrlParameters& from);
  FtrlParameters(FtrlParameters&& from) noexcept
    : FtrlParameters() {
    *this = ::std::move(from);
  }

  inline FtrlParameters& operator=(const FtrlParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline FtrlParameters& operator=(FtrlParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FtrlParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FtrlParameters* internal_default_instance() {
    return reinterpret_cast<const FtrlParameters*>(
               &_FtrlParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(FtrlParameters& a, FtrlParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(FtrlParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FtrlParameters* New() const final {
    return CreateMaybeMessage<FtrlParameters>(nullptr);
  }

  FtrlParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FtrlParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FtrlParameters& from);
  void MergeFrom(const FtrlParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FtrlParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.FtrlParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kL1FieldNumber = 1,
    kL2FieldNumber = 2,
    kLrPowerFieldNumber = 3,
    kBetaFieldNumber = 7,
    kMultiplyLinearByLrFieldNumber = 6,
    kAllowZeroAccumulatorFieldNumber = 8,
  };
  // float l1 = 1;
  void clear_l1();
  float l1() const;
  void set_l1(float value);

  // float l2 = 2;
  void clear_l2();
  float l2() const;
  void set_l2(float value);

  // float lr_power = 3;
  void clear_lr_power();
  float lr_power() const;
  void set_lr_power(float value);

  // float beta = 7;
  void clear_beta();
  float beta() const;
  void set_beta(float value);

  // bool multiply_linear_by_lr = 6;
  void clear_multiply_linear_by_lr();
  bool multiply_linear_by_lr() const;
  void set_multiply_linear_by_lr(bool value);

  // bool allow_zero_accumulator = 8;
  void clear_allow_zero_accumulator();
  bool allow_zero_accumulator() const;
  void set_allow_zero_accumulator(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.FtrlParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float l1_;
  float l2_;
  float lr_power_;
  float beta_;
  bool multiply_linear_by_lr_;
  bool allow_zero_accumulator_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class AdamParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AdamParameters) */ {
 public:
  AdamParameters();
  virtual ~AdamParameters();

  AdamParameters(const AdamParameters& from);
  AdamParameters(AdamParameters&& from) noexcept
    : AdamParameters() {
    *this = ::std::move(from);
  }

  inline AdamParameters& operator=(const AdamParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdamParameters& operator=(AdamParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AdamParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdamParameters* internal_default_instance() {
    return reinterpret_cast<const AdamParameters*>(
               &_AdamParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(AdamParameters& a, AdamParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(AdamParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AdamParameters* New() const final {
    return CreateMaybeMessage<AdamParameters>(nullptr);
  }

  AdamParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AdamParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AdamParameters& from);
  void MergeFrom(const AdamParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdamParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.AdamParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBeta1FieldNumber = 3,
    kBeta2FieldNumber = 4,
    kEpsilonFieldNumber = 5,
    kUseNonLazyAdamFieldNumber = 8,
    kUseSumInsideSqrtFieldNumber = 10,
  };
  // float beta1 = 3;
  void clear_beta1();
  float beta1() const;
  void set_beta1(float value);

  // float beta2 = 4;
  void clear_beta2();
  float beta2() const;
  void set_beta2(float value);

  // float epsilon = 5;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);

  // bool use_non_lazy_adam = 8;
  void clear_use_non_lazy_adam();
  bool use_non_lazy_adam() const;
  void set_use_non_lazy_adam(bool value);

  // bool use_sum_inside_sqrt = 10;
  void clear_use_sum_inside_sqrt();
  bool use_sum_inside_sqrt() const;
  void set_use_sum_inside_sqrt(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AdamParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float beta1_;
  float beta2_;
  float epsilon_;
  bool use_non_lazy_adam_;
  bool use_sum_inside_sqrt_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class MomentumParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.MomentumParameters) */ {
 public:
  MomentumParameters();
  virtual ~MomentumParameters();

  MomentumParameters(const MomentumParameters& from);
  MomentumParameters(MomentumParameters&& from) noexcept
    : MomentumParameters() {
    *this = ::std::move(from);
  }

  inline MomentumParameters& operator=(const MomentumParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline MomentumParameters& operator=(MomentumParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MomentumParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MomentumParameters* internal_default_instance() {
    return reinterpret_cast<const MomentumParameters*>(
               &_MomentumParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(MomentumParameters& a, MomentumParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(MomentumParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MomentumParameters* New() const final {
    return CreateMaybeMessage<MomentumParameters>(nullptr);
  }

  MomentumParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MomentumParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MomentumParameters& from);
  void MergeFrom(const MomentumParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MomentumParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.MomentumParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMomentumFieldNumber = 1,
    kUseNesterovFieldNumber = 2,
  };
  // float momentum = 1;
  void clear_momentum();
  float momentum() const;
  void set_momentum(float value);

  // bool use_nesterov = 2;
  void clear_use_nesterov();
  bool use_nesterov() const;
  void set_use_nesterov(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.MomentumParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float momentum_;
  bool use_nesterov_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class RmsPropParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.RmsPropParameters) */ {
 public:
  RmsPropParameters();
  virtual ~RmsPropParameters();

  RmsPropParameters(const RmsPropParameters& from);
  RmsPropParameters(RmsPropParameters&& from) noexcept
    : RmsPropParameters() {
    *this = ::std::move(from);
  }

  inline RmsPropParameters& operator=(const RmsPropParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline RmsPropParameters& operator=(RmsPropParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RmsPropParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RmsPropParameters* internal_default_instance() {
    return reinterpret_cast<const RmsPropParameters*>(
               &_RmsPropParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(RmsPropParameters& a, RmsPropParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(RmsPropParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RmsPropParameters* New() const final {
    return CreateMaybeMessage<RmsPropParameters>(nullptr);
  }

  RmsPropParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RmsPropParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RmsPropParameters& from);
  void MergeFrom(const RmsPropParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RmsPropParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.RmsPropParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRhoFieldNumber = 1,
    kMomentumFieldNumber = 2,
    kEpsilonFieldNumber = 3,
  };
  // float rho = 1;
  void clear_rho();
  float rho() const;
  void set_rho(float value);

  // float momentum = 2;
  void clear_momentum();
  float momentum() const;
  void set_momentum(float value);

  // float epsilon = 3;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.RmsPropParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float rho_;
  float momentum_;
  float epsilon_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class CenteredRmsPropParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.CenteredRmsPropParameters) */ {
 public:
  CenteredRmsPropParameters();
  virtual ~CenteredRmsPropParameters();

  CenteredRmsPropParameters(const CenteredRmsPropParameters& from);
  CenteredRmsPropParameters(CenteredRmsPropParameters&& from) noexcept
    : CenteredRmsPropParameters() {
    *this = ::std::move(from);
  }

  inline CenteredRmsPropParameters& operator=(const CenteredRmsPropParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline CenteredRmsPropParameters& operator=(CenteredRmsPropParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CenteredRmsPropParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CenteredRmsPropParameters* internal_default_instance() {
    return reinterpret_cast<const CenteredRmsPropParameters*>(
               &_CenteredRmsPropParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(CenteredRmsPropParameters& a, CenteredRmsPropParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(CenteredRmsPropParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CenteredRmsPropParameters* New() const final {
    return CreateMaybeMessage<CenteredRmsPropParameters>(nullptr);
  }

  CenteredRmsPropParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CenteredRmsPropParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CenteredRmsPropParameters& from);
  void MergeFrom(const CenteredRmsPropParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CenteredRmsPropParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.CenteredRmsPropParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRhoFieldNumber = 1,
    kMomentumFieldNumber = 2,
    kEpsilonFieldNumber = 3,
  };
  // float rho = 1;
  void clear_rho();
  float rho() const;
  void set_rho(float value);

  // float momentum = 2;
  void clear_momentum();
  float momentum() const;
  void set_momentum(float value);

  // float epsilon = 3;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.CenteredRmsPropParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float rho_;
  float momentum_;
  float epsilon_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class MdlAdagradLightParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.MdlAdagradLightParameters) */ {
 public:
  MdlAdagradLightParameters();
  virtual ~MdlAdagradLightParameters();

  MdlAdagradLightParameters(const MdlAdagradLightParameters& from);
  MdlAdagradLightParameters(MdlAdagradLightParameters&& from) noexcept
    : MdlAdagradLightParameters() {
    *this = ::std::move(from);
  }

  inline MdlAdagradLightParameters& operator=(const MdlAdagradLightParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline MdlAdagradLightParameters& operator=(MdlAdagradLightParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MdlAdagradLightParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MdlAdagradLightParameters* internal_default_instance() {
    return reinterpret_cast<const MdlAdagradLightParameters*>(
               &_MdlAdagradLightParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(MdlAdagradLightParameters& a, MdlAdagradLightParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(MdlAdagradLightParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MdlAdagradLightParameters* New() const final {
    return CreateMaybeMessage<MdlAdagradLightParameters>(nullptr);
  }

  MdlAdagradLightParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MdlAdagradLightParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MdlAdagradLightParameters& from);
  void MergeFrom(const MdlAdagradLightParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MdlAdagradLightParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.MdlAdagradLightParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kL2FieldNumber = 1,
    kLrPowerFieldNumber = 2,
    kMinServableMdlBenefitFieldNumber = 3,
    kMdlMixInMarginFieldNumber = 4,
    kMdlBenefitRampupCoeffFieldNumber = 5,
    kMdlMinWeightFieldNumber = 6,
    kBenefitRevisitScaleFieldNumber = 7,
    kMaxEventBenefitFieldNumber = 8,
    kMaxTotalBenefitFieldNumber = 9,
    kMdlHardLimitFieldNumber = 10,
    kHardLimitMinBenefitFieldNumber = 11,
    kMdlRegularizeFieldNumber = 12,
  };
  // float l2 = 1;
  void clear_l2();
  float l2() const;
  void set_l2(float value);

  // float lr_power = 2;
  void clear_lr_power();
  float lr_power() const;
  void set_lr_power(float value);

  // float min_servable_mdl_benefit = 3;
  void clear_min_servable_mdl_benefit();
  float min_servable_mdl_benefit() const;
  void set_min_servable_mdl_benefit(float value);

  // float mdl_mix_in_margin = 4;
  void clear_mdl_mix_in_margin();
  float mdl_mix_in_margin() const;
  void set_mdl_mix_in_margin(float value);

  // float mdl_benefit_rampup_coeff = 5;
  void clear_mdl_benefit_rampup_coeff();
  float mdl_benefit_rampup_coeff() const;
  void set_mdl_benefit_rampup_coeff(float value);

  // float mdl_min_weight = 6;
  void clear_mdl_min_weight();
  float mdl_min_weight() const;
  void set_mdl_min_weight(float value);

  // float benefit_revisit_scale = 7;
  void clear_benefit_revisit_scale();
  float benefit_revisit_scale() const;
  void set_benefit_revisit_scale(float value);

  // float max_event_benefit = 8;
  void clear_max_event_benefit();
  float max_event_benefit() const;
  void set_max_event_benefit(float value);

  // float max_total_benefit = 9;
  void clear_max_total_benefit();
  float max_total_benefit() const;
  void set_max_total_benefit(float value);

  // float mdl_hard_limit = 10;
  void clear_mdl_hard_limit();
  float mdl_hard_limit() const;
  void set_mdl_hard_limit(float value);

  // bool hard_limit_min_benefit = 11;
  void clear_hard_limit_min_benefit();
  bool hard_limit_min_benefit() const;
  void set_hard_limit_min_benefit(bool value);

  // bool mdl_regularize = 12;
  void clear_mdl_regularize();
  bool mdl_regularize() const;
  void set_mdl_regularize(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.MdlAdagradLightParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float l2_;
  float lr_power_;
  float min_servable_mdl_benefit_;
  float mdl_mix_in_margin_;
  float mdl_benefit_rampup_coeff_;
  float mdl_min_weight_;
  float benefit_revisit_scale_;
  float max_event_benefit_;
  float max_total_benefit_;
  float mdl_hard_limit_;
  bool hard_limit_min_benefit_;
  bool mdl_regularize_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class AdadeltaParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AdadeltaParameters) */ {
 public:
  AdadeltaParameters();
  virtual ~AdadeltaParameters();

  AdadeltaParameters(const AdadeltaParameters& from);
  AdadeltaParameters(AdadeltaParameters&& from) noexcept
    : AdadeltaParameters() {
    *this = ::std::move(from);
  }

  inline AdadeltaParameters& operator=(const AdadeltaParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdadeltaParameters& operator=(AdadeltaParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AdadeltaParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdadeltaParameters* internal_default_instance() {
    return reinterpret_cast<const AdadeltaParameters*>(
               &_AdadeltaParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(AdadeltaParameters& a, AdadeltaParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(AdadeltaParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AdadeltaParameters* New() const final {
    return CreateMaybeMessage<AdadeltaParameters>(nullptr);
  }

  AdadeltaParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AdadeltaParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AdadeltaParameters& from);
  void MergeFrom(const AdadeltaParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdadeltaParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.AdadeltaParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRhoFieldNumber = 1,
    kEpsilonFieldNumber = 2,
  };
  // float rho = 1;
  void clear_rho();
  float rho() const;
  void set_rho(float value);

  // float epsilon = 2;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AdadeltaParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float rho_;
  float epsilon_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class ProximalAdagradParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.ProximalAdagradParameters) */ {
 public:
  ProximalAdagradParameters();
  virtual ~ProximalAdagradParameters();

  ProximalAdagradParameters(const ProximalAdagradParameters& from);
  ProximalAdagradParameters(ProximalAdagradParameters&& from) noexcept
    : ProximalAdagradParameters() {
    *this = ::std::move(from);
  }

  inline ProximalAdagradParameters& operator=(const ProximalAdagradParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProximalAdagradParameters& operator=(ProximalAdagradParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProximalAdagradParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProximalAdagradParameters* internal_default_instance() {
    return reinterpret_cast<const ProximalAdagradParameters*>(
               &_ProximalAdagradParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(ProximalAdagradParameters& a, ProximalAdagradParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(ProximalAdagradParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProximalAdagradParameters* New() const final {
    return CreateMaybeMessage<ProximalAdagradParameters>(nullptr);
  }

  ProximalAdagradParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProximalAdagradParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProximalAdagradParameters& from);
  void MergeFrom(const ProximalAdagradParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProximalAdagradParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.ProximalAdagradParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kL1FieldNumber = 1,
    kL2FieldNumber = 2,
  };
  // float l1 = 1;
  void clear_l1();
  float l1() const;
  void set_l1(float value);

  // float l2 = 2;
  void clear_l2();
  float l2() const;
  void set_l2(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.ProximalAdagradParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float l1_;
  float l2_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class OnlineYogiParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.OnlineYogiParameters) */ {
 public:
  OnlineYogiParameters();
  virtual ~OnlineYogiParameters();

  OnlineYogiParameters(const OnlineYogiParameters& from);
  OnlineYogiParameters(OnlineYogiParameters&& from) noexcept
    : OnlineYogiParameters() {
    *this = ::std::move(from);
  }

  inline OnlineYogiParameters& operator=(const OnlineYogiParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline OnlineYogiParameters& operator=(OnlineYogiParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OnlineYogiParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OnlineYogiParameters* internal_default_instance() {
    return reinterpret_cast<const OnlineYogiParameters*>(
               &_OnlineYogiParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(OnlineYogiParameters& a, OnlineYogiParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(OnlineYogiParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OnlineYogiParameters* New() const final {
    return CreateMaybeMessage<OnlineYogiParameters>(nullptr);
  }

  OnlineYogiParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OnlineYogiParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OnlineYogiParameters& from);
  void MergeFrom(const OnlineYogiParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OnlineYogiParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.OnlineYogiParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kL1FieldNumber = 1,
    kL2FieldNumber = 2,
    kBeta2FieldNumber = 3,
  };
  // float l1 = 1;
  void clear_l1();
  float l1() const;
  void set_l1(float value);

  // float l2 = 2;
  void clear_l2();
  float l2() const;
  void set_l2(float value);

  // float beta2 = 3;
  void clear_beta2();
  float beta2() const;
  void set_beta2(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.OnlineYogiParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float l1_;
  float l2_;
  float beta2_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class ProximalYogiParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.ProximalYogiParameters) */ {
 public:
  ProximalYogiParameters();
  virtual ~ProximalYogiParameters();

  ProximalYogiParameters(const ProximalYogiParameters& from);
  ProximalYogiParameters(ProximalYogiParameters&& from) noexcept
    : ProximalYogiParameters() {
    *this = ::std::move(from);
  }

  inline ProximalYogiParameters& operator=(const ProximalYogiParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProximalYogiParameters& operator=(ProximalYogiParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProximalYogiParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProximalYogiParameters* internal_default_instance() {
    return reinterpret_cast<const ProximalYogiParameters*>(
               &_ProximalYogiParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(ProximalYogiParameters& a, ProximalYogiParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(ProximalYogiParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProximalYogiParameters* New() const final {
    return CreateMaybeMessage<ProximalYogiParameters>(nullptr);
  }

  ProximalYogiParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProximalYogiParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProximalYogiParameters& from);
  void MergeFrom(const ProximalYogiParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProximalYogiParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.ProximalYogiParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kL1FieldNumber = 1,
    kL2FieldNumber = 2,
    kBeta1FieldNumber = 3,
    kBeta2FieldNumber = 4,
    kEpsilonFieldNumber = 5,
  };
  // float l1 = 1;
  void clear_l1();
  float l1() const;
  void set_l1(float value);

  // float l2 = 2;
  void clear_l2();
  float l2() const;
  void set_l2(float value);

  // float beta1 = 3;
  void clear_beta1();
  float beta1() const;
  void set_beta1(float value);

  // float beta2 = 4;
  void clear_beta2();
  float beta2() const;
  void set_beta2(float value);

  // float epsilon = 5;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.ProximalYogiParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float l1_;
  float l2_;
  float beta1_;
  float beta2_;
  float epsilon_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class FrequencyEstimatorParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.FrequencyEstimatorParameters) */ {
 public:
  FrequencyEstimatorParameters();
  virtual ~FrequencyEstimatorParameters();

  FrequencyEstimatorParameters(const FrequencyEstimatorParameters& from);
  FrequencyEstimatorParameters(FrequencyEstimatorParameters&& from) noexcept
    : FrequencyEstimatorParameters() {
    *this = ::std::move(from);
  }

  inline FrequencyEstimatorParameters& operator=(const FrequencyEstimatorParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline FrequencyEstimatorParameters& operator=(FrequencyEstimatorParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FrequencyEstimatorParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FrequencyEstimatorParameters* internal_default_instance() {
    return reinterpret_cast<const FrequencyEstimatorParameters*>(
               &_FrequencyEstimatorParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(FrequencyEstimatorParameters& a, FrequencyEstimatorParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(FrequencyEstimatorParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FrequencyEstimatorParameters* New() const final {
    return CreateMaybeMessage<FrequencyEstimatorParameters>(nullptr);
  }

  FrequencyEstimatorParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FrequencyEstimatorParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FrequencyEstimatorParameters& from);
  void MergeFrom(const FrequencyEstimatorParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FrequencyEstimatorParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.FrequencyEstimatorParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTauFieldNumber = 1,
    kMaxDeltaFieldNumber = 2,
    kOutlierThresholdFieldNumber = 3,
    kWeightExponentFieldNumber = 4,
  };
  // float tau = 1;
  void clear_tau();
  float tau() const;
  void set_tau(float value);

  // float max_delta = 2;
  void clear_max_delta();
  float max_delta() const;
  void set_max_delta(float value);

  // float outlier_threshold = 3;
  void clear_outlier_threshold();
  float outlier_threshold() const;
  void set_outlier_threshold(float value);

  // float weight_exponent = 4;
  void clear_weight_exponent();
  float weight_exponent() const;
  void set_weight_exponent(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.FrequencyEstimatorParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  float tau_;
  float max_delta_;
  float outlier_threshold_;
  float weight_exponent_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class UserDefinedProgramParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.UserDefinedProgramParameters) */ {
 public:
  UserDefinedProgramParameters();
  virtual ~UserDefinedProgramParameters();

  UserDefinedProgramParameters(const UserDefinedProgramParameters& from);
  UserDefinedProgramParameters(UserDefinedProgramParameters&& from) noexcept
    : UserDefinedProgramParameters() {
    *this = ::std::move(from);
  }

  inline UserDefinedProgramParameters& operator=(const UserDefinedProgramParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline UserDefinedProgramParameters& operator=(UserDefinedProgramParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UserDefinedProgramParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const UserDefinedProgramParameters* internal_default_instance() {
    return reinterpret_cast<const UserDefinedProgramParameters*>(
               &_UserDefinedProgramParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(UserDefinedProgramParameters& a, UserDefinedProgramParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(UserDefinedProgramParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UserDefinedProgramParameters* New() const final {
    return CreateMaybeMessage<UserDefinedProgramParameters>(nullptr);
  }

  UserDefinedProgramParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UserDefinedProgramParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UserDefinedProgramParameters& from);
  void MergeFrom(const UserDefinedProgramParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UserDefinedProgramParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.UserDefinedProgramParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPaddingValuesFieldNumber = 2,
    kProgramFieldNumber = 1,
  };
  // repeated float padding_values = 2;
  int padding_values_size() const;
  void clear_padding_values();
  float padding_values(int index) const;
  void set_padding_values(int index, float value);
  void add_padding_values(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      padding_values() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_padding_values();

  // .xla.HloModuleProto program = 1;
  bool has_program() const;
  void clear_program();
  const ::xla::HloModuleProto& program() const;
  ::xla::HloModuleProto* release_program();
  ::xla::HloModuleProto* mutable_program();
  void set_allocated_program(::xla::HloModuleProto* program);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.UserDefinedProgramParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > padding_values_;
  mutable std::atomic<int> _padding_values_cached_byte_size_;
  ::xla::HloModuleProto* program_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class AssignParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AssignParameters) */ {
 public:
  AssignParameters();
  virtual ~AssignParameters();

  AssignParameters(const AssignParameters& from);
  AssignParameters(AssignParameters&& from) noexcept
    : AssignParameters() {
    *this = ::std::move(from);
  }

  inline AssignParameters& operator=(const AssignParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline AssignParameters& operator=(AssignParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AssignParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AssignParameters* internal_default_instance() {
    return reinterpret_cast<const AssignParameters*>(
               &_AssignParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(AssignParameters& a, AssignParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(AssignParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AssignParameters* New() const final {
    return CreateMaybeMessage<AssignParameters>(nullptr);
  }

  AssignParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AssignParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AssignParameters& from);
  void MergeFrom(const AssignParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AssignParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.AssignParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AssignParameters)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class GradientAccumulationStatus :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.GradientAccumulationStatus) */ {
 public:
  GradientAccumulationStatus();
  virtual ~GradientAccumulationStatus();

  GradientAccumulationStatus(const GradientAccumulationStatus& from);
  GradientAccumulationStatus(GradientAccumulationStatus&& from) noexcept
    : GradientAccumulationStatus() {
    *this = ::std::move(from);
  }

  inline GradientAccumulationStatus& operator=(const GradientAccumulationStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline GradientAccumulationStatus& operator=(GradientAccumulationStatus&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GradientAccumulationStatus& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GradientAccumulationStatus* internal_default_instance() {
    return reinterpret_cast<const GradientAccumulationStatus*>(
               &_GradientAccumulationStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(GradientAccumulationStatus& a, GradientAccumulationStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(GradientAccumulationStatus* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GradientAccumulationStatus* New() const final {
    return CreateMaybeMessage<GradientAccumulationStatus>(nullptr);
  }

  GradientAccumulationStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GradientAccumulationStatus>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GradientAccumulationStatus& from);
  void MergeFrom(const GradientAccumulationStatus& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GradientAccumulationStatus* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.GradientAccumulationStatus";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef GradientAccumulationStatus_Status Status;
  static constexpr Status UNSPECIFIED =
    GradientAccumulationStatus_Status_UNSPECIFIED;
  static constexpr Status ENABLED =
    GradientAccumulationStatus_Status_ENABLED;
  static constexpr Status DISABLED =
    GradientAccumulationStatus_Status_DISABLED;
  static inline bool Status_IsValid(int value) {
    return GradientAccumulationStatus_Status_IsValid(value);
  }
  static constexpr Status Status_MIN =
    GradientAccumulationStatus_Status_Status_MIN;
  static constexpr Status Status_MAX =
    GradientAccumulationStatus_Status_Status_MAX;
  static constexpr int Status_ARRAYSIZE =
    GradientAccumulationStatus_Status_Status_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Status_descriptor() {
    return GradientAccumulationStatus_Status_descriptor();
  }
  template<typename T>
  static inline const std::string& Status_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Status>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Status_Name.");
    return GradientAccumulationStatus_Status_Name(enum_t_value);
  }
  static inline bool Status_Parse(const std::string& name,
      Status* value) {
    return GradientAccumulationStatus_Status_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.GradientAccumulationStatus)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class HotIdReplicationConfiguration :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.HotIdReplicationConfiguration) */ {
 public:
  HotIdReplicationConfiguration();
  virtual ~HotIdReplicationConfiguration();

  HotIdReplicationConfiguration(const HotIdReplicationConfiguration& from);
  HotIdReplicationConfiguration(HotIdReplicationConfiguration&& from) noexcept
    : HotIdReplicationConfiguration() {
    *this = ::std::move(from);
  }

  inline HotIdReplicationConfiguration& operator=(const HotIdReplicationConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline HotIdReplicationConfiguration& operator=(HotIdReplicationConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HotIdReplicationConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HotIdReplicationConfiguration* internal_default_instance() {
    return reinterpret_cast<const HotIdReplicationConfiguration*>(
               &_HotIdReplicationConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(HotIdReplicationConfiguration& a, HotIdReplicationConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(HotIdReplicationConfiguration* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HotIdReplicationConfiguration* New() const final {
    return CreateMaybeMessage<HotIdReplicationConfiguration>(nullptr);
  }

  HotIdReplicationConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HotIdReplicationConfiguration>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HotIdReplicationConfiguration& from);
  void MergeFrom(const HotIdReplicationConfiguration& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HotIdReplicationConfiguration* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.HotIdReplicationConfiguration";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef HotIdReplicationConfiguration_Status Status;
  static constexpr Status UNSPECIFIED =
    HotIdReplicationConfiguration_Status_UNSPECIFIED;
  static constexpr Status ENABLED =
    HotIdReplicationConfiguration_Status_ENABLED;
  static constexpr Status DISABLED =
    HotIdReplicationConfiguration_Status_DISABLED;
  static inline bool Status_IsValid(int value) {
    return HotIdReplicationConfiguration_Status_IsValid(value);
  }
  static constexpr Status Status_MIN =
    HotIdReplicationConfiguration_Status_Status_MIN;
  static constexpr Status Status_MAX =
    HotIdReplicationConfiguration_Status_Status_MAX;
  static constexpr int Status_ARRAYSIZE =
    HotIdReplicationConfiguration_Status_Status_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Status_descriptor() {
    return HotIdReplicationConfiguration_Status_descriptor();
  }
  template<typename T>
  static inline const std::string& Status_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Status>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Status_Name.");
    return HotIdReplicationConfiguration_Status_Name(enum_t_value);
  }
  static inline bool Status_Parse(const std::string& name,
      Status* value) {
    return HotIdReplicationConfiguration_Status_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kStatusFieldNumber = 1,
  };
  // .tensorflow.tpu.HotIdReplicationConfiguration.Status status = 1;
  void clear_status();
  ::tensorflow::tpu::HotIdReplicationConfiguration_Status status() const;
  void set_status(::tensorflow::tpu::HotIdReplicationConfiguration_Status value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.HotIdReplicationConfiguration)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  int status_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class OptimizationParameters :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.OptimizationParameters) */ {
 public:
  OptimizationParameters();
  virtual ~OptimizationParameters();

  OptimizationParameters(const OptimizationParameters& from);
  OptimizationParameters(OptimizationParameters&& from) noexcept
    : OptimizationParameters() {
    *this = ::std::move(from);
  }

  inline OptimizationParameters& operator=(const OptimizationParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline OptimizationParameters& operator=(OptimizationParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const OptimizationParameters& default_instance();

  enum ParametersCase {
    kAdagrad = 3,
    kBoundedAdagrad = 19,
    kStochasticGradientDescent = 4,
    kFtrl = 5,
    kAdam = 6,
    kMomentum = 8,
    kRmsProp = 9,
    kCenteredRmsProp = 10,
    kMdlAdagradLight = 11,
    kAdadelta = 12,
    kProximalAdagrad = 14,
    kOnlineYogi = 20,
    kProximalYogi = 21,
    kFrequencyEstimator = 23,
    kUserDefinedProgram = 24,
    kAssign = 25,
    PARAMETERS_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OptimizationParameters* internal_default_instance() {
    return reinterpret_cast<const OptimizationParameters*>(
               &_OptimizationParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(OptimizationParameters& a, OptimizationParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(OptimizationParameters* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline OptimizationParameters* New() const final {
    return CreateMaybeMessage<OptimizationParameters>(nullptr);
  }

  OptimizationParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<OptimizationParameters>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const OptimizationParameters& from);
  void MergeFrom(const OptimizationParameters& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptimizationParameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.OptimizationParameters";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClippingLimitsFieldNumber = 2,
    kGradientClippingLimitsFieldNumber = 7,
    kLearningRateFieldNumber = 13,
    kHotIdReplicationConfigurationFieldNumber = 18,
    kMultiplyWeightDecayFactorByLearningRateFieldNumber = 22,
    kWeightDecayFactorFieldNumber = 16,
    kGradientAccumulationStatusFieldNumber = 17,
    kAdagradFieldNumber = 3,
    kBoundedAdagradFieldNumber = 19,
    kStochasticGradientDescentFieldNumber = 4,
    kFtrlFieldNumber = 5,
    kAdamFieldNumber = 6,
    kMomentumFieldNumber = 8,
    kRmsPropFieldNumber = 9,
    kCenteredRmsPropFieldNumber = 10,
    kMdlAdagradLightFieldNumber = 11,
    kAdadeltaFieldNumber = 12,
    kProximalAdagradFieldNumber = 14,
    kOnlineYogiFieldNumber = 20,
    kProximalYogiFieldNumber = 21,
    kFrequencyEstimatorFieldNumber = 23,
    kUserDefinedProgramFieldNumber = 24,
    kAssignFieldNumber = 25,
  };
  // .tensorflow.tpu.ClippingLimits clipping_limits = 2;
  bool has_clipping_limits() const;
  void clear_clipping_limits();
  const ::tensorflow::tpu::ClippingLimits& clipping_limits() const;
  ::tensorflow::tpu::ClippingLimits* release_clipping_limits();
  ::tensorflow::tpu::ClippingLimits* mutable_clipping_limits();
  void set_allocated_clipping_limits(::tensorflow::tpu::ClippingLimits* clipping_limits);

  // .tensorflow.tpu.ClippingLimits gradient_clipping_limits = 7;
  bool has_gradient_clipping_limits() const;
  void clear_gradient_clipping_limits();
  const ::tensorflow::tpu::ClippingLimits& gradient_clipping_limits() const;
  ::tensorflow::tpu::ClippingLimits* release_gradient_clipping_limits();
  ::tensorflow::tpu::ClippingLimits* mutable_gradient_clipping_limits();
  void set_allocated_gradient_clipping_limits(::tensorflow::tpu::ClippingLimits* gradient_clipping_limits);

  // .tensorflow.tpu.LearningRate learning_rate = 13;
  bool has_learning_rate() const;
  void clear_learning_rate();
  const ::tensorflow::tpu::LearningRate& learning_rate() const;
  ::tensorflow::tpu::LearningRate* release_learning_rate();
  ::tensorflow::tpu::LearningRate* mutable_learning_rate();
  void set_allocated_learning_rate(::tensorflow::tpu::LearningRate* learning_rate);

  // .tensorflow.tpu.HotIdReplicationConfiguration hot_id_replication_configuration = 18;
  bool has_hot_id_replication_configuration() const;
  void clear_hot_id_replication_configuration();
  const ::tensorflow::tpu::HotIdReplicationConfiguration& hot_id_replication_configuration() const;
  ::tensorflow::tpu::HotIdReplicationConfiguration* release_hot_id_replication_configuration();
  ::tensorflow::tpu::HotIdReplicationConfiguration* mutable_hot_id_replication_configuration();
  void set_allocated_hot_id_replication_configuration(::tensorflow::tpu::HotIdReplicationConfiguration* hot_id_replication_configuration);

  // bool multiply_weight_decay_factor_by_learning_rate = 22;
  void clear_multiply_weight_decay_factor_by_learning_rate();
  bool multiply_weight_decay_factor_by_learning_rate() const;
  void set_multiply_weight_decay_factor_by_learning_rate(bool value);

  // float weight_decay_factor = 16;
  void clear_weight_decay_factor();
  float weight_decay_factor() const;
  void set_weight_decay_factor(float value);

  // .tensorflow.tpu.GradientAccumulationStatus.Status gradient_accumulation_status = 17;
  void clear_gradient_accumulation_status();
  ::tensorflow::tpu::GradientAccumulationStatus_Status gradient_accumulation_status() const;
  void set_gradient_accumulation_status(::tensorflow::tpu::GradientAccumulationStatus_Status value);

  // .tensorflow.tpu.AdagradParameters adagrad = 3;
  bool has_adagrad() const;
  void clear_adagrad();
  const ::tensorflow::tpu::AdagradParameters& adagrad() const;
  ::tensorflow::tpu::AdagradParameters* release_adagrad();
  ::tensorflow::tpu::AdagradParameters* mutable_adagrad();
  void set_allocated_adagrad(::tensorflow::tpu::AdagradParameters* adagrad);

  // .tensorflow.tpu.BoundedAdagradParameters bounded_adagrad = 19;
  bool has_bounded_adagrad() const;
  void clear_bounded_adagrad();
  const ::tensorflow::tpu::BoundedAdagradParameters& bounded_adagrad() const;
  ::tensorflow::tpu::BoundedAdagradParameters* release_bounded_adagrad();
  ::tensorflow::tpu::BoundedAdagradParameters* mutable_bounded_adagrad();
  void set_allocated_bounded_adagrad(::tensorflow::tpu::BoundedAdagradParameters* bounded_adagrad);

  // .tensorflow.tpu.StochasticGradientDescentParameters stochastic_gradient_descent = 4;
  bool has_stochastic_gradient_descent() const;
  void clear_stochastic_gradient_descent();
  const ::tensorflow::tpu::StochasticGradientDescentParameters& stochastic_gradient_descent() const;
  ::tensorflow::tpu::StochasticGradientDescentParameters* release_stochastic_gradient_descent();
  ::tensorflow::tpu::StochasticGradientDescentParameters* mutable_stochastic_gradient_descent();
  void set_allocated_stochastic_gradient_descent(::tensorflow::tpu::StochasticGradientDescentParameters* stochastic_gradient_descent);

  // .tensorflow.tpu.FtrlParameters ftrl = 5;
  bool has_ftrl() const;
  void clear_ftrl();
  const ::tensorflow::tpu::FtrlParameters& ftrl() const;
  ::tensorflow::tpu::FtrlParameters* release_ftrl();
  ::tensorflow::tpu::FtrlParameters* mutable_ftrl();
  void set_allocated_ftrl(::tensorflow::tpu::FtrlParameters* ftrl);

  // .tensorflow.tpu.AdamParameters adam = 6;
  bool has_adam() const;
  void clear_adam();
  const ::tensorflow::tpu::AdamParameters& adam() const;
  ::tensorflow::tpu::AdamParameters* release_adam();
  ::tensorflow::tpu::AdamParameters* mutable_adam();
  void set_allocated_adam(::tensorflow::tpu::AdamParameters* adam);

  // .tensorflow.tpu.MomentumParameters momentum = 8;
  bool has_momentum() const;
  void clear_momentum();
  const ::tensorflow::tpu::MomentumParameters& momentum() const;
  ::tensorflow::tpu::MomentumParameters* release_momentum();
  ::tensorflow::tpu::MomentumParameters* mutable_momentum();
  void set_allocated_momentum(::tensorflow::tpu::MomentumParameters* momentum);

  // .tensorflow.tpu.RmsPropParameters rms_prop = 9;
  bool has_rms_prop() const;
  void clear_rms_prop();
  const ::tensorflow::tpu::RmsPropParameters& rms_prop() const;
  ::tensorflow::tpu::RmsPropParameters* release_rms_prop();
  ::tensorflow::tpu::RmsPropParameters* mutable_rms_prop();
  void set_allocated_rms_prop(::tensorflow::tpu::RmsPropParameters* rms_prop);

  // .tensorflow.tpu.CenteredRmsPropParameters centered_rms_prop = 10;
  bool has_centered_rms_prop() const;
  void clear_centered_rms_prop();
  const ::tensorflow::tpu::CenteredRmsPropParameters& centered_rms_prop() const;
  ::tensorflow::tpu::CenteredRmsPropParameters* release_centered_rms_prop();
  ::tensorflow::tpu::CenteredRmsPropParameters* mutable_centered_rms_prop();
  void set_allocated_centered_rms_prop(::tensorflow::tpu::CenteredRmsPropParameters* centered_rms_prop);

  // .tensorflow.tpu.MdlAdagradLightParameters mdl_adagrad_light = 11;
  bool has_mdl_adagrad_light() const;
  void clear_mdl_adagrad_light();
  const ::tensorflow::tpu::MdlAdagradLightParameters& mdl_adagrad_light() const;
  ::tensorflow::tpu::MdlAdagradLightParameters* release_mdl_adagrad_light();
  ::tensorflow::tpu::MdlAdagradLightParameters* mutable_mdl_adagrad_light();
  void set_allocated_mdl_adagrad_light(::tensorflow::tpu::MdlAdagradLightParameters* mdl_adagrad_light);

  // .tensorflow.tpu.AdadeltaParameters adadelta = 12;
  bool has_adadelta() const;
  void clear_adadelta();
  const ::tensorflow::tpu::AdadeltaParameters& adadelta() const;
  ::tensorflow::tpu::AdadeltaParameters* release_adadelta();
  ::tensorflow::tpu::AdadeltaParameters* mutable_adadelta();
  void set_allocated_adadelta(::tensorflow::tpu::AdadeltaParameters* adadelta);

  // .tensorflow.tpu.ProximalAdagradParameters proximal_adagrad = 14;
  bool has_proximal_adagrad() const;
  void clear_proximal_adagrad();
  const ::tensorflow::tpu::ProximalAdagradParameters& proximal_adagrad() const;
  ::tensorflow::tpu::ProximalAdagradParameters* release_proximal_adagrad();
  ::tensorflow::tpu::ProximalAdagradParameters* mutable_proximal_adagrad();
  void set_allocated_proximal_adagrad(::tensorflow::tpu::ProximalAdagradParameters* proximal_adagrad);

  // .tensorflow.tpu.OnlineYogiParameters online_yogi = 20;
  bool has_online_yogi() const;
  void clear_online_yogi();
  const ::tensorflow::tpu::OnlineYogiParameters& online_yogi() const;
  ::tensorflow::tpu::OnlineYogiParameters* release_online_yogi();
  ::tensorflow::tpu::OnlineYogiParameters* mutable_online_yogi();
  void set_allocated_online_yogi(::tensorflow::tpu::OnlineYogiParameters* online_yogi);

  // .tensorflow.tpu.ProximalYogiParameters proximal_yogi = 21;
  bool has_proximal_yogi() const;
  void clear_proximal_yogi();
  const ::tensorflow::tpu::ProximalYogiParameters& proximal_yogi() const;
  ::tensorflow::tpu::ProximalYogiParameters* release_proximal_yogi();
  ::tensorflow::tpu::ProximalYogiParameters* mutable_proximal_yogi();
  void set_allocated_proximal_yogi(::tensorflow::tpu::ProximalYogiParameters* proximal_yogi);

  // .tensorflow.tpu.FrequencyEstimatorParameters frequency_estimator = 23;
  bool has_frequency_estimator() const;
  void clear_frequency_estimator();
  const ::tensorflow::tpu::FrequencyEstimatorParameters& frequency_estimator() const;
  ::tensorflow::tpu::FrequencyEstimatorParameters* release_frequency_estimator();
  ::tensorflow::tpu::FrequencyEstimatorParameters* mutable_frequency_estimator();
  void set_allocated_frequency_estimator(::tensorflow::tpu::FrequencyEstimatorParameters* frequency_estimator);

  // .tensorflow.tpu.UserDefinedProgramParameters user_defined_program = 24;
  bool has_user_defined_program() const;
  void clear_user_defined_program();
  const ::tensorflow::tpu::UserDefinedProgramParameters& user_defined_program() const;
  ::tensorflow::tpu::UserDefinedProgramParameters* release_user_defined_program();
  ::tensorflow::tpu::UserDefinedProgramParameters* mutable_user_defined_program();
  void set_allocated_user_defined_program(::tensorflow::tpu::UserDefinedProgramParameters* user_defined_program);

  // .tensorflow.tpu.AssignParameters assign = 25;
  bool has_assign() const;
  void clear_assign();
  const ::tensorflow::tpu::AssignParameters& assign() const;
  ::tensorflow::tpu::AssignParameters* release_assign();
  ::tensorflow::tpu::AssignParameters* mutable_assign();
  void set_allocated_assign(::tensorflow::tpu::AssignParameters* assign);

  void clear_parameters();
  ParametersCase parameters_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.OptimizationParameters)
 private:
  class _Internal;
  void set_has_adagrad();
  void set_has_bounded_adagrad();
  void set_has_stochastic_gradient_descent();
  void set_has_ftrl();
  void set_has_adam();
  void set_has_momentum();
  void set_has_rms_prop();
  void set_has_centered_rms_prop();
  void set_has_mdl_adagrad_light();
  void set_has_adadelta();
  void set_has_proximal_adagrad();
  void set_has_online_yogi();
  void set_has_proximal_yogi();
  void set_has_frequency_estimator();
  void set_has_user_defined_program();
  void set_has_assign();

  inline bool has_parameters() const;
  inline void clear_has_parameters();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::tpu::ClippingLimits* clipping_limits_;
  ::tensorflow::tpu::ClippingLimits* gradient_clipping_limits_;
  ::tensorflow::tpu::LearningRate* learning_rate_;
  ::tensorflow::tpu::HotIdReplicationConfiguration* hot_id_replication_configuration_;
  bool multiply_weight_decay_factor_by_learning_rate_;
  float weight_decay_factor_;
  int gradient_accumulation_status_;
  union ParametersUnion {
    ParametersUnion() {}
    ::tensorflow::tpu::AdagradParameters* adagrad_;
    ::tensorflow::tpu::BoundedAdagradParameters* bounded_adagrad_;
    ::tensorflow::tpu::StochasticGradientDescentParameters* stochastic_gradient_descent_;
    ::tensorflow::tpu::FtrlParameters* ftrl_;
    ::tensorflow::tpu::AdamParameters* adam_;
    ::tensorflow::tpu::MomentumParameters* momentum_;
    ::tensorflow::tpu::RmsPropParameters* rms_prop_;
    ::tensorflow::tpu::CenteredRmsPropParameters* centered_rms_prop_;
    ::tensorflow::tpu::MdlAdagradLightParameters* mdl_adagrad_light_;
    ::tensorflow::tpu::AdadeltaParameters* adadelta_;
    ::tensorflow::tpu::ProximalAdagradParameters* proximal_adagrad_;
    ::tensorflow::tpu::OnlineYogiParameters* online_yogi_;
    ::tensorflow::tpu::ProximalYogiParameters* proximal_yogi_;
    ::tensorflow::tpu::FrequencyEstimatorParameters* frequency_estimator_;
    ::tensorflow::tpu::UserDefinedProgramParameters* user_defined_program_;
    ::tensorflow::tpu::AssignParameters* assign_;
  } parameters_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class StateVariableSpecification_UserDefined :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StateVariableSpecification.UserDefined) */ {
 public:
  StateVariableSpecification_UserDefined();
  virtual ~StateVariableSpecification_UserDefined();

  StateVariableSpecification_UserDefined(const StateVariableSpecification_UserDefined& from);
  StateVariableSpecification_UserDefined(StateVariableSpecification_UserDefined&& from) noexcept
    : StateVariableSpecification_UserDefined() {
    *this = ::std::move(from);
  }

  inline StateVariableSpecification_UserDefined& operator=(const StateVariableSpecification_UserDefined& from) {
    CopyFrom(from);
    return *this;
  }
  inline StateVariableSpecification_UserDefined& operator=(StateVariableSpecification_UserDefined&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StateVariableSpecification_UserDefined& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StateVariableSpecification_UserDefined* internal_default_instance() {
    return reinterpret_cast<const StateVariableSpecification_UserDefined*>(
               &_StateVariableSpecification_UserDefined_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(StateVariableSpecification_UserDefined& a, StateVariableSpecification_UserDefined& b) {
    a.Swap(&b);
  }
  inline void Swap(StateVariableSpecification_UserDefined* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StateVariableSpecification_UserDefined* New() const final {
    return CreateMaybeMessage<StateVariableSpecification_UserDefined>(nullptr);
  }

  StateVariableSpecification_UserDefined* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StateVariableSpecification_UserDefined>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StateVariableSpecification_UserDefined& from);
  void MergeFrom(const StateVariableSpecification_UserDefined& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StateVariableSpecification_UserDefined* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.StateVariableSpecification.UserDefined";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPaddingInitialValueFieldNumber = 1,
  };
  // double padding_initial_value = 1;
  void clear_padding_initial_value();
  double padding_initial_value() const;
  void set_padding_initial_value(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification.UserDefined)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  double padding_initial_value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class StateVariableSpecification_FillWithConstant :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StateVariableSpecification.FillWithConstant) */ {
 public:
  StateVariableSpecification_FillWithConstant();
  virtual ~StateVariableSpecification_FillWithConstant();

  StateVariableSpecification_FillWithConstant(const StateVariableSpecification_FillWithConstant& from);
  StateVariableSpecification_FillWithConstant(StateVariableSpecification_FillWithConstant&& from) noexcept
    : StateVariableSpecification_FillWithConstant() {
    *this = ::std::move(from);
  }

  inline StateVariableSpecification_FillWithConstant& operator=(const StateVariableSpecification_FillWithConstant& from) {
    CopyFrom(from);
    return *this;
  }
  inline StateVariableSpecification_FillWithConstant& operator=(StateVariableSpecification_FillWithConstant&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StateVariableSpecification_FillWithConstant& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StateVariableSpecification_FillWithConstant* internal_default_instance() {
    return reinterpret_cast<const StateVariableSpecification_FillWithConstant*>(
               &_StateVariableSpecification_FillWithConstant_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(StateVariableSpecification_FillWithConstant& a, StateVariableSpecification_FillWithConstant& b) {
    a.Swap(&b);
  }
  inline void Swap(StateVariableSpecification_FillWithConstant* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StateVariableSpecification_FillWithConstant* New() const final {
    return CreateMaybeMessage<StateVariableSpecification_FillWithConstant>(nullptr);
  }

  StateVariableSpecification_FillWithConstant* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StateVariableSpecification_FillWithConstant>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StateVariableSpecification_FillWithConstant& from);
  void MergeFrom(const StateVariableSpecification_FillWithConstant& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StateVariableSpecification_FillWithConstant* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.StateVariableSpecification.FillWithConstant";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInitialValueFieldNumber = 1,
  };
  // double initial_value = 1;
  void clear_initial_value();
  double initial_value() const;
  void set_initial_value(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  double initial_value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class StateVariableSpecification :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StateVariableSpecification) */ {
 public:
  StateVariableSpecification();
  virtual ~StateVariableSpecification();

  StateVariableSpecification(const StateVariableSpecification& from);
  StateVariableSpecification(StateVariableSpecification&& from) noexcept
    : StateVariableSpecification() {
    *this = ::std::move(from);
  }

  inline StateVariableSpecification& operator=(const StateVariableSpecification& from) {
    CopyFrom(from);
    return *this;
  }
  inline StateVariableSpecification& operator=(StateVariableSpecification&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StateVariableSpecification& default_instance();

  enum UsageCase {
    kUserDefined = 2,
    kFillWithConstant = 3,
    USAGE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StateVariableSpecification* internal_default_instance() {
    return reinterpret_cast<const StateVariableSpecification*>(
               &_StateVariableSpecification_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(StateVariableSpecification& a, StateVariableSpecification& b) {
    a.Swap(&b);
  }
  inline void Swap(StateVariableSpecification* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StateVariableSpecification* New() const final {
    return CreateMaybeMessage<StateVariableSpecification>(nullptr);
  }

  StateVariableSpecification* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StateVariableSpecification>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StateVariableSpecification& from);
  void MergeFrom(const StateVariableSpecification& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StateVariableSpecification* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.StateVariableSpecification";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef StateVariableSpecification_UserDefined UserDefined;
  typedef StateVariableSpecification_FillWithConstant FillWithConstant;

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kUserDefinedFieldNumber = 2,
    kFillWithConstantFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // .tensorflow.tpu.StateVariableSpecification.UserDefined user_defined = 2;
  bool has_user_defined() const;
  void clear_user_defined();
  const ::tensorflow::tpu::StateVariableSpecification_UserDefined& user_defined() const;
  ::tensorflow::tpu::StateVariableSpecification_UserDefined* release_user_defined();
  ::tensorflow::tpu::StateVariableSpecification_UserDefined* mutable_user_defined();
  void set_allocated_user_defined(::tensorflow::tpu::StateVariableSpecification_UserDefined* user_defined);

  // .tensorflow.tpu.StateVariableSpecification.FillWithConstant fill_with_constant = 3;
  bool has_fill_with_constant() const;
  void clear_fill_with_constant();
  const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant& fill_with_constant() const;
  ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* release_fill_with_constant();
  ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* mutable_fill_with_constant();
  void set_allocated_fill_with_constant(::tensorflow::tpu::StateVariableSpecification_FillWithConstant* fill_with_constant);

  void clear_usage();
  UsageCase usage_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification)
 private:
  class _Internal;
  void set_has_user_defined();
  void set_has_fill_with_constant();

  inline bool has_usage() const;
  inline void clear_has_usage();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  union UsageUnion {
    UsageUnion() {}
    ::tensorflow::tpu::StateVariableSpecification_UserDefined* user_defined_;
    ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* fill_with_constant_;
  } usage_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ClippingLimits

// .google.protobuf.FloatValue lower = 1;
inline bool ClippingLimits::has_lower() const {
  return this != internal_default_instance() && lower_ != nullptr;
}
inline const PROTOBUF_NAMESPACE_ID::FloatValue& ClippingLimits::lower() const {
  const PROTOBUF_NAMESPACE_ID::FloatValue* p = lower_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ClippingLimits.lower)
  return p != nullptr ? *p : *reinterpret_cast<const PROTOBUF_NAMESPACE_ID::FloatValue*>(
      &PROTOBUF_NAMESPACE_ID::_FloatValue_default_instance_);
}
inline PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::release_lower() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.ClippingLimits.lower)
  
  PROTOBUF_NAMESPACE_ID::FloatValue* temp = lower_;
  lower_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::mutable_lower() {
  
  if (lower_ == nullptr) {
    auto* p = CreateMaybeMessage<PROTOBUF_NAMESPACE_ID::FloatValue>(GetArenaNoVirtual());
    lower_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.ClippingLimits.lower)
  return lower_;
}
inline void ClippingLimits::set_allocated_lower(PROTOBUF_NAMESPACE_ID::FloatValue* lower) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(lower_);
  }
  if (lower) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(lower)->GetArena();
    if (message_arena != submessage_arena) {
      lower = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, lower, submessage_arena);
    }
    
  } else {
    
  }
  lower_ = lower;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.ClippingLimits.lower)
}

// .google.protobuf.FloatValue upper = 2;
inline bool ClippingLimits::has_upper() const {
  return this != internal_default_instance() && upper_ != nullptr;
}
inline const PROTOBUF_NAMESPACE_ID::FloatValue& ClippingLimits::upper() const {
  const PROTOBUF_NAMESPACE_ID::FloatValue* p = upper_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ClippingLimits.upper)
  return p != nullptr ? *p : *reinterpret_cast<const PROTOBUF_NAMESPACE_ID::FloatValue*>(
      &PROTOBUF_NAMESPACE_ID::_FloatValue_default_instance_);
}
inline PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::release_upper() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.ClippingLimits.upper)
  
  PROTOBUF_NAMESPACE_ID::FloatValue* temp = upper_;
  upper_ = nullptr;
  return temp;
}
inline PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::mutable_upper() {
  
  if (upper_ == nullptr) {
    auto* p = CreateMaybeMessage<PROTOBUF_NAMESPACE_ID::FloatValue>(GetArenaNoVirtual());
    upper_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.ClippingLimits.upper)
  return upper_;
}
inline void ClippingLimits::set_allocated_upper(PROTOBUF_NAMESPACE_ID::FloatValue* upper) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(upper_);
  }
  if (upper) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(upper)->GetArena();
    if (message_arena != submessage_arena) {
      upper = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, upper, submessage_arena);
    }
    
  } else {
    
  }
  upper_ = upper;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.ClippingLimits.upper)
}

// -------------------------------------------------------------------

// DynamicLearningRate

// int32 tag = 1;
inline void DynamicLearningRate::clear_tag() {
  tag_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DynamicLearningRate::tag() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.DynamicLearningRate.tag)
  return tag_;
}
inline void DynamicLearningRate::set_tag(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  tag_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.DynamicLearningRate.tag)
}

// -------------------------------------------------------------------

// LearningRate

// float constant = 1;
inline bool LearningRate::has_constant() const {
  return learning_rate_case() == kConstant;
}
inline void LearningRate::set_has_constant() {
  _oneof_case_[0] = kConstant;
}
inline void LearningRate::clear_constant() {
  if (has_constant()) {
    learning_rate_.constant_ = 0;
    clear_has_learning_rate();
  }
}
inline float LearningRate::constant() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.LearningRate.constant)
  if (has_constant()) {
    return learning_rate_.constant_;
  }
  return 0;
}
inline void LearningRate::set_constant(float value) {
  if (!has_constant()) {
    clear_learning_rate();
    set_has_constant();
  }
  learning_rate_.constant_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.LearningRate.constant)
}

// .tensorflow.tpu.DynamicLearningRate dynamic = 2;
inline bool LearningRate::has_dynamic() const {
  return learning_rate_case() == kDynamic;
}
inline void LearningRate::set_has_dynamic() {
  _oneof_case_[0] = kDynamic;
}
inline void LearningRate::clear_dynamic() {
  if (has_dynamic()) {
    delete learning_rate_.dynamic_;
    clear_has_learning_rate();
  }
}
inline ::tensorflow::tpu::DynamicLearningRate* LearningRate::release_dynamic() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.LearningRate.dynamic)
  if (has_dynamic()) {
    clear_has_learning_rate();
      ::tensorflow::tpu::DynamicLearningRate* temp = learning_rate_.dynamic_;
    learning_rate_.dynamic_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::DynamicLearningRate& LearningRate::dynamic() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.LearningRate.dynamic)
  return has_dynamic()
      ? *learning_rate_.dynamic_
      : *reinterpret_cast< ::tensorflow::tpu::DynamicLearningRate*>(&::tensorflow::tpu::_DynamicLearningRate_default_instance_);
}
inline ::tensorflow::tpu::DynamicLearningRate* LearningRate::mutable_dynamic() {
  if (!has_dynamic()) {
    clear_learning_rate();
    set_has_dynamic();
    learning_rate_.dynamic_ = CreateMaybeMessage< ::tensorflow::tpu::DynamicLearningRate >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.LearningRate.dynamic)
  return learning_rate_.dynamic_;
}

inline bool LearningRate::has_learning_rate() const {
  return learning_rate_case() != LEARNING_RATE_NOT_SET;
}
inline void LearningRate::clear_has_learning_rate() {
  _oneof_case_[0] = LEARNING_RATE_NOT_SET;
}
inline LearningRate::LearningRateCase LearningRate::learning_rate_case() const {
  return LearningRate::LearningRateCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// AdagradParameters

// -------------------------------------------------------------------

// BoundedAdagradParameters

// bool update_accumulator_first = 1;
inline void BoundedAdagradParameters::clear_update_accumulator_first() {
  update_accumulator_first_ = false;
}
inline bool BoundedAdagradParameters::update_accumulator_first() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.BoundedAdagradParameters.update_accumulator_first)
  return update_accumulator_first_;
}
inline void BoundedAdagradParameters::set_update_accumulator_first(bool value) {
  
  update_accumulator_first_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.BoundedAdagradParameters.update_accumulator_first)
}

// float max_var_update = 2;
inline void BoundedAdagradParameters::clear_max_var_update() {
  max_var_update_ = 0;
}
inline float BoundedAdagradParameters::max_var_update() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.BoundedAdagradParameters.max_var_update)
  return max_var_update_;
}
inline void BoundedAdagradParameters::set_max_var_update(float value) {
  
  max_var_update_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.BoundedAdagradParameters.max_var_update)
}

// float max_accumulator = 3;
inline void BoundedAdagradParameters::clear_max_accumulator() {
  max_accumulator_ = 0;
}
inline float BoundedAdagradParameters::max_accumulator() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.BoundedAdagradParameters.max_accumulator)
  return max_accumulator_;
}
inline void BoundedAdagradParameters::set_max_accumulator(float value) {
  
  max_accumulator_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.BoundedAdagradParameters.max_accumulator)
}

// -------------------------------------------------------------------

// StochasticGradientDescentParameters

// -------------------------------------------------------------------

// FtrlParameters

// float l1 = 1;
inline void FtrlParameters::clear_l1() {
  l1_ = 0;
}
inline float FtrlParameters::l1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.l1)
  return l1_;
}
inline void FtrlParameters::set_l1(float value) {
  
  l1_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.l1)
}

// float l2 = 2;
inline void FtrlParameters::clear_l2() {
  l2_ = 0;
}
inline float FtrlParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.l2)
  return l2_;
}
inline void FtrlParameters::set_l2(float value) {
  
  l2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.l2)
}

// float lr_power = 3;
inline void FtrlParameters::clear_lr_power() {
  lr_power_ = 0;
}
inline float FtrlParameters::lr_power() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.lr_power)
  return lr_power_;
}
inline void FtrlParameters::set_lr_power(float value) {
  
  lr_power_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.lr_power)
}

// float beta = 7;
inline void FtrlParameters::clear_beta() {
  beta_ = 0;
}
inline float FtrlParameters::beta() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.beta)
  return beta_;
}
inline void FtrlParameters::set_beta(float value) {
  
  beta_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.beta)
}

// bool multiply_linear_by_lr = 6;
inline void FtrlParameters::clear_multiply_linear_by_lr() {
  multiply_linear_by_lr_ = false;
}
inline bool FtrlParameters::multiply_linear_by_lr() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.multiply_linear_by_lr)
  return multiply_linear_by_lr_;
}
inline void FtrlParameters::set_multiply_linear_by_lr(bool value) {
  
  multiply_linear_by_lr_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.multiply_linear_by_lr)
}

// bool allow_zero_accumulator = 8;
inline void FtrlParameters::clear_allow_zero_accumulator() {
  allow_zero_accumulator_ = false;
}
inline bool FtrlParameters::allow_zero_accumulator() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.allow_zero_accumulator)
  return allow_zero_accumulator_;
}
inline void FtrlParameters::set_allow_zero_accumulator(bool value) {
  
  allow_zero_accumulator_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.allow_zero_accumulator)
}

// -------------------------------------------------------------------

// AdamParameters

// float beta1 = 3;
inline void AdamParameters::clear_beta1() {
  beta1_ = 0;
}
inline float AdamParameters::beta1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.beta1)
  return beta1_;
}
inline void AdamParameters::set_beta1(float value) {
  
  beta1_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.beta1)
}

// float beta2 = 4;
inline void AdamParameters::clear_beta2() {
  beta2_ = 0;
}
inline float AdamParameters::beta2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.beta2)
  return beta2_;
}
inline void AdamParameters::set_beta2(float value) {
  
  beta2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.beta2)
}

// float epsilon = 5;
inline void AdamParameters::clear_epsilon() {
  epsilon_ = 0;
}
inline float AdamParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.epsilon)
  return epsilon_;
}
inline void AdamParameters::set_epsilon(float value) {
  
  epsilon_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.epsilon)
}

// bool use_non_lazy_adam = 8;
inline void AdamParameters::clear_use_non_lazy_adam() {
  use_non_lazy_adam_ = false;
}
inline bool AdamParameters::use_non_lazy_adam() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.use_non_lazy_adam)
  return use_non_lazy_adam_;
}
inline void AdamParameters::set_use_non_lazy_adam(bool value) {
  
  use_non_lazy_adam_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.use_non_lazy_adam)
}

// bool use_sum_inside_sqrt = 10;
inline void AdamParameters::clear_use_sum_inside_sqrt() {
  use_sum_inside_sqrt_ = false;
}
inline bool AdamParameters::use_sum_inside_sqrt() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.use_sum_inside_sqrt)
  return use_sum_inside_sqrt_;
}
inline void AdamParameters::set_use_sum_inside_sqrt(bool value) {
  
  use_sum_inside_sqrt_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.use_sum_inside_sqrt)
}

// -------------------------------------------------------------------

// MomentumParameters

// float momentum = 1;
inline void MomentumParameters::clear_momentum() {
  momentum_ = 0;
}
inline float MomentumParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MomentumParameters.momentum)
  return momentum_;
}
inline void MomentumParameters::set_momentum(float value) {
  
  momentum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MomentumParameters.momentum)
}

// bool use_nesterov = 2;
inline void MomentumParameters::clear_use_nesterov() {
  use_nesterov_ = false;
}
inline bool MomentumParameters::use_nesterov() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MomentumParameters.use_nesterov)
  return use_nesterov_;
}
inline void MomentumParameters::set_use_nesterov(bool value) {
  
  use_nesterov_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MomentumParameters.use_nesterov)
}

// -------------------------------------------------------------------

// RmsPropParameters

// float rho = 1;
inline void RmsPropParameters::clear_rho() {
  rho_ = 0;
}
inline float RmsPropParameters::rho() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.rho)
  return rho_;
}
inline void RmsPropParameters::set_rho(float value) {
  
  rho_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.rho)
}

// float momentum = 2;
inline void RmsPropParameters::clear_momentum() {
  momentum_ = 0;
}
inline float RmsPropParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.momentum)
  return momentum_;
}
inline void RmsPropParameters::set_momentum(float value) {
  
  momentum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.momentum)
}

// float epsilon = 3;
inline void RmsPropParameters::clear_epsilon() {
  epsilon_ = 0;
}
inline float RmsPropParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.epsilon)
  return epsilon_;
}
inline void RmsPropParameters::set_epsilon(float value) {
  
  epsilon_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.epsilon)
}

// -------------------------------------------------------------------

// CenteredRmsPropParameters

// float rho = 1;
inline void CenteredRmsPropParameters::clear_rho() {
  rho_ = 0;
}
inline float CenteredRmsPropParameters::rho() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.rho)
  return rho_;
}
inline void CenteredRmsPropParameters::set_rho(float value) {
  
  rho_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.rho)
}

// float momentum = 2;
inline void CenteredRmsPropParameters::clear_momentum() {
  momentum_ = 0;
}
inline float CenteredRmsPropParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.momentum)
  return momentum_;
}
inline void CenteredRmsPropParameters::set_momentum(float value) {
  
  momentum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.momentum)
}

// float epsilon = 3;
inline void CenteredRmsPropParameters::clear_epsilon() {
  epsilon_ = 0;
}
inline float CenteredRmsPropParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.epsilon)
  return epsilon_;
}
inline void CenteredRmsPropParameters::set_epsilon(float value) {
  
  epsilon_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.epsilon)
}

// -------------------------------------------------------------------

// MdlAdagradLightParameters

// float l2 = 1;
inline void MdlAdagradLightParameters::clear_l2() {
  l2_ = 0;
}
inline float MdlAdagradLightParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.l2)
  return l2_;
}
inline void MdlAdagradLightParameters::set_l2(float value) {
  
  l2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.l2)
}

// float lr_power = 2;
inline void MdlAdagradLightParameters::clear_lr_power() {
  lr_power_ = 0;
}
inline float MdlAdagradLightParameters::lr_power() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.lr_power)
  return lr_power_;
}
inline void MdlAdagradLightParameters::set_lr_power(float value) {
  
  lr_power_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.lr_power)
}

// float min_servable_mdl_benefit = 3;
inline void MdlAdagradLightParameters::clear_min_servable_mdl_benefit() {
  min_servable_mdl_benefit_ = 0;
}
inline float MdlAdagradLightParameters::min_servable_mdl_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.min_servable_mdl_benefit)
  return min_servable_mdl_benefit_;
}
inline void MdlAdagradLightParameters::set_min_servable_mdl_benefit(float value) {
  
  min_servable_mdl_benefit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.min_servable_mdl_benefit)
}

// float mdl_mix_in_margin = 4;
inline void MdlAdagradLightParameters::clear_mdl_mix_in_margin() {
  mdl_mix_in_margin_ = 0;
}
inline float MdlAdagradLightParameters::mdl_mix_in_margin() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_mix_in_margin)
  return mdl_mix_in_margin_;
}
inline void MdlAdagradLightParameters::set_mdl_mix_in_margin(float value) {
  
  mdl_mix_in_margin_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_mix_in_margin)
}

// float mdl_benefit_rampup_coeff = 5;
inline void MdlAdagradLightParameters::clear_mdl_benefit_rampup_coeff() {
  mdl_benefit_rampup_coeff_ = 0;
}
inline float MdlAdagradLightParameters::mdl_benefit_rampup_coeff() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_benefit_rampup_coeff)
  return mdl_benefit_rampup_coeff_;
}
inline void MdlAdagradLightParameters::set_mdl_benefit_rampup_coeff(float value) {
  
  mdl_benefit_rampup_coeff_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_benefit_rampup_coeff)
}

// float mdl_min_weight = 6;
inline void MdlAdagradLightParameters::clear_mdl_min_weight() {
  mdl_min_weight_ = 0;
}
inline float MdlAdagradLightParameters::mdl_min_weight() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_min_weight)
  return mdl_min_weight_;
}
inline void MdlAdagradLightParameters::set_mdl_min_weight(float value) {
  
  mdl_min_weight_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_min_weight)
}

// float benefit_revisit_scale = 7;
inline void MdlAdagradLightParameters::clear_benefit_revisit_scale() {
  benefit_revisit_scale_ = 0;
}
inline float MdlAdagradLightParameters::benefit_revisit_scale() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.benefit_revisit_scale)
  return benefit_revisit_scale_;
}
inline void MdlAdagradLightParameters::set_benefit_revisit_scale(float value) {
  
  benefit_revisit_scale_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.benefit_revisit_scale)
}

// float max_event_benefit = 8;
inline void MdlAdagradLightParameters::clear_max_event_benefit() {
  max_event_benefit_ = 0;
}
inline float MdlAdagradLightParameters::max_event_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.max_event_benefit)
  return max_event_benefit_;
}
inline void MdlAdagradLightParameters::set_max_event_benefit(float value) {
  
  max_event_benefit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.max_event_benefit)
}

// float max_total_benefit = 9;
inline void MdlAdagradLightParameters::clear_max_total_benefit() {
  max_total_benefit_ = 0;
}
inline float MdlAdagradLightParameters::max_total_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.max_total_benefit)
  return max_total_benefit_;
}
inline void MdlAdagradLightParameters::set_max_total_benefit(float value) {
  
  max_total_benefit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.max_total_benefit)
}

// float mdl_hard_limit = 10;
inline void MdlAdagradLightParameters::clear_mdl_hard_limit() {
  mdl_hard_limit_ = 0;
}
inline float MdlAdagradLightParameters::mdl_hard_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_hard_limit)
  return mdl_hard_limit_;
}
inline void MdlAdagradLightParameters::set_mdl_hard_limit(float value) {
  
  mdl_hard_limit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_hard_limit)
}

// bool hard_limit_min_benefit = 11;
inline void MdlAdagradLightParameters::clear_hard_limit_min_benefit() {
  hard_limit_min_benefit_ = false;
}
inline bool MdlAdagradLightParameters::hard_limit_min_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.hard_limit_min_benefit)
  return hard_limit_min_benefit_;
}
inline void MdlAdagradLightParameters::set_hard_limit_min_benefit(bool value) {
  
  hard_limit_min_benefit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.hard_limit_min_benefit)
}

// bool mdl_regularize = 12;
inline void MdlAdagradLightParameters::clear_mdl_regularize() {
  mdl_regularize_ = false;
}
inline bool MdlAdagradLightParameters::mdl_regularize() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_regularize)
  return mdl_regularize_;
}
inline void MdlAdagradLightParameters::set_mdl_regularize(bool value) {
  
  mdl_regularize_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_regularize)
}

// -------------------------------------------------------------------

// AdadeltaParameters

// float rho = 1;
inline void AdadeltaParameters::clear_rho() {
  rho_ = 0;
}
inline float AdadeltaParameters::rho() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdadeltaParameters.rho)
  return rho_;
}
inline void AdadeltaParameters::set_rho(float value) {
  
  rho_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdadeltaParameters.rho)
}

// float epsilon = 2;
inline void AdadeltaParameters::clear_epsilon() {
  epsilon_ = 0;
}
inline float AdadeltaParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdadeltaParameters.epsilon)
  return epsilon_;
}
inline void AdadeltaParameters::set_epsilon(float value) {
  
  epsilon_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdadeltaParameters.epsilon)
}

// -------------------------------------------------------------------

// ProximalAdagradParameters

// float l1 = 1;
inline void ProximalAdagradParameters::clear_l1() {
  l1_ = 0;
}
inline float ProximalAdagradParameters::l1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalAdagradParameters.l1)
  return l1_;
}
inline void ProximalAdagradParameters::set_l1(float value) {
  
  l1_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalAdagradParameters.l1)
}

// float l2 = 2;
inline void ProximalAdagradParameters::clear_l2() {
  l2_ = 0;
}
inline float ProximalAdagradParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalAdagradParameters.l2)
  return l2_;
}
inline void ProximalAdagradParameters::set_l2(float value) {
  
  l2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalAdagradParameters.l2)
}

// -------------------------------------------------------------------

// OnlineYogiParameters

// float l1 = 1;
inline void OnlineYogiParameters::clear_l1() {
  l1_ = 0;
}
inline float OnlineYogiParameters::l1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OnlineYogiParameters.l1)
  return l1_;
}
inline void OnlineYogiParameters::set_l1(float value) {
  
  l1_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OnlineYogiParameters.l1)
}

// float l2 = 2;
inline void OnlineYogiParameters::clear_l2() {
  l2_ = 0;
}
inline float OnlineYogiParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OnlineYogiParameters.l2)
  return l2_;
}
inline void OnlineYogiParameters::set_l2(float value) {
  
  l2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OnlineYogiParameters.l2)
}

// float beta2 = 3;
inline void OnlineYogiParameters::clear_beta2() {
  beta2_ = 0;
}
inline float OnlineYogiParameters::beta2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OnlineYogiParameters.beta2)
  return beta2_;
}
inline void OnlineYogiParameters::set_beta2(float value) {
  
  beta2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OnlineYogiParameters.beta2)
}

// -------------------------------------------------------------------

// ProximalYogiParameters

// float l1 = 1;
inline void ProximalYogiParameters::clear_l1() {
  l1_ = 0;
}
inline float ProximalYogiParameters::l1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalYogiParameters.l1)
  return l1_;
}
inline void ProximalYogiParameters::set_l1(float value) {
  
  l1_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalYogiParameters.l1)
}

// float l2 = 2;
inline void ProximalYogiParameters::clear_l2() {
  l2_ = 0;
}
inline float ProximalYogiParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalYogiParameters.l2)
  return l2_;
}
inline void ProximalYogiParameters::set_l2(float value) {
  
  l2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalYogiParameters.l2)
}

// float beta1 = 3;
inline void ProximalYogiParameters::clear_beta1() {
  beta1_ = 0;
}
inline float ProximalYogiParameters::beta1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalYogiParameters.beta1)
  return beta1_;
}
inline void ProximalYogiParameters::set_beta1(float value) {
  
  beta1_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalYogiParameters.beta1)
}

// float beta2 = 4;
inline void ProximalYogiParameters::clear_beta2() {
  beta2_ = 0;
}
inline float ProximalYogiParameters::beta2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalYogiParameters.beta2)
  return beta2_;
}
inline void ProximalYogiParameters::set_beta2(float value) {
  
  beta2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalYogiParameters.beta2)
}

// float epsilon = 5;
inline void ProximalYogiParameters::clear_epsilon() {
  epsilon_ = 0;
}
inline float ProximalYogiParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalYogiParameters.epsilon)
  return epsilon_;
}
inline void ProximalYogiParameters::set_epsilon(float value) {
  
  epsilon_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalYogiParameters.epsilon)
}

// -------------------------------------------------------------------

// FrequencyEstimatorParameters

// float tau = 1;
inline void FrequencyEstimatorParameters::clear_tau() {
  tau_ = 0;
}
inline float FrequencyEstimatorParameters::tau() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FrequencyEstimatorParameters.tau)
  return tau_;
}
inline void FrequencyEstimatorParameters::set_tau(float value) {
  
  tau_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FrequencyEstimatorParameters.tau)
}

// float max_delta = 2;
inline void FrequencyEstimatorParameters::clear_max_delta() {
  max_delta_ = 0;
}
inline float FrequencyEstimatorParameters::max_delta() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FrequencyEstimatorParameters.max_delta)
  return max_delta_;
}
inline void FrequencyEstimatorParameters::set_max_delta(float value) {
  
  max_delta_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FrequencyEstimatorParameters.max_delta)
}

// float outlier_threshold = 3;
inline void FrequencyEstimatorParameters::clear_outlier_threshold() {
  outlier_threshold_ = 0;
}
inline float FrequencyEstimatorParameters::outlier_threshold() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FrequencyEstimatorParameters.outlier_threshold)
  return outlier_threshold_;
}
inline void FrequencyEstimatorParameters::set_outlier_threshold(float value) {
  
  outlier_threshold_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FrequencyEstimatorParameters.outlier_threshold)
}

// float weight_exponent = 4;
inline void FrequencyEstimatorParameters::clear_weight_exponent() {
  weight_exponent_ = 0;
}
inline float FrequencyEstimatorParameters::weight_exponent() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FrequencyEstimatorParameters.weight_exponent)
  return weight_exponent_;
}
inline void FrequencyEstimatorParameters::set_weight_exponent(float value) {
  
  weight_exponent_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FrequencyEstimatorParameters.weight_exponent)
}

// -------------------------------------------------------------------

// UserDefinedProgramParameters

// .xla.HloModuleProto program = 1;
inline bool UserDefinedProgramParameters::has_program() const {
  return this != internal_default_instance() && program_ != nullptr;
}
inline const ::xla::HloModuleProto& UserDefinedProgramParameters::program() const {
  const ::xla::HloModuleProto* p = program_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.UserDefinedProgramParameters.program)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloModuleProto*>(
      &::xla::_HloModuleProto_default_instance_);
}
inline ::xla::HloModuleProto* UserDefinedProgramParameters::release_program() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.UserDefinedProgramParameters.program)
  
  ::xla::HloModuleProto* temp = program_;
  program_ = nullptr;
  return temp;
}
inline ::xla::HloModuleProto* UserDefinedProgramParameters::mutable_program() {
  
  if (program_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloModuleProto>(GetArenaNoVirtual());
    program_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.UserDefinedProgramParameters.program)
  return program_;
}
inline void UserDefinedProgramParameters::set_allocated_program(::xla::HloModuleProto* program) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(program_);
  }
  if (program) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(program)->GetArena();
    if (message_arena != submessage_arena) {
      program = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, program, submessage_arena);
    }
    
  } else {
    
  }
  program_ = program;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.UserDefinedProgramParameters.program)
}

// repeated float padding_values = 2;
inline int UserDefinedProgramParameters::padding_values_size() const {
  return padding_values_.size();
}
inline void UserDefinedProgramParameters::clear_padding_values() {
  padding_values_.Clear();
}
inline float UserDefinedProgramParameters::padding_values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.UserDefinedProgramParameters.padding_values)
  return padding_values_.Get(index);
}
inline void UserDefinedProgramParameters::set_padding_values(int index, float value) {
  padding_values_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.UserDefinedProgramParameters.padding_values)
}
inline void UserDefinedProgramParameters::add_padding_values(float value) {
  padding_values_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tpu.UserDefinedProgramParameters.padding_values)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
UserDefinedProgramParameters::padding_values() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.UserDefinedProgramParameters.padding_values)
  return padding_values_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
UserDefinedProgramParameters::mutable_padding_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.UserDefinedProgramParameters.padding_values)
  return &padding_values_;
}

// -------------------------------------------------------------------

// AssignParameters

// -------------------------------------------------------------------

// GradientAccumulationStatus

// -------------------------------------------------------------------

// HotIdReplicationConfiguration

// .tensorflow.tpu.HotIdReplicationConfiguration.Status status = 1;
inline void HotIdReplicationConfiguration::clear_status() {
  status_ = 0;
}
inline ::tensorflow::tpu::HotIdReplicationConfiguration_Status HotIdReplicationConfiguration::status() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.HotIdReplicationConfiguration.status)
  return static_cast< ::tensorflow::tpu::HotIdReplicationConfiguration_Status >(status_);
}
inline void HotIdReplicationConfiguration::set_status(::tensorflow::tpu::HotIdReplicationConfiguration_Status value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.HotIdReplicationConfiguration.status)
}

// -------------------------------------------------------------------

// OptimizationParameters

// .tensorflow.tpu.LearningRate learning_rate = 13;
inline bool OptimizationParameters::has_learning_rate() const {
  return this != internal_default_instance() && learning_rate_ != nullptr;
}
inline void OptimizationParameters::clear_learning_rate() {
  if (GetArenaNoVirtual() == nullptr && learning_rate_ != nullptr) {
    delete learning_rate_;
  }
  learning_rate_ = nullptr;
}
inline const ::tensorflow::tpu::LearningRate& OptimizationParameters::learning_rate() const {
  const ::tensorflow::tpu::LearningRate* p = learning_rate_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.learning_rate)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tpu::LearningRate*>(
      &::tensorflow::tpu::_LearningRate_default_instance_);
}
inline ::tensorflow::tpu::LearningRate* OptimizationParameters::release_learning_rate() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.learning_rate)
  
  ::tensorflow::tpu::LearningRate* temp = learning_rate_;
  learning_rate_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::LearningRate* OptimizationParameters::mutable_learning_rate() {
  
  if (learning_rate_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::LearningRate>(GetArenaNoVirtual());
    learning_rate_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.learning_rate)
  return learning_rate_;
}
inline void OptimizationParameters::set_allocated_learning_rate(::tensorflow::tpu::LearningRate* learning_rate) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete learning_rate_;
  }
  if (learning_rate) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      learning_rate = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, learning_rate, submessage_arena);
    }
    
  } else {
    
  }
  learning_rate_ = learning_rate;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.learning_rate)
}

// .tensorflow.tpu.ClippingLimits clipping_limits = 2;
inline bool OptimizationParameters::has_clipping_limits() const {
  return this != internal_default_instance() && clipping_limits_ != nullptr;
}
inline void OptimizationParameters::clear_clipping_limits() {
  if (GetArenaNoVirtual() == nullptr && clipping_limits_ != nullptr) {
    delete clipping_limits_;
  }
  clipping_limits_ = nullptr;
}
inline const ::tensorflow::tpu::ClippingLimits& OptimizationParameters::clipping_limits() const {
  const ::tensorflow::tpu::ClippingLimits* p = clipping_limits_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.clipping_limits)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tpu::ClippingLimits*>(
      &::tensorflow::tpu::_ClippingLimits_default_instance_);
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::release_clipping_limits() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.clipping_limits)
  
  ::tensorflow::tpu::ClippingLimits* temp = clipping_limits_;
  clipping_limits_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::mutable_clipping_limits() {
  
  if (clipping_limits_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::ClippingLimits>(GetArenaNoVirtual());
    clipping_limits_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.clipping_limits)
  return clipping_limits_;
}
inline void OptimizationParameters::set_allocated_clipping_limits(::tensorflow::tpu::ClippingLimits* clipping_limits) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete clipping_limits_;
  }
  if (clipping_limits) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      clipping_limits = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, clipping_limits, submessage_arena);
    }
    
  } else {
    
  }
  clipping_limits_ = clipping_limits;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.clipping_limits)
}

// .tensorflow.tpu.ClippingLimits gradient_clipping_limits = 7;
inline bool OptimizationParameters::has_gradient_clipping_limits() const {
  return this != internal_default_instance() && gradient_clipping_limits_ != nullptr;
}
inline void OptimizationParameters::clear_gradient_clipping_limits() {
  if (GetArenaNoVirtual() == nullptr && gradient_clipping_limits_ != nullptr) {
    delete gradient_clipping_limits_;
  }
  gradient_clipping_limits_ = nullptr;
}
inline const ::tensorflow::tpu::ClippingLimits& OptimizationParameters::gradient_clipping_limits() const {
  const ::tensorflow::tpu::ClippingLimits* p = gradient_clipping_limits_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tpu::ClippingLimits*>(
      &::tensorflow::tpu::_ClippingLimits_default_instance_);
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::release_gradient_clipping_limits() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
  
  ::tensorflow::tpu::ClippingLimits* temp = gradient_clipping_limits_;
  gradient_clipping_limits_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::mutable_gradient_clipping_limits() {
  
  if (gradient_clipping_limits_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::ClippingLimits>(GetArenaNoVirtual());
    gradient_clipping_limits_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
  return gradient_clipping_limits_;
}
inline void OptimizationParameters::set_allocated_gradient_clipping_limits(::tensorflow::tpu::ClippingLimits* gradient_clipping_limits) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete gradient_clipping_limits_;
  }
  if (gradient_clipping_limits) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      gradient_clipping_limits = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, gradient_clipping_limits, submessage_arena);
    }
    
  } else {
    
  }
  gradient_clipping_limits_ = gradient_clipping_limits;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
}

// float weight_decay_factor = 16;
inline void OptimizationParameters::clear_weight_decay_factor() {
  weight_decay_factor_ = 0;
}
inline float OptimizationParameters::weight_decay_factor() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.weight_decay_factor)
  return weight_decay_factor_;
}
inline void OptimizationParameters::set_weight_decay_factor(float value) {
  
  weight_decay_factor_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OptimizationParameters.weight_decay_factor)
}

// bool multiply_weight_decay_factor_by_learning_rate = 22;
inline void OptimizationParameters::clear_multiply_weight_decay_factor_by_learning_rate() {
  multiply_weight_decay_factor_by_learning_rate_ = false;
}
inline bool OptimizationParameters::multiply_weight_decay_factor_by_learning_rate() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.multiply_weight_decay_factor_by_learning_rate)
  return multiply_weight_decay_factor_by_learning_rate_;
}
inline void OptimizationParameters::set_multiply_weight_decay_factor_by_learning_rate(bool value) {
  
  multiply_weight_decay_factor_by_learning_rate_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OptimizationParameters.multiply_weight_decay_factor_by_learning_rate)
}

// .tensorflow.tpu.GradientAccumulationStatus.Status gradient_accumulation_status = 17;
inline void OptimizationParameters::clear_gradient_accumulation_status() {
  gradient_accumulation_status_ = 0;
}
inline ::tensorflow::tpu::GradientAccumulationStatus_Status OptimizationParameters::gradient_accumulation_status() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.gradient_accumulation_status)
  return static_cast< ::tensorflow::tpu::GradientAccumulationStatus_Status >(gradient_accumulation_status_);
}
inline void OptimizationParameters::set_gradient_accumulation_status(::tensorflow::tpu::GradientAccumulationStatus_Status value) {
  
  gradient_accumulation_status_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OptimizationParameters.gradient_accumulation_status)
}

// .tensorflow.tpu.HotIdReplicationConfiguration hot_id_replication_configuration = 18;
inline bool OptimizationParameters::has_hot_id_replication_configuration() const {
  return this != internal_default_instance() && hot_id_replication_configuration_ != nullptr;
}
inline void OptimizationParameters::clear_hot_id_replication_configuration() {
  if (GetArenaNoVirtual() == nullptr && hot_id_replication_configuration_ != nullptr) {
    delete hot_id_replication_configuration_;
  }
  hot_id_replication_configuration_ = nullptr;
}
inline const ::tensorflow::tpu::HotIdReplicationConfiguration& OptimizationParameters::hot_id_replication_configuration() const {
  const ::tensorflow::tpu::HotIdReplicationConfiguration* p = hot_id_replication_configuration_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.hot_id_replication_configuration)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::tpu::HotIdReplicationConfiguration*>(
      &::tensorflow::tpu::_HotIdReplicationConfiguration_default_instance_);
}
inline ::tensorflow::tpu::HotIdReplicationConfiguration* OptimizationParameters::release_hot_id_replication_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.hot_id_replication_configuration)
  
  ::tensorflow::tpu::HotIdReplicationConfiguration* temp = hot_id_replication_configuration_;
  hot_id_replication_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::HotIdReplicationConfiguration* OptimizationParameters::mutable_hot_id_replication_configuration() {
  
  if (hot_id_replication_configuration_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::HotIdReplicationConfiguration>(GetArenaNoVirtual());
    hot_id_replication_configuration_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.hot_id_replication_configuration)
  return hot_id_replication_configuration_;
}
inline void OptimizationParameters::set_allocated_hot_id_replication_configuration(::tensorflow::tpu::HotIdReplicationConfiguration* hot_id_replication_configuration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete hot_id_replication_configuration_;
  }
  if (hot_id_replication_configuration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      hot_id_replication_configuration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hot_id_replication_configuration, submessage_arena);
    }
    
  } else {
    
  }
  hot_id_replication_configuration_ = hot_id_replication_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.hot_id_replication_configuration)
}

// .tensorflow.tpu.AdagradParameters adagrad = 3;
inline bool OptimizationParameters::has_adagrad() const {
  return parameters_case() == kAdagrad;
}
inline void OptimizationParameters::set_has_adagrad() {
  _oneof_case_[0] = kAdagrad;
}
inline void OptimizationParameters::clear_adagrad() {
  if (has_adagrad()) {
    delete parameters_.adagrad_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::AdagradParameters* OptimizationParameters::release_adagrad() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.adagrad)
  if (has_adagrad()) {
    clear_has_parameters();
      ::tensorflow::tpu::AdagradParameters* temp = parameters_.adagrad_;
    parameters_.adagrad_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::AdagradParameters& OptimizationParameters::adagrad() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.adagrad)
  return has_adagrad()
      ? *parameters_.adagrad_
      : *reinterpret_cast< ::tensorflow::tpu::AdagradParameters*>(&::tensorflow::tpu::_AdagradParameters_default_instance_);
}
inline ::tensorflow::tpu::AdagradParameters* OptimizationParameters::mutable_adagrad() {
  if (!has_adagrad()) {
    clear_parameters();
    set_has_adagrad();
    parameters_.adagrad_ = CreateMaybeMessage< ::tensorflow::tpu::AdagradParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.adagrad)
  return parameters_.adagrad_;
}

// .tensorflow.tpu.BoundedAdagradParameters bounded_adagrad = 19;
inline bool OptimizationParameters::has_bounded_adagrad() const {
  return parameters_case() == kBoundedAdagrad;
}
inline void OptimizationParameters::set_has_bounded_adagrad() {
  _oneof_case_[0] = kBoundedAdagrad;
}
inline void OptimizationParameters::clear_bounded_adagrad() {
  if (has_bounded_adagrad()) {
    delete parameters_.bounded_adagrad_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::BoundedAdagradParameters* OptimizationParameters::release_bounded_adagrad() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.bounded_adagrad)
  if (has_bounded_adagrad()) {
    clear_has_parameters();
      ::tensorflow::tpu::BoundedAdagradParameters* temp = parameters_.bounded_adagrad_;
    parameters_.bounded_adagrad_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::BoundedAdagradParameters& OptimizationParameters::bounded_adagrad() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.bounded_adagrad)
  return has_bounded_adagrad()
      ? *parameters_.bounded_adagrad_
      : *reinterpret_cast< ::tensorflow::tpu::BoundedAdagradParameters*>(&::tensorflow::tpu::_BoundedAdagradParameters_default_instance_);
}
inline ::tensorflow::tpu::BoundedAdagradParameters* OptimizationParameters::mutable_bounded_adagrad() {
  if (!has_bounded_adagrad()) {
    clear_parameters();
    set_has_bounded_adagrad();
    parameters_.bounded_adagrad_ = CreateMaybeMessage< ::tensorflow::tpu::BoundedAdagradParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.bounded_adagrad)
  return parameters_.bounded_adagrad_;
}

// .tensorflow.tpu.StochasticGradientDescentParameters stochastic_gradient_descent = 4;
inline bool OptimizationParameters::has_stochastic_gradient_descent() const {
  return parameters_case() == kStochasticGradientDescent;
}
inline void OptimizationParameters::set_has_stochastic_gradient_descent() {
  _oneof_case_[0] = kStochasticGradientDescent;
}
inline void OptimizationParameters::clear_stochastic_gradient_descent() {
  if (has_stochastic_gradient_descent()) {
    delete parameters_.stochastic_gradient_descent_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::StochasticGradientDescentParameters* OptimizationParameters::release_stochastic_gradient_descent() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
  if (has_stochastic_gradient_descent()) {
    clear_has_parameters();
      ::tensorflow::tpu::StochasticGradientDescentParameters* temp = parameters_.stochastic_gradient_descent_;
    parameters_.stochastic_gradient_descent_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::StochasticGradientDescentParameters& OptimizationParameters::stochastic_gradient_descent() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
  return has_stochastic_gradient_descent()
      ? *parameters_.stochastic_gradient_descent_
      : *reinterpret_cast< ::tensorflow::tpu::StochasticGradientDescentParameters*>(&::tensorflow::tpu::_StochasticGradientDescentParameters_default_instance_);
}
inline ::tensorflow::tpu::StochasticGradientDescentParameters* OptimizationParameters::mutable_stochastic_gradient_descent() {
  if (!has_stochastic_gradient_descent()) {
    clear_parameters();
    set_has_stochastic_gradient_descent();
    parameters_.stochastic_gradient_descent_ = CreateMaybeMessage< ::tensorflow::tpu::StochasticGradientDescentParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
  return parameters_.stochastic_gradient_descent_;
}

// .tensorflow.tpu.FtrlParameters ftrl = 5;
inline bool OptimizationParameters::has_ftrl() const {
  return parameters_case() == kFtrl;
}
inline void OptimizationParameters::set_has_ftrl() {
  _oneof_case_[0] = kFtrl;
}
inline void OptimizationParameters::clear_ftrl() {
  if (has_ftrl()) {
    delete parameters_.ftrl_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::FtrlParameters* OptimizationParameters::release_ftrl() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.ftrl)
  if (has_ftrl()) {
    clear_has_parameters();
      ::tensorflow::tpu::FtrlParameters* temp = parameters_.ftrl_;
    parameters_.ftrl_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::FtrlParameters& OptimizationParameters::ftrl() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.ftrl)
  return has_ftrl()
      ? *parameters_.ftrl_
      : *reinterpret_cast< ::tensorflow::tpu::FtrlParameters*>(&::tensorflow::tpu::_FtrlParameters_default_instance_);
}
inline ::tensorflow::tpu::FtrlParameters* OptimizationParameters::mutable_ftrl() {
  if (!has_ftrl()) {
    clear_parameters();
    set_has_ftrl();
    parameters_.ftrl_ = CreateMaybeMessage< ::tensorflow::tpu::FtrlParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.ftrl)
  return parameters_.ftrl_;
}

// .tensorflow.tpu.AdamParameters adam = 6;
inline bool OptimizationParameters::has_adam() const {
  return parameters_case() == kAdam;
}
inline void OptimizationParameters::set_has_adam() {
  _oneof_case_[0] = kAdam;
}
inline void OptimizationParameters::clear_adam() {
  if (has_adam()) {
    delete parameters_.adam_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::AdamParameters* OptimizationParameters::release_adam() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.adam)
  if (has_adam()) {
    clear_has_parameters();
      ::tensorflow::tpu::AdamParameters* temp = parameters_.adam_;
    parameters_.adam_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::AdamParameters& OptimizationParameters::adam() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.adam)
  return has_adam()
      ? *parameters_.adam_
      : *reinterpret_cast< ::tensorflow::tpu::AdamParameters*>(&::tensorflow::tpu::_AdamParameters_default_instance_);
}
inline ::tensorflow::tpu::AdamParameters* OptimizationParameters::mutable_adam() {
  if (!has_adam()) {
    clear_parameters();
    set_has_adam();
    parameters_.adam_ = CreateMaybeMessage< ::tensorflow::tpu::AdamParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.adam)
  return parameters_.adam_;
}

// .tensorflow.tpu.MomentumParameters momentum = 8;
inline bool OptimizationParameters::has_momentum() const {
  return parameters_case() == kMomentum;
}
inline void OptimizationParameters::set_has_momentum() {
  _oneof_case_[0] = kMomentum;
}
inline void OptimizationParameters::clear_momentum() {
  if (has_momentum()) {
    delete parameters_.momentum_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::MomentumParameters* OptimizationParameters::release_momentum() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.momentum)
  if (has_momentum()) {
    clear_has_parameters();
      ::tensorflow::tpu::MomentumParameters* temp = parameters_.momentum_;
    parameters_.momentum_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::MomentumParameters& OptimizationParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.momentum)
  return has_momentum()
      ? *parameters_.momentum_
      : *reinterpret_cast< ::tensorflow::tpu::MomentumParameters*>(&::tensorflow::tpu::_MomentumParameters_default_instance_);
}
inline ::tensorflow::tpu::MomentumParameters* OptimizationParameters::mutable_momentum() {
  if (!has_momentum()) {
    clear_parameters();
    set_has_momentum();
    parameters_.momentum_ = CreateMaybeMessage< ::tensorflow::tpu::MomentumParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.momentum)
  return parameters_.momentum_;
}

// .tensorflow.tpu.RmsPropParameters rms_prop = 9;
inline bool OptimizationParameters::has_rms_prop() const {
  return parameters_case() == kRmsProp;
}
inline void OptimizationParameters::set_has_rms_prop() {
  _oneof_case_[0] = kRmsProp;
}
inline void OptimizationParameters::clear_rms_prop() {
  if (has_rms_prop()) {
    delete parameters_.rms_prop_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::RmsPropParameters* OptimizationParameters::release_rms_prop() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.rms_prop)
  if (has_rms_prop()) {
    clear_has_parameters();
      ::tensorflow::tpu::RmsPropParameters* temp = parameters_.rms_prop_;
    parameters_.rms_prop_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::RmsPropParameters& OptimizationParameters::rms_prop() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.rms_prop)
  return has_rms_prop()
      ? *parameters_.rms_prop_
      : *reinterpret_cast< ::tensorflow::tpu::RmsPropParameters*>(&::tensorflow::tpu::_RmsPropParameters_default_instance_);
}
inline ::tensorflow::tpu::RmsPropParameters* OptimizationParameters::mutable_rms_prop() {
  if (!has_rms_prop()) {
    clear_parameters();
    set_has_rms_prop();
    parameters_.rms_prop_ = CreateMaybeMessage< ::tensorflow::tpu::RmsPropParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.rms_prop)
  return parameters_.rms_prop_;
}

// .tensorflow.tpu.CenteredRmsPropParameters centered_rms_prop = 10;
inline bool OptimizationParameters::has_centered_rms_prop() const {
  return parameters_case() == kCenteredRmsProp;
}
inline void OptimizationParameters::set_has_centered_rms_prop() {
  _oneof_case_[0] = kCenteredRmsProp;
}
inline void OptimizationParameters::clear_centered_rms_prop() {
  if (has_centered_rms_prop()) {
    delete parameters_.centered_rms_prop_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::CenteredRmsPropParameters* OptimizationParameters::release_centered_rms_prop() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
  if (has_centered_rms_prop()) {
    clear_has_parameters();
      ::tensorflow::tpu::CenteredRmsPropParameters* temp = parameters_.centered_rms_prop_;
    parameters_.centered_rms_prop_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::CenteredRmsPropParameters& OptimizationParameters::centered_rms_prop() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
  return has_centered_rms_prop()
      ? *parameters_.centered_rms_prop_
      : *reinterpret_cast< ::tensorflow::tpu::CenteredRmsPropParameters*>(&::tensorflow::tpu::_CenteredRmsPropParameters_default_instance_);
}
inline ::tensorflow::tpu::CenteredRmsPropParameters* OptimizationParameters::mutable_centered_rms_prop() {
  if (!has_centered_rms_prop()) {
    clear_parameters();
    set_has_centered_rms_prop();
    parameters_.centered_rms_prop_ = CreateMaybeMessage< ::tensorflow::tpu::CenteredRmsPropParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
  return parameters_.centered_rms_prop_;
}

// .tensorflow.tpu.MdlAdagradLightParameters mdl_adagrad_light = 11;
inline bool OptimizationParameters::has_mdl_adagrad_light() const {
  return parameters_case() == kMdlAdagradLight;
}
inline void OptimizationParameters::set_has_mdl_adagrad_light() {
  _oneof_case_[0] = kMdlAdagradLight;
}
inline void OptimizationParameters::clear_mdl_adagrad_light() {
  if (has_mdl_adagrad_light()) {
    delete parameters_.mdl_adagrad_light_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::MdlAdagradLightParameters* OptimizationParameters::release_mdl_adagrad_light() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
  if (has_mdl_adagrad_light()) {
    clear_has_parameters();
      ::tensorflow::tpu::MdlAdagradLightParameters* temp = parameters_.mdl_adagrad_light_;
    parameters_.mdl_adagrad_light_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::MdlAdagradLightParameters& OptimizationParameters::mdl_adagrad_light() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
  return has_mdl_adagrad_light()
      ? *parameters_.mdl_adagrad_light_
      : *reinterpret_cast< ::tensorflow::tpu::MdlAdagradLightParameters*>(&::tensorflow::tpu::_MdlAdagradLightParameters_default_instance_);
}
inline ::tensorflow::tpu::MdlAdagradLightParameters* OptimizationParameters::mutable_mdl_adagrad_light() {
  if (!has_mdl_adagrad_light()) {
    clear_parameters();
    set_has_mdl_adagrad_light();
    parameters_.mdl_adagrad_light_ = CreateMaybeMessage< ::tensorflow::tpu::MdlAdagradLightParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
  return parameters_.mdl_adagrad_light_;
}

// .tensorflow.tpu.AdadeltaParameters adadelta = 12;
inline bool OptimizationParameters::has_adadelta() const {
  return parameters_case() == kAdadelta;
}
inline void OptimizationParameters::set_has_adadelta() {
  _oneof_case_[0] = kAdadelta;
}
inline void OptimizationParameters::clear_adadelta() {
  if (has_adadelta()) {
    delete parameters_.adadelta_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::AdadeltaParameters* OptimizationParameters::release_adadelta() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.adadelta)
  if (has_adadelta()) {
    clear_has_parameters();
      ::tensorflow::tpu::AdadeltaParameters* temp = parameters_.adadelta_;
    parameters_.adadelta_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::AdadeltaParameters& OptimizationParameters::adadelta() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.adadelta)
  return has_adadelta()
      ? *parameters_.adadelta_
      : *reinterpret_cast< ::tensorflow::tpu::AdadeltaParameters*>(&::tensorflow::tpu::_AdadeltaParameters_default_instance_);
}
inline ::tensorflow::tpu::AdadeltaParameters* OptimizationParameters::mutable_adadelta() {
  if (!has_adadelta()) {
    clear_parameters();
    set_has_adadelta();
    parameters_.adadelta_ = CreateMaybeMessage< ::tensorflow::tpu::AdadeltaParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.adadelta)
  return parameters_.adadelta_;
}

// .tensorflow.tpu.ProximalAdagradParameters proximal_adagrad = 14;
inline bool OptimizationParameters::has_proximal_adagrad() const {
  return parameters_case() == kProximalAdagrad;
}
inline void OptimizationParameters::set_has_proximal_adagrad() {
  _oneof_case_[0] = kProximalAdagrad;
}
inline void OptimizationParameters::clear_proximal_adagrad() {
  if (has_proximal_adagrad()) {
    delete parameters_.proximal_adagrad_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::ProximalAdagradParameters* OptimizationParameters::release_proximal_adagrad() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
  if (has_proximal_adagrad()) {
    clear_has_parameters();
      ::tensorflow::tpu::ProximalAdagradParameters* temp = parameters_.proximal_adagrad_;
    parameters_.proximal_adagrad_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::ProximalAdagradParameters& OptimizationParameters::proximal_adagrad() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
  return has_proximal_adagrad()
      ? *parameters_.proximal_adagrad_
      : *reinterpret_cast< ::tensorflow::tpu::ProximalAdagradParameters*>(&::tensorflow::tpu::_ProximalAdagradParameters_default_instance_);
}
inline ::tensorflow::tpu::ProximalAdagradParameters* OptimizationParameters::mutable_proximal_adagrad() {
  if (!has_proximal_adagrad()) {
    clear_parameters();
    set_has_proximal_adagrad();
    parameters_.proximal_adagrad_ = CreateMaybeMessage< ::tensorflow::tpu::ProximalAdagradParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
  return parameters_.proximal_adagrad_;
}

// .tensorflow.tpu.OnlineYogiParameters online_yogi = 20;
inline bool OptimizationParameters::has_online_yogi() const {
  return parameters_case() == kOnlineYogi;
}
inline void OptimizationParameters::set_has_online_yogi() {
  _oneof_case_[0] = kOnlineYogi;
}
inline void OptimizationParameters::clear_online_yogi() {
  if (has_online_yogi()) {
    delete parameters_.online_yogi_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::OnlineYogiParameters* OptimizationParameters::release_online_yogi() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.online_yogi)
  if (has_online_yogi()) {
    clear_has_parameters();
      ::tensorflow::tpu::OnlineYogiParameters* temp = parameters_.online_yogi_;
    parameters_.online_yogi_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::OnlineYogiParameters& OptimizationParameters::online_yogi() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.online_yogi)
  return has_online_yogi()
      ? *parameters_.online_yogi_
      : *reinterpret_cast< ::tensorflow::tpu::OnlineYogiParameters*>(&::tensorflow::tpu::_OnlineYogiParameters_default_instance_);
}
inline ::tensorflow::tpu::OnlineYogiParameters* OptimizationParameters::mutable_online_yogi() {
  if (!has_online_yogi()) {
    clear_parameters();
    set_has_online_yogi();
    parameters_.online_yogi_ = CreateMaybeMessage< ::tensorflow::tpu::OnlineYogiParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.online_yogi)
  return parameters_.online_yogi_;
}

// .tensorflow.tpu.ProximalYogiParameters proximal_yogi = 21;
inline bool OptimizationParameters::has_proximal_yogi() const {
  return parameters_case() == kProximalYogi;
}
inline void OptimizationParameters::set_has_proximal_yogi() {
  _oneof_case_[0] = kProximalYogi;
}
inline void OptimizationParameters::clear_proximal_yogi() {
  if (has_proximal_yogi()) {
    delete parameters_.proximal_yogi_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::ProximalYogiParameters* OptimizationParameters::release_proximal_yogi() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.proximal_yogi)
  if (has_proximal_yogi()) {
    clear_has_parameters();
      ::tensorflow::tpu::ProximalYogiParameters* temp = parameters_.proximal_yogi_;
    parameters_.proximal_yogi_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::ProximalYogiParameters& OptimizationParameters::proximal_yogi() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.proximal_yogi)
  return has_proximal_yogi()
      ? *parameters_.proximal_yogi_
      : *reinterpret_cast< ::tensorflow::tpu::ProximalYogiParameters*>(&::tensorflow::tpu::_ProximalYogiParameters_default_instance_);
}
inline ::tensorflow::tpu::ProximalYogiParameters* OptimizationParameters::mutable_proximal_yogi() {
  if (!has_proximal_yogi()) {
    clear_parameters();
    set_has_proximal_yogi();
    parameters_.proximal_yogi_ = CreateMaybeMessage< ::tensorflow::tpu::ProximalYogiParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.proximal_yogi)
  return parameters_.proximal_yogi_;
}

// .tensorflow.tpu.FrequencyEstimatorParameters frequency_estimator = 23;
inline bool OptimizationParameters::has_frequency_estimator() const {
  return parameters_case() == kFrequencyEstimator;
}
inline void OptimizationParameters::set_has_frequency_estimator() {
  _oneof_case_[0] = kFrequencyEstimator;
}
inline void OptimizationParameters::clear_frequency_estimator() {
  if (has_frequency_estimator()) {
    delete parameters_.frequency_estimator_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::FrequencyEstimatorParameters* OptimizationParameters::release_frequency_estimator() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.frequency_estimator)
  if (has_frequency_estimator()) {
    clear_has_parameters();
      ::tensorflow::tpu::FrequencyEstimatorParameters* temp = parameters_.frequency_estimator_;
    parameters_.frequency_estimator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::FrequencyEstimatorParameters& OptimizationParameters::frequency_estimator() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.frequency_estimator)
  return has_frequency_estimator()
      ? *parameters_.frequency_estimator_
      : *reinterpret_cast< ::tensorflow::tpu::FrequencyEstimatorParameters*>(&::tensorflow::tpu::_FrequencyEstimatorParameters_default_instance_);
}
inline ::tensorflow::tpu::FrequencyEstimatorParameters* OptimizationParameters::mutable_frequency_estimator() {
  if (!has_frequency_estimator()) {
    clear_parameters();
    set_has_frequency_estimator();
    parameters_.frequency_estimator_ = CreateMaybeMessage< ::tensorflow::tpu::FrequencyEstimatorParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.frequency_estimator)
  return parameters_.frequency_estimator_;
}

// .tensorflow.tpu.UserDefinedProgramParameters user_defined_program = 24;
inline bool OptimizationParameters::has_user_defined_program() const {
  return parameters_case() == kUserDefinedProgram;
}
inline void OptimizationParameters::set_has_user_defined_program() {
  _oneof_case_[0] = kUserDefinedProgram;
}
inline void OptimizationParameters::clear_user_defined_program() {
  if (has_user_defined_program()) {
    delete parameters_.user_defined_program_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::UserDefinedProgramParameters* OptimizationParameters::release_user_defined_program() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.user_defined_program)
  if (has_user_defined_program()) {
    clear_has_parameters();
      ::tensorflow::tpu::UserDefinedProgramParameters* temp = parameters_.user_defined_program_;
    parameters_.user_defined_program_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::UserDefinedProgramParameters& OptimizationParameters::user_defined_program() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.user_defined_program)
  return has_user_defined_program()
      ? *parameters_.user_defined_program_
      : *reinterpret_cast< ::tensorflow::tpu::UserDefinedProgramParameters*>(&::tensorflow::tpu::_UserDefinedProgramParameters_default_instance_);
}
inline ::tensorflow::tpu::UserDefinedProgramParameters* OptimizationParameters::mutable_user_defined_program() {
  if (!has_user_defined_program()) {
    clear_parameters();
    set_has_user_defined_program();
    parameters_.user_defined_program_ = CreateMaybeMessage< ::tensorflow::tpu::UserDefinedProgramParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.user_defined_program)
  return parameters_.user_defined_program_;
}

// .tensorflow.tpu.AssignParameters assign = 25;
inline bool OptimizationParameters::has_assign() const {
  return parameters_case() == kAssign;
}
inline void OptimizationParameters::set_has_assign() {
  _oneof_case_[0] = kAssign;
}
inline void OptimizationParameters::clear_assign() {
  if (has_assign()) {
    delete parameters_.assign_;
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::AssignParameters* OptimizationParameters::release_assign() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.assign)
  if (has_assign()) {
    clear_has_parameters();
      ::tensorflow::tpu::AssignParameters* temp = parameters_.assign_;
    parameters_.assign_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::AssignParameters& OptimizationParameters::assign() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.assign)
  return has_assign()
      ? *parameters_.assign_
      : *reinterpret_cast< ::tensorflow::tpu::AssignParameters*>(&::tensorflow::tpu::_AssignParameters_default_instance_);
}
inline ::tensorflow::tpu::AssignParameters* OptimizationParameters::mutable_assign() {
  if (!has_assign()) {
    clear_parameters();
    set_has_assign();
    parameters_.assign_ = CreateMaybeMessage< ::tensorflow::tpu::AssignParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.assign)
  return parameters_.assign_;
}

inline bool OptimizationParameters::has_parameters() const {
  return parameters_case() != PARAMETERS_NOT_SET;
}
inline void OptimizationParameters::clear_has_parameters() {
  _oneof_case_[0] = PARAMETERS_NOT_SET;
}
inline OptimizationParameters::ParametersCase OptimizationParameters::parameters_case() const {
  return OptimizationParameters::ParametersCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// StateVariableSpecification_UserDefined

// double padding_initial_value = 1;
inline void StateVariableSpecification_UserDefined::clear_padding_initial_value() {
  padding_initial_value_ = 0;
}
inline double StateVariableSpecification_UserDefined::padding_initial_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.UserDefined.padding_initial_value)
  return padding_initial_value_;
}
inline void StateVariableSpecification_UserDefined::set_padding_initial_value(double value) {
  
  padding_initial_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.StateVariableSpecification.UserDefined.padding_initial_value)
}

// -------------------------------------------------------------------

// StateVariableSpecification_FillWithConstant

// double initial_value = 1;
inline void StateVariableSpecification_FillWithConstant::clear_initial_value() {
  initial_value_ = 0;
}
inline double StateVariableSpecification_FillWithConstant::initial_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.FillWithConstant.initial_value)
  return initial_value_;
}
inline void StateVariableSpecification_FillWithConstant::set_initial_value(double value) {
  
  initial_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.StateVariableSpecification.FillWithConstant.initial_value)
}

// -------------------------------------------------------------------

// StateVariableSpecification

// string name = 1;
inline void StateVariableSpecification::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& StateVariableSpecification::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.name)
  return name_.GetNoArena();
}
inline void StateVariableSpecification::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.StateVariableSpecification.name)
}
inline void StateVariableSpecification::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tpu.StateVariableSpecification.name)
}
inline void StateVariableSpecification::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tpu.StateVariableSpecification.name)
}
inline void StateVariableSpecification::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tpu.StateVariableSpecification.name)
}
inline std::string* StateVariableSpecification::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.StateVariableSpecification.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* StateVariableSpecification::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.StateVariableSpecification.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void StateVariableSpecification::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.StateVariableSpecification.name)
}

// .tensorflow.tpu.StateVariableSpecification.UserDefined user_defined = 2;
inline bool StateVariableSpecification::has_user_defined() const {
  return usage_case() == kUserDefined;
}
inline void StateVariableSpecification::set_has_user_defined() {
  _oneof_case_[0] = kUserDefined;
}
inline void StateVariableSpecification::clear_user_defined() {
  if (has_user_defined()) {
    delete usage_.user_defined_;
    clear_has_usage();
  }
}
inline ::tensorflow::tpu::StateVariableSpecification_UserDefined* StateVariableSpecification::release_user_defined() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.StateVariableSpecification.user_defined)
  if (has_user_defined()) {
    clear_has_usage();
      ::tensorflow::tpu::StateVariableSpecification_UserDefined* temp = usage_.user_defined_;
    usage_.user_defined_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::StateVariableSpecification_UserDefined& StateVariableSpecification::user_defined() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.user_defined)
  return has_user_defined()
      ? *usage_.user_defined_
      : *reinterpret_cast< ::tensorflow::tpu::StateVariableSpecification_UserDefined*>(&::tensorflow::tpu::_StateVariableSpecification_UserDefined_default_instance_);
}
inline ::tensorflow::tpu::StateVariableSpecification_UserDefined* StateVariableSpecification::mutable_user_defined() {
  if (!has_user_defined()) {
    clear_usage();
    set_has_user_defined();
    usage_.user_defined_ = CreateMaybeMessage< ::tensorflow::tpu::StateVariableSpecification_UserDefined >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.StateVariableSpecification.user_defined)
  return usage_.user_defined_;
}

// .tensorflow.tpu.StateVariableSpecification.FillWithConstant fill_with_constant = 3;
inline bool StateVariableSpecification::has_fill_with_constant() const {
  return usage_case() == kFillWithConstant;
}
inline void StateVariableSpecification::set_has_fill_with_constant() {
  _oneof_case_[0] = kFillWithConstant;
}
inline void StateVariableSpecification::clear_fill_with_constant() {
  if (has_fill_with_constant()) {
    delete usage_.fill_with_constant_;
    clear_has_usage();
  }
}
inline ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* StateVariableSpecification::release_fill_with_constant() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
  if (has_fill_with_constant()) {
    clear_has_usage();
      ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* temp = usage_.fill_with_constant_;
    usage_.fill_with_constant_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant& StateVariableSpecification::fill_with_constant() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
  return has_fill_with_constant()
      ? *usage_.fill_with_constant_
      : *reinterpret_cast< ::tensorflow::tpu::StateVariableSpecification_FillWithConstant*>(&::tensorflow::tpu::_StateVariableSpecification_FillWithConstant_default_instance_);
}
inline ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* StateVariableSpecification::mutable_fill_with_constant() {
  if (!has_fill_with_constant()) {
    clear_usage();
    set_has_fill_with_constant();
    usage_.fill_with_constant_ = CreateMaybeMessage< ::tensorflow::tpu::StateVariableSpecification_FillWithConstant >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
  return usage_.fill_with_constant_;
}

inline bool StateVariableSpecification::has_usage() const {
  return usage_case() != USAGE_NOT_SET;
}
inline void StateVariableSpecification::clear_has_usage() {
  _oneof_case_[0] = USAGE_NOT_SET;
}
inline StateVariableSpecification::UsageCase StateVariableSpecification::usage_case() const {
  return StateVariableSpecification::UsageCase(_oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::tpu::GradientAccumulationStatus_Status> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::GradientAccumulationStatus_Status>() {
  return ::tensorflow::tpu::GradientAccumulationStatus_Status_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::tpu::HotIdReplicationConfiguration_Status> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::HotIdReplicationConfiguration_Status>() {
  return ::tensorflow::tpu::HotIdReplicationConfiguration_Status_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
