# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-12-29 16:31+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Uzbek (http://www.transifex.com/django/django/language/uz/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: uz\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Personal info"
msgstr "Shaxsiy ma'lumotlar"

msgid "Permissions"
msgstr "Ruhsatnomalar"

msgid "Important dates"
msgstr "Muhim sanalar"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "%(key)r asosiy ka<PERSON> %(name)s obyekt mavjud emas"

msgid "Password changed successfully."
msgstr "Parol muvaffaqiyatli o'zgartirildi."

#, python-format
msgid "Change password: %s"
msgstr "Parolni o'zgartirish: %s"

msgid "Authentication and Authorization"
msgstr "Autentifikatsiya va Avtorizatsiya"

msgid "password"
msgstr "parol"

msgid "last login"
msgstr "oxirgi kirish"

msgid "No password set."
msgstr "Hech qanday parol qo'yilmagan."

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Noto'g'ri parol formati yoki noma'lum heshlash algoritmi ishlatilgan."

msgid "The two password fields didn’t match."
msgstr "Ikkala parol maydoni bir-biriga mos kelmadi."

msgid "Password"
msgstr "Parol"

msgid "Password confirmation"
msgstr "Parolni tasdiqlash"

msgid "Enter the same password as before, for verification."
msgstr "Tekshirish uchun avvalgi parolni kiriting.."

msgid ""
"Raw passwords are not stored, so there is no way to see this user’s "
"password, but you can change the password using <a href=\"{}\">this form</a>."
msgstr ""
"Xom parollar saqlanmaydi, shuning uchun bu foydanaluvchining parolini "
"ko'rishni iloji yo'q, lekin siz <a href=\"{}\"> shu formadan </a> foydalanib "
"parolni o'zgartirishingiz mumkin. "

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Iltimos, to'g'ri %(username)s va parolni kiriting. Ahamiyat bering, ikkala "
"maydonlar ham katta-kichik harfga sezgir bo'lishi mumkin."

msgid "This account is inactive."
msgstr "Bu akkaunt nofaol"

msgid "Email"
msgstr "Elektron pochta"

msgid "New password"
msgstr "Yangi parol"

msgid "New password confirmation"
msgstr "Yangi parolni tasdiqlash"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""
"Sizning eski parolingiz noto'g'ri kiritilgan edi. Iltimos, qaytadan kiriting."

msgid "Old password"
msgstr ""

msgid "Password (again)"
msgstr ""

msgid "algorithm"
msgstr ""

msgid "iterations"
msgstr ""

msgid "salt"
msgstr ""

msgid "hash"
msgstr ""

msgid "variety"
msgstr ""

msgid "version"
msgstr ""

msgid "memory cost"
msgstr ""

msgid "time cost"
msgstr ""

msgid "parallelism"
msgstr ""

msgid "work factor"
msgstr ""

msgid "checksum"
msgstr ""

msgid "name"
msgstr ""

msgid "content type"
msgstr ""

msgid "codename"
msgstr ""

msgid "permission"
msgstr ""

msgid "permissions"
msgstr ""

msgid "group"
msgstr ""

msgid "groups"
msgstr ""

msgid "superuser status"
msgstr ""

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""

msgid "user permissions"
msgstr ""

msgid "Specific permissions for this user."
msgstr ""

msgid "username"
msgstr ""

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""

msgid "A user with that username already exists."
msgstr ""

msgid "first name"
msgstr ""

msgid "last name"
msgstr ""

msgid "email address"
msgstr ""

msgid "staff status"
msgstr ""

msgid "Designates whether the user can log into this admin site."
msgstr ""

msgid "active"
msgstr ""

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""

msgid "date joined"
msgstr ""

msgid "user"
msgstr ""

msgid "users"
msgstr ""

#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] ""

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] ""

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr ""

msgid "Your password can’t be too similar to your other personal information."
msgstr ""

msgid "This password is too common."
msgstr ""

msgid "Your password can’t be a commonly used password."
msgstr ""

msgid "This password is entirely numeric."
msgstr ""

msgid "Your password can’t be entirely numeric."
msgstr ""

#, python-format
msgid "Password reset on %(site_name)s"
msgstr ""

msgid ""
"Enter a valid username. This value may contain only English letters, "
"numbers, and @/./+/-/_ characters."
msgstr ""

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""

msgid "Logged out"
msgstr ""

msgid "Password reset"
msgstr ""

msgid "Password reset sent"
msgstr ""

msgid "Enter new password"
msgstr ""

msgid "Password reset unsuccessful"
msgstr ""

msgid "Password reset complete"
msgstr ""

msgid "Password change"
msgstr ""

msgid "Password change successful"
msgstr ""
