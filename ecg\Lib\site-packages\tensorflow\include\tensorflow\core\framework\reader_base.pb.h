// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/reader_base.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2freader_5fbase_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2freader_5fbase_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2freader_5fbase_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2freader_5fbase_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2freader_5fbase_2eproto;
namespace tensorflow {
class ReaderBaseState;
class ReaderBaseStateDefaultTypeInternal;
extern ReaderBaseStateDefaultTypeInternal _ReaderBaseState_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ReaderBaseState* Arena::CreateMaybeMessage<::tensorflow::ReaderBaseState>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class ReaderBaseState :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ReaderBaseState) */ {
 public:
  ReaderBaseState();
  virtual ~ReaderBaseState();

  ReaderBaseState(const ReaderBaseState& from);
  ReaderBaseState(ReaderBaseState&& from) noexcept
    : ReaderBaseState() {
    *this = ::std::move(from);
  }

  inline ReaderBaseState& operator=(const ReaderBaseState& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReaderBaseState& operator=(ReaderBaseState&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ReaderBaseState& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ReaderBaseState* internal_default_instance() {
    return reinterpret_cast<const ReaderBaseState*>(
               &_ReaderBaseState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ReaderBaseState& a, ReaderBaseState& b) {
    a.Swap(&b);
  }
  inline void Swap(ReaderBaseState* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReaderBaseState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ReaderBaseState* New() const final {
    return CreateMaybeMessage<ReaderBaseState>(nullptr);
  }

  ReaderBaseState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ReaderBaseState>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ReaderBaseState& from);
  void MergeFrom(const ReaderBaseState& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReaderBaseState* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReaderBaseState";
  }
  protected:
  explicit ReaderBaseState(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2freader_5fbase_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2freader_5fbase_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCurrentWorkFieldNumber = 4,
    kWorkStartedFieldNumber = 1,
    kWorkFinishedFieldNumber = 2,
    kNumRecordsProducedFieldNumber = 3,
  };
  // bytes current_work = 4;
  void clear_current_work();
  const std::string& current_work() const;
  void set_current_work(const std::string& value);
  void set_current_work(std::string&& value);
  void set_current_work(const char* value);
  void set_current_work(const void* value, size_t size);
  std::string* mutable_current_work();
  std::string* release_current_work();
  void set_allocated_current_work(std::string* current_work);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_current_work();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_current_work(
      std::string* current_work);

  // int64 work_started = 1;
  void clear_work_started();
  ::PROTOBUF_NAMESPACE_ID::int64 work_started() const;
  void set_work_started(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 work_finished = 2;
  void clear_work_finished();
  ::PROTOBUF_NAMESPACE_ID::int64 work_finished() const;
  void set_work_finished(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 num_records_produced = 3;
  void clear_num_records_produced();
  ::PROTOBUF_NAMESPACE_ID::int64 num_records_produced() const;
  void set_num_records_produced(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ReaderBaseState)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr current_work_;
  ::PROTOBUF_NAMESPACE_ID::int64 work_started_;
  ::PROTOBUF_NAMESPACE_ID::int64 work_finished_;
  ::PROTOBUF_NAMESPACE_ID::int64 num_records_produced_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2freader_5fbase_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ReaderBaseState

// int64 work_started = 1;
inline void ReaderBaseState::clear_work_started() {
  work_started_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ReaderBaseState::work_started() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReaderBaseState.work_started)
  return work_started_;
}
inline void ReaderBaseState::set_work_started(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  work_started_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ReaderBaseState.work_started)
}

// int64 work_finished = 2;
inline void ReaderBaseState::clear_work_finished() {
  work_finished_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ReaderBaseState::work_finished() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReaderBaseState.work_finished)
  return work_finished_;
}
inline void ReaderBaseState::set_work_finished(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  work_finished_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ReaderBaseState.work_finished)
}

// int64 num_records_produced = 3;
inline void ReaderBaseState::clear_num_records_produced() {
  num_records_produced_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ReaderBaseState::num_records_produced() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReaderBaseState.num_records_produced)
  return num_records_produced_;
}
inline void ReaderBaseState::set_num_records_produced(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  num_records_produced_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ReaderBaseState.num_records_produced)
}

// bytes current_work = 4;
inline void ReaderBaseState::clear_current_work() {
  current_work_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ReaderBaseState::current_work() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReaderBaseState.current_work)
  return current_work_.Get();
}
inline void ReaderBaseState::set_current_work(const std::string& value) {
  
  current_work_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ReaderBaseState.current_work)
}
inline void ReaderBaseState::set_current_work(std::string&& value) {
  
  current_work_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ReaderBaseState.current_work)
}
inline void ReaderBaseState::set_current_work(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  current_work_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ReaderBaseState.current_work)
}
inline void ReaderBaseState::set_current_work(const void* value,
    size_t size) {
  
  current_work_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ReaderBaseState.current_work)
}
inline std::string* ReaderBaseState::mutable_current_work() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ReaderBaseState.current_work)
  return current_work_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ReaderBaseState::release_current_work() {
  // @@protoc_insertion_point(field_release:tensorflow.ReaderBaseState.current_work)
  
  return current_work_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ReaderBaseState::set_allocated_current_work(std::string* current_work) {
  if (current_work != nullptr) {
    
  } else {
    
  }
  current_work_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), current_work,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ReaderBaseState.current_work)
}
inline std::string* ReaderBaseState::unsafe_arena_release_current_work() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReaderBaseState.current_work)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return current_work_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ReaderBaseState::unsafe_arena_set_allocated_current_work(
    std::string* current_work) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (current_work != nullptr) {
    
  } else {
    
  }
  current_work_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      current_work, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReaderBaseState.current_work)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2freader_5fbase_2eproto
