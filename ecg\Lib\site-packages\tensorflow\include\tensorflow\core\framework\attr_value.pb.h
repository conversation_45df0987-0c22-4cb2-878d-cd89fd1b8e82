// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/attr_value.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto;
namespace tensorflow {
class AttrValue;
class AttrValueDefaultTypeInternal;
extern AttrValueDefaultTypeInternal _AttrValue_default_instance_;
class AttrValue_ListValue;
class AttrValue_ListValueDefaultTypeInternal;
extern AttrValue_ListValueDefaultTypeInternal _AttrValue_ListValue_default_instance_;
class NameAttrList;
class NameAttrListDefaultTypeInternal;
extern NameAttrListDefaultTypeInternal _NameAttrList_default_instance_;
class NameAttrList_AttrEntry_DoNotUse;
class NameAttrList_AttrEntry_DoNotUseDefaultTypeInternal;
extern NameAttrList_AttrEntry_DoNotUseDefaultTypeInternal _NameAttrList_AttrEntry_DoNotUse_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::AttrValue* Arena::CreateMaybeMessage<::tensorflow::AttrValue>(Arena*);
template<> ::tensorflow::AttrValue_ListValue* Arena::CreateMaybeMessage<::tensorflow::AttrValue_ListValue>(Arena*);
template<> ::tensorflow::NameAttrList* Arena::CreateMaybeMessage<::tensorflow::NameAttrList>(Arena*);
template<> ::tensorflow::NameAttrList_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::NameAttrList_AttrEntry_DoNotUse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class AttrValue_ListValue :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AttrValue.ListValue) */ {
 public:
  AttrValue_ListValue();
  virtual ~AttrValue_ListValue();

  AttrValue_ListValue(const AttrValue_ListValue& from);
  AttrValue_ListValue(AttrValue_ListValue&& from) noexcept
    : AttrValue_ListValue() {
    *this = ::std::move(from);
  }

  inline AttrValue_ListValue& operator=(const AttrValue_ListValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline AttrValue_ListValue& operator=(AttrValue_ListValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AttrValue_ListValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AttrValue_ListValue* internal_default_instance() {
    return reinterpret_cast<const AttrValue_ListValue*>(
               &_AttrValue_ListValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AttrValue_ListValue& a, AttrValue_ListValue& b) {
    a.Swap(&b);
  }
  inline void Swap(AttrValue_ListValue* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AttrValue_ListValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AttrValue_ListValue* New() const final {
    return CreateMaybeMessage<AttrValue_ListValue>(nullptr);
  }

  AttrValue_ListValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AttrValue_ListValue>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AttrValue_ListValue& from);
  void MergeFrom(const AttrValue_ListValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AttrValue_ListValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AttrValue.ListValue";
  }
  protected:
  explicit AttrValue_ListValue(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSFieldNumber = 2,
    kIFieldNumber = 3,
    kFFieldNumber = 4,
    kBFieldNumber = 5,
    kTypeFieldNumber = 6,
    kShapeFieldNumber = 7,
    kTensorFieldNumber = 8,
    kFuncFieldNumber = 9,
  };
  // repeated bytes s = 2;
  int s_size() const;
  void clear_s();
  const std::string& s(int index) const;
  std::string* mutable_s(int index);
  void set_s(int index, const std::string& value);
  void set_s(int index, std::string&& value);
  void set_s(int index, const char* value);
  void set_s(int index, const void* value, size_t size);
  std::string* add_s();
  void add_s(const std::string& value);
  void add_s(std::string&& value);
  void add_s(const char* value);
  void add_s(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_s();

  // repeated int64 i = 3 [packed = true];
  int i_size() const;
  void clear_i();
  ::PROTOBUF_NAMESPACE_ID::int64 i(int index) const;
  void set_i(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_i(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      i() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_i();

  // repeated float f = 4 [packed = true];
  int f_size() const;
  void clear_f();
  float f(int index) const;
  void set_f(int index, float value);
  void add_f(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      f() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_f();

  // repeated bool b = 5 [packed = true];
  int b_size() const;
  void clear_b();
  bool b(int index) const;
  void set_b(int index, bool value);
  void add_b(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      b() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_b();

  // repeated .tensorflow.DataType type = 6 [packed = true];
  int type_size() const;
  void clear_type();
  ::tensorflow::DataType type(int index) const;
  void set_type(int index, ::tensorflow::DataType value);
  void add_type(::tensorflow::DataType value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& type() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_type();

  // repeated .tensorflow.TensorShapeProto shape = 7;
  int shape_size() const;
  void clear_shape();
  ::tensorflow::TensorShapeProto* mutable_shape(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_shape();
  const ::tensorflow::TensorShapeProto& shape(int index) const;
  ::tensorflow::TensorShapeProto* add_shape();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      shape() const;

  // repeated .tensorflow.TensorProto tensor = 8;
  int tensor_size() const;
  void clear_tensor();
  ::tensorflow::TensorProto* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensor();
  const ::tensorflow::TensorProto& tensor(int index) const;
  ::tensorflow::TensorProto* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensor() const;

  // repeated .tensorflow.NameAttrList func = 9;
  int func_size() const;
  void clear_func();
  ::tensorflow::NameAttrList* mutable_func(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NameAttrList >*
      mutable_func();
  const ::tensorflow::NameAttrList& func(int index) const;
  ::tensorflow::NameAttrList* add_func();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NameAttrList >&
      func() const;

  // @@protoc_insertion_point(class_scope:tensorflow.AttrValue.ListValue)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> s_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > i_;
  mutable std::atomic<int> _i_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > f_;
  mutable std::atomic<int> _f_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > b_;
  mutable std::atomic<int> _b_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> type_;
  mutable std::atomic<int> _type_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto > shape_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensor_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NameAttrList > func_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto;
};
// -------------------------------------------------------------------

class AttrValue :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AttrValue) */ {
 public:
  AttrValue();
  virtual ~AttrValue();

  AttrValue(const AttrValue& from);
  AttrValue(AttrValue&& from) noexcept
    : AttrValue() {
    *this = ::std::move(from);
  }

  inline AttrValue& operator=(const AttrValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline AttrValue& operator=(AttrValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AttrValue& default_instance();

  enum ValueCase {
    kS = 2,
    kI = 3,
    kF = 4,
    kB = 5,
    kType = 6,
    kShape = 7,
    kTensor = 8,
    kList = 1,
    kFunc = 10,
    kPlaceholder = 9,
    VALUE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AttrValue* internal_default_instance() {
    return reinterpret_cast<const AttrValue*>(
               &_AttrValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(AttrValue& a, AttrValue& b) {
    a.Swap(&b);
  }
  inline void Swap(AttrValue* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AttrValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AttrValue* New() const final {
    return CreateMaybeMessage<AttrValue>(nullptr);
  }

  AttrValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AttrValue>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AttrValue& from);
  void MergeFrom(const AttrValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AttrValue* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AttrValue";
  }
  protected:
  explicit AttrValue(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef AttrValue_ListValue ListValue;

  // accessors -------------------------------------------------------

  enum : int {
    kSFieldNumber = 2,
    kIFieldNumber = 3,
    kFFieldNumber = 4,
    kBFieldNumber = 5,
    kTypeFieldNumber = 6,
    kShapeFieldNumber = 7,
    kTensorFieldNumber = 8,
    kListFieldNumber = 1,
    kFuncFieldNumber = 10,
    kPlaceholderFieldNumber = 9,
  };
  // bytes s = 2;
  private:
  bool has_s() const;
  public:
  void clear_s();
  const std::string& s() const;
  void set_s(const std::string& value);
  void set_s(std::string&& value);
  void set_s(const char* value);
  void set_s(const void* value, size_t size);
  std::string* mutable_s();
  std::string* release_s();
  void set_allocated_s(std::string* s);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_s();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_s(
      std::string* s);

  // int64 i = 3;
  private:
  bool has_i() const;
  public:
  void clear_i();
  ::PROTOBUF_NAMESPACE_ID::int64 i() const;
  void set_i(::PROTOBUF_NAMESPACE_ID::int64 value);

  // float f = 4;
  private:
  bool has_f() const;
  public:
  void clear_f();
  float f() const;
  void set_f(float value);

  // bool b = 5;
  private:
  bool has_b() const;
  public:
  void clear_b();
  bool b() const;
  void set_b(bool value);

  // .tensorflow.DataType type = 6;
  private:
  bool has_type() const;
  public:
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);

  // .tensorflow.TensorShapeProto shape = 7;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.TensorProto tensor = 8;
  bool has_tensor() const;
  void clear_tensor();
  const ::tensorflow::TensorProto& tensor() const;
  ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorProto* tensor);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor();

  // .tensorflow.AttrValue.ListValue list = 1;
  bool has_list() const;
  void clear_list();
  const ::tensorflow::AttrValue_ListValue& list() const;
  ::tensorflow::AttrValue_ListValue* release_list();
  ::tensorflow::AttrValue_ListValue* mutable_list();
  void set_allocated_list(::tensorflow::AttrValue_ListValue* list);
  void unsafe_arena_set_allocated_list(
      ::tensorflow::AttrValue_ListValue* list);
  ::tensorflow::AttrValue_ListValue* unsafe_arena_release_list();

  // .tensorflow.NameAttrList func = 10;
  bool has_func() const;
  void clear_func();
  const ::tensorflow::NameAttrList& func() const;
  ::tensorflow::NameAttrList* release_func();
  ::tensorflow::NameAttrList* mutable_func();
  void set_allocated_func(::tensorflow::NameAttrList* func);
  void unsafe_arena_set_allocated_func(
      ::tensorflow::NameAttrList* func);
  ::tensorflow::NameAttrList* unsafe_arena_release_func();

  // string placeholder = 9;
  private:
  bool has_placeholder() const;
  public:
  void clear_placeholder();
  const std::string& placeholder() const;
  void set_placeholder(const std::string& value);
  void set_placeholder(std::string&& value);
  void set_placeholder(const char* value);
  void set_placeholder(const char* value, size_t size);
  std::string* mutable_placeholder();
  std::string* release_placeholder();
  void set_allocated_placeholder(std::string* placeholder);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_placeholder();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_placeholder(
      std::string* placeholder);

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.AttrValue)
 private:
  class _Internal;
  void set_has_s();
  void set_has_i();
  void set_has_f();
  void set_has_b();
  void set_has_type();
  void set_has_shape();
  void set_has_tensor();
  void set_has_list();
  void set_has_func();
  void set_has_placeholder();

  inline bool has_value() const;
  inline void clear_has_value();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ValueUnion {
    ValueUnion() {}
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr s_;
    ::PROTOBUF_NAMESPACE_ID::int64 i_;
    float f_;
    bool b_;
    int type_;
    ::tensorflow::TensorShapeProto* shape_;
    ::tensorflow::TensorProto* tensor_;
    ::tensorflow::AttrValue_ListValue* list_;
    ::tensorflow::NameAttrList* func_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr placeholder_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto;
};
// -------------------------------------------------------------------

class NameAttrList_AttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<NameAttrList_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<NameAttrList_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  NameAttrList_AttrEntry_DoNotUse();
  NameAttrList_AttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const NameAttrList_AttrEntry_DoNotUse& other);
  static const NameAttrList_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const NameAttrList_AttrEntry_DoNotUse*>(&_NameAttrList_AttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.NameAttrList.AttrEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto.file_level_metadata[2];
  }

  public:
};

// -------------------------------------------------------------------

class NameAttrList :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NameAttrList) */ {
 public:
  NameAttrList();
  virtual ~NameAttrList();

  NameAttrList(const NameAttrList& from);
  NameAttrList(NameAttrList&& from) noexcept
    : NameAttrList() {
    *this = ::std::move(from);
  }

  inline NameAttrList& operator=(const NameAttrList& from) {
    CopyFrom(from);
    return *this;
  }
  inline NameAttrList& operator=(NameAttrList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NameAttrList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NameAttrList* internal_default_instance() {
    return reinterpret_cast<const NameAttrList*>(
               &_NameAttrList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(NameAttrList& a, NameAttrList& b) {
    a.Swap(&b);
  }
  inline void Swap(NameAttrList* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NameAttrList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NameAttrList* New() const final {
    return CreateMaybeMessage<NameAttrList>(nullptr);
  }

  NameAttrList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NameAttrList>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NameAttrList& from);
  void MergeFrom(const NameAttrList& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NameAttrList* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NameAttrList";
  }
  protected:
  explicit NameAttrList(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAttrFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // map<string, .tensorflow.AttrValue> attr = 2;
  int attr_size() const;
  void clear_attr();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // @@protoc_insertion_point(class_scope:tensorflow.NameAttrList)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      NameAttrList_AttrEntry_DoNotUse,
      std::string, ::tensorflow::AttrValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attr_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AttrValue_ListValue

// repeated bytes s = 2;
inline int AttrValue_ListValue::s_size() const {
  return s_.size();
}
inline void AttrValue_ListValue::clear_s() {
  s_.Clear();
}
inline const std::string& AttrValue_ListValue::s(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.s)
  return s_.Get(index);
}
inline std::string* AttrValue_ListValue::mutable_s(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.ListValue.s)
  return s_.Mutable(index);
}
inline void AttrValue_ListValue::set_s(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.s)
  s_.Mutable(index)->assign(value);
}
inline void AttrValue_ListValue::set_s(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.s)
  s_.Mutable(index)->assign(std::move(value));
}
inline void AttrValue_ListValue::set_s(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  s_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::set_s(int index, const void* value, size_t size) {
  s_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AttrValue.ListValue.s)
}
inline std::string* AttrValue_ListValue::add_s() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.AttrValue.ListValue.s)
  return s_.Add();
}
inline void AttrValue_ListValue::add_s(const std::string& value) {
  s_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::add_s(std::string&& value) {
  s_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::add_s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  s_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::add_s(const void* value, size_t size) {
  s_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.AttrValue.ListValue.s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
AttrValue_ListValue::s() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.s)
  return s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
AttrValue_ListValue::mutable_s() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.s)
  return &s_;
}

// repeated int64 i = 3 [packed = true];
inline int AttrValue_ListValue::i_size() const {
  return i_.size();
}
inline void AttrValue_ListValue::clear_i() {
  i_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AttrValue_ListValue::i(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.i)
  return i_.Get(index);
}
inline void AttrValue_ListValue::set_i(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  i_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.i)
}
inline void AttrValue_ListValue::add_i(::PROTOBUF_NAMESPACE_ID::int64 value) {
  i_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.i)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
AttrValue_ListValue::i() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.i)
  return i_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
AttrValue_ListValue::mutable_i() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.i)
  return &i_;
}

// repeated float f = 4 [packed = true];
inline int AttrValue_ListValue::f_size() const {
  return f_.size();
}
inline void AttrValue_ListValue::clear_f() {
  f_.Clear();
}
inline float AttrValue_ListValue::f(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.f)
  return f_.Get(index);
}
inline void AttrValue_ListValue::set_f(int index, float value) {
  f_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.f)
}
inline void AttrValue_ListValue::add_f(float value) {
  f_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.f)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
AttrValue_ListValue::f() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.f)
  return f_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
AttrValue_ListValue::mutable_f() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.f)
  return &f_;
}

// repeated bool b = 5 [packed = true];
inline int AttrValue_ListValue::b_size() const {
  return b_.size();
}
inline void AttrValue_ListValue::clear_b() {
  b_.Clear();
}
inline bool AttrValue_ListValue::b(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.b)
  return b_.Get(index);
}
inline void AttrValue_ListValue::set_b(int index, bool value) {
  b_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.b)
}
inline void AttrValue_ListValue::add_b(bool value) {
  b_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.b)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
AttrValue_ListValue::b() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.b)
  return b_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
AttrValue_ListValue::mutable_b() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.b)
  return &b_;
}

// repeated .tensorflow.DataType type = 6 [packed = true];
inline int AttrValue_ListValue::type_size() const {
  return type_.size();
}
inline void AttrValue_ListValue::clear_type() {
  type_.Clear();
}
inline ::tensorflow::DataType AttrValue_ListValue::type(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.type)
  return static_cast< ::tensorflow::DataType >(type_.Get(index));
}
inline void AttrValue_ListValue::set_type(int index, ::tensorflow::DataType value) {
  type_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.ListValue.type)
}
inline void AttrValue_ListValue::add_type(::tensorflow::DataType value) {
  type_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.type)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
AttrValue_ListValue::type() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.type)
  return type_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
AttrValue_ListValue::mutable_type() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.type)
  return &type_;
}

// repeated .tensorflow.TensorShapeProto shape = 7;
inline int AttrValue_ListValue::shape_size() const {
  return shape_.size();
}
inline ::tensorflow::TensorShapeProto* AttrValue_ListValue::mutable_shape(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.ListValue.shape)
  return shape_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
AttrValue_ListValue::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.shape)
  return &shape_;
}
inline const ::tensorflow::TensorShapeProto& AttrValue_ListValue::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.shape)
  return shape_.Get(index);
}
inline ::tensorflow::TensorShapeProto* AttrValue_ListValue::add_shape() {
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.shape)
  return shape_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
AttrValue_ListValue::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.shape)
  return shape_;
}

// repeated .tensorflow.TensorProto tensor = 8;
inline int AttrValue_ListValue::tensor_size() const {
  return tensor_.size();
}
inline ::tensorflow::TensorProto* AttrValue_ListValue::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.ListValue.tensor)
  return tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
AttrValue_ListValue::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.tensor)
  return &tensor_;
}
inline const ::tensorflow::TensorProto& AttrValue_ListValue::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.tensor)
  return tensor_.Get(index);
}
inline ::tensorflow::TensorProto* AttrValue_ListValue::add_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.tensor)
  return tensor_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
AttrValue_ListValue::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.tensor)
  return tensor_;
}

// repeated .tensorflow.NameAttrList func = 9;
inline int AttrValue_ListValue::func_size() const {
  return func_.size();
}
inline void AttrValue_ListValue::clear_func() {
  func_.Clear();
}
inline ::tensorflow::NameAttrList* AttrValue_ListValue::mutable_func(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.ListValue.func)
  return func_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NameAttrList >*
AttrValue_ListValue::mutable_func() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AttrValue.ListValue.func)
  return &func_;
}
inline const ::tensorflow::NameAttrList& AttrValue_ListValue::func(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.ListValue.func)
  return func_.Get(index);
}
inline ::tensorflow::NameAttrList* AttrValue_ListValue::add_func() {
  // @@protoc_insertion_point(field_add:tensorflow.AttrValue.ListValue.func)
  return func_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NameAttrList >&
AttrValue_ListValue::func() const {
  // @@protoc_insertion_point(field_list:tensorflow.AttrValue.ListValue.func)
  return func_;
}

// -------------------------------------------------------------------

// AttrValue

// bytes s = 2;
inline bool AttrValue::has_s() const {
  return value_case() == kS;
}
inline void AttrValue::set_has_s() {
  _oneof_case_[0] = kS;
}
inline void AttrValue::clear_s() {
  if (has_s()) {
    value_.s_.Destroy(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_value();
  }
}
inline const std::string& AttrValue::s() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.s)
  if (has_s()) {
    return value_.s_.Get();
  }
  return *&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void AttrValue::set_s(const std::string& value) {
  if (!has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.s)
}
inline void AttrValue::set_s(std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.s)
  if (!has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.s_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AttrValue.s)
}
inline void AttrValue::set_s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  if (!has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AttrValue.s)
}
inline void AttrValue::set_s(const void* value,
                             size_t size) {
  if (!has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.s_.Set(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AttrValue.s)
}
inline std::string* AttrValue::mutable_s() {
  if (!has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return value_.s_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.s)
}
inline std::string* AttrValue::release_s() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.s)
  if (has_s()) {
    clear_has_value();
    return value_.s_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void AttrValue::set_allocated_s(std::string* s) {
  if (has_value()) {
    clear_value();
  }
  if (s != nullptr) {
    set_has_s();
    value_.s_.UnsafeSetDefault(s);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AttrValue.s)
}
inline std::string* AttrValue::unsafe_arena_release_s() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.s)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (has_s()) {
    clear_has_value();
    return value_.s_.UnsafeArenaRelease(
        &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_s(std::string* s) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (!has_s()) {
    value_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (s) {
    set_has_s();
    value_.s_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), s, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.s)
}

// int64 i = 3;
inline bool AttrValue::has_i() const {
  return value_case() == kI;
}
inline void AttrValue::set_has_i() {
  _oneof_case_[0] = kI;
}
inline void AttrValue::clear_i() {
  if (has_i()) {
    value_.i_ = PROTOBUF_LONGLONG(0);
    clear_has_value();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AttrValue::i() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.i)
  if (has_i()) {
    return value_.i_;
  }
  return PROTOBUF_LONGLONG(0);
}
inline void AttrValue::set_i(::PROTOBUF_NAMESPACE_ID::int64 value) {
  if (!has_i()) {
    clear_value();
    set_has_i();
  }
  value_.i_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.i)
}

// float f = 4;
inline bool AttrValue::has_f() const {
  return value_case() == kF;
}
inline void AttrValue::set_has_f() {
  _oneof_case_[0] = kF;
}
inline void AttrValue::clear_f() {
  if (has_f()) {
    value_.f_ = 0;
    clear_has_value();
  }
}
inline float AttrValue::f() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.f)
  if (has_f()) {
    return value_.f_;
  }
  return 0;
}
inline void AttrValue::set_f(float value) {
  if (!has_f()) {
    clear_value();
    set_has_f();
  }
  value_.f_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.f)
}

// bool b = 5;
inline bool AttrValue::has_b() const {
  return value_case() == kB;
}
inline void AttrValue::set_has_b() {
  _oneof_case_[0] = kB;
}
inline void AttrValue::clear_b() {
  if (has_b()) {
    value_.b_ = false;
    clear_has_value();
  }
}
inline bool AttrValue::b() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.b)
  if (has_b()) {
    return value_.b_;
  }
  return false;
}
inline void AttrValue::set_b(bool value) {
  if (!has_b()) {
    clear_value();
    set_has_b();
  }
  value_.b_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.b)
}

// .tensorflow.DataType type = 6;
inline bool AttrValue::has_type() const {
  return value_case() == kType;
}
inline void AttrValue::set_has_type() {
  _oneof_case_[0] = kType;
}
inline void AttrValue::clear_type() {
  if (has_type()) {
    value_.type_ = 0;
    clear_has_value();
  }
}
inline ::tensorflow::DataType AttrValue::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.type)
  if (has_type()) {
    return static_cast< ::tensorflow::DataType >(value_.type_);
  }
  return static_cast< ::tensorflow::DataType >(0);
}
inline void AttrValue::set_type(::tensorflow::DataType value) {
  if (!has_type()) {
    clear_value();
    set_has_type();
  }
  value_.type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.type)
}

// .tensorflow.TensorShapeProto shape = 7;
inline bool AttrValue::has_shape() const {
  return value_case() == kShape;
}
inline void AttrValue::set_has_shape() {
  _oneof_case_[0] = kShape;
}
inline ::tensorflow::TensorShapeProto* AttrValue::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.shape)
  if (has_shape()) {
    clear_has_value();
      ::tensorflow::TensorShapeProto* temp = value_.shape_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.shape_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorShapeProto& AttrValue::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.shape)
  return has_shape()
      ? *value_.shape_
      : *reinterpret_cast< ::tensorflow::TensorShapeProto*>(&::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* AttrValue::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.shape)
  if (has_shape()) {
    clear_has_value();
    ::tensorflow::TensorShapeProto* temp = value_.shape_;
    value_.shape_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  clear_value();
  if (shape) {
    set_has_shape();
    value_.shape_ = shape;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.shape)
}
inline ::tensorflow::TensorShapeProto* AttrValue::mutable_shape() {
  if (!has_shape()) {
    clear_value();
    set_has_shape();
    value_.shape_ = CreateMaybeMessage< ::tensorflow::TensorShapeProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.shape)
  return value_.shape_;
}

// .tensorflow.TensorProto tensor = 8;
inline bool AttrValue::has_tensor() const {
  return value_case() == kTensor;
}
inline void AttrValue::set_has_tensor() {
  _oneof_case_[0] = kTensor;
}
inline ::tensorflow::TensorProto* AttrValue::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.tensor)
  if (has_tensor()) {
    clear_has_value();
      ::tensorflow::TensorProto* temp = value_.tensor_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorProto& AttrValue::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.tensor)
  return has_tensor()
      ? *value_.tensor_
      : *reinterpret_cast< ::tensorflow::TensorProto*>(&::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* AttrValue::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.tensor)
  if (has_tensor()) {
    clear_has_value();
    ::tensorflow::TensorProto* temp = value_.tensor_;
    value_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  clear_value();
  if (tensor) {
    set_has_tensor();
    value_.tensor_ = tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.tensor)
}
inline ::tensorflow::TensorProto* AttrValue::mutable_tensor() {
  if (!has_tensor()) {
    clear_value();
    set_has_tensor();
    value_.tensor_ = CreateMaybeMessage< ::tensorflow::TensorProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.tensor)
  return value_.tensor_;
}

// .tensorflow.AttrValue.ListValue list = 1;
inline bool AttrValue::has_list() const {
  return value_case() == kList;
}
inline void AttrValue::set_has_list() {
  _oneof_case_[0] = kList;
}
inline void AttrValue::clear_list() {
  if (has_list()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete value_.list_;
    }
    clear_has_value();
  }
}
inline ::tensorflow::AttrValue_ListValue* AttrValue::release_list() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.list)
  if (has_list()) {
    clear_has_value();
      ::tensorflow::AttrValue_ListValue* temp = value_.list_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::AttrValue_ListValue& AttrValue::list() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.list)
  return has_list()
      ? *value_.list_
      : *reinterpret_cast< ::tensorflow::AttrValue_ListValue*>(&::tensorflow::_AttrValue_ListValue_default_instance_);
}
inline ::tensorflow::AttrValue_ListValue* AttrValue::unsafe_arena_release_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.list)
  if (has_list()) {
    clear_has_value();
    ::tensorflow::AttrValue_ListValue* temp = value_.list_;
    value_.list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_list(::tensorflow::AttrValue_ListValue* list) {
  clear_value();
  if (list) {
    set_has_list();
    value_.list_ = list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.list)
}
inline ::tensorflow::AttrValue_ListValue* AttrValue::mutable_list() {
  if (!has_list()) {
    clear_value();
    set_has_list();
    value_.list_ = CreateMaybeMessage< ::tensorflow::AttrValue_ListValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.list)
  return value_.list_;
}

// .tensorflow.NameAttrList func = 10;
inline bool AttrValue::has_func() const {
  return value_case() == kFunc;
}
inline void AttrValue::set_has_func() {
  _oneof_case_[0] = kFunc;
}
inline void AttrValue::clear_func() {
  if (has_func()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete value_.func_;
    }
    clear_has_value();
  }
}
inline ::tensorflow::NameAttrList* AttrValue::release_func() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.func)
  if (has_func()) {
    clear_has_value();
      ::tensorflow::NameAttrList* temp = value_.func_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.func_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::NameAttrList& AttrValue::func() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.func)
  return has_func()
      ? *value_.func_
      : *reinterpret_cast< ::tensorflow::NameAttrList*>(&::tensorflow::_NameAttrList_default_instance_);
}
inline ::tensorflow::NameAttrList* AttrValue::unsafe_arena_release_func() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.func)
  if (has_func()) {
    clear_has_value();
    ::tensorflow::NameAttrList* temp = value_.func_;
    value_.func_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_func(::tensorflow::NameAttrList* func) {
  clear_value();
  if (func) {
    set_has_func();
    value_.func_ = func;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.func)
}
inline ::tensorflow::NameAttrList* AttrValue::mutable_func() {
  if (!has_func()) {
    clear_value();
    set_has_func();
    value_.func_ = CreateMaybeMessage< ::tensorflow::NameAttrList >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.func)
  return value_.func_;
}

// string placeholder = 9;
inline bool AttrValue::has_placeholder() const {
  return value_case() == kPlaceholder;
}
inline void AttrValue::set_has_placeholder() {
  _oneof_case_[0] = kPlaceholder;
}
inline void AttrValue::clear_placeholder() {
  if (has_placeholder()) {
    value_.placeholder_.Destroy(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_value();
  }
}
inline const std::string& AttrValue::placeholder() const {
  // @@protoc_insertion_point(field_get:tensorflow.AttrValue.placeholder)
  if (has_placeholder()) {
    return value_.placeholder_.Get();
  }
  return *&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void AttrValue::set_placeholder(const std::string& value) {
  if (!has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.placeholder_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.placeholder)
}
inline void AttrValue::set_placeholder(std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.AttrValue.placeholder)
  if (!has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.placeholder_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AttrValue.placeholder)
}
inline void AttrValue::set_placeholder(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  if (!has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.placeholder_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AttrValue.placeholder)
}
inline void AttrValue::set_placeholder(const char* value,
                             size_t size) {
  if (!has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.placeholder_.Set(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AttrValue.placeholder)
}
inline std::string* AttrValue::mutable_placeholder() {
  if (!has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return value_.placeholder_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.AttrValue.placeholder)
}
inline std::string* AttrValue::release_placeholder() {
  // @@protoc_insertion_point(field_release:tensorflow.AttrValue.placeholder)
  if (has_placeholder()) {
    clear_has_value();
    return value_.placeholder_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void AttrValue::set_allocated_placeholder(std::string* placeholder) {
  if (has_value()) {
    clear_value();
  }
  if (placeholder != nullptr) {
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(placeholder);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AttrValue.placeholder)
}
inline std::string* AttrValue::unsafe_arena_release_placeholder() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AttrValue.placeholder)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (has_placeholder()) {
    clear_has_value();
    return value_.placeholder_.UnsafeArenaRelease(
        &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return nullptr;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_placeholder(std::string* placeholder) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (!has_placeholder()) {
    value_.placeholder_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (placeholder) {
    set_has_placeholder();
    value_.placeholder_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), placeholder, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AttrValue.placeholder)
}

inline bool AttrValue::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void AttrValue::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline AttrValue::ValueCase AttrValue::value_case() const {
  return AttrValue::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// NameAttrList

// string name = 1;
inline void NameAttrList::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& NameAttrList::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NameAttrList.name)
  return name_.Get();
}
inline void NameAttrList::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.NameAttrList.name)
}
inline void NameAttrList::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NameAttrList.name)
}
inline void NameAttrList::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.NameAttrList.name)
}
inline void NameAttrList::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NameAttrList.name)
}
inline std::string* NameAttrList::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NameAttrList.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* NameAttrList::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NameAttrList.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void NameAttrList::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NameAttrList.name)
}
inline std::string* NameAttrList::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NameAttrList.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void NameAttrList::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NameAttrList.name)
}

// map<string, .tensorflow.AttrValue> attr = 2;
inline int NameAttrList::attr_size() const {
  return attr_.size();
}
inline void NameAttrList::clear_attr() {
  attr_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
NameAttrList::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.NameAttrList.attr)
  return attr_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
NameAttrList::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.NameAttrList.attr)
  return attr_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
