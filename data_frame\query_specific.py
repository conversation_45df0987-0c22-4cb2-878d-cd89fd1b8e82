import pymysql
import datetime
import csv
import json
import os

# 数据库连接配置
db_config = {
    'host': 'rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
    'user': 'ai',
    'password': 'wq$$4r%ixg',
    'database': 'backend_test',
    'port': 3306,
    'charset': 'utf8mb4'
}

def test_db_connection(config):
    """
    测试数据库连接
    """
    connection = None
    try:
        connection = pymysql.connect(**config)
        print(f"成功连接到数据库: {config['database']}@{config['host']}:{config['port']}")
        return True
    except Exception as e:
        print(f"连接失败: {e}")
        return False
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭")

def query_by_time_range(target_date, start_hour, start_minute, end_hour, end_minute):
    """
    根据时间范围查询数据
    
    Args:
        target_date: 目标日期，格式为 datetime 对象
        start_hour: 开始小时（24小时制）
        start_minute: 开始分钟
        end_hour: 结束小时（24小时制）
        end_minute: 结束分钟
    """
    connection = None
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 1. 构造表名和时间范围
        table_date = target_date.strftime("%Y%m%d")
        table_name = f"t_data_ecg_{table_date}"
        
        # 构造时间范围
        start_time = target_date.replace(hour=start_hour, minute=start_minute, second=0)
        end_time = target_date.replace(hour=end_hour, minute=end_minute, second=59)
        
        print(f"尝试查询表: {table_name}")
        print(f"时间范围: {start_time} 到 {end_time}")
        
        # 2. 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{db_config['database']}' 
            AND table_name = '{table_name}'
        """)
        if cursor.fetchone()[0] == 0:
            print(f"表 {table_name} 不存在")
            return None
        
        # 3. 根据时间范围查询数据
        print(f"\n------ 查询时间在 {start_hour}:{start_minute} - {end_hour}:{end_minute} 的记录 ------")
        
        cursor.execute(f"""
            SELECT id, union_id, start_time, end_time, es_key
            FROM {table_name}
            WHERE start_time BETWEEN '{start_time}' AND '{end_time}'
            ORDER BY start_time
            LIMIT 20
        """)
        records = cursor.fetchall()
        
        if not records:
            print(f"没有找到符合条件的记录")
            return None
        else:
            print(f"找到 {len(records)} 条记录")
            
            # 获取列名并打印结果
            column_names = ['id', 'union_id', 'start_time', 'end_time', 'es_key']
            for i, record in enumerate(records):
                print(f"\n记录 {i+1}:")
                for j, col_name in enumerate(column_names):
                    print(f"  {col_name}: {record[j]}")
            
            return {"column_names": column_names, "records": records}
    except Exception as e:
        print(f"查询出错: {e}")
        return None
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭")

def query_by_id(target_date, record_id):
    """
    根据ID查询数据
    
    Args:
        target_date: 目标日期，格式为 datetime 对象
        record_id: 记录ID
    """
    connection = None
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 1. 构造表名
        table_date = target_date.strftime("%Y%m%d")
        table_name = f"t_data_ecg_{table_date}"
        
        print(f"尝试查询表: {table_name}")
        
        # 2. 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{db_config['database']}' 
            AND table_name = '{table_name}'
        """)
        if cursor.fetchone()[0] == 0:
            print(f"表 {table_name} 不存在")
            return None
        
        # 3. 根据ID查询数据
        print(f"\n------ 查询 ID = {record_id} 的记录 ------")
        
        cursor.execute(f"""
            SELECT * FROM {table_name}
            WHERE id = {record_id}
        """)
        record = cursor.fetchone()
        
        if not record:
            print(f"没有找到 ID = {record_id} 的记录")
            return None
        else:
            print(f"找到 ID = {record_id} 的记录")
            
            # 获取列名
            cursor.execute(f"SHOW COLUMNS FROM {table_name}")
            column_names = [column[0] for column in cursor.fetchall()]
            
            # 将记录转换为字典
            result = dict(zip(column_names, record))
            
            # 打印记录的基本信息（不包括大型数据字段）
            print("\n记录基本信息:")
            for key, value in result.items():
                if key not in ['ecg', 'ecgII', 'ecgIII', 'ecg_byte', 'ecg_summary', 'ecg_analysis', 
                               'ecg_analysisII', 'ecg_analysisIII']:
                    print(f"  {key}: {value}")
            
            return {"column_names": column_names, "record": result}
    except Exception as e:
        print(f"查询出错: {e}")
        return None
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭")

def save_record_to_json(data, record_id, date_str):
    """保存记录到JSON文件"""
    if not data or not data.get("record"):
        print("没有数据可以保存")
        return None
    
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    filename = f'ecg_record_{record_id}_{date_str}_{timestamp}.json'
    
    try:
        # 创建一个新字典，仅包含基本信息（不包括大型数据字段）
        record_summary = {}
        for key, value in data["record"].items():
            if key not in ['ecg', 'ecgII', 'ecgIII', 'ecg_byte', 'ecg_summary', 'ecg_analysis', 
                          'ecg_analysisII', 'ecg_analysisIII']:
                record_summary[key] = value
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(record_summary, f, ensure_ascii=False, indent=2, default=str)
        print(f"记录基本信息已成功导出到文件: {filename}")
        
        # 保存完整记录（包括大型数据字段）
        full_filename = f'ecg_record_full_{record_id}_{date_str}_{timestamp}.json'
        with open(full_filename, 'w', encoding='utf-8') as f:
            json.dump(data["record"], f, ensure_ascii=False, indent=2, default=str)
        print(f"完整记录已成功导出到文件: {full_filename}")
        
        return filename
    except Exception as e:
        print(f"保存JSON时出错: {e}")
        return None

if __name__ == "__main__":
    target_date = datetime.datetime(2025, 4, 1)  # 查询日期：2025年4月1日
    date_str = target_date.strftime("%Y%m%d")
    
    if test_db_connection(db_config):
        print(f"\n------ 开始查询 {target_date.strftime('%Y-%m-%d')} 的数据 ------")
        
        # 1. 查询下午3点30分左右的记录
        print("\n1. 查询下午3点30分左右的记录")
        query_by_time_range(target_date, 15, 25, 15, 35)
        
        # 2. 查询特定ID的记录
        print("\n2. 查询特定ID的记录")
        specific_id = 1906970379739693058  # 前面发现的一个ID
        record_data = query_by_id(target_date, specific_id)
        
        if record_data and record_data.get("record"):
            # 保存记录到JSON文件
            save_record_to_json(record_data, specific_id, date_str)
        
        # 查询3:30附近的其他记录
        print("\n3. 查询3:30附近的其他记录")
        query_by_time_range(target_date, 15, 30, 15, 40)
    else:
        print("数据库连接测试失败，无法执行查询") 