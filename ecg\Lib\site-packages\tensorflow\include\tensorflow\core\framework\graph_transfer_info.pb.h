// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/graph_transfer_info.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[8]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
namespace tensorflow {
class GraphTransferConstNodeInfo;
class GraphTransferConstNodeInfoDefaultTypeInternal;
extern GraphTransferConstNodeInfoDefaultTypeInternal _GraphTransferConstNodeInfo_default_instance_;
class GraphTransferGraphInputNodeInfo;
class GraphTransferGraphInputNodeInfoDefaultTypeInternal;
extern GraphTransferGraphInputNodeInfoDefaultTypeInternal _GraphTransferGraphInputNodeInfo_default_instance_;
class GraphTransferGraphOutputNodeInfo;
class GraphTransferGraphOutputNodeInfoDefaultTypeInternal;
extern GraphTransferGraphOutputNodeInfoDefaultTypeInternal _GraphTransferGraphOutputNodeInfo_default_instance_;
class GraphTransferInfo;
class GraphTransferInfoDefaultTypeInternal;
extern GraphTransferInfoDefaultTypeInternal _GraphTransferInfo_default_instance_;
class GraphTransferNodeInfo;
class GraphTransferNodeInfoDefaultTypeInternal;
extern GraphTransferNodeInfoDefaultTypeInternal _GraphTransferNodeInfo_default_instance_;
class GraphTransferNodeInput;
class GraphTransferNodeInputDefaultTypeInternal;
extern GraphTransferNodeInputDefaultTypeInternal _GraphTransferNodeInput_default_instance_;
class GraphTransferNodeInputInfo;
class GraphTransferNodeInputInfoDefaultTypeInternal;
extern GraphTransferNodeInputInfoDefaultTypeInternal _GraphTransferNodeInputInfo_default_instance_;
class GraphTransferNodeOutputInfo;
class GraphTransferNodeOutputInfoDefaultTypeInternal;
extern GraphTransferNodeOutputInfoDefaultTypeInternal _GraphTransferNodeOutputInfo_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::GraphTransferConstNodeInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferConstNodeInfo>(Arena*);
template<> ::tensorflow::GraphTransferGraphInputNodeInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferGraphInputNodeInfo>(Arena*);
template<> ::tensorflow::GraphTransferGraphOutputNodeInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferGraphOutputNodeInfo>(Arena*);
template<> ::tensorflow::GraphTransferInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferInfo>(Arena*);
template<> ::tensorflow::GraphTransferNodeInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferNodeInfo>(Arena*);
template<> ::tensorflow::GraphTransferNodeInput* Arena::CreateMaybeMessage<::tensorflow::GraphTransferNodeInput>(Arena*);
template<> ::tensorflow::GraphTransferNodeInputInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferNodeInputInfo>(Arena*);
template<> ::tensorflow::GraphTransferNodeOutputInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferNodeOutputInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum GraphTransferInfo_Destination : int {
  GraphTransferInfo_Destination_NOP = 0,
  GraphTransferInfo_Destination_HEXAGON = 1,
  GraphTransferInfo_Destination_GraphTransferInfo_Destination_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  GraphTransferInfo_Destination_GraphTransferInfo_Destination_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool GraphTransferInfo_Destination_IsValid(int value);
constexpr GraphTransferInfo_Destination GraphTransferInfo_Destination_Destination_MIN = GraphTransferInfo_Destination_NOP;
constexpr GraphTransferInfo_Destination GraphTransferInfo_Destination_Destination_MAX = GraphTransferInfo_Destination_HEXAGON;
constexpr int GraphTransferInfo_Destination_Destination_ARRAYSIZE = GraphTransferInfo_Destination_Destination_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GraphTransferInfo_Destination_descriptor();
template<typename T>
inline const std::string& GraphTransferInfo_Destination_Name(T enum_t_value) {
  static_assert(::std::is_same<T, GraphTransferInfo_Destination>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function GraphTransferInfo_Destination_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    GraphTransferInfo_Destination_descriptor(), enum_t_value);
}
inline bool GraphTransferInfo_Destination_Parse(
    const std::string& name, GraphTransferInfo_Destination* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<GraphTransferInfo_Destination>(
    GraphTransferInfo_Destination_descriptor(), name, value);
}
// ===================================================================

class GraphTransferNodeInput :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferNodeInput) */ {
 public:
  GraphTransferNodeInput();
  virtual ~GraphTransferNodeInput();

  GraphTransferNodeInput(const GraphTransferNodeInput& from);
  GraphTransferNodeInput(GraphTransferNodeInput&& from) noexcept
    : GraphTransferNodeInput() {
    *this = ::std::move(from);
  }

  inline GraphTransferNodeInput& operator=(const GraphTransferNodeInput& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferNodeInput& operator=(GraphTransferNodeInput&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphTransferNodeInput& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphTransferNodeInput* internal_default_instance() {
    return reinterpret_cast<const GraphTransferNodeInput*>(
               &_GraphTransferNodeInput_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GraphTransferNodeInput& a, GraphTransferNodeInput& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferNodeInput* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferNodeInput* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphTransferNodeInput* New() const final {
    return CreateMaybeMessage<GraphTransferNodeInput>(nullptr);
  }

  GraphTransferNodeInput* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphTransferNodeInput>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphTransferNodeInput& from);
  void MergeFrom(const GraphTransferNodeInput& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferNodeInput* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferNodeInput";
  }
  protected:
  explicit GraphTransferNodeInput(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
    kOutputPortFieldNumber = 2,
  };
  // int32 node_id = 1;
  void clear_node_id();
  ::PROTOBUF_NAMESPACE_ID::int32 node_id() const;
  void set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 output_port = 2;
  void clear_output_port();
  ::PROTOBUF_NAMESPACE_ID::int32 output_port() const;
  void set_output_port(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferNodeInput)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int32 node_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 output_port_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferNodeInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferNodeInfo) */ {
 public:
  GraphTransferNodeInfo();
  virtual ~GraphTransferNodeInfo();

  GraphTransferNodeInfo(const GraphTransferNodeInfo& from);
  GraphTransferNodeInfo(GraphTransferNodeInfo&& from) noexcept
    : GraphTransferNodeInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferNodeInfo& operator=(const GraphTransferNodeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferNodeInfo& operator=(GraphTransferNodeInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphTransferNodeInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphTransferNodeInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferNodeInfo*>(
               &_GraphTransferNodeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GraphTransferNodeInfo& a, GraphTransferNodeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferNodeInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferNodeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphTransferNodeInfo* New() const final {
    return CreateMaybeMessage<GraphTransferNodeInfo>(nullptr);
  }

  GraphTransferNodeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphTransferNodeInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphTransferNodeInfo& from);
  void MergeFrom(const GraphTransferNodeInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferNodeInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferNodeInfo";
  }
  protected:
  explicit GraphTransferNodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kTypeNameFieldNumber = 3,
    kNodeIdFieldNumber = 2,
    kSocOpIdFieldNumber = 4,
    kPaddingIdFieldNumber = 5,
    kInputCountFieldNumber = 6,
    kOutputCountFieldNumber = 7,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string type_name = 3;
  void clear_type_name();
  const std::string& type_name() const;
  void set_type_name(const std::string& value);
  void set_type_name(std::string&& value);
  void set_type_name(const char* value);
  void set_type_name(const char* value, size_t size);
  std::string* mutable_type_name();
  std::string* release_type_name();
  void set_allocated_type_name(std::string* type_name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_type_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type_name(
      std::string* type_name);

  // int32 node_id = 2;
  void clear_node_id();
  ::PROTOBUF_NAMESPACE_ID::int32 node_id() const;
  void set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 soc_op_id = 4;
  void clear_soc_op_id();
  ::PROTOBUF_NAMESPACE_ID::int32 soc_op_id() const;
  void set_soc_op_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 padding_id = 5;
  void clear_padding_id();
  ::PROTOBUF_NAMESPACE_ID::int32 padding_id() const;
  void set_padding_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 input_count = 6;
  void clear_input_count();
  ::PROTOBUF_NAMESPACE_ID::int32 input_count() const;
  void set_input_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 output_count = 7;
  void clear_output_count();
  ::PROTOBUF_NAMESPACE_ID::int32 output_count() const;
  void set_output_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferNodeInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_name_;
  ::PROTOBUF_NAMESPACE_ID::int32 node_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 soc_op_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 padding_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 input_count_;
  ::PROTOBUF_NAMESPACE_ID::int32 output_count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferConstNodeInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferConstNodeInfo) */ {
 public:
  GraphTransferConstNodeInfo();
  virtual ~GraphTransferConstNodeInfo();

  GraphTransferConstNodeInfo(const GraphTransferConstNodeInfo& from);
  GraphTransferConstNodeInfo(GraphTransferConstNodeInfo&& from) noexcept
    : GraphTransferConstNodeInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferConstNodeInfo& operator=(const GraphTransferConstNodeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferConstNodeInfo& operator=(GraphTransferConstNodeInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphTransferConstNodeInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphTransferConstNodeInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferConstNodeInfo*>(
               &_GraphTransferConstNodeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GraphTransferConstNodeInfo& a, GraphTransferConstNodeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferConstNodeInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferConstNodeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphTransferConstNodeInfo* New() const final {
    return CreateMaybeMessage<GraphTransferConstNodeInfo>(nullptr);
  }

  GraphTransferConstNodeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphTransferConstNodeInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphTransferConstNodeInfo& from);
  void MergeFrom(const GraphTransferConstNodeInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferConstNodeInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferConstNodeInfo";
  }
  protected:
  explicit GraphTransferConstNodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 3,
    kNameFieldNumber = 1,
    kDataFieldNumber = 4,
    kNodeIdFieldNumber = 2,
    kDtypeFieldNumber = 5,
  };
  // repeated int64 shape = 3;
  int shape_size() const;
  void clear_shape();
  ::PROTOBUF_NAMESPACE_ID::int64 shape(int index) const;
  void set_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_shape(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_shape();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // bytes data = 4;
  void clear_data();
  const std::string& data() const;
  void set_data(const std::string& value);
  void set_data(std::string&& value);
  void set_data(const char* value);
  void set_data(const void* value, size_t size);
  std::string* mutable_data();
  std::string* release_data();
  void set_allocated_data(std::string* data);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_data();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_data(
      std::string* data);

  // int32 node_id = 2;
  void clear_node_id();
  ::PROTOBUF_NAMESPACE_ID::int32 node_id() const;
  void set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // .tensorflow.DataType dtype = 5;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferConstNodeInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > shape_;
  mutable std::atomic<int> _shape_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  ::PROTOBUF_NAMESPACE_ID::int32 node_id_;
  int dtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferNodeInputInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferNodeInputInfo) */ {
 public:
  GraphTransferNodeInputInfo();
  virtual ~GraphTransferNodeInputInfo();

  GraphTransferNodeInputInfo(const GraphTransferNodeInputInfo& from);
  GraphTransferNodeInputInfo(GraphTransferNodeInputInfo&& from) noexcept
    : GraphTransferNodeInputInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferNodeInputInfo& operator=(const GraphTransferNodeInputInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferNodeInputInfo& operator=(GraphTransferNodeInputInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphTransferNodeInputInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphTransferNodeInputInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferNodeInputInfo*>(
               &_GraphTransferNodeInputInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GraphTransferNodeInputInfo& a, GraphTransferNodeInputInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferNodeInputInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferNodeInputInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphTransferNodeInputInfo* New() const final {
    return CreateMaybeMessage<GraphTransferNodeInputInfo>(nullptr);
  }

  GraphTransferNodeInputInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphTransferNodeInputInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphTransferNodeInputInfo& from);
  void MergeFrom(const GraphTransferNodeInputInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferNodeInputInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferNodeInputInfo";
  }
  protected:
  explicit GraphTransferNodeInputInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeInputFieldNumber = 2,
    kNodeIdFieldNumber = 1,
  };
  // repeated .tensorflow.GraphTransferNodeInput node_input = 2;
  int node_input_size() const;
  void clear_node_input();
  ::tensorflow::GraphTransferNodeInput* mutable_node_input(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInput >*
      mutable_node_input();
  const ::tensorflow::GraphTransferNodeInput& node_input(int index) const;
  ::tensorflow::GraphTransferNodeInput* add_node_input();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInput >&
      node_input() const;

  // int32 node_id = 1;
  void clear_node_id();
  ::PROTOBUF_NAMESPACE_ID::int32 node_id() const;
  void set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferNodeInputInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInput > node_input_;
  ::PROTOBUF_NAMESPACE_ID::int32 node_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferNodeOutputInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferNodeOutputInfo) */ {
 public:
  GraphTransferNodeOutputInfo();
  virtual ~GraphTransferNodeOutputInfo();

  GraphTransferNodeOutputInfo(const GraphTransferNodeOutputInfo& from);
  GraphTransferNodeOutputInfo(GraphTransferNodeOutputInfo&& from) noexcept
    : GraphTransferNodeOutputInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferNodeOutputInfo& operator=(const GraphTransferNodeOutputInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferNodeOutputInfo& operator=(GraphTransferNodeOutputInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphTransferNodeOutputInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphTransferNodeOutputInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferNodeOutputInfo*>(
               &_GraphTransferNodeOutputInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GraphTransferNodeOutputInfo& a, GraphTransferNodeOutputInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferNodeOutputInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferNodeOutputInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphTransferNodeOutputInfo* New() const final {
    return CreateMaybeMessage<GraphTransferNodeOutputInfo>(nullptr);
  }

  GraphTransferNodeOutputInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphTransferNodeOutputInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphTransferNodeOutputInfo& from);
  void MergeFrom(const GraphTransferNodeOutputInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferNodeOutputInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferNodeOutputInfo";
  }
  protected:
  explicit GraphTransferNodeOutputInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMaxByteSizeFieldNumber = 2,
    kNodeIdFieldNumber = 1,
  };
  // repeated int32 max_byte_size = 2;
  int max_byte_size_size() const;
  void clear_max_byte_size();
  ::PROTOBUF_NAMESPACE_ID::int32 max_byte_size(int index) const;
  void set_max_byte_size(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_max_byte_size(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      max_byte_size() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_max_byte_size();

  // int32 node_id = 1;
  void clear_node_id();
  ::PROTOBUF_NAMESPACE_ID::int32 node_id() const;
  void set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferNodeOutputInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > max_byte_size_;
  mutable std::atomic<int> _max_byte_size_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int32 node_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferGraphInputNodeInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferGraphInputNodeInfo) */ {
 public:
  GraphTransferGraphInputNodeInfo();
  virtual ~GraphTransferGraphInputNodeInfo();

  GraphTransferGraphInputNodeInfo(const GraphTransferGraphInputNodeInfo& from);
  GraphTransferGraphInputNodeInfo(GraphTransferGraphInputNodeInfo&& from) noexcept
    : GraphTransferGraphInputNodeInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferGraphInputNodeInfo& operator=(const GraphTransferGraphInputNodeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferGraphInputNodeInfo& operator=(GraphTransferGraphInputNodeInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphTransferGraphInputNodeInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphTransferGraphInputNodeInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferGraphInputNodeInfo*>(
               &_GraphTransferGraphInputNodeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GraphTransferGraphInputNodeInfo& a, GraphTransferGraphInputNodeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferGraphInputNodeInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferGraphInputNodeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphTransferGraphInputNodeInfo* New() const final {
    return CreateMaybeMessage<GraphTransferGraphInputNodeInfo>(nullptr);
  }

  GraphTransferGraphInputNodeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphTransferGraphInputNodeInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphTransferGraphInputNodeInfo& from);
  void MergeFrom(const GraphTransferGraphInputNodeInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferGraphInputNodeInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferGraphInputNodeInfo";
  }
  protected:
  explicit GraphTransferGraphInputNodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kNameFieldNumber = 1,
    kDtypeFieldNumber = 3,
  };
  // repeated int64 shape = 2;
  int shape_size() const;
  void clear_shape();
  ::PROTOBUF_NAMESPACE_ID::int64 shape(int index) const;
  void set_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_shape(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_shape();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .tensorflow.DataType dtype = 3;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferGraphInputNodeInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > shape_;
  mutable std::atomic<int> _shape_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  int dtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferGraphOutputNodeInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferGraphOutputNodeInfo) */ {
 public:
  GraphTransferGraphOutputNodeInfo();
  virtual ~GraphTransferGraphOutputNodeInfo();

  GraphTransferGraphOutputNodeInfo(const GraphTransferGraphOutputNodeInfo& from);
  GraphTransferGraphOutputNodeInfo(GraphTransferGraphOutputNodeInfo&& from) noexcept
    : GraphTransferGraphOutputNodeInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferGraphOutputNodeInfo& operator=(const GraphTransferGraphOutputNodeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferGraphOutputNodeInfo& operator=(GraphTransferGraphOutputNodeInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphTransferGraphOutputNodeInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphTransferGraphOutputNodeInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferGraphOutputNodeInfo*>(
               &_GraphTransferGraphOutputNodeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(GraphTransferGraphOutputNodeInfo& a, GraphTransferGraphOutputNodeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferGraphOutputNodeInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferGraphOutputNodeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphTransferGraphOutputNodeInfo* New() const final {
    return CreateMaybeMessage<GraphTransferGraphOutputNodeInfo>(nullptr);
  }

  GraphTransferGraphOutputNodeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphTransferGraphOutputNodeInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphTransferGraphOutputNodeInfo& from);
  void MergeFrom(const GraphTransferGraphOutputNodeInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferGraphOutputNodeInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferGraphOutputNodeInfo";
  }
  protected:
  explicit GraphTransferGraphOutputNodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kNameFieldNumber = 1,
    kDtypeFieldNumber = 3,
  };
  // repeated int64 shape = 2;
  int shape_size() const;
  void clear_shape();
  ::PROTOBUF_NAMESPACE_ID::int64 shape(int index) const;
  void set_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_shape(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_shape();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .tensorflow.DataType dtype = 3;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferGraphOutputNodeInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > shape_;
  mutable std::atomic<int> _shape_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  int dtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferInfo) */ {
 public:
  GraphTransferInfo();
  virtual ~GraphTransferInfo();

  GraphTransferInfo(const GraphTransferInfo& from);
  GraphTransferInfo(GraphTransferInfo&& from) noexcept
    : GraphTransferInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferInfo& operator=(const GraphTransferInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferInfo& operator=(GraphTransferInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GraphTransferInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphTransferInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferInfo*>(
               &_GraphTransferInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(GraphTransferInfo& a, GraphTransferInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GraphTransferInfo* New() const final {
    return CreateMaybeMessage<GraphTransferInfo>(nullptr);
  }

  GraphTransferInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GraphTransferInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GraphTransferInfo& from);
  void MergeFrom(const GraphTransferInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferInfo";
  }
  protected:
  explicit GraphTransferInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef GraphTransferInfo_Destination Destination;
  static constexpr Destination NOP =
    GraphTransferInfo_Destination_NOP;
  static constexpr Destination HEXAGON =
    GraphTransferInfo_Destination_HEXAGON;
  static inline bool Destination_IsValid(int value) {
    return GraphTransferInfo_Destination_IsValid(value);
  }
  static constexpr Destination Destination_MIN =
    GraphTransferInfo_Destination_Destination_MIN;
  static constexpr Destination Destination_MAX =
    GraphTransferInfo_Destination_Destination_MAX;
  static constexpr int Destination_ARRAYSIZE =
    GraphTransferInfo_Destination_Destination_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Destination_descriptor() {
    return GraphTransferInfo_Destination_descriptor();
  }
  template<typename T>
  static inline const std::string& Destination_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Destination>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Destination_Name.");
    return GraphTransferInfo_Destination_Name(enum_t_value);
  }
  static inline bool Destination_Parse(const std::string& name,
      Destination* value) {
    return GraphTransferInfo_Destination_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kNodeInfoFieldNumber = 1,
    kConstNodeInfoFieldNumber = 2,
    kNodeInputInfoFieldNumber = 3,
    kNodeOutputInfoFieldNumber = 4,
    kGraphInputNodeInfoFieldNumber = 5,
    kGraphOutputNodeInfoFieldNumber = 6,
    kDestinationFieldNumber = 7,
  };
  // repeated .tensorflow.GraphTransferNodeInfo node_info = 1;
  int node_info_size() const;
  void clear_node_info();
  ::tensorflow::GraphTransferNodeInfo* mutable_node_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInfo >*
      mutable_node_info();
  const ::tensorflow::GraphTransferNodeInfo& node_info(int index) const;
  ::tensorflow::GraphTransferNodeInfo* add_node_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInfo >&
      node_info() const;

  // repeated .tensorflow.GraphTransferConstNodeInfo const_node_info = 2;
  int const_node_info_size() const;
  void clear_const_node_info();
  ::tensorflow::GraphTransferConstNodeInfo* mutable_const_node_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferConstNodeInfo >*
      mutable_const_node_info();
  const ::tensorflow::GraphTransferConstNodeInfo& const_node_info(int index) const;
  ::tensorflow::GraphTransferConstNodeInfo* add_const_node_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferConstNodeInfo >&
      const_node_info() const;

  // repeated .tensorflow.GraphTransferNodeInputInfo node_input_info = 3;
  int node_input_info_size() const;
  void clear_node_input_info();
  ::tensorflow::GraphTransferNodeInputInfo* mutable_node_input_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInputInfo >*
      mutable_node_input_info();
  const ::tensorflow::GraphTransferNodeInputInfo& node_input_info(int index) const;
  ::tensorflow::GraphTransferNodeInputInfo* add_node_input_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInputInfo >&
      node_input_info() const;

  // repeated .tensorflow.GraphTransferNodeOutputInfo node_output_info = 4;
  int node_output_info_size() const;
  void clear_node_output_info();
  ::tensorflow::GraphTransferNodeOutputInfo* mutable_node_output_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeOutputInfo >*
      mutable_node_output_info();
  const ::tensorflow::GraphTransferNodeOutputInfo& node_output_info(int index) const;
  ::tensorflow::GraphTransferNodeOutputInfo* add_node_output_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeOutputInfo >&
      node_output_info() const;

  // repeated .tensorflow.GraphTransferGraphInputNodeInfo graph_input_node_info = 5;
  int graph_input_node_info_size() const;
  void clear_graph_input_node_info();
  ::tensorflow::GraphTransferGraphInputNodeInfo* mutable_graph_input_node_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphInputNodeInfo >*
      mutable_graph_input_node_info();
  const ::tensorflow::GraphTransferGraphInputNodeInfo& graph_input_node_info(int index) const;
  ::tensorflow::GraphTransferGraphInputNodeInfo* add_graph_input_node_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphInputNodeInfo >&
      graph_input_node_info() const;

  // repeated .tensorflow.GraphTransferGraphOutputNodeInfo graph_output_node_info = 6;
  int graph_output_node_info_size() const;
  void clear_graph_output_node_info();
  ::tensorflow::GraphTransferGraphOutputNodeInfo* mutable_graph_output_node_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphOutputNodeInfo >*
      mutable_graph_output_node_info();
  const ::tensorflow::GraphTransferGraphOutputNodeInfo& graph_output_node_info(int index) const;
  ::tensorflow::GraphTransferGraphOutputNodeInfo* add_graph_output_node_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphOutputNodeInfo >&
      graph_output_node_info() const;

  // .tensorflow.GraphTransferInfo.Destination destination = 7;
  void clear_destination();
  ::tensorflow::GraphTransferInfo_Destination destination() const;
  void set_destination(::tensorflow::GraphTransferInfo_Destination value);

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInfo > node_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferConstNodeInfo > const_node_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInputInfo > node_input_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeOutputInfo > node_output_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphInputNodeInfo > graph_input_node_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphOutputNodeInfo > graph_output_node_info_;
  int destination_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GraphTransferNodeInput

// int32 node_id = 1;
inline void GraphTransferNodeInput::clear_node_id() {
  node_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferNodeInput::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInput.node_id)
  return node_id_;
}
inline void GraphTransferNodeInput::set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInput.node_id)
}

// int32 output_port = 2;
inline void GraphTransferNodeInput::clear_output_port() {
  output_port_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferNodeInput::output_port() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInput.output_port)
  return output_port_;
}
inline void GraphTransferNodeInput::set_output_port(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  output_port_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInput.output_port)
}

// -------------------------------------------------------------------

// GraphTransferNodeInfo

// string name = 1;
inline void GraphTransferNodeInfo::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphTransferNodeInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.name)
  return name_.Get();
}
inline void GraphTransferNodeInfo::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.name)
}
inline void GraphTransferNodeInfo::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphTransferNodeInfo.name)
}
inline void GraphTransferNodeInfo::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphTransferNodeInfo.name)
}
inline void GraphTransferNodeInfo::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphTransferNodeInfo.name)
}
inline std::string* GraphTransferNodeInfo::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferNodeInfo.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphTransferNodeInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferNodeInfo.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphTransferNodeInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferNodeInfo.name)
}
inline std::string* GraphTransferNodeInfo::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphTransferNodeInfo.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphTransferNodeInfo::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphTransferNodeInfo.name)
}

// int32 node_id = 2;
inline void GraphTransferNodeInfo::clear_node_id() {
  node_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferNodeInfo::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.node_id)
  return node_id_;
}
inline void GraphTransferNodeInfo::set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.node_id)
}

// string type_name = 3;
inline void GraphTransferNodeInfo::clear_type_name() {
  type_name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphTransferNodeInfo::type_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.type_name)
  return type_name_.Get();
}
inline void GraphTransferNodeInfo::set_type_name(const std::string& value) {
  
  type_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.type_name)
}
inline void GraphTransferNodeInfo::set_type_name(std::string&& value) {
  
  type_name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphTransferNodeInfo.type_name)
}
inline void GraphTransferNodeInfo::set_type_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  type_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphTransferNodeInfo.type_name)
}
inline void GraphTransferNodeInfo::set_type_name(const char* value,
    size_t size) {
  
  type_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphTransferNodeInfo.type_name)
}
inline std::string* GraphTransferNodeInfo::mutable_type_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferNodeInfo.type_name)
  return type_name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphTransferNodeInfo::release_type_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferNodeInfo.type_name)
  
  return type_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphTransferNodeInfo::set_allocated_type_name(std::string* type_name) {
  if (type_name != nullptr) {
    
  } else {
    
  }
  type_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferNodeInfo.type_name)
}
inline std::string* GraphTransferNodeInfo::unsafe_arena_release_type_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphTransferNodeInfo.type_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return type_name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphTransferNodeInfo::unsafe_arena_set_allocated_type_name(
    std::string* type_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (type_name != nullptr) {
    
  } else {
    
  }
  type_name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      type_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphTransferNodeInfo.type_name)
}

// int32 soc_op_id = 4;
inline void GraphTransferNodeInfo::clear_soc_op_id() {
  soc_op_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferNodeInfo::soc_op_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.soc_op_id)
  return soc_op_id_;
}
inline void GraphTransferNodeInfo::set_soc_op_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  soc_op_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.soc_op_id)
}

// int32 padding_id = 5;
inline void GraphTransferNodeInfo::clear_padding_id() {
  padding_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferNodeInfo::padding_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.padding_id)
  return padding_id_;
}
inline void GraphTransferNodeInfo::set_padding_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  padding_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.padding_id)
}

// int32 input_count = 6;
inline void GraphTransferNodeInfo::clear_input_count() {
  input_count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferNodeInfo::input_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.input_count)
  return input_count_;
}
inline void GraphTransferNodeInfo::set_input_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  input_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.input_count)
}

// int32 output_count = 7;
inline void GraphTransferNodeInfo::clear_output_count() {
  output_count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferNodeInfo::output_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.output_count)
  return output_count_;
}
inline void GraphTransferNodeInfo::set_output_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  output_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.output_count)
}

// -------------------------------------------------------------------

// GraphTransferConstNodeInfo

// string name = 1;
inline void GraphTransferConstNodeInfo::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphTransferConstNodeInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferConstNodeInfo.name)
  return name_.Get();
}
inline void GraphTransferConstNodeInfo::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferConstNodeInfo.name)
}
inline void GraphTransferConstNodeInfo::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphTransferConstNodeInfo.name)
}
inline void GraphTransferConstNodeInfo::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphTransferConstNodeInfo.name)
}
inline void GraphTransferConstNodeInfo::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphTransferConstNodeInfo.name)
}
inline std::string* GraphTransferConstNodeInfo::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferConstNodeInfo.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphTransferConstNodeInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferConstNodeInfo.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphTransferConstNodeInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferConstNodeInfo.name)
}
inline std::string* GraphTransferConstNodeInfo::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphTransferConstNodeInfo.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphTransferConstNodeInfo::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphTransferConstNodeInfo.name)
}

// int32 node_id = 2;
inline void GraphTransferConstNodeInfo::clear_node_id() {
  node_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferConstNodeInfo::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferConstNodeInfo.node_id)
  return node_id_;
}
inline void GraphTransferConstNodeInfo::set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferConstNodeInfo.node_id)
}

// repeated int64 shape = 3;
inline int GraphTransferConstNodeInfo::shape_size() const {
  return shape_.size();
}
inline void GraphTransferConstNodeInfo::clear_shape() {
  shape_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphTransferConstNodeInfo::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferConstNodeInfo.shape)
  return shape_.Get(index);
}
inline void GraphTransferConstNodeInfo::set_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferConstNodeInfo.shape)
}
inline void GraphTransferConstNodeInfo::add_shape(::PROTOBUF_NAMESPACE_ID::int64 value) {
  shape_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferConstNodeInfo.shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
GraphTransferConstNodeInfo::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferConstNodeInfo.shape)
  return shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
GraphTransferConstNodeInfo::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferConstNodeInfo.shape)
  return &shape_;
}

// bytes data = 4;
inline void GraphTransferConstNodeInfo::clear_data() {
  data_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphTransferConstNodeInfo::data() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferConstNodeInfo.data)
  return data_.Get();
}
inline void GraphTransferConstNodeInfo::set_data(const std::string& value) {
  
  data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferConstNodeInfo.data)
}
inline void GraphTransferConstNodeInfo::set_data(std::string&& value) {
  
  data_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphTransferConstNodeInfo.data)
}
inline void GraphTransferConstNodeInfo::set_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphTransferConstNodeInfo.data)
}
inline void GraphTransferConstNodeInfo::set_data(const void* value,
    size_t size) {
  
  data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphTransferConstNodeInfo.data)
}
inline std::string* GraphTransferConstNodeInfo::mutable_data() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferConstNodeInfo.data)
  return data_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphTransferConstNodeInfo::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferConstNodeInfo.data)
  
  return data_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphTransferConstNodeInfo::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferConstNodeInfo.data)
}
inline std::string* GraphTransferConstNodeInfo::unsafe_arena_release_data() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphTransferConstNodeInfo.data)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return data_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphTransferConstNodeInfo::unsafe_arena_set_allocated_data(
    std::string* data) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (data != nullptr) {
    
  } else {
    
  }
  data_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      data, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphTransferConstNodeInfo.data)
}

// .tensorflow.DataType dtype = 5;
inline void GraphTransferConstNodeInfo::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType GraphTransferConstNodeInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferConstNodeInfo.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void GraphTransferConstNodeInfo::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferConstNodeInfo.dtype)
}

// -------------------------------------------------------------------

// GraphTransferNodeInputInfo

// int32 node_id = 1;
inline void GraphTransferNodeInputInfo::clear_node_id() {
  node_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferNodeInputInfo::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInputInfo.node_id)
  return node_id_;
}
inline void GraphTransferNodeInputInfo::set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInputInfo.node_id)
}

// repeated .tensorflow.GraphTransferNodeInput node_input = 2;
inline int GraphTransferNodeInputInfo::node_input_size() const {
  return node_input_.size();
}
inline void GraphTransferNodeInputInfo::clear_node_input() {
  node_input_.Clear();
}
inline ::tensorflow::GraphTransferNodeInput* GraphTransferNodeInputInfo::mutable_node_input(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferNodeInputInfo.node_input)
  return node_input_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInput >*
GraphTransferNodeInputInfo::mutable_node_input() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferNodeInputInfo.node_input)
  return &node_input_;
}
inline const ::tensorflow::GraphTransferNodeInput& GraphTransferNodeInputInfo::node_input(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInputInfo.node_input)
  return node_input_.Get(index);
}
inline ::tensorflow::GraphTransferNodeInput* GraphTransferNodeInputInfo::add_node_input() {
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferNodeInputInfo.node_input)
  return node_input_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInput >&
GraphTransferNodeInputInfo::node_input() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferNodeInputInfo.node_input)
  return node_input_;
}

// -------------------------------------------------------------------

// GraphTransferNodeOutputInfo

// int32 node_id = 1;
inline void GraphTransferNodeOutputInfo::clear_node_id() {
  node_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferNodeOutputInfo::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeOutputInfo.node_id)
  return node_id_;
}
inline void GraphTransferNodeOutputInfo::set_node_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeOutputInfo.node_id)
}

// repeated int32 max_byte_size = 2;
inline int GraphTransferNodeOutputInfo::max_byte_size_size() const {
  return max_byte_size_.size();
}
inline void GraphTransferNodeOutputInfo::clear_max_byte_size() {
  max_byte_size_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GraphTransferNodeOutputInfo::max_byte_size(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeOutputInfo.max_byte_size)
  return max_byte_size_.Get(index);
}
inline void GraphTransferNodeOutputInfo::set_max_byte_size(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  max_byte_size_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeOutputInfo.max_byte_size)
}
inline void GraphTransferNodeOutputInfo::add_max_byte_size(::PROTOBUF_NAMESPACE_ID::int32 value) {
  max_byte_size_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferNodeOutputInfo.max_byte_size)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
GraphTransferNodeOutputInfo::max_byte_size() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferNodeOutputInfo.max_byte_size)
  return max_byte_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
GraphTransferNodeOutputInfo::mutable_max_byte_size() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferNodeOutputInfo.max_byte_size)
  return &max_byte_size_;
}

// -------------------------------------------------------------------

// GraphTransferGraphInputNodeInfo

// string name = 1;
inline void GraphTransferGraphInputNodeInfo::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphTransferGraphInputNodeInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphInputNodeInfo.name)
  return name_.Get();
}
inline void GraphTransferGraphInputNodeInfo::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphInputNodeInfo.name)
}
inline void GraphTransferGraphInputNodeInfo::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphTransferGraphInputNodeInfo.name)
}
inline void GraphTransferGraphInputNodeInfo::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphTransferGraphInputNodeInfo.name)
}
inline void GraphTransferGraphInputNodeInfo::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphTransferGraphInputNodeInfo.name)
}
inline std::string* GraphTransferGraphInputNodeInfo::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferGraphInputNodeInfo.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphTransferGraphInputNodeInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferGraphInputNodeInfo.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphTransferGraphInputNodeInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferGraphInputNodeInfo.name)
}
inline std::string* GraphTransferGraphInputNodeInfo::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphTransferGraphInputNodeInfo.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphTransferGraphInputNodeInfo::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphTransferGraphInputNodeInfo.name)
}

// repeated int64 shape = 2;
inline int GraphTransferGraphInputNodeInfo::shape_size() const {
  return shape_.size();
}
inline void GraphTransferGraphInputNodeInfo::clear_shape() {
  shape_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphTransferGraphInputNodeInfo::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphInputNodeInfo.shape)
  return shape_.Get(index);
}
inline void GraphTransferGraphInputNodeInfo::set_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphInputNodeInfo.shape)
}
inline void GraphTransferGraphInputNodeInfo::add_shape(::PROTOBUF_NAMESPACE_ID::int64 value) {
  shape_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferGraphInputNodeInfo.shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
GraphTransferGraphInputNodeInfo::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferGraphInputNodeInfo.shape)
  return shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
GraphTransferGraphInputNodeInfo::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferGraphInputNodeInfo.shape)
  return &shape_;
}

// .tensorflow.DataType dtype = 3;
inline void GraphTransferGraphInputNodeInfo::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType GraphTransferGraphInputNodeInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphInputNodeInfo.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void GraphTransferGraphInputNodeInfo::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphInputNodeInfo.dtype)
}

// -------------------------------------------------------------------

// GraphTransferGraphOutputNodeInfo

// string name = 1;
inline void GraphTransferGraphOutputNodeInfo::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& GraphTransferGraphOutputNodeInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphOutputNodeInfo.name)
  return name_.Get();
}
inline void GraphTransferGraphOutputNodeInfo::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphOutputNodeInfo.name)
}
inline void GraphTransferGraphOutputNodeInfo::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.GraphTransferGraphOutputNodeInfo.name)
}
inline void GraphTransferGraphOutputNodeInfo::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphTransferGraphOutputNodeInfo.name)
}
inline void GraphTransferGraphOutputNodeInfo::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphTransferGraphOutputNodeInfo.name)
}
inline std::string* GraphTransferGraphOutputNodeInfo::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferGraphOutputNodeInfo.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* GraphTransferGraphOutputNodeInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferGraphOutputNodeInfo.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void GraphTransferGraphOutputNodeInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferGraphOutputNodeInfo.name)
}
inline std::string* GraphTransferGraphOutputNodeInfo::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.GraphTransferGraphOutputNodeInfo.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void GraphTransferGraphOutputNodeInfo::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphTransferGraphOutputNodeInfo.name)
}

// repeated int64 shape = 2;
inline int GraphTransferGraphOutputNodeInfo::shape_size() const {
  return shape_.size();
}
inline void GraphTransferGraphOutputNodeInfo::clear_shape() {
  shape_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GraphTransferGraphOutputNodeInfo::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphOutputNodeInfo.shape)
  return shape_.Get(index);
}
inline void GraphTransferGraphOutputNodeInfo::set_shape(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphOutputNodeInfo.shape)
}
inline void GraphTransferGraphOutputNodeInfo::add_shape(::PROTOBUF_NAMESPACE_ID::int64 value) {
  shape_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferGraphOutputNodeInfo.shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
GraphTransferGraphOutputNodeInfo::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferGraphOutputNodeInfo.shape)
  return shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
GraphTransferGraphOutputNodeInfo::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferGraphOutputNodeInfo.shape)
  return &shape_;
}

// .tensorflow.DataType dtype = 3;
inline void GraphTransferGraphOutputNodeInfo::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType GraphTransferGraphOutputNodeInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphOutputNodeInfo.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void GraphTransferGraphOutputNodeInfo::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphOutputNodeInfo.dtype)
}

// -------------------------------------------------------------------

// GraphTransferInfo

// repeated .tensorflow.GraphTransferNodeInfo node_info = 1;
inline int GraphTransferInfo::node_info_size() const {
  return node_info_.size();
}
inline void GraphTransferInfo::clear_node_info() {
  node_info_.Clear();
}
inline ::tensorflow::GraphTransferNodeInfo* GraphTransferInfo::mutable_node_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.node_info)
  return node_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInfo >*
GraphTransferInfo::mutable_node_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.node_info)
  return &node_info_;
}
inline const ::tensorflow::GraphTransferNodeInfo& GraphTransferInfo::node_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.node_info)
  return node_info_.Get(index);
}
inline ::tensorflow::GraphTransferNodeInfo* GraphTransferInfo::add_node_info() {
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.node_info)
  return node_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInfo >&
GraphTransferInfo::node_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.node_info)
  return node_info_;
}

// repeated .tensorflow.GraphTransferConstNodeInfo const_node_info = 2;
inline int GraphTransferInfo::const_node_info_size() const {
  return const_node_info_.size();
}
inline void GraphTransferInfo::clear_const_node_info() {
  const_node_info_.Clear();
}
inline ::tensorflow::GraphTransferConstNodeInfo* GraphTransferInfo::mutable_const_node_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.const_node_info)
  return const_node_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferConstNodeInfo >*
GraphTransferInfo::mutable_const_node_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.const_node_info)
  return &const_node_info_;
}
inline const ::tensorflow::GraphTransferConstNodeInfo& GraphTransferInfo::const_node_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.const_node_info)
  return const_node_info_.Get(index);
}
inline ::tensorflow::GraphTransferConstNodeInfo* GraphTransferInfo::add_const_node_info() {
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.const_node_info)
  return const_node_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferConstNodeInfo >&
GraphTransferInfo::const_node_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.const_node_info)
  return const_node_info_;
}

// repeated .tensorflow.GraphTransferNodeInputInfo node_input_info = 3;
inline int GraphTransferInfo::node_input_info_size() const {
  return node_input_info_.size();
}
inline void GraphTransferInfo::clear_node_input_info() {
  node_input_info_.Clear();
}
inline ::tensorflow::GraphTransferNodeInputInfo* GraphTransferInfo::mutable_node_input_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.node_input_info)
  return node_input_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInputInfo >*
GraphTransferInfo::mutable_node_input_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.node_input_info)
  return &node_input_info_;
}
inline const ::tensorflow::GraphTransferNodeInputInfo& GraphTransferInfo::node_input_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.node_input_info)
  return node_input_info_.Get(index);
}
inline ::tensorflow::GraphTransferNodeInputInfo* GraphTransferInfo::add_node_input_info() {
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.node_input_info)
  return node_input_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInputInfo >&
GraphTransferInfo::node_input_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.node_input_info)
  return node_input_info_;
}

// repeated .tensorflow.GraphTransferNodeOutputInfo node_output_info = 4;
inline int GraphTransferInfo::node_output_info_size() const {
  return node_output_info_.size();
}
inline void GraphTransferInfo::clear_node_output_info() {
  node_output_info_.Clear();
}
inline ::tensorflow::GraphTransferNodeOutputInfo* GraphTransferInfo::mutable_node_output_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.node_output_info)
  return node_output_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeOutputInfo >*
GraphTransferInfo::mutable_node_output_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.node_output_info)
  return &node_output_info_;
}
inline const ::tensorflow::GraphTransferNodeOutputInfo& GraphTransferInfo::node_output_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.node_output_info)
  return node_output_info_.Get(index);
}
inline ::tensorflow::GraphTransferNodeOutputInfo* GraphTransferInfo::add_node_output_info() {
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.node_output_info)
  return node_output_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeOutputInfo >&
GraphTransferInfo::node_output_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.node_output_info)
  return node_output_info_;
}

// repeated .tensorflow.GraphTransferGraphInputNodeInfo graph_input_node_info = 5;
inline int GraphTransferInfo::graph_input_node_info_size() const {
  return graph_input_node_info_.size();
}
inline void GraphTransferInfo::clear_graph_input_node_info() {
  graph_input_node_info_.Clear();
}
inline ::tensorflow::GraphTransferGraphInputNodeInfo* GraphTransferInfo::mutable_graph_input_node_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.graph_input_node_info)
  return graph_input_node_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphInputNodeInfo >*
GraphTransferInfo::mutable_graph_input_node_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.graph_input_node_info)
  return &graph_input_node_info_;
}
inline const ::tensorflow::GraphTransferGraphInputNodeInfo& GraphTransferInfo::graph_input_node_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.graph_input_node_info)
  return graph_input_node_info_.Get(index);
}
inline ::tensorflow::GraphTransferGraphInputNodeInfo* GraphTransferInfo::add_graph_input_node_info() {
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.graph_input_node_info)
  return graph_input_node_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphInputNodeInfo >&
GraphTransferInfo::graph_input_node_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.graph_input_node_info)
  return graph_input_node_info_;
}

// repeated .tensorflow.GraphTransferGraphOutputNodeInfo graph_output_node_info = 6;
inline int GraphTransferInfo::graph_output_node_info_size() const {
  return graph_output_node_info_.size();
}
inline void GraphTransferInfo::clear_graph_output_node_info() {
  graph_output_node_info_.Clear();
}
inline ::tensorflow::GraphTransferGraphOutputNodeInfo* GraphTransferInfo::mutable_graph_output_node_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.graph_output_node_info)
  return graph_output_node_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphOutputNodeInfo >*
GraphTransferInfo::mutable_graph_output_node_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.graph_output_node_info)
  return &graph_output_node_info_;
}
inline const ::tensorflow::GraphTransferGraphOutputNodeInfo& GraphTransferInfo::graph_output_node_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.graph_output_node_info)
  return graph_output_node_info_.Get(index);
}
inline ::tensorflow::GraphTransferGraphOutputNodeInfo* GraphTransferInfo::add_graph_output_node_info() {
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.graph_output_node_info)
  return graph_output_node_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphOutputNodeInfo >&
GraphTransferInfo::graph_output_node_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.graph_output_node_info)
  return graph_output_node_info_;
}

// .tensorflow.GraphTransferInfo.Destination destination = 7;
inline void GraphTransferInfo::clear_destination() {
  destination_ = 0;
}
inline ::tensorflow::GraphTransferInfo_Destination GraphTransferInfo::destination() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.destination)
  return static_cast< ::tensorflow::GraphTransferInfo_Destination >(destination_);
}
inline void GraphTransferInfo::set_destination(::tensorflow::GraphTransferInfo_Destination value) {
  
  destination_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferInfo.destination)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::GraphTransferInfo_Destination> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::GraphTransferInfo_Destination>() {
  return ::tensorflow::GraphTransferInfo_Destination_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto
