# 时域统计特征分析
import os
import tensorflow as tf
import numpy as np
from scipy import signal
from scipy import stats
from scipy.signal import find_peaks

from apps.analysis.common.ecg_signal_processing import resample
from apps.utils.logger_helper import Logger


def is_noise(ecg_data, sampling_rate):
    """
    识别是否为噪音 - 改进版
    :param ecg_data: ECG信号数据
    :param sampling_rate: 采样率
    :return: 1 正常信号或可分析的心律异常，-1 技术性噪音
    """
    # 先进行原始信号噪音检测，快速排除明显的技术性问题
    if len(ecg_data) == 0:
        Logger().info("检测到噪音：空数据")
        return -1, "空数据"

    # 计算原始信号SNR
    signal_power = np.mean(np.square(ecg_data))
    # 估计噪声功率（可用高通滤波器提取高频成分）
    fc = 40.0  # 高通滤波截止频率
    w = fc / (sampling_rate / 2)
    b, a = signal.butter(3, w, 'high')
    noise = signal.filtfilt(b, a, ecg_data)
    noise_power = np.mean(np.square(noise))

    # 计算SNR(dB)
    if noise_power > 0:
        snr = 10 * np.log10(signal_power / noise_power)
    else:
        snr = 100  # 防止除零

    # 如果SNR特别低，直接判断为噪音
    if snr < 1.5:
        Logger().info(f"检测到噪音：信噪比过低 (SNR={snr:.2f}dB)")
        return -1, f"信噪比过低 (SNR={snr:.2f}dB)，原始信号为噪音"

    # 原始信号基线评估
    fc = 0.5
    w = fc / (sampling_rate / 2)
    b, a = signal.butter(2, w, 'low')
    baseline = signal.filtfilt(b, a, ecg_data)
    baseline_var = np.std(baseline)

    # 如果基线漂移极其严重
    if baseline_var > 0.4 * np.std(ecg_data):
        Logger().info("检测到噪音：原始信号基线漂移极其严重")
        return -1, "原始信号基线漂移极其严重，可能是电极接触不良"

    # 进行QRS波群检测和分析 - 这是关键改进
    # 使用改进的Pan-Tompkins算法检测R波

    # 1. 带通滤波 (5-15Hz)增强QRS波
    w1, w2 = 5.0 / (sampling_rate / 2), 15.0 / (sampling_rate / 2)
    b, a = signal.butter(3, [w1, w2], 'band')
    filtered_ecg = signal.filtfilt(b, a, ecg_data)

    # 2. 求导突出QRS斜率变化
    diff_ecg = np.diff(filtered_ecg)

    # 3. 平方强调高频成分
    squared_ecg = diff_ecg ** 2

    # 4. 移动平均积分窗口
    window_size = int(0.15 * sampling_rate)  # 约150ms窗口
    if window_size % 2 == 0:
        window_size += 1  # 确保窗口大小为奇数

    integrated_ecg = np.convolve(squared_ecg, np.ones(window_size) / window_size, mode='same')

    # 5. 自适应阈值检测R波峰值
    threshold = 0.3 * np.max(integrated_ecg)
    min_distance = int(0.2 * sampling_rate)  # 最小RR间隔200ms (300bpm)
    peaks, _ = find_peaks(integrated_ecg, height=threshold, distance=min_distance)

    # 如果检测不到任何R波，很可能是严重噪音
    if len(peaks) < 3:
        Logger().info("检测到噪音：无法检测到足够的QRS波群")
        return -1, "无法检测到足够的QRS波群，可能是严重噪音"

    # 计算RR间隔及其变异性
    rr_intervals = np.diff(peaks) / sampling_rate * 1000  # 毫秒

    # 排除极端异常值 (>3000ms 或 <200ms)
    valid_rr = rr_intervals[(rr_intervals > 200) & (rr_intervals < 3000)]

    # 如果有效RR间隔太少，可能是噪音
    if len(valid_rr) < 5:
        Logger().info("检测到噪音：有效QRS波群过少")
        return -1, "检测到的有效QRS波群过少，无法进行可靠分析"

    # 分析RR间隔的统计特征 - 区分噪音和心律失常的关键
    rr_mean = np.mean(valid_rr)
    rr_std = np.std(valid_rr)
    rr_cv = rr_std / rr_mean if rr_mean > 0 else 0  # 变异系数

    # 新增: 检测窦性心律 - 如果有五个连续正常R波且心率在60-100之间
    # 计算当前心率(bpm) = 60000 / RR间隔(ms)
    heart_rate = 60000 / rr_mean if rr_mean > 0 else 0

    # 检查是否有至少五个连续的RR间隔在正常范围内
    # 定义正常RR间隔范围：对应60-100bpm的范围是600-1000ms
    normal_rr_ranges = (valid_rr >= 600) & (valid_rr <= 1000)

    # 寻找连续正常RR间隔
    consecutive_count = 0
    max_consecutive = 0
    for is_normal in normal_rr_ranges:
        if is_normal:
            consecutive_count += 1
            max_consecutive = max(max_consecutive, consecutive_count)
        else:
            consecutive_count = 0

    # 如果有五个连续正常RR间隔且平均心率在60-100之间，判定为窦性心律
    if max_consecutive >= 5 and 60 <= heart_rate <= 100:
        Logger().info(f"检测到窦性心律: 心率={heart_rate:.1f}bpm，有{max_consecutive}个连续正常RR间隔")
        return 1, f"窦性心律: 心率{heart_rate:.1f}bpm"

    # 噪音特征1: 极度不规则的RR间隔 (超出生理范围的变异)
    if rr_cv > 0.8:  # 噪音通常变异系数极高
        # 但需要排除房颤的可能 - 房颤变异性大但有一定规律
        # 房颤特征: 高变异性但保持在生理范围内，且功率谱有特征

        # 分析RR间隔的频域特征
        if len(valid_rr) > 10:  # 需要足够样本进行频谱分析
            # 计算RR间隔的功率谱
            f, Pxx = signal.welch(valid_rr, fs=1.0, nperseg=min(len(valid_rr), 10))

            # 房颤的特征：低频成分(0.04-0.15Hz)显著
            low_freq_power = np.sum(Pxx[(f >= 0.04) & (f <= 0.15)])
            total_power = np.sum(Pxx)

            # 如果低频功率占比显著且RR间隔分布接近正态，可能是房颤而非噪音
            if low_freq_power / total_power > 0.3 and stats.normaltest(valid_rr)[1] < 0.05:
                # 可能是房颤，不应判为噪音
                pass
            else:
                Logger().info("检测到噪音：RR间隔极度不规则且不符合房颤特征")
                return -1, "RR间隔极度不规则，可能是严重噪音干扰"

    # 噪音特征2: 检查相邻心搏波形间的相似性
    # 真实ECG信号即使在异常时也应保持一定的波形相似性
    # 提取每个QRS波形进行比较
    qrs_width = int(0.12 * sampling_rate)  # 典型QRS宽度约120ms
    qrs_segments = []

    for peak in peaks:
        if peak - qrs_width // 2 >= 0 and peak + qrs_width // 2 < len(filtered_ecg):
            segment = filtered_ecg[peak - qrs_width // 2:peak + qrs_width // 2]
            # 归一化段落以便比较
            segment = (segment - np.min(segment)) / (np.max(segment) - np.min(segment) + 1e-6)
            qrs_segments.append(segment)

    # 计算相邻QRS波形之间的相关性
    correlations = []
    if len(qrs_segments) > 3:
        for i in range(len(qrs_segments) - 1):
            corr = np.corrcoef(qrs_segments[i], qrs_segments[i + 1])[0, 1]
            correlations.append(corr)

        avg_correlation = np.mean(correlations)

        # 噪音通常表现为相邻心搏间相关性极低
        # 但需考虑如室早等可能导致的波形变化

        # 分析相关性分布 - 噪音会有随机相关性，而室早等会有模式
        corr_std = np.std(correlations)

        # 如果相关性均值极低且标准差小，很可能是噪音
        if avg_correlation < 0.3 and corr_std < 0.15:
            Logger().info(f"检测到噪音：心搏波形相似性极低 (相关系数={avg_correlation:.2f})")
            return -1, f"心搏波形间缺乏一致性，平均相关系数仅{avg_correlation:.2f}，可能是噪音"

    # 噪音特征3: 基线漂移检测 - 仅检测极端基线问题
    # 低通滤波提取基线
    fc = 0.5
    w = fc / (sampling_rate / 2)
    b, a = signal.butter(2, w, 'low')
    baseline = signal.filtfilt(b, a, ecg_data)

    # 仅当基线漂移极其严重时才判为噪音
    if np.std(baseline) > 0.5:  # 提高阈值，允许一定的基线漂移
        # 检查是否基线漂移随呼吸变化(正常)还是无规律跳变(异常)
        # 呼吸频率通常在0.1-0.4Hz之间
        f, Pxx = signal.welch(baseline, fs=sampling_rate, nperseg=sampling_rate * 4)
        resp_power = np.sum(Pxx[(f >= 0.1) & (f <= 0.4)])
        total_power = np.sum(Pxx)

        # 如果基线变化不主要由呼吸引起，可能是接触不良
        if resp_power / total_power < 0.3:
            Logger().info("检测到噪音：基线漂移极其严重且不符合呼吸模式")
            return -1, "基线漂移极其严重且不规则，可能是电极接触不良"

    # 噪音特征4: 极端高频噪音检测
    # 特别关注50/60Hz工频干扰
    f, Pxx = signal.welch(ecg_data, fs=sampling_rate, nperseg=sampling_rate * 2)
    power_line_freq = 50  # 或60，取决于地区
    line_idx = np.argmin(np.abs(f - power_line_freq))

    # 检查50/60Hz附近的功率
    line_power = np.sum(Pxx[max(0, line_idx - 1):min(len(Pxx), line_idx + 2)])
    total_power = np.sum(Pxx)

    # 如果工频干扰特别强烈
    if line_power / total_power > 0.4:
        Logger().info(f"检测到噪音：严重的工频干扰 ({power_line_freq}Hz)")
        return -1, f"严重的{power_line_freq}Hz工频干扰，建议检查设备接地"

    # 噪音特征5: 信号饱和检测 - 适用于设备或放大器问题
    # 检查信号是否频繁达到最大/最小值(剪切失真)
    range_percent = 0.05  # 最高/最低5%范围
    high_threshold = np.max(ecg_data) - (np.max(ecg_data) - np.min(ecg_data)) * range_percent
    low_threshold = np.min(ecg_data) + (np.max(ecg_data) - np.min(ecg_data)) * range_percent

    high_samples = np.sum(ecg_data > high_threshold)
    low_samples = np.sum(ecg_data < low_threshold)
    saturation_percent = (high_samples + low_samples) / len(ecg_data)

    if saturation_percent > 0.15:  # 如果超过15%的采样点接近极值
        Logger().info(f"检测到噪音：信号饱和 ({saturation_percent * 100:.1f}%)")
        return -1, f"信号饱和/剪切失真，约{saturation_percent * 100:.1f}%的采样点接近极值"

    # 如果通过所有噪音检测，最后使用神经网络模型作为备用
    # 但赋予前面的临床规则更高权重，因为它们更具可解释性
    ecg_data_resampled = resample(ecg_data, sampling_rate)

    current_file_path = os.path.abspath(__file__)
    current_dir = os.path.dirname(current_file_path)
    model_path = os.path.join(current_dir, 'model', 'noise_model.h5')
    model = tf.keras.models.load_model(model_path)

    predict = model.predict(np.expand_dims([ecg_data_resampled], axis=2))

    # 仅当模型高度确信是噪音时(可能性>90%)才将其作为噪音处理
    if predict[0][0] < 0.1:  # 假设正常信号预测值接近1
        Logger().info("深度学习模型强烈检测到噪音")
        return -1, "深度学习模型检测到典型的噪音模式"

    # 通过所有检查，认为是正常信号或可分析的异常心律
    return 1, "正常信号或可分析的心律波形"

