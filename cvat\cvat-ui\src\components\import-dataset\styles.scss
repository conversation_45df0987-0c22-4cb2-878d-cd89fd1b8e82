// Copyright (C) 2021-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-modal-import-dataset-option-item > .ant-select-item-option-content,
.cvat-modal-import-select .ant-select-selection-item {
    > span[role='img'] {
        color: $info-icon-color;
        margin-right: $grid-unit-size;
    }
}

.cvat-modal-import-header-question-icon {
    margin-left: $grid-unit-size;
    color: $text-color-secondary;
}

.cvat-switch-use-default-storage,
.cvat-modal-import-switch-use-default-storage,
.cvat-modal-import-switch-conv-mask-to-poly {
    display: table-cell;
}

.cvat-modal-import-switch-use-default-storage-container,
.cvat-modal-import-switch-conv-mask-to-poly-container {
    width: 100%;
}
