# Generated by Django 3.2.15 on 2022-08-09 12:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0057_auto_20220726_0926"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="labeledskeletonattributeval",
            name="skeleton",
        ),
        migrations.RemoveField(
            model_name="labeledskeletonattributeval",
            name="spec",
        ),
        migrations.RemoveField(
            model_name="trackedskeleton",
            name="label",
        ),
        migrations.RemoveField(
            model_name="trackedskeleton",
            name="shape",
        ),
        migrations.RemoveField(
            model_name="trackedskeletonattributeval",
            name="skeleton",
        ),
        migrations.RemoveField(
            model_name="trackedskeletonattributeval",
            name="spec",
        ),
        migrations.AddField(
            model_name="labeledshape",
            name="parent",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="elements",
                to="engine.labeledshape",
            ),
        ),
        migrations.AddField(
            model_name="labeledtrack",
            name="parent",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="elements",
                to="engine.labeledtrack",
            ),
        ),
        migrations.DeleteModel(
            name="LabeledSkeleton",
        ),
        migrations.DeleteModel(
            name="LabeledSkeletonAttributeVal",
        ),
        migrations.DeleteModel(
            name="TrackedSkeleton",
        ),
        migrations.DeleteModel(
            name="TrackedSkeletonAttributeVal",
        ),
    ]
