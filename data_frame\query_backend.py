import pymysql
import datetime
import csv
import json
import os

# 数据库连接配置
db_config = {
    'host': 'rm-2ze590s2u3ovv83qzbo.mysql.rds.aliyuncs.com',
    'user': 'ai',
    'password': 'wq$$4r%ixg',
    'database': 'backend_test',
    'port': 3306,
    'charset': 'utf8mb4'
}

def test_db_connection(config):
    """
    测试数据库连接
    """
    connection = None
    try:
        connection = pymysql.connect(**config)
        print(f"成功连接到数据库: {config['database']}@{config['host']}:{config['port']}")
        return True
    except Exception as e:
        print(f"连接失败: {e}")
        return False
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭")

def list_sample_records(target_date, limit=10):
    """
    列出表中的一些记录，用于查看数据
    
    Args:
        target_date: 目标日期，格式为 datetime 对象
        limit: 要列出的记录数量，默认为10条
    """
    connection = None
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 1. 构造表名
        table_date = target_date.strftime("%Y%m%d")
        table_name = f"t_data_ecg_{table_date}"
        print(f"尝试查询表: {table_name}")
        
        # 2. 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{db_config['database']}' 
            AND table_name = '{table_name}'
        """)
        if cursor.fetchone()[0] == 0:
            print(f"表 {table_name} 不存在")
            return None
        
        # 3. 获取记录总数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        total_count = cursor.fetchone()[0]
        print(f"表中共有 {total_count} 条记录")
        
        # 4. 获取表中的一些记录
        print(f"\n------ 列出表 {table_name} 中的 {limit} 条记录 ------")
        cursor.execute(f"SELECT id, union_id, start_time, end_time, es_key FROM {table_name} LIMIT {limit}")
        records = cursor.fetchall()
        
        # 获取列名
        column_names = ['id', 'union_id', 'start_time', 'end_time', 'es_key']
        
        # 打印记录
        for i, record in enumerate(records):
            print(f"\n记录 {i+1}:")
            for j, col_name in enumerate(column_names):
                print(f"  {col_name}: {record[j]}")
        
        # 5. 尝试查找下午3点左右的记录
        print(f"\n------ 查找下午3点左右的记录 ------")
        start_time = target_date.replace(hour=15, minute=0, second=0)
        end_time = target_date.replace(hour=16, minute=0, second=0)
        
        cursor.execute(f"""
            SELECT id, union_id, start_time, end_time, es_key 
            FROM {table_name} 
            WHERE start_time BETWEEN '{start_time}' AND '{end_time}'
            LIMIT {limit}
        """)
        time_records = cursor.fetchall()
        
        if time_records:
            print(f"找到 {len(time_records)} 条下午3点左右的记录:")
            for i, record in enumerate(time_records):
                print(f"\n记录 {i+1}:")
                for j, col_name in enumerate(column_names):
                    print(f"  {col_name}: {record[j]}")
        else:
            print("没有找到下午3点左右的记录")
        
        return {"column_names": column_names, "records": records, "time_records": time_records}
    except Exception as e:
        print(f"查询出错: {e}")
        return None
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭")

def search_approximate_id(target_date, id_prefix):
    """
    搜索与指定ID前缀匹配的记录
    
    Args:
        target_date: 目标日期，格式为 datetime 对象
        id_prefix: ID前缀，用于模糊匹配
    """
    connection = None
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 1. 构造表名
        table_date = target_date.strftime("%Y%m%d")
        table_name = f"t_data_ecg_{table_date}"
        print(f"尝试查询表: {table_name}")
        
        # 2. 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{db_config['database']}' 
            AND table_name = '{table_name}'
        """)
        if cursor.fetchone()[0] == 0:
            print(f"表 {table_name} 不存在")
            return None
        
        # 3. 搜索匹配的ID
        print(f"\n------ 搜索 ID 前缀为 {id_prefix} 的记录 ------")
        
        # 尝试union_id字段
        cursor.execute(f"""
            SELECT id, union_id, start_time, end_time, es_key
            FROM {table_name}
            WHERE union_id LIKE '{id_prefix}%'
            LIMIT 10
        """)
        union_id_records = cursor.fetchall()
        
        # 尝试es_key字段
        cursor.execute(f"""
            SELECT id, union_id, start_time, end_time, es_key
            FROM {table_name}
            WHERE es_key LIKE '{id_prefix}%'
            LIMIT 10
        """)
        es_key_records = cursor.fetchall()
        
        # 获取列名
        column_names = ['id', 'union_id', 'start_time', 'end_time', 'es_key']
        
        # 打印结果
        if union_id_records:
            print(f"\n找到 {len(union_id_records)} 条 union_id 匹配的记录:")
            for i, record in enumerate(union_id_records):
                print(f"\n记录 {i+1}:")
                for j, col_name in enumerate(column_names):
                    print(f"  {col_name}: {record[j]}")
        else:
            print("没有找到 union_id 匹配的记录")
            
        if es_key_records:
            print(f"\n找到 {len(es_key_records)} 条 es_key 匹配的记录:")
            for i, record in enumerate(es_key_records):
                print(f"\n记录 {i+1}:")
                for j, col_name in enumerate(column_names):
                    print(f"  {col_name}: {record[j]}")
        else:
            print("没有找到 es_key 匹配的记录")
        
        return {
            "column_names": column_names, 
            "union_id_records": union_id_records, 
            "es_key_records": es_key_records
        }
    except Exception as e:
        print(f"查询出错: {e}")
        return None
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭")

def query_by_union_id(union_id, target_date):
    """
    根据union_id和日期查询数据
    
    Args:
        union_id: 用户的union_id
        target_date: 目标日期，格式为 datetime 对象
    """
    connection = None
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 1. 构造表名
        table_date = target_date.strftime("%Y%m%d")
        table_name = f"t_data_ecg_{table_date}"
        print(f"尝试查询表: {table_name}")
        
        # 2. 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{db_config['database']}' 
            AND table_name = '{table_name}'
        """)
        if cursor.fetchone()[0] == 0:
            print(f"表 {table_name} 不存在")
            return None
        
        # 3. 查询表结构
        print(f"\n------ 查看表 {table_name} 结构 ------")
        cursor.execute(f"DESCRIBE {table_name}")
        columns = cursor.fetchall()
        print("表结构:")
        for column in columns:
            print(f"- {column[0]}: {column[1]}")
        
        # 4. 根据union_id查询数据
        print(f"\n------ 查询 union_id = {union_id} 的记录 ------")
        try:
            cursor.execute(f"""
                SELECT * FROM {table_name}
                WHERE union_id = '{union_id}'
                ORDER BY start_time
            """)
            records = cursor.fetchall()
            if not records:
                print(f"没有找到 union_id = {union_id} 的记录")
                
                # 尝试使用es_key字段
                print("尝试使用es_key字段...")
                try:
                    cursor.execute(f"""
                        SELECT * FROM {table_name}
                        WHERE es_key = '{union_id}'
                        ORDER BY start_time
                    """)
                    records = cursor.fetchall()
                    if not records:
                        print(f"没有找到 es_key = {union_id} 的记录")
                        return None
                    
                    print(f"找到 {len(records)} 条记录（使用 es_key 字段）")
                except Exception as e3:
                    print(f"使用 es_key 查询出错: {e3}")
                    return None
            else:
                print(f"找到 {len(records)} 条记录")
            
            # 获取列名
            cursor.execute(f"SHOW COLUMNS FROM {table_name}")
            column_names = [column[0] for column in cursor.fetchall()]
            
            # 将记录转换为字典列表
            result = []
            for record in records:
                result.append(dict(zip(column_names, record)))
            
            return {"column_names": column_names, "records": result}
        except Exception as e:
            print(f"查询出错: {e}")
            return None
    except Exception as e:
        print(f"数据库操作出错: {e}")
        return None
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭")

def query_by_time_range(union_id, target_date, start_hour, end_hour):
    """
    根据union_id、日期和时间范围查询数据
    
    Args:
        union_id: 用户的union_id
        target_date: 目标日期，格式为 datetime 对象
        start_hour: 开始小时（24小时制）
        end_hour: 结束小时（24小时制）
    """
    connection = None
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 1. 构造表名和时间范围
        table_date = target_date.strftime("%Y%m%d")
        table_name = f"t_data_ecg_{table_date}"
        
        # 构造时间范围
        start_time = target_date.replace(hour=start_hour, minute=0, second=0)
        end_time = target_date.replace(hour=end_hour, minute=59, second=59)
        
        print(f"尝试查询表: {table_name}")
        print(f"时间范围: {start_time} 到 {end_time}")
        
        # 2. 检查表是否存在
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{db_config['database']}' 
            AND table_name = '{table_name}'
        """)
        if cursor.fetchone()[0] == 0:
            print(f"表 {table_name} 不存在")
            return None
        
        # 3. 查询表结构以确认正确的时间字段
        cursor.execute(f"DESCRIBE {table_name}")
        columns = [column[0] for column in cursor.fetchall()]
        
        time_field = None
        if 'start_time' in columns:
            time_field = 'start_time'
        elif 'create_time' in columns:
            time_field = 'create_time'
        else:
            print(f"未找到合适的时间字段，无法按时间范围查询")
            return None
            
        print(f"使用时间字段: {time_field}")
        
        # 4. 根据union_id和时间范围查询数据
        print(f"\n------ 查询 union_id = {union_id} 且时间在 {start_hour}:00 - {end_hour}:59 的记录 ------")
        try:
            cursor.execute(f"""
                SELECT * FROM {table_name}
                WHERE union_id = '{union_id}'
                AND {time_field} BETWEEN '{start_time}' AND '{end_time}'
                ORDER BY {time_field}
            """)
            records = cursor.fetchall()
            if not records:
                print(f"没有找到符合条件的记录，尝试使用es_key字段...")
                # 尝试使用es_key字段
                cursor.execute(f"""
                    SELECT * FROM {table_name}
                    WHERE es_key = '{union_id}'
                    AND {time_field} BETWEEN '{start_time}' AND '{end_time}'
                    ORDER BY {time_field}
                """)
                records = cursor.fetchall()
                if not records:
                    print(f"使用es_key也没有找到符合条件的记录")
                    return None
                else:
                    print(f"使用es_key字段找到 {len(records)} 条记录")
            else:
                print(f"找到 {len(records)} 条记录")
            
            # 获取列名
            cursor.execute(f"SHOW COLUMNS FROM {table_name}")
            column_names = [column[0] for column in cursor.fetchall()]
            
            # 将记录转换为字典列表
            result = []
            for record in records:
                result.append(dict(zip(column_names, record)))
            
            return {"column_names": column_names, "records": result}
        except Exception as e:
            print(f"查询出错: {e}")
            return None
    except Exception as e:
        print(f"数据库操作出错: {e}")
        return None
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭")

def save_records_to_csv(data, union_id, date_str, time_range):
    """保存记录到CSV文件"""
    if not data or not data.get("records"):
        print("没有数据可以保存")
        return None
    
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    filename = f'ecg_data_{union_id}_{date_str}_{time_range}_{timestamp}.csv'
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            # 写入表头
            writer.writerow(data["column_names"])
            # 写入数据
            for record in data["records"]:
                writer.writerow([record.get(col) for col in data["column_names"]])
        print(f"数据已成功导出到文件: {filename}")
        return filename
    except Exception as e:
        print(f"保存CSV时出错: {e}")
        return None

def save_records_to_json(data, union_id, date_str, time_range):
    """保存记录到JSON文件"""
    if not data or not data.get("records"):
        print("没有数据可以保存")
        return None
    
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    filename = f'ecg_data_{union_id}_{date_str}_{time_range}_{timestamp}.json'
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data["records"], f, ensure_ascii=False, indent=2, default=str)
        print(f"数据已成功导出到JSON文件: {filename}")
        return filename
    except Exception as e:
        print(f"保存JSON时出错: {e}")
        return None

def get_record_without_ecg_data(record):
    """获取记录的基本信息，去除大型ECG数据"""
    result = {}
    for key, value in record.items():
        # 排除大型数据字段
        if key not in ['ecg', 'ecgII', 'ecgIII', 'ecg_byte', 'ecg_summary', 'ecg_analysis', 
                      'ecg_analysisII', 'ecg_analysisIII']:
            result[key] = value
    return result

def save_record_summary(data, union_id, date_str, time_range):
    """保存记录的摘要信息（不包含ECG数据）"""
    if not data or not data.get("records"):
        print("没有数据可以保存")
        return None
    
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    filename = f'ecg_summary_{union_id}_{date_str}_{time_range}_{timestamp}.json'
    
    try:
        summary_records = [get_record_without_ecg_data(record) for record in data["records"]]
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(summary_records, f, ensure_ascii=False, indent=2, default=str)
        print(f"数据摘要已成功导出到文件: {filename}")
        return filename
    except Exception as e:
        print(f"保存摘要信息时出错: {e}")
        return None

if __name__ == "__main__":
    # 查询参数
    union_id = '1906972114197643266'  # 要查询的union_id
    target_date = datetime.datetime(2025, 4, 1)  # 查询日期：2025年4月1日
    date_str = target_date.strftime("%Y%m%d")
    id_prefix = '19069'  # ID前缀，用于模糊匹配
    
    if test_db_connection(db_config):
        print(f"\n------ 开始查询 {target_date.strftime('%Y-%m-%d')} 的数据 ------")
        
        # 首先列出表中的一些记录
        print("\n1. 列出表中的样本记录")
        list_sample_records(target_date, 5)
        
        # 搜索近似ID
        print("\n2. 搜索ID前缀匹配的记录")
        search_results = search_approximate_id(target_date, id_prefix)
        
        # 如果找到了匹配的记录，询问用户是否要查询特定记录
        if search_results and (search_results.get('union_id_records') or search_results.get('es_key_records')):
            print("\n找到了一些可能匹配的记录。如果需要查询特定记录，请使用上述任一记录的ID。")
            print("例如，可以使用 union_id 或 es_key 值重新运行查询。")
        else:
            # 尝试按原始指定ID进行查询
            print("\n3. 尝试按原始指定ID查询")
            # 按时间范围查询数据
            data = query_by_time_range(union_id, target_date, 15, 16)
            
            if data and data.get("records"):
                # 打印记录的时间信息
                print("\n记录的时间信息:")
                for i, record in enumerate(data["records"]):
                    start_time = record.get("start_time")
                    end_time = record.get("end_time")
                    create_time = record.get("create_time")
                    print(f"记录 {i+1}:")
                    if start_time:
                        print(f"  开始时间: {start_time}")
                    if end_time:
                        print(f"  结束时间: {end_time}")
                    if create_time:
                        print(f"  创建时间: {create_time}")
                
                # 保存数据的摘要信息（不包含ECG数据）
                save_record_summary(data, union_id, date_str, "15-16")
                
                # 保存完整数据到CSV和JSON文件
                save_records_to_csv(data, union_id, date_str, "15-16")
                save_records_to_json(data, union_id, date_str, "15-16")
            else:
                print("未找到符合条件的记录，尝试查询整天的数据")
                # 查询整天的数据
                all_day_data = query_by_union_id(union_id, target_date)
                
                if all_day_data and all_day_data.get("records"):
                    print("\n找到当天的记录，请查看是否有接近下午3点30分的数据:")
                    for i, record in enumerate(all_day_data["records"]):
                        start_time = record.get("start_time")
                        end_time = record.get("end_time")
                        create_time = record.get("create_time")
                        print(f"记录 {i+1}:")
                        if start_time:
                            print(f"  开始时间: {start_time}")
                        if end_time:
                            print(f"  结束时间: {end_time}")
                        if create_time:
                            print(f"  创建时间: {create_time}")
                    
                    # 保存数据摘要
                    save_record_summary(all_day_data, union_id, date_str, "all_day")
                    
                    # 保存整天的数据
                    save_records_to_csv(all_day_data, union_id, date_str, "all_day")
                    save_records_to_json(all_day_data, union_id, date_str, "all_day")
                else:
                    print("当天没有该用户的记录")
    else:
        print("数据库连接测试失败，无法执行查询") 