// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/master.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/device_attributes.pb.h"
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/protobuf/config.pb.h"
#include "tensorflow/core/protobuf/error_codes.pb.h"
#include "tensorflow/core/protobuf/named_tensor.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[20]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
namespace tensorflow {
class CloseSessionRequest;
class CloseSessionRequestDefaultTypeInternal;
extern CloseSessionRequestDefaultTypeInternal _CloseSessionRequest_default_instance_;
class CloseSessionResponse;
class CloseSessionResponseDefaultTypeInternal;
extern CloseSessionResponseDefaultTypeInternal _CloseSessionResponse_default_instance_;
class CreateSessionRequest;
class CreateSessionRequestDefaultTypeInternal;
extern CreateSessionRequestDefaultTypeInternal _CreateSessionRequest_default_instance_;
class CreateSessionResponse;
class CreateSessionResponseDefaultTypeInternal;
extern CreateSessionResponseDefaultTypeInternal _CreateSessionResponse_default_instance_;
class ExtendSessionRequest;
class ExtendSessionRequestDefaultTypeInternal;
extern ExtendSessionRequestDefaultTypeInternal _ExtendSessionRequest_default_instance_;
class ExtendSessionResponse;
class ExtendSessionResponseDefaultTypeInternal;
extern ExtendSessionResponseDefaultTypeInternal _ExtendSessionResponse_default_instance_;
class ListDevicesRequest;
class ListDevicesRequestDefaultTypeInternal;
extern ListDevicesRequestDefaultTypeInternal _ListDevicesRequest_default_instance_;
class ListDevicesResponse;
class ListDevicesResponseDefaultTypeInternal;
extern ListDevicesResponseDefaultTypeInternal _ListDevicesResponse_default_instance_;
class MakeCallableRequest;
class MakeCallableRequestDefaultTypeInternal;
extern MakeCallableRequestDefaultTypeInternal _MakeCallableRequest_default_instance_;
class MakeCallableResponse;
class MakeCallableResponseDefaultTypeInternal;
extern MakeCallableResponseDefaultTypeInternal _MakeCallableResponse_default_instance_;
class PartialRunSetupRequest;
class PartialRunSetupRequestDefaultTypeInternal;
extern PartialRunSetupRequestDefaultTypeInternal _PartialRunSetupRequest_default_instance_;
class PartialRunSetupResponse;
class PartialRunSetupResponseDefaultTypeInternal;
extern PartialRunSetupResponseDefaultTypeInternal _PartialRunSetupResponse_default_instance_;
class ReleaseCallableRequest;
class ReleaseCallableRequestDefaultTypeInternal;
extern ReleaseCallableRequestDefaultTypeInternal _ReleaseCallableRequest_default_instance_;
class ReleaseCallableResponse;
class ReleaseCallableResponseDefaultTypeInternal;
extern ReleaseCallableResponseDefaultTypeInternal _ReleaseCallableResponse_default_instance_;
class ResetRequest;
class ResetRequestDefaultTypeInternal;
extern ResetRequestDefaultTypeInternal _ResetRequest_default_instance_;
class ResetResponse;
class ResetResponseDefaultTypeInternal;
extern ResetResponseDefaultTypeInternal _ResetResponse_default_instance_;
class RunCallableRequest;
class RunCallableRequestDefaultTypeInternal;
extern RunCallableRequestDefaultTypeInternal _RunCallableRequest_default_instance_;
class RunCallableResponse;
class RunCallableResponseDefaultTypeInternal;
extern RunCallableResponseDefaultTypeInternal _RunCallableResponse_default_instance_;
class RunStepRequest;
class RunStepRequestDefaultTypeInternal;
extern RunStepRequestDefaultTypeInternal _RunStepRequest_default_instance_;
class RunStepResponse;
class RunStepResponseDefaultTypeInternal;
extern RunStepResponseDefaultTypeInternal _RunStepResponse_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CloseSessionRequest* Arena::CreateMaybeMessage<::tensorflow::CloseSessionRequest>(Arena*);
template<> ::tensorflow::CloseSessionResponse* Arena::CreateMaybeMessage<::tensorflow::CloseSessionResponse>(Arena*);
template<> ::tensorflow::CreateSessionRequest* Arena::CreateMaybeMessage<::tensorflow::CreateSessionRequest>(Arena*);
template<> ::tensorflow::CreateSessionResponse* Arena::CreateMaybeMessage<::tensorflow::CreateSessionResponse>(Arena*);
template<> ::tensorflow::ExtendSessionRequest* Arena::CreateMaybeMessage<::tensorflow::ExtendSessionRequest>(Arena*);
template<> ::tensorflow::ExtendSessionResponse* Arena::CreateMaybeMessage<::tensorflow::ExtendSessionResponse>(Arena*);
template<> ::tensorflow::ListDevicesRequest* Arena::CreateMaybeMessage<::tensorflow::ListDevicesRequest>(Arena*);
template<> ::tensorflow::ListDevicesResponse* Arena::CreateMaybeMessage<::tensorflow::ListDevicesResponse>(Arena*);
template<> ::tensorflow::MakeCallableRequest* Arena::CreateMaybeMessage<::tensorflow::MakeCallableRequest>(Arena*);
template<> ::tensorflow::MakeCallableResponse* Arena::CreateMaybeMessage<::tensorflow::MakeCallableResponse>(Arena*);
template<> ::tensorflow::PartialRunSetupRequest* Arena::CreateMaybeMessage<::tensorflow::PartialRunSetupRequest>(Arena*);
template<> ::tensorflow::PartialRunSetupResponse* Arena::CreateMaybeMessage<::tensorflow::PartialRunSetupResponse>(Arena*);
template<> ::tensorflow::ReleaseCallableRequest* Arena::CreateMaybeMessage<::tensorflow::ReleaseCallableRequest>(Arena*);
template<> ::tensorflow::ReleaseCallableResponse* Arena::CreateMaybeMessage<::tensorflow::ReleaseCallableResponse>(Arena*);
template<> ::tensorflow::ResetRequest* Arena::CreateMaybeMessage<::tensorflow::ResetRequest>(Arena*);
template<> ::tensorflow::ResetResponse* Arena::CreateMaybeMessage<::tensorflow::ResetResponse>(Arena*);
template<> ::tensorflow::RunCallableRequest* Arena::CreateMaybeMessage<::tensorflow::RunCallableRequest>(Arena*);
template<> ::tensorflow::RunCallableResponse* Arena::CreateMaybeMessage<::tensorflow::RunCallableResponse>(Arena*);
template<> ::tensorflow::RunStepRequest* Arena::CreateMaybeMessage<::tensorflow::RunStepRequest>(Arena*);
template<> ::tensorflow::RunStepResponse* Arena::CreateMaybeMessage<::tensorflow::RunStepResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class CreateSessionRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateSessionRequest) */ {
 public:
  CreateSessionRequest();
  virtual ~CreateSessionRequest();

  CreateSessionRequest(const CreateSessionRequest& from);
  CreateSessionRequest(CreateSessionRequest&& from) noexcept
    : CreateSessionRequest() {
    *this = ::std::move(from);
  }

  inline CreateSessionRequest& operator=(const CreateSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateSessionRequest& operator=(CreateSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CreateSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateSessionRequest* internal_default_instance() {
    return reinterpret_cast<const CreateSessionRequest*>(
               &_CreateSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CreateSessionRequest& a, CreateSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateSessionRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CreateSessionRequest* New() const final {
    return CreateMaybeMessage<CreateSessionRequest>(nullptr);
  }

  CreateSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CreateSessionRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CreateSessionRequest& from);
  void MergeFrom(const CreateSessionRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateSessionRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CreateSessionRequest";
  }
  protected:
  explicit CreateSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTargetFieldNumber = 3,
    kGraphDefFieldNumber = 1,
    kConfigFieldNumber = 2,
  };
  // string target = 3;
  void clear_target();
  const std::string& target() const;
  void set_target(const std::string& value);
  void set_target(std::string&& value);
  void set_target(const char* value);
  void set_target(const char* value, size_t size);
  std::string* mutable_target();
  std::string* release_target();
  void set_allocated_target(std::string* target);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_target();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_target(
      std::string* target);

  // .tensorflow.GraphDef graph_def = 1;
  bool has_graph_def() const;
  void clear_graph_def();
  const ::tensorflow::GraphDef& graph_def() const;
  ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // .tensorflow.ConfigProto config = 2;
  bool has_config() const;
  void clear_config();
  const ::tensorflow::ConfigProto& config() const;
  ::tensorflow::ConfigProto* release_config();
  ::tensorflow::ConfigProto* mutable_config();
  void set_allocated_config(::tensorflow::ConfigProto* config);
  void unsafe_arena_set_allocated_config(
      ::tensorflow::ConfigProto* config);
  ::tensorflow::ConfigProto* unsafe_arena_release_config();

  // @@protoc_insertion_point(class_scope:tensorflow.CreateSessionRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr target_;
  ::tensorflow::GraphDef* graph_def_;
  ::tensorflow::ConfigProto* config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class CreateSessionResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateSessionResponse) */ {
 public:
  CreateSessionResponse();
  virtual ~CreateSessionResponse();

  CreateSessionResponse(const CreateSessionResponse& from);
  CreateSessionResponse(CreateSessionResponse&& from) noexcept
    : CreateSessionResponse() {
    *this = ::std::move(from);
  }

  inline CreateSessionResponse& operator=(const CreateSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateSessionResponse& operator=(CreateSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CreateSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateSessionResponse* internal_default_instance() {
    return reinterpret_cast<const CreateSessionResponse*>(
               &_CreateSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CreateSessionResponse& a, CreateSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateSessionResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CreateSessionResponse* New() const final {
    return CreateMaybeMessage<CreateSessionResponse>(nullptr);
  }

  CreateSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CreateSessionResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CreateSessionResponse& from);
  void MergeFrom(const CreateSessionResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateSessionResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CreateSessionResponse";
  }
  protected:
  explicit CreateSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
    kGraphVersionFieldNumber = 2,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // int64 graph_version = 2;
  void clear_graph_version();
  ::PROTOBUF_NAMESPACE_ID::int64 graph_version() const;
  void set_graph_version(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CreateSessionResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::PROTOBUF_NAMESPACE_ID::int64 graph_version_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ExtendSessionRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExtendSessionRequest) */ {
 public:
  ExtendSessionRequest();
  virtual ~ExtendSessionRequest();

  ExtendSessionRequest(const ExtendSessionRequest& from);
  ExtendSessionRequest(ExtendSessionRequest&& from) noexcept
    : ExtendSessionRequest() {
    *this = ::std::move(from);
  }

  inline ExtendSessionRequest& operator=(const ExtendSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExtendSessionRequest& operator=(ExtendSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExtendSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExtendSessionRequest* internal_default_instance() {
    return reinterpret_cast<const ExtendSessionRequest*>(
               &_ExtendSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ExtendSessionRequest& a, ExtendSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ExtendSessionRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExtendSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExtendSessionRequest* New() const final {
    return CreateMaybeMessage<ExtendSessionRequest>(nullptr);
  }

  ExtendSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExtendSessionRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExtendSessionRequest& from);
  void MergeFrom(const ExtendSessionRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExtendSessionRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ExtendSessionRequest";
  }
  protected:
  explicit ExtendSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
    kGraphDefFieldNumber = 2,
    kCurrentGraphVersionFieldNumber = 3,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // .tensorflow.GraphDef graph_def = 2;
  bool has_graph_def() const;
  void clear_graph_def();
  const ::tensorflow::GraphDef& graph_def() const;
  ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // int64 current_graph_version = 3;
  void clear_current_graph_version();
  ::PROTOBUF_NAMESPACE_ID::int64 current_graph_version() const;
  void set_current_graph_version(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ExtendSessionRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::tensorflow::GraphDef* graph_def_;
  ::PROTOBUF_NAMESPACE_ID::int64 current_graph_version_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ExtendSessionResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExtendSessionResponse) */ {
 public:
  ExtendSessionResponse();
  virtual ~ExtendSessionResponse();

  ExtendSessionResponse(const ExtendSessionResponse& from);
  ExtendSessionResponse(ExtendSessionResponse&& from) noexcept
    : ExtendSessionResponse() {
    *this = ::std::move(from);
  }

  inline ExtendSessionResponse& operator=(const ExtendSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExtendSessionResponse& operator=(ExtendSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExtendSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExtendSessionResponse* internal_default_instance() {
    return reinterpret_cast<const ExtendSessionResponse*>(
               &_ExtendSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ExtendSessionResponse& a, ExtendSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ExtendSessionResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExtendSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExtendSessionResponse* New() const final {
    return CreateMaybeMessage<ExtendSessionResponse>(nullptr);
  }

  ExtendSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExtendSessionResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExtendSessionResponse& from);
  void MergeFrom(const ExtendSessionResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExtendSessionResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ExtendSessionResponse";
  }
  protected:
  explicit ExtendSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNewGraphVersionFieldNumber = 4,
  };
  // int64 new_graph_version = 4;
  void clear_new_graph_version();
  ::PROTOBUF_NAMESPACE_ID::int64 new_graph_version() const;
  void set_new_graph_version(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ExtendSessionResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 new_graph_version_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class RunStepRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunStepRequest) */ {
 public:
  RunStepRequest();
  virtual ~RunStepRequest();

  RunStepRequest(const RunStepRequest& from);
  RunStepRequest(RunStepRequest&& from) noexcept
    : RunStepRequest() {
    *this = ::std::move(from);
  }

  inline RunStepRequest& operator=(const RunStepRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunStepRequest& operator=(RunStepRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunStepRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunStepRequest* internal_default_instance() {
    return reinterpret_cast<const RunStepRequest*>(
               &_RunStepRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(RunStepRequest& a, RunStepRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RunStepRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunStepRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunStepRequest* New() const final {
    return CreateMaybeMessage<RunStepRequest>(nullptr);
  }

  RunStepRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunStepRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunStepRequest& from);
  void MergeFrom(const RunStepRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunStepRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunStepRequest";
  }
  protected:
  explicit RunStepRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeedFieldNumber = 2,
    kFetchFieldNumber = 3,
    kTargetFieldNumber = 4,
    kSessionHandleFieldNumber = 1,
    kPartialRunHandleFieldNumber = 6,
    kOptionsFieldNumber = 5,
    kRequestIdFieldNumber = 8,
    kStoreErrorsInResponseBodyFieldNumber = 7,
  };
  // repeated .tensorflow.NamedTensorProto feed = 2;
  int feed_size() const;
  void clear_feed();
  ::tensorflow::NamedTensorProto* mutable_feed(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_feed();
  const ::tensorflow::NamedTensorProto& feed(int index) const;
  ::tensorflow::NamedTensorProto* add_feed();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      feed() const;

  // repeated string fetch = 3;
  int fetch_size() const;
  void clear_fetch();
  const std::string& fetch(int index) const;
  std::string* mutable_fetch(int index);
  void set_fetch(int index, const std::string& value);
  void set_fetch(int index, std::string&& value);
  void set_fetch(int index, const char* value);
  void set_fetch(int index, const char* value, size_t size);
  std::string* add_fetch();
  void add_fetch(const std::string& value);
  void add_fetch(std::string&& value);
  void add_fetch(const char* value);
  void add_fetch(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& fetch() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_fetch();

  // repeated string target = 4;
  int target_size() const;
  void clear_target();
  const std::string& target(int index) const;
  std::string* mutable_target(int index);
  void set_target(int index, const std::string& value);
  void set_target(int index, std::string&& value);
  void set_target(int index, const char* value);
  void set_target(int index, const char* value, size_t size);
  std::string* add_target();
  void add_target(const std::string& value);
  void add_target(std::string&& value);
  void add_target(const char* value);
  void add_target(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& target() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_target();

  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // string partial_run_handle = 6;
  void clear_partial_run_handle();
  const std::string& partial_run_handle() const;
  void set_partial_run_handle(const std::string& value);
  void set_partial_run_handle(std::string&& value);
  void set_partial_run_handle(const char* value);
  void set_partial_run_handle(const char* value, size_t size);
  std::string* mutable_partial_run_handle();
  std::string* release_partial_run_handle();
  void set_allocated_partial_run_handle(std::string* partial_run_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_partial_run_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_partial_run_handle(
      std::string* partial_run_handle);

  // .tensorflow.RunOptions options = 5;
  bool has_options() const;
  void clear_options();
  const ::tensorflow::RunOptions& options() const;
  ::tensorflow::RunOptions* release_options();
  ::tensorflow::RunOptions* mutable_options();
  void set_allocated_options(::tensorflow::RunOptions* options);
  void unsafe_arena_set_allocated_options(
      ::tensorflow::RunOptions* options);
  ::tensorflow::RunOptions* unsafe_arena_release_options();

  // int64 request_id = 8;
  void clear_request_id();
  ::PROTOBUF_NAMESPACE_ID::int64 request_id() const;
  void set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool store_errors_in_response_body = 7;
  void clear_store_errors_in_response_body();
  bool store_errors_in_response_body() const;
  void set_store_errors_in_response_body(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunStepRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto > feed_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> fetch_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> target_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr partial_run_handle_;
  ::tensorflow::RunOptions* options_;
  ::PROTOBUF_NAMESPACE_ID::int64 request_id_;
  bool store_errors_in_response_body_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class RunStepResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunStepResponse) */ {
 public:
  RunStepResponse();
  virtual ~RunStepResponse();

  RunStepResponse(const RunStepResponse& from);
  RunStepResponse(RunStepResponse&& from) noexcept
    : RunStepResponse() {
    *this = ::std::move(from);
  }

  inline RunStepResponse& operator=(const RunStepResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunStepResponse& operator=(RunStepResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunStepResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunStepResponse* internal_default_instance() {
    return reinterpret_cast<const RunStepResponse*>(
               &_RunStepResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(RunStepResponse& a, RunStepResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RunStepResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunStepResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunStepResponse* New() const final {
    return CreateMaybeMessage<RunStepResponse>(nullptr);
  }

  RunStepResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunStepResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunStepResponse& from);
  void MergeFrom(const RunStepResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunStepResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunStepResponse";
  }
  protected:
  explicit RunStepResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorFieldNumber = 1,
    kStatusErrorMessageFieldNumber = 4,
    kMetadataFieldNumber = 2,
    kStatusCodeFieldNumber = 3,
  };
  // repeated .tensorflow.NamedTensorProto tensor = 1;
  int tensor_size() const;
  void clear_tensor();
  ::tensorflow::NamedTensorProto* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_tensor();
  const ::tensorflow::NamedTensorProto& tensor(int index) const;
  ::tensorflow::NamedTensorProto* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      tensor() const;

  // string status_error_message = 4;
  void clear_status_error_message();
  const std::string& status_error_message() const;
  void set_status_error_message(const std::string& value);
  void set_status_error_message(std::string&& value);
  void set_status_error_message(const char* value);
  void set_status_error_message(const char* value, size_t size);
  std::string* mutable_status_error_message();
  std::string* release_status_error_message();
  void set_allocated_status_error_message(std::string* status_error_message);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_status_error_message();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_status_error_message(
      std::string* status_error_message);

  // .tensorflow.RunMetadata metadata = 2;
  bool has_metadata() const;
  void clear_metadata();
  const ::tensorflow::RunMetadata& metadata() const;
  ::tensorflow::RunMetadata* release_metadata();
  ::tensorflow::RunMetadata* mutable_metadata();
  void set_allocated_metadata(::tensorflow::RunMetadata* metadata);
  void unsafe_arena_set_allocated_metadata(
      ::tensorflow::RunMetadata* metadata);
  ::tensorflow::RunMetadata* unsafe_arena_release_metadata();

  // .tensorflow.error.Code status_code = 3;
  void clear_status_code();
  ::tensorflow::error::Code status_code() const;
  void set_status_code(::tensorflow::error::Code value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunStepResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto > tensor_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_error_message_;
  ::tensorflow::RunMetadata* metadata_;
  int status_code_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class PartialRunSetupRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.PartialRunSetupRequest) */ {
 public:
  PartialRunSetupRequest();
  virtual ~PartialRunSetupRequest();

  PartialRunSetupRequest(const PartialRunSetupRequest& from);
  PartialRunSetupRequest(PartialRunSetupRequest&& from) noexcept
    : PartialRunSetupRequest() {
    *this = ::std::move(from);
  }

  inline PartialRunSetupRequest& operator=(const PartialRunSetupRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PartialRunSetupRequest& operator=(PartialRunSetupRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PartialRunSetupRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PartialRunSetupRequest* internal_default_instance() {
    return reinterpret_cast<const PartialRunSetupRequest*>(
               &_PartialRunSetupRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(PartialRunSetupRequest& a, PartialRunSetupRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PartialRunSetupRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PartialRunSetupRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PartialRunSetupRequest* New() const final {
    return CreateMaybeMessage<PartialRunSetupRequest>(nullptr);
  }

  PartialRunSetupRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PartialRunSetupRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PartialRunSetupRequest& from);
  void MergeFrom(const PartialRunSetupRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PartialRunSetupRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.PartialRunSetupRequest";
  }
  protected:
  explicit PartialRunSetupRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeedFieldNumber = 2,
    kFetchFieldNumber = 3,
    kTargetFieldNumber = 4,
    kSessionHandleFieldNumber = 1,
    kRequestIdFieldNumber = 5,
  };
  // repeated string feed = 2;
  int feed_size() const;
  void clear_feed();
  const std::string& feed(int index) const;
  std::string* mutable_feed(int index);
  void set_feed(int index, const std::string& value);
  void set_feed(int index, std::string&& value);
  void set_feed(int index, const char* value);
  void set_feed(int index, const char* value, size_t size);
  std::string* add_feed();
  void add_feed(const std::string& value);
  void add_feed(std::string&& value);
  void add_feed(const char* value);
  void add_feed(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& feed() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_feed();

  // repeated string fetch = 3;
  int fetch_size() const;
  void clear_fetch();
  const std::string& fetch(int index) const;
  std::string* mutable_fetch(int index);
  void set_fetch(int index, const std::string& value);
  void set_fetch(int index, std::string&& value);
  void set_fetch(int index, const char* value);
  void set_fetch(int index, const char* value, size_t size);
  std::string* add_fetch();
  void add_fetch(const std::string& value);
  void add_fetch(std::string&& value);
  void add_fetch(const char* value);
  void add_fetch(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& fetch() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_fetch();

  // repeated string target = 4;
  int target_size() const;
  void clear_target();
  const std::string& target(int index) const;
  std::string* mutable_target(int index);
  void set_target(int index, const std::string& value);
  void set_target(int index, std::string&& value);
  void set_target(int index, const char* value);
  void set_target(int index, const char* value, size_t size);
  std::string* add_target();
  void add_target(const std::string& value);
  void add_target(std::string&& value);
  void add_target(const char* value);
  void add_target(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& target() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_target();

  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // int64 request_id = 5;
  void clear_request_id();
  ::PROTOBUF_NAMESPACE_ID::int64 request_id() const;
  void set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.PartialRunSetupRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> feed_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> fetch_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> target_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::PROTOBUF_NAMESPACE_ID::int64 request_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class PartialRunSetupResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.PartialRunSetupResponse) */ {
 public:
  PartialRunSetupResponse();
  virtual ~PartialRunSetupResponse();

  PartialRunSetupResponse(const PartialRunSetupResponse& from);
  PartialRunSetupResponse(PartialRunSetupResponse&& from) noexcept
    : PartialRunSetupResponse() {
    *this = ::std::move(from);
  }

  inline PartialRunSetupResponse& operator=(const PartialRunSetupResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline PartialRunSetupResponse& operator=(PartialRunSetupResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PartialRunSetupResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PartialRunSetupResponse* internal_default_instance() {
    return reinterpret_cast<const PartialRunSetupResponse*>(
               &_PartialRunSetupResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(PartialRunSetupResponse& a, PartialRunSetupResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(PartialRunSetupResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PartialRunSetupResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PartialRunSetupResponse* New() const final {
    return CreateMaybeMessage<PartialRunSetupResponse>(nullptr);
  }

  PartialRunSetupResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PartialRunSetupResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PartialRunSetupResponse& from);
  void MergeFrom(const PartialRunSetupResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PartialRunSetupResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.PartialRunSetupResponse";
  }
  protected:
  explicit PartialRunSetupResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPartialRunHandleFieldNumber = 1,
  };
  // string partial_run_handle = 1;
  void clear_partial_run_handle();
  const std::string& partial_run_handle() const;
  void set_partial_run_handle(const std::string& value);
  void set_partial_run_handle(std::string&& value);
  void set_partial_run_handle(const char* value);
  void set_partial_run_handle(const char* value, size_t size);
  std::string* mutable_partial_run_handle();
  std::string* release_partial_run_handle();
  void set_allocated_partial_run_handle(std::string* partial_run_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_partial_run_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_partial_run_handle(
      std::string* partial_run_handle);

  // @@protoc_insertion_point(class_scope:tensorflow.PartialRunSetupResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr partial_run_handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class CloseSessionRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CloseSessionRequest) */ {
 public:
  CloseSessionRequest();
  virtual ~CloseSessionRequest();

  CloseSessionRequest(const CloseSessionRequest& from);
  CloseSessionRequest(CloseSessionRequest&& from) noexcept
    : CloseSessionRequest() {
    *this = ::std::move(from);
  }

  inline CloseSessionRequest& operator=(const CloseSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CloseSessionRequest& operator=(CloseSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CloseSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CloseSessionRequest* internal_default_instance() {
    return reinterpret_cast<const CloseSessionRequest*>(
               &_CloseSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(CloseSessionRequest& a, CloseSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CloseSessionRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CloseSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CloseSessionRequest* New() const final {
    return CreateMaybeMessage<CloseSessionRequest>(nullptr);
  }

  CloseSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CloseSessionRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CloseSessionRequest& from);
  void MergeFrom(const CloseSessionRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CloseSessionRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CloseSessionRequest";
  }
  protected:
  explicit CloseSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // @@protoc_insertion_point(class_scope:tensorflow.CloseSessionRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class CloseSessionResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CloseSessionResponse) */ {
 public:
  CloseSessionResponse();
  virtual ~CloseSessionResponse();

  CloseSessionResponse(const CloseSessionResponse& from);
  CloseSessionResponse(CloseSessionResponse&& from) noexcept
    : CloseSessionResponse() {
    *this = ::std::move(from);
  }

  inline CloseSessionResponse& operator=(const CloseSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CloseSessionResponse& operator=(CloseSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CloseSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CloseSessionResponse* internal_default_instance() {
    return reinterpret_cast<const CloseSessionResponse*>(
               &_CloseSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(CloseSessionResponse& a, CloseSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CloseSessionResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CloseSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CloseSessionResponse* New() const final {
    return CreateMaybeMessage<CloseSessionResponse>(nullptr);
  }

  CloseSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CloseSessionResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CloseSessionResponse& from);
  void MergeFrom(const CloseSessionResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CloseSessionResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CloseSessionResponse";
  }
  protected:
  explicit CloseSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CloseSessionResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ResetRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ResetRequest) */ {
 public:
  ResetRequest();
  virtual ~ResetRequest();

  ResetRequest(const ResetRequest& from);
  ResetRequest(ResetRequest&& from) noexcept
    : ResetRequest() {
    *this = ::std::move(from);
  }

  inline ResetRequest& operator=(const ResetRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResetRequest& operator=(ResetRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ResetRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ResetRequest* internal_default_instance() {
    return reinterpret_cast<const ResetRequest*>(
               &_ResetRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(ResetRequest& a, ResetRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ResetRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResetRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ResetRequest* New() const final {
    return CreateMaybeMessage<ResetRequest>(nullptr);
  }

  ResetRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ResetRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ResetRequest& from);
  void MergeFrom(const ResetRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResetRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ResetRequest";
  }
  protected:
  explicit ResetRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContainerFieldNumber = 1,
    kDeviceFiltersFieldNumber = 2,
  };
  // repeated string container = 1;
  int container_size() const;
  void clear_container();
  const std::string& container(int index) const;
  std::string* mutable_container(int index);
  void set_container(int index, const std::string& value);
  void set_container(int index, std::string&& value);
  void set_container(int index, const char* value);
  void set_container(int index, const char* value, size_t size);
  std::string* add_container();
  void add_container(const std::string& value);
  void add_container(std::string&& value);
  void add_container(const char* value);
  void add_container(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& container() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_container();

  // repeated string device_filters = 2;
  int device_filters_size() const;
  void clear_device_filters();
  const std::string& device_filters(int index) const;
  std::string* mutable_device_filters(int index);
  void set_device_filters(int index, const std::string& value);
  void set_device_filters(int index, std::string&& value);
  void set_device_filters(int index, const char* value);
  void set_device_filters(int index, const char* value, size_t size);
  std::string* add_device_filters();
  void add_device_filters(const std::string& value);
  void add_device_filters(std::string&& value);
  void add_device_filters(const char* value);
  void add_device_filters(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& device_filters() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_device_filters();

  // @@protoc_insertion_point(class_scope:tensorflow.ResetRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> container_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> device_filters_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ResetResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ResetResponse) */ {
 public:
  ResetResponse();
  virtual ~ResetResponse();

  ResetResponse(const ResetResponse& from);
  ResetResponse(ResetResponse&& from) noexcept
    : ResetResponse() {
    *this = ::std::move(from);
  }

  inline ResetResponse& operator=(const ResetResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResetResponse& operator=(ResetResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ResetResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ResetResponse* internal_default_instance() {
    return reinterpret_cast<const ResetResponse*>(
               &_ResetResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(ResetResponse& a, ResetResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ResetResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResetResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ResetResponse* New() const final {
    return CreateMaybeMessage<ResetResponse>(nullptr);
  }

  ResetResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ResetResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ResetResponse& from);
  void MergeFrom(const ResetResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResetResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ResetResponse";
  }
  protected:
  explicit ResetResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.ResetResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ListDevicesRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ListDevicesRequest) */ {
 public:
  ListDevicesRequest();
  virtual ~ListDevicesRequest();

  ListDevicesRequest(const ListDevicesRequest& from);
  ListDevicesRequest(ListDevicesRequest&& from) noexcept
    : ListDevicesRequest() {
    *this = ::std::move(from);
  }

  inline ListDevicesRequest& operator=(const ListDevicesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListDevicesRequest& operator=(ListDevicesRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ListDevicesRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ListDevicesRequest* internal_default_instance() {
    return reinterpret_cast<const ListDevicesRequest*>(
               &_ListDevicesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(ListDevicesRequest& a, ListDevicesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ListDevicesRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListDevicesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ListDevicesRequest* New() const final {
    return CreateMaybeMessage<ListDevicesRequest>(nullptr);
  }

  ListDevicesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ListDevicesRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ListDevicesRequest& from);
  void MergeFrom(const ListDevicesRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListDevicesRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ListDevicesRequest";
  }
  protected:
  explicit ListDevicesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // @@protoc_insertion_point(class_scope:tensorflow.ListDevicesRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ListDevicesResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ListDevicesResponse) */ {
 public:
  ListDevicesResponse();
  virtual ~ListDevicesResponse();

  ListDevicesResponse(const ListDevicesResponse& from);
  ListDevicesResponse(ListDevicesResponse&& from) noexcept
    : ListDevicesResponse() {
    *this = ::std::move(from);
  }

  inline ListDevicesResponse& operator=(const ListDevicesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListDevicesResponse& operator=(ListDevicesResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ListDevicesResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ListDevicesResponse* internal_default_instance() {
    return reinterpret_cast<const ListDevicesResponse*>(
               &_ListDevicesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(ListDevicesResponse& a, ListDevicesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ListDevicesResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListDevicesResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ListDevicesResponse* New() const final {
    return CreateMaybeMessage<ListDevicesResponse>(nullptr);
  }

  ListDevicesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ListDevicesResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ListDevicesResponse& from);
  void MergeFrom(const ListDevicesResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListDevicesResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ListDevicesResponse";
  }
  protected:
  explicit ListDevicesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocalDeviceFieldNumber = 1,
    kRemoteDeviceFieldNumber = 2,
  };
  // repeated .tensorflow.DeviceAttributes local_device = 1;
  int local_device_size() const;
  void clear_local_device();
  ::tensorflow::DeviceAttributes* mutable_local_device(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_local_device();
  const ::tensorflow::DeviceAttributes& local_device(int index) const;
  ::tensorflow::DeviceAttributes* add_local_device();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      local_device() const;

  // repeated .tensorflow.DeviceAttributes remote_device = 2;
  int remote_device_size() const;
  void clear_remote_device();
  ::tensorflow::DeviceAttributes* mutable_remote_device(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_remote_device();
  const ::tensorflow::DeviceAttributes& remote_device(int index) const;
  ::tensorflow::DeviceAttributes* add_remote_device();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      remote_device() const;

  // @@protoc_insertion_point(class_scope:tensorflow.ListDevicesResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > local_device_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > remote_device_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class MakeCallableRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MakeCallableRequest) */ {
 public:
  MakeCallableRequest();
  virtual ~MakeCallableRequest();

  MakeCallableRequest(const MakeCallableRequest& from);
  MakeCallableRequest(MakeCallableRequest&& from) noexcept
    : MakeCallableRequest() {
    *this = ::std::move(from);
  }

  inline MakeCallableRequest& operator=(const MakeCallableRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MakeCallableRequest& operator=(MakeCallableRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MakeCallableRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MakeCallableRequest* internal_default_instance() {
    return reinterpret_cast<const MakeCallableRequest*>(
               &_MakeCallableRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(MakeCallableRequest& a, MakeCallableRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MakeCallableRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MakeCallableRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MakeCallableRequest* New() const final {
    return CreateMaybeMessage<MakeCallableRequest>(nullptr);
  }

  MakeCallableRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MakeCallableRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MakeCallableRequest& from);
  void MergeFrom(const MakeCallableRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MakeCallableRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MakeCallableRequest";
  }
  protected:
  explicit MakeCallableRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
    kOptionsFieldNumber = 2,
    kRequestIdFieldNumber = 3,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // .tensorflow.CallableOptions options = 2;
  bool has_options() const;
  void clear_options();
  const ::tensorflow::CallableOptions& options() const;
  ::tensorflow::CallableOptions* release_options();
  ::tensorflow::CallableOptions* mutable_options();
  void set_allocated_options(::tensorflow::CallableOptions* options);
  void unsafe_arena_set_allocated_options(
      ::tensorflow::CallableOptions* options);
  ::tensorflow::CallableOptions* unsafe_arena_release_options();

  // int64 request_id = 3;
  void clear_request_id();
  ::PROTOBUF_NAMESPACE_ID::int64 request_id() const;
  void set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MakeCallableRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::tensorflow::CallableOptions* options_;
  ::PROTOBUF_NAMESPACE_ID::int64 request_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class MakeCallableResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MakeCallableResponse) */ {
 public:
  MakeCallableResponse();
  virtual ~MakeCallableResponse();

  MakeCallableResponse(const MakeCallableResponse& from);
  MakeCallableResponse(MakeCallableResponse&& from) noexcept
    : MakeCallableResponse() {
    *this = ::std::move(from);
  }

  inline MakeCallableResponse& operator=(const MakeCallableResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MakeCallableResponse& operator=(MakeCallableResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MakeCallableResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MakeCallableResponse* internal_default_instance() {
    return reinterpret_cast<const MakeCallableResponse*>(
               &_MakeCallableResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(MakeCallableResponse& a, MakeCallableResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MakeCallableResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MakeCallableResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MakeCallableResponse* New() const final {
    return CreateMaybeMessage<MakeCallableResponse>(nullptr);
  }

  MakeCallableResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MakeCallableResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MakeCallableResponse& from);
  void MergeFrom(const MakeCallableResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MakeCallableResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MakeCallableResponse";
  }
  protected:
  explicit MakeCallableResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleFieldNumber = 1,
  };
  // int64 handle = 1;
  void clear_handle();
  ::PROTOBUF_NAMESPACE_ID::int64 handle() const;
  void set_handle(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MakeCallableResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class RunCallableRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunCallableRequest) */ {
 public:
  RunCallableRequest();
  virtual ~RunCallableRequest();

  RunCallableRequest(const RunCallableRequest& from);
  RunCallableRequest(RunCallableRequest&& from) noexcept
    : RunCallableRequest() {
    *this = ::std::move(from);
  }

  inline RunCallableRequest& operator=(const RunCallableRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunCallableRequest& operator=(RunCallableRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunCallableRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunCallableRequest* internal_default_instance() {
    return reinterpret_cast<const RunCallableRequest*>(
               &_RunCallableRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(RunCallableRequest& a, RunCallableRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RunCallableRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunCallableRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunCallableRequest* New() const final {
    return CreateMaybeMessage<RunCallableRequest>(nullptr);
  }

  RunCallableRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunCallableRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunCallableRequest& from);
  void MergeFrom(const RunCallableRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunCallableRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunCallableRequest";
  }
  protected:
  explicit RunCallableRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeedFieldNumber = 3,
    kSessionHandleFieldNumber = 1,
    kHandleFieldNumber = 2,
    kRequestIdFieldNumber = 4,
  };
  // repeated .tensorflow.TensorProto feed = 3;
  int feed_size() const;
  void clear_feed();
  ::tensorflow::TensorProto* mutable_feed(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_feed();
  const ::tensorflow::TensorProto& feed(int index) const;
  ::tensorflow::TensorProto* add_feed();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      feed() const;

  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // int64 handle = 2;
  void clear_handle();
  ::PROTOBUF_NAMESPACE_ID::int64 handle() const;
  void set_handle(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 request_id = 4;
  void clear_request_id();
  ::PROTOBUF_NAMESPACE_ID::int64 request_id() const;
  void set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunCallableRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > feed_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::PROTOBUF_NAMESPACE_ID::int64 handle_;
  ::PROTOBUF_NAMESPACE_ID::int64 request_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class RunCallableResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunCallableResponse) */ {
 public:
  RunCallableResponse();
  virtual ~RunCallableResponse();

  RunCallableResponse(const RunCallableResponse& from);
  RunCallableResponse(RunCallableResponse&& from) noexcept
    : RunCallableResponse() {
    *this = ::std::move(from);
  }

  inline RunCallableResponse& operator=(const RunCallableResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunCallableResponse& operator=(RunCallableResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RunCallableResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunCallableResponse* internal_default_instance() {
    return reinterpret_cast<const RunCallableResponse*>(
               &_RunCallableResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(RunCallableResponse& a, RunCallableResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RunCallableResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunCallableResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RunCallableResponse* New() const final {
    return CreateMaybeMessage<RunCallableResponse>(nullptr);
  }

  RunCallableResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RunCallableResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RunCallableResponse& from);
  void MergeFrom(const RunCallableResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunCallableResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunCallableResponse";
  }
  protected:
  explicit RunCallableResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFetchFieldNumber = 1,
    kMetadataFieldNumber = 2,
  };
  // repeated .tensorflow.TensorProto fetch = 1;
  int fetch_size() const;
  void clear_fetch();
  ::tensorflow::TensorProto* mutable_fetch(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_fetch();
  const ::tensorflow::TensorProto& fetch(int index) const;
  ::tensorflow::TensorProto* add_fetch();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      fetch() const;

  // .tensorflow.RunMetadata metadata = 2;
  bool has_metadata() const;
  void clear_metadata();
  const ::tensorflow::RunMetadata& metadata() const;
  ::tensorflow::RunMetadata* release_metadata();
  ::tensorflow::RunMetadata* mutable_metadata();
  void set_allocated_metadata(::tensorflow::RunMetadata* metadata);
  void unsafe_arena_set_allocated_metadata(
      ::tensorflow::RunMetadata* metadata);
  ::tensorflow::RunMetadata* unsafe_arena_release_metadata();

  // @@protoc_insertion_point(class_scope:tensorflow.RunCallableResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > fetch_;
  ::tensorflow::RunMetadata* metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ReleaseCallableRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ReleaseCallableRequest) */ {
 public:
  ReleaseCallableRequest();
  virtual ~ReleaseCallableRequest();

  ReleaseCallableRequest(const ReleaseCallableRequest& from);
  ReleaseCallableRequest(ReleaseCallableRequest&& from) noexcept
    : ReleaseCallableRequest() {
    *this = ::std::move(from);
  }

  inline ReleaseCallableRequest& operator=(const ReleaseCallableRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReleaseCallableRequest& operator=(ReleaseCallableRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ReleaseCallableRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ReleaseCallableRequest* internal_default_instance() {
    return reinterpret_cast<const ReleaseCallableRequest*>(
               &_ReleaseCallableRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(ReleaseCallableRequest& a, ReleaseCallableRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ReleaseCallableRequest* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReleaseCallableRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ReleaseCallableRequest* New() const final {
    return CreateMaybeMessage<ReleaseCallableRequest>(nullptr);
  }

  ReleaseCallableRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ReleaseCallableRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ReleaseCallableRequest& from);
  void MergeFrom(const ReleaseCallableRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReleaseCallableRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReleaseCallableRequest";
  }
  protected:
  explicit ReleaseCallableRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
    kHandleFieldNumber = 2,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // int64 handle = 2;
  void clear_handle();
  ::PROTOBUF_NAMESPACE_ID::int64 handle() const;
  void set_handle(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ReleaseCallableRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::PROTOBUF_NAMESPACE_ID::int64 handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ReleaseCallableResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ReleaseCallableResponse) */ {
 public:
  ReleaseCallableResponse();
  virtual ~ReleaseCallableResponse();

  ReleaseCallableResponse(const ReleaseCallableResponse& from);
  ReleaseCallableResponse(ReleaseCallableResponse&& from) noexcept
    : ReleaseCallableResponse() {
    *this = ::std::move(from);
  }

  inline ReleaseCallableResponse& operator=(const ReleaseCallableResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReleaseCallableResponse& operator=(ReleaseCallableResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ReleaseCallableResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ReleaseCallableResponse* internal_default_instance() {
    return reinterpret_cast<const ReleaseCallableResponse*>(
               &_ReleaseCallableResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(ReleaseCallableResponse& a, ReleaseCallableResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ReleaseCallableResponse* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReleaseCallableResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ReleaseCallableResponse* New() const final {
    return CreateMaybeMessage<ReleaseCallableResponse>(nullptr);
  }

  ReleaseCallableResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ReleaseCallableResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ReleaseCallableResponse& from);
  void MergeFrom(const ReleaseCallableResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReleaseCallableResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReleaseCallableResponse";
  }
  protected:
  explicit ReleaseCallableResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.ReleaseCallableResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CreateSessionRequest

// .tensorflow.GraphDef graph_def = 1;
inline bool CreateSessionRequest::has_graph_def() const {
  return this != internal_default_instance() && graph_def_ != nullptr;
}
inline const ::tensorflow::GraphDef& CreateSessionRequest::graph_def() const {
  const ::tensorflow::GraphDef* p = graph_def_;
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionRequest.graph_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::GraphDef*>(
      &::tensorflow::_GraphDef_default_instance_);
}
inline ::tensorflow::GraphDef* CreateSessionRequest::release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* CreateSessionRequest::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateSessionRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* CreateSessionRequest::mutable_graph_def() {
  
  if (graph_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaNoVirtual());
    graph_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionRequest.graph_def)
  return graph_def_;
}
inline void CreateSessionRequest::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def_);
  }
  if (graph_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def)->GetArena();
    if (message_arena != submessage_arena) {
      graph_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionRequest.graph_def)
}

// .tensorflow.ConfigProto config = 2;
inline bool CreateSessionRequest::has_config() const {
  return this != internal_default_instance() && config_ != nullptr;
}
inline const ::tensorflow::ConfigProto& CreateSessionRequest::config() const {
  const ::tensorflow::ConfigProto* p = config_;
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionRequest.config)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ConfigProto*>(
      &::tensorflow::_ConfigProto_default_instance_);
}
inline ::tensorflow::ConfigProto* CreateSessionRequest::release_config() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionRequest.config)
  
  ::tensorflow::ConfigProto* temp = config_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  config_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto* CreateSessionRequest::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateSessionRequest.config)
  
  ::tensorflow::ConfigProto* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto* CreateSessionRequest::mutable_config() {
  
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ConfigProto>(GetArenaNoVirtual());
    config_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionRequest.config)
  return config_;
}
inline void CreateSessionRequest::set_allocated_config(::tensorflow::ConfigProto* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config)->GetArena();
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionRequest.config)
}

// string target = 3;
inline void CreateSessionRequest::clear_target() {
  target_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CreateSessionRequest::target() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionRequest.target)
  return target_.Get();
}
inline void CreateSessionRequest::set_target(const std::string& value) {
  
  target_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CreateSessionRequest.target)
}
inline void CreateSessionRequest::set_target(std::string&& value) {
  
  target_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CreateSessionRequest.target)
}
inline void CreateSessionRequest::set_target(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  target_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CreateSessionRequest.target)
}
inline void CreateSessionRequest::set_target(const char* value,
    size_t size) {
  
  target_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CreateSessionRequest.target)
}
inline std::string* CreateSessionRequest::mutable_target() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionRequest.target)
  return target_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CreateSessionRequest::release_target() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionRequest.target)
  
  return target_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CreateSessionRequest::set_allocated_target(std::string* target) {
  if (target != nullptr) {
    
  } else {
    
  }
  target_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), target,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionRequest.target)
}
inline std::string* CreateSessionRequest::unsafe_arena_release_target() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateSessionRequest.target)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return target_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CreateSessionRequest::unsafe_arena_set_allocated_target(
    std::string* target) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (target != nullptr) {
    
  } else {
    
  }
  target_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      target, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateSessionRequest.target)
}

// -------------------------------------------------------------------

// CreateSessionResponse

// string session_handle = 1;
inline void CreateSessionResponse::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CreateSessionResponse::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionResponse.session_handle)
  return session_handle_.Get();
}
inline void CreateSessionResponse::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CreateSessionResponse.session_handle)
}
inline void CreateSessionResponse::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CreateSessionResponse.session_handle)
}
inline void CreateSessionResponse::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CreateSessionResponse.session_handle)
}
inline void CreateSessionResponse::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CreateSessionResponse.session_handle)
}
inline std::string* CreateSessionResponse::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionResponse.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CreateSessionResponse::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionResponse.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CreateSessionResponse::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionResponse.session_handle)
}
inline std::string* CreateSessionResponse::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateSessionResponse.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CreateSessionResponse::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateSessionResponse.session_handle)
}

// int64 graph_version = 2;
inline void CreateSessionResponse::clear_graph_version() {
  graph_version_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CreateSessionResponse::graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionResponse.graph_version)
  return graph_version_;
}
inline void CreateSessionResponse::set_graph_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  graph_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CreateSessionResponse.graph_version)
}

// -------------------------------------------------------------------

// ExtendSessionRequest

// string session_handle = 1;
inline void ExtendSessionRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ExtendSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionRequest.session_handle)
  return session_handle_.Get();
}
inline void ExtendSessionRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ExtendSessionRequest.session_handle)
}
inline void ExtendSessionRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ExtendSessionRequest.session_handle)
}
inline void ExtendSessionRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ExtendSessionRequest.session_handle)
}
inline void ExtendSessionRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ExtendSessionRequest.session_handle)
}
inline std::string* ExtendSessionRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ExtendSessionRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ExtendSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.ExtendSessionRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ExtendSessionRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ExtendSessionRequest.session_handle)
}
inline std::string* ExtendSessionRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ExtendSessionRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ExtendSessionRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ExtendSessionRequest.session_handle)
}

// .tensorflow.GraphDef graph_def = 2;
inline bool ExtendSessionRequest::has_graph_def() const {
  return this != internal_default_instance() && graph_def_ != nullptr;
}
inline const ::tensorflow::GraphDef& ExtendSessionRequest::graph_def() const {
  const ::tensorflow::GraphDef* p = graph_def_;
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionRequest.graph_def)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::GraphDef*>(
      &::tensorflow::_GraphDef_default_instance_);
}
inline ::tensorflow::GraphDef* ExtendSessionRequest::release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.ExtendSessionRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* ExtendSessionRequest::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ExtendSessionRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* ExtendSessionRequest::mutable_graph_def() {
  
  if (graph_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaNoVirtual());
    graph_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ExtendSessionRequest.graph_def)
  return graph_def_;
}
inline void ExtendSessionRequest::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def_);
  }
  if (graph_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def)->GetArena();
    if (message_arena != submessage_arena) {
      graph_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ExtendSessionRequest.graph_def)
}

// int64 current_graph_version = 3;
inline void ExtendSessionRequest::clear_current_graph_version() {
  current_graph_version_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExtendSessionRequest::current_graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionRequest.current_graph_version)
  return current_graph_version_;
}
inline void ExtendSessionRequest::set_current_graph_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  current_graph_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExtendSessionRequest.current_graph_version)
}

// -------------------------------------------------------------------

// ExtendSessionResponse

// int64 new_graph_version = 4;
inline void ExtendSessionResponse::clear_new_graph_version() {
  new_graph_version_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExtendSessionResponse::new_graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionResponse.new_graph_version)
  return new_graph_version_;
}
inline void ExtendSessionResponse::set_new_graph_version(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  new_graph_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExtendSessionResponse.new_graph_version)
}

// -------------------------------------------------------------------

// RunStepRequest

// string session_handle = 1;
inline void RunStepRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RunStepRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.session_handle)
  return session_handle_.Get();
}
inline void RunStepRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.session_handle)
}
inline void RunStepRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunStepRequest.session_handle)
}
inline void RunStepRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepRequest.session_handle)
}
inline void RunStepRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepRequest.session_handle)
}
inline std::string* RunStepRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RunStepRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunStepRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepRequest.session_handle)
}
inline std::string* RunStepRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunStepRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunStepRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunStepRequest.session_handle)
}

// repeated .tensorflow.NamedTensorProto feed = 2;
inline int RunStepRequest::feed_size() const {
  return feed_.size();
}
inline ::tensorflow::NamedTensorProto* RunStepRequest::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.feed)
  return feed_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunStepRequest::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepRequest.feed)
  return &feed_;
}
inline const ::tensorflow::NamedTensorProto& RunStepRequest::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.feed)
  return feed_.Get(index);
}
inline ::tensorflow::NamedTensorProto* RunStepRequest::add_feed() {
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.feed)
  return feed_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunStepRequest::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepRequest.feed)
  return feed_;
}

// repeated string fetch = 3;
inline int RunStepRequest::fetch_size() const {
  return fetch_.size();
}
inline void RunStepRequest::clear_fetch() {
  fetch_.Clear();
}
inline const std::string& RunStepRequest::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.fetch)
  return fetch_.Get(index);
}
inline std::string* RunStepRequest::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.fetch)
  return fetch_.Mutable(index);
}
inline void RunStepRequest::set_fetch(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.fetch)
  fetch_.Mutable(index)->assign(value);
}
inline void RunStepRequest::set_fetch(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.fetch)
  fetch_.Mutable(index)->assign(std::move(value));
}
inline void RunStepRequest::set_fetch(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::set_fetch(int index, const char* value, size_t size) {
  fetch_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepRequest.fetch)
}
inline std::string* RunStepRequest::add_fetch() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunStepRequest.fetch)
  return fetch_.Add();
}
inline void RunStepRequest::add_fetch(const std::string& value) {
  fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::add_fetch(std::string&& value) {
  fetch_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::add_fetch(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::add_fetch(const char* value, size_t size) {
  fetch_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunStepRequest.fetch)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RunStepRequest::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepRequest.fetch)
  return fetch_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RunStepRequest::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepRequest.fetch)
  return &fetch_;
}

// repeated string target = 4;
inline int RunStepRequest::target_size() const {
  return target_.size();
}
inline void RunStepRequest::clear_target() {
  target_.Clear();
}
inline const std::string& RunStepRequest::target(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.target)
  return target_.Get(index);
}
inline std::string* RunStepRequest::mutable_target(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.target)
  return target_.Mutable(index);
}
inline void RunStepRequest::set_target(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.target)
  target_.Mutable(index)->assign(value);
}
inline void RunStepRequest::set_target(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.target)
  target_.Mutable(index)->assign(std::move(value));
}
inline void RunStepRequest::set_target(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::set_target(int index, const char* value, size_t size) {
  target_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepRequest.target)
}
inline std::string* RunStepRequest::add_target() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunStepRequest.target)
  return target_.Add();
}
inline void RunStepRequest::add_target(const std::string& value) {
  target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::add_target(std::string&& value) {
  target_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::add_target(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::add_target(const char* value, size_t size) {
  target_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunStepRequest.target)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RunStepRequest::target() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepRequest.target)
  return target_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RunStepRequest::mutable_target() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepRequest.target)
  return &target_;
}

// .tensorflow.RunOptions options = 5;
inline bool RunStepRequest::has_options() const {
  return this != internal_default_instance() && options_ != nullptr;
}
inline const ::tensorflow::RunOptions& RunStepRequest::options() const {
  const ::tensorflow::RunOptions* p = options_;
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::RunOptions*>(
      &::tensorflow::_RunOptions_default_instance_);
}
inline ::tensorflow::RunOptions* RunStepRequest::release_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepRequest.options)
  
  ::tensorflow::RunOptions* temp = options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  options_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions* RunStepRequest::unsafe_arena_release_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunStepRequest.options)
  
  ::tensorflow::RunOptions* temp = options_;
  options_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions* RunStepRequest::mutable_options() {
  
  if (options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunOptions>(GetArenaNoVirtual());
    options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.options)
  return options_;
}
inline void RunStepRequest::set_allocated_options(::tensorflow::RunOptions* options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(options_);
  }
  if (options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(options)->GetArena();
    if (message_arena != submessage_arena) {
      options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, options, submessage_arena);
    }
    
  } else {
    
  }
  options_ = options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepRequest.options)
}

// string partial_run_handle = 6;
inline void RunStepRequest::clear_partial_run_handle() {
  partial_run_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RunStepRequest::partial_run_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.partial_run_handle)
  return partial_run_handle_.Get();
}
inline void RunStepRequest::set_partial_run_handle(const std::string& value) {
  
  partial_run_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.partial_run_handle)
}
inline void RunStepRequest::set_partial_run_handle(std::string&& value) {
  
  partial_run_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunStepRequest.partial_run_handle)
}
inline void RunStepRequest::set_partial_run_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  partial_run_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepRequest.partial_run_handle)
}
inline void RunStepRequest::set_partial_run_handle(const char* value,
    size_t size) {
  
  partial_run_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepRequest.partial_run_handle)
}
inline std::string* RunStepRequest::mutable_partial_run_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.partial_run_handle)
  return partial_run_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RunStepRequest::release_partial_run_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepRequest.partial_run_handle)
  
  return partial_run_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunStepRequest::set_allocated_partial_run_handle(std::string* partial_run_handle) {
  if (partial_run_handle != nullptr) {
    
  } else {
    
  }
  partial_run_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), partial_run_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepRequest.partial_run_handle)
}
inline std::string* RunStepRequest::unsafe_arena_release_partial_run_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunStepRequest.partial_run_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return partial_run_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunStepRequest::unsafe_arena_set_allocated_partial_run_handle(
    std::string* partial_run_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (partial_run_handle != nullptr) {
    
  } else {
    
  }
  partial_run_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      partial_run_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunStepRequest.partial_run_handle)
}

// bool store_errors_in_response_body = 7;
inline void RunStepRequest::clear_store_errors_in_response_body() {
  store_errors_in_response_body_ = false;
}
inline bool RunStepRequest::store_errors_in_response_body() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.store_errors_in_response_body)
  return store_errors_in_response_body_;
}
inline void RunStepRequest::set_store_errors_in_response_body(bool value) {
  
  store_errors_in_response_body_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.store_errors_in_response_body)
}

// int64 request_id = 8;
inline void RunStepRequest::clear_request_id() {
  request_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RunStepRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.request_id)
  return request_id_;
}
inline void RunStepRequest::set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.request_id)
}

// -------------------------------------------------------------------

// RunStepResponse

// repeated .tensorflow.NamedTensorProto tensor = 1;
inline int RunStepResponse::tensor_size() const {
  return tensor_.size();
}
inline ::tensorflow::NamedTensorProto* RunStepResponse::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepResponse.tensor)
  return tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunStepResponse::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepResponse.tensor)
  return &tensor_;
}
inline const ::tensorflow::NamedTensorProto& RunStepResponse::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.tensor)
  return tensor_.Get(index);
}
inline ::tensorflow::NamedTensorProto* RunStepResponse::add_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.RunStepResponse.tensor)
  return tensor_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunStepResponse::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepResponse.tensor)
  return tensor_;
}

// .tensorflow.RunMetadata metadata = 2;
inline bool RunStepResponse::has_metadata() const {
  return this != internal_default_instance() && metadata_ != nullptr;
}
inline const ::tensorflow::RunMetadata& RunStepResponse::metadata() const {
  const ::tensorflow::RunMetadata* p = metadata_;
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.metadata)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::RunMetadata*>(
      &::tensorflow::_RunMetadata_default_instance_);
}
inline ::tensorflow::RunMetadata* RunStepResponse::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepResponse.metadata)
  
  ::tensorflow::RunMetadata* temp = metadata_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::RunMetadata* RunStepResponse::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunStepResponse.metadata)
  
  ::tensorflow::RunMetadata* temp = metadata_;
  metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::RunMetadata* RunStepResponse::mutable_metadata() {
  
  if (metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunMetadata>(GetArenaNoVirtual());
    metadata_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepResponse.metadata)
  return metadata_;
}
inline void RunStepResponse::set_allocated_metadata(::tensorflow::RunMetadata* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata_);
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata)->GetArena();
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepResponse.metadata)
}

// .tensorflow.error.Code status_code = 3;
inline void RunStepResponse::clear_status_code() {
  status_code_ = 0;
}
inline ::tensorflow::error::Code RunStepResponse::status_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.status_code)
  return static_cast< ::tensorflow::error::Code >(status_code_);
}
inline void RunStepResponse::set_status_code(::tensorflow::error::Code value) {
  
  status_code_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunStepResponse.status_code)
}

// string status_error_message = 4;
inline void RunStepResponse::clear_status_error_message() {
  status_error_message_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RunStepResponse::status_error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.status_error_message)
  return status_error_message_.Get();
}
inline void RunStepResponse::set_status_error_message(const std::string& value) {
  
  status_error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunStepResponse.status_error_message)
}
inline void RunStepResponse::set_status_error_message(std::string&& value) {
  
  status_error_message_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunStepResponse.status_error_message)
}
inline void RunStepResponse::set_status_error_message(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  status_error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepResponse.status_error_message)
}
inline void RunStepResponse::set_status_error_message(const char* value,
    size_t size) {
  
  status_error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepResponse.status_error_message)
}
inline std::string* RunStepResponse::mutable_status_error_message() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepResponse.status_error_message)
  return status_error_message_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RunStepResponse::release_status_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepResponse.status_error_message)
  
  return status_error_message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunStepResponse::set_allocated_status_error_message(std::string* status_error_message) {
  if (status_error_message != nullptr) {
    
  } else {
    
  }
  status_error_message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), status_error_message,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepResponse.status_error_message)
}
inline std::string* RunStepResponse::unsafe_arena_release_status_error_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunStepResponse.status_error_message)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return status_error_message_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunStepResponse::unsafe_arena_set_allocated_status_error_message(
    std::string* status_error_message) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (status_error_message != nullptr) {
    
  } else {
    
  }
  status_error_message_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      status_error_message, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunStepResponse.status_error_message)
}

// -------------------------------------------------------------------

// PartialRunSetupRequest

// string session_handle = 1;
inline void PartialRunSetupRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& PartialRunSetupRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.session_handle)
  return session_handle_.Get();
}
inline void PartialRunSetupRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.session_handle)
}
inline void PartialRunSetupRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PartialRunSetupRequest.session_handle)
}
inline void PartialRunSetupRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.session_handle)
}
inline void PartialRunSetupRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.session_handle)
}
inline std::string* PartialRunSetupRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* PartialRunSetupRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.PartialRunSetupRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PartialRunSetupRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PartialRunSetupRequest.session_handle)
}
inline std::string* PartialRunSetupRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PartialRunSetupRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PartialRunSetupRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PartialRunSetupRequest.session_handle)
}

// repeated string feed = 2;
inline int PartialRunSetupRequest::feed_size() const {
  return feed_.size();
}
inline void PartialRunSetupRequest::clear_feed() {
  feed_.Clear();
}
inline const std::string& PartialRunSetupRequest::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.feed)
  return feed_.Get(index);
}
inline std::string* PartialRunSetupRequest::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.feed)
  return feed_.Mutable(index);
}
inline void PartialRunSetupRequest::set_feed(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.feed)
  feed_.Mutable(index)->assign(value);
}
inline void PartialRunSetupRequest::set_feed(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.feed)
  feed_.Mutable(index)->assign(std::move(value));
}
inline void PartialRunSetupRequest::set_feed(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  feed_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::set_feed(int index, const char* value, size_t size) {
  feed_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.feed)
}
inline std::string* PartialRunSetupRequest::add_feed() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.PartialRunSetupRequest.feed)
  return feed_.Add();
}
inline void PartialRunSetupRequest::add_feed(const std::string& value) {
  feed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::add_feed(std::string&& value) {
  feed_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::add_feed(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  feed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::add_feed(const char* value, size_t size) {
  feed_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.PartialRunSetupRequest.feed)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
PartialRunSetupRequest::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.PartialRunSetupRequest.feed)
  return feed_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
PartialRunSetupRequest::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.PartialRunSetupRequest.feed)
  return &feed_;
}

// repeated string fetch = 3;
inline int PartialRunSetupRequest::fetch_size() const {
  return fetch_.size();
}
inline void PartialRunSetupRequest::clear_fetch() {
  fetch_.Clear();
}
inline const std::string& PartialRunSetupRequest::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.fetch)
  return fetch_.Get(index);
}
inline std::string* PartialRunSetupRequest::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.fetch)
  return fetch_.Mutable(index);
}
inline void PartialRunSetupRequest::set_fetch(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.fetch)
  fetch_.Mutable(index)->assign(value);
}
inline void PartialRunSetupRequest::set_fetch(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.fetch)
  fetch_.Mutable(index)->assign(std::move(value));
}
inline void PartialRunSetupRequest::set_fetch(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::set_fetch(int index, const char* value, size_t size) {
  fetch_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.fetch)
}
inline std::string* PartialRunSetupRequest::add_fetch() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.PartialRunSetupRequest.fetch)
  return fetch_.Add();
}
inline void PartialRunSetupRequest::add_fetch(const std::string& value) {
  fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::add_fetch(std::string&& value) {
  fetch_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::add_fetch(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::add_fetch(const char* value, size_t size) {
  fetch_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.PartialRunSetupRequest.fetch)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
PartialRunSetupRequest::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.PartialRunSetupRequest.fetch)
  return fetch_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
PartialRunSetupRequest::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.PartialRunSetupRequest.fetch)
  return &fetch_;
}

// repeated string target = 4;
inline int PartialRunSetupRequest::target_size() const {
  return target_.size();
}
inline void PartialRunSetupRequest::clear_target() {
  target_.Clear();
}
inline const std::string& PartialRunSetupRequest::target(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.target)
  return target_.Get(index);
}
inline std::string* PartialRunSetupRequest::mutable_target(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.target)
  return target_.Mutable(index);
}
inline void PartialRunSetupRequest::set_target(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.target)
  target_.Mutable(index)->assign(value);
}
inline void PartialRunSetupRequest::set_target(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.target)
  target_.Mutable(index)->assign(std::move(value));
}
inline void PartialRunSetupRequest::set_target(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::set_target(int index, const char* value, size_t size) {
  target_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.target)
}
inline std::string* PartialRunSetupRequest::add_target() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.PartialRunSetupRequest.target)
  return target_.Add();
}
inline void PartialRunSetupRequest::add_target(const std::string& value) {
  target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::add_target(std::string&& value) {
  target_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::add_target(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::add_target(const char* value, size_t size) {
  target_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.PartialRunSetupRequest.target)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
PartialRunSetupRequest::target() const {
  // @@protoc_insertion_point(field_list:tensorflow.PartialRunSetupRequest.target)
  return target_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
PartialRunSetupRequest::mutable_target() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.PartialRunSetupRequest.target)
  return &target_;
}

// int64 request_id = 5;
inline void PartialRunSetupRequest::clear_request_id() {
  request_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 PartialRunSetupRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.request_id)
  return request_id_;
}
inline void PartialRunSetupRequest::set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.request_id)
}

// -------------------------------------------------------------------

// PartialRunSetupResponse

// string partial_run_handle = 1;
inline void PartialRunSetupResponse::clear_partial_run_handle() {
  partial_run_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& PartialRunSetupResponse::partial_run_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupResponse.partial_run_handle)
  return partial_run_handle_.Get();
}
inline void PartialRunSetupResponse::set_partial_run_handle(const std::string& value) {
  
  partial_run_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
inline void PartialRunSetupResponse::set_partial_run_handle(std::string&& value) {
  
  partial_run_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
inline void PartialRunSetupResponse::set_partial_run_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  partial_run_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
inline void PartialRunSetupResponse::set_partial_run_handle(const char* value,
    size_t size) {
  
  partial_run_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
inline std::string* PartialRunSetupResponse::mutable_partial_run_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupResponse.partial_run_handle)
  return partial_run_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* PartialRunSetupResponse::release_partial_run_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.PartialRunSetupResponse.partial_run_handle)
  
  return partial_run_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PartialRunSetupResponse::set_allocated_partial_run_handle(std::string* partial_run_handle) {
  if (partial_run_handle != nullptr) {
    
  } else {
    
  }
  partial_run_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), partial_run_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
inline std::string* PartialRunSetupResponse::unsafe_arena_release_partial_run_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PartialRunSetupResponse.partial_run_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return partial_run_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PartialRunSetupResponse::unsafe_arena_set_allocated_partial_run_handle(
    std::string* partial_run_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (partial_run_handle != nullptr) {
    
  } else {
    
  }
  partial_run_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      partial_run_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PartialRunSetupResponse.partial_run_handle)
}

// -------------------------------------------------------------------

// CloseSessionRequest

// string session_handle = 1;
inline void CloseSessionRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CloseSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.CloseSessionRequest.session_handle)
  return session_handle_.Get();
}
inline void CloseSessionRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CloseSessionRequest.session_handle)
}
inline void CloseSessionRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CloseSessionRequest.session_handle)
}
inline void CloseSessionRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CloseSessionRequest.session_handle)
}
inline void CloseSessionRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CloseSessionRequest.session_handle)
}
inline std::string* CloseSessionRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CloseSessionRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CloseSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.CloseSessionRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CloseSessionRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CloseSessionRequest.session_handle)
}
inline std::string* CloseSessionRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CloseSessionRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CloseSessionRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CloseSessionRequest.session_handle)
}

// -------------------------------------------------------------------

// CloseSessionResponse

// -------------------------------------------------------------------

// ResetRequest

// repeated string container = 1;
inline int ResetRequest::container_size() const {
  return container_.size();
}
inline void ResetRequest::clear_container() {
  container_.Clear();
}
inline const std::string& ResetRequest::container(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ResetRequest.container)
  return container_.Get(index);
}
inline std::string* ResetRequest::mutable_container(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ResetRequest.container)
  return container_.Mutable(index);
}
inline void ResetRequest::set_container(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.container)
  container_.Mutable(index)->assign(value);
}
inline void ResetRequest::set_container(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.container)
  container_.Mutable(index)->assign(std::move(value));
}
inline void ResetRequest::set_container(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  container_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ResetRequest.container)
}
inline void ResetRequest::set_container(int index, const char* value, size_t size) {
  container_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ResetRequest.container)
}
inline std::string* ResetRequest::add_container() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ResetRequest.container)
  return container_.Add();
}
inline void ResetRequest::add_container(const std::string& value) {
  container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.container)
}
inline void ResetRequest::add_container(std::string&& value) {
  container_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.container)
}
inline void ResetRequest::add_container(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ResetRequest.container)
}
inline void ResetRequest::add_container(const char* value, size_t size) {
  container_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ResetRequest.container)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ResetRequest::container() const {
  // @@protoc_insertion_point(field_list:tensorflow.ResetRequest.container)
  return container_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ResetRequest::mutable_container() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ResetRequest.container)
  return &container_;
}

// repeated string device_filters = 2;
inline int ResetRequest::device_filters_size() const {
  return device_filters_.size();
}
inline void ResetRequest::clear_device_filters() {
  device_filters_.Clear();
}
inline const std::string& ResetRequest::device_filters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ResetRequest.device_filters)
  return device_filters_.Get(index);
}
inline std::string* ResetRequest::mutable_device_filters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ResetRequest.device_filters)
  return device_filters_.Mutable(index);
}
inline void ResetRequest::set_device_filters(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.device_filters)
  device_filters_.Mutable(index)->assign(value);
}
inline void ResetRequest::set_device_filters(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.device_filters)
  device_filters_.Mutable(index)->assign(std::move(value));
}
inline void ResetRequest::set_device_filters(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  device_filters_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::set_device_filters(int index, const char* value, size_t size) {
  device_filters_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ResetRequest.device_filters)
}
inline std::string* ResetRequest::add_device_filters() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ResetRequest.device_filters)
  return device_filters_.Add();
}
inline void ResetRequest::add_device_filters(const std::string& value) {
  device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::add_device_filters(std::string&& value) {
  device_filters_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::add_device_filters(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::add_device_filters(const char* value, size_t size) {
  device_filters_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ResetRequest.device_filters)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ResetRequest::device_filters() const {
  // @@protoc_insertion_point(field_list:tensorflow.ResetRequest.device_filters)
  return device_filters_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ResetRequest::mutable_device_filters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ResetRequest.device_filters)
  return &device_filters_;
}

// -------------------------------------------------------------------

// ResetResponse

// -------------------------------------------------------------------

// ListDevicesRequest

// string session_handle = 1;
inline void ListDevicesRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ListDevicesRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ListDevicesRequest.session_handle)
  return session_handle_.Get();
}
inline void ListDevicesRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ListDevicesRequest.session_handle)
}
inline void ListDevicesRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ListDevicesRequest.session_handle)
}
inline void ListDevicesRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ListDevicesRequest.session_handle)
}
inline void ListDevicesRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ListDevicesRequest.session_handle)
}
inline std::string* ListDevicesRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ListDevicesRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ListDevicesRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.ListDevicesRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ListDevicesRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ListDevicesRequest.session_handle)
}
inline std::string* ListDevicesRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ListDevicesRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ListDevicesRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ListDevicesRequest.session_handle)
}

// -------------------------------------------------------------------

// ListDevicesResponse

// repeated .tensorflow.DeviceAttributes local_device = 1;
inline int ListDevicesResponse::local_device_size() const {
  return local_device_.size();
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::mutable_local_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ListDevicesResponse.local_device)
  return local_device_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
ListDevicesResponse::mutable_local_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ListDevicesResponse.local_device)
  return &local_device_;
}
inline const ::tensorflow::DeviceAttributes& ListDevicesResponse::local_device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ListDevicesResponse.local_device)
  return local_device_.Get(index);
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::add_local_device() {
  // @@protoc_insertion_point(field_add:tensorflow.ListDevicesResponse.local_device)
  return local_device_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
ListDevicesResponse::local_device() const {
  // @@protoc_insertion_point(field_list:tensorflow.ListDevicesResponse.local_device)
  return local_device_;
}

// repeated .tensorflow.DeviceAttributes remote_device = 2;
inline int ListDevicesResponse::remote_device_size() const {
  return remote_device_.size();
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::mutable_remote_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ListDevicesResponse.remote_device)
  return remote_device_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
ListDevicesResponse::mutable_remote_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ListDevicesResponse.remote_device)
  return &remote_device_;
}
inline const ::tensorflow::DeviceAttributes& ListDevicesResponse::remote_device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ListDevicesResponse.remote_device)
  return remote_device_.Get(index);
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::add_remote_device() {
  // @@protoc_insertion_point(field_add:tensorflow.ListDevicesResponse.remote_device)
  return remote_device_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
ListDevicesResponse::remote_device() const {
  // @@protoc_insertion_point(field_list:tensorflow.ListDevicesResponse.remote_device)
  return remote_device_;
}

// -------------------------------------------------------------------

// MakeCallableRequest

// string session_handle = 1;
inline void MakeCallableRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& MakeCallableRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableRequest.session_handle)
  return session_handle_.Get();
}
inline void MakeCallableRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MakeCallableRequest.session_handle)
}
inline void MakeCallableRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MakeCallableRequest.session_handle)
}
inline void MakeCallableRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MakeCallableRequest.session_handle)
}
inline void MakeCallableRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MakeCallableRequest.session_handle)
}
inline std::string* MakeCallableRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MakeCallableRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* MakeCallableRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.MakeCallableRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MakeCallableRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MakeCallableRequest.session_handle)
}
inline std::string* MakeCallableRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MakeCallableRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MakeCallableRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MakeCallableRequest.session_handle)
}

// .tensorflow.CallableOptions options = 2;
inline bool MakeCallableRequest::has_options() const {
  return this != internal_default_instance() && options_ != nullptr;
}
inline const ::tensorflow::CallableOptions& MakeCallableRequest::options() const {
  const ::tensorflow::CallableOptions* p = options_;
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableRequest.options)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::CallableOptions*>(
      &::tensorflow::_CallableOptions_default_instance_);
}
inline ::tensorflow::CallableOptions* MakeCallableRequest::release_options() {
  // @@protoc_insertion_point(field_release:tensorflow.MakeCallableRequest.options)
  
  ::tensorflow::CallableOptions* temp = options_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  options_ = nullptr;
  return temp;
}
inline ::tensorflow::CallableOptions* MakeCallableRequest::unsafe_arena_release_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MakeCallableRequest.options)
  
  ::tensorflow::CallableOptions* temp = options_;
  options_ = nullptr;
  return temp;
}
inline ::tensorflow::CallableOptions* MakeCallableRequest::mutable_options() {
  
  if (options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CallableOptions>(GetArenaNoVirtual());
    options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MakeCallableRequest.options)
  return options_;
}
inline void MakeCallableRequest::set_allocated_options(::tensorflow::CallableOptions* options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(options_);
  }
  if (options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(options)->GetArena();
    if (message_arena != submessage_arena) {
      options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, options, submessage_arena);
    }
    
  } else {
    
  }
  options_ = options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MakeCallableRequest.options)
}

// int64 request_id = 3;
inline void MakeCallableRequest::clear_request_id() {
  request_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MakeCallableRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableRequest.request_id)
  return request_id_;
}
inline void MakeCallableRequest::set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MakeCallableRequest.request_id)
}

// -------------------------------------------------------------------

// MakeCallableResponse

// int64 handle = 1;
inline void MakeCallableResponse::clear_handle() {
  handle_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 MakeCallableResponse::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableResponse.handle)
  return handle_;
}
inline void MakeCallableResponse::set_handle(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MakeCallableResponse.handle)
}

// -------------------------------------------------------------------

// RunCallableRequest

// string session_handle = 1;
inline void RunCallableRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& RunCallableRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.session_handle)
  return session_handle_.Get();
}
inline void RunCallableRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunCallableRequest.session_handle)
}
inline void RunCallableRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunCallableRequest.session_handle)
}
inline void RunCallableRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunCallableRequest.session_handle)
}
inline void RunCallableRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunCallableRequest.session_handle)
}
inline std::string* RunCallableRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* RunCallableRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunCallableRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunCallableRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunCallableRequest.session_handle)
}
inline std::string* RunCallableRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunCallableRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunCallableRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunCallableRequest.session_handle)
}

// int64 handle = 2;
inline void RunCallableRequest::clear_handle() {
  handle_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RunCallableRequest::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.handle)
  return handle_;
}
inline void RunCallableRequest::set_handle(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunCallableRequest.handle)
}

// repeated .tensorflow.TensorProto feed = 3;
inline int RunCallableRequest::feed_size() const {
  return feed_.size();
}
inline ::tensorflow::TensorProto* RunCallableRequest::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableRequest.feed)
  return feed_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
RunCallableRequest::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunCallableRequest.feed)
  return &feed_;
}
inline const ::tensorflow::TensorProto& RunCallableRequest::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.feed)
  return feed_.Get(index);
}
inline ::tensorflow::TensorProto* RunCallableRequest::add_feed() {
  // @@protoc_insertion_point(field_add:tensorflow.RunCallableRequest.feed)
  return feed_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
RunCallableRequest::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunCallableRequest.feed)
  return feed_;
}

// int64 request_id = 4;
inline void RunCallableRequest::clear_request_id() {
  request_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RunCallableRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.request_id)
  return request_id_;
}
inline void RunCallableRequest::set_request_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunCallableRequest.request_id)
}

// -------------------------------------------------------------------

// RunCallableResponse

// repeated .tensorflow.TensorProto fetch = 1;
inline int RunCallableResponse::fetch_size() const {
  return fetch_.size();
}
inline ::tensorflow::TensorProto* RunCallableResponse::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableResponse.fetch)
  return fetch_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
RunCallableResponse::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunCallableResponse.fetch)
  return &fetch_;
}
inline const ::tensorflow::TensorProto& RunCallableResponse::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableResponse.fetch)
  return fetch_.Get(index);
}
inline ::tensorflow::TensorProto* RunCallableResponse::add_fetch() {
  // @@protoc_insertion_point(field_add:tensorflow.RunCallableResponse.fetch)
  return fetch_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
RunCallableResponse::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunCallableResponse.fetch)
  return fetch_;
}

// .tensorflow.RunMetadata metadata = 2;
inline bool RunCallableResponse::has_metadata() const {
  return this != internal_default_instance() && metadata_ != nullptr;
}
inline const ::tensorflow::RunMetadata& RunCallableResponse::metadata() const {
  const ::tensorflow::RunMetadata* p = metadata_;
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableResponse.metadata)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::RunMetadata*>(
      &::tensorflow::_RunMetadata_default_instance_);
}
inline ::tensorflow::RunMetadata* RunCallableResponse::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.RunCallableResponse.metadata)
  
  ::tensorflow::RunMetadata* temp = metadata_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::RunMetadata* RunCallableResponse::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunCallableResponse.metadata)
  
  ::tensorflow::RunMetadata* temp = metadata_;
  metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::RunMetadata* RunCallableResponse::mutable_metadata() {
  
  if (metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunMetadata>(GetArenaNoVirtual());
    metadata_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableResponse.metadata)
  return metadata_;
}
inline void RunCallableResponse::set_allocated_metadata(::tensorflow::RunMetadata* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata_);
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata)->GetArena();
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunCallableResponse.metadata)
}

// -------------------------------------------------------------------

// ReleaseCallableRequest

// string session_handle = 1;
inline void ReleaseCallableRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ReleaseCallableRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReleaseCallableRequest.session_handle)
  return session_handle_.Get();
}
inline void ReleaseCallableRequest::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ReleaseCallableRequest.session_handle)
}
inline void ReleaseCallableRequest::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ReleaseCallableRequest.session_handle)
}
inline void ReleaseCallableRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ReleaseCallableRequest.session_handle)
}
inline void ReleaseCallableRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ReleaseCallableRequest.session_handle)
}
inline std::string* ReleaseCallableRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ReleaseCallableRequest.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ReleaseCallableRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.ReleaseCallableRequest.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ReleaseCallableRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ReleaseCallableRequest.session_handle)
}
inline std::string* ReleaseCallableRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReleaseCallableRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ReleaseCallableRequest::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReleaseCallableRequest.session_handle)
}

// int64 handle = 2;
inline void ReleaseCallableRequest::clear_handle() {
  handle_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ReleaseCallableRequest::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReleaseCallableRequest.handle)
  return handle_;
}
inline void ReleaseCallableRequest::set_handle(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ReleaseCallableRequest.handle)
}

// -------------------------------------------------------------------

// ReleaseCallableResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto
