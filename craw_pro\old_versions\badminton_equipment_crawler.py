#!/usr/bin/env python3
"""
羽毛球装备详细信息爬虫
专门爬取中羽在线装备数据，包含完整的技术参数和价格信息
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import csv
from datetime import datetime
import logging
import os
from urllib.parse import urljoin

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BadmintonEquipmentCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
        self.base_url = "https://www.badmintoncn.com"
        
        # 装备类型映射
        self.equipment_types = {
            1: "羽毛球拍",
            2: "羽毛球鞋", 
            3: "运动包",
            4: "羽毛球线",
            5: "羽毛球",
            6: "运动服饰",
            7: "手胶"
        }
        
        os.makedirs('output', exist_ok=True)
        logger.info("🏸 羽毛球装备爬虫初始化完成")

    def ask_ai_for_answer(self, question):
        """AI验证问题回答"""
        try:
            question_lower = str(question).lower()
            
            # 直接回答
            direct_answers = {
                '羽毛球有几根毛': '16',
                '羽毛球几根毛': '16', 
                'zyzx小写怎么写': 'zyzx',
                'zyzx大写怎么写': 'ZYZX',
                'zyzx怎么写': 'zyzx',
                '中羽在线英文缩写': 'ZYZX',
                '中羽缩写': 'ZYZX',
            }
            
            for key, answer in direct_answers.items():
                if key in question_lower:
                    return answer
            
            # 数学计算
            math_result = self.calculate_math_expression(question)
            if math_result is not None:
                return str(math_result)
            
            return "42"  # 默认答案
                
        except Exception as e:
            logger.error(f"AI验证失败: {e}")
            return "42"
    
    def calculate_math_expression(self, question):
        """计算数学表达式"""
        try:
            patterns = [
                r'(\d+)\s*[×*]\s*(\d+)',
                r'(\d+)\s*[+]\s*(\d+)',
                r'(\d+)\s*[-]\s*(\d+)',
                r'(\d+)\s*[/÷]\s*(\d+)',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, str(question))
                if match:
                    num1, num2 = int(match.group(1)), int(match.group(2))
                    
                    if '×' in question or '*' in question:
                        return num1 * num2
                    elif '+' in question:
                        return num1 + num2
                    elif '-' in question:
                        return num1 - num2
                    elif '/' in question or '÷' in question:
                        return num1 // num2 if num2 != 0 else None
            
            return None
        except Exception:
            return None

    def handle_verification(self, url, max_retries=3):
        """处理验证和访问"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=20)
                
                if response.status_code != 200:
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                    return None
                
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text()
                
                # 检查验证
                verification_patterns = [
                    r'\d+[×*]\d+=？', r'\d+[+]\d+=？', r'\d+[-]\d+=？',
                    r'羽毛球有几根毛', r'ZYZX.*?怎么写', r'中羽.*?缩写',
                ]
                
                has_verification = any(re.search(pattern, page_text, re.IGNORECASE) 
                                     for pattern in verification_patterns)
                
                verification_form = soup.find('form')
                if verification_form and has_verification:
                    if self.solve_verification(verification_form, page_text, url):
                        time.sleep(2)
                        final_response = self.session.get(url, timeout=20)
                        if final_response.status_code == 200:
                            return final_response.text
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue
                else:
                    return response.text
                    
            except Exception as e:
                logger.error(f"访问失败 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue
        
        return None

    def solve_verification(self, form, page_text, original_url):
        """解决验证"""
        try:
            # 查找问题
            question_patterns = [
                r'(\d+[×*]\d+)=？', r'(\d+[+]\d+)=？', r'(\d+[-]\d+)=？',
                r'羽毛球有几根毛', r'ZYZX.*?怎么写', r'中羽.*?缩写',
            ]
            
            question = None
            for pattern in question_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    question = matches[0]
                    break
            
            if not question:
                return False
            
            answer = self.ask_ai_for_answer(question)
            if not answer:
                return False
            
            # 表单数据
            form_data = {}
            
            for inp in form.find_all('input', {'type': 'hidden'}):
                name = inp.get('name')
                value = inp.get('value', '')
                if name:
                    form_data[name] = value
            
            # 答案字段
            answer_fields = ['answer', 'verify', 'code', 'result', 'a']
            for field in answer_fields:
                if form.find('input', {'name': field}):
                    form_data[field] = answer
                    break
            else:
                form_data['a'] = answer
            
            # 提交
            action = form.get('action', original_url)
            submit_url = urljoin(self.base_url, action) if not action.startswith('http') else action
            
            response = self.session.post(submit_url, data=form_data, timeout=20)
            return response.status_code == 200
                
        except Exception as e:
            logger.error(f"验证失败: {e}")
            return False

    def get_equipment_list(self, equipment_type_id=None):
        """获取装备列表"""
        try:
            if equipment_type_id:
                list_url = f"{self.base_url}/cbo_eq/list.php?tid={equipment_type_id}"
                type_name = self.equipment_types.get(equipment_type_id, f"类型{equipment_type_id}")
                logger.info(f"🔍 获取{type_name}装备列表...")
            else:
                list_url = f"{self.base_url}/cbo_eq/list.php"
                logger.info("🔍 获取装备列表...")
            
            html_content = self.handle_verification(list_url)
            if not html_content:
                return []
            
            soup = BeautifulSoup(html_content, 'html.parser')
            equipment_links = []
            
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and 'view.php?eid=' in href:
                    if not href.startswith('http'):
                        href = urljoin(self.base_url, href)
                    equipment_links.append(href)
            
            equipment_links = list(set(equipment_links))
            logger.info(f"找到 {len(equipment_links)} 个装备")
            return equipment_links
            
        except Exception as e:
            logger.error(f"获取装备列表失败: {e}")
            return []

    def parse_equipment_detail(self, url):
        """解析装备详情"""
        try:
            eid_match = re.search(r'eid=(\d+)', url)
            equipment_id = eid_match.group(1) if eid_match else 'unknown'
            
            logger.info(f"📋 解析装备 (ID: {equipment_id})")
            
            html_content = self.handle_verification(url)
            if not html_content:
                return None
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 初始化数据
            equipment_data = {
                'equipment_id': equipment_id,
                'equipment_name': '',
                'equipment_type': '',
                'equipment_brand': '',
                'equipment_series': '',
                'equipment_description': '',
                'release_date': '',
                'equipment_introduction': '',
                'specifications': '',
                'frame_material': '',
                'shaft_material': '',
                'weight': '',
                'length': '',
                'grip_size': '',
                'shaft_stiffness': '',
                'string_tension': '',
                'balance_point': '',
                'new_avg_price': '',
                'used_avg_price': '',
                'total_registered_users': '',
                'detail_url': url,
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 装备名称
            if soup.title:
                title = soup.title.string.strip()
                equipment_name = title.replace('中羽在线 badmintoncn.com', '').strip()
                equipment_data['equipment_name'] = equipment_name[:100]
            
            # 提取表格信息
            self.extract_table_info(soup, equipment_data)
            
            # 获取价格信息
            self.get_price_info(equipment_id, equipment_data)
            
            return equipment_data
            
        except Exception as e:
            logger.error(f"解析装备详情失败: {e}")
            return None

    def extract_table_info(self, soup, equipment_data):
        """提取表格信息"""
        try:
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        # 字段映射
                        mappings = {
                            '装备类型': 'equipment_type', '类型': 'equipment_type',
                            '装备品牌': 'equipment_brand', '品牌': 'equipment_brand',
                            '装备系列': 'equipment_series', '系列': 'equipment_series',
                            '上市日期': 'release_date', '发布日期': 'release_date',
                            '拍框材质': 'frame_material', '框架材质': 'frame_material',
                            '拍杆材质': 'shaft_material', '中管材质': 'shaft_material',
                            '重量': 'weight', '拍重': 'weight', '拍身重量': 'weight',
                            '长度': 'length', '拍身长度': 'length',
                            '手柄尺寸': 'grip_size', '拍柄粗细': 'grip_size',
                            '中管韧度': 'shaft_stiffness', '硬度': 'shaft_stiffness',
                            '拉线磅数': 'string_tension', '穿线磅数': 'string_tension',
                            '平衡点': 'balance_point', '重心': 'balance_point',
                        }
                        
                        for keyword, field in mappings.items():
                            if keyword in key and not equipment_data[field]:
                                equipment_data[field] = value
                                break
                        
                        # 规格参数
                        if key and value:
                            if equipment_data['specifications']:
                                equipment_data['specifications'] += f"; {key}: {value}"
                            else:
                                equipment_data['specifications'] = f"{key}: {value}"
                                
        except Exception as e:
            logger.error(f"提取表格信息失败: {e}")

    def get_price_info(self, equipment_id, equipment_data):
        """获取价格信息"""
        try:
            price_url = f"{self.base_url}/cbo_eq/view_buy.php?eid={equipment_id}"
            price_html = self.handle_verification(price_url)
            
            if not price_html:
                return
                
            price_soup = BeautifulSoup(price_html, 'html.parser')
            price_text = price_soup.get_text()
            
            # 价格模式
            patterns = {
                'new_avg_price': [r'最近全新均价[：:]\s*(\d+)', r'全新均价[：:]\s*(\d+)'],
                'used_avg_price': [r'最近二手均价[：:]\s*(\d+)', r'二手均价[：:]\s*(\d+)'],
                'total_registered_users': [r'总登记球友[：:]\s*(\d+)', r'登记球友[：:]\s*(\d+)'],
            }
            
            for field, field_patterns in patterns.items():
                for pattern in field_patterns:
                    match = re.search(pattern, price_text)
                    if match:
                        equipment_data[field] = match.group(1)
                        break
                        
        except Exception as e:
            logger.error(f"获取价格信息失败: {e}")

    def crawl_by_type(self, equipment_type_id, max_items=5):
        """按类型爬取"""
        type_name = self.equipment_types.get(equipment_type_id, f"类型{equipment_type_id}")
        logger.info(f"\n📦 开始爬取 {type_name}")
        
        equipment_links = self.get_equipment_list(equipment_type_id)
        if not equipment_links:
            return []
        
        data = []
        for i, link in enumerate(equipment_links[:max_items]):
            logger.info(f"  🔍 解析 ({i+1}/{max_items})")
            
            equipment_data = self.parse_equipment_detail(link)
            if equipment_data:
                equipment_data['equipment_type'] = type_name
                data.append(equipment_data)
                
                filled = sum(1 for v in equipment_data.values() if v and v != '')
                total = len(equipment_data)
                logger.info(f"    ✅ 完整度: {filled/total*100:.1f}% ({filled}/{total})")
            
            time.sleep(3)
        
        return data

    def crawl_all_types(self, max_items_per_type=3):
        """爬取所有类型"""
        logger.info("🚀 开始爬取所有装备类型...")
        
        all_data = []
        for type_id in self.equipment_types.keys():
            type_data = self.crawl_by_type(type_id, max_items_per_type)
            all_data.extend(type_data)
            time.sleep(5)
        
        logger.info(f"\n🎉 爬取完成！总共 {len(all_data)} 条数据")
        return all_data

    def save_data(self, data):
        """保存数据"""
        if not data:
            return
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # CSV
        csv_file = f"output/equipment_{timestamp}.csv"
        fieldnames = list(data[0].keys())
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        # JSON
        json_file = f"output/equipment_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📁 数据已保存: {csv_file}, {json_file}")
        return csv_file, json_file

    def analyze_data(self, data):
        """分析数据"""
        if not data:
            return
            
        logger.info("\n📊 数据分析:")
        logger.info(f"总数量: {len(data)}")
        
        # 类型分布
        types = {}
        brands = {}
        
        for item in data:
            eq_type = item.get('equipment_type', '未知')
            types[eq_type] = types.get(eq_type, 0) + 1
            
            brand = item.get('equipment_brand', '未知')
            brands[brand] = brands.get(brand, 0) + 1
        
        logger.info("\n类型分布:")
        for eq_type, count in sorted(types.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {eq_type}: {count}")
        
        logger.info("\n品牌分布:")
        for brand, count in sorted(brands.items(), key=lambda x: x[1], reverse=True)[:5]:
            logger.info(f"  {brand}: {count}")

def main():
    """主函数"""
    crawler = BadmintonEquipmentCrawler()
    
    # 爬取数据
    data = crawler.crawl_all_types(max_items_per_type=2)
    
    if data:
        # 保存和分析
        crawler.save_data(data)
        crawler.analyze_data(data)
    else:
        logger.error("未获取到数据")

if __name__ == "__main__":
    main() 