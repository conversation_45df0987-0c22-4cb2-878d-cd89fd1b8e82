import gzip
import json
import os
import threading
import time
import uuid
from datetime import datetime

from apps.models.ecg_analysis_modes import TAnalysisLog


def record(custom_id, interface_log):
    """
    记录日志
    接口请求在数据库中留痕，请求和响应参数保存物理文件，数据记录路径
    :param custom_id: 客户主键ID
    :param interface_log: 日志对象
    :return: 日志记录的ID
    """
    date_str = datetime.now().strftime('%Y/%m/%d')
    log_dir = os.path.join('logs', 'api', date_str, str(custom_id))
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    date_str = datetime.now().strftime('%Y%m%d')
    random_uuid = uuid.uuid4().hex
    log_filename = f"{date_str}_{random_uuid}.log"
    log_file_path = os.path.join(log_dir, log_filename)

    # 这里是数据库操作部分
    analysis_log = TAnalysisLog()
    analysis_log.custom_id = custom_id
    analysis_log.analysis_info_path = log_file_path
    analysis_log.create_date = int(time.time())

    analysis_log.save()

    thread_save_interface = threading.Thread(target=save_interface_log, args=(log_file_path, interface_log,))
    thread_save_interface.start()

    return analysis_log.id


def save_interface_log(log_file_path, interface_log):
    """
    保存接口日志
    :param log_file_path: 日志文件路径
    :param interface_log: 接口日志对象
    :return:
    """
    log_content = json.dumps(interface_log.to_entity_dict())  # 获取日志文件jons格式

    # 压缩log_content
    compressed_log_content = gzip.compress(log_content.encode())

    # 将日志内容写入文件
    with open(log_file_path, 'wb') as file:
        file.write(compressed_log_content)
