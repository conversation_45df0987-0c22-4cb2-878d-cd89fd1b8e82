
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterAsyncPasses();


/* Create Async Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAsyncAsyncParallelFor();
MLIR_CAPI_EXPORTED void mlirRegisterAsyncAsyncParallelFor();


/* Create Async Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAsyncAsyncRuntimeRefCounting();
MLIR_CAPI_EXPORTED void mlirRegisterAsyncAsyncRuntimeRefCounting();


/* Create Async Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAsyncAsyncRuntimeRefCountingOpt();
MLIR_CAPI_EXPORTED void mlirRegisterAsyncAsyncRuntimeRefCountingOpt();


/* Create Async Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAsyncAsyncToAsyncRuntime();
MLIR_CAPI_EXPORTED void mlirRegisterAsyncAsyncToAsyncRuntime();



#ifdef __cplusplus
}
#endif
