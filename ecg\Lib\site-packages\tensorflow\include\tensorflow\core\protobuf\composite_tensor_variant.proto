syntax = "proto3";

package tensorflow;

import "tensorflow/core/protobuf/struct.proto";

option go_package = "github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto";

// Metadata for CompositeTensorVariant, used when serializing as Variant.
//
// We define a new message here (rather than directly using TypeSpecProto for
// the metadata string) to retain flexibility to change the metadata encoding
// to support additional features.
message CompositeTensorVariantMetadata {
  TypeSpecProto type_spec_proto = 1;
}
