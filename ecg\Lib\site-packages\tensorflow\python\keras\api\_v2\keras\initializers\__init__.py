# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Keras initializer serialization / deserialization.
"""

from __future__ import print_function as _print_function

import sys as _sys

from tensorflow.python.keras.initializers import deserialize
from tensorflow.python.keras.initializers import get
from tensorflow.python.keras.initializers import serialize
from tensorflow.python.keras.initializers.initializers_v2 import Constant
from tensorflow.python.keras.initializers.initializers_v2 import Constant as constant
from tensorflow.python.keras.initializers.initializers_v2 import GlorotNormal
from tensorflow.python.keras.initializers.initializers_v2 import GlorotNormal as glorot_normal
from tensorflow.python.keras.initializers.initializers_v2 import GlorotUniform
from tensorflow.python.keras.initializers.initializers_v2 import GlorotUniform as glorot_uniform
from tensorflow.python.keras.initializers.initializers_v2 import HeNorm<PERSON>
from tensorflow.python.keras.initializers.initializers_v2 import HeNormal as he_normal
from tensorflow.python.keras.initializers.initializers_v2 import HeUniform
from tensorflow.python.keras.initializers.initializers_v2 import HeUniform as he_uniform
from tensorflow.python.keras.initializers.initializers_v2 import Identity
from tensorflow.python.keras.initializers.initializers_v2 import Identity as identity
from tensorflow.python.keras.initializers.initializers_v2 import Initializer
from tensorflow.python.keras.initializers.initializers_v2 import LecunNormal
from tensorflow.python.keras.initializers.initializers_v2 import LecunNormal as lecun_normal
from tensorflow.python.keras.initializers.initializers_v2 import LecunUniform
from tensorflow.python.keras.initializers.initializers_v2 import LecunUniform as lecun_uniform
from tensorflow.python.keras.initializers.initializers_v2 import Ones
from tensorflow.python.keras.initializers.initializers_v2 import Ones as ones
from tensorflow.python.keras.initializers.initializers_v2 import Orthogonal
from tensorflow.python.keras.initializers.initializers_v2 import Orthogonal as orthogonal
from tensorflow.python.keras.initializers.initializers_v2 import RandomNormal
from tensorflow.python.keras.initializers.initializers_v2 import RandomNormal as random_normal
from tensorflow.python.keras.initializers.initializers_v2 import RandomUniform
from tensorflow.python.keras.initializers.initializers_v2 import RandomUniform as random_uniform
from tensorflow.python.keras.initializers.initializers_v2 import TruncatedNormal
from tensorflow.python.keras.initializers.initializers_v2 import TruncatedNormal as truncated_normal
from tensorflow.python.keras.initializers.initializers_v2 import VarianceScaling
from tensorflow.python.keras.initializers.initializers_v2 import VarianceScaling as variance_scaling
from tensorflow.python.keras.initializers.initializers_v2 import Zeros
from tensorflow.python.keras.initializers.initializers_v2 import Zeros as zeros

del _print_function
