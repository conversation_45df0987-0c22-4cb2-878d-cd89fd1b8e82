import hashlib
import traceback

from django.utils import timezone
from django.views import View

from apps.models.ecg_analysis_modes import TCustom
from apps.utils.get_response import GetResponse
from apps.utils.logger_helper import Logger
from apps.utils.param_extraction import param_extraction
from apps.utils.redis_helper import RedisHelper


class LoginView(View):
    def post(self, request):
        """
        POST请求接口
        :param request: 请求对象
        :return: 诊断结果
        """
        try:
            custom_no = param_extraction(self.request, 'clientId', 'POST')  # 客户端ID
            custom_secret = param_extraction(self.request, 'clientSecret', 'POST')  # 客户端密码

            custom = TCustom.objects.filter(custom_no=custom_no, custom_secret=custom_secret).first()

            if custom:
                # 验证用户成功返回Token
                # 将数据和盐结合起来
                duration_minutes = 60
                # 将动态数据和盐结合起来
                dynamic_data = str(timezone.now())  # 使用当前时间作为动态数据
                salted_data = (dynamic_data + custom.salt + custom_secret).encode('utf-8')
                # 使用 SHA-256 哈希算法
                hash_object = hashlib.sha256(salted_data)
                # 获取十六进制的哈希值作为token
                token = hash_object.hexdigest()
                # 将 Token 和过期时间存储到 Redis 中
                cache_key = f"token_{token}"
                expire = duration_minutes * 60
                value = {"custom_id": custom.id}
                RedisHelper().set(cache_key, value, ex=expire)

                return GetResponse.get_response(code=0, data={
                    'token': token, 'expire': expire
                })
            else:
                return GetResponse.get_response(code=3)
        except Exception:
            # 记录错误日志
            Logger().error(f'登录获取Token接口异常：{traceback.format_exc()}')
            return GetResponse.get_response(code=2)
