/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// PrepareForExportPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class PrepareForExportPassBase : public ::mlir::FunctionPass {
public:
  using Base = PrepareForExportPassBase;

  PrepareForExportPassBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  PrepareForExportPassBase(const PrepareForExportPassBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-prepare-for-export");
  }
  ::llvm::StringRef getArgument() const override { return "xla-prepare-for-export"; }

  ::llvm::StringRef getDescription() const override { return "Prepare for XLA export"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrepareForExportPass");
  }
  ::llvm::StringRef getName() const override { return "PrepareForExportPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// PrepareForExportPass Registration
//===----------------------------------------------------------------------===//

inline void registerPrepareForExportPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::mhlo::CreatePrepareForExport();
  });
}

//===----------------------------------------------------------------------===//
// Xla Registration
//===----------------------------------------------------------------------===//

inline void registerXlaPasses() {
  registerPrepareForExportPassPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
