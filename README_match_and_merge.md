# 数据匹配和合并工具使用说明

## 功能概述

这个工具实现了两个数据文件的关联匹配并合并特定列的数据，专门用于处理ECG数据的匹配和合并任务。

## 主要特性

### 1. 智能匹配策略
- **多格式匹配**：自动尝试带下划线和不带下划线的匹配格式
- **自动列识别**：自动识别源文件1的最后一列作为匹配键
- **灵活键组合**：将源文件2的 `es_key` + `lead` 组合作为匹配键

### 2. 数据处理能力
- **多格式支持**：支持CSV和Excel文件格式
- **编码自适应**：自动处理UTF-8和GBK编码问题
- **错误处理**：完善的异常处理和错误提示

### 3. 详细报告
- **匹配统计**：提供详细的匹配成功率统计
- **失败分析**：显示未匹配记录的示例
- **结果分布**：生成疾病名称分布统计

## 文件结构

```
match_and_merge.py          # 主程序文件
test_match_and_merge.py     # 测试程序
README_match_and_merge.md   # 使用说明（本文件）
```

## 使用方法

### 方法1：使用默认路径
如果您的文件位于默认路径，直接运行：
```bash
python match_and_merge.py
```

默认路径：
- 源文件1：`D:\ECG\0723一分钟项目测试\标注平台数据\数据\一分钟接口结论\合并结果.csv`
- 源文件2：`D:\ECG\0723一分钟项目测试\标注平台数据\无标题.xls`

### 方法2：交互式文件选择
如果默认文件不存在，程序会自动启动交互式文件选择模式：
1. 程序会扫描当前目录及子目录中的所有CSV和Excel文件
2. 显示文件列表供您选择
3. 按提示输入文件编号即可

### 方法3：编程方式调用
```python
from match_and_merge import DataMatcher

# 指定自定义文件路径
matcher = DataMatcher(
    file1_path="path/to/your/file1.csv",
    file2_path="path/to/your/file2.xlsx",
    output_path="custom_output.csv"
)

success = matcher.run()
```

## 数据要求

### 源文件1要求
- 格式：CSV文件
- 匹配键：最后一列将被用作匹配键
- 编码：支持UTF-8或GBK

### 源文件2要求
- 格式：Excel文件（.xls或.xlsx）
- 必需列：
  - `es_key`：匹配键的第一部分
  - `lead`：匹配键的第二部分
  - `disease_name`：要合并的疾病名称列

## 匹配逻辑

### 匹配键生成
- **源文件1**：使用最后一列的值作为匹配键
- **源文件2**：将 `es_key` + `lead` 组合作为匹配键

### 匹配策略
1. **优先匹配**：尝试 `es_key + "_" + lead` 格式（带下划线）
2. **备用匹配**：对未匹配记录尝试 `es_key + lead` 格式（不带下划线）

### 示例
```
源文件1匹配键：KEY001_I, KEY002_II
源文件2生成键：KEY001_I, KEY002_II (优先) 或 KEY001I, KEY002II (备用)
```

## 输出文件

### 主要输出
- **合并结果文件**：包含原始数据和新增的 `disease_name` 列
- **匹配报告**：`matching_report.txt` - 详细的匹配统计和分析
- **日志文件**：`match_and_merge.log` - 完整的执行日志

### 报告内容
- 输入输出文件路径
- 匹配统计（总数、成功、失败、匹配率）
- 疾病名称分布统计
- 未匹配记录示例

## 测试功能

运行测试程序验证功能：
```bash
python test_match_and_merge.py
```

测试程序会：
1. 创建示例数据文件
2. 执行匹配测试
3. 显示匹配结果
4. 尝试真实数据测试（如果文件存在）

## 常见问题

### Q1: 匹配率很低怎么办？
**A**: 检查以下几点：
- 确认源文件2包含必需的列（`es_key`, `lead`, `disease_name`）
- 检查匹配键的格式是否一致
- 查看日志文件了解具体的匹配键格式

### Q2: 文件编码问题
**A**: 程序会自动尝试UTF-8和GBK编码，如果仍有问题：
- 确保CSV文件保存为UTF-8编码
- 或者手动转换文件编码

### Q3: Excel文件读取失败
**A**: 确保：
- Excel文件没有被其他程序占用
- 文件格式正确（.xls或.xlsx）
- 文件没有损坏

### Q4: 列名不匹配
**A**: 程序会提供列名建议，根据提示：
- 检查实际的列名
- 如需要，手动修改源文件的列名

## 性能优化建议

1. **大文件处理**：对于大型数据文件，建议分批处理
2. **内存使用**：程序会将整个文件加载到内存，确保有足够的可用内存
3. **磁盘空间**：确保有足够的磁盘空间存储输出文件

## 更新日志

### v1.0 (2025-07-30)
- 初始版本发布
- 支持基本的数据匹配和合并功能
- 多格式匹配策略
- 详细的报告和日志功能
- 交互式文件选择
- 完整的测试套件

## 技术支持

如遇到问题，请：
1. 查看生成的日志文件 `match_and_merge.log`
2. 检查匹配报告 `matching_report.txt`
3. 运行测试程序验证基本功能
4. 提供详细的错误信息和数据样本
