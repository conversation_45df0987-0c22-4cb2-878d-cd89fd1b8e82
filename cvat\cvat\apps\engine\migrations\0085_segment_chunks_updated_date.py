# Generated by Django 4.2.15 on 2024-09-25 13:52

from datetime import datetime

from django.db import migrations, models


def _get_0083_move_to_segment_chunks_migration_date(apps, schema_editor) -> datetime:
    with schema_editor.connection.cursor() as cursor:
        cursor.execute(
            """\
            SELECT applied
            FROM django_migrations
            WHERE app = %s AND name = %s
            """,
            ["engine", "0083_move_to_segment_chunks"],
        )
        return cursor.fetchone()[0]


def init_chunks_updated_date(apps, schema_editor):
    # The 0083 migration changed data distribution by chunks
    migration_0083_date = _get_0083_move_to_segment_chunks_migration_date(apps, schema_editor)

    Segment = apps.get_model("engine", "Segment")
    task_created_date_subquery = models.Subquery(
        Segment.objects.select_related("task")
        .filter(pk=models.OuterRef("pk"))
        .values("task__created_date")[:1]
    )

    Segment.objects.update(
        chunks_updated_date=models.functions.Greatest(
            task_created_date_subquery,
            migration_0083_date,
        )
    )


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0084_honeypot_support"),
    ]

    operations = [
        migrations.AddField(
            model_name="segment",
            name="chunks_updated_date",
            field=models.DateTimeField(default=None, null=True),
            preserve_default=False,
        ),
        migrations.RunPython(
            init_chunks_updated_date,
            reverse_code=migrations.RunPython.noop,
        ),
        migrations.AlterField(
            model_name="segment",
            name="chunks_updated_date",
            field=models.DateTimeField(null=False, auto_now_add=True),
        ),
    ]
