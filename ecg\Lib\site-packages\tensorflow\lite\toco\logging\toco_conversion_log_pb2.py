# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/lite/toco/logging/toco_conversion_log.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/lite/toco/logging/toco_conversion_log.proto',
  package='toco',
  syntax='proto2',
  serialized_options=None,
  serialized_pb=_b('\n6tensorflow/lite/toco/logging/toco_conversion_log.proto\x12\x04toco\"\xc9\x04\n\x11TocoConversionLog\x12\x0f\n\x07op_list\x18\x01 \x03(\t\x12=\n\x0c\x62uilt_in_ops\x18\x02 \x03(\x0b\x32\'.toco.TocoConversionLog.BuiltInOpsEntry\x12:\n\ncustom_ops\x18\x03 \x03(\x0b\x32&.toco.TocoConversionLog.CustomOpsEntry\x12:\n\nselect_ops\x18\x04 \x03(\x0b\x32&.toco.TocoConversionLog.SelectOpsEntry\x12\x15\n\rop_signatures\x18\x05 \x03(\t\x12\x1a\n\x12input_tensor_types\x18\x06 \x03(\t\x12\x1b\n\x13output_tensor_types\x18\x07 \x03(\t\x12\x19\n\x11log_generation_ts\x18\x08 \x01(\x03\x12\x12\n\nmodel_size\x18\t \x01(\x05\x12\x17\n\x0ftf_lite_version\x18\n \x01(\t\x12\x12\n\nos_version\x18\x0b \x01(\t\x12\x12\n\nmodel_hash\x18\x0c \x01(\t\x12\x15\n\rtoco_err_logs\x18\r \x01(\t\x1a\x31\n\x0f\x42uiltInOpsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\x1a\x30\n\x0e\x43ustomOpsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\x1a\x30\n\x0eSelectOpsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01')
)




_TOCOCONVERSIONLOG_BUILTINOPSENTRY = _descriptor.Descriptor(
  name='BuiltInOpsEntry',
  full_name='toco.TocoConversionLog.BuiltInOpsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='toco.TocoConversionLog.BuiltInOpsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='toco.TocoConversionLog.BuiltInOpsEntry.value', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=501,
  serialized_end=550,
)

_TOCOCONVERSIONLOG_CUSTOMOPSENTRY = _descriptor.Descriptor(
  name='CustomOpsEntry',
  full_name='toco.TocoConversionLog.CustomOpsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='toco.TocoConversionLog.CustomOpsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='toco.TocoConversionLog.CustomOpsEntry.value', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=552,
  serialized_end=600,
)

_TOCOCONVERSIONLOG_SELECTOPSENTRY = _descriptor.Descriptor(
  name='SelectOpsEntry',
  full_name='toco.TocoConversionLog.SelectOpsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='toco.TocoConversionLog.SelectOpsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='toco.TocoConversionLog.SelectOpsEntry.value', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=602,
  serialized_end=650,
)

_TOCOCONVERSIONLOG = _descriptor.Descriptor(
  name='TocoConversionLog',
  full_name='toco.TocoConversionLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='op_list', full_name='toco.TocoConversionLog.op_list', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='built_in_ops', full_name='toco.TocoConversionLog.built_in_ops', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='custom_ops', full_name='toco.TocoConversionLog.custom_ops', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='select_ops', full_name='toco.TocoConversionLog.select_ops', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='op_signatures', full_name='toco.TocoConversionLog.op_signatures', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='input_tensor_types', full_name='toco.TocoConversionLog.input_tensor_types', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_tensor_types', full_name='toco.TocoConversionLog.output_tensor_types', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='log_generation_ts', full_name='toco.TocoConversionLog.log_generation_ts', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_size', full_name='toco.TocoConversionLog.model_size', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tf_lite_version', full_name='toco.TocoConversionLog.tf_lite_version', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='os_version', full_name='toco.TocoConversionLog.os_version', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_hash', full_name='toco.TocoConversionLog.model_hash', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='toco_err_logs', full_name='toco.TocoConversionLog.toco_err_logs', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_TOCOCONVERSIONLOG_BUILTINOPSENTRY, _TOCOCONVERSIONLOG_CUSTOMOPSENTRY, _TOCOCONVERSIONLOG_SELECTOPSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=65,
  serialized_end=650,
)

_TOCOCONVERSIONLOG_BUILTINOPSENTRY.containing_type = _TOCOCONVERSIONLOG
_TOCOCONVERSIONLOG_CUSTOMOPSENTRY.containing_type = _TOCOCONVERSIONLOG
_TOCOCONVERSIONLOG_SELECTOPSENTRY.containing_type = _TOCOCONVERSIONLOG
_TOCOCONVERSIONLOG.fields_by_name['built_in_ops'].message_type = _TOCOCONVERSIONLOG_BUILTINOPSENTRY
_TOCOCONVERSIONLOG.fields_by_name['custom_ops'].message_type = _TOCOCONVERSIONLOG_CUSTOMOPSENTRY
_TOCOCONVERSIONLOG.fields_by_name['select_ops'].message_type = _TOCOCONVERSIONLOG_SELECTOPSENTRY
DESCRIPTOR.message_types_by_name['TocoConversionLog'] = _TOCOCONVERSIONLOG
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TocoConversionLog = _reflection.GeneratedProtocolMessageType('TocoConversionLog', (_message.Message,), {

  'BuiltInOpsEntry' : _reflection.GeneratedProtocolMessageType('BuiltInOpsEntry', (_message.Message,), {
    'DESCRIPTOR' : _TOCOCONVERSIONLOG_BUILTINOPSENTRY,
    '__module__' : 'tensorflow.lite.toco.logging.toco_conversion_log_pb2'
    # @@protoc_insertion_point(class_scope:toco.TocoConversionLog.BuiltInOpsEntry)
    })
  ,

  'CustomOpsEntry' : _reflection.GeneratedProtocolMessageType('CustomOpsEntry', (_message.Message,), {
    'DESCRIPTOR' : _TOCOCONVERSIONLOG_CUSTOMOPSENTRY,
    '__module__' : 'tensorflow.lite.toco.logging.toco_conversion_log_pb2'
    # @@protoc_insertion_point(class_scope:toco.TocoConversionLog.CustomOpsEntry)
    })
  ,

  'SelectOpsEntry' : _reflection.GeneratedProtocolMessageType('SelectOpsEntry', (_message.Message,), {
    'DESCRIPTOR' : _TOCOCONVERSIONLOG_SELECTOPSENTRY,
    '__module__' : 'tensorflow.lite.toco.logging.toco_conversion_log_pb2'
    # @@protoc_insertion_point(class_scope:toco.TocoConversionLog.SelectOpsEntry)
    })
  ,
  'DESCRIPTOR' : _TOCOCONVERSIONLOG,
  '__module__' : 'tensorflow.lite.toco.logging.toco_conversion_log_pb2'
  # @@protoc_insertion_point(class_scope:toco.TocoConversionLog)
  })
_sym_db.RegisterMessage(TocoConversionLog)
_sym_db.RegisterMessage(TocoConversionLog.BuiltInOpsEntry)
_sym_db.RegisterMessage(TocoConversionLog.CustomOpsEntry)
_sym_db.RegisterMessage(TocoConversionLog.SelectOpsEntry)


_TOCOCONVERSIONLOG_BUILTINOPSENTRY._options = None
_TOCOCONVERSIONLOG_CUSTOMOPSENTRY._options = None
_TOCOCONVERSIONLOG_SELECTOPSENTRY._options = None
# @@protoc_insertion_point(module_scope)
