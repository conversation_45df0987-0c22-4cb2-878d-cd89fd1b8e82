baseURL = "/"
title = "CVAT"
relativeURLs = true

enableRobotsTXT = true

# Hugo allows theme composition (and inheritance). The precedence is from left to right.
theme = ["docsy"]

# Will give values to .Lastmod etc.
enableGitInfo = false

# Language settings
contentDir = "content/en"
defaultContentLanguage = "en"
defaultContentLanguageInSubdir = false
# Useful when translating.
enableMissingTranslationPlaceholders = true

disableKinds = ["taxonomy", "taxonomyTerm"]

# Highlighting config
pygmentsCodeFences = true
pygmentsUseClasses = false
# Use the new Chroma Go highlighter in Hugo.
pygmentsUseClassic = false
#pygmentsOptions = "linenos=table"
# See https://help.farbox.com/pygments.html
pygmentsStyle = "tango"

# Configure how URLs look like per section.
[permalinks]
blog = "/:section/:year/:month/:day/:slug/"

## Configuration for BlackFriday markdown parser: https://github.com/russross/blackfriday
[blackfriday]
plainIDAnchors = true
hrefTargetBlank = true
angledQuotes = false
latexDashes = true

# Image processing configuration.
[imaging]
resampleFilter = "CatmullRom"
quality = 75
anchor = "smart"

[[menu.main]]
    name = "Try it now"
    weight = 50
    url = "https://app.cvat.ai"
[[menu.main]]
    name = "GitHub"
    weight = 60
    url = "https://github.com/cvat-ai/cvat"
    pre = "<i class='fab fa-github'></i>"

[services]
[services.googleAnalytics]
# Comment out the next line to disable GA tracking. Also disables the feature described in [params.ui.feedback].
# id = "UA-00000000-0"

# Language configuration

[languages]
[languages.en]
title = "CVAT"
description = ""
languageName ="English"
# Weight used for sorting.
weight = 1

[markup]
  [markup.goldmark]
    [markup.goldmark.renderer]
      unsafe = true
  [markup.highlight]
      # See a complete list of available styles at https://xyproto.github.io/splash/docs/all.html
      style = "tango"
      # Uncomment if you want your chosen highlight style used for code blocks without a specified language
      # guessSyntax = "true"

# Everything below this are Site Params

# Comment out if you don't want the "print entire section" link enabled.
[outputs]
section = ["HTML", "print"]

[params]
cvat_ai_terms_of_use = "https://www.cvat.ai/terms-of-use"
cvat_ai_privacy_notice = "https://www.cvat.ai/privacy"

# First one is picked as the Twitter card image if not set on page.
# images = ["images/project-illustration.png"]

# Menu title if your navbar has a versions selector to access old versions of your site.
# This menu appears only if you have at least one [params.versions] set.
# version_menu = "Releases"

# Flag used in the "version-banner" partial to decide whether to display a
# banner on every page indicating that this is an archived version of the docs.
# Set this flag to "true" if you want to display the banner.
archived_version = false

# The version number for the version of the docs represented in this doc set.
# Used in the "version-banner" partial to display a version number for the
# current doc set.
version = "0.0"

# A link to latest version of the docs. Used in the "version-banner" partial to
# point people to the main doc site.
url_latest_version = "https://example.com"

# Repository configuration (URLs for in-page links to opening issues and suggesting changes)
github_repo = "https://github.com/cvat-ai/cvat"
# An optional link to a related project repo. For example, the sibling repository where your product code lives.
github_project_repo = "https://github.com/cvat-ai/cvat"

# Specify a value here if your content directory is not in your repo's root directory
github_subdir = "site"

# Uncomment this if you have a newer GitHub repo with "main" as the default branch,
# or specify a new value if you want to reference another branch in your GitHub links
github_branch = "develop"

# Google Custom Search Engine ID. Remove or comment out to disable search.
# gcs_engine_id = "011737558837375720776:fsdu1nryfng"

# Enable Algolia DocSearch
algolia_docsearch = false

# Enable Lunr.js offline search
offlineSearch = true

# Enable syntax highlighting and copy buttons on code blocks with Prism
prism_syntax_highlighting = true

# User interface configuration
[params.ui]
# Enable to show the side bar menu in its compact state.
sidebar_menu_compact = true
ul_show = 2
#  Set to true to disable breadcrumb navigation.
breadcrumb_disable = false
#  Set to true to hide the sidebar search box (the top nav search box will still be displayed if search is enabled)
sidebar_search_disable = true
#  Set to false if you don't want to display a logo (/assets/icons/logo.svg) in the top nav bar
navbar_logo = true
# Set to true to disable the About link in the site footer
footer_about_disable = false

# Adds a H2 section titled "Feedback" to the bottom of each doc. The responses are sent to Google Analytics as events.
# This feature depends on [services.googleAnalytics] and will be disabled if "services.googleAnalytics.id" is not set.
# If you want this feature, but occasionally need to remove the "Feedback" section from a single page,
# add "hide_feedback: true" to the page's front matter.
[params.ui.feedback]
enable = false
# The responses that the user sees after clicking "yes" (the page was helpful) or "no" (the page was not helpful).
yes = 'Glad to hear it! Please <a href="https://github.com/USERNAME/REPOSITORY/issues/new">tell us how we can improve</a>.'
no = 'Sorry to hear that. Please <a href="https://github.com/USERNAME/REPOSITORY/issues/new">tell us how we can improve</a>.'

# Adds a reading time to the top of each doc.
# If you want this feature, but occasionally need to remove the Reading time from a single page,
# add "hide_readingtime: true" to the page's front matter
[params.ui.readingtime]
enable = false

[params.links]
# End user relevant links. These will show up on left side of footer and in the community page if you have one.
[[params.links.user]]
	name ="Gitter public chat"
	url = "https://gitter.im/opencv-cvat/public"
	icon = "fab fa-gitter"
	desc = "Join our Gitter channel for community support."
[[params.links.user]]
	name = "Stack Overflow"
	url = "https://stackoverflow.com/search?q=%23cvat"
	icon = "fab fa-stack-overflow"
	desc = "Practical questions and curated answers"
[[params.links.user]]
	name = "YouTube"
	url = "https://www.youtube.com/@cvat-ai"
	icon = "fab fa-youtube"
	desc = "YouTube channel of CVAT.ai"

# Developer relevant links. These will show up on right side of footer and in the community page if you have one.
[[params.links.developer]]
	name = "GitHub"
	url = "https://github.com/cvat-ai/cvat"
	icon = "fab fa-github"
	desc = "Development takes place here!"
[[params.links.developer]]
	name ="Gitter developers chat"
	url = "https://gitter.im/opencv-cvat/dev"
	icon = "fab fa-gitter"
	desc = "Join our Gitter channel for community support."
[[params.links.developer]]
	name = "Discord chat"
	url = "https://discord.gg/fNR3eXfk6C"
	icon = "fab fa-discord"
	desc = "Join our Discord server for community support."
