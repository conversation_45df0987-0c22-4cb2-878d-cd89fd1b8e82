---
title: 'CVAT Architecture'
linkTitle: 'CVAT Architecture'
weight: 1
description: 'Description of CVAT architecture and components'
---

This guide is designed to provide a comprehensive overview of the architecture and components
of the CVAT and to illustrate how each component interacts within the system.

![CVAT Architecture](/images/cvat-arch.png)

<!--lint disable maximum-line-length-->

| Domain                  | Component                                                                                                                                                                                                                    | Functionality                                                                                                  | Description                                                                                                                                                                                                                                                                 |
| ----------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Analytics**           | Vector                                                                                                                                                                                                                       | Event processing                                                                                               | There are several components that process events (backend, frontend, web UI). All events are sent to a single point - Vector, where they are processed and then redirected to ClickHouse. For more information, see [Analytics](/docs/administration/advanced/analytics/).  |
|                         | ClickHouse                                                                                                                                                                                                                   | Event database                                                                                                 | Stores events. For more information, see [Analytics](/docs/administration/advanced/analytics/).                                                                                                                                                                             |
|                         | Grafana                                                                                                                                                                                                                      | Dashboards                                                                                                     | Data based on the web interface. For more information, see [Analytics](/docs/administration/advanced/analytics/).                                                                                                                                                           |
| **Data storage NFS**    | RVVX access mode storage is required in case of multi-node deployment. Available with different types of storages: <br><br><li> AWS: Amazon Elastic File System (EFS)<li>Azure: Azure Files with NFS <li>GCP: Filestore NFS  | Contains data required for CVAT operations                                                                    | It is necessary to have the capability for multiple mounting (across several nodes) in RWX mode. For more information, see [K8 Deployment with Helm](/docs/administration/advanced/k8s_deployment_with_helm/)                                                               |
| **Data cache**          | Apache kvrocks                                                                                                                                                                                                               | Used for data caching (queries and search). Suitable for environments that require frequent database queries.  | [Apache Kvrocks](https://kvrocks.apache.org/)                                                                                                                                                                                                                               |
| **Job queue**           | Redis                                                                                                                                                                                                                        | Queue manager                                                                                                  |                                                                                                                                                                                                                                                                             |
| **Database**            | PostgreSQL                                                                                                                                                                                                                   | Database                                                                                                       | A database where data is stored in a structured form.                                                                                                                                                                                                                       |
| **CVAT.ai Components**  | Ingress Controller (can be disabled)                                                                                                                                                                                         | Routing traffic.                                                                                               | [CVAT deployment on Kubernetes with Helm](/docs/administration/advanced/k8s_deployment_with_helm/)                                                                                                                                                                          |
|                         | Authorization                                                                                                                                                                                                                | [Authorization service based on Open Policy Agent.](/docs/manual/advanced/iam_user_roles/)                     |
| **Backend CVAT**        | Backend                                                                                                                                                                                                                      | Main framework                                                                                                 | Main engine, uses Django + Django DRF.                                                                                                                                                                                                                                      |
| **Workers**             | Import Worker                                                                                                                                                                                                                | Everything related to loading data - creating tasks, uploading annotations, etc.                               |                                                                                                                                                                                                                                                                             |
|                         | Export Worker                                                                                                                                                                                                                | Everything related to exporting data - exporting results, creating dumps, etc.                                 |                                                                                                                                                                                                                                                                             |
|                         | Annotation Worker                                                                                                                                                                                                            | Auto-annotation tasks.                                                                                         |                                                                                                                                                                                                                                                                             |
|                         | Utils Worker                                                                                                                                                                                                                 | Responsible for tracking various file changes and more.                                                        |                                                                                                                                                                                                                                                                             |
|                         | Analytics Report                                                                                                                                                                                                             | Reports and analytics that are displayed in the CVAT interface.                                                |                                                                                                                                                                                                                                                                             |
|                         | Quality Report                                                                                                                                                                                                               | Analysis and reports on data quality.                                                                          |                                                                                                                                                                                                                                                                             |
|                         | Webhook Worker                                                                                                                                                                                                               | Manages webhooks.                                                                                              |                                                                                                                                                                                                                                                                             |
| Auto annotation         | Auto Annotation Nucio                                                                                                                                                                                                        | Microservice application, used for auto annotation.                                                            | [How to enable auto annotation feature.](/docs/administration/advanced/k8s_deployment_with_helm/#optional-enable-auto-annotation-feature)                                                                                                                                   |

<!--lint enable maximum-line-length-->
